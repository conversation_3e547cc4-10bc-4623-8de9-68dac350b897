package com.moego.lib.featureflag.impl.growthbook;

import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.featureflag.FeatureFlag;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import growthbook.sdk.java.GBContext;
import growthbook.sdk.java.GBFeaturesRepository;
import growthbook.sdk.java.GrowthBook;

/**
 * GrowthBook implementation of {@link FeatureFlagApi}.
 *
 * <AUTHOR>
 * @since 2024/12/17
 */
public class GrowthBookFeatureFlagApi implements FeatureFlagApi {

    private final GBFeaturesRepository repository;

    public GrowthBookFeatureFlagApi(GBFeaturesRepository repository) {
        this.repository = repository;
    }

    @Override
    public boolean isOn(FeatureFlag feature, FeatureFlagContext context) {

        var growthBook = buildGrowthBook(context);

        return growthBook.isOn(feature.getFeature());
    }

    @Override
    public <T> T getJsonValue(
            FeatureFlag feature, FeatureFlagContext context, T defaultValue, Class<T> gsonDeserializableClass) {

        var growthBook = buildGrowthBook(context);

        return growthBook.getFeatureValue(feature.getFeature(), defaultValue, gsonDeserializableClass);
    }

    private GrowthBook buildGrowthBook(FeatureFlagContext context) {

        var propertiesMap = context.toPropertiesMap();

        var gbContext = GBContext.builder()
                .featuresJson(repository.getFeaturesJson())
                .attributesJson(JsonUtil.toJson(propertiesMap))
                .build();
        return new GrowthBook(gbContext);
    }
}
