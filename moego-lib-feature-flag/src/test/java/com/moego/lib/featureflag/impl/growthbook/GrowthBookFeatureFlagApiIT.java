package com.moego.lib.featureflag.impl.growthbook;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.featureflag.FeatureFlag;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import java.util.UUID;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;

@SpringBootTest(
        classes = GrowthBookFeatureFlagApiIT.Cfg.class,
        properties = {
            "moego.feature-flag.growth-book.api-host=https://growthbook.moego.pet/growthbook-api",
            "moego.feature-flag.growth-book.client-key=sdk-qygeRRneunZQJxf"
        })
class GrowthBookFeatureFlagApiIT {

    @Autowired
    private FeatureFlagApi featureFlagApi;

    @Test
    @DisplayName("When feature flag doesn't exist, isOn should return false")
    void whenFeatureFlagDoesNotExist_thenIsOnReturnsFalse() {
        // Create a non-existent feature flag
        var nonExistentFlag = new TestFeatureFlag(UUID.randomUUID().toString());

        var ctx = FeatureFlagContext.builder().company(123L).build();

        var actual = featureFlagApi.isOn(nonExistentFlag, ctx);

        // Assert that the non-existent feature flag is considered off
        assertThat(actual).isFalse();
    }

    @Test
    @DisplayName("When feature flag doesn't exist, getJsonValue should return default value")
    void whenFeatureFlagDoesNotExist_thenGetJsonValueReturnsDefaultValue() {
        // Create a non-existent feature flag
        var nonExistentFlag = new TestFeatureFlag(UUID.randomUUID().toString());

        var ctx = FeatureFlagContext.builder().company(123L).build();

        var actual = featureFlagApi.getJsonValue(nonExistentFlag, ctx, nonExistentFlag, TestFeatureFlag.class);
        assertThat(actual).isEqualTo(nonExistentFlag);

        var actual2 = featureFlagApi.getJsonValue(nonExistentFlag, ctx, null, TestFeatureFlag.class);
        assertThat(actual2).isEqualTo(null);
    }

    @DisplayName("When JSON object type doesn't match, getJsonValue should return default value")
    @Test
    void whenJsonObjectTypeNotMatch_thenGetJsonValueReturnsDefaultValue() {
        // an existing feature flag, value is like '{"feature": "test_feature_flag_getJsonValue"}'
        var existentFlag = new TestFeatureFlag("test_feature_flag_getJsonValue");

        var ctx = FeatureFlagContext.builder().company(123L).build();
        TestFeatureFlag2 defaultValue = new TestFeatureFlag2(123);
        var actual = featureFlagApi.getJsonValue(existentFlag, ctx, defaultValue, TestFeatureFlag2.class);
        assertThat(actual).isEqualTo(defaultValue);

        var actual2 = featureFlagApi.getJsonValue(existentFlag, ctx, null, TestFeatureFlag2.class);
        assertThat(actual2).isEqualTo(null);
    }

    @DisplayName("When JSON object type match and flag existing, getJsonValue should return existing value")
    @Test
    void whenJsonObjectTypeMatchAndFlagExisting_thenGetJsonValueReturnsExistingValue() {
        // an existing feature flag, value is like '{"feature": "test_feature_flag_getJsonValue"}'
        TestFeatureFlag existingValue = new TestFeatureFlag("test_feature_flag_getJsonValue");

        var ctx = FeatureFlagContext.builder().company(123L).build();
        TestFeatureFlag defaultValue = new TestFeatureFlag(UUID.randomUUID().toString());
        var actual = featureFlagApi.getJsonValue(existingValue, ctx, defaultValue, TestFeatureFlag.class);
        assertThat(actual).isEqualTo(existingValue);
    }

    private record TestFeatureFlag2(Integer feature) {}

    private record TestFeatureFlag(String feature) implements FeatureFlag {
        @Override
        public String getFeature() {
            return feature;
        }
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    static class Cfg {}
}
