package com.moego.lib.risk.control.recaptcha;

import com.moego.idl.models.risk_control.v1.RecaptchaAction;
import com.moego.idl.models.risk_control.v1.RecaptchaVersion;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 只可作用于有会话 ID 的接口上，即 AuthContext 确保获取到的 sessionId 不为空
 * 目前仅在 C 端的 booking.moego.pet 和 form.moego.pet 两个域名下可以获取到 sessionId
 * B 端的 go.moego.pet 未引入匿名会话，如需接入 recaptcha，需要先引入匿名会话
 *
 * <AUTHOR>
 * @since 2023/9/6
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface Recaptcha {

    RecaptchaAction action();

    RecaptchaVersion[] applicableVersions();
}
