package com.moego.lib.risk.control.recaptcha;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.risk_control.v1.RecaptchaAction;
import com.moego.idl.models.risk_control.v1.RecaptchaVersion;
import com.moego.idl.models.risk_control.v1.RiskControlDef;
import com.moego.idl.service.risk_control.v1.RecaptchaInput;
import com.moego.idl.service.risk_control.v1.RecaptchaServiceGrpc.RecaptchaServiceBlockingStub;
import com.moego.idl.service.risk_control.v1.RiskControlVerifyInput;
import com.moego.idl.service.risk_control.v1.RiskControlVerifyOutput;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.risk.control.config.RiskControlConstant;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/9/8
 */
@Slf4j
public class RecaptchaUtils {

    public static void verify(Recaptcha recaptcha, RecaptchaServiceBlockingStub recaptchaServiceBlockingStub) {
        if (Objects.isNull(recaptcha) || Objects.isNull(recaptchaServiceBlockingStub)) {
            throw ExceptionUtil.bizException(Code.CODE_GOOGLE_RECAPTCHA_INVALID);
        }
        verify(recaptcha.action(), Arrays.asList(recaptcha.applicableVersions()), recaptchaServiceBlockingStub);
    }

    public static void verify(
            RecaptchaAction action,
            List<RecaptchaVersion> applicableVersions,
            RecaptchaServiceBlockingStub recaptchaServiceBlockingStub) {
        if (Objects.isNull(action)
                || CollectionUtils.isEmpty(applicableVersions)
                || Objects.isNull(recaptchaServiceBlockingStub)) {
            throw ExceptionUtil.bizException(Code.CODE_GOOGLE_RECAPTCHA_INVALID);
        }
        AuthContext authContext = AuthContext.get();
        if (!StringUtils.hasText(authContext.sessionData())) {
            log.error("session data is empty, sessionId: {}", authContext.sessionId());
            throw ExceptionUtil.bizException(
                    Code.CODE_FORBIDDEN, "Your page is out of date, please refresh the page and try again.");
        }
        Map<String, String> sessionData = JsonUtil.toBean(authContext.sessionData(), new TypeRef<>() {});
        if (CollectionUtils.isEmpty(sessionData)) {
            log.error("session data is empty, sessionId: {}", authContext.sessionId());
            throw ExceptionUtil.bizException(
                    Code.CODE_FORBIDDEN, "Your page is out of date, please refresh the page and try again.");
        }
        String riskControlToken = sessionData.get(RiskControlConstant.RISK_CONTROL_TOKEN);
        if (!StringUtils.hasText(riskControlToken) || Objects.isNull(authContext.sessionId())) {
            log.error("risk control token is empty, sessionId: {}", authContext.sessionId());
            throw ExceptionUtil.bizException(
                    Code.CODE_FORBIDDEN, "Your page is out of date, please refresh the page and try again.");
        }
        RiskControlVerifyInput verifyInput = RiskControlVerifyInput.newBuilder()
                .setRiskControl(
                        RiskControlDef.newBuilder().setToken(riskControlToken).build())
                .setRecaptcha(RecaptchaInput.newBuilder()
                        .setSessionId(authContext.sessionId())
                        .setAction(action)
                        .addAllVersions(applicableVersions)
                        .build())
                .build();
        RiskControlVerifyOutput verifyOutput = recaptchaServiceBlockingStub.verify(verifyInput);
        if (!verifyOutput.getSuccess()) {
            throw ExceptionUtil.bizException(Code.CODE_GOOGLE_RECAPTCHA_INVALID);
        }
    }
}
