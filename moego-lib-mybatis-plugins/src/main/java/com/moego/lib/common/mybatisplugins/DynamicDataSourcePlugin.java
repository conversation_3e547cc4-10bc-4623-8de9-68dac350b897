package com.moego.lib.common.mybatisplugins;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import java.util.List;
import org.mybatis.generator.api.IntrospectedTable;
import org.mybatis.generator.api.PluginAdapter;
import org.mybatis.generator.api.dom.java.FullyQualifiedJavaType;
import org.mybatis.generator.api.dom.java.Interface;

/**
 * Make MyBatis mapper interfaces implement {@link DynamicDataSource}.
 *
 * <p> Usage:
 * <pre>{@code
 * <plugin type="com.moego.lib.common.mybatisplugins.DynamicDataSourcePlugin"/>
 * }</pre>
 *
 * <p> Generated code:
 * <pre>{@code
 * public interface BookingRequestMapper extends DynamicDataSource<BookingRequestMapper> {
 *     // ...
 * }
 * }</pre>
 *
 * <AUTHOR>
 * @since 2024/12/14
 */
public class DynamicDataSourcePlugin extends PluginAdapter {
    @Override
    public boolean validate(List<String> warnings) {
        return true;
    }

    @Override
    public boolean clientGenerated(Interface interfaze, IntrospectedTable introspectedTable) {
        var baseType = new FullyQualifiedJavaType(
                "DynamicDataSource<" + interfaze.getType().getShortName() + ">");
        interfaze.addImportedType(
                new FullyQualifiedJavaType("com.moego.lib.common.autoconfigure.datasource.DynamicDataSource"));
        interfaze.addSuperInterface(baseType);
        return true;
    }
}
