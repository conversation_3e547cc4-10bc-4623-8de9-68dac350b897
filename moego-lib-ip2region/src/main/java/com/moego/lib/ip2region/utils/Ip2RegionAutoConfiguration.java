package com.moego.lib.ip2region.utils;

import com.maxmind.db.CHMCache;
import com.maxmind.geoip2.DatabaseReader;
import java.io.IOException;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnResource;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.ClassPathResource;

@AutoConfiguration
@ConditionalOnResource(resources = "classpath:dbip-city-lite-2023-11.mmdb")
public class Ip2RegionAutoConfiguration {

    @Bean
    public IPSearcher ipSearcher() throws IOException {
        ClassPathResource classPathResource = new ClassPathResource("dbip-city-lite-2023-11.mmdb");
        DatabaseReader databaseReader = new DatabaseReader.Builder(classPathResource.getInputStream())
                .withCache(new CHMCache())
                .build();
        return new IPSearcher(databaseReader);
    }
}
