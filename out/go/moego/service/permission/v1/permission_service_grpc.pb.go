// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/permission/v1/permission_service.proto

package permissionsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PermissionServiceClient is the client API for PermissionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PermissionServiceClient interface {
	// get role list
	GetRoleList(ctx context.Context, in *GetRoleListRequest, opts ...grpc.CallOption) (*GetRoleListResponse, error)
	// get role detail
	GetRoleDetail(ctx context.Context, in *GetRoleDetailRequest, opts ...grpc.CallOption) (*GetRoleDetailResponse, error)
	// list role details
	ListRoleDetails(ctx context.Context, in *ListRoleDetailsRequest, opts ...grpc.CallOption) (*ListRoleDetailsResponse, error)
	// create role
	CreateRole(ctx context.Context, in *CreateRoleRequest, opts ...grpc.CallOption) (*CreateRoleResponse, error)
	// update role
	UpdateRole(ctx context.Context, in *UpdateRoleRequest, opts ...grpc.CallOption) (*UpdateRoleResponse, error)
	// delete role
	DeleteRole(ctx context.Context, in *DeleteRoleRequest, opts ...grpc.CallOption) (*DeleteRoleResponse, error)
	// duplicate role
	DuplicateRole(ctx context.Context, in *DuplicateRoleRequest, opts ...grpc.CallOption) (*DuplicateRoleResponse, error)
	// edit permissions
	EditPermissions(ctx context.Context, in *EditPermissionsRequest, opts ...grpc.CallOption) (*EditPermissionsResponse, error)
	// check permission
	CheckPermission(ctx context.Context, in *CheckPermissionRequest, opts ...grpc.CallOption) (*CheckPermissionResponse, error)
	// init role
	InitRole(ctx context.Context, in *InitRoleRequest, opts ...grpc.CallOption) (*InitRoleResponse, error)
	// permission mapping
	PermissionMapping(ctx context.Context, in *PermissionMappingRequest, opts ...grpc.CallOption) (*PermissionMappingResponse, error)
	// get owner permission
	GetOwnerPermission(ctx context.Context, in *GetOwnerPermissionRequest, opts ...grpc.CallOption) (*GetOwnerPermissionResponse, error)
	// Init owner role for companies
	InitOwnerRoleForCompanies(ctx context.Context, in *InitOwnerRoleForCompaniesRequest, opts ...grpc.CallOption) (*InitOwnerRoleForCompaniesResponse, error)
	// init enterprise role
	InitEnterpriseRole(ctx context.Context, in *InitEnterpriseRoleRequest, opts ...grpc.CallOption) (*InitEnterpriseRoleResponse, error)
	// copy permissions from one role to another
	CopyPermissions(ctx context.Context, in *CopyPermissionsRequest, opts ...grpc.CallOption) (*CopyPermissionsResponse, error)
	// Remove any permissions not in the given list
	RetainRolePermissions(ctx context.Context, in *RetainRolePermissionsRequest, opts ...grpc.CallOption) (*RetainRolePermissionsResponse, error)
}

type permissionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPermissionServiceClient(cc grpc.ClientConnInterface) PermissionServiceClient {
	return &permissionServiceClient{cc}
}

func (c *permissionServiceClient) GetRoleList(ctx context.Context, in *GetRoleListRequest, opts ...grpc.CallOption) (*GetRoleListResponse, error) {
	out := new(GetRoleListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.permission.v1.PermissionService/GetRoleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) GetRoleDetail(ctx context.Context, in *GetRoleDetailRequest, opts ...grpc.CallOption) (*GetRoleDetailResponse, error) {
	out := new(GetRoleDetailResponse)
	err := c.cc.Invoke(ctx, "/moego.service.permission.v1.PermissionService/GetRoleDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) ListRoleDetails(ctx context.Context, in *ListRoleDetailsRequest, opts ...grpc.CallOption) (*ListRoleDetailsResponse, error) {
	out := new(ListRoleDetailsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.permission.v1.PermissionService/ListRoleDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) CreateRole(ctx context.Context, in *CreateRoleRequest, opts ...grpc.CallOption) (*CreateRoleResponse, error) {
	out := new(CreateRoleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.permission.v1.PermissionService/CreateRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) UpdateRole(ctx context.Context, in *UpdateRoleRequest, opts ...grpc.CallOption) (*UpdateRoleResponse, error) {
	out := new(UpdateRoleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.permission.v1.PermissionService/UpdateRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) DeleteRole(ctx context.Context, in *DeleteRoleRequest, opts ...grpc.CallOption) (*DeleteRoleResponse, error) {
	out := new(DeleteRoleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.permission.v1.PermissionService/DeleteRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) DuplicateRole(ctx context.Context, in *DuplicateRoleRequest, opts ...grpc.CallOption) (*DuplicateRoleResponse, error) {
	out := new(DuplicateRoleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.permission.v1.PermissionService/DuplicateRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) EditPermissions(ctx context.Context, in *EditPermissionsRequest, opts ...grpc.CallOption) (*EditPermissionsResponse, error) {
	out := new(EditPermissionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.permission.v1.PermissionService/EditPermissions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) CheckPermission(ctx context.Context, in *CheckPermissionRequest, opts ...grpc.CallOption) (*CheckPermissionResponse, error) {
	out := new(CheckPermissionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.permission.v1.PermissionService/CheckPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) InitRole(ctx context.Context, in *InitRoleRequest, opts ...grpc.CallOption) (*InitRoleResponse, error) {
	out := new(InitRoleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.permission.v1.PermissionService/InitRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) PermissionMapping(ctx context.Context, in *PermissionMappingRequest, opts ...grpc.CallOption) (*PermissionMappingResponse, error) {
	out := new(PermissionMappingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.permission.v1.PermissionService/PermissionMapping", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) GetOwnerPermission(ctx context.Context, in *GetOwnerPermissionRequest, opts ...grpc.CallOption) (*GetOwnerPermissionResponse, error) {
	out := new(GetOwnerPermissionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.permission.v1.PermissionService/GetOwnerPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) InitOwnerRoleForCompanies(ctx context.Context, in *InitOwnerRoleForCompaniesRequest, opts ...grpc.CallOption) (*InitOwnerRoleForCompaniesResponse, error) {
	out := new(InitOwnerRoleForCompaniesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.permission.v1.PermissionService/InitOwnerRoleForCompanies", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) InitEnterpriseRole(ctx context.Context, in *InitEnterpriseRoleRequest, opts ...grpc.CallOption) (*InitEnterpriseRoleResponse, error) {
	out := new(InitEnterpriseRoleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.permission.v1.PermissionService/InitEnterpriseRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) CopyPermissions(ctx context.Context, in *CopyPermissionsRequest, opts ...grpc.CallOption) (*CopyPermissionsResponse, error) {
	out := new(CopyPermissionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.permission.v1.PermissionService/CopyPermissions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) RetainRolePermissions(ctx context.Context, in *RetainRolePermissionsRequest, opts ...grpc.CallOption) (*RetainRolePermissionsResponse, error) {
	out := new(RetainRolePermissionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.permission.v1.PermissionService/RetainRolePermissions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PermissionServiceServer is the server API for PermissionService service.
// All implementations must embed UnimplementedPermissionServiceServer
// for forward compatibility
type PermissionServiceServer interface {
	// get role list
	GetRoleList(context.Context, *GetRoleListRequest) (*GetRoleListResponse, error)
	// get role detail
	GetRoleDetail(context.Context, *GetRoleDetailRequest) (*GetRoleDetailResponse, error)
	// list role details
	ListRoleDetails(context.Context, *ListRoleDetailsRequest) (*ListRoleDetailsResponse, error)
	// create role
	CreateRole(context.Context, *CreateRoleRequest) (*CreateRoleResponse, error)
	// update role
	UpdateRole(context.Context, *UpdateRoleRequest) (*UpdateRoleResponse, error)
	// delete role
	DeleteRole(context.Context, *DeleteRoleRequest) (*DeleteRoleResponse, error)
	// duplicate role
	DuplicateRole(context.Context, *DuplicateRoleRequest) (*DuplicateRoleResponse, error)
	// edit permissions
	EditPermissions(context.Context, *EditPermissionsRequest) (*EditPermissionsResponse, error)
	// check permission
	CheckPermission(context.Context, *CheckPermissionRequest) (*CheckPermissionResponse, error)
	// init role
	InitRole(context.Context, *InitRoleRequest) (*InitRoleResponse, error)
	// permission mapping
	PermissionMapping(context.Context, *PermissionMappingRequest) (*PermissionMappingResponse, error)
	// get owner permission
	GetOwnerPermission(context.Context, *GetOwnerPermissionRequest) (*GetOwnerPermissionResponse, error)
	// Init owner role for companies
	InitOwnerRoleForCompanies(context.Context, *InitOwnerRoleForCompaniesRequest) (*InitOwnerRoleForCompaniesResponse, error)
	// init enterprise role
	InitEnterpriseRole(context.Context, *InitEnterpriseRoleRequest) (*InitEnterpriseRoleResponse, error)
	// copy permissions from one role to another
	CopyPermissions(context.Context, *CopyPermissionsRequest) (*CopyPermissionsResponse, error)
	// Remove any permissions not in the given list
	RetainRolePermissions(context.Context, *RetainRolePermissionsRequest) (*RetainRolePermissionsResponse, error)
	mustEmbedUnimplementedPermissionServiceServer()
}

// UnimplementedPermissionServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPermissionServiceServer struct {
}

func (UnimplementedPermissionServiceServer) GetRoleList(context.Context, *GetRoleListRequest) (*GetRoleListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRoleList not implemented")
}
func (UnimplementedPermissionServiceServer) GetRoleDetail(context.Context, *GetRoleDetailRequest) (*GetRoleDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRoleDetail not implemented")
}
func (UnimplementedPermissionServiceServer) ListRoleDetails(context.Context, *ListRoleDetailsRequest) (*ListRoleDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRoleDetails not implemented")
}
func (UnimplementedPermissionServiceServer) CreateRole(context.Context, *CreateRoleRequest) (*CreateRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRole not implemented")
}
func (UnimplementedPermissionServiceServer) UpdateRole(context.Context, *UpdateRoleRequest) (*UpdateRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRole not implemented")
}
func (UnimplementedPermissionServiceServer) DeleteRole(context.Context, *DeleteRoleRequest) (*DeleteRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRole not implemented")
}
func (UnimplementedPermissionServiceServer) DuplicateRole(context.Context, *DuplicateRoleRequest) (*DuplicateRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DuplicateRole not implemented")
}
func (UnimplementedPermissionServiceServer) EditPermissions(context.Context, *EditPermissionsRequest) (*EditPermissionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditPermissions not implemented")
}
func (UnimplementedPermissionServiceServer) CheckPermission(context.Context, *CheckPermissionRequest) (*CheckPermissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPermission not implemented")
}
func (UnimplementedPermissionServiceServer) InitRole(context.Context, *InitRoleRequest) (*InitRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitRole not implemented")
}
func (UnimplementedPermissionServiceServer) PermissionMapping(context.Context, *PermissionMappingRequest) (*PermissionMappingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PermissionMapping not implemented")
}
func (UnimplementedPermissionServiceServer) GetOwnerPermission(context.Context, *GetOwnerPermissionRequest) (*GetOwnerPermissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOwnerPermission not implemented")
}
func (UnimplementedPermissionServiceServer) InitOwnerRoleForCompanies(context.Context, *InitOwnerRoleForCompaniesRequest) (*InitOwnerRoleForCompaniesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitOwnerRoleForCompanies not implemented")
}
func (UnimplementedPermissionServiceServer) InitEnterpriseRole(context.Context, *InitEnterpriseRoleRequest) (*InitEnterpriseRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitEnterpriseRole not implemented")
}
func (UnimplementedPermissionServiceServer) CopyPermissions(context.Context, *CopyPermissionsRequest) (*CopyPermissionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyPermissions not implemented")
}
func (UnimplementedPermissionServiceServer) RetainRolePermissions(context.Context, *RetainRolePermissionsRequest) (*RetainRolePermissionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetainRolePermissions not implemented")
}
func (UnimplementedPermissionServiceServer) mustEmbedUnimplementedPermissionServiceServer() {}

// UnsafePermissionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PermissionServiceServer will
// result in compilation errors.
type UnsafePermissionServiceServer interface {
	mustEmbedUnimplementedPermissionServiceServer()
}

func RegisterPermissionServiceServer(s grpc.ServiceRegistrar, srv PermissionServiceServer) {
	s.RegisterService(&PermissionService_ServiceDesc, srv)
}

func _PermissionService_GetRoleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRoleListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).GetRoleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.permission.v1.PermissionService/GetRoleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).GetRoleList(ctx, req.(*GetRoleListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_GetRoleDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRoleDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).GetRoleDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.permission.v1.PermissionService/GetRoleDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).GetRoleDetail(ctx, req.(*GetRoleDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_ListRoleDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRoleDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).ListRoleDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.permission.v1.PermissionService/ListRoleDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).ListRoleDetails(ctx, req.(*ListRoleDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_CreateRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).CreateRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.permission.v1.PermissionService/CreateRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).CreateRole(ctx, req.(*CreateRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_UpdateRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).UpdateRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.permission.v1.PermissionService/UpdateRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).UpdateRole(ctx, req.(*UpdateRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_DeleteRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).DeleteRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.permission.v1.PermissionService/DeleteRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).DeleteRole(ctx, req.(*DeleteRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_DuplicateRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DuplicateRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).DuplicateRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.permission.v1.PermissionService/DuplicateRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).DuplicateRole(ctx, req.(*DuplicateRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_EditPermissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditPermissionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).EditPermissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.permission.v1.PermissionService/EditPermissions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).EditPermissions(ctx, req.(*EditPermissionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_CheckPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).CheckPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.permission.v1.PermissionService/CheckPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).CheckPermission(ctx, req.(*CheckPermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_InitRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).InitRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.permission.v1.PermissionService/InitRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).InitRole(ctx, req.(*InitRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_PermissionMapping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PermissionMappingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).PermissionMapping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.permission.v1.PermissionService/PermissionMapping",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).PermissionMapping(ctx, req.(*PermissionMappingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_GetOwnerPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOwnerPermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).GetOwnerPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.permission.v1.PermissionService/GetOwnerPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).GetOwnerPermission(ctx, req.(*GetOwnerPermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_InitOwnerRoleForCompanies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitOwnerRoleForCompaniesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).InitOwnerRoleForCompanies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.permission.v1.PermissionService/InitOwnerRoleForCompanies",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).InitOwnerRoleForCompanies(ctx, req.(*InitOwnerRoleForCompaniesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_InitEnterpriseRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitEnterpriseRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).InitEnterpriseRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.permission.v1.PermissionService/InitEnterpriseRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).InitEnterpriseRole(ctx, req.(*InitEnterpriseRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_CopyPermissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyPermissionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).CopyPermissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.permission.v1.PermissionService/CopyPermissions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).CopyPermissions(ctx, req.(*CopyPermissionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_RetainRolePermissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetainRolePermissionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).RetainRolePermissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.permission.v1.PermissionService/RetainRolePermissions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).RetainRolePermissions(ctx, req.(*RetainRolePermissionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PermissionService_ServiceDesc is the grpc.ServiceDesc for PermissionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PermissionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.permission.v1.PermissionService",
	HandlerType: (*PermissionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRoleList",
			Handler:    _PermissionService_GetRoleList_Handler,
		},
		{
			MethodName: "GetRoleDetail",
			Handler:    _PermissionService_GetRoleDetail_Handler,
		},
		{
			MethodName: "ListRoleDetails",
			Handler:    _PermissionService_ListRoleDetails_Handler,
		},
		{
			MethodName: "CreateRole",
			Handler:    _PermissionService_CreateRole_Handler,
		},
		{
			MethodName: "UpdateRole",
			Handler:    _PermissionService_UpdateRole_Handler,
		},
		{
			MethodName: "DeleteRole",
			Handler:    _PermissionService_DeleteRole_Handler,
		},
		{
			MethodName: "DuplicateRole",
			Handler:    _PermissionService_DuplicateRole_Handler,
		},
		{
			MethodName: "EditPermissions",
			Handler:    _PermissionService_EditPermissions_Handler,
		},
		{
			MethodName: "CheckPermission",
			Handler:    _PermissionService_CheckPermission_Handler,
		},
		{
			MethodName: "InitRole",
			Handler:    _PermissionService_InitRole_Handler,
		},
		{
			MethodName: "PermissionMapping",
			Handler:    _PermissionService_PermissionMapping_Handler,
		},
		{
			MethodName: "GetOwnerPermission",
			Handler:    _PermissionService_GetOwnerPermission_Handler,
		},
		{
			MethodName: "InitOwnerRoleForCompanies",
			Handler:    _PermissionService_InitOwnerRoleForCompanies_Handler,
		},
		{
			MethodName: "InitEnterpriseRole",
			Handler:    _PermissionService_InitEnterpriseRole_Handler,
		},
		{
			MethodName: "CopyPermissions",
			Handler:    _PermissionService_CopyPermissions_Handler,
		},
		{
			MethodName: "RetainRolePermissions",
			Handler:    _PermissionService_RetainRolePermissions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/permission/v1/permission_service.proto",
}
