// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/account/v1/account_association_service.proto

package accountsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get account association request
type GetAccountAssociationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// identifier
	//
	// Types that are assignable to Identifier:
	//
	//	*GetAccountAssociationRequest_AccountAndPlatform
	//	*GetAccountAssociationRequest_PlatformAccount
	Identifier isGetAccountAssociationRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *GetAccountAssociationRequest) Reset() {
	*x = GetAccountAssociationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_account_association_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountAssociationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountAssociationRequest) ProtoMessage() {}

func (x *GetAccountAssociationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_account_association_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountAssociationRequest.ProtoReflect.Descriptor instead.
func (*GetAccountAssociationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_account_association_service_proto_rawDescGZIP(), []int{0}
}

func (m *GetAccountAssociationRequest) GetIdentifier() isGetAccountAssociationRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetAccountAssociationRequest) GetAccountAndPlatform() *v1.AccountAndPlatformQueryDef {
	if x, ok := x.GetIdentifier().(*GetAccountAssociationRequest_AccountAndPlatform); ok {
		return x.AccountAndPlatform
	}
	return nil
}

func (x *GetAccountAssociationRequest) GetPlatformAccount() *v1.PlatformAccountQueryDef {
	if x, ok := x.GetIdentifier().(*GetAccountAssociationRequest_PlatformAccount); ok {
		return x.PlatformAccount
	}
	return nil
}

type isGetAccountAssociationRequest_Identifier interface {
	isGetAccountAssociationRequest_Identifier()
}

type GetAccountAssociationRequest_AccountAndPlatform struct {
	// by account and platform
	AccountAndPlatform *v1.AccountAndPlatformQueryDef `protobuf:"bytes,2,opt,name=account_and_platform,json=accountAndPlatform,proto3,oneof"`
}

type GetAccountAssociationRequest_PlatformAccount struct {
	// by platform account
	PlatformAccount *v1.PlatformAccountQueryDef `protobuf:"bytes,3,opt,name=platform_account,json=platformAccount,proto3,oneof"`
}

func (*GetAccountAssociationRequest_AccountAndPlatform) isGetAccountAssociationRequest_Identifier() {}

func (*GetAccountAssociationRequest_PlatformAccount) isGetAccountAssociationRequest_Identifier() {}

// get account association response
type GetAccountAssociationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// an account association, may not exist
	Association *v1.AccountAssociationModel `protobuf:"bytes,1,opt,name=association,proto3,oneof" json:"association,omitempty"`
}

func (x *GetAccountAssociationResponse) Reset() {
	*x = GetAccountAssociationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_account_association_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountAssociationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountAssociationResponse) ProtoMessage() {}

func (x *GetAccountAssociationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_account_association_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountAssociationResponse.ProtoReflect.Descriptor instead.
func (*GetAccountAssociationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_account_association_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetAccountAssociationResponse) GetAssociation() *v1.AccountAssociationModel {
	if x != nil {
		return x.Association
	}
	return nil
}

// list account association request
type ListAccountAssociationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id
	AccountId int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// platform, optional
	Platform *v1.AccountAssociationPlatform `protobuf:"varint,2,opt,name=platform,proto3,enum=moego.models.account.v1.AccountAssociationPlatform,oneof" json:"platform,omitempty"`
}

func (x *ListAccountAssociationRequest) Reset() {
	*x = ListAccountAssociationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_account_association_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAccountAssociationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccountAssociationRequest) ProtoMessage() {}

func (x *ListAccountAssociationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_account_association_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccountAssociationRequest.ProtoReflect.Descriptor instead.
func (*ListAccountAssociationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_account_association_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListAccountAssociationRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *ListAccountAssociationRequest) GetPlatform() v1.AccountAssociationPlatform {
	if x != nil && x.Platform != nil {
		return *x.Platform
	}
	return v1.AccountAssociationPlatform(0)
}

// list account association response
type ListAccountAssociationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account associations
	Associations []*v1.AccountAssociationModel `protobuf:"bytes,1,rep,name=associations,proto3" json:"associations,omitempty"`
}

func (x *ListAccountAssociationResponse) Reset() {
	*x = ListAccountAssociationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_account_association_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAccountAssociationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccountAssociationResponse) ProtoMessage() {}

func (x *ListAccountAssociationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_account_association_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccountAssociationResponse.ProtoReflect.Descriptor instead.
func (*ListAccountAssociationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_account_association_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListAccountAssociationResponse) GetAssociations() []*v1.AccountAssociationModel {
	if x != nil {
		return x.Associations
	}
	return nil
}

// add account association request
type AddAccountAssociationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// association
	Association *v1.AccountAssociationCreateDef `protobuf:"bytes,1,opt,name=association,proto3" json:"association,omitempty"`
}

func (x *AddAccountAssociationRequest) Reset() {
	*x = AddAccountAssociationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_account_association_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddAccountAssociationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAccountAssociationRequest) ProtoMessage() {}

func (x *AddAccountAssociationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_account_association_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAccountAssociationRequest.ProtoReflect.Descriptor instead.
func (*AddAccountAssociationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_account_association_service_proto_rawDescGZIP(), []int{4}
}

func (x *AddAccountAssociationRequest) GetAssociation() *v1.AccountAssociationCreateDef {
	if x != nil {
		return x.Association
	}
	return nil
}

// add account association response
type AddAccountAssociationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddAccountAssociationResponse) Reset() {
	*x = AddAccountAssociationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_account_association_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddAccountAssociationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAccountAssociationResponse) ProtoMessage() {}

func (x *AddAccountAssociationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_account_association_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAccountAssociationResponse.ProtoReflect.Descriptor instead.
func (*AddAccountAssociationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_account_association_service_proto_rawDescGZIP(), []int{5}
}

// reset account association request
type ResetAccountAssociationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// association
	Association *v1.AccountAssociationCreateDef `protobuf:"bytes,1,opt,name=association,proto3" json:"association,omitempty"`
}

func (x *ResetAccountAssociationRequest) Reset() {
	*x = ResetAccountAssociationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_account_association_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetAccountAssociationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetAccountAssociationRequest) ProtoMessage() {}

func (x *ResetAccountAssociationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_account_association_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetAccountAssociationRequest.ProtoReflect.Descriptor instead.
func (*ResetAccountAssociationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_account_association_service_proto_rawDescGZIP(), []int{6}
}

func (x *ResetAccountAssociationRequest) GetAssociation() *v1.AccountAssociationCreateDef {
	if x != nil {
		return x.Association
	}
	return nil
}

// reset account association response
type ResetAccountAssociationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ResetAccountAssociationResponse) Reset() {
	*x = ResetAccountAssociationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_account_association_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetAccountAssociationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetAccountAssociationResponse) ProtoMessage() {}

func (x *ResetAccountAssociationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_account_association_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetAccountAssociationResponse.ProtoReflect.Descriptor instead.
func (*ResetAccountAssociationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_account_association_service_proto_rawDescGZIP(), []int{7}
}

var File_moego_service_account_v1_account_association_service_proto protoreflect.FileDescriptor

var file_moego_service_account_v1_account_association_service_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf9, 0x01, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x67, 0x0a, 0x14, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6e, 0x64, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x65, 0x66, 0x48, 0x00,
	0x52, 0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6e, 0x64, 0x50, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x12, 0x5d, 0x0a, 0x10, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x65, 0x66,
	0x48, 0x00, 0x52, 0x0f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x11, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x88, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41,
	0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48,
	0x00, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01,
	0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0xb6, 0x01, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x08, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41,
	0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a,
	0x09, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0x76, 0x0a, 0x1e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x0c,
	0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x22, 0x80, 0x01, 0x0a, 0x1c, 0x41, 0x64, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x60, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x1f, 0x0a, 0x1d, 0x41, 0x64, 0x64, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x1e, 0x52, 0x65, 0x73, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x60, 0x0a, 0x0b, 0x61, 0x73, 0x73,
	0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b,
	0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x21, 0x0a, 0x1f, 0x52,
	0x65, 0x73, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xd0,
	0x04, 0x0a, 0x19, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x88, 0x01, 0x0a,
	0x15, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8b, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73,
	0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x8e, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x73, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x80, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_account_v1_account_association_service_proto_rawDescOnce sync.Once
	file_moego_service_account_v1_account_association_service_proto_rawDescData = file_moego_service_account_v1_account_association_service_proto_rawDesc
)

func file_moego_service_account_v1_account_association_service_proto_rawDescGZIP() []byte {
	file_moego_service_account_v1_account_association_service_proto_rawDescOnce.Do(func() {
		file_moego_service_account_v1_account_association_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_account_v1_account_association_service_proto_rawDescData)
	})
	return file_moego_service_account_v1_account_association_service_proto_rawDescData
}

var file_moego_service_account_v1_account_association_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_service_account_v1_account_association_service_proto_goTypes = []interface{}{
	(*GetAccountAssociationRequest)(nil),    // 0: moego.service.account.v1.GetAccountAssociationRequest
	(*GetAccountAssociationResponse)(nil),   // 1: moego.service.account.v1.GetAccountAssociationResponse
	(*ListAccountAssociationRequest)(nil),   // 2: moego.service.account.v1.ListAccountAssociationRequest
	(*ListAccountAssociationResponse)(nil),  // 3: moego.service.account.v1.ListAccountAssociationResponse
	(*AddAccountAssociationRequest)(nil),    // 4: moego.service.account.v1.AddAccountAssociationRequest
	(*AddAccountAssociationResponse)(nil),   // 5: moego.service.account.v1.AddAccountAssociationResponse
	(*ResetAccountAssociationRequest)(nil),  // 6: moego.service.account.v1.ResetAccountAssociationRequest
	(*ResetAccountAssociationResponse)(nil), // 7: moego.service.account.v1.ResetAccountAssociationResponse
	(*v1.AccountAndPlatformQueryDef)(nil),   // 8: moego.models.account.v1.AccountAndPlatformQueryDef
	(*v1.PlatformAccountQueryDef)(nil),      // 9: moego.models.account.v1.PlatformAccountQueryDef
	(*v1.AccountAssociationModel)(nil),      // 10: moego.models.account.v1.AccountAssociationModel
	(v1.AccountAssociationPlatform)(0),      // 11: moego.models.account.v1.AccountAssociationPlatform
	(*v1.AccountAssociationCreateDef)(nil),  // 12: moego.models.account.v1.AccountAssociationCreateDef
}
var file_moego_service_account_v1_account_association_service_proto_depIdxs = []int32{
	8,  // 0: moego.service.account.v1.GetAccountAssociationRequest.account_and_platform:type_name -> moego.models.account.v1.AccountAndPlatformQueryDef
	9,  // 1: moego.service.account.v1.GetAccountAssociationRequest.platform_account:type_name -> moego.models.account.v1.PlatformAccountQueryDef
	10, // 2: moego.service.account.v1.GetAccountAssociationResponse.association:type_name -> moego.models.account.v1.AccountAssociationModel
	11, // 3: moego.service.account.v1.ListAccountAssociationRequest.platform:type_name -> moego.models.account.v1.AccountAssociationPlatform
	10, // 4: moego.service.account.v1.ListAccountAssociationResponse.associations:type_name -> moego.models.account.v1.AccountAssociationModel
	12, // 5: moego.service.account.v1.AddAccountAssociationRequest.association:type_name -> moego.models.account.v1.AccountAssociationCreateDef
	12, // 6: moego.service.account.v1.ResetAccountAssociationRequest.association:type_name -> moego.models.account.v1.AccountAssociationCreateDef
	0,  // 7: moego.service.account.v1.AccountAssociationService.GetAccountAssociation:input_type -> moego.service.account.v1.GetAccountAssociationRequest
	2,  // 8: moego.service.account.v1.AccountAssociationService.ListAccountAssociation:input_type -> moego.service.account.v1.ListAccountAssociationRequest
	4,  // 9: moego.service.account.v1.AccountAssociationService.AddAccountAssociation:input_type -> moego.service.account.v1.AddAccountAssociationRequest
	6,  // 10: moego.service.account.v1.AccountAssociationService.ResetAccountAssociation:input_type -> moego.service.account.v1.ResetAccountAssociationRequest
	1,  // 11: moego.service.account.v1.AccountAssociationService.GetAccountAssociation:output_type -> moego.service.account.v1.GetAccountAssociationResponse
	3,  // 12: moego.service.account.v1.AccountAssociationService.ListAccountAssociation:output_type -> moego.service.account.v1.ListAccountAssociationResponse
	5,  // 13: moego.service.account.v1.AccountAssociationService.AddAccountAssociation:output_type -> moego.service.account.v1.AddAccountAssociationResponse
	7,  // 14: moego.service.account.v1.AccountAssociationService.ResetAccountAssociation:output_type -> moego.service.account.v1.ResetAccountAssociationResponse
	11, // [11:15] is the sub-list for method output_type
	7,  // [7:11] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_moego_service_account_v1_account_association_service_proto_init() }
func file_moego_service_account_v1_account_association_service_proto_init() {
	if File_moego_service_account_v1_account_association_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_account_v1_account_association_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountAssociationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_account_association_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountAssociationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_account_association_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAccountAssociationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_account_association_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAccountAssociationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_account_association_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddAccountAssociationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_account_association_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddAccountAssociationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_account_association_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetAccountAssociationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_account_association_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetAccountAssociationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_account_v1_account_association_service_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*GetAccountAssociationRequest_AccountAndPlatform)(nil),
		(*GetAccountAssociationRequest_PlatformAccount)(nil),
	}
	file_moego_service_account_v1_account_association_service_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_service_account_v1_account_association_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_account_v1_account_association_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_account_v1_account_association_service_proto_goTypes,
		DependencyIndexes: file_moego_service_account_v1_account_association_service_proto_depIdxs,
		MessageInfos:      file_moego_service_account_v1_account_association_service_proto_msgTypes,
	}.Build()
	File_moego_service_account_v1_account_association_service_proto = out.File
	file_moego_service_account_v1_account_association_service_proto_rawDesc = nil
	file_moego_service_account_v1_account_association_service_proto_goTypes = nil
	file_moego_service_account_v1_account_association_service_proto_depIdxs = nil
}
