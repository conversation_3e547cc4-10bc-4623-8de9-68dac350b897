// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/order/v1/split_tips_service.proto

package ordersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SplitTipsServiceClient is the client API for SplitTipsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SplitTipsServiceClient interface {
	// get split tips info
	GetSplitTipsRecord(ctx context.Context, in *GetSplitTipsInput, opts ...grpc.CallOption) (*GetSplitTipsOutput, error)
	// get split tips info list
	GetSplitTipsListRecord(ctx context.Context, in *GetSplitTipsListInput, opts ...grpc.CallOption) (*GetSplitTipsListOutput, error)
	// save split tips
	SaveSplitTipsRecord(ctx context.Context, in *SaveSplitTipsInput, opts ...grpc.CallOption) (*SaveSplitTipsOutput, error)
}

type splitTipsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSplitTipsServiceClient(cc grpc.ClientConnInterface) SplitTipsServiceClient {
	return &splitTipsServiceClient{cc}
}

func (c *splitTipsServiceClient) GetSplitTipsRecord(ctx context.Context, in *GetSplitTipsInput, opts ...grpc.CallOption) (*GetSplitTipsOutput, error) {
	out := new(GetSplitTipsOutput)
	err := c.cc.Invoke(ctx, "/moego.service.order.v1.SplitTipsService/GetSplitTipsRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *splitTipsServiceClient) GetSplitTipsListRecord(ctx context.Context, in *GetSplitTipsListInput, opts ...grpc.CallOption) (*GetSplitTipsListOutput, error) {
	out := new(GetSplitTipsListOutput)
	err := c.cc.Invoke(ctx, "/moego.service.order.v1.SplitTipsService/GetSplitTipsListRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *splitTipsServiceClient) SaveSplitTipsRecord(ctx context.Context, in *SaveSplitTipsInput, opts ...grpc.CallOption) (*SaveSplitTipsOutput, error) {
	out := new(SaveSplitTipsOutput)
	err := c.cc.Invoke(ctx, "/moego.service.order.v1.SplitTipsService/SaveSplitTipsRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SplitTipsServiceServer is the server API for SplitTipsService service.
// All implementations must embed UnimplementedSplitTipsServiceServer
// for forward compatibility
type SplitTipsServiceServer interface {
	// get split tips info
	GetSplitTipsRecord(context.Context, *GetSplitTipsInput) (*GetSplitTipsOutput, error)
	// get split tips info list
	GetSplitTipsListRecord(context.Context, *GetSplitTipsListInput) (*GetSplitTipsListOutput, error)
	// save split tips
	SaveSplitTipsRecord(context.Context, *SaveSplitTipsInput) (*SaveSplitTipsOutput, error)
	mustEmbedUnimplementedSplitTipsServiceServer()
}

// UnimplementedSplitTipsServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSplitTipsServiceServer struct {
}

func (UnimplementedSplitTipsServiceServer) GetSplitTipsRecord(context.Context, *GetSplitTipsInput) (*GetSplitTipsOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSplitTipsRecord not implemented")
}
func (UnimplementedSplitTipsServiceServer) GetSplitTipsListRecord(context.Context, *GetSplitTipsListInput) (*GetSplitTipsListOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSplitTipsListRecord not implemented")
}
func (UnimplementedSplitTipsServiceServer) SaveSplitTipsRecord(context.Context, *SaveSplitTipsInput) (*SaveSplitTipsOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveSplitTipsRecord not implemented")
}
func (UnimplementedSplitTipsServiceServer) mustEmbedUnimplementedSplitTipsServiceServer() {}

// UnsafeSplitTipsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SplitTipsServiceServer will
// result in compilation errors.
type UnsafeSplitTipsServiceServer interface {
	mustEmbedUnimplementedSplitTipsServiceServer()
}

func RegisterSplitTipsServiceServer(s grpc.ServiceRegistrar, srv SplitTipsServiceServer) {
	s.RegisterService(&SplitTipsService_ServiceDesc, srv)
}

func _SplitTipsService_GetSplitTipsRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSplitTipsInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SplitTipsServiceServer).GetSplitTipsRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v1.SplitTipsService/GetSplitTipsRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SplitTipsServiceServer).GetSplitTipsRecord(ctx, req.(*GetSplitTipsInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _SplitTipsService_GetSplitTipsListRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSplitTipsListInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SplitTipsServiceServer).GetSplitTipsListRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v1.SplitTipsService/GetSplitTipsListRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SplitTipsServiceServer).GetSplitTipsListRecord(ctx, req.(*GetSplitTipsListInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _SplitTipsService_SaveSplitTipsRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveSplitTipsInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SplitTipsServiceServer).SaveSplitTipsRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v1.SplitTipsService/SaveSplitTipsRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SplitTipsServiceServer).SaveSplitTipsRecord(ctx, req.(*SaveSplitTipsInput))
	}
	return interceptor(ctx, in, info, handler)
}

// SplitTipsService_ServiceDesc is the grpc.ServiceDesc for SplitTipsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SplitTipsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.order.v1.SplitTipsService",
	HandlerType: (*SplitTipsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSplitTipsRecord",
			Handler:    _SplitTipsService_GetSplitTipsRecord_Handler,
		},
		{
			MethodName: "GetSplitTipsListRecord",
			Handler:    _SplitTipsService_GetSplitTipsListRecord_Handler,
		},
		{
			MethodName: "SaveSplitTipsRecord",
			Handler:    _SplitTipsService_SaveSplitTipsRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/order/v1/split_tips_service.proto",
}
