// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/order/v1/customer_order_service.proto

package ordersvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// *
// Get customer recent items request params
type GetCustomerRecentItemsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// type list
	Types []string `protobuf:"bytes,3,rep,name=types,proto3" json:"types,omitempty"`
	// object id, for only these object
	ObjectIds []int64 `protobuf:"varint,4,rep,packed,name=object_ids,json=objectIds,proto3" json:"object_ids,omitempty"`
	// query from time
	SinceTime *int64 `protobuf:"varint,5,opt,name=since_time,json=sinceTime,proto3,oneof" json:"since_time,omitempty"`
	// limit count
	Count *int32 `protobuf:"varint,6,opt,name=count,proto3,oneof" json:"count,omitempty"`
}

func (x *GetCustomerRecentItemsRequest) Reset() {
	*x = GetCustomerRecentItemsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_customer_order_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerRecentItemsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerRecentItemsRequest) ProtoMessage() {}

func (x *GetCustomerRecentItemsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_customer_order_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerRecentItemsRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerRecentItemsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_customer_order_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetCustomerRecentItemsRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *GetCustomerRecentItemsRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetCustomerRecentItemsRequest) GetTypes() []string {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *GetCustomerRecentItemsRequest) GetObjectIds() []int64 {
	if x != nil {
		return x.ObjectIds
	}
	return nil
}

func (x *GetCustomerRecentItemsRequest) GetSinceTime() int64 {
	if x != nil && x.SinceTime != nil {
		return *x.SinceTime
	}
	return 0
}

func (x *GetCustomerRecentItemsRequest) GetCount() int32 {
	if x != nil && x.Count != nil {
		return *x.Count
	}
	return 0
}

// *
// Get customer recent orders request params
type GetCustomerRecentOrdersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// type list
	Types []string `protobuf:"bytes,3,rep,name=types,proto3" json:"types,omitempty"`
	// object id, for only these object
	ObjectIds []int64 `protobuf:"varint,4,rep,packed,name=object_ids,json=objectIds,proto3" json:"object_ids,omitempty"`
	// status
	Status []int32 `protobuf:"varint,5,rep,packed,name=status,proto3" json:"status,omitempty"`
	// query from time
	SinceTime *int64 `protobuf:"varint,6,opt,name=since_time,json=sinceTime,proto3,oneof" json:"since_time,omitempty"`
	// limit count
	Count *int32 `protobuf:"varint,7,opt,name=count,proto3,oneof" json:"count,omitempty"`
}

func (x *GetCustomerRecentOrdersRequest) Reset() {
	*x = GetCustomerRecentOrdersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_customer_order_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerRecentOrdersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerRecentOrdersRequest) ProtoMessage() {}

func (x *GetCustomerRecentOrdersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_customer_order_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerRecentOrdersRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerRecentOrdersRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_customer_order_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetCustomerRecentOrdersRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *GetCustomerRecentOrdersRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetCustomerRecentOrdersRequest) GetTypes() []string {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *GetCustomerRecentOrdersRequest) GetObjectIds() []int64 {
	if x != nil {
		return x.ObjectIds
	}
	return nil
}

func (x *GetCustomerRecentOrdersRequest) GetStatus() []int32 {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCustomerRecentOrdersRequest) GetSinceTime() int64 {
	if x != nil && x.SinceTime != nil {
		return *x.SinceTime
	}
	return 0
}

func (x *GetCustomerRecentOrdersRequest) GetCount() int32 {
	if x != nil && x.Count != nil {
		return *x.Count
	}
	return 0
}

// *
// Get customer payment summary request params
type GetCustomerPaymentSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId *int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// query customer ids
	CustomerId []int64 `protobuf:"varint,2,rep,packed,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// order source types
	SourceTypes []string `protobuf:"bytes,3,rep,name=source_types,json=sourceTypes,proto3" json:"source_types,omitempty"`
	// order status
	Status *int32 `protobuf:"varint,4,opt,name=status,proto3,oneof" json:"status,omitempty"`
	// order source id for query remain amount
	SourceId []int64 `protobuf:"varint,5,rep,packed,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
}

func (x *GetCustomerPaymentSummaryRequest) Reset() {
	*x = GetCustomerPaymentSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_customer_order_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerPaymentSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerPaymentSummaryRequest) ProtoMessage() {}

func (x *GetCustomerPaymentSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_customer_order_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerPaymentSummaryRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerPaymentSummaryRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_customer_order_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetCustomerPaymentSummaryRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetCustomerPaymentSummaryRequest) GetCustomerId() []int64 {
	if x != nil {
		return x.CustomerId
	}
	return nil
}

func (x *GetCustomerPaymentSummaryRequest) GetSourceTypes() []string {
	if x != nil {
		return x.SourceTypes
	}
	return nil
}

func (x *GetCustomerPaymentSummaryRequest) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *GetCustomerPaymentSummaryRequest) GetSourceId() []int64 {
	if x != nil {
		return x.SourceId
	}
	return nil
}

// *
// Customer order item
type CustomerOrderItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// object id
	ObjectId int64 `protobuf:"varint,1,opt,name=object_id,json=objectId,proto3" json:"object_id,omitempty"`
	// item type
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	// item name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// item unit price
	UnitPrice float64 `protobuf:"fixed64,4,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
	// total quantity
	Quantity int32 `protobuf:"varint,5,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,6,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// order id
	OrderId *int64 `protobuf:"varint,7,opt,name=order_id,json=orderId,proto3,oneof" json:"order_id,omitempty"`
	// order item id
	OrderItemId *int64 `protobuf:"varint,8,opt,name=order_item_id,json=orderItemId,proto3,oneof" json:"order_item_id,omitempty"`
	// quantity of using package
	PurchasedQuantity *int32 `protobuf:"varint,9,opt,name=purchased_quantity,json=purchasedQuantity,proto3,oneof" json:"purchased_quantity,omitempty"`
	// purchased time
	PurchasedTime *int64 `protobuf:"varint,10,opt,name=purchased_time,json=purchasedTime,proto3,oneof" json:"purchased_time,omitempty"`
	// item description
	Description *string `protobuf:"bytes,11,opt,name=description,proto3,oneof" json:"description,omitempty"`
}

func (x *CustomerOrderItem) Reset() {
	*x = CustomerOrderItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_customer_order_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerOrderItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerOrderItem) ProtoMessage() {}

func (x *CustomerOrderItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_customer_order_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerOrderItem.ProtoReflect.Descriptor instead.
func (*CustomerOrderItem) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_customer_order_service_proto_rawDescGZIP(), []int{3}
}

func (x *CustomerOrderItem) GetObjectId() int64 {
	if x != nil {
		return x.ObjectId
	}
	return 0
}

func (x *CustomerOrderItem) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CustomerOrderItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerOrderItem) GetUnitPrice() float64 {
	if x != nil {
		return x.UnitPrice
	}
	return 0
}

func (x *CustomerOrderItem) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *CustomerOrderItem) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *CustomerOrderItem) GetOrderId() int64 {
	if x != nil && x.OrderId != nil {
		return *x.OrderId
	}
	return 0
}

func (x *CustomerOrderItem) GetOrderItemId() int64 {
	if x != nil && x.OrderItemId != nil {
		return *x.OrderItemId
	}
	return 0
}

func (x *CustomerOrderItem) GetPurchasedQuantity() int32 {
	if x != nil && x.PurchasedQuantity != nil {
		return *x.PurchasedQuantity
	}
	return 0
}

func (x *CustomerOrderItem) GetPurchasedTime() int64 {
	if x != nil && x.PurchasedTime != nil {
		return *x.PurchasedTime
	}
	return 0
}

func (x *CustomerOrderItem) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

// *
// Get customer recent items response: list
type GetCustomerRecentItemsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// item list
	Items []*CustomerOrderItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *GetCustomerRecentItemsResponse) Reset() {
	*x = GetCustomerRecentItemsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_customer_order_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerRecentItemsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerRecentItemsResponse) ProtoMessage() {}

func (x *GetCustomerRecentItemsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_customer_order_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerRecentItemsResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerRecentItemsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_customer_order_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetCustomerRecentItemsResponse) GetItems() []*CustomerOrderItem {
	if x != nil {
		return x.Items
	}
	return nil
}

// *
// Get customer recent orders response: list
type GetCustomerRecentOrdersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order list
	Orders []*v1.OrderModel `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
}

func (x *GetCustomerRecentOrdersResponse) Reset() {
	*x = GetCustomerRecentOrdersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_customer_order_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerRecentOrdersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerRecentOrdersResponse) ProtoMessage() {}

func (x *GetCustomerRecentOrdersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_customer_order_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerRecentOrdersResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerRecentOrdersResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_customer_order_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetCustomerRecentOrdersResponse) GetOrders() []*v1.OrderModel {
	if x != nil {
		return x.Orders
	}
	return nil
}

// *
// Customer payment summary item
type CustomerPaymentSummaryModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// total payment amount
	TotalPaymentAmount float64 `protobuf:"fixed64,2,opt,name=total_payment_amount,json=totalPaymentAmount,proto3" json:"total_payment_amount,omitempty"`
	// total paid amount
	TotalPaidAmount float64 `protobuf:"fixed64,3,opt,name=total_paid_amount,json=totalPaidAmount,proto3" json:"total_paid_amount,omitempty"`
	// total remain amount
	TotalRemainAmount float64 `protobuf:"fixed64,4,opt,name=total_remain_amount,json=totalRemainAmount,proto3" json:"total_remain_amount,omitempty"`
	// grooming id list for query remain amount (special query condition)
	SourceId []int64 `protobuf:"varint,5,rep,packed,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
}

func (x *CustomerPaymentSummaryModel) Reset() {
	*x = CustomerPaymentSummaryModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_customer_order_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerPaymentSummaryModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentSummaryModel) ProtoMessage() {}

func (x *CustomerPaymentSummaryModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_customer_order_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentSummaryModel.ProtoReflect.Descriptor instead.
func (*CustomerPaymentSummaryModel) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_customer_order_service_proto_rawDescGZIP(), []int{6}
}

func (x *CustomerPaymentSummaryModel) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CustomerPaymentSummaryModel) GetTotalPaymentAmount() float64 {
	if x != nil {
		return x.TotalPaymentAmount
	}
	return 0
}

func (x *CustomerPaymentSummaryModel) GetTotalPaidAmount() float64 {
	if x != nil {
		return x.TotalPaidAmount
	}
	return 0
}

func (x *CustomerPaymentSummaryModel) GetTotalRemainAmount() float64 {
	if x != nil {
		return x.TotalRemainAmount
	}
	return 0
}

func (x *CustomerPaymentSummaryModel) GetSourceId() []int64 {
	if x != nil {
		return x.SourceId
	}
	return nil
}

// *
// Get customer payment summary response: list
type GetCustomerPaymentSummaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// summary list
	PaymentSummaries []*CustomerPaymentSummaryModel `protobuf:"bytes,1,rep,name=payment_summaries,json=paymentSummaries,proto3" json:"payment_summaries,omitempty"`
}

func (x *GetCustomerPaymentSummaryResponse) Reset() {
	*x = GetCustomerPaymentSummaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_customer_order_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerPaymentSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerPaymentSummaryResponse) ProtoMessage() {}

func (x *GetCustomerPaymentSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_customer_order_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerPaymentSummaryResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerPaymentSummaryResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_customer_order_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetCustomerPaymentSummaryResponse) GetPaymentSummaries() []*CustomerPaymentSummaryModel {
	if x != nil {
		return x.PaymentSummaries
	}
	return nil
}

var File_moego_service_order_v1_customer_order_service_proto protoreflect.FileDescriptor

var file_moego_service_order_v1_customer_order_service_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x28, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x83, 0x02, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x6f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x73, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x09, 0x73, 0x69, 0x6e,
	0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x48, 0x02, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x9c, 0x02,
	0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x63,
	0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x24, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x09, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x09, 0x73, 0x69, 0x6e, 0x63,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x48, 0x02, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xe1, 0x01, 0x0a,
	0x20, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x49, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0xe9, 0x03, 0x0a, 0x11, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75,
	0x6e, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x09, 0x75, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75,
	0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x71, 0x75,
	0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52,
	0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x32, 0x0a, 0x12, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x71, 0x75, 0x61,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x48, 0x03, 0x52, 0x11, 0x70,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x48, 0x04, 0x52, 0x0d, 0x70,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x25, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x69, 0x64, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64,
	0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x70, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x61, 0x0a, 0x1e,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x63, 0x65, 0x6e,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f,
	0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22,
	0x5c, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x63, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x39, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x22, 0xe9, 0x01,
	0x0a, 0x1b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x30,
	0x0a, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x2a, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x50, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x13,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x22, 0x85, 0x01, 0x0a, 0x21, 0x47, 0x65,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x60, 0x0a, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x10, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65,
	0x73, 0x32, 0xc0, 0x03, 0x0a, 0x14, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x17, 0x47,
	0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x63, 0x65, 0x6e,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x63, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x90, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x7a, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x76, 0x63, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_order_v1_customer_order_service_proto_rawDescOnce sync.Once
	file_moego_service_order_v1_customer_order_service_proto_rawDescData = file_moego_service_order_v1_customer_order_service_proto_rawDesc
)

func file_moego_service_order_v1_customer_order_service_proto_rawDescGZIP() []byte {
	file_moego_service_order_v1_customer_order_service_proto_rawDescOnce.Do(func() {
		file_moego_service_order_v1_customer_order_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_order_v1_customer_order_service_proto_rawDescData)
	})
	return file_moego_service_order_v1_customer_order_service_proto_rawDescData
}

var file_moego_service_order_v1_customer_order_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_service_order_v1_customer_order_service_proto_goTypes = []interface{}{
	(*GetCustomerRecentItemsRequest)(nil),     // 0: moego.service.order.v1.GetCustomerRecentItemsRequest
	(*GetCustomerRecentOrdersRequest)(nil),    // 1: moego.service.order.v1.GetCustomerRecentOrdersRequest
	(*GetCustomerPaymentSummaryRequest)(nil),  // 2: moego.service.order.v1.GetCustomerPaymentSummaryRequest
	(*CustomerOrderItem)(nil),                 // 3: moego.service.order.v1.CustomerOrderItem
	(*GetCustomerRecentItemsResponse)(nil),    // 4: moego.service.order.v1.GetCustomerRecentItemsResponse
	(*GetCustomerRecentOrdersResponse)(nil),   // 5: moego.service.order.v1.GetCustomerRecentOrdersResponse
	(*CustomerPaymentSummaryModel)(nil),       // 6: moego.service.order.v1.CustomerPaymentSummaryModel
	(*GetCustomerPaymentSummaryResponse)(nil), // 7: moego.service.order.v1.GetCustomerPaymentSummaryResponse
	(*v1.OrderModel)(nil),                     // 8: moego.models.order.v1.OrderModel
}
var file_moego_service_order_v1_customer_order_service_proto_depIdxs = []int32{
	3, // 0: moego.service.order.v1.GetCustomerRecentItemsResponse.items:type_name -> moego.service.order.v1.CustomerOrderItem
	8, // 1: moego.service.order.v1.GetCustomerRecentOrdersResponse.orders:type_name -> moego.models.order.v1.OrderModel
	6, // 2: moego.service.order.v1.GetCustomerPaymentSummaryResponse.payment_summaries:type_name -> moego.service.order.v1.CustomerPaymentSummaryModel
	1, // 3: moego.service.order.v1.CustomerOrderService.GetCustomerRecentOrders:input_type -> moego.service.order.v1.GetCustomerRecentOrdersRequest
	0, // 4: moego.service.order.v1.CustomerOrderService.GetCustomerRecentItems:input_type -> moego.service.order.v1.GetCustomerRecentItemsRequest
	2, // 5: moego.service.order.v1.CustomerOrderService.GetCustomerPaymentSummary:input_type -> moego.service.order.v1.GetCustomerPaymentSummaryRequest
	5, // 6: moego.service.order.v1.CustomerOrderService.GetCustomerRecentOrders:output_type -> moego.service.order.v1.GetCustomerRecentOrdersResponse
	4, // 7: moego.service.order.v1.CustomerOrderService.GetCustomerRecentItems:output_type -> moego.service.order.v1.GetCustomerRecentItemsResponse
	7, // 8: moego.service.order.v1.CustomerOrderService.GetCustomerPaymentSummary:output_type -> moego.service.order.v1.GetCustomerPaymentSummaryResponse
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_service_order_v1_customer_order_service_proto_init() }
func file_moego_service_order_v1_customer_order_service_proto_init() {
	if File_moego_service_order_v1_customer_order_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_order_v1_customer_order_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerRecentItemsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_customer_order_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerRecentOrdersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_customer_order_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerPaymentSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_customer_order_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerOrderItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_customer_order_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerRecentItemsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_customer_order_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerRecentOrdersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_customer_order_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerPaymentSummaryModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_customer_order_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerPaymentSummaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_order_v1_customer_order_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_customer_order_service_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_customer_order_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_service_order_v1_customer_order_service_proto_msgTypes[3].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_order_v1_customer_order_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_order_v1_customer_order_service_proto_goTypes,
		DependencyIndexes: file_moego_service_order_v1_customer_order_service_proto_depIdxs,
		MessageInfos:      file_moego_service_order_v1_customer_order_service_proto_msgTypes,
	}.Build()
	File_moego_service_order_v1_customer_order_service_proto = out.File
	file_moego_service_order_v1_customer_order_service_proto_rawDesc = nil
	file_moego_service_order_v1_customer_order_service_proto_goTypes = nil
	file_moego_service_order_v1_customer_order_service_proto_depIdxs = nil
}
