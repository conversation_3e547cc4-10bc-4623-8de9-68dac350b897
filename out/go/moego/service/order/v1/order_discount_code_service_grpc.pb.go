// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/order/v1/order_discount_code_service.proto

package ordersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// OrderDiscountCodeServiceClient is the client API for OrderDiscountCodeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OrderDiscountCodeServiceClient interface {
	// get available discount code list
	GetAvailableDiscountList(ctx context.Context, in *GetAvailableDiscountListInput, opts ...grpc.CallOption) (*GetAvailableDiscountListOutput, error)
	// migrate discount code id
	MigrateDiscountCodeId(ctx context.Context, in *MigrateDiscountCodeInput, opts ...grpc.CallOption) (*MigrateDiscountCodeInputOutput, error)
}

type orderDiscountCodeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOrderDiscountCodeServiceClient(cc grpc.ClientConnInterface) OrderDiscountCodeServiceClient {
	return &orderDiscountCodeServiceClient{cc}
}

func (c *orderDiscountCodeServiceClient) GetAvailableDiscountList(ctx context.Context, in *GetAvailableDiscountListInput, opts ...grpc.CallOption) (*GetAvailableDiscountListOutput, error) {
	out := new(GetAvailableDiscountListOutput)
	err := c.cc.Invoke(ctx, "/moego.service.order.v1.OrderDiscountCodeService/GetAvailableDiscountList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderDiscountCodeServiceClient) MigrateDiscountCodeId(ctx context.Context, in *MigrateDiscountCodeInput, opts ...grpc.CallOption) (*MigrateDiscountCodeInputOutput, error) {
	out := new(MigrateDiscountCodeInputOutput)
	err := c.cc.Invoke(ctx, "/moego.service.order.v1.OrderDiscountCodeService/MigrateDiscountCodeId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrderDiscountCodeServiceServer is the server API for OrderDiscountCodeService service.
// All implementations must embed UnimplementedOrderDiscountCodeServiceServer
// for forward compatibility
type OrderDiscountCodeServiceServer interface {
	// get available discount code list
	GetAvailableDiscountList(context.Context, *GetAvailableDiscountListInput) (*GetAvailableDiscountListOutput, error)
	// migrate discount code id
	MigrateDiscountCodeId(context.Context, *MigrateDiscountCodeInput) (*MigrateDiscountCodeInputOutput, error)
	mustEmbedUnimplementedOrderDiscountCodeServiceServer()
}

// UnimplementedOrderDiscountCodeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedOrderDiscountCodeServiceServer struct {
}

func (UnimplementedOrderDiscountCodeServiceServer) GetAvailableDiscountList(context.Context, *GetAvailableDiscountListInput) (*GetAvailableDiscountListOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAvailableDiscountList not implemented")
}
func (UnimplementedOrderDiscountCodeServiceServer) MigrateDiscountCodeId(context.Context, *MigrateDiscountCodeInput) (*MigrateDiscountCodeInputOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MigrateDiscountCodeId not implemented")
}
func (UnimplementedOrderDiscountCodeServiceServer) mustEmbedUnimplementedOrderDiscountCodeServiceServer() {
}

// UnsafeOrderDiscountCodeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OrderDiscountCodeServiceServer will
// result in compilation errors.
type UnsafeOrderDiscountCodeServiceServer interface {
	mustEmbedUnimplementedOrderDiscountCodeServiceServer()
}

func RegisterOrderDiscountCodeServiceServer(s grpc.ServiceRegistrar, srv OrderDiscountCodeServiceServer) {
	s.RegisterService(&OrderDiscountCodeService_ServiceDesc, srv)
}

func _OrderDiscountCodeService_GetAvailableDiscountList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvailableDiscountListInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderDiscountCodeServiceServer).GetAvailableDiscountList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v1.OrderDiscountCodeService/GetAvailableDiscountList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderDiscountCodeServiceServer).GetAvailableDiscountList(ctx, req.(*GetAvailableDiscountListInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderDiscountCodeService_MigrateDiscountCodeId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MigrateDiscountCodeInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderDiscountCodeServiceServer).MigrateDiscountCodeId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v1.OrderDiscountCodeService/MigrateDiscountCodeId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderDiscountCodeServiceServer).MigrateDiscountCodeId(ctx, req.(*MigrateDiscountCodeInput))
	}
	return interceptor(ctx, in, info, handler)
}

// OrderDiscountCodeService_ServiceDesc is the grpc.ServiceDesc for OrderDiscountCodeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OrderDiscountCodeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.order.v1.OrderDiscountCodeService",
	HandlerType: (*OrderDiscountCodeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAvailableDiscountList",
			Handler:    _OrderDiscountCodeService_GetAvailableDiscountList_Handler,
		},
		{
			MethodName: "MigrateDiscountCodeId",
			Handler:    _OrderDiscountCodeService_MigrateDiscountCodeId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/order/v1/order_discount_code_service.proto",
}
