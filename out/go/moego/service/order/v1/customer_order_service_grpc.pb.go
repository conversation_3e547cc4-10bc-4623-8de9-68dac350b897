// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/order/v1/customer_order_service.proto

package ordersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CustomerOrderServiceClient is the client API for CustomerOrderService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CustomerOrderServiceClient interface {
	// get customer recent orders
	GetCustomerRecentOrders(ctx context.Context, in *GetCustomerRecentOrdersRequest, opts ...grpc.CallOption) (*GetCustomerRecentOrdersResponse, error)
	// get customer recent items
	GetCustomerRecentItems(ctx context.Context, in *GetCustomerRecentItemsRequest, opts ...grpc.CallOption) (*GetCustomerRecentItemsResponse, error)
	// get customer payment summary
	GetCustomerPaymentSummary(ctx context.Context, in *GetCustomerPaymentSummaryRequest, opts ...grpc.CallOption) (*GetCustomerPaymentSummaryResponse, error)
}

type customerOrderServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCustomerOrderServiceClient(cc grpc.ClientConnInterface) CustomerOrderServiceClient {
	return &customerOrderServiceClient{cc}
}

func (c *customerOrderServiceClient) GetCustomerRecentOrders(ctx context.Context, in *GetCustomerRecentOrdersRequest, opts ...grpc.CallOption) (*GetCustomerRecentOrdersResponse, error) {
	out := new(GetCustomerRecentOrdersResponse)
	err := c.cc.Invoke(ctx, "/moego.service.order.v1.CustomerOrderService/GetCustomerRecentOrders", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerOrderServiceClient) GetCustomerRecentItems(ctx context.Context, in *GetCustomerRecentItemsRequest, opts ...grpc.CallOption) (*GetCustomerRecentItemsResponse, error) {
	out := new(GetCustomerRecentItemsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.order.v1.CustomerOrderService/GetCustomerRecentItems", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerOrderServiceClient) GetCustomerPaymentSummary(ctx context.Context, in *GetCustomerPaymentSummaryRequest, opts ...grpc.CallOption) (*GetCustomerPaymentSummaryResponse, error) {
	out := new(GetCustomerPaymentSummaryResponse)
	err := c.cc.Invoke(ctx, "/moego.service.order.v1.CustomerOrderService/GetCustomerPaymentSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CustomerOrderServiceServer is the server API for CustomerOrderService service.
// All implementations must embed UnimplementedCustomerOrderServiceServer
// for forward compatibility
type CustomerOrderServiceServer interface {
	// get customer recent orders
	GetCustomerRecentOrders(context.Context, *GetCustomerRecentOrdersRequest) (*GetCustomerRecentOrdersResponse, error)
	// get customer recent items
	GetCustomerRecentItems(context.Context, *GetCustomerRecentItemsRequest) (*GetCustomerRecentItemsResponse, error)
	// get customer payment summary
	GetCustomerPaymentSummary(context.Context, *GetCustomerPaymentSummaryRequest) (*GetCustomerPaymentSummaryResponse, error)
	mustEmbedUnimplementedCustomerOrderServiceServer()
}

// UnimplementedCustomerOrderServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCustomerOrderServiceServer struct {
}

func (UnimplementedCustomerOrderServiceServer) GetCustomerRecentOrders(context.Context, *GetCustomerRecentOrdersRequest) (*GetCustomerRecentOrdersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerRecentOrders not implemented")
}
func (UnimplementedCustomerOrderServiceServer) GetCustomerRecentItems(context.Context, *GetCustomerRecentItemsRequest) (*GetCustomerRecentItemsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerRecentItems not implemented")
}
func (UnimplementedCustomerOrderServiceServer) GetCustomerPaymentSummary(context.Context, *GetCustomerPaymentSummaryRequest) (*GetCustomerPaymentSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerPaymentSummary not implemented")
}
func (UnimplementedCustomerOrderServiceServer) mustEmbedUnimplementedCustomerOrderServiceServer() {}

// UnsafeCustomerOrderServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CustomerOrderServiceServer will
// result in compilation errors.
type UnsafeCustomerOrderServiceServer interface {
	mustEmbedUnimplementedCustomerOrderServiceServer()
}

func RegisterCustomerOrderServiceServer(s grpc.ServiceRegistrar, srv CustomerOrderServiceServer) {
	s.RegisterService(&CustomerOrderService_ServiceDesc, srv)
}

func _CustomerOrderService_GetCustomerRecentOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerRecentOrdersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerOrderServiceServer).GetCustomerRecentOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v1.CustomerOrderService/GetCustomerRecentOrders",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerOrderServiceServer).GetCustomerRecentOrders(ctx, req.(*GetCustomerRecentOrdersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerOrderService_GetCustomerRecentItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerRecentItemsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerOrderServiceServer).GetCustomerRecentItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v1.CustomerOrderService/GetCustomerRecentItems",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerOrderServiceServer).GetCustomerRecentItems(ctx, req.(*GetCustomerRecentItemsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerOrderService_GetCustomerPaymentSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerPaymentSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerOrderServiceServer).GetCustomerPaymentSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v1.CustomerOrderService/GetCustomerPaymentSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerOrderServiceServer).GetCustomerPaymentSummary(ctx, req.(*GetCustomerPaymentSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CustomerOrderService_ServiceDesc is the grpc.ServiceDesc for CustomerOrderService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CustomerOrderService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.order.v1.CustomerOrderService",
	HandlerType: (*CustomerOrderServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCustomerRecentOrders",
			Handler:    _CustomerOrderService_GetCustomerRecentOrders_Handler,
		},
		{
			MethodName: "GetCustomerRecentItems",
			Handler:    _CustomerOrderService_GetCustomerRecentItems_Handler,
		},
		{
			MethodName: "GetCustomerPaymentSummary",
			Handler:    _CustomerOrderService_GetCustomerPaymentSummary_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/order/v1/customer_order_service.proto",
}
