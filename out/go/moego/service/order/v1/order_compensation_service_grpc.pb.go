// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/order/v1/order_compensation_service.proto

package ordersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// OrderCompensationServiceClient is the client API for OrderCompensationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OrderCompensationServiceClient interface {
	// get order id by business id and create time range
	GetIDByBusinessIDAndTimeRange(ctx context.Context, in *GetIDByBusinessIDAndTimeRangeRequest, opts ...grpc.CallOption) (*GetIDByBusinessIDAndTimeRangeResponse, error)
}

type orderCompensationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOrderCompensationServiceClient(cc grpc.ClientConnInterface) OrderCompensationServiceClient {
	return &orderCompensationServiceClient{cc}
}

func (c *orderCompensationServiceClient) GetIDByBusinessIDAndTimeRange(ctx context.Context, in *GetIDByBusinessIDAndTimeRangeRequest, opts ...grpc.CallOption) (*GetIDByBusinessIDAndTimeRangeResponse, error) {
	out := new(GetIDByBusinessIDAndTimeRangeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.order.v1.OrderCompensationService/GetIDByBusinessIDAndTimeRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrderCompensationServiceServer is the server API for OrderCompensationService service.
// All implementations must embed UnimplementedOrderCompensationServiceServer
// for forward compatibility
type OrderCompensationServiceServer interface {
	// get order id by business id and create time range
	GetIDByBusinessIDAndTimeRange(context.Context, *GetIDByBusinessIDAndTimeRangeRequest) (*GetIDByBusinessIDAndTimeRangeResponse, error)
	mustEmbedUnimplementedOrderCompensationServiceServer()
}

// UnimplementedOrderCompensationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedOrderCompensationServiceServer struct {
}

func (UnimplementedOrderCompensationServiceServer) GetIDByBusinessIDAndTimeRange(context.Context, *GetIDByBusinessIDAndTimeRangeRequest) (*GetIDByBusinessIDAndTimeRangeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIDByBusinessIDAndTimeRange not implemented")
}
func (UnimplementedOrderCompensationServiceServer) mustEmbedUnimplementedOrderCompensationServiceServer() {
}

// UnsafeOrderCompensationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OrderCompensationServiceServer will
// result in compilation errors.
type UnsafeOrderCompensationServiceServer interface {
	mustEmbedUnimplementedOrderCompensationServiceServer()
}

func RegisterOrderCompensationServiceServer(s grpc.ServiceRegistrar, srv OrderCompensationServiceServer) {
	s.RegisterService(&OrderCompensationService_ServiceDesc, srv)
}

func _OrderCompensationService_GetIDByBusinessIDAndTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIDByBusinessIDAndTimeRangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderCompensationServiceServer).GetIDByBusinessIDAndTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v1.OrderCompensationService/GetIDByBusinessIDAndTimeRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderCompensationServiceServer).GetIDByBusinessIDAndTimeRange(ctx, req.(*GetIDByBusinessIDAndTimeRangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OrderCompensationService_ServiceDesc is the grpc.ServiceDesc for OrderCompensationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OrderCompensationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.order.v1.OrderCompensationService",
	HandlerType: (*OrderCompensationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetIDByBusinessIDAndTimeRange",
			Handler:    _OrderCompensationService_GetIDByBusinessIDAndTimeRange_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/order/v1/order_compensation_service.proto",
}
