// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/order/v1/order_merge_service.proto

package ordersvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// order merge client request
type MergeCustomerOrderDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// merge relation
	MergeRelation *v1.BusinessCustomerMergeRelationDef `protobuf:"bytes,2,opt,name=merge_relation,json=mergeRelation,proto3" json:"merge_relation,omitempty"`
}

func (x *MergeCustomerOrderDataRequest) Reset() {
	*x = MergeCustomerOrderDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_merge_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeCustomerOrderDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeCustomerOrderDataRequest) ProtoMessage() {}

func (x *MergeCustomerOrderDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_merge_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeCustomerOrderDataRequest.ProtoReflect.Descriptor instead.
func (*MergeCustomerOrderDataRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_merge_service_proto_rawDescGZIP(), []int{0}
}

func (x *MergeCustomerOrderDataRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *MergeCustomerOrderDataRequest) GetMergeRelation() *v1.BusinessCustomerMergeRelationDef {
	if x != nil {
		return x.MergeRelation
	}
	return nil
}

// order merge client response
type MergeCustomerOrderDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *MergeCustomerOrderDataResponse) Reset() {
	*x = MergeCustomerOrderDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v1_order_merge_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MergeCustomerOrderDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MergeCustomerOrderDataResponse) ProtoMessage() {}

func (x *MergeCustomerOrderDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v1_order_merge_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MergeCustomerOrderDataResponse.ProtoReflect.Descriptor instead.
func (*MergeCustomerOrderDataResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v1_order_merge_service_proto_rawDescGZIP(), []int{1}
}

func (x *MergeCustomerOrderDataResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_moego_service_order_v1_order_merge_service_proto protoreflect.FileDescriptor

var file_moego_service_order_v1_order_merge_service_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6d,
	0x65, 0x72, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x16, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb3, 0x01, 0x0a, 0x1d, 0x4d, 0x65,
	0x72, 0x67, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x6a, 0x0a, 0x0e, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x4d, 0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66,
	0x52, 0x0d, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x3a, 0x0a, 0x1e, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x32, 0x9d, 0x01, 0x0a, 0x11,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x87, 0x01, 0x0a, 0x16, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x35, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x72,
	0x67, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x7a, 0x0a, 0x1e, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_order_v1_order_merge_service_proto_rawDescOnce sync.Once
	file_moego_service_order_v1_order_merge_service_proto_rawDescData = file_moego_service_order_v1_order_merge_service_proto_rawDesc
)

func file_moego_service_order_v1_order_merge_service_proto_rawDescGZIP() []byte {
	file_moego_service_order_v1_order_merge_service_proto_rawDescOnce.Do(func() {
		file_moego_service_order_v1_order_merge_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_order_v1_order_merge_service_proto_rawDescData)
	})
	return file_moego_service_order_v1_order_merge_service_proto_rawDescData
}

var file_moego_service_order_v1_order_merge_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_service_order_v1_order_merge_service_proto_goTypes = []interface{}{
	(*MergeCustomerOrderDataRequest)(nil),       // 0: moego.service.order.v1.MergeCustomerOrderDataRequest
	(*MergeCustomerOrderDataResponse)(nil),      // 1: moego.service.order.v1.MergeCustomerOrderDataResponse
	(*v1.BusinessCustomerMergeRelationDef)(nil), // 2: moego.models.business_customer.v1.BusinessCustomerMergeRelationDef
}
var file_moego_service_order_v1_order_merge_service_proto_depIdxs = []int32{
	2, // 0: moego.service.order.v1.MergeCustomerOrderDataRequest.merge_relation:type_name -> moego.models.business_customer.v1.BusinessCustomerMergeRelationDef
	0, // 1: moego.service.order.v1.OrderMergeService.MergeCustomerOrderData:input_type -> moego.service.order.v1.MergeCustomerOrderDataRequest
	1, // 2: moego.service.order.v1.OrderMergeService.MergeCustomerOrderData:output_type -> moego.service.order.v1.MergeCustomerOrderDataResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_service_order_v1_order_merge_service_proto_init() }
func file_moego_service_order_v1_order_merge_service_proto_init() {
	if File_moego_service_order_v1_order_merge_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_order_v1_order_merge_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MergeCustomerOrderDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v1_order_merge_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MergeCustomerOrderDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_order_v1_order_merge_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_order_v1_order_merge_service_proto_goTypes,
		DependencyIndexes: file_moego_service_order_v1_order_merge_service_proto_depIdxs,
		MessageInfos:      file_moego_service_order_v1_order_merge_service_proto_msgTypes,
	}.Build()
	File_moego_service_order_v1_order_merge_service_proto = out.File
	file_moego_service_order_v1_order_merge_service_proto_rawDesc = nil
	file_moego_service_order_v1_order_merge_service_proto_goTypes = nil
	file_moego_service_order_v1_order_merge_service_proto_depIdxs = nil
}
