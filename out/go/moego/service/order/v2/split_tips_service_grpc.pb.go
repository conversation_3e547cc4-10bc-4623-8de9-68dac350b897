// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/order/v2/split_tips_service.proto

package ordersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SplitTipsServiceClient is the client API for SplitTipsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SplitTipsServiceClient interface {
	// get tips details for source type
	GetTipsSplitDetails(ctx context.Context, in *GetTipsSplitRequest, opts ...grpc.CallOption) (*GetTipsSplitResponse, error)
	// preview edit staff and split tips
	PreviewEditTipsSplit(ctx context.Context, in *EditStaffAndTipsSplitRequest, opts ...grpc.CallOption) (*PreviewEditTipsSplitResponse, error)
	// update edit staff and split tips
	EditTipsSplit(ctx context.Context, in *EditStaffAndTipsSplitRequest, opts ...grpc.CallOption) (*EditTipsSplitResponse, error)
	// get tips split changed status
	GetTipsSplitChangedStatus(ctx context.Context, in *GetTipsSplitChangedStatusRequest, opts ...grpc.CallOption) (*GetTipsSplitChangedStatusResponse, error)
	// clear tips split changed status
	ClearTipsSplitChangedStatus(ctx context.Context, in *ClearTipsSplitChangedStatusRequest, opts ...grpc.CallOption) (*ClearTipsSplitChangedStatusResponse, error)
	// ListTipsSplitDetailsBySource 通过 SourceID+SourceType 批量查询 Tips 分配详情.
	// 主要提供给 老 Report 进行实时查询.
	ListTipsSplitDetailsBySource(ctx context.Context, in *ListTipsSplitDetailsBySourceRequest, opts ...grpc.CallOption) (*ListTipsSplitDetailsBySourceResponse, error)
}

type splitTipsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSplitTipsServiceClient(cc grpc.ClientConnInterface) SplitTipsServiceClient {
	return &splitTipsServiceClient{cc}
}

func (c *splitTipsServiceClient) GetTipsSplitDetails(ctx context.Context, in *GetTipsSplitRequest, opts ...grpc.CallOption) (*GetTipsSplitResponse, error) {
	out := new(GetTipsSplitResponse)
	err := c.cc.Invoke(ctx, "/moego.service.order.v2.SplitTipsService/GetTipsSplitDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *splitTipsServiceClient) PreviewEditTipsSplit(ctx context.Context, in *EditStaffAndTipsSplitRequest, opts ...grpc.CallOption) (*PreviewEditTipsSplitResponse, error) {
	out := new(PreviewEditTipsSplitResponse)
	err := c.cc.Invoke(ctx, "/moego.service.order.v2.SplitTipsService/PreviewEditTipsSplit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *splitTipsServiceClient) EditTipsSplit(ctx context.Context, in *EditStaffAndTipsSplitRequest, opts ...grpc.CallOption) (*EditTipsSplitResponse, error) {
	out := new(EditTipsSplitResponse)
	err := c.cc.Invoke(ctx, "/moego.service.order.v2.SplitTipsService/EditTipsSplit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *splitTipsServiceClient) GetTipsSplitChangedStatus(ctx context.Context, in *GetTipsSplitChangedStatusRequest, opts ...grpc.CallOption) (*GetTipsSplitChangedStatusResponse, error) {
	out := new(GetTipsSplitChangedStatusResponse)
	err := c.cc.Invoke(ctx, "/moego.service.order.v2.SplitTipsService/GetTipsSplitChangedStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *splitTipsServiceClient) ClearTipsSplitChangedStatus(ctx context.Context, in *ClearTipsSplitChangedStatusRequest, opts ...grpc.CallOption) (*ClearTipsSplitChangedStatusResponse, error) {
	out := new(ClearTipsSplitChangedStatusResponse)
	err := c.cc.Invoke(ctx, "/moego.service.order.v2.SplitTipsService/ClearTipsSplitChangedStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *splitTipsServiceClient) ListTipsSplitDetailsBySource(ctx context.Context, in *ListTipsSplitDetailsBySourceRequest, opts ...grpc.CallOption) (*ListTipsSplitDetailsBySourceResponse, error) {
	out := new(ListTipsSplitDetailsBySourceResponse)
	err := c.cc.Invoke(ctx, "/moego.service.order.v2.SplitTipsService/ListTipsSplitDetailsBySource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SplitTipsServiceServer is the server API for SplitTipsService service.
// All implementations must embed UnimplementedSplitTipsServiceServer
// for forward compatibility
type SplitTipsServiceServer interface {
	// get tips details for source type
	GetTipsSplitDetails(context.Context, *GetTipsSplitRequest) (*GetTipsSplitResponse, error)
	// preview edit staff and split tips
	PreviewEditTipsSplit(context.Context, *EditStaffAndTipsSplitRequest) (*PreviewEditTipsSplitResponse, error)
	// update edit staff and split tips
	EditTipsSplit(context.Context, *EditStaffAndTipsSplitRequest) (*EditTipsSplitResponse, error)
	// get tips split changed status
	GetTipsSplitChangedStatus(context.Context, *GetTipsSplitChangedStatusRequest) (*GetTipsSplitChangedStatusResponse, error)
	// clear tips split changed status
	ClearTipsSplitChangedStatus(context.Context, *ClearTipsSplitChangedStatusRequest) (*ClearTipsSplitChangedStatusResponse, error)
	// ListTipsSplitDetailsBySource 通过 SourceID+SourceType 批量查询 Tips 分配详情.
	// 主要提供给 老 Report 进行实时查询.
	ListTipsSplitDetailsBySource(context.Context, *ListTipsSplitDetailsBySourceRequest) (*ListTipsSplitDetailsBySourceResponse, error)
	mustEmbedUnimplementedSplitTipsServiceServer()
}

// UnimplementedSplitTipsServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSplitTipsServiceServer struct {
}

func (UnimplementedSplitTipsServiceServer) GetTipsSplitDetails(context.Context, *GetTipsSplitRequest) (*GetTipsSplitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTipsSplitDetails not implemented")
}
func (UnimplementedSplitTipsServiceServer) PreviewEditTipsSplit(context.Context, *EditStaffAndTipsSplitRequest) (*PreviewEditTipsSplitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreviewEditTipsSplit not implemented")
}
func (UnimplementedSplitTipsServiceServer) EditTipsSplit(context.Context, *EditStaffAndTipsSplitRequest) (*EditTipsSplitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditTipsSplit not implemented")
}
func (UnimplementedSplitTipsServiceServer) GetTipsSplitChangedStatus(context.Context, *GetTipsSplitChangedStatusRequest) (*GetTipsSplitChangedStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTipsSplitChangedStatus not implemented")
}
func (UnimplementedSplitTipsServiceServer) ClearTipsSplitChangedStatus(context.Context, *ClearTipsSplitChangedStatusRequest) (*ClearTipsSplitChangedStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClearTipsSplitChangedStatus not implemented")
}
func (UnimplementedSplitTipsServiceServer) ListTipsSplitDetailsBySource(context.Context, *ListTipsSplitDetailsBySourceRequest) (*ListTipsSplitDetailsBySourceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTipsSplitDetailsBySource not implemented")
}
func (UnimplementedSplitTipsServiceServer) mustEmbedUnimplementedSplitTipsServiceServer() {}

// UnsafeSplitTipsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SplitTipsServiceServer will
// result in compilation errors.
type UnsafeSplitTipsServiceServer interface {
	mustEmbedUnimplementedSplitTipsServiceServer()
}

func RegisterSplitTipsServiceServer(s grpc.ServiceRegistrar, srv SplitTipsServiceServer) {
	s.RegisterService(&SplitTipsService_ServiceDesc, srv)
}

func _SplitTipsService_GetTipsSplitDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTipsSplitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SplitTipsServiceServer).GetTipsSplitDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v2.SplitTipsService/GetTipsSplitDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SplitTipsServiceServer).GetTipsSplitDetails(ctx, req.(*GetTipsSplitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SplitTipsService_PreviewEditTipsSplit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditStaffAndTipsSplitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SplitTipsServiceServer).PreviewEditTipsSplit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v2.SplitTipsService/PreviewEditTipsSplit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SplitTipsServiceServer).PreviewEditTipsSplit(ctx, req.(*EditStaffAndTipsSplitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SplitTipsService_EditTipsSplit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditStaffAndTipsSplitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SplitTipsServiceServer).EditTipsSplit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v2.SplitTipsService/EditTipsSplit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SplitTipsServiceServer).EditTipsSplit(ctx, req.(*EditStaffAndTipsSplitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SplitTipsService_GetTipsSplitChangedStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTipsSplitChangedStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SplitTipsServiceServer).GetTipsSplitChangedStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v2.SplitTipsService/GetTipsSplitChangedStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SplitTipsServiceServer).GetTipsSplitChangedStatus(ctx, req.(*GetTipsSplitChangedStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SplitTipsService_ClearTipsSplitChangedStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearTipsSplitChangedStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SplitTipsServiceServer).ClearTipsSplitChangedStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v2.SplitTipsService/ClearTipsSplitChangedStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SplitTipsServiceServer).ClearTipsSplitChangedStatus(ctx, req.(*ClearTipsSplitChangedStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SplitTipsService_ListTipsSplitDetailsBySource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTipsSplitDetailsBySourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SplitTipsServiceServer).ListTipsSplitDetailsBySource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v2.SplitTipsService/ListTipsSplitDetailsBySource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SplitTipsServiceServer).ListTipsSplitDetailsBySource(ctx, req.(*ListTipsSplitDetailsBySourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SplitTipsService_ServiceDesc is the grpc.ServiceDesc for SplitTipsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SplitTipsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.order.v2.SplitTipsService",
	HandlerType: (*SplitTipsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetTipsSplitDetails",
			Handler:    _SplitTipsService_GetTipsSplitDetails_Handler,
		},
		{
			MethodName: "PreviewEditTipsSplit",
			Handler:    _SplitTipsService_PreviewEditTipsSplit_Handler,
		},
		{
			MethodName: "EditTipsSplit",
			Handler:    _SplitTipsService_EditTipsSplit_Handler,
		},
		{
			MethodName: "GetTipsSplitChangedStatus",
			Handler:    _SplitTipsService_GetTipsSplitChangedStatus_Handler,
		},
		{
			MethodName: "ClearTipsSplitChangedStatus",
			Handler:    _SplitTipsService_ClearTipsSplitChangedStatus_Handler,
		},
		{
			MethodName: "ListTipsSplitDetailsBySource",
			Handler:    _SplitTipsService_ListTipsSplitDetailsBySource_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/order/v2/split_tips_service.proto",
}
