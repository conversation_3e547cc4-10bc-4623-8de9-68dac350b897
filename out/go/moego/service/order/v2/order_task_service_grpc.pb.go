// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/order/v2/order_task_service.proto

package ordersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// OrderTaskServiceClient is the client API for OrderTaskService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OrderTaskServiceClient interface {
	// retry redeem promotion
	RetryRedeemPromotion(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type orderTaskServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOrderTaskServiceClient(cc grpc.ClientConnInterface) OrderTaskServiceClient {
	return &orderTaskServiceClient{cc}
}

func (c *orderTaskServiceClient) RetryRedeemPromotion(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.order.v2.OrderTaskService/RetryRedeemPromotion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrderTaskServiceServer is the server API for OrderTaskService service.
// All implementations must embed UnimplementedOrderTaskServiceServer
// for forward compatibility
type OrderTaskServiceServer interface {
	// retry redeem promotion
	RetryRedeemPromotion(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	mustEmbedUnimplementedOrderTaskServiceServer()
}

// UnimplementedOrderTaskServiceServer must be embedded to have forward compatible implementations.
type UnimplementedOrderTaskServiceServer struct {
}

func (UnimplementedOrderTaskServiceServer) RetryRedeemPromotion(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetryRedeemPromotion not implemented")
}
func (UnimplementedOrderTaskServiceServer) mustEmbedUnimplementedOrderTaskServiceServer() {}

// UnsafeOrderTaskServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OrderTaskServiceServer will
// result in compilation errors.
type UnsafeOrderTaskServiceServer interface {
	mustEmbedUnimplementedOrderTaskServiceServer()
}

func RegisterOrderTaskServiceServer(s grpc.ServiceRegistrar, srv OrderTaskServiceServer) {
	s.RegisterService(&OrderTaskService_ServiceDesc, srv)
}

func _OrderTaskService_RetryRedeemPromotion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderTaskServiceServer).RetryRedeemPromotion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.order.v2.OrderTaskService/RetryRedeemPromotion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderTaskServiceServer).RetryRedeemPromotion(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// OrderTaskService_ServiceDesc is the grpc.ServiceDesc for OrderTaskService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OrderTaskService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.order.v2.OrderTaskService",
	HandlerType: (*OrderTaskServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RetryRedeemPromotion",
			Handler:    _OrderTaskService_RetryRedeemPromotion_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/order/v2/order_task_service.proto",
}
