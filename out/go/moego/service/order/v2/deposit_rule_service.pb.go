// 这个 service 应该归属业务域，不应该在 order 域，只是现在是 order 服务实现的，先放这里。

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/order/v2/deposit_rule_service.proto

package ordersvcpb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request for CreateDepositRule
type CreateDepositRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Def
	Rule *v1.CreateDepositRuleDef `protobuf:"bytes,3,opt,name=rule,proto3" json:"rule,omitempty"`
}

func (x *CreateDepositRuleRequest) Reset() {
	*x = CreateDepositRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDepositRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDepositRuleRequest) ProtoMessage() {}

func (x *CreateDepositRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDepositRuleRequest.ProtoReflect.Descriptor instead.
func (*CreateDepositRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v2_deposit_rule_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateDepositRuleRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateDepositRuleRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateDepositRuleRequest) GetRule() *v1.CreateDepositRuleDef {
	if x != nil {
		return x.Rule
	}
	return nil
}

// Response for CreateDepositRule
type CreateDepositRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Created rule
	Rule *v1.DepositRule `protobuf:"bytes,1,opt,name=rule,proto3" json:"rule,omitempty"`
}

func (x *CreateDepositRuleResponse) Reset() {
	*x = CreateDepositRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDepositRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDepositRuleResponse) ProtoMessage() {}

func (x *CreateDepositRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDepositRuleResponse.ProtoReflect.Descriptor instead.
func (*CreateDepositRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v2_deposit_rule_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateDepositRuleResponse) GetRule() *v1.DepositRule {
	if x != nil {
		return x.Rule
	}
	return nil
}

// Request for UpdateDepositRule
type UpdateDepositRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Rule ID
	RuleId int64 `protobuf:"varint,1,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`
	// Def
	Rule *v1.UpdateDepositRuleDef `protobuf:"bytes,2,opt,name=rule,proto3" json:"rule,omitempty"`
}

func (x *UpdateDepositRuleRequest) Reset() {
	*x = UpdateDepositRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDepositRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDepositRuleRequest) ProtoMessage() {}

func (x *UpdateDepositRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDepositRuleRequest.ProtoReflect.Descriptor instead.
func (*UpdateDepositRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v2_deposit_rule_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateDepositRuleRequest) GetRuleId() int64 {
	if x != nil {
		return x.RuleId
	}
	return 0
}

func (x *UpdateDepositRuleRequest) GetRule() *v1.UpdateDepositRuleDef {
	if x != nil {
		return x.Rule
	}
	return nil
}

// Response for UpdateDepositRule
type UpdateDepositRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Updated rule
	Rule *v1.DepositRule `protobuf:"bytes,1,opt,name=rule,proto3" json:"rule,omitempty"`
}

func (x *UpdateDepositRuleResponse) Reset() {
	*x = UpdateDepositRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDepositRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDepositRuleResponse) ProtoMessage() {}

func (x *UpdateDepositRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDepositRuleResponse.ProtoReflect.Descriptor instead.
func (*UpdateDepositRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v2_deposit_rule_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateDepositRuleResponse) GetRule() *v1.DepositRule {
	if x != nil {
		return x.Rule
	}
	return nil
}

// Request for DeleteDepositRule
type DeleteDepositRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Rule ID
	RuleId int64 `protobuf:"varint,1,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`
}

func (x *DeleteDepositRuleRequest) Reset() {
	*x = DeleteDepositRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDepositRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDepositRuleRequest) ProtoMessage() {}

func (x *DeleteDepositRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDepositRuleRequest.ProtoReflect.Descriptor instead.
func (*DeleteDepositRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v2_deposit_rule_service_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteDepositRuleRequest) GetRuleId() int64 {
	if x != nil {
		return x.RuleId
	}
	return 0
}

// Response for DeleteDepositRule
type DeleteDepositRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteDepositRuleResponse) Reset() {
	*x = DeleteDepositRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDepositRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDepositRuleResponse) ProtoMessage() {}

func (x *DeleteDepositRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDepositRuleResponse.ProtoReflect.Descriptor instead.
func (*DeleteDepositRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v2_deposit_rule_service_proto_rawDescGZIP(), []int{5}
}

// Request for ListDepositRules
type ListDepositRulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business ID
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *ListDepositRulesRequest) Reset() {
	*x = ListDepositRulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDepositRulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDepositRulesRequest) ProtoMessage() {}

func (x *ListDepositRulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDepositRulesRequest.ProtoReflect.Descriptor instead.
func (*ListDepositRulesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v2_deposit_rule_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListDepositRulesRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// Response for ListDepositRules
type ListDepositRulesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Rules
	Rules []*v1.DepositRule `protobuf:"bytes,1,rep,name=rules,proto3" json:"rules,omitempty"`
}

func (x *ListDepositRulesResponse) Reset() {
	*x = ListDepositRulesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDepositRulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDepositRulesResponse) ProtoMessage() {}

func (x *ListDepositRulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDepositRulesResponse.ProtoReflect.Descriptor instead.
func (*ListDepositRulesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v2_deposit_rule_service_proto_rawDescGZIP(), []int{7}
}

func (x *ListDepositRulesResponse) GetRules() []*v1.DepositRule {
	if x != nil {
		return x.Rules
	}
	return nil
}

// Request for PreviewDepositOrder
type PreviewDepositOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Company ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Customer ID，**对于还没创建的新 customer （包括 Lead）则不传**。
	CustomerId *int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// start date, format: yyyy-mm-dd
	// appointment/booking request 不方便构造，所以简单定义个 start date 先用着了……
	AppointmentStartDate string `protobuf:"bytes,4,opt,name=appointment_start_date,json=appointmentStartDate,proto3" json:"appointment_start_date,omitempty"`
	// Service pricing of the appointment, after applying pricing rules. Note that the discounts, membership discounts and
	// taxes are not applied yet.
	ServicePricingDetails []*PreviewDepositOrderRequest_ServicePricingDetail `protobuf:"bytes,5,rep,name=service_pricing_details,json=servicePricingDetails,proto3" json:"service_pricing_details,omitempty"`
	// Include convenience fee
	IncludeConvenienceFee bool `protobuf:"varint,6,opt,name=include_convenience_fee,json=includeConvenienceFee,proto3" json:"include_convenience_fee,omitempty"`
}

func (x *PreviewDepositOrderRequest) Reset() {
	*x = PreviewDepositOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewDepositOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewDepositOrderRequest) ProtoMessage() {}

func (x *PreviewDepositOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewDepositOrderRequest.ProtoReflect.Descriptor instead.
func (*PreviewDepositOrderRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v2_deposit_rule_service_proto_rawDescGZIP(), []int{8}
}

func (x *PreviewDepositOrderRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *PreviewDepositOrderRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *PreviewDepositOrderRequest) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *PreviewDepositOrderRequest) GetAppointmentStartDate() string {
	if x != nil {
		return x.AppointmentStartDate
	}
	return ""
}

func (x *PreviewDepositOrderRequest) GetServicePricingDetails() []*PreviewDepositOrderRequest_ServicePricingDetail {
	if x != nil {
		return x.ServicePricingDetails
	}
	return nil
}

func (x *PreviewDepositOrderRequest) GetIncludeConvenienceFee() bool {
	if x != nil {
		return x.IncludeConvenienceFee
	}
	return false
}

// Response for PreviewDepositOrder
type PreviewDepositOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Preview of the deposit order. If not set, it means no deposit rule is matched.
	DepositOrderDetailPreview *v1.OrderDetailModelV1 `protobuf:"bytes,1,opt,name=deposit_order_detail_preview,json=depositOrderDetailPreview,proto3,oneof" json:"deposit_order_detail_preview,omitempty"`
}

func (x *PreviewDepositOrderResponse) Reset() {
	*x = PreviewDepositOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewDepositOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewDepositOrderResponse) ProtoMessage() {}

func (x *PreviewDepositOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewDepositOrderResponse.ProtoReflect.Descriptor instead.
func (*PreviewDepositOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v2_deposit_rule_service_proto_rawDescGZIP(), []int{9}
}

func (x *PreviewDepositOrderResponse) GetDepositOrderDetailPreview() *v1.OrderDetailModelV1 {
	if x != nil {
		return x.DepositOrderDetailPreview
	}
	return nil
}

// Request for MigrateToDepositRules
type MigrateToDepositRulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business IDs to migrate.
	BusinessIds []int64 `protobuf:"varint,1,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *MigrateToDepositRulesRequest) Reset() {
	*x = MigrateToDepositRulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MigrateToDepositRulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MigrateToDepositRulesRequest) ProtoMessage() {}

func (x *MigrateToDepositRulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MigrateToDepositRulesRequest.ProtoReflect.Descriptor instead.
func (*MigrateToDepositRulesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v2_deposit_rule_service_proto_rawDescGZIP(), []int{10}
}

func (x *MigrateToDepositRulesRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// Response for MigrateToDepositRules
type MigrateToDepositRulesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Migration errors, if any.
	Errors []*MigrateToDepositRulesResponse_Error `protobuf:"bytes,1,rep,name=errors,proto3" json:"errors,omitempty"`
}

func (x *MigrateToDepositRulesResponse) Reset() {
	*x = MigrateToDepositRulesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MigrateToDepositRulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MigrateToDepositRulesResponse) ProtoMessage() {}

func (x *MigrateToDepositRulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MigrateToDepositRulesResponse.ProtoReflect.Descriptor instead.
func (*MigrateToDepositRulesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v2_deposit_rule_service_proto_rawDescGZIP(), []int{11}
}

func (x *MigrateToDepositRulesResponse) GetErrors() []*MigrateToDepositRulesResponse_Error {
	if x != nil {
		return x.Errors
	}
	return nil
}

// Appointment 域现在是用 PetDetail 来承载服务记录信息，太重了，暂时没找到其他合适的 pb，所以这里另行定义。字段基本参照
// CalculateServiceAmount。
type PreviewDepositOrderRequest_ServicePricingDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The pet id receiving this service.
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// Service detail. Note that the price of it is the original price.
	Service *v11.CustomizedServiceView `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
	// The unit price after applying pricing rules.
	UnitPrice *money.Money `protobuf:"bytes,3,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
	// The quantity.
	Quantity int64 `protobuf:"varint,4,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// The total price after applying pricing rules.
	TotalPrice *money.Money `protobuf:"bytes,5,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
}

func (x *PreviewDepositOrderRequest_ServicePricingDetail) Reset() {
	*x = PreviewDepositOrderRequest_ServicePricingDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewDepositOrderRequest_ServicePricingDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewDepositOrderRequest_ServicePricingDetail) ProtoMessage() {}

func (x *PreviewDepositOrderRequest_ServicePricingDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewDepositOrderRequest_ServicePricingDetail.ProtoReflect.Descriptor instead.
func (*PreviewDepositOrderRequest_ServicePricingDetail) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v2_deposit_rule_service_proto_rawDescGZIP(), []int{8, 0}
}

func (x *PreviewDepositOrderRequest_ServicePricingDetail) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PreviewDepositOrderRequest_ServicePricingDetail) GetService() *v11.CustomizedServiceView {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *PreviewDepositOrderRequest_ServicePricingDetail) GetUnitPrice() *money.Money {
	if x != nil {
		return x.UnitPrice
	}
	return nil
}

func (x *PreviewDepositOrderRequest_ServicePricingDetail) GetQuantity() int64 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *PreviewDepositOrderRequest_ServicePricingDetail) GetTotalPrice() *money.Money {
	if x != nil {
		return x.TotalPrice
	}
	return nil
}

// Migration error.
type MigrateToDepositRulesResponse_Error struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business ID
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Error message
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *MigrateToDepositRulesResponse_Error) Reset() {
	*x = MigrateToDepositRulesResponse_Error{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MigrateToDepositRulesResponse_Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MigrateToDepositRulesResponse_Error) ProtoMessage() {}

func (x *MigrateToDepositRulesResponse_Error) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MigrateToDepositRulesResponse_Error.ProtoReflect.Descriptor instead.
func (*MigrateToDepositRulesResponse_Error) Descriptor() ([]byte, []int) {
	return file_moego_service_order_v2_deposit_rule_service_proto_rawDescGZIP(), []int{11, 0}
}

func (x *MigrateToDepositRulesResponse_Error) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *MigrateToDepositRulesResponse_Error) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_moego_service_order_v2_deposit_rule_service_proto protoreflect.FileDescriptor

var file_moego_service_order_v2_deposit_rule_service_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x1a, 0x17, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xad, 0x01,
	0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x04,
	0x72, 0x75, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x52, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x22, 0x53, 0x0a,
	0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x04, 0x72, 0x75,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x04, 0x72, 0x75,
	0x6c, 0x65, 0x22, 0x7d, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20,
	0x0a, 0x07, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64,
	0x12, 0x3f, 0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x52, 0x04, 0x72, 0x75, 0x6c,
	0x65, 0x22, 0x53, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36,
	0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x22, 0x3c, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x72, 0x75,
	0x6c, 0x65, 0x49, 0x64, 0x22, 0x1b, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x43, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x54, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x38, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x22, 0x92, 0x05, 0x0a,
	0x1a, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x24, 0x0a,
	0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x7f, 0x0a, 0x17, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x15, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x69, 0x6e,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x69, 0x6e, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46,
	0x65, 0x65, 0x1a, 0xfc, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x15, 0x0a, 0x06, 0x70,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74,
	0x49, 0x64, 0x12, 0x49, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x31, 0x0a,
	0x0a, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x75, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x33, 0x0a, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x22, 0xaf, 0x01, 0x0a, 0x1b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x6f, 0x0a, 0x1c, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x56, 0x31, 0x48, 0x00, 0x52, 0x19, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x88,
	0x01, 0x01, 0x42, 0x1f, 0x0a, 0x1d, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x70, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x22, 0x41, 0x0a, 0x1c, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x54, 0x6f,
	0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x22, 0xc1, 0x01, 0x0a, 0x1d, 0x4d, 0x69, 0x67, 0x72, 0x61,
	0x74, 0x65, 0x54, 0x6f, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x53, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x32, 0x2e, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x1a, 0x4b, 0x0a,
	0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x32, 0x80, 0x06, 0x0a, 0x12, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x78, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x11, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x75, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x13, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x15, 0x4d, 0x69, 0x67, 0x72, 0x61,
	0x74, 0x65, 0x54, 0x6f, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74,
	0x65, 0x54, 0x6f, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x2e,
	0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x7a, 0x0a,
	0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x50,
	0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x32, 0x3b, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_service_order_v2_deposit_rule_service_proto_rawDescOnce sync.Once
	file_moego_service_order_v2_deposit_rule_service_proto_rawDescData = file_moego_service_order_v2_deposit_rule_service_proto_rawDesc
)

func file_moego_service_order_v2_deposit_rule_service_proto_rawDescGZIP() []byte {
	file_moego_service_order_v2_deposit_rule_service_proto_rawDescOnce.Do(func() {
		file_moego_service_order_v2_deposit_rule_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_order_v2_deposit_rule_service_proto_rawDescData)
	})
	return file_moego_service_order_v2_deposit_rule_service_proto_rawDescData
}

var file_moego_service_order_v2_deposit_rule_service_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_moego_service_order_v2_deposit_rule_service_proto_goTypes = []interface{}{
	(*CreateDepositRuleRequest)(nil),                        // 0: moego.service.order.v2.CreateDepositRuleRequest
	(*CreateDepositRuleResponse)(nil),                       // 1: moego.service.order.v2.CreateDepositRuleResponse
	(*UpdateDepositRuleRequest)(nil),                        // 2: moego.service.order.v2.UpdateDepositRuleRequest
	(*UpdateDepositRuleResponse)(nil),                       // 3: moego.service.order.v2.UpdateDepositRuleResponse
	(*DeleteDepositRuleRequest)(nil),                        // 4: moego.service.order.v2.DeleteDepositRuleRequest
	(*DeleteDepositRuleResponse)(nil),                       // 5: moego.service.order.v2.DeleteDepositRuleResponse
	(*ListDepositRulesRequest)(nil),                         // 6: moego.service.order.v2.ListDepositRulesRequest
	(*ListDepositRulesResponse)(nil),                        // 7: moego.service.order.v2.ListDepositRulesResponse
	(*PreviewDepositOrderRequest)(nil),                      // 8: moego.service.order.v2.PreviewDepositOrderRequest
	(*PreviewDepositOrderResponse)(nil),                     // 9: moego.service.order.v2.PreviewDepositOrderResponse
	(*MigrateToDepositRulesRequest)(nil),                    // 10: moego.service.order.v2.MigrateToDepositRulesRequest
	(*MigrateToDepositRulesResponse)(nil),                   // 11: moego.service.order.v2.MigrateToDepositRulesResponse
	(*PreviewDepositOrderRequest_ServicePricingDetail)(nil), // 12: moego.service.order.v2.PreviewDepositOrderRequest.ServicePricingDetail
	(*MigrateToDepositRulesResponse_Error)(nil),             // 13: moego.service.order.v2.MigrateToDepositRulesResponse.Error
	(*v1.CreateDepositRuleDef)(nil),                         // 14: moego.models.order.v1.CreateDepositRuleDef
	(*v1.DepositRule)(nil),                                  // 15: moego.models.order.v1.DepositRule
	(*v1.UpdateDepositRuleDef)(nil),                         // 16: moego.models.order.v1.UpdateDepositRuleDef
	(*v1.OrderDetailModelV1)(nil),                           // 17: moego.models.order.v1.OrderDetailModelV1
	(*v11.CustomizedServiceView)(nil),                       // 18: moego.models.offering.v1.CustomizedServiceView
	(*money.Money)(nil),                                     // 19: google.type.Money
}
var file_moego_service_order_v2_deposit_rule_service_proto_depIdxs = []int32{
	14, // 0: moego.service.order.v2.CreateDepositRuleRequest.rule:type_name -> moego.models.order.v1.CreateDepositRuleDef
	15, // 1: moego.service.order.v2.CreateDepositRuleResponse.rule:type_name -> moego.models.order.v1.DepositRule
	16, // 2: moego.service.order.v2.UpdateDepositRuleRequest.rule:type_name -> moego.models.order.v1.UpdateDepositRuleDef
	15, // 3: moego.service.order.v2.UpdateDepositRuleResponse.rule:type_name -> moego.models.order.v1.DepositRule
	15, // 4: moego.service.order.v2.ListDepositRulesResponse.rules:type_name -> moego.models.order.v1.DepositRule
	12, // 5: moego.service.order.v2.PreviewDepositOrderRequest.service_pricing_details:type_name -> moego.service.order.v2.PreviewDepositOrderRequest.ServicePricingDetail
	17, // 6: moego.service.order.v2.PreviewDepositOrderResponse.deposit_order_detail_preview:type_name -> moego.models.order.v1.OrderDetailModelV1
	13, // 7: moego.service.order.v2.MigrateToDepositRulesResponse.errors:type_name -> moego.service.order.v2.MigrateToDepositRulesResponse.Error
	18, // 8: moego.service.order.v2.PreviewDepositOrderRequest.ServicePricingDetail.service:type_name -> moego.models.offering.v1.CustomizedServiceView
	19, // 9: moego.service.order.v2.PreviewDepositOrderRequest.ServicePricingDetail.unit_price:type_name -> google.type.Money
	19, // 10: moego.service.order.v2.PreviewDepositOrderRequest.ServicePricingDetail.total_price:type_name -> google.type.Money
	0,  // 11: moego.service.order.v2.DepositRuleService.CreateDepositRule:input_type -> moego.service.order.v2.CreateDepositRuleRequest
	2,  // 12: moego.service.order.v2.DepositRuleService.UpdateDepositRule:input_type -> moego.service.order.v2.UpdateDepositRuleRequest
	4,  // 13: moego.service.order.v2.DepositRuleService.DeleteDepositRule:input_type -> moego.service.order.v2.DeleteDepositRuleRequest
	6,  // 14: moego.service.order.v2.DepositRuleService.ListDepositRules:input_type -> moego.service.order.v2.ListDepositRulesRequest
	8,  // 15: moego.service.order.v2.DepositRuleService.PreviewDepositOrder:input_type -> moego.service.order.v2.PreviewDepositOrderRequest
	10, // 16: moego.service.order.v2.DepositRuleService.MigrateToDepositRules:input_type -> moego.service.order.v2.MigrateToDepositRulesRequest
	1,  // 17: moego.service.order.v2.DepositRuleService.CreateDepositRule:output_type -> moego.service.order.v2.CreateDepositRuleResponse
	3,  // 18: moego.service.order.v2.DepositRuleService.UpdateDepositRule:output_type -> moego.service.order.v2.UpdateDepositRuleResponse
	5,  // 19: moego.service.order.v2.DepositRuleService.DeleteDepositRule:output_type -> moego.service.order.v2.DeleteDepositRuleResponse
	7,  // 20: moego.service.order.v2.DepositRuleService.ListDepositRules:output_type -> moego.service.order.v2.ListDepositRulesResponse
	9,  // 21: moego.service.order.v2.DepositRuleService.PreviewDepositOrder:output_type -> moego.service.order.v2.PreviewDepositOrderResponse
	11, // 22: moego.service.order.v2.DepositRuleService.MigrateToDepositRules:output_type -> moego.service.order.v2.MigrateToDepositRulesResponse
	17, // [17:23] is the sub-list for method output_type
	11, // [11:17] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_moego_service_order_v2_deposit_rule_service_proto_init() }
func file_moego_service_order_v2_deposit_rule_service_proto_init() {
	if File_moego_service_order_v2_deposit_rule_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDepositRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDepositRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDepositRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDepositRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDepositRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDepositRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDepositRulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDepositRulesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewDepositOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewDepositOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MigrateToDepositRulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MigrateToDepositRulesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewDepositOrderRequest_ServicePricingDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MigrateToDepositRulesResponse_Error); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_service_order_v2_deposit_rule_service_proto_msgTypes[9].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_order_v2_deposit_rule_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_order_v2_deposit_rule_service_proto_goTypes,
		DependencyIndexes: file_moego_service_order_v2_deposit_rule_service_proto_depIdxs,
		MessageInfos:      file_moego_service_order_v2_deposit_rule_service_proto_msgTypes,
	}.Build()
	File_moego_service_order_v2_deposit_rule_service_proto = out.File
	file_moego_service_order_v2_deposit_rule_service_proto_rawDesc = nil
	file_moego_service_order_v2_deposit_rule_service_proto_goTypes = nil
	file_moego_service_order_v2_deposit_rule_service_proto_depIdxs = nil
}
