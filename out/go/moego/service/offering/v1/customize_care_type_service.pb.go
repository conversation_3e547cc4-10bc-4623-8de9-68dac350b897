// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/offering/v1/customize_care_type_service.proto

package offeringsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// update care type name request
type UpdateCareTypeNameRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// care type name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// white list filter
	WhiteListFilter *WhiteListFilter `protobuf:"bytes,5,opt,name=white_list_filter,json=whiteListFilter,proto3" json:"white_list_filter,omitempty"`
}

func (x *UpdateCareTypeNameRequest) Reset() {
	*x = UpdateCareTypeNameRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCareTypeNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCareTypeNameRequest) ProtoMessage() {}

func (x *UpdateCareTypeNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCareTypeNameRequest.ProtoReflect.Descriptor instead.
func (*UpdateCareTypeNameRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_customize_care_type_service_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateCareTypeNameRequest) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *UpdateCareTypeNameRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateCareTypeNameRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateCareTypeNameRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateCareTypeNameRequest) GetWhiteListFilter() *WhiteListFilter {
	if x != nil {
		return x.WhiteListFilter
	}
	return nil
}

// update care type name response
type UpdateCareTypeNameResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateCareTypeNameResponse) Reset() {
	*x = UpdateCareTypeNameResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCareTypeNameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCareTypeNameResponse) ProtoMessage() {}

func (x *UpdateCareTypeNameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCareTypeNameResponse.ProtoReflect.Descriptor instead.
func (*UpdateCareTypeNameResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_customize_care_type_service_proto_rawDescGZIP(), []int{1}
}

// list care types request
type ListCareTypesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// service item type
	ServiceItemType []v1.ServiceItemType `protobuf:"varint,2,rep,packed,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// white list filter
	WhiteListFilter *WhiteListFilter `protobuf:"bytes,3,opt,name=white_list_filter,json=whiteListFilter,proto3" json:"white_list_filter,omitempty"`
}

func (x *ListCareTypesRequest) Reset() {
	*x = ListCareTypesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCareTypesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCareTypesRequest) ProtoMessage() {}

func (x *ListCareTypesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCareTypesRequest.ProtoReflect.Descriptor instead.
func (*ListCareTypesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_customize_care_type_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListCareTypesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListCareTypesRequest) GetServiceItemType() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return nil
}

func (x *ListCareTypesRequest) GetWhiteListFilter() *WhiteListFilter {
	if x != nil {
		return x.WhiteListFilter
	}
	return nil
}

// list care types response
type ListCareTypesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// care types
	CareTypes []*v1.CustomizeCareTypeView `protobuf:"bytes,1,rep,name=care_types,json=careTypes,proto3" json:"care_types,omitempty"`
}

func (x *ListCareTypesResponse) Reset() {
	*x = ListCareTypesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCareTypesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCareTypesResponse) ProtoMessage() {}

func (x *ListCareTypesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCareTypesResponse.ProtoReflect.Descriptor instead.
func (*ListCareTypesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_customize_care_type_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListCareTypesResponse) GetCareTypes() []*v1.CustomizeCareTypeView {
	if x != nil {
		return x.CareTypes
	}
	return nil
}

// white list filter
type WhiteListFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is in Boarding/Daycare/Evaluation white list
	IsAllowBoardingAndDaycare bool `protobuf:"varint,1,opt,name=is_allow_boarding_and_daycare,json=isAllowBoardingAndDaycare,proto3" json:"is_allow_boarding_and_daycare,omitempty"`
	// is in DogWalking white list
	IsAllowDogWalking bool `protobuf:"varint,2,opt,name=is_allow_dog_walking,json=isAllowDogWalking,proto3" json:"is_allow_dog_walking,omitempty"`
	// is in GroupClass white list
	IsAllowGroupClass bool `protobuf:"varint,3,opt,name=is_allow_group_class,json=isAllowGroupClass,proto3" json:"is_allow_group_class,omitempty"`
}

func (x *WhiteListFilter) Reset() {
	*x = WhiteListFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WhiteListFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WhiteListFilter) ProtoMessage() {}

func (x *WhiteListFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WhiteListFilter.ProtoReflect.Descriptor instead.
func (*WhiteListFilter) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_customize_care_type_service_proto_rawDescGZIP(), []int{4}
}

func (x *WhiteListFilter) GetIsAllowBoardingAndDaycare() bool {
	if x != nil {
		return x.IsAllowBoardingAndDaycare
	}
	return false
}

func (x *WhiteListFilter) GetIsAllowDogWalking() bool {
	if x != nil {
		return x.IsAllowDogWalking
	}
	return false
}

func (x *WhiteListFilter) GetIsAllowGroupClass() bool {
	if x != nil {
		return x.IsAllowGroupClass
	}
	return false
}

var File_moego_service_offering_v1_customize_care_type_service_proto protoreflect.FileDescriptor

var file_moego_service_offering_v1_customize_care_type_service_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x5f, 0x63, 0x61, 0x72,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb6, 0x02, 0x0a, 0x19, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x56, 0x0a, 0x11, 0x77, 0x68, 0x69,
	0x74, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x0f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x22, 0x1c, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x72, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0xed, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x11, 0x77, 0x68, 0x69, 0x74, 0x65,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x0f,
	0x77, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22,
	0x67, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a,
	0x65, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x09, 0x63,
	0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0xb5, 0x01, 0x0a, 0x0f, 0x57, 0x68, 0x69,
	0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x1d,
	0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x19, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x41, 0x6e, 0x64, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x12, 0x2f,
	0x0a, 0x14, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6f, 0x67, 0x5f, 0x77,
	0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x73,
	0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x44, 0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x12,
	0x2f, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69,
	0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x32, 0x92, 0x02, 0x0a, 0x18, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x43, 0x61,
	0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x81, 0x01,
	0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x72,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x72, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x83, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_service_offering_v1_customize_care_type_service_proto_rawDescOnce sync.Once
	file_moego_service_offering_v1_customize_care_type_service_proto_rawDescData = file_moego_service_offering_v1_customize_care_type_service_proto_rawDesc
)

func file_moego_service_offering_v1_customize_care_type_service_proto_rawDescGZIP() []byte {
	file_moego_service_offering_v1_customize_care_type_service_proto_rawDescOnce.Do(func() {
		file_moego_service_offering_v1_customize_care_type_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_offering_v1_customize_care_type_service_proto_rawDescData)
	})
	return file_moego_service_offering_v1_customize_care_type_service_proto_rawDescData
}

var file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_service_offering_v1_customize_care_type_service_proto_goTypes = []interface{}{
	(*UpdateCareTypeNameRequest)(nil),  // 0: moego.service.offering.v1.UpdateCareTypeNameRequest
	(*UpdateCareTypeNameResponse)(nil), // 1: moego.service.offering.v1.UpdateCareTypeNameResponse
	(*ListCareTypesRequest)(nil),       // 2: moego.service.offering.v1.ListCareTypesRequest
	(*ListCareTypesResponse)(nil),      // 3: moego.service.offering.v1.ListCareTypesResponse
	(*WhiteListFilter)(nil),            // 4: moego.service.offering.v1.WhiteListFilter
	(v1.ServiceItemType)(0),            // 5: moego.models.offering.v1.ServiceItemType
	(*v1.CustomizeCareTypeView)(nil),   // 6: moego.models.offering.v1.CustomizeCareTypeView
}
var file_moego_service_offering_v1_customize_care_type_service_proto_depIdxs = []int32{
	5, // 0: moego.service.offering.v1.UpdateCareTypeNameRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	4, // 1: moego.service.offering.v1.UpdateCareTypeNameRequest.white_list_filter:type_name -> moego.service.offering.v1.WhiteListFilter
	5, // 2: moego.service.offering.v1.ListCareTypesRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	4, // 3: moego.service.offering.v1.ListCareTypesRequest.white_list_filter:type_name -> moego.service.offering.v1.WhiteListFilter
	6, // 4: moego.service.offering.v1.ListCareTypesResponse.care_types:type_name -> moego.models.offering.v1.CustomizeCareTypeView
	0, // 5: moego.service.offering.v1.CustomizeCareTypeService.UpdateCareTypeName:input_type -> moego.service.offering.v1.UpdateCareTypeNameRequest
	2, // 6: moego.service.offering.v1.CustomizeCareTypeService.ListCareTypes:input_type -> moego.service.offering.v1.ListCareTypesRequest
	1, // 7: moego.service.offering.v1.CustomizeCareTypeService.UpdateCareTypeName:output_type -> moego.service.offering.v1.UpdateCareTypeNameResponse
	3, // 8: moego.service.offering.v1.CustomizeCareTypeService.ListCareTypes:output_type -> moego.service.offering.v1.ListCareTypesResponse
	7, // [7:9] is the sub-list for method output_type
	5, // [5:7] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_service_offering_v1_customize_care_type_service_proto_init() }
func file_moego_service_offering_v1_customize_care_type_service_proto_init() {
	if File_moego_service_offering_v1_customize_care_type_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCareTypeNameRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCareTypeNameResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCareTypesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCareTypesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WhiteListFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_offering_v1_customize_care_type_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_offering_v1_customize_care_type_service_proto_goTypes,
		DependencyIndexes: file_moego_service_offering_v1_customize_care_type_service_proto_depIdxs,
		MessageInfos:      file_moego_service_offering_v1_customize_care_type_service_proto_msgTypes,
	}.Build()
	File_moego_service_offering_v1_customize_care_type_service_proto = out.File
	file_moego_service_offering_v1_customize_care_type_service_proto_rawDesc = nil
	file_moego_service_offering_v1_customize_care_type_service_proto_goTypes = nil
	file_moego_service_offering_v1_customize_care_type_service_proto_depIdxs = nil
}
