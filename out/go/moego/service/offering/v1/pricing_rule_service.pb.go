// @since 2024-07-30 11:14:10
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/offering/v1/pricing_rule_service.proto

package offeringsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// upsert pricing rule request
type UpsertPricingRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pricing_rule def
	PricingRuleUpsertDef *v1.PricingRuleUpsertDef `protobuf:"bytes,1,opt,name=pricing_rule_upsert_def,json=pricingRuleUpsertDef,proto3" json:"pricing_rule_upsert_def,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,6,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,7,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *UpsertPricingRuleRequest) Reset() {
	*x = UpsertPricingRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertPricingRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertPricingRuleRequest) ProtoMessage() {}

func (x *UpsertPricingRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertPricingRuleRequest.ProtoReflect.Descriptor instead.
func (*UpsertPricingRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_pricing_rule_service_proto_rawDescGZIP(), []int{0}
}

func (x *UpsertPricingRuleRequest) GetPricingRuleUpsertDef() *v1.PricingRuleUpsertDef {
	if x != nil {
		return x.PricingRuleUpsertDef
	}
	return nil
}

func (x *UpsertPricingRuleRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpsertPricingRuleRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// upsert pricing rule response
type UpsertPricingRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the created pricing_rule
	PricingRule *v1.PricingRuleModel `protobuf:"bytes,1,opt,name=pricing_rule,json=pricingRule,proto3" json:"pricing_rule,omitempty"`
}

func (x *UpsertPricingRuleResponse) Reset() {
	*x = UpsertPricingRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertPricingRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertPricingRuleResponse) ProtoMessage() {}

func (x *UpsertPricingRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertPricingRuleResponse.ProtoReflect.Descriptor instead.
func (*UpsertPricingRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_pricing_rule_service_proto_rawDescGZIP(), []int{1}
}

func (x *UpsertPricingRuleResponse) GetPricingRule() *v1.PricingRuleModel {
	if x != nil {
		return x.PricingRule
	}
	return nil
}

// get pricing rule request
type GetPricingRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,6,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetPricingRuleRequest) Reset() {
	*x = GetPricingRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPricingRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPricingRuleRequest) ProtoMessage() {}

func (x *GetPricingRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPricingRuleRequest.ProtoReflect.Descriptor instead.
func (*GetPricingRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_pricing_rule_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetPricingRuleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPricingRuleRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// get pricing rule response
type GetPricingRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pricing rule
	PricingRule *v1.PricingRuleModel `protobuf:"bytes,1,opt,name=pricing_rule,json=pricingRule,proto3" json:"pricing_rule,omitempty"`
}

func (x *GetPricingRuleResponse) Reset() {
	*x = GetPricingRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPricingRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPricingRuleResponse) ProtoMessage() {}

func (x *GetPricingRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPricingRuleResponse.ProtoReflect.Descriptor instead.
func (*GetPricingRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_pricing_rule_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetPricingRuleResponse) GetPricingRule() *v1.PricingRuleModel {
	if x != nil {
		return x.PricingRule
	}
	return nil
}

// list pricing rule request
type ListPricingRulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *v1.ListPricingRuleFilter `protobuf:"bytes,3,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// pricing rule ids
	Ids []int64 `protobuf:"varint,5,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,10,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *ListPricingRulesRequest) Reset() {
	*x = ListPricingRulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRulesRequest) ProtoMessage() {}

func (x *ListPricingRulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRulesRequest.ProtoReflect.Descriptor instead.
func (*ListPricingRulesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_pricing_rule_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListPricingRulesRequest) GetFilter() *v1.ListPricingRuleFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListPricingRulesRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListPricingRulesRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListPricingRulesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// list pricing rule response
type ListPricingRulesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pricing_rule
	PricingRules []*v1.PricingRuleModel `protobuf:"bytes,1,rep,name=pricing_rules,json=pricingRules,proto3" json:"pricing_rules,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListPricingRulesResponse) Reset() {
	*x = ListPricingRulesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRulesResponse) ProtoMessage() {}

func (x *ListPricingRulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRulesResponse.ProtoReflect.Descriptor instead.
func (*ListPricingRulesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_pricing_rule_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListPricingRulesResponse) GetPricingRules() []*v1.PricingRuleModel {
	if x != nil {
		return x.PricingRules
	}
	return nil
}

func (x *ListPricingRulesResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// calculate pricing rule request
type CalculatePricingRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail list
	PetDetails []*v1.PetDetailCalculateDef `protobuf:"bytes,1,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// pricing_rule def
	PricingRuleUpsertDef *v1.PricingRuleUpsertDef `protobuf:"bytes,2,opt,name=pricing_rule_upsert_def,json=pricingRuleUpsertDef,proto3,oneof" json:"pricing_rule_upsert_def,omitempty"`
	// calculate for preview
	IsPreview bool `protobuf:"varint,3,opt,name=is_preview,json=isPreview,proto3" json:"is_preview,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,9,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,10,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *CalculatePricingRuleRequest) Reset() {
	*x = CalculatePricingRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculatePricingRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculatePricingRuleRequest) ProtoMessage() {}

func (x *CalculatePricingRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculatePricingRuleRequest.ProtoReflect.Descriptor instead.
func (*CalculatePricingRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_pricing_rule_service_proto_rawDescGZIP(), []int{6}
}

func (x *CalculatePricingRuleRequest) GetPetDetails() []*v1.PetDetailCalculateDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *CalculatePricingRuleRequest) GetPricingRuleUpsertDef() *v1.PricingRuleUpsertDef {
	if x != nil {
		return x.PricingRuleUpsertDef
	}
	return nil
}

func (x *CalculatePricingRuleRequest) GetIsPreview() bool {
	if x != nil {
		return x.IsPreview
	}
	return false
}

func (x *CalculatePricingRuleRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CalculatePricingRuleRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// calculate pricing rule response
type CalculatePricingRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail list
	PetDetails []*v1.PetDetailCalculateResultDef `protobuf:"bytes,1,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// formula for preview calculation
	Formula *string `protobuf:"bytes,2,opt,name=formula,proto3,oneof" json:"formula,omitempty"`
}

func (x *CalculatePricingRuleResponse) Reset() {
	*x = CalculatePricingRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculatePricingRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculatePricingRuleResponse) ProtoMessage() {}

func (x *CalculatePricingRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculatePricingRuleResponse.ProtoReflect.Descriptor instead.
func (*CalculatePricingRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_pricing_rule_service_proto_rawDescGZIP(), []int{7}
}

func (x *CalculatePricingRuleResponse) GetPetDetails() []*v1.PetDetailCalculateResultDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *CalculatePricingRuleResponse) GetFormula() string {
	if x != nil && x.Formula != nil {
		return *x.Formula
	}
	return ""
}

// delete pricing rule request
type DeletePricingRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *DeletePricingRuleRequest) Reset() {
	*x = DeletePricingRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePricingRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePricingRuleRequest) ProtoMessage() {}

func (x *DeletePricingRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePricingRuleRequest.ProtoReflect.Descriptor instead.
func (*DeletePricingRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_pricing_rule_service_proto_rawDescGZIP(), []int{8}
}

func (x *DeletePricingRuleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeletePricingRuleRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *DeletePricingRuleRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// delete pricing rule response
type DeletePricingRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePricingRuleResponse) Reset() {
	*x = DeletePricingRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePricingRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePricingRuleResponse) ProtoMessage() {}

func (x *DeletePricingRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePricingRuleResponse.ProtoReflect.Descriptor instead.
func (*DeletePricingRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_pricing_rule_service_proto_rawDescGZIP(), []int{9}
}

// get associated services request
type ListAssociatedServicesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item: grooming/boarding/daycare
	ServiceItemType v1.ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// service type: service/addon
	ServiceType v1.ServiceType `protobuf:"varint,2,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_type,omitempty"`
	// filter
	Filter *ListAssociatedServicesRequest_Filter `protobuf:"bytes,3,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *ListAssociatedServicesRequest) Reset() {
	*x = ListAssociatedServicesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAssociatedServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssociatedServicesRequest) ProtoMessage() {}

func (x *ListAssociatedServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssociatedServicesRequest.ProtoReflect.Descriptor instead.
func (*ListAssociatedServicesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_pricing_rule_service_proto_rawDescGZIP(), []int{10}
}

func (x *ListAssociatedServicesRequest) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *ListAssociatedServicesRequest) GetServiceType() v1.ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return v1.ServiceType(0)
}

func (x *ListAssociatedServicesRequest) GetFilter() *ListAssociatedServicesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListAssociatedServicesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// get associated services response
type ListAssociatedServicesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// associated service ids
	AssociatedServiceIds []int64 `protobuf:"varint,1,rep,packed,name=associated_service_ids,json=associatedServiceIds,proto3" json:"associated_service_ids,omitempty"`
	// all service associated
	AllServiceAssociated bool `protobuf:"varint,2,opt,name=all_service_associated,json=allServiceAssociated,proto3" json:"all_service_associated,omitempty"`
}

func (x *ListAssociatedServicesResponse) Reset() {
	*x = ListAssociatedServicesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAssociatedServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssociatedServicesResponse) ProtoMessage() {}

func (x *ListAssociatedServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssociatedServicesResponse.ProtoReflect.Descriptor instead.
func (*ListAssociatedServicesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_pricing_rule_service_proto_rawDescGZIP(), []int{11}
}

func (x *ListAssociatedServicesResponse) GetAssociatedServiceIds() []int64 {
	if x != nil {
		return x.AssociatedServiceIds
	}
	return nil
}

func (x *ListAssociatedServicesResponse) GetAllServiceAssociated() bool {
	if x != nil {
		return x.AllServiceAssociated
	}
	return false
}

// check rule name is exist request
type CheckRuleNameRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rule name
	RuleName string `protobuf:"bytes,1,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`
	// exclude pricing rule id
	ExcludePricingRuleId *int64 `protobuf:"varint,2,opt,name=exclude_pricing_rule_id,json=excludePricingRuleId,proto3,oneof" json:"exclude_pricing_rule_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *CheckRuleNameRequest) Reset() {
	*x = CheckRuleNameRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckRuleNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckRuleNameRequest) ProtoMessage() {}

func (x *CheckRuleNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckRuleNameRequest.ProtoReflect.Descriptor instead.
func (*CheckRuleNameRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_pricing_rule_service_proto_rawDescGZIP(), []int{12}
}

func (x *CheckRuleNameRequest) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *CheckRuleNameRequest) GetExcludePricingRuleId() int64 {
	if x != nil && x.ExcludePricingRuleId != nil {
		return *x.ExcludePricingRuleId
	}
	return 0
}

func (x *CheckRuleNameRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// check rule name is exist response
type CheckRuleNameResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rule name is exist
	IsExist bool `protobuf:"varint,1,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"`
}

func (x *CheckRuleNameResponse) Reset() {
	*x = CheckRuleNameResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckRuleNameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckRuleNameResponse) ProtoMessage() {}

func (x *CheckRuleNameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckRuleNameResponse.ProtoReflect.Descriptor instead.
func (*CheckRuleNameResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_pricing_rule_service_proto_rawDescGZIP(), []int{13}
}

func (x *CheckRuleNameResponse) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

// filter
type ListAssociatedServicesRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// exclude pricing rule id
	ExcludePricingRuleId *int64 `protobuf:"varint,1,opt,name=exclude_pricing_rule_id,json=excludePricingRuleId,proto3,oneof" json:"exclude_pricing_rule_id,omitempty"`
	// rule group type
	RuleGroupType *v1.RuleGroupType `protobuf:"varint,2,opt,name=rule_group_type,json=ruleGroupType,proto3,enum=moego.models.offering.v1.RuleGroupType,oneof" json:"rule_group_type,omitempty"`
}

func (x *ListAssociatedServicesRequest_Filter) Reset() {
	*x = ListAssociatedServicesRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAssociatedServicesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssociatedServicesRequest_Filter) ProtoMessage() {}

func (x *ListAssociatedServicesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssociatedServicesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListAssociatedServicesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_pricing_rule_service_proto_rawDescGZIP(), []int{10, 0}
}

func (x *ListAssociatedServicesRequest_Filter) GetExcludePricingRuleId() int64 {
	if x != nil && x.ExcludePricingRuleId != nil {
		return *x.ExcludePricingRuleId
	}
	return 0
}

func (x *ListAssociatedServicesRequest_Filter) GetRuleGroupType() v1.RuleGroupType {
	if x != nil && x.RuleGroupType != nil {
		return *x.RuleGroupType
	}
	return v1.RuleGroupType(0)
}

var File_moego_service_offering_v1_pricing_rule_service_proto protoreflect.FileDescriptor

var file_moego_service_offering_v1_pricing_rule_service_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd7, 0x01, 0x0a, 0x18, 0x55,
	0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x6f, 0x0a, 0x17, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x75, 0x70, 0x73, 0x65, 0x72, 0x74, 0x5f, 0x64,
	0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x55,
	0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x14, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x55,
	0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x65, 0x66, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x22, 0x6a, 0x0a, 0x19, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4d, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x22, 0x58, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x67, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x22, 0x81, 0x02, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x4c, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x48, 0x00, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x22, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa,
	0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x03, 0x69, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x42, 0x09, 0x0a, 0x07,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xaf, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xf2, 0x02, 0x0a, 0x1b, 0x43, 0x61,
	0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5a, 0x0a, 0x0b, 0x70, 0x65, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x6a, 0x0a, 0x17, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x75, 0x70, 0x73, 0x65, 0x72, 0x74, 0x5f, 0x64, 0x65, 0x66,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x55, 0x70, 0x73,
	0x65, 0x72, 0x74, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x14, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x65, 0x66, 0x88, 0x01,
	0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72,
	0x75, 0x6c, 0x65, 0x5f, 0x75, 0x70, 0x73, 0x65, 0x72, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x22, 0xa1,
	0x01, 0x0a, 0x1c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x56, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x70, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x75,
	0x6c, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x66, 0x6f, 0x72, 0x6d,
	0x75, 0x6c, 0x61, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x75,
	0x6c, 0x61, 0x22, 0x7f, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x22, 0x1b, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0xcb, 0x04, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x54, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5c, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x1a, 0xdf, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x17,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x14, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x60, 0x0a, 0x0f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48,
	0x01, 0x52, 0x0d, 0x72, 0x75, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65,
	0x88, 0x01, 0x01, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x42,
	0x12, 0x0a, 0x10, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x8c,
	0x01, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65,
	0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x14, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x6c, 0x6c, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x61, 0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x22, 0xc8, 0x01,
	0x0a, 0x14, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05,
	0x10, 0x01, 0x18, 0x96, 0x01, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x43, 0x0a, 0x17, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x14, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x42, 0x1a, 0x0a, 0x18,
	0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x22, 0x32, 0x0a, 0x15, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x52, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x32, 0xbc, 0x07, 0x0a,
	0x12, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x7a, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x30, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x80, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x8c, 0x01, 0x0a, 0x14, 0x43, 0x61, 0x6c,
	0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c,
	0x65, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x83, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x92, 0x01,
	0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88,
	0x02, 0x01, 0x12, 0x77, 0x0a, 0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x42, 0x83, 0x01, 0x0a, 0x21,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x76, 0x63, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_offering_v1_pricing_rule_service_proto_rawDescOnce sync.Once
	file_moego_service_offering_v1_pricing_rule_service_proto_rawDescData = file_moego_service_offering_v1_pricing_rule_service_proto_rawDesc
)

func file_moego_service_offering_v1_pricing_rule_service_proto_rawDescGZIP() []byte {
	file_moego_service_offering_v1_pricing_rule_service_proto_rawDescOnce.Do(func() {
		file_moego_service_offering_v1_pricing_rule_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_offering_v1_pricing_rule_service_proto_rawDescData)
	})
	return file_moego_service_offering_v1_pricing_rule_service_proto_rawDescData
}

var file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_moego_service_offering_v1_pricing_rule_service_proto_goTypes = []interface{}{
	(*UpsertPricingRuleRequest)(nil),             // 0: moego.service.offering.v1.UpsertPricingRuleRequest
	(*UpsertPricingRuleResponse)(nil),            // 1: moego.service.offering.v1.UpsertPricingRuleResponse
	(*GetPricingRuleRequest)(nil),                // 2: moego.service.offering.v1.GetPricingRuleRequest
	(*GetPricingRuleResponse)(nil),               // 3: moego.service.offering.v1.GetPricingRuleResponse
	(*ListPricingRulesRequest)(nil),              // 4: moego.service.offering.v1.ListPricingRulesRequest
	(*ListPricingRulesResponse)(nil),             // 5: moego.service.offering.v1.ListPricingRulesResponse
	(*CalculatePricingRuleRequest)(nil),          // 6: moego.service.offering.v1.CalculatePricingRuleRequest
	(*CalculatePricingRuleResponse)(nil),         // 7: moego.service.offering.v1.CalculatePricingRuleResponse
	(*DeletePricingRuleRequest)(nil),             // 8: moego.service.offering.v1.DeletePricingRuleRequest
	(*DeletePricingRuleResponse)(nil),            // 9: moego.service.offering.v1.DeletePricingRuleResponse
	(*ListAssociatedServicesRequest)(nil),        // 10: moego.service.offering.v1.ListAssociatedServicesRequest
	(*ListAssociatedServicesResponse)(nil),       // 11: moego.service.offering.v1.ListAssociatedServicesResponse
	(*CheckRuleNameRequest)(nil),                 // 12: moego.service.offering.v1.CheckRuleNameRequest
	(*CheckRuleNameResponse)(nil),                // 13: moego.service.offering.v1.CheckRuleNameResponse
	(*ListAssociatedServicesRequest_Filter)(nil), // 14: moego.service.offering.v1.ListAssociatedServicesRequest.Filter
	(*v1.PricingRuleUpsertDef)(nil),              // 15: moego.models.offering.v1.PricingRuleUpsertDef
	(*v1.PricingRuleModel)(nil),                  // 16: moego.models.offering.v1.PricingRuleModel
	(*v1.ListPricingRuleFilter)(nil),             // 17: moego.models.offering.v1.ListPricingRuleFilter
	(*v2.PaginationRequest)(nil),                 // 18: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),                // 19: moego.utils.v2.PaginationResponse
	(*v1.PetDetailCalculateDef)(nil),             // 20: moego.models.offering.v1.PetDetailCalculateDef
	(*v1.PetDetailCalculateResultDef)(nil),       // 21: moego.models.offering.v1.PetDetailCalculateResultDef
	(v1.ServiceItemType)(0),                      // 22: moego.models.offering.v1.ServiceItemType
	(v1.ServiceType)(0),                          // 23: moego.models.offering.v1.ServiceType
	(v1.RuleGroupType)(0),                        // 24: moego.models.offering.v1.RuleGroupType
}
var file_moego_service_offering_v1_pricing_rule_service_proto_depIdxs = []int32{
	15, // 0: moego.service.offering.v1.UpsertPricingRuleRequest.pricing_rule_upsert_def:type_name -> moego.models.offering.v1.PricingRuleUpsertDef
	16, // 1: moego.service.offering.v1.UpsertPricingRuleResponse.pricing_rule:type_name -> moego.models.offering.v1.PricingRuleModel
	16, // 2: moego.service.offering.v1.GetPricingRuleResponse.pricing_rule:type_name -> moego.models.offering.v1.PricingRuleModel
	17, // 3: moego.service.offering.v1.ListPricingRulesRequest.filter:type_name -> moego.models.offering.v1.ListPricingRuleFilter
	18, // 4: moego.service.offering.v1.ListPricingRulesRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	16, // 5: moego.service.offering.v1.ListPricingRulesResponse.pricing_rules:type_name -> moego.models.offering.v1.PricingRuleModel
	19, // 6: moego.service.offering.v1.ListPricingRulesResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	20, // 7: moego.service.offering.v1.CalculatePricingRuleRequest.pet_details:type_name -> moego.models.offering.v1.PetDetailCalculateDef
	15, // 8: moego.service.offering.v1.CalculatePricingRuleRequest.pricing_rule_upsert_def:type_name -> moego.models.offering.v1.PricingRuleUpsertDef
	21, // 9: moego.service.offering.v1.CalculatePricingRuleResponse.pet_details:type_name -> moego.models.offering.v1.PetDetailCalculateResultDef
	22, // 10: moego.service.offering.v1.ListAssociatedServicesRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	23, // 11: moego.service.offering.v1.ListAssociatedServicesRequest.service_type:type_name -> moego.models.offering.v1.ServiceType
	14, // 12: moego.service.offering.v1.ListAssociatedServicesRequest.filter:type_name -> moego.service.offering.v1.ListAssociatedServicesRequest.Filter
	24, // 13: moego.service.offering.v1.ListAssociatedServicesRequest.Filter.rule_group_type:type_name -> moego.models.offering.v1.RuleGroupType
	0,  // 14: moego.service.offering.v1.PricingRuleService.UpsertPricingRule:input_type -> moego.service.offering.v1.UpsertPricingRuleRequest
	2,  // 15: moego.service.offering.v1.PricingRuleService.GetPricingRule:input_type -> moego.service.offering.v1.GetPricingRuleRequest
	4,  // 16: moego.service.offering.v1.PricingRuleService.ListPricingRules:input_type -> moego.service.offering.v1.ListPricingRulesRequest
	6,  // 17: moego.service.offering.v1.PricingRuleService.CalculatePricingRule:input_type -> moego.service.offering.v1.CalculatePricingRuleRequest
	8,  // 18: moego.service.offering.v1.PricingRuleService.DeletePricingRule:input_type -> moego.service.offering.v1.DeletePricingRuleRequest
	10, // 19: moego.service.offering.v1.PricingRuleService.ListAssociatedServices:input_type -> moego.service.offering.v1.ListAssociatedServicesRequest
	12, // 20: moego.service.offering.v1.PricingRuleService.CheckRuleName:input_type -> moego.service.offering.v1.CheckRuleNameRequest
	1,  // 21: moego.service.offering.v1.PricingRuleService.UpsertPricingRule:output_type -> moego.service.offering.v1.UpsertPricingRuleResponse
	3,  // 22: moego.service.offering.v1.PricingRuleService.GetPricingRule:output_type -> moego.service.offering.v1.GetPricingRuleResponse
	5,  // 23: moego.service.offering.v1.PricingRuleService.ListPricingRules:output_type -> moego.service.offering.v1.ListPricingRulesResponse
	7,  // 24: moego.service.offering.v1.PricingRuleService.CalculatePricingRule:output_type -> moego.service.offering.v1.CalculatePricingRuleResponse
	9,  // 25: moego.service.offering.v1.PricingRuleService.DeletePricingRule:output_type -> moego.service.offering.v1.DeletePricingRuleResponse
	11, // 26: moego.service.offering.v1.PricingRuleService.ListAssociatedServices:output_type -> moego.service.offering.v1.ListAssociatedServicesResponse
	13, // 27: moego.service.offering.v1.PricingRuleService.CheckRuleName:output_type -> moego.service.offering.v1.CheckRuleNameResponse
	21, // [21:28] is the sub-list for method output_type
	14, // [14:21] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_moego_service_offering_v1_pricing_rule_service_proto_init() }
func file_moego_service_offering_v1_pricing_rule_service_proto_init() {
	if File_moego_service_offering_v1_pricing_rule_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertPricingRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertPricingRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPricingRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPricingRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRulesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculatePricingRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculatePricingRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePricingRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePricingRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAssociatedServicesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAssociatedServicesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckRuleNameRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckRuleNameResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAssociatedServicesRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes[14].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_offering_v1_pricing_rule_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_offering_v1_pricing_rule_service_proto_goTypes,
		DependencyIndexes: file_moego_service_offering_v1_pricing_rule_service_proto_depIdxs,
		MessageInfos:      file_moego_service_offering_v1_pricing_rule_service_proto_msgTypes,
	}.Build()
	File_moego_service_offering_v1_pricing_rule_service_proto = out.File
	file_moego_service_offering_v1_pricing_rule_service_proto_rawDesc = nil
	file_moego_service_offering_v1_pricing_rule_service_proto_goTypes = nil
	file_moego_service_offering_v1_pricing_rule_service_proto_depIdxs = nil
}
