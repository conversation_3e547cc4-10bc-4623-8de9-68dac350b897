// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/offering/v1/lodging_unit_service.proto

package offeringsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// *
// Request body for batch create LodgingUnit service
type BatchCreateLodgingUnitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the lodging unit
	LodgingUnitParamsList []*CreateLodgingUnitRequest `protobuf:"bytes,1,rep,name=lodging_unit_params_list,json=lodgingUnitParamsList,proto3" json:"lodging_unit_params_list,omitempty"`
}

func (x *BatchCreateLodgingUnitRequest) Reset() {
	*x = BatchCreateLodgingUnitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCreateLodgingUnitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateLodgingUnitRequest) ProtoMessage() {}

func (x *BatchCreateLodgingUnitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateLodgingUnitRequest.ProtoReflect.Descriptor instead.
func (*BatchCreateLodgingUnitRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP(), []int{0}
}

func (x *BatchCreateLodgingUnitRequest) GetLodgingUnitParamsList() []*CreateLodgingUnitRequest {
	if x != nil {
		return x.LodgingUnitParamsList
	}
	return nil
}

// *
// Response body for create LodgingUnit
type BatchCreateLodgingUnitResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging unit list
	LodgingUnitList []*v1.LodgingUnitModel `protobuf:"bytes,1,rep,name=lodging_unit_list,json=lodgingUnitList,proto3" json:"lodging_unit_list,omitempty"`
}

func (x *BatchCreateLodgingUnitResponse) Reset() {
	*x = BatchCreateLodgingUnitResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCreateLodgingUnitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateLodgingUnitResponse) ProtoMessage() {}

func (x *BatchCreateLodgingUnitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateLodgingUnitResponse.ProtoReflect.Descriptor instead.
func (*BatchCreateLodgingUnitResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP(), []int{1}
}

func (x *BatchCreateLodgingUnitResponse) GetLodgingUnitList() []*v1.LodgingUnitModel {
	if x != nil {
		return x.LodgingUnitList
	}
	return nil
}

// *
// Request body for create LodgingUnit service
type CreateLodgingUnitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// token staff id
	TokenStaffId int64 `protobuf:"varint,3,opt,name=token_staff_id,json=tokenStaffId,proto3" json:"token_staff_id,omitempty"`
	// name of the lodging unit
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// lodging type of this lodging unit
	LodgingTypeId int64 `protobuf:"varint,5,opt,name=lodging_type_id,json=lodgingTypeId,proto3" json:"lodging_type_id,omitempty"`
	// camera id
	CameraId *int64 `protobuf:"varint,6,opt,name=camera_id,json=cameraId,proto3,oneof" json:"camera_id,omitempty"`
}

func (x *CreateLodgingUnitRequest) Reset() {
	*x = CreateLodgingUnitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLodgingUnitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLodgingUnitRequest) ProtoMessage() {}

func (x *CreateLodgingUnitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLodgingUnitRequest.ProtoReflect.Descriptor instead.
func (*CreateLodgingUnitRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateLodgingUnitRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateLodgingUnitRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateLodgingUnitRequest) GetTokenStaffId() int64 {
	if x != nil {
		return x.TokenStaffId
	}
	return 0
}

func (x *CreateLodgingUnitRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateLodgingUnitRequest) GetLodgingTypeId() int64 {
	if x != nil {
		return x.LodgingTypeId
	}
	return 0
}

func (x *CreateLodgingUnitRequest) GetCameraId() int64 {
	if x != nil && x.CameraId != nil {
		return *x.CameraId
	}
	return 0
}

// *
// Request body for update LodgingUnit
type UpdateLodgingUnitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the lodging unit
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// token staff id
	TokenStaffId int64 `protobuf:"varint,2,opt,name=token_staff_id,json=tokenStaffId,proto3" json:"token_staff_id,omitempty"`
	// company id for authorization
	CompanyId *int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// name of the lodging unit
	Name *string `protobuf:"bytes,4,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// camera id
	CameraId *int64 `protobuf:"varint,5,opt,name=camera_id,json=cameraId,proto3,oneof" json:"camera_id,omitempty"`
}

func (x *UpdateLodgingUnitRequest) Reset() {
	*x = UpdateLodgingUnitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLodgingUnitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLodgingUnitRequest) ProtoMessage() {}

func (x *UpdateLodgingUnitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLodgingUnitRequest.ProtoReflect.Descriptor instead.
func (*UpdateLodgingUnitRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateLodgingUnitRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateLodgingUnitRequest) GetTokenStaffId() int64 {
	if x != nil {
		return x.TokenStaffId
	}
	return 0
}

func (x *UpdateLodgingUnitRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *UpdateLodgingUnitRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateLodgingUnitRequest) GetCameraId() int64 {
	if x != nil && x.CameraId != nil {
		return *x.CameraId
	}
	return 0
}

// *
// Response body for update LodgingUnit
type UpdateLodgingUnitResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging unit
	LodgingUnit *v1.LodgingUnitModel `protobuf:"bytes,1,opt,name=lodging_unit,json=lodgingUnit,proto3" json:"lodging_unit,omitempty"`
}

func (x *UpdateLodgingUnitResponse) Reset() {
	*x = UpdateLodgingUnitResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLodgingUnitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLodgingUnitResponse) ProtoMessage() {}

func (x *UpdateLodgingUnitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLodgingUnitResponse.ProtoReflect.Descriptor instead.
func (*UpdateLodgingUnitResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateLodgingUnitResponse) GetLodgingUnit() *v1.LodgingUnitModel {
	if x != nil {
		return x.LodgingUnit
	}
	return nil
}

// *
// Request body for delete LodgingUnit
type DeleteLodgingUnitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the lodging unit
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// token staff id
	TokenStaffId int64 `protobuf:"varint,2,opt,name=token_staff_id,json=tokenStaffId,proto3" json:"token_staff_id,omitempty"`
	// company id for authorization
	CompanyId *int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
}

func (x *DeleteLodgingUnitRequest) Reset() {
	*x = DeleteLodgingUnitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLodgingUnitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLodgingUnitRequest) ProtoMessage() {}

func (x *DeleteLodgingUnitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLodgingUnitRequest.ProtoReflect.Descriptor instead.
func (*DeleteLodgingUnitRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteLodgingUnitRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteLodgingUnitRequest) GetTokenStaffId() int64 {
	if x != nil {
		return x.TokenStaffId
	}
	return 0
}

func (x *DeleteLodgingUnitRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// *
// Response body for delete LodgingUnit
type DeleteLodgingUnitResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteLodgingUnitResponse) Reset() {
	*x = DeleteLodgingUnitResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLodgingUnitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLodgingUnitResponse) ProtoMessage() {}

func (x *DeleteLodgingUnitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLodgingUnitResponse.ProtoReflect.Descriptor instead.
func (*DeleteLodgingUnitResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP(), []int{6}
}

// *
// Request body for batch delete LodgingUnit
type BatchDeleteLodgingUnitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id list of the lodging unit
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// token staff id
	TokenStaffId *int64 `protobuf:"varint,2,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof" json:"token_staff_id,omitempty"`
	// company id for authorization
	CompanyId *int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
}

func (x *BatchDeleteLodgingUnitRequest) Reset() {
	*x = BatchDeleteLodgingUnitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteLodgingUnitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteLodgingUnitRequest) ProtoMessage() {}

func (x *BatchDeleteLodgingUnitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteLodgingUnitRequest.ProtoReflect.Descriptor instead.
func (*BatchDeleteLodgingUnitRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP(), []int{7}
}

func (x *BatchDeleteLodgingUnitRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *BatchDeleteLodgingUnitRequest) GetTokenStaffId() int64 {
	if x != nil && x.TokenStaffId != nil {
		return *x.TokenStaffId
	}
	return 0
}

func (x *BatchDeleteLodgingUnitRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// *
// Response body for batch delete LodgingUnit
type BatchDeleteLodgingUnitResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchDeleteLodgingUnitResponse) Reset() {
	*x = BatchDeleteLodgingUnitResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteLodgingUnitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteLodgingUnitResponse) ProtoMessage() {}

func (x *BatchDeleteLodgingUnitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteLodgingUnitResponse.ProtoReflect.Descriptor instead.
func (*BatchDeleteLodgingUnitResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP(), []int{8}
}

// *
// Request body for get LodgingUnit list
type GetLodgingUnitListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// lodging type id list
	TypeIdList *LodgingTypeIdList `protobuf:"bytes,3,opt,name=type_id_list,json=typeIdList,proto3,oneof" json:"type_id_list,omitempty"`
	// service id
	ServiceId *int64 `protobuf:"varint,4,opt,name=service_id,json=serviceId,proto3,oneof" json:"service_id,omitempty"`
	// evaluation service id
	EvaluationServiceId *int64 `protobuf:"varint,5,opt,name=evaluation_service_id,json=evaluationServiceId,proto3,oneof" json:"evaluation_service_id,omitempty"`
	// lodging unit ids
	UnitIds []int64 `protobuf:"varint,6,rep,packed,name=unit_ids,json=unitIds,proto3" json:"unit_ids,omitempty"`
	// camera ids
	CameraIds []int64 `protobuf:"varint,7,rep,packed,name=camera_ids,json=cameraIds,proto3" json:"camera_ids,omitempty"`
}

func (x *GetLodgingUnitListRequest) Reset() {
	*x = GetLodgingUnitListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLodgingUnitListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLodgingUnitListRequest) ProtoMessage() {}

func (x *GetLodgingUnitListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLodgingUnitListRequest.ProtoReflect.Descriptor instead.
func (*GetLodgingUnitListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetLodgingUnitListRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetLodgingUnitListRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetLodgingUnitListRequest) GetTypeIdList() *LodgingTypeIdList {
	if x != nil {
		return x.TypeIdList
	}
	return nil
}

func (x *GetLodgingUnitListRequest) GetServiceId() int64 {
	if x != nil && x.ServiceId != nil {
		return *x.ServiceId
	}
	return 0
}

func (x *GetLodgingUnitListRequest) GetEvaluationServiceId() int64 {
	if x != nil && x.EvaluationServiceId != nil {
		return *x.EvaluationServiceId
	}
	return 0
}

func (x *GetLodgingUnitListRequest) GetUnitIds() []int64 {
	if x != nil {
		return x.UnitIds
	}
	return nil
}

func (x *GetLodgingUnitListRequest) GetCameraIds() []int64 {
	if x != nil {
		return x.CameraIds
	}
	return nil
}

// *
// Request body for lodging type id list
type LodgingTypeIdList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging type id list
	IdList []int64 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
}

func (x *LodgingTypeIdList) Reset() {
	*x = LodgingTypeIdList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingTypeIdList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingTypeIdList) ProtoMessage() {}

func (x *LodgingTypeIdList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingTypeIdList.ProtoReflect.Descriptor instead.
func (*LodgingTypeIdList) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP(), []int{10}
}

func (x *LodgingTypeIdList) GetIdList() []int64 {
	if x != nil {
		return x.IdList
	}
	return nil
}

// *
// get LodgingUnit list response
type GetLodgingUnitListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging unit list
	LodgingUnitList []*v1.LodgingUnitModel `protobuf:"bytes,1,rep,name=lodging_unit_list,json=lodgingUnitList,proto3" json:"lodging_unit_list,omitempty"`
}

func (x *GetLodgingUnitListResponse) Reset() {
	*x = GetLodgingUnitListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLodgingUnitListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLodgingUnitListResponse) ProtoMessage() {}

func (x *GetLodgingUnitListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLodgingUnitListResponse.ProtoReflect.Descriptor instead.
func (*GetLodgingUnitListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetLodgingUnitListResponse) GetLodgingUnitList() []*v1.LodgingUnitModel {
	if x != nil {
		return x.LodgingUnitList
	}
	return nil
}

// *
// Request body for get LodgingUnit list
type MGetLodgingUnitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging unit id list
	IdList []int64 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
}

func (x *MGetLodgingUnitRequest) Reset() {
	*x = MGetLodgingUnitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MGetLodgingUnitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MGetLodgingUnitRequest) ProtoMessage() {}

func (x *MGetLodgingUnitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MGetLodgingUnitRequest.ProtoReflect.Descriptor instead.
func (*MGetLodgingUnitRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP(), []int{12}
}

func (x *MGetLodgingUnitRequest) GetIdList() []int64 {
	if x != nil {
		return x.IdList
	}
	return nil
}

// *
// get LodgingUnit list response
type MGetLodgingUnitResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging unit list
	LodgingUnitList []*v1.LodgingUnitModel `protobuf:"bytes,1,rep,name=lodging_unit_list,json=lodgingUnitList,proto3" json:"lodging_unit_list,omitempty"`
	// relate lodging type list
	LodgingTypeList []*v1.LodgingTypeModel `protobuf:"bytes,2,rep,name=lodging_type_list,json=lodgingTypeList,proto3" json:"lodging_type_list,omitempty"`
}

func (x *MGetLodgingUnitResponse) Reset() {
	*x = MGetLodgingUnitResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MGetLodgingUnitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MGetLodgingUnitResponse) ProtoMessage() {}

func (x *MGetLodgingUnitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MGetLodgingUnitResponse.ProtoReflect.Descriptor instead.
func (*MGetLodgingUnitResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP(), []int{13}
}

func (x *MGetLodgingUnitResponse) GetLodgingUnitList() []*v1.LodgingUnitModel {
	if x != nil {
		return x.LodgingUnitList
	}
	return nil
}

func (x *MGetLodgingUnitResponse) GetLodgingTypeList() []*v1.LodgingTypeModel {
	if x != nil {
		return x.LodgingTypeList
	}
	return nil
}

// The request for sort lodging unit by ids
type SortLodgingUnitByIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ids of lodging unit to sort
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// the company id
	CompanyId *int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// the login staff id
	StaffId *int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
}

func (x *SortLodgingUnitByIdsRequest) Reset() {
	*x = SortLodgingUnitByIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortLodgingUnitByIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortLodgingUnitByIdsRequest) ProtoMessage() {}

func (x *SortLodgingUnitByIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortLodgingUnitByIdsRequest.ProtoReflect.Descriptor instead.
func (*SortLodgingUnitByIdsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP(), []int{14}
}

func (x *SortLodgingUnitByIdsRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *SortLodgingUnitByIdsRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *SortLodgingUnitByIdsRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

// The response for sort lodging unit by ids
type SortLodgingUnitByIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortLodgingUnitByIdsResponse) Reset() {
	*x = SortLodgingUnitByIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortLodgingUnitByIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortLodgingUnitByIdsResponse) ProtoMessage() {}

func (x *SortLodgingUnitByIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortLodgingUnitByIdsResponse.ProtoReflect.Descriptor instead.
func (*SortLodgingUnitByIdsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP(), []int{15}
}

var File_moego_service_offering_v1_lodging_unit_service_proto protoreflect.FileDescriptor

var file_moego_service_offering_v1_lodging_unit_service_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x97, 0x01, 0x0a, 0x1d, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x76, 0x0a, 0x18, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f,
	0x75, 0x6e, 0x69, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x15, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e,
	0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x78, 0x0a, 0x1e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56,
	0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e,
	0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xa5, 0x02, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28,
	0x00, 0x48, 0x00, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x22, 0x85,
	0x02, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x23, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x01, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28,
	0x00, 0x48, 0x02, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x42,
	0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x63, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x22, 0x6a, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x0c, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e,
	0x69, 0x74, 0x22, 0x9e, 0x01, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x22, 0x1b, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0xc8, 0x01, 0x0a, 0x1d, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x24, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42,
	0x12, 0xfa, 0x42, 0x0f, 0x92, 0x01, 0x0c, 0x08, 0x01, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x32, 0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x22, 0x20, 0x0a, 0x1e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xde, 0x03,
	0x0a, 0x19, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x53, 0x0a, 0x0c, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x01, 0x52, 0x0a, 0x74, 0x79, 0x70, 0x65, 0x49, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x48, 0x02, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x15, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x03, 0x52, 0x13,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x08, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a,
	0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x75, 0x6e, 0x69, 0x74,
	0x49, 0x64, 0x73, 0x12, 0x2f, 0x0a, 0x0a, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10,
	0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x61, 0x6d, 0x65, 0x72,
	0x61, 0x49, 0x64, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x22, 0x36,
	0x0a, 0x11, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x00, 0x52, 0x06,
	0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x74, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f,
	0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0f, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x3b, 0x0a, 0x16,
	0x4d, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08,
	0x00, 0x52, 0x06, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xc9, 0x01, 0x0a, 0x17, 0x4d, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0f, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x56, 0x0a,
	0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xb3, 0x01, 0x0a, 0x1b, 0x53, 0x6f, 0x72, 0x74, 0x4c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x08, 0x01, 0x18, 0x01, 0x22, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x01, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x0b,
	0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x22, 0x1e, 0x0a, 0x1c, 0x53,
	0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x42, 0x79,
	0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xbc, 0x07, 0x0a, 0x12,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x8d, 0x01, 0x0a, 0x16, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x7e, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x7e, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x8d, 0x01, 0x0a, 0x16, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x55, 0x6e, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x55, 0x6e, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x0f, 0x4d, 0x47, 0x65, 0x74, 0x4c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x87, 0x01, 0x0a, 0x14, 0x53, 0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x55, 0x6e, 0x69, 0x74, 0x42, 0x79, 0x49, 0x64, 0x73, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x55, 0x6e, 0x69, 0x74, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f,
	0x72, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x42, 0x79, 0x49,
	0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x83, 0x01, 0x0a, 0x21, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x76, 0x63, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_offering_v1_lodging_unit_service_proto_rawDescOnce sync.Once
	file_moego_service_offering_v1_lodging_unit_service_proto_rawDescData = file_moego_service_offering_v1_lodging_unit_service_proto_rawDesc
)

func file_moego_service_offering_v1_lodging_unit_service_proto_rawDescGZIP() []byte {
	file_moego_service_offering_v1_lodging_unit_service_proto_rawDescOnce.Do(func() {
		file_moego_service_offering_v1_lodging_unit_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_offering_v1_lodging_unit_service_proto_rawDescData)
	})
	return file_moego_service_offering_v1_lodging_unit_service_proto_rawDescData
}

var file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_moego_service_offering_v1_lodging_unit_service_proto_goTypes = []interface{}{
	(*BatchCreateLodgingUnitRequest)(nil),  // 0: moego.service.offering.v1.BatchCreateLodgingUnitRequest
	(*BatchCreateLodgingUnitResponse)(nil), // 1: moego.service.offering.v1.BatchCreateLodgingUnitResponse
	(*CreateLodgingUnitRequest)(nil),       // 2: moego.service.offering.v1.CreateLodgingUnitRequest
	(*UpdateLodgingUnitRequest)(nil),       // 3: moego.service.offering.v1.UpdateLodgingUnitRequest
	(*UpdateLodgingUnitResponse)(nil),      // 4: moego.service.offering.v1.UpdateLodgingUnitResponse
	(*DeleteLodgingUnitRequest)(nil),       // 5: moego.service.offering.v1.DeleteLodgingUnitRequest
	(*DeleteLodgingUnitResponse)(nil),      // 6: moego.service.offering.v1.DeleteLodgingUnitResponse
	(*BatchDeleteLodgingUnitRequest)(nil),  // 7: moego.service.offering.v1.BatchDeleteLodgingUnitRequest
	(*BatchDeleteLodgingUnitResponse)(nil), // 8: moego.service.offering.v1.BatchDeleteLodgingUnitResponse
	(*GetLodgingUnitListRequest)(nil),      // 9: moego.service.offering.v1.GetLodgingUnitListRequest
	(*LodgingTypeIdList)(nil),              // 10: moego.service.offering.v1.LodgingTypeIdList
	(*GetLodgingUnitListResponse)(nil),     // 11: moego.service.offering.v1.GetLodgingUnitListResponse
	(*MGetLodgingUnitRequest)(nil),         // 12: moego.service.offering.v1.MGetLodgingUnitRequest
	(*MGetLodgingUnitResponse)(nil),        // 13: moego.service.offering.v1.MGetLodgingUnitResponse
	(*SortLodgingUnitByIdsRequest)(nil),    // 14: moego.service.offering.v1.SortLodgingUnitByIdsRequest
	(*SortLodgingUnitByIdsResponse)(nil),   // 15: moego.service.offering.v1.SortLodgingUnitByIdsResponse
	(*v1.LodgingUnitModel)(nil),            // 16: moego.models.offering.v1.LodgingUnitModel
	(*v1.LodgingTypeModel)(nil),            // 17: moego.models.offering.v1.LodgingTypeModel
}
var file_moego_service_offering_v1_lodging_unit_service_proto_depIdxs = []int32{
	2,  // 0: moego.service.offering.v1.BatchCreateLodgingUnitRequest.lodging_unit_params_list:type_name -> moego.service.offering.v1.CreateLodgingUnitRequest
	16, // 1: moego.service.offering.v1.BatchCreateLodgingUnitResponse.lodging_unit_list:type_name -> moego.models.offering.v1.LodgingUnitModel
	16, // 2: moego.service.offering.v1.UpdateLodgingUnitResponse.lodging_unit:type_name -> moego.models.offering.v1.LodgingUnitModel
	10, // 3: moego.service.offering.v1.GetLodgingUnitListRequest.type_id_list:type_name -> moego.service.offering.v1.LodgingTypeIdList
	16, // 4: moego.service.offering.v1.GetLodgingUnitListResponse.lodging_unit_list:type_name -> moego.models.offering.v1.LodgingUnitModel
	16, // 5: moego.service.offering.v1.MGetLodgingUnitResponse.lodging_unit_list:type_name -> moego.models.offering.v1.LodgingUnitModel
	17, // 6: moego.service.offering.v1.MGetLodgingUnitResponse.lodging_type_list:type_name -> moego.models.offering.v1.LodgingTypeModel
	0,  // 7: moego.service.offering.v1.LodgingUnitService.BatchCreateLodgingUnit:input_type -> moego.service.offering.v1.BatchCreateLodgingUnitRequest
	3,  // 8: moego.service.offering.v1.LodgingUnitService.UpdateLodgingUnit:input_type -> moego.service.offering.v1.UpdateLodgingUnitRequest
	5,  // 9: moego.service.offering.v1.LodgingUnitService.DeleteLodgingUnit:input_type -> moego.service.offering.v1.DeleteLodgingUnitRequest
	7,  // 10: moego.service.offering.v1.LodgingUnitService.BatchDeleteLodgingUnit:input_type -> moego.service.offering.v1.BatchDeleteLodgingUnitRequest
	9,  // 11: moego.service.offering.v1.LodgingUnitService.GetLodgingUnitList:input_type -> moego.service.offering.v1.GetLodgingUnitListRequest
	12, // 12: moego.service.offering.v1.LodgingUnitService.MGetLodgingUnit:input_type -> moego.service.offering.v1.MGetLodgingUnitRequest
	14, // 13: moego.service.offering.v1.LodgingUnitService.SortLodgingUnitByIds:input_type -> moego.service.offering.v1.SortLodgingUnitByIdsRequest
	1,  // 14: moego.service.offering.v1.LodgingUnitService.BatchCreateLodgingUnit:output_type -> moego.service.offering.v1.BatchCreateLodgingUnitResponse
	4,  // 15: moego.service.offering.v1.LodgingUnitService.UpdateLodgingUnit:output_type -> moego.service.offering.v1.UpdateLodgingUnitResponse
	6,  // 16: moego.service.offering.v1.LodgingUnitService.DeleteLodgingUnit:output_type -> moego.service.offering.v1.DeleteLodgingUnitResponse
	8,  // 17: moego.service.offering.v1.LodgingUnitService.BatchDeleteLodgingUnit:output_type -> moego.service.offering.v1.BatchDeleteLodgingUnitResponse
	11, // 18: moego.service.offering.v1.LodgingUnitService.GetLodgingUnitList:output_type -> moego.service.offering.v1.GetLodgingUnitListResponse
	13, // 19: moego.service.offering.v1.LodgingUnitService.MGetLodgingUnit:output_type -> moego.service.offering.v1.MGetLodgingUnitResponse
	15, // 20: moego.service.offering.v1.LodgingUnitService.SortLodgingUnitByIds:output_type -> moego.service.offering.v1.SortLodgingUnitByIdsResponse
	14, // [14:21] is the sub-list for method output_type
	7,  // [7:14] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_moego_service_offering_v1_lodging_unit_service_proto_init() }
func file_moego_service_offering_v1_lodging_unit_service_proto_init() {
	if File_moego_service_offering_v1_lodging_unit_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCreateLodgingUnitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCreateLodgingUnitResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLodgingUnitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLodgingUnitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLodgingUnitResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLodgingUnitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLodgingUnitResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteLodgingUnitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteLodgingUnitResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLodgingUnitListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingTypeIdList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLodgingUnitListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MGetLodgingUnitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MGetLodgingUnitResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortLodgingUnitByIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortLodgingUnitByIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes[14].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_offering_v1_lodging_unit_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_offering_v1_lodging_unit_service_proto_goTypes,
		DependencyIndexes: file_moego_service_offering_v1_lodging_unit_service_proto_depIdxs,
		MessageInfos:      file_moego_service_offering_v1_lodging_unit_service_proto_msgTypes,
	}.Build()
	File_moego_service_offering_v1_lodging_unit_service_proto = out.File
	file_moego_service_offering_v1_lodging_unit_service_proto_rawDesc = nil
	file_moego_service_offering_v1_lodging_unit_service_proto_goTypes = nil
	file_moego_service_offering_v1_lodging_unit_service_proto_depIdxs = nil
}
