// @since 2024-09-19 18:00:00
// <AUTHOR> <zhang<PERSON>@moego.pet>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/offering/v1/service_vaccine_requirement_service.proto

package offeringsvcpb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// update service vaccine requirement for service request
type UpdateVaccineRequirementForServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tenant
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// service item type
	ServiceItemType v11.ServiceItemType `protobuf:"varint,2,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// vaccine id
	VaccineIds []int64 `protobuf:"varint,3,rep,packed,name=vaccine_ids,json=vaccineIds,proto3" json:"vaccine_ids,omitempty"`
}

func (x *UpdateVaccineRequirementForServiceRequest) Reset() {
	*x = UpdateVaccineRequirementForServiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVaccineRequirementForServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVaccineRequirementForServiceRequest) ProtoMessage() {}

func (x *UpdateVaccineRequirementForServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVaccineRequirementForServiceRequest.ProtoReflect.Descriptor instead.
func (*UpdateVaccineRequirementForServiceRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateVaccineRequirementForServiceRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *UpdateVaccineRequirementForServiceRequest) GetServiceItemType() v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v11.ServiceItemType(0)
}

func (x *UpdateVaccineRequirementForServiceRequest) GetVaccineIds() []int64 {
	if x != nil {
		return x.VaccineIds
	}
	return nil
}

// update service vaccine requirement for service item type response
type UpdateVaccineRequirementForServiceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateVaccineRequirementForServiceResponse) Reset() {
	*x = UpdateVaccineRequirementForServiceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVaccineRequirementForServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVaccineRequirementForServiceResponse) ProtoMessage() {}

func (x *UpdateVaccineRequirementForServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVaccineRequirementForServiceResponse.ProtoReflect.Descriptor instead.
func (*UpdateVaccineRequirementForServiceResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDescGZIP(), []int{1}
}

// update service vaccine requirement for vaccine request
type UpdateServiceRequirementForVaccineRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tenant
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// vaccine id
	VaccineId int64 `protobuf:"varint,2,opt,name=vaccine_id,json=vaccineId,proto3" json:"vaccine_id,omitempty"`
	// service item types
	ServiceItemTypes []v11.ServiceItemType `protobuf:"varint,3,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
}

func (x *UpdateServiceRequirementForVaccineRequest) Reset() {
	*x = UpdateServiceRequirementForVaccineRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceRequirementForVaccineRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceRequirementForVaccineRequest) ProtoMessage() {}

func (x *UpdateServiceRequirementForVaccineRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceRequirementForVaccineRequest.ProtoReflect.Descriptor instead.
func (*UpdateServiceRequirementForVaccineRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateServiceRequirementForVaccineRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *UpdateServiceRequirementForVaccineRequest) GetVaccineId() int64 {
	if x != nil {
		return x.VaccineId
	}
	return 0
}

func (x *UpdateServiceRequirementForVaccineRequest) GetServiceItemTypes() []v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

// update service vaccine requirement for vaccine response
type UpdateServiceRequirementForVaccineResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateServiceRequirementForVaccineResponse) Reset() {
	*x = UpdateServiceRequirementForVaccineResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceRequirementForVaccineResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceRequirementForVaccineResponse) ProtoMessage() {}

func (x *UpdateServiceRequirementForVaccineResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceRequirementForVaccineResponse.ProtoReflect.Descriptor instead.
func (*UpdateServiceRequirementForVaccineResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDescGZIP(), []int{3}
}

// list service_vaccine_requirement request
type ListServiceVaccineRequirementsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tenant
	Tenant *v1.Tenant `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// filters
	Filter *ListServiceVaccineRequirementsRequest_Filters `protobuf:"bytes,3,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *ListServiceVaccineRequirementsRequest) Reset() {
	*x = ListServiceVaccineRequirementsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceVaccineRequirementsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceVaccineRequirementsRequest) ProtoMessage() {}

func (x *ListServiceVaccineRequirementsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceVaccineRequirementsRequest.ProtoReflect.Descriptor instead.
func (*ListServiceVaccineRequirementsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListServiceVaccineRequirementsRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *ListServiceVaccineRequirementsRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServiceVaccineRequirementsRequest) GetFilter() *ListServiceVaccineRequirementsRequest_Filters {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list service_vaccine_requirement response
type ListServiceVaccineRequirementsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service_vaccine_requirements
	ServiceVaccineRequirements []*v11.ServiceVaccineRequirementModel `protobuf:"bytes,1,rep,name=service_vaccine_requirements,json=serviceVaccineRequirements,proto3" json:"service_vaccine_requirements,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListServiceVaccineRequirementsResponse) Reset() {
	*x = ListServiceVaccineRequirementsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceVaccineRequirementsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceVaccineRequirementsResponse) ProtoMessage() {}

func (x *ListServiceVaccineRequirementsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceVaccineRequirementsResponse.ProtoReflect.Descriptor instead.
func (*ListServiceVaccineRequirementsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListServiceVaccineRequirementsResponse) GetServiceVaccineRequirements() []*v11.ServiceVaccineRequirementModel {
	if x != nil {
		return x.ServiceVaccineRequirements
	}
	return nil
}

func (x *ListServiceVaccineRequirementsResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// filter, will return all if not set, otherwise will return the intersection of all non-empty filters
type ListServiceVaccineRequirementsRequest_Filters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item type
	ServiceItemTypes []v11.ServiceItemType `protobuf:"varint,1,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// vaccine ids
	VaccineIds []int64 `protobuf:"varint,2,rep,packed,name=vaccine_ids,json=vaccineIds,proto3" json:"vaccine_ids,omitempty"`
}

func (x *ListServiceVaccineRequirementsRequest_Filters) Reset() {
	*x = ListServiceVaccineRequirementsRequest_Filters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceVaccineRequirementsRequest_Filters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceVaccineRequirementsRequest_Filters) ProtoMessage() {}

func (x *ListServiceVaccineRequirementsRequest_Filters) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceVaccineRequirementsRequest_Filters.ProtoReflect.Descriptor instead.
func (*ListServiceVaccineRequirementsRequest_Filters) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ListServiceVaccineRequirementsRequest_Filters) GetServiceItemTypes() []v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *ListServiceVaccineRequirementsRequest_Filters) GetVaccineIds() []int64 {
	if x != nil {
		return x.VaccineIds
	}
	return nil
}

var File_moego_service_offering_v1_service_vaccine_requirement_service_proto protoreflect.FileDescriptor

var file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDesc = []byte{
	0x0a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x41, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x87,
	0x02, 0x0a, 0x29, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x06,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x0b, 0x76, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42,
	0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x76, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x73, 0x22, 0x2c, 0x0a, 0x2a, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x87, 0x02, 0x0a, 0x29, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0a,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x76, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x49, 0x64, 0x12, 0x6a, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x11, 0xfa, 0x42, 0x0e,
	0x92, 0x01, 0x0b, 0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x10,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x22, 0x2c, 0x0a, 0x2a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x56,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb4,
	0x03, 0x0a, 0x25, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x65, 0x0a,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x48, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x88, 0x01, 0x01, 0x1a, 0x83, 0x01, 0x0a, 0x07, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xe8, 0x01, 0x0a, 0x26, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x7a, 0x0a, 0x1c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x76, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x1a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x42, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x32, 0xb2, 0x04, 0x0a, 0x20, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xb1, 0x01, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x46, 0x6f, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x44, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x46, 0x6f, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb1, 0x01, 0x0a, 0x22, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x12, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x56, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa5, 0x01,
	0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x83, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDescOnce sync.Once
	file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDescData = file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDesc
)

func file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDescGZIP() []byte {
	file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDescOnce.Do(func() {
		file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDescData)
	})
	return file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDescData
}

var file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_service_offering_v1_service_vaccine_requirement_service_proto_goTypes = []interface{}{
	(*UpdateVaccineRequirementForServiceRequest)(nil),     // 0: moego.service.offering.v1.UpdateVaccineRequirementForServiceRequest
	(*UpdateVaccineRequirementForServiceResponse)(nil),    // 1: moego.service.offering.v1.UpdateVaccineRequirementForServiceResponse
	(*UpdateServiceRequirementForVaccineRequest)(nil),     // 2: moego.service.offering.v1.UpdateServiceRequirementForVaccineRequest
	(*UpdateServiceRequirementForVaccineResponse)(nil),    // 3: moego.service.offering.v1.UpdateServiceRequirementForVaccineResponse
	(*ListServiceVaccineRequirementsRequest)(nil),         // 4: moego.service.offering.v1.ListServiceVaccineRequirementsRequest
	(*ListServiceVaccineRequirementsResponse)(nil),        // 5: moego.service.offering.v1.ListServiceVaccineRequirementsResponse
	(*ListServiceVaccineRequirementsRequest_Filters)(nil), // 6: moego.service.offering.v1.ListServiceVaccineRequirementsRequest.Filters
	(*v1.Tenant)(nil),                          // 7: moego.models.organization.v1.Tenant
	(v11.ServiceItemType)(0),                   // 8: moego.models.offering.v1.ServiceItemType
	(*v2.PaginationRequest)(nil),               // 9: moego.utils.v2.PaginationRequest
	(*v11.ServiceVaccineRequirementModel)(nil), // 10: moego.models.offering.v1.ServiceVaccineRequirementModel
	(*v2.PaginationResponse)(nil),              // 11: moego.utils.v2.PaginationResponse
}
var file_moego_service_offering_v1_service_vaccine_requirement_service_proto_depIdxs = []int32{
	7,  // 0: moego.service.offering.v1.UpdateVaccineRequirementForServiceRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	8,  // 1: moego.service.offering.v1.UpdateVaccineRequirementForServiceRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	7,  // 2: moego.service.offering.v1.UpdateServiceRequirementForVaccineRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	8,  // 3: moego.service.offering.v1.UpdateServiceRequirementForVaccineRequest.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	7,  // 4: moego.service.offering.v1.ListServiceVaccineRequirementsRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	9,  // 5: moego.service.offering.v1.ListServiceVaccineRequirementsRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	6,  // 6: moego.service.offering.v1.ListServiceVaccineRequirementsRequest.filter:type_name -> moego.service.offering.v1.ListServiceVaccineRequirementsRequest.Filters
	10, // 7: moego.service.offering.v1.ListServiceVaccineRequirementsResponse.service_vaccine_requirements:type_name -> moego.models.offering.v1.ServiceVaccineRequirementModel
	11, // 8: moego.service.offering.v1.ListServiceVaccineRequirementsResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	8,  // 9: moego.service.offering.v1.ListServiceVaccineRequirementsRequest.Filters.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	0,  // 10: moego.service.offering.v1.ServiceVaccineRequirementService.UpdateVaccineRequirementForService:input_type -> moego.service.offering.v1.UpdateVaccineRequirementForServiceRequest
	2,  // 11: moego.service.offering.v1.ServiceVaccineRequirementService.UpdateServiceRequirementForVaccine:input_type -> moego.service.offering.v1.UpdateServiceRequirementForVaccineRequest
	4,  // 12: moego.service.offering.v1.ServiceVaccineRequirementService.ListServiceVaccineRequirements:input_type -> moego.service.offering.v1.ListServiceVaccineRequirementsRequest
	1,  // 13: moego.service.offering.v1.ServiceVaccineRequirementService.UpdateVaccineRequirementForService:output_type -> moego.service.offering.v1.UpdateVaccineRequirementForServiceResponse
	3,  // 14: moego.service.offering.v1.ServiceVaccineRequirementService.UpdateServiceRequirementForVaccine:output_type -> moego.service.offering.v1.UpdateServiceRequirementForVaccineResponse
	5,  // 15: moego.service.offering.v1.ServiceVaccineRequirementService.ListServiceVaccineRequirements:output_type -> moego.service.offering.v1.ListServiceVaccineRequirementsResponse
	13, // [13:16] is the sub-list for method output_type
	10, // [10:13] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_moego_service_offering_v1_service_vaccine_requirement_service_proto_init() }
func file_moego_service_offering_v1_service_vaccine_requirement_service_proto_init() {
	if File_moego_service_offering_v1_service_vaccine_requirement_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateVaccineRequirementForServiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateVaccineRequirementForServiceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceRequirementForVaccineRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceRequirementForVaccineResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceVaccineRequirementsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceVaccineRequirementsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceVaccineRequirementsRequest_Filters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes[4].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_offering_v1_service_vaccine_requirement_service_proto_goTypes,
		DependencyIndexes: file_moego_service_offering_v1_service_vaccine_requirement_service_proto_depIdxs,
		MessageInfos:      file_moego_service_offering_v1_service_vaccine_requirement_service_proto_msgTypes,
	}.Build()
	File_moego_service_offering_v1_service_vaccine_requirement_service_proto = out.File
	file_moego_service_offering_v1_service_vaccine_requirement_service_proto_rawDesc = nil
	file_moego_service_offering_v1_service_vaccine_requirement_service_proto_goTypes = nil
	file_moego_service_offering_v1_service_vaccine_requirement_service_proto_depIdxs = nil
}
