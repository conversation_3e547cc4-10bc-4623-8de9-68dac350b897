// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/agreement/v1/agreement_record_service.proto

package agreementsvcpb

import (
	context "context"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AgreementRecordServiceClient is the client API for AgreementRecordService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AgreementRecordServiceClient interface {
	// check agreement record
	CheckRecord(ctx context.Context, in *CheckRecordRequest, opts ...grpc.CallOption) (*CheckRecordResponse, error)
	// add an agreement record
	AddRecord(ctx context.Context, in *AddRecordRequest, opts ...grpc.CallOption) (*v1.AgreementRecordModel, error)
	// upload signed agreement files
	UploadSignedFile(ctx context.Context, in *UploadSignedFileRequest, opts ...grpc.CallOption) (*v1.AgreementRecordModel, error)
	// get agreement with recent signed record list
	GetRecentSignedAgreementList(ctx context.Context, in *GetRecentSignedAgreementListRequest, opts ...grpc.CallOption) (*GetRecentSignedAgreementListResponse, error)
	// batch get agreement with recent signed record list
	BatchGetRecentSignedAgreementList(ctx context.Context, in *BatchGetRecentSignedAgreementListRequest, opts ...grpc.CallOption) (*BatchGetRecentSignedAgreementListResponse, error)
	// query agreement record list
	GetRecordList(ctx context.Context, in *GetRecordListRequest, opts ...grpc.CallOption) (*GetRecordListResponse, error)
	// get an agreement record detail by id
	GetRecord(ctx context.Context, in *GetRecordRequest, opts ...grpc.CallOption) (*v1.AgreementRecordModel, error)
	// get an agreement record by id
	GetRecordSimpleView(ctx context.Context, in *GetRecordRequest, opts ...grpc.CallOption) (*v1.AgreementRecordSimpleView, error)
	// direct sign a record from an agreement
	SignAgreement(ctx context.Context, in *SignAgreementRequest, opts ...grpc.CallOption) (*v1.AgreementRecordSimpleView, error)
	// sign a created agreement record
	SignRecord(ctx context.Context, in *SignRecordRequest, opts ...grpc.CallOption) (*v1.AgreementRecordSimpleView, error)
	// send an already created agreement record to sign
	SendSignRequest(ctx context.Context, in *SendSignRequestRequest, opts ...grpc.CallOption) (*SendSignRequestResponse, error)
	// delete an agreement record
	DeleteRecord(ctx context.Context, in *DeleteRecordRequest, opts ...grpc.CallOption) (*DeleteRecordResponse, error)
	// get agreement with recent signed record list by company
	GetRecentSignedAgreementListByCompany(ctx context.Context, in *GetRecentSignedAgreementListByCompanyRequest, opts ...grpc.CallOption) (*GetRecentSignedAgreementListByCompanyResponse, error)
	// get agreement record list by company
	GetRecordListByCompany(ctx context.Context, in *GetRecordListByCompanyRequest, opts ...grpc.CallOption) (*GetRecordListByCompanyResponse, error)
}

type agreementRecordServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAgreementRecordServiceClient(cc grpc.ClientConnInterface) AgreementRecordServiceClient {
	return &agreementRecordServiceClient{cc}
}

func (c *agreementRecordServiceClient) CheckRecord(ctx context.Context, in *CheckRecordRequest, opts ...grpc.CallOption) (*CheckRecordResponse, error) {
	out := new(CheckRecordResponse)
	err := c.cc.Invoke(ctx, "/moego.service.agreement.v1.AgreementRecordService/CheckRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) AddRecord(ctx context.Context, in *AddRecordRequest, opts ...grpc.CallOption) (*v1.AgreementRecordModel, error) {
	out := new(v1.AgreementRecordModel)
	err := c.cc.Invoke(ctx, "/moego.service.agreement.v1.AgreementRecordService/AddRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) UploadSignedFile(ctx context.Context, in *UploadSignedFileRequest, opts ...grpc.CallOption) (*v1.AgreementRecordModel, error) {
	out := new(v1.AgreementRecordModel)
	err := c.cc.Invoke(ctx, "/moego.service.agreement.v1.AgreementRecordService/UploadSignedFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) GetRecentSignedAgreementList(ctx context.Context, in *GetRecentSignedAgreementListRequest, opts ...grpc.CallOption) (*GetRecentSignedAgreementListResponse, error) {
	out := new(GetRecentSignedAgreementListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.agreement.v1.AgreementRecordService/GetRecentSignedAgreementList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) BatchGetRecentSignedAgreementList(ctx context.Context, in *BatchGetRecentSignedAgreementListRequest, opts ...grpc.CallOption) (*BatchGetRecentSignedAgreementListResponse, error) {
	out := new(BatchGetRecentSignedAgreementListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.agreement.v1.AgreementRecordService/BatchGetRecentSignedAgreementList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) GetRecordList(ctx context.Context, in *GetRecordListRequest, opts ...grpc.CallOption) (*GetRecordListResponse, error) {
	out := new(GetRecordListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.agreement.v1.AgreementRecordService/GetRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) GetRecord(ctx context.Context, in *GetRecordRequest, opts ...grpc.CallOption) (*v1.AgreementRecordModel, error) {
	out := new(v1.AgreementRecordModel)
	err := c.cc.Invoke(ctx, "/moego.service.agreement.v1.AgreementRecordService/GetRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) GetRecordSimpleView(ctx context.Context, in *GetRecordRequest, opts ...grpc.CallOption) (*v1.AgreementRecordSimpleView, error) {
	out := new(v1.AgreementRecordSimpleView)
	err := c.cc.Invoke(ctx, "/moego.service.agreement.v1.AgreementRecordService/GetRecordSimpleView", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) SignAgreement(ctx context.Context, in *SignAgreementRequest, opts ...grpc.CallOption) (*v1.AgreementRecordSimpleView, error) {
	out := new(v1.AgreementRecordSimpleView)
	err := c.cc.Invoke(ctx, "/moego.service.agreement.v1.AgreementRecordService/SignAgreement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) SignRecord(ctx context.Context, in *SignRecordRequest, opts ...grpc.CallOption) (*v1.AgreementRecordSimpleView, error) {
	out := new(v1.AgreementRecordSimpleView)
	err := c.cc.Invoke(ctx, "/moego.service.agreement.v1.AgreementRecordService/SignRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) SendSignRequest(ctx context.Context, in *SendSignRequestRequest, opts ...grpc.CallOption) (*SendSignRequestResponse, error) {
	out := new(SendSignRequestResponse)
	err := c.cc.Invoke(ctx, "/moego.service.agreement.v1.AgreementRecordService/SendSignRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) DeleteRecord(ctx context.Context, in *DeleteRecordRequest, opts ...grpc.CallOption) (*DeleteRecordResponse, error) {
	out := new(DeleteRecordResponse)
	err := c.cc.Invoke(ctx, "/moego.service.agreement.v1.AgreementRecordService/DeleteRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) GetRecentSignedAgreementListByCompany(ctx context.Context, in *GetRecentSignedAgreementListByCompanyRequest, opts ...grpc.CallOption) (*GetRecentSignedAgreementListByCompanyResponse, error) {
	out := new(GetRecentSignedAgreementListByCompanyResponse)
	err := c.cc.Invoke(ctx, "/moego.service.agreement.v1.AgreementRecordService/GetRecentSignedAgreementListByCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) GetRecordListByCompany(ctx context.Context, in *GetRecordListByCompanyRequest, opts ...grpc.CallOption) (*GetRecordListByCompanyResponse, error) {
	out := new(GetRecordListByCompanyResponse)
	err := c.cc.Invoke(ctx, "/moego.service.agreement.v1.AgreementRecordService/GetRecordListByCompany", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AgreementRecordServiceServer is the server API for AgreementRecordService service.
// All implementations must embed UnimplementedAgreementRecordServiceServer
// for forward compatibility
type AgreementRecordServiceServer interface {
	// check agreement record
	CheckRecord(context.Context, *CheckRecordRequest) (*CheckRecordResponse, error)
	// add an agreement record
	AddRecord(context.Context, *AddRecordRequest) (*v1.AgreementRecordModel, error)
	// upload signed agreement files
	UploadSignedFile(context.Context, *UploadSignedFileRequest) (*v1.AgreementRecordModel, error)
	// get agreement with recent signed record list
	GetRecentSignedAgreementList(context.Context, *GetRecentSignedAgreementListRequest) (*GetRecentSignedAgreementListResponse, error)
	// batch get agreement with recent signed record list
	BatchGetRecentSignedAgreementList(context.Context, *BatchGetRecentSignedAgreementListRequest) (*BatchGetRecentSignedAgreementListResponse, error)
	// query agreement record list
	GetRecordList(context.Context, *GetRecordListRequest) (*GetRecordListResponse, error)
	// get an agreement record detail by id
	GetRecord(context.Context, *GetRecordRequest) (*v1.AgreementRecordModel, error)
	// get an agreement record by id
	GetRecordSimpleView(context.Context, *GetRecordRequest) (*v1.AgreementRecordSimpleView, error)
	// direct sign a record from an agreement
	SignAgreement(context.Context, *SignAgreementRequest) (*v1.AgreementRecordSimpleView, error)
	// sign a created agreement record
	SignRecord(context.Context, *SignRecordRequest) (*v1.AgreementRecordSimpleView, error)
	// send an already created agreement record to sign
	SendSignRequest(context.Context, *SendSignRequestRequest) (*SendSignRequestResponse, error)
	// delete an agreement record
	DeleteRecord(context.Context, *DeleteRecordRequest) (*DeleteRecordResponse, error)
	// get agreement with recent signed record list by company
	GetRecentSignedAgreementListByCompany(context.Context, *GetRecentSignedAgreementListByCompanyRequest) (*GetRecentSignedAgreementListByCompanyResponse, error)
	// get agreement record list by company
	GetRecordListByCompany(context.Context, *GetRecordListByCompanyRequest) (*GetRecordListByCompanyResponse, error)
	mustEmbedUnimplementedAgreementRecordServiceServer()
}

// UnimplementedAgreementRecordServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAgreementRecordServiceServer struct {
}

func (UnimplementedAgreementRecordServiceServer) CheckRecord(context.Context, *CheckRecordRequest) (*CheckRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckRecord not implemented")
}
func (UnimplementedAgreementRecordServiceServer) AddRecord(context.Context, *AddRecordRequest) (*v1.AgreementRecordModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddRecord not implemented")
}
func (UnimplementedAgreementRecordServiceServer) UploadSignedFile(context.Context, *UploadSignedFileRequest) (*v1.AgreementRecordModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadSignedFile not implemented")
}
func (UnimplementedAgreementRecordServiceServer) GetRecentSignedAgreementList(context.Context, *GetRecentSignedAgreementListRequest) (*GetRecentSignedAgreementListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecentSignedAgreementList not implemented")
}
func (UnimplementedAgreementRecordServiceServer) BatchGetRecentSignedAgreementList(context.Context, *BatchGetRecentSignedAgreementListRequest) (*BatchGetRecentSignedAgreementListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetRecentSignedAgreementList not implemented")
}
func (UnimplementedAgreementRecordServiceServer) GetRecordList(context.Context, *GetRecordListRequest) (*GetRecordListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecordList not implemented")
}
func (UnimplementedAgreementRecordServiceServer) GetRecord(context.Context, *GetRecordRequest) (*v1.AgreementRecordModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecord not implemented")
}
func (UnimplementedAgreementRecordServiceServer) GetRecordSimpleView(context.Context, *GetRecordRequest) (*v1.AgreementRecordSimpleView, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecordSimpleView not implemented")
}
func (UnimplementedAgreementRecordServiceServer) SignAgreement(context.Context, *SignAgreementRequest) (*v1.AgreementRecordSimpleView, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignAgreement not implemented")
}
func (UnimplementedAgreementRecordServiceServer) SignRecord(context.Context, *SignRecordRequest) (*v1.AgreementRecordSimpleView, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignRecord not implemented")
}
func (UnimplementedAgreementRecordServiceServer) SendSignRequest(context.Context, *SendSignRequestRequest) (*SendSignRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSignRequest not implemented")
}
func (UnimplementedAgreementRecordServiceServer) DeleteRecord(context.Context, *DeleteRecordRequest) (*DeleteRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRecord not implemented")
}
func (UnimplementedAgreementRecordServiceServer) GetRecentSignedAgreementListByCompany(context.Context, *GetRecentSignedAgreementListByCompanyRequest) (*GetRecentSignedAgreementListByCompanyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecentSignedAgreementListByCompany not implemented")
}
func (UnimplementedAgreementRecordServiceServer) GetRecordListByCompany(context.Context, *GetRecordListByCompanyRequest) (*GetRecordListByCompanyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecordListByCompany not implemented")
}
func (UnimplementedAgreementRecordServiceServer) mustEmbedUnimplementedAgreementRecordServiceServer() {
}

// UnsafeAgreementRecordServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AgreementRecordServiceServer will
// result in compilation errors.
type UnsafeAgreementRecordServiceServer interface {
	mustEmbedUnimplementedAgreementRecordServiceServer()
}

func RegisterAgreementRecordServiceServer(s grpc.ServiceRegistrar, srv AgreementRecordServiceServer) {
	s.RegisterService(&AgreementRecordService_ServiceDesc, srv)
}

func _AgreementRecordService_CheckRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).CheckRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.agreement.v1.AgreementRecordService/CheckRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).CheckRecord(ctx, req.(*CheckRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_AddRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).AddRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.agreement.v1.AgreementRecordService/AddRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).AddRecord(ctx, req.(*AddRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_UploadSignedFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadSignedFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).UploadSignedFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.agreement.v1.AgreementRecordService/UploadSignedFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).UploadSignedFile(ctx, req.(*UploadSignedFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_GetRecentSignedAgreementList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecentSignedAgreementListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecentSignedAgreementList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.agreement.v1.AgreementRecordService/GetRecentSignedAgreementList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecentSignedAgreementList(ctx, req.(*GetRecentSignedAgreementListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_BatchGetRecentSignedAgreementList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetRecentSignedAgreementListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).BatchGetRecentSignedAgreementList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.agreement.v1.AgreementRecordService/BatchGetRecentSignedAgreementList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).BatchGetRecentSignedAgreementList(ctx, req.(*BatchGetRecentSignedAgreementListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_GetRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecordListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.agreement.v1.AgreementRecordService/GetRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecordList(ctx, req.(*GetRecordListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_GetRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.agreement.v1.AgreementRecordService/GetRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecord(ctx, req.(*GetRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_GetRecordSimpleView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecordSimpleView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.agreement.v1.AgreementRecordService/GetRecordSimpleView",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecordSimpleView(ctx, req.(*GetRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_SignAgreement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignAgreementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).SignAgreement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.agreement.v1.AgreementRecordService/SignAgreement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).SignAgreement(ctx, req.(*SignAgreementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_SignRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).SignRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.agreement.v1.AgreementRecordService/SignRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).SignRecord(ctx, req.(*SignRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_SendSignRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSignRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).SendSignRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.agreement.v1.AgreementRecordService/SendSignRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).SendSignRequest(ctx, req.(*SendSignRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_DeleteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).DeleteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.agreement.v1.AgreementRecordService/DeleteRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).DeleteRecord(ctx, req.(*DeleteRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_GetRecentSignedAgreementListByCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecentSignedAgreementListByCompanyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecentSignedAgreementListByCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.agreement.v1.AgreementRecordService/GetRecentSignedAgreementListByCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecentSignedAgreementListByCompany(ctx, req.(*GetRecentSignedAgreementListByCompanyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_GetRecordListByCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecordListByCompanyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecordListByCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.agreement.v1.AgreementRecordService/GetRecordListByCompany",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecordListByCompany(ctx, req.(*GetRecordListByCompanyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AgreementRecordService_ServiceDesc is the grpc.ServiceDesc for AgreementRecordService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AgreementRecordService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.agreement.v1.AgreementRecordService",
	HandlerType: (*AgreementRecordServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckRecord",
			Handler:    _AgreementRecordService_CheckRecord_Handler,
		},
		{
			MethodName: "AddRecord",
			Handler:    _AgreementRecordService_AddRecord_Handler,
		},
		{
			MethodName: "UploadSignedFile",
			Handler:    _AgreementRecordService_UploadSignedFile_Handler,
		},
		{
			MethodName: "GetRecentSignedAgreementList",
			Handler:    _AgreementRecordService_GetRecentSignedAgreementList_Handler,
		},
		{
			MethodName: "BatchGetRecentSignedAgreementList",
			Handler:    _AgreementRecordService_BatchGetRecentSignedAgreementList_Handler,
		},
		{
			MethodName: "GetRecordList",
			Handler:    _AgreementRecordService_GetRecordList_Handler,
		},
		{
			MethodName: "GetRecord",
			Handler:    _AgreementRecordService_GetRecord_Handler,
		},
		{
			MethodName: "GetRecordSimpleView",
			Handler:    _AgreementRecordService_GetRecordSimpleView_Handler,
		},
		{
			MethodName: "SignAgreement",
			Handler:    _AgreementRecordService_SignAgreement_Handler,
		},
		{
			MethodName: "SignRecord",
			Handler:    _AgreementRecordService_SignRecord_Handler,
		},
		{
			MethodName: "SendSignRequest",
			Handler:    _AgreementRecordService_SendSignRequest_Handler,
		},
		{
			MethodName: "DeleteRecord",
			Handler:    _AgreementRecordService_DeleteRecord_Handler,
		},
		{
			MethodName: "GetRecentSignedAgreementListByCompany",
			Handler:    _AgreementRecordService_GetRecentSignedAgreementListByCompany_Handler,
		},
		{
			MethodName: "GetRecordListByCompany",
			Handler:    _AgreementRecordService_GetRecordListByCompany_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/agreement/v1/agreement_record_service.proto",
}
