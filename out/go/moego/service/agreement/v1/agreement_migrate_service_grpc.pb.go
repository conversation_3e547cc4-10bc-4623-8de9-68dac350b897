// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/agreement/v1/agreement_migrate_service.proto

package agreementsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AgreementMigrateServiceClient is the client API for AgreementMigrateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AgreementMigrateServiceClient interface {
	// upsert agreement
	UpsertAgreement(ctx context.Context, in *UpsertAgreementRequest, opts ...grpc.CallOption) (*UpsertAgreementResponse, error)
}

type agreementMigrateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAgreementMigrateServiceClient(cc grpc.ClientConnInterface) AgreementMigrateServiceClient {
	return &agreementMigrateServiceClient{cc}
}

func (c *agreementMigrateServiceClient) UpsertAgreement(ctx context.Context, in *UpsertAgreementRequest, opts ...grpc.CallOption) (*UpsertAgreementResponse, error) {
	out := new(UpsertAgreementResponse)
	err := c.cc.Invoke(ctx, "/moego.service.agreement.v1.AgreementMigrateService/UpsertAgreement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AgreementMigrateServiceServer is the server API for AgreementMigrateService service.
// All implementations must embed UnimplementedAgreementMigrateServiceServer
// for forward compatibility
type AgreementMigrateServiceServer interface {
	// upsert agreement
	UpsertAgreement(context.Context, *UpsertAgreementRequest) (*UpsertAgreementResponse, error)
	mustEmbedUnimplementedAgreementMigrateServiceServer()
}

// UnimplementedAgreementMigrateServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAgreementMigrateServiceServer struct {
}

func (UnimplementedAgreementMigrateServiceServer) UpsertAgreement(context.Context, *UpsertAgreementRequest) (*UpsertAgreementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertAgreement not implemented")
}
func (UnimplementedAgreementMigrateServiceServer) mustEmbedUnimplementedAgreementMigrateServiceServer() {
}

// UnsafeAgreementMigrateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AgreementMigrateServiceServer will
// result in compilation errors.
type UnsafeAgreementMigrateServiceServer interface {
	mustEmbedUnimplementedAgreementMigrateServiceServer()
}

func RegisterAgreementMigrateServiceServer(s grpc.ServiceRegistrar, srv AgreementMigrateServiceServer) {
	s.RegisterService(&AgreementMigrateService_ServiceDesc, srv)
}

func _AgreementMigrateService_UpsertAgreement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertAgreementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementMigrateServiceServer).UpsertAgreement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.agreement.v1.AgreementMigrateService/UpsertAgreement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementMigrateServiceServer).UpsertAgreement(ctx, req.(*UpsertAgreementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AgreementMigrateService_ServiceDesc is the grpc.ServiceDesc for AgreementMigrateService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AgreementMigrateService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.agreement.v1.AgreementMigrateService",
	HandlerType: (*AgreementMigrateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpsertAgreement",
			Handler:    _AgreementMigrateService_UpsertAgreement_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/agreement/v1/agreement_migrate_service.proto",
}
