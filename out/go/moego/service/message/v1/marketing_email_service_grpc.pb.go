// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/message/v1/marketing_email_service.proto

package messagesvcpb

import (
	context "context"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// MarketingEmailServiceClient is the client API for MarketingEmailService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MarketingEmailServiceClient interface {
	// mass email send
	MassEmailSend(ctx context.Context, in *MassEmailSendRequest, opts ...grpc.CallOption) (*MassEmailSendResponse, error)
	// get email list
	GetEmailList(ctx context.Context, in *GetEmailListRequest, opts ...grpc.CallOption) (*GetEmailListResponse, error)
	// get email detail
	GetEmailDetail(ctx context.Context, in *GetEmailDetailRequest, opts ...grpc.CallOption) (*v1.MarketingEmailModel, error)
	// send now for schedule email
	SendNow(ctx context.Context, in *SendNowRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// send test email for preview
	SendTestEmail(ctx context.Context, in *SendTestEmailRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// get available emails count
	GetAvailableEmailsCount(ctx context.Context, in *GetAvailableEmailsCountRequest, opts ...grpc.CallOption) (*GetAvailableEmailsCountResponse, error)
	// cancel schedule email
	CancelScheduleEmail(ctx context.Context, in *CancelScheduleEmailRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// save email draft
	SaveEmailDraft(ctx context.Context, in *SaveEmailDraftRequest, opts ...grpc.CallOption) (*SaveEmailDraftResponse, error)
	// reschedule email
	RescheduleEmail(ctx context.Context, in *RescheduleEmailRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// get recipient list
	GetRecipientList(ctx context.Context, in *GetRecipientListRequest, opts ...grpc.CallOption) (*GetRecipientListResponse, error)
	// create marketing email template
	CreateMarketingEmailTemplate(ctx context.Context, in *CreateMarketingEmailTemplateRequest, opts ...grpc.CallOption) (*CreateMarketingEmailTemplateResponse, error)
	// update marketing email template
	UpdateMarketingEmailTemplate(ctx context.Context, in *UpdateMarketingEmailTemplateRequest, opts ...grpc.CallOption) (*UpdateMarketingEmailTemplateResponse, error)
	// get marketing email template list
	GetMarketingEmailTemplateList(ctx context.Context, in *GetMarketingEmailTemplateListRequest, opts ...grpc.CallOption) (*GetMarketingEmailTemplateListResponse, error)
	// get marketing email template detail
	GetMarketingEmailTemplateDetail(ctx context.Context, in *GetMarketingEmailTemplateDetailRequest, opts ...grpc.CallOption) (*v1.MarketingEmailTemplateModel, error)
	// view email reply
	ViewEmailReply(ctx context.Context, in *ViewEmailReplyRequest, opts ...grpc.CallOption) (*ViewEmailReplyResponse, error)
	// delete email
	DeleteEmail(ctx context.Context, in *DeleteEmailRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// trigger schedule email
	TriggerScheduleEmail(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// handle email event
	HandleEmailEvent(ctx context.Context, in *HandleEmailEventRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// calculate credit cost
	CalculateCreditCost(ctx context.Context, in *CalculateCreditCostRequest, opts ...grpc.CallOption) (*CalculateCreditCostResponse, error)
	// get appointments after an email
	GetAppointmentsAfterEmail(ctx context.Context, in *GetAppointmentsAfterEmailRequest, opts ...grpc.CallOption) (*GetAppointmentsAfterEmailResponse, error)
	// trigger email resend for pending emails
	TriggerEmailResend(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// remove email from rejected list
	RemoveEmailFromRejectedList(ctx context.Context, in *RemoveEmailFromRejectedListRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// get marketing campaigns email annual summary
	MarketingCampaignsSummary(ctx context.Context, in *MarketingCampaignsSummaryRequest, opts ...grpc.CallOption) (*MarketingCampaignsSummaryResponse, error)
}

type marketingEmailServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMarketingEmailServiceClient(cc grpc.ClientConnInterface) MarketingEmailServiceClient {
	return &marketingEmailServiceClient{cc}
}

func (c *marketingEmailServiceClient) MassEmailSend(ctx context.Context, in *MassEmailSendRequest, opts ...grpc.CallOption) (*MassEmailSendResponse, error) {
	out := new(MassEmailSendResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/MassEmailSend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) GetEmailList(ctx context.Context, in *GetEmailListRequest, opts ...grpc.CallOption) (*GetEmailListResponse, error) {
	out := new(GetEmailListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/GetEmailList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) GetEmailDetail(ctx context.Context, in *GetEmailDetailRequest, opts ...grpc.CallOption) (*v1.MarketingEmailModel, error) {
	out := new(v1.MarketingEmailModel)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/GetEmailDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) SendNow(ctx context.Context, in *SendNowRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/SendNow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) SendTestEmail(ctx context.Context, in *SendTestEmailRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/SendTestEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) GetAvailableEmailsCount(ctx context.Context, in *GetAvailableEmailsCountRequest, opts ...grpc.CallOption) (*GetAvailableEmailsCountResponse, error) {
	out := new(GetAvailableEmailsCountResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/GetAvailableEmailsCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) CancelScheduleEmail(ctx context.Context, in *CancelScheduleEmailRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/CancelScheduleEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) SaveEmailDraft(ctx context.Context, in *SaveEmailDraftRequest, opts ...grpc.CallOption) (*SaveEmailDraftResponse, error) {
	out := new(SaveEmailDraftResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/SaveEmailDraft", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) RescheduleEmail(ctx context.Context, in *RescheduleEmailRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/RescheduleEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) GetRecipientList(ctx context.Context, in *GetRecipientListRequest, opts ...grpc.CallOption) (*GetRecipientListResponse, error) {
	out := new(GetRecipientListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/GetRecipientList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) CreateMarketingEmailTemplate(ctx context.Context, in *CreateMarketingEmailTemplateRequest, opts ...grpc.CallOption) (*CreateMarketingEmailTemplateResponse, error) {
	out := new(CreateMarketingEmailTemplateResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/CreateMarketingEmailTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) UpdateMarketingEmailTemplate(ctx context.Context, in *UpdateMarketingEmailTemplateRequest, opts ...grpc.CallOption) (*UpdateMarketingEmailTemplateResponse, error) {
	out := new(UpdateMarketingEmailTemplateResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/UpdateMarketingEmailTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) GetMarketingEmailTemplateList(ctx context.Context, in *GetMarketingEmailTemplateListRequest, opts ...grpc.CallOption) (*GetMarketingEmailTemplateListResponse, error) {
	out := new(GetMarketingEmailTemplateListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/GetMarketingEmailTemplateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) GetMarketingEmailTemplateDetail(ctx context.Context, in *GetMarketingEmailTemplateDetailRequest, opts ...grpc.CallOption) (*v1.MarketingEmailTemplateModel, error) {
	out := new(v1.MarketingEmailTemplateModel)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/GetMarketingEmailTemplateDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) ViewEmailReply(ctx context.Context, in *ViewEmailReplyRequest, opts ...grpc.CallOption) (*ViewEmailReplyResponse, error) {
	out := new(ViewEmailReplyResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/ViewEmailReply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) DeleteEmail(ctx context.Context, in *DeleteEmailRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/DeleteEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) TriggerScheduleEmail(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/TriggerScheduleEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) HandleEmailEvent(ctx context.Context, in *HandleEmailEventRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/HandleEmailEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) CalculateCreditCost(ctx context.Context, in *CalculateCreditCostRequest, opts ...grpc.CallOption) (*CalculateCreditCostResponse, error) {
	out := new(CalculateCreditCostResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/CalculateCreditCost", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) GetAppointmentsAfterEmail(ctx context.Context, in *GetAppointmentsAfterEmailRequest, opts ...grpc.CallOption) (*GetAppointmentsAfterEmailResponse, error) {
	out := new(GetAppointmentsAfterEmailResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/GetAppointmentsAfterEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) TriggerEmailResend(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/TriggerEmailResend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) RemoveEmailFromRejectedList(ctx context.Context, in *RemoveEmailFromRejectedListRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/RemoveEmailFromRejectedList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketingEmailServiceClient) MarketingCampaignsSummary(ctx context.Context, in *MarketingCampaignsSummaryRequest, opts ...grpc.CallOption) (*MarketingCampaignsSummaryResponse, error) {
	out := new(MarketingCampaignsSummaryResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.MarketingEmailService/MarketingCampaignsSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MarketingEmailServiceServer is the server API for MarketingEmailService service.
// All implementations must embed UnimplementedMarketingEmailServiceServer
// for forward compatibility
type MarketingEmailServiceServer interface {
	// mass email send
	MassEmailSend(context.Context, *MassEmailSendRequest) (*MassEmailSendResponse, error)
	// get email list
	GetEmailList(context.Context, *GetEmailListRequest) (*GetEmailListResponse, error)
	// get email detail
	GetEmailDetail(context.Context, *GetEmailDetailRequest) (*v1.MarketingEmailModel, error)
	// send now for schedule email
	SendNow(context.Context, *SendNowRequest) (*emptypb.Empty, error)
	// send test email for preview
	SendTestEmail(context.Context, *SendTestEmailRequest) (*emptypb.Empty, error)
	// get available emails count
	GetAvailableEmailsCount(context.Context, *GetAvailableEmailsCountRequest) (*GetAvailableEmailsCountResponse, error)
	// cancel schedule email
	CancelScheduleEmail(context.Context, *CancelScheduleEmailRequest) (*emptypb.Empty, error)
	// save email draft
	SaveEmailDraft(context.Context, *SaveEmailDraftRequest) (*SaveEmailDraftResponse, error)
	// reschedule email
	RescheduleEmail(context.Context, *RescheduleEmailRequest) (*emptypb.Empty, error)
	// get recipient list
	GetRecipientList(context.Context, *GetRecipientListRequest) (*GetRecipientListResponse, error)
	// create marketing email template
	CreateMarketingEmailTemplate(context.Context, *CreateMarketingEmailTemplateRequest) (*CreateMarketingEmailTemplateResponse, error)
	// update marketing email template
	UpdateMarketingEmailTemplate(context.Context, *UpdateMarketingEmailTemplateRequest) (*UpdateMarketingEmailTemplateResponse, error)
	// get marketing email template list
	GetMarketingEmailTemplateList(context.Context, *GetMarketingEmailTemplateListRequest) (*GetMarketingEmailTemplateListResponse, error)
	// get marketing email template detail
	GetMarketingEmailTemplateDetail(context.Context, *GetMarketingEmailTemplateDetailRequest) (*v1.MarketingEmailTemplateModel, error)
	// view email reply
	ViewEmailReply(context.Context, *ViewEmailReplyRequest) (*ViewEmailReplyResponse, error)
	// delete email
	DeleteEmail(context.Context, *DeleteEmailRequest) (*emptypb.Empty, error)
	// trigger schedule email
	TriggerScheduleEmail(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// handle email event
	HandleEmailEvent(context.Context, *HandleEmailEventRequest) (*emptypb.Empty, error)
	// calculate credit cost
	CalculateCreditCost(context.Context, *CalculateCreditCostRequest) (*CalculateCreditCostResponse, error)
	// get appointments after an email
	GetAppointmentsAfterEmail(context.Context, *GetAppointmentsAfterEmailRequest) (*GetAppointmentsAfterEmailResponse, error)
	// trigger email resend for pending emails
	TriggerEmailResend(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// remove email from rejected list
	RemoveEmailFromRejectedList(context.Context, *RemoveEmailFromRejectedListRequest) (*emptypb.Empty, error)
	// get marketing campaigns email annual summary
	MarketingCampaignsSummary(context.Context, *MarketingCampaignsSummaryRequest) (*MarketingCampaignsSummaryResponse, error)
	mustEmbedUnimplementedMarketingEmailServiceServer()
}

// UnimplementedMarketingEmailServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMarketingEmailServiceServer struct {
}

func (UnimplementedMarketingEmailServiceServer) MassEmailSend(context.Context, *MassEmailSendRequest) (*MassEmailSendResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MassEmailSend not implemented")
}
func (UnimplementedMarketingEmailServiceServer) GetEmailList(context.Context, *GetEmailListRequest) (*GetEmailListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEmailList not implemented")
}
func (UnimplementedMarketingEmailServiceServer) GetEmailDetail(context.Context, *GetEmailDetailRequest) (*v1.MarketingEmailModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEmailDetail not implemented")
}
func (UnimplementedMarketingEmailServiceServer) SendNow(context.Context, *SendNowRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendNow not implemented")
}
func (UnimplementedMarketingEmailServiceServer) SendTestEmail(context.Context, *SendTestEmailRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendTestEmail not implemented")
}
func (UnimplementedMarketingEmailServiceServer) GetAvailableEmailsCount(context.Context, *GetAvailableEmailsCountRequest) (*GetAvailableEmailsCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAvailableEmailsCount not implemented")
}
func (UnimplementedMarketingEmailServiceServer) CancelScheduleEmail(context.Context, *CancelScheduleEmailRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelScheduleEmail not implemented")
}
func (UnimplementedMarketingEmailServiceServer) SaveEmailDraft(context.Context, *SaveEmailDraftRequest) (*SaveEmailDraftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveEmailDraft not implemented")
}
func (UnimplementedMarketingEmailServiceServer) RescheduleEmail(context.Context, *RescheduleEmailRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RescheduleEmail not implemented")
}
func (UnimplementedMarketingEmailServiceServer) GetRecipientList(context.Context, *GetRecipientListRequest) (*GetRecipientListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecipientList not implemented")
}
func (UnimplementedMarketingEmailServiceServer) CreateMarketingEmailTemplate(context.Context, *CreateMarketingEmailTemplateRequest) (*CreateMarketingEmailTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMarketingEmailTemplate not implemented")
}
func (UnimplementedMarketingEmailServiceServer) UpdateMarketingEmailTemplate(context.Context, *UpdateMarketingEmailTemplateRequest) (*UpdateMarketingEmailTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMarketingEmailTemplate not implemented")
}
func (UnimplementedMarketingEmailServiceServer) GetMarketingEmailTemplateList(context.Context, *GetMarketingEmailTemplateListRequest) (*GetMarketingEmailTemplateListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarketingEmailTemplateList not implemented")
}
func (UnimplementedMarketingEmailServiceServer) GetMarketingEmailTemplateDetail(context.Context, *GetMarketingEmailTemplateDetailRequest) (*v1.MarketingEmailTemplateModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarketingEmailTemplateDetail not implemented")
}
func (UnimplementedMarketingEmailServiceServer) ViewEmailReply(context.Context, *ViewEmailReplyRequest) (*ViewEmailReplyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ViewEmailReply not implemented")
}
func (UnimplementedMarketingEmailServiceServer) DeleteEmail(context.Context, *DeleteEmailRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteEmail not implemented")
}
func (UnimplementedMarketingEmailServiceServer) TriggerScheduleEmail(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerScheduleEmail not implemented")
}
func (UnimplementedMarketingEmailServiceServer) HandleEmailEvent(context.Context, *HandleEmailEventRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleEmailEvent not implemented")
}
func (UnimplementedMarketingEmailServiceServer) CalculateCreditCost(context.Context, *CalculateCreditCostRequest) (*CalculateCreditCostResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculateCreditCost not implemented")
}
func (UnimplementedMarketingEmailServiceServer) GetAppointmentsAfterEmail(context.Context, *GetAppointmentsAfterEmailRequest) (*GetAppointmentsAfterEmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppointmentsAfterEmail not implemented")
}
func (UnimplementedMarketingEmailServiceServer) TriggerEmailResend(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerEmailResend not implemented")
}
func (UnimplementedMarketingEmailServiceServer) RemoveEmailFromRejectedList(context.Context, *RemoveEmailFromRejectedListRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveEmailFromRejectedList not implemented")
}
func (UnimplementedMarketingEmailServiceServer) MarketingCampaignsSummary(context.Context, *MarketingCampaignsSummaryRequest) (*MarketingCampaignsSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarketingCampaignsSummary not implemented")
}
func (UnimplementedMarketingEmailServiceServer) mustEmbedUnimplementedMarketingEmailServiceServer() {}

// UnsafeMarketingEmailServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MarketingEmailServiceServer will
// result in compilation errors.
type UnsafeMarketingEmailServiceServer interface {
	mustEmbedUnimplementedMarketingEmailServiceServer()
}

func RegisterMarketingEmailServiceServer(s grpc.ServiceRegistrar, srv MarketingEmailServiceServer) {
	s.RegisterService(&MarketingEmailService_ServiceDesc, srv)
}

func _MarketingEmailService_MassEmailSend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MassEmailSendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).MassEmailSend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/MassEmailSend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).MassEmailSend(ctx, req.(*MassEmailSendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_GetEmailList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEmailListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).GetEmailList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/GetEmailList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).GetEmailList(ctx, req.(*GetEmailListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_GetEmailDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEmailDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).GetEmailDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/GetEmailDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).GetEmailDetail(ctx, req.(*GetEmailDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_SendNow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendNowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).SendNow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/SendNow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).SendNow(ctx, req.(*SendNowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_SendTestEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendTestEmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).SendTestEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/SendTestEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).SendTestEmail(ctx, req.(*SendTestEmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_GetAvailableEmailsCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvailableEmailsCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).GetAvailableEmailsCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/GetAvailableEmailsCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).GetAvailableEmailsCount(ctx, req.(*GetAvailableEmailsCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_CancelScheduleEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelScheduleEmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).CancelScheduleEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/CancelScheduleEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).CancelScheduleEmail(ctx, req.(*CancelScheduleEmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_SaveEmailDraft_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveEmailDraftRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).SaveEmailDraft(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/SaveEmailDraft",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).SaveEmailDraft(ctx, req.(*SaveEmailDraftRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_RescheduleEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RescheduleEmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).RescheduleEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/RescheduleEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).RescheduleEmail(ctx, req.(*RescheduleEmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_GetRecipientList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecipientListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).GetRecipientList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/GetRecipientList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).GetRecipientList(ctx, req.(*GetRecipientListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_CreateMarketingEmailTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMarketingEmailTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).CreateMarketingEmailTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/CreateMarketingEmailTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).CreateMarketingEmailTemplate(ctx, req.(*CreateMarketingEmailTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_UpdateMarketingEmailTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMarketingEmailTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).UpdateMarketingEmailTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/UpdateMarketingEmailTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).UpdateMarketingEmailTemplate(ctx, req.(*UpdateMarketingEmailTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_GetMarketingEmailTemplateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMarketingEmailTemplateListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).GetMarketingEmailTemplateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/GetMarketingEmailTemplateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).GetMarketingEmailTemplateList(ctx, req.(*GetMarketingEmailTemplateListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_GetMarketingEmailTemplateDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMarketingEmailTemplateDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).GetMarketingEmailTemplateDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/GetMarketingEmailTemplateDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).GetMarketingEmailTemplateDetail(ctx, req.(*GetMarketingEmailTemplateDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_ViewEmailReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ViewEmailReplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).ViewEmailReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/ViewEmailReply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).ViewEmailReply(ctx, req.(*ViewEmailReplyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_DeleteEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).DeleteEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/DeleteEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).DeleteEmail(ctx, req.(*DeleteEmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_TriggerScheduleEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).TriggerScheduleEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/TriggerScheduleEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).TriggerScheduleEmail(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_HandleEmailEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleEmailEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).HandleEmailEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/HandleEmailEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).HandleEmailEvent(ctx, req.(*HandleEmailEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_CalculateCreditCost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculateCreditCostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).CalculateCreditCost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/CalculateCreditCost",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).CalculateCreditCost(ctx, req.(*CalculateCreditCostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_GetAppointmentsAfterEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointmentsAfterEmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).GetAppointmentsAfterEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/GetAppointmentsAfterEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).GetAppointmentsAfterEmail(ctx, req.(*GetAppointmentsAfterEmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_TriggerEmailResend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).TriggerEmailResend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/TriggerEmailResend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).TriggerEmailResend(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_RemoveEmailFromRejectedList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveEmailFromRejectedListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).RemoveEmailFromRejectedList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/RemoveEmailFromRejectedList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).RemoveEmailFromRejectedList(ctx, req.(*RemoveEmailFromRejectedListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketingEmailService_MarketingCampaignsSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarketingCampaignsSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketingEmailServiceServer).MarketingCampaignsSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.MarketingEmailService/MarketingCampaignsSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketingEmailServiceServer).MarketingCampaignsSummary(ctx, req.(*MarketingCampaignsSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MarketingEmailService_ServiceDesc is the grpc.ServiceDesc for MarketingEmailService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MarketingEmailService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.message.v1.MarketingEmailService",
	HandlerType: (*MarketingEmailServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MassEmailSend",
			Handler:    _MarketingEmailService_MassEmailSend_Handler,
		},
		{
			MethodName: "GetEmailList",
			Handler:    _MarketingEmailService_GetEmailList_Handler,
		},
		{
			MethodName: "GetEmailDetail",
			Handler:    _MarketingEmailService_GetEmailDetail_Handler,
		},
		{
			MethodName: "SendNow",
			Handler:    _MarketingEmailService_SendNow_Handler,
		},
		{
			MethodName: "SendTestEmail",
			Handler:    _MarketingEmailService_SendTestEmail_Handler,
		},
		{
			MethodName: "GetAvailableEmailsCount",
			Handler:    _MarketingEmailService_GetAvailableEmailsCount_Handler,
		},
		{
			MethodName: "CancelScheduleEmail",
			Handler:    _MarketingEmailService_CancelScheduleEmail_Handler,
		},
		{
			MethodName: "SaveEmailDraft",
			Handler:    _MarketingEmailService_SaveEmailDraft_Handler,
		},
		{
			MethodName: "RescheduleEmail",
			Handler:    _MarketingEmailService_RescheduleEmail_Handler,
		},
		{
			MethodName: "GetRecipientList",
			Handler:    _MarketingEmailService_GetRecipientList_Handler,
		},
		{
			MethodName: "CreateMarketingEmailTemplate",
			Handler:    _MarketingEmailService_CreateMarketingEmailTemplate_Handler,
		},
		{
			MethodName: "UpdateMarketingEmailTemplate",
			Handler:    _MarketingEmailService_UpdateMarketingEmailTemplate_Handler,
		},
		{
			MethodName: "GetMarketingEmailTemplateList",
			Handler:    _MarketingEmailService_GetMarketingEmailTemplateList_Handler,
		},
		{
			MethodName: "GetMarketingEmailTemplateDetail",
			Handler:    _MarketingEmailService_GetMarketingEmailTemplateDetail_Handler,
		},
		{
			MethodName: "ViewEmailReply",
			Handler:    _MarketingEmailService_ViewEmailReply_Handler,
		},
		{
			MethodName: "DeleteEmail",
			Handler:    _MarketingEmailService_DeleteEmail_Handler,
		},
		{
			MethodName: "TriggerScheduleEmail",
			Handler:    _MarketingEmailService_TriggerScheduleEmail_Handler,
		},
		{
			MethodName: "HandleEmailEvent",
			Handler:    _MarketingEmailService_HandleEmailEvent_Handler,
		},
		{
			MethodName: "CalculateCreditCost",
			Handler:    _MarketingEmailService_CalculateCreditCost_Handler,
		},
		{
			MethodName: "GetAppointmentsAfterEmail",
			Handler:    _MarketingEmailService_GetAppointmentsAfterEmail_Handler,
		},
		{
			MethodName: "TriggerEmailResend",
			Handler:    _MarketingEmailService_TriggerEmailResend_Handler,
		},
		{
			MethodName: "RemoveEmailFromRejectedList",
			Handler:    _MarketingEmailService_RemoveEmailFromRejectedList_Handler,
		},
		{
			MethodName: "MarketingCampaignsSummary",
			Handler:    _MarketingEmailService_MarketingCampaignsSummary_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/message/v1/marketing_email_service.proto",
}
