// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/message/v1/template_service.proto

package messagesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// TemplateServiceClient is the client API for TemplateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TemplateServiceClient interface {
	// batch create templates
	BatchCreateTemplates(ctx context.Context, in *BatchCreateTemplatesRequest, opts ...grpc.CallOption) (*BatchCreateTemplatesResponse, error)
	// batch get templates by id
	MGetTemplates(ctx context.Context, in *MGetTemplatesRequest, opts ...grpc.CallOption) (*MGetTemplatesResponse, error)
}

type templateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTemplateServiceClient(cc grpc.ClientConnInterface) TemplateServiceClient {
	return &templateServiceClient{cc}
}

func (c *templateServiceClient) BatchCreateTemplates(ctx context.Context, in *BatchCreateTemplatesRequest, opts ...grpc.CallOption) (*BatchCreateTemplatesResponse, error) {
	out := new(BatchCreateTemplatesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.TemplateService/BatchCreateTemplates", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templateServiceClient) MGetTemplates(ctx context.Context, in *MGetTemplatesRequest, opts ...grpc.CallOption) (*MGetTemplatesResponse, error) {
	out := new(MGetTemplatesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.TemplateService/MGetTemplates", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TemplateServiceServer is the server API for TemplateService service.
// All implementations must embed UnimplementedTemplateServiceServer
// for forward compatibility
type TemplateServiceServer interface {
	// batch create templates
	BatchCreateTemplates(context.Context, *BatchCreateTemplatesRequest) (*BatchCreateTemplatesResponse, error)
	// batch get templates by id
	MGetTemplates(context.Context, *MGetTemplatesRequest) (*MGetTemplatesResponse, error)
	mustEmbedUnimplementedTemplateServiceServer()
}

// UnimplementedTemplateServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTemplateServiceServer struct {
}

func (UnimplementedTemplateServiceServer) BatchCreateTemplates(context.Context, *BatchCreateTemplatesRequest) (*BatchCreateTemplatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCreateTemplates not implemented")
}
func (UnimplementedTemplateServiceServer) MGetTemplates(context.Context, *MGetTemplatesRequest) (*MGetTemplatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MGetTemplates not implemented")
}
func (UnimplementedTemplateServiceServer) mustEmbedUnimplementedTemplateServiceServer() {}

// UnsafeTemplateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TemplateServiceServer will
// result in compilation errors.
type UnsafeTemplateServiceServer interface {
	mustEmbedUnimplementedTemplateServiceServer()
}

func RegisterTemplateServiceServer(s grpc.ServiceRegistrar, srv TemplateServiceServer) {
	s.RegisterService(&TemplateService_ServiceDesc, srv)
}

func _TemplateService_BatchCreateTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCreateTemplatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplateServiceServer).BatchCreateTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.TemplateService/BatchCreateTemplates",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplateServiceServer).BatchCreateTemplates(ctx, req.(*BatchCreateTemplatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplateService_MGetTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MGetTemplatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplateServiceServer).MGetTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.TemplateService/MGetTemplates",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplateServiceServer).MGetTemplates(ctx, req.(*MGetTemplatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TemplateService_ServiceDesc is the grpc.ServiceDesc for TemplateService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TemplateService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.message.v1.TemplateService",
	HandlerType: (*TemplateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchCreateTemplates",
			Handler:    _TemplateService_BatchCreateTemplates_Handler,
		},
		{
			MethodName: "MGetTemplates",
			Handler:    _TemplateService_MGetTemplates_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/message/v1/template_service.proto",
}
