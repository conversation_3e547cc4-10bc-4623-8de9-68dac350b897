// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/message/v1/schedule_message_service.proto

package messagesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ScheduleMessageServiceClient is the client API for ScheduleMessageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ScheduleMessageServiceClient interface {
	// Create a schedule message
	CreateScheduleMessage(ctx context.Context, in *CreateScheduleMessageRequest, opts ...grpc.CallOption) (*CreateScheduleMessageResponse, error)
	// Incremental update a schedule message
	UpdateScheduledMessage(ctx context.Context, in *UpdateScheduleMessageRequest, opts ...grpc.CallOption) (*UpdateScheduleMessageResponse, error)
	// Delete a schedule message
	DeleteScheduleMessage(ctx context.Context, in *DeleteScheduleMessageRequest, opts ...grpc.CallOption) (*DeleteScheduleMessageResponse, error)
	// Delete all schedule messages associated with the appointment
	DeleteAppointmentScheduleMessages(ctx context.Context, in *DeleteAppointmentScheduleMessageRequest, opts ...grpc.CallOption) (*DeleteAppointmentScheduleMessageResponse, error)
	// Get a specific schedule message detail
	GetScheduleMessage(ctx context.Context, in *GetScheduleMessageRequest, opts ...grpc.CallOption) (*GetScheduleMessageResponse, error)
	// Get the schedule message list of business or specific customers
	GetScheduleMessages(ctx context.Context, in *GetScheduleMessagesRequest, opts ...grpc.CallOption) (*GetScheduleMessagesResponse, error)
	// Run the schedule message task
	RunScheduleMessageTask(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Send the schedule message now
	SendScheduleMessage(ctx context.Context, in *SendScheduleMessageRequest, opts ...grpc.CallOption) (*SendScheduleMessageResponse, error)
}

type scheduleMessageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewScheduleMessageServiceClient(cc grpc.ClientConnInterface) ScheduleMessageServiceClient {
	return &scheduleMessageServiceClient{cc}
}

func (c *scheduleMessageServiceClient) CreateScheduleMessage(ctx context.Context, in *CreateScheduleMessageRequest, opts ...grpc.CallOption) (*CreateScheduleMessageResponse, error) {
	out := new(CreateScheduleMessageResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.ScheduleMessageService/CreateScheduleMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleMessageServiceClient) UpdateScheduledMessage(ctx context.Context, in *UpdateScheduleMessageRequest, opts ...grpc.CallOption) (*UpdateScheduleMessageResponse, error) {
	out := new(UpdateScheduleMessageResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.ScheduleMessageService/UpdateScheduledMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleMessageServiceClient) DeleteScheduleMessage(ctx context.Context, in *DeleteScheduleMessageRequest, opts ...grpc.CallOption) (*DeleteScheduleMessageResponse, error) {
	out := new(DeleteScheduleMessageResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.ScheduleMessageService/DeleteScheduleMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleMessageServiceClient) DeleteAppointmentScheduleMessages(ctx context.Context, in *DeleteAppointmentScheduleMessageRequest, opts ...grpc.CallOption) (*DeleteAppointmentScheduleMessageResponse, error) {
	out := new(DeleteAppointmentScheduleMessageResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.ScheduleMessageService/DeleteAppointmentScheduleMessages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleMessageServiceClient) GetScheduleMessage(ctx context.Context, in *GetScheduleMessageRequest, opts ...grpc.CallOption) (*GetScheduleMessageResponse, error) {
	out := new(GetScheduleMessageResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.ScheduleMessageService/GetScheduleMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleMessageServiceClient) GetScheduleMessages(ctx context.Context, in *GetScheduleMessagesRequest, opts ...grpc.CallOption) (*GetScheduleMessagesResponse, error) {
	out := new(GetScheduleMessagesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.ScheduleMessageService/GetScheduleMessages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleMessageServiceClient) RunScheduleMessageTask(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.ScheduleMessageService/RunScheduleMessageTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleMessageServiceClient) SendScheduleMessage(ctx context.Context, in *SendScheduleMessageRequest, opts ...grpc.CallOption) (*SendScheduleMessageResponse, error) {
	out := new(SendScheduleMessageResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v1.ScheduleMessageService/SendScheduleMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ScheduleMessageServiceServer is the server API for ScheduleMessageService service.
// All implementations must embed UnimplementedScheduleMessageServiceServer
// for forward compatibility
type ScheduleMessageServiceServer interface {
	// Create a schedule message
	CreateScheduleMessage(context.Context, *CreateScheduleMessageRequest) (*CreateScheduleMessageResponse, error)
	// Incremental update a schedule message
	UpdateScheduledMessage(context.Context, *UpdateScheduleMessageRequest) (*UpdateScheduleMessageResponse, error)
	// Delete a schedule message
	DeleteScheduleMessage(context.Context, *DeleteScheduleMessageRequest) (*DeleteScheduleMessageResponse, error)
	// Delete all schedule messages associated with the appointment
	DeleteAppointmentScheduleMessages(context.Context, *DeleteAppointmentScheduleMessageRequest) (*DeleteAppointmentScheduleMessageResponse, error)
	// Get a specific schedule message detail
	GetScheduleMessage(context.Context, *GetScheduleMessageRequest) (*GetScheduleMessageResponse, error)
	// Get the schedule message list of business or specific customers
	GetScheduleMessages(context.Context, *GetScheduleMessagesRequest) (*GetScheduleMessagesResponse, error)
	// Run the schedule message task
	RunScheduleMessageTask(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// Send the schedule message now
	SendScheduleMessage(context.Context, *SendScheduleMessageRequest) (*SendScheduleMessageResponse, error)
	mustEmbedUnimplementedScheduleMessageServiceServer()
}

// UnimplementedScheduleMessageServiceServer must be embedded to have forward compatible implementations.
type UnimplementedScheduleMessageServiceServer struct {
}

func (UnimplementedScheduleMessageServiceServer) CreateScheduleMessage(context.Context, *CreateScheduleMessageRequest) (*CreateScheduleMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateScheduleMessage not implemented")
}
func (UnimplementedScheduleMessageServiceServer) UpdateScheduledMessage(context.Context, *UpdateScheduleMessageRequest) (*UpdateScheduleMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateScheduledMessage not implemented")
}
func (UnimplementedScheduleMessageServiceServer) DeleteScheduleMessage(context.Context, *DeleteScheduleMessageRequest) (*DeleteScheduleMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteScheduleMessage not implemented")
}
func (UnimplementedScheduleMessageServiceServer) DeleteAppointmentScheduleMessages(context.Context, *DeleteAppointmentScheduleMessageRequest) (*DeleteAppointmentScheduleMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAppointmentScheduleMessages not implemented")
}
func (UnimplementedScheduleMessageServiceServer) GetScheduleMessage(context.Context, *GetScheduleMessageRequest) (*GetScheduleMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetScheduleMessage not implemented")
}
func (UnimplementedScheduleMessageServiceServer) GetScheduleMessages(context.Context, *GetScheduleMessagesRequest) (*GetScheduleMessagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetScheduleMessages not implemented")
}
func (UnimplementedScheduleMessageServiceServer) RunScheduleMessageTask(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunScheduleMessageTask not implemented")
}
func (UnimplementedScheduleMessageServiceServer) SendScheduleMessage(context.Context, *SendScheduleMessageRequest) (*SendScheduleMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendScheduleMessage not implemented")
}
func (UnimplementedScheduleMessageServiceServer) mustEmbedUnimplementedScheduleMessageServiceServer() {
}

// UnsafeScheduleMessageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ScheduleMessageServiceServer will
// result in compilation errors.
type UnsafeScheduleMessageServiceServer interface {
	mustEmbedUnimplementedScheduleMessageServiceServer()
}

func RegisterScheduleMessageServiceServer(s grpc.ServiceRegistrar, srv ScheduleMessageServiceServer) {
	s.RegisterService(&ScheduleMessageService_ServiceDesc, srv)
}

func _ScheduleMessageService_CreateScheduleMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateScheduleMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleMessageServiceServer).CreateScheduleMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.ScheduleMessageService/CreateScheduleMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleMessageServiceServer).CreateScheduleMessage(ctx, req.(*CreateScheduleMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleMessageService_UpdateScheduledMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateScheduleMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleMessageServiceServer).UpdateScheduledMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.ScheduleMessageService/UpdateScheduledMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleMessageServiceServer).UpdateScheduledMessage(ctx, req.(*UpdateScheduleMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleMessageService_DeleteScheduleMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteScheduleMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleMessageServiceServer).DeleteScheduleMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.ScheduleMessageService/DeleteScheduleMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleMessageServiceServer).DeleteScheduleMessage(ctx, req.(*DeleteScheduleMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleMessageService_DeleteAppointmentScheduleMessages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAppointmentScheduleMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleMessageServiceServer).DeleteAppointmentScheduleMessages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.ScheduleMessageService/DeleteAppointmentScheduleMessages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleMessageServiceServer).DeleteAppointmentScheduleMessages(ctx, req.(*DeleteAppointmentScheduleMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleMessageService_GetScheduleMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScheduleMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleMessageServiceServer).GetScheduleMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.ScheduleMessageService/GetScheduleMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleMessageServiceServer).GetScheduleMessage(ctx, req.(*GetScheduleMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleMessageService_GetScheduleMessages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScheduleMessagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleMessageServiceServer).GetScheduleMessages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.ScheduleMessageService/GetScheduleMessages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleMessageServiceServer).GetScheduleMessages(ctx, req.(*GetScheduleMessagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleMessageService_RunScheduleMessageTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleMessageServiceServer).RunScheduleMessageTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.ScheduleMessageService/RunScheduleMessageTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleMessageServiceServer).RunScheduleMessageTask(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleMessageService_SendScheduleMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendScheduleMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleMessageServiceServer).SendScheduleMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v1.ScheduleMessageService/SendScheduleMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleMessageServiceServer).SendScheduleMessage(ctx, req.(*SendScheduleMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ScheduleMessageService_ServiceDesc is the grpc.ServiceDesc for ScheduleMessageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ScheduleMessageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.message.v1.ScheduleMessageService",
	HandlerType: (*ScheduleMessageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateScheduleMessage",
			Handler:    _ScheduleMessageService_CreateScheduleMessage_Handler,
		},
		{
			MethodName: "UpdateScheduledMessage",
			Handler:    _ScheduleMessageService_UpdateScheduledMessage_Handler,
		},
		{
			MethodName: "DeleteScheduleMessage",
			Handler:    _ScheduleMessageService_DeleteScheduleMessage_Handler,
		},
		{
			MethodName: "DeleteAppointmentScheduleMessages",
			Handler:    _ScheduleMessageService_DeleteAppointmentScheduleMessages_Handler,
		},
		{
			MethodName: "GetScheduleMessage",
			Handler:    _ScheduleMessageService_GetScheduleMessage_Handler,
		},
		{
			MethodName: "GetScheduleMessages",
			Handler:    _ScheduleMessageService_GetScheduleMessages_Handler,
		},
		{
			MethodName: "RunScheduleMessageTask",
			Handler:    _ScheduleMessageService_RunScheduleMessageTask_Handler,
		},
		{
			MethodName: "SendScheduleMessage",
			Handler:    _ScheduleMessageService_SendScheduleMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/message/v1/schedule_message_service.proto",
}
