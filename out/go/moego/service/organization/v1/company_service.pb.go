// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/organization/v1/company_service.proto

package organizationsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create company request
type CreateCompanyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id
	AccountId int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// location info
	Location *v1.CreateLocationDef `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
	// how to know us
	Source *v1.SourceType `protobuf:"varint,3,opt,name=source,proto3,enum=moego.models.organization.v1.SourceType,oneof" json:"source,omitempty"`
	// country
	Country *v1.CountryDef `protobuf:"bytes,4,opt,name=country,proto3,oneof" json:"country,omitempty"`
	// time zone
	TimeZone *v1.TimeZone `protobuf:"bytes,5,opt,name=time_zone,json=timeZone,proto3,oneof" json:"time_zone,omitempty"`
	// know about us, if source is other, this field is required
	KnowAboutUs *string `protobuf:"bytes,6,opt,name=know_about_us,json=knowAboutUs,proto3,oneof" json:"know_about_us,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,7,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// currency code
	CurrencyCode string `protobuf:"bytes,8,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// currency symbol
	CurrencySymbol string `protobuf:"bytes,9,opt,name=currency_symbol,json=currencySymbol,proto3" json:"currency_symbol,omitempty"`
	// company type
	CompanyType *int32 `protobuf:"varint,10,opt,name=company_type,json=companyType,proto3,oneof" json:"company_type,omitempty"`
}

func (x *CreateCompanyRequest) Reset() {
	*x = CreateCompanyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCompanyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCompanyRequest) ProtoMessage() {}

func (x *CreateCompanyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCompanyRequest.ProtoReflect.Descriptor instead.
func (*CreateCompanyRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCompanyRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *CreateCompanyRequest) GetLocation() *v1.CreateLocationDef {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *CreateCompanyRequest) GetSource() v1.SourceType {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return v1.SourceType(0)
}

func (x *CreateCompanyRequest) GetCountry() *v1.CountryDef {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *CreateCompanyRequest) GetTimeZone() *v1.TimeZone {
	if x != nil {
		return x.TimeZone
	}
	return nil
}

func (x *CreateCompanyRequest) GetKnowAboutUs() string {
	if x != nil && x.KnowAboutUs != nil {
		return *x.KnowAboutUs
	}
	return ""
}

func (x *CreateCompanyRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *CreateCompanyRequest) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *CreateCompanyRequest) GetCurrencySymbol() string {
	if x != nil {
		return x.CurrencySymbol
	}
	return ""
}

func (x *CreateCompanyRequest) GetCompanyType() int32 {
	if x != nil && x.CompanyType != nil {
		return *x.CompanyType
	}
	return 0
}

// create company response
type CreateCompanyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *CreateCompanyResponse) Reset() {
	*x = CreateCompanyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCompanyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCompanyResponse) ProtoMessage() {}

func (x *CreateCompanyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCompanyResponse.ProtoReflect.Descriptor instead.
func (*CreateCompanyResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateCompanyResponse) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateCompanyResponse) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// query companies by ids request
type QueryCompaniesByIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyIds []int64 `protobuf:"varint,1,rep,packed,name=company_ids,json=companyIds,proto3" json:"company_ids,omitempty"`
}

func (x *QueryCompaniesByIdsRequest) Reset() {
	*x = QueryCompaniesByIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCompaniesByIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCompaniesByIdsRequest) ProtoMessage() {}

func (x *QueryCompaniesByIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCompaniesByIdsRequest.ProtoReflect.Descriptor instead.
func (*QueryCompaniesByIdsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{2}
}

func (x *QueryCompaniesByIdsRequest) GetCompanyIds() []int64 {
	if x != nil {
		return x.CompanyIds
	}
	return nil
}

// query companies by ids response
type QueryCompaniesByIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id to company model, if company not exist or be deleted, it will not be contained in this map
	CompanyIdToCompany map[int64]*v1.CompanyModel `protobuf:"bytes,1,rep,name=company_id_to_company,json=companyIdToCompany,proto3" json:"company_id_to_company,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *QueryCompaniesByIdsResponse) Reset() {
	*x = QueryCompaniesByIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCompaniesByIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCompaniesByIdsResponse) ProtoMessage() {}

func (x *QueryCompaniesByIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCompaniesByIdsResponse.ProtoReflect.Descriptor instead.
func (*QueryCompaniesByIdsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{3}
}

func (x *QueryCompaniesByIdsResponse) GetCompanyIdToCompany() map[int64]*v1.CompanyModel {
	if x != nil {
		return x.CompanyIdToCompany
	}
	return nil
}

// update company preference setting request
type UpdateCompanyPreferenceSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// preference setting
	PreferenceSetting *v1.UpdateCompanyPreferenceSettingDef `protobuf:"bytes,2,opt,name=preference_setting,json=preferenceSetting,proto3" json:"preference_setting,omitempty"`
	// token staff id
	TokenStaffId int64 `protobuf:"varint,3,opt,name=token_staff_id,json=tokenStaffId,proto3" json:"token_staff_id,omitempty"`
}

func (x *UpdateCompanyPreferenceSettingRequest) Reset() {
	*x = UpdateCompanyPreferenceSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCompanyPreferenceSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCompanyPreferenceSettingRequest) ProtoMessage() {}

func (x *UpdateCompanyPreferenceSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCompanyPreferenceSettingRequest.ProtoReflect.Descriptor instead.
func (*UpdateCompanyPreferenceSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateCompanyPreferenceSettingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateCompanyPreferenceSettingRequest) GetPreferenceSetting() *v1.UpdateCompanyPreferenceSettingDef {
	if x != nil {
		return x.PreferenceSetting
	}
	return nil
}

func (x *UpdateCompanyPreferenceSettingRequest) GetTokenStaffId() int64 {
	if x != nil {
		return x.TokenStaffId
	}
	return 0
}

// update company preference setting response
type UpdateCompanyPreferenceSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success or not
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UpdateCompanyPreferenceSettingResponse) Reset() {
	*x = UpdateCompanyPreferenceSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCompanyPreferenceSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCompanyPreferenceSettingResponse) ProtoMessage() {}

func (x *UpdateCompanyPreferenceSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCompanyPreferenceSettingResponse.ProtoReflect.Descriptor instead.
func (*UpdateCompanyPreferenceSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateCompanyPreferenceSettingResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// get company preference setting request
type GetCompanyPreferenceSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetCompanyPreferenceSettingRequest) Reset() {
	*x = GetCompanyPreferenceSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompanyPreferenceSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompanyPreferenceSettingRequest) ProtoMessage() {}

func (x *GetCompanyPreferenceSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompanyPreferenceSettingRequest.ProtoReflect.Descriptor instead.
func (*GetCompanyPreferenceSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetCompanyPreferenceSettingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// get company preference setting response
type GetCompanyPreferenceSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// preference setting
	PreferenceSetting *v1.CompanyPreferenceSettingModel `protobuf:"bytes,1,opt,name=preference_setting,json=preferenceSetting,proto3" json:"preference_setting,omitempty"`
}

func (x *GetCompanyPreferenceSettingResponse) Reset() {
	*x = GetCompanyPreferenceSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompanyPreferenceSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompanyPreferenceSettingResponse) ProtoMessage() {}

func (x *GetCompanyPreferenceSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompanyPreferenceSettingResponse.ProtoReflect.Descriptor instead.
func (*GetCompanyPreferenceSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetCompanyPreferenceSettingResponse) GetPreferenceSetting() *v1.CompanyPreferenceSettingModel {
	if x != nil {
		return x.PreferenceSetting
	}
	return nil
}

// is moego pay enable request
type IsMoegoPayEnableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *IsMoegoPayEnableRequest) Reset() {
	*x = IsMoegoPayEnableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsMoegoPayEnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsMoegoPayEnableRequest) ProtoMessage() {}

func (x *IsMoegoPayEnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsMoegoPayEnableRequest.ProtoReflect.Descriptor instead.
func (*IsMoegoPayEnableRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{8}
}

func (x *IsMoegoPayEnableRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// is moego pay enable response
type IsMoegoPayEnableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is moego pay enable
	IsMoegoPayEnable bool `protobuf:"varint,1,opt,name=is_moego_pay_enable,json=isMoegoPayEnable,proto3" json:"is_moego_pay_enable,omitempty"`
}

func (x *IsMoegoPayEnableResponse) Reset() {
	*x = IsMoegoPayEnableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsMoegoPayEnableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsMoegoPayEnableResponse) ProtoMessage() {}

func (x *IsMoegoPayEnableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsMoegoPayEnableResponse.ProtoReflect.Descriptor instead.
func (*IsMoegoPayEnableResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{9}
}

func (x *IsMoegoPayEnableResponse) GetIsMoegoPayEnable() bool {
	if x != nil {
		return x.IsMoegoPayEnable
	}
	return false
}

// is retail enable request
type IsRetailEnableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id, if not set, will query all business in this company
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *IsRetailEnableRequest) Reset() {
	*x = IsRetailEnableRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsRetailEnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsRetailEnableRequest) ProtoMessage() {}

func (x *IsRetailEnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsRetailEnableRequest.ProtoReflect.Descriptor instead.
func (*IsRetailEnableRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{10}
}

func (x *IsRetailEnableRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *IsRetailEnableRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// is retail enable response
type IsRetailEnableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is retail enable
	IsRetailEnable bool `protobuf:"varint,1,opt,name=is_retail_enable,json=isRetailEnable,proto3" json:"is_retail_enable,omitempty"`
}

func (x *IsRetailEnableResponse) Reset() {
	*x = IsRetailEnableResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsRetailEnableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsRetailEnableResponse) ProtoMessage() {}

func (x *IsRetailEnableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsRetailEnableResponse.ProtoReflect.Descriptor instead.
func (*IsRetailEnableResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{11}
}

func (x *IsRetailEnableResponse) GetIsRetailEnable() bool {
	if x != nil {
		return x.IsRetailEnable
	}
	return false
}

// add tax rule request
type AddTaxRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tax rule
	TaxRule *v1.TaxRuleDef `protobuf:"bytes,1,opt,name=tax_rule,json=taxRule,proto3" json:"tax_rule,omitempty"`
	// the operator id, allow internal modifier or external modifier
	//
	// Types that are assignable to OperatorIdentifier:
	//
	//	*AddTaxRuleRequest_TokenStaffId
	//	*AddTaxRuleRequest_InternalOperatorId
	OperatorIdentifier isAddTaxRuleRequest_OperatorIdentifier `protobuf_oneof:"operator_identifier"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,4,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
}

func (x *AddTaxRuleRequest) Reset() {
	*x = AddTaxRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddTaxRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTaxRuleRequest) ProtoMessage() {}

func (x *AddTaxRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTaxRuleRequest.ProtoReflect.Descriptor instead.
func (*AddTaxRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{12}
}

func (x *AddTaxRuleRequest) GetTaxRule() *v1.TaxRuleDef {
	if x != nil {
		return x.TaxRule
	}
	return nil
}

func (m *AddTaxRuleRequest) GetOperatorIdentifier() isAddTaxRuleRequest_OperatorIdentifier {
	if m != nil {
		return m.OperatorIdentifier
	}
	return nil
}

func (x *AddTaxRuleRequest) GetTokenStaffId() int64 {
	if x, ok := x.GetOperatorIdentifier().(*AddTaxRuleRequest_TokenStaffId); ok {
		return x.TokenStaffId
	}
	return 0
}

func (x *AddTaxRuleRequest) GetInternalOperatorId() string {
	if x, ok := x.GetOperatorIdentifier().(*AddTaxRuleRequest_InternalOperatorId); ok {
		return x.InternalOperatorId
	}
	return ""
}

func (x *AddTaxRuleRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

type isAddTaxRuleRequest_OperatorIdentifier interface {
	isAddTaxRuleRequest_OperatorIdentifier()
}

type AddTaxRuleRequest_TokenStaffId struct {
	// staff id from token
	TokenStaffId int64 `protobuf:"varint,2,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof"`
}

type AddTaxRuleRequest_InternalOperatorId struct {
	// the internal operator id
	InternalOperatorId string `protobuf:"bytes,3,opt,name=internal_operator_id,json=internalOperatorId,proto3,oneof"`
}

func (*AddTaxRuleRequest_TokenStaffId) isAddTaxRuleRequest_OperatorIdentifier() {}

func (*AddTaxRuleRequest_InternalOperatorId) isAddTaxRuleRequest_OperatorIdentifier() {}

// add tax rule response
type AddTaxRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tax rule id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *AddTaxRuleResponse) Reset() {
	*x = AddTaxRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddTaxRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTaxRuleResponse) ProtoMessage() {}

func (x *AddTaxRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTaxRuleResponse.ProtoReflect.Descriptor instead.
func (*AddTaxRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{13}
}

func (x *AddTaxRuleResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// update tax rule request
type UpdateTaxRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tax rule id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// tax rule
	TaxRule *v1.TaxRuleDef `protobuf:"bytes,2,opt,name=tax_rule,json=taxRule,proto3" json:"tax_rule,omitempty"`
	// the operator id, allow internal modifier or external modifier
	//
	// Types that are assignable to OperatorIdentifier:
	//
	//	*UpdateTaxRuleRequest_TokenStaffId
	//	*UpdateTaxRuleRequest_InternalOperatorId
	OperatorIdentifier isUpdateTaxRuleRequest_OperatorIdentifier `protobuf_oneof:"operator_identifier"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,5,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
}

func (x *UpdateTaxRuleRequest) Reset() {
	*x = UpdateTaxRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTaxRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTaxRuleRequest) ProtoMessage() {}

func (x *UpdateTaxRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTaxRuleRequest.ProtoReflect.Descriptor instead.
func (*UpdateTaxRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateTaxRuleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateTaxRuleRequest) GetTaxRule() *v1.TaxRuleDef {
	if x != nil {
		return x.TaxRule
	}
	return nil
}

func (m *UpdateTaxRuleRequest) GetOperatorIdentifier() isUpdateTaxRuleRequest_OperatorIdentifier {
	if m != nil {
		return m.OperatorIdentifier
	}
	return nil
}

func (x *UpdateTaxRuleRequest) GetTokenStaffId() int64 {
	if x, ok := x.GetOperatorIdentifier().(*UpdateTaxRuleRequest_TokenStaffId); ok {
		return x.TokenStaffId
	}
	return 0
}

func (x *UpdateTaxRuleRequest) GetInternalOperatorId() string {
	if x, ok := x.GetOperatorIdentifier().(*UpdateTaxRuleRequest_InternalOperatorId); ok {
		return x.InternalOperatorId
	}
	return ""
}

func (x *UpdateTaxRuleRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

type isUpdateTaxRuleRequest_OperatorIdentifier interface {
	isUpdateTaxRuleRequest_OperatorIdentifier()
}

type UpdateTaxRuleRequest_TokenStaffId struct {
	// staff id from token
	TokenStaffId int64 `protobuf:"varint,3,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof"`
}

type UpdateTaxRuleRequest_InternalOperatorId struct {
	// the internal operator id
	InternalOperatorId string `protobuf:"bytes,4,opt,name=internal_operator_id,json=internalOperatorId,proto3,oneof"`
}

func (*UpdateTaxRuleRequest_TokenStaffId) isUpdateTaxRuleRequest_OperatorIdentifier() {}

func (*UpdateTaxRuleRequest_InternalOperatorId) isUpdateTaxRuleRequest_OperatorIdentifier() {}

// update tax rule response
type UpdateTaxRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UpdateTaxRuleResponse) Reset() {
	*x = UpdateTaxRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTaxRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTaxRuleResponse) ProtoMessage() {}

func (x *UpdateTaxRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTaxRuleResponse.ProtoReflect.Descriptor instead.
func (*UpdateTaxRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateTaxRuleResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// delete tax rule request
type DeleteTaxRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tax rule id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the operator id, allow internal modifier or external modifier
	//
	// Types that are assignable to OperatorIdentifier:
	//
	//	*DeleteTaxRuleRequest_TokenStaffId
	//	*DeleteTaxRuleRequest_InternalOperatorId
	OperatorIdentifier isDeleteTaxRuleRequest_OperatorIdentifier `protobuf_oneof:"operator_identifier"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,4,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
}

func (x *DeleteTaxRuleRequest) Reset() {
	*x = DeleteTaxRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteTaxRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTaxRuleRequest) ProtoMessage() {}

func (x *DeleteTaxRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTaxRuleRequest.ProtoReflect.Descriptor instead.
func (*DeleteTaxRuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteTaxRuleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (m *DeleteTaxRuleRequest) GetOperatorIdentifier() isDeleteTaxRuleRequest_OperatorIdentifier {
	if m != nil {
		return m.OperatorIdentifier
	}
	return nil
}

func (x *DeleteTaxRuleRequest) GetTokenStaffId() int64 {
	if x, ok := x.GetOperatorIdentifier().(*DeleteTaxRuleRequest_TokenStaffId); ok {
		return x.TokenStaffId
	}
	return 0
}

func (x *DeleteTaxRuleRequest) GetInternalOperatorId() string {
	if x, ok := x.GetOperatorIdentifier().(*DeleteTaxRuleRequest_InternalOperatorId); ok {
		return x.InternalOperatorId
	}
	return ""
}

func (x *DeleteTaxRuleRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

type isDeleteTaxRuleRequest_OperatorIdentifier interface {
	isDeleteTaxRuleRequest_OperatorIdentifier()
}

type DeleteTaxRuleRequest_TokenStaffId struct {
	// staff id from token
	TokenStaffId int64 `protobuf:"varint,2,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof"`
}

type DeleteTaxRuleRequest_InternalOperatorId struct {
	// the internal operator id
	InternalOperatorId string `protobuf:"bytes,3,opt,name=internal_operator_id,json=internalOperatorId,proto3,oneof"`
}

func (*DeleteTaxRuleRequest_TokenStaffId) isDeleteTaxRuleRequest_OperatorIdentifier() {}

func (*DeleteTaxRuleRequest_InternalOperatorId) isDeleteTaxRuleRequest_OperatorIdentifier() {}

// delete tax rule response
type DeleteTaxRuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// delete result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *DeleteTaxRuleResponse) Reset() {
	*x = DeleteTaxRuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteTaxRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTaxRuleResponse) ProtoMessage() {}

func (x *DeleteTaxRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTaxRuleResponse.ProtoReflect.Descriptor instead.
func (*DeleteTaxRuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{17}
}

func (x *DeleteTaxRuleResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// get tax rule list request
type GetTaxRuleListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the operator id, allow internal modifier or external modifier
	//
	// Types that are assignable to OperatorIdentifier:
	//
	//	*GetTaxRuleListRequest_TokenStaffId
	//	*GetTaxRuleListRequest_InternalOperatorId
	OperatorIdentifier isGetTaxRuleListRequest_OperatorIdentifier `protobuf_oneof:"operator_identifier"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,3,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
}

func (x *GetTaxRuleListRequest) Reset() {
	*x = GetTaxRuleListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaxRuleListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaxRuleListRequest) ProtoMessage() {}

func (x *GetTaxRuleListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaxRuleListRequest.ProtoReflect.Descriptor instead.
func (*GetTaxRuleListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{18}
}

func (m *GetTaxRuleListRequest) GetOperatorIdentifier() isGetTaxRuleListRequest_OperatorIdentifier {
	if m != nil {
		return m.OperatorIdentifier
	}
	return nil
}

func (x *GetTaxRuleListRequest) GetTokenStaffId() int64 {
	if x, ok := x.GetOperatorIdentifier().(*GetTaxRuleListRequest_TokenStaffId); ok {
		return x.TokenStaffId
	}
	return 0
}

func (x *GetTaxRuleListRequest) GetInternalOperatorId() string {
	if x, ok := x.GetOperatorIdentifier().(*GetTaxRuleListRequest_InternalOperatorId); ok {
		return x.InternalOperatorId
	}
	return ""
}

func (x *GetTaxRuleListRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

type isGetTaxRuleListRequest_OperatorIdentifier interface {
	isGetTaxRuleListRequest_OperatorIdentifier()
}

type GetTaxRuleListRequest_TokenStaffId struct {
	// staff id from token
	TokenStaffId int64 `protobuf:"varint,1,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof"`
}

type GetTaxRuleListRequest_InternalOperatorId struct {
	// the internal operator id
	InternalOperatorId string `protobuf:"bytes,2,opt,name=internal_operator_id,json=internalOperatorId,proto3,oneof"`
}

func (*GetTaxRuleListRequest_TokenStaffId) isGetTaxRuleListRequest_OperatorIdentifier() {}

func (*GetTaxRuleListRequest_InternalOperatorId) isGetTaxRuleListRequest_OperatorIdentifier() {}

// get tax rule list response
type GetTaxRuleListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tax rule list
	Rule []*v1.TaxRuleModel `protobuf:"bytes,1,rep,name=rule,proto3" json:"rule,omitempty"`
}

func (x *GetTaxRuleListResponse) Reset() {
	*x = GetTaxRuleListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaxRuleListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaxRuleListResponse) ProtoMessage() {}

func (x *GetTaxRuleListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaxRuleListResponse.ProtoReflect.Descriptor instead.
func (*GetTaxRuleListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetTaxRuleListResponse) GetRule() []*v1.TaxRuleModel {
	if x != nil {
		return x.Rule
	}
	return nil
}

// get business id for mobile location request
type GetBusinessIdForMobileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetBusinessIdForMobileRequest) Reset() {
	*x = GetBusinessIdForMobileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessIdForMobileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessIdForMobileRequest) ProtoMessage() {}

func (x *GetBusinessIdForMobileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessIdForMobileRequest.ProtoReflect.Descriptor instead.
func (*GetBusinessIdForMobileRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetBusinessIdForMobileRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// get business id for mobile location response
type GetBusinessIdForMobileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetBusinessIdForMobileResponse) Reset() {
	*x = GetBusinessIdForMobileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessIdForMobileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessIdForMobileResponse) ProtoMessage() {}

func (x *GetBusinessIdForMobileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessIdForMobileResponse.ProtoReflect.Descriptor instead.
func (*GetBusinessIdForMobileResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetBusinessIdForMobileResponse) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// set company migrate status request
type SetCompanyMigrateStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// migrate status
	MigrateStatus v1.MigrateStatus `protobuf:"varint,2,opt,name=migrate_status,json=migrateStatus,proto3,enum=moego.models.organization.v1.MigrateStatus" json:"migrate_status,omitempty"`
}

func (x *SetCompanyMigrateStatusRequest) Reset() {
	*x = SetCompanyMigrateStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetCompanyMigrateStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCompanyMigrateStatusRequest) ProtoMessage() {}

func (x *SetCompanyMigrateStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCompanyMigrateStatusRequest.ProtoReflect.Descriptor instead.
func (*SetCompanyMigrateStatusRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{22}
}

func (x *SetCompanyMigrateStatusRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SetCompanyMigrateStatusRequest) GetMigrateStatus() v1.MigrateStatus {
	if x != nil {
		return x.MigrateStatus
	}
	return v1.MigrateStatus(0)
}

// set company migrate status response
type SetCompanyMigrateStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetCompanyMigrateStatusResponse) Reset() {
	*x = SetCompanyMigrateStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetCompanyMigrateStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCompanyMigrateStatusResponse) ProtoMessage() {}

func (x *SetCompanyMigrateStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCompanyMigrateStatusResponse.ProtoReflect.Descriptor instead.
func (*SetCompanyMigrateStatusResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{23}
}

// mark company as migrated response
type MarkCompanyAsMigratedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MarkCompanyAsMigratedResponse) Reset() {
	*x = MarkCompanyAsMigratedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkCompanyAsMigratedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkCompanyAsMigratedResponse) ProtoMessage() {}

func (x *MarkCompanyAsMigratedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkCompanyAsMigratedResponse.ProtoReflect.Descriptor instead.
func (*MarkCompanyAsMigratedResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{24}
}

// is company migrate request
type IsCompanyMigrateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id, 可选 , not support now
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *IsCompanyMigrateRequest) Reset() {
	*x = IsCompanyMigrateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsCompanyMigrateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsCompanyMigrateRequest) ProtoMessage() {}

func (x *IsCompanyMigrateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsCompanyMigrateRequest.ProtoReflect.Descriptor instead.
func (*IsCompanyMigrateRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{25}
}

func (x *IsCompanyMigrateRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *IsCompanyMigrateRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// is company migrate response
type IsCompanyMigrateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is company migrate
	IsCompanyMigrate bool `protobuf:"varint,1,opt,name=is_company_migrate,json=isCompanyMigrate,proto3" json:"is_company_migrate,omitempty"`
}

func (x *IsCompanyMigrateResponse) Reset() {
	*x = IsCompanyMigrateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsCompanyMigrateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsCompanyMigrateResponse) ProtoMessage() {}

func (x *IsCompanyMigrateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsCompanyMigrateResponse.ProtoReflect.Descriptor instead.
func (*IsCompanyMigrateResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{26}
}

func (x *IsCompanyMigrateResponse) GetIsCompanyMigrate() bool {
	if x != nil {
		return x.IsCompanyMigrate
	}
	return false
}

// is companies migrate request
type IsCompaniesMigrateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyIds []int64 `protobuf:"varint,1,rep,packed,name=company_ids,json=companyIds,proto3" json:"company_ids,omitempty"`
}

func (x *IsCompaniesMigrateRequest) Reset() {
	*x = IsCompaniesMigrateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsCompaniesMigrateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsCompaniesMigrateRequest) ProtoMessage() {}

func (x *IsCompaniesMigrateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsCompaniesMigrateRequest.ProtoReflect.Descriptor instead.
func (*IsCompaniesMigrateRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{27}
}

func (x *IsCompaniesMigrateRequest) GetCompanyIds() []int64 {
	if x != nil {
		return x.CompanyIds
	}
	return nil
}

// is companies migrate response
type IsCompaniesMigrateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is companies migrate
	IsCompaniesMigrateMap map[int64]bool `protobuf:"bytes,1,rep,name=is_companies_migrate_map,json=isCompaniesMigrateMap,proto3" json:"is_companies_migrate_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *IsCompaniesMigrateResponse) Reset() {
	*x = IsCompaniesMigrateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsCompaniesMigrateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsCompaniesMigrateResponse) ProtoMessage() {}

func (x *IsCompaniesMigrateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsCompaniesMigrateResponse.ProtoReflect.Descriptor instead.
func (*IsCompaniesMigrateResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{28}
}

func (x *IsCompaniesMigrateResponse) GetIsCompaniesMigrateMap() map[int64]bool {
	if x != nil {
		return x.IsCompaniesMigrateMap
	}
	return nil
}

// get clock in/out setting request
type GetClockInOutSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// token company id
	TokenCompanyId int64 `protobuf:"varint,1,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
}

func (x *GetClockInOutSettingRequest) Reset() {
	*x = GetClockInOutSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClockInOutSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClockInOutSettingRequest) ProtoMessage() {}

func (x *GetClockInOutSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClockInOutSettingRequest.ProtoReflect.Descriptor instead.
func (*GetClockInOutSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{29}
}

func (x *GetClockInOutSettingRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

// get clock in out setting response
type GetClockInOutSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// clock in/out setting
	ClockInOutSetting *v1.ClockInOutSettingModel `protobuf:"bytes,1,opt,name=clock_in_out_setting,json=clockInOutSetting,proto3" json:"clock_in_out_setting,omitempty"`
}

func (x *GetClockInOutSettingResponse) Reset() {
	*x = GetClockInOutSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClockInOutSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClockInOutSettingResponse) ProtoMessage() {}

func (x *GetClockInOutSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClockInOutSettingResponse.ProtoReflect.Descriptor instead.
func (*GetClockInOutSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{30}
}

func (x *GetClockInOutSettingResponse) GetClockInOutSetting() *v1.ClockInOutSettingModel {
	if x != nil {
		return x.ClockInOutSetting
	}
	return nil
}

// update clock in/out setting request
type UpdateClockInOutSettingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// token company id
	TokenCompanyId int64 `protobuf:"varint,1,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
	// token staff id, operator id
	TokenStaffId int64 `protobuf:"varint,2,opt,name=token_staff_id,json=tokenStaffId,proto3" json:"token_staff_id,omitempty"` // preserved 3-5 for future use
	// update clock in/out setting params
	ClockInOutSetting *v1.UpdateClockInOutSettingDef `protobuf:"bytes,6,opt,name=clock_in_out_setting,json=clockInOutSetting,proto3" json:"clock_in_out_setting,omitempty"`
}

func (x *UpdateClockInOutSettingRequest) Reset() {
	*x = UpdateClockInOutSettingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateClockInOutSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateClockInOutSettingRequest) ProtoMessage() {}

func (x *UpdateClockInOutSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateClockInOutSettingRequest.ProtoReflect.Descriptor instead.
func (*UpdateClockInOutSettingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{31}
}

func (x *UpdateClockInOutSettingRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

func (x *UpdateClockInOutSettingRequest) GetTokenStaffId() int64 {
	if x != nil {
		return x.TokenStaffId
	}
	return 0
}

func (x *UpdateClockInOutSettingRequest) GetClockInOutSetting() *v1.UpdateClockInOutSettingDef {
	if x != nil {
		return x.ClockInOutSetting
	}
	return nil
}

// update clock in/out setting response
type UpdateClockInOutSettingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UpdateClockInOutSettingResponse) Reset() {
	*x = UpdateClockInOutSettingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateClockInOutSettingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateClockInOutSettingResponse) ProtoMessage() {}

func (x *UpdateClockInOutSettingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateClockInOutSettingResponse.ProtoReflect.Descriptor instead.
func (*UpdateClockInOutSettingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{32}
}

func (x *UpdateClockInOutSettingResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// list companies by enterprise id request
type ListCompaniesByEnterpriseIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *ListCompaniesByEnterpriseIdRequest) Reset() {
	*x = ListCompaniesByEnterpriseIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCompaniesByEnterpriseIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCompaniesByEnterpriseIdRequest) ProtoMessage() {}

func (x *ListCompaniesByEnterpriseIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCompaniesByEnterpriseIdRequest.ProtoReflect.Descriptor instead.
func (*ListCompaniesByEnterpriseIdRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{33}
}

func (x *ListCompaniesByEnterpriseIdRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *ListCompaniesByEnterpriseIdRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list companies by enterprise id response
type ListCompaniesByEnterpriseIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company models
	Companies []*v1.CompanyModel `protobuf:"bytes,1,rep,name=companies,proto3" json:"companies,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *ListCompaniesByEnterpriseIdResponse) Reset() {
	*x = ListCompaniesByEnterpriseIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCompaniesByEnterpriseIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCompaniesByEnterpriseIdResponse) ProtoMessage() {}

func (x *ListCompaniesByEnterpriseIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCompaniesByEnterpriseIdResponse.ProtoReflect.Descriptor instead.
func (*ListCompaniesByEnterpriseIdResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{34}
}

func (x *ListCompaniesByEnterpriseIdResponse) GetCompanies() []*v1.CompanyModel {
	if x != nil {
		return x.Companies
	}
	return nil
}

func (x *ListCompaniesByEnterpriseIdResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// company question query
type CompanyQuestionRecordQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *CompanyQuestionRecordQueryRequest) Reset() {
	*x = CompanyQuestionRecordQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyQuestionRecordQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyQuestionRecordQueryRequest) ProtoMessage() {}

func (x *CompanyQuestionRecordQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyQuestionRecordQueryRequest.ProtoReflect.Descriptor instead.
func (*CompanyQuestionRecordQueryRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{35}
}

func (x *CompanyQuestionRecordQueryRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// company question result
type CompanyQuestionRecordQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is have fill question
	IsFillQuestion bool `protobuf:"varint,1,opt,name=is_fill_question,json=isFillQuestion,proto3" json:"is_fill_question,omitempty"`
}

func (x *CompanyQuestionRecordQueryResponse) Reset() {
	*x = CompanyQuestionRecordQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyQuestionRecordQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyQuestionRecordQueryResponse) ProtoMessage() {}

func (x *CompanyQuestionRecordQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyQuestionRecordQueryResponse.ProtoReflect.Descriptor instead.
func (*CompanyQuestionRecordQueryResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{36}
}

func (x *CompanyQuestionRecordQueryResponse) GetIsFillQuestion() bool {
	if x != nil {
		return x.IsFillQuestion
	}
	return false
}

// save company question record
type CompanyQuestionRecordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pets per month
	PetPerMonth string `protobuf:"bytes,1,opt,name=pet_per_month,json=petPerMonth,proto3" json:"pet_per_month,omitempty"`
	// total location number
	TotalLocations *string `protobuf:"bytes,2,opt,name=total_locations,json=totalLocations,proto3,oneof" json:"total_locations,omitempty"`
	// total van number
	TotalVans *string `protobuf:"bytes,3,opt,name=total_vans,json=totalVans,proto3,oneof" json:"total_vans,omitempty"`
	// move from other software
	MoveFrom string `protobuf:"bytes,4,opt,name=move_from,json=moveFrom,proto3" json:"move_from,omitempty"`
	// source from
	SourceFrom string `protobuf:"bytes,5,opt,name=source_from,json=sourceFrom,proto3" json:"source_from,omitempty"`
	// other source from
	SourceFromOther *string `protobuf:"bytes,6,opt,name=source_from_other,json=sourceFromOther,proto3,oneof" json:"source_from_other,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,7,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *CompanyQuestionRecordRequest) Reset() {
	*x = CompanyQuestionRecordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyQuestionRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyQuestionRecordRequest) ProtoMessage() {}

func (x *CompanyQuestionRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyQuestionRecordRequest.ProtoReflect.Descriptor instead.
func (*CompanyQuestionRecordRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{37}
}

func (x *CompanyQuestionRecordRequest) GetPetPerMonth() string {
	if x != nil {
		return x.PetPerMonth
	}
	return ""
}

func (x *CompanyQuestionRecordRequest) GetTotalLocations() string {
	if x != nil && x.TotalLocations != nil {
		return *x.TotalLocations
	}
	return ""
}

func (x *CompanyQuestionRecordRequest) GetTotalVans() string {
	if x != nil && x.TotalVans != nil {
		return *x.TotalVans
	}
	return ""
}

func (x *CompanyQuestionRecordRequest) GetMoveFrom() string {
	if x != nil {
		return x.MoveFrom
	}
	return ""
}

func (x *CompanyQuestionRecordRequest) GetSourceFrom() string {
	if x != nil {
		return x.SourceFrom
	}
	return ""
}

func (x *CompanyQuestionRecordRequest) GetSourceFromOther() string {
	if x != nil && x.SourceFromOther != nil {
		return *x.SourceFromOther
	}
	return ""
}

func (x *CompanyQuestionRecordRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// save company question record result
type CompanyQuestionRecordResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *CompanyQuestionRecordResponse) Reset() {
	*x = CompanyQuestionRecordResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyQuestionRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyQuestionRecordResponse) ProtoMessage() {}

func (x *CompanyQuestionRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyQuestionRecordResponse.ProtoReflect.Descriptor instead.
func (*CompanyQuestionRecordResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{38}
}

func (x *CompanyQuestionRecordResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// modify company account sorting request
type SortCompanyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id
	AccountId int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// ids
	Ids []int64 `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortCompanyRequest) Reset() {
	*x = SortCompanyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortCompanyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortCompanyRequest) ProtoMessage() {}

func (x *SortCompanyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortCompanyRequest.ProtoReflect.Descriptor instead.
func (*SortCompanyRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{39}
}

func (x *SortCompanyRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *SortCompanyRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// modify company account sorting response
type SortCompanyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortCompanyResponse) Reset() {
	*x = SortCompanyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortCompanyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortCompanyResponse) ProtoMessage() {}

func (x *SortCompanyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortCompanyResponse.ProtoReflect.Descriptor instead.
func (*SortCompanyResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{40}
}

// query company list  request
type ListCompanyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the account id
	AccountId *int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3,oneof" json:"account_id,omitempty"`
	// the company id
	CompanyId *int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// the company name
	Name *string `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// the company country
	Country *string `protobuf:"bytes,4,opt,name=country,proto3,oneof" json:"country,omitempty"`
	// the enterprise id
	EnterpriseId *int64 `protobuf:"varint,5,opt,name=enterprise_id,json=enterpriseId,proto3,oneof" json:"enterprise_id,omitempty"`
	// the pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListCompanyRequest) Reset() {
	*x = ListCompanyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCompanyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCompanyRequest) ProtoMessage() {}

func (x *ListCompanyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCompanyRequest.ProtoReflect.Descriptor instead.
func (*ListCompanyRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{41}
}

func (x *ListCompanyRequest) GetAccountId() int64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *ListCompanyRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *ListCompanyRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ListCompanyRequest) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

func (x *ListCompanyRequest) GetEnterpriseId() int64 {
	if x != nil && x.EnterpriseId != nil {
		return *x.EnterpriseId
	}
	return 0
}

func (x *ListCompanyRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// query company list  response
type ListCompanyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company list
	Companies []*v1.CompanyModel `protobuf:"bytes,1,rep,name=companies,proto3" json:"companies,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListCompanyResponse) Reset() {
	*x = ListCompanyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCompanyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCompanyResponse) ProtoMessage() {}

func (x *ListCompanyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCompanyResponse.ProtoReflect.Descriptor instead.
func (*ListCompanyResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{42}
}

func (x *ListCompanyResponse) GetCompanies() []*v1.CompanyModel {
	if x != nil {
		return x.Companies
	}
	return nil
}

func (x *ListCompanyResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// qurey company list  request
type UpdateCompanyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// company name
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// enable square
	EnableSquare *bool `protobuf:"varint,3,opt,name=enable_square,json=enableSquare,proto3,oneof" json:"enable_square,omitempty"`
	// enable stripe reader
	EnableStripeReader *bool `protobuf:"varint,4,opt,name=enable_stripe_reader,json=enableStripeReader,proto3,oneof" json:"enable_stripe_reader,omitempty"`
	// change company account_id
	AccountId *int64 `protobuf:"varint,5,opt,name=account_id,json=accountId,proto3,oneof" json:"account_id,omitempty"`
	// location num
	LocationNum *int32 `protobuf:"varint,6,opt,name=location_num,json=locationNum,proto3,oneof" json:"location_num,omitempty"`
	// van num
	VanNum *int32 `protobuf:"varint,7,opt,name=van_num,json=vanNum,proto3,oneof" json:"van_num,omitempty"`
	// enterprise id
	EnterpriseId *int64 `protobuf:"varint,8,opt,name=enterprise_id,json=enterpriseId,proto3,oneof" json:"enterprise_id,omitempty"`
	// level
	Level *int32 `protobuf:"varint,9,opt,name=level,proto3,oneof" json:"level,omitempty"`
}

func (x *UpdateCompanyRequest) Reset() {
	*x = UpdateCompanyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCompanyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCompanyRequest) ProtoMessage() {}

func (x *UpdateCompanyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCompanyRequest.ProtoReflect.Descriptor instead.
func (*UpdateCompanyRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{43}
}

func (x *UpdateCompanyRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateCompanyRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateCompanyRequest) GetEnableSquare() bool {
	if x != nil && x.EnableSquare != nil {
		return *x.EnableSquare
	}
	return false
}

func (x *UpdateCompanyRequest) GetEnableStripeReader() bool {
	if x != nil && x.EnableStripeReader != nil {
		return *x.EnableStripeReader
	}
	return false
}

func (x *UpdateCompanyRequest) GetAccountId() int64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *UpdateCompanyRequest) GetLocationNum() int32 {
	if x != nil && x.LocationNum != nil {
		return *x.LocationNum
	}
	return 0
}

func (x *UpdateCompanyRequest) GetVanNum() int32 {
	if x != nil && x.VanNum != nil {
		return *x.VanNum
	}
	return 0
}

func (x *UpdateCompanyRequest) GetEnterpriseId() int64 {
	if x != nil && x.EnterpriseId != nil {
		return *x.EnterpriseId
	}
	return 0
}

func (x *UpdateCompanyRequest) GetLevel() int32 {
	if x != nil && x.Level != nil {
		return *x.Level
	}
	return 0
}

// update company list  response
type UpdateCompanyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *UpdateCompanyResponse) Reset() {
	*x = UpdateCompanyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCompanyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCompanyResponse) ProtoMessage() {}

func (x *UpdateCompanyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCompanyResponse.ProtoReflect.Descriptor instead.
func (*UpdateCompanyResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{44}
}

func (x *UpdateCompanyResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

// list company preference setting request
type ListCompanyPreferenceSettingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company ids
	CompanyIds []int64 `protobuf:"varint,1,rep,packed,name=company_ids,json=companyIds,proto3" json:"company_ids,omitempty"`
}

func (x *ListCompanyPreferenceSettingsRequest) Reset() {
	*x = ListCompanyPreferenceSettingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCompanyPreferenceSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCompanyPreferenceSettingsRequest) ProtoMessage() {}

func (x *ListCompanyPreferenceSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCompanyPreferenceSettingsRequest.ProtoReflect.Descriptor instead.
func (*ListCompanyPreferenceSettingsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{45}
}

func (x *ListCompanyPreferenceSettingsRequest) GetCompanyIds() []int64 {
	if x != nil {
		return x.CompanyIds
	}
	return nil
}

// list company preference setting response
type ListCompanyPreferenceSettingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company preference setting list
	CompanyPreferenceMap map[int64]*v1.CompanyPreferenceSettingModel `protobuf:"bytes,1,rep,name=company_preference_map,json=companyPreferenceMap,proto3" json:"company_preference_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ListCompanyPreferenceSettingsResponse) Reset() {
	*x = ListCompanyPreferenceSettingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCompanyPreferenceSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCompanyPreferenceSettingsResponse) ProtoMessage() {}

func (x *ListCompanyPreferenceSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCompanyPreferenceSettingsResponse.ProtoReflect.Descriptor instead.
func (*ListCompanyPreferenceSettingsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{46}
}

func (x *ListCompanyPreferenceSettingsResponse) GetCompanyPreferenceMap() map[int64]*v1.CompanyPreferenceSettingModel {
	if x != nil {
		return x.CompanyPreferenceMap
	}
	return nil
}

// create company from enterprise hub request
type CreateCompanyFromEnterpriseHubRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id if set it will create a company owner with this account id
	AccountId int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// company id
	TemplateCompanyId int64 `protobuf:"varint,2,opt,name=template_company_id,json=templateCompanyId,proto3" json:"template_company_id,omitempty"`
	// if first name & last name has value ,should create new own staff which is not bind account id
	Email *string `protobuf:"bytes,3,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// first name
	FirstName *string `protobuf:"bytes,4,opt,name=first_name,json=firstName,proto3,oneof" json:"first_name,omitempty"`
	// last name
	LastName *string `protobuf:"bytes,5,opt,name=last_name,json=lastName,proto3,oneof" json:"last_name,omitempty"`
	// location num
	LocationNum *int32 `protobuf:"varint,6,opt,name=location_num,json=locationNum,proto3,oneof" json:"location_num,omitempty"`
	// van num
	VanNum *int32 `protobuf:"varint,7,opt,name=van_num,json=vanNum,proto3,oneof" json:"van_num,omitempty"`
	// phone number
	PhoneNumber *string `protobuf:"bytes,8,opt,name=phone_number,json=phoneNumber,proto3,oneof" json:"phone_number,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,9,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// name
	Name string `protobuf:"bytes,10,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CreateCompanyFromEnterpriseHubRequest) Reset() {
	*x = CreateCompanyFromEnterpriseHubRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCompanyFromEnterpriseHubRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCompanyFromEnterpriseHubRequest) ProtoMessage() {}

func (x *CreateCompanyFromEnterpriseHubRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_company_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCompanyFromEnterpriseHubRequest.ProtoReflect.Descriptor instead.
func (*CreateCompanyFromEnterpriseHubRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_company_service_proto_rawDescGZIP(), []int{47}
}

func (x *CreateCompanyFromEnterpriseHubRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *CreateCompanyFromEnterpriseHubRequest) GetTemplateCompanyId() int64 {
	if x != nil {
		return x.TemplateCompanyId
	}
	return 0
}

func (x *CreateCompanyFromEnterpriseHubRequest) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *CreateCompanyFromEnterpriseHubRequest) GetFirstName() string {
	if x != nil && x.FirstName != nil {
		return *x.FirstName
	}
	return ""
}

func (x *CreateCompanyFromEnterpriseHubRequest) GetLastName() string {
	if x != nil && x.LastName != nil {
		return *x.LastName
	}
	return ""
}

func (x *CreateCompanyFromEnterpriseHubRequest) GetLocationNum() int32 {
	if x != nil && x.LocationNum != nil {
		return *x.LocationNum
	}
	return 0
}

func (x *CreateCompanyFromEnterpriseHubRequest) GetVanNum() int32 {
	if x != nil && x.VanNum != nil {
		return *x.VanNum
	}
	return 0
}

func (x *CreateCompanyFromEnterpriseHubRequest) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

func (x *CreateCompanyFromEnterpriseHubRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CreateCompanyFromEnterpriseHubRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_moego_service_organization_v1_company_service_proto protoreflect.FileDescriptor

var file_moego_service_organization_v1_company_service_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x3c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x2f, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x3d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65,
	0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74,
	0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x61, 0x78, 0x5f,
	0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x61, 0x78, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa3, 0x05, 0x0a, 0x14, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x55, 0x0a, 0x08, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x47, 0x0a, 0x07, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x44, 0x65, 0x66, 0x48, 0x01, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x88,
	0x01, 0x01, 0x12, 0x48, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x48, 0x02, 0x52,
	0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x0d,
	0x6b, 0x6e, 0x6f, 0x77, 0x5f, 0x61, 0x62, 0x6f, 0x75, 0x74, 0x5f, 0x75, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48,
	0x03, 0x52, 0x0b, 0x6b, 0x6e, 0x6f, 0x77, 0x41, 0x62, 0x6f, 0x75, 0x74, 0x55, 0x73, 0x88, 0x01,
	0x01, 0x12, 0x2c, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x32, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x2d, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x98, 0x01, 0x03,
	0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x30,
	0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x12, 0x26, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x48, 0x04, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x42,
	0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x42, 0x10, 0x0a,
	0x0e, 0x5f, 0x6b, 0x6e, 0x6f, 0x77, 0x5f, 0x61, 0x62, 0x6f, 0x75, 0x74, 0x5f, 0x75, 0x73, 0x42,
	0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x22, 0x57, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x50, 0x0a, 0x1a, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x11, 0xfa, 0x42,
	0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0xf4, 0x03, 0x22, 0x04, 0x22, 0x02, 0x38, 0x00, 0x52,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0x98, 0x02, 0x0a, 0x1b,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x42, 0x79,
	0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x15,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x52, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x54, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x12, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x54, 0x6f, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x1a, 0x71, 0x0a, 0x17, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x54, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x40, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xee, 0x01, 0x0a, 0x25, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x6e, 0x0a, 0x12, 0x70, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x44, 0x65, 0x66, 0x52, 0x11, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x2d, 0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x22, 0x42, 0x0a, 0x26, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x4c, 0x0a, 0x22, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x91, 0x01, 0x0a, 0x23, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x6a, 0x0a, 0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x11, 0x70, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x41, 0x0a,
	0x17, 0x49, 0x73, 0x4d, 0x6f, 0x65, 0x67, 0x6f, 0x50, 0x61, 0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x22, 0x49, 0x0a, 0x18, 0x49, 0x73, 0x4d, 0x6f, 0x65, 0x67, 0x6f, 0x50, 0x61, 0x79, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d, 0x0a, 0x13,
	0x69, 0x73, 0x5f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x4d, 0x6f, 0x65,
	0x67, 0x6f, 0x50, 0x61, 0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x7e, 0x0a, 0x15, 0x49,
	0x73, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x42, 0x0a, 0x16, 0x49,
	0x73, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0e, 0x69, 0x73, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22,
	0xa1, 0x02, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4d, 0x0a, 0x08, 0x74, 0x61, 0x78, 0x5f, 0x72, 0x75, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65,
	0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x74, 0x61, 0x78,
	0x52, 0x75, 0x6c, 0x65, 0x12, 0x2f, 0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x02, 0x18, 0x32, 0x48, 0x00,
	0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x42, 0x1a, 0x0a, 0x13, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03,
	0xf8, 0x42, 0x01, 0x22, 0x24, 0x0a, 0x12, 0x41, 0x64, 0x64, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xbd, 0x02, 0x0a, 0x14, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x4d, 0x0a, 0x08, 0x74,
	0x61, 0x78, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x78,
	0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x07, 0x74, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x2f, 0x0a, 0x0e, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0c, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x14, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x42, 0x1a, 0x0a,
	0x13, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x31, 0x0a, 0x15, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0xee, 0x01, 0x0a,
	0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2f,
	0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48,
	0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12,
	0x3d, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x31,
	0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x42, 0x1a, 0x0a, 0x13, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x31, 0x0a,
	0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x22, 0xd6, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x0e, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0c, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x14, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x42, 0x1a, 0x0a,
	0x13, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x58, 0x0a, 0x16, 0x47, 0x65, 0x74,
	0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x72,
	0x75, 0x6c, 0x65, 0x22, 0x47, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x46, 0x6f, 0x72, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x41, 0x0a, 0x1e,
	0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x46, 0x6f, 0x72,
	0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22,
	0x9c, 0x01, 0x0a, 0x1e, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4d, 0x69,
	0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x52, 0x0a, 0x0e, 0x6d, 0x69,
	0x67, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x0d, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x21,
	0x0a, 0x1f, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4d, 0x69, 0x67, 0x72,
	0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x1f, 0x0a, 0x1d, 0x4d, 0x61, 0x72, 0x6b, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x41, 0x73, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x80, 0x01, 0x0a, 0x17, 0x49, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x48, 0x0a, 0x18, 0x49, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69,
	0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x22,
	0x51, 0x0a, 0x19, 0x49, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x4d, 0x69,
	0x67, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x0b,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x08, 0x01, 0x10, 0xf4, 0x03, 0x18, 0x01,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x73, 0x22, 0xf6, 0x01, 0x0a, 0x1a, 0x49, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69,
	0x65, 0x73, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x8d, 0x01, 0x0a, 0x18, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69,
	0x65, 0x73, 0x5f, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x54, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73,
	0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x49, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x4d, 0x69, 0x67, 0x72, 0x61,
	0x74, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x15, 0x69, 0x73, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x4d, 0x61,
	0x70, 0x1a, 0x48, 0x0a, 0x1a, 0x49, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73,
	0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x50, 0x0a, 0x1b, 0x47,
	0x65, 0x74, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x10, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x85, 0x01,
	0x0a, 0x1c, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65,
	0x0a, 0x14, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x6f, 0x63,
	0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x11, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0xf7, 0x01, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0e, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x73, 0x0a, 0x14, 0x63, 0x6c,
	0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c,
	0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x11, 0x63, 0x6c,
	0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22,
	0x3b, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e,
	0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0xa9, 0x01, 0x0a,
	0x22, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x42, 0x79,
	0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49,
	0x64, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xc7, 0x01, 0x0a, 0x23, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x42, 0x79, 0x45, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x48, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x12, 0x47, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x42, 0x0a, 0x21, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x4e, 0x0a, 0x22, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x10,
	0x69, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x6c, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x46, 0x69, 0x6c, 0x6c, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9a, 0x03, 0x0a, 0x1c, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x70,
	0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x50, 0x65, 0x72, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x12, 0x35, 0x0a, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x76, 0x61, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x01, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x56, 0x61, 0x6e, 0x73, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x09, 0x6d, 0x6f, 0x76, 0x65,
	0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x18, 0x32, 0x52, 0x08, 0x6d, 0x6f, 0x76, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x28,
	0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x0a, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x38, 0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x02, 0x52, 0x0f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x88,
	0x01, 0x01, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x76, 0x61, 0x6e, 0x73, 0x42, 0x14, 0x0a,
	0x12, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x6f, 0x74,
	0x68, 0x65, 0x72, 0x22, 0x39, 0x0a, 0x1d, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x63,
	0x0a, 0x12, 0x53, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x03,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01,
	0x0d, 0x08, 0x01, 0x10, 0xff, 0x01, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03,
	0x69, 0x64, 0x73, 0x22, 0x15, 0x0a, 0x13, 0x53, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xe1, 0x02, 0x0a, 0x12, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2b, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2b,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x48, 0x04, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x22, 0xa3,
	0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73,
	0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x88, 0x04, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x01, 0x52, 0x0c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x71, 0x75, 0x61, 0x72, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x35, 0x0a, 0x14, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x70, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x02, 0x52, 0x12, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65,
	0x52, 0x65, 0x61, 0x64, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x03, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x48, 0x04, 0x52, 0x0b,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x1c,
	0x0a, 0x07, 0x76, 0x61, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x48,
	0x05, 0x52, 0x06, 0x76, 0x61, 0x6e, 0x4e, 0x75, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x0d,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x06, 0x52, 0x0c,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x22, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x07, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65, 0x42, 0x17,
	0x0a, 0x15, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65,
	0x5f, 0x72, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x76, 0x61, 0x6e, 0x5f,
	0x6e, 0x75, 0x6d, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22,
	0x2f, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x5a, 0x0a, 0x24, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x11, 0xfa,
	0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0xf4, 0x03, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0xc5, 0x02, 0x0a,
	0x25, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x94, 0x01, 0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x61,
	0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x5e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x14, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x61, 0x70, 0x1a, 0x84, 0x01,
	0x0a, 0x19, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x51, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x91, 0x04, 0x0a, 0x25, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46, 0x72, 0x6f, 0x6d, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x48, 0x75, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x11, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x60, 0x01, 0x48, 0x00,
	0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x0a, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x01, 0x52, 0x09, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x09, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x02, 0x52, 0x08, 0x6c, 0x61, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x48, 0x03,
	0x52, 0x0b, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x88, 0x01, 0x01,
	0x12, 0x1c, 0x0a, 0x07, 0x76, 0x61, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x48, 0x04, 0x52, 0x06, 0x76, 0x61, 0x6e, 0x4e, 0x75, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x31,
	0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x48,
	0x05, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x88, 0x01,
	0x01, 0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a,
	0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x42, 0x0a, 0x0a, 0x08, 0x5f,
	0x76, 0x61, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x32, 0xf5, 0x1a, 0x0a, 0x0e, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7a, 0x0a, 0x0d, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c, 0x01, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x12,
	0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x42, 0x79,
	0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x10, 0x49, 0x73, 0x4d, 0x6f, 0x65,
	0x67, 0x6f, 0x50, 0x61, 0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x73, 0x4d, 0x6f,
	0x65, 0x67, 0x6f, 0x50, 0x61, 0x79, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x49, 0x73, 0x4d, 0x6f, 0x65, 0x67, 0x6f, 0x50, 0x61, 0x79, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xad, 0x01, 0x0a,
	0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12,
	0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa4, 0x01, 0x0a,
	0x1b, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x41, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x7d, 0x0a, 0x0e, 0x49, 0x73, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x73, 0x52, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x73, 0x52, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x71, 0x0a, 0x0a, 0x41, 0x64, 0x64, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x64, 0x64, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7a, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x78,
	0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x7a, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x78, 0x52, 0x75,
	0x6c, 0x65, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61,
	0x78, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7d, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x95, 0x01, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x46, 0x6f,
	0x72, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x46, 0x6f, 0x72, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x46, 0x6f, 0x72, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x10, 0x49, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x73, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4d, 0x69, 0x67, 0x72, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8f, 0x01, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75,
	0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x98, 0x01, 0x0a,
	0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75,
	0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c,
	0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x12, 0x49, 0x73, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x12, 0x38,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49,
	0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x69, 0x65, 0x73, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x98, 0x01, 0x0a, 0x17, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa4,
	0x01, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73,
	0x42, 0x79, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x41,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x42, 0x79, 0x45, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x42,
	0x79, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa1, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x12, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x98, 0x01, 0x0a, 0x19, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x53, 0x61, 0x76, 0x65, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x76, 0x0a, 0x0b, 0x53, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x76, 0x0a, 0x0b,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0xaa, 0x01, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x7c, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x9e, 0x01, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x46, 0x72, 0x6f, 0x6d, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x48,
	0x75, 0x62, 0x12, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x46, 0x72, 0x6f, 0x6d, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x48, 0x75,
	0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x42, 0x8f, 0x01, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x64, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x76, 0x63,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_organization_v1_company_service_proto_rawDescOnce sync.Once
	file_moego_service_organization_v1_company_service_proto_rawDescData = file_moego_service_organization_v1_company_service_proto_rawDesc
)

func file_moego_service_organization_v1_company_service_proto_rawDescGZIP() []byte {
	file_moego_service_organization_v1_company_service_proto_rawDescOnce.Do(func() {
		file_moego_service_organization_v1_company_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_organization_v1_company_service_proto_rawDescData)
	})
	return file_moego_service_organization_v1_company_service_proto_rawDescData
}

var file_moego_service_organization_v1_company_service_proto_msgTypes = make([]protoimpl.MessageInfo, 51)
var file_moego_service_organization_v1_company_service_proto_goTypes = []interface{}{
	(*CreateCompanyRequest)(nil),                   // 0: moego.service.organization.v1.CreateCompanyRequest
	(*CreateCompanyResponse)(nil),                  // 1: moego.service.organization.v1.CreateCompanyResponse
	(*QueryCompaniesByIdsRequest)(nil),             // 2: moego.service.organization.v1.QueryCompaniesByIdsRequest
	(*QueryCompaniesByIdsResponse)(nil),            // 3: moego.service.organization.v1.QueryCompaniesByIdsResponse
	(*UpdateCompanyPreferenceSettingRequest)(nil),  // 4: moego.service.organization.v1.UpdateCompanyPreferenceSettingRequest
	(*UpdateCompanyPreferenceSettingResponse)(nil), // 5: moego.service.organization.v1.UpdateCompanyPreferenceSettingResponse
	(*GetCompanyPreferenceSettingRequest)(nil),     // 6: moego.service.organization.v1.GetCompanyPreferenceSettingRequest
	(*GetCompanyPreferenceSettingResponse)(nil),    // 7: moego.service.organization.v1.GetCompanyPreferenceSettingResponse
	(*IsMoegoPayEnableRequest)(nil),                // 8: moego.service.organization.v1.IsMoegoPayEnableRequest
	(*IsMoegoPayEnableResponse)(nil),               // 9: moego.service.organization.v1.IsMoegoPayEnableResponse
	(*IsRetailEnableRequest)(nil),                  // 10: moego.service.organization.v1.IsRetailEnableRequest
	(*IsRetailEnableResponse)(nil),                 // 11: moego.service.organization.v1.IsRetailEnableResponse
	(*AddTaxRuleRequest)(nil),                      // 12: moego.service.organization.v1.AddTaxRuleRequest
	(*AddTaxRuleResponse)(nil),                     // 13: moego.service.organization.v1.AddTaxRuleResponse
	(*UpdateTaxRuleRequest)(nil),                   // 14: moego.service.organization.v1.UpdateTaxRuleRequest
	(*UpdateTaxRuleResponse)(nil),                  // 15: moego.service.organization.v1.UpdateTaxRuleResponse
	(*DeleteTaxRuleRequest)(nil),                   // 16: moego.service.organization.v1.DeleteTaxRuleRequest
	(*DeleteTaxRuleResponse)(nil),                  // 17: moego.service.organization.v1.DeleteTaxRuleResponse
	(*GetTaxRuleListRequest)(nil),                  // 18: moego.service.organization.v1.GetTaxRuleListRequest
	(*GetTaxRuleListResponse)(nil),                 // 19: moego.service.organization.v1.GetTaxRuleListResponse
	(*GetBusinessIdForMobileRequest)(nil),          // 20: moego.service.organization.v1.GetBusinessIdForMobileRequest
	(*GetBusinessIdForMobileResponse)(nil),         // 21: moego.service.organization.v1.GetBusinessIdForMobileResponse
	(*SetCompanyMigrateStatusRequest)(nil),         // 22: moego.service.organization.v1.SetCompanyMigrateStatusRequest
	(*SetCompanyMigrateStatusResponse)(nil),        // 23: moego.service.organization.v1.SetCompanyMigrateStatusResponse
	(*MarkCompanyAsMigratedResponse)(nil),          // 24: moego.service.organization.v1.MarkCompanyAsMigratedResponse
	(*IsCompanyMigrateRequest)(nil),                // 25: moego.service.organization.v1.IsCompanyMigrateRequest
	(*IsCompanyMigrateResponse)(nil),               // 26: moego.service.organization.v1.IsCompanyMigrateResponse
	(*IsCompaniesMigrateRequest)(nil),              // 27: moego.service.organization.v1.IsCompaniesMigrateRequest
	(*IsCompaniesMigrateResponse)(nil),             // 28: moego.service.organization.v1.IsCompaniesMigrateResponse
	(*GetClockInOutSettingRequest)(nil),            // 29: moego.service.organization.v1.GetClockInOutSettingRequest
	(*GetClockInOutSettingResponse)(nil),           // 30: moego.service.organization.v1.GetClockInOutSettingResponse
	(*UpdateClockInOutSettingRequest)(nil),         // 31: moego.service.organization.v1.UpdateClockInOutSettingRequest
	(*UpdateClockInOutSettingResponse)(nil),        // 32: moego.service.organization.v1.UpdateClockInOutSettingResponse
	(*ListCompaniesByEnterpriseIdRequest)(nil),     // 33: moego.service.organization.v1.ListCompaniesByEnterpriseIdRequest
	(*ListCompaniesByEnterpriseIdResponse)(nil),    // 34: moego.service.organization.v1.ListCompaniesByEnterpriseIdResponse
	(*CompanyQuestionRecordQueryRequest)(nil),      // 35: moego.service.organization.v1.CompanyQuestionRecordQueryRequest
	(*CompanyQuestionRecordQueryResponse)(nil),     // 36: moego.service.organization.v1.CompanyQuestionRecordQueryResponse
	(*CompanyQuestionRecordRequest)(nil),           // 37: moego.service.organization.v1.CompanyQuestionRecordRequest
	(*CompanyQuestionRecordResponse)(nil),          // 38: moego.service.organization.v1.CompanyQuestionRecordResponse
	(*SortCompanyRequest)(nil),                     // 39: moego.service.organization.v1.SortCompanyRequest
	(*SortCompanyResponse)(nil),                    // 40: moego.service.organization.v1.SortCompanyResponse
	(*ListCompanyRequest)(nil),                     // 41: moego.service.organization.v1.ListCompanyRequest
	(*ListCompanyResponse)(nil),                    // 42: moego.service.organization.v1.ListCompanyResponse
	(*UpdateCompanyRequest)(nil),                   // 43: moego.service.organization.v1.UpdateCompanyRequest
	(*UpdateCompanyResponse)(nil),                  // 44: moego.service.organization.v1.UpdateCompanyResponse
	(*ListCompanyPreferenceSettingsRequest)(nil),   // 45: moego.service.organization.v1.ListCompanyPreferenceSettingsRequest
	(*ListCompanyPreferenceSettingsResponse)(nil),  // 46: moego.service.organization.v1.ListCompanyPreferenceSettingsResponse
	(*CreateCompanyFromEnterpriseHubRequest)(nil),  // 47: moego.service.organization.v1.CreateCompanyFromEnterpriseHubRequest
	nil,                          // 48: moego.service.organization.v1.QueryCompaniesByIdsResponse.CompanyIdToCompanyEntry
	nil,                          // 49: moego.service.organization.v1.IsCompaniesMigrateResponse.IsCompaniesMigrateMapEntry
	nil,                          // 50: moego.service.organization.v1.ListCompanyPreferenceSettingsResponse.CompanyPreferenceMapEntry
	(*v1.CreateLocationDef)(nil), // 51: moego.models.organization.v1.CreateLocationDef
	(v1.SourceType)(0),           // 52: moego.models.organization.v1.SourceType
	(*v1.CountryDef)(nil),        // 53: moego.models.organization.v1.CountryDef
	(*v1.TimeZone)(nil),          // 54: moego.models.organization.v1.TimeZone
	(*v1.UpdateCompanyPreferenceSettingDef)(nil), // 55: moego.models.organization.v1.UpdateCompanyPreferenceSettingDef
	(*v1.CompanyPreferenceSettingModel)(nil),     // 56: moego.models.organization.v1.CompanyPreferenceSettingModel
	(*v1.TaxRuleDef)(nil),                        // 57: moego.models.organization.v1.TaxRuleDef
	(*v1.TaxRuleModel)(nil),                      // 58: moego.models.organization.v1.TaxRuleModel
	(v1.MigrateStatus)(0),                        // 59: moego.models.organization.v1.MigrateStatus
	(*v1.ClockInOutSettingModel)(nil),            // 60: moego.models.organization.v1.ClockInOutSettingModel
	(*v1.UpdateClockInOutSettingDef)(nil),        // 61: moego.models.organization.v1.UpdateClockInOutSettingDef
	(*v2.PaginationRequest)(nil),                 // 62: moego.utils.v2.PaginationRequest
	(*v1.CompanyModel)(nil),                      // 63: moego.models.organization.v1.CompanyModel
	(*v2.PaginationResponse)(nil),                // 64: moego.utils.v2.PaginationResponse
}
var file_moego_service_organization_v1_company_service_proto_depIdxs = []int32{
	51, // 0: moego.service.organization.v1.CreateCompanyRequest.location:type_name -> moego.models.organization.v1.CreateLocationDef
	52, // 1: moego.service.organization.v1.CreateCompanyRequest.source:type_name -> moego.models.organization.v1.SourceType
	53, // 2: moego.service.organization.v1.CreateCompanyRequest.country:type_name -> moego.models.organization.v1.CountryDef
	54, // 3: moego.service.organization.v1.CreateCompanyRequest.time_zone:type_name -> moego.models.organization.v1.TimeZone
	48, // 4: moego.service.organization.v1.QueryCompaniesByIdsResponse.company_id_to_company:type_name -> moego.service.organization.v1.QueryCompaniesByIdsResponse.CompanyIdToCompanyEntry
	55, // 5: moego.service.organization.v1.UpdateCompanyPreferenceSettingRequest.preference_setting:type_name -> moego.models.organization.v1.UpdateCompanyPreferenceSettingDef
	56, // 6: moego.service.organization.v1.GetCompanyPreferenceSettingResponse.preference_setting:type_name -> moego.models.organization.v1.CompanyPreferenceSettingModel
	57, // 7: moego.service.organization.v1.AddTaxRuleRequest.tax_rule:type_name -> moego.models.organization.v1.TaxRuleDef
	57, // 8: moego.service.organization.v1.UpdateTaxRuleRequest.tax_rule:type_name -> moego.models.organization.v1.TaxRuleDef
	58, // 9: moego.service.organization.v1.GetTaxRuleListResponse.rule:type_name -> moego.models.organization.v1.TaxRuleModel
	59, // 10: moego.service.organization.v1.SetCompanyMigrateStatusRequest.migrate_status:type_name -> moego.models.organization.v1.MigrateStatus
	49, // 11: moego.service.organization.v1.IsCompaniesMigrateResponse.is_companies_migrate_map:type_name -> moego.service.organization.v1.IsCompaniesMigrateResponse.IsCompaniesMigrateMapEntry
	60, // 12: moego.service.organization.v1.GetClockInOutSettingResponse.clock_in_out_setting:type_name -> moego.models.organization.v1.ClockInOutSettingModel
	61, // 13: moego.service.organization.v1.UpdateClockInOutSettingRequest.clock_in_out_setting:type_name -> moego.models.organization.v1.UpdateClockInOutSettingDef
	62, // 14: moego.service.organization.v1.ListCompaniesByEnterpriseIdRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	63, // 15: moego.service.organization.v1.ListCompaniesByEnterpriseIdResponse.companies:type_name -> moego.models.organization.v1.CompanyModel
	64, // 16: moego.service.organization.v1.ListCompaniesByEnterpriseIdResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	62, // 17: moego.service.organization.v1.ListCompanyRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	63, // 18: moego.service.organization.v1.ListCompanyResponse.companies:type_name -> moego.models.organization.v1.CompanyModel
	64, // 19: moego.service.organization.v1.ListCompanyResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	50, // 20: moego.service.organization.v1.ListCompanyPreferenceSettingsResponse.company_preference_map:type_name -> moego.service.organization.v1.ListCompanyPreferenceSettingsResponse.CompanyPreferenceMapEntry
	63, // 21: moego.service.organization.v1.QueryCompaniesByIdsResponse.CompanyIdToCompanyEntry.value:type_name -> moego.models.organization.v1.CompanyModel
	56, // 22: moego.service.organization.v1.ListCompanyPreferenceSettingsResponse.CompanyPreferenceMapEntry.value:type_name -> moego.models.organization.v1.CompanyPreferenceSettingModel
	0,  // 23: moego.service.organization.v1.CompanyService.CreateCompany:input_type -> moego.service.organization.v1.CreateCompanyRequest
	2,  // 24: moego.service.organization.v1.CompanyService.QueryCompaniesByIds:input_type -> moego.service.organization.v1.QueryCompaniesByIdsRequest
	8,  // 25: moego.service.organization.v1.CompanyService.IsMoegoPayEnable:input_type -> moego.service.organization.v1.IsMoegoPayEnableRequest
	4,  // 26: moego.service.organization.v1.CompanyService.UpdateCompanyPreferenceSetting:input_type -> moego.service.organization.v1.UpdateCompanyPreferenceSettingRequest
	6,  // 27: moego.service.organization.v1.CompanyService.GetCompanyPreferenceSetting:input_type -> moego.service.organization.v1.GetCompanyPreferenceSettingRequest
	10, // 28: moego.service.organization.v1.CompanyService.IsRetailEnable:input_type -> moego.service.organization.v1.IsRetailEnableRequest
	12, // 29: moego.service.organization.v1.CompanyService.AddTaxRule:input_type -> moego.service.organization.v1.AddTaxRuleRequest
	14, // 30: moego.service.organization.v1.CompanyService.UpdateTaxRule:input_type -> moego.service.organization.v1.UpdateTaxRuleRequest
	16, // 31: moego.service.organization.v1.CompanyService.DeleteTaxRule:input_type -> moego.service.organization.v1.DeleteTaxRuleRequest
	18, // 32: moego.service.organization.v1.CompanyService.GetTaxRuleList:input_type -> moego.service.organization.v1.GetTaxRuleListRequest
	20, // 33: moego.service.organization.v1.CompanyService.GetBusinessIdForMobile:input_type -> moego.service.organization.v1.GetBusinessIdForMobileRequest
	25, // 34: moego.service.organization.v1.CompanyService.IsCompanyMigrate:input_type -> moego.service.organization.v1.IsCompanyMigrateRequest
	29, // 35: moego.service.organization.v1.CompanyService.GetClockInOutSetting:input_type -> moego.service.organization.v1.GetClockInOutSettingRequest
	31, // 36: moego.service.organization.v1.CompanyService.UpdateClockInOutSetting:input_type -> moego.service.organization.v1.UpdateClockInOutSettingRequest
	27, // 37: moego.service.organization.v1.CompanyService.IsCompaniesMigrate:input_type -> moego.service.organization.v1.IsCompaniesMigrateRequest
	22, // 38: moego.service.organization.v1.CompanyService.SetCompanyMigrateStatus:input_type -> moego.service.organization.v1.SetCompanyMigrateStatusRequest
	33, // 39: moego.service.organization.v1.CompanyService.ListCompaniesByEnterpriseId:input_type -> moego.service.organization.v1.ListCompaniesByEnterpriseIdRequest
	35, // 40: moego.service.organization.v1.CompanyService.GetCompanyQuestionRecord:input_type -> moego.service.organization.v1.CompanyQuestionRecordQueryRequest
	37, // 41: moego.service.organization.v1.CompanyService.CompanyQuestionRecordSave:input_type -> moego.service.organization.v1.CompanyQuestionRecordRequest
	39, // 42: moego.service.organization.v1.CompanyService.SortCompany:input_type -> moego.service.organization.v1.SortCompanyRequest
	41, // 43: moego.service.organization.v1.CompanyService.ListCompany:input_type -> moego.service.organization.v1.ListCompanyRequest
	45, // 44: moego.service.organization.v1.CompanyService.ListCompanyPreferenceSettings:input_type -> moego.service.organization.v1.ListCompanyPreferenceSettingsRequest
	43, // 45: moego.service.organization.v1.CompanyService.UpdateCompany:input_type -> moego.service.organization.v1.UpdateCompanyRequest
	47, // 46: moego.service.organization.v1.CompanyService.CreateCompanyFromEnterpriseHub:input_type -> moego.service.organization.v1.CreateCompanyFromEnterpriseHubRequest
	1,  // 47: moego.service.organization.v1.CompanyService.CreateCompany:output_type -> moego.service.organization.v1.CreateCompanyResponse
	3,  // 48: moego.service.organization.v1.CompanyService.QueryCompaniesByIds:output_type -> moego.service.organization.v1.QueryCompaniesByIdsResponse
	9,  // 49: moego.service.organization.v1.CompanyService.IsMoegoPayEnable:output_type -> moego.service.organization.v1.IsMoegoPayEnableResponse
	5,  // 50: moego.service.organization.v1.CompanyService.UpdateCompanyPreferenceSetting:output_type -> moego.service.organization.v1.UpdateCompanyPreferenceSettingResponse
	7,  // 51: moego.service.organization.v1.CompanyService.GetCompanyPreferenceSetting:output_type -> moego.service.organization.v1.GetCompanyPreferenceSettingResponse
	11, // 52: moego.service.organization.v1.CompanyService.IsRetailEnable:output_type -> moego.service.organization.v1.IsRetailEnableResponse
	13, // 53: moego.service.organization.v1.CompanyService.AddTaxRule:output_type -> moego.service.organization.v1.AddTaxRuleResponse
	15, // 54: moego.service.organization.v1.CompanyService.UpdateTaxRule:output_type -> moego.service.organization.v1.UpdateTaxRuleResponse
	17, // 55: moego.service.organization.v1.CompanyService.DeleteTaxRule:output_type -> moego.service.organization.v1.DeleteTaxRuleResponse
	19, // 56: moego.service.organization.v1.CompanyService.GetTaxRuleList:output_type -> moego.service.organization.v1.GetTaxRuleListResponse
	21, // 57: moego.service.organization.v1.CompanyService.GetBusinessIdForMobile:output_type -> moego.service.organization.v1.GetBusinessIdForMobileResponse
	26, // 58: moego.service.organization.v1.CompanyService.IsCompanyMigrate:output_type -> moego.service.organization.v1.IsCompanyMigrateResponse
	30, // 59: moego.service.organization.v1.CompanyService.GetClockInOutSetting:output_type -> moego.service.organization.v1.GetClockInOutSettingResponse
	32, // 60: moego.service.organization.v1.CompanyService.UpdateClockInOutSetting:output_type -> moego.service.organization.v1.UpdateClockInOutSettingResponse
	28, // 61: moego.service.organization.v1.CompanyService.IsCompaniesMigrate:output_type -> moego.service.organization.v1.IsCompaniesMigrateResponse
	23, // 62: moego.service.organization.v1.CompanyService.SetCompanyMigrateStatus:output_type -> moego.service.organization.v1.SetCompanyMigrateStatusResponse
	34, // 63: moego.service.organization.v1.CompanyService.ListCompaniesByEnterpriseId:output_type -> moego.service.organization.v1.ListCompaniesByEnterpriseIdResponse
	36, // 64: moego.service.organization.v1.CompanyService.GetCompanyQuestionRecord:output_type -> moego.service.organization.v1.CompanyQuestionRecordQueryResponse
	38, // 65: moego.service.organization.v1.CompanyService.CompanyQuestionRecordSave:output_type -> moego.service.organization.v1.CompanyQuestionRecordResponse
	40, // 66: moego.service.organization.v1.CompanyService.SortCompany:output_type -> moego.service.organization.v1.SortCompanyResponse
	42, // 67: moego.service.organization.v1.CompanyService.ListCompany:output_type -> moego.service.organization.v1.ListCompanyResponse
	46, // 68: moego.service.organization.v1.CompanyService.ListCompanyPreferenceSettings:output_type -> moego.service.organization.v1.ListCompanyPreferenceSettingsResponse
	44, // 69: moego.service.organization.v1.CompanyService.UpdateCompany:output_type -> moego.service.organization.v1.UpdateCompanyResponse
	1,  // 70: moego.service.organization.v1.CompanyService.CreateCompanyFromEnterpriseHub:output_type -> moego.service.organization.v1.CreateCompanyResponse
	47, // [47:71] is the sub-list for method output_type
	23, // [23:47] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_moego_service_organization_v1_company_service_proto_init() }
func file_moego_service_organization_v1_company_service_proto_init() {
	if File_moego_service_organization_v1_company_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_organization_v1_company_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCompanyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCompanyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCompaniesByIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCompaniesByIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCompanyPreferenceSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCompanyPreferenceSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompanyPreferenceSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompanyPreferenceSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsMoegoPayEnableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsMoegoPayEnableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsRetailEnableRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsRetailEnableResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddTaxRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddTaxRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTaxRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTaxRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteTaxRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteTaxRuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTaxRuleListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTaxRuleListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessIdForMobileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessIdForMobileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetCompanyMigrateStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetCompanyMigrateStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkCompanyAsMigratedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsCompanyMigrateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsCompanyMigrateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsCompaniesMigrateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsCompaniesMigrateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClockInOutSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClockInOutSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateClockInOutSettingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateClockInOutSettingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCompaniesByEnterpriseIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCompaniesByEnterpriseIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyQuestionRecordQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyQuestionRecordQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyQuestionRecordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyQuestionRecordResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortCompanyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortCompanyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCompanyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCompanyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCompanyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCompanyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCompanyPreferenceSettingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCompanyPreferenceSettingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_company_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCompanyFromEnterpriseHubRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_organization_v1_company_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_company_service_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_company_service_proto_msgTypes[12].OneofWrappers = []interface{}{
		(*AddTaxRuleRequest_TokenStaffId)(nil),
		(*AddTaxRuleRequest_InternalOperatorId)(nil),
	}
	file_moego_service_organization_v1_company_service_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*UpdateTaxRuleRequest_TokenStaffId)(nil),
		(*UpdateTaxRuleRequest_InternalOperatorId)(nil),
	}
	file_moego_service_organization_v1_company_service_proto_msgTypes[16].OneofWrappers = []interface{}{
		(*DeleteTaxRuleRequest_TokenStaffId)(nil),
		(*DeleteTaxRuleRequest_InternalOperatorId)(nil),
	}
	file_moego_service_organization_v1_company_service_proto_msgTypes[18].OneofWrappers = []interface{}{
		(*GetTaxRuleListRequest_TokenStaffId)(nil),
		(*GetTaxRuleListRequest_InternalOperatorId)(nil),
	}
	file_moego_service_organization_v1_company_service_proto_msgTypes[25].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_company_service_proto_msgTypes[33].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_company_service_proto_msgTypes[34].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_company_service_proto_msgTypes[37].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_company_service_proto_msgTypes[41].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_company_service_proto_msgTypes[43].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_company_service_proto_msgTypes[47].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_organization_v1_company_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   51,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_organization_v1_company_service_proto_goTypes,
		DependencyIndexes: file_moego_service_organization_v1_company_service_proto_depIdxs,
		MessageInfos:      file_moego_service_organization_v1_company_service_proto_msgTypes,
	}.Build()
	File_moego_service_organization_v1_company_service_proto = out.File
	file_moego_service_organization_v1_company_service_proto_rawDesc = nil
	file_moego_service_organization_v1_company_service_proto_goTypes = nil
	file_moego_service_organization_v1_company_service_proto_depIdxs = nil
}
