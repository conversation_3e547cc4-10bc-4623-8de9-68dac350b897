// @since 2024-02-20 16:14:40
// <AUTHOR> <zhang<PERSON>@moego.pet>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/organization/v1/migrate_service.proto

package organizationsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get company migrate status request
type GetMigrateStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// identity id, company id or business id
	//
	// Types that are assignable to Identifier:
	//
	//	*GetMigrateStatusRequest_CompanyId
	//	*GetMigrateStatusRequest_BusinessId
	Identifier isGetMigrateStatusRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *GetMigrateStatusRequest) Reset() {
	*x = GetMigrateStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_migrate_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMigrateStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMigrateStatusRequest) ProtoMessage() {}

func (x *GetMigrateStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_migrate_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMigrateStatusRequest.ProtoReflect.Descriptor instead.
func (*GetMigrateStatusRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_migrate_service_proto_rawDescGZIP(), []int{0}
}

func (m *GetMigrateStatusRequest) GetIdentifier() isGetMigrateStatusRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetMigrateStatusRequest) GetCompanyId() int64 {
	if x, ok := x.GetIdentifier().(*GetMigrateStatusRequest_CompanyId); ok {
		return x.CompanyId
	}
	return 0
}

func (x *GetMigrateStatusRequest) GetBusinessId() int64 {
	if x, ok := x.GetIdentifier().(*GetMigrateStatusRequest_BusinessId); ok {
		return x.BusinessId
	}
	return 0
}

type isGetMigrateStatusRequest_Identifier interface {
	isGetMigrateStatusRequest_Identifier()
}

type GetMigrateStatusRequest_CompanyId struct {
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof"`
}

type GetMigrateStatusRequest_BusinessId struct {
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof"`
}

func (*GetMigrateStatusRequest_CompanyId) isGetMigrateStatusRequest_Identifier() {}

func (*GetMigrateStatusRequest_BusinessId) isGetMigrateStatusRequest_Identifier() {}

// get company migrate status response
type GetMigrateStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// migrate status
	Status v1.MigrateStatus `protobuf:"varint,1,opt,name=status,proto3,enum=moego.models.organization.v1.MigrateStatus" json:"status,omitempty"`
}

func (x *GetMigrateStatusResponse) Reset() {
	*x = GetMigrateStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_migrate_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMigrateStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMigrateStatusResponse) ProtoMessage() {}

func (x *GetMigrateStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_migrate_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMigrateStatusResponse.ProtoReflect.Descriptor instead.
func (*GetMigrateStatusResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_migrate_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetMigrateStatusResponse) GetStatus() v1.MigrateStatus {
	if x != nil {
		return x.Status
	}
	return v1.MigrateStatus(0)
}

var File_moego_service_organization_v1_migrate_service_proto protoreflect.FileDescriptor

var file_moego_service_organization_v1_migrate_service_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x2f, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x70, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x42, 0x11,
	0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42,
	0x01, 0x22, 0x5f, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x69, 0x67,
	0x72, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x32, 0x96, 0x01, 0x0a, 0x0e, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4d, 0x69, 0x67,
	0x72, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x69,
	0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8f, 0x01, 0x0a, 0x25,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x64, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_organization_v1_migrate_service_proto_rawDescOnce sync.Once
	file_moego_service_organization_v1_migrate_service_proto_rawDescData = file_moego_service_organization_v1_migrate_service_proto_rawDesc
)

func file_moego_service_organization_v1_migrate_service_proto_rawDescGZIP() []byte {
	file_moego_service_organization_v1_migrate_service_proto_rawDescOnce.Do(func() {
		file_moego_service_organization_v1_migrate_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_organization_v1_migrate_service_proto_rawDescData)
	})
	return file_moego_service_organization_v1_migrate_service_proto_rawDescData
}

var file_moego_service_organization_v1_migrate_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_service_organization_v1_migrate_service_proto_goTypes = []interface{}{
	(*GetMigrateStatusRequest)(nil),  // 0: moego.service.organization.v1.GetMigrateStatusRequest
	(*GetMigrateStatusResponse)(nil), // 1: moego.service.organization.v1.GetMigrateStatusResponse
	(v1.MigrateStatus)(0),            // 2: moego.models.organization.v1.MigrateStatus
}
var file_moego_service_organization_v1_migrate_service_proto_depIdxs = []int32{
	2, // 0: moego.service.organization.v1.GetMigrateStatusResponse.status:type_name -> moego.models.organization.v1.MigrateStatus
	0, // 1: moego.service.organization.v1.MigrateService.GetMigrateStatus:input_type -> moego.service.organization.v1.GetMigrateStatusRequest
	1, // 2: moego.service.organization.v1.MigrateService.GetMigrateStatus:output_type -> moego.service.organization.v1.GetMigrateStatusResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_service_organization_v1_migrate_service_proto_init() }
func file_moego_service_organization_v1_migrate_service_proto_init() {
	if File_moego_service_organization_v1_migrate_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_organization_v1_migrate_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMigrateStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_migrate_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMigrateStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_organization_v1_migrate_service_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*GetMigrateStatusRequest_CompanyId)(nil),
		(*GetMigrateStatusRequest_BusinessId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_organization_v1_migrate_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_organization_v1_migrate_service_proto_goTypes,
		DependencyIndexes: file_moego_service_organization_v1_migrate_service_proto_depIdxs,
		MessageInfos:      file_moego_service_organization_v1_migrate_service_proto_msgTypes,
	}.Build()
	File_moego_service_organization_v1_migrate_service_proto = out.File
	file_moego_service_organization_v1_migrate_service_proto_rawDesc = nil
	file_moego_service_organization_v1_migrate_service_proto_goTypes = nil
	file_moego_service_organization_v1_migrate_service_proto_depIdxs = nil
}
