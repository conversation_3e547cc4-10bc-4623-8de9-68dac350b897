// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/organization/v1/business_service.proto

package organizationsvcpb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get company id request
type GetCompanyIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetCompanyIdRequest) Reset() {
	*x = GetCompanyIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompanyIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompanyIdRequest) ProtoMessage() {}

func (x *GetCompanyIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompanyIdRequest.ProtoReflect.Descriptor instead.
func (*GetCompanyIdRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetCompanyIdRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// get company id response
type GetCompanyIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetCompanyIdResponse) Reset() {
	*x = GetCompanyIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompanyIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompanyIdResponse) ProtoMessage() {}

func (x *GetCompanyIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompanyIdResponse.ProtoReflect.Descriptor instead.
func (*GetCompanyIdResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetCompanyIdResponse) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// batch get company id request
type BatchGetCompanyIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id list
	BusinessIds []int64 `protobuf:"varint,1,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *BatchGetCompanyIdRequest) Reset() {
	*x = BatchGetCompanyIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetCompanyIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetCompanyIdRequest) ProtoMessage() {}

func (x *BatchGetCompanyIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetCompanyIdRequest.ProtoReflect.Descriptor instead.
func (*BatchGetCompanyIdRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{2}
}

func (x *BatchGetCompanyIdRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// batch get company id response
type BatchGetCompanyIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id to company id
	BusinessCompanyIdMap map[int64]int64 `protobuf:"bytes,1,rep,name=business_company_id_map,json=businessCompanyIdMap,proto3" json:"business_company_id_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *BatchGetCompanyIdResponse) Reset() {
	*x = BatchGetCompanyIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetCompanyIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetCompanyIdResponse) ProtoMessage() {}

func (x *BatchGetCompanyIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetCompanyIdResponse.ProtoReflect.Descriptor instead.
func (*BatchGetCompanyIdResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{3}
}

func (x *BatchGetCompanyIdResponse) GetBusinessCompanyIdMap() map[int64]int64 {
	if x != nil {
		return x.BusinessCompanyIdMap
	}
	return nil
}

// params to create a location
type CreateLocationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location to create
	Location *v1.CreateLocationDef `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
	// the operator id, allow internal modifier or external modifier
	//
	// Types that are assignable to OperatorIdentifier:
	//
	//	*CreateLocationRequest_TokenStaffId
	//	*CreateLocationRequest_InternalOperatorId
	OperatorIdentifier isCreateLocationRequest_OperatorIdentifier `protobuf_oneof:"operator_identifier"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,4,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
}

func (x *CreateLocationRequest) Reset() {
	*x = CreateLocationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLocationRequest) ProtoMessage() {}

func (x *CreateLocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLocationRequest.ProtoReflect.Descriptor instead.
func (*CreateLocationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateLocationRequest) GetLocation() *v1.CreateLocationDef {
	if x != nil {
		return x.Location
	}
	return nil
}

func (m *CreateLocationRequest) GetOperatorIdentifier() isCreateLocationRequest_OperatorIdentifier {
	if m != nil {
		return m.OperatorIdentifier
	}
	return nil
}

func (x *CreateLocationRequest) GetTokenStaffId() int64 {
	if x, ok := x.GetOperatorIdentifier().(*CreateLocationRequest_TokenStaffId); ok {
		return x.TokenStaffId
	}
	return 0
}

func (x *CreateLocationRequest) GetInternalOperatorId() string {
	if x, ok := x.GetOperatorIdentifier().(*CreateLocationRequest_InternalOperatorId); ok {
		return x.InternalOperatorId
	}
	return ""
}

func (x *CreateLocationRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

type isCreateLocationRequest_OperatorIdentifier interface {
	isCreateLocationRequest_OperatorIdentifier()
}

type CreateLocationRequest_TokenStaffId struct {
	// staff id from token
	TokenStaffId int64 `protobuf:"varint,2,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof"`
}

type CreateLocationRequest_InternalOperatorId struct {
	// the internal operator id
	InternalOperatorId string `protobuf:"bytes,3,opt,name=internal_operator_id,json=internalOperatorId,proto3,oneof"`
}

func (*CreateLocationRequest_TokenStaffId) isCreateLocationRequest_OperatorIdentifier() {}

func (*CreateLocationRequest_InternalOperatorId) isCreateLocationRequest_OperatorIdentifier() {}

// result to create a location
type CreateLocationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateLocationResponse) Reset() {
	*x = CreateLocationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLocationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLocationResponse) ProtoMessage() {}

func (x *CreateLocationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLocationResponse.ProtoReflect.Descriptor instead.
func (*CreateLocationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreateLocationResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// params to update a location
type UpdateLocationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location to update
	Location *v1.UpdateLocationDef `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
	// the operator id, allow internal modifier or external modifier
	//
	// Types that are assignable to OperatorIdentifier:
	//
	//	*UpdateLocationRequest_TokenStaffId
	//	*UpdateLocationRequest_InternalOperatorId
	OperatorIdentifier isUpdateLocationRequest_OperatorIdentifier `protobuf_oneof:"operator_identifier"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,4,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
}

func (x *UpdateLocationRequest) Reset() {
	*x = UpdateLocationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLocationRequest) ProtoMessage() {}

func (x *UpdateLocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLocationRequest.ProtoReflect.Descriptor instead.
func (*UpdateLocationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateLocationRequest) GetLocation() *v1.UpdateLocationDef {
	if x != nil {
		return x.Location
	}
	return nil
}

func (m *UpdateLocationRequest) GetOperatorIdentifier() isUpdateLocationRequest_OperatorIdentifier {
	if m != nil {
		return m.OperatorIdentifier
	}
	return nil
}

func (x *UpdateLocationRequest) GetTokenStaffId() int64 {
	if x, ok := x.GetOperatorIdentifier().(*UpdateLocationRequest_TokenStaffId); ok {
		return x.TokenStaffId
	}
	return 0
}

func (x *UpdateLocationRequest) GetInternalOperatorId() string {
	if x, ok := x.GetOperatorIdentifier().(*UpdateLocationRequest_InternalOperatorId); ok {
		return x.InternalOperatorId
	}
	return ""
}

func (x *UpdateLocationRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

type isUpdateLocationRequest_OperatorIdentifier interface {
	isUpdateLocationRequest_OperatorIdentifier()
}

type UpdateLocationRequest_TokenStaffId struct {
	// staff id from token
	TokenStaffId int64 `protobuf:"varint,2,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof"`
}

type UpdateLocationRequest_InternalOperatorId struct {
	// the internal operator id
	InternalOperatorId string `protobuf:"bytes,3,opt,name=internal_operator_id,json=internalOperatorId,proto3,oneof"`
}

func (*UpdateLocationRequest_TokenStaffId) isUpdateLocationRequest_OperatorIdentifier() {}

func (*UpdateLocationRequest_InternalOperatorId) isUpdateLocationRequest_OperatorIdentifier() {}

// result to update a location
type UpdateLocationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UpdateLocationResponse) Reset() {
	*x = UpdateLocationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLocationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLocationResponse) ProtoMessage() {}

func (x *UpdateLocationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLocationResponse.ProtoReflect.Descriptor instead.
func (*UpdateLocationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateLocationResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// params to get location list, currently only support get all
type GetLocationListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id from token
	TokenCompanyId int64 `protobuf:"varint,1,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
	// staff id, optional
	TokenStaffId *int64 `protobuf:"varint,2,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof" json:"token_staff_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *GetLocationListRequest) Reset() {
	*x = GetLocationListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLocationListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLocationListRequest) ProtoMessage() {}

func (x *GetLocationListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLocationListRequest.ProtoReflect.Descriptor instead.
func (*GetLocationListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetLocationListRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

func (x *GetLocationListRequest) GetTokenStaffId() int64 {
	if x != nil && x.TokenStaffId != nil {
		return *x.TokenStaffId
	}
	return 0
}

func (x *GetLocationListRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// result to get location list
type GetLocationListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location list
	Location []*v1.LocationBriefView `protobuf:"bytes,1,rep,name=location,proto3" json:"location,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *GetLocationListResponse) Reset() {
	*x = GetLocationListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLocationListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLocationListResponse) ProtoMessage() {}

func (x *GetLocationListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLocationListResponse.ProtoReflect.Descriptor instead.
func (*GetLocationListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetLocationListResponse) GetLocation() []*v1.LocationBriefView {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *GetLocationListResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// params to get location detail
type GetLocationDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id from token
	TokenCompanyId *int64 `protobuf:"varint,2,opt,name=token_company_id,json=tokenCompanyId,proto3,oneof" json:"token_company_id,omitempty"`
}

func (x *GetLocationDetailRequest) Reset() {
	*x = GetLocationDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLocationDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLocationDetailRequest) ProtoMessage() {}

func (x *GetLocationDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLocationDetailRequest.ProtoReflect.Descriptor instead.
func (*GetLocationDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetLocationDetailRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetLocationDetailRequest) GetTokenCompanyId() int64 {
	if x != nil && x.TokenCompanyId != nil {
		return *x.TokenCompanyId
	}
	return 0
}

// result to get location detail
type GetLocationDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location detail
	Location *v1.LocationModel `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
}

func (x *GetLocationDetailResponse) Reset() {
	*x = GetLocationDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLocationDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLocationDetailResponse) ProtoMessage() {}

func (x *GetLocationDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLocationDetailResponse.ProtoReflect.Descriptor instead.
func (*GetLocationDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetLocationDetailResponse) GetLocation() *v1.LocationModel {
	if x != nil {
		return x.Location
	}
	return nil
}

// update online preference request
type UpdateOnlinePreferenceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online preference
	OnlinePreference *v1.UpdateOnlinePreferenceDef `protobuf:"bytes,1,opt,name=online_preference,json=onlinePreference,proto3" json:"online_preference,omitempty"`
	// the operator id, allow internal modifier or external modifier
	//
	// Types that are assignable to OperatorIdentifier:
	//
	//	*UpdateOnlinePreferenceRequest_TokenStaffId
	//	*UpdateOnlinePreferenceRequest_InternalOperatorId
	OperatorIdentifier isUpdateOnlinePreferenceRequest_OperatorIdentifier `protobuf_oneof:"operator_identifier"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,4,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
}

func (x *UpdateOnlinePreferenceRequest) Reset() {
	*x = UpdateOnlinePreferenceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOnlinePreferenceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOnlinePreferenceRequest) ProtoMessage() {}

func (x *UpdateOnlinePreferenceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOnlinePreferenceRequest.ProtoReflect.Descriptor instead.
func (*UpdateOnlinePreferenceRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateOnlinePreferenceRequest) GetOnlinePreference() *v1.UpdateOnlinePreferenceDef {
	if x != nil {
		return x.OnlinePreference
	}
	return nil
}

func (m *UpdateOnlinePreferenceRequest) GetOperatorIdentifier() isUpdateOnlinePreferenceRequest_OperatorIdentifier {
	if m != nil {
		return m.OperatorIdentifier
	}
	return nil
}

func (x *UpdateOnlinePreferenceRequest) GetTokenStaffId() int64 {
	if x, ok := x.GetOperatorIdentifier().(*UpdateOnlinePreferenceRequest_TokenStaffId); ok {
		return x.TokenStaffId
	}
	return 0
}

func (x *UpdateOnlinePreferenceRequest) GetInternalOperatorId() string {
	if x, ok := x.GetOperatorIdentifier().(*UpdateOnlinePreferenceRequest_InternalOperatorId); ok {
		return x.InternalOperatorId
	}
	return ""
}

func (x *UpdateOnlinePreferenceRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

type isUpdateOnlinePreferenceRequest_OperatorIdentifier interface {
	isUpdateOnlinePreferenceRequest_OperatorIdentifier()
}

type UpdateOnlinePreferenceRequest_TokenStaffId struct {
	// staff id from token
	TokenStaffId int64 `protobuf:"varint,2,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof"`
}

type UpdateOnlinePreferenceRequest_InternalOperatorId struct {
	// the internal operator id
	InternalOperatorId string `protobuf:"bytes,3,opt,name=internal_operator_id,json=internalOperatorId,proto3,oneof"`
}

func (*UpdateOnlinePreferenceRequest_TokenStaffId) isUpdateOnlinePreferenceRequest_OperatorIdentifier() {
}

func (*UpdateOnlinePreferenceRequest_InternalOperatorId) isUpdateOnlinePreferenceRequest_OperatorIdentifier() {
}

// update online preference response
type UpdateOnlinePreferenceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UpdateOnlinePreferenceResponse) Reset() {
	*x = UpdateOnlinePreferenceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOnlinePreferenceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOnlinePreferenceResponse) ProtoMessage() {}

func (x *UpdateOnlinePreferenceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOnlinePreferenceResponse.ProtoReflect.Descriptor instead.
func (*UpdateOnlinePreferenceResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateOnlinePreferenceResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// get working hour list request
type GetWorkingHourListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,2,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
}

func (x *GetWorkingHourListRequest) Reset() {
	*x = GetWorkingHourListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkingHourListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkingHourListRequest) ProtoMessage() {}

func (x *GetWorkingHourListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkingHourListRequest.ProtoReflect.Descriptor instead.
func (*GetWorkingHourListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetWorkingHourListRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetWorkingHourListRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

// get working hour list response
type GetWorkingHourListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// working hour list
	WorkingHour *v1.WorkingHoursDef `protobuf:"bytes,1,opt,name=working_hour,json=workingHour,proto3" json:"working_hour,omitempty"`
}

func (x *GetWorkingHourListResponse) Reset() {
	*x = GetWorkingHourListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkingHourListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkingHourListResponse) ProtoMessage() {}

func (x *GetWorkingHourListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkingHourListResponse.ProtoReflect.Descriptor instead.
func (*GetWorkingHourListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetWorkingHourListResponse) GetWorkingHour() *v1.WorkingHoursDef {
	if x != nil {
		return x.WorkingHour
	}
	return nil
}

// update working hour request
type UpdateWorkingHourRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// working hour list
	WorkingHour *v1.WorkingHoursDef `protobuf:"bytes,2,opt,name=working_hour,json=workingHour,proto3" json:"working_hour,omitempty"`
	// the operator id, allow internal modifier or external modifier
	//
	// Types that are assignable to OperatorIdentifier:
	//
	//	*UpdateWorkingHourRequest_TokenStaffId
	//	*UpdateWorkingHourRequest_InternalOperatorId
	OperatorIdentifier isUpdateWorkingHourRequest_OperatorIdentifier `protobuf_oneof:"operator_identifier"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,5,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
}

func (x *UpdateWorkingHourRequest) Reset() {
	*x = UpdateWorkingHourRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorkingHourRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkingHourRequest) ProtoMessage() {}

func (x *UpdateWorkingHourRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkingHourRequest.ProtoReflect.Descriptor instead.
func (*UpdateWorkingHourRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateWorkingHourRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateWorkingHourRequest) GetWorkingHour() *v1.WorkingHoursDef {
	if x != nil {
		return x.WorkingHour
	}
	return nil
}

func (m *UpdateWorkingHourRequest) GetOperatorIdentifier() isUpdateWorkingHourRequest_OperatorIdentifier {
	if m != nil {
		return m.OperatorIdentifier
	}
	return nil
}

func (x *UpdateWorkingHourRequest) GetTokenStaffId() int64 {
	if x, ok := x.GetOperatorIdentifier().(*UpdateWorkingHourRequest_TokenStaffId); ok {
		return x.TokenStaffId
	}
	return 0
}

func (x *UpdateWorkingHourRequest) GetInternalOperatorId() string {
	if x, ok := x.GetOperatorIdentifier().(*UpdateWorkingHourRequest_InternalOperatorId); ok {
		return x.InternalOperatorId
	}
	return ""
}

func (x *UpdateWorkingHourRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

type isUpdateWorkingHourRequest_OperatorIdentifier interface {
	isUpdateWorkingHourRequest_OperatorIdentifier()
}

type UpdateWorkingHourRequest_TokenStaffId struct {
	// staff id from token
	TokenStaffId int64 `protobuf:"varint,3,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof"`
}

type UpdateWorkingHourRequest_InternalOperatorId struct {
	// the internal operator id
	InternalOperatorId string `protobuf:"bytes,4,opt,name=internal_operator_id,json=internalOperatorId,proto3,oneof"`
}

func (*UpdateWorkingHourRequest_TokenStaffId) isUpdateWorkingHourRequest_OperatorIdentifier() {}

func (*UpdateWorkingHourRequest_InternalOperatorId) isUpdateWorkingHourRequest_OperatorIdentifier() {}

// update working hour response
type UpdateWorkingHourResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UpdateWorkingHourResponse) Reset() {
	*x = UpdateWorkingHourResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorkingHourResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkingHourResponse) ProtoMessage() {}

func (x *UpdateWorkingHourResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkingHourResponse.ProtoReflect.Descriptor instead.
func (*UpdateWorkingHourResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{17}
}

func (x *UpdateWorkingHourResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// add closed date request
type AddClosedDateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// closed date
	ClosedDate *v1.CloseDateDef `protobuf:"bytes,2,opt,name=closed_date,json=closedDate,proto3" json:"closed_date,omitempty"`
	// the operator id, allow internal modifier or external modifier
	//
	// Types that are assignable to OperatorIdentifier:
	//
	//	*AddClosedDateRequest_TokenStaffId
	//	*AddClosedDateRequest_InternalOperatorId
	OperatorIdentifier isAddClosedDateRequest_OperatorIdentifier `protobuf_oneof:"operator_identifier"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,5,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
}

func (x *AddClosedDateRequest) Reset() {
	*x = AddClosedDateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddClosedDateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddClosedDateRequest) ProtoMessage() {}

func (x *AddClosedDateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddClosedDateRequest.ProtoReflect.Descriptor instead.
func (*AddClosedDateRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{18}
}

func (x *AddClosedDateRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AddClosedDateRequest) GetClosedDate() *v1.CloseDateDef {
	if x != nil {
		return x.ClosedDate
	}
	return nil
}

func (m *AddClosedDateRequest) GetOperatorIdentifier() isAddClosedDateRequest_OperatorIdentifier {
	if m != nil {
		return m.OperatorIdentifier
	}
	return nil
}

func (x *AddClosedDateRequest) GetTokenStaffId() int64 {
	if x, ok := x.GetOperatorIdentifier().(*AddClosedDateRequest_TokenStaffId); ok {
		return x.TokenStaffId
	}
	return 0
}

func (x *AddClosedDateRequest) GetInternalOperatorId() string {
	if x, ok := x.GetOperatorIdentifier().(*AddClosedDateRequest_InternalOperatorId); ok {
		return x.InternalOperatorId
	}
	return ""
}

func (x *AddClosedDateRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

type isAddClosedDateRequest_OperatorIdentifier interface {
	isAddClosedDateRequest_OperatorIdentifier()
}

type AddClosedDateRequest_TokenStaffId struct {
	// staff id from token
	TokenStaffId int64 `protobuf:"varint,3,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof"`
}

type AddClosedDateRequest_InternalOperatorId struct {
	// the internal operator id
	InternalOperatorId string `protobuf:"bytes,4,opt,name=internal_operator_id,json=internalOperatorId,proto3,oneof"`
}

func (*AddClosedDateRequest_TokenStaffId) isAddClosedDateRequest_OperatorIdentifier() {}

func (*AddClosedDateRequest_InternalOperatorId) isAddClosedDateRequest_OperatorIdentifier() {}

// add closed date response
type AddClosedDateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// closed date id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *AddClosedDateResponse) Reset() {
	*x = AddClosedDateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddClosedDateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddClosedDateResponse) ProtoMessage() {}

func (x *AddClosedDateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddClosedDateResponse.ProtoReflect.Descriptor instead.
func (*AddClosedDateResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{19}
}

func (x *AddClosedDateResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// update closed date request
type UpdateClosedDateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// closed date
	ClosedDate *v1.CloseDateDef `protobuf:"bytes,2,opt,name=closed_date,json=closedDate,proto3" json:"closed_date,omitempty"`
	// the operator id, allow internal modifier or external modifier
	//
	// Types that are assignable to OperatorIdentifier:
	//
	//	*UpdateClosedDateRequest_TokenStaffId
	//	*UpdateClosedDateRequest_InternalOperatorId
	OperatorIdentifier isUpdateClosedDateRequest_OperatorIdentifier `protobuf_oneof:"operator_identifier"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,5,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
}

func (x *UpdateClosedDateRequest) Reset() {
	*x = UpdateClosedDateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateClosedDateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateClosedDateRequest) ProtoMessage() {}

func (x *UpdateClosedDateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateClosedDateRequest.ProtoReflect.Descriptor instead.
func (*UpdateClosedDateRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{20}
}

func (x *UpdateClosedDateRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateClosedDateRequest) GetClosedDate() *v1.CloseDateDef {
	if x != nil {
		return x.ClosedDate
	}
	return nil
}

func (m *UpdateClosedDateRequest) GetOperatorIdentifier() isUpdateClosedDateRequest_OperatorIdentifier {
	if m != nil {
		return m.OperatorIdentifier
	}
	return nil
}

func (x *UpdateClosedDateRequest) GetTokenStaffId() int64 {
	if x, ok := x.GetOperatorIdentifier().(*UpdateClosedDateRequest_TokenStaffId); ok {
		return x.TokenStaffId
	}
	return 0
}

func (x *UpdateClosedDateRequest) GetInternalOperatorId() string {
	if x, ok := x.GetOperatorIdentifier().(*UpdateClosedDateRequest_InternalOperatorId); ok {
		return x.InternalOperatorId
	}
	return ""
}

func (x *UpdateClosedDateRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

type isUpdateClosedDateRequest_OperatorIdentifier interface {
	isUpdateClosedDateRequest_OperatorIdentifier()
}

type UpdateClosedDateRequest_TokenStaffId struct {
	// staff id from token
	TokenStaffId int64 `protobuf:"varint,3,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof"`
}

type UpdateClosedDateRequest_InternalOperatorId struct {
	// the internal operator id
	InternalOperatorId string `protobuf:"bytes,4,opt,name=internal_operator_id,json=internalOperatorId,proto3,oneof"`
}

func (*UpdateClosedDateRequest_TokenStaffId) isUpdateClosedDateRequest_OperatorIdentifier() {}

func (*UpdateClosedDateRequest_InternalOperatorId) isUpdateClosedDateRequest_OperatorIdentifier() {}

// update closed date response
type UpdateClosedDateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UpdateClosedDateResponse) Reset() {
	*x = UpdateClosedDateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateClosedDateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateClosedDateResponse) ProtoMessage() {}

func (x *UpdateClosedDateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateClosedDateResponse.ProtoReflect.Descriptor instead.
func (*UpdateClosedDateResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{21}
}

func (x *UpdateClosedDateResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// delete closed date request
type DeleteClosedDateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// closed date id
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// the operator id, allow internal modifier or external modifier
	//
	// Types that are assignable to OperatorIdentifier:
	//
	//	*DeleteClosedDateRequest_TokenStaffId
	//	*DeleteClosedDateRequest_InternalOperatorId
	OperatorIdentifier isDeleteClosedDateRequest_OperatorIdentifier `protobuf_oneof:"operator_identifier"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,5,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
}

func (x *DeleteClosedDateRequest) Reset() {
	*x = DeleteClosedDateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteClosedDateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteClosedDateRequest) ProtoMessage() {}

func (x *DeleteClosedDateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteClosedDateRequest.ProtoReflect.Descriptor instead.
func (*DeleteClosedDateRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{22}
}

func (x *DeleteClosedDateRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *DeleteClosedDateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (m *DeleteClosedDateRequest) GetOperatorIdentifier() isDeleteClosedDateRequest_OperatorIdentifier {
	if m != nil {
		return m.OperatorIdentifier
	}
	return nil
}

func (x *DeleteClosedDateRequest) GetTokenStaffId() int64 {
	if x, ok := x.GetOperatorIdentifier().(*DeleteClosedDateRequest_TokenStaffId); ok {
		return x.TokenStaffId
	}
	return 0
}

func (x *DeleteClosedDateRequest) GetInternalOperatorId() string {
	if x, ok := x.GetOperatorIdentifier().(*DeleteClosedDateRequest_InternalOperatorId); ok {
		return x.InternalOperatorId
	}
	return ""
}

func (x *DeleteClosedDateRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

type isDeleteClosedDateRequest_OperatorIdentifier interface {
	isDeleteClosedDateRequest_OperatorIdentifier()
}

type DeleteClosedDateRequest_TokenStaffId struct {
	// staff id from token
	TokenStaffId int64 `protobuf:"varint,3,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof"`
}

type DeleteClosedDateRequest_InternalOperatorId struct {
	// the internal operator id
	InternalOperatorId string `protobuf:"bytes,4,opt,name=internal_operator_id,json=internalOperatorId,proto3,oneof"`
}

func (*DeleteClosedDateRequest_TokenStaffId) isDeleteClosedDateRequest_OperatorIdentifier() {}

func (*DeleteClosedDateRequest_InternalOperatorId) isDeleteClosedDateRequest_OperatorIdentifier() {}

// delete closed date response
type DeleteClosedDateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// delete result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *DeleteClosedDateResponse) Reset() {
	*x = DeleteClosedDateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteClosedDateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteClosedDateResponse) ProtoMessage() {}

func (x *DeleteClosedDateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteClosedDateResponse.ProtoReflect.Descriptor instead.
func (*DeleteClosedDateResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{23}
}

func (x *DeleteClosedDateResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// get closed date list request
type GetClosedDateListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,2,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
	// year
	Year *int32 `protobuf:"varint,3,opt,name=year,proto3,oneof" json:"year,omitempty"`
}

func (x *GetClosedDateListRequest) Reset() {
	*x = GetClosedDateListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClosedDateListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClosedDateListRequest) ProtoMessage() {}

func (x *GetClosedDateListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClosedDateListRequest.ProtoReflect.Descriptor instead.
func (*GetClosedDateListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetClosedDateListRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetClosedDateListRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

func (x *GetClosedDateListRequest) GetYear() int32 {
	if x != nil && x.Year != nil {
		return *x.Year
	}
	return 0
}

// get closed date list response
type GetClosedDateListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// closed date list
	ClosedDate []*v1.CloseDateDef `protobuf:"bytes,1,rep,name=closed_date,json=closedDate,proto3" json:"closed_date,omitempty"`
}

func (x *GetClosedDateListResponse) Reset() {
	*x = GetClosedDateListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClosedDateListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClosedDateListResponse) ProtoMessage() {}

func (x *GetClosedDateListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClosedDateListResponse.ProtoReflect.Descriptor instead.
func (*GetClosedDateListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetClosedDateListResponse) GetClosedDate() []*v1.CloseDateDef {
	if x != nil {
		return x.ClosedDate
	}
	return nil
}

// get closed holiday list request
type GetClosedHolidayListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,2,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
	// year
	Year *string `protobuf:"bytes,3,opt,name=year,proto3,oneof" json:"year,omitempty"`
}

func (x *GetClosedHolidayListRequest) Reset() {
	*x = GetClosedHolidayListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClosedHolidayListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClosedHolidayListRequest) ProtoMessage() {}

func (x *GetClosedHolidayListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClosedHolidayListRequest.ProtoReflect.Descriptor instead.
func (*GetClosedHolidayListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{26}
}

func (x *GetClosedHolidayListRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetClosedHolidayListRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

func (x *GetClosedHolidayListRequest) GetYear() string {
	if x != nil && x.Year != nil {
		return *x.Year
	}
	return ""
}

// get closed holiday list response
type GetClosedHolidayListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// closed holiday list
	ClosedDate []*v1.CloseDateDef `protobuf:"bytes,1,rep,name=closed_date,json=closedDate,proto3" json:"closed_date,omitempty"`
}

func (x *GetClosedHolidayListResponse) Reset() {
	*x = GetClosedHolidayListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClosedHolidayListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClosedHolidayListResponse) ProtoMessage() {}

func (x *GetClosedHolidayListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClosedHolidayListResponse.ProtoReflect.Descriptor instead.
func (*GetClosedHolidayListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetClosedHolidayListResponse) GetClosedDate() []*v1.CloseDateDef {
	if x != nil {
		return x.ClosedDate
	}
	return nil
}

// edit closed holiday
type EditClosedHolidayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// the operator id, allow internal modifier or external modifier
	//
	// Types that are assignable to OperatorIdentifier:
	//
	//	*EditClosedHolidayRequest_TokenStaffId
	//	*EditClosedHolidayRequest_InternalOperatorId
	OperatorIdentifier isEditClosedHolidayRequest_OperatorIdentifier `protobuf_oneof:"operator_identifier"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,4,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
	// start date
	StartDate string `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate string `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// close or open
	IsClosed bool `protobuf:"varint,7,opt,name=is_closed,json=isClosed,proto3" json:"is_closed,omitempty"`
	// holiday description
	Description string `protobuf:"bytes,8,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *EditClosedHolidayRequest) Reset() {
	*x = EditClosedHolidayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditClosedHolidayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditClosedHolidayRequest) ProtoMessage() {}

func (x *EditClosedHolidayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditClosedHolidayRequest.ProtoReflect.Descriptor instead.
func (*EditClosedHolidayRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{28}
}

func (x *EditClosedHolidayRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (m *EditClosedHolidayRequest) GetOperatorIdentifier() isEditClosedHolidayRequest_OperatorIdentifier {
	if m != nil {
		return m.OperatorIdentifier
	}
	return nil
}

func (x *EditClosedHolidayRequest) GetTokenStaffId() int64 {
	if x, ok := x.GetOperatorIdentifier().(*EditClosedHolidayRequest_TokenStaffId); ok {
		return x.TokenStaffId
	}
	return 0
}

func (x *EditClosedHolidayRequest) GetInternalOperatorId() string {
	if x, ok := x.GetOperatorIdentifier().(*EditClosedHolidayRequest_InternalOperatorId); ok {
		return x.InternalOperatorId
	}
	return ""
}

func (x *EditClosedHolidayRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

func (x *EditClosedHolidayRequest) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *EditClosedHolidayRequest) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *EditClosedHolidayRequest) GetIsClosed() bool {
	if x != nil {
		return x.IsClosed
	}
	return false
}

func (x *EditClosedHolidayRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type isEditClosedHolidayRequest_OperatorIdentifier interface {
	isEditClosedHolidayRequest_OperatorIdentifier()
}

type EditClosedHolidayRequest_TokenStaffId struct {
	// staff id from token
	TokenStaffId int64 `protobuf:"varint,2,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof"`
}

type EditClosedHolidayRequest_InternalOperatorId struct {
	// the internal operator id
	InternalOperatorId string `protobuf:"bytes,3,opt,name=internal_operator_id,json=internalOperatorId,proto3,oneof"`
}

func (*EditClosedHolidayRequest_TokenStaffId) isEditClosedHolidayRequest_OperatorIdentifier() {}

func (*EditClosedHolidayRequest_InternalOperatorId) isEditClosedHolidayRequest_OperatorIdentifier() {}

// edit closed holiday response
type EditClosedHolidayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *EditClosedHolidayResponse) Reset() {
	*x = EditClosedHolidayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditClosedHolidayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditClosedHolidayResponse) ProtoMessage() {}

func (x *EditClosedHolidayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditClosedHolidayResponse.ProtoReflect.Descriptor instead.
func (*EditClosedHolidayResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{29}
}

func (x *EditClosedHolidayResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// mass edit closed holiday
type MassEditClosedHolidayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// closed date list
	ClosedHoliday []*v1.ClosedHolidayMassEditDef `protobuf:"bytes,2,rep,name=closed_holiday,json=closedHoliday,proto3" json:"closed_holiday,omitempty"`
	// the operator id, allow internal modifier or external modifier
	//
	// Types that are assignable to OperatorIdentifier:
	//
	//	*MassEditClosedHolidayRequest_TokenStaffId
	//	*MassEditClosedHolidayRequest_InternalOperatorId
	OperatorIdentifier isMassEditClosedHolidayRequest_OperatorIdentifier `protobuf_oneof:"operator_identifier"`
	// company id from token
	TokenCompanyId int64 `protobuf:"varint,5,opt,name=token_company_id,json=tokenCompanyId,proto3" json:"token_company_id,omitempty"`
}

func (x *MassEditClosedHolidayRequest) Reset() {
	*x = MassEditClosedHolidayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MassEditClosedHolidayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MassEditClosedHolidayRequest) ProtoMessage() {}

func (x *MassEditClosedHolidayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MassEditClosedHolidayRequest.ProtoReflect.Descriptor instead.
func (*MassEditClosedHolidayRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{30}
}

func (x *MassEditClosedHolidayRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *MassEditClosedHolidayRequest) GetClosedHoliday() []*v1.ClosedHolidayMassEditDef {
	if x != nil {
		return x.ClosedHoliday
	}
	return nil
}

func (m *MassEditClosedHolidayRequest) GetOperatorIdentifier() isMassEditClosedHolidayRequest_OperatorIdentifier {
	if m != nil {
		return m.OperatorIdentifier
	}
	return nil
}

func (x *MassEditClosedHolidayRequest) GetTokenStaffId() int64 {
	if x, ok := x.GetOperatorIdentifier().(*MassEditClosedHolidayRequest_TokenStaffId); ok {
		return x.TokenStaffId
	}
	return 0
}

func (x *MassEditClosedHolidayRequest) GetInternalOperatorId() string {
	if x, ok := x.GetOperatorIdentifier().(*MassEditClosedHolidayRequest_InternalOperatorId); ok {
		return x.InternalOperatorId
	}
	return ""
}

func (x *MassEditClosedHolidayRequest) GetTokenCompanyId() int64 {
	if x != nil {
		return x.TokenCompanyId
	}
	return 0
}

type isMassEditClosedHolidayRequest_OperatorIdentifier interface {
	isMassEditClosedHolidayRequest_OperatorIdentifier()
}

type MassEditClosedHolidayRequest_TokenStaffId struct {
	// staff id from token
	TokenStaffId int64 `protobuf:"varint,3,opt,name=token_staff_id,json=tokenStaffId,proto3,oneof"`
}

type MassEditClosedHolidayRequest_InternalOperatorId struct {
	// the internal operator id
	InternalOperatorId string `protobuf:"bytes,4,opt,name=internal_operator_id,json=internalOperatorId,proto3,oneof"`
}

func (*MassEditClosedHolidayRequest_TokenStaffId) isMassEditClosedHolidayRequest_OperatorIdentifier() {
}

func (*MassEditClosedHolidayRequest_InternalOperatorId) isMassEditClosedHolidayRequest_OperatorIdentifier() {
}

// mass edit closed holiday response
type MassEditClosedHolidayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *MassEditClosedHolidayResponse) Reset() {
	*x = MassEditClosedHolidayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MassEditClosedHolidayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MassEditClosedHolidayResponse) ProtoMessage() {}

func (x *MassEditClosedHolidayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MassEditClosedHolidayResponse.ProtoReflect.Descriptor instead.
func (*MassEditClosedHolidayResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{31}
}

func (x *MassEditClosedHolidayResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// get working location list by companies staff request
type GetWorkingLocationListByCompaniesStaffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// param list
	Params []*GetWorkingLocationListByCompaniesStaffRequest_Param `protobuf:"bytes,1,rep,name=params,proto3" json:"params,omitempty"`
}

func (x *GetWorkingLocationListByCompaniesStaffRequest) Reset() {
	*x = GetWorkingLocationListByCompaniesStaffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkingLocationListByCompaniesStaffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkingLocationListByCompaniesStaffRequest) ProtoMessage() {}

func (x *GetWorkingLocationListByCompaniesStaffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkingLocationListByCompaniesStaffRequest.ProtoReflect.Descriptor instead.
func (*GetWorkingLocationListByCompaniesStaffRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{32}
}

func (x *GetWorkingLocationListByCompaniesStaffRequest) GetParams() []*GetWorkingLocationListByCompaniesStaffRequest_Param {
	if x != nil {
		return x.Params
	}
	return nil
}

// get working location list by companies staff response
type GetWorkingLocationListByCompaniesStaffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id to location list
	CompanyLocationsMap map[int64]*GetWorkingLocationListByCompaniesStaffResponse_LocationList `protobuf:"bytes,1,rep,name=company_locations_map,json=companyLocationsMap,proto3" json:"company_locations_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetWorkingLocationListByCompaniesStaffResponse) Reset() {
	*x = GetWorkingLocationListByCompaniesStaffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkingLocationListByCompaniesStaffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkingLocationListByCompaniesStaffResponse) ProtoMessage() {}

func (x *GetWorkingLocationListByCompaniesStaffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkingLocationListByCompaniesStaffResponse.ProtoReflect.Descriptor instead.
func (*GetWorkingLocationListByCompaniesStaffResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{33}
}

func (x *GetWorkingLocationListByCompaniesStaffResponse) GetCompanyLocationsMap() map[int64]*GetWorkingLocationListByCompaniesStaffResponse_LocationList {
	if x != nil {
		return x.CompanyLocationsMap
	}
	return nil
}

// get staff's working location id request
type GetWorkingLocationIdsByStaffIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff ids
	StaffId []int64 `protobuf:"varint,1,rep,packed,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetWorkingLocationIdsByStaffIdsRequest) Reset() {
	*x = GetWorkingLocationIdsByStaffIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkingLocationIdsByStaffIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkingLocationIdsByStaffIdsRequest) ProtoMessage() {}

func (x *GetWorkingLocationIdsByStaffIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkingLocationIdsByStaffIdsRequest.ProtoReflect.Descriptor instead.
func (*GetWorkingLocationIdsByStaffIdsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{34}
}

func (x *GetWorkingLocationIdsByStaffIdsRequest) GetStaffId() []int64 {
	if x != nil {
		return x.StaffId
	}
	return nil
}

func (x *GetWorkingLocationIdsByStaffIdsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// get staff's working location id response
type GetWorkingLocationIdsByStaffIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id to location id list
	StaffLocationIds []*GetWorkingLocationIdsByStaffIdsResponse_StaffLocationIds `protobuf:"bytes,1,rep,name=staff_location_ids,json=staffLocationIds,proto3" json:"staff_location_ids,omitempty"`
}

func (x *GetWorkingLocationIdsByStaffIdsResponse) Reset() {
	*x = GetWorkingLocationIdsByStaffIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkingLocationIdsByStaffIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkingLocationIdsByStaffIdsResponse) ProtoMessage() {}

func (x *GetWorkingLocationIdsByStaffIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkingLocationIdsByStaffIdsResponse.ProtoReflect.Descriptor instead.
func (*GetWorkingLocationIdsByStaffIdsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{35}
}

func (x *GetWorkingLocationIdsByStaffIdsResponse) GetStaffLocationIds() []*GetWorkingLocationIdsByStaffIdsResponse_StaffLocationIds {
	if x != nil {
		return x.StaffLocationIds
	}
	return nil
}

// get working location current date time request
type GetWorkingLocationDateTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business ids
	BusinessIds []int64 `protobuf:"varint,1,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *GetWorkingLocationDateTimeRequest) Reset() {
	*x = GetWorkingLocationDateTimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkingLocationDateTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkingLocationDateTimeRequest) ProtoMessage() {}

func (x *GetWorkingLocationDateTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkingLocationDateTimeRequest.ProtoReflect.Descriptor instead.
func (*GetWorkingLocationDateTimeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{36}
}

func (x *GetWorkingLocationDateTimeRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// get working location current date time response
type GetWorkingLocationDateTimeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location_date_time
	LocationDateTime []*v1.LocationDateTimeDef `protobuf:"bytes,1,rep,name=location_date_time,json=locationDateTime,proto3" json:"location_date_time,omitempty"`
}

func (x *GetWorkingLocationDateTimeResponse) Reset() {
	*x = GetWorkingLocationDateTimeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkingLocationDateTimeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkingLocationDateTimeResponse) ProtoMessage() {}

func (x *GetWorkingLocationDateTimeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkingLocationDateTimeResponse.ProtoReflect.Descriptor instead.
func (*GetWorkingLocationDateTimeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{37}
}

func (x *GetWorkingLocationDateTimeResponse) GetLocationDateTime() []*v1.LocationDateTimeDef {
	if x != nil {
		return x.LocationDateTime
	}
	return nil
}

// batch get location id list request
type BatchGetLocationIdListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id list
	CompanyId []int64 `protobuf:"varint,1,rep,packed,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *BatchGetLocationIdListRequest) Reset() {
	*x = BatchGetLocationIdListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetLocationIdListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetLocationIdListRequest) ProtoMessage() {}

func (x *BatchGetLocationIdListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetLocationIdListRequest.ProtoReflect.Descriptor instead.
func (*BatchGetLocationIdListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{38}
}

func (x *BatchGetLocationIdListRequest) GetCompanyId() []int64 {
	if x != nil {
		return x.CompanyId
	}
	return nil
}

// batch get location id list response
type BatchGetLocationIdListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id to location id list
	CompanyLocationIdListMap map[int64]*v2.Int64List `protobuf:"bytes,1,rep,name=company_location_id_list_map,json=companyLocationIdListMap,proto3" json:"company_location_id_list_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BatchGetLocationIdListResponse) Reset() {
	*x = BatchGetLocationIdListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetLocationIdListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetLocationIdListResponse) ProtoMessage() {}

func (x *BatchGetLocationIdListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetLocationIdListResponse.ProtoReflect.Descriptor instead.
func (*BatchGetLocationIdListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{39}
}

func (x *BatchGetLocationIdListResponse) GetCompanyLocationIdListMap() map[int64]*v2.Int64List {
	if x != nil {
		return x.CompanyLocationIdListMap
	}
	return nil
}

// legacy api, get business info with owner email request
type GetBusinessInfoWithOwnerEmailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetBusinessInfoWithOwnerEmailRequest) Reset() {
	*x = GetBusinessInfoWithOwnerEmailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessInfoWithOwnerEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessInfoWithOwnerEmailRequest) ProtoMessage() {}

func (x *GetBusinessInfoWithOwnerEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessInfoWithOwnerEmailRequest.ProtoReflect.Descriptor instead.
func (*GetBusinessInfoWithOwnerEmailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{40}
}

func (x *GetBusinessInfoWithOwnerEmailRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// legacy api, get business info with owner email response
type GetBusinessInfoWithOwnerEmailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business model
	Business *v11.BusinessModel `protobuf:"bytes,1,opt,name=business,proto3" json:"business,omitempty"`
}

func (x *GetBusinessInfoWithOwnerEmailResponse) Reset() {
	*x = GetBusinessInfoWithOwnerEmailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessInfoWithOwnerEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessInfoWithOwnerEmailResponse) ProtoMessage() {}

func (x *GetBusinessInfoWithOwnerEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessInfoWithOwnerEmailResponse.ProtoReflect.Descriptor instead.
func (*GetBusinessInfoWithOwnerEmailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{41}
}

func (x *GetBusinessInfoWithOwnerEmailResponse) GetBusiness() *v11.BusinessModel {
	if x != nil {
		return x.Business
	}
	return nil
}

// batch get business owner account id request
type BatchGetBusinessOwnerAccountIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id list
	BusinessIds []int64 `protobuf:"varint,1,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// get all businesses
	GetAllBusinesses bool `protobuf:"varint,2,opt,name=get_all_businesses,json=getAllBusinesses,proto3" json:"get_all_businesses,omitempty"`
}

func (x *BatchGetBusinessOwnerAccountIdRequest) Reset() {
	*x = BatchGetBusinessOwnerAccountIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetBusinessOwnerAccountIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetBusinessOwnerAccountIdRequest) ProtoMessage() {}

func (x *BatchGetBusinessOwnerAccountIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetBusinessOwnerAccountIdRequest.ProtoReflect.Descriptor instead.
func (*BatchGetBusinessOwnerAccountIdRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{42}
}

func (x *BatchGetBusinessOwnerAccountIdRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *BatchGetBusinessOwnerAccountIdRequest) GetGetAllBusinesses() bool {
	if x != nil {
		return x.GetAllBusinesses
	}
	return false
}

// batch get business owner account id response
type BatchGetBusinessOwnerAccountIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id to owner account id
	BusinessOwnerAccountIdMap map[int64]int64 `protobuf:"bytes,1,rep,name=business_owner_account_id_map,json=businessOwnerAccountIdMap,proto3" json:"business_owner_account_id_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *BatchGetBusinessOwnerAccountIdResponse) Reset() {
	*x = BatchGetBusinessOwnerAccountIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetBusinessOwnerAccountIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetBusinessOwnerAccountIdResponse) ProtoMessage() {}

func (x *BatchGetBusinessOwnerAccountIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetBusinessOwnerAccountIdResponse.ProtoReflect.Descriptor instead.
func (*BatchGetBusinessOwnerAccountIdResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{43}
}

func (x *BatchGetBusinessOwnerAccountIdResponse) GetBusinessOwnerAccountIdMap() map[int64]int64 {
	if x != nil {
		return x.BusinessOwnerAccountIdMap
	}
	return nil
}

// get location list by enterprise staff request
type GetLocationListByEnterpriseStaffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
}

func (x *GetLocationListByEnterpriseStaffRequest) Reset() {
	*x = GetLocationListByEnterpriseStaffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLocationListByEnterpriseStaffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLocationListByEnterpriseStaffRequest) ProtoMessage() {}

func (x *GetLocationListByEnterpriseStaffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLocationListByEnterpriseStaffRequest.ProtoReflect.Descriptor instead.
func (*GetLocationListByEnterpriseStaffRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{44}
}

func (x *GetLocationListByEnterpriseStaffRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *GetLocationListByEnterpriseStaffRequest) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

// get location list by enterprise staff response
type GetLocationListByEnterpriseStaffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location list
	Locations []*v1.LocationModel `protobuf:"bytes,1,rep,name=locations,proto3" json:"locations,omitempty"`
}

func (x *GetLocationListByEnterpriseStaffResponse) Reset() {
	*x = GetLocationListByEnterpriseStaffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLocationListByEnterpriseStaffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLocationListByEnterpriseStaffResponse) ProtoMessage() {}

func (x *GetLocationListByEnterpriseStaffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLocationListByEnterpriseStaffResponse.ProtoReflect.Descriptor instead.
func (*GetLocationListByEnterpriseStaffResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{45}
}

func (x *GetLocationListByEnterpriseStaffResponse) GetLocations() []*v1.LocationModel {
	if x != nil {
		return x.Locations
	}
	return nil
}

// batch get business payment method request
type ListPaymentMethodsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// business id list
	BusinessIds []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *ListPaymentMethodsRequest) Reset() {
	*x = ListPaymentMethodsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPaymentMethodsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPaymentMethodsRequest) ProtoMessage() {}

func (x *ListPaymentMethodsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPaymentMethodsRequest.ProtoReflect.Descriptor instead.
func (*ListPaymentMethodsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{46}
}

func (x *ListPaymentMethodsRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *ListPaymentMethodsRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// batch get business payment method response
type ListPaymentMethodsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id to payment method list
	BusinessPaymentMethodsMap map[int64]*v1.PaymentMethodListDef `protobuf:"bytes,1,rep,name=business_payment_methods_map,json=businessPaymentMethodsMap,proto3" json:"business_payment_methods_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ListPaymentMethodsResponse) Reset() {
	*x = ListPaymentMethodsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPaymentMethodsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPaymentMethodsResponse) ProtoMessage() {}

func (x *ListPaymentMethodsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPaymentMethodsResponse.ProtoReflect.Descriptor instead.
func (*ListPaymentMethodsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{47}
}

func (x *ListPaymentMethodsResponse) GetBusinessPaymentMethodsMap() map[int64]*v1.PaymentMethodListDef {
	if x != nil {
		return x.BusinessPaymentMethodsMap
	}
	return nil
}

// list business working hours request
type ListBusinessWorkingHoursRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *ListBusinessWorkingHoursRequest) Reset() {
	*x = ListBusinessWorkingHoursRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBusinessWorkingHoursRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBusinessWorkingHoursRequest) ProtoMessage() {}

func (x *ListBusinessWorkingHoursRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBusinessWorkingHoursRequest.ProtoReflect.Descriptor instead.
func (*ListBusinessWorkingHoursRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{48}
}

func (x *ListBusinessWorkingHoursRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// list business working hours response
type ListBusinessWorkingHoursResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id to working hours
	BusinessWorkingHoursMap map[int64]*v1.WorkingHoursDef `protobuf:"bytes,1,rep,name=business_working_hours_map,json=businessWorkingHoursMap,proto3" json:"business_working_hours_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ListBusinessWorkingHoursResponse) Reset() {
	*x = ListBusinessWorkingHoursResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBusinessWorkingHoursResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBusinessWorkingHoursResponse) ProtoMessage() {}

func (x *ListBusinessWorkingHoursResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBusinessWorkingHoursResponse.ProtoReflect.Descriptor instead.
func (*ListBusinessWorkingHoursResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{49}
}

func (x *ListBusinessWorkingHoursResponse) GetBusinessWorkingHoursMap() map[int64]*v1.WorkingHoursDef {
	if x != nil {
		return x.BusinessWorkingHoursMap
	}
	return nil
}

// list locations
type ListLocationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination, not set for get all
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// filter
	Filter *ListLocationsRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListLocationsRequest) Reset() {
	*x = ListLocationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLocationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLocationsRequest) ProtoMessage() {}

func (x *ListLocationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLocationsRequest.ProtoReflect.Descriptor instead.
func (*ListLocationsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{50}
}

func (x *ListLocationsRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListLocationsRequest) GetFilter() *ListLocationsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list locations response
type ListLocationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// location list
	Locations []*v1.LocationModel `protobuf:"bytes,2,rep,name=locations,proto3" json:"locations,omitempty"`
}

func (x *ListLocationsResponse) Reset() {
	*x = ListLocationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLocationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLocationsResponse) ProtoMessage() {}

func (x *ListLocationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLocationsResponse.ProtoReflect.Descriptor instead.
func (*ListLocationsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{51}
}

func (x *ListLocationsResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListLocationsResponse) GetLocations() []*v1.LocationModel {
	if x != nil {
		return x.Locations
	}
	return nil
}

// param
type GetWorkingLocationListByCompaniesStaffRequest_Param struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staff id,use to check permission
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *GetWorkingLocationListByCompaniesStaffRequest_Param) Reset() {
	*x = GetWorkingLocationListByCompaniesStaffRequest_Param{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkingLocationListByCompaniesStaffRequest_Param) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkingLocationListByCompaniesStaffRequest_Param) ProtoMessage() {}

func (x *GetWorkingLocationListByCompaniesStaffRequest_Param) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkingLocationListByCompaniesStaffRequest_Param.ProtoReflect.Descriptor instead.
func (*GetWorkingLocationListByCompaniesStaffRequest_Param) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{32, 0}
}

func (x *GetWorkingLocationListByCompaniesStaffRequest_Param) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetWorkingLocationListByCompaniesStaffRequest_Param) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// location list
type GetWorkingLocationListByCompaniesStaffResponse_LocationList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location list
	Locations []*v1.LocationModel `protobuf:"bytes,1,rep,name=locations,proto3" json:"locations,omitempty"`
}

func (x *GetWorkingLocationListByCompaniesStaffResponse_LocationList) Reset() {
	*x = GetWorkingLocationListByCompaniesStaffResponse_LocationList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkingLocationListByCompaniesStaffResponse_LocationList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkingLocationListByCompaniesStaffResponse_LocationList) ProtoMessage() {}

func (x *GetWorkingLocationListByCompaniesStaffResponse_LocationList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkingLocationListByCompaniesStaffResponse_LocationList.ProtoReflect.Descriptor instead.
func (*GetWorkingLocationListByCompaniesStaffResponse_LocationList) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{33, 0}
}

func (x *GetWorkingLocationListByCompaniesStaffResponse_LocationList) GetLocations() []*v1.LocationModel {
	if x != nil {
		return x.Locations
	}
	return nil
}

// StaffLocationIds
type GetWorkingLocationIdsByStaffIdsResponse_StaffLocationIds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// location id list
	LocationId []int64 `protobuf:"varint,2,rep,packed,name=location_id,json=locationId,proto3" json:"location_id,omitempty"`
}

func (x *GetWorkingLocationIdsByStaffIdsResponse_StaffLocationIds) Reset() {
	*x = GetWorkingLocationIdsByStaffIdsResponse_StaffLocationIds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkingLocationIdsByStaffIdsResponse_StaffLocationIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkingLocationIdsByStaffIdsResponse_StaffLocationIds) ProtoMessage() {}

func (x *GetWorkingLocationIdsByStaffIdsResponse_StaffLocationIds) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkingLocationIdsByStaffIdsResponse_StaffLocationIds.ProtoReflect.Descriptor instead.
func (*GetWorkingLocationIdsByStaffIdsResponse_StaffLocationIds) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{35, 0}
}

func (x *GetWorkingLocationIdsByStaffIdsResponse_StaffLocationIds) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *GetWorkingLocationIdsByStaffIdsResponse_StaffLocationIds) GetLocationId() []int64 {
	if x != nil {
		return x.LocationId
	}
	return nil
}

// filter, each field will be used as AND , under the same field will be used as OR
type ListLocationsRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// company ids
	CompanyIds []int64 `protobuf:"varint,3,rep,packed,name=company_ids,json=companyIds,proto3" json:"company_ids,omitempty"`
}

func (x *ListLocationsRequest_Filter) Reset() {
	*x = ListLocationsRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLocationsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLocationsRequest_Filter) ProtoMessage() {}

func (x *ListLocationsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_business_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLocationsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListLocationsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_business_service_proto_rawDescGZIP(), []int{50, 0}
}

func (x *ListLocationsRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListLocationsRequest_Filter) GetCompanyIds() []int64 {
	if x != nil {
		return x.CompanyIds
	}
	return nil
}

var File_moego_service_organization_v1_business_service_proto protoreflect.FileDescriptor

var file_moego_service_organization_v1_business_service_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64,
	0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x64, 0x65, 0x66,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x68, 0x6f,
	0x75, 0x72, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x6c, 0x69,
	0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x36, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x22, 0x35, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x47, 0x0a, 0x18, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x73, 0x22, 0xf0, 0x01, 0x0a, 0x19, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x89, 0x01, 0x0a, 0x17, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x52, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x14, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x4d, 0x61, 0x70, 0x1a, 0x47, 0x0a,
	0x19, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xad, 0x02, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x55, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x02, 0x18,
	0x32, 0x48, 0x00, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x42, 0x1a, 0x0a, 0x13, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x28, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x22, 0xad, 0x02, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x55, 0x0a, 0x08, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2f, 0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x48, 0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x12, 0x3d, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x12, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x31, 0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x42, 0x1a, 0x0a, 0x13, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01,
	0x22, 0x32, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x22, 0xe9, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x31, 0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x32, 0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x01, 0x52,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x11,
	0x0a, 0x0f, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0xbe, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x08,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x80, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x36, 0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0e, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42,
	0x13, 0x0a, 0x11, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x22, 0x64, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x47, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xce, 0x02, 0x0a, 0x1d, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x6e, 0x0a, 0x11,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x65, 0x66,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x10, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x2f, 0x0a, 0x0e,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52,
	0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x3d, 0x0a,
	0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x42,
	0x1a, 0x0a, 0x13, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x3a, 0x0a, 0x1e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x78, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x31,
	0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x22, 0x6e, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48,
	0x6f, 0x75, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x50, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72,
	0x73, 0x44, 0x65, 0x66, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75,
	0x72, 0x22, 0xdf, 0x02, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f,
	0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67,
	0x48, 0x6f, 0x75, 0x72, 0x12, 0x2f, 0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x02, 0x18, 0x32, 0x48, 0x00,
	0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x42, 0x1a, 0x0a, 0x13, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03,
	0xf8, 0x42, 0x01, 0x22, 0x35, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0xd6, 0x02, 0x0a, 0x14, 0x41,
	0x64, 0x64, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x55, 0x0a,
	0x0b, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x44, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x02, 0x18, 0x32, 0x48, 0x00,
	0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x42, 0x1a, 0x0a, 0x13, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03,
	0xf8, 0x42, 0x01, 0x22, 0x27, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xd9, 0x02, 0x0a,
	0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x55, 0x0a, 0x0b, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x63,
	0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x0e, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0c, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x14, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x42, 0x1a, 0x0a, 0x13,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x34, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x9b,
	0x02, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2f, 0x0a,
	0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00,
	0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x3d,
	0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a,
	0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x42, 0x1a, 0x0a, 0x13, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x34, 0x0a, 0x18,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x22, 0xa2, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64,
	0x44, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x04,
	0x79, 0x65, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x04, 0x79, 0x65, 0x61, 0x72, 0x88, 0x01, 0x01, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x22, 0x68, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43, 0x6c,
	0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0b, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x22, 0xa6, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x48,
	0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x04, 0x79, 0x65, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x72, 0x03, 0x98, 0x01, 0x04, 0x48, 0x00, 0x52, 0x04, 0x79, 0x65, 0x61, 0x72, 0x88, 0x01,
	0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x22, 0x6b, 0x0a, 0x1c, 0x47, 0x65,
	0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0b, 0x63, 0x6c,
	0x6f, 0x73, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6c, 0x6f, 0x73, 0x65, 0x44, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x63, 0x6c, 0x6f,
	0x73, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x22, 0x90, 0x03, 0x0a, 0x18, 0x45, 0x64, 0x69, 0x74,
	0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2f,
	0x0a, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48,
	0x00, 0x52, 0x0c, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12,
	0x3d, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x31,
	0x0a, 0x10, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0e, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x27, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x98, 0x01, 0x0a, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x72, 0x03, 0x98, 0x01, 0x0a, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x1a,
	0x0a, 0x13, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x35, 0x0a, 0x19, 0x45, 0x64,
	0x69, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x22, 0xe6, 0x02, 0x0a, 0x1c, 0x4d, 0x61, 0x73, 0x73, 0x45, 0x64, 0x69, 0x74, 0x43, 0x6c,
	0x6f, 0x73, 0x65, 0x64, 0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x5d, 0x0a, 0x0e,
	0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x68, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61,
	0x79, 0x4d, 0x61, 0x73, 0x73, 0x45, 0x64, 0x69, 0x74, 0x44, 0x65, 0x66, 0x52, 0x0d, 0x63, 0x6c,
	0x6f, 0x73, 0x65, 0x64, 0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x12, 0x2f, 0x0a, 0x0e, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0c,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x14,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x42, 0x1a,
	0x0a, 0x13, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x39, 0x0a, 0x1d, 0x4d, 0x61,
	0x73, 0x73, 0x45, 0x64, 0x69, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x48, 0x6f, 0x6c, 0x69,
	0x64, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0xfd, 0x01, 0x0a, 0x2d, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x77, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x52, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x42, 0x0b, 0xfa, 0x42, 0x08,
	0x92, 0x01, 0x05, 0x08, 0x01, 0x10, 0xf4, 0x03, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x53, 0x0a, 0x05, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x22, 0xcd, 0x03, 0x0a, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9a, 0x01, 0x0a, 0x15, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x6d,
	0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x66, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42,
	0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x13, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x4d, 0x61, 0x70, 0x1a, 0x59, 0x0a, 0x0c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x1a, 0xa2, 0x01, 0x0a, 0x18, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x70, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x5a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x7b, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x42, 0x79,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x29, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x22, 0x81, 0x02, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e,
	0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x42, 0x79, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x85,
	0x01, 0x0a, 0x12, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x57, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x73, 0x42, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x73, 0x52, 0x10, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x1a, 0x4e, 0x0a, 0x10, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x46, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x22, 0x85,
	0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a, 0x12, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x44, 0x65, 0x66, 0x52, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x48, 0x0a, 0x1d, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x22, 0xa6, 0x02, 0x0a, 0x1e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x9b, 0x01, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x5b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x18, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61,
	0x70, 0x1a, 0x66, 0x0a, 0x1d, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x3f, 0x0a, 0x24, 0x47, 0x65, 0x74,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74, 0x68,
	0x4f, 0x77, 0x6e, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x6c, 0x0a, 0x25, 0x47, 0x65,
	0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74,
	0x68, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x22, 0x82, 0x01, 0x0a, 0x25, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e,
	0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02,
	0x18, 0x01, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12,
	0x2c, 0x0a, 0x12, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x67, 0x65, 0x74,
	0x41, 0x6c, 0x6c, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x22, 0x9f, 0x02,
	0x0a, 0x26, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa6, 0x01, 0x0a, 0x1d, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x64, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x4f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x4d, 0x61,
	0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x19, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x4f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x4d, 0x61,
	0x70, 0x1a, 0x4c, 0x0a, 0x1e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e,
	0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x7b, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x79, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x2c,
	0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x22, 0x75, 0x0a, 0x28,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42,
	0x79, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x09, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0x84, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2b,
	0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x22, 0xbb, 0x02, 0x0a, 0x1a, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x1c, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x58, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x19, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x73, 0x4d, 0x61, 0x70, 0x1a, 0x80, 0x01, 0x0a, 0x1e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x48, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x66, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x49, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48,
	0x6f, 0x75, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x22, 0xb9, 0x02, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x1a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x68, 0x6f,
	0x75, 0x72, 0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x5c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e,
	0x67, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f,
	0x75, 0x72, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x17, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72,
	0x73, 0x4d, 0x61, 0x70, 0x1a, 0x79, 0x0a, 0x1c, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x43, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72,
	0x73, 0x44, 0x65, 0x66, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x86, 0x02, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x52, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a,
	0x57, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x03, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x2d, 0x0a, 0x0b, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0c,
	0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0xa6, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73,
	0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x49, 0x0a, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x32, 0xfb, 0x1d, 0x0a, 0x0f, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x77, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x86,
	0x01, 0x0a, 0x11, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7d, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7d, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x80, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x95, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x3c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x12, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7a,
	0x0a, 0x0d, 0x41, 0x64, 0x64, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x64, 0x64, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x10, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c,
	0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x83, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65,
	0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x73,
	0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x6c,
	0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x37, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x8f, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x48, 0x6f, 0x6c,
	0x69, 0x64, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x73,
	0x65, 0x64, 0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x48, 0x6f,
	0x6c, 0x69, 0x64, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x86, 0x01, 0x0a, 0x11, 0x45, 0x64, 0x69, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64,
	0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x43, 0x6c, 0x6f, 0x73,
	0x65, 0x64, 0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x64, 0x69, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x48, 0x6f, 0x6c, 0x69, 0x64,
	0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92, 0x01, 0x0a, 0x15, 0x4d,
	0x61, 0x73, 0x73, 0x45, 0x64, 0x69, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x48, 0x6f, 0x6c,
	0x69, 0x64, 0x61, 0x79, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61, 0x73, 0x73, 0x45, 0x64, 0x69, 0x74, 0x43, 0x6c, 0x6f,
	0x73, 0x65, 0x64, 0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x61, 0x73, 0x73, 0x45, 0x64, 0x69, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64,
	0x48, 0x6f, 0x6c, 0x69, 0x64, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0xc5, 0x01, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x69, 0x65, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x4c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b,
	0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42,
	0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb0, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x73, 0x42, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x12, 0x45, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x73, 0x42, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x42, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49,
	0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa1, 0x01, 0x0a, 0x1a, 0x47,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x95,
	0x01, 0x0a, 0x16, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xaa, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74, 0x68, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74, 0x68, 0x4f, 0x77, 0x6e, 0x65,
	0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74,
	0x68, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0xad, 0x01, 0x0a, 0x1e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x77, 0x6e,
	0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0xb3, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x79, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x12, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73,
	0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9b, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75,
	0x72, 0x73, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x57,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x57,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x7a, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x8f, 0x01, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x64, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x76, 0x63, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_organization_v1_business_service_proto_rawDescOnce sync.Once
	file_moego_service_organization_v1_business_service_proto_rawDescData = file_moego_service_organization_v1_business_service_proto_rawDesc
)

func file_moego_service_organization_v1_business_service_proto_rawDescGZIP() []byte {
	file_moego_service_organization_v1_business_service_proto_rawDescOnce.Do(func() {
		file_moego_service_organization_v1_business_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_organization_v1_business_service_proto_rawDescData)
	})
	return file_moego_service_organization_v1_business_service_proto_rawDescData
}

var file_moego_service_organization_v1_business_service_proto_msgTypes = make([]protoimpl.MessageInfo, 62)
var file_moego_service_organization_v1_business_service_proto_goTypes = []interface{}{
	(*GetCompanyIdRequest)(nil),                                         // 0: moego.service.organization.v1.GetCompanyIdRequest
	(*GetCompanyIdResponse)(nil),                                        // 1: moego.service.organization.v1.GetCompanyIdResponse
	(*BatchGetCompanyIdRequest)(nil),                                    // 2: moego.service.organization.v1.BatchGetCompanyIdRequest
	(*BatchGetCompanyIdResponse)(nil),                                   // 3: moego.service.organization.v1.BatchGetCompanyIdResponse
	(*CreateLocationRequest)(nil),                                       // 4: moego.service.organization.v1.CreateLocationRequest
	(*CreateLocationResponse)(nil),                                      // 5: moego.service.organization.v1.CreateLocationResponse
	(*UpdateLocationRequest)(nil),                                       // 6: moego.service.organization.v1.UpdateLocationRequest
	(*UpdateLocationResponse)(nil),                                      // 7: moego.service.organization.v1.UpdateLocationResponse
	(*GetLocationListRequest)(nil),                                      // 8: moego.service.organization.v1.GetLocationListRequest
	(*GetLocationListResponse)(nil),                                     // 9: moego.service.organization.v1.GetLocationListResponse
	(*GetLocationDetailRequest)(nil),                                    // 10: moego.service.organization.v1.GetLocationDetailRequest
	(*GetLocationDetailResponse)(nil),                                   // 11: moego.service.organization.v1.GetLocationDetailResponse
	(*UpdateOnlinePreferenceRequest)(nil),                               // 12: moego.service.organization.v1.UpdateOnlinePreferenceRequest
	(*UpdateOnlinePreferenceResponse)(nil),                              // 13: moego.service.organization.v1.UpdateOnlinePreferenceResponse
	(*GetWorkingHourListRequest)(nil),                                   // 14: moego.service.organization.v1.GetWorkingHourListRequest
	(*GetWorkingHourListResponse)(nil),                                  // 15: moego.service.organization.v1.GetWorkingHourListResponse
	(*UpdateWorkingHourRequest)(nil),                                    // 16: moego.service.organization.v1.UpdateWorkingHourRequest
	(*UpdateWorkingHourResponse)(nil),                                   // 17: moego.service.organization.v1.UpdateWorkingHourResponse
	(*AddClosedDateRequest)(nil),                                        // 18: moego.service.organization.v1.AddClosedDateRequest
	(*AddClosedDateResponse)(nil),                                       // 19: moego.service.organization.v1.AddClosedDateResponse
	(*UpdateClosedDateRequest)(nil),                                     // 20: moego.service.organization.v1.UpdateClosedDateRequest
	(*UpdateClosedDateResponse)(nil),                                    // 21: moego.service.organization.v1.UpdateClosedDateResponse
	(*DeleteClosedDateRequest)(nil),                                     // 22: moego.service.organization.v1.DeleteClosedDateRequest
	(*DeleteClosedDateResponse)(nil),                                    // 23: moego.service.organization.v1.DeleteClosedDateResponse
	(*GetClosedDateListRequest)(nil),                                    // 24: moego.service.organization.v1.GetClosedDateListRequest
	(*GetClosedDateListResponse)(nil),                                   // 25: moego.service.organization.v1.GetClosedDateListResponse
	(*GetClosedHolidayListRequest)(nil),                                 // 26: moego.service.organization.v1.GetClosedHolidayListRequest
	(*GetClosedHolidayListResponse)(nil),                                // 27: moego.service.organization.v1.GetClosedHolidayListResponse
	(*EditClosedHolidayRequest)(nil),                                    // 28: moego.service.organization.v1.EditClosedHolidayRequest
	(*EditClosedHolidayResponse)(nil),                                   // 29: moego.service.organization.v1.EditClosedHolidayResponse
	(*MassEditClosedHolidayRequest)(nil),                                // 30: moego.service.organization.v1.MassEditClosedHolidayRequest
	(*MassEditClosedHolidayResponse)(nil),                               // 31: moego.service.organization.v1.MassEditClosedHolidayResponse
	(*GetWorkingLocationListByCompaniesStaffRequest)(nil),               // 32: moego.service.organization.v1.GetWorkingLocationListByCompaniesStaffRequest
	(*GetWorkingLocationListByCompaniesStaffResponse)(nil),              // 33: moego.service.organization.v1.GetWorkingLocationListByCompaniesStaffResponse
	(*GetWorkingLocationIdsByStaffIdsRequest)(nil),                      // 34: moego.service.organization.v1.GetWorkingLocationIdsByStaffIdsRequest
	(*GetWorkingLocationIdsByStaffIdsResponse)(nil),                     // 35: moego.service.organization.v1.GetWorkingLocationIdsByStaffIdsResponse
	(*GetWorkingLocationDateTimeRequest)(nil),                           // 36: moego.service.organization.v1.GetWorkingLocationDateTimeRequest
	(*GetWorkingLocationDateTimeResponse)(nil),                          // 37: moego.service.organization.v1.GetWorkingLocationDateTimeResponse
	(*BatchGetLocationIdListRequest)(nil),                               // 38: moego.service.organization.v1.BatchGetLocationIdListRequest
	(*BatchGetLocationIdListResponse)(nil),                              // 39: moego.service.organization.v1.BatchGetLocationIdListResponse
	(*GetBusinessInfoWithOwnerEmailRequest)(nil),                        // 40: moego.service.organization.v1.GetBusinessInfoWithOwnerEmailRequest
	(*GetBusinessInfoWithOwnerEmailResponse)(nil),                       // 41: moego.service.organization.v1.GetBusinessInfoWithOwnerEmailResponse
	(*BatchGetBusinessOwnerAccountIdRequest)(nil),                       // 42: moego.service.organization.v1.BatchGetBusinessOwnerAccountIdRequest
	(*BatchGetBusinessOwnerAccountIdResponse)(nil),                      // 43: moego.service.organization.v1.BatchGetBusinessOwnerAccountIdResponse
	(*GetLocationListByEnterpriseStaffRequest)(nil),                     // 44: moego.service.organization.v1.GetLocationListByEnterpriseStaffRequest
	(*GetLocationListByEnterpriseStaffResponse)(nil),                    // 45: moego.service.organization.v1.GetLocationListByEnterpriseStaffResponse
	(*ListPaymentMethodsRequest)(nil),                                   // 46: moego.service.organization.v1.ListPaymentMethodsRequest
	(*ListPaymentMethodsResponse)(nil),                                  // 47: moego.service.organization.v1.ListPaymentMethodsResponse
	(*ListBusinessWorkingHoursRequest)(nil),                             // 48: moego.service.organization.v1.ListBusinessWorkingHoursRequest
	(*ListBusinessWorkingHoursResponse)(nil),                            // 49: moego.service.organization.v1.ListBusinessWorkingHoursResponse
	(*ListLocationsRequest)(nil),                                        // 50: moego.service.organization.v1.ListLocationsRequest
	(*ListLocationsResponse)(nil),                                       // 51: moego.service.organization.v1.ListLocationsResponse
	nil,                                                                 // 52: moego.service.organization.v1.BatchGetCompanyIdResponse.BusinessCompanyIdMapEntry
	(*GetWorkingLocationListByCompaniesStaffRequest_Param)(nil),         // 53: moego.service.organization.v1.GetWorkingLocationListByCompaniesStaffRequest.Param
	(*GetWorkingLocationListByCompaniesStaffResponse_LocationList)(nil), // 54: moego.service.organization.v1.GetWorkingLocationListByCompaniesStaffResponse.LocationList
	nil, // 55: moego.service.organization.v1.GetWorkingLocationListByCompaniesStaffResponse.CompanyLocationsMapEntry
	(*GetWorkingLocationIdsByStaffIdsResponse_StaffLocationIds)(nil), // 56: moego.service.organization.v1.GetWorkingLocationIdsByStaffIdsResponse.StaffLocationIds
	nil,                                  // 57: moego.service.organization.v1.BatchGetLocationIdListResponse.CompanyLocationIdListMapEntry
	nil,                                  // 58: moego.service.organization.v1.BatchGetBusinessOwnerAccountIdResponse.BusinessOwnerAccountIdMapEntry
	nil,                                  // 59: moego.service.organization.v1.ListPaymentMethodsResponse.BusinessPaymentMethodsMapEntry
	nil,                                  // 60: moego.service.organization.v1.ListBusinessWorkingHoursResponse.BusinessWorkingHoursMapEntry
	(*ListLocationsRequest_Filter)(nil),  // 61: moego.service.organization.v1.ListLocationsRequest.Filter
	(*v1.CreateLocationDef)(nil),         // 62: moego.models.organization.v1.CreateLocationDef
	(*v1.UpdateLocationDef)(nil),         // 63: moego.models.organization.v1.UpdateLocationDef
	(*v2.PaginationRequest)(nil),         // 64: moego.utils.v2.PaginationRequest
	(*v1.LocationBriefView)(nil),         // 65: moego.models.organization.v1.LocationBriefView
	(*v2.PaginationResponse)(nil),        // 66: moego.utils.v2.PaginationResponse
	(*v1.LocationModel)(nil),             // 67: moego.models.organization.v1.LocationModel
	(*v1.UpdateOnlinePreferenceDef)(nil), // 68: moego.models.organization.v1.UpdateOnlinePreferenceDef
	(*v1.WorkingHoursDef)(nil),           // 69: moego.models.organization.v1.WorkingHoursDef
	(*v1.CloseDateDef)(nil),              // 70: moego.models.organization.v1.CloseDateDef
	(*v1.ClosedHolidayMassEditDef)(nil),  // 71: moego.models.organization.v1.ClosedHolidayMassEditDef
	(*v1.LocationDateTimeDef)(nil),       // 72: moego.models.organization.v1.LocationDateTimeDef
	(*v11.BusinessModel)(nil),            // 73: moego.models.business.v1.BusinessModel
	(*v2.Int64List)(nil),                 // 74: moego.utils.v2.Int64List
	(*v1.PaymentMethodListDef)(nil),      // 75: moego.models.organization.v1.PaymentMethodListDef
}
var file_moego_service_organization_v1_business_service_proto_depIdxs = []int32{
	52, // 0: moego.service.organization.v1.BatchGetCompanyIdResponse.business_company_id_map:type_name -> moego.service.organization.v1.BatchGetCompanyIdResponse.BusinessCompanyIdMapEntry
	62, // 1: moego.service.organization.v1.CreateLocationRequest.location:type_name -> moego.models.organization.v1.CreateLocationDef
	63, // 2: moego.service.organization.v1.UpdateLocationRequest.location:type_name -> moego.models.organization.v1.UpdateLocationDef
	64, // 3: moego.service.organization.v1.GetLocationListRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	65, // 4: moego.service.organization.v1.GetLocationListResponse.location:type_name -> moego.models.organization.v1.LocationBriefView
	66, // 5: moego.service.organization.v1.GetLocationListResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	67, // 6: moego.service.organization.v1.GetLocationDetailResponse.location:type_name -> moego.models.organization.v1.LocationModel
	68, // 7: moego.service.organization.v1.UpdateOnlinePreferenceRequest.online_preference:type_name -> moego.models.organization.v1.UpdateOnlinePreferenceDef
	69, // 8: moego.service.organization.v1.GetWorkingHourListResponse.working_hour:type_name -> moego.models.organization.v1.WorkingHoursDef
	69, // 9: moego.service.organization.v1.UpdateWorkingHourRequest.working_hour:type_name -> moego.models.organization.v1.WorkingHoursDef
	70, // 10: moego.service.organization.v1.AddClosedDateRequest.closed_date:type_name -> moego.models.organization.v1.CloseDateDef
	70, // 11: moego.service.organization.v1.UpdateClosedDateRequest.closed_date:type_name -> moego.models.organization.v1.CloseDateDef
	70, // 12: moego.service.organization.v1.GetClosedDateListResponse.closed_date:type_name -> moego.models.organization.v1.CloseDateDef
	70, // 13: moego.service.organization.v1.GetClosedHolidayListResponse.closed_date:type_name -> moego.models.organization.v1.CloseDateDef
	71, // 14: moego.service.organization.v1.MassEditClosedHolidayRequest.closed_holiday:type_name -> moego.models.organization.v1.ClosedHolidayMassEditDef
	53, // 15: moego.service.organization.v1.GetWorkingLocationListByCompaniesStaffRequest.params:type_name -> moego.service.organization.v1.GetWorkingLocationListByCompaniesStaffRequest.Param
	55, // 16: moego.service.organization.v1.GetWorkingLocationListByCompaniesStaffResponse.company_locations_map:type_name -> moego.service.organization.v1.GetWorkingLocationListByCompaniesStaffResponse.CompanyLocationsMapEntry
	56, // 17: moego.service.organization.v1.GetWorkingLocationIdsByStaffIdsResponse.staff_location_ids:type_name -> moego.service.organization.v1.GetWorkingLocationIdsByStaffIdsResponse.StaffLocationIds
	72, // 18: moego.service.organization.v1.GetWorkingLocationDateTimeResponse.location_date_time:type_name -> moego.models.organization.v1.LocationDateTimeDef
	57, // 19: moego.service.organization.v1.BatchGetLocationIdListResponse.company_location_id_list_map:type_name -> moego.service.organization.v1.BatchGetLocationIdListResponse.CompanyLocationIdListMapEntry
	73, // 20: moego.service.organization.v1.GetBusinessInfoWithOwnerEmailResponse.business:type_name -> moego.models.business.v1.BusinessModel
	58, // 21: moego.service.organization.v1.BatchGetBusinessOwnerAccountIdResponse.business_owner_account_id_map:type_name -> moego.service.organization.v1.BatchGetBusinessOwnerAccountIdResponse.BusinessOwnerAccountIdMapEntry
	67, // 22: moego.service.organization.v1.GetLocationListByEnterpriseStaffResponse.locations:type_name -> moego.models.organization.v1.LocationModel
	59, // 23: moego.service.organization.v1.ListPaymentMethodsResponse.business_payment_methods_map:type_name -> moego.service.organization.v1.ListPaymentMethodsResponse.BusinessPaymentMethodsMapEntry
	60, // 24: moego.service.organization.v1.ListBusinessWorkingHoursResponse.business_working_hours_map:type_name -> moego.service.organization.v1.ListBusinessWorkingHoursResponse.BusinessWorkingHoursMapEntry
	64, // 25: moego.service.organization.v1.ListLocationsRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	61, // 26: moego.service.organization.v1.ListLocationsRequest.filter:type_name -> moego.service.organization.v1.ListLocationsRequest.Filter
	66, // 27: moego.service.organization.v1.ListLocationsResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	67, // 28: moego.service.organization.v1.ListLocationsResponse.locations:type_name -> moego.models.organization.v1.LocationModel
	67, // 29: moego.service.organization.v1.GetWorkingLocationListByCompaniesStaffResponse.LocationList.locations:type_name -> moego.models.organization.v1.LocationModel
	54, // 30: moego.service.organization.v1.GetWorkingLocationListByCompaniesStaffResponse.CompanyLocationsMapEntry.value:type_name -> moego.service.organization.v1.GetWorkingLocationListByCompaniesStaffResponse.LocationList
	74, // 31: moego.service.organization.v1.BatchGetLocationIdListResponse.CompanyLocationIdListMapEntry.value:type_name -> moego.utils.v2.Int64List
	75, // 32: moego.service.organization.v1.ListPaymentMethodsResponse.BusinessPaymentMethodsMapEntry.value:type_name -> moego.models.organization.v1.PaymentMethodListDef
	69, // 33: moego.service.organization.v1.ListBusinessWorkingHoursResponse.BusinessWorkingHoursMapEntry.value:type_name -> moego.models.organization.v1.WorkingHoursDef
	0,  // 34: moego.service.organization.v1.BusinessService.GetCompanyId:input_type -> moego.service.organization.v1.GetCompanyIdRequest
	2,  // 35: moego.service.organization.v1.BusinessService.BatchGetCompanyId:input_type -> moego.service.organization.v1.BatchGetCompanyIdRequest
	4,  // 36: moego.service.organization.v1.BusinessService.CreateLocation:input_type -> moego.service.organization.v1.CreateLocationRequest
	6,  // 37: moego.service.organization.v1.BusinessService.UpdateLocation:input_type -> moego.service.organization.v1.UpdateLocationRequest
	8,  // 38: moego.service.organization.v1.BusinessService.GetLocationList:input_type -> moego.service.organization.v1.GetLocationListRequest
	10, // 39: moego.service.organization.v1.BusinessService.GetLocationDetail:input_type -> moego.service.organization.v1.GetLocationDetailRequest
	12, // 40: moego.service.organization.v1.BusinessService.UpdateOnlinePreference:input_type -> moego.service.organization.v1.UpdateOnlinePreferenceRequest
	14, // 41: moego.service.organization.v1.BusinessService.GetWorkingHourList:input_type -> moego.service.organization.v1.GetWorkingHourListRequest
	16, // 42: moego.service.organization.v1.BusinessService.UpdateWorkingHour:input_type -> moego.service.organization.v1.UpdateWorkingHourRequest
	18, // 43: moego.service.organization.v1.BusinessService.AddClosedDate:input_type -> moego.service.organization.v1.AddClosedDateRequest
	20, // 44: moego.service.organization.v1.BusinessService.UpdateClosedDate:input_type -> moego.service.organization.v1.UpdateClosedDateRequest
	22, // 45: moego.service.organization.v1.BusinessService.DeleteClosedDate:input_type -> moego.service.organization.v1.DeleteClosedDateRequest
	24, // 46: moego.service.organization.v1.BusinessService.GetClosedDateList:input_type -> moego.service.organization.v1.GetClosedDateListRequest
	26, // 47: moego.service.organization.v1.BusinessService.GetClosedHolidayList:input_type -> moego.service.organization.v1.GetClosedHolidayListRequest
	28, // 48: moego.service.organization.v1.BusinessService.EditClosedHoliday:input_type -> moego.service.organization.v1.EditClosedHolidayRequest
	30, // 49: moego.service.organization.v1.BusinessService.MassEditClosedHoliday:input_type -> moego.service.organization.v1.MassEditClosedHolidayRequest
	32, // 50: moego.service.organization.v1.BusinessService.GetWorkingLocationListByCompaniesStaff:input_type -> moego.service.organization.v1.GetWorkingLocationListByCompaniesStaffRequest
	34, // 51: moego.service.organization.v1.BusinessService.GetWorkingLocationIdsByStaffIds:input_type -> moego.service.organization.v1.GetWorkingLocationIdsByStaffIdsRequest
	36, // 52: moego.service.organization.v1.BusinessService.GetWorkingLocationDateTime:input_type -> moego.service.organization.v1.GetWorkingLocationDateTimeRequest
	38, // 53: moego.service.organization.v1.BusinessService.BatchGetLocationIdList:input_type -> moego.service.organization.v1.BatchGetLocationIdListRequest
	40, // 54: moego.service.organization.v1.BusinessService.GetBusinessInfoWithOwnerEmail:input_type -> moego.service.organization.v1.GetBusinessInfoWithOwnerEmailRequest
	42, // 55: moego.service.organization.v1.BusinessService.BatchGetBusinessOwnerAccountId:input_type -> moego.service.organization.v1.BatchGetBusinessOwnerAccountIdRequest
	44, // 56: moego.service.organization.v1.BusinessService.GetLocationListByEnterpriseStaff:input_type -> moego.service.organization.v1.GetLocationListByEnterpriseStaffRequest
	46, // 57: moego.service.organization.v1.BusinessService.ListPaymentMethods:input_type -> moego.service.organization.v1.ListPaymentMethodsRequest
	48, // 58: moego.service.organization.v1.BusinessService.ListBusinessWorkingHours:input_type -> moego.service.organization.v1.ListBusinessWorkingHoursRequest
	50, // 59: moego.service.organization.v1.BusinessService.ListLocations:input_type -> moego.service.organization.v1.ListLocationsRequest
	1,  // 60: moego.service.organization.v1.BusinessService.GetCompanyId:output_type -> moego.service.organization.v1.GetCompanyIdResponse
	3,  // 61: moego.service.organization.v1.BusinessService.BatchGetCompanyId:output_type -> moego.service.organization.v1.BatchGetCompanyIdResponse
	5,  // 62: moego.service.organization.v1.BusinessService.CreateLocation:output_type -> moego.service.organization.v1.CreateLocationResponse
	7,  // 63: moego.service.organization.v1.BusinessService.UpdateLocation:output_type -> moego.service.organization.v1.UpdateLocationResponse
	9,  // 64: moego.service.organization.v1.BusinessService.GetLocationList:output_type -> moego.service.organization.v1.GetLocationListResponse
	11, // 65: moego.service.organization.v1.BusinessService.GetLocationDetail:output_type -> moego.service.organization.v1.GetLocationDetailResponse
	13, // 66: moego.service.organization.v1.BusinessService.UpdateOnlinePreference:output_type -> moego.service.organization.v1.UpdateOnlinePreferenceResponse
	15, // 67: moego.service.organization.v1.BusinessService.GetWorkingHourList:output_type -> moego.service.organization.v1.GetWorkingHourListResponse
	17, // 68: moego.service.organization.v1.BusinessService.UpdateWorkingHour:output_type -> moego.service.organization.v1.UpdateWorkingHourResponse
	19, // 69: moego.service.organization.v1.BusinessService.AddClosedDate:output_type -> moego.service.organization.v1.AddClosedDateResponse
	21, // 70: moego.service.organization.v1.BusinessService.UpdateClosedDate:output_type -> moego.service.organization.v1.UpdateClosedDateResponse
	23, // 71: moego.service.organization.v1.BusinessService.DeleteClosedDate:output_type -> moego.service.organization.v1.DeleteClosedDateResponse
	25, // 72: moego.service.organization.v1.BusinessService.GetClosedDateList:output_type -> moego.service.organization.v1.GetClosedDateListResponse
	27, // 73: moego.service.organization.v1.BusinessService.GetClosedHolidayList:output_type -> moego.service.organization.v1.GetClosedHolidayListResponse
	29, // 74: moego.service.organization.v1.BusinessService.EditClosedHoliday:output_type -> moego.service.organization.v1.EditClosedHolidayResponse
	31, // 75: moego.service.organization.v1.BusinessService.MassEditClosedHoliday:output_type -> moego.service.organization.v1.MassEditClosedHolidayResponse
	33, // 76: moego.service.organization.v1.BusinessService.GetWorkingLocationListByCompaniesStaff:output_type -> moego.service.organization.v1.GetWorkingLocationListByCompaniesStaffResponse
	35, // 77: moego.service.organization.v1.BusinessService.GetWorkingLocationIdsByStaffIds:output_type -> moego.service.organization.v1.GetWorkingLocationIdsByStaffIdsResponse
	37, // 78: moego.service.organization.v1.BusinessService.GetWorkingLocationDateTime:output_type -> moego.service.organization.v1.GetWorkingLocationDateTimeResponse
	39, // 79: moego.service.organization.v1.BusinessService.BatchGetLocationIdList:output_type -> moego.service.organization.v1.BatchGetLocationIdListResponse
	41, // 80: moego.service.organization.v1.BusinessService.GetBusinessInfoWithOwnerEmail:output_type -> moego.service.organization.v1.GetBusinessInfoWithOwnerEmailResponse
	43, // 81: moego.service.organization.v1.BusinessService.BatchGetBusinessOwnerAccountId:output_type -> moego.service.organization.v1.BatchGetBusinessOwnerAccountIdResponse
	45, // 82: moego.service.organization.v1.BusinessService.GetLocationListByEnterpriseStaff:output_type -> moego.service.organization.v1.GetLocationListByEnterpriseStaffResponse
	47, // 83: moego.service.organization.v1.BusinessService.ListPaymentMethods:output_type -> moego.service.organization.v1.ListPaymentMethodsResponse
	49, // 84: moego.service.organization.v1.BusinessService.ListBusinessWorkingHours:output_type -> moego.service.organization.v1.ListBusinessWorkingHoursResponse
	51, // 85: moego.service.organization.v1.BusinessService.ListLocations:output_type -> moego.service.organization.v1.ListLocationsResponse
	60, // [60:86] is the sub-list for method output_type
	34, // [34:60] is the sub-list for method input_type
	34, // [34:34] is the sub-list for extension type_name
	34, // [34:34] is the sub-list for extension extendee
	0,  // [0:34] is the sub-list for field type_name
}

func init() { file_moego_service_organization_v1_business_service_proto_init() }
func file_moego_service_organization_v1_business_service_proto_init() {
	if File_moego_service_organization_v1_business_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_organization_v1_business_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompanyIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompanyIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetCompanyIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetCompanyIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLocationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLocationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLocationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLocationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLocationListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLocationListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLocationDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLocationDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOnlinePreferenceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOnlinePreferenceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkingHourListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkingHourListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWorkingHourRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWorkingHourResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddClosedDateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddClosedDateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateClosedDateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateClosedDateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteClosedDateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteClosedDateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClosedDateListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClosedDateListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClosedHolidayListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClosedHolidayListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditClosedHolidayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditClosedHolidayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MassEditClosedHolidayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MassEditClosedHolidayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkingLocationListByCompaniesStaffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkingLocationListByCompaniesStaffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkingLocationIdsByStaffIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkingLocationIdsByStaffIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkingLocationDateTimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkingLocationDateTimeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetLocationIdListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetLocationIdListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessInfoWithOwnerEmailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessInfoWithOwnerEmailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetBusinessOwnerAccountIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetBusinessOwnerAccountIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLocationListByEnterpriseStaffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLocationListByEnterpriseStaffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPaymentMethodsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPaymentMethodsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBusinessWorkingHoursRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBusinessWorkingHoursResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLocationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLocationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkingLocationListByCompaniesStaffRequest_Param); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkingLocationListByCompaniesStaffResponse_LocationList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkingLocationIdsByStaffIdsResponse_StaffLocationIds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_business_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLocationsRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_organization_v1_business_service_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*CreateLocationRequest_TokenStaffId)(nil),
		(*CreateLocationRequest_InternalOperatorId)(nil),
	}
	file_moego_service_organization_v1_business_service_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*UpdateLocationRequest_TokenStaffId)(nil),
		(*UpdateLocationRequest_InternalOperatorId)(nil),
	}
	file_moego_service_organization_v1_business_service_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_business_service_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_business_service_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_business_service_proto_msgTypes[12].OneofWrappers = []interface{}{
		(*UpdateOnlinePreferenceRequest_TokenStaffId)(nil),
		(*UpdateOnlinePreferenceRequest_InternalOperatorId)(nil),
	}
	file_moego_service_organization_v1_business_service_proto_msgTypes[16].OneofWrappers = []interface{}{
		(*UpdateWorkingHourRequest_TokenStaffId)(nil),
		(*UpdateWorkingHourRequest_InternalOperatorId)(nil),
	}
	file_moego_service_organization_v1_business_service_proto_msgTypes[18].OneofWrappers = []interface{}{
		(*AddClosedDateRequest_TokenStaffId)(nil),
		(*AddClosedDateRequest_InternalOperatorId)(nil),
	}
	file_moego_service_organization_v1_business_service_proto_msgTypes[20].OneofWrappers = []interface{}{
		(*UpdateClosedDateRequest_TokenStaffId)(nil),
		(*UpdateClosedDateRequest_InternalOperatorId)(nil),
	}
	file_moego_service_organization_v1_business_service_proto_msgTypes[22].OneofWrappers = []interface{}{
		(*DeleteClosedDateRequest_TokenStaffId)(nil),
		(*DeleteClosedDateRequest_InternalOperatorId)(nil),
	}
	file_moego_service_organization_v1_business_service_proto_msgTypes[24].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_business_service_proto_msgTypes[26].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_business_service_proto_msgTypes[28].OneofWrappers = []interface{}{
		(*EditClosedHolidayRequest_TokenStaffId)(nil),
		(*EditClosedHolidayRequest_InternalOperatorId)(nil),
	}
	file_moego_service_organization_v1_business_service_proto_msgTypes[30].OneofWrappers = []interface{}{
		(*MassEditClosedHolidayRequest_TokenStaffId)(nil),
		(*MassEditClosedHolidayRequest_InternalOperatorId)(nil),
	}
	file_moego_service_organization_v1_business_service_proto_msgTypes[46].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_organization_v1_business_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   62,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_organization_v1_business_service_proto_goTypes,
		DependencyIndexes: file_moego_service_organization_v1_business_service_proto_depIdxs,
		MessageInfos:      file_moego_service_organization_v1_business_service_proto_msgTypes,
	}.Build()
	File_moego_service_organization_v1_business_service_proto = out.File
	file_moego_service_organization_v1_business_service_proto_rawDesc = nil
	file_moego_service_organization_v1_business_service_proto_goTypes = nil
	file_moego_service_organization_v1_business_service_proto_depIdxs = nil
}
