// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/organization/v1/tax_service.proto

package organizationsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// TaxRuleServiceClient is the client API for TaxRuleService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TaxRuleServiceClient interface {
	// add tax rule
	CreateTaxRule(ctx context.Context, in *CreateTaxRuleRequest, opts ...grpc.CallOption) (*CreateTaxRuleResponse, error)
	// update tax rule
	UpdateTaxRuleV2(ctx context.Context, in *UpdateTaxRuleV2Request, opts ...grpc.CallOption) (*UpdateTaxRuleV2Response, error)
	// delete tax rule
	DeleteTaxRuleV2(ctx context.Context, in *DeleteTaxRuleV2Request, opts ...grpc.CallOption) (*DeleteTaxRuleV2Response, error)
	// list tax rule
	ListTaxRule(ctx context.Context, in *ListTaxRuleRequest, opts ...grpc.CallOption) (*ListTaxRuleResponse, error)
	// get tax rule
	GetTaxRule(ctx context.Context, in *GetTaxRuleRequest, opts ...grpc.CallOption) (*GetTaxRuleResponse, error)
	// get tax rule
	BatchGetTaxRule(ctx context.Context, in *BatchGetTaxRuleRequest, opts ...grpc.CallOption) (*BatchGetTaxRuleResponse, error)
}

type taxRuleServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTaxRuleServiceClient(cc grpc.ClientConnInterface) TaxRuleServiceClient {
	return &taxRuleServiceClient{cc}
}

func (c *taxRuleServiceClient) CreateTaxRule(ctx context.Context, in *CreateTaxRuleRequest, opts ...grpc.CallOption) (*CreateTaxRuleResponse, error) {
	out := new(CreateTaxRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.TaxRuleService/CreateTaxRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *taxRuleServiceClient) UpdateTaxRuleV2(ctx context.Context, in *UpdateTaxRuleV2Request, opts ...grpc.CallOption) (*UpdateTaxRuleV2Response, error) {
	out := new(UpdateTaxRuleV2Response)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.TaxRuleService/UpdateTaxRuleV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *taxRuleServiceClient) DeleteTaxRuleV2(ctx context.Context, in *DeleteTaxRuleV2Request, opts ...grpc.CallOption) (*DeleteTaxRuleV2Response, error) {
	out := new(DeleteTaxRuleV2Response)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.TaxRuleService/DeleteTaxRuleV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *taxRuleServiceClient) ListTaxRule(ctx context.Context, in *ListTaxRuleRequest, opts ...grpc.CallOption) (*ListTaxRuleResponse, error) {
	out := new(ListTaxRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.TaxRuleService/ListTaxRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *taxRuleServiceClient) GetTaxRule(ctx context.Context, in *GetTaxRuleRequest, opts ...grpc.CallOption) (*GetTaxRuleResponse, error) {
	out := new(GetTaxRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.TaxRuleService/GetTaxRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *taxRuleServiceClient) BatchGetTaxRule(ctx context.Context, in *BatchGetTaxRuleRequest, opts ...grpc.CallOption) (*BatchGetTaxRuleResponse, error) {
	out := new(BatchGetTaxRuleResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.TaxRuleService/BatchGetTaxRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TaxRuleServiceServer is the server API for TaxRuleService service.
// All implementations must embed UnimplementedTaxRuleServiceServer
// for forward compatibility
type TaxRuleServiceServer interface {
	// add tax rule
	CreateTaxRule(context.Context, *CreateTaxRuleRequest) (*CreateTaxRuleResponse, error)
	// update tax rule
	UpdateTaxRuleV2(context.Context, *UpdateTaxRuleV2Request) (*UpdateTaxRuleV2Response, error)
	// delete tax rule
	DeleteTaxRuleV2(context.Context, *DeleteTaxRuleV2Request) (*DeleteTaxRuleV2Response, error)
	// list tax rule
	ListTaxRule(context.Context, *ListTaxRuleRequest) (*ListTaxRuleResponse, error)
	// get tax rule
	GetTaxRule(context.Context, *GetTaxRuleRequest) (*GetTaxRuleResponse, error)
	// get tax rule
	BatchGetTaxRule(context.Context, *BatchGetTaxRuleRequest) (*BatchGetTaxRuleResponse, error)
	mustEmbedUnimplementedTaxRuleServiceServer()
}

// UnimplementedTaxRuleServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTaxRuleServiceServer struct {
}

func (UnimplementedTaxRuleServiceServer) CreateTaxRule(context.Context, *CreateTaxRuleRequest) (*CreateTaxRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTaxRule not implemented")
}
func (UnimplementedTaxRuleServiceServer) UpdateTaxRuleV2(context.Context, *UpdateTaxRuleV2Request) (*UpdateTaxRuleV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTaxRuleV2 not implemented")
}
func (UnimplementedTaxRuleServiceServer) DeleteTaxRuleV2(context.Context, *DeleteTaxRuleV2Request) (*DeleteTaxRuleV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTaxRuleV2 not implemented")
}
func (UnimplementedTaxRuleServiceServer) ListTaxRule(context.Context, *ListTaxRuleRequest) (*ListTaxRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTaxRule not implemented")
}
func (UnimplementedTaxRuleServiceServer) GetTaxRule(context.Context, *GetTaxRuleRequest) (*GetTaxRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaxRule not implemented")
}
func (UnimplementedTaxRuleServiceServer) BatchGetTaxRule(context.Context, *BatchGetTaxRuleRequest) (*BatchGetTaxRuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetTaxRule not implemented")
}
func (UnimplementedTaxRuleServiceServer) mustEmbedUnimplementedTaxRuleServiceServer() {}

// UnsafeTaxRuleServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TaxRuleServiceServer will
// result in compilation errors.
type UnsafeTaxRuleServiceServer interface {
	mustEmbedUnimplementedTaxRuleServiceServer()
}

func RegisterTaxRuleServiceServer(s grpc.ServiceRegistrar, srv TaxRuleServiceServer) {
	s.RegisterService(&TaxRuleService_ServiceDesc, srv)
}

func _TaxRuleService_CreateTaxRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTaxRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaxRuleServiceServer).CreateTaxRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.TaxRuleService/CreateTaxRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaxRuleServiceServer).CreateTaxRule(ctx, req.(*CreateTaxRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TaxRuleService_UpdateTaxRuleV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTaxRuleV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaxRuleServiceServer).UpdateTaxRuleV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.TaxRuleService/UpdateTaxRuleV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaxRuleServiceServer).UpdateTaxRuleV2(ctx, req.(*UpdateTaxRuleV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _TaxRuleService_DeleteTaxRuleV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTaxRuleV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaxRuleServiceServer).DeleteTaxRuleV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.TaxRuleService/DeleteTaxRuleV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaxRuleServiceServer).DeleteTaxRuleV2(ctx, req.(*DeleteTaxRuleV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _TaxRuleService_ListTaxRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTaxRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaxRuleServiceServer).ListTaxRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.TaxRuleService/ListTaxRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaxRuleServiceServer).ListTaxRule(ctx, req.(*ListTaxRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TaxRuleService_GetTaxRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTaxRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaxRuleServiceServer).GetTaxRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.TaxRuleService/GetTaxRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaxRuleServiceServer).GetTaxRule(ctx, req.(*GetTaxRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TaxRuleService_BatchGetTaxRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetTaxRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaxRuleServiceServer).BatchGetTaxRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.TaxRuleService/BatchGetTaxRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaxRuleServiceServer).BatchGetTaxRule(ctx, req.(*BatchGetTaxRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TaxRuleService_ServiceDesc is the grpc.ServiceDesc for TaxRuleService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TaxRuleService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.organization.v1.TaxRuleService",
	HandlerType: (*TaxRuleServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateTaxRule",
			Handler:    _TaxRuleService_CreateTaxRule_Handler,
		},
		{
			MethodName: "UpdateTaxRuleV2",
			Handler:    _TaxRuleService_UpdateTaxRuleV2_Handler,
		},
		{
			MethodName: "DeleteTaxRuleV2",
			Handler:    _TaxRuleService_DeleteTaxRuleV2_Handler,
		},
		{
			MethodName: "ListTaxRule",
			Handler:    _TaxRuleService_ListTaxRule_Handler,
		},
		{
			MethodName: "GetTaxRule",
			Handler:    _TaxRuleService_GetTaxRule_Handler,
		},
		{
			MethodName: "BatchGetTaxRule",
			Handler:    _TaxRuleService_BatchGetTaxRule_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/organization/v1/tax_service.proto",
}
