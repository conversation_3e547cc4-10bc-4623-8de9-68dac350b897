// @since 2023-09-05 17:03:16
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/risk_control/v1/recaptcha_service.proto

package riskcontrolsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/risk_control/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// challenge input
type RecaptchaChallengeInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the recaptcha challenge def
	RecaptchaDef *v1.RecaptchaDef `protobuf:"bytes,1,opt,name=recaptcha_def,json=recaptchaDef,proto3" json:"recaptcha_def,omitempty"`
	// ob session id
	SessionId int64 `protobuf:"varint,2,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
}

func (x *RecaptchaChallengeInput) Reset() {
	*x = RecaptchaChallengeInput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecaptchaChallengeInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecaptchaChallengeInput) ProtoMessage() {}

func (x *RecaptchaChallengeInput) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecaptchaChallengeInput.ProtoReflect.Descriptor instead.
func (*RecaptchaChallengeInput) Descriptor() ([]byte, []int) {
	return file_moego_service_risk_control_v1_recaptcha_service_proto_rawDescGZIP(), []int{0}
}

func (x *RecaptchaChallengeInput) GetRecaptchaDef() *v1.RecaptchaDef {
	if x != nil {
		return x.RecaptchaDef
	}
	return nil
}

func (x *RecaptchaChallengeInput) GetSessionId() int64 {
	if x != nil {
		return x.SessionId
	}
	return 0
}

// challenge output
type RecaptchaChallengeOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// risk control token
	RiskControlToken string `protobuf:"bytes,1,opt,name=risk_control_token,json=riskControlToken,proto3" json:"risk_control_token,omitempty"`
}

func (x *RecaptchaChallengeOutput) Reset() {
	*x = RecaptchaChallengeOutput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecaptchaChallengeOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecaptchaChallengeOutput) ProtoMessage() {}

func (x *RecaptchaChallengeOutput) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecaptchaChallengeOutput.ProtoReflect.Descriptor instead.
func (*RecaptchaChallengeOutput) Descriptor() ([]byte, []int) {
	return file_moego_service_risk_control_v1_recaptcha_service_proto_rawDescGZIP(), []int{1}
}

func (x *RecaptchaChallengeOutput) GetRiskControlToken() string {
	if x != nil {
		return x.RiskControlToken
	}
	return ""
}

// risk control verify input
type RiskControlVerifyInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// risk control def
	RiskControl *v1.RiskControlDef `protobuf:"bytes,1,opt,name=risk_control,json=riskControl,proto3" json:"risk_control,omitempty"`
	// verify method
	//
	// Types that are assignable to VerifyMethod:
	//
	//	*RiskControlVerifyInput_Recaptcha
	VerifyMethod isRiskControlVerifyInput_VerifyMethod `protobuf_oneof:"verify_method"`
}

func (x *RiskControlVerifyInput) Reset() {
	*x = RiskControlVerifyInput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskControlVerifyInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskControlVerifyInput) ProtoMessage() {}

func (x *RiskControlVerifyInput) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskControlVerifyInput.ProtoReflect.Descriptor instead.
func (*RiskControlVerifyInput) Descriptor() ([]byte, []int) {
	return file_moego_service_risk_control_v1_recaptcha_service_proto_rawDescGZIP(), []int{2}
}

func (x *RiskControlVerifyInput) GetRiskControl() *v1.RiskControlDef {
	if x != nil {
		return x.RiskControl
	}
	return nil
}

func (m *RiskControlVerifyInput) GetVerifyMethod() isRiskControlVerifyInput_VerifyMethod {
	if m != nil {
		return m.VerifyMethod
	}
	return nil
}

func (x *RiskControlVerifyInput) GetRecaptcha() *RecaptchaInput {
	if x, ok := x.GetVerifyMethod().(*RiskControlVerifyInput_Recaptcha); ok {
		return x.Recaptcha
	}
	return nil
}

type isRiskControlVerifyInput_VerifyMethod interface {
	isRiskControlVerifyInput_VerifyMethod()
}

type RiskControlVerifyInput_Recaptcha struct {
	// google recaptcha input
	Recaptcha *RecaptchaInput `protobuf:"bytes,2,opt,name=recaptcha,proto3,oneof"`
}

func (*RiskControlVerifyInput_Recaptcha) isRiskControlVerifyInput_VerifyMethod() {}

// google recaptcha input
type RecaptchaInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// session id
	SessionId int64 `protobuf:"varint,1,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	// recaptcha action
	Action v1.RecaptchaAction `protobuf:"varint,2,opt,name=action,proto3,enum=moego.models.risk_control.v1.RecaptchaAction" json:"action,omitempty"`
	// recaptcha applicable version
	Versions []v1.RecaptchaVersion `protobuf:"varint,3,rep,packed,name=versions,proto3,enum=moego.models.risk_control.v1.RecaptchaVersion" json:"versions,omitempty"`
}

func (x *RecaptchaInput) Reset() {
	*x = RecaptchaInput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecaptchaInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecaptchaInput) ProtoMessage() {}

func (x *RecaptchaInput) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecaptchaInput.ProtoReflect.Descriptor instead.
func (*RecaptchaInput) Descriptor() ([]byte, []int) {
	return file_moego_service_risk_control_v1_recaptcha_service_proto_rawDescGZIP(), []int{3}
}

func (x *RecaptchaInput) GetSessionId() int64 {
	if x != nil {
		return x.SessionId
	}
	return 0
}

func (x *RecaptchaInput) GetAction() v1.RecaptchaAction {
	if x != nil {
		return x.Action
	}
	return v1.RecaptchaAction(0)
}

func (x *RecaptchaInput) GetVersions() []v1.RecaptchaVersion {
	if x != nil {
		return x.Versions
	}
	return nil
}

// risk control verify output
type RiskControlVerifyOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// verify result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *RiskControlVerifyOutput) Reset() {
	*x = RiskControlVerifyOutput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskControlVerifyOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskControlVerifyOutput) ProtoMessage() {}

func (x *RiskControlVerifyOutput) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskControlVerifyOutput.ProtoReflect.Descriptor instead.
func (*RiskControlVerifyOutput) Descriptor() ([]byte, []int) {
	return file_moego_service_risk_control_v1_recaptcha_service_proto_rawDescGZIP(), []int{4}
}

func (x *RiskControlVerifyOutput) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_moego_service_risk_control_v1_recaptcha_service_proto protoreflect.FileDescriptor

var file_moego_service_risk_control_v1_recaptcha_service_proto_rawDesc = []byte{
	0x0a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x2f,
	0x72, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x5f, 0x64,
	0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68,
	0x61, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x93, 0x01, 0x0a,
	0x17, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x43, 0x68, 0x61, 0x6c, 0x6c, 0x65,
	0x6e, 0x67, 0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x59, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61,
	0x44, 0x65, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x22, 0x48, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x43,
	0x68, 0x61, 0x6c, 0x6c, 0x65, 0x6e, 0x67, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x2c,
	0x0a, 0x12, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x69, 0x73, 0x6b,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xd8, 0x01, 0x0a,
	0x16, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x59, 0x0a, 0x0c, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x69, 0x73,
	0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x72, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x12, 0x4d, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x48, 0x00, 0x52, 0x09, 0x72, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68,
	0x61, 0x42, 0x14, 0x0a, 0x0d, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xc2, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x63, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63,
	0x68, 0x61, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x4a, 0x0a, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x33, 0x0a, 0x17,
	0x52, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x32, 0x89, 0x02, 0x0a, 0x10, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7c, 0x0a, 0x09, 0x43, 0x68, 0x61, 0x6c, 0x6c, 0x65,
	0x6e, 0x67, 0x65, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x43, 0x68, 0x61,
	0x6c, 0x6c, 0x65, 0x6e, 0x67, 0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x61,
	0x70, 0x74, 0x63, 0x68, 0x61, 0x43, 0x68, 0x61, 0x6c, 0x6c, 0x65, 0x6e, 0x67, 0x65, 0x4f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x12, 0x77, 0x0a, 0x06, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x12, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x69, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x42, 0x8e, 0x01,
	0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x3b, 0x72, 0x69,
	0x73, 0x6b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_risk_control_v1_recaptcha_service_proto_rawDescOnce sync.Once
	file_moego_service_risk_control_v1_recaptcha_service_proto_rawDescData = file_moego_service_risk_control_v1_recaptcha_service_proto_rawDesc
)

func file_moego_service_risk_control_v1_recaptcha_service_proto_rawDescGZIP() []byte {
	file_moego_service_risk_control_v1_recaptcha_service_proto_rawDescOnce.Do(func() {
		file_moego_service_risk_control_v1_recaptcha_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_risk_control_v1_recaptcha_service_proto_rawDescData)
	})
	return file_moego_service_risk_control_v1_recaptcha_service_proto_rawDescData
}

var file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_service_risk_control_v1_recaptcha_service_proto_goTypes = []interface{}{
	(*RecaptchaChallengeInput)(nil),  // 0: moego.service.risk_control.v1.RecaptchaChallengeInput
	(*RecaptchaChallengeOutput)(nil), // 1: moego.service.risk_control.v1.RecaptchaChallengeOutput
	(*RiskControlVerifyInput)(nil),   // 2: moego.service.risk_control.v1.RiskControlVerifyInput
	(*RecaptchaInput)(nil),           // 3: moego.service.risk_control.v1.RecaptchaInput
	(*RiskControlVerifyOutput)(nil),  // 4: moego.service.risk_control.v1.RiskControlVerifyOutput
	(*v1.RecaptchaDef)(nil),          // 5: moego.models.risk_control.v1.RecaptchaDef
	(*v1.RiskControlDef)(nil),        // 6: moego.models.risk_control.v1.RiskControlDef
	(v1.RecaptchaAction)(0),          // 7: moego.models.risk_control.v1.RecaptchaAction
	(v1.RecaptchaVersion)(0),         // 8: moego.models.risk_control.v1.RecaptchaVersion
}
var file_moego_service_risk_control_v1_recaptcha_service_proto_depIdxs = []int32{
	5, // 0: moego.service.risk_control.v1.RecaptchaChallengeInput.recaptcha_def:type_name -> moego.models.risk_control.v1.RecaptchaDef
	6, // 1: moego.service.risk_control.v1.RiskControlVerifyInput.risk_control:type_name -> moego.models.risk_control.v1.RiskControlDef
	3, // 2: moego.service.risk_control.v1.RiskControlVerifyInput.recaptcha:type_name -> moego.service.risk_control.v1.RecaptchaInput
	7, // 3: moego.service.risk_control.v1.RecaptchaInput.action:type_name -> moego.models.risk_control.v1.RecaptchaAction
	8, // 4: moego.service.risk_control.v1.RecaptchaInput.versions:type_name -> moego.models.risk_control.v1.RecaptchaVersion
	0, // 5: moego.service.risk_control.v1.RecaptchaService.Challenge:input_type -> moego.service.risk_control.v1.RecaptchaChallengeInput
	2, // 6: moego.service.risk_control.v1.RecaptchaService.Verify:input_type -> moego.service.risk_control.v1.RiskControlVerifyInput
	1, // 7: moego.service.risk_control.v1.RecaptchaService.Challenge:output_type -> moego.service.risk_control.v1.RecaptchaChallengeOutput
	4, // 8: moego.service.risk_control.v1.RecaptchaService.Verify:output_type -> moego.service.risk_control.v1.RiskControlVerifyOutput
	7, // [7:9] is the sub-list for method output_type
	5, // [5:7] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_service_risk_control_v1_recaptcha_service_proto_init() }
func file_moego_service_risk_control_v1_recaptcha_service_proto_init() {
	if File_moego_service_risk_control_v1_recaptcha_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecaptchaChallengeInput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecaptchaChallengeOutput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskControlVerifyInput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecaptchaInput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskControlVerifyOutput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*RiskControlVerifyInput_Recaptcha)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_risk_control_v1_recaptcha_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_risk_control_v1_recaptcha_service_proto_goTypes,
		DependencyIndexes: file_moego_service_risk_control_v1_recaptcha_service_proto_depIdxs,
		MessageInfos:      file_moego_service_risk_control_v1_recaptcha_service_proto_msgTypes,
	}.Build()
	File_moego_service_risk_control_v1_recaptcha_service_proto = out.File
	file_moego_service_risk_control_v1_recaptcha_service_proto_rawDesc = nil
	file_moego_service_risk_control_v1_recaptcha_service_proto_goTypes = nil
	file_moego_service_risk_control_v1_recaptcha_service_proto_depIdxs = nil
}
