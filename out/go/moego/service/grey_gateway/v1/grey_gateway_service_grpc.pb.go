// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/grey_gateway/v1/grey_gateway_service.proto

package greygatewaysvcpb

import (
	context "context"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grey_gateway/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// GreyGatewayServiceClient is the client API for GreyGatewayService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GreyGatewayServiceClient interface {
	// Get services branches mapping
	GetServiceBranchMap(ctx context.Context, in *GetServiceBranchMapRequest, opts ...grpc.CallOption) (*GetServiceBranchMapResponse, error)
	// list grey items
	GetGreyItemList(ctx context.Context, in *GetGreyItemListRequest, opts ...grpc.CallOption) (*GetGreyItemListResponse, error)
	// get grey item
	GetGreyItem(ctx context.Context, in *v1.Id, opts ...grpc.CallOption) (*v11.GreyItemModel, error)
	// get grey item by name
	GetGreyItemByName(ctx context.Context, in *GetGreyItemByNameRequest, opts ...grpc.CallOption) (*v11.GreyItemModel, error)
	// insert grey item
	InsertGreyItem(ctx context.Context, in *InsertGreyItemRequest, opts ...grpc.CallOption) (*v11.GreyItemModel, error)
	// update grey item
	UpdateGreyItem(ctx context.Context, in *UpdateGreyItemRequest, opts ...grpc.CallOption) (*v11.GreyItemModel, error)
	// delete grey item
	DeleteGreyItem(ctx context.Context, in *v1.Id, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type greyGatewayServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGreyGatewayServiceClient(cc grpc.ClientConnInterface) GreyGatewayServiceClient {
	return &greyGatewayServiceClient{cc}
}

func (c *greyGatewayServiceClient) GetServiceBranchMap(ctx context.Context, in *GetServiceBranchMapRequest, opts ...grpc.CallOption) (*GetServiceBranchMapResponse, error) {
	out := new(GetServiceBranchMapResponse)
	err := c.cc.Invoke(ctx, "/moego.service.grey_gateway.v1.GreyGatewayService/GetServiceBranchMap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greyGatewayServiceClient) GetGreyItemList(ctx context.Context, in *GetGreyItemListRequest, opts ...grpc.CallOption) (*GetGreyItemListResponse, error) {
	out := new(GetGreyItemListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.grey_gateway.v1.GreyGatewayService/GetGreyItemList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greyGatewayServiceClient) GetGreyItem(ctx context.Context, in *v1.Id, opts ...grpc.CallOption) (*v11.GreyItemModel, error) {
	out := new(v11.GreyItemModel)
	err := c.cc.Invoke(ctx, "/moego.service.grey_gateway.v1.GreyGatewayService/GetGreyItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greyGatewayServiceClient) GetGreyItemByName(ctx context.Context, in *GetGreyItemByNameRequest, opts ...grpc.CallOption) (*v11.GreyItemModel, error) {
	out := new(v11.GreyItemModel)
	err := c.cc.Invoke(ctx, "/moego.service.grey_gateway.v1.GreyGatewayService/GetGreyItemByName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greyGatewayServiceClient) InsertGreyItem(ctx context.Context, in *InsertGreyItemRequest, opts ...grpc.CallOption) (*v11.GreyItemModel, error) {
	out := new(v11.GreyItemModel)
	err := c.cc.Invoke(ctx, "/moego.service.grey_gateway.v1.GreyGatewayService/InsertGreyItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greyGatewayServiceClient) UpdateGreyItem(ctx context.Context, in *UpdateGreyItemRequest, opts ...grpc.CallOption) (*v11.GreyItemModel, error) {
	out := new(v11.GreyItemModel)
	err := c.cc.Invoke(ctx, "/moego.service.grey_gateway.v1.GreyGatewayService/UpdateGreyItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greyGatewayServiceClient) DeleteGreyItem(ctx context.Context, in *v1.Id, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.grey_gateway.v1.GreyGatewayService/DeleteGreyItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GreyGatewayServiceServer is the server API for GreyGatewayService service.
// All implementations must embed UnimplementedGreyGatewayServiceServer
// for forward compatibility
type GreyGatewayServiceServer interface {
	// Get services branches mapping
	GetServiceBranchMap(context.Context, *GetServiceBranchMapRequest) (*GetServiceBranchMapResponse, error)
	// list grey items
	GetGreyItemList(context.Context, *GetGreyItemListRequest) (*GetGreyItemListResponse, error)
	// get grey item
	GetGreyItem(context.Context, *v1.Id) (*v11.GreyItemModel, error)
	// get grey item by name
	GetGreyItemByName(context.Context, *GetGreyItemByNameRequest) (*v11.GreyItemModel, error)
	// insert grey item
	InsertGreyItem(context.Context, *InsertGreyItemRequest) (*v11.GreyItemModel, error)
	// update grey item
	UpdateGreyItem(context.Context, *UpdateGreyItemRequest) (*v11.GreyItemModel, error)
	// delete grey item
	DeleteGreyItem(context.Context, *v1.Id) (*emptypb.Empty, error)
	mustEmbedUnimplementedGreyGatewayServiceServer()
}

// UnimplementedGreyGatewayServiceServer must be embedded to have forward compatible implementations.
type UnimplementedGreyGatewayServiceServer struct {
}

func (UnimplementedGreyGatewayServiceServer) GetServiceBranchMap(context.Context, *GetServiceBranchMapRequest) (*GetServiceBranchMapResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceBranchMap not implemented")
}
func (UnimplementedGreyGatewayServiceServer) GetGreyItemList(context.Context, *GetGreyItemListRequest) (*GetGreyItemListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGreyItemList not implemented")
}
func (UnimplementedGreyGatewayServiceServer) GetGreyItem(context.Context, *v1.Id) (*v11.GreyItemModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGreyItem not implemented")
}
func (UnimplementedGreyGatewayServiceServer) GetGreyItemByName(context.Context, *GetGreyItemByNameRequest) (*v11.GreyItemModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGreyItemByName not implemented")
}
func (UnimplementedGreyGatewayServiceServer) InsertGreyItem(context.Context, *InsertGreyItemRequest) (*v11.GreyItemModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InsertGreyItem not implemented")
}
func (UnimplementedGreyGatewayServiceServer) UpdateGreyItem(context.Context, *UpdateGreyItemRequest) (*v11.GreyItemModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGreyItem not implemented")
}
func (UnimplementedGreyGatewayServiceServer) DeleteGreyItem(context.Context, *v1.Id) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGreyItem not implemented")
}
func (UnimplementedGreyGatewayServiceServer) mustEmbedUnimplementedGreyGatewayServiceServer() {}

// UnsafeGreyGatewayServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GreyGatewayServiceServer will
// result in compilation errors.
type UnsafeGreyGatewayServiceServer interface {
	mustEmbedUnimplementedGreyGatewayServiceServer()
}

func RegisterGreyGatewayServiceServer(s grpc.ServiceRegistrar, srv GreyGatewayServiceServer) {
	s.RegisterService(&GreyGatewayService_ServiceDesc, srv)
}

func _GreyGatewayService_GetServiceBranchMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceBranchMapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreyGatewayServiceServer).GetServiceBranchMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.grey_gateway.v1.GreyGatewayService/GetServiceBranchMap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreyGatewayServiceServer).GetServiceBranchMap(ctx, req.(*GetServiceBranchMapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GreyGatewayService_GetGreyItemList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGreyItemListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreyGatewayServiceServer).GetGreyItemList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.grey_gateway.v1.GreyGatewayService/GetGreyItemList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreyGatewayServiceServer).GetGreyItemList(ctx, req.(*GetGreyItemListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GreyGatewayService_GetGreyItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.Id)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreyGatewayServiceServer).GetGreyItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.grey_gateway.v1.GreyGatewayService/GetGreyItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreyGatewayServiceServer).GetGreyItem(ctx, req.(*v1.Id))
	}
	return interceptor(ctx, in, info, handler)
}

func _GreyGatewayService_GetGreyItemByName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGreyItemByNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreyGatewayServiceServer).GetGreyItemByName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.grey_gateway.v1.GreyGatewayService/GetGreyItemByName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreyGatewayServiceServer).GetGreyItemByName(ctx, req.(*GetGreyItemByNameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GreyGatewayService_InsertGreyItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsertGreyItemRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreyGatewayServiceServer).InsertGreyItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.grey_gateway.v1.GreyGatewayService/InsertGreyItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreyGatewayServiceServer).InsertGreyItem(ctx, req.(*InsertGreyItemRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GreyGatewayService_UpdateGreyItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGreyItemRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreyGatewayServiceServer).UpdateGreyItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.grey_gateway.v1.GreyGatewayService/UpdateGreyItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreyGatewayServiceServer).UpdateGreyItem(ctx, req.(*UpdateGreyItemRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GreyGatewayService_DeleteGreyItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.Id)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreyGatewayServiceServer).DeleteGreyItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.grey_gateway.v1.GreyGatewayService/DeleteGreyItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreyGatewayServiceServer).DeleteGreyItem(ctx, req.(*v1.Id))
	}
	return interceptor(ctx, in, info, handler)
}

// GreyGatewayService_ServiceDesc is the grpc.ServiceDesc for GreyGatewayService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GreyGatewayService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.grey_gateway.v1.GreyGatewayService",
	HandlerType: (*GreyGatewayServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetServiceBranchMap",
			Handler:    _GreyGatewayService_GetServiceBranchMap_Handler,
		},
		{
			MethodName: "GetGreyItemList",
			Handler:    _GreyGatewayService_GetGreyItemList_Handler,
		},
		{
			MethodName: "GetGreyItem",
			Handler:    _GreyGatewayService_GetGreyItem_Handler,
		},
		{
			MethodName: "GetGreyItemByName",
			Handler:    _GreyGatewayService_GetGreyItemByName_Handler,
		},
		{
			MethodName: "InsertGreyItem",
			Handler:    _GreyGatewayService_InsertGreyItem_Handler,
		},
		{
			MethodName: "UpdateGreyItem",
			Handler:    _GreyGatewayService_UpdateGreyItem_Handler,
		},
		{
			MethodName: "DeleteGreyItem",
			Handler:    _GreyGatewayService_DeleteGreyItem_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/grey_gateway/v1/grey_gateway_service.proto",
}
