// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/online_booking/v1/ob_availability_setting_service.proto

package onlinebookingsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// OBAvailabilitySettingServiceClient is the client API for OBAvailabilitySettingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OBAvailabilitySettingServiceClient interface {
	// get boarding service availability setting
	GetBoardingServiceAvailabilitySetting(ctx context.Context, in *GetBoardingServiceAvailabilitySettingRequest, opts ...grpc.CallOption) (*GetBoardingServiceAvailabilitySettingResponse, error)
	// update boarding service availability setting
	UpdateBoardingServiceAvailabilitySetting(ctx context.Context, in *UpdateBoardingServiceAvailabilitySettingRequest, opts ...grpc.CallOption) (*UpdateBoardingServiceAvailabilitySettingResponse, error)
	// get daycare service availability setting
	GetDaycareServiceAvailabilitySetting(ctx context.Context, in *GetDaycareServiceAvailabilitySettingRequest, opts ...grpc.CallOption) (*GetDaycareServiceAvailabilitySettingResponse, error)
	// update daycare service availability setting
	UpdateDaycareServiceAvailabilitySetting(ctx context.Context, in *UpdateDaycareServiceAvailabilitySettingRequest, opts ...grpc.CallOption) (*UpdateDaycareServiceAvailabilitySettingResponse, error)
	// query available pet type
	QueryAvailablePetType(ctx context.Context, in *QueryAvailablePetTypeRequest, opts ...grpc.CallOption) (*QueryAvailablePetTypeResponse, error)
	// query available booking date range
	QueryAvailableBookingDateRange(ctx context.Context, in *QueryAvailableBookingDateRangeRequest, opts ...grpc.CallOption) (*QueryAvailableBookingDateRangeResponse, error)
	// get evaluation service availability
	GetEvaluationServiceAvailability(ctx context.Context, in *GetEvaluationServiceAvailabilityRequest, opts ...grpc.CallOption) (*GetEvaluationServiceAvailabilityResponse, error)
	// update evaluation service availability
	UpdateEvaluationServiceAvailability(ctx context.Context, in *UpdateEvaluationServiceAvailabilityRequest, opts ...grpc.CallOption) (*UpdateEvaluationServiceAvailabilityResponse, error)
	// get grooming service availability¡
	GetGroomingServiceAvailability(ctx context.Context, in *GetGroomingServiceAvailabilityRequest, opts ...grpc.CallOption) (*GetGroomingServiceAvailabilityResponse, error)
	// update grooming service availability
	UpdateGroomingServiceAvailability(ctx context.Context, in *UpdateGroomingServiceAvailabilityRequest, opts ...grpc.CallOption) (*UpdateGroomingServiceAvailabilityResponse, error)
	// list available booking time range
	ListAvailableBookingTimeRange(ctx context.Context, in *ListAvailableBookingTimeRangeRequest, opts ...grpc.CallOption) (*ListAvailableBookingTimeRangeResponse, error)
	// list accepted customer setting
	ListAcceptedCustomerSetting(ctx context.Context, in *ListAcceptedCustomerSettingRequest, opts ...grpc.CallOption) (*ListAcceptedCustomerSettingResponse, error)
	// update accepted customer setting, temporary for migration settings
	UpdateAcceptedCustomerSetting(ctx context.Context, in *UpdateAcceptedCustomerSettingRequest, opts ...grpc.CallOption) (*UpdateAcceptedCustomerSettingResponse, error)
	// list arrival pick up time overrides
	ListArrivalPickUpTimeOverrides(ctx context.Context, in *ListArrivalPickUpTimeOverridesRequest, opts ...grpc.CallOption) (*ListArrivalPickUpTimeOverridesResponse, error)
	// batch create arrival pick up time override
	BatchCreateArrivalPickUpTimeOverride(ctx context.Context, in *BatchCreateArrivalPickUpTimeOverrideRequest, opts ...grpc.CallOption) (*BatchCreateArrivalPickUpTimeOverrideResponse, error)
	// batch delete arrival pick up time override
	BatchDeleteArrivalPickUpTimeOverride(ctx context.Context, in *BatchDeleteArrivalPickUpTimeOverrideRequest, opts ...grpc.CallOption) (*BatchDeleteArrivalPickUpTimeOverrideResponse, error)
	// batch update arrival pick up time override
	BatchUpdateArrivalPickUpTimeOverride(ctx context.Context, in *BatchUpdateArrivalPickUpTimeOverrideRequest, opts ...grpc.CallOption) (*BatchUpdateArrivalPickUpTimeOverrideResponse, error)
	// list capacity override
	ListCapacityOverrides(ctx context.Context, in *ListCapacityOverridesRequest, opts ...grpc.CallOption) (*ListCapacityOverridesResponse, error)
	// create capacity override
	CreateCapacityOverride(ctx context.Context, in *CreateCapacityOverrideRequest, opts ...grpc.CallOption) (*CreateCapacityOverrideResponse, error)
	// delete capacity override
	DeleteCapacityOverride(ctx context.Context, in *DeleteCapacityOverrideRequest, opts ...grpc.CallOption) (*DeleteCapacityOverrideResponse, error)
	// update capacity override
	UpdateCapacityOverride(ctx context.Context, in *UpdateCapacityOverrideRequest, opts ...grpc.CallOption) (*UpdateCapacityOverrideResponse, error)
	// init ob service
	InitOBService(ctx context.Context, in *InitOBServiceRequest, opts ...grpc.CallOption) (*InitOBServiceResponse, error)
}

type oBAvailabilitySettingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOBAvailabilitySettingServiceClient(cc grpc.ClientConnInterface) OBAvailabilitySettingServiceClient {
	return &oBAvailabilitySettingServiceClient{cc}
}

func (c *oBAvailabilitySettingServiceClient) GetBoardingServiceAvailabilitySetting(ctx context.Context, in *GetBoardingServiceAvailabilitySettingRequest, opts ...grpc.CallOption) (*GetBoardingServiceAvailabilitySettingResponse, error) {
	out := new(GetBoardingServiceAvailabilitySettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/GetBoardingServiceAvailabilitySetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) UpdateBoardingServiceAvailabilitySetting(ctx context.Context, in *UpdateBoardingServiceAvailabilitySettingRequest, opts ...grpc.CallOption) (*UpdateBoardingServiceAvailabilitySettingResponse, error) {
	out := new(UpdateBoardingServiceAvailabilitySettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/UpdateBoardingServiceAvailabilitySetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) GetDaycareServiceAvailabilitySetting(ctx context.Context, in *GetDaycareServiceAvailabilitySettingRequest, opts ...grpc.CallOption) (*GetDaycareServiceAvailabilitySettingResponse, error) {
	out := new(GetDaycareServiceAvailabilitySettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/GetDaycareServiceAvailabilitySetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) UpdateDaycareServiceAvailabilitySetting(ctx context.Context, in *UpdateDaycareServiceAvailabilitySettingRequest, opts ...grpc.CallOption) (*UpdateDaycareServiceAvailabilitySettingResponse, error) {
	out := new(UpdateDaycareServiceAvailabilitySettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/UpdateDaycareServiceAvailabilitySetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) QueryAvailablePetType(ctx context.Context, in *QueryAvailablePetTypeRequest, opts ...grpc.CallOption) (*QueryAvailablePetTypeResponse, error) {
	out := new(QueryAvailablePetTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/QueryAvailablePetType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) QueryAvailableBookingDateRange(ctx context.Context, in *QueryAvailableBookingDateRangeRequest, opts ...grpc.CallOption) (*QueryAvailableBookingDateRangeResponse, error) {
	out := new(QueryAvailableBookingDateRangeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/QueryAvailableBookingDateRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) GetEvaluationServiceAvailability(ctx context.Context, in *GetEvaluationServiceAvailabilityRequest, opts ...grpc.CallOption) (*GetEvaluationServiceAvailabilityResponse, error) {
	out := new(GetEvaluationServiceAvailabilityResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/GetEvaluationServiceAvailability", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) UpdateEvaluationServiceAvailability(ctx context.Context, in *UpdateEvaluationServiceAvailabilityRequest, opts ...grpc.CallOption) (*UpdateEvaluationServiceAvailabilityResponse, error) {
	out := new(UpdateEvaluationServiceAvailabilityResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/UpdateEvaluationServiceAvailability", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) GetGroomingServiceAvailability(ctx context.Context, in *GetGroomingServiceAvailabilityRequest, opts ...grpc.CallOption) (*GetGroomingServiceAvailabilityResponse, error) {
	out := new(GetGroomingServiceAvailabilityResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/GetGroomingServiceAvailability", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) UpdateGroomingServiceAvailability(ctx context.Context, in *UpdateGroomingServiceAvailabilityRequest, opts ...grpc.CallOption) (*UpdateGroomingServiceAvailabilityResponse, error) {
	out := new(UpdateGroomingServiceAvailabilityResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/UpdateGroomingServiceAvailability", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) ListAvailableBookingTimeRange(ctx context.Context, in *ListAvailableBookingTimeRangeRequest, opts ...grpc.CallOption) (*ListAvailableBookingTimeRangeResponse, error) {
	out := new(ListAvailableBookingTimeRangeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/ListAvailableBookingTimeRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) ListAcceptedCustomerSetting(ctx context.Context, in *ListAcceptedCustomerSettingRequest, opts ...grpc.CallOption) (*ListAcceptedCustomerSettingResponse, error) {
	out := new(ListAcceptedCustomerSettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/ListAcceptedCustomerSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) UpdateAcceptedCustomerSetting(ctx context.Context, in *UpdateAcceptedCustomerSettingRequest, opts ...grpc.CallOption) (*UpdateAcceptedCustomerSettingResponse, error) {
	out := new(UpdateAcceptedCustomerSettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/UpdateAcceptedCustomerSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) ListArrivalPickUpTimeOverrides(ctx context.Context, in *ListArrivalPickUpTimeOverridesRequest, opts ...grpc.CallOption) (*ListArrivalPickUpTimeOverridesResponse, error) {
	out := new(ListArrivalPickUpTimeOverridesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/ListArrivalPickUpTimeOverrides", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) BatchCreateArrivalPickUpTimeOverride(ctx context.Context, in *BatchCreateArrivalPickUpTimeOverrideRequest, opts ...grpc.CallOption) (*BatchCreateArrivalPickUpTimeOverrideResponse, error) {
	out := new(BatchCreateArrivalPickUpTimeOverrideResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/BatchCreateArrivalPickUpTimeOverride", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) BatchDeleteArrivalPickUpTimeOverride(ctx context.Context, in *BatchDeleteArrivalPickUpTimeOverrideRequest, opts ...grpc.CallOption) (*BatchDeleteArrivalPickUpTimeOverrideResponse, error) {
	out := new(BatchDeleteArrivalPickUpTimeOverrideResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/BatchDeleteArrivalPickUpTimeOverride", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) BatchUpdateArrivalPickUpTimeOverride(ctx context.Context, in *BatchUpdateArrivalPickUpTimeOverrideRequest, opts ...grpc.CallOption) (*BatchUpdateArrivalPickUpTimeOverrideResponse, error) {
	out := new(BatchUpdateArrivalPickUpTimeOverrideResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/BatchUpdateArrivalPickUpTimeOverride", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) ListCapacityOverrides(ctx context.Context, in *ListCapacityOverridesRequest, opts ...grpc.CallOption) (*ListCapacityOverridesResponse, error) {
	out := new(ListCapacityOverridesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/ListCapacityOverrides", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) CreateCapacityOverride(ctx context.Context, in *CreateCapacityOverrideRequest, opts ...grpc.CallOption) (*CreateCapacityOverrideResponse, error) {
	out := new(CreateCapacityOverrideResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/CreateCapacityOverride", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) DeleteCapacityOverride(ctx context.Context, in *DeleteCapacityOverrideRequest, opts ...grpc.CallOption) (*DeleteCapacityOverrideResponse, error) {
	out := new(DeleteCapacityOverrideResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/DeleteCapacityOverride", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) UpdateCapacityOverride(ctx context.Context, in *UpdateCapacityOverrideRequest, opts ...grpc.CallOption) (*UpdateCapacityOverrideResponse, error) {
	out := new(UpdateCapacityOverrideResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/UpdateCapacityOverride", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *oBAvailabilitySettingServiceClient) InitOBService(ctx context.Context, in *InitOBServiceRequest, opts ...grpc.CallOption) (*InitOBServiceResponse, error) {
	out := new(InitOBServiceResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.OBAvailabilitySettingService/InitOBService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OBAvailabilitySettingServiceServer is the server API for OBAvailabilitySettingService service.
// All implementations must embed UnimplementedOBAvailabilitySettingServiceServer
// for forward compatibility
type OBAvailabilitySettingServiceServer interface {
	// get boarding service availability setting
	GetBoardingServiceAvailabilitySetting(context.Context, *GetBoardingServiceAvailabilitySettingRequest) (*GetBoardingServiceAvailabilitySettingResponse, error)
	// update boarding service availability setting
	UpdateBoardingServiceAvailabilitySetting(context.Context, *UpdateBoardingServiceAvailabilitySettingRequest) (*UpdateBoardingServiceAvailabilitySettingResponse, error)
	// get daycare service availability setting
	GetDaycareServiceAvailabilitySetting(context.Context, *GetDaycareServiceAvailabilitySettingRequest) (*GetDaycareServiceAvailabilitySettingResponse, error)
	// update daycare service availability setting
	UpdateDaycareServiceAvailabilitySetting(context.Context, *UpdateDaycareServiceAvailabilitySettingRequest) (*UpdateDaycareServiceAvailabilitySettingResponse, error)
	// query available pet type
	QueryAvailablePetType(context.Context, *QueryAvailablePetTypeRequest) (*QueryAvailablePetTypeResponse, error)
	// query available booking date range
	QueryAvailableBookingDateRange(context.Context, *QueryAvailableBookingDateRangeRequest) (*QueryAvailableBookingDateRangeResponse, error)
	// get evaluation service availability
	GetEvaluationServiceAvailability(context.Context, *GetEvaluationServiceAvailabilityRequest) (*GetEvaluationServiceAvailabilityResponse, error)
	// update evaluation service availability
	UpdateEvaluationServiceAvailability(context.Context, *UpdateEvaluationServiceAvailabilityRequest) (*UpdateEvaluationServiceAvailabilityResponse, error)
	// get grooming service availability¡
	GetGroomingServiceAvailability(context.Context, *GetGroomingServiceAvailabilityRequest) (*GetGroomingServiceAvailabilityResponse, error)
	// update grooming service availability
	UpdateGroomingServiceAvailability(context.Context, *UpdateGroomingServiceAvailabilityRequest) (*UpdateGroomingServiceAvailabilityResponse, error)
	// list available booking time range
	ListAvailableBookingTimeRange(context.Context, *ListAvailableBookingTimeRangeRequest) (*ListAvailableBookingTimeRangeResponse, error)
	// list accepted customer setting
	ListAcceptedCustomerSetting(context.Context, *ListAcceptedCustomerSettingRequest) (*ListAcceptedCustomerSettingResponse, error)
	// update accepted customer setting, temporary for migration settings
	UpdateAcceptedCustomerSetting(context.Context, *UpdateAcceptedCustomerSettingRequest) (*UpdateAcceptedCustomerSettingResponse, error)
	// list arrival pick up time overrides
	ListArrivalPickUpTimeOverrides(context.Context, *ListArrivalPickUpTimeOverridesRequest) (*ListArrivalPickUpTimeOverridesResponse, error)
	// batch create arrival pick up time override
	BatchCreateArrivalPickUpTimeOverride(context.Context, *BatchCreateArrivalPickUpTimeOverrideRequest) (*BatchCreateArrivalPickUpTimeOverrideResponse, error)
	// batch delete arrival pick up time override
	BatchDeleteArrivalPickUpTimeOverride(context.Context, *BatchDeleteArrivalPickUpTimeOverrideRequest) (*BatchDeleteArrivalPickUpTimeOverrideResponse, error)
	// batch update arrival pick up time override
	BatchUpdateArrivalPickUpTimeOverride(context.Context, *BatchUpdateArrivalPickUpTimeOverrideRequest) (*BatchUpdateArrivalPickUpTimeOverrideResponse, error)
	// list capacity override
	ListCapacityOverrides(context.Context, *ListCapacityOverridesRequest) (*ListCapacityOverridesResponse, error)
	// create capacity override
	CreateCapacityOverride(context.Context, *CreateCapacityOverrideRequest) (*CreateCapacityOverrideResponse, error)
	// delete capacity override
	DeleteCapacityOverride(context.Context, *DeleteCapacityOverrideRequest) (*DeleteCapacityOverrideResponse, error)
	// update capacity override
	UpdateCapacityOverride(context.Context, *UpdateCapacityOverrideRequest) (*UpdateCapacityOverrideResponse, error)
	// init ob service
	InitOBService(context.Context, *InitOBServiceRequest) (*InitOBServiceResponse, error)
	mustEmbedUnimplementedOBAvailabilitySettingServiceServer()
}

// UnimplementedOBAvailabilitySettingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedOBAvailabilitySettingServiceServer struct {
}

func (UnimplementedOBAvailabilitySettingServiceServer) GetBoardingServiceAvailabilitySetting(context.Context, *GetBoardingServiceAvailabilitySettingRequest) (*GetBoardingServiceAvailabilitySettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBoardingServiceAvailabilitySetting not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) UpdateBoardingServiceAvailabilitySetting(context.Context, *UpdateBoardingServiceAvailabilitySettingRequest) (*UpdateBoardingServiceAvailabilitySettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBoardingServiceAvailabilitySetting not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) GetDaycareServiceAvailabilitySetting(context.Context, *GetDaycareServiceAvailabilitySettingRequest) (*GetDaycareServiceAvailabilitySettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDaycareServiceAvailabilitySetting not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) UpdateDaycareServiceAvailabilitySetting(context.Context, *UpdateDaycareServiceAvailabilitySettingRequest) (*UpdateDaycareServiceAvailabilitySettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDaycareServiceAvailabilitySetting not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) QueryAvailablePetType(context.Context, *QueryAvailablePetTypeRequest) (*QueryAvailablePetTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryAvailablePetType not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) QueryAvailableBookingDateRange(context.Context, *QueryAvailableBookingDateRangeRequest) (*QueryAvailableBookingDateRangeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryAvailableBookingDateRange not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) GetEvaluationServiceAvailability(context.Context, *GetEvaluationServiceAvailabilityRequest) (*GetEvaluationServiceAvailabilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEvaluationServiceAvailability not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) UpdateEvaluationServiceAvailability(context.Context, *UpdateEvaluationServiceAvailabilityRequest) (*UpdateEvaluationServiceAvailabilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEvaluationServiceAvailability not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) GetGroomingServiceAvailability(context.Context, *GetGroomingServiceAvailabilityRequest) (*GetGroomingServiceAvailabilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroomingServiceAvailability not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) UpdateGroomingServiceAvailability(context.Context, *UpdateGroomingServiceAvailabilityRequest) (*UpdateGroomingServiceAvailabilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGroomingServiceAvailability not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) ListAvailableBookingTimeRange(context.Context, *ListAvailableBookingTimeRangeRequest) (*ListAvailableBookingTimeRangeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAvailableBookingTimeRange not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) ListAcceptedCustomerSetting(context.Context, *ListAcceptedCustomerSettingRequest) (*ListAcceptedCustomerSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAcceptedCustomerSetting not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) UpdateAcceptedCustomerSetting(context.Context, *UpdateAcceptedCustomerSettingRequest) (*UpdateAcceptedCustomerSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAcceptedCustomerSetting not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) ListArrivalPickUpTimeOverrides(context.Context, *ListArrivalPickUpTimeOverridesRequest) (*ListArrivalPickUpTimeOverridesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListArrivalPickUpTimeOverrides not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) BatchCreateArrivalPickUpTimeOverride(context.Context, *BatchCreateArrivalPickUpTimeOverrideRequest) (*BatchCreateArrivalPickUpTimeOverrideResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCreateArrivalPickUpTimeOverride not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) BatchDeleteArrivalPickUpTimeOverride(context.Context, *BatchDeleteArrivalPickUpTimeOverrideRequest) (*BatchDeleteArrivalPickUpTimeOverrideResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchDeleteArrivalPickUpTimeOverride not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) BatchUpdateArrivalPickUpTimeOverride(context.Context, *BatchUpdateArrivalPickUpTimeOverrideRequest) (*BatchUpdateArrivalPickUpTimeOverrideResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpdateArrivalPickUpTimeOverride not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) ListCapacityOverrides(context.Context, *ListCapacityOverridesRequest) (*ListCapacityOverridesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCapacityOverrides not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) CreateCapacityOverride(context.Context, *CreateCapacityOverrideRequest) (*CreateCapacityOverrideResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCapacityOverride not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) DeleteCapacityOverride(context.Context, *DeleteCapacityOverrideRequest) (*DeleteCapacityOverrideResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCapacityOverride not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) UpdateCapacityOverride(context.Context, *UpdateCapacityOverrideRequest) (*UpdateCapacityOverrideResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCapacityOverride not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) InitOBService(context.Context, *InitOBServiceRequest) (*InitOBServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitOBService not implemented")
}
func (UnimplementedOBAvailabilitySettingServiceServer) mustEmbedUnimplementedOBAvailabilitySettingServiceServer() {
}

// UnsafeOBAvailabilitySettingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OBAvailabilitySettingServiceServer will
// result in compilation errors.
type UnsafeOBAvailabilitySettingServiceServer interface {
	mustEmbedUnimplementedOBAvailabilitySettingServiceServer()
}

func RegisterOBAvailabilitySettingServiceServer(s grpc.ServiceRegistrar, srv OBAvailabilitySettingServiceServer) {
	s.RegisterService(&OBAvailabilitySettingService_ServiceDesc, srv)
}

func _OBAvailabilitySettingService_GetBoardingServiceAvailabilitySetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBoardingServiceAvailabilitySettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).GetBoardingServiceAvailabilitySetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/GetBoardingServiceAvailabilitySetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).GetBoardingServiceAvailabilitySetting(ctx, req.(*GetBoardingServiceAvailabilitySettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_UpdateBoardingServiceAvailabilitySetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBoardingServiceAvailabilitySettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).UpdateBoardingServiceAvailabilitySetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/UpdateBoardingServiceAvailabilitySetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).UpdateBoardingServiceAvailabilitySetting(ctx, req.(*UpdateBoardingServiceAvailabilitySettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_GetDaycareServiceAvailabilitySetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDaycareServiceAvailabilitySettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).GetDaycareServiceAvailabilitySetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/GetDaycareServiceAvailabilitySetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).GetDaycareServiceAvailabilitySetting(ctx, req.(*GetDaycareServiceAvailabilitySettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_UpdateDaycareServiceAvailabilitySetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDaycareServiceAvailabilitySettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).UpdateDaycareServiceAvailabilitySetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/UpdateDaycareServiceAvailabilitySetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).UpdateDaycareServiceAvailabilitySetting(ctx, req.(*UpdateDaycareServiceAvailabilitySettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_QueryAvailablePetType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryAvailablePetTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).QueryAvailablePetType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/QueryAvailablePetType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).QueryAvailablePetType(ctx, req.(*QueryAvailablePetTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_QueryAvailableBookingDateRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryAvailableBookingDateRangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).QueryAvailableBookingDateRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/QueryAvailableBookingDateRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).QueryAvailableBookingDateRange(ctx, req.(*QueryAvailableBookingDateRangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_GetEvaluationServiceAvailability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEvaluationServiceAvailabilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).GetEvaluationServiceAvailability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/GetEvaluationServiceAvailability",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).GetEvaluationServiceAvailability(ctx, req.(*GetEvaluationServiceAvailabilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_UpdateEvaluationServiceAvailability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEvaluationServiceAvailabilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).UpdateEvaluationServiceAvailability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/UpdateEvaluationServiceAvailability",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).UpdateEvaluationServiceAvailability(ctx, req.(*UpdateEvaluationServiceAvailabilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_GetGroomingServiceAvailability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroomingServiceAvailabilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).GetGroomingServiceAvailability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/GetGroomingServiceAvailability",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).GetGroomingServiceAvailability(ctx, req.(*GetGroomingServiceAvailabilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_UpdateGroomingServiceAvailability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroomingServiceAvailabilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).UpdateGroomingServiceAvailability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/UpdateGroomingServiceAvailability",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).UpdateGroomingServiceAvailability(ctx, req.(*UpdateGroomingServiceAvailabilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_ListAvailableBookingTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAvailableBookingTimeRangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).ListAvailableBookingTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/ListAvailableBookingTimeRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).ListAvailableBookingTimeRange(ctx, req.(*ListAvailableBookingTimeRangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_ListAcceptedCustomerSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAcceptedCustomerSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).ListAcceptedCustomerSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/ListAcceptedCustomerSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).ListAcceptedCustomerSetting(ctx, req.(*ListAcceptedCustomerSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_UpdateAcceptedCustomerSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAcceptedCustomerSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).UpdateAcceptedCustomerSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/UpdateAcceptedCustomerSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).UpdateAcceptedCustomerSetting(ctx, req.(*UpdateAcceptedCustomerSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_ListArrivalPickUpTimeOverrides_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListArrivalPickUpTimeOverridesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).ListArrivalPickUpTimeOverrides(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/ListArrivalPickUpTimeOverrides",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).ListArrivalPickUpTimeOverrides(ctx, req.(*ListArrivalPickUpTimeOverridesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_BatchCreateArrivalPickUpTimeOverride_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCreateArrivalPickUpTimeOverrideRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).BatchCreateArrivalPickUpTimeOverride(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/BatchCreateArrivalPickUpTimeOverride",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).BatchCreateArrivalPickUpTimeOverride(ctx, req.(*BatchCreateArrivalPickUpTimeOverrideRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_BatchDeleteArrivalPickUpTimeOverride_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteArrivalPickUpTimeOverrideRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).BatchDeleteArrivalPickUpTimeOverride(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/BatchDeleteArrivalPickUpTimeOverride",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).BatchDeleteArrivalPickUpTimeOverride(ctx, req.(*BatchDeleteArrivalPickUpTimeOverrideRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_BatchUpdateArrivalPickUpTimeOverride_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateArrivalPickUpTimeOverrideRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).BatchUpdateArrivalPickUpTimeOverride(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/BatchUpdateArrivalPickUpTimeOverride",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).BatchUpdateArrivalPickUpTimeOverride(ctx, req.(*BatchUpdateArrivalPickUpTimeOverrideRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_ListCapacityOverrides_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCapacityOverridesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).ListCapacityOverrides(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/ListCapacityOverrides",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).ListCapacityOverrides(ctx, req.(*ListCapacityOverridesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_CreateCapacityOverride_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCapacityOverrideRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).CreateCapacityOverride(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/CreateCapacityOverride",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).CreateCapacityOverride(ctx, req.(*CreateCapacityOverrideRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_DeleteCapacityOverride_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCapacityOverrideRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).DeleteCapacityOverride(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/DeleteCapacityOverride",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).DeleteCapacityOverride(ctx, req.(*DeleteCapacityOverrideRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_UpdateCapacityOverride_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCapacityOverrideRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).UpdateCapacityOverride(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/UpdateCapacityOverride",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).UpdateCapacityOverride(ctx, req.(*UpdateCapacityOverrideRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OBAvailabilitySettingService_InitOBService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitOBServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OBAvailabilitySettingServiceServer).InitOBService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.OBAvailabilitySettingService/InitOBService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OBAvailabilitySettingServiceServer).InitOBService(ctx, req.(*InitOBServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OBAvailabilitySettingService_ServiceDesc is the grpc.ServiceDesc for OBAvailabilitySettingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OBAvailabilitySettingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.online_booking.v1.OBAvailabilitySettingService",
	HandlerType: (*OBAvailabilitySettingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetBoardingServiceAvailabilitySetting",
			Handler:    _OBAvailabilitySettingService_GetBoardingServiceAvailabilitySetting_Handler,
		},
		{
			MethodName: "UpdateBoardingServiceAvailabilitySetting",
			Handler:    _OBAvailabilitySettingService_UpdateBoardingServiceAvailabilitySetting_Handler,
		},
		{
			MethodName: "GetDaycareServiceAvailabilitySetting",
			Handler:    _OBAvailabilitySettingService_GetDaycareServiceAvailabilitySetting_Handler,
		},
		{
			MethodName: "UpdateDaycareServiceAvailabilitySetting",
			Handler:    _OBAvailabilitySettingService_UpdateDaycareServiceAvailabilitySetting_Handler,
		},
		{
			MethodName: "QueryAvailablePetType",
			Handler:    _OBAvailabilitySettingService_QueryAvailablePetType_Handler,
		},
		{
			MethodName: "QueryAvailableBookingDateRange",
			Handler:    _OBAvailabilitySettingService_QueryAvailableBookingDateRange_Handler,
		},
		{
			MethodName: "GetEvaluationServiceAvailability",
			Handler:    _OBAvailabilitySettingService_GetEvaluationServiceAvailability_Handler,
		},
		{
			MethodName: "UpdateEvaluationServiceAvailability",
			Handler:    _OBAvailabilitySettingService_UpdateEvaluationServiceAvailability_Handler,
		},
		{
			MethodName: "GetGroomingServiceAvailability",
			Handler:    _OBAvailabilitySettingService_GetGroomingServiceAvailability_Handler,
		},
		{
			MethodName: "UpdateGroomingServiceAvailability",
			Handler:    _OBAvailabilitySettingService_UpdateGroomingServiceAvailability_Handler,
		},
		{
			MethodName: "ListAvailableBookingTimeRange",
			Handler:    _OBAvailabilitySettingService_ListAvailableBookingTimeRange_Handler,
		},
		{
			MethodName: "ListAcceptedCustomerSetting",
			Handler:    _OBAvailabilitySettingService_ListAcceptedCustomerSetting_Handler,
		},
		{
			MethodName: "UpdateAcceptedCustomerSetting",
			Handler:    _OBAvailabilitySettingService_UpdateAcceptedCustomerSetting_Handler,
		},
		{
			MethodName: "ListArrivalPickUpTimeOverrides",
			Handler:    _OBAvailabilitySettingService_ListArrivalPickUpTimeOverrides_Handler,
		},
		{
			MethodName: "BatchCreateArrivalPickUpTimeOverride",
			Handler:    _OBAvailabilitySettingService_BatchCreateArrivalPickUpTimeOverride_Handler,
		},
		{
			MethodName: "BatchDeleteArrivalPickUpTimeOverride",
			Handler:    _OBAvailabilitySettingService_BatchDeleteArrivalPickUpTimeOverride_Handler,
		},
		{
			MethodName: "BatchUpdateArrivalPickUpTimeOverride",
			Handler:    _OBAvailabilitySettingService_BatchUpdateArrivalPickUpTimeOverride_Handler,
		},
		{
			MethodName: "ListCapacityOverrides",
			Handler:    _OBAvailabilitySettingService_ListCapacityOverrides_Handler,
		},
		{
			MethodName: "CreateCapacityOverride",
			Handler:    _OBAvailabilitySettingService_CreateCapacityOverride_Handler,
		},
		{
			MethodName: "DeleteCapacityOverride",
			Handler:    _OBAvailabilitySettingService_DeleteCapacityOverride_Handler,
		},
		{
			MethodName: "UpdateCapacityOverride",
			Handler:    _OBAvailabilitySettingService_UpdateCapacityOverride_Handler,
		},
		{
			MethodName: "InitOBService",
			Handler:    _OBAvailabilitySettingService_InitOBService_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/online_booking/v1/ob_availability_setting_service.proto",
}
