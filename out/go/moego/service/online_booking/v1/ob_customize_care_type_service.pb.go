// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/online_booking/v1/ob_customize_care_type_service.proto

package onlinebookingsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Create booking care type request
type CreateBookingCareTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// create booking care type def
	BookingCareType *v1.BookingCareType `protobuf:"bytes,1,opt,name=booking_care_type,json=bookingCareType,proto3" json:"booking_care_type,omitempty"`
}

func (x *CreateBookingCareTypeRequest) Reset() {
	*x = CreateBookingCareTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingCareTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingCareTypeRequest) ProtoMessage() {}

func (x *CreateBookingCareTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingCareTypeRequest.ProtoReflect.Descriptor instead.
func (*CreateBookingCareTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateBookingCareTypeRequest) GetBookingCareType() *v1.BookingCareType {
	if x != nil {
		return x.BookingCareType
	}
	return nil
}

// Create booking care type response
type CreateBookingCareTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// create booking care type result
	BookingCareType *v1.BookingCareType `protobuf:"bytes,1,opt,name=booking_care_type,json=bookingCareType,proto3" json:"booking_care_type,omitempty"`
}

func (x *CreateBookingCareTypeResponse) Reset() {
	*x = CreateBookingCareTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingCareTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingCareTypeResponse) ProtoMessage() {}

func (x *CreateBookingCareTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingCareTypeResponse.ProtoReflect.Descriptor instead.
func (*CreateBookingCareTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateBookingCareTypeResponse) GetBookingCareType() *v1.BookingCareType {
	if x != nil {
		return x.BookingCareType
	}
	return nil
}

// Update booking care type request
type UpdateBookingCareTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update booking care type def
	BookingCareType *v1.BookingCareType `protobuf:"bytes,1,opt,name=booking_care_type,json=bookingCareType,proto3" json:"booking_care_type,omitempty"`
}

func (x *UpdateBookingCareTypeRequest) Reset() {
	*x = UpdateBookingCareTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingCareTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingCareTypeRequest) ProtoMessage() {}

func (x *UpdateBookingCareTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingCareTypeRequest.ProtoReflect.Descriptor instead.
func (*UpdateBookingCareTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateBookingCareTypeRequest) GetBookingCareType() *v1.BookingCareType {
	if x != nil {
		return x.BookingCareType
	}
	return nil
}

// Update booking care type response
type UpdateBookingCareTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update booking care type result
	BookingCareType *v1.BookingCareType `protobuf:"bytes,1,opt,name=booking_care_type,json=bookingCareType,proto3" json:"booking_care_type,omitempty"`
}

func (x *UpdateBookingCareTypeResponse) Reset() {
	*x = UpdateBookingCareTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingCareTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingCareTypeResponse) ProtoMessage() {}

func (x *UpdateBookingCareTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingCareTypeResponse.ProtoReflect.Descriptor instead.
func (*UpdateBookingCareTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateBookingCareTypeResponse) GetBookingCareType() *v1.BookingCareType {
	if x != nil {
		return x.BookingCareType
	}
	return nil
}

// List booking care types request
type ListBookingCareTypesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *ListBookingCareTypesRequest) Reset() {
	*x = ListBookingCareTypesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBookingCareTypesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBookingCareTypesRequest) ProtoMessage() {}

func (x *ListBookingCareTypesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBookingCareTypesRequest.ProtoReflect.Descriptor instead.
func (*ListBookingCareTypesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListBookingCareTypesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListBookingCareTypesRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListBookingCareTypesRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// List booking care types response
type ListBookingCareTypesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking care type list
	BookingCareTypes []*v1.BookingCareTypeView `protobuf:"bytes,1,rep,name=booking_care_types,json=bookingCareTypes,proto3" json:"booking_care_types,omitempty"`
}

func (x *ListBookingCareTypesResponse) Reset() {
	*x = ListBookingCareTypesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBookingCareTypesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBookingCareTypesResponse) ProtoMessage() {}

func (x *ListBookingCareTypesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBookingCareTypesResponse.ProtoReflect.Descriptor instead.
func (*ListBookingCareTypesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListBookingCareTypesResponse) GetBookingCareTypes() []*v1.BookingCareTypeView {
	if x != nil {
		return x.BookingCareTypes
	}
	return nil
}

// Get booking care type request
type GetBookingCareTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking care type id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetBookingCareTypeRequest) Reset() {
	*x = GetBookingCareTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBookingCareTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookingCareTypeRequest) ProtoMessage() {}

func (x *GetBookingCareTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookingCareTypeRequest.ProtoReflect.Descriptor instead.
func (*GetBookingCareTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetBookingCareTypeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetBookingCareTypeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetBookingCareTypeRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// Get booking care type response
type GetBookingCareTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking care type
	BookingCareType *v1.BookingCareType `protobuf:"bytes,1,opt,name=booking_care_type,json=bookingCareType,proto3" json:"booking_care_type,omitempty"`
}

func (x *GetBookingCareTypeResponse) Reset() {
	*x = GetBookingCareTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBookingCareTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookingCareTypeResponse) ProtoMessage() {}

func (x *GetBookingCareTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookingCareTypeResponse.ProtoReflect.Descriptor instead.
func (*GetBookingCareTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetBookingCareTypeResponse) GetBookingCareType() *v1.BookingCareType {
	if x != nil {
		return x.BookingCareType
	}
	return nil
}

// Delete booking care type request
type DeleteBookingCareTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking care type id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *DeleteBookingCareTypeRequest) Reset() {
	*x = DeleteBookingCareTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteBookingCareTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBookingCareTypeRequest) ProtoMessage() {}

func (x *DeleteBookingCareTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBookingCareTypeRequest.ProtoReflect.Descriptor instead.
func (*DeleteBookingCareTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteBookingCareTypeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteBookingCareTypeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *DeleteBookingCareTypeRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *DeleteBookingCareTypeRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// Delete booking care type response
type DeleteBookingCareTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteBookingCareTypeResponse) Reset() {
	*x = DeleteBookingCareTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteBookingCareTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBookingCareTypeResponse) ProtoMessage() {}

func (x *DeleteBookingCareTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBookingCareTypeResponse.ProtoReflect.Descriptor instead.
func (*DeleteBookingCareTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescGZIP(), []int{9}
}

// Sort booking care type request
type SortBookingCareTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// booking care type ids
	BookingCareTypeIds []int64 `protobuf:"varint,4,rep,packed,name=booking_care_type_ids,json=bookingCareTypeIds,proto3" json:"booking_care_type_ids,omitempty"`
}

func (x *SortBookingCareTypeRequest) Reset() {
	*x = SortBookingCareTypeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortBookingCareTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortBookingCareTypeRequest) ProtoMessage() {}

func (x *SortBookingCareTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortBookingCareTypeRequest.ProtoReflect.Descriptor instead.
func (*SortBookingCareTypeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescGZIP(), []int{10}
}

func (x *SortBookingCareTypeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SortBookingCareTypeRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *SortBookingCareTypeRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *SortBookingCareTypeRequest) GetBookingCareTypeIds() []int64 {
	if x != nil {
		return x.BookingCareTypeIds
	}
	return nil
}

// Sort booking care type response
type SortBookingCareTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the sorted booking care type
	BookingCareTypes []*v1.BookingCareTypeView `protobuf:"bytes,1,rep,name=booking_care_types,json=bookingCareTypes,proto3" json:"booking_care_types,omitempty"`
}

func (x *SortBookingCareTypeResponse) Reset() {
	*x = SortBookingCareTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortBookingCareTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortBookingCareTypeResponse) ProtoMessage() {}

func (x *SortBookingCareTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortBookingCareTypeResponse.ProtoReflect.Descriptor instead.
func (*SortBookingCareTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescGZIP(), []int{11}
}

func (x *SortBookingCareTypeResponse) GetBookingCareTypes() []*v1.BookingCareTypeView {
	if x != nil {
		return x.BookingCareTypes
	}
	return nil
}

var File_moego_service_online_booking_v1_ob_customize_care_type_service_proto protoreflect.FileDescriptor

var file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDesc = []byte{
	0x0a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x6f, 0x62, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x5f, 0x63,
	0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x42, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x6f, 0x62, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x5f, 0x63, 0x61,
	0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a,
	0x65, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x85, 0x01, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x65, 0x0a, 0x11, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x7c,
	0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43,
	0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x5b, 0x0a, 0x11, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x85, 0x01, 0x0a,
	0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61,
	0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x65, 0x0a,
	0x11, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x0f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x7c, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x93, 0x01, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x22, 0x81, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73,
	0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x61, 0x0a, 0x12, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61,
	0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x86, 0x01, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x79, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x22, 0xad, 0x01, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64,
	0x22, 0x1f, 0x0a, 0x1d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0xd5, 0x01, 0x0a, 0x1a, 0x53, 0x6f, 0x72, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x15, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61,
	0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x73, 0x22, 0x80, 0x01, 0x0a, 0x1b, 0x53, 0x6f,
	0x72, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x61, 0x0a, 0x12, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61,
	0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x32, 0x9c, 0x07, 0x0a,
	0x16, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x96, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x96, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x14, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61,
	0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x8d, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61,
	0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43,
	0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x96, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x90, 0x01, 0x0a, 0x13, 0x53, 0x6f, 0x72,
	0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61,
	0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x6f, 0x72, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x94, 0x01, 0x0a, 0x27,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x67, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x76, 0x63,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescOnce sync.Once
	file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescData = file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDesc
)

func file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescGZIP() []byte {
	file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescOnce.Do(func() {
		file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescData)
	})
	return file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDescData
}

var file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_goTypes = []interface{}{
	(*CreateBookingCareTypeRequest)(nil),  // 0: moego.service.online_booking.v1.CreateBookingCareTypeRequest
	(*CreateBookingCareTypeResponse)(nil), // 1: moego.service.online_booking.v1.CreateBookingCareTypeResponse
	(*UpdateBookingCareTypeRequest)(nil),  // 2: moego.service.online_booking.v1.UpdateBookingCareTypeRequest
	(*UpdateBookingCareTypeResponse)(nil), // 3: moego.service.online_booking.v1.UpdateBookingCareTypeResponse
	(*ListBookingCareTypesRequest)(nil),   // 4: moego.service.online_booking.v1.ListBookingCareTypesRequest
	(*ListBookingCareTypesResponse)(nil),  // 5: moego.service.online_booking.v1.ListBookingCareTypesResponse
	(*GetBookingCareTypeRequest)(nil),     // 6: moego.service.online_booking.v1.GetBookingCareTypeRequest
	(*GetBookingCareTypeResponse)(nil),    // 7: moego.service.online_booking.v1.GetBookingCareTypeResponse
	(*DeleteBookingCareTypeRequest)(nil),  // 8: moego.service.online_booking.v1.DeleteBookingCareTypeRequest
	(*DeleteBookingCareTypeResponse)(nil), // 9: moego.service.online_booking.v1.DeleteBookingCareTypeResponse
	(*SortBookingCareTypeRequest)(nil),    // 10: moego.service.online_booking.v1.SortBookingCareTypeRequest
	(*SortBookingCareTypeResponse)(nil),   // 11: moego.service.online_booking.v1.SortBookingCareTypeResponse
	(*v1.BookingCareType)(nil),            // 12: moego.models.online_booking.v1.BookingCareType
	(*v1.BookingCareTypeView)(nil),        // 13: moego.models.online_booking.v1.BookingCareTypeView
}
var file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_depIdxs = []int32{
	12, // 0: moego.service.online_booking.v1.CreateBookingCareTypeRequest.booking_care_type:type_name -> moego.models.online_booking.v1.BookingCareType
	12, // 1: moego.service.online_booking.v1.CreateBookingCareTypeResponse.booking_care_type:type_name -> moego.models.online_booking.v1.BookingCareType
	12, // 2: moego.service.online_booking.v1.UpdateBookingCareTypeRequest.booking_care_type:type_name -> moego.models.online_booking.v1.BookingCareType
	12, // 3: moego.service.online_booking.v1.UpdateBookingCareTypeResponse.booking_care_type:type_name -> moego.models.online_booking.v1.BookingCareType
	13, // 4: moego.service.online_booking.v1.ListBookingCareTypesResponse.booking_care_types:type_name -> moego.models.online_booking.v1.BookingCareTypeView
	12, // 5: moego.service.online_booking.v1.GetBookingCareTypeResponse.booking_care_type:type_name -> moego.models.online_booking.v1.BookingCareType
	13, // 6: moego.service.online_booking.v1.SortBookingCareTypeResponse.booking_care_types:type_name -> moego.models.online_booking.v1.BookingCareTypeView
	0,  // 7: moego.service.online_booking.v1.BookingCareTypeService.CreateBookingCareType:input_type -> moego.service.online_booking.v1.CreateBookingCareTypeRequest
	2,  // 8: moego.service.online_booking.v1.BookingCareTypeService.UpdateBookingCareType:input_type -> moego.service.online_booking.v1.UpdateBookingCareTypeRequest
	4,  // 9: moego.service.online_booking.v1.BookingCareTypeService.ListBookingCareTypes:input_type -> moego.service.online_booking.v1.ListBookingCareTypesRequest
	6,  // 10: moego.service.online_booking.v1.BookingCareTypeService.GetBookingCareType:input_type -> moego.service.online_booking.v1.GetBookingCareTypeRequest
	8,  // 11: moego.service.online_booking.v1.BookingCareTypeService.DeleteBookingCareType:input_type -> moego.service.online_booking.v1.DeleteBookingCareTypeRequest
	10, // 12: moego.service.online_booking.v1.BookingCareTypeService.SortBookingCareType:input_type -> moego.service.online_booking.v1.SortBookingCareTypeRequest
	1,  // 13: moego.service.online_booking.v1.BookingCareTypeService.CreateBookingCareType:output_type -> moego.service.online_booking.v1.CreateBookingCareTypeResponse
	3,  // 14: moego.service.online_booking.v1.BookingCareTypeService.UpdateBookingCareType:output_type -> moego.service.online_booking.v1.UpdateBookingCareTypeResponse
	5,  // 15: moego.service.online_booking.v1.BookingCareTypeService.ListBookingCareTypes:output_type -> moego.service.online_booking.v1.ListBookingCareTypesResponse
	7,  // 16: moego.service.online_booking.v1.BookingCareTypeService.GetBookingCareType:output_type -> moego.service.online_booking.v1.GetBookingCareTypeResponse
	9,  // 17: moego.service.online_booking.v1.BookingCareTypeService.DeleteBookingCareType:output_type -> moego.service.online_booking.v1.DeleteBookingCareTypeResponse
	11, // 18: moego.service.online_booking.v1.BookingCareTypeService.SortBookingCareType:output_type -> moego.service.online_booking.v1.SortBookingCareTypeResponse
	13, // [13:19] is the sub-list for method output_type
	7,  // [7:13] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_init() }
func file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_init() {
	if File_moego_service_online_booking_v1_ob_customize_care_type_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingCareTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingCareTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingCareTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingCareTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBookingCareTypesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBookingCareTypesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBookingCareTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBookingCareTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteBookingCareTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteBookingCareTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortBookingCareTypeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortBookingCareTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_goTypes,
		DependencyIndexes: file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_depIdxs,
		MessageInfos:      file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_msgTypes,
	}.Build()
	File_moego_service_online_booking_v1_ob_customize_care_type_service_proto = out.File
	file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_rawDesc = nil
	file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_goTypes = nil
	file_moego_service_online_booking_v1_ob_customize_care_type_service_proto_depIdxs = nil
}
