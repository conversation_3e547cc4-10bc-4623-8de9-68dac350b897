// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/online_booking/v1/ob_customize_care_type_service.proto

package onlinebookingsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BookingCareTypeServiceClient is the client API for BookingCareTypeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BookingCareTypeServiceClient interface {
	// Create booking care type
	CreateBookingCareType(ctx context.Context, in *CreateBookingCareTypeRequest, opts ...grpc.CallOption) (*CreateBookingCareTypeResponse, error)
	// Update booking care type
	UpdateBookingCareType(ctx context.Context, in *UpdateBookingCareTypeRequest, opts ...grpc.CallOption) (*UpdateBookingCareTypeResponse, error)
	// List booking care types
	ListBookingCareTypes(ctx context.Context, in *ListBookingCareTypesRequest, opts ...grpc.CallOption) (*ListBookingCareTypesResponse, error)
	// Get booking care type
	GetBookingCareType(ctx context.Context, in *GetBookingCareTypeRequest, opts ...grpc.CallOption) (*GetBookingCareTypeResponse, error)
	// Delete booking care type
	DeleteBookingCareType(ctx context.Context, in *DeleteBookingCareTypeRequest, opts ...grpc.CallOption) (*DeleteBookingCareTypeResponse, error)
	// Sort booking care type
	SortBookingCareType(ctx context.Context, in *SortBookingCareTypeRequest, opts ...grpc.CallOption) (*SortBookingCareTypeResponse, error)
	// Update selected services
	UpdateSelectedServices(ctx context.Context, in *UpdateSelectedServicesRequest, opts ...grpc.CallOption) (*UpdateSelectedServicesResponse, error)
}

type bookingCareTypeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBookingCareTypeServiceClient(cc grpc.ClientConnInterface) BookingCareTypeServiceClient {
	return &bookingCareTypeServiceClient{cc}
}

func (c *bookingCareTypeServiceClient) CreateBookingCareType(ctx context.Context, in *CreateBookingCareTypeRequest, opts ...grpc.CallOption) (*CreateBookingCareTypeResponse, error) {
	out := new(CreateBookingCareTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingCareTypeService/CreateBookingCareType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingCareTypeServiceClient) UpdateBookingCareType(ctx context.Context, in *UpdateBookingCareTypeRequest, opts ...grpc.CallOption) (*UpdateBookingCareTypeResponse, error) {
	out := new(UpdateBookingCareTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingCareTypeService/UpdateBookingCareType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingCareTypeServiceClient) ListBookingCareTypes(ctx context.Context, in *ListBookingCareTypesRequest, opts ...grpc.CallOption) (*ListBookingCareTypesResponse, error) {
	out := new(ListBookingCareTypesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingCareTypeService/ListBookingCareTypes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingCareTypeServiceClient) GetBookingCareType(ctx context.Context, in *GetBookingCareTypeRequest, opts ...grpc.CallOption) (*GetBookingCareTypeResponse, error) {
	out := new(GetBookingCareTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingCareTypeService/GetBookingCareType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingCareTypeServiceClient) DeleteBookingCareType(ctx context.Context, in *DeleteBookingCareTypeRequest, opts ...grpc.CallOption) (*DeleteBookingCareTypeResponse, error) {
	out := new(DeleteBookingCareTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingCareTypeService/DeleteBookingCareType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingCareTypeServiceClient) SortBookingCareType(ctx context.Context, in *SortBookingCareTypeRequest, opts ...grpc.CallOption) (*SortBookingCareTypeResponse, error) {
	out := new(SortBookingCareTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingCareTypeService/SortBookingCareType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingCareTypeServiceClient) UpdateSelectedServices(ctx context.Context, in *UpdateSelectedServicesRequest, opts ...grpc.CallOption) (*UpdateSelectedServicesResponse, error) {
	out := new(UpdateSelectedServicesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingCareTypeService/UpdateSelectedServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BookingCareTypeServiceServer is the server API for BookingCareTypeService service.
// All implementations must embed UnimplementedBookingCareTypeServiceServer
// for forward compatibility
type BookingCareTypeServiceServer interface {
	// Create booking care type
	CreateBookingCareType(context.Context, *CreateBookingCareTypeRequest) (*CreateBookingCareTypeResponse, error)
	// Update booking care type
	UpdateBookingCareType(context.Context, *UpdateBookingCareTypeRequest) (*UpdateBookingCareTypeResponse, error)
	// List booking care types
	ListBookingCareTypes(context.Context, *ListBookingCareTypesRequest) (*ListBookingCareTypesResponse, error)
	// Get booking care type
	GetBookingCareType(context.Context, *GetBookingCareTypeRequest) (*GetBookingCareTypeResponse, error)
	// Delete booking care type
	DeleteBookingCareType(context.Context, *DeleteBookingCareTypeRequest) (*DeleteBookingCareTypeResponse, error)
	// Sort booking care type
	SortBookingCareType(context.Context, *SortBookingCareTypeRequest) (*SortBookingCareTypeResponse, error)
	// Update selected services
	UpdateSelectedServices(context.Context, *UpdateSelectedServicesRequest) (*UpdateSelectedServicesResponse, error)
	mustEmbedUnimplementedBookingCareTypeServiceServer()
}

// UnimplementedBookingCareTypeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBookingCareTypeServiceServer struct {
}

func (UnimplementedBookingCareTypeServiceServer) CreateBookingCareType(context.Context, *CreateBookingCareTypeRequest) (*CreateBookingCareTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBookingCareType not implemented")
}
func (UnimplementedBookingCareTypeServiceServer) UpdateBookingCareType(context.Context, *UpdateBookingCareTypeRequest) (*UpdateBookingCareTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBookingCareType not implemented")
}
func (UnimplementedBookingCareTypeServiceServer) ListBookingCareTypes(context.Context, *ListBookingCareTypesRequest) (*ListBookingCareTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBookingCareTypes not implemented")
}
func (UnimplementedBookingCareTypeServiceServer) GetBookingCareType(context.Context, *GetBookingCareTypeRequest) (*GetBookingCareTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBookingCareType not implemented")
}
func (UnimplementedBookingCareTypeServiceServer) DeleteBookingCareType(context.Context, *DeleteBookingCareTypeRequest) (*DeleteBookingCareTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBookingCareType not implemented")
}
func (UnimplementedBookingCareTypeServiceServer) SortBookingCareType(context.Context, *SortBookingCareTypeRequest) (*SortBookingCareTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortBookingCareType not implemented")
}
func (UnimplementedBookingCareTypeServiceServer) UpdateSelectedServices(context.Context, *UpdateSelectedServicesRequest) (*UpdateSelectedServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSelectedServices not implemented")
}
func (UnimplementedBookingCareTypeServiceServer) mustEmbedUnimplementedBookingCareTypeServiceServer() {
}

// UnsafeBookingCareTypeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BookingCareTypeServiceServer will
// result in compilation errors.
type UnsafeBookingCareTypeServiceServer interface {
	mustEmbedUnimplementedBookingCareTypeServiceServer()
}

func RegisterBookingCareTypeServiceServer(s grpc.ServiceRegistrar, srv BookingCareTypeServiceServer) {
	s.RegisterService(&BookingCareTypeService_ServiceDesc, srv)
}

func _BookingCareTypeService_CreateBookingCareType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBookingCareTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingCareTypeServiceServer).CreateBookingCareType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingCareTypeService/CreateBookingCareType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingCareTypeServiceServer).CreateBookingCareType(ctx, req.(*CreateBookingCareTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingCareTypeService_UpdateBookingCareType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBookingCareTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingCareTypeServiceServer).UpdateBookingCareType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingCareTypeService/UpdateBookingCareType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingCareTypeServiceServer).UpdateBookingCareType(ctx, req.(*UpdateBookingCareTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingCareTypeService_ListBookingCareTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBookingCareTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingCareTypeServiceServer).ListBookingCareTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingCareTypeService/ListBookingCareTypes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingCareTypeServiceServer).ListBookingCareTypes(ctx, req.(*ListBookingCareTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingCareTypeService_GetBookingCareType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBookingCareTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingCareTypeServiceServer).GetBookingCareType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingCareTypeService/GetBookingCareType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingCareTypeServiceServer).GetBookingCareType(ctx, req.(*GetBookingCareTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingCareTypeService_DeleteBookingCareType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBookingCareTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingCareTypeServiceServer).DeleteBookingCareType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingCareTypeService/DeleteBookingCareType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingCareTypeServiceServer).DeleteBookingCareType(ctx, req.(*DeleteBookingCareTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingCareTypeService_SortBookingCareType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortBookingCareTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingCareTypeServiceServer).SortBookingCareType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingCareTypeService/SortBookingCareType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingCareTypeServiceServer).SortBookingCareType(ctx, req.(*SortBookingCareTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingCareTypeService_UpdateSelectedServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSelectedServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingCareTypeServiceServer).UpdateSelectedServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingCareTypeService/UpdateSelectedServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingCareTypeServiceServer).UpdateSelectedServices(ctx, req.(*UpdateSelectedServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BookingCareTypeService_ServiceDesc is the grpc.ServiceDesc for BookingCareTypeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BookingCareTypeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.online_booking.v1.BookingCareTypeService",
	HandlerType: (*BookingCareTypeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBookingCareType",
			Handler:    _BookingCareTypeService_CreateBookingCareType_Handler,
		},
		{
			MethodName: "UpdateBookingCareType",
			Handler:    _BookingCareTypeService_UpdateBookingCareType_Handler,
		},
		{
			MethodName: "ListBookingCareTypes",
			Handler:    _BookingCareTypeService_ListBookingCareTypes_Handler,
		},
		{
			MethodName: "GetBookingCareType",
			Handler:    _BookingCareTypeService_GetBookingCareType_Handler,
		},
		{
			MethodName: "DeleteBookingCareType",
			Handler:    _BookingCareTypeService_DeleteBookingCareType_Handler,
		},
		{
			MethodName: "SortBookingCareType",
			Handler:    _BookingCareTypeService_SortBookingCareType_Handler,
		},
		{
			MethodName: "UpdateSelectedServices",
			Handler:    _BookingCareTypeService_UpdateSelectedServices_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/online_booking/v1/ob_customize_care_type_service.proto",
}
