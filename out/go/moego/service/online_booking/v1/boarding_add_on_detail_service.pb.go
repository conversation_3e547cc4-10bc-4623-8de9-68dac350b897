// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/online_booking/v1/boarding_add_on_detail_service.proto

package onlinebookingsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Create BoardingAddOnDetail request
type CreateBoardingAddOnDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The id of pet, associated with the current add-on
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// The id of current add-on service
	AddOnId int64 `protobuf:"varint,4,opt,name=add_on_id,json=addOnId,proto3" json:"add_on_id,omitempty"`
	// The specific dates of the add-on service
	SpecificDates []string `protobuf:"bytes,5,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// whether the add-on service is everyday, not include checkout day
	// deprecated. use date_type instead
	//
	// Deprecated: Do not use.
	IsEveryday *bool `protobuf:"varint,6,opt,name=is_everyday,json=isEveryday,proto3,oneof" json:"is_everyday,omitempty"`
	// The price of current add-on service
	ServicePrice *float64 `protobuf:"fixed64,7,opt,name=service_price,json=servicePrice,proto3,oneof" json:"service_price,omitempty"`
	// taxId
	TaxId *int64 `protobuf:"varint,8,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
	// duration
	Duration *int32 `protobuf:"varint,9,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// createdAt
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3,oneof" json:"created_at,omitempty"`
	// updatedAt
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3,oneof" json:"updated_at,omitempty"`
	// quantity per day
	QuantityPerDay *int32 `protobuf:"varint,12,opt,name=quantity_per_day,json=quantityPerDay,proto3,oneof" json:"quantity_per_day,omitempty"`
	// date type
	DateType *v1.PetDetailDateType `protobuf:"varint,13,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.PetDetailDateType,oneof" json:"date_type,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,14,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
}

func (x *CreateBoardingAddOnDetailRequest) Reset() {
	*x = CreateBoardingAddOnDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBoardingAddOnDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBoardingAddOnDetailRequest) ProtoMessage() {}

func (x *CreateBoardingAddOnDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBoardingAddOnDetailRequest.ProtoReflect.Descriptor instead.
func (*CreateBoardingAddOnDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateBoardingAddOnDetailRequest) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *CreateBoardingAddOnDetailRequest) GetAddOnId() int64 {
	if x != nil {
		return x.AddOnId
	}
	return 0
}

func (x *CreateBoardingAddOnDetailRequest) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

// Deprecated: Do not use.
func (x *CreateBoardingAddOnDetailRequest) GetIsEveryday() bool {
	if x != nil && x.IsEveryday != nil {
		return *x.IsEveryday
	}
	return false
}

func (x *CreateBoardingAddOnDetailRequest) GetServicePrice() float64 {
	if x != nil && x.ServicePrice != nil {
		return *x.ServicePrice
	}
	return 0
}

func (x *CreateBoardingAddOnDetailRequest) GetTaxId() int64 {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return 0
}

func (x *CreateBoardingAddOnDetailRequest) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *CreateBoardingAddOnDetailRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CreateBoardingAddOnDetailRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *CreateBoardingAddOnDetailRequest) GetQuantityPerDay() int32 {
	if x != nil && x.QuantityPerDay != nil {
		return *x.QuantityPerDay
	}
	return 0
}

func (x *CreateBoardingAddOnDetailRequest) GetDateType() v1.PetDetailDateType {
	if x != nil && x.DateType != nil {
		return *x.DateType
	}
	return v1.PetDetailDateType(0)
}

func (x *CreateBoardingAddOnDetailRequest) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

// Update BoardingAddOnDetail response
type UpdateBoardingAddOnDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The id of boarding add-on detail
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// date type
	DateType *v1.PetDetailDateType `protobuf:"varint,2,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.PetDetailDateType,oneof" json:"date_type,omitempty"`
	// The specific dates of the add-on service. VValid only when date_type is PET_DETAIL_DATE_SPECIFIC_DATE
	SpecificDates *v11.StringListValue `protobuf:"bytes,3,opt,name=specific_dates,json=specificDates,proto3,oneof" json:"specific_dates,omitempty"`
	// quantity per day
	QuantityPerDay *int32 `protobuf:"varint,4,opt,name=quantity_per_day,json=quantityPerDay,proto3,oneof" json:"quantity_per_day,omitempty"`
	// start date
	// 当 date_type 为 PET_DETAIL_DATE_SPECIFIC_DATE 时使用
	StartDate *string `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
}

func (x *UpdateBoardingAddOnDetailRequest) Reset() {
	*x = UpdateBoardingAddOnDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBoardingAddOnDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBoardingAddOnDetailRequest) ProtoMessage() {}

func (x *UpdateBoardingAddOnDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBoardingAddOnDetailRequest.ProtoReflect.Descriptor instead.
func (*UpdateBoardingAddOnDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateBoardingAddOnDetailRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateBoardingAddOnDetailRequest) GetDateType() v1.PetDetailDateType {
	if x != nil && x.DateType != nil {
		return *x.DateType
	}
	return v1.PetDetailDateType(0)
}

func (x *UpdateBoardingAddOnDetailRequest) GetSpecificDates() *v11.StringListValue {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *UpdateBoardingAddOnDetailRequest) GetQuantityPerDay() int32 {
	if x != nil && x.QuantityPerDay != nil {
		return *x.QuantityPerDay
	}
	return 0
}

func (x *UpdateBoardingAddOnDetailRequest) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

var File_moego_service_online_booking_v1_boarding_add_on_detail_service_proto protoreflect.FileDescriptor

var file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_rawDesc = []byte{
	0x0a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x5f, 0x6f,
	0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x82, 0x06, 0x0a, 0x20, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64,
	0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x23, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x61, 0x64, 0x64,
	0x4f, 0x6e, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1f, 0xfa, 0x42,
	0x1c, 0x92, 0x01, 0x19, 0x22, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d,
	0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x0d, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x28, 0x0a, 0x0b,
	0x69, 0x73, 0x5f, 0x65, 0x76, 0x65, 0x72, 0x79, 0x64, 0x61, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x0a, 0x69, 0x73, 0x45, 0x76, 0x65, 0x72, 0x79,
	0x64, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x48, 0x01, 0x52,
	0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x1a, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03,
	0x48, 0x02, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x48, 0x03,
	0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x04, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x05, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a,
	0x10, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x61,
	0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x48, 0x06, 0x52, 0x0e, 0x71, 0x75, 0x61, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x50, 0x65, 0x72, 0x44, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x50, 0x0a, 0x09,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48,
	0x07, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x35,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x08, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x69, 0x73, 0x5f, 0x65, 0x76, 0x65,
	0x72, 0x79, 0x64, 0x61, 0x79, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x74, 0x61, 0x78, 0x5f,
	0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x13, 0x0a,
	0x11, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64,
	0x61, 0x79, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22,
	0xa3, 0x03, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x5c, 0x0a,
	0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x08,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x0e, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x48, 0x01, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x44, 0x61, 0x74, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x10, 0x71, 0x75, 0x61, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x02, 0x52, 0x0e, 0x71,
	0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x50, 0x65, 0x72, 0x44, 0x61, 0x79, 0x88, 0x01, 0x01,
	0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64,
	0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24,
	0x48, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01,
	0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x11,
	0x0a, 0x0f, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x73, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70,
	0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x32, 0x1c, 0x0a, 0x1a, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x42, 0x94, 0x01, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x67, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_rawDescOnce sync.Once
	file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_rawDescData = file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_rawDesc
)

func file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_rawDescGZIP() []byte {
	file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_rawDescOnce.Do(func() {
		file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_rawDescData)
	})
	return file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_rawDescData
}

var file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_goTypes = []interface{}{
	(*CreateBoardingAddOnDetailRequest)(nil), // 0: moego.service.online_booking.v1.CreateBoardingAddOnDetailRequest
	(*UpdateBoardingAddOnDetailRequest)(nil), // 1: moego.service.online_booking.v1.UpdateBoardingAddOnDetailRequest
	(*timestamppb.Timestamp)(nil),            // 2: google.protobuf.Timestamp
	(v1.PetDetailDateType)(0),                // 3: moego.models.appointment.v1.PetDetailDateType
	(*date.Date)(nil),                        // 4: google.type.Date
	(*v11.StringListValue)(nil),              // 5: moego.utils.v1.StringListValue
}
var file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_depIdxs = []int32{
	2, // 0: moego.service.online_booking.v1.CreateBoardingAddOnDetailRequest.created_at:type_name -> google.protobuf.Timestamp
	2, // 1: moego.service.online_booking.v1.CreateBoardingAddOnDetailRequest.updated_at:type_name -> google.protobuf.Timestamp
	3, // 2: moego.service.online_booking.v1.CreateBoardingAddOnDetailRequest.date_type:type_name -> moego.models.appointment.v1.PetDetailDateType
	4, // 3: moego.service.online_booking.v1.CreateBoardingAddOnDetailRequest.start_date:type_name -> google.type.Date
	3, // 4: moego.service.online_booking.v1.UpdateBoardingAddOnDetailRequest.date_type:type_name -> moego.models.appointment.v1.PetDetailDateType
	5, // 5: moego.service.online_booking.v1.UpdateBoardingAddOnDetailRequest.specific_dates:type_name -> moego.utils.v1.StringListValue
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_init() }
func file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_init() {
	if File_moego_service_online_booking_v1_boarding_add_on_detail_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBoardingAddOnDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBoardingAddOnDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_goTypes,
		DependencyIndexes: file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_depIdxs,
		MessageInfos:      file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_msgTypes,
	}.Build()
	File_moego_service_online_booking_v1_boarding_add_on_detail_service_proto = out.File
	file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_rawDesc = nil
	file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_goTypes = nil
	file_moego_service_online_booking_v1_boarding_add_on_detail_service_proto_depIdxs = nil
}
