// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/business_customer/v1/business_pet_evaluation_service.proto

package businesscustomersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessPetEvaluationServiceClient is the client API for BusinessPetEvaluationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessPetEvaluationServiceClient interface {
	// create pet evaluation history
	CreatePetEvaluationHistory(ctx context.Context, in *CreatePetEvaluationHistoryRequest, opts ...grpc.CallOption) (*CreatePetEvaluationHistoryResponse, error)
	// list pet evaluation history
	ListPetEvaluationHistory(ctx context.Context, in *ListPetEvaluationHistoryRequest, opts ...grpc.CallOption) (*ListPetEvaluationHistoryResponse, error)
	// pet reset evaluation
	ResetPetEvaluationTask(ctx context.Context, in *ResetPetEvaluationTaskRequest, opts ...grpc.CallOption) (*ResetPetEvaluationTaskResponse, error)
	// update pet evaluation
	UpdatePetEvaluation(ctx context.Context, in *UpdatePetEvaluationRequest, opts ...grpc.CallOption) (*UpdatePetEvaluationResponse, error)
	// List pet evaluation
	ListPetEvaluation(ctx context.Context, in *ListPetEvaluationRequest, opts ...grpc.CallOption) (*ListPetEvaluationResponse, error)
}

type businessPetEvaluationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessPetEvaluationServiceClient(cc grpc.ClientConnInterface) BusinessPetEvaluationServiceClient {
	return &businessPetEvaluationServiceClient{cc}
}

func (c *businessPetEvaluationServiceClient) CreatePetEvaluationHistory(ctx context.Context, in *CreatePetEvaluationHistoryRequest, opts ...grpc.CallOption) (*CreatePetEvaluationHistoryResponse, error) {
	out := new(CreatePetEvaluationHistoryResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetEvaluationService/CreatePetEvaluationHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetEvaluationServiceClient) ListPetEvaluationHistory(ctx context.Context, in *ListPetEvaluationHistoryRequest, opts ...grpc.CallOption) (*ListPetEvaluationHistoryResponse, error) {
	out := new(ListPetEvaluationHistoryResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetEvaluationService/ListPetEvaluationHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetEvaluationServiceClient) ResetPetEvaluationTask(ctx context.Context, in *ResetPetEvaluationTaskRequest, opts ...grpc.CallOption) (*ResetPetEvaluationTaskResponse, error) {
	out := new(ResetPetEvaluationTaskResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetEvaluationService/ResetPetEvaluationTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetEvaluationServiceClient) UpdatePetEvaluation(ctx context.Context, in *UpdatePetEvaluationRequest, opts ...grpc.CallOption) (*UpdatePetEvaluationResponse, error) {
	out := new(UpdatePetEvaluationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetEvaluationService/UpdatePetEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetEvaluationServiceClient) ListPetEvaluation(ctx context.Context, in *ListPetEvaluationRequest, opts ...grpc.CallOption) (*ListPetEvaluationResponse, error) {
	out := new(ListPetEvaluationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetEvaluationService/ListPetEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessPetEvaluationServiceServer is the server API for BusinessPetEvaluationService service.
// All implementations must embed UnimplementedBusinessPetEvaluationServiceServer
// for forward compatibility
type BusinessPetEvaluationServiceServer interface {
	// create pet evaluation history
	CreatePetEvaluationHistory(context.Context, *CreatePetEvaluationHistoryRequest) (*CreatePetEvaluationHistoryResponse, error)
	// list pet evaluation history
	ListPetEvaluationHistory(context.Context, *ListPetEvaluationHistoryRequest) (*ListPetEvaluationHistoryResponse, error)
	// pet reset evaluation
	ResetPetEvaluationTask(context.Context, *ResetPetEvaluationTaskRequest) (*ResetPetEvaluationTaskResponse, error)
	// update pet evaluation
	UpdatePetEvaluation(context.Context, *UpdatePetEvaluationRequest) (*UpdatePetEvaluationResponse, error)
	// List pet evaluation
	ListPetEvaluation(context.Context, *ListPetEvaluationRequest) (*ListPetEvaluationResponse, error)
	mustEmbedUnimplementedBusinessPetEvaluationServiceServer()
}

// UnimplementedBusinessPetEvaluationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessPetEvaluationServiceServer struct {
}

func (UnimplementedBusinessPetEvaluationServiceServer) CreatePetEvaluationHistory(context.Context, *CreatePetEvaluationHistoryRequest) (*CreatePetEvaluationHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetEvaluationHistory not implemented")
}
func (UnimplementedBusinessPetEvaluationServiceServer) ListPetEvaluationHistory(context.Context, *ListPetEvaluationHistoryRequest) (*ListPetEvaluationHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetEvaluationHistory not implemented")
}
func (UnimplementedBusinessPetEvaluationServiceServer) ResetPetEvaluationTask(context.Context, *ResetPetEvaluationTaskRequest) (*ResetPetEvaluationTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetPetEvaluationTask not implemented")
}
func (UnimplementedBusinessPetEvaluationServiceServer) UpdatePetEvaluation(context.Context, *UpdatePetEvaluationRequest) (*UpdatePetEvaluationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetEvaluation not implemented")
}
func (UnimplementedBusinessPetEvaluationServiceServer) ListPetEvaluation(context.Context, *ListPetEvaluationRequest) (*ListPetEvaluationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetEvaluation not implemented")
}
func (UnimplementedBusinessPetEvaluationServiceServer) mustEmbedUnimplementedBusinessPetEvaluationServiceServer() {
}

// UnsafeBusinessPetEvaluationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessPetEvaluationServiceServer will
// result in compilation errors.
type UnsafeBusinessPetEvaluationServiceServer interface {
	mustEmbedUnimplementedBusinessPetEvaluationServiceServer()
}

func RegisterBusinessPetEvaluationServiceServer(s grpc.ServiceRegistrar, srv BusinessPetEvaluationServiceServer) {
	s.RegisterService(&BusinessPetEvaluationService_ServiceDesc, srv)
}

func _BusinessPetEvaluationService_CreatePetEvaluationHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetEvaluationHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetEvaluationServiceServer).CreatePetEvaluationHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetEvaluationService/CreatePetEvaluationHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetEvaluationServiceServer).CreatePetEvaluationHistory(ctx, req.(*CreatePetEvaluationHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetEvaluationService_ListPetEvaluationHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetEvaluationHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetEvaluationServiceServer).ListPetEvaluationHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetEvaluationService/ListPetEvaluationHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetEvaluationServiceServer).ListPetEvaluationHistory(ctx, req.(*ListPetEvaluationHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetEvaluationService_ResetPetEvaluationTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetPetEvaluationTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetEvaluationServiceServer).ResetPetEvaluationTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetEvaluationService/ResetPetEvaluationTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetEvaluationServiceServer).ResetPetEvaluationTask(ctx, req.(*ResetPetEvaluationTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetEvaluationService_UpdatePetEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetEvaluationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetEvaluationServiceServer).UpdatePetEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetEvaluationService/UpdatePetEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetEvaluationServiceServer).UpdatePetEvaluation(ctx, req.(*UpdatePetEvaluationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetEvaluationService_ListPetEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetEvaluationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetEvaluationServiceServer).ListPetEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetEvaluationService/ListPetEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetEvaluationServiceServer).ListPetEvaluation(ctx, req.(*ListPetEvaluationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessPetEvaluationService_ServiceDesc is the grpc.ServiceDesc for BusinessPetEvaluationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessPetEvaluationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.business_customer.v1.BusinessPetEvaluationService",
	HandlerType: (*BusinessPetEvaluationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePetEvaluationHistory",
			Handler:    _BusinessPetEvaluationService_CreatePetEvaluationHistory_Handler,
		},
		{
			MethodName: "ListPetEvaluationHistory",
			Handler:    _BusinessPetEvaluationService_ListPetEvaluationHistory_Handler,
		},
		{
			MethodName: "ResetPetEvaluationTask",
			Handler:    _BusinessPetEvaluationService_ResetPetEvaluationTask_Handler,
		},
		{
			MethodName: "UpdatePetEvaluation",
			Handler:    _BusinessPetEvaluationService_UpdatePetEvaluation_Handler,
		},
		{
			MethodName: "ListPetEvaluation",
			Handler:    _BusinessPetEvaluationService_ListPetEvaluation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/business_customer/v1/business_pet_evaluation_service.proto",
}
