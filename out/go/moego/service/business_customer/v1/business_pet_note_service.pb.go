// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/business_customer/v1/business_pet_note_service.proto

package businesscustomersvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list pet note request
type ListPetNoteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id, optional
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pagination, required
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListPetNoteRequest) Reset() {
	*x = ListPetNoteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetNoteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetNoteRequest) ProtoMessage() {}

func (x *ListPetNoteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetNoteRequest.ProtoReflect.Descriptor instead.
func (*ListPetNoteRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListPetNoteRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListPetNoteRequest) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListPetNoteRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list pet note response
type ListPetNoteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet note list
	Notes []*v1.BusinessPetNoteModel `protobuf:"bytes,1,rep,name=notes,proto3" json:"notes,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListPetNoteResponse) Reset() {
	*x = ListPetNoteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetNoteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetNoteResponse) ProtoMessage() {}

func (x *ListPetNoteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetNoteResponse.ProtoReflect.Descriptor instead.
func (*ListPetNoteResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListPetNoteResponse) GetNotes() []*v1.BusinessPetNoteModel {
	if x != nil {
		return x.Notes
	}
	return nil
}

func (x *ListPetNoteResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// batch list pet note request
type BatchListPetNoteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet ids
	PetIds []int64 `protobuf:"varint,1,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
}

func (x *BatchListPetNoteRequest) Reset() {
	*x = BatchListPetNoteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchListPetNoteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchListPetNoteRequest) ProtoMessage() {}

func (x *BatchListPetNoteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchListPetNoteRequest.ProtoReflect.Descriptor instead.
func (*BatchListPetNoteRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{2}
}

func (x *BatchListPetNoteRequest) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

// batch list pet notes response
type BatchListPetNoteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id to notes
	PetNotesMap map[int64]*BatchListPetNoteResponse_PetNotes `protobuf:"bytes,1,rep,name=pet_notes_map,json=petNotesMap,proto3" json:"pet_notes_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BatchListPetNoteResponse) Reset() {
	*x = BatchListPetNoteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchListPetNoteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchListPetNoteResponse) ProtoMessage() {}

func (x *BatchListPetNoteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchListPetNoteResponse.ProtoReflect.Descriptor instead.
func (*BatchListPetNoteResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{3}
}

func (x *BatchListPetNoteResponse) GetPetNotesMap() map[int64]*BatchListPetNoteResponse_PetNotes {
	if x != nil {
		return x.PetNotesMap
	}
	return nil
}

// create pet note request
type CreatePetNoteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// created by staff id
	CreatedBy *int64 `protobuf:"varint,1,opt,name=created_by,json=createdBy,proto3,oneof" json:"created_by,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet note
	Note *v1.BusinessPetNoteCreateDef `protobuf:"bytes,3,opt,name=note,proto3" json:"note,omitempty"`
}

func (x *CreatePetNoteRequest) Reset() {
	*x = CreatePetNoteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetNoteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetNoteRequest) ProtoMessage() {}

func (x *CreatePetNoteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetNoteRequest.ProtoReflect.Descriptor instead.
func (*CreatePetNoteRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreatePetNoteRequest) GetCreatedBy() int64 {
	if x != nil && x.CreatedBy != nil {
		return *x.CreatedBy
	}
	return 0
}

func (x *CreatePetNoteRequest) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *CreatePetNoteRequest) GetNote() *v1.BusinessPetNoteCreateDef {
	if x != nil {
		return x.Note
	}
	return nil
}

// create pet note response
type CreatePetNoteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet note
	Note *v1.BusinessPetNoteModel `protobuf:"bytes,1,opt,name=note,proto3" json:"note,omitempty"`
}

func (x *CreatePetNoteResponse) Reset() {
	*x = CreatePetNoteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetNoteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetNoteResponse) ProtoMessage() {}

func (x *CreatePetNoteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetNoteResponse.ProtoReflect.Descriptor instead.
func (*CreatePetNoteResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreatePetNoteResponse) GetNote() *v1.BusinessPetNoteModel {
	if x != nil {
		return x.Note
	}
	return nil
}

// update pet note request
type UpdatePetNoteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// updated by staff id
	UpdatedBy *int64 `protobuf:"varint,1,opt,name=updated_by,json=updatedBy,proto3,oneof" json:"updated_by,omitempty"`
	// pet note id
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// pet note
	Note *v1.BusinessPetNoteUpdateDef `protobuf:"bytes,3,opt,name=note,proto3" json:"note,omitempty"`
}

func (x *UpdatePetNoteRequest) Reset() {
	*x = UpdatePetNoteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetNoteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetNoteRequest) ProtoMessage() {}

func (x *UpdatePetNoteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetNoteRequest.ProtoReflect.Descriptor instead.
func (*UpdatePetNoteRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdatePetNoteRequest) GetUpdatedBy() int64 {
	if x != nil && x.UpdatedBy != nil {
		return *x.UpdatedBy
	}
	return 0
}

func (x *UpdatePetNoteRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePetNoteRequest) GetNote() *v1.BusinessPetNoteUpdateDef {
	if x != nil {
		return x.Note
	}
	return nil
}

// update pet note response
type UpdatePetNoteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet note
	Note *v1.BusinessPetNoteModel `protobuf:"bytes,1,opt,name=note,proto3" json:"note,omitempty"`
}

func (x *UpdatePetNoteResponse) Reset() {
	*x = UpdatePetNoteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetNoteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetNoteResponse) ProtoMessage() {}

func (x *UpdatePetNoteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetNoteResponse.ProtoReflect.Descriptor instead.
func (*UpdatePetNoteResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdatePetNoteResponse) GetNote() *v1.BusinessPetNoteModel {
	if x != nil {
		return x.Note
	}
	return nil
}

// delete pet note request
type DeletePetNoteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// deleted by staff id
	DeletedBy *int64 `protobuf:"varint,1,opt,name=deleted_by,json=deletedBy,proto3,oneof" json:"deleted_by,omitempty"`
	// pet note id
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletePetNoteRequest) Reset() {
	*x = DeletePetNoteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetNoteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetNoteRequest) ProtoMessage() {}

func (x *DeletePetNoteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetNoteRequest.ProtoReflect.Descriptor instead.
func (*DeletePetNoteRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{8}
}

func (x *DeletePetNoteRequest) GetDeletedBy() int64 {
	if x != nil && x.DeletedBy != nil {
		return *x.DeletedBy
	}
	return 0
}

func (x *DeletePetNoteRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete pet note response
type DeletePetNoteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePetNoteResponse) Reset() {
	*x = DeletePetNoteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetNoteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetNoteResponse) ProtoMessage() {}

func (x *DeletePetNoteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetNoteResponse.ProtoReflect.Descriptor instead.
func (*DeletePetNoteResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{9}
}

// get pet note request
type GetPetNoteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet note id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetPetNoteRequest) Reset() {
	*x = GetPetNoteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetNoteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetNoteRequest) ProtoMessage() {}

func (x *GetPetNoteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetNoteRequest.ProtoReflect.Descriptor instead.
func (*GetPetNoteRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetPetNoteRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get pet note response
type GetPetNoteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet note
	Note *v1.BusinessPetNoteModel `protobuf:"bytes,1,opt,name=note,proto3" json:"note,omitempty"`
}

func (x *GetPetNoteResponse) Reset() {
	*x = GetPetNoteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetNoteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetNoteResponse) ProtoMessage() {}

func (x *GetPetNoteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetNoteResponse.ProtoReflect.Descriptor instead.
func (*GetPetNoteResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetPetNoteResponse) GetNote() *v1.BusinessPetNoteModel {
	if x != nil {
		return x.Note
	}
	return nil
}

// get pet latest note request
type GetPetLatestNoteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
}

func (x *GetPetLatestNoteRequest) Reset() {
	*x = GetPetLatestNoteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetLatestNoteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetLatestNoteRequest) ProtoMessage() {}

func (x *GetPetLatestNoteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetLatestNoteRequest.ProtoReflect.Descriptor instead.
func (*GetPetLatestNoteRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetPetLatestNoteRequest) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

// get pet latest note response
type GetPetLatestNoteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet note, may not exist
	Note *v1.BusinessPetNoteModel `protobuf:"bytes,1,opt,name=note,proto3,oneof" json:"note,omitempty"`
}

func (x *GetPetLatestNoteResponse) Reset() {
	*x = GetPetLatestNoteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetLatestNoteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetLatestNoteResponse) ProtoMessage() {}

func (x *GetPetLatestNoteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetLatestNoteResponse.ProtoReflect.Descriptor instead.
func (*GetPetLatestNoteResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetPetLatestNoteResponse) GetNote() *v1.BusinessPetNoteModel {
	if x != nil {
		return x.Note
	}
	return nil
}

// pin pet note request
type PinPetNoteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// updated by staff id
	UpdatedBy *int64 `protobuf:"varint,1,opt,name=updated_by,json=updatedBy,proto3,oneof" json:"updated_by,omitempty"`
	// pet note id
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// is pinned
	IsPinned bool `protobuf:"varint,3,opt,name=is_pinned,json=isPinned,proto3" json:"is_pinned,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,5,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *PinPetNoteRequest) Reset() {
	*x = PinPetNoteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PinPetNoteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinPetNoteRequest) ProtoMessage() {}

func (x *PinPetNoteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinPetNoteRequest.ProtoReflect.Descriptor instead.
func (*PinPetNoteRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{14}
}

func (x *PinPetNoteRequest) GetUpdatedBy() int64 {
	if x != nil && x.UpdatedBy != nil {
		return *x.UpdatedBy
	}
	return 0
}

func (x *PinPetNoteRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PinPetNoteRequest) GetIsPinned() bool {
	if x != nil {
		return x.IsPinned
	}
	return false
}

func (x *PinPetNoteRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *PinPetNoteRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// pin pet note response
type PinPetNoteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet note
	Note *v1.BusinessPetNoteModel `protobuf:"bytes,1,opt,name=note,proto3" json:"note,omitempty"`
}

func (x *PinPetNoteResponse) Reset() {
	*x = PinPetNoteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PinPetNoteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinPetNoteResponse) ProtoMessage() {}

func (x *PinPetNoteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinPetNoteResponse.ProtoReflect.Descriptor instead.
func (*PinPetNoteResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{15}
}

func (x *PinPetNoteResponse) GetNote() *v1.BusinessPetNoteModel {
	if x != nil {
		return x.Note
	}
	return nil
}

// notes
type BatchListPetNoteResponse_PetNotes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// notes
	Notes []*v1.BusinessPetNoteModel `protobuf:"bytes,1,rep,name=notes,proto3" json:"notes,omitempty"`
}

func (x *BatchListPetNoteResponse_PetNotes) Reset() {
	*x = BatchListPetNoteResponse_PetNotes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchListPetNoteResponse_PetNotes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchListPetNoteResponse_PetNotes) ProtoMessage() {}

func (x *BatchListPetNoteResponse_PetNotes) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchListPetNoteResponse_PetNotes.ProtoReflect.Descriptor instead.
func (*BatchListPetNoteResponse_PetNotes) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP(), []int{3, 0}
}

func (x *BatchListPetNoteResponse_PetNotes) GetNotes() []*v1.BusinessPetNoteModel {
	if x != nil {
		return x.Notes
	}
	return nil
}

var File_moego_service_business_customer_v1_business_pet_note_service_proto protoreflect.FileDescriptor

var file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDesc = []byte{
	0x0a, 0x42, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x22, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x3e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x64, 0x65,
	0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9f, 0x01,
	0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28,
	0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06,
	0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0xa8, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x45, 0x0a, 0x17, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01,
	0x10, 0xf4, 0x03, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x70, 0x65, 0x74, 0x49, 0x64,
	0x73, 0x22, 0xf0, 0x02, 0x0a, 0x18, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x71,
	0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x4d, 0x61,
	0x70, 0x1a, 0x59, 0x0a, 0x08, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x4d, 0x0a,
	0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x1a, 0x85, 0x01, 0x0a,
	0x10, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x5b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xcd, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x59, 0x0a, 0x04, 0x6e, 0x6f,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x04, 0x6e, 0x6f, 0x74, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x22, 0x64, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65,
	0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a,
	0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x22, 0xc6, 0x01, 0x0a, 0x14, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x48, 0x00, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x88, 0x01, 0x01,
	0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x59, 0x0a, 0x04, 0x6e, 0x6f, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04,
	0x6e, 0x6f, 0x74, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x22, 0x64, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74,
	0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x04,
	0x6e, 0x6f, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x22, 0x6b, 0x0a, 0x14, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2b, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00,
	0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x42, 0x79, 0x88, 0x01, 0x01, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x22, 0x17, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x2c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x61, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65,
	0x74, 0x4e, 0x6f, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65,
	0x22, 0x39, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74,
	0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x06, 0x70,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x22, 0x75, 0x0a, 0x18, 0x47,
	0x65, 0x74, 0x50, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x00,
	0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x6f,
	0x74, 0x65, 0x22, 0xec, 0x01, 0x0a, 0x11, 0x50, 0x69, 0x6e, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x42, 0x79, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x73, 0x5f, 0x70, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x69, 0x73, 0x50, 0x69, 0x6e, 0x6e, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x01, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x22, 0x61, 0x0a, 0x12, 0x50, 0x69, 0x6e, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04,
	0x6e, 0x6f, 0x74, 0x65, 0x32, 0xc7, 0x08, 0x0a, 0x16, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x84, 0x01, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74,
	0x65, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74,
	0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65,
	0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x84, 0x01,
	0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x12,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4e, 0x6f,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4e,
	0x6f, 0x74, 0x65, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74,
	0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x4e, 0x6f,
	0x74, 0x65, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x4e, 0x6f,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x8d, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65,
	0x73, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x4c,
	0x61, 0x74, 0x65, 0x73, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x8d, 0x01, 0x0a, 0x10, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x7b, 0x0a, 0x0a, 0x50, 0x69, 0x6e, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x12,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x69, 0x6e, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x69, 0x6e, 0x50,
	0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x9d,
	0x01, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x6d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescOnce sync.Once
	file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescData = file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDesc
)

func file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescGZIP() []byte {
	file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescOnce.Do(func() {
		file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescData)
	})
	return file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDescData
}

var file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_moego_service_business_customer_v1_business_pet_note_service_proto_goTypes = []interface{}{
	(*ListPetNoteRequest)(nil),                // 0: moego.service.business_customer.v1.ListPetNoteRequest
	(*ListPetNoteResponse)(nil),               // 1: moego.service.business_customer.v1.ListPetNoteResponse
	(*BatchListPetNoteRequest)(nil),           // 2: moego.service.business_customer.v1.BatchListPetNoteRequest
	(*BatchListPetNoteResponse)(nil),          // 3: moego.service.business_customer.v1.BatchListPetNoteResponse
	(*CreatePetNoteRequest)(nil),              // 4: moego.service.business_customer.v1.CreatePetNoteRequest
	(*CreatePetNoteResponse)(nil),             // 5: moego.service.business_customer.v1.CreatePetNoteResponse
	(*UpdatePetNoteRequest)(nil),              // 6: moego.service.business_customer.v1.UpdatePetNoteRequest
	(*UpdatePetNoteResponse)(nil),             // 7: moego.service.business_customer.v1.UpdatePetNoteResponse
	(*DeletePetNoteRequest)(nil),              // 8: moego.service.business_customer.v1.DeletePetNoteRequest
	(*DeletePetNoteResponse)(nil),             // 9: moego.service.business_customer.v1.DeletePetNoteResponse
	(*GetPetNoteRequest)(nil),                 // 10: moego.service.business_customer.v1.GetPetNoteRequest
	(*GetPetNoteResponse)(nil),                // 11: moego.service.business_customer.v1.GetPetNoteResponse
	(*GetPetLatestNoteRequest)(nil),           // 12: moego.service.business_customer.v1.GetPetLatestNoteRequest
	(*GetPetLatestNoteResponse)(nil),          // 13: moego.service.business_customer.v1.GetPetLatestNoteResponse
	(*PinPetNoteRequest)(nil),                 // 14: moego.service.business_customer.v1.PinPetNoteRequest
	(*PinPetNoteResponse)(nil),                // 15: moego.service.business_customer.v1.PinPetNoteResponse
	(*BatchListPetNoteResponse_PetNotes)(nil), // 16: moego.service.business_customer.v1.BatchListPetNoteResponse.PetNotes
	nil,                                 // 17: moego.service.business_customer.v1.BatchListPetNoteResponse.PetNotesMapEntry
	(*v2.PaginationRequest)(nil),        // 18: moego.utils.v2.PaginationRequest
	(*v1.BusinessPetNoteModel)(nil),     // 19: moego.models.business_customer.v1.BusinessPetNoteModel
	(*v2.PaginationResponse)(nil),       // 20: moego.utils.v2.PaginationResponse
	(*v1.BusinessPetNoteCreateDef)(nil), // 21: moego.models.business_customer.v1.BusinessPetNoteCreateDef
	(*v1.BusinessPetNoteUpdateDef)(nil), // 22: moego.models.business_customer.v1.BusinessPetNoteUpdateDef
}
var file_moego_service_business_customer_v1_business_pet_note_service_proto_depIdxs = []int32{
	18, // 0: moego.service.business_customer.v1.ListPetNoteRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	19, // 1: moego.service.business_customer.v1.ListPetNoteResponse.notes:type_name -> moego.models.business_customer.v1.BusinessPetNoteModel
	20, // 2: moego.service.business_customer.v1.ListPetNoteResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	17, // 3: moego.service.business_customer.v1.BatchListPetNoteResponse.pet_notes_map:type_name -> moego.service.business_customer.v1.BatchListPetNoteResponse.PetNotesMapEntry
	21, // 4: moego.service.business_customer.v1.CreatePetNoteRequest.note:type_name -> moego.models.business_customer.v1.BusinessPetNoteCreateDef
	19, // 5: moego.service.business_customer.v1.CreatePetNoteResponse.note:type_name -> moego.models.business_customer.v1.BusinessPetNoteModel
	22, // 6: moego.service.business_customer.v1.UpdatePetNoteRequest.note:type_name -> moego.models.business_customer.v1.BusinessPetNoteUpdateDef
	19, // 7: moego.service.business_customer.v1.UpdatePetNoteResponse.note:type_name -> moego.models.business_customer.v1.BusinessPetNoteModel
	19, // 8: moego.service.business_customer.v1.GetPetNoteResponse.note:type_name -> moego.models.business_customer.v1.BusinessPetNoteModel
	19, // 9: moego.service.business_customer.v1.GetPetLatestNoteResponse.note:type_name -> moego.models.business_customer.v1.BusinessPetNoteModel
	19, // 10: moego.service.business_customer.v1.PinPetNoteResponse.note:type_name -> moego.models.business_customer.v1.BusinessPetNoteModel
	19, // 11: moego.service.business_customer.v1.BatchListPetNoteResponse.PetNotes.notes:type_name -> moego.models.business_customer.v1.BusinessPetNoteModel
	16, // 12: moego.service.business_customer.v1.BatchListPetNoteResponse.PetNotesMapEntry.value:type_name -> moego.service.business_customer.v1.BatchListPetNoteResponse.PetNotes
	4,  // 13: moego.service.business_customer.v1.BusinessPetNoteService.CreatePetNote:input_type -> moego.service.business_customer.v1.CreatePetNoteRequest
	6,  // 14: moego.service.business_customer.v1.BusinessPetNoteService.UpdatePetNote:input_type -> moego.service.business_customer.v1.UpdatePetNoteRequest
	8,  // 15: moego.service.business_customer.v1.BusinessPetNoteService.DeletePetNote:input_type -> moego.service.business_customer.v1.DeletePetNoteRequest
	0,  // 16: moego.service.business_customer.v1.BusinessPetNoteService.ListPetNote:input_type -> moego.service.business_customer.v1.ListPetNoteRequest
	10, // 17: moego.service.business_customer.v1.BusinessPetNoteService.GetPetNote:input_type -> moego.service.business_customer.v1.GetPetNoteRequest
	12, // 18: moego.service.business_customer.v1.BusinessPetNoteService.GetPetLatestNote:input_type -> moego.service.business_customer.v1.GetPetLatestNoteRequest
	2,  // 19: moego.service.business_customer.v1.BusinessPetNoteService.BatchListPetNote:input_type -> moego.service.business_customer.v1.BatchListPetNoteRequest
	14, // 20: moego.service.business_customer.v1.BusinessPetNoteService.PinPetNote:input_type -> moego.service.business_customer.v1.PinPetNoteRequest
	5,  // 21: moego.service.business_customer.v1.BusinessPetNoteService.CreatePetNote:output_type -> moego.service.business_customer.v1.CreatePetNoteResponse
	7,  // 22: moego.service.business_customer.v1.BusinessPetNoteService.UpdatePetNote:output_type -> moego.service.business_customer.v1.UpdatePetNoteResponse
	9,  // 23: moego.service.business_customer.v1.BusinessPetNoteService.DeletePetNote:output_type -> moego.service.business_customer.v1.DeletePetNoteResponse
	1,  // 24: moego.service.business_customer.v1.BusinessPetNoteService.ListPetNote:output_type -> moego.service.business_customer.v1.ListPetNoteResponse
	11, // 25: moego.service.business_customer.v1.BusinessPetNoteService.GetPetNote:output_type -> moego.service.business_customer.v1.GetPetNoteResponse
	13, // 26: moego.service.business_customer.v1.BusinessPetNoteService.GetPetLatestNote:output_type -> moego.service.business_customer.v1.GetPetLatestNoteResponse
	3,  // 27: moego.service.business_customer.v1.BusinessPetNoteService.BatchListPetNote:output_type -> moego.service.business_customer.v1.BatchListPetNoteResponse
	15, // 28: moego.service.business_customer.v1.BusinessPetNoteService.PinPetNote:output_type -> moego.service.business_customer.v1.PinPetNoteResponse
	21, // [21:29] is the sub-list for method output_type
	13, // [13:21] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_moego_service_business_customer_v1_business_pet_note_service_proto_init() }
func file_moego_service_business_customer_v1_business_pet_note_service_proto_init() {
	if File_moego_service_business_customer_v1_business_pet_note_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetNoteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetNoteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchListPetNoteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchListPetNoteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetNoteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetNoteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetNoteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetNoteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetNoteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetNoteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetNoteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetNoteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetLatestNoteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetLatestNoteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PinPetNoteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PinPetNoteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchListPetNoteResponse_PetNotes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes[14].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_business_customer_v1_business_pet_note_service_proto_goTypes,
		DependencyIndexes: file_moego_service_business_customer_v1_business_pet_note_service_proto_depIdxs,
		MessageInfos:      file_moego_service_business_customer_v1_business_pet_note_service_proto_msgTypes,
	}.Build()
	File_moego_service_business_customer_v1_business_pet_note_service_proto = out.File
	file_moego_service_business_customer_v1_business_pet_note_service_proto_rawDesc = nil
	file_moego_service_business_customer_v1_business_pet_note_service_proto_goTypes = nil
	file_moego_service_business_customer_v1_business_pet_note_service_proto_depIdxs = nil
}
