// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/business_customer/v1/business_pet_behavior_service.proto

package businesscustomersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessPetBehaviorServiceClient is the client API for BusinessPetBehaviorService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessPetBehaviorServiceClient interface {
	// Get a pet behavior.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The pet behavior does not exist, or does not belong to the given company or business.
	GetPetBehavior(ctx context.Context, in *GetPetBehaviorRequest, opts ...grpc.CallOption) (*GetPetBehaviorResponse, error)
	// List pet behaviors.
	// If the company does not exists, or does not define any pet behaviors, an empty list will be returned rather than an error.
	ListPetBehavior(ctx context.Context, in *ListPetBehaviorRequest, opts ...grpc.CallOption) (*ListPetBehaviorResponse, error)
	// Create a pet behavior.
	// The name of the new pet behavior must be unique among all pet behaviors of the company or business.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The name is already used by another pet behavior of the company or business.
	CreatePetBehavior(ctx context.Context, in *CreatePetBehaviorRequest, opts ...grpc.CallOption) (*CreatePetBehaviorResponse, error)
	// Update a pet behavior.
	// If the name of the pet behavior is changed, it must be unique among all pet behavior of the company or business.
	//
	// Error codes:
	//   - CODE_PARAMS_ERROR: The name is already used by another pet behavior of the company or business, or the pet
	//     behavior does not exist, or has been deleted, or does not belong to the company or business.
	UpdatePetBehavior(ctx context.Context, in *UpdatePetBehaviorRequest, opts ...grpc.CallOption) (*UpdatePetBehaviorResponse, error)
	// Sort pet behaviors of the company or business.
	// Pet behaviors will be sorted according to the order of `ids`. If there are pet behaviors of the company or business
	// whose ids are not included in `ids`, they will be sorted to the end. If an id in `ids` does not exist or does not
	// belong to the company or business, it will be ignored.
	SortPetBehavior(ctx context.Context, in *SortPetBehaviorRequest, opts ...grpc.CallOption) (*SortPetBehaviorResponse, error)
	// Delete a pet behavior.
	// If the pet behavior is already deleted, it will return success without throwing any error.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The pet behavior does not exist, or does not belong to the given company.
	DeletePetBehavior(ctx context.Context, in *DeletePetBehaviorRequest, opts ...grpc.CallOption) (*DeletePetBehaviorResponse, error)
}

type businessPetBehaviorServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessPetBehaviorServiceClient(cc grpc.ClientConnInterface) BusinessPetBehaviorServiceClient {
	return &businessPetBehaviorServiceClient{cc}
}

func (c *businessPetBehaviorServiceClient) GetPetBehavior(ctx context.Context, in *GetPetBehaviorRequest, opts ...grpc.CallOption) (*GetPetBehaviorResponse, error) {
	out := new(GetPetBehaviorResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetBehaviorService/GetPetBehavior", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetBehaviorServiceClient) ListPetBehavior(ctx context.Context, in *ListPetBehaviorRequest, opts ...grpc.CallOption) (*ListPetBehaviorResponse, error) {
	out := new(ListPetBehaviorResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetBehaviorService/ListPetBehavior", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetBehaviorServiceClient) CreatePetBehavior(ctx context.Context, in *CreatePetBehaviorRequest, opts ...grpc.CallOption) (*CreatePetBehaviorResponse, error) {
	out := new(CreatePetBehaviorResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetBehaviorService/CreatePetBehavior", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetBehaviorServiceClient) UpdatePetBehavior(ctx context.Context, in *UpdatePetBehaviorRequest, opts ...grpc.CallOption) (*UpdatePetBehaviorResponse, error) {
	out := new(UpdatePetBehaviorResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetBehaviorService/UpdatePetBehavior", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetBehaviorServiceClient) SortPetBehavior(ctx context.Context, in *SortPetBehaviorRequest, opts ...grpc.CallOption) (*SortPetBehaviorResponse, error) {
	out := new(SortPetBehaviorResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetBehaviorService/SortPetBehavior", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetBehaviorServiceClient) DeletePetBehavior(ctx context.Context, in *DeletePetBehaviorRequest, opts ...grpc.CallOption) (*DeletePetBehaviorResponse, error) {
	out := new(DeletePetBehaviorResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetBehaviorService/DeletePetBehavior", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessPetBehaviorServiceServer is the server API for BusinessPetBehaviorService service.
// All implementations must embed UnimplementedBusinessPetBehaviorServiceServer
// for forward compatibility
type BusinessPetBehaviorServiceServer interface {
	// Get a pet behavior.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The pet behavior does not exist, or does not belong to the given company or business.
	GetPetBehavior(context.Context, *GetPetBehaviorRequest) (*GetPetBehaviorResponse, error)
	// List pet behaviors.
	// If the company does not exists, or does not define any pet behaviors, an empty list will be returned rather than an error.
	ListPetBehavior(context.Context, *ListPetBehaviorRequest) (*ListPetBehaviorResponse, error)
	// Create a pet behavior.
	// The name of the new pet behavior must be unique among all pet behaviors of the company or business.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The name is already used by another pet behavior of the company or business.
	CreatePetBehavior(context.Context, *CreatePetBehaviorRequest) (*CreatePetBehaviorResponse, error)
	// Update a pet behavior.
	// If the name of the pet behavior is changed, it must be unique among all pet behavior of the company or business.
	//
	// Error codes:
	//   - CODE_PARAMS_ERROR: The name is already used by another pet behavior of the company or business, or the pet
	//     behavior does not exist, or has been deleted, or does not belong to the company or business.
	UpdatePetBehavior(context.Context, *UpdatePetBehaviorRequest) (*UpdatePetBehaviorResponse, error)
	// Sort pet behaviors of the company or business.
	// Pet behaviors will be sorted according to the order of `ids`. If there are pet behaviors of the company or business
	// whose ids are not included in `ids`, they will be sorted to the end. If an id in `ids` does not exist or does not
	// belong to the company or business, it will be ignored.
	SortPetBehavior(context.Context, *SortPetBehaviorRequest) (*SortPetBehaviorResponse, error)
	// Delete a pet behavior.
	// If the pet behavior is already deleted, it will return success without throwing any error.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The pet behavior does not exist, or does not belong to the given company.
	DeletePetBehavior(context.Context, *DeletePetBehaviorRequest) (*DeletePetBehaviorResponse, error)
	mustEmbedUnimplementedBusinessPetBehaviorServiceServer()
}

// UnimplementedBusinessPetBehaviorServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessPetBehaviorServiceServer struct {
}

func (UnimplementedBusinessPetBehaviorServiceServer) GetPetBehavior(context.Context, *GetPetBehaviorRequest) (*GetPetBehaviorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetBehavior not implemented")
}
func (UnimplementedBusinessPetBehaviorServiceServer) ListPetBehavior(context.Context, *ListPetBehaviorRequest) (*ListPetBehaviorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetBehavior not implemented")
}
func (UnimplementedBusinessPetBehaviorServiceServer) CreatePetBehavior(context.Context, *CreatePetBehaviorRequest) (*CreatePetBehaviorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetBehavior not implemented")
}
func (UnimplementedBusinessPetBehaviorServiceServer) UpdatePetBehavior(context.Context, *UpdatePetBehaviorRequest) (*UpdatePetBehaviorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetBehavior not implemented")
}
func (UnimplementedBusinessPetBehaviorServiceServer) SortPetBehavior(context.Context, *SortPetBehaviorRequest) (*SortPetBehaviorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortPetBehavior not implemented")
}
func (UnimplementedBusinessPetBehaviorServiceServer) DeletePetBehavior(context.Context, *DeletePetBehaviorRequest) (*DeletePetBehaviorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePetBehavior not implemented")
}
func (UnimplementedBusinessPetBehaviorServiceServer) mustEmbedUnimplementedBusinessPetBehaviorServiceServer() {
}

// UnsafeBusinessPetBehaviorServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessPetBehaviorServiceServer will
// result in compilation errors.
type UnsafeBusinessPetBehaviorServiceServer interface {
	mustEmbedUnimplementedBusinessPetBehaviorServiceServer()
}

func RegisterBusinessPetBehaviorServiceServer(s grpc.ServiceRegistrar, srv BusinessPetBehaviorServiceServer) {
	s.RegisterService(&BusinessPetBehaviorService_ServiceDesc, srv)
}

func _BusinessPetBehaviorService_GetPetBehavior_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetBehaviorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBehaviorServiceServer).GetPetBehavior(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetBehaviorService/GetPetBehavior",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBehaviorServiceServer).GetPetBehavior(ctx, req.(*GetPetBehaviorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetBehaviorService_ListPetBehavior_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetBehaviorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBehaviorServiceServer).ListPetBehavior(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetBehaviorService/ListPetBehavior",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBehaviorServiceServer).ListPetBehavior(ctx, req.(*ListPetBehaviorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetBehaviorService_CreatePetBehavior_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetBehaviorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBehaviorServiceServer).CreatePetBehavior(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetBehaviorService/CreatePetBehavior",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBehaviorServiceServer).CreatePetBehavior(ctx, req.(*CreatePetBehaviorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetBehaviorService_UpdatePetBehavior_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetBehaviorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBehaviorServiceServer).UpdatePetBehavior(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetBehaviorService/UpdatePetBehavior",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBehaviorServiceServer).UpdatePetBehavior(ctx, req.(*UpdatePetBehaviorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetBehaviorService_SortPetBehavior_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortPetBehaviorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBehaviorServiceServer).SortPetBehavior(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetBehaviorService/SortPetBehavior",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBehaviorServiceServer).SortPetBehavior(ctx, req.(*SortPetBehaviorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetBehaviorService_DeletePetBehavior_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetBehaviorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBehaviorServiceServer).DeletePetBehavior(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetBehaviorService/DeletePetBehavior",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBehaviorServiceServer).DeletePetBehavior(ctx, req.(*DeletePetBehaviorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessPetBehaviorService_ServiceDesc is the grpc.ServiceDesc for BusinessPetBehaviorService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessPetBehaviorService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.business_customer.v1.BusinessPetBehaviorService",
	HandlerType: (*BusinessPetBehaviorServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPetBehavior",
			Handler:    _BusinessPetBehaviorService_GetPetBehavior_Handler,
		},
		{
			MethodName: "ListPetBehavior",
			Handler:    _BusinessPetBehaviorService_ListPetBehavior_Handler,
		},
		{
			MethodName: "CreatePetBehavior",
			Handler:    _BusinessPetBehaviorService_CreatePetBehavior_Handler,
		},
		{
			MethodName: "UpdatePetBehavior",
			Handler:    _BusinessPetBehaviorService_UpdatePetBehavior_Handler,
		},
		{
			MethodName: "SortPetBehavior",
			Handler:    _BusinessPetBehaviorService_SortPetBehavior_Handler,
		},
		{
			MethodName: "DeletePetBehavior",
			Handler:    _BusinessPetBehaviorService_DeletePetBehavior_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/business_customer/v1/business_pet_behavior_service.proto",
}
