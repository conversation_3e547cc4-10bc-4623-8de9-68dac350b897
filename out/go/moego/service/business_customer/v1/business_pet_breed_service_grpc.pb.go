// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/business_customer/v1/business_pet_breed_service.proto

package businesscustomersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessPetBreedServiceClient is the client API for BusinessPetBreedService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessPetBreedServiceClient interface {
	// Get a pet breed.
	//
	// Error codes:
	// - CODE_NOT_FOUND: The pet breed does not exist, or does not belong to the company or business.
	GetPetBreed(ctx context.Context, in *GetPetBreedRequest, opts ...grpc.CallOption) (*GetPetBreedResponse, error)
	// List pet breeds.
	// Pet breeds of the given pet type defined by the company will be returned, or all pet breeds defined by the company
	// will be returned if `type` is not set.
	// If the company does not exists, or does not define any pet breeds, an empty list will be returned rather than an error.
	ListPetBreed(ctx context.Context, in *ListPetBreedRequest, opts ...grpc.CallOption) (*ListPetBreedResponse, error)
	// Batch upsert pet breeds of a pet type and set the pet type to available.
	// If a pet breed of the pet type is not in the request (create or update) but exists in the database, it will be deleted.
	// Each pet breed name in the request should be unique among the pet type.
	// Please notice that this method upsert pet breeds of the company and don't care about the business.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The breed name is duplicated, or the pet type does not belong to the company.
	BatchUpsertPetBreed(ctx context.Context, in *BatchUpsertPetBreedRequest, opts ...grpc.CallOption) (*BatchUpsertPetBreedResponse, error)
	// Create a pet breed.
	// The name of the new pet breed must be unique among all pet breeds of the company or business.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The name is already used by another pet breed of the company or business.
	CreatePetBreed(ctx context.Context, in *CreatePetBreedRequest, opts ...grpc.CallOption) (*CreatePetBreedResponse, error)
	// Update a pet breed.
	// If the name of the pet breed is changed, it must be unique among all pet breed of the company or business.
	//
	// Error codes:
	//   - CODE_NOT_FOUND: The name is already used by another pet breed of the company or business, or the pet breed does
	//     not exist, or has been deleted, or does not belong to the company or business.
	UpdatePetBreed(ctx context.Context, in *UpdatePetBreedRequest, opts ...grpc.CallOption) (*UpdatePetBreedResponse, error)
	// Delete a pet breed.
	// If the pet breed is already deleted, it will return success without throwing any error.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The pet breed does not exist, or does not belong to the given company.
	DeletePetBreed(ctx context.Context, in *DeletePetBreedRequest, opts ...grpc.CallOption) (*DeletePetBreedResponse, error)
}

type businessPetBreedServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessPetBreedServiceClient(cc grpc.ClientConnInterface) BusinessPetBreedServiceClient {
	return &businessPetBreedServiceClient{cc}
}

func (c *businessPetBreedServiceClient) GetPetBreed(ctx context.Context, in *GetPetBreedRequest, opts ...grpc.CallOption) (*GetPetBreedResponse, error) {
	out := new(GetPetBreedResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetBreedService/GetPetBreed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetBreedServiceClient) ListPetBreed(ctx context.Context, in *ListPetBreedRequest, opts ...grpc.CallOption) (*ListPetBreedResponse, error) {
	out := new(ListPetBreedResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetBreedService/ListPetBreed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetBreedServiceClient) BatchUpsertPetBreed(ctx context.Context, in *BatchUpsertPetBreedRequest, opts ...grpc.CallOption) (*BatchUpsertPetBreedResponse, error) {
	out := new(BatchUpsertPetBreedResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetBreedService/BatchUpsertPetBreed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetBreedServiceClient) CreatePetBreed(ctx context.Context, in *CreatePetBreedRequest, opts ...grpc.CallOption) (*CreatePetBreedResponse, error) {
	out := new(CreatePetBreedResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetBreedService/CreatePetBreed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetBreedServiceClient) UpdatePetBreed(ctx context.Context, in *UpdatePetBreedRequest, opts ...grpc.CallOption) (*UpdatePetBreedResponse, error) {
	out := new(UpdatePetBreedResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetBreedService/UpdatePetBreed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetBreedServiceClient) DeletePetBreed(ctx context.Context, in *DeletePetBreedRequest, opts ...grpc.CallOption) (*DeletePetBreedResponse, error) {
	out := new(DeletePetBreedResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetBreedService/DeletePetBreed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessPetBreedServiceServer is the server API for BusinessPetBreedService service.
// All implementations must embed UnimplementedBusinessPetBreedServiceServer
// for forward compatibility
type BusinessPetBreedServiceServer interface {
	// Get a pet breed.
	//
	// Error codes:
	// - CODE_NOT_FOUND: The pet breed does not exist, or does not belong to the company or business.
	GetPetBreed(context.Context, *GetPetBreedRequest) (*GetPetBreedResponse, error)
	// List pet breeds.
	// Pet breeds of the given pet type defined by the company will be returned, or all pet breeds defined by the company
	// will be returned if `type` is not set.
	// If the company does not exists, or does not define any pet breeds, an empty list will be returned rather than an error.
	ListPetBreed(context.Context, *ListPetBreedRequest) (*ListPetBreedResponse, error)
	// Batch upsert pet breeds of a pet type and set the pet type to available.
	// If a pet breed of the pet type is not in the request (create or update) but exists in the database, it will be deleted.
	// Each pet breed name in the request should be unique among the pet type.
	// Please notice that this method upsert pet breeds of the company and don't care about the business.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The breed name is duplicated, or the pet type does not belong to the company.
	BatchUpsertPetBreed(context.Context, *BatchUpsertPetBreedRequest) (*BatchUpsertPetBreedResponse, error)
	// Create a pet breed.
	// The name of the new pet breed must be unique among all pet breeds of the company or business.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The name is already used by another pet breed of the company or business.
	CreatePetBreed(context.Context, *CreatePetBreedRequest) (*CreatePetBreedResponse, error)
	// Update a pet breed.
	// If the name of the pet breed is changed, it must be unique among all pet breed of the company or business.
	//
	// Error codes:
	//   - CODE_NOT_FOUND: The name is already used by another pet breed of the company or business, or the pet breed does
	//     not exist, or has been deleted, or does not belong to the company or business.
	UpdatePetBreed(context.Context, *UpdatePetBreedRequest) (*UpdatePetBreedResponse, error)
	// Delete a pet breed.
	// If the pet breed is already deleted, it will return success without throwing any error.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The pet breed does not exist, or does not belong to the given company.
	DeletePetBreed(context.Context, *DeletePetBreedRequest) (*DeletePetBreedResponse, error)
	mustEmbedUnimplementedBusinessPetBreedServiceServer()
}

// UnimplementedBusinessPetBreedServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessPetBreedServiceServer struct {
}

func (UnimplementedBusinessPetBreedServiceServer) GetPetBreed(context.Context, *GetPetBreedRequest) (*GetPetBreedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetBreed not implemented")
}
func (UnimplementedBusinessPetBreedServiceServer) ListPetBreed(context.Context, *ListPetBreedRequest) (*ListPetBreedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetBreed not implemented")
}
func (UnimplementedBusinessPetBreedServiceServer) BatchUpsertPetBreed(context.Context, *BatchUpsertPetBreedRequest) (*BatchUpsertPetBreedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpsertPetBreed not implemented")
}
func (UnimplementedBusinessPetBreedServiceServer) CreatePetBreed(context.Context, *CreatePetBreedRequest) (*CreatePetBreedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetBreed not implemented")
}
func (UnimplementedBusinessPetBreedServiceServer) UpdatePetBreed(context.Context, *UpdatePetBreedRequest) (*UpdatePetBreedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetBreed not implemented")
}
func (UnimplementedBusinessPetBreedServiceServer) DeletePetBreed(context.Context, *DeletePetBreedRequest) (*DeletePetBreedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePetBreed not implemented")
}
func (UnimplementedBusinessPetBreedServiceServer) mustEmbedUnimplementedBusinessPetBreedServiceServer() {
}

// UnsafeBusinessPetBreedServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessPetBreedServiceServer will
// result in compilation errors.
type UnsafeBusinessPetBreedServiceServer interface {
	mustEmbedUnimplementedBusinessPetBreedServiceServer()
}

func RegisterBusinessPetBreedServiceServer(s grpc.ServiceRegistrar, srv BusinessPetBreedServiceServer) {
	s.RegisterService(&BusinessPetBreedService_ServiceDesc, srv)
}

func _BusinessPetBreedService_GetPetBreed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetBreedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBreedServiceServer).GetPetBreed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetBreedService/GetPetBreed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBreedServiceServer).GetPetBreed(ctx, req.(*GetPetBreedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetBreedService_ListPetBreed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetBreedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBreedServiceServer).ListPetBreed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetBreedService/ListPetBreed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBreedServiceServer).ListPetBreed(ctx, req.(*ListPetBreedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetBreedService_BatchUpsertPetBreed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpsertPetBreedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBreedServiceServer).BatchUpsertPetBreed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetBreedService/BatchUpsertPetBreed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBreedServiceServer).BatchUpsertPetBreed(ctx, req.(*BatchUpsertPetBreedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetBreedService_CreatePetBreed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetBreedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBreedServiceServer).CreatePetBreed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetBreedService/CreatePetBreed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBreedServiceServer).CreatePetBreed(ctx, req.(*CreatePetBreedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetBreedService_UpdatePetBreed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetBreedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBreedServiceServer).UpdatePetBreed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetBreedService/UpdatePetBreed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBreedServiceServer).UpdatePetBreed(ctx, req.(*UpdatePetBreedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetBreedService_DeletePetBreed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetBreedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBreedServiceServer).DeletePetBreed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetBreedService/DeletePetBreed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBreedServiceServer).DeletePetBreed(ctx, req.(*DeletePetBreedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessPetBreedService_ServiceDesc is the grpc.ServiceDesc for BusinessPetBreedService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessPetBreedService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.business_customer.v1.BusinessPetBreedService",
	HandlerType: (*BusinessPetBreedServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPetBreed",
			Handler:    _BusinessPetBreedService_GetPetBreed_Handler,
		},
		{
			MethodName: "ListPetBreed",
			Handler:    _BusinessPetBreedService_ListPetBreed_Handler,
		},
		{
			MethodName: "BatchUpsertPetBreed",
			Handler:    _BusinessPetBreedService_BatchUpsertPetBreed_Handler,
		},
		{
			MethodName: "CreatePetBreed",
			Handler:    _BusinessPetBreedService_CreatePetBreed_Handler,
		},
		{
			MethodName: "UpdatePetBreed",
			Handler:    _BusinessPetBreedService_UpdatePetBreed_Handler,
		},
		{
			MethodName: "DeletePetBreed",
			Handler:    _BusinessPetBreedService_DeletePetBreed_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/business_customer/v1/business_pet_breed_service.proto",
}
