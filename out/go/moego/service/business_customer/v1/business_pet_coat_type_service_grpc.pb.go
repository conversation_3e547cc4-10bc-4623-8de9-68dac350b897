// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/business_customer/v1/business_pet_coat_type_service.proto

package businesscustomersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessPetCoatTypeServiceClient is the client API for BusinessPetCoatTypeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessPetCoatTypeServiceClient interface {
	// Get a pet coat type.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The pet coat type does not exist, or does not belong to the given company or business.
	GetPetCoatType(ctx context.Context, in *GetPetCoatTypeRequest, opts ...grpc.CallOption) (*GetPetCoatTypeResponse, error)
	// List pet coat types.
	// If the company does not exists, or does not define any pet coat types, an empty list will be returned rather than an error.
	ListPetCoatType(ctx context.Context, in *ListPetCoatTypeRequest, opts ...grpc.CallOption) (*ListPetCoatTypeResponse, error)
	// List pet coat type template.
	ListPetCoatTypeTemplate(ctx context.Context, in *ListPetCoatTypeTemplateRequest, opts ...grpc.CallOption) (*ListPetCoatTypeTemplateResponse, error)
	// Create a pet coat type.
	// The name of the new pet coat type must be unique among all pet coat types of the company or business.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The name is already used by another pet coat type of the company or business.
	CreatePetCoatType(ctx context.Context, in *CreatePetCoatTypeRequest, opts ...grpc.CallOption) (*CreatePetCoatTypeResponse, error)
	// Update a pet coat type.
	// If the name of the pet coat type is changed, it must be unique among all pet coat types of the company or business.
	//
	// Error codes:
	//   - CODE_PARAMS_ERROR: The name is already used by another pet coat type of the company or business, or the pet coat
	//     type does not exist, or has been deleted, or does not belong to the company or business.
	UpdatePetCoatType(ctx context.Context, in *UpdatePetCoatTypeRequest, opts ...grpc.CallOption) (*UpdatePetCoatTypeResponse, error)
	// Sort pet coat type of the company or business.
	// Pet coat types will be sorted according to the order of `ids`. If there are coat types of the company or business
	// whose ids are not included in `ids`, they will be sorted to the end. If an id in `ids` does not exist or does not
	// belong to the company or business, it will be ignored.
	SortPetCoatType(ctx context.Context, in *SortPetCoatTypeRequest, opts ...grpc.CallOption) (*SortPetCoatTypeResponse, error)
	// Delete a pet coat type.
	// If the pet coat type is already deleted, will return success without throwing any error.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The pet coat type does not exist, or does not belong to the given company.
	DeletePetCoatType(ctx context.Context, in *DeletePetCoatTypeRequest, opts ...grpc.CallOption) (*DeletePetCoatTypeResponse, error)
}

type businessPetCoatTypeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessPetCoatTypeServiceClient(cc grpc.ClientConnInterface) BusinessPetCoatTypeServiceClient {
	return &businessPetCoatTypeServiceClient{cc}
}

func (c *businessPetCoatTypeServiceClient) GetPetCoatType(ctx context.Context, in *GetPetCoatTypeRequest, opts ...grpc.CallOption) (*GetPetCoatTypeResponse, error) {
	out := new(GetPetCoatTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetCoatTypeService/GetPetCoatType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetCoatTypeServiceClient) ListPetCoatType(ctx context.Context, in *ListPetCoatTypeRequest, opts ...grpc.CallOption) (*ListPetCoatTypeResponse, error) {
	out := new(ListPetCoatTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetCoatTypeService/ListPetCoatType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetCoatTypeServiceClient) ListPetCoatTypeTemplate(ctx context.Context, in *ListPetCoatTypeTemplateRequest, opts ...grpc.CallOption) (*ListPetCoatTypeTemplateResponse, error) {
	out := new(ListPetCoatTypeTemplateResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetCoatTypeService/ListPetCoatTypeTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetCoatTypeServiceClient) CreatePetCoatType(ctx context.Context, in *CreatePetCoatTypeRequest, opts ...grpc.CallOption) (*CreatePetCoatTypeResponse, error) {
	out := new(CreatePetCoatTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetCoatTypeService/CreatePetCoatType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetCoatTypeServiceClient) UpdatePetCoatType(ctx context.Context, in *UpdatePetCoatTypeRequest, opts ...grpc.CallOption) (*UpdatePetCoatTypeResponse, error) {
	out := new(UpdatePetCoatTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetCoatTypeService/UpdatePetCoatType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetCoatTypeServiceClient) SortPetCoatType(ctx context.Context, in *SortPetCoatTypeRequest, opts ...grpc.CallOption) (*SortPetCoatTypeResponse, error) {
	out := new(SortPetCoatTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetCoatTypeService/SortPetCoatType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetCoatTypeServiceClient) DeletePetCoatType(ctx context.Context, in *DeletePetCoatTypeRequest, opts ...grpc.CallOption) (*DeletePetCoatTypeResponse, error) {
	out := new(DeletePetCoatTypeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetCoatTypeService/DeletePetCoatType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessPetCoatTypeServiceServer is the server API for BusinessPetCoatTypeService service.
// All implementations must embed UnimplementedBusinessPetCoatTypeServiceServer
// for forward compatibility
type BusinessPetCoatTypeServiceServer interface {
	// Get a pet coat type.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The pet coat type does not exist, or does not belong to the given company or business.
	GetPetCoatType(context.Context, *GetPetCoatTypeRequest) (*GetPetCoatTypeResponse, error)
	// List pet coat types.
	// If the company does not exists, or does not define any pet coat types, an empty list will be returned rather than an error.
	ListPetCoatType(context.Context, *ListPetCoatTypeRequest) (*ListPetCoatTypeResponse, error)
	// List pet coat type template.
	ListPetCoatTypeTemplate(context.Context, *ListPetCoatTypeTemplateRequest) (*ListPetCoatTypeTemplateResponse, error)
	// Create a pet coat type.
	// The name of the new pet coat type must be unique among all pet coat types of the company or business.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The name is already used by another pet coat type of the company or business.
	CreatePetCoatType(context.Context, *CreatePetCoatTypeRequest) (*CreatePetCoatTypeResponse, error)
	// Update a pet coat type.
	// If the name of the pet coat type is changed, it must be unique among all pet coat types of the company or business.
	//
	// Error codes:
	//   - CODE_PARAMS_ERROR: The name is already used by another pet coat type of the company or business, or the pet coat
	//     type does not exist, or has been deleted, or does not belong to the company or business.
	UpdatePetCoatType(context.Context, *UpdatePetCoatTypeRequest) (*UpdatePetCoatTypeResponse, error)
	// Sort pet coat type of the company or business.
	// Pet coat types will be sorted according to the order of `ids`. If there are coat types of the company or business
	// whose ids are not included in `ids`, they will be sorted to the end. If an id in `ids` does not exist or does not
	// belong to the company or business, it will be ignored.
	SortPetCoatType(context.Context, *SortPetCoatTypeRequest) (*SortPetCoatTypeResponse, error)
	// Delete a pet coat type.
	// If the pet coat type is already deleted, will return success without throwing any error.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The pet coat type does not exist, or does not belong to the given company.
	DeletePetCoatType(context.Context, *DeletePetCoatTypeRequest) (*DeletePetCoatTypeResponse, error)
	mustEmbedUnimplementedBusinessPetCoatTypeServiceServer()
}

// UnimplementedBusinessPetCoatTypeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessPetCoatTypeServiceServer struct {
}

func (UnimplementedBusinessPetCoatTypeServiceServer) GetPetCoatType(context.Context, *GetPetCoatTypeRequest) (*GetPetCoatTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetCoatType not implemented")
}
func (UnimplementedBusinessPetCoatTypeServiceServer) ListPetCoatType(context.Context, *ListPetCoatTypeRequest) (*ListPetCoatTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetCoatType not implemented")
}
func (UnimplementedBusinessPetCoatTypeServiceServer) ListPetCoatTypeTemplate(context.Context, *ListPetCoatTypeTemplateRequest) (*ListPetCoatTypeTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetCoatTypeTemplate not implemented")
}
func (UnimplementedBusinessPetCoatTypeServiceServer) CreatePetCoatType(context.Context, *CreatePetCoatTypeRequest) (*CreatePetCoatTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetCoatType not implemented")
}
func (UnimplementedBusinessPetCoatTypeServiceServer) UpdatePetCoatType(context.Context, *UpdatePetCoatTypeRequest) (*UpdatePetCoatTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetCoatType not implemented")
}
func (UnimplementedBusinessPetCoatTypeServiceServer) SortPetCoatType(context.Context, *SortPetCoatTypeRequest) (*SortPetCoatTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortPetCoatType not implemented")
}
func (UnimplementedBusinessPetCoatTypeServiceServer) DeletePetCoatType(context.Context, *DeletePetCoatTypeRequest) (*DeletePetCoatTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePetCoatType not implemented")
}
func (UnimplementedBusinessPetCoatTypeServiceServer) mustEmbedUnimplementedBusinessPetCoatTypeServiceServer() {
}

// UnsafeBusinessPetCoatTypeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessPetCoatTypeServiceServer will
// result in compilation errors.
type UnsafeBusinessPetCoatTypeServiceServer interface {
	mustEmbedUnimplementedBusinessPetCoatTypeServiceServer()
}

func RegisterBusinessPetCoatTypeServiceServer(s grpc.ServiceRegistrar, srv BusinessPetCoatTypeServiceServer) {
	s.RegisterService(&BusinessPetCoatTypeService_ServiceDesc, srv)
}

func _BusinessPetCoatTypeService_GetPetCoatType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetCoatTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetCoatTypeServiceServer).GetPetCoatType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetCoatTypeService/GetPetCoatType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetCoatTypeServiceServer).GetPetCoatType(ctx, req.(*GetPetCoatTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetCoatTypeService_ListPetCoatType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetCoatTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetCoatTypeServiceServer).ListPetCoatType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetCoatTypeService/ListPetCoatType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetCoatTypeServiceServer).ListPetCoatType(ctx, req.(*ListPetCoatTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetCoatTypeService_ListPetCoatTypeTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetCoatTypeTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetCoatTypeServiceServer).ListPetCoatTypeTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetCoatTypeService/ListPetCoatTypeTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetCoatTypeServiceServer).ListPetCoatTypeTemplate(ctx, req.(*ListPetCoatTypeTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetCoatTypeService_CreatePetCoatType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetCoatTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetCoatTypeServiceServer).CreatePetCoatType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetCoatTypeService/CreatePetCoatType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetCoatTypeServiceServer).CreatePetCoatType(ctx, req.(*CreatePetCoatTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetCoatTypeService_UpdatePetCoatType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetCoatTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetCoatTypeServiceServer).UpdatePetCoatType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetCoatTypeService/UpdatePetCoatType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetCoatTypeServiceServer).UpdatePetCoatType(ctx, req.(*UpdatePetCoatTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetCoatTypeService_SortPetCoatType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortPetCoatTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetCoatTypeServiceServer).SortPetCoatType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetCoatTypeService/SortPetCoatType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetCoatTypeServiceServer).SortPetCoatType(ctx, req.(*SortPetCoatTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetCoatTypeService_DeletePetCoatType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetCoatTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetCoatTypeServiceServer).DeletePetCoatType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetCoatTypeService/DeletePetCoatType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetCoatTypeServiceServer).DeletePetCoatType(ctx, req.(*DeletePetCoatTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessPetCoatTypeService_ServiceDesc is the grpc.ServiceDesc for BusinessPetCoatTypeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessPetCoatTypeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.business_customer.v1.BusinessPetCoatTypeService",
	HandlerType: (*BusinessPetCoatTypeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPetCoatType",
			Handler:    _BusinessPetCoatTypeService_GetPetCoatType_Handler,
		},
		{
			MethodName: "ListPetCoatType",
			Handler:    _BusinessPetCoatTypeService_ListPetCoatType_Handler,
		},
		{
			MethodName: "ListPetCoatTypeTemplate",
			Handler:    _BusinessPetCoatTypeService_ListPetCoatTypeTemplate_Handler,
		},
		{
			MethodName: "CreatePetCoatType",
			Handler:    _BusinessPetCoatTypeService_CreatePetCoatType_Handler,
		},
		{
			MethodName: "UpdatePetCoatType",
			Handler:    _BusinessPetCoatTypeService_UpdatePetCoatType_Handler,
		},
		{
			MethodName: "SortPetCoatType",
			Handler:    _BusinessPetCoatTypeService_SortPetCoatType_Handler,
		},
		{
			MethodName: "DeletePetCoatType",
			Handler:    _BusinessPetCoatTypeService_DeletePetCoatType_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/business_customer/v1/business_pet_coat_type_service.proto",
}
