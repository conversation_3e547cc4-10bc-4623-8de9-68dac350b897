// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/business_customer/v1/business_customer_initialization_service.proto

package businesscustomersvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// init setting definition
type InitSettingDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// copy from tenant, optional
	// if set, will copy client & pet settings from this tenant
	CopyFromTenant *v1.Tenant `protobuf:"bytes,1,opt,name=copy_from_tenant,json=copyFromTenant,proto3,oneof" json:"copy_from_tenant,omitempty"`
	// weight unit, optional
	// if `copy_from_tenant` is not set, this field should be set
	WeightUnit *v1.WeightUnit `protobuf:"varint,2,opt,name=weight_unit,json=weightUnit,proto3,enum=moego.models.organization.v1.WeightUnit,oneof" json:"weight_unit,omitempty"`
}

func (x *InitSettingDef) Reset() {
	*x = InitSettingDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_initialization_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitSettingDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitSettingDef) ProtoMessage() {}

func (x *InitSettingDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_initialization_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitSettingDef.ProtoReflect.Descriptor instead.
func (*InitSettingDef) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_initialization_service_proto_rawDescGZIP(), []int{0}
}

func (x *InitSettingDef) GetCopyFromTenant() *v1.Tenant {
	if x != nil {
		return x.CopyFromTenant
	}
	return nil
}

func (x *InitSettingDef) GetWeightUnit() v1.WeightUnit {
	if x != nil && x.WeightUnit != nil {
		return *x.WeightUnit
	}
	return v1.WeightUnit(0)
}

// init demo profile definition
type InitDemoProfileDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// preferred business id
	PreferredBusinessId int64 `protobuf:"varint,1,opt,name=preferred_business_id,json=preferredBusinessId,proto3" json:"preferred_business_id,omitempty"`
	// staff id, it's better to use the owner's staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *InitDemoProfileDef) Reset() {
	*x = InitDemoProfileDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_initialization_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitDemoProfileDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitDemoProfileDef) ProtoMessage() {}

func (x *InitDemoProfileDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_initialization_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitDemoProfileDef.ProtoReflect.Descriptor instead.
func (*InitDemoProfileDef) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_initialization_service_proto_rawDescGZIP(), []int{1}
}

func (x *InitDemoProfileDef) GetPreferredBusinessId() int64 {
	if x != nil {
		return x.PreferredBusinessId
	}
	return 0
}

func (x *InitDemoProfileDef) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// initialize business customer module request
type InitializeBusinessCustomerModuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id, deprecated
	// use `tenant` instead
	//
	// Deprecated: Do not use.
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id, deprecated
	// use `preferred_business_id` in `init_demo_profile` instead
	//
	// Deprecated: Do not use.
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// staff id, deprecated
	// use `staff_id` in `init_demo_profile` instead
	//
	// Deprecated: Do not use.
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// weight unit, deprecated
	// use `weight_unit` in `init_setting` instead
	//
	// Deprecated: Do not use.
	WeightUnit v1.WeightUnit `protobuf:"varint,4,opt,name=weight_unit,json=weightUnit,proto3,enum=moego.models.organization.v1.WeightUnit" json:"weight_unit,omitempty"`
	// tenant, required
	Tenant *v1.Tenant `protobuf:"bytes,5,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// init setting, optional
	InitSetting *InitSettingDef `protobuf:"bytes,6,opt,name=init_setting,json=initSetting,proto3,oneof" json:"init_setting,omitempty"`
	// init demo profile, optional
	InitDemoProfile *InitDemoProfileDef `protobuf:"bytes,7,opt,name=init_demo_profile,json=initDemoProfile,proto3,oneof" json:"init_demo_profile,omitempty"`
}

func (x *InitializeBusinessCustomerModuleRequest) Reset() {
	*x = InitializeBusinessCustomerModuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_initialization_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitializeBusinessCustomerModuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitializeBusinessCustomerModuleRequest) ProtoMessage() {}

func (x *InitializeBusinessCustomerModuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_initialization_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitializeBusinessCustomerModuleRequest.ProtoReflect.Descriptor instead.
func (*InitializeBusinessCustomerModuleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_initialization_service_proto_rawDescGZIP(), []int{2}
}

// Deprecated: Do not use.
func (x *InitializeBusinessCustomerModuleRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// Deprecated: Do not use.
func (x *InitializeBusinessCustomerModuleRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// Deprecated: Do not use.
func (x *InitializeBusinessCustomerModuleRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// Deprecated: Do not use.
func (x *InitializeBusinessCustomerModuleRequest) GetWeightUnit() v1.WeightUnit {
	if x != nil {
		return x.WeightUnit
	}
	return v1.WeightUnit(0)
}

func (x *InitializeBusinessCustomerModuleRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *InitializeBusinessCustomerModuleRequest) GetInitSetting() *InitSettingDef {
	if x != nil {
		return x.InitSetting
	}
	return nil
}

func (x *InitializeBusinessCustomerModuleRequest) GetInitDemoProfile() *InitDemoProfileDef {
	if x != nil {
		return x.InitDemoProfile
	}
	return nil
}

// initialize business customer module response
type InitializeBusinessCustomerModuleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	// if `init_demo_profile` is set, customer id of demo profile will be returned,
	// otherwise, it will be 0
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// mini's pet id
	// if `init_demo_profile` is set, mini's pet id will be returned,
	// otherwise, it will be 0
	MiniPetId int64 `protobuf:"varint,2,opt,name=mini_pet_id,json=miniPetId,proto3" json:"mini_pet_id,omitempty"`
	// max's pet id
	// if `init_demo_profile` is set, max's pet id will be returned,
	// otherwise, it will be 0
	MaxPetId int64 `protobuf:"varint,3,opt,name=max_pet_id,json=maxPetId,proto3" json:"max_pet_id,omitempty"`
}

func (x *InitializeBusinessCustomerModuleResponse) Reset() {
	*x = InitializeBusinessCustomerModuleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_initialization_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitializeBusinessCustomerModuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitializeBusinessCustomerModuleResponse) ProtoMessage() {}

func (x *InitializeBusinessCustomerModuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_initialization_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitializeBusinessCustomerModuleResponse.ProtoReflect.Descriptor instead.
func (*InitializeBusinessCustomerModuleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_initialization_service_proto_rawDescGZIP(), []int{3}
}

func (x *InitializeBusinessCustomerModuleResponse) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *InitializeBusinessCustomerModuleResponse) GetMiniPetId() int64 {
	if x != nil {
		return x.MiniPetId
	}
	return 0
}

func (x *InitializeBusinessCustomerModuleResponse) GetMaxPetId() int64 {
	if x != nil {
		return x.MaxPetId
	}
	return 0
}

var File_moego_service_business_customer_v1_business_customer_initialization_service_proto protoreflect.FileDescriptor

var file_moego_service_business_customer_v1_business_customer_initialization_service_proto_rawDesc = []byte{
	0x0a, 0x51, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x22, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xda, 0x01,
	0x0a, 0x0e, 0x49, 0x6e, 0x69, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66,
	0x12, 0x53, 0x0a, 0x10, 0x63, 0x6f, 0x70, 0x79, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x48, 0x00, 0x52, 0x0e, 0x63, 0x6f, 0x70, 0x79, 0x46, 0x72, 0x6f, 0x6d, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x4e, 0x0a, 0x0b, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f,
	0x75, 0x6e, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x55, 0x6e, 0x69, 0x74, 0x48, 0x01, 0x52, 0x0a, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x55, 0x6e,
	0x69, 0x74, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x63, 0x6f, 0x70, 0x79, 0x5f, 0x66,
	0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x77,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x22, 0x75, 0x0a, 0x12, 0x49, 0x6e,
	0x69, 0x74, 0x44, 0x65, 0x6d, 0x6f, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x66,
	0x12, 0x3b, 0x0a, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x13, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x65, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49,
	0x64, 0x22, 0xb3, 0x04, 0x0a, 0x27, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x09, 0x18, 0x01, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0x18, 0x01,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0x18, 0x01, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x4d,
	0x0a, 0x0b, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x0a, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x3c, 0x0a,
	0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x5a, 0x0a, 0x0c, 0x69,
	0x6e, 0x69, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x44, 0x65, 0x66, 0x48, 0x01, 0x52, 0x0b, 0x69, 0x6e, 0x69, 0x74, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x67, 0x0a, 0x11, 0x69, 0x6e, 0x69, 0x74, 0x5f,
	0x64, 0x65, 0x6d, 0x6f, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x44, 0x65, 0x6d, 0x6f,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x48, 0x02, 0x52, 0x0f, 0x69, 0x6e,
	0x69, 0x74, 0x44, 0x65, 0x6d, 0x6f, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x88, 0x01, 0x01,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x64, 0x65, 0x6d, 0x6f, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x89, 0x01, 0x0a, 0x28, 0x49, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x6d, 0x69, 0x6e, 0x69, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x69,
	0x50, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x50, 0x65,
	0x74, 0x49, 0x64, 0x32, 0xe7, 0x01, 0x0a, 0x25, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xbd, 0x01,
	0x0a, 0x20, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x12, 0x4b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69,
	0x7a, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x9d, 0x01,
	0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x6d,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_business_customer_v1_business_customer_initialization_service_proto_rawDescOnce sync.Once
	file_moego_service_business_customer_v1_business_customer_initialization_service_proto_rawDescData = file_moego_service_business_customer_v1_business_customer_initialization_service_proto_rawDesc
)

func file_moego_service_business_customer_v1_business_customer_initialization_service_proto_rawDescGZIP() []byte {
	file_moego_service_business_customer_v1_business_customer_initialization_service_proto_rawDescOnce.Do(func() {
		file_moego_service_business_customer_v1_business_customer_initialization_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_business_customer_v1_business_customer_initialization_service_proto_rawDescData)
	})
	return file_moego_service_business_customer_v1_business_customer_initialization_service_proto_rawDescData
}

var file_moego_service_business_customer_v1_business_customer_initialization_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_service_business_customer_v1_business_customer_initialization_service_proto_goTypes = []interface{}{
	(*InitSettingDef)(nil),                           // 0: moego.service.business_customer.v1.InitSettingDef
	(*InitDemoProfileDef)(nil),                       // 1: moego.service.business_customer.v1.InitDemoProfileDef
	(*InitializeBusinessCustomerModuleRequest)(nil),  // 2: moego.service.business_customer.v1.InitializeBusinessCustomerModuleRequest
	(*InitializeBusinessCustomerModuleResponse)(nil), // 3: moego.service.business_customer.v1.InitializeBusinessCustomerModuleResponse
	(*v1.Tenant)(nil),                                // 4: moego.models.organization.v1.Tenant
	(v1.WeightUnit)(0),                               // 5: moego.models.organization.v1.WeightUnit
}
var file_moego_service_business_customer_v1_business_customer_initialization_service_proto_depIdxs = []int32{
	4, // 0: moego.service.business_customer.v1.InitSettingDef.copy_from_tenant:type_name -> moego.models.organization.v1.Tenant
	5, // 1: moego.service.business_customer.v1.InitSettingDef.weight_unit:type_name -> moego.models.organization.v1.WeightUnit
	5, // 2: moego.service.business_customer.v1.InitializeBusinessCustomerModuleRequest.weight_unit:type_name -> moego.models.organization.v1.WeightUnit
	4, // 3: moego.service.business_customer.v1.InitializeBusinessCustomerModuleRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	0, // 4: moego.service.business_customer.v1.InitializeBusinessCustomerModuleRequest.init_setting:type_name -> moego.service.business_customer.v1.InitSettingDef
	1, // 5: moego.service.business_customer.v1.InitializeBusinessCustomerModuleRequest.init_demo_profile:type_name -> moego.service.business_customer.v1.InitDemoProfileDef
	2, // 6: moego.service.business_customer.v1.BusinessCustomerInitializationService.InitializeBusinessCustomerModule:input_type -> moego.service.business_customer.v1.InitializeBusinessCustomerModuleRequest
	3, // 7: moego.service.business_customer.v1.BusinessCustomerInitializationService.InitializeBusinessCustomerModule:output_type -> moego.service.business_customer.v1.InitializeBusinessCustomerModuleResponse
	7, // [7:8] is the sub-list for method output_type
	6, // [6:7] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() {
	file_moego_service_business_customer_v1_business_customer_initialization_service_proto_init()
}
func file_moego_service_business_customer_v1_business_customer_initialization_service_proto_init() {
	if File_moego_service_business_customer_v1_business_customer_initialization_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_business_customer_v1_business_customer_initialization_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitSettingDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_initialization_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitDemoProfileDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_initialization_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitializeBusinessCustomerModuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_initialization_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitializeBusinessCustomerModuleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_business_customer_v1_business_customer_initialization_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_customer_initialization_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_business_customer_v1_business_customer_initialization_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_business_customer_v1_business_customer_initialization_service_proto_goTypes,
		DependencyIndexes: file_moego_service_business_customer_v1_business_customer_initialization_service_proto_depIdxs,
		MessageInfos:      file_moego_service_business_customer_v1_business_customer_initialization_service_proto_msgTypes,
	}.Build()
	File_moego_service_business_customer_v1_business_customer_initialization_service_proto = out.File
	file_moego_service_business_customer_v1_business_customer_initialization_service_proto_rawDesc = nil
	file_moego_service_business_customer_v1_business_customer_initialization_service_proto_goTypes = nil
	file_moego_service_business_customer_v1_business_customer_initialization_service_proto_depIdxs = nil
}
