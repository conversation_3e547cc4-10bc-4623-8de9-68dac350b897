// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/business_customer/v1/business_customer_service.proto

package businesscustomersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessCustomerServiceClient is the client API for BusinessCustomerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessCustomerServiceClient interface {
	// Deprecated: Do not use.
	// get business customer, deprecated
	// use `GetCustomerInfo` instead
	GetCustomer(ctx context.Context, in *GetCustomerRequest, opts ...grpc.CallOption) (*GetCustomerResponse, error)
	// get customer info
	GetCustomerInfo(ctx context.Context, in *GetCustomerInfoRequest, opts ...grpc.CallOption) (*GetCustomerInfoResponse, error)
	// Deprecated: Do not use.
	// batch get business customer, deprecated
	// use `BatchGetCustomerInfo` instead
	BatchGetCustomer(ctx context.Context, in *BatchGetCustomerRequest, opts ...grpc.CallOption) (*BatchGetCustomerResponse, error)
	// batch get business customer info
	BatchGetCustomerInfo(ctx context.Context, in *BatchGetCustomerInfoRequest, opts ...grpc.CallOption) (*BatchGetCustomerInfoResponse, error)
	// Update customer's preferred business.
	// 1. Update business id of customer (should not be deleted)
	// 2. Update business id of customer's address (exclude deleted)
	// 3. Update business id of customer's contact (exclude deleted)
	// 4. Update business id of customer's preferred tip config
	// 5. Update business id of customer's pets (include passed away, exclude deleted)
	//
	// Notice that business id of customer's notes and pets' notes doesn't need to be changed.
	UpdateCustomerPreferredBusiness(ctx context.Context, in *UpdateCustomerPreferredBusinessRequest, opts ...grpc.CallOption) (*UpdateCustomerPreferredBusinessResponse, error)
	// list customers
	ListCustomers(ctx context.Context, in *ListCustomersRequest, opts ...grpc.CallOption) (*ListCustomersResponse, error)
	// create a customer with additional info
	// additional info includes:
	// - communication preference
	// - appointment preference
	// - payment preference
	// - additional contacts
	// - addresses
	// - pets with additional info
	CreateCustomerWithAdditionalInfo(ctx context.Context, in *CreateCustomerWithAdditionalInfoRequest, opts ...grpc.CallOption) (*CreateCustomerWithAdditionalInfoResponse, error)
	// update customer
	UpdateCustomer(ctx context.Context, in *UpdateCustomerRequest, opts ...grpc.CallOption) (*UpdateCustomerResponse, error)
	// check if the identifier (phone number) is used in the tenant
	CheckIdentifier(ctx context.Context, in *CheckIdentifierRequest, opts ...grpc.CallOption) (*CheckIdentifierResponse, error)
}

type businessCustomerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessCustomerServiceClient(cc grpc.ClientConnInterface) BusinessCustomerServiceClient {
	return &businessCustomerServiceClient{cc}
}

// Deprecated: Do not use.
func (c *businessCustomerServiceClient) GetCustomer(ctx context.Context, in *GetCustomerRequest, opts ...grpc.CallOption) (*GetCustomerResponse, error) {
	out := new(GetCustomerResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerService/GetCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerServiceClient) GetCustomerInfo(ctx context.Context, in *GetCustomerInfoRequest, opts ...grpc.CallOption) (*GetCustomerInfoResponse, error) {
	out := new(GetCustomerInfoResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerService/GetCustomerInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *businessCustomerServiceClient) BatchGetCustomer(ctx context.Context, in *BatchGetCustomerRequest, opts ...grpc.CallOption) (*BatchGetCustomerResponse, error) {
	out := new(BatchGetCustomerResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerService/BatchGetCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerServiceClient) BatchGetCustomerInfo(ctx context.Context, in *BatchGetCustomerInfoRequest, opts ...grpc.CallOption) (*BatchGetCustomerInfoResponse, error) {
	out := new(BatchGetCustomerInfoResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerService/BatchGetCustomerInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerServiceClient) UpdateCustomerPreferredBusiness(ctx context.Context, in *UpdateCustomerPreferredBusinessRequest, opts ...grpc.CallOption) (*UpdateCustomerPreferredBusinessResponse, error) {
	out := new(UpdateCustomerPreferredBusinessResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerService/UpdateCustomerPreferredBusiness", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerServiceClient) ListCustomers(ctx context.Context, in *ListCustomersRequest, opts ...grpc.CallOption) (*ListCustomersResponse, error) {
	out := new(ListCustomersResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerService/ListCustomers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerServiceClient) CreateCustomerWithAdditionalInfo(ctx context.Context, in *CreateCustomerWithAdditionalInfoRequest, opts ...grpc.CallOption) (*CreateCustomerWithAdditionalInfoResponse, error) {
	out := new(CreateCustomerWithAdditionalInfoResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerService/CreateCustomerWithAdditionalInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerServiceClient) UpdateCustomer(ctx context.Context, in *UpdateCustomerRequest, opts ...grpc.CallOption) (*UpdateCustomerResponse, error) {
	out := new(UpdateCustomerResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerService/UpdateCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerServiceClient) CheckIdentifier(ctx context.Context, in *CheckIdentifierRequest, opts ...grpc.CallOption) (*CheckIdentifierResponse, error) {
	out := new(CheckIdentifierResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerService/CheckIdentifier", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessCustomerServiceServer is the server API for BusinessCustomerService service.
// All implementations must embed UnimplementedBusinessCustomerServiceServer
// for forward compatibility
type BusinessCustomerServiceServer interface {
	// Deprecated: Do not use.
	// get business customer, deprecated
	// use `GetCustomerInfo` instead
	GetCustomer(context.Context, *GetCustomerRequest) (*GetCustomerResponse, error)
	// get customer info
	GetCustomerInfo(context.Context, *GetCustomerInfoRequest) (*GetCustomerInfoResponse, error)
	// Deprecated: Do not use.
	// batch get business customer, deprecated
	// use `BatchGetCustomerInfo` instead
	BatchGetCustomer(context.Context, *BatchGetCustomerRequest) (*BatchGetCustomerResponse, error)
	// batch get business customer info
	BatchGetCustomerInfo(context.Context, *BatchGetCustomerInfoRequest) (*BatchGetCustomerInfoResponse, error)
	// Update customer's preferred business.
	// 1. Update business id of customer (should not be deleted)
	// 2. Update business id of customer's address (exclude deleted)
	// 3. Update business id of customer's contact (exclude deleted)
	// 4. Update business id of customer's preferred tip config
	// 5. Update business id of customer's pets (include passed away, exclude deleted)
	//
	// Notice that business id of customer's notes and pets' notes doesn't need to be changed.
	UpdateCustomerPreferredBusiness(context.Context, *UpdateCustomerPreferredBusinessRequest) (*UpdateCustomerPreferredBusinessResponse, error)
	// list customers
	ListCustomers(context.Context, *ListCustomersRequest) (*ListCustomersResponse, error)
	// create a customer with additional info
	// additional info includes:
	// - communication preference
	// - appointment preference
	// - payment preference
	// - additional contacts
	// - addresses
	// - pets with additional info
	CreateCustomerWithAdditionalInfo(context.Context, *CreateCustomerWithAdditionalInfoRequest) (*CreateCustomerWithAdditionalInfoResponse, error)
	// update customer
	UpdateCustomer(context.Context, *UpdateCustomerRequest) (*UpdateCustomerResponse, error)
	// check if the identifier (phone number) is used in the tenant
	CheckIdentifier(context.Context, *CheckIdentifierRequest) (*CheckIdentifierResponse, error)
	mustEmbedUnimplementedBusinessCustomerServiceServer()
}

// UnimplementedBusinessCustomerServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessCustomerServiceServer struct {
}

func (UnimplementedBusinessCustomerServiceServer) GetCustomer(context.Context, *GetCustomerRequest) (*GetCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomer not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) GetCustomerInfo(context.Context, *GetCustomerInfoRequest) (*GetCustomerInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerInfo not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) BatchGetCustomer(context.Context, *BatchGetCustomerRequest) (*BatchGetCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetCustomer not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) BatchGetCustomerInfo(context.Context, *BatchGetCustomerInfoRequest) (*BatchGetCustomerInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetCustomerInfo not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) UpdateCustomerPreferredBusiness(context.Context, *UpdateCustomerPreferredBusinessRequest) (*UpdateCustomerPreferredBusinessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCustomerPreferredBusiness not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) ListCustomers(context.Context, *ListCustomersRequest) (*ListCustomersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomers not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) CreateCustomerWithAdditionalInfo(context.Context, *CreateCustomerWithAdditionalInfoRequest) (*CreateCustomerWithAdditionalInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCustomerWithAdditionalInfo not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) UpdateCustomer(context.Context, *UpdateCustomerRequest) (*UpdateCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCustomer not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) CheckIdentifier(context.Context, *CheckIdentifierRequest) (*CheckIdentifierResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckIdentifier not implemented")
}
func (UnimplementedBusinessCustomerServiceServer) mustEmbedUnimplementedBusinessCustomerServiceServer() {
}

// UnsafeBusinessCustomerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessCustomerServiceServer will
// result in compilation errors.
type UnsafeBusinessCustomerServiceServer interface {
	mustEmbedUnimplementedBusinessCustomerServiceServer()
}

func RegisterBusinessCustomerServiceServer(s grpc.ServiceRegistrar, srv BusinessCustomerServiceServer) {
	s.RegisterService(&BusinessCustomerService_ServiceDesc, srv)
}

func _BusinessCustomerService_GetCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).GetCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerService/GetCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).GetCustomer(ctx, req.(*GetCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerService_GetCustomerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).GetCustomerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerService/GetCustomerInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).GetCustomerInfo(ctx, req.(*GetCustomerInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerService_BatchGetCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).BatchGetCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerService/BatchGetCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).BatchGetCustomer(ctx, req.(*BatchGetCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerService_BatchGetCustomerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetCustomerInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).BatchGetCustomerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerService/BatchGetCustomerInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).BatchGetCustomerInfo(ctx, req.(*BatchGetCustomerInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerService_UpdateCustomerPreferredBusiness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomerPreferredBusinessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).UpdateCustomerPreferredBusiness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerService/UpdateCustomerPreferredBusiness",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).UpdateCustomerPreferredBusiness(ctx, req.(*UpdateCustomerPreferredBusinessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerService_ListCustomers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).ListCustomers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerService/ListCustomers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).ListCustomers(ctx, req.(*ListCustomersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerService_CreateCustomerWithAdditionalInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomerWithAdditionalInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).CreateCustomerWithAdditionalInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerService/CreateCustomerWithAdditionalInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).CreateCustomerWithAdditionalInfo(ctx, req.(*CreateCustomerWithAdditionalInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerService_UpdateCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).UpdateCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerService/UpdateCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).UpdateCustomer(ctx, req.(*UpdateCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerService_CheckIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIdentifierRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerServiceServer).CheckIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerService/CheckIdentifier",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerServiceServer).CheckIdentifier(ctx, req.(*CheckIdentifierRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessCustomerService_ServiceDesc is the grpc.ServiceDesc for BusinessCustomerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessCustomerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.business_customer.v1.BusinessCustomerService",
	HandlerType: (*BusinessCustomerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCustomer",
			Handler:    _BusinessCustomerService_GetCustomer_Handler,
		},
		{
			MethodName: "GetCustomerInfo",
			Handler:    _BusinessCustomerService_GetCustomerInfo_Handler,
		},
		{
			MethodName: "BatchGetCustomer",
			Handler:    _BusinessCustomerService_BatchGetCustomer_Handler,
		},
		{
			MethodName: "BatchGetCustomerInfo",
			Handler:    _BusinessCustomerService_BatchGetCustomerInfo_Handler,
		},
		{
			MethodName: "UpdateCustomerPreferredBusiness",
			Handler:    _BusinessCustomerService_UpdateCustomerPreferredBusiness_Handler,
		},
		{
			MethodName: "ListCustomers",
			Handler:    _BusinessCustomerService_ListCustomers_Handler,
		},
		{
			MethodName: "CreateCustomerWithAdditionalInfo",
			Handler:    _BusinessCustomerService_CreateCustomerWithAdditionalInfo_Handler,
		},
		{
			MethodName: "UpdateCustomer",
			Handler:    _BusinessCustomerService_UpdateCustomer_Handler,
		},
		{
			MethodName: "CheckIdentifier",
			Handler:    _BusinessCustomerService_CheckIdentifier_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/business_customer/v1/business_customer_service.proto",
}
