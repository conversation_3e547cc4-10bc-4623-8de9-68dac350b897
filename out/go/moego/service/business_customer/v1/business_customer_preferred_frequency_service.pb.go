// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/business_customer/v1/business_customer_preferred_frequency_service.proto

package businesscustomersvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get customer grooming frequency request
type GetCustomerGroomingFrequencyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id, deprecated, use tenant instead
	//
	// Deprecated: Do not use.
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id, deprecated, use tenant instead
	//
	// Deprecated: Do not use.
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// tenant, required
	Tenant *v1.Tenant `protobuf:"bytes,3,opt,name=tenant,proto3" json:"tenant,omitempty"`
}

func (x *GetCustomerGroomingFrequencyRequest) Reset() {
	*x = GetCustomerGroomingFrequencyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerGroomingFrequencyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerGroomingFrequencyRequest) ProtoMessage() {}

func (x *GetCustomerGroomingFrequencyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerGroomingFrequencyRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerGroomingFrequencyRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_rawDescGZIP(), []int{0}
}

// Deprecated: Do not use.
func (x *GetCustomerGroomingFrequencyRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// Deprecated: Do not use.
func (x *GetCustomerGroomingFrequencyRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetCustomerGroomingFrequencyRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

// get customer grooming frequency response
type GetCustomerGroomingFrequencyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming frequency
	GroomingFrequency *v11.TimePeriod `protobuf:"bytes,1,opt,name=grooming_frequency,json=groomingFrequency,proto3" json:"grooming_frequency,omitempty"`
}

func (x *GetCustomerGroomingFrequencyResponse) Reset() {
	*x = GetCustomerGroomingFrequencyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerGroomingFrequencyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerGroomingFrequencyResponse) ProtoMessage() {}

func (x *GetCustomerGroomingFrequencyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerGroomingFrequencyResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerGroomingFrequencyResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetCustomerGroomingFrequencyResponse) GetGroomingFrequency() *v11.TimePeriod {
	if x != nil {
		return x.GroomingFrequency
	}
	return nil
}

// upsert customer grooming frequency request
type UpsertCustomerGroomingFrequencyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id, deprecated, use tenant instead
	//
	// Deprecated: Do not use.
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id, deprecated, use tenant instead
	//
	// Deprecated: Do not use.
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// tenant, required
	Tenant *v1.Tenant `protobuf:"bytes,5,opt,name=tenant,proto3" json:"tenant,omitempty"`
	// grooming frequency to upsert, currently only support unit of DAY, WEEK, MONTH
	GroomingFrequency *v11.TimePeriod `protobuf:"bytes,3,opt,name=grooming_frequency,json=groomingFrequency,proto3" json:"grooming_frequency,omitempty"`
	// apply to all customers, default is false
	ApplyToAllCustomers bool `protobuf:"varint,4,opt,name=apply_to_all_customers,json=applyToAllCustomers,proto3" json:"apply_to_all_customers,omitempty"`
}

func (x *UpsertCustomerGroomingFrequencyRequest) Reset() {
	*x = UpsertCustomerGroomingFrequencyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertCustomerGroomingFrequencyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertCustomerGroomingFrequencyRequest) ProtoMessage() {}

func (x *UpsertCustomerGroomingFrequencyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertCustomerGroomingFrequencyRequest.ProtoReflect.Descriptor instead.
func (*UpsertCustomerGroomingFrequencyRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_rawDescGZIP(), []int{2}
}

// Deprecated: Do not use.
func (x *UpsertCustomerGroomingFrequencyRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// Deprecated: Do not use.
func (x *UpsertCustomerGroomingFrequencyRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *UpsertCustomerGroomingFrequencyRequest) GetTenant() *v1.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

func (x *UpsertCustomerGroomingFrequencyRequest) GetGroomingFrequency() *v11.TimePeriod {
	if x != nil {
		return x.GroomingFrequency
	}
	return nil
}

func (x *UpsertCustomerGroomingFrequencyRequest) GetApplyToAllCustomers() bool {
	if x != nil {
		return x.ApplyToAllCustomers
	}
	return false
}

// upsert customer grooming frequency response
type UpsertCustomerGroomingFrequencyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpsertCustomerGroomingFrequencyResponse) Reset() {
	*x = UpsertCustomerGroomingFrequencyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertCustomerGroomingFrequencyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertCustomerGroomingFrequencyResponse) ProtoMessage() {}

func (x *UpsertCustomerGroomingFrequencyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertCustomerGroomingFrequencyResponse.ProtoReflect.Descriptor instead.
func (*UpsertCustomerGroomingFrequencyResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_rawDescGZIP(), []int{3}
}

var File_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto protoreflect.FileDescriptor

var file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_rawDesc = []byte{
	0x0a, 0x56, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64,
	0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x22, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x29, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xce, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09,
	0x18, 0x01, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0x18, 0x01, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x28, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x3c, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52, 0x06, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x22, 0x71, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x12, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x52, 0x11, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x22, 0xdb, 0x02, 0x0a, 0x26, 0x55, 0x70, 0x73, 0x65, 0x72,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0x18, 0x01, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x09, 0x18, 0x01, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x3c, 0x0a, 0x06,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x53, 0x0a, 0x12, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x11, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12,
	0x33, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x6f, 0x5f, 0x61, 0x6c, 0x6c, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x13, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x6f, 0x41, 0x6c, 0x6c, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x22, 0x29, 0x0a, 0x27, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32,
	0x9c, 0x03, 0x0a, 0x29, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x46, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xb1, 0x01,
	0x0a, 0x1c, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x47,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0xba, 0x01, 0x0a, 0x1f, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x4a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x4b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x9d,
	0x01, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x6d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_rawDescOnce sync.Once
	file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_rawDescData = file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_rawDesc
)

func file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_rawDescGZIP() []byte {
	file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_rawDescOnce.Do(func() {
		file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_rawDescData)
	})
	return file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_rawDescData
}

var file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_goTypes = []interface{}{
	(*GetCustomerGroomingFrequencyRequest)(nil),     // 0: moego.service.business_customer.v1.GetCustomerGroomingFrequencyRequest
	(*GetCustomerGroomingFrequencyResponse)(nil),    // 1: moego.service.business_customer.v1.GetCustomerGroomingFrequencyResponse
	(*UpsertCustomerGroomingFrequencyRequest)(nil),  // 2: moego.service.business_customer.v1.UpsertCustomerGroomingFrequencyRequest
	(*UpsertCustomerGroomingFrequencyResponse)(nil), // 3: moego.service.business_customer.v1.UpsertCustomerGroomingFrequencyResponse
	(*v1.Tenant)(nil),      // 4: moego.models.organization.v1.Tenant
	(*v11.TimePeriod)(nil), // 5: moego.utils.v1.TimePeriod
}
var file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_depIdxs = []int32{
	4, // 0: moego.service.business_customer.v1.GetCustomerGroomingFrequencyRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	5, // 1: moego.service.business_customer.v1.GetCustomerGroomingFrequencyResponse.grooming_frequency:type_name -> moego.utils.v1.TimePeriod
	4, // 2: moego.service.business_customer.v1.UpsertCustomerGroomingFrequencyRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	5, // 3: moego.service.business_customer.v1.UpsertCustomerGroomingFrequencyRequest.grooming_frequency:type_name -> moego.utils.v1.TimePeriod
	0, // 4: moego.service.business_customer.v1.BusinessCustomerPreferredFrequencyService.GetCustomerGroomingFrequency:input_type -> moego.service.business_customer.v1.GetCustomerGroomingFrequencyRequest
	2, // 5: moego.service.business_customer.v1.BusinessCustomerPreferredFrequencyService.UpsertCustomerGroomingFrequency:input_type -> moego.service.business_customer.v1.UpsertCustomerGroomingFrequencyRequest
	1, // 6: moego.service.business_customer.v1.BusinessCustomerPreferredFrequencyService.GetCustomerGroomingFrequency:output_type -> moego.service.business_customer.v1.GetCustomerGroomingFrequencyResponse
	3, // 7: moego.service.business_customer.v1.BusinessCustomerPreferredFrequencyService.UpsertCustomerGroomingFrequency:output_type -> moego.service.business_customer.v1.UpsertCustomerGroomingFrequencyResponse
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() {
	file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_init()
}
func file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_init() {
	if File_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerGroomingFrequencyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerGroomingFrequencyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertCustomerGroomingFrequencyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertCustomerGroomingFrequencyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_goTypes,
		DependencyIndexes: file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_depIdxs,
		MessageInfos:      file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_msgTypes,
	}.Build()
	File_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto = out.File
	file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_rawDesc = nil
	file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_goTypes = nil
	file_moego_service_business_customer_v1_business_customer_preferred_frequency_service_proto_depIdxs = nil
}
