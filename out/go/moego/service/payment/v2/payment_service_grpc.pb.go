// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/payment/v2/payment_service.proto

package paymentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PaymentServiceClient is the client API for PaymentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PaymentServiceClient interface {
	// GetPaymentVersion 获取支付版本, 用于前端判断该走哪个支付流程，控制灰度
	GetPaymentVersion(ctx context.Context, in *GetPaymentVersionRequest, opts ...grpc.CallOption) (*GetPaymentVersionResponse, error)
	// CreatePayment 创建支付单据，返回支付单据 ID
	CreatePayment(ctx context.Context, in *CreatePaymentRequest, opts ...grpc.CallOption) (*CreatePaymentResponse, error)
	// Cancel 取消支付
	CancelPayment(ctx context.Context, in *CancelPaymentRequest, opts ...grpc.CallOption) (*CancelPaymentResponse, error)
	// GetPayData 获取支付时所需的数据，用于前端加载第三方支付组件，注意此 API 只关注 Pay 支付动作
	GetPayData(ctx context.Context, in *GetPayDataRequest, opts ...grpc.CallOption) (*GetPayDataResponse, error)
	// Pay 确认支付 Confirm，在用户提交完所有支付凭证后调用
	PayPayment(ctx context.Context, in *PayPaymentRequest, opts ...grpc.CallOption) (*PayPaymentResponse, error)
	// SubmitActionDetail 提交支付 detail 凭证，一般是在完成 Pay 要求的 Action 后调用
	SubmitActionDetail(ctx context.Context, in *SubmitActionDetailRequest, opts ...grpc.CallOption) (*SubmitActionDetailResponse, error)
	// GetPayment 获取支付单据
	GetPayment(ctx context.Context, in *GetPaymentRequest, opts ...grpc.CallOption) (*GetPaymentResponse, error)
	// ListPayment 获取支付单据列表
	ListPayment(ctx context.Context, in *ListPaymentRequest, opts ...grpc.CallOption) (*ListPaymentResponse, error)
	// 获取已保存的支付方式
	ListRecurringPaymentMethods(ctx context.Context, in *ListRecurringPaymentMethodsRequest, opts ...grpc.CallOption) (*ListRecurringPaymentMethodsResponse, error)
}

type paymentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPaymentServiceClient(cc grpc.ClientConnInterface) PaymentServiceClient {
	return &paymentServiceClient{cc}
}

func (c *paymentServiceClient) GetPaymentVersion(ctx context.Context, in *GetPaymentVersionRequest, opts ...grpc.CallOption) (*GetPaymentVersionResponse, error) {
	out := new(GetPaymentVersionResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.PaymentService/GetPaymentVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentServiceClient) CreatePayment(ctx context.Context, in *CreatePaymentRequest, opts ...grpc.CallOption) (*CreatePaymentResponse, error) {
	out := new(CreatePaymentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.PaymentService/CreatePayment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentServiceClient) CancelPayment(ctx context.Context, in *CancelPaymentRequest, opts ...grpc.CallOption) (*CancelPaymentResponse, error) {
	out := new(CancelPaymentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.PaymentService/CancelPayment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentServiceClient) GetPayData(ctx context.Context, in *GetPayDataRequest, opts ...grpc.CallOption) (*GetPayDataResponse, error) {
	out := new(GetPayDataResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.PaymentService/GetPayData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentServiceClient) PayPayment(ctx context.Context, in *PayPaymentRequest, opts ...grpc.CallOption) (*PayPaymentResponse, error) {
	out := new(PayPaymentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.PaymentService/PayPayment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentServiceClient) SubmitActionDetail(ctx context.Context, in *SubmitActionDetailRequest, opts ...grpc.CallOption) (*SubmitActionDetailResponse, error) {
	out := new(SubmitActionDetailResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.PaymentService/SubmitActionDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentServiceClient) GetPayment(ctx context.Context, in *GetPaymentRequest, opts ...grpc.CallOption) (*GetPaymentResponse, error) {
	out := new(GetPaymentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.PaymentService/GetPayment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentServiceClient) ListPayment(ctx context.Context, in *ListPaymentRequest, opts ...grpc.CallOption) (*ListPaymentResponse, error) {
	out := new(ListPaymentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.PaymentService/ListPayment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentServiceClient) ListRecurringPaymentMethods(ctx context.Context, in *ListRecurringPaymentMethodsRequest, opts ...grpc.CallOption) (*ListRecurringPaymentMethodsResponse, error) {
	out := new(ListRecurringPaymentMethodsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.payment.v2.PaymentService/ListRecurringPaymentMethods", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PaymentServiceServer is the server API for PaymentService service.
// All implementations must embed UnimplementedPaymentServiceServer
// for forward compatibility
type PaymentServiceServer interface {
	// GetPaymentVersion 获取支付版本, 用于前端判断该走哪个支付流程，控制灰度
	GetPaymentVersion(context.Context, *GetPaymentVersionRequest) (*GetPaymentVersionResponse, error)
	// CreatePayment 创建支付单据，返回支付单据 ID
	CreatePayment(context.Context, *CreatePaymentRequest) (*CreatePaymentResponse, error)
	// Cancel 取消支付
	CancelPayment(context.Context, *CancelPaymentRequest) (*CancelPaymentResponse, error)
	// GetPayData 获取支付时所需的数据，用于前端加载第三方支付组件，注意此 API 只关注 Pay 支付动作
	GetPayData(context.Context, *GetPayDataRequest) (*GetPayDataResponse, error)
	// Pay 确认支付 Confirm，在用户提交完所有支付凭证后调用
	PayPayment(context.Context, *PayPaymentRequest) (*PayPaymentResponse, error)
	// SubmitActionDetail 提交支付 detail 凭证，一般是在完成 Pay 要求的 Action 后调用
	SubmitActionDetail(context.Context, *SubmitActionDetailRequest) (*SubmitActionDetailResponse, error)
	// GetPayment 获取支付单据
	GetPayment(context.Context, *GetPaymentRequest) (*GetPaymentResponse, error)
	// ListPayment 获取支付单据列表
	ListPayment(context.Context, *ListPaymentRequest) (*ListPaymentResponse, error)
	// 获取已保存的支付方式
	ListRecurringPaymentMethods(context.Context, *ListRecurringPaymentMethodsRequest) (*ListRecurringPaymentMethodsResponse, error)
	mustEmbedUnimplementedPaymentServiceServer()
}

// UnimplementedPaymentServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPaymentServiceServer struct {
}

func (UnimplementedPaymentServiceServer) GetPaymentVersion(context.Context, *GetPaymentVersionRequest) (*GetPaymentVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPaymentVersion not implemented")
}
func (UnimplementedPaymentServiceServer) CreatePayment(context.Context, *CreatePaymentRequest) (*CreatePaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePayment not implemented")
}
func (UnimplementedPaymentServiceServer) CancelPayment(context.Context, *CancelPaymentRequest) (*CancelPaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelPayment not implemented")
}
func (UnimplementedPaymentServiceServer) GetPayData(context.Context, *GetPayDataRequest) (*GetPayDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPayData not implemented")
}
func (UnimplementedPaymentServiceServer) PayPayment(context.Context, *PayPaymentRequest) (*PayPaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayPayment not implemented")
}
func (UnimplementedPaymentServiceServer) SubmitActionDetail(context.Context, *SubmitActionDetailRequest) (*SubmitActionDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitActionDetail not implemented")
}
func (UnimplementedPaymentServiceServer) GetPayment(context.Context, *GetPaymentRequest) (*GetPaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPayment not implemented")
}
func (UnimplementedPaymentServiceServer) ListPayment(context.Context, *ListPaymentRequest) (*ListPaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPayment not implemented")
}
func (UnimplementedPaymentServiceServer) ListRecurringPaymentMethods(context.Context, *ListRecurringPaymentMethodsRequest) (*ListRecurringPaymentMethodsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRecurringPaymentMethods not implemented")
}
func (UnimplementedPaymentServiceServer) mustEmbedUnimplementedPaymentServiceServer() {}

// UnsafePaymentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PaymentServiceServer will
// result in compilation errors.
type UnsafePaymentServiceServer interface {
	mustEmbedUnimplementedPaymentServiceServer()
}

func RegisterPaymentServiceServer(s grpc.ServiceRegistrar, srv PaymentServiceServer) {
	s.RegisterService(&PaymentService_ServiceDesc, srv)
}

func _PaymentService_GetPaymentVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaymentVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServiceServer).GetPaymentVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.PaymentService/GetPaymentVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServiceServer).GetPaymentVersion(ctx, req.(*GetPaymentVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentService_CreatePayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServiceServer).CreatePayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.PaymentService/CreatePayment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServiceServer).CreatePayment(ctx, req.(*CreatePaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentService_CancelPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelPaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServiceServer).CancelPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.PaymentService/CancelPayment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServiceServer).CancelPayment(ctx, req.(*CancelPaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentService_GetPayData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPayDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServiceServer).GetPayData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.PaymentService/GetPayData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServiceServer).GetPayData(ctx, req.(*GetPayDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentService_PayPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayPaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServiceServer).PayPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.PaymentService/PayPayment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServiceServer).PayPayment(ctx, req.(*PayPaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentService_SubmitActionDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitActionDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServiceServer).SubmitActionDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.PaymentService/SubmitActionDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServiceServer).SubmitActionDetail(ctx, req.(*SubmitActionDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentService_GetPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServiceServer).GetPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.PaymentService/GetPayment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServiceServer).GetPayment(ctx, req.(*GetPaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentService_ListPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServiceServer).ListPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.PaymentService/ListPayment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServiceServer).ListPayment(ctx, req.(*ListPaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentService_ListRecurringPaymentMethods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRecurringPaymentMethodsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServiceServer).ListRecurringPaymentMethods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.payment.v2.PaymentService/ListRecurringPaymentMethods",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServiceServer).ListRecurringPaymentMethods(ctx, req.(*ListRecurringPaymentMethodsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PaymentService_ServiceDesc is the grpc.ServiceDesc for PaymentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PaymentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.payment.v2.PaymentService",
	HandlerType: (*PaymentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPaymentVersion",
			Handler:    _PaymentService_GetPaymentVersion_Handler,
		},
		{
			MethodName: "CreatePayment",
			Handler:    _PaymentService_CreatePayment_Handler,
		},
		{
			MethodName: "CancelPayment",
			Handler:    _PaymentService_CancelPayment_Handler,
		},
		{
			MethodName: "GetPayData",
			Handler:    _PaymentService_GetPayData_Handler,
		},
		{
			MethodName: "PayPayment",
			Handler:    _PaymentService_PayPayment_Handler,
		},
		{
			MethodName: "SubmitActionDetail",
			Handler:    _PaymentService_SubmitActionDetail_Handler,
		},
		{
			MethodName: "GetPayment",
			Handler:    _PaymentService_GetPayment_Handler,
		},
		{
			MethodName: "ListPayment",
			Handler:    _PaymentService_ListPayment_Handler,
		},
		{
			MethodName: "ListRecurringPaymentMethods",
			Handler:    _PaymentService_ListRecurringPaymentMethods_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/payment/v2/payment_service.proto",
}
