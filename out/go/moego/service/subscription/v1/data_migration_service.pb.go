// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/subscription/v1/data_migration_service.proto

package subscriptionpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/subscription/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// import products request
type ImportProductsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// product data
	Data []*v1.ProductData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ImportProductsRequest) Reset() {
	*x = ImportProductsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportProductsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportProductsRequest) ProtoMessage() {}

func (x *ImportProductsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportProductsRequest.ProtoReflect.Descriptor instead.
func (*ImportProductsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_subscription_v1_data_migration_service_proto_rawDescGZIP(), []int{0}
}

func (x *ImportProductsRequest) GetData() []*v1.ProductData {
	if x != nil {
		return x.Data
	}
	return nil
}

// import products response
type ImportProductsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// successfully imported product data
	Imported []*v1.ProductData `protobuf:"bytes,1,rep,name=imported,proto3" json:"imported,omitempty"`
	// failed to import product data
	Failed []*v1.ProductData `protobuf:"bytes,2,rep,name=failed,proto3" json:"failed,omitempty"`
}

func (x *ImportProductsResponse) Reset() {
	*x = ImportProductsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportProductsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportProductsResponse) ProtoMessage() {}

func (x *ImportProductsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportProductsResponse.ProtoReflect.Descriptor instead.
func (*ImportProductsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_subscription_v1_data_migration_service_proto_rawDescGZIP(), []int{1}
}

func (x *ImportProductsResponse) GetImported() []*v1.ProductData {
	if x != nil {
		return x.Imported
	}
	return nil
}

func (x *ImportProductsResponse) GetFailed() []*v1.ProductData {
	if x != nil {
		return x.Failed
	}
	return nil
}

// import prices request
type ImportPricesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price data
	Data []*v1.PriceData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ImportPricesRequest) Reset() {
	*x = ImportPricesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportPricesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportPricesRequest) ProtoMessage() {}

func (x *ImportPricesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportPricesRequest.ProtoReflect.Descriptor instead.
func (*ImportPricesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_subscription_v1_data_migration_service_proto_rawDescGZIP(), []int{2}
}

func (x *ImportPricesRequest) GetData() []*v1.PriceData {
	if x != nil {
		return x.Data
	}
	return nil
}

// import prices response
type ImportPricesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// successfully imported price data
	Imported []*v1.PriceData `protobuf:"bytes,1,rep,name=imported,proto3" json:"imported,omitempty"`
	// failed to import price data
	Failed []*v1.PriceData `protobuf:"bytes,2,rep,name=failed,proto3" json:"failed,omitempty"`
}

func (x *ImportPricesResponse) Reset() {
	*x = ImportPricesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportPricesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportPricesResponse) ProtoMessage() {}

func (x *ImportPricesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportPricesResponse.ProtoReflect.Descriptor instead.
func (*ImportPricesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_subscription_v1_data_migration_service_proto_rawDescGZIP(), []int{3}
}

func (x *ImportPricesResponse) GetImported() []*v1.PriceData {
	if x != nil {
		return x.Imported
	}
	return nil
}

func (x *ImportPricesResponse) GetFailed() []*v1.PriceData {
	if x != nil {
		return x.Failed
	}
	return nil
}

// import features request
type ImportFeaturesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// feature data
	Data []*v1.FeatureData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ImportFeaturesRequest) Reset() {
	*x = ImportFeaturesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportFeaturesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportFeaturesRequest) ProtoMessage() {}

func (x *ImportFeaturesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportFeaturesRequest.ProtoReflect.Descriptor instead.
func (*ImportFeaturesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_subscription_v1_data_migration_service_proto_rawDescGZIP(), []int{4}
}

func (x *ImportFeaturesRequest) GetData() []*v1.FeatureData {
	if x != nil {
		return x.Data
	}
	return nil
}

// import features response
type ImportFeaturesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// successfully imported feature data
	Imported []*v1.FeatureData `protobuf:"bytes,1,rep,name=imported,proto3" json:"imported,omitempty"`
	// failed to import feature data
	Failed []*v1.FeatureData `protobuf:"bytes,2,rep,name=failed,proto3" json:"failed,omitempty"`
}

func (x *ImportFeaturesResponse) Reset() {
	*x = ImportFeaturesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportFeaturesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportFeaturesResponse) ProtoMessage() {}

func (x *ImportFeaturesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportFeaturesResponse.ProtoReflect.Descriptor instead.
func (*ImportFeaturesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_subscription_v1_data_migration_service_proto_rawDescGZIP(), []int{5}
}

func (x *ImportFeaturesResponse) GetImported() []*v1.FeatureData {
	if x != nil {
		return x.Imported
	}
	return nil
}

func (x *ImportFeaturesResponse) GetFailed() []*v1.FeatureData {
	if x != nil {
		return x.Failed
	}
	return nil
}

// import subscriptions request
type ImportSubscriptionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription data
	Data []*v1.SubscriptionData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ImportSubscriptionsRequest) Reset() {
	*x = ImportSubscriptionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportSubscriptionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportSubscriptionsRequest) ProtoMessage() {}

func (x *ImportSubscriptionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportSubscriptionsRequest.ProtoReflect.Descriptor instead.
func (*ImportSubscriptionsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_subscription_v1_data_migration_service_proto_rawDescGZIP(), []int{6}
}

func (x *ImportSubscriptionsRequest) GetData() []*v1.SubscriptionData {
	if x != nil {
		return x.Data
	}
	return nil
}

// import subscriptions response
type ImportSubscriptionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// successfully imported subscription data
	Imported []*v1.SubscriptionData `protobuf:"bytes,1,rep,name=imported,proto3" json:"imported,omitempty"`
	// failed to import subscription data
	Failed []*v1.SubscriptionData `protobuf:"bytes,2,rep,name=failed,proto3" json:"failed,omitempty"`
}

func (x *ImportSubscriptionsResponse) Reset() {
	*x = ImportSubscriptionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportSubscriptionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportSubscriptionsResponse) ProtoMessage() {}

func (x *ImportSubscriptionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportSubscriptionsResponse.ProtoReflect.Descriptor instead.
func (*ImportSubscriptionsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_subscription_v1_data_migration_service_proto_rawDescGZIP(), []int{7}
}

func (x *ImportSubscriptionsResponse) GetImported() []*v1.SubscriptionData {
	if x != nil {
		return x.Imported
	}
	return nil
}

func (x *ImportSubscriptionsResponse) GetFailed() []*v1.SubscriptionData {
	if x != nil {
		return x.Failed
	}
	return nil
}

// import entitlements request
type ImportEntitlementsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// entitlement data
	Data []*v1.EntitlementData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ImportEntitlementsRequest) Reset() {
	*x = ImportEntitlementsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportEntitlementsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportEntitlementsRequest) ProtoMessage() {}

func (x *ImportEntitlementsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportEntitlementsRequest.ProtoReflect.Descriptor instead.
func (*ImportEntitlementsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_subscription_v1_data_migration_service_proto_rawDescGZIP(), []int{8}
}

func (x *ImportEntitlementsRequest) GetData() []*v1.EntitlementData {
	if x != nil {
		return x.Data
	}
	return nil
}

// import entitlements response
type ImportEntitlementsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// successfully imported entitlement data
	Imported []*v1.EntitlementData `protobuf:"bytes,1,rep,name=imported,proto3" json:"imported,omitempty"`
	// failed to import entitlement data
	Failed []*v1.EntitlementData `protobuf:"bytes,2,rep,name=failed,proto3" json:"failed,omitempty"`
}

func (x *ImportEntitlementsResponse) Reset() {
	*x = ImportEntitlementsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportEntitlementsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportEntitlementsResponse) ProtoMessage() {}

func (x *ImportEntitlementsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportEntitlementsResponse.ProtoReflect.Descriptor instead.
func (*ImportEntitlementsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_subscription_v1_data_migration_service_proto_rawDescGZIP(), []int{9}
}

func (x *ImportEntitlementsResponse) GetImported() []*v1.EntitlementData {
	if x != nil {
		return x.Imported
	}
	return nil
}

func (x *ImportEntitlementsResponse) GetFailed() []*v1.EntitlementData {
	if x != nil {
		return x.Failed
	}
	return nil
}

var File_moego_service_subscription_v1_data_migration_service_proto protoreflect.FileDescriptor

var file_moego_service_subscription_v1_data_migration_service_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x38, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d,
	0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x56, 0x0a, 0x15, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3d,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa2, 0x01,
	0x0a, 0x16, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x12,
	0x41, 0x0a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x66, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x22, 0x52, 0x0a, 0x13, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x9c, 0x01, 0x0a, 0x14, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x43, 0x0a, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x64, 0x12, 0x3f, 0x0a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x66,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x22, 0x56, 0x0a, 0x15, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x46,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3d,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa2, 0x01,
	0x0a, 0x16, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x12,
	0x41, 0x0a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x66, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x22, 0x60, 0x0a, 0x1a, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x42, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0xb1, 0x01, 0x0a, 0x1b, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64,
	0x12, 0x46, 0x0a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x22, 0x5e, 0x0a, 0x19, 0x49, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x41, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xae, 0x01, 0x0a, 0x1a, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x65, 0x64, 0x12, 0x45, 0x0a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x32, 0xa8, 0x05, 0x0a, 0x14, 0x44, 0x61,
	0x74, 0x61, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x7d, 0x0a, 0x0e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x73, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x77, 0x0a, 0x0c, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x73, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7d, 0x0a, 0x0e, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c, 0x01, 0x0a, 0x13, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x12, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8c, 0x01, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x61, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_subscription_v1_data_migration_service_proto_rawDescOnce sync.Once
	file_moego_service_subscription_v1_data_migration_service_proto_rawDescData = file_moego_service_subscription_v1_data_migration_service_proto_rawDesc
)

func file_moego_service_subscription_v1_data_migration_service_proto_rawDescGZIP() []byte {
	file_moego_service_subscription_v1_data_migration_service_proto_rawDescOnce.Do(func() {
		file_moego_service_subscription_v1_data_migration_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_subscription_v1_data_migration_service_proto_rawDescData)
	})
	return file_moego_service_subscription_v1_data_migration_service_proto_rawDescData
}

var file_moego_service_subscription_v1_data_migration_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_service_subscription_v1_data_migration_service_proto_goTypes = []interface{}{
	(*ImportProductsRequest)(nil),       // 0: moego.service.subscription.v1.ImportProductsRequest
	(*ImportProductsResponse)(nil),      // 1: moego.service.subscription.v1.ImportProductsResponse
	(*ImportPricesRequest)(nil),         // 2: moego.service.subscription.v1.ImportPricesRequest
	(*ImportPricesResponse)(nil),        // 3: moego.service.subscription.v1.ImportPricesResponse
	(*ImportFeaturesRequest)(nil),       // 4: moego.service.subscription.v1.ImportFeaturesRequest
	(*ImportFeaturesResponse)(nil),      // 5: moego.service.subscription.v1.ImportFeaturesResponse
	(*ImportSubscriptionsRequest)(nil),  // 6: moego.service.subscription.v1.ImportSubscriptionsRequest
	(*ImportSubscriptionsResponse)(nil), // 7: moego.service.subscription.v1.ImportSubscriptionsResponse
	(*ImportEntitlementsRequest)(nil),   // 8: moego.service.subscription.v1.ImportEntitlementsRequest
	(*ImportEntitlementsResponse)(nil),  // 9: moego.service.subscription.v1.ImportEntitlementsResponse
	(*v1.ProductData)(nil),              // 10: moego.models.subscription.v1.ProductData
	(*v1.PriceData)(nil),                // 11: moego.models.subscription.v1.PriceData
	(*v1.FeatureData)(nil),              // 12: moego.models.subscription.v1.FeatureData
	(*v1.SubscriptionData)(nil),         // 13: moego.models.subscription.v1.SubscriptionData
	(*v1.EntitlementData)(nil),          // 14: moego.models.subscription.v1.EntitlementData
}
var file_moego_service_subscription_v1_data_migration_service_proto_depIdxs = []int32{
	10, // 0: moego.service.subscription.v1.ImportProductsRequest.data:type_name -> moego.models.subscription.v1.ProductData
	10, // 1: moego.service.subscription.v1.ImportProductsResponse.imported:type_name -> moego.models.subscription.v1.ProductData
	10, // 2: moego.service.subscription.v1.ImportProductsResponse.failed:type_name -> moego.models.subscription.v1.ProductData
	11, // 3: moego.service.subscription.v1.ImportPricesRequest.data:type_name -> moego.models.subscription.v1.PriceData
	11, // 4: moego.service.subscription.v1.ImportPricesResponse.imported:type_name -> moego.models.subscription.v1.PriceData
	11, // 5: moego.service.subscription.v1.ImportPricesResponse.failed:type_name -> moego.models.subscription.v1.PriceData
	12, // 6: moego.service.subscription.v1.ImportFeaturesRequest.data:type_name -> moego.models.subscription.v1.FeatureData
	12, // 7: moego.service.subscription.v1.ImportFeaturesResponse.imported:type_name -> moego.models.subscription.v1.FeatureData
	12, // 8: moego.service.subscription.v1.ImportFeaturesResponse.failed:type_name -> moego.models.subscription.v1.FeatureData
	13, // 9: moego.service.subscription.v1.ImportSubscriptionsRequest.data:type_name -> moego.models.subscription.v1.SubscriptionData
	13, // 10: moego.service.subscription.v1.ImportSubscriptionsResponse.imported:type_name -> moego.models.subscription.v1.SubscriptionData
	13, // 11: moego.service.subscription.v1.ImportSubscriptionsResponse.failed:type_name -> moego.models.subscription.v1.SubscriptionData
	14, // 12: moego.service.subscription.v1.ImportEntitlementsRequest.data:type_name -> moego.models.subscription.v1.EntitlementData
	14, // 13: moego.service.subscription.v1.ImportEntitlementsResponse.imported:type_name -> moego.models.subscription.v1.EntitlementData
	14, // 14: moego.service.subscription.v1.ImportEntitlementsResponse.failed:type_name -> moego.models.subscription.v1.EntitlementData
	0,  // 15: moego.service.subscription.v1.DataMigrationService.ImportProducts:input_type -> moego.service.subscription.v1.ImportProductsRequest
	2,  // 16: moego.service.subscription.v1.DataMigrationService.ImportPrices:input_type -> moego.service.subscription.v1.ImportPricesRequest
	4,  // 17: moego.service.subscription.v1.DataMigrationService.ImportFeatures:input_type -> moego.service.subscription.v1.ImportFeaturesRequest
	6,  // 18: moego.service.subscription.v1.DataMigrationService.ImportSubscriptions:input_type -> moego.service.subscription.v1.ImportSubscriptionsRequest
	8,  // 19: moego.service.subscription.v1.DataMigrationService.ImportEntitlements:input_type -> moego.service.subscription.v1.ImportEntitlementsRequest
	1,  // 20: moego.service.subscription.v1.DataMigrationService.ImportProducts:output_type -> moego.service.subscription.v1.ImportProductsResponse
	3,  // 21: moego.service.subscription.v1.DataMigrationService.ImportPrices:output_type -> moego.service.subscription.v1.ImportPricesResponse
	5,  // 22: moego.service.subscription.v1.DataMigrationService.ImportFeatures:output_type -> moego.service.subscription.v1.ImportFeaturesResponse
	7,  // 23: moego.service.subscription.v1.DataMigrationService.ImportSubscriptions:output_type -> moego.service.subscription.v1.ImportSubscriptionsResponse
	9,  // 24: moego.service.subscription.v1.DataMigrationService.ImportEntitlements:output_type -> moego.service.subscription.v1.ImportEntitlementsResponse
	20, // [20:25] is the sub-list for method output_type
	15, // [15:20] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_moego_service_subscription_v1_data_migration_service_proto_init() }
func file_moego_service_subscription_v1_data_migration_service_proto_init() {
	if File_moego_service_subscription_v1_data_migration_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportProductsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportProductsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportPricesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportPricesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportFeaturesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportFeaturesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportSubscriptionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportSubscriptionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportEntitlementsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_subscription_v1_data_migration_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportEntitlementsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_subscription_v1_data_migration_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_subscription_v1_data_migration_service_proto_goTypes,
		DependencyIndexes: file_moego_service_subscription_v1_data_migration_service_proto_depIdxs,
		MessageInfos:      file_moego_service_subscription_v1_data_migration_service_proto_msgTypes,
	}.Build()
	File_moego_service_subscription_v1_data_migration_service_proto = out.File
	file_moego_service_subscription_v1_data_migration_service_proto_rawDesc = nil
	file_moego_service_subscription_v1_data_migration_service_proto_goTypes = nil
	file_moego_service_subscription_v1_data_migration_service_proto_depIdxs = nil
}
