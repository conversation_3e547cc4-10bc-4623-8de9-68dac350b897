// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/open_platform/v1/obfuscate_service.proto

package openplatformsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ObfuscateServiceClient is the client API for ObfuscateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ObfuscateServiceClient interface {
	// batch encode id
	BatchEncodeID(ctx context.Context, in *BatchEncodeIDRequest, opts ...grpc.CallOption) (*BatchEncodeIDResponse, error)
	// batch decode id
	BatchDecodeID(ctx context.Context, in *BatchDecodeIDRequest, opts ...grpc.CallOption) (*BatchDecodeIDResponse, error)
}

type obfuscateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewObfuscateServiceClient(cc grpc.ClientConnInterface) ObfuscateServiceClient {
	return &obfuscateServiceClient{cc}
}

func (c *obfuscateServiceClient) BatchEncodeID(ctx context.Context, in *BatchEncodeIDRequest, opts ...grpc.CallOption) (*BatchEncodeIDResponse, error) {
	out := new(BatchEncodeIDResponse)
	err := c.cc.Invoke(ctx, "/moego.service.open_platform.v1.ObfuscateService/BatchEncodeID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *obfuscateServiceClient) BatchDecodeID(ctx context.Context, in *BatchDecodeIDRequest, opts ...grpc.CallOption) (*BatchDecodeIDResponse, error) {
	out := new(BatchDecodeIDResponse)
	err := c.cc.Invoke(ctx, "/moego.service.open_platform.v1.ObfuscateService/BatchDecodeID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ObfuscateServiceServer is the server API for ObfuscateService service.
// All implementations must embed UnimplementedObfuscateServiceServer
// for forward compatibility
type ObfuscateServiceServer interface {
	// batch encode id
	BatchEncodeID(context.Context, *BatchEncodeIDRequest) (*BatchEncodeIDResponse, error)
	// batch decode id
	BatchDecodeID(context.Context, *BatchDecodeIDRequest) (*BatchDecodeIDResponse, error)
	mustEmbedUnimplementedObfuscateServiceServer()
}

// UnimplementedObfuscateServiceServer must be embedded to have forward compatible implementations.
type UnimplementedObfuscateServiceServer struct {
}

func (UnimplementedObfuscateServiceServer) BatchEncodeID(context.Context, *BatchEncodeIDRequest) (*BatchEncodeIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchEncodeID not implemented")
}
func (UnimplementedObfuscateServiceServer) BatchDecodeID(context.Context, *BatchDecodeIDRequest) (*BatchDecodeIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchDecodeID not implemented")
}
func (UnimplementedObfuscateServiceServer) mustEmbedUnimplementedObfuscateServiceServer() {}

// UnsafeObfuscateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ObfuscateServiceServer will
// result in compilation errors.
type UnsafeObfuscateServiceServer interface {
	mustEmbedUnimplementedObfuscateServiceServer()
}

func RegisterObfuscateServiceServer(s grpc.ServiceRegistrar, srv ObfuscateServiceServer) {
	s.RegisterService(&ObfuscateService_ServiceDesc, srv)
}

func _ObfuscateService_BatchEncodeID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchEncodeIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObfuscateServiceServer).BatchEncodeID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.open_platform.v1.ObfuscateService/BatchEncodeID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObfuscateServiceServer).BatchEncodeID(ctx, req.(*BatchEncodeIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ObfuscateService_BatchDecodeID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDecodeIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ObfuscateServiceServer).BatchDecodeID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.open_platform.v1.ObfuscateService/BatchDecodeID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ObfuscateServiceServer).BatchDecodeID(ctx, req.(*BatchDecodeIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ObfuscateService_ServiceDesc is the grpc.ServiceDesc for ObfuscateService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ObfuscateService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.open_platform.v1.ObfuscateService",
	HandlerType: (*ObfuscateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchEncodeID",
			Handler:    _ObfuscateService_BatchEncodeID_Handler,
		},
		{
			MethodName: "BatchDecodeID",
			Handler:    _ObfuscateService_BatchDecodeID_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/open_platform/v1/obfuscate_service.proto",
}
