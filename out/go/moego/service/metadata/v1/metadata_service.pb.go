// @since 2023-04-07 09:48:36
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/metadata/v1/metadata_service.proto

package metadatasvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/metadata/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// describe key groups response
type DescribeGroupsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// all of the groups
	Groups []string `protobuf:"bytes,1,rep,name=groups,proto3" json:"groups,omitempty"`
}

func (x *DescribeGroupsResponse) Reset() {
	*x = DescribeGroupsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeGroupsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeGroupsResponse) ProtoMessage() {}

func (x *DescribeGroupsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeGroupsResponse.ProtoReflect.Descriptor instead.
func (*DescribeGroupsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{0}
}

func (x *DescribeGroupsResponse) GetGroups() []string {
	if x != nil {
		return x.Groups
	}
	return nil
}

// create key request
type CreateKeyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the key def
	KeyDef *v1.KeyFullDef `protobuf:"bytes,1,opt,name=key_def,json=keyDef,proto3" json:"key_def,omitempty"`
	// the operator id
	InternalOperatorId string `protobuf:"bytes,2,opt,name=internal_operator_id,json=internalOperatorId,proto3" json:"internal_operator_id,omitempty"`
}

func (x *CreateKeyRequest) Reset() {
	*x = CreateKeyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateKeyRequest) ProtoMessage() {}

func (x *CreateKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateKeyRequest.ProtoReflect.Descriptor instead.
func (*CreateKeyRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateKeyRequest) GetKeyDef() *v1.KeyFullDef {
	if x != nil {
		return x.KeyDef
	}
	return nil
}

func (x *CreateKeyRequest) GetInternalOperatorId() string {
	if x != nil {
		return x.InternalOperatorId
	}
	return ""
}

// create key response
type CreateKeyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the created key model
	Key *v1.KeyModel `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *CreateKeyResponse) Reset() {
	*x = CreateKeyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateKeyResponse) ProtoMessage() {}

func (x *CreateKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateKeyResponse.ProtoReflect.Descriptor instead.
func (*CreateKeyResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateKeyResponse) GetKey() *v1.KeyModel {
	if x != nil {
		return x.Key
	}
	return nil
}

// update key request
type UpdateKeyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the key def
	KeyDef *v1.KeyPartialDef `protobuf:"bytes,2,opt,name=key_def,json=keyDef,proto3" json:"key_def,omitempty"`
	// the operator id
	InternalOperatorId string `protobuf:"bytes,3,opt,name=internal_operator_id,json=internalOperatorId,proto3" json:"internal_operator_id,omitempty"`
}

func (x *UpdateKeyRequest) Reset() {
	*x = UpdateKeyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKeyRequest) ProtoMessage() {}

func (x *UpdateKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKeyRequest.ProtoReflect.Descriptor instead.
func (*UpdateKeyRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateKeyRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateKeyRequest) GetKeyDef() *v1.KeyPartialDef {
	if x != nil {
		return x.KeyDef
	}
	return nil
}

func (x *UpdateKeyRequest) GetInternalOperatorId() string {
	if x != nil {
		return x.InternalOperatorId
	}
	return ""
}

// get key request
type GetKeyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the key identifier
	//
	// Types that are assignable to Identifier:
	//
	//	*GetKeyRequest_Id
	//	*GetKeyRequest_Name
	Identifier isGetKeyRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *GetKeyRequest) Reset() {
	*x = GetKeyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKeyRequest) ProtoMessage() {}

func (x *GetKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKeyRequest.ProtoReflect.Descriptor instead.
func (*GetKeyRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{4}
}

func (m *GetKeyRequest) GetIdentifier() isGetKeyRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetKeyRequest) GetId() int64 {
	if x, ok := x.GetIdentifier().(*GetKeyRequest_Id); ok {
		return x.Id
	}
	return 0
}

func (x *GetKeyRequest) GetName() string {
	if x, ok := x.GetIdentifier().(*GetKeyRequest_Name); ok {
		return x.Name
	}
	return ""
}

type isGetKeyRequest_Identifier interface {
	isGetKeyRequest_Identifier()
}

type GetKeyRequest_Id struct {
	// the id, will not filter deleted
	Id int64 `protobuf:"varint,1,opt,name=id,proto3,oneof"`
}

type GetKeyRequest_Name struct {
	// the name, will filter non deleted
	Name string `protobuf:"bytes,2,opt,name=name,proto3,oneof"`
}

func (*GetKeyRequest_Id) isGetKeyRequest_Identifier() {}

func (*GetKeyRequest_Name) isGetKeyRequest_Identifier() {}

// get key response
type GetKeyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the key model
	Key *v1.KeyModel `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *GetKeyResponse) Reset() {
	*x = GetKeyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKeyResponse) ProtoMessage() {}

func (x *GetKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKeyResponse.ProtoReflect.Descriptor instead.
func (*GetKeyResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetKeyResponse) GetKey() *v1.KeyModel {
	if x != nil {
		return x.Key
	}
	return nil
}

// describe keys request
type DescribeKeysRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter by group, empty will not filter
	Group *string `protobuf:"bytes,1,opt,name=group,proto3,oneof" json:"group,omitempty"`
	// filter by owner type, 0 will not filter
	OwnerType *v1.OwnerType `protobuf:"varint,2,opt,name=owner_type,json=ownerType,proto3,enum=moego.models.metadata.v1.OwnerType,oneof" json:"owner_type,omitempty"`
	// filter by name like
	NameLike *string `protobuf:"bytes,3,opt,name=name_like,json=nameLike,proto3,oneof" json:"name_like,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *DescribeKeysRequest) Reset() {
	*x = DescribeKeysRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeKeysRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeKeysRequest) ProtoMessage() {}

func (x *DescribeKeysRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeKeysRequest.ProtoReflect.Descriptor instead.
func (*DescribeKeysRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{6}
}

func (x *DescribeKeysRequest) GetGroup() string {
	if x != nil && x.Group != nil {
		return *x.Group
	}
	return ""
}

func (x *DescribeKeysRequest) GetOwnerType() v1.OwnerType {
	if x != nil && x.OwnerType != nil {
		return *x.OwnerType
	}
	return v1.OwnerType(0)
}

func (x *DescribeKeysRequest) GetNameLike() string {
	if x != nil && x.NameLike != nil {
		return *x.NameLike
	}
	return ""
}

func (x *DescribeKeysRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// describe keys response
type DescribeKeysResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the key list
	Keys []*v1.KeyModel `protobuf:"bytes,1,rep,name=keys,proto3" json:"keys,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *DescribeKeysResponse) Reset() {
	*x = DescribeKeysResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeKeysResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeKeysResponse) ProtoMessage() {}

func (x *DescribeKeysResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeKeysResponse.ProtoReflect.Descriptor instead.
func (*DescribeKeysResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{7}
}

func (x *DescribeKeysResponse) GetKeys() []*v1.KeyModel {
	if x != nil {
		return x.Keys
	}
	return nil
}

func (x *DescribeKeysResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// delete key request
type DeleteKeyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the operator
	InternalOperatorId string `protobuf:"bytes,2,opt,name=internal_operator_id,json=internalOperatorId,proto3" json:"internal_operator_id,omitempty"`
}

func (x *DeleteKeyRequest) Reset() {
	*x = DeleteKeyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteKeyRequest) ProtoMessage() {}

func (x *DeleteKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteKeyRequest.ProtoReflect.Descriptor instead.
func (*DeleteKeyRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteKeyRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteKeyRequest) GetInternalOperatorId() string {
	if x != nil {
		return x.InternalOperatorId
	}
	return ""
}

// update value request
type UpdateValueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the key id
	KeyId int64 `protobuf:"varint,1,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	// the owner id
	OwnerId int64 `protobuf:"varint,2,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// the value, skip this field will reset the value to default value
	Value *string `protobuf:"bytes,3,opt,name=value,proto3,oneof" json:"value,omitempty"`
	// the operator id, allow internal modifier or external modifier
	//
	// Types that are assignable to OperatorIdentifier:
	//
	//	*UpdateValueRequest_OperatorId
	//	*UpdateValueRequest_InternalOperatorId
	OperatorIdentifier isUpdateValueRequest_OperatorIdentifier `protobuf_oneof:"operator_identifier"`
}

func (x *UpdateValueRequest) Reset() {
	*x = UpdateValueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateValueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateValueRequest) ProtoMessage() {}

func (x *UpdateValueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateValueRequest.ProtoReflect.Descriptor instead.
func (*UpdateValueRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateValueRequest) GetKeyId() int64 {
	if x != nil {
		return x.KeyId
	}
	return 0
}

func (x *UpdateValueRequest) GetOwnerId() int64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (x *UpdateValueRequest) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

func (m *UpdateValueRequest) GetOperatorIdentifier() isUpdateValueRequest_OperatorIdentifier {
	if m != nil {
		return m.OperatorIdentifier
	}
	return nil
}

func (x *UpdateValueRequest) GetOperatorId() int64 {
	if x, ok := x.GetOperatorIdentifier().(*UpdateValueRequest_OperatorId); ok {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateValueRequest) GetInternalOperatorId() string {
	if x, ok := x.GetOperatorIdentifier().(*UpdateValueRequest_InternalOperatorId); ok {
		return x.InternalOperatorId
	}
	return ""
}

type isUpdateValueRequest_OperatorIdentifier interface {
	isUpdateValueRequest_OperatorIdentifier()
}

type UpdateValueRequest_OperatorId struct {
	// the operator id
	OperatorId int64 `protobuf:"varint,4,opt,name=operator_id,json=operatorId,proto3,oneof"`
}

type UpdateValueRequest_InternalOperatorId struct {
	// the internal operator id
	InternalOperatorId string `protobuf:"bytes,5,opt,name=internal_operator_id,json=internalOperatorId,proto3,oneof"`
}

func (*UpdateValueRequest_OperatorId) isUpdateValueRequest_OperatorIdentifier() {}

func (*UpdateValueRequest_InternalOperatorId) isUpdateValueRequest_OperatorIdentifier() {}

// get value request
type GetValueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the key id
	KeyId int64 `protobuf:"varint,1,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	// the owner id, if it is system, any value is allowed
	OwnerId int64 `protobuf:"varint,3,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
}

func (x *GetValueRequest) Reset() {
	*x = GetValueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetValueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetValueRequest) ProtoMessage() {}

func (x *GetValueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetValueRequest.ProtoReflect.Descriptor instead.
func (*GetValueRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetValueRequest) GetKeyId() int64 {
	if x != nil {
		return x.KeyId
	}
	return 0
}

func (x *GetValueRequest) GetOwnerId() int64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

// get value response
type GetValueResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the value
	Value *v1.ValueModel `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *GetValueResponse) Reset() {
	*x = GetValueResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetValueResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetValueResponse) ProtoMessage() {}

func (x *GetValueResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetValueResponse.ProtoReflect.Descriptor instead.
func (*GetValueResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetValueResponse) GetValue() *v1.ValueModel {
	if x != nil {
		return x.Value
	}
	return nil
}

// describe values request, only find values
type DescribeValuesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter by key
	KeyId int64 `protobuf:"varint,1,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	// filter by owner ids
	OwnerIds []int64 `protobuf:"varint,2,rep,packed,name=owner_ids,json=ownerIds,proto3" json:"owner_ids,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,15,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *DescribeValuesRequest) Reset() {
	*x = DescribeValuesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeValuesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeValuesRequest) ProtoMessage() {}

func (x *DescribeValuesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeValuesRequest.ProtoReflect.Descriptor instead.
func (*DescribeValuesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{12}
}

func (x *DescribeValuesRequest) GetKeyId() int64 {
	if x != nil {
		return x.KeyId
	}
	return 0
}

func (x *DescribeValuesRequest) GetOwnerIds() []int64 {
	if x != nil {
		return x.OwnerIds
	}
	return nil
}

func (x *DescribeValuesRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// describe values response
type DescribeValuesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the values
	Values []*v1.ValueModel `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *DescribeValuesResponse) Reset() {
	*x = DescribeValuesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeValuesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeValuesResponse) ProtoMessage() {}

func (x *DescribeValuesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeValuesResponse.ProtoReflect.Descriptor instead.
func (*DescribeValuesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{13}
}

func (x *DescribeValuesResponse) GetValues() []*v1.ValueModel {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *DescribeValuesResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// get all values for specified owners
type ExtractValuesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the specifier to filter keys
	//
	// Types that are assignable to Specifier:
	//
	//	*ExtractValuesRequest_Group
	//	*ExtractValuesRequest_KeyId
	//	*ExtractValuesRequest_KeyName
	Specifier isExtractValuesRequest_Specifier `protobuf_oneof:"specifier"`
	// the owner map, key is OwnerType
	Owners map[int64]int64 `protobuf:"bytes,4,rep,name=owners,proto3" json:"owners,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *ExtractValuesRequest) Reset() {
	*x = ExtractValuesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtractValuesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtractValuesRequest) ProtoMessage() {}

func (x *ExtractValuesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtractValuesRequest.ProtoReflect.Descriptor instead.
func (*ExtractValuesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{14}
}

func (m *ExtractValuesRequest) GetSpecifier() isExtractValuesRequest_Specifier {
	if m != nil {
		return m.Specifier
	}
	return nil
}

func (x *ExtractValuesRequest) GetGroup() string {
	if x, ok := x.GetSpecifier().(*ExtractValuesRequest_Group); ok {
		return x.Group
	}
	return ""
}

func (x *ExtractValuesRequest) GetKeyId() int64 {
	if x, ok := x.GetSpecifier().(*ExtractValuesRequest_KeyId); ok {
		return x.KeyId
	}
	return 0
}

func (x *ExtractValuesRequest) GetKeyName() string {
	if x, ok := x.GetSpecifier().(*ExtractValuesRequest_KeyName); ok {
		return x.KeyName
	}
	return ""
}

func (x *ExtractValuesRequest) GetOwners() map[int64]int64 {
	if x != nil {
		return x.Owners
	}
	return nil
}

type isExtractValuesRequest_Specifier interface {
	isExtractValuesRequest_Specifier()
}

type ExtractValuesRequest_Group struct {
	// filter by group
	Group string `protobuf:"bytes,1,opt,name=group,proto3,oneof"`
}

type ExtractValuesRequest_KeyId struct {
	// the key id
	KeyId int64 `protobuf:"varint,2,opt,name=key_id,json=keyId,proto3,oneof"`
}

type ExtractValuesRequest_KeyName struct {
	// filter by key, if specified key does not
	// exist or expired, will throw an not found exception.
	KeyName string `protobuf:"bytes,3,opt,name=key_name,json=keyName,proto3,oneof"`
}

func (*ExtractValuesRequest_Group) isExtractValuesRequest_Specifier() {}

func (*ExtractValuesRequest_KeyId) isExtractValuesRequest_Specifier() {}

func (*ExtractValuesRequest_KeyName) isExtractValuesRequest_Specifier() {}

// get all values for specified owners
type DescribeMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the specifier to filter keys
	//
	// Types that are assignable to Specifier:
	//
	//	*DescribeMetadataRequest_Group
	//	*DescribeMetadataRequest_KeyId
	//	*DescribeMetadataRequest_KeyName
	Specifier isDescribeMetadataRequest_Specifier `protobuf_oneof:"specifier"`
	// the owner map, key is OwnerType
	Owners map[int64]int64 `protobuf:"bytes,4,rep,name=owners,proto3" json:"owners,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *DescribeMetadataRequest) Reset() {
	*x = DescribeMetadataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeMetadataRequest) ProtoMessage() {}

func (x *DescribeMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeMetadataRequest.ProtoReflect.Descriptor instead.
func (*DescribeMetadataRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{15}
}

func (m *DescribeMetadataRequest) GetSpecifier() isDescribeMetadataRequest_Specifier {
	if m != nil {
		return m.Specifier
	}
	return nil
}

func (x *DescribeMetadataRequest) GetGroup() string {
	if x, ok := x.GetSpecifier().(*DescribeMetadataRequest_Group); ok {
		return x.Group
	}
	return ""
}

func (x *DescribeMetadataRequest) GetKeyId() int64 {
	if x, ok := x.GetSpecifier().(*DescribeMetadataRequest_KeyId); ok {
		return x.KeyId
	}
	return 0
}

func (x *DescribeMetadataRequest) GetKeyName() string {
	if x, ok := x.GetSpecifier().(*DescribeMetadataRequest_KeyName); ok {
		return x.KeyName
	}
	return ""
}

func (x *DescribeMetadataRequest) GetOwners() map[int64]int64 {
	if x != nil {
		return x.Owners
	}
	return nil
}

type isDescribeMetadataRequest_Specifier interface {
	isDescribeMetadataRequest_Specifier()
}

type DescribeMetadataRequest_Group struct {
	// filter by group
	Group string `protobuf:"bytes,1,opt,name=group,proto3,oneof"`
}

type DescribeMetadataRequest_KeyId struct {
	// the key id
	KeyId int64 `protobuf:"varint,2,opt,name=key_id,json=keyId,proto3,oneof"`
}

type DescribeMetadataRequest_KeyName struct {
	// filter by key, if specified key does not
	// exist or expired, will throw an not found exception.
	KeyName string `protobuf:"bytes,3,opt,name=key_name,json=keyName,proto3,oneof"`
}

func (*DescribeMetadataRequest_Group) isDescribeMetadataRequest_Specifier() {}

func (*DescribeMetadataRequest_KeyId) isDescribeMetadataRequest_Specifier() {}

func (*DescribeMetadataRequest_KeyName) isDescribeMetadataRequest_Specifier() {}

// get all values response
type ExtractValuesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the values map
	//
	// Deprecated: Do not use.
	Values map[string]string `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ExtractValuesResponse) Reset() {
	*x = ExtractValuesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtractValuesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtractValuesResponse) ProtoMessage() {}

func (x *ExtractValuesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtractValuesResponse.ProtoReflect.Descriptor instead.
func (*ExtractValuesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{16}
}

// Deprecated: Do not use.
func (x *ExtractValuesResponse) GetValues() map[string]string {
	if x != nil {
		return x.Values
	}
	return nil
}

// get all values response
type DescribeMetadataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the key map
	KeyMap map[string]*v1.KeyModel `protobuf:"bytes,1,rep,name=key_map,json=keyMap,proto3" json:"key_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// the values map
	ValueMap map[string]*v1.ValueModel `protobuf:"bytes,2,rep,name=value_map,json=valueMap,proto3" json:"value_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DescribeMetadataResponse) Reset() {
	*x = DescribeMetadataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeMetadataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeMetadataResponse) ProtoMessage() {}

func (x *DescribeMetadataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeMetadataResponse.ProtoReflect.Descriptor instead.
func (*DescribeMetadataResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{17}
}

func (x *DescribeMetadataResponse) GetKeyMap() map[string]*v1.KeyModel {
	if x != nil {
		return x.KeyMap
	}
	return nil
}

func (x *DescribeMetadataResponse) GetValueMap() map[string]*v1.ValueModel {
	if x != nil {
		return x.ValueMap
	}
	return nil
}

// extract values v2 request
type ExtractValuesV2Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the specifier to filter keys
	//
	// Types that are assignable to Specifier:
	//
	//	*ExtractValuesV2Request_KeyId
	//	*ExtractValuesV2Request_KeyName
	Specifier isExtractValuesV2Request_Specifier `protobuf_oneof:"specifier"`
	// owner list
	Owners []*v1.OwnerModel `protobuf:"bytes,3,rep,name=owners,proto3" json:"owners,omitempty"`
}

func (x *ExtractValuesV2Request) Reset() {
	*x = ExtractValuesV2Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtractValuesV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtractValuesV2Request) ProtoMessage() {}

func (x *ExtractValuesV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtractValuesV2Request.ProtoReflect.Descriptor instead.
func (*ExtractValuesV2Request) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{18}
}

func (m *ExtractValuesV2Request) GetSpecifier() isExtractValuesV2Request_Specifier {
	if m != nil {
		return m.Specifier
	}
	return nil
}

func (x *ExtractValuesV2Request) GetKeyId() int64 {
	if x, ok := x.GetSpecifier().(*ExtractValuesV2Request_KeyId); ok {
		return x.KeyId
	}
	return 0
}

func (x *ExtractValuesV2Request) GetKeyName() string {
	if x, ok := x.GetSpecifier().(*ExtractValuesV2Request_KeyName); ok {
		return x.KeyName
	}
	return ""
}

func (x *ExtractValuesV2Request) GetOwners() []*v1.OwnerModel {
	if x != nil {
		return x.Owners
	}
	return nil
}

type isExtractValuesV2Request_Specifier interface {
	isExtractValuesV2Request_Specifier()
}

type ExtractValuesV2Request_KeyId struct {
	// the key id
	KeyId int64 `protobuf:"varint,1,opt,name=key_id,json=keyId,proto3,oneof"`
}

type ExtractValuesV2Request_KeyName struct {
	// filter by key, if specified key does not
	// exist or expired, will throw an not found exception.
	KeyName string `protobuf:"bytes,2,opt,name=key_name,json=keyName,proto3,oneof"`
}

func (*ExtractValuesV2Request_KeyId) isExtractValuesV2Request_Specifier() {}

func (*ExtractValuesV2Request_KeyName) isExtractValuesV2Request_Specifier() {}

// extract values v2 response
type ExtractValuesV2Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// results
	Results []*ExtractValuesV2Response_Result `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *ExtractValuesV2Response) Reset() {
	*x = ExtractValuesV2Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtractValuesV2Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtractValuesV2Response) ProtoMessage() {}

func (x *ExtractValuesV2Response) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtractValuesV2Response.ProtoReflect.Descriptor instead.
func (*ExtractValuesV2Response) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{19}
}

func (x *ExtractValuesV2Response) GetResults() []*ExtractValuesV2Response_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

// result
type ExtractValuesV2Response_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// owner
	Owner *v1.OwnerModel `protobuf:"bytes,1,opt,name=owner,proto3" json:"owner,omitempty"`
	// values
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *ExtractValuesV2Response_Result) Reset() {
	*x = ExtractValuesV2Response_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtractValuesV2Response_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtractValuesV2Response_Result) ProtoMessage() {}

func (x *ExtractValuesV2Response_Result) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_metadata_v1_metadata_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtractValuesV2Response_Result.ProtoReflect.Descriptor instead.
func (*ExtractValuesV2Response_Result) Descriptor() ([]byte, []int) {
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP(), []int{19, 0}
}

func (x *ExtractValuesV2Response_Result) GetOwner() *v1.OwnerModel {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *ExtractValuesV2Response_Result) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

var File_moego_service_metadata_v1_metadata_service_proto protoreflect.FileDescriptor

var file_moego_service_metadata_v1_metadata_service_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x64, 0x65,
	0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2f,
	0x76, 0x31, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76,
	0x31, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x30, 0x0a, 0x16, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0x98, 0x01, 0x0a,
	0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x47, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x65,
	0x79, 0x46, 0x75, 0x6c, 0x6c, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x06, 0x6b, 0x65, 0x79, 0x44, 0x65, 0x66, 0x12, 0x3b, 0x0a, 0x14, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x02, 0x18, 0x32, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x49, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x65, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x22, 0xb4, 0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x4a, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x65, 0x79,
	0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6b, 0x65, 0x79, 0x44, 0x65, 0x66, 0x12, 0x3b, 0x0a, 0x14,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x02, 0x18, 0x32, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x5e, 0x0a, 0x0d, 0x47, 0x65, 0x74,
	0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48,
	0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x02, 0x18, 0x32, 0x48, 0x00,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x11, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x46, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x65, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x22, 0xb9, 0x02, 0x0a, 0x13, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4b, 0x65,
	0x79, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x05, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x32, 0x48, 0x00, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x88, 0x01, 0x01, 0x12,
	0x53, 0x0a, 0x0a, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x77, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x69, 0x6b,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32,
	0x48, 0x02, 0x52, 0x08, 0x6e, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x6b, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x03, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x69, 0x6b, 0x65, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x92, 0x01,
	0x0a, 0x14, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4b, 0x65, 0x79, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e,
	0x4b, 0x65, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x12, 0x42,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x68, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x3b, 0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x02, 0x18, 0x32, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x8f, 0x02, 0x0a,
	0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x06, 0x6b, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x6b, 0x65,
	0x79, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x18, 0x80, 0x80,
	0x40, 0x48, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a,
	0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x14, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x02,
	0x18, 0x32, 0x48, 0x00, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x42, 0x1a, 0x0a, 0x13, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12,
	0x03, 0xf8, 0x42, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x55,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1e, 0x0a, 0x06, 0x6b, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x6b, 0x65, 0x79, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x49, 0x64, 0x22, 0x4e, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xc0, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1e, 0x0a, 0x06, 0x6b, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x6b, 0x65, 0x79, 0x49, 0x64, 0x12,
	0x30, 0x0a, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x10, 0xc8, 0x01, 0x18, 0x01, 0x22,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x28, 0x01, 0x52, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64,
	0x73, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9a, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xaf, 0x02, 0x0a, 0x14, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21,
	0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x48, 0x00, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x20, 0x0a, 0x06, 0x6b, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x05, 0x6b, 0x65,
	0x79, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x02, 0x18, 0x32,
	0x48, 0x00, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x5d, 0x0a, 0x06, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x77, 0x6e,
	0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x9a, 0x01, 0x02,
	0x10, 0x0a, 0x52, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x10, 0x0a, 0x09, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xb5, 0x02, 0x0a, 0x17, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x48, 0x00, 0x52,
	0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x20, 0x0a, 0x06, 0x6b, 0x65, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48,
	0x00, 0x52, 0x05, 0x6b, 0x65, 0x79, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x60, 0x0a, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x9a, 0x01, 0x02, 0x10, 0x0a, 0x52, 0x06, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x10, 0x0a,
	0x09, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22,
	0xac, 0x01, 0x0a, 0x15, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x06, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x96,
	0x03, 0x0a, 0x18, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x07, 0x6b,
	0x65, 0x79, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x4b, 0x65, 0x79, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6b,
	0x65, 0x79, 0x4d, 0x61, 0x70, 0x12, 0x5e, 0x0a, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x6d,
	0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x4d, 0x61, 0x70, 0x1a, 0x5d, 0x0a, 0x0b, 0x4b, 0x65, 0x79, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x38, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31,
	0x2e, 0x4b, 0x65, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x61, 0x0a, 0x0d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76,
	0x31, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb2, 0x01, 0x0a, 0x16, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x20, 0x0a, 0x06, 0x6b, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x05, 0x6b,
	0x65, 0x79, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x02, 0x18,
	0x32, 0x48, 0x00, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x06,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x42, 0x10, 0x0a, 0x09, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xca, 0x01, 0x0a,
	0x17, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x56, 0x32,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x53, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x5a, 0x0a,
	0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3a, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x32, 0xe7, 0x09, 0x0a, 0x0f, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5b, 0x0a,
	0x0e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x09, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x50, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x12,
	0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x5d, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x28,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4b, 0x65,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x6f, 0x0a, 0x0c, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4b,
	0x65, 0x79, 0x73, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4b, 0x65, 0x79, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4b, 0x65, 0x79, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x65,
	0x79, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x54, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x63, 0x0a, 0x08,
	0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x75, 0x0a, 0x0e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x0d, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x10,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x0f, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x56, 0x32, 0x12, 0x31, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x56, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x83, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_service_metadata_v1_metadata_service_proto_rawDescOnce sync.Once
	file_moego_service_metadata_v1_metadata_service_proto_rawDescData = file_moego_service_metadata_v1_metadata_service_proto_rawDesc
)

func file_moego_service_metadata_v1_metadata_service_proto_rawDescGZIP() []byte {
	file_moego_service_metadata_v1_metadata_service_proto_rawDescOnce.Do(func() {
		file_moego_service_metadata_v1_metadata_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_metadata_v1_metadata_service_proto_rawDescData)
	})
	return file_moego_service_metadata_v1_metadata_service_proto_rawDescData
}

var file_moego_service_metadata_v1_metadata_service_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_moego_service_metadata_v1_metadata_service_proto_goTypes = []interface{}{
	(*DescribeGroupsResponse)(nil),         // 0: moego.service.metadata.v1.DescribeGroupsResponse
	(*CreateKeyRequest)(nil),               // 1: moego.service.metadata.v1.CreateKeyRequest
	(*CreateKeyResponse)(nil),              // 2: moego.service.metadata.v1.CreateKeyResponse
	(*UpdateKeyRequest)(nil),               // 3: moego.service.metadata.v1.UpdateKeyRequest
	(*GetKeyRequest)(nil),                  // 4: moego.service.metadata.v1.GetKeyRequest
	(*GetKeyResponse)(nil),                 // 5: moego.service.metadata.v1.GetKeyResponse
	(*DescribeKeysRequest)(nil),            // 6: moego.service.metadata.v1.DescribeKeysRequest
	(*DescribeKeysResponse)(nil),           // 7: moego.service.metadata.v1.DescribeKeysResponse
	(*DeleteKeyRequest)(nil),               // 8: moego.service.metadata.v1.DeleteKeyRequest
	(*UpdateValueRequest)(nil),             // 9: moego.service.metadata.v1.UpdateValueRequest
	(*GetValueRequest)(nil),                // 10: moego.service.metadata.v1.GetValueRequest
	(*GetValueResponse)(nil),               // 11: moego.service.metadata.v1.GetValueResponse
	(*DescribeValuesRequest)(nil),          // 12: moego.service.metadata.v1.DescribeValuesRequest
	(*DescribeValuesResponse)(nil),         // 13: moego.service.metadata.v1.DescribeValuesResponse
	(*ExtractValuesRequest)(nil),           // 14: moego.service.metadata.v1.ExtractValuesRequest
	(*DescribeMetadataRequest)(nil),        // 15: moego.service.metadata.v1.DescribeMetadataRequest
	(*ExtractValuesResponse)(nil),          // 16: moego.service.metadata.v1.ExtractValuesResponse
	(*DescribeMetadataResponse)(nil),       // 17: moego.service.metadata.v1.DescribeMetadataResponse
	(*ExtractValuesV2Request)(nil),         // 18: moego.service.metadata.v1.ExtractValuesV2Request
	(*ExtractValuesV2Response)(nil),        // 19: moego.service.metadata.v1.ExtractValuesV2Response
	nil,                                    // 20: moego.service.metadata.v1.ExtractValuesRequest.OwnersEntry
	nil,                                    // 21: moego.service.metadata.v1.DescribeMetadataRequest.OwnersEntry
	nil,                                    // 22: moego.service.metadata.v1.ExtractValuesResponse.ValuesEntry
	nil,                                    // 23: moego.service.metadata.v1.DescribeMetadataResponse.KeyMapEntry
	nil,                                    // 24: moego.service.metadata.v1.DescribeMetadataResponse.ValueMapEntry
	(*ExtractValuesV2Response_Result)(nil), // 25: moego.service.metadata.v1.ExtractValuesV2Response.Result
	(*v1.KeyFullDef)(nil),                  // 26: moego.models.metadata.v1.KeyFullDef
	(*v1.KeyModel)(nil),                    // 27: moego.models.metadata.v1.KeyModel
	(*v1.KeyPartialDef)(nil),               // 28: moego.models.metadata.v1.KeyPartialDef
	(v1.OwnerType)(0),                      // 29: moego.models.metadata.v1.OwnerType
	(*v2.PaginationRequest)(nil),           // 30: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),          // 31: moego.utils.v2.PaginationResponse
	(*v1.ValueModel)(nil),                  // 32: moego.models.metadata.v1.ValueModel
	(*v1.OwnerModel)(nil),                  // 33: moego.models.metadata.v1.OwnerModel
	(*emptypb.Empty)(nil),                  // 34: google.protobuf.Empty
}
var file_moego_service_metadata_v1_metadata_service_proto_depIdxs = []int32{
	26, // 0: moego.service.metadata.v1.CreateKeyRequest.key_def:type_name -> moego.models.metadata.v1.KeyFullDef
	27, // 1: moego.service.metadata.v1.CreateKeyResponse.key:type_name -> moego.models.metadata.v1.KeyModel
	28, // 2: moego.service.metadata.v1.UpdateKeyRequest.key_def:type_name -> moego.models.metadata.v1.KeyPartialDef
	27, // 3: moego.service.metadata.v1.GetKeyResponse.key:type_name -> moego.models.metadata.v1.KeyModel
	29, // 4: moego.service.metadata.v1.DescribeKeysRequest.owner_type:type_name -> moego.models.metadata.v1.OwnerType
	30, // 5: moego.service.metadata.v1.DescribeKeysRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	27, // 6: moego.service.metadata.v1.DescribeKeysResponse.keys:type_name -> moego.models.metadata.v1.KeyModel
	31, // 7: moego.service.metadata.v1.DescribeKeysResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	32, // 8: moego.service.metadata.v1.GetValueResponse.value:type_name -> moego.models.metadata.v1.ValueModel
	30, // 9: moego.service.metadata.v1.DescribeValuesRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	32, // 10: moego.service.metadata.v1.DescribeValuesResponse.values:type_name -> moego.models.metadata.v1.ValueModel
	31, // 11: moego.service.metadata.v1.DescribeValuesResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	20, // 12: moego.service.metadata.v1.ExtractValuesRequest.owners:type_name -> moego.service.metadata.v1.ExtractValuesRequest.OwnersEntry
	21, // 13: moego.service.metadata.v1.DescribeMetadataRequest.owners:type_name -> moego.service.metadata.v1.DescribeMetadataRequest.OwnersEntry
	22, // 14: moego.service.metadata.v1.ExtractValuesResponse.values:type_name -> moego.service.metadata.v1.ExtractValuesResponse.ValuesEntry
	23, // 15: moego.service.metadata.v1.DescribeMetadataResponse.key_map:type_name -> moego.service.metadata.v1.DescribeMetadataResponse.KeyMapEntry
	24, // 16: moego.service.metadata.v1.DescribeMetadataResponse.value_map:type_name -> moego.service.metadata.v1.DescribeMetadataResponse.ValueMapEntry
	33, // 17: moego.service.metadata.v1.ExtractValuesV2Request.owners:type_name -> moego.models.metadata.v1.OwnerModel
	25, // 18: moego.service.metadata.v1.ExtractValuesV2Response.results:type_name -> moego.service.metadata.v1.ExtractValuesV2Response.Result
	27, // 19: moego.service.metadata.v1.DescribeMetadataResponse.KeyMapEntry.value:type_name -> moego.models.metadata.v1.KeyModel
	32, // 20: moego.service.metadata.v1.DescribeMetadataResponse.ValueMapEntry.value:type_name -> moego.models.metadata.v1.ValueModel
	33, // 21: moego.service.metadata.v1.ExtractValuesV2Response.Result.owner:type_name -> moego.models.metadata.v1.OwnerModel
	34, // 22: moego.service.metadata.v1.MetadataService.DescribeGroups:input_type -> google.protobuf.Empty
	1,  // 23: moego.service.metadata.v1.MetadataService.CreateKey:input_type -> moego.service.metadata.v1.CreateKeyRequest
	3,  // 24: moego.service.metadata.v1.MetadataService.UpdateKey:input_type -> moego.service.metadata.v1.UpdateKeyRequest
	4,  // 25: moego.service.metadata.v1.MetadataService.GetKey:input_type -> moego.service.metadata.v1.GetKeyRequest
	6,  // 26: moego.service.metadata.v1.MetadataService.DescribeKeys:input_type -> moego.service.metadata.v1.DescribeKeysRequest
	8,  // 27: moego.service.metadata.v1.MetadataService.DeleteKey:input_type -> moego.service.metadata.v1.DeleteKeyRequest
	9,  // 28: moego.service.metadata.v1.MetadataService.UpdateValue:input_type -> moego.service.metadata.v1.UpdateValueRequest
	10, // 29: moego.service.metadata.v1.MetadataService.GetValue:input_type -> moego.service.metadata.v1.GetValueRequest
	12, // 30: moego.service.metadata.v1.MetadataService.DescribeValues:input_type -> moego.service.metadata.v1.DescribeValuesRequest
	14, // 31: moego.service.metadata.v1.MetadataService.ExtractValues:input_type -> moego.service.metadata.v1.ExtractValuesRequest
	15, // 32: moego.service.metadata.v1.MetadataService.DescribeMetadata:input_type -> moego.service.metadata.v1.DescribeMetadataRequest
	18, // 33: moego.service.metadata.v1.MetadataService.ExtractValuesV2:input_type -> moego.service.metadata.v1.ExtractValuesV2Request
	0,  // 34: moego.service.metadata.v1.MetadataService.DescribeGroups:output_type -> moego.service.metadata.v1.DescribeGroupsResponse
	2,  // 35: moego.service.metadata.v1.MetadataService.CreateKey:output_type -> moego.service.metadata.v1.CreateKeyResponse
	34, // 36: moego.service.metadata.v1.MetadataService.UpdateKey:output_type -> google.protobuf.Empty
	5,  // 37: moego.service.metadata.v1.MetadataService.GetKey:output_type -> moego.service.metadata.v1.GetKeyResponse
	7,  // 38: moego.service.metadata.v1.MetadataService.DescribeKeys:output_type -> moego.service.metadata.v1.DescribeKeysResponse
	34, // 39: moego.service.metadata.v1.MetadataService.DeleteKey:output_type -> google.protobuf.Empty
	34, // 40: moego.service.metadata.v1.MetadataService.UpdateValue:output_type -> google.protobuf.Empty
	11, // 41: moego.service.metadata.v1.MetadataService.GetValue:output_type -> moego.service.metadata.v1.GetValueResponse
	13, // 42: moego.service.metadata.v1.MetadataService.DescribeValues:output_type -> moego.service.metadata.v1.DescribeValuesResponse
	16, // 43: moego.service.metadata.v1.MetadataService.ExtractValues:output_type -> moego.service.metadata.v1.ExtractValuesResponse
	17, // 44: moego.service.metadata.v1.MetadataService.DescribeMetadata:output_type -> moego.service.metadata.v1.DescribeMetadataResponse
	19, // 45: moego.service.metadata.v1.MetadataService.ExtractValuesV2:output_type -> moego.service.metadata.v1.ExtractValuesV2Response
	34, // [34:46] is the sub-list for method output_type
	22, // [22:34] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_moego_service_metadata_v1_metadata_service_proto_init() }
func file_moego_service_metadata_v1_metadata_service_proto_init() {
	if File_moego_service_metadata_v1_metadata_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeGroupsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateKeyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateKeyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateKeyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetKeyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetKeyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeKeysRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeKeysResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteKeyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateValueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetValueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetValueResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeValuesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeValuesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtractValuesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeMetadataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtractValuesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeMetadataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtractValuesV2Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtractValuesV2Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_metadata_v1_metadata_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtractValuesV2Response_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_metadata_v1_metadata_service_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*GetKeyRequest_Id)(nil),
		(*GetKeyRequest_Name)(nil),
	}
	file_moego_service_metadata_v1_metadata_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_service_metadata_v1_metadata_service_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*UpdateValueRequest_OperatorId)(nil),
		(*UpdateValueRequest_InternalOperatorId)(nil),
	}
	file_moego_service_metadata_v1_metadata_service_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_service_metadata_v1_metadata_service_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*ExtractValuesRequest_Group)(nil),
		(*ExtractValuesRequest_KeyId)(nil),
		(*ExtractValuesRequest_KeyName)(nil),
	}
	file_moego_service_metadata_v1_metadata_service_proto_msgTypes[15].OneofWrappers = []interface{}{
		(*DescribeMetadataRequest_Group)(nil),
		(*DescribeMetadataRequest_KeyId)(nil),
		(*DescribeMetadataRequest_KeyName)(nil),
	}
	file_moego_service_metadata_v1_metadata_service_proto_msgTypes[18].OneofWrappers = []interface{}{
		(*ExtractValuesV2Request_KeyId)(nil),
		(*ExtractValuesV2Request_KeyName)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_metadata_v1_metadata_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_metadata_v1_metadata_service_proto_goTypes,
		DependencyIndexes: file_moego_service_metadata_v1_metadata_service_proto_depIdxs,
		MessageInfos:      file_moego_service_metadata_v1_metadata_service_proto_msgTypes,
	}.Build()
	File_moego_service_metadata_v1_metadata_service_proto = out.File
	file_moego_service_metadata_v1_metadata_service_proto_rawDesc = nil
	file_moego_service_metadata_v1_metadata_service_proto_goTypes = nil
	file_moego_service_metadata_v1_metadata_service_proto_depIdxs = nil
}
