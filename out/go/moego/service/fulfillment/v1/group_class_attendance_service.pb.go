// @since 2025-04-07 15:32:56
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/fulfillment/v1/group_class_attendance_service.proto

package fulfillmentsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The request message for ListAttendances
type ListAttendancesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The group class session id
	GroupClassSessionId []int64 `protobuf:"varint,1,rep,packed,name=group_class_session_id,json=groupClassSessionId,proto3" json:"group_class_session_id,omitempty"`
}

func (x *ListAttendancesRequest) Reset() {
	*x = ListAttendancesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_group_class_attendance_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAttendancesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAttendancesRequest) ProtoMessage() {}

func (x *ListAttendancesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_group_class_attendance_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAttendancesRequest.ProtoReflect.Descriptor instead.
func (*ListAttendancesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_group_class_attendance_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListAttendancesRequest) GetGroupClassSessionId() []int64 {
	if x != nil {
		return x.GroupClassSessionId
	}
	return nil
}

// The response message for ListAttendances
type ListAttendancesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The group class attendance details
	Attendances []*v1.GroupClassAttendanceModel `protobuf:"bytes,1,rep,name=attendances,proto3" json:"attendances,omitempty"`
}

func (x *ListAttendancesResponse) Reset() {
	*x = ListAttendancesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_fulfillment_v1_group_class_attendance_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAttendancesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAttendancesResponse) ProtoMessage() {}

func (x *ListAttendancesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_fulfillment_v1_group_class_attendance_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAttendancesResponse.ProtoReflect.Descriptor instead.
func (*ListAttendancesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_fulfillment_v1_group_class_attendance_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListAttendancesResponse) GetAttendances() []*v1.GroupClassAttendanceModel {
	if x != nil {
		return x.Attendances
	}
	return nil
}

var File_moego_service_fulfillment_v1_group_class_attendance_service_proto protoreflect.FileDescriptor

var file_moego_service_fulfillment_v1_group_class_attendance_service_proto_rawDesc = []byte{
	0x0a, 0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6e,
	0x64, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x1a, 0x3f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6e,
	0x64, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x62, 0x0a, 0x16, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x74, 0x74, 0x65, 0x6e, 0x64, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x16, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x08, 0x01, 0x10,
	0xe8, 0x07, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x13, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22,
	0x73, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x74, 0x74, 0x65, 0x6e, 0x64, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x0b, 0x61, 0x74,
	0x74, 0x65, 0x6e, 0x64, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66,
	0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6e, 0x64, 0x61, 0x6e,
	0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x65, 0x6e, 0x64, 0x61,
	0x6e, 0x63, 0x65, 0x73, 0x32, 0x9d, 0x01, 0x0a, 0x1b, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6e, 0x64, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x7e, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x74, 0x74, 0x65,
	0x6e, 0x64, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x74, 0x74, 0x65, 0x6e,
	0x64, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x75,
	0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x74, 0x74, 0x65, 0x6e, 0x64, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8c, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66,
	0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x62, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2f, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x3b, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76,
	0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_fulfillment_v1_group_class_attendance_service_proto_rawDescOnce sync.Once
	file_moego_service_fulfillment_v1_group_class_attendance_service_proto_rawDescData = file_moego_service_fulfillment_v1_group_class_attendance_service_proto_rawDesc
)

func file_moego_service_fulfillment_v1_group_class_attendance_service_proto_rawDescGZIP() []byte {
	file_moego_service_fulfillment_v1_group_class_attendance_service_proto_rawDescOnce.Do(func() {
		file_moego_service_fulfillment_v1_group_class_attendance_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_fulfillment_v1_group_class_attendance_service_proto_rawDescData)
	})
	return file_moego_service_fulfillment_v1_group_class_attendance_service_proto_rawDescData
}

var file_moego_service_fulfillment_v1_group_class_attendance_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_service_fulfillment_v1_group_class_attendance_service_proto_goTypes = []interface{}{
	(*ListAttendancesRequest)(nil),       // 0: moego.service.fulfillment.v1.ListAttendancesRequest
	(*ListAttendancesResponse)(nil),      // 1: moego.service.fulfillment.v1.ListAttendancesResponse
	(*v1.GroupClassAttendanceModel)(nil), // 2: moego.models.fulfillment.v1.GroupClassAttendanceModel
}
var file_moego_service_fulfillment_v1_group_class_attendance_service_proto_depIdxs = []int32{
	2, // 0: moego.service.fulfillment.v1.ListAttendancesResponse.attendances:type_name -> moego.models.fulfillment.v1.GroupClassAttendanceModel
	0, // 1: moego.service.fulfillment.v1.GroupClassAttendanceService.ListAttendances:input_type -> moego.service.fulfillment.v1.ListAttendancesRequest
	1, // 2: moego.service.fulfillment.v1.GroupClassAttendanceService.ListAttendances:output_type -> moego.service.fulfillment.v1.ListAttendancesResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_service_fulfillment_v1_group_class_attendance_service_proto_init() }
func file_moego_service_fulfillment_v1_group_class_attendance_service_proto_init() {
	if File_moego_service_fulfillment_v1_group_class_attendance_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_fulfillment_v1_group_class_attendance_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAttendancesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_fulfillment_v1_group_class_attendance_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAttendancesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_fulfillment_v1_group_class_attendance_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_fulfillment_v1_group_class_attendance_service_proto_goTypes,
		DependencyIndexes: file_moego_service_fulfillment_v1_group_class_attendance_service_proto_depIdxs,
		MessageInfos:      file_moego_service_fulfillment_v1_group_class_attendance_service_proto_msgTypes,
	}.Build()
	File_moego_service_fulfillment_v1_group_class_attendance_service_proto = out.File
	file_moego_service_fulfillment_v1_group_class_attendance_service_proto_rawDesc = nil
	file_moego_service_fulfillment_v1_group_class_attendance_service_proto_goTypes = nil
	file_moego_service_fulfillment_v1_group_class_attendance_service_proto_depIdxs = nil
}
