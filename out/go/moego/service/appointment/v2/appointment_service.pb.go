// @since 2025/6/13
// <AUTHOR>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/appointment/v2/appointment_service.proto

package appointmentsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UpdateAppointment request
type UpdateAppointmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id, optional
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// business id, optional
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// Appointment id
	AppointmentId int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// appointment to update
	Appointment *UpdateAppointmentRequest_Appointment `protobuf:"bytes,4,opt,name=appointment,proto3,oneof" json:"appointment,omitempty"`
	// pet details to update
	PetDetails []*UpdateAppointmentRequest_PetDetail `protobuf:"bytes,5,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
}

func (x *UpdateAppointmentRequest) Reset() {
	*x = UpdateAppointmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v2_appointment_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentRequest) ProtoMessage() {}

func (x *UpdateAppointmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v2_appointment_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentRequest.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v2_appointment_service_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateAppointmentRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *UpdateAppointmentRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *UpdateAppointmentRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *UpdateAppointmentRequest) GetAppointment() *UpdateAppointmentRequest_Appointment {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *UpdateAppointmentRequest) GetPetDetails() []*UpdateAppointmentRequest_PetDetail {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

// UpdateAppointment response
type UpdateAppointmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateAppointmentResponse) Reset() {
	*x = UpdateAppointmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v2_appointment_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentResponse) ProtoMessage() {}

func (x *UpdateAppointmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v2_appointment_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentResponse.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v2_appointment_service_proto_rawDescGZIP(), []int{1}
}

// Appointment
type UpdateAppointmentRequest_Appointment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateAppointmentRequest_Appointment) Reset() {
	*x = UpdateAppointmentRequest_Appointment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v2_appointment_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentRequest_Appointment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentRequest_Appointment) ProtoMessage() {}

func (x *UpdateAppointmentRequest_Appointment) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v2_appointment_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentRequest_Appointment.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentRequest_Appointment) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v2_appointment_service_proto_rawDescGZIP(), []int{0, 0}
}

// Pet detail
type UpdateAppointmentRequest_PetDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// action
	//
	// Types that are assignable to Action:
	//
	//	*UpdateAppointmentRequest_PetDetail_Add_
	//	*UpdateAppointmentRequest_PetDetail_Update_
	//	*UpdateAppointmentRequest_PetDetail_Delete_
	Action isUpdateAppointmentRequest_PetDetail_Action `protobuf_oneof:"action"`
}

func (x *UpdateAppointmentRequest_PetDetail) Reset() {
	*x = UpdateAppointmentRequest_PetDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v2_appointment_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentRequest_PetDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentRequest_PetDetail) ProtoMessage() {}

func (x *UpdateAppointmentRequest_PetDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v2_appointment_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentRequest_PetDetail.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentRequest_PetDetail) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v2_appointment_service_proto_rawDescGZIP(), []int{0, 1}
}

func (m *UpdateAppointmentRequest_PetDetail) GetAction() isUpdateAppointmentRequest_PetDetail_Action {
	if m != nil {
		return m.Action
	}
	return nil
}

func (x *UpdateAppointmentRequest_PetDetail) GetAdd() *UpdateAppointmentRequest_PetDetail_Add {
	if x, ok := x.GetAction().(*UpdateAppointmentRequest_PetDetail_Add_); ok {
		return x.Add
	}
	return nil
}

func (x *UpdateAppointmentRequest_PetDetail) GetUpdate() *UpdateAppointmentRequest_PetDetail_Update {
	if x, ok := x.GetAction().(*UpdateAppointmentRequest_PetDetail_Update_); ok {
		return x.Update
	}
	return nil
}

func (x *UpdateAppointmentRequest_PetDetail) GetDelete() *UpdateAppointmentRequest_PetDetail_Delete {
	if x, ok := x.GetAction().(*UpdateAppointmentRequest_PetDetail_Delete_); ok {
		return x.Delete
	}
	return nil
}

type isUpdateAppointmentRequest_PetDetail_Action interface {
	isUpdateAppointmentRequest_PetDetail_Action()
}

type UpdateAppointmentRequest_PetDetail_Add_ struct {
	// add pet detail
	Add *UpdateAppointmentRequest_PetDetail_Add `protobuf:"bytes,1,opt,name=add,proto3,oneof"`
}

type UpdateAppointmentRequest_PetDetail_Update_ struct {
	// update pet detail
	Update *UpdateAppointmentRequest_PetDetail_Update `protobuf:"bytes,2,opt,name=update,proto3,oneof"`
}

type UpdateAppointmentRequest_PetDetail_Delete_ struct {
	// delete pet detail
	Delete *UpdateAppointmentRequest_PetDetail_Delete `protobuf:"bytes,3,opt,name=delete,proto3,oneof"`
}

func (*UpdateAppointmentRequest_PetDetail_Add_) isUpdateAppointmentRequest_PetDetail_Action() {}

func (*UpdateAppointmentRequest_PetDetail_Update_) isUpdateAppointmentRequest_PetDetail_Action() {}

func (*UpdateAppointmentRequest_PetDetail_Delete_) isUpdateAppointmentRequest_PetDetail_Action() {}

// Add pet detail
type UpdateAppointmentRequest_PetDetail_Add struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail
	PetDetail *v1.CreatePetDetailRequest `protobuf:"bytes,1,opt,name=pet_detail,json=petDetail,proto3" json:"pet_detail,omitempty"`
}

func (x *UpdateAppointmentRequest_PetDetail_Add) Reset() {
	*x = UpdateAppointmentRequest_PetDetail_Add{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v2_appointment_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentRequest_PetDetail_Add) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentRequest_PetDetail_Add) ProtoMessage() {}

func (x *UpdateAppointmentRequest_PetDetail_Add) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v2_appointment_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentRequest_PetDetail_Add.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentRequest_PetDetail_Add) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v2_appointment_service_proto_rawDescGZIP(), []int{0, 1, 0}
}

func (x *UpdateAppointmentRequest_PetDetail_Add) GetPetDetail() *v1.CreatePetDetailRequest {
	if x != nil {
		return x.PetDetail
	}
	return nil
}

// Update pet detail
type UpdateAppointmentRequest_PetDetail_Update struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail
	PetDetail *v1.UpdatePetDetailRequest `protobuf:"bytes,1,opt,name=pet_detail,json=petDetail,proto3" json:"pet_detail,omitempty"`
}

func (x *UpdateAppointmentRequest_PetDetail_Update) Reset() {
	*x = UpdateAppointmentRequest_PetDetail_Update{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v2_appointment_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentRequest_PetDetail_Update) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentRequest_PetDetail_Update) ProtoMessage() {}

func (x *UpdateAppointmentRequest_PetDetail_Update) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v2_appointment_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentRequest_PetDetail_Update.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentRequest_PetDetail_Update) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v2_appointment_service_proto_rawDescGZIP(), []int{0, 1, 1}
}

func (x *UpdateAppointmentRequest_PetDetail_Update) GetPetDetail() *v1.UpdatePetDetailRequest {
	if x != nil {
		return x.PetDetail
	}
	return nil
}

// Delete pet detail
type UpdateAppointmentRequest_PetDetail_Delete struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail id
	PetDetailId int64 `protobuf:"varint,1,opt,name=pet_detail_id,json=petDetailId,proto3" json:"pet_detail_id,omitempty"`
}

func (x *UpdateAppointmentRequest_PetDetail_Delete) Reset() {
	*x = UpdateAppointmentRequest_PetDetail_Delete{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v2_appointment_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentRequest_PetDetail_Delete) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentRequest_PetDetail_Delete) ProtoMessage() {}

func (x *UpdateAppointmentRequest_PetDetail_Delete) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v2_appointment_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentRequest_PetDetail_Delete.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentRequest_PetDetail_Delete) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v2_appointment_service_proto_rawDescGZIP(), []int{0, 1, 2}
}

func (x *UpdateAppointmentRequest_PetDetail_Delete) GetPetDetailId() int64 {
	if x != nil {
		return x.PetDetailId
	}
	return 0
}

var File_moego_service_appointment_v2_appointment_service_proto protoreflect.FileDescriptor

var file_moego_service_appointment_v2_appointment_service_proto_rawDesc = []byte{
	0x0a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x1a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf0, 0x07, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x69, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x02, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x61, 0x0a, 0x0b, 0x70, 0x65,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x0d, 0x0a,
	0x0b, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0xbb, 0x04, 0x0a,
	0x09, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x58, 0x0a, 0x03, 0x61, 0x64,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x41, 0x64, 0x64, 0x48, 0x00, 0x52,
	0x03, 0x61, 0x64, 0x64, 0x12, 0x61, 0x0a, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52,
	0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x61, 0x0a, 0x06, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x48, 0x00, 0x52, 0x06, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x1a, 0x64, 0x0a, 0x03, 0x41, 0x64,
	0x64, 0x12, 0x5d, 0x0a, 0x0a, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x09, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x1a, 0x67, 0x0a, 0x06, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x5d, 0x0a, 0x0a, 0x70, 0x65,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x09,
	0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x1a, 0x35, 0x0a, 0x06, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x12, 0x2b, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64,
	0x42, 0x08, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x1b, 0x0a, 0x19, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x9b, 0x01, 0x0a, 0x12, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x84, 0x01,
	0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8c, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a,
	0x62, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x32, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76,
	0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_appointment_v2_appointment_service_proto_rawDescOnce sync.Once
	file_moego_service_appointment_v2_appointment_service_proto_rawDescData = file_moego_service_appointment_v2_appointment_service_proto_rawDesc
)

func file_moego_service_appointment_v2_appointment_service_proto_rawDescGZIP() []byte {
	file_moego_service_appointment_v2_appointment_service_proto_rawDescOnce.Do(func() {
		file_moego_service_appointment_v2_appointment_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_appointment_v2_appointment_service_proto_rawDescData)
	})
	return file_moego_service_appointment_v2_appointment_service_proto_rawDescData
}

var file_moego_service_appointment_v2_appointment_service_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_service_appointment_v2_appointment_service_proto_goTypes = []interface{}{
	(*UpdateAppointmentRequest)(nil),                  // 0: moego.service.appointment.v2.UpdateAppointmentRequest
	(*UpdateAppointmentResponse)(nil),                 // 1: moego.service.appointment.v2.UpdateAppointmentResponse
	(*UpdateAppointmentRequest_Appointment)(nil),      // 2: moego.service.appointment.v2.UpdateAppointmentRequest.Appointment
	(*UpdateAppointmentRequest_PetDetail)(nil),        // 3: moego.service.appointment.v2.UpdateAppointmentRequest.PetDetail
	(*UpdateAppointmentRequest_PetDetail_Add)(nil),    // 4: moego.service.appointment.v2.UpdateAppointmentRequest.PetDetail.Add
	(*UpdateAppointmentRequest_PetDetail_Update)(nil), // 5: moego.service.appointment.v2.UpdateAppointmentRequest.PetDetail.Update
	(*UpdateAppointmentRequest_PetDetail_Delete)(nil), // 6: moego.service.appointment.v2.UpdateAppointmentRequest.PetDetail.Delete
	(*v1.CreatePetDetailRequest)(nil),                 // 7: moego.service.appointment.v1.CreatePetDetailRequest
	(*v1.UpdatePetDetailRequest)(nil),                 // 8: moego.service.appointment.v1.UpdatePetDetailRequest
}
var file_moego_service_appointment_v2_appointment_service_proto_depIdxs = []int32{
	2, // 0: moego.service.appointment.v2.UpdateAppointmentRequest.appointment:type_name -> moego.service.appointment.v2.UpdateAppointmentRequest.Appointment
	3, // 1: moego.service.appointment.v2.UpdateAppointmentRequest.pet_details:type_name -> moego.service.appointment.v2.UpdateAppointmentRequest.PetDetail
	4, // 2: moego.service.appointment.v2.UpdateAppointmentRequest.PetDetail.add:type_name -> moego.service.appointment.v2.UpdateAppointmentRequest.PetDetail.Add
	5, // 3: moego.service.appointment.v2.UpdateAppointmentRequest.PetDetail.update:type_name -> moego.service.appointment.v2.UpdateAppointmentRequest.PetDetail.Update
	6, // 4: moego.service.appointment.v2.UpdateAppointmentRequest.PetDetail.delete:type_name -> moego.service.appointment.v2.UpdateAppointmentRequest.PetDetail.Delete
	7, // 5: moego.service.appointment.v2.UpdateAppointmentRequest.PetDetail.Add.pet_detail:type_name -> moego.service.appointment.v1.CreatePetDetailRequest
	8, // 6: moego.service.appointment.v2.UpdateAppointmentRequest.PetDetail.Update.pet_detail:type_name -> moego.service.appointment.v1.UpdatePetDetailRequest
	0, // 7: moego.service.appointment.v2.AppointmentService.UpdateAppointment:input_type -> moego.service.appointment.v2.UpdateAppointmentRequest
	1, // 8: moego.service.appointment.v2.AppointmentService.UpdateAppointment:output_type -> moego.service.appointment.v2.UpdateAppointmentResponse
	8, // [8:9] is the sub-list for method output_type
	7, // [7:8] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_moego_service_appointment_v2_appointment_service_proto_init() }
func file_moego_service_appointment_v2_appointment_service_proto_init() {
	if File_moego_service_appointment_v2_appointment_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_appointment_v2_appointment_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v2_appointment_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v2_appointment_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentRequest_Appointment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v2_appointment_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentRequest_PetDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v2_appointment_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentRequest_PetDetail_Add); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v2_appointment_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentRequest_PetDetail_Update); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v2_appointment_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentRequest_PetDetail_Delete); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_appointment_v2_appointment_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v2_appointment_service_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*UpdateAppointmentRequest_PetDetail_Add_)(nil),
		(*UpdateAppointmentRequest_PetDetail_Update_)(nil),
		(*UpdateAppointmentRequest_PetDetail_Delete_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_appointment_v2_appointment_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_appointment_v2_appointment_service_proto_goTypes,
		DependencyIndexes: file_moego_service_appointment_v2_appointment_service_proto_depIdxs,
		MessageInfos:      file_moego_service_appointment_v2_appointment_service_proto_msgTypes,
	}.Build()
	File_moego_service_appointment_v2_appointment_service_proto = out.File
	file_moego_service_appointment_v2_appointment_service_proto_rawDesc = nil
	file_moego_service_appointment_v2_appointment_service_proto_goTypes = nil
	file_moego_service_appointment_v2_appointment_service_proto_depIdxs = nil
}
