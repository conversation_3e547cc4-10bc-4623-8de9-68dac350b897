// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/appointment/v1/overview_service.proto

package appointmentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// OverviewServiceClient is the client API for OverviewService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OverviewServiceClient interface {
	// get overview list
	GetOverviewList(ctx context.Context, in *GetOverviewListRequest, opts ...grpc.CallOption) (*GetOverviewListResponse, error)
	// list overview appointment
	ListOverviewAppointment(ctx context.Context, in *ListOverviewAppointmentRequest, opts ...grpc.CallOption) (*ListOverviewAppointmentResponse, error)
}

type overviewServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOverviewServiceClient(cc grpc.ClientConnInterface) OverviewServiceClient {
	return &overviewServiceClient{cc}
}

func (c *overviewServiceClient) GetOverviewList(ctx context.Context, in *GetOverviewListRequest, opts ...grpc.CallOption) (*GetOverviewListResponse, error) {
	out := new(GetOverviewListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.OverviewService/GetOverviewList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *overviewServiceClient) ListOverviewAppointment(ctx context.Context, in *ListOverviewAppointmentRequest, opts ...grpc.CallOption) (*ListOverviewAppointmentResponse, error) {
	out := new(ListOverviewAppointmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.OverviewService/ListOverviewAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OverviewServiceServer is the server API for OverviewService service.
// All implementations must embed UnimplementedOverviewServiceServer
// for forward compatibility
type OverviewServiceServer interface {
	// get overview list
	GetOverviewList(context.Context, *GetOverviewListRequest) (*GetOverviewListResponse, error)
	// list overview appointment
	ListOverviewAppointment(context.Context, *ListOverviewAppointmentRequest) (*ListOverviewAppointmentResponse, error)
	mustEmbedUnimplementedOverviewServiceServer()
}

// UnimplementedOverviewServiceServer must be embedded to have forward compatible implementations.
type UnimplementedOverviewServiceServer struct {
}

func (UnimplementedOverviewServiceServer) GetOverviewList(context.Context, *GetOverviewListRequest) (*GetOverviewListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOverviewList not implemented")
}
func (UnimplementedOverviewServiceServer) ListOverviewAppointment(context.Context, *ListOverviewAppointmentRequest) (*ListOverviewAppointmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOverviewAppointment not implemented")
}
func (UnimplementedOverviewServiceServer) mustEmbedUnimplementedOverviewServiceServer() {}

// UnsafeOverviewServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OverviewServiceServer will
// result in compilation errors.
type UnsafeOverviewServiceServer interface {
	mustEmbedUnimplementedOverviewServiceServer()
}

func RegisterOverviewServiceServer(s grpc.ServiceRegistrar, srv OverviewServiceServer) {
	s.RegisterService(&OverviewService_ServiceDesc, srv)
}

func _OverviewService_GetOverviewList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOverviewListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OverviewServiceServer).GetOverviewList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.OverviewService/GetOverviewList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OverviewServiceServer).GetOverviewList(ctx, req.(*GetOverviewListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OverviewService_ListOverviewAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOverviewAppointmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OverviewServiceServer).ListOverviewAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.OverviewService/ListOverviewAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OverviewServiceServer).ListOverviewAppointment(ctx, req.(*ListOverviewAppointmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OverviewService_ServiceDesc is the grpc.ServiceDesc for OverviewService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OverviewService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.appointment.v1.OverviewService",
	HandlerType: (*OverviewServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOverviewList",
			Handler:    _OverviewService_GetOverviewList_Handler,
		},
		{
			MethodName: "ListOverviewAppointment",
			Handler:    _OverviewService_ListOverviewAppointment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/appointment/v1/overview_service.proto",
}
