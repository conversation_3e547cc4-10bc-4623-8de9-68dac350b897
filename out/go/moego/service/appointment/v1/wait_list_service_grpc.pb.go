// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/appointment/v1/wait_list_service.proto

package appointmentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// WaitListServiceClient is the client API for WaitListService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WaitListServiceClient interface {
	// get wait_list
	GetWaitListByAppointment(ctx context.Context, in *GetWaitListByAppointmentRequest, opts ...grpc.CallOption) (*GetWaitListByAppointmentResponse, error)
}

type waitListServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWaitListServiceClient(cc grpc.ClientConnInterface) WaitListServiceClient {
	return &waitListServiceClient{cc}
}

func (c *waitListServiceClient) GetWaitListByAppointment(ctx context.Context, in *GetWaitListByAppointmentRequest, opts ...grpc.CallOption) (*GetWaitListByAppointmentResponse, error) {
	out := new(GetWaitListByAppointmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.WaitListService/GetWaitListByAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WaitListServiceServer is the server API for WaitListService service.
// All implementations must embed UnimplementedWaitListServiceServer
// for forward compatibility
type WaitListServiceServer interface {
	// get wait_list
	GetWaitListByAppointment(context.Context, *GetWaitListByAppointmentRequest) (*GetWaitListByAppointmentResponse, error)
	mustEmbedUnimplementedWaitListServiceServer()
}

// UnimplementedWaitListServiceServer must be embedded to have forward compatible implementations.
type UnimplementedWaitListServiceServer struct {
}

func (UnimplementedWaitListServiceServer) GetWaitListByAppointment(context.Context, *GetWaitListByAppointmentRequest) (*GetWaitListByAppointmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWaitListByAppointment not implemented")
}
func (UnimplementedWaitListServiceServer) mustEmbedUnimplementedWaitListServiceServer() {}

// UnsafeWaitListServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WaitListServiceServer will
// result in compilation errors.
type UnsafeWaitListServiceServer interface {
	mustEmbedUnimplementedWaitListServiceServer()
}

func RegisterWaitListServiceServer(s grpc.ServiceRegistrar, srv WaitListServiceServer) {
	s.RegisterService(&WaitListService_ServiceDesc, srv)
}

func _WaitListService_GetWaitListByAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWaitListByAppointmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WaitListServiceServer).GetWaitListByAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.WaitListService/GetWaitListByAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WaitListServiceServer).GetWaitListByAppointment(ctx, req.(*GetWaitListByAppointmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WaitListService_ServiceDesc is the grpc.ServiceDesc for WaitListService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WaitListService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.appointment.v1.WaitListService",
	HandlerType: (*WaitListServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetWaitListByAppointment",
			Handler:    _WaitListService_GetWaitListByAppointment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/appointment/v1/wait_list_service.proto",
}
