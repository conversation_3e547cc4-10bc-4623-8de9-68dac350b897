// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/appointment/v1/apply_service_charge_service.proto

package appointmentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ApplyServiceChargeServiceClient is the client API for ApplyServiceChargeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ApplyServiceChargeServiceClient interface {
	// get auto apply service charge
	GetAutoApplyServiceCharge(ctx context.Context, in *GetAutoApplyServiceChargeRequest, opts ...grpc.CallOption) (*GetAutoApplyServiceChargeResponse, error)
}

type applyServiceChargeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewApplyServiceChargeServiceClient(cc grpc.ClientConnInterface) ApplyServiceChargeServiceClient {
	return &applyServiceChargeServiceClient{cc}
}

func (c *applyServiceChargeServiceClient) GetAutoApplyServiceCharge(ctx context.Context, in *GetAutoApplyServiceChargeRequest, opts ...grpc.CallOption) (*GetAutoApplyServiceChargeResponse, error) {
	out := new(GetAutoApplyServiceChargeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.ApplyServiceChargeService/GetAutoApplyServiceCharge", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApplyServiceChargeServiceServer is the server API for ApplyServiceChargeService service.
// All implementations must embed UnimplementedApplyServiceChargeServiceServer
// for forward compatibility
type ApplyServiceChargeServiceServer interface {
	// get auto apply service charge
	GetAutoApplyServiceCharge(context.Context, *GetAutoApplyServiceChargeRequest) (*GetAutoApplyServiceChargeResponse, error)
	mustEmbedUnimplementedApplyServiceChargeServiceServer()
}

// UnimplementedApplyServiceChargeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedApplyServiceChargeServiceServer struct {
}

func (UnimplementedApplyServiceChargeServiceServer) GetAutoApplyServiceCharge(context.Context, *GetAutoApplyServiceChargeRequest) (*GetAutoApplyServiceChargeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAutoApplyServiceCharge not implemented")
}
func (UnimplementedApplyServiceChargeServiceServer) mustEmbedUnimplementedApplyServiceChargeServiceServer() {
}

// UnsafeApplyServiceChargeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ApplyServiceChargeServiceServer will
// result in compilation errors.
type UnsafeApplyServiceChargeServiceServer interface {
	mustEmbedUnimplementedApplyServiceChargeServiceServer()
}

func RegisterApplyServiceChargeServiceServer(s grpc.ServiceRegistrar, srv ApplyServiceChargeServiceServer) {
	s.RegisterService(&ApplyServiceChargeService_ServiceDesc, srv)
}

func _ApplyServiceChargeService_GetAutoApplyServiceCharge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAutoApplyServiceChargeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApplyServiceChargeServiceServer).GetAutoApplyServiceCharge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.ApplyServiceChargeService/GetAutoApplyServiceCharge",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApplyServiceChargeServiceServer).GetAutoApplyServiceCharge(ctx, req.(*GetAutoApplyServiceChargeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ApplyServiceChargeService_ServiceDesc is the grpc.ServiceDesc for ApplyServiceChargeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ApplyServiceChargeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.appointment.v1.ApplyServiceChargeService",
	HandlerType: (*ApplyServiceChargeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAutoApplyServiceCharge",
			Handler:    _ApplyServiceChargeService_GetAutoApplyServiceCharge_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/appointment/v1/apply_service_charge_service.proto",
}
