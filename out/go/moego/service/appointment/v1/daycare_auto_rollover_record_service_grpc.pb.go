// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/appointment/v1/daycare_auto_rollover_record_service.proto

package appointmentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// DaycareAutoRolloverRecordServiceClient is the client API for DaycareAutoRolloverRecordService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DaycareAutoRolloverRecordServiceClient interface {
	// Create a record, return inserted record.
	CreateDaycareAutoRolloverRecord(ctx context.Context, in *CreateDaycareAutoRolloverRecordRequest, opts ...grpc.CallOption) (*CreateDaycareAutoRolloverRecordResponse, error)
	// 给指定 service id 批量创建 rollover 记录
	BatchCreateDaycareAutoRolloverRecordByServiceId(ctx context.Context, in *BatchCreateDaycareAutoRolloverRecordByServiceIdRequest, opts ...grpc.CallOption) (*BatchCreateDaycareAutoRolloverRecordByServiceIdResponse, error)
	// 删除指定 service id 的所有 rollover 记录
	BatchDeleteDaycareAutoRolloverRecordByServiceId(ctx context.Context, in *BatchDeleteDaycareAutoRolloverRecordByServiceIdRequest, opts ...grpc.CallOption) (*BatchDeleteDaycareAutoRolloverRecordByServiceIdResponse, error)
	// 刷新指定 service id 的所有 rollover 记录的 rollover_time
	RefreshDaycareAutoRolloverRecordByServiceId(ctx context.Context, in *RefreshDaycareAutoRolloverRecordByServiceIdRequest, opts ...grpc.CallOption) (*RefreshDaycareAutoRolloverRecordByServiceIdResponse, error)
	// Rollover DaycareAutoRolloverRecord.
	// Rollover 指定时间范围内的记录
	RolloverDaycareAutoRolloverRecord(ctx context.Context, in *RolloverDaycareAutoRolloverRecordRequest, opts ...grpc.CallOption) (*RolloverDaycareAutoRolloverRecordResponse, error)
}

type daycareAutoRolloverRecordServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDaycareAutoRolloverRecordServiceClient(cc grpc.ClientConnInterface) DaycareAutoRolloverRecordServiceClient {
	return &daycareAutoRolloverRecordServiceClient{cc}
}

func (c *daycareAutoRolloverRecordServiceClient) CreateDaycareAutoRolloverRecord(ctx context.Context, in *CreateDaycareAutoRolloverRecordRequest, opts ...grpc.CallOption) (*CreateDaycareAutoRolloverRecordResponse, error) {
	out := new(CreateDaycareAutoRolloverRecordResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DaycareAutoRolloverRecordService/CreateDaycareAutoRolloverRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *daycareAutoRolloverRecordServiceClient) BatchCreateDaycareAutoRolloverRecordByServiceId(ctx context.Context, in *BatchCreateDaycareAutoRolloverRecordByServiceIdRequest, opts ...grpc.CallOption) (*BatchCreateDaycareAutoRolloverRecordByServiceIdResponse, error) {
	out := new(BatchCreateDaycareAutoRolloverRecordByServiceIdResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DaycareAutoRolloverRecordService/BatchCreateDaycareAutoRolloverRecordByServiceId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *daycareAutoRolloverRecordServiceClient) BatchDeleteDaycareAutoRolloverRecordByServiceId(ctx context.Context, in *BatchDeleteDaycareAutoRolloverRecordByServiceIdRequest, opts ...grpc.CallOption) (*BatchDeleteDaycareAutoRolloverRecordByServiceIdResponse, error) {
	out := new(BatchDeleteDaycareAutoRolloverRecordByServiceIdResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DaycareAutoRolloverRecordService/BatchDeleteDaycareAutoRolloverRecordByServiceId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *daycareAutoRolloverRecordServiceClient) RefreshDaycareAutoRolloverRecordByServiceId(ctx context.Context, in *RefreshDaycareAutoRolloverRecordByServiceIdRequest, opts ...grpc.CallOption) (*RefreshDaycareAutoRolloverRecordByServiceIdResponse, error) {
	out := new(RefreshDaycareAutoRolloverRecordByServiceIdResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DaycareAutoRolloverRecordService/RefreshDaycareAutoRolloverRecordByServiceId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *daycareAutoRolloverRecordServiceClient) RolloverDaycareAutoRolloverRecord(ctx context.Context, in *RolloverDaycareAutoRolloverRecordRequest, opts ...grpc.CallOption) (*RolloverDaycareAutoRolloverRecordResponse, error) {
	out := new(RolloverDaycareAutoRolloverRecordResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.DaycareAutoRolloverRecordService/RolloverDaycareAutoRolloverRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DaycareAutoRolloverRecordServiceServer is the server API for DaycareAutoRolloverRecordService service.
// All implementations must embed UnimplementedDaycareAutoRolloverRecordServiceServer
// for forward compatibility
type DaycareAutoRolloverRecordServiceServer interface {
	// Create a record, return inserted record.
	CreateDaycareAutoRolloverRecord(context.Context, *CreateDaycareAutoRolloverRecordRequest) (*CreateDaycareAutoRolloverRecordResponse, error)
	// 给指定 service id 批量创建 rollover 记录
	BatchCreateDaycareAutoRolloverRecordByServiceId(context.Context, *BatchCreateDaycareAutoRolloverRecordByServiceIdRequest) (*BatchCreateDaycareAutoRolloverRecordByServiceIdResponse, error)
	// 删除指定 service id 的所有 rollover 记录
	BatchDeleteDaycareAutoRolloverRecordByServiceId(context.Context, *BatchDeleteDaycareAutoRolloverRecordByServiceIdRequest) (*BatchDeleteDaycareAutoRolloverRecordByServiceIdResponse, error)
	// 刷新指定 service id 的所有 rollover 记录的 rollover_time
	RefreshDaycareAutoRolloverRecordByServiceId(context.Context, *RefreshDaycareAutoRolloverRecordByServiceIdRequest) (*RefreshDaycareAutoRolloverRecordByServiceIdResponse, error)
	// Rollover DaycareAutoRolloverRecord.
	// Rollover 指定时间范围内的记录
	RolloverDaycareAutoRolloverRecord(context.Context, *RolloverDaycareAutoRolloverRecordRequest) (*RolloverDaycareAutoRolloverRecordResponse, error)
	mustEmbedUnimplementedDaycareAutoRolloverRecordServiceServer()
}

// UnimplementedDaycareAutoRolloverRecordServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDaycareAutoRolloverRecordServiceServer struct {
}

func (UnimplementedDaycareAutoRolloverRecordServiceServer) CreateDaycareAutoRolloverRecord(context.Context, *CreateDaycareAutoRolloverRecordRequest) (*CreateDaycareAutoRolloverRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDaycareAutoRolloverRecord not implemented")
}
func (UnimplementedDaycareAutoRolloverRecordServiceServer) BatchCreateDaycareAutoRolloverRecordByServiceId(context.Context, *BatchCreateDaycareAutoRolloverRecordByServiceIdRequest) (*BatchCreateDaycareAutoRolloverRecordByServiceIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCreateDaycareAutoRolloverRecordByServiceId not implemented")
}
func (UnimplementedDaycareAutoRolloverRecordServiceServer) BatchDeleteDaycareAutoRolloverRecordByServiceId(context.Context, *BatchDeleteDaycareAutoRolloverRecordByServiceIdRequest) (*BatchDeleteDaycareAutoRolloverRecordByServiceIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchDeleteDaycareAutoRolloverRecordByServiceId not implemented")
}
func (UnimplementedDaycareAutoRolloverRecordServiceServer) RefreshDaycareAutoRolloverRecordByServiceId(context.Context, *RefreshDaycareAutoRolloverRecordByServiceIdRequest) (*RefreshDaycareAutoRolloverRecordByServiceIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshDaycareAutoRolloverRecordByServiceId not implemented")
}
func (UnimplementedDaycareAutoRolloverRecordServiceServer) RolloverDaycareAutoRolloverRecord(context.Context, *RolloverDaycareAutoRolloverRecordRequest) (*RolloverDaycareAutoRolloverRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RolloverDaycareAutoRolloverRecord not implemented")
}
func (UnimplementedDaycareAutoRolloverRecordServiceServer) mustEmbedUnimplementedDaycareAutoRolloverRecordServiceServer() {
}

// UnsafeDaycareAutoRolloverRecordServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DaycareAutoRolloverRecordServiceServer will
// result in compilation errors.
type UnsafeDaycareAutoRolloverRecordServiceServer interface {
	mustEmbedUnimplementedDaycareAutoRolloverRecordServiceServer()
}

func RegisterDaycareAutoRolloverRecordServiceServer(s grpc.ServiceRegistrar, srv DaycareAutoRolloverRecordServiceServer) {
	s.RegisterService(&DaycareAutoRolloverRecordService_ServiceDesc, srv)
}

func _DaycareAutoRolloverRecordService_CreateDaycareAutoRolloverRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDaycareAutoRolloverRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DaycareAutoRolloverRecordServiceServer).CreateDaycareAutoRolloverRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DaycareAutoRolloverRecordService/CreateDaycareAutoRolloverRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DaycareAutoRolloverRecordServiceServer).CreateDaycareAutoRolloverRecord(ctx, req.(*CreateDaycareAutoRolloverRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DaycareAutoRolloverRecordService_BatchCreateDaycareAutoRolloverRecordByServiceId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCreateDaycareAutoRolloverRecordByServiceIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DaycareAutoRolloverRecordServiceServer).BatchCreateDaycareAutoRolloverRecordByServiceId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DaycareAutoRolloverRecordService/BatchCreateDaycareAutoRolloverRecordByServiceId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DaycareAutoRolloverRecordServiceServer).BatchCreateDaycareAutoRolloverRecordByServiceId(ctx, req.(*BatchCreateDaycareAutoRolloverRecordByServiceIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DaycareAutoRolloverRecordService_BatchDeleteDaycareAutoRolloverRecordByServiceId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteDaycareAutoRolloverRecordByServiceIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DaycareAutoRolloverRecordServiceServer).BatchDeleteDaycareAutoRolloverRecordByServiceId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DaycareAutoRolloverRecordService/BatchDeleteDaycareAutoRolloverRecordByServiceId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DaycareAutoRolloverRecordServiceServer).BatchDeleteDaycareAutoRolloverRecordByServiceId(ctx, req.(*BatchDeleteDaycareAutoRolloverRecordByServiceIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DaycareAutoRolloverRecordService_RefreshDaycareAutoRolloverRecordByServiceId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshDaycareAutoRolloverRecordByServiceIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DaycareAutoRolloverRecordServiceServer).RefreshDaycareAutoRolloverRecordByServiceId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DaycareAutoRolloverRecordService/RefreshDaycareAutoRolloverRecordByServiceId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DaycareAutoRolloverRecordServiceServer).RefreshDaycareAutoRolloverRecordByServiceId(ctx, req.(*RefreshDaycareAutoRolloverRecordByServiceIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DaycareAutoRolloverRecordService_RolloverDaycareAutoRolloverRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RolloverDaycareAutoRolloverRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DaycareAutoRolloverRecordServiceServer).RolloverDaycareAutoRolloverRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.DaycareAutoRolloverRecordService/RolloverDaycareAutoRolloverRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DaycareAutoRolloverRecordServiceServer).RolloverDaycareAutoRolloverRecord(ctx, req.(*RolloverDaycareAutoRolloverRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DaycareAutoRolloverRecordService_ServiceDesc is the grpc.ServiceDesc for DaycareAutoRolloverRecordService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DaycareAutoRolloverRecordService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.appointment.v1.DaycareAutoRolloverRecordService",
	HandlerType: (*DaycareAutoRolloverRecordServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDaycareAutoRolloverRecord",
			Handler:    _DaycareAutoRolloverRecordService_CreateDaycareAutoRolloverRecord_Handler,
		},
		{
			MethodName: "BatchCreateDaycareAutoRolloverRecordByServiceId",
			Handler:    _DaycareAutoRolloverRecordService_BatchCreateDaycareAutoRolloverRecordByServiceId_Handler,
		},
		{
			MethodName: "BatchDeleteDaycareAutoRolloverRecordByServiceId",
			Handler:    _DaycareAutoRolloverRecordService_BatchDeleteDaycareAutoRolloverRecordByServiceId_Handler,
		},
		{
			MethodName: "RefreshDaycareAutoRolloverRecordByServiceId",
			Handler:    _DaycareAutoRolloverRecordService_RefreshDaycareAutoRolloverRecordByServiceId_Handler,
		},
		{
			MethodName: "RolloverDaycareAutoRolloverRecord",
			Handler:    _DaycareAutoRolloverRecordService_RolloverDaycareAutoRolloverRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/appointment/v1/daycare_auto_rollover_record_service.proto",
}
