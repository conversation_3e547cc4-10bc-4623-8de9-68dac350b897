// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/membership/v1/data_migration_service.proto

package membershipsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// DataMigrationServiceClient is the client API for DataMigrationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DataMigrationServiceClient interface {
	// import memberships
	ImportMemberships(ctx context.Context, in *ImportMembershipsRequest, opts ...grpc.CallOption) (*ImportMembershipsResponse, error)
	// import subscriptions
	ImportSubscriptions(ctx context.Context, in *ImportSubscriptionsRequest, opts ...grpc.CallOption) (*ImportSubscriptionsResponse, error)
}

type dataMigrationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDataMigrationServiceClient(cc grpc.ClientConnInterface) DataMigrationServiceClient {
	return &dataMigrationServiceClient{cc}
}

func (c *dataMigrationServiceClient) ImportMemberships(ctx context.Context, in *ImportMembershipsRequest, opts ...grpc.CallOption) (*ImportMembershipsResponse, error) {
	out := new(ImportMembershipsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.DataMigrationService/ImportMemberships", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataMigrationServiceClient) ImportSubscriptions(ctx context.Context, in *ImportSubscriptionsRequest, opts ...grpc.CallOption) (*ImportSubscriptionsResponse, error) {
	out := new(ImportSubscriptionsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.membership.v1.DataMigrationService/ImportSubscriptions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DataMigrationServiceServer is the server API for DataMigrationService service.
// All implementations must embed UnimplementedDataMigrationServiceServer
// for forward compatibility
type DataMigrationServiceServer interface {
	// import memberships
	ImportMemberships(context.Context, *ImportMembershipsRequest) (*ImportMembershipsResponse, error)
	// import subscriptions
	ImportSubscriptions(context.Context, *ImportSubscriptionsRequest) (*ImportSubscriptionsResponse, error)
	mustEmbedUnimplementedDataMigrationServiceServer()
}

// UnimplementedDataMigrationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDataMigrationServiceServer struct {
}

func (UnimplementedDataMigrationServiceServer) ImportMemberships(context.Context, *ImportMembershipsRequest) (*ImportMembershipsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportMemberships not implemented")
}
func (UnimplementedDataMigrationServiceServer) ImportSubscriptions(context.Context, *ImportSubscriptionsRequest) (*ImportSubscriptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportSubscriptions not implemented")
}
func (UnimplementedDataMigrationServiceServer) mustEmbedUnimplementedDataMigrationServiceServer() {}

// UnsafeDataMigrationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DataMigrationServiceServer will
// result in compilation errors.
type UnsafeDataMigrationServiceServer interface {
	mustEmbedUnimplementedDataMigrationServiceServer()
}

func RegisterDataMigrationServiceServer(s grpc.ServiceRegistrar, srv DataMigrationServiceServer) {
	s.RegisterService(&DataMigrationService_ServiceDesc, srv)
}

func _DataMigrationService_ImportMemberships_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportMembershipsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataMigrationServiceServer).ImportMemberships(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.DataMigrationService/ImportMemberships",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataMigrationServiceServer).ImportMemberships(ctx, req.(*ImportMembershipsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataMigrationService_ImportSubscriptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportSubscriptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataMigrationServiceServer).ImportSubscriptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.membership.v1.DataMigrationService/ImportSubscriptions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataMigrationServiceServer).ImportSubscriptions(ctx, req.(*ImportSubscriptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DataMigrationService_ServiceDesc is the grpc.ServiceDesc for DataMigrationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DataMigrationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.membership.v1.DataMigrationService",
	HandlerType: (*DataMigrationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ImportMemberships",
			Handler:    _DataMigrationService_ImportMemberships_Handler,
		},
		{
			MethodName: "ImportSubscriptions",
			Handler:    _DataMigrationService_ImportSubscriptions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/membership/v1/data_migration_service.proto",
}
