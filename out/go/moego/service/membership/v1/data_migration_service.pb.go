// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/membership/v1/data_migration_service.proto

package membershipsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// import memberships request
type ImportMembershipsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// membership data
	Data []*v1.MembershipData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ImportMembershipsRequest) Reset() {
	*x = ImportMembershipsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportMembershipsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportMembershipsRequest) ProtoMessage() {}

func (x *ImportMembershipsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportMembershipsRequest.ProtoReflect.Descriptor instead.
func (*ImportMembershipsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_membership_v1_data_migration_service_proto_rawDescGZIP(), []int{0}
}

func (x *ImportMembershipsRequest) GetData() []*v1.MembershipData {
	if x != nil {
		return x.Data
	}
	return nil
}

// import memberships response
type ImportMembershipsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// successfully imported membership data
	Imported []*v1.MembershipData `protobuf:"bytes,1,rep,name=imported,proto3" json:"imported,omitempty"`
	// failed to import membership data
	Failed []*v1.MembershipData `protobuf:"bytes,2,rep,name=failed,proto3" json:"failed,omitempty"`
}

func (x *ImportMembershipsResponse) Reset() {
	*x = ImportMembershipsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportMembershipsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportMembershipsResponse) ProtoMessage() {}

func (x *ImportMembershipsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportMembershipsResponse.ProtoReflect.Descriptor instead.
func (*ImportMembershipsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_membership_v1_data_migration_service_proto_rawDescGZIP(), []int{1}
}

func (x *ImportMembershipsResponse) GetImported() []*v1.MembershipData {
	if x != nil {
		return x.Imported
	}
	return nil
}

func (x *ImportMembershipsResponse) GetFailed() []*v1.MembershipData {
	if x != nil {
		return x.Failed
	}
	return nil
}

// import discount benefits request
type ImportDiscountBenefitsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount benefit data
	Data []*v1.DiscountBenefitData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ImportDiscountBenefitsRequest) Reset() {
	*x = ImportDiscountBenefitsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportDiscountBenefitsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportDiscountBenefitsRequest) ProtoMessage() {}

func (x *ImportDiscountBenefitsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportDiscountBenefitsRequest.ProtoReflect.Descriptor instead.
func (*ImportDiscountBenefitsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_membership_v1_data_migration_service_proto_rawDescGZIP(), []int{2}
}

func (x *ImportDiscountBenefitsRequest) GetData() []*v1.DiscountBenefitData {
	if x != nil {
		return x.Data
	}
	return nil
}

// import discount benefits response
type ImportDiscountBenefitsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// successfully imported discount benefit data
	Imported []*v1.DiscountBenefitData `protobuf:"bytes,1,rep,name=imported,proto3" json:"imported,omitempty"`
	// failed to import discount benefit data
	Failed []*v1.DiscountBenefitData `protobuf:"bytes,2,rep,name=failed,proto3" json:"failed,omitempty"`
}

func (x *ImportDiscountBenefitsResponse) Reset() {
	*x = ImportDiscountBenefitsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportDiscountBenefitsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportDiscountBenefitsResponse) ProtoMessage() {}

func (x *ImportDiscountBenefitsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportDiscountBenefitsResponse.ProtoReflect.Descriptor instead.
func (*ImportDiscountBenefitsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_membership_v1_data_migration_service_proto_rawDescGZIP(), []int{3}
}

func (x *ImportDiscountBenefitsResponse) GetImported() []*v1.DiscountBenefitData {
	if x != nil {
		return x.Imported
	}
	return nil
}

func (x *ImportDiscountBenefitsResponse) GetFailed() []*v1.DiscountBenefitData {
	if x != nil {
		return x.Failed
	}
	return nil
}

// import quantity benefits request
type ImportQuantityBenefitsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// quantity benefit data
	Data []*v1.QuantityBenefitData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ImportQuantityBenefitsRequest) Reset() {
	*x = ImportQuantityBenefitsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportQuantityBenefitsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportQuantityBenefitsRequest) ProtoMessage() {}

func (x *ImportQuantityBenefitsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportQuantityBenefitsRequest.ProtoReflect.Descriptor instead.
func (*ImportQuantityBenefitsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_membership_v1_data_migration_service_proto_rawDescGZIP(), []int{4}
}

func (x *ImportQuantityBenefitsRequest) GetData() []*v1.QuantityBenefitData {
	if x != nil {
		return x.Data
	}
	return nil
}

// import quantity benefits response
type ImportQuantityBenefitsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// successfully imported quantity benefit data
	Imported []*v1.QuantityBenefitData `protobuf:"bytes,1,rep,name=imported,proto3" json:"imported,omitempty"`
	// failed to import quantity benefit data
	Failed []*v1.QuantityBenefitData `protobuf:"bytes,2,rep,name=failed,proto3" json:"failed,omitempty"`
}

func (x *ImportQuantityBenefitsResponse) Reset() {
	*x = ImportQuantityBenefitsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportQuantityBenefitsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportQuantityBenefitsResponse) ProtoMessage() {}

func (x *ImportQuantityBenefitsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportQuantityBenefitsResponse.ProtoReflect.Descriptor instead.
func (*ImportQuantityBenefitsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_membership_v1_data_migration_service_proto_rawDescGZIP(), []int{5}
}

func (x *ImportQuantityBenefitsResponse) GetImported() []*v1.QuantityBenefitData {
	if x != nil {
		return x.Imported
	}
	return nil
}

func (x *ImportQuantityBenefitsResponse) GetFailed() []*v1.QuantityBenefitData {
	if x != nil {
		return x.Failed
	}
	return nil
}

// import subscriptions request
type ImportSubscriptionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription data
	Data []*v1.SubscriptionData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ImportSubscriptionsRequest) Reset() {
	*x = ImportSubscriptionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportSubscriptionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportSubscriptionsRequest) ProtoMessage() {}

func (x *ImportSubscriptionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportSubscriptionsRequest.ProtoReflect.Descriptor instead.
func (*ImportSubscriptionsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_membership_v1_data_migration_service_proto_rawDescGZIP(), []int{6}
}

func (x *ImportSubscriptionsRequest) GetData() []*v1.SubscriptionData {
	if x != nil {
		return x.Data
	}
	return nil
}

// import subscriptions response
type ImportSubscriptionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// successfully imported subscription data
	Imported []*v1.SubscriptionData `protobuf:"bytes,1,rep,name=imported,proto3" json:"imported,omitempty"`
	// failed to import subscription data
	Failed []*v1.SubscriptionData `protobuf:"bytes,2,rep,name=failed,proto3" json:"failed,omitempty"`
}

func (x *ImportSubscriptionsResponse) Reset() {
	*x = ImportSubscriptionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportSubscriptionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportSubscriptionsResponse) ProtoMessage() {}

func (x *ImportSubscriptionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportSubscriptionsResponse.ProtoReflect.Descriptor instead.
func (*ImportSubscriptionsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_membership_v1_data_migration_service_proto_rawDescGZIP(), []int{7}
}

func (x *ImportSubscriptionsResponse) GetImported() []*v1.SubscriptionData {
	if x != nil {
		return x.Imported
	}
	return nil
}

func (x *ImportSubscriptionsResponse) GetFailed() []*v1.SubscriptionData {
	if x != nil {
		return x.Failed
	}
	return nil
}

// import quantity entitlements request
type ImportQuantityEntitlementsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// quantity entitlement data
	Data []*v1.QuantityEntitlementData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *ImportQuantityEntitlementsRequest) Reset() {
	*x = ImportQuantityEntitlementsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportQuantityEntitlementsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportQuantityEntitlementsRequest) ProtoMessage() {}

func (x *ImportQuantityEntitlementsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportQuantityEntitlementsRequest.ProtoReflect.Descriptor instead.
func (*ImportQuantityEntitlementsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_membership_v1_data_migration_service_proto_rawDescGZIP(), []int{8}
}

func (x *ImportQuantityEntitlementsRequest) GetData() []*v1.QuantityEntitlementData {
	if x != nil {
		return x.Data
	}
	return nil
}

// import quantity entitlements response
type ImportQuantityEntitlementsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// successfully imported quantity entitlement data
	Imported []*v1.QuantityEntitlementData `protobuf:"bytes,1,rep,name=imported,proto3" json:"imported,omitempty"`
	// failed to import quantity entitlement data
	Failed []*v1.QuantityEntitlementData `protobuf:"bytes,2,rep,name=failed,proto3" json:"failed,omitempty"`
}

func (x *ImportQuantityEntitlementsResponse) Reset() {
	*x = ImportQuantityEntitlementsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportQuantityEntitlementsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportQuantityEntitlementsResponse) ProtoMessage() {}

func (x *ImportQuantityEntitlementsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_membership_v1_data_migration_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportQuantityEntitlementsResponse.ProtoReflect.Descriptor instead.
func (*ImportQuantityEntitlementsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_membership_v1_data_migration_service_proto_rawDescGZIP(), []int{9}
}

func (x *ImportQuantityEntitlementsResponse) GetImported() []*v1.QuantityEntitlementData {
	if x != nil {
		return x.Imported
	}
	return nil
}

func (x *ImportQuantityEntitlementsResponse) GetFailed() []*v1.QuantityEntitlementData {
	if x != nil {
		return x.Failed
	}
	return nil
}

var File_moego_service_membership_v1_data_migration_service_proto protoreflect.FileDescriptor

var file_moego_service_membership_v1_data_migration_service_proto_rawDesc = []byte{
	0x0a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x5a, 0x0a, 0x18, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3e, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa7, 0x01, 0x0a, 0x19,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x08, 0x69, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x64, 0x12, 0x42, 0x0a, 0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x66,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x22, 0x64, 0x0a, 0x1d, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xb6, 0x01, 0x0a, 0x1e,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65,
	0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b,
	0x0a, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x12, 0x47, 0x0a, 0x06, 0x66,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x66, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x22, 0x64, 0x0a, 0x1d, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x51, 0x75,
	0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xb6, 0x01, 0x0a, 0x1e, 0x49,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a,
	0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x61,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x12, 0x47, 0x0a, 0x06, 0x66, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x22, 0x5e, 0x0a, 0x1a, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x40, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x22, 0xad, 0x01, 0x0a, 0x1b, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x12, 0x44, 0x0a,
	0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x22, 0x6c, 0x0a, 0x21, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x51, 0x75, 0x61,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x47, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0xc2, 0x01, 0x0a, 0x22, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x51, 0x75, 0x61, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x08, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x12, 0x4b, 0x0a, 0x06, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x32, 0xee, 0x05, 0x0a, 0x14, 0x44, 0x61, 0x74, 0x61, 0x4d,
	0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x82, 0x01, 0x0a, 0x11, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x16, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12,
	0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x16, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x74, 0x73, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x88, 0x01, 0x0a,
	0x13, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9d, 0x01, 0x0a, 0x1a, 0x49, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x51, 0x75, 0x61, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x51, 0x75, 0x61, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x89, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x73, 0x76,
	0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_membership_v1_data_migration_service_proto_rawDescOnce sync.Once
	file_moego_service_membership_v1_data_migration_service_proto_rawDescData = file_moego_service_membership_v1_data_migration_service_proto_rawDesc
)

func file_moego_service_membership_v1_data_migration_service_proto_rawDescGZIP() []byte {
	file_moego_service_membership_v1_data_migration_service_proto_rawDescOnce.Do(func() {
		file_moego_service_membership_v1_data_migration_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_membership_v1_data_migration_service_proto_rawDescData)
	})
	return file_moego_service_membership_v1_data_migration_service_proto_rawDescData
}

var file_moego_service_membership_v1_data_migration_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_service_membership_v1_data_migration_service_proto_goTypes = []interface{}{
	(*ImportMembershipsRequest)(nil),           // 0: moego.service.membership.v1.ImportMembershipsRequest
	(*ImportMembershipsResponse)(nil),          // 1: moego.service.membership.v1.ImportMembershipsResponse
	(*ImportDiscountBenefitsRequest)(nil),      // 2: moego.service.membership.v1.ImportDiscountBenefitsRequest
	(*ImportDiscountBenefitsResponse)(nil),     // 3: moego.service.membership.v1.ImportDiscountBenefitsResponse
	(*ImportQuantityBenefitsRequest)(nil),      // 4: moego.service.membership.v1.ImportQuantityBenefitsRequest
	(*ImportQuantityBenefitsResponse)(nil),     // 5: moego.service.membership.v1.ImportQuantityBenefitsResponse
	(*ImportSubscriptionsRequest)(nil),         // 6: moego.service.membership.v1.ImportSubscriptionsRequest
	(*ImportSubscriptionsResponse)(nil),        // 7: moego.service.membership.v1.ImportSubscriptionsResponse
	(*ImportQuantityEntitlementsRequest)(nil),  // 8: moego.service.membership.v1.ImportQuantityEntitlementsRequest
	(*ImportQuantityEntitlementsResponse)(nil), // 9: moego.service.membership.v1.ImportQuantityEntitlementsResponse
	(*v1.MembershipData)(nil),                  // 10: moego.models.membership.v1.MembershipData
	(*v1.DiscountBenefitData)(nil),             // 11: moego.models.membership.v1.DiscountBenefitData
	(*v1.QuantityBenefitData)(nil),             // 12: moego.models.membership.v1.QuantityBenefitData
	(*v1.SubscriptionData)(nil),                // 13: moego.models.membership.v1.SubscriptionData
	(*v1.QuantityEntitlementData)(nil),         // 14: moego.models.membership.v1.QuantityEntitlementData
}
var file_moego_service_membership_v1_data_migration_service_proto_depIdxs = []int32{
	10, // 0: moego.service.membership.v1.ImportMembershipsRequest.data:type_name -> moego.models.membership.v1.MembershipData
	10, // 1: moego.service.membership.v1.ImportMembershipsResponse.imported:type_name -> moego.models.membership.v1.MembershipData
	10, // 2: moego.service.membership.v1.ImportMembershipsResponse.failed:type_name -> moego.models.membership.v1.MembershipData
	11, // 3: moego.service.membership.v1.ImportDiscountBenefitsRequest.data:type_name -> moego.models.membership.v1.DiscountBenefitData
	11, // 4: moego.service.membership.v1.ImportDiscountBenefitsResponse.imported:type_name -> moego.models.membership.v1.DiscountBenefitData
	11, // 5: moego.service.membership.v1.ImportDiscountBenefitsResponse.failed:type_name -> moego.models.membership.v1.DiscountBenefitData
	12, // 6: moego.service.membership.v1.ImportQuantityBenefitsRequest.data:type_name -> moego.models.membership.v1.QuantityBenefitData
	12, // 7: moego.service.membership.v1.ImportQuantityBenefitsResponse.imported:type_name -> moego.models.membership.v1.QuantityBenefitData
	12, // 8: moego.service.membership.v1.ImportQuantityBenefitsResponse.failed:type_name -> moego.models.membership.v1.QuantityBenefitData
	13, // 9: moego.service.membership.v1.ImportSubscriptionsRequest.data:type_name -> moego.models.membership.v1.SubscriptionData
	13, // 10: moego.service.membership.v1.ImportSubscriptionsResponse.imported:type_name -> moego.models.membership.v1.SubscriptionData
	13, // 11: moego.service.membership.v1.ImportSubscriptionsResponse.failed:type_name -> moego.models.membership.v1.SubscriptionData
	14, // 12: moego.service.membership.v1.ImportQuantityEntitlementsRequest.data:type_name -> moego.models.membership.v1.QuantityEntitlementData
	14, // 13: moego.service.membership.v1.ImportQuantityEntitlementsResponse.imported:type_name -> moego.models.membership.v1.QuantityEntitlementData
	14, // 14: moego.service.membership.v1.ImportQuantityEntitlementsResponse.failed:type_name -> moego.models.membership.v1.QuantityEntitlementData
	0,  // 15: moego.service.membership.v1.DataMigrationService.ImportMemberships:input_type -> moego.service.membership.v1.ImportMembershipsRequest
	2,  // 16: moego.service.membership.v1.DataMigrationService.ImportDiscountBenefits:input_type -> moego.service.membership.v1.ImportDiscountBenefitsRequest
	4,  // 17: moego.service.membership.v1.DataMigrationService.ImportQuantityBenefits:input_type -> moego.service.membership.v1.ImportQuantityBenefitsRequest
	6,  // 18: moego.service.membership.v1.DataMigrationService.ImportSubscriptions:input_type -> moego.service.membership.v1.ImportSubscriptionsRequest
	8,  // 19: moego.service.membership.v1.DataMigrationService.ImportQuantityEntitlements:input_type -> moego.service.membership.v1.ImportQuantityEntitlementsRequest
	1,  // 20: moego.service.membership.v1.DataMigrationService.ImportMemberships:output_type -> moego.service.membership.v1.ImportMembershipsResponse
	3,  // 21: moego.service.membership.v1.DataMigrationService.ImportDiscountBenefits:output_type -> moego.service.membership.v1.ImportDiscountBenefitsResponse
	5,  // 22: moego.service.membership.v1.DataMigrationService.ImportQuantityBenefits:output_type -> moego.service.membership.v1.ImportQuantityBenefitsResponse
	7,  // 23: moego.service.membership.v1.DataMigrationService.ImportSubscriptions:output_type -> moego.service.membership.v1.ImportSubscriptionsResponse
	9,  // 24: moego.service.membership.v1.DataMigrationService.ImportQuantityEntitlements:output_type -> moego.service.membership.v1.ImportQuantityEntitlementsResponse
	20, // [20:25] is the sub-list for method output_type
	15, // [15:20] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_moego_service_membership_v1_data_migration_service_proto_init() }
func file_moego_service_membership_v1_data_migration_service_proto_init() {
	if File_moego_service_membership_v1_data_migration_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_membership_v1_data_migration_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportMembershipsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_membership_v1_data_migration_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportMembershipsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_membership_v1_data_migration_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportDiscountBenefitsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_membership_v1_data_migration_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportDiscountBenefitsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_membership_v1_data_migration_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportQuantityBenefitsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_membership_v1_data_migration_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportQuantityBenefitsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_membership_v1_data_migration_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportSubscriptionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_membership_v1_data_migration_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportSubscriptionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_membership_v1_data_migration_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportQuantityEntitlementsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_membership_v1_data_migration_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportQuantityEntitlementsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_membership_v1_data_migration_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_membership_v1_data_migration_service_proto_goTypes,
		DependencyIndexes: file_moego_service_membership_v1_data_migration_service_proto_depIdxs,
		MessageInfos:      file_moego_service_membership_v1_data_migration_service_proto_msgTypes,
	}.Build()
	File_moego_service_membership_v1_data_migration_service_proto = out.File
	file_moego_service_membership_v1_data_migration_service_proto_rawDesc = nil
	file_moego_service_membership_v1_data_migration_service_proto_goTypes = nil
	file_moego_service_membership_v1_data_migration_service_proto_depIdxs = nil
}
