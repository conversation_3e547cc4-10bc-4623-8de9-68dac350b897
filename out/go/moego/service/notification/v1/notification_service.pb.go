// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/notification/v1/notification_service.proto

package notificationsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/notification/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create inbox notification request
type CreateInboxNotificationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// notification source
	Source v1.NotificationSource `protobuf:"varint,2,opt,name=source,proto3,enum=moego.models.notification.v1.NotificationSource" json:"source,omitempty"`
	// sender id
	SenderId int64 `protobuf:"varint,3,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
	// receiver id
	ReceiverId int64 `protobuf:"varint,4,opt,name=receiver_id,json=receiverId,proto3" json:"receiver_id,omitempty"`
	// title
	Title *string `protobuf:"bytes,5,opt,name=title,proto3,oneof" json:"title,omitempty"`
	// content
	Content *string `protobuf:"bytes,6,opt,name=content,proto3,oneof" json:"content,omitempty"`
	// notification method
	Method v1.NotificationMethod `protobuf:"varint,7,opt,name=method,proto3,enum=moego.models.notification.v1.NotificationMethod" json:"method,omitempty"`
	// notification type
	Type v1.NotificationType `protobuf:"varint,8,opt,name=type,proto3,enum=moego.models.notification.v1.NotificationType" json:"type,omitempty"`
	// extra info
	Extra *v1.NotificationExtraDef `protobuf:"bytes,9,opt,name=extra,proto3" json:"extra,omitempty"`
	// app push
	AppPush *v1.AppPushDef `protobuf:"bytes,10,opt,name=app_push,json=appPush,proto3,oneof" json:"app_push,omitempty"`
}

func (x *CreateInboxNotificationRequest) Reset() {
	*x = CreateInboxNotificationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_notification_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateInboxNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateInboxNotificationRequest) ProtoMessage() {}

func (x *CreateInboxNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_notification_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateInboxNotificationRequest.ProtoReflect.Descriptor instead.
func (*CreateInboxNotificationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_notification_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateInboxNotificationRequest) GetSource() v1.NotificationSource {
	if x != nil {
		return x.Source
	}
	return v1.NotificationSource(0)
}

func (x *CreateInboxNotificationRequest) GetSenderId() int64 {
	if x != nil {
		return x.SenderId
	}
	return 0
}

func (x *CreateInboxNotificationRequest) GetReceiverId() int64 {
	if x != nil {
		return x.ReceiverId
	}
	return 0
}

func (x *CreateInboxNotificationRequest) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *CreateInboxNotificationRequest) GetContent() string {
	if x != nil && x.Content != nil {
		return *x.Content
	}
	return ""
}

func (x *CreateInboxNotificationRequest) GetMethod() v1.NotificationMethod {
	if x != nil {
		return x.Method
	}
	return v1.NotificationMethod(0)
}

func (x *CreateInboxNotificationRequest) GetType() v1.NotificationType {
	if x != nil {
		return x.Type
	}
	return v1.NotificationType(0)
}

func (x *CreateInboxNotificationRequest) GetExtra() *v1.NotificationExtraDef {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *CreateInboxNotificationRequest) GetAppPush() *v1.AppPushDef {
	if x != nil {
		return x.AppPush
	}
	return nil
}

// create inbox notification response
type CreateInboxNotificationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// notification id
	NotificationId int64 `protobuf:"varint,1,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
}

func (x *CreateInboxNotificationResponse) Reset() {
	*x = CreateInboxNotificationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_notification_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateInboxNotificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateInboxNotificationResponse) ProtoMessage() {}

func (x *CreateInboxNotificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_notification_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateInboxNotificationResponse.ProtoReflect.Descriptor instead.
func (*CreateInboxNotificationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_notification_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateInboxNotificationResponse) GetNotificationId() int64 {
	if x != nil {
		return x.NotificationId
	}
	return 0
}

// read inbox notification request
type ReadInboxNotificationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// notification id
	NotificationId int64 `protobuf:"varint,1,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
}

func (x *ReadInboxNotificationRequest) Reset() {
	*x = ReadInboxNotificationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_notification_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadInboxNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadInboxNotificationRequest) ProtoMessage() {}

func (x *ReadInboxNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_notification_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadInboxNotificationRequest.ProtoReflect.Descriptor instead.
func (*ReadInboxNotificationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_notification_service_proto_rawDescGZIP(), []int{2}
}

func (x *ReadInboxNotificationRequest) GetNotificationId() int64 {
	if x != nil {
		return x.NotificationId
	}
	return 0
}

// read inbox notification response
type ReadInboxNotificationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// read time
	ReadTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=read_time,json=readTime,proto3" json:"read_time,omitempty"`
}

func (x *ReadInboxNotificationResponse) Reset() {
	*x = ReadInboxNotificationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_notification_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadInboxNotificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadInboxNotificationResponse) ProtoMessage() {}

func (x *ReadInboxNotificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_notification_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadInboxNotificationResponse.ProtoReflect.Descriptor instead.
func (*ReadInboxNotificationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_notification_service_proto_rawDescGZIP(), []int{3}
}

func (x *ReadInboxNotificationResponse) GetReadTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ReadTime
	}
	return nil
}

// delete inbox notification request
type DeleteInboxNotificationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// notification id
	NotificationId int64 `protobuf:"varint,1,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
}

func (x *DeleteInboxNotificationRequest) Reset() {
	*x = DeleteInboxNotificationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_notification_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteInboxNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteInboxNotificationRequest) ProtoMessage() {}

func (x *DeleteInboxNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_notification_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteInboxNotificationRequest.ProtoReflect.Descriptor instead.
func (*DeleteInboxNotificationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_notification_service_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteInboxNotificationRequest) GetNotificationId() int64 {
	if x != nil {
		return x.NotificationId
	}
	return 0
}

// delete inbox notification response
type DeleteInboxNotificationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// deleted time
	DeletedTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=deleted_time,json=deletedTime,proto3" json:"deleted_time,omitempty"`
}

func (x *DeleteInboxNotificationResponse) Reset() {
	*x = DeleteInboxNotificationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_notification_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteInboxNotificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteInboxNotificationResponse) ProtoMessage() {}

func (x *DeleteInboxNotificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_notification_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteInboxNotificationResponse.ProtoReflect.Descriptor instead.
func (*DeleteInboxNotificationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_notification_service_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteInboxNotificationResponse) GetDeletedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedTime
	}
	return nil
}

// get notification list request
type GetNotificationListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// notification type
	Types []v1.NotificationType `protobuf:"varint,1,rep,packed,name=types,proto3,enum=moego.models.notification.v1.NotificationType" json:"types,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// sort by
	Sorts []*v1.NotificationSortDef `protobuf:"bytes,3,rep,name=sorts,proto3" json:"sorts,omitempty"`
	// notification source
	Sources []v1.NotificationSource `protobuf:"varint,4,rep,packed,name=sources,proto3,enum=moego.models.notification.v1.NotificationSource" json:"sources,omitempty"`
	// notification method
	Methods []v1.NotificationMethod `protobuf:"varint,5,rep,packed,name=methods,proto3,enum=moego.models.notification.v1.NotificationMethod" json:"methods,omitempty"`
	// receiver id
	ReceiverId int64 `protobuf:"varint,6,opt,name=receiver_id,json=receiverId,proto3" json:"receiver_id,omitempty"`
}

func (x *GetNotificationListRequest) Reset() {
	*x = GetNotificationListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_notification_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNotificationListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationListRequest) ProtoMessage() {}

func (x *GetNotificationListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_notification_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationListRequest.ProtoReflect.Descriptor instead.
func (*GetNotificationListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_notification_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetNotificationListRequest) GetTypes() []v1.NotificationType {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *GetNotificationListRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetNotificationListRequest) GetSorts() []*v1.NotificationSortDef {
	if x != nil {
		return x.Sorts
	}
	return nil
}

func (x *GetNotificationListRequest) GetSources() []v1.NotificationSource {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *GetNotificationListRequest) GetMethods() []v1.NotificationMethod {
	if x != nil {
		return x.Methods
	}
	return nil
}

func (x *GetNotificationListRequest) GetReceiverId() int64 {
	if x != nil {
		return x.ReceiverId
	}
	return 0
}

// get notification list response
type GetNotificationListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// notification list
	Notifications []*v1.NotificationModel `protobuf:"bytes,1,rep,name=notifications,proto3" json:"notifications,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetNotificationListResponse) Reset() {
	*x = GetNotificationListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_notification_v1_notification_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNotificationListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationListResponse) ProtoMessage() {}

func (x *GetNotificationListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_notification_v1_notification_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationListResponse.ProtoReflect.Descriptor instead.
func (*GetNotificationListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_notification_v1_notification_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetNotificationListResponse) GetNotifications() []*v1.NotificationModel {
	if x != nil {
		return x.Notifications
	}
	return nil
}

func (x *GetNotificationListResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

var File_moego_service_notification_v1_notification_service_proto protoreflect.FileDescriptor

var file_moego_service_notification_v1_notification_service_proto_rawDesc = []byte{
	0x0a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe5,
	0x04, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x62, 0x6f, 0x78, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x52, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x18, 0x80, 0x80, 0x40, 0x48,
	0x01, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x52, 0x0a,
	0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x12, 0x4c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x48, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x44,
	0x65, 0x66, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x48, 0x0a, 0x08, 0x61, 0x70, 0x70,
	0x5f, 0x70, 0x75, 0x73, 0x68, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x50, 0x75,
	0x73, 0x68, 0x44, 0x65, 0x66, 0x48, 0x02, 0x52, 0x07, 0x61, 0x70, 0x70, 0x50, 0x75, 0x73, 0x68,
	0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x61, 0x70,
	0x70, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x22, 0x53, 0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x49, 0x6e, 0x62, 0x6f, 0x78, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x0f, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x50, 0x0a, 0x1c, 0x52,
	0x65, 0x61, 0x64, 0x49, 0x6e, 0x62, 0x6f, 0x78, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x0f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x58, 0x0a,
	0x1d, 0x52, 0x65, 0x61, 0x64, 0x49, 0x6e, 0x62, 0x6f, 0x78, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37,
	0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x72,
	0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x52, 0x0a, 0x1e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x49, 0x6e, 0x62, 0x6f, 0x78, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x0f, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x60, 0x0a, 0x1f, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x62, 0x6f, 0x78, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d,
	0x0a, 0x0c, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0b, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xfd, 0x03,
	0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x57, 0x0a, 0x05,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x11, 0xfa, 0x42, 0x0e,
	0x92, 0x01, 0x0b, 0x08, 0x00, 0x22, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x28, 0x01, 0x52, 0x05,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x47, 0x0a,
	0x05, 0x73, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x66, 0x52,
	0x05, 0x73, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x5d, 0x0a, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01,
	0x0b, 0x08, 0x00, 0x22, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x28, 0x01, 0x52, 0x07, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x5d, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b,
	0x08, 0x00, 0x22, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x28, 0x01, 0x52, 0x07, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x49, 0x64, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb8, 0x01,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x55, 0x0a,
	0x0d, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0xef, 0x04, 0x0a, 0x13, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x98, 0x01, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x62, 0x6f, 0x78,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x62, 0x6f, 0x78, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x49, 0x6e, 0x62, 0x6f, 0x78, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92, 0x01, 0x0a, 0x15,
	0x52, 0x65, 0x61, 0x64, 0x49, 0x6e, 0x62, 0x6f, 0x78, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x49, 0x6e, 0x62, 0x6f, 0x78, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x49, 0x6e, 0x62, 0x6f, 0x78, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x98, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x62, 0x6f, 0x78,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x49, 0x6e, 0x62, 0x6f, 0x78, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x49, 0x6e, 0x62, 0x6f, 0x78, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c, 0x01, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8f, 0x01, 0x0a, 0x25, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x64, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_notification_v1_notification_service_proto_rawDescOnce sync.Once
	file_moego_service_notification_v1_notification_service_proto_rawDescData = file_moego_service_notification_v1_notification_service_proto_rawDesc
)

func file_moego_service_notification_v1_notification_service_proto_rawDescGZIP() []byte {
	file_moego_service_notification_v1_notification_service_proto_rawDescOnce.Do(func() {
		file_moego_service_notification_v1_notification_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_notification_v1_notification_service_proto_rawDescData)
	})
	return file_moego_service_notification_v1_notification_service_proto_rawDescData
}

var file_moego_service_notification_v1_notification_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_service_notification_v1_notification_service_proto_goTypes = []interface{}{
	(*CreateInboxNotificationRequest)(nil),  // 0: moego.service.notification.v1.CreateInboxNotificationRequest
	(*CreateInboxNotificationResponse)(nil), // 1: moego.service.notification.v1.CreateInboxNotificationResponse
	(*ReadInboxNotificationRequest)(nil),    // 2: moego.service.notification.v1.ReadInboxNotificationRequest
	(*ReadInboxNotificationResponse)(nil),   // 3: moego.service.notification.v1.ReadInboxNotificationResponse
	(*DeleteInboxNotificationRequest)(nil),  // 4: moego.service.notification.v1.DeleteInboxNotificationRequest
	(*DeleteInboxNotificationResponse)(nil), // 5: moego.service.notification.v1.DeleteInboxNotificationResponse
	(*GetNotificationListRequest)(nil),      // 6: moego.service.notification.v1.GetNotificationListRequest
	(*GetNotificationListResponse)(nil),     // 7: moego.service.notification.v1.GetNotificationListResponse
	(v1.NotificationSource)(0),              // 8: moego.models.notification.v1.NotificationSource
	(v1.NotificationMethod)(0),              // 9: moego.models.notification.v1.NotificationMethod
	(v1.NotificationType)(0),                // 10: moego.models.notification.v1.NotificationType
	(*v1.NotificationExtraDef)(nil),         // 11: moego.models.notification.v1.NotificationExtraDef
	(*v1.AppPushDef)(nil),                   // 12: moego.models.notification.v1.AppPushDef
	(*timestamppb.Timestamp)(nil),           // 13: google.protobuf.Timestamp
	(*v2.PaginationRequest)(nil),            // 14: moego.utils.v2.PaginationRequest
	(*v1.NotificationSortDef)(nil),          // 15: moego.models.notification.v1.NotificationSortDef
	(*v1.NotificationModel)(nil),            // 16: moego.models.notification.v1.NotificationModel
	(*v2.PaginationResponse)(nil),           // 17: moego.utils.v2.PaginationResponse
}
var file_moego_service_notification_v1_notification_service_proto_depIdxs = []int32{
	8,  // 0: moego.service.notification.v1.CreateInboxNotificationRequest.source:type_name -> moego.models.notification.v1.NotificationSource
	9,  // 1: moego.service.notification.v1.CreateInboxNotificationRequest.method:type_name -> moego.models.notification.v1.NotificationMethod
	10, // 2: moego.service.notification.v1.CreateInboxNotificationRequest.type:type_name -> moego.models.notification.v1.NotificationType
	11, // 3: moego.service.notification.v1.CreateInboxNotificationRequest.extra:type_name -> moego.models.notification.v1.NotificationExtraDef
	12, // 4: moego.service.notification.v1.CreateInboxNotificationRequest.app_push:type_name -> moego.models.notification.v1.AppPushDef
	13, // 5: moego.service.notification.v1.ReadInboxNotificationResponse.read_time:type_name -> google.protobuf.Timestamp
	13, // 6: moego.service.notification.v1.DeleteInboxNotificationResponse.deleted_time:type_name -> google.protobuf.Timestamp
	10, // 7: moego.service.notification.v1.GetNotificationListRequest.types:type_name -> moego.models.notification.v1.NotificationType
	14, // 8: moego.service.notification.v1.GetNotificationListRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	15, // 9: moego.service.notification.v1.GetNotificationListRequest.sorts:type_name -> moego.models.notification.v1.NotificationSortDef
	8,  // 10: moego.service.notification.v1.GetNotificationListRequest.sources:type_name -> moego.models.notification.v1.NotificationSource
	9,  // 11: moego.service.notification.v1.GetNotificationListRequest.methods:type_name -> moego.models.notification.v1.NotificationMethod
	16, // 12: moego.service.notification.v1.GetNotificationListResponse.notifications:type_name -> moego.models.notification.v1.NotificationModel
	17, // 13: moego.service.notification.v1.GetNotificationListResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	0,  // 14: moego.service.notification.v1.NotificationService.CreateInboxNotification:input_type -> moego.service.notification.v1.CreateInboxNotificationRequest
	2,  // 15: moego.service.notification.v1.NotificationService.ReadInboxNotification:input_type -> moego.service.notification.v1.ReadInboxNotificationRequest
	4,  // 16: moego.service.notification.v1.NotificationService.DeleteInboxNotification:input_type -> moego.service.notification.v1.DeleteInboxNotificationRequest
	6,  // 17: moego.service.notification.v1.NotificationService.GetNotificationList:input_type -> moego.service.notification.v1.GetNotificationListRequest
	1,  // 18: moego.service.notification.v1.NotificationService.CreateInboxNotification:output_type -> moego.service.notification.v1.CreateInboxNotificationResponse
	3,  // 19: moego.service.notification.v1.NotificationService.ReadInboxNotification:output_type -> moego.service.notification.v1.ReadInboxNotificationResponse
	5,  // 20: moego.service.notification.v1.NotificationService.DeleteInboxNotification:output_type -> moego.service.notification.v1.DeleteInboxNotificationResponse
	7,  // 21: moego.service.notification.v1.NotificationService.GetNotificationList:output_type -> moego.service.notification.v1.GetNotificationListResponse
	18, // [18:22] is the sub-list for method output_type
	14, // [14:18] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_moego_service_notification_v1_notification_service_proto_init() }
func file_moego_service_notification_v1_notification_service_proto_init() {
	if File_moego_service_notification_v1_notification_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_notification_v1_notification_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateInboxNotificationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_notification_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateInboxNotificationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_notification_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadInboxNotificationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_notification_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadInboxNotificationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_notification_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteInboxNotificationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_notification_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteInboxNotificationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_notification_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNotificationListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_notification_v1_notification_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNotificationListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_notification_v1_notification_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_notification_v1_notification_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_notification_v1_notification_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_notification_v1_notification_service_proto_goTypes,
		DependencyIndexes: file_moego_service_notification_v1_notification_service_proto_depIdxs,
		MessageInfos:      file_moego_service_notification_v1_notification_service_proto_msgTypes,
	}.Build()
	File_moego_service_notification_v1_notification_service_proto = out.File
	file_moego_service_notification_v1_notification_service_proto_rawDesc = nil
	file_moego_service_notification_v1_notification_service_proto_goTypes = nil
	file_moego_service_notification_v1_notification_service_proto_depIdxs = nil
}
