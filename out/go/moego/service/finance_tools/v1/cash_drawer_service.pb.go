// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/finance_tools/v1/cash_drawer_service.proto

package financetoolssvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/finance_tools/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	interval "google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request for ListReports
type ListReportsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the business
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListReportsRequest) Reset() {
	*x = ListReportsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListReportsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReportsRequest) ProtoMessage() {}

func (x *ListReportsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReportsRequest.ProtoReflect.Descriptor instead.
func (*ListReportsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListReportsRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListReportsRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// Response for ListReports
type ListReportsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// Reports
	Reports []*v1.CashDrawerReport `protobuf:"bytes,2,rep,name=reports,proto3" json:"reports,omitempty"`
}

func (x *ListReportsResponse) Reset() {
	*x = ListReportsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListReportsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReportsResponse) ProtoMessage() {}

func (x *ListReportsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReportsResponse.ProtoReflect.Descriptor instead.
func (*ListReportsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListReportsResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListReportsResponse) GetReports() []*v1.CashDrawerReport {
	if x != nil {
		return x.Reports
	}
	return nil
}

// Request for GetLastReport
type GetLastReportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the business
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetLastReportRequest) Reset() {
	*x = GetLastReportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLastReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLastReportRequest) ProtoMessage() {}

func (x *GetLastReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLastReportRequest.ProtoReflect.Descriptor instead.
func (*GetLastReportRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetLastReportRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// Response for GetLastReport
type GetLastReportResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The last report
	LastReport *v1.CashDrawerReport `protobuf:"bytes,1,opt,name=last_report,json=lastReport,proto3,oneof" json:"last_report,omitempty"`
}

func (x *GetLastReportResponse) Reset() {
	*x = GetLastReportResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLastReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLastReportResponse) ProtoMessage() {}

func (x *GetLastReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLastReportResponse.ProtoReflect.Descriptor instead.
func (*GetLastReportResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetLastReportResponse) GetLastReport() *v1.CashDrawerReport {
	if x != nil {
		return x.LastReport
	}
	return nil
}

// Request for GetReportedCashTotal
type GetReportedCashTotalRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the business
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The range
	Range *interval.Interval `protobuf:"bytes,2,opt,name=range,proto3" json:"range,omitempty"`
}

func (x *GetReportedCashTotalRequest) Reset() {
	*x = GetReportedCashTotalRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReportedCashTotalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReportedCashTotalRequest) ProtoMessage() {}

func (x *GetReportedCashTotalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReportedCashTotalRequest.ProtoReflect.Descriptor instead.
func (*GetReportedCashTotalRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetReportedCashTotalRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetReportedCashTotalRequest) GetRange() *interval.Interval {
	if x != nil {
		return x.Range
	}
	return nil
}

// Response for GetReportedCashTotal
type GetReportedCashTotalResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The reported total amount
	ReportedCashTotal *money.Money `protobuf:"bytes,1,opt,name=reported_cash_total,json=reportedCashTotal,proto3" json:"reported_cash_total,omitempty"`
}

func (x *GetReportedCashTotalResponse) Reset() {
	*x = GetReportedCashTotalResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReportedCashTotalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReportedCashTotalResponse) ProtoMessage() {}

func (x *GetReportedCashTotalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReportedCashTotalResponse.ProtoReflect.Descriptor instead.
func (*GetReportedCashTotalResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetReportedCashTotalResponse) GetReportedCashTotal() *money.Money {
	if x != nil {
		return x.ReportedCashTotal
	}
	return nil
}

// Request for ListCashAdjustments
type ListCashAdjustmentsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the business
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// Result sorting. The result is always sorted by the "created_at" field. Set to true if you want to sort by ascending
	// order. The default is false (descending order).
	Asc *bool `protobuf:"varint,3,opt,name=asc,proto3,oneof" json:"asc,omitempty"`
	// The range
	Range *interval.Interval `protobuf:"bytes,4,opt,name=range,proto3,oneof" json:"range,omitempty"`
	// The report ID. If specified, only adjustments linked to the report are returned.
	ReportId *int64 `protobuf:"varint,5,opt,name=report_id,json=reportId,proto3,oneof" json:"report_id,omitempty"`
}

func (x *ListCashAdjustmentsRequest) Reset() {
	*x = ListCashAdjustmentsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCashAdjustmentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCashAdjustmentsRequest) ProtoMessage() {}

func (x *ListCashAdjustmentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCashAdjustmentsRequest.ProtoReflect.Descriptor instead.
func (*ListCashAdjustmentsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListCashAdjustmentsRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListCashAdjustmentsRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListCashAdjustmentsRequest) GetAsc() bool {
	if x != nil && x.Asc != nil {
		return *x.Asc
	}
	return false
}

func (x *ListCashAdjustmentsRequest) GetRange() *interval.Interval {
	if x != nil {
		return x.Range
	}
	return nil
}

func (x *ListCashAdjustmentsRequest) GetReportId() int64 {
	if x != nil && x.ReportId != nil {
		return *x.ReportId
	}
	return 0
}

// Response for ListCashAdjustments
type ListCashAdjustmentsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// Adjustments
	Adjustments []*v1.CashDrawerAdjustment `protobuf:"bytes,2,rep,name=adjustments,proto3" json:"adjustments,omitempty"`
}

func (x *ListCashAdjustmentsResponse) Reset() {
	*x = ListCashAdjustmentsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCashAdjustmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCashAdjustmentsResponse) ProtoMessage() {}

func (x *ListCashAdjustmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCashAdjustmentsResponse.ProtoReflect.Descriptor instead.
func (*ListCashAdjustmentsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescGZIP(), []int{7}
}

func (x *ListCashAdjustmentsResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListCashAdjustmentsResponse) GetAdjustments() []*v1.CashDrawerAdjustment {
	if x != nil {
		return x.Adjustments
	}
	return nil
}

// Request for CreateCashAdjustment
type CreateCashAdjustmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the business
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The ID of the staff
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// Adjustment
	Adjustment *v1.CreateCashDrawerAdjustmentDef `protobuf:"bytes,3,opt,name=adjustment,proto3" json:"adjustment,omitempty"`
}

func (x *CreateCashAdjustmentRequest) Reset() {
	*x = CreateCashAdjustmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCashAdjustmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCashAdjustmentRequest) ProtoMessage() {}

func (x *CreateCashAdjustmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCashAdjustmentRequest.ProtoReflect.Descriptor instead.
func (*CreateCashAdjustmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescGZIP(), []int{8}
}

func (x *CreateCashAdjustmentRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateCashAdjustmentRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateCashAdjustmentRequest) GetAdjustment() *v1.CreateCashDrawerAdjustmentDef {
	if x != nil {
		return x.Adjustment
	}
	return nil
}

// Response for CreateCashAdjustment
type CreateCashAdjustmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The created adjustment
	Adjustment *v1.CashDrawerAdjustment `protobuf:"bytes,1,opt,name=adjustment,proto3" json:"adjustment,omitempty"`
}

func (x *CreateCashAdjustmentResponse) Reset() {
	*x = CreateCashAdjustmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCashAdjustmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCashAdjustmentResponse) ProtoMessage() {}

func (x *CreateCashAdjustmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCashAdjustmentResponse.ProtoReflect.Descriptor instead.
func (*CreateCashAdjustmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescGZIP(), []int{9}
}

func (x *CreateCashAdjustmentResponse) GetAdjustment() *v1.CashDrawerAdjustment {
	if x != nil {
		return x.Adjustment
	}
	return nil
}

// Request for CreateReport
type CreateReportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the company
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// The ID of the business
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The ID of the staff
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// Report
	Report *v1.CreateCashDrawerReportDef `protobuf:"bytes,4,opt,name=report,proto3" json:"report,omitempty"`
}

func (x *CreateReportRequest) Reset() {
	*x = CreateReportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateReportRequest) ProtoMessage() {}

func (x *CreateReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateReportRequest.ProtoReflect.Descriptor instead.
func (*CreateReportRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescGZIP(), []int{10}
}

func (x *CreateReportRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateReportRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateReportRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateReportRequest) GetReport() *v1.CreateCashDrawerReportDef {
	if x != nil {
		return x.Report
	}
	return nil
}

// Response for CreateReport
type CreateReportResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The created report
	Report *v1.CashDrawerReport `protobuf:"bytes,1,opt,name=report,proto3" json:"report,omitempty"`
}

func (x *CreateReportResponse) Reset() {
	*x = CreateReportResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateReportResponse) ProtoMessage() {}

func (x *CreateReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateReportResponse.ProtoReflect.Descriptor instead.
func (*CreateReportResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescGZIP(), []int{11}
}

func (x *CreateReportResponse) GetReport() *v1.CashDrawerReport {
	if x != nil {
		return x.Report
	}
	return nil
}

// Request for UpdateReport
type UpdateReportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Report
	Report *v1.UpdateCashDrawerReportDef `protobuf:"bytes,1,opt,name=report,proto3" json:"report,omitempty"`
	// The ID of the business, for ownership validation
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *UpdateReportRequest) Reset() {
	*x = UpdateReportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateReportRequest) ProtoMessage() {}

func (x *UpdateReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateReportRequest.ProtoReflect.Descriptor instead.
func (*UpdateReportRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateReportRequest) GetReport() *v1.UpdateCashDrawerReportDef {
	if x != nil {
		return x.Report
	}
	return nil
}

func (x *UpdateReportRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// Response for UpdateReport
type UpdateReportResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateReportResponse) Reset() {
	*x = UpdateReportResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateReportResponse) ProtoMessage() {}

func (x *UpdateReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateReportResponse.ProtoReflect.Descriptor instead.
func (*UpdateReportResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescGZIP(), []int{13}
}

var File_moego_service_finance_tools_v1_cash_drawer_service_proto protoreflect.FileDescriptor

var file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDesc = []byte{
	0x0a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x69,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x61, 0x73, 0x68, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x65, 0x72,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x81, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa4, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x49, 0x0a, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x61, 0x73, 0x68, 0x44, 0x72, 0x61, 0x77, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x22, 0x40, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x7e, 0x0a,
	0x15, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x55, 0x0a, 0x0b, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x68,
	0x44, 0x72, 0x61, 0x77, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x00, 0x52, 0x0a,
	0x6c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x7e, 0x0a,
	0x1b, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x43, 0x61, 0x73, 0x68,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x62, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x43, 0x61, 0x73, 0x68,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a,
	0x13, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x43, 0x61, 0x73, 0x68, 0x54, 0x6f, 0x74, 0x61,
	0x6c, 0x22, 0x9d, 0x02, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x68, 0x41, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x15, 0x0a,
	0x03, 0x61, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x03, 0x61, 0x73,
	0x63, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x48, 0x01, 0x52, 0x05, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x48, 0x02, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x61, 0x73, 0x63, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69,
	0x64, 0x22, 0xb8, 0x01, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x68, 0x41, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x55, 0x0a, 0x0b, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x68, 0x44,
	0x72, 0x61, 0x77, 0x65, 0x72, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x0b, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xd3, 0x01, 0x0a,
	0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x68, 0x41, 0x64, 0x6a, 0x75, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x66, 0x0a, 0x0a, 0x61, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x68, 0x44, 0x72, 0x61, 0x77, 0x65, 0x72, 0x41,
	0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x22, 0x73, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x68,
	0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x53, 0x0a, 0x0a, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f,
	0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x68, 0x44, 0x72, 0x61, 0x77, 0x65,
	0x72, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x64, 0x6a,
	0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xe7, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f,
	0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x68,
	0x44, 0x72, 0x61, 0x77, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x66, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x22, 0x5f, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x06, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x68, 0x44, 0x72,
	0x61, 0x77, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x06, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x22, 0x9b, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5a, 0x0a, 0x06, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x61, 0x73, 0x68, 0x44, 0x72, 0x61, 0x77, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x22, 0x16, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xb8, 0x07, 0x0a, 0x11, 0x43, 0x61, 0x73,
	0x68, 0x44, 0x72, 0x61, 0x77, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x76,
	0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x74,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x64, 0x43, 0x61, 0x73, 0x68, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x43, 0x61, 0x73, 0x68, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x43, 0x61, 0x73, 0x68, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8e, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x61, 0x73, 0x68, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x73, 0x68, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6e,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x61, 0x73, 0x68, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x14, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x68, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x68, 0x41, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x68, 0x41, 0x64, 0x6a, 0x75, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x79, 0x0a,
	0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x79, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x91, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x65, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f,
	0x6c, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x74, 0x6f, 0x6f,
	0x6c, 0x73, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescOnce sync.Once
	file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescData = file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDesc
)

func file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescGZIP() []byte {
	file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescOnce.Do(func() {
		file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescData)
	})
	return file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDescData
}

var file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_moego_service_finance_tools_v1_cash_drawer_service_proto_goTypes = []interface{}{
	(*ListReportsRequest)(nil),               // 0: moego.service.finance_tools.v1.ListReportsRequest
	(*ListReportsResponse)(nil),              // 1: moego.service.finance_tools.v1.ListReportsResponse
	(*GetLastReportRequest)(nil),             // 2: moego.service.finance_tools.v1.GetLastReportRequest
	(*GetLastReportResponse)(nil),            // 3: moego.service.finance_tools.v1.GetLastReportResponse
	(*GetReportedCashTotalRequest)(nil),      // 4: moego.service.finance_tools.v1.GetReportedCashTotalRequest
	(*GetReportedCashTotalResponse)(nil),     // 5: moego.service.finance_tools.v1.GetReportedCashTotalResponse
	(*ListCashAdjustmentsRequest)(nil),       // 6: moego.service.finance_tools.v1.ListCashAdjustmentsRequest
	(*ListCashAdjustmentsResponse)(nil),      // 7: moego.service.finance_tools.v1.ListCashAdjustmentsResponse
	(*CreateCashAdjustmentRequest)(nil),      // 8: moego.service.finance_tools.v1.CreateCashAdjustmentRequest
	(*CreateCashAdjustmentResponse)(nil),     // 9: moego.service.finance_tools.v1.CreateCashAdjustmentResponse
	(*CreateReportRequest)(nil),              // 10: moego.service.finance_tools.v1.CreateReportRequest
	(*CreateReportResponse)(nil),             // 11: moego.service.finance_tools.v1.CreateReportResponse
	(*UpdateReportRequest)(nil),              // 12: moego.service.finance_tools.v1.UpdateReportRequest
	(*UpdateReportResponse)(nil),             // 13: moego.service.finance_tools.v1.UpdateReportResponse
	(*v2.PaginationRequest)(nil),             // 14: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),            // 15: moego.utils.v2.PaginationResponse
	(*v1.CashDrawerReport)(nil),              // 16: moego.models.finance_tools.v1.CashDrawerReport
	(*interval.Interval)(nil),                // 17: google.type.Interval
	(*money.Money)(nil),                      // 18: google.type.Money
	(*v1.CashDrawerAdjustment)(nil),          // 19: moego.models.finance_tools.v1.CashDrawerAdjustment
	(*v1.CreateCashDrawerAdjustmentDef)(nil), // 20: moego.models.finance_tools.v1.CreateCashDrawerAdjustmentDef
	(*v1.CreateCashDrawerReportDef)(nil),     // 21: moego.models.finance_tools.v1.CreateCashDrawerReportDef
	(*v1.UpdateCashDrawerReportDef)(nil),     // 22: moego.models.finance_tools.v1.UpdateCashDrawerReportDef
}
var file_moego_service_finance_tools_v1_cash_drawer_service_proto_depIdxs = []int32{
	14, // 0: moego.service.finance_tools.v1.ListReportsRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	15, // 1: moego.service.finance_tools.v1.ListReportsResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	16, // 2: moego.service.finance_tools.v1.ListReportsResponse.reports:type_name -> moego.models.finance_tools.v1.CashDrawerReport
	16, // 3: moego.service.finance_tools.v1.GetLastReportResponse.last_report:type_name -> moego.models.finance_tools.v1.CashDrawerReport
	17, // 4: moego.service.finance_tools.v1.GetReportedCashTotalRequest.range:type_name -> google.type.Interval
	18, // 5: moego.service.finance_tools.v1.GetReportedCashTotalResponse.reported_cash_total:type_name -> google.type.Money
	14, // 6: moego.service.finance_tools.v1.ListCashAdjustmentsRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	17, // 7: moego.service.finance_tools.v1.ListCashAdjustmentsRequest.range:type_name -> google.type.Interval
	15, // 8: moego.service.finance_tools.v1.ListCashAdjustmentsResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	19, // 9: moego.service.finance_tools.v1.ListCashAdjustmentsResponse.adjustments:type_name -> moego.models.finance_tools.v1.CashDrawerAdjustment
	20, // 10: moego.service.finance_tools.v1.CreateCashAdjustmentRequest.adjustment:type_name -> moego.models.finance_tools.v1.CreateCashDrawerAdjustmentDef
	19, // 11: moego.service.finance_tools.v1.CreateCashAdjustmentResponse.adjustment:type_name -> moego.models.finance_tools.v1.CashDrawerAdjustment
	21, // 12: moego.service.finance_tools.v1.CreateReportRequest.report:type_name -> moego.models.finance_tools.v1.CreateCashDrawerReportDef
	16, // 13: moego.service.finance_tools.v1.CreateReportResponse.report:type_name -> moego.models.finance_tools.v1.CashDrawerReport
	22, // 14: moego.service.finance_tools.v1.UpdateReportRequest.report:type_name -> moego.models.finance_tools.v1.UpdateCashDrawerReportDef
	0,  // 15: moego.service.finance_tools.v1.CashDrawerService.ListReports:input_type -> moego.service.finance_tools.v1.ListReportsRequest
	2,  // 16: moego.service.finance_tools.v1.CashDrawerService.GetLastReport:input_type -> moego.service.finance_tools.v1.GetLastReportRequest
	4,  // 17: moego.service.finance_tools.v1.CashDrawerService.GetReportedCashTotal:input_type -> moego.service.finance_tools.v1.GetReportedCashTotalRequest
	6,  // 18: moego.service.finance_tools.v1.CashDrawerService.ListCashAdjustments:input_type -> moego.service.finance_tools.v1.ListCashAdjustmentsRequest
	8,  // 19: moego.service.finance_tools.v1.CashDrawerService.CreateCashAdjustment:input_type -> moego.service.finance_tools.v1.CreateCashAdjustmentRequest
	10, // 20: moego.service.finance_tools.v1.CashDrawerService.CreateReport:input_type -> moego.service.finance_tools.v1.CreateReportRequest
	12, // 21: moego.service.finance_tools.v1.CashDrawerService.UpdateReport:input_type -> moego.service.finance_tools.v1.UpdateReportRequest
	1,  // 22: moego.service.finance_tools.v1.CashDrawerService.ListReports:output_type -> moego.service.finance_tools.v1.ListReportsResponse
	3,  // 23: moego.service.finance_tools.v1.CashDrawerService.GetLastReport:output_type -> moego.service.finance_tools.v1.GetLastReportResponse
	5,  // 24: moego.service.finance_tools.v1.CashDrawerService.GetReportedCashTotal:output_type -> moego.service.finance_tools.v1.GetReportedCashTotalResponse
	7,  // 25: moego.service.finance_tools.v1.CashDrawerService.ListCashAdjustments:output_type -> moego.service.finance_tools.v1.ListCashAdjustmentsResponse
	9,  // 26: moego.service.finance_tools.v1.CashDrawerService.CreateCashAdjustment:output_type -> moego.service.finance_tools.v1.CreateCashAdjustmentResponse
	11, // 27: moego.service.finance_tools.v1.CashDrawerService.CreateReport:output_type -> moego.service.finance_tools.v1.CreateReportResponse
	13, // 28: moego.service.finance_tools.v1.CashDrawerService.UpdateReport:output_type -> moego.service.finance_tools.v1.UpdateReportResponse
	22, // [22:29] is the sub-list for method output_type
	15, // [15:22] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_moego_service_finance_tools_v1_cash_drawer_service_proto_init() }
func file_moego_service_finance_tools_v1_cash_drawer_service_proto_init() {
	if File_moego_service_finance_tools_v1_cash_drawer_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListReportsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListReportsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLastReportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLastReportResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReportedCashTotalRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReportedCashTotalResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCashAdjustmentsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCashAdjustmentsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCashAdjustmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCashAdjustmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateReportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateReportResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateReportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateReportResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_finance_tools_v1_cash_drawer_service_proto_goTypes,
		DependencyIndexes: file_moego_service_finance_tools_v1_cash_drawer_service_proto_depIdxs,
		MessageInfos:      file_moego_service_finance_tools_v1_cash_drawer_service_proto_msgTypes,
	}.Build()
	File_moego_service_finance_tools_v1_cash_drawer_service_proto = out.File
	file_moego_service_finance_tools_v1_cash_drawer_service_proto_rawDesc = nil
	file_moego_service_finance_tools_v1_cash_drawer_service_proto_goTypes = nil
	file_moego_service_finance_tools_v1_cash_drawer_service_proto_depIdxs = nil
}
