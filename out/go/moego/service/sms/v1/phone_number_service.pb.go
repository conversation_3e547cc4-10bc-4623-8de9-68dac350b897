// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/sms/v1/phone_number_service.proto

package smssvcpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// format phone number request
type FormatPhoneNumberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId uint64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *FormatPhoneNumberRequest) Reset() {
	*x = FormatPhoneNumberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_phone_number_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FormatPhoneNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormatPhoneNumberRequest) ProtoMessage() {}

func (x *FormatPhoneNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_phone_number_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormatPhoneNumberRequest.ProtoReflect.Descriptor instead.
func (*FormatPhoneNumberRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_phone_number_service_proto_rawDescGZIP(), []int{0}
}

func (x *FormatPhoneNumberRequest) GetBusinessId() uint64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *FormatPhoneNumberRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

// format phone number response
type FormatPhoneNumberResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// e164 phone number
	E164PhoneNumber string `protobuf:"bytes,1,opt,name=e164_phone_number,json=e164PhoneNumber,proto3" json:"e164_phone_number,omitempty"`
}

func (x *FormatPhoneNumberResponse) Reset() {
	*x = FormatPhoneNumberResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_phone_number_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FormatPhoneNumberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormatPhoneNumberResponse) ProtoMessage() {}

func (x *FormatPhoneNumberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_phone_number_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormatPhoneNumberResponse.ProtoReflect.Descriptor instead.
func (*FormatPhoneNumberResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_phone_number_service_proto_rawDescGZIP(), []int{1}
}

func (x *FormatPhoneNumberResponse) GetE164PhoneNumber() string {
	if x != nil {
		return x.E164PhoneNumber
	}
	return ""
}

var File_moego_service_sms_v1_phone_number_service_proto protoreflect.FileDescriptor

var file_moego_service_sms_v1_phone_number_service_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x73, 0x6d, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x14, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x69, 0x0a, 0x18, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2c, 0x0a,
	0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x1e, 0x52, 0x0b,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x47, 0x0a, 0x19, 0x46,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x31, 0x36, 0x34,
	0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x31, 0x36, 0x34, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x32, 0x7f, 0x0a, 0x12, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x69, 0x0a, 0x06, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x74, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73,
	0x6d, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x73, 0x6d, 0x73, 0x2f,
	0x76, 0x31, 0x3b, 0x73, 0x6d, 0x73, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_service_sms_v1_phone_number_service_proto_rawDescOnce sync.Once
	file_moego_service_sms_v1_phone_number_service_proto_rawDescData = file_moego_service_sms_v1_phone_number_service_proto_rawDesc
)

func file_moego_service_sms_v1_phone_number_service_proto_rawDescGZIP() []byte {
	file_moego_service_sms_v1_phone_number_service_proto_rawDescOnce.Do(func() {
		file_moego_service_sms_v1_phone_number_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_sms_v1_phone_number_service_proto_rawDescData)
	})
	return file_moego_service_sms_v1_phone_number_service_proto_rawDescData
}

var file_moego_service_sms_v1_phone_number_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_service_sms_v1_phone_number_service_proto_goTypes = []interface{}{
	(*FormatPhoneNumberRequest)(nil),  // 0: moego.service.sms.v1.FormatPhoneNumberRequest
	(*FormatPhoneNumberResponse)(nil), // 1: moego.service.sms.v1.FormatPhoneNumberResponse
}
var file_moego_service_sms_v1_phone_number_service_proto_depIdxs = []int32{
	0, // 0: moego.service.sms.v1.PhoneNumberService.Format:input_type -> moego.service.sms.v1.FormatPhoneNumberRequest
	1, // 1: moego.service.sms.v1.PhoneNumberService.Format:output_type -> moego.service.sms.v1.FormatPhoneNumberResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_service_sms_v1_phone_number_service_proto_init() }
func file_moego_service_sms_v1_phone_number_service_proto_init() {
	if File_moego_service_sms_v1_phone_number_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_sms_v1_phone_number_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FormatPhoneNumberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_phone_number_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FormatPhoneNumberResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_sms_v1_phone_number_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_sms_v1_phone_number_service_proto_goTypes,
		DependencyIndexes: file_moego_service_sms_v1_phone_number_service_proto_depIdxs,
		MessageInfos:      file_moego_service_sms_v1_phone_number_service_proto_msgTypes,
	}.Build()
	File_moego_service_sms_v1_phone_number_service_proto = out.File
	file_moego_service_sms_v1_phone_number_service_proto_rawDesc = nil
	file_moego_service_sms_v1_phone_number_service_proto_goTypes = nil
	file_moego_service_sms_v1_phone_number_service_proto_depIdxs = nil
}
