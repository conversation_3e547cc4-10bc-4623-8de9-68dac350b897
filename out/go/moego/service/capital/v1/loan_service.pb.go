// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/capital/v1/loan_service.proto

package capitalsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/capital/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/split_payment/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// type of account link
type CreateLinkRequest_AccountLinkType int32

const (
	// Unspecified
	CreateLinkRequest_ACCOUNT_LINK_TYPE_UNSPECIFIED CreateLinkRequest_AccountLinkType = 0
	// link to apply an offer
	CreateLinkRequest_APPLY_OFFER CreateLinkRequest_AccountLinkType = 1
	// link to review eligibility and to prove eligibility
	CreateLinkRequest_ELIGIBILITY_REVIEW CreateLinkRequest_AccountLinkType = 2
	// link to manually repayment
	CreateLinkRequest_MANUALLY_REPAY CreateLinkRequest_AccountLinkType = 3
)

// Enum value maps for CreateLinkRequest_AccountLinkType.
var (
	CreateLinkRequest_AccountLinkType_name = map[int32]string{
		0: "ACCOUNT_LINK_TYPE_UNSPECIFIED",
		1: "APPLY_OFFER",
		2: "ELIGIBILITY_REVIEW",
		3: "MANUALLY_REPAY",
	}
	CreateLinkRequest_AccountLinkType_value = map[string]int32{
		"ACCOUNT_LINK_TYPE_UNSPECIFIED": 0,
		"APPLY_OFFER":                   1,
		"ELIGIBILITY_REVIEW":            2,
		"MANUALLY_REPAY":                3,
	}
)

func (x CreateLinkRequest_AccountLinkType) Enum() *CreateLinkRequest_AccountLinkType {
	p := new(CreateLinkRequest_AccountLinkType)
	*p = x
	return p
}

func (x CreateLinkRequest_AccountLinkType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateLinkRequest_AccountLinkType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_service_capital_v1_loan_service_proto_enumTypes[0].Descriptor()
}

func (CreateLinkRequest_AccountLinkType) Type() protoreflect.EnumType {
	return &file_moego_service_capital_v1_loan_service_proto_enumTypes[0]
}

func (x CreateLinkRequest_AccountLinkType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateLinkRequest_AccountLinkType.Descriptor instead.
func (CreateLinkRequest_AccountLinkType) EnumDescriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{12, 0}
}

// The type of a loan offer event
type ProcessOfferEventRequest_OfferEventType int32

const (
	// Unspecified
	ProcessOfferEventRequest_OFFER_EVENT_TYPE_UNSPECIFIED ProcessOfferEventRequest_OfferEventType = 0
	// The event type that a loan offer is created
	ProcessOfferEventRequest_CREATED ProcessOfferEventRequest_OfferEventType = 1
	// The event type that the user submits their offer application
	ProcessOfferEventRequest_ACCEPTED ProcessOfferEventRequest_OfferEventType = 2
	// The event type that the capital approves the offer application and funds are paid out to the user
	ProcessOfferEventRequest_PAID_OUT ProcessOfferEventRequest_OfferEventType = 3
	// The event type that the user fully repays the financing balance
	ProcessOfferEventRequest_FULLY_REPAID ProcessOfferEventRequest_OfferEventType = 4
	// The event type that the user cancels the financing offer
	ProcessOfferEventRequest_CANCELED ProcessOfferEventRequest_OfferEventType = 5
	// The event type that the user’s application isn’t approved
	ProcessOfferEventRequest_REJECTED ProcessOfferEventRequest_OfferEventType = 6
	// The event type that a loan offer expires and is no longer available
	ProcessOfferEventRequest_EXPIRED ProcessOfferEventRequest_OfferEventType = 7
	// The event type that a business is prequalified
	ProcessOfferEventRequest_PREQUALIFIED ProcessOfferEventRequest_OfferEventType = 8
)

// Enum value maps for ProcessOfferEventRequest_OfferEventType.
var (
	ProcessOfferEventRequest_OfferEventType_name = map[int32]string{
		0: "OFFER_EVENT_TYPE_UNSPECIFIED",
		1: "CREATED",
		2: "ACCEPTED",
		3: "PAID_OUT",
		4: "FULLY_REPAID",
		5: "CANCELED",
		6: "REJECTED",
		7: "EXPIRED",
		8: "PREQUALIFIED",
	}
	ProcessOfferEventRequest_OfferEventType_value = map[string]int32{
		"OFFER_EVENT_TYPE_UNSPECIFIED": 0,
		"CREATED":                      1,
		"ACCEPTED":                     2,
		"PAID_OUT":                     3,
		"FULLY_REPAID":                 4,
		"CANCELED":                     5,
		"REJECTED":                     6,
		"EXPIRED":                      7,
		"PREQUALIFIED":                 8,
	}
)

func (x ProcessOfferEventRequest_OfferEventType) Enum() *ProcessOfferEventRequest_OfferEventType {
	p := new(ProcessOfferEventRequest_OfferEventType)
	*p = x
	return p
}

func (x ProcessOfferEventRequest_OfferEventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessOfferEventRequest_OfferEventType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_service_capital_v1_loan_service_proto_enumTypes[1].Descriptor()
}

func (ProcessOfferEventRequest_OfferEventType) Type() protoreflect.EnumType {
	return &file_moego_service_capital_v1_loan_service_proto_enumTypes[1]
}

func (x ProcessOfferEventRequest_OfferEventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessOfferEventRequest_OfferEventType.Descriptor instead.
func (ProcessOfferEventRequest_OfferEventType) EnumDescriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{14, 0}
}

// The account event type
type ProcessAccountEventRequest_AccountEventType int32

const (
	// Unspecified
	ProcessAccountEventRequest_ACCOUNT_EVENT_TYPE_UNSPECIFIED ProcessAccountEventRequest_AccountEventType = 0
	// New channel account is created.
	ProcessAccountEventRequest_CREATED ProcessAccountEventRequest_AccountEventType = 1
	// Account is onboarded.
	ProcessAccountEventRequest_ONBOARDED ProcessAccountEventRequest_AccountEventType = 2
	// Input required.
	ProcessAccountEventRequest_INPUT_REQUIRED ProcessAccountEventRequest_AccountEventType = 3
)

// Enum value maps for ProcessAccountEventRequest_AccountEventType.
var (
	ProcessAccountEventRequest_AccountEventType_name = map[int32]string{
		0: "ACCOUNT_EVENT_TYPE_UNSPECIFIED",
		1: "CREATED",
		2: "ONBOARDED",
		3: "INPUT_REQUIRED",
	}
	ProcessAccountEventRequest_AccountEventType_value = map[string]int32{
		"ACCOUNT_EVENT_TYPE_UNSPECIFIED": 0,
		"CREATED":                        1,
		"ONBOARDED":                      2,
		"INPUT_REQUIRED":                 3,
	}
)

func (x ProcessAccountEventRequest_AccountEventType) Enum() *ProcessAccountEventRequest_AccountEventType {
	p := new(ProcessAccountEventRequest_AccountEventType)
	*p = x
	return p
}

func (x ProcessAccountEventRequest_AccountEventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessAccountEventRequest_AccountEventType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_service_capital_v1_loan_service_proto_enumTypes[2].Descriptor()
}

func (ProcessAccountEventRequest_AccountEventType) Type() protoreflect.EnumType {
	return &file_moego_service_capital_v1_loan_service_proto_enumTypes[2]
}

func (x ProcessAccountEventRequest_AccountEventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessAccountEventRequest_AccountEventType.Descriptor instead.
func (ProcessAccountEventRequest_AccountEventType) EnumDescriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{17, 0}
}

// The event payload of a loan offer event. It is basically a "partial" version of LoanOfferModel.
type LoanOfferEventPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the channel that created the offer.
	ChannelName v1.LoanChannel `protobuf:"varint,1,opt,name=channel_name,json=channelName,proto3,enum=moego.models.capital.v1.LoanChannel" json:"channel_name,omitempty"`
	// The ID of the account in the channel.
	ChannelAccountId string `protobuf:"bytes,2,opt,name=channel_account_id,json=channelAccountId,proto3" json:"channel_account_id,omitempty"`
	// The ID of the offer in the channel.
	ChannelOfferId string `protobuf:"bytes,3,opt,name=channel_offer_id,json=channelOfferId,proto3" json:"channel_offer_id,omitempty"`
	// The type of the offer, e.g. MCA, Term Loan.
	OfferType v1.LoanOfferType `protobuf:"varint,4,opt,name=offer_type,json=offerType,proto3,enum=moego.models.capital.v1.LoanOfferType" json:"offer_type,omitempty"`
	// The unix timestamp in seconds when the offer was created.
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// The unix timestamp in seconds when the offer expires.
	ExpireAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	// The offered terms of the offer.
	OfferedTerms *LoanOfferEventLoanTerms `protobuf:"bytes,7,opt,name=offered_terms,json=offeredTerms,proto3" json:"offered_terms,omitempty"`
	// The accepted terms of the offer.
	AcceptedTerms *LoanOfferEventLoanTerms `protobuf:"bytes,8,opt,name=accepted_terms,json=acceptedTerms,proto3,oneof" json:"accepted_terms,omitempty"`
	// Financing product identifier. e.g. Standard, Refill.
	ProductType v1.LoanProductType `protobuf:"varint,9,opt,name=product_type,json=productType,proto3,enum=moego.models.capital.v1.LoanProductType" json:"product_type,omitempty"`
	// The status of the offer in the channel.
	// This field is a backup for original status field from the channel, so we use string type instead of enum.
	// Possible values for Stripe channel: "delivered", "accepted", "canceled", "expired", "fully_repaid", "paid_out",
	// "rejected", "replaced", "undelivered".
	// Kanmon doesn't have a status field for an offer, but only some for issued product. Note that REFINANCED is not
	// supported yet so will not pass validation.
	ChannelStatus string `protobuf:"bytes,10,opt,name=channel_status,json=channelStatus,proto3" json:"channel_status,omitempty"`
}

func (x *LoanOfferEventPayload) Reset() {
	*x = LoanOfferEventPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanOfferEventPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanOfferEventPayload) ProtoMessage() {}

func (x *LoanOfferEventPayload) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanOfferEventPayload.ProtoReflect.Descriptor instead.
func (*LoanOfferEventPayload) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{0}
}

func (x *LoanOfferEventPayload) GetChannelName() v1.LoanChannel {
	if x != nil {
		return x.ChannelName
	}
	return v1.LoanChannel(0)
}

func (x *LoanOfferEventPayload) GetChannelAccountId() string {
	if x != nil {
		return x.ChannelAccountId
	}
	return ""
}

func (x *LoanOfferEventPayload) GetChannelOfferId() string {
	if x != nil {
		return x.ChannelOfferId
	}
	return ""
}

func (x *LoanOfferEventPayload) GetOfferType() v1.LoanOfferType {
	if x != nil {
		return x.OfferType
	}
	return v1.LoanOfferType(0)
}

func (x *LoanOfferEventPayload) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *LoanOfferEventPayload) GetExpireAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireAt
	}
	return nil
}

func (x *LoanOfferEventPayload) GetOfferedTerms() *LoanOfferEventLoanTerms {
	if x != nil {
		return x.OfferedTerms
	}
	return nil
}

func (x *LoanOfferEventPayload) GetAcceptedTerms() *LoanOfferEventLoanTerms {
	if x != nil {
		return x.AcceptedTerms
	}
	return nil
}

func (x *LoanOfferEventPayload) GetProductType() v1.LoanProductType {
	if x != nil {
		return x.ProductType
	}
	return v1.LoanProductType(0)
}

func (x *LoanOfferEventPayload) GetChannelStatus() string {
	if x != nil {
		return x.ChannelStatus
	}
	return ""
}

// LoanOfferEventLoanTerms
type LoanOfferEventLoanTerms struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Amount of financing offered, in minor units.
	AdvanceAmount float64 `protobuf:"fixed64,1,opt,name=advance_amount,json=advanceAmount,proto3" json:"advance_amount,omitempty"`
	// Type of campaign, e.g. "newly_eligible_user", "previously_eligible_user", "repeat_user"
	// Not available for the accepted terms.
	CampaignType *v1.LoanCampaignType `protobuf:"varint,2,opt,name=campaign_type,json=campaignType,proto3,enum=moego.models.capital.v1.LoanCampaignType,oneof" json:"campaign_type,omitempty"`
	// Currency code, e.g. "usd", "cad"
	Currency string `protobuf:"bytes,3,opt,name=currency,proto3" json:"currency,omitempty"`
	// Fixed fee amount, in minor units.
	FeeAmount float64 `protobuf:"fixed64,4,opt,name=fee_amount,json=feeAmount,proto3" json:"fee_amount,omitempty"`
	// Populated when the product_type is refill.
	// Represents the discount rate percentage on remaining fee on the existing loan.
	// When the financing_offer is paid out, the previous_financing_fee_discount_amount
	// will be computed as the multiple of this rate and the remaining fee.
	// Not available for the accepted terms.
	PreviousFinancingFeeDiscountRate *float64 `protobuf:"fixed64,5,opt,name=previous_financing_fee_discount_rate,json=previousFinancingFeeDiscountRate,proto3,oneof" json:"previous_financing_fee_discount_rate,omitempty"`
	// Per-transaction rate at which Stripe will withhold funds to repay the financing.
	WithholdRate float64 `protobuf:"fixed64,6,opt,name=withhold_rate,json=withholdRate,proto3" json:"withhold_rate,omitempty"`
	// Populated when the product type of the offer is refill. Represents the discount amount on remaining premium for the
	// existing loan at payout time.
	// Not available for the offered terms.
	PreviousFinancingFeeDiscountAmount *float64 `protobuf:"fixed64,7,opt,name=previous_financing_fee_discount_amount,json=previousFinancingFeeDiscountAmount,proto3,oneof" json:"previous_financing_fee_discount_amount,omitempty"`
	// Interest rate, available for Term Loan.
	InterestRate float64 `protobuf:"fixed64,9,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
	// Duration months
	DurationMonths int64 `protobuf:"varint,10,opt,name=duration_months,json=durationMonths,proto3" json:"duration_months,omitempty"`
	// Interest amount.
	InterestAmount float64 `protobuf:"fixed64,11,opt,name=interest_amount,json=interestAmount,proto3" json:"interest_amount,omitempty"`
}

func (x *LoanOfferEventLoanTerms) Reset() {
	*x = LoanOfferEventLoanTerms{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanOfferEventLoanTerms) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanOfferEventLoanTerms) ProtoMessage() {}

func (x *LoanOfferEventLoanTerms) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanOfferEventLoanTerms.ProtoReflect.Descriptor instead.
func (*LoanOfferEventLoanTerms) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{1}
}

func (x *LoanOfferEventLoanTerms) GetAdvanceAmount() float64 {
	if x != nil {
		return x.AdvanceAmount
	}
	return 0
}

func (x *LoanOfferEventLoanTerms) GetCampaignType() v1.LoanCampaignType {
	if x != nil && x.CampaignType != nil {
		return *x.CampaignType
	}
	return v1.LoanCampaignType(0)
}

func (x *LoanOfferEventLoanTerms) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *LoanOfferEventLoanTerms) GetFeeAmount() float64 {
	if x != nil {
		return x.FeeAmount
	}
	return 0
}

func (x *LoanOfferEventLoanTerms) GetPreviousFinancingFeeDiscountRate() float64 {
	if x != nil && x.PreviousFinancingFeeDiscountRate != nil {
		return *x.PreviousFinancingFeeDiscountRate
	}
	return 0
}

func (x *LoanOfferEventLoanTerms) GetWithholdRate() float64 {
	if x != nil {
		return x.WithholdRate
	}
	return 0
}

func (x *LoanOfferEventLoanTerms) GetPreviousFinancingFeeDiscountAmount() float64 {
	if x != nil && x.PreviousFinancingFeeDiscountAmount != nil {
		return *x.PreviousFinancingFeeDiscountAmount
	}
	return 0
}

func (x *LoanOfferEventLoanTerms) GetInterestRate() float64 {
	if x != nil {
		return x.InterestRate
	}
	return 0
}

func (x *LoanOfferEventLoanTerms) GetDurationMonths() int64 {
	if x != nil {
		return x.DurationMonths
	}
	return 0
}

func (x *LoanOfferEventLoanTerms) GetInterestAmount() float64 {
	if x != nil {
		return x.InterestAmount
	}
	return 0
}

// Request for GetLoanEligibilityByEntity
type GetLoanEligibilityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the business
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetLoanEligibilityRequest) Reset() {
	*x = GetLoanEligibilityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanEligibilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanEligibilityRequest) ProtoMessage() {}

func (x *GetLoanEligibilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanEligibilityRequest.ProtoReflect.Descriptor instead.
func (*GetLoanEligibilityRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetLoanEligibilityRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// Response for GetLoanEligibility
type GetLoanEligibilityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether the specified loan user is eligible for MoeGo capital.
	IsEligible bool `protobuf:"varint,1,opt,name=is_eligible,json=isEligible,proto3" json:"is_eligible,omitempty"`
	// Ineligible reason.
	IneligibleReason v1.LoanIneligibleReason `protobuf:"varint,2,opt,name=ineligible_reason,json=ineligibleReason,proto3,enum=moego.models.capital.v1.LoanIneligibleReason" json:"ineligible_reason,omitempty"`
}

func (x *GetLoanEligibilityResponse) Reset() {
	*x = GetLoanEligibilityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanEligibilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanEligibilityResponse) ProtoMessage() {}

func (x *GetLoanEligibilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanEligibilityResponse.ProtoReflect.Descriptor instead.
func (*GetLoanEligibilityResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetLoanEligibilityResponse) GetIsEligible() bool {
	if x != nil {
		return x.IsEligible
	}
	return false
}

func (x *GetLoanEligibilityResponse) GetIneligibleReason() v1.LoanIneligibleReason {
	if x != nil {
		return x.IneligibleReason
	}
	return v1.LoanIneligibleReason(0)
}

// Request for GetLoanEligibilityFlags
type GetLoanEligibilityFlagsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the business
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetLoanEligibilityFlagsRequest) Reset() {
	*x = GetLoanEligibilityFlagsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanEligibilityFlagsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanEligibilityFlagsRequest) ProtoMessage() {}

func (x *GetLoanEligibilityFlagsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanEligibilityFlagsRequest.ProtoReflect.Descriptor instead.
func (*GetLoanEligibilityFlagsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetLoanEligibilityFlagsRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// Response for GetLoanEligibilityFlags
type GetLoanEligibilityFlagsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether the user has setup MGP
	IsMgpSetup bool `protobuf:"varint,1,opt,name=is_mgp_setup,json=isMgpSetup,proto3" json:"is_mgp_setup,omitempty"`
	// Whether the user has MGP primary type
	IsMgpPrimary bool `protobuf:"varint,2,opt,name=is_mgp_primary,json=isMgpPrimary,proto3" json:"is_mgp_primary,omitempty"`
	// Whether the user has MGP primary type long enough
	IsMgpPrimaryLongEnough bool `protobuf:"varint,3,opt,name=is_mgp_primary_long_enough,json=isMgpPrimaryLongEnough,proto3" json:"is_mgp_primary_long_enough,omitempty"`
}

func (x *GetLoanEligibilityFlagsResponse) Reset() {
	*x = GetLoanEligibilityFlagsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanEligibilityFlagsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanEligibilityFlagsResponse) ProtoMessage() {}

func (x *GetLoanEligibilityFlagsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanEligibilityFlagsResponse.ProtoReflect.Descriptor instead.
func (*GetLoanEligibilityFlagsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetLoanEligibilityFlagsResponse) GetIsMgpSetup() bool {
	if x != nil {
		return x.IsMgpSetup
	}
	return false
}

func (x *GetLoanEligibilityFlagsResponse) GetIsMgpPrimary() bool {
	if x != nil {
		return x.IsMgpPrimary
	}
	return false
}

func (x *GetLoanEligibilityFlagsResponse) GetIsMgpPrimaryLongEnough() bool {
	if x != nil {
		return x.IsMgpPrimaryLongEnough
	}
	return false
}

// Request for GetOfferList
type GetOfferListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the business
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The status of the loan offer
	Status *v1.LoanOfferStatus `protobuf:"varint,2,opt,name=status,proto3,enum=moego.models.capital.v1.LoanOfferStatus,oneof" json:"status,omitempty"`
}

func (x *GetOfferListRequest) Reset() {
	*x = GetOfferListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOfferListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOfferListRequest) ProtoMessage() {}

func (x *GetOfferListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOfferListRequest.ProtoReflect.Descriptor instead.
func (*GetOfferListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetOfferListRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetOfferListRequest) GetStatus() v1.LoanOfferStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.LoanOfferStatus(0)
}

// Response for GetOfferList
type GetOfferListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of loan offers.
	Offers []*v1.LoanOfferModel `protobuf:"bytes,1,rep,name=offers,proto3" json:"offers,omitempty"`
}

func (x *GetOfferListResponse) Reset() {
	*x = GetOfferListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOfferListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOfferListResponse) ProtoMessage() {}

func (x *GetOfferListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOfferListResponse.ProtoReflect.Descriptor instead.
func (*GetOfferListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetOfferListResponse) GetOffers() []*v1.LoanOfferModel {
	if x != nil {
		return x.Offers
	}
	return nil
}

// Request for GetOfferListByCompany
type GetOfferListByCompanyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the company
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetOfferListByCompanyRequest) Reset() {
	*x = GetOfferListByCompanyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOfferListByCompanyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOfferListByCompanyRequest) ProtoMessage() {}

func (x *GetOfferListByCompanyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOfferListByCompanyRequest.ProtoReflect.Descriptor instead.
func (*GetOfferListByCompanyRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetOfferListByCompanyRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// Response for GetOfferListByCompany
type GetOfferListCompanyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of loan offers.
	Offers []*v1.LoanOfferModel `protobuf:"bytes,1,rep,name=offers,proto3" json:"offers,omitempty"`
}

func (x *GetOfferListCompanyResponse) Reset() {
	*x = GetOfferListCompanyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOfferListCompanyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOfferListCompanyResponse) ProtoMessage() {}

func (x *GetOfferListCompanyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOfferListCompanyResponse.ProtoReflect.Descriptor instead.
func (*GetOfferListCompanyResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetOfferListCompanyResponse) GetOffers() []*v1.LoanOfferModel {
	if x != nil {
		return x.Offers
	}
	return nil
}

// Request for GetOfferByChannel
type GetOfferByChannelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The channel
	ChannelName v1.LoanChannel `protobuf:"varint,1,opt,name=channel_name,json=channelName,proto3,enum=moego.models.capital.v1.LoanChannel" json:"channel_name,omitempty"`
	// The channel ID of the loan offer
	ChannelOfferId string `protobuf:"bytes,2,opt,name=channel_offer_id,json=channelOfferId,proto3" json:"channel_offer_id,omitempty"`
}

func (x *GetOfferByChannelRequest) Reset() {
	*x = GetOfferByChannelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOfferByChannelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOfferByChannelRequest) ProtoMessage() {}

func (x *GetOfferByChannelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOfferByChannelRequest.ProtoReflect.Descriptor instead.
func (*GetOfferByChannelRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetOfferByChannelRequest) GetChannelName() v1.LoanChannel {
	if x != nil {
		return x.ChannelName
	}
	return v1.LoanChannel(0)
}

func (x *GetOfferByChannelRequest) GetChannelOfferId() string {
	if x != nil {
		return x.ChannelOfferId
	}
	return ""
}

// Response for GetOfferByChannel
type GetOfferByChannelResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The loan offer
	Offer *v1.LoanOfferModel `protobuf:"bytes,1,opt,name=offer,proto3" json:"offer,omitempty"`
}

func (x *GetOfferByChannelResponse) Reset() {
	*x = GetOfferByChannelResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOfferByChannelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOfferByChannelResponse) ProtoMessage() {}

func (x *GetOfferByChannelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOfferByChannelResponse.ProtoReflect.Descriptor instead.
func (*GetOfferByChannelResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetOfferByChannelResponse) GetOffer() *v1.LoanOfferModel {
	if x != nil {
		return x.Offer
	}
	return nil
}

// Request for CreateLink
type CreateLinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// link type
	Type CreateLinkRequest_AccountLinkType `protobuf:"varint,1,opt,name=type,proto3,enum=moego.service.capital.v1.CreateLinkRequest_AccountLinkType" json:"type,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The ID of the loan offer
	OfferId *string `protobuf:"bytes,3,opt,name=offer_id,json=offerId,proto3,oneof" json:"offer_id,omitempty"`
	// The URL to redirect to after the user finishes operations at the apply link site. Require to be validated for
	// APPLY_OFFER, ELIGIBILITY_REVIEW and MANUALLY_REPAY.
	ReturnUrl string `protobuf:"bytes,4,opt,name=return_url,json=returnUrl,proto3" json:"return_url,omitempty"`
	// The URL to redirect to if the old link expires. Require to be validated for APPLY_OFFER, ELIGIBILITY_REVIEW and
	// MANUALLY_REPAY.
	RefreshUrl string `protobuf:"bytes,5,opt,name=refresh_url,json=refreshUrl,proto3" json:"refresh_url,omitempty"`
}

func (x *CreateLinkRequest) Reset() {
	*x = CreateLinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLinkRequest) ProtoMessage() {}

func (x *CreateLinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLinkRequest.ProtoReflect.Descriptor instead.
func (*CreateLinkRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{12}
}

func (x *CreateLinkRequest) GetType() CreateLinkRequest_AccountLinkType {
	if x != nil {
		return x.Type
	}
	return CreateLinkRequest_ACCOUNT_LINK_TYPE_UNSPECIFIED
}

func (x *CreateLinkRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateLinkRequest) GetOfferId() string {
	if x != nil && x.OfferId != nil {
		return *x.OfferId
	}
	return ""
}

func (x *CreateLinkRequest) GetReturnUrl() string {
	if x != nil {
		return x.ReturnUrl
	}
	return ""
}

func (x *CreateLinkRequest) GetRefreshUrl() string {
	if x != nil {
		return x.RefreshUrl
	}
	return ""
}

// Response for CreateLink
type CreateLinkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The URL of the apply link
	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	// Time when the URL or token will expire, in second timestamp. 0 if it never expires.
	ExpireAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	// The token to connect to the link
	ConnectToken *string `protobuf:"bytes,3,opt,name=connect_token,json=connectToken,proto3,oneof" json:"connect_token,omitempty"`
}

func (x *CreateLinkResponse) Reset() {
	*x = CreateLinkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLinkResponse) ProtoMessage() {}

func (x *CreateLinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLinkResponse.ProtoReflect.Descriptor instead.
func (*CreateLinkResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{13}
}

func (x *CreateLinkResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *CreateLinkResponse) GetExpireAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireAt
	}
	return nil
}

func (x *CreateLinkResponse) GetConnectToken() string {
	if x != nil && x.ConnectToken != nil {
		return *x.ConnectToken
	}
	return ""
}

// Request for ProcessOfferEvent
type ProcessOfferEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The event type.
	EventType ProcessOfferEventRequest_OfferEventType `protobuf:"varint,1,opt,name=event_type,json=eventType,proto3,enum=moego.service.capital.v1.ProcessOfferEventRequest_OfferEventType" json:"event_type,omitempty"`
	// The loan channel.
	ChannelName v1.LoanChannel `protobuf:"varint,2,opt,name=channel_name,json=channelName,proto3,enum=moego.models.capital.v1.LoanChannel" json:"channel_name,omitempty"`
	// The related updated LoanOffer payload.
	Offer *LoanOfferEventPayload `protobuf:"bytes,3,opt,name=offer,proto3" json:"offer,omitempty"`
}

func (x *ProcessOfferEventRequest) Reset() {
	*x = ProcessOfferEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessOfferEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessOfferEventRequest) ProtoMessage() {}

func (x *ProcessOfferEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessOfferEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessOfferEventRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{14}
}

func (x *ProcessOfferEventRequest) GetEventType() ProcessOfferEventRequest_OfferEventType {
	if x != nil {
		return x.EventType
	}
	return ProcessOfferEventRequest_OFFER_EVENT_TYPE_UNSPECIFIED
}

func (x *ProcessOfferEventRequest) GetChannelName() v1.LoanChannel {
	if x != nil {
		return x.ChannelName
	}
	return v1.LoanChannel(0)
}

func (x *ProcessOfferEventRequest) GetOffer() *LoanOfferEventPayload {
	if x != nil {
		return x.Offer
	}
	return nil
}

// Request for ProcessPrequalificationEvent
type ProcessPrequalificationEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The business ID, given to the channel as a platform-side ID (might be obfuscated).
	PlatformBusinessId string `protobuf:"bytes,1,opt,name=platform_business_id,json=platformBusinessId,proto3" json:"platform_business_id,omitempty"`
	// The loan channel.
	ChannelName v1.LoanChannel `protobuf:"varint,2,opt,name=channel_name,json=channelName,proto3,enum=moego.models.capital.v1.LoanChannel" json:"channel_name,omitempty"`
	// The loan product type.
	ProductType v1.LoanOfferType `protobuf:"varint,3,opt,name=product_type,json=productType,proto3,enum=moego.models.capital.v1.LoanOfferType" json:"product_type,omitempty"`
	// Prequalified or not.
	IsPrequalified bool `protobuf:"varint,4,opt,name=is_prequalified,json=isPrequalified,proto3" json:"is_prequalified,omitempty"`
	// Prequalified amount.
	PrequalifiedAmount *money.Money `protobuf:"bytes,5,opt,name=prequalified_amount,json=prequalifiedAmount,proto3" json:"prequalified_amount,omitempty"`
}

func (x *ProcessPrequalificationEventRequest) Reset() {
	*x = ProcessPrequalificationEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessPrequalificationEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessPrequalificationEventRequest) ProtoMessage() {}

func (x *ProcessPrequalificationEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessPrequalificationEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessPrequalificationEventRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{15}
}

func (x *ProcessPrequalificationEventRequest) GetPlatformBusinessId() string {
	if x != nil {
		return x.PlatformBusinessId
	}
	return ""
}

func (x *ProcessPrequalificationEventRequest) GetChannelName() v1.LoanChannel {
	if x != nil {
		return x.ChannelName
	}
	return v1.LoanChannel(0)
}

func (x *ProcessPrequalificationEventRequest) GetProductType() v1.LoanOfferType {
	if x != nil {
		return x.ProductType
	}
	return v1.LoanOfferType(0)
}

func (x *ProcessPrequalificationEventRequest) GetIsPrequalified() bool {
	if x != nil {
		return x.IsPrequalified
	}
	return false
}

func (x *ProcessPrequalificationEventRequest) GetPrequalifiedAmount() *money.Money {
	if x != nil {
		return x.PrequalifiedAmount
	}
	return nil
}

// Response for ProcessPrequalificationEvent
type ProcessPrequalificationEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ProcessPrequalificationEventResponse) Reset() {
	*x = ProcessPrequalificationEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessPrequalificationEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessPrequalificationEventResponse) ProtoMessage() {}

func (x *ProcessPrequalificationEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessPrequalificationEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessPrequalificationEventResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{16}
}

// Request for ProcessAccountEventRequest
type ProcessAccountEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Event type.
	EventType ProcessAccountEventRequest_AccountEventType `protobuf:"varint,1,opt,name=event_type,json=eventType,proto3,enum=moego.service.capital.v1.ProcessAccountEventRequest_AccountEventType" json:"event_type,omitempty"`
	// The business ID, given to the channel as a platform-side ID (might be obfuscated).
	PlatformBusinessId string `protobuf:"bytes,2,opt,name=platform_business_id,json=platformBusinessId,proto3" json:"platform_business_id,omitempty"`
	// The loan channel.
	ChannelName v1.LoanChannel `protobuf:"varint,3,opt,name=channel_name,json=channelName,proto3,enum=moego.models.capital.v1.LoanChannel" json:"channel_name,omitempty"`
	// Requirements, vary by the channel. Available only for INPUT_REQUIRED.
	Requirements []string `protobuf:"bytes,4,rep,name=requirements,proto3" json:"requirements,omitempty"`
}

func (x *ProcessAccountEventRequest) Reset() {
	*x = ProcessAccountEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessAccountEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessAccountEventRequest) ProtoMessage() {}

func (x *ProcessAccountEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessAccountEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessAccountEventRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{17}
}

func (x *ProcessAccountEventRequest) GetEventType() ProcessAccountEventRequest_AccountEventType {
	if x != nil {
		return x.EventType
	}
	return ProcessAccountEventRequest_ACCOUNT_EVENT_TYPE_UNSPECIFIED
}

func (x *ProcessAccountEventRequest) GetPlatformBusinessId() string {
	if x != nil {
		return x.PlatformBusinessId
	}
	return ""
}

func (x *ProcessAccountEventRequest) GetChannelName() v1.LoanChannel {
	if x != nil {
		return x.ChannelName
	}
	return v1.LoanChannel(0)
}

func (x *ProcessAccountEventRequest) GetRequirements() []string {
	if x != nil {
		return x.Requirements
	}
	return nil
}

// Response for ProcessAccountEventRequest
type ProcessAccountEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ProcessAccountEventResponse) Reset() {
	*x = ProcessAccountEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessAccountEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessAccountEventResponse) ProtoMessage() {}

func (x *ProcessAccountEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessAccountEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessAccountEventResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{18}
}

// Request for SyncOfferAndTransactionList
type SyncOfferAndTransactionListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// offer_id
	OfferId string `protobuf:"bytes,1,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
}

func (x *SyncOfferAndTransactionListRequest) Reset() {
	*x = SyncOfferAndTransactionListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncOfferAndTransactionListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncOfferAndTransactionListRequest) ProtoMessage() {}

func (x *SyncOfferAndTransactionListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncOfferAndTransactionListRequest.ProtoReflect.Descriptor instead.
func (*SyncOfferAndTransactionListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{19}
}

func (x *SyncOfferAndTransactionListRequest) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

// Request for GetRepayments
type GetRepaymentListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The ID of the loan offer
	OfferId string `protobuf:"bytes,2,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	// Pagination query
	Pagination *v2.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetRepaymentListRequest) Reset() {
	*x = GetRepaymentListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentListRequest) ProtoMessage() {}

func (x *GetRepaymentListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentListRequest.ProtoReflect.Descriptor instead.
func (*GetRepaymentListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetRepaymentListRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetRepaymentListRequest) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *GetRepaymentListRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// Response for GetRepayments
type GetRepaymentListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of repayment records.
	Repayments []*v1.LoanOfferRepaymentTransactionModel `protobuf:"bytes,1,rep,name=repayments,proto3" json:"repayments,omitempty"`
	// Pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetRepaymentListResponse) Reset() {
	*x = GetRepaymentListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentListResponse) ProtoMessage() {}

func (x *GetRepaymentListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentListResponse.ProtoReflect.Descriptor instead.
func (*GetRepaymentListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetRepaymentListResponse) GetRepayments() []*v1.LoanOfferRepaymentTransactionModel {
	if x != nil {
		return x.Repayments
	}
	return nil
}

func (x *GetRepaymentListResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// Request for GetRepaymentInterval
type GetRepaymentIntervalsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The ID of the loan offer
	OfferId string `protobuf:"bytes,2,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
}

func (x *GetRepaymentIntervalsRequest) Reset() {
	*x = GetRepaymentIntervalsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentIntervalsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentIntervalsRequest) ProtoMessage() {}

func (x *GetRepaymentIntervalsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentIntervalsRequest.ProtoReflect.Descriptor instead.
func (*GetRepaymentIntervalsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{22}
}

func (x *GetRepaymentIntervalsRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetRepaymentIntervalsRequest) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

// Response for GetRepaymentInterval
type GetRepaymentIntervalsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The intervals of the loan offer
	Intervals []*v1.LoanOfferIntervalModel `protobuf:"bytes,1,rep,name=intervals,proto3" json:"intervals,omitempty"`
}

func (x *GetRepaymentIntervalsResponse) Reset() {
	*x = GetRepaymentIntervalsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepaymentIntervalsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepaymentIntervalsResponse) ProtoMessage() {}

func (x *GetRepaymentIntervalsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepaymentIntervalsResponse.ProtoReflect.Descriptor instead.
func (*GetRepaymentIntervalsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetRepaymentIntervalsResponse) GetIntervals() []*v1.LoanOfferIntervalModel {
	if x != nil {
		return x.Intervals
	}
	return nil
}

// Request for BatchGetRepaymentInterval
type BatchGetRepaymentIntervalsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The ID of the loan offer
	OfferIds []string `protobuf:"bytes,2,rep,name=offer_ids,json=offerIds,proto3" json:"offer_ids,omitempty"`
}

func (x *BatchGetRepaymentIntervalsRequest) Reset() {
	*x = BatchGetRepaymentIntervalsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetRepaymentIntervalsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetRepaymentIntervalsRequest) ProtoMessage() {}

func (x *BatchGetRepaymentIntervalsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetRepaymentIntervalsRequest.ProtoReflect.Descriptor instead.
func (*BatchGetRepaymentIntervalsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{24}
}

func (x *BatchGetRepaymentIntervalsRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchGetRepaymentIntervalsRequest) GetOfferIds() []string {
	if x != nil {
		return x.OfferIds
	}
	return nil
}

// Response for BatchGetBatchRepaymentInterval
type BatchGetRepaymentIntervalsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The intervals of the loan offers
	Intervals []*v1.LoanOfferIntervalModel `protobuf:"bytes,1,rep,name=intervals,proto3" json:"intervals,omitempty"`
}

func (x *BatchGetRepaymentIntervalsResponse) Reset() {
	*x = BatchGetRepaymentIntervalsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetRepaymentIntervalsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetRepaymentIntervalsResponse) ProtoMessage() {}

func (x *BatchGetRepaymentIntervalsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetRepaymentIntervalsResponse.ProtoReflect.Descriptor instead.
func (*BatchGetRepaymentIntervalsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{25}
}

func (x *BatchGetRepaymentIntervalsResponse) GetIntervals() []*v1.LoanOfferIntervalModel {
	if x != nil {
		return x.Intervals
	}
	return nil
}

// Request for GetNotableUpdates
type GetNotableUpdatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// used to get notable info
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetNotableUpdatesRequest) Reset() {
	*x = GetNotableUpdatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNotableUpdatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotableUpdatesRequest) ProtoMessage() {}

func (x *GetNotableUpdatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotableUpdatesRequest.ProtoReflect.Descriptor instead.
func (*GetNotableUpdatesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{26}
}

func (x *GetNotableUpdatesRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// Response for GetNotableUpdates
type GetNotableUpdatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether the user has updates
	HaveUpdates bool `protobuf:"varint,1,opt,name=have_updates,json=haveUpdates,proto3" json:"have_updates,omitempty"`
	// Notable updates for offers
	NotableOfferUpdates []*v1.NotableOfferUpdate `protobuf:"bytes,2,rep,name=notable_offer_updates,json=notableOfferUpdates,proto3" json:"notable_offer_updates,omitempty"`
}

func (x *GetNotableUpdatesResponse) Reset() {
	*x = GetNotableUpdatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNotableUpdatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotableUpdatesResponse) ProtoMessage() {}

func (x *GetNotableUpdatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotableUpdatesResponse.ProtoReflect.Descriptor instead.
func (*GetNotableUpdatesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetNotableUpdatesResponse) GetHaveUpdates() bool {
	if x != nil {
		return x.HaveUpdates
	}
	return false
}

func (x *GetNotableUpdatesResponse) GetNotableOfferUpdates() []*v1.NotableOfferUpdate {
	if x != nil {
		return x.NotableOfferUpdates
	}
	return nil
}

// Request for GetOfferNotableUpdates
type GetOfferNotableUpdatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business ID
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The ID of the offer
	OfferId string `protobuf:"bytes,2,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
}

func (x *GetOfferNotableUpdatesRequest) Reset() {
	*x = GetOfferNotableUpdatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOfferNotableUpdatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOfferNotableUpdatesRequest) ProtoMessage() {}

func (x *GetOfferNotableUpdatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOfferNotableUpdatesRequest.ProtoReflect.Descriptor instead.
func (*GetOfferNotableUpdatesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{28}
}

func (x *GetOfferNotableUpdatesRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetOfferNotableUpdatesRequest) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

// Response for GetOfferNotableUpdates
type GetOfferNotableUpdatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether the user has updates
	HaveUpdates bool `protobuf:"varint,1,opt,name=have_updates,json=haveUpdates,proto3" json:"have_updates,omitempty"`
}

func (x *GetOfferNotableUpdatesResponse) Reset() {
	*x = GetOfferNotableUpdatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOfferNotableUpdatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOfferNotableUpdatesResponse) ProtoMessage() {}

func (x *GetOfferNotableUpdatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOfferNotableUpdatesResponse.ProtoReflect.Descriptor instead.
func (*GetOfferNotableUpdatesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{29}
}

func (x *GetOfferNotableUpdatesResponse) GetHaveUpdates() bool {
	if x != nil {
		return x.HaveUpdates
	}
	return false
}

// Request for DismissNotableUpdates
type DismissNotableUpdatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// used to dismiss  notable info
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *DismissNotableUpdatesRequest) Reset() {
	*x = DismissNotableUpdatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DismissNotableUpdatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DismissNotableUpdatesRequest) ProtoMessage() {}

func (x *DismissNotableUpdatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DismissNotableUpdatesRequest.ProtoReflect.Descriptor instead.
func (*DismissNotableUpdatesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{30}
}

func (x *DismissNotableUpdatesRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// Request for DismissOfferNotableUpdate
type DismissOfferNotableUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// used to dismiss  notable info
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The ID of the loan offer, to dismiss the corresponding offer update
	OfferId *string `protobuf:"bytes,2,opt,name=offer_id,json=offerId,proto3,oneof" json:"offer_id,omitempty"`
}

func (x *DismissOfferNotableUpdateRequest) Reset() {
	*x = DismissOfferNotableUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DismissOfferNotableUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DismissOfferNotableUpdateRequest) ProtoMessage() {}

func (x *DismissOfferNotableUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DismissOfferNotableUpdateRequest.ProtoReflect.Descriptor instead.
func (*DismissOfferNotableUpdateRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{31}
}

func (x *DismissOfferNotableUpdateRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *DismissOfferNotableUpdateRequest) GetOfferId() string {
	if x != nil && x.OfferId != nil {
		return *x.OfferId
	}
	return ""
}

// Response for DismissOfferNotableUpdate
type DismissOfferNotableUpdateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DismissOfferNotableUpdateResponse) Reset() {
	*x = DismissOfferNotableUpdateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DismissOfferNotableUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DismissOfferNotableUpdateResponse) ProtoMessage() {}

func (x *DismissOfferNotableUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DismissOfferNotableUpdateResponse.ProtoReflect.Descriptor instead.
func (*DismissOfferNotableUpdateResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{32}
}

// Request for GetOnboardingStatus
type GetOnboardingStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The channel.
	ChannelName v1.LoanChannel `protobuf:"varint,1,opt,name=channel_name,json=channelName,proto3,enum=moego.models.capital.v1.LoanChannel" json:"channel_name,omitempty"`
	// The ID of the business
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetOnboardingStatusRequest) Reset() {
	*x = GetOnboardingStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnboardingStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnboardingStatusRequest) ProtoMessage() {}

func (x *GetOnboardingStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnboardingStatusRequest.ProtoReflect.Descriptor instead.
func (*GetOnboardingStatusRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{33}
}

func (x *GetOnboardingStatusRequest) GetChannelName() v1.LoanChannel {
	if x != nil {
		return x.ChannelName
	}
	return v1.LoanChannel(0)
}

func (x *GetOnboardingStatusRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// Response for GetOnboardingStatus
type GetOnboardingStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The onboarding status of the business
	OnboardingStatus v1.LoanOnboardingStatus `protobuf:"varint,1,opt,name=onboarding_status,json=onboardingStatus,proto3,enum=moego.models.capital.v1.LoanOnboardingStatus" json:"onboarding_status,omitempty"`
	// The offer type of the business has chosen
	OfferType v1.LoanOfferType `protobuf:"varint,2,opt,name=offer_type,json=offerType,proto3,enum=moego.models.capital.v1.LoanOfferType" json:"offer_type,omitempty"`
}

func (x *GetOnboardingStatusResponse) Reset() {
	*x = GetOnboardingStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnboardingStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnboardingStatusResponse) ProtoMessage() {}

func (x *GetOnboardingStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnboardingStatusResponse.ProtoReflect.Descriptor instead.
func (*GetOnboardingStatusResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{34}
}

func (x *GetOnboardingStatusResponse) GetOnboardingStatus() v1.LoanOnboardingStatus {
	if x != nil {
		return x.OnboardingStatus
	}
	return v1.LoanOnboardingStatus(0)
}

func (x *GetOnboardingStatusResponse) GetOfferType() v1.LoanOfferType {
	if x != nil {
		return x.OfferType
	}
	return v1.LoanOfferType(0)
}

// Request for GetOfferDetail
type GetOfferDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the business
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The ID of the loan offer
	OfferId string `protobuf:"bytes,2,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
}

func (x *GetOfferDetailRequest) Reset() {
	*x = GetOfferDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOfferDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOfferDetailRequest) ProtoMessage() {}

func (x *GetOfferDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOfferDetailRequest.ProtoReflect.Descriptor instead.
func (*GetOfferDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{35}
}

func (x *GetOfferDetailRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetOfferDetailRequest) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

// Response for GetOfferDetail
type GetOfferDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The loan offer
	Offer *v1.LoanOfferModel `protobuf:"bytes,1,opt,name=offer,proto3" json:"offer,omitempty"`
}

func (x *GetOfferDetailResponse) Reset() {
	*x = GetOfferDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOfferDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOfferDetailResponse) ProtoMessage() {}

func (x *GetOfferDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOfferDetailResponse.ProtoReflect.Descriptor instead.
func (*GetOfferDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{36}
}

func (x *GetOfferDetailResponse) GetOffer() *v1.LoanOfferModel {
	if x != nil {
		return x.Offer
	}
	return nil
}

// Request for DeliverPendingMessages
type DeliverPendingMessagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeliverPendingMessagesRequest) Reset() {
	*x = DeliverPendingMessagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliverPendingMessagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliverPendingMessagesRequest) ProtoMessage() {}

func (x *DeliverPendingMessagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliverPendingMessagesRequest.ProtoReflect.Descriptor instead.
func (*DeliverPendingMessagesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{37}
}

// Response for DeliverPendingMessages
type DeliverPendingMessagesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeliverPendingMessagesResponse) Reset() {
	*x = DeliverPendingMessagesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliverPendingMessagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliverPendingMessagesResponse) ProtoMessage() {}

func (x *DeliverPendingMessagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_capital_v1_loan_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliverPendingMessagesResponse.ProtoReflect.Descriptor instead.
func (*DeliverPendingMessagesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_capital_v1_loan_service_proto_rawDescGZIP(), []int{38}
}

var File_moego_service_capital_v1_loan_service_proto protoreflect.FileDescriptor

var file_moego_service_capital_v1_loan_service_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70,
	0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x61, 0x70,
	0x69, 0x74, 0x61, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2f, 0x76,
	0x31, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xf5, 0x06, 0x0a, 0x15, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x47, 0x0a, 0x0c,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61,
	0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x12, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x40, 0x52, 0x10, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x40, 0x52,
	0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x45, 0x0a, 0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f,
	0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x32, 0x00,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x41, 0x0a, 0x09, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2,
	0x01, 0x02, 0x32, 0x00, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x41, 0x74, 0x12, 0x56,
	0x0a, 0x0d, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x54, 0x65, 0x72, 0x6d, 0x73, 0x52, 0x0c, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x65,
	0x64, 0x54, 0x65, 0x72, 0x6d, 0x73, 0x12, 0x5d, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74,
	0x65, 0x64, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63,
	0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x54, 0x65, 0x72, 0x6d,
	0x73, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x54, 0x65, 0x72,
	0x6d, 0x73, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74,
	0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0xc2, 0x01, 0x0a, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x9a, 0x01, 0xfa, 0x42,
	0x96, 0x01, 0x72, 0x93, 0x01, 0x18, 0x14, 0x52, 0x00, 0x52, 0x07, 0x43, 0x55, 0x52, 0x52, 0x45,
	0x4e, 0x54, 0x52, 0x04, 0x4c, 0x41, 0x54, 0x45, 0x52, 0x0a, 0x46, 0x55, 0x4c, 0x4c, 0x59, 0x5f,
	0x50, 0x41, 0x49, 0x44, 0x52, 0x06, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x52, 0x09, 0x44, 0x45,
	0x46, 0x41, 0x55, 0x4c, 0x54, 0x45, 0x44, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x65, 0x64, 0x52, 0x08, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x52, 0x08, 0x63, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64, 0x52, 0x07, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x0c, 0x66, 0x75, 0x6c, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x70, 0x61, 0x69, 0x64, 0x52, 0x08, 0x70,
	0x61, 0x69, 0x64, 0x5f, 0x6f, 0x75, 0x74, 0x52, 0x08, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x64, 0x52, 0x0b, 0x75, 0x6e, 0x64,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x65, 0x64, 0x52, 0x0d, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x63, 0x63, 0x65,
	0x70, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x22, 0xa9, 0x05, 0x0a, 0x17, 0x4c,
	0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x61,
	0x6e, 0x54, 0x65, 0x72, 0x6d, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d,
	0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x53, 0x0a,
	0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x6f, 0x61, 0x6e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x48,
	0x00, 0x52, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x66, 0x65, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x09, 0x66, 0x65, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x53, 0x0a,
	0x24, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63,
	0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x48, 0x01, 0x52, 0x20, 0x70,
	0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67,
	0x46, 0x65, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x23, 0x0a, 0x0d, 0x77, 0x69, 0x74, 0x68, 0x68, 0x6f, 0x6c, 0x64, 0x5f, 0x72,
	0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x77, 0x69, 0x74, 0x68, 0x68,
	0x6f, 0x6c, 0x64, 0x52, 0x61, 0x74, 0x65, 0x12, 0x57, 0x0a, 0x26, 0x70, 0x72, 0x65, 0x76, 0x69,
	0x6f, 0x75, 0x73, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65,
	0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x48, 0x02, 0x52, 0x22, 0x70, 0x72, 0x65, 0x76, 0x69,
	0x6f, 0x75, 0x73, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x33, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x61, 0x74,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x52, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x0f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0e, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x37, 0x0a, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x65, 0x73, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01,
	0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x52, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x42, 0x27, 0x0a, 0x25, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f,
	0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x42, 0x29, 0x0a, 0x27, 0x5f,
	0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69,
	0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x45, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61,
	0x6e, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x99, 0x01,
	0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x69, 0x73, 0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x69, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x5a, 0x0a,
	0x11, 0x69, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c,
	0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x10, 0x69, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x6c, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x4a, 0x0a, 0x1e, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x61, 0x6e, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x46,
	0x6c, 0x61, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0xa5, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61,
	0x6e, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x46, 0x6c, 0x61, 0x67,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x69, 0x73, 0x5f,
	0x6d, 0x67, 0x70, 0x5f, 0x73, 0x65, 0x74, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0a, 0x69, 0x73, 0x4d, 0x67, 0x70, 0x53, 0x65, 0x74, 0x75, 0x70, 0x12, 0x24, 0x0a, 0x0e, 0x69,
	0x73, 0x5f, 0x6d, 0x67, 0x70, 0x5f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x4d, 0x67, 0x70, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72,
	0x79, 0x12, 0x3a, 0x0a, 0x1a, 0x69, 0x73, 0x5f, 0x6d, 0x67, 0x70, 0x5f, 0x70, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x5f, 0x6c, 0x6f, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x69, 0x73, 0x4d, 0x67, 0x70, 0x50, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x4c, 0x6f, 0x6e, 0x67, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x22, 0x91, 0x01,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x45, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63,
	0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x57, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x06, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x22, 0x46, 0x0a, 0x1c, 0x47, 0x65,
	0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x22, 0x5e, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x3f, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x22, 0x98, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x42,
	0x79, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x0b, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x10, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x0e, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x22, 0x5a, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x42, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x05, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x05, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x22, 0xa2, 0x03, 0x0a, 0x11, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x5b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x72, 0x07, 0x10,
	0x01, 0x18, 0x40, 0xd0, 0x01, 0x01, 0x48, 0x00, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x0a, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0xd0,
	0x01, 0x01, 0x88, 0x01, 0x01, 0x52, 0x09, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x55, 0x72, 0x6c,
	0x12, 0x2c, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0xd0, 0x01, 0x01, 0x88,
	0x01, 0x01, 0x52, 0x0a, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x55, 0x72, 0x6c, 0x22, 0x71,
	0x0a, 0x0f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4c, 0x49, 0x4e,
	0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x50, 0x50, 0x4c, 0x59, 0x5f, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x02, 0x12, 0x12, 0x0a,
	0x0e, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x52, 0x45, 0x50, 0x41, 0x59, 0x10,
	0x03, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x22, 0xbb,
	0x01, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0xd0, 0x01, 0x01, 0x88, 0x01, 0x01, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x12, 0x41, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x32, 0x00, 0x52, 0x08, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x41, 0x74, 0x12, 0x31, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xc3, 0x03, 0x0a,
	0x18, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x6c, 0x0a, 0x0a, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x09, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70,
	0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x45, 0x0a, 0x05, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x52, 0x05, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x22, 0xa8, 0x01, 0x0a, 0x0e, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x46,
	0x46, 0x45, 0x52, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x43, 0x43,
	0x45, 0x50, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x41, 0x49, 0x44, 0x5f,
	0x4f, 0x55, 0x54, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x55, 0x4c, 0x4c, 0x59, 0x5f, 0x52,
	0x45, 0x50, 0x41, 0x49, 0x44, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x4e, 0x43, 0x45,
	0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x10, 0x06, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x07,
	0x12, 0x10, 0x0a, 0x0c, 0x50, 0x52, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x08, 0x22, 0xfa, 0x02, 0x0a, 0x23, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x72,
	0x65, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x14, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x12, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74,
	0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0b, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x55, 0x0a, 0x0c, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x50, 0x72,
	0x65, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x43, 0x0a, 0x13, 0x70, 0x72,
	0x65, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x12, 0x70, 0x72, 0x65,
	0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x26, 0x0a, 0x24, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x72, 0x65, 0x71, 0x75, 0x61,
	0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xaa, 0x03, 0x0a, 0x1a, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x70, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74,
	0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x09, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x14, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x12, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0b, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x66, 0x0a, 0x10,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x22, 0x0a, 0x1e, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x45, 0x56, 0x45, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x45, 0x44, 0x10, 0x02,
	0x12, 0x12, 0x0a, 0x0e, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52,
	0x45, 0x44, 0x10, 0x03, 0x22, 0x1d, 0x0a, 0x1b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x4a, 0x0a, 0x22, 0x53, 0x79, 0x6e, 0x63, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x41, 0x6e, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x08, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x22,
	0xac, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x40, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xbb,
	0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x0a, 0x72,
	0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63,
	0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x72, 0x65,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x6e, 0x0a, 0x1c,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x40, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x22, 0x6e, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a,
	0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x73, 0x22, 0x6a, 0x0a, 0x21,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0x73, 0x0a, 0x22, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d,
	0x0a, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x73, 0x22, 0x44, 0x0a,
	0x18, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x22, 0x9f, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x61, 0x76, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x68, 0x61, 0x76, 0x65, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x73, 0x12, 0x5f, 0x0a, 0x15, 0x6e, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x13, 0x6e, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x73, 0x22, 0x6f, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x4e, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x24, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x07, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x22, 0x43, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x4e, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x61, 0x76, 0x65,
	0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x68, 0x61, 0x76, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x22, 0x48, 0x0a, 0x1c, 0x44,
	0x69, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x4e, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x84, 0x01, 0x0a, 0x20, 0x44, 0x69, 0x73, 0x6d, 0x69, 0x73,
	0x73, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x40, 0x48, 0x00, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42,
	0x0b, 0x0a, 0x09, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x22, 0x23, 0x0a, 0x21,
	0x44, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x8f, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x0b, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x22, 0xc0, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x11, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61,
	0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x45, 0x0a, 0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f,
	0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0x67, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x08, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x22,
	0x57, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x05, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x05, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x22, 0x1f, 0x0a, 0x1d, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x20, 0x0a, 0x1e, 0x44, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xb0, 0x17, 0x0a, 0x0b,
	0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5f, 0x0a, 0x11, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x9d, 0x01, 0x0a,
	0x1c, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x72, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x3d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x50, 0x72, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70,
	0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50,
	0x72, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x82, 0x01, 0x0a,
	0x13, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74,
	0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x8e, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x45, 0x6c, 0x69,
	0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e,
	0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x46, 0x6c, 0x61, 0x67, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x7f, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x45, 0x6c, 0x69,
	0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x45, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e,
	0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x6d, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70,
	0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x42, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x42, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x42, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a, 0x0a, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x73, 0x0a, 0x1b, 0x53, 0x79, 0x6e, 0x63, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x41,
	0x6e, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e,
	0x63, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x6e, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x79, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69,
	0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63,
	0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x73, 0x12, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70,
	0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x97, 0x01,
	0x0a, 0x1a, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x73, 0x12, 0x3b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70,
	0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61,
	0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4e, 0x6f,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70,
	0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4e,
	0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a, 0x15, 0x44, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73,
	0x4e, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x36,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63,
	0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x6d, 0x69, 0x73,
	0x73, 0x4e, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x94,
	0x01, 0x0a, 0x19, 0x44, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4e,
	0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x3a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70,
	0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x4e, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x0e, 0x47, 0x65,
	0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70,
	0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61,
	0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x8b, 0x01, 0x0a, 0x12, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x53, 0x70, 0x6c, 0x69, 0x74,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x53,
	0x70, 0x6c, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa0, 0x01,
	0x0a, 0x19, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65,
	0x53, 0x70, 0x6c, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x40, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x70, 0x6c, 0x69,
	0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x53, 0x70, 0x6c, 0x69, 0x74,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x70,
	0x6c, 0x69, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x53, 0x70, 0x6c,
	0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x3e, 0x0a, 0x0c, 0x53, 0x61, 0x76, 0x65, 0x41, 0x6c, 0x6c, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x12, 0x50, 0x0a, 0x1e, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x6c, 0x6c, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x41, 0x6e, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x12, 0x4c, 0x0a, 0x1a, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x12, 0x8b, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x50, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69,
	0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x50, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x80,
	0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61,
	0x6c, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x73, 0x76, 0x63, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_capital_v1_loan_service_proto_rawDescOnce sync.Once
	file_moego_service_capital_v1_loan_service_proto_rawDescData = file_moego_service_capital_v1_loan_service_proto_rawDesc
)

func file_moego_service_capital_v1_loan_service_proto_rawDescGZIP() []byte {
	file_moego_service_capital_v1_loan_service_proto_rawDescOnce.Do(func() {
		file_moego_service_capital_v1_loan_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_capital_v1_loan_service_proto_rawDescData)
	})
	return file_moego_service_capital_v1_loan_service_proto_rawDescData
}

var file_moego_service_capital_v1_loan_service_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_service_capital_v1_loan_service_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_moego_service_capital_v1_loan_service_proto_goTypes = []interface{}{
	(CreateLinkRequest_AccountLinkType)(0),           // 0: moego.service.capital.v1.CreateLinkRequest.AccountLinkType
	(ProcessOfferEventRequest_OfferEventType)(0),     // 1: moego.service.capital.v1.ProcessOfferEventRequest.OfferEventType
	(ProcessAccountEventRequest_AccountEventType)(0), // 2: moego.service.capital.v1.ProcessAccountEventRequest.AccountEventType
	(*LoanOfferEventPayload)(nil),                    // 3: moego.service.capital.v1.LoanOfferEventPayload
	(*LoanOfferEventLoanTerms)(nil),                  // 4: moego.service.capital.v1.LoanOfferEventLoanTerms
	(*GetLoanEligibilityRequest)(nil),                // 5: moego.service.capital.v1.GetLoanEligibilityRequest
	(*GetLoanEligibilityResponse)(nil),               // 6: moego.service.capital.v1.GetLoanEligibilityResponse
	(*GetLoanEligibilityFlagsRequest)(nil),           // 7: moego.service.capital.v1.GetLoanEligibilityFlagsRequest
	(*GetLoanEligibilityFlagsResponse)(nil),          // 8: moego.service.capital.v1.GetLoanEligibilityFlagsResponse
	(*GetOfferListRequest)(nil),                      // 9: moego.service.capital.v1.GetOfferListRequest
	(*GetOfferListResponse)(nil),                     // 10: moego.service.capital.v1.GetOfferListResponse
	(*GetOfferListByCompanyRequest)(nil),             // 11: moego.service.capital.v1.GetOfferListByCompanyRequest
	(*GetOfferListCompanyResponse)(nil),              // 12: moego.service.capital.v1.GetOfferListCompanyResponse
	(*GetOfferByChannelRequest)(nil),                 // 13: moego.service.capital.v1.GetOfferByChannelRequest
	(*GetOfferByChannelResponse)(nil),                // 14: moego.service.capital.v1.GetOfferByChannelResponse
	(*CreateLinkRequest)(nil),                        // 15: moego.service.capital.v1.CreateLinkRequest
	(*CreateLinkResponse)(nil),                       // 16: moego.service.capital.v1.CreateLinkResponse
	(*ProcessOfferEventRequest)(nil),                 // 17: moego.service.capital.v1.ProcessOfferEventRequest
	(*ProcessPrequalificationEventRequest)(nil),      // 18: moego.service.capital.v1.ProcessPrequalificationEventRequest
	(*ProcessPrequalificationEventResponse)(nil),     // 19: moego.service.capital.v1.ProcessPrequalificationEventResponse
	(*ProcessAccountEventRequest)(nil),               // 20: moego.service.capital.v1.ProcessAccountEventRequest
	(*ProcessAccountEventResponse)(nil),              // 21: moego.service.capital.v1.ProcessAccountEventResponse
	(*SyncOfferAndTransactionListRequest)(nil),       // 22: moego.service.capital.v1.SyncOfferAndTransactionListRequest
	(*GetRepaymentListRequest)(nil),                  // 23: moego.service.capital.v1.GetRepaymentListRequest
	(*GetRepaymentListResponse)(nil),                 // 24: moego.service.capital.v1.GetRepaymentListResponse
	(*GetRepaymentIntervalsRequest)(nil),             // 25: moego.service.capital.v1.GetRepaymentIntervalsRequest
	(*GetRepaymentIntervalsResponse)(nil),            // 26: moego.service.capital.v1.GetRepaymentIntervalsResponse
	(*BatchGetRepaymentIntervalsRequest)(nil),        // 27: moego.service.capital.v1.BatchGetRepaymentIntervalsRequest
	(*BatchGetRepaymentIntervalsResponse)(nil),       // 28: moego.service.capital.v1.BatchGetRepaymentIntervalsResponse
	(*GetNotableUpdatesRequest)(nil),                 // 29: moego.service.capital.v1.GetNotableUpdatesRequest
	(*GetNotableUpdatesResponse)(nil),                // 30: moego.service.capital.v1.GetNotableUpdatesResponse
	(*GetOfferNotableUpdatesRequest)(nil),            // 31: moego.service.capital.v1.GetOfferNotableUpdatesRequest
	(*GetOfferNotableUpdatesResponse)(nil),           // 32: moego.service.capital.v1.GetOfferNotableUpdatesResponse
	(*DismissNotableUpdatesRequest)(nil),             // 33: moego.service.capital.v1.DismissNotableUpdatesRequest
	(*DismissOfferNotableUpdateRequest)(nil),         // 34: moego.service.capital.v1.DismissOfferNotableUpdateRequest
	(*DismissOfferNotableUpdateResponse)(nil),        // 35: moego.service.capital.v1.DismissOfferNotableUpdateResponse
	(*GetOnboardingStatusRequest)(nil),               // 36: moego.service.capital.v1.GetOnboardingStatusRequest
	(*GetOnboardingStatusResponse)(nil),              // 37: moego.service.capital.v1.GetOnboardingStatusResponse
	(*GetOfferDetailRequest)(nil),                    // 38: moego.service.capital.v1.GetOfferDetailRequest
	(*GetOfferDetailResponse)(nil),                   // 39: moego.service.capital.v1.GetOfferDetailResponse
	(*DeliverPendingMessagesRequest)(nil),            // 40: moego.service.capital.v1.DeliverPendingMessagesRequest
	(*DeliverPendingMessagesResponse)(nil),           // 41: moego.service.capital.v1.DeliverPendingMessagesResponse
	(v1.LoanChannel)(0),                              // 42: moego.models.capital.v1.LoanChannel
	(v1.LoanOfferType)(0),                            // 43: moego.models.capital.v1.LoanOfferType
	(*timestamppb.Timestamp)(nil),                    // 44: google.protobuf.Timestamp
	(v1.LoanProductType)(0),                          // 45: moego.models.capital.v1.LoanProductType
	(v1.LoanCampaignType)(0),                         // 46: moego.models.capital.v1.LoanCampaignType
	(v1.LoanIneligibleReason)(0),                     // 47: moego.models.capital.v1.LoanIneligibleReason
	(v1.LoanOfferStatus)(0),                          // 48: moego.models.capital.v1.LoanOfferStatus
	(*v1.LoanOfferModel)(nil),                        // 49: moego.models.capital.v1.LoanOfferModel
	(*money.Money)(nil),                              // 50: google.type.Money
	(*v2.PaginationRequest)(nil),                     // 51: moego.utils.v2.PaginationRequest
	(*v1.LoanOfferRepaymentTransactionModel)(nil),    // 52: moego.models.capital.v1.LoanOfferRepaymentTransactionModel
	(*v2.PaginationResponse)(nil),                    // 53: moego.utils.v2.PaginationResponse
	(*v1.LoanOfferIntervalModel)(nil),                // 54: moego.models.capital.v1.LoanOfferIntervalModel
	(*v1.NotableOfferUpdate)(nil),                    // 55: moego.models.capital.v1.NotableOfferUpdate
	(v1.LoanOnboardingStatus)(0),                     // 56: moego.models.capital.v1.LoanOnboardingStatus
	(*v11.AcquireSplitAmountRequest)(nil),            // 57: moego.service.split_payment.v1.AcquireSplitAmountRequest
	(*v11.AcquireReverseSplitAmountRequest)(nil),     // 58: moego.service.split_payment.v1.AcquireReverseSplitAmountRequest
	(*emptypb.Empty)(nil),                            // 59: google.protobuf.Empty
	(*v11.AcquireSplitAmountResponse)(nil),           // 60: moego.service.split_payment.v1.AcquireSplitAmountResponse
	(*v11.AcquireReverseSplitAmountResponse)(nil),    // 61: moego.service.split_payment.v1.AcquireReverseSplitAmountResponse
}
var file_moego_service_capital_v1_loan_service_proto_depIdxs = []int32{
	42, // 0: moego.service.capital.v1.LoanOfferEventPayload.channel_name:type_name -> moego.models.capital.v1.LoanChannel
	43, // 1: moego.service.capital.v1.LoanOfferEventPayload.offer_type:type_name -> moego.models.capital.v1.LoanOfferType
	44, // 2: moego.service.capital.v1.LoanOfferEventPayload.created_at:type_name -> google.protobuf.Timestamp
	44, // 3: moego.service.capital.v1.LoanOfferEventPayload.expire_at:type_name -> google.protobuf.Timestamp
	4,  // 4: moego.service.capital.v1.LoanOfferEventPayload.offered_terms:type_name -> moego.service.capital.v1.LoanOfferEventLoanTerms
	4,  // 5: moego.service.capital.v1.LoanOfferEventPayload.accepted_terms:type_name -> moego.service.capital.v1.LoanOfferEventLoanTerms
	45, // 6: moego.service.capital.v1.LoanOfferEventPayload.product_type:type_name -> moego.models.capital.v1.LoanProductType
	46, // 7: moego.service.capital.v1.LoanOfferEventLoanTerms.campaign_type:type_name -> moego.models.capital.v1.LoanCampaignType
	47, // 8: moego.service.capital.v1.GetLoanEligibilityResponse.ineligible_reason:type_name -> moego.models.capital.v1.LoanIneligibleReason
	48, // 9: moego.service.capital.v1.GetOfferListRequest.status:type_name -> moego.models.capital.v1.LoanOfferStatus
	49, // 10: moego.service.capital.v1.GetOfferListResponse.offers:type_name -> moego.models.capital.v1.LoanOfferModel
	49, // 11: moego.service.capital.v1.GetOfferListCompanyResponse.offers:type_name -> moego.models.capital.v1.LoanOfferModel
	42, // 12: moego.service.capital.v1.GetOfferByChannelRequest.channel_name:type_name -> moego.models.capital.v1.LoanChannel
	49, // 13: moego.service.capital.v1.GetOfferByChannelResponse.offer:type_name -> moego.models.capital.v1.LoanOfferModel
	0,  // 14: moego.service.capital.v1.CreateLinkRequest.type:type_name -> moego.service.capital.v1.CreateLinkRequest.AccountLinkType
	44, // 15: moego.service.capital.v1.CreateLinkResponse.expire_at:type_name -> google.protobuf.Timestamp
	1,  // 16: moego.service.capital.v1.ProcessOfferEventRequest.event_type:type_name -> moego.service.capital.v1.ProcessOfferEventRequest.OfferEventType
	42, // 17: moego.service.capital.v1.ProcessOfferEventRequest.channel_name:type_name -> moego.models.capital.v1.LoanChannel
	3,  // 18: moego.service.capital.v1.ProcessOfferEventRequest.offer:type_name -> moego.service.capital.v1.LoanOfferEventPayload
	42, // 19: moego.service.capital.v1.ProcessPrequalificationEventRequest.channel_name:type_name -> moego.models.capital.v1.LoanChannel
	43, // 20: moego.service.capital.v1.ProcessPrequalificationEventRequest.product_type:type_name -> moego.models.capital.v1.LoanOfferType
	50, // 21: moego.service.capital.v1.ProcessPrequalificationEventRequest.prequalified_amount:type_name -> google.type.Money
	2,  // 22: moego.service.capital.v1.ProcessAccountEventRequest.event_type:type_name -> moego.service.capital.v1.ProcessAccountEventRequest.AccountEventType
	42, // 23: moego.service.capital.v1.ProcessAccountEventRequest.channel_name:type_name -> moego.models.capital.v1.LoanChannel
	51, // 24: moego.service.capital.v1.GetRepaymentListRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	52, // 25: moego.service.capital.v1.GetRepaymentListResponse.repayments:type_name -> moego.models.capital.v1.LoanOfferRepaymentTransactionModel
	53, // 26: moego.service.capital.v1.GetRepaymentListResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	54, // 27: moego.service.capital.v1.GetRepaymentIntervalsResponse.intervals:type_name -> moego.models.capital.v1.LoanOfferIntervalModel
	54, // 28: moego.service.capital.v1.BatchGetRepaymentIntervalsResponse.intervals:type_name -> moego.models.capital.v1.LoanOfferIntervalModel
	55, // 29: moego.service.capital.v1.GetNotableUpdatesResponse.notable_offer_updates:type_name -> moego.models.capital.v1.NotableOfferUpdate
	42, // 30: moego.service.capital.v1.GetOnboardingStatusRequest.channel_name:type_name -> moego.models.capital.v1.LoanChannel
	56, // 31: moego.service.capital.v1.GetOnboardingStatusResponse.onboarding_status:type_name -> moego.models.capital.v1.LoanOnboardingStatus
	43, // 32: moego.service.capital.v1.GetOnboardingStatusResponse.offer_type:type_name -> moego.models.capital.v1.LoanOfferType
	49, // 33: moego.service.capital.v1.GetOfferDetailResponse.offer:type_name -> moego.models.capital.v1.LoanOfferModel
	17, // 34: moego.service.capital.v1.LoanService.ProcessOfferEvent:input_type -> moego.service.capital.v1.ProcessOfferEventRequest
	18, // 35: moego.service.capital.v1.LoanService.ProcessPrequalificationEvent:input_type -> moego.service.capital.v1.ProcessPrequalificationEventRequest
	20, // 36: moego.service.capital.v1.LoanService.ProcessAccountEvent:input_type -> moego.service.capital.v1.ProcessAccountEventRequest
	7,  // 37: moego.service.capital.v1.LoanService.GetLoanEligibilityFlags:input_type -> moego.service.capital.v1.GetLoanEligibilityFlagsRequest
	5,  // 38: moego.service.capital.v1.LoanService.GetLoanEligibility:input_type -> moego.service.capital.v1.GetLoanEligibilityRequest
	9,  // 39: moego.service.capital.v1.LoanService.GetOfferList:input_type -> moego.service.capital.v1.GetOfferListRequest
	11, // 40: moego.service.capital.v1.LoanService.GetOfferListByCompany:input_type -> moego.service.capital.v1.GetOfferListByCompanyRequest
	13, // 41: moego.service.capital.v1.LoanService.GetOfferByChannel:input_type -> moego.service.capital.v1.GetOfferByChannelRequest
	15, // 42: moego.service.capital.v1.LoanService.CreateLink:input_type -> moego.service.capital.v1.CreateLinkRequest
	22, // 43: moego.service.capital.v1.LoanService.SyncOfferAndTransactionList:input_type -> moego.service.capital.v1.SyncOfferAndTransactionListRequest
	23, // 44: moego.service.capital.v1.LoanService.GetRepaymentList:input_type -> moego.service.capital.v1.GetRepaymentListRequest
	25, // 45: moego.service.capital.v1.LoanService.GetRepaymentIntervals:input_type -> moego.service.capital.v1.GetRepaymentIntervalsRequest
	27, // 46: moego.service.capital.v1.LoanService.BatchGetRepaymentIntervals:input_type -> moego.service.capital.v1.BatchGetRepaymentIntervalsRequest
	29, // 47: moego.service.capital.v1.LoanService.GetNotableUpdates:input_type -> moego.service.capital.v1.GetNotableUpdatesRequest
	33, // 48: moego.service.capital.v1.LoanService.DismissNotableUpdates:input_type -> moego.service.capital.v1.DismissNotableUpdatesRequest
	34, // 49: moego.service.capital.v1.LoanService.DismissOfferNotableUpdate:input_type -> moego.service.capital.v1.DismissOfferNotableUpdateRequest
	36, // 50: moego.service.capital.v1.LoanService.GetOnboardingStatus:input_type -> moego.service.capital.v1.GetOnboardingStatusRequest
	38, // 51: moego.service.capital.v1.LoanService.GetOfferDetail:input_type -> moego.service.capital.v1.GetOfferDetailRequest
	57, // 52: moego.service.capital.v1.LoanService.AcquireSplitAmount:input_type -> moego.service.split_payment.v1.AcquireSplitAmountRequest
	58, // 53: moego.service.capital.v1.LoanService.AcquireReverseSplitAmount:input_type -> moego.service.split_payment.v1.AcquireReverseSplitAmountRequest
	59, // 54: moego.service.capital.v1.LoanService.SaveAllOffer:input_type -> google.protobuf.Empty
	59, // 55: moego.service.capital.v1.LoanService.SyncAllOfferAndTransactionList:input_type -> google.protobuf.Empty
	59, // 56: moego.service.capital.v1.LoanService.EvaluateAccountEligibility:input_type -> google.protobuf.Empty
	40, // 57: moego.service.capital.v1.LoanService.DeliverPendingMessages:input_type -> moego.service.capital.v1.DeliverPendingMessagesRequest
	59, // 58: moego.service.capital.v1.LoanService.ProcessOfferEvent:output_type -> google.protobuf.Empty
	19, // 59: moego.service.capital.v1.LoanService.ProcessPrequalificationEvent:output_type -> moego.service.capital.v1.ProcessPrequalificationEventResponse
	21, // 60: moego.service.capital.v1.LoanService.ProcessAccountEvent:output_type -> moego.service.capital.v1.ProcessAccountEventResponse
	8,  // 61: moego.service.capital.v1.LoanService.GetLoanEligibilityFlags:output_type -> moego.service.capital.v1.GetLoanEligibilityFlagsResponse
	6,  // 62: moego.service.capital.v1.LoanService.GetLoanEligibility:output_type -> moego.service.capital.v1.GetLoanEligibilityResponse
	10, // 63: moego.service.capital.v1.LoanService.GetOfferList:output_type -> moego.service.capital.v1.GetOfferListResponse
	12, // 64: moego.service.capital.v1.LoanService.GetOfferListByCompany:output_type -> moego.service.capital.v1.GetOfferListCompanyResponse
	14, // 65: moego.service.capital.v1.LoanService.GetOfferByChannel:output_type -> moego.service.capital.v1.GetOfferByChannelResponse
	16, // 66: moego.service.capital.v1.LoanService.CreateLink:output_type -> moego.service.capital.v1.CreateLinkResponse
	59, // 67: moego.service.capital.v1.LoanService.SyncOfferAndTransactionList:output_type -> google.protobuf.Empty
	24, // 68: moego.service.capital.v1.LoanService.GetRepaymentList:output_type -> moego.service.capital.v1.GetRepaymentListResponse
	26, // 69: moego.service.capital.v1.LoanService.GetRepaymentIntervals:output_type -> moego.service.capital.v1.GetRepaymentIntervalsResponse
	28, // 70: moego.service.capital.v1.LoanService.BatchGetRepaymentIntervals:output_type -> moego.service.capital.v1.BatchGetRepaymentIntervalsResponse
	30, // 71: moego.service.capital.v1.LoanService.GetNotableUpdates:output_type -> moego.service.capital.v1.GetNotableUpdatesResponse
	59, // 72: moego.service.capital.v1.LoanService.DismissNotableUpdates:output_type -> google.protobuf.Empty
	35, // 73: moego.service.capital.v1.LoanService.DismissOfferNotableUpdate:output_type -> moego.service.capital.v1.DismissOfferNotableUpdateResponse
	37, // 74: moego.service.capital.v1.LoanService.GetOnboardingStatus:output_type -> moego.service.capital.v1.GetOnboardingStatusResponse
	39, // 75: moego.service.capital.v1.LoanService.GetOfferDetail:output_type -> moego.service.capital.v1.GetOfferDetailResponse
	60, // 76: moego.service.capital.v1.LoanService.AcquireSplitAmount:output_type -> moego.service.split_payment.v1.AcquireSplitAmountResponse
	61, // 77: moego.service.capital.v1.LoanService.AcquireReverseSplitAmount:output_type -> moego.service.split_payment.v1.AcquireReverseSplitAmountResponse
	59, // 78: moego.service.capital.v1.LoanService.SaveAllOffer:output_type -> google.protobuf.Empty
	59, // 79: moego.service.capital.v1.LoanService.SyncAllOfferAndTransactionList:output_type -> google.protobuf.Empty
	59, // 80: moego.service.capital.v1.LoanService.EvaluateAccountEligibility:output_type -> google.protobuf.Empty
	41, // 81: moego.service.capital.v1.LoanService.DeliverPendingMessages:output_type -> moego.service.capital.v1.DeliverPendingMessagesResponse
	58, // [58:82] is the sub-list for method output_type
	34, // [34:58] is the sub-list for method input_type
	34, // [34:34] is the sub-list for extension type_name
	34, // [34:34] is the sub-list for extension extendee
	0,  // [0:34] is the sub-list for field type_name
}

func init() { file_moego_service_capital_v1_loan_service_proto_init() }
func file_moego_service_capital_v1_loan_service_proto_init() {
	if File_moego_service_capital_v1_loan_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_capital_v1_loan_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanOfferEventPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanOfferEventLoanTerms); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanEligibilityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanEligibilityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanEligibilityFlagsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanEligibilityFlagsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOfferListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOfferListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOfferListByCompanyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOfferListCompanyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOfferByChannelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOfferByChannelResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLinkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessOfferEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessPrequalificationEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessPrequalificationEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessAccountEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessAccountEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncOfferAndTransactionListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentIntervalsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepaymentIntervalsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetRepaymentIntervalsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetRepaymentIntervalsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNotableUpdatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNotableUpdatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOfferNotableUpdatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOfferNotableUpdatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DismissNotableUpdatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DismissOfferNotableUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DismissOfferNotableUpdateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnboardingStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnboardingStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOfferDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOfferDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliverPendingMessagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_capital_v1_loan_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliverPendingMessagesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_capital_v1_loan_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_capital_v1_loan_service_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_service_capital_v1_loan_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_service_capital_v1_loan_service_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_service_capital_v1_loan_service_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_moego_service_capital_v1_loan_service_proto_msgTypes[31].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_capital_v1_loan_service_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_capital_v1_loan_service_proto_goTypes,
		DependencyIndexes: file_moego_service_capital_v1_loan_service_proto_depIdxs,
		EnumInfos:         file_moego_service_capital_v1_loan_service_proto_enumTypes,
		MessageInfos:      file_moego_service_capital_v1_loan_service_proto_msgTypes,
	}.Build()
	File_moego_service_capital_v1_loan_service_proto = out.File
	file_moego_service_capital_v1_loan_service_proto_rawDesc = nil
	file_moego_service_capital_v1_loan_service_proto_goTypes = nil
	file_moego_service_capital_v1_loan_service_proto_depIdxs = nil
}
