// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/file/v2/file_search_service.proto

package filesvcpb

import (
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/file/v2"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// search file request
type DescribeFilesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// creator id
	CreatorIds []int64 `protobuf:"varint,1,rep,packed,name=creator_ids,json=creatorIds,proto3" json:"creator_ids,omitempty"`
	// business id
	BusinessIds []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// owner type
	OwnerTypeLike *string `protobuf:"bytes,3,opt,name=owner_type_like,json=ownerTypeLike,proto3,oneof" json:"owner_type_like,omitempty"`
	// usage
	UsageLike *string `protobuf:"bytes,4,opt,name=usage_like,json=usageLike,proto3,oneof" json:"usage_like,omitempty"`
	// id
	Ids []int64 `protobuf:"varint,5,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// default not include
	IncludeDeleted *bool `protobuf:"varint,6,opt,name=include_deleted,json=includeDeleted,proto3,oneof" json:"include_deleted,omitempty"`
	// owner ids
	OwnerIds []int64 `protobuf:"varint,7,rep,packed,name=owner_ids,json=ownerIds,proto3" json:"owner_ids,omitempty"`
	// company ids
	CompanyIds []int64 `protobuf:"varint,8,rep,packed,name=company_ids,json=companyIds,proto3" json:"company_ids,omitempty"`
	// order by
	OrderBy *v2.OrderBy `protobuf:"bytes,14,opt,name=order_by,json=orderBy,proto3,oneof" json:"order_by,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *DescribeFilesRequest) Reset() {
	*x = DescribeFilesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_search_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeFilesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeFilesRequest) ProtoMessage() {}

func (x *DescribeFilesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_search_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeFilesRequest.ProtoReflect.Descriptor instead.
func (*DescribeFilesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_search_service_proto_rawDescGZIP(), []int{0}
}

func (x *DescribeFilesRequest) GetCreatorIds() []int64 {
	if x != nil {
		return x.CreatorIds
	}
	return nil
}

func (x *DescribeFilesRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *DescribeFilesRequest) GetOwnerTypeLike() string {
	if x != nil && x.OwnerTypeLike != nil {
		return *x.OwnerTypeLike
	}
	return ""
}

func (x *DescribeFilesRequest) GetUsageLike() string {
	if x != nil && x.UsageLike != nil {
		return *x.UsageLike
	}
	return ""
}

func (x *DescribeFilesRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *DescribeFilesRequest) GetIncludeDeleted() bool {
	if x != nil && x.IncludeDeleted != nil {
		return *x.IncludeDeleted
	}
	return false
}

func (x *DescribeFilesRequest) GetOwnerIds() []int64 {
	if x != nil {
		return x.OwnerIds
	}
	return nil
}

func (x *DescribeFilesRequest) GetCompanyIds() []int64 {
	if x != nil {
		return x.CompanyIds
	}
	return nil
}

func (x *DescribeFilesRequest) GetOrderBy() *v2.OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *DescribeFilesRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// search file response
type DescribeFilesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// file list
	Files []*v21.FileModel `protobuf:"bytes,2,rep,name=files,proto3" json:"files,omitempty"`
}

func (x *DescribeFilesResponse) Reset() {
	*x = DescribeFilesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_file_search_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeFilesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeFilesResponse) ProtoMessage() {}

func (x *DescribeFilesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_file_search_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeFilesResponse.ProtoReflect.Descriptor instead.
func (*DescribeFilesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_file_search_service_proto_rawDescGZIP(), []int{1}
}

func (x *DescribeFilesResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *DescribeFilesResponse) GetFiles() []*v21.FileModel {
	if x != nil {
		return x.Files
	}
	return nil
}

var File_moego_service_file_v2_file_search_service_proto protoreflect.FileDescriptor

var file_moego_service_file_v2_file_search_service_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x66, 0x69, 0x6c, 0x65, 0x2f, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x1a, 0x26, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x2f, 0x76, 0x32, 0x2f, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd9, 0x04, 0x0a,
	0x14, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92,
	0x01, 0x0a, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x73, 0x12, 0x33, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10,
	0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x28, 0x00,
	0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x36, 0x0a,
	0x0f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x69, 0x6b, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x32, 0x48, 0x00, 0x52, 0x0d, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69,
	0x6b, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0a, 0x75, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x6c,
	0x69, 0x6b, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x32, 0x48, 0x01, 0x52, 0x09, 0x75, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x6b,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x0f, 0x69, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x02, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01,
	0x0a, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x31, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92,
	0x01, 0x0a, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x12, 0x37, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x62, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x42, 0x79, 0x48, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x88, 0x01,
	0x01, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x6c, 0x69, 0x6b, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x75, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x6c, 0x69, 0x6b, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x69, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x22, 0x92, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x32, 0x7f, 0x0a,
	0x11, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x6a, 0x0a, 0x0d, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x73, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x77,
	0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x50,
	0x01, 0x5a, 0x54, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x2f, 0x76, 0x32, 0x3b, 0x66, 0x69,
	0x6c, 0x65, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_file_v2_file_search_service_proto_rawDescOnce sync.Once
	file_moego_service_file_v2_file_search_service_proto_rawDescData = file_moego_service_file_v2_file_search_service_proto_rawDesc
)

func file_moego_service_file_v2_file_search_service_proto_rawDescGZIP() []byte {
	file_moego_service_file_v2_file_search_service_proto_rawDescOnce.Do(func() {
		file_moego_service_file_v2_file_search_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_file_v2_file_search_service_proto_rawDescData)
	})
	return file_moego_service_file_v2_file_search_service_proto_rawDescData
}

var file_moego_service_file_v2_file_search_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_service_file_v2_file_search_service_proto_goTypes = []interface{}{
	(*DescribeFilesRequest)(nil),  // 0: moego.service.file.v2.DescribeFilesRequest
	(*DescribeFilesResponse)(nil), // 1: moego.service.file.v2.DescribeFilesResponse
	(*v2.OrderBy)(nil),            // 2: moego.utils.v2.OrderBy
	(*v2.PaginationRequest)(nil),  // 3: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil), // 4: moego.utils.v2.PaginationResponse
	(*v21.FileModel)(nil),         // 5: moego.models.file.v2.FileModel
}
var file_moego_service_file_v2_file_search_service_proto_depIdxs = []int32{
	2, // 0: moego.service.file.v2.DescribeFilesRequest.order_by:type_name -> moego.utils.v2.OrderBy
	3, // 1: moego.service.file.v2.DescribeFilesRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	4, // 2: moego.service.file.v2.DescribeFilesResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	5, // 3: moego.service.file.v2.DescribeFilesResponse.files:type_name -> moego.models.file.v2.FileModel
	0, // 4: moego.service.file.v2.FileSearchService.DescribeFiles:input_type -> moego.service.file.v2.DescribeFilesRequest
	1, // 5: moego.service.file.v2.FileSearchService.DescribeFiles:output_type -> moego.service.file.v2.DescribeFilesResponse
	5, // [5:6] is the sub-list for method output_type
	4, // [4:5] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_service_file_v2_file_search_service_proto_init() }
func file_moego_service_file_v2_file_search_service_proto_init() {
	if File_moego_service_file_v2_file_search_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_file_v2_file_search_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeFilesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_file_search_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeFilesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_file_v2_file_search_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_file_v2_file_search_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_file_v2_file_search_service_proto_goTypes,
		DependencyIndexes: file_moego_service_file_v2_file_search_service_proto_depIdxs,
		MessageInfos:      file_moego_service_file_v2_file_search_service_proto_msgTypes,
	}.Build()
	File_moego_service_file_v2_file_search_service_proto = out.File
	file_moego_service_file_v2_file_search_service_proto_rawDesc = nil
	file_moego_service_file_v2_file_search_service_proto_goTypes = nil
	file_moego_service_file_v2_file_search_service_proto_depIdxs = nil
}
