// @since 2022-05-30 17:05:07
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/todo/v1/todo_service.proto

package todosvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/todo/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/universal/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// add todo input
type AddTodoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user id
	UserId int64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// title
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
}

func (x *AddTodoRequest) Reset() {
	*x = AddTodoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddTodoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTodoRequest) ProtoMessage() {}

func (x *AddTodoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTodoRequest.ProtoReflect.Descriptor instead.
func (*AddTodoRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{0}
}

func (x *AddTodoRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddTodoRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

// list all todo input
type ListTodoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user id
	UserId int64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *ListTodoRequest) Reset() {
	*x = ListTodoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTodoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTodoRequest) ProtoMessage() {}

func (x *ListTodoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTodoRequest.ProtoReflect.Descriptor instead.
func (*ListTodoRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListTodoRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// 多租隔离, 所有 service 层接口都需要校验数据所有权
// update todo input
type UpdateTodoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user id
	UserId int64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// id
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// title
	Title string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// status
	Status v1.TodoModel_Status `protobuf:"varint,4,opt,name=status,proto3,enum=moego.models.todo.v1.TodoModel_Status" json:"status,omitempty"`
}

func (x *UpdateTodoRequest) Reset() {
	*x = UpdateTodoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTodoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTodoRequest) ProtoMessage() {}

func (x *UpdateTodoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTodoRequest.ProtoReflect.Descriptor instead.
func (*UpdateTodoRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateTodoRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateTodoRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateTodoRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UpdateTodoRequest) GetStatus() v1.TodoModel_Status {
	if x != nil {
		return x.Status
	}
	return v1.TodoModel_Status(0)
}

// response body
type HelloChannyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// response code
	HelloChanny string `protobuf:"bytes,1,opt,name=hello_channy,json=helloChanny,proto3" json:"hello_channy,omitempty"`
}

func (x *HelloChannyResponse) Reset() {
	*x = HelloChannyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloChannyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloChannyResponse) ProtoMessage() {}

func (x *HelloChannyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloChannyResponse.ProtoReflect.Descriptor instead.
func (*HelloChannyResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{3}
}

func (x *HelloChannyResponse) GetHelloChanny() string {
	if x != nil {
		return x.HelloChanny
	}
	return ""
}

// response body
type HelloDongResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// response content
	HelloDong string `protobuf:"bytes,1,opt,name=hello_dong,json=helloDong,proto3" json:"hello_dong,omitempty"`
}

func (x *HelloDongResponse) Reset() {
	*x = HelloDongResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloDongResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloDongResponse) ProtoMessage() {}

func (x *HelloDongResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloDongResponse.ProtoReflect.Descriptor instead.
func (*HelloDongResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{4}
}

func (x *HelloDongResponse) GetHelloDong() string {
	if x != nil {
		return x.HelloDong
	}
	return ""
}

// response body
type HelloJettResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// response content
	HelloJett string `protobuf:"bytes,1,opt,name=hello_jett,json=helloJett,proto3" json:"hello_jett,omitempty"`
}

func (x *HelloJettResponse) Reset() {
	*x = HelloJettResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloJettResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloJettResponse) ProtoMessage() {}

func (x *HelloJettResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloJettResponse.ProtoReflect.Descriptor instead.
func (*HelloJettResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{5}
}

func (x *HelloJettResponse) GetHelloJett() string {
	if x != nil {
		return x.HelloJett
	}
	return ""
}

// EchoHzRequest
type EchoHzRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// msg
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	// id
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *EchoHzRequest) Reset() {
	*x = EchoHzRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EchoHzRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoHzRequest) ProtoMessage() {}

func (x *EchoHzRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoHzRequest.ProtoReflect.Descriptor instead.
func (*EchoHzRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{6}
}

func (x *EchoHzRequest) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *EchoHzRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// EchoHzResponse
type EchoHzResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// msg
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	// id
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *EchoHzResponse) Reset() {
	*x = EchoHzResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EchoHzResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoHzResponse) ProtoMessage() {}

func (x *EchoHzResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoHzResponse.ProtoReflect.Descriptor instead.
func (*EchoHzResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{7}
}

func (x *EchoHzResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *EchoHzResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// HelloArkRequest
type HelloArkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// message
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *HelloArkRequest) Reset() {
	*x = HelloArkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloArkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloArkRequest) ProtoMessage() {}

func (x *HelloArkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloArkRequest.ProtoReflect.Descriptor instead.
func (*HelloArkRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{8}
}

func (x *HelloArkRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// HelloArkResponse
type HelloArkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reply
	Reply string `protobuf:"bytes,1,opt,name=reply,proto3" json:"reply,omitempty"`
}

func (x *HelloArkResponse) Reset() {
	*x = HelloArkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloArkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloArkResponse) ProtoMessage() {}

func (x *HelloArkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloArkResponse.ProtoReflect.Descriptor instead.
func (*HelloArkResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{9}
}

func (x *HelloArkResponse) GetReply() string {
	if x != nil {
		return x.Reply
	}
	return ""
}

// HelloBetterRequest
type HelloBetterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// message
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *HelloBetterRequest) Reset() {
	*x = HelloBetterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloBetterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloBetterRequest) ProtoMessage() {}

func (x *HelloBetterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloBetterRequest.ProtoReflect.Descriptor instead.
func (*HelloBetterRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{10}
}

func (x *HelloBetterRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// HelloBetterResponse
type HelloBetterResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reply
	Reply string `protobuf:"bytes,1,opt,name=reply,proto3" json:"reply,omitempty"`
}

func (x *HelloBetterResponse) Reset() {
	*x = HelloBetterResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloBetterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloBetterResponse) ProtoMessage() {}

func (x *HelloBetterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloBetterResponse.ProtoReflect.Descriptor instead.
func (*HelloBetterResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{11}
}

func (x *HelloBetterResponse) GetReply() string {
	if x != nil {
		return x.Reply
	}
	return ""
}

// HelloPerqinRequest
type HelloPerqinRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// message
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *HelloPerqinRequest) Reset() {
	*x = HelloPerqinRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloPerqinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloPerqinRequest) ProtoMessage() {}

func (x *HelloPerqinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloPerqinRequest.ProtoReflect.Descriptor instead.
func (*HelloPerqinRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{12}
}

func (x *HelloPerqinRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// HelloPerqinResponse
type HelloPerqinResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reply
	Reply string `protobuf:"bytes,1,opt,name=reply,proto3" json:"reply,omitempty"`
}

func (x *HelloPerqinResponse) Reset() {
	*x = HelloPerqinResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloPerqinResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloPerqinResponse) ProtoMessage() {}

func (x *HelloPerqinResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloPerqinResponse.ProtoReflect.Descriptor instead.
func (*HelloPerqinResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{13}
}

func (x *HelloPerqinResponse) GetReply() string {
	if x != nil {
		return x.Reply
	}
	return ""
}

// HelloYueyueRequest
type HelloYueyueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// message
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *HelloYueyueRequest) Reset() {
	*x = HelloYueyueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloYueyueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloYueyueRequest) ProtoMessage() {}

func (x *HelloYueyueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloYueyueRequest.ProtoReflect.Descriptor instead.
func (*HelloYueyueRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{14}
}

func (x *HelloYueyueRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// HelloYueyueResponse
type HelloYueyueResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reply
	Reply string `protobuf:"bytes,1,opt,name=reply,proto3" json:"reply,omitempty"`
}

func (x *HelloYueyueResponse) Reset() {
	*x = HelloYueyueResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloYueyueResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloYueyueResponse) ProtoMessage() {}

func (x *HelloYueyueResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloYueyueResponse.ProtoReflect.Descriptor instead.
func (*HelloYueyueResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{15}
}

func (x *HelloYueyueResponse) GetReply() string {
	if x != nil {
		return x.Reply
	}
	return ""
}

// HelloYueyueRequest
type HelloKaiRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// message
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *HelloKaiRequest) Reset() {
	*x = HelloKaiRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloKaiRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloKaiRequest) ProtoMessage() {}

func (x *HelloKaiRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloKaiRequest.ProtoReflect.Descriptor instead.
func (*HelloKaiRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{16}
}

func (x *HelloKaiRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// HelloYueyueResponse
type HelloKaiResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reply
	Reply string `protobuf:"bytes,1,opt,name=reply,proto3" json:"reply,omitempty"`
}

func (x *HelloKaiResponse) Reset() {
	*x = HelloKaiResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloKaiResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloKaiResponse) ProtoMessage() {}

func (x *HelloKaiResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloKaiResponse.ProtoReflect.Descriptor instead.
func (*HelloKaiResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{17}
}

func (x *HelloKaiResponse) GetReply() string {
	if x != nil {
		return x.Reply
	}
	return ""
}

// HelloKurokoRequest
type HelloKurokoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// message
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *HelloKurokoRequest) Reset() {
	*x = HelloKurokoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloKurokoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloKurokoRequest) ProtoMessage() {}

func (x *HelloKurokoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloKurokoRequest.ProtoReflect.Descriptor instead.
func (*HelloKurokoRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{18}
}

func (x *HelloKurokoRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// HelloKurokoResponse
type HelloKurokoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reply
	Reply string `protobuf:"bytes,1,opt,name=reply,proto3" json:"reply,omitempty"`
}

func (x *HelloKurokoResponse) Reset() {
	*x = HelloKurokoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloKurokoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloKurokoResponse) ProtoMessage() {}

func (x *HelloKurokoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloKurokoResponse.ProtoReflect.Descriptor instead.
func (*HelloKurokoResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{19}
}

func (x *HelloKurokoResponse) GetReply() string {
	if x != nil {
		return x.Reply
	}
	return ""
}

// HelloBrysonRequest
type HelloBrysonRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// message
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *HelloBrysonRequest) Reset() {
	*x = HelloBrysonRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloBrysonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloBrysonRequest) ProtoMessage() {}

func (x *HelloBrysonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloBrysonRequest.ProtoReflect.Descriptor instead.
func (*HelloBrysonRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{20}
}

func (x *HelloBrysonRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// HelloBrysonResponse
type HelloBrysonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reply
	Reply string `protobuf:"bytes,1,opt,name=reply,proto3" json:"reply,omitempty"`
}

func (x *HelloBrysonResponse) Reset() {
	*x = HelloBrysonResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloBrysonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloBrysonResponse) ProtoMessage() {}

func (x *HelloBrysonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloBrysonResponse.ProtoReflect.Descriptor instead.
func (*HelloBrysonResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{21}
}

func (x *HelloBrysonResponse) GetReply() string {
	if x != nil {
		return x.Reply
	}
	return ""
}

// HelloHarvieRequest
type HelloHarvieRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// message
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *HelloHarvieRequest) Reset() {
	*x = HelloHarvieRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloHarvieRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloHarvieRequest) ProtoMessage() {}

func (x *HelloHarvieRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloHarvieRequest.ProtoReflect.Descriptor instead.
func (*HelloHarvieRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{22}
}

func (x *HelloHarvieRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// HelloHarvieResponse
type HelloHarvieResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reply
	Reply string `protobuf:"bytes,1,opt,name=reply,proto3" json:"reply,omitempty"`
}

func (x *HelloHarvieResponse) Reset() {
	*x = HelloHarvieResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelloHarvieResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloHarvieResponse) ProtoMessage() {}

func (x *HelloHarvieResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_todo_v1_todo_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloHarvieResponse.ProtoReflect.Descriptor instead.
func (*HelloHarvieResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_todo_v1_todo_service_proto_rawDescGZIP(), []int{23}
}

func (x *HelloHarvieResponse) GetReply() string {
	if x != nil {
		return x.Reply
	}
	return ""
}

var File_moego_service_todo_v1_todo_service_proto protoreflect.FileDescriptor

var file_moego_service_todo_v1_todo_service_proto_rawDesc = []byte{
	0x0a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x74, 0x6f, 0x64, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x6f, 0x64, 0x6f, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76,
	0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x74, 0x6f, 0x64,
	0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x6f, 0x64, 0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x75, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x53, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x54, 0x6f, 0x64, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x20, 0x01, 0x28, 0x64,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x22, 0x33, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x6f, 0x64, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xb2, 0x01, 0x0a,
	0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x64, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42,
	0x09, 0x72, 0x07, 0x20, 0x01, 0x28, 0x64, 0xd0, 0x01, 0x01, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x3e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x64, 0x6f, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x38, 0x0a, 0x13, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x65, 0x6c, 0x6c,
	0x6f, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x79, 0x22, 0x32, 0x0a, 0x11, 0x48,
	0x65, 0x6c, 0x6c, 0x6f, 0x44, 0x6f, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x5f, 0x64, 0x6f, 0x6e, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x44, 0x6f, 0x6e, 0x67, 0x22,
	0x32, 0x0a, 0x11, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x4a, 0x65, 0x74, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x5f, 0x6a, 0x65,
	0x74, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x4a,
	0x65, 0x74, 0x74, 0x22, 0x3f, 0x0a, 0x0d, 0x45, 0x63, 0x68, 0x6f, 0x48, 0x7a, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x72, 0x07, 0x20, 0x01, 0x28, 0x64, 0xd0, 0x01, 0x01, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x32, 0x0a, 0x0e, 0x45, 0x63, 0x68, 0x6f, 0x48, 0x7a, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x37, 0x0a, 0x0f, 0x48, 0x65, 0x6c, 0x6c,
	0x6f, 0x41, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x72, 0x05, 0x20, 0x01, 0x28, 0x80, 0x04, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0x28, 0x0a, 0x10, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x41, 0x72, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3a, 0x0a, 0x12, 0x48,
	0x65, 0x6c, 0x6c, 0x6f, 0x42, 0x65, 0x74, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x04, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x2b, 0x0a, 0x13, 0x48, 0x65, 0x6c, 0x6c, 0x6f,
	0x42, 0x65, 0x74, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x3a, 0x0a, 0x12, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x50, 0x65, 0x72,
	0x71, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x22, 0x2b, 0x0a, 0x13, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x50, 0x65, 0x72, 0x71, 0x69, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3a, 0x0a,
	0x12, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x59, 0x75, 0x65, 0x79, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x2b, 0x0a, 0x13, 0x48, 0x65, 0x6c,
	0x6c, 0x6f, 0x59, 0x75, 0x65, 0x79, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x37, 0x0a, 0x0f, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x4b,
	0x61, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72,
	0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0x28, 0x0a, 0x10, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x4b, 0x61, 0x69, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3a, 0x0a, 0x12, 0x48, 0x65, 0x6c,
	0x6c, 0x6f, 0x4b, 0x75, 0x72, 0x6f, 0x6b, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x24, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x2b, 0x0a, 0x13, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x4b, 0x75,
	0x72, 0x6f, 0x6b, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x72, 0x65, 0x70, 0x6c, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x3a, 0x0a, 0x12, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x42, 0x72, 0x79, 0x73, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05,
	0x10, 0x00, 0x18, 0xff, 0x01, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x2b,
	0x0a, 0x13, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x42, 0x72, 0x79, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3a, 0x0a, 0x12, 0x48,
	0x65, 0x6c, 0x6c, 0x6f, 0x48, 0x61, 0x72, 0x76, 0x69, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x2b, 0x0a, 0x13, 0x48, 0x65, 0x6c, 0x6c, 0x6f,
	0x48, 0x61, 0x72, 0x76, 0x69, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72,
	0x65, 0x70, 0x6c, 0x79, 0x32, 0xe9, 0x0a, 0x0a, 0x0b, 0x54, 0x6f, 0x64, 0x6f, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x53, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x54, 0x6f, 0x64, 0x6f, 0x12,
	0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x6f, 0x64, 0x6f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f,
	0x64, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x00, 0x12, 0x43, 0x0a, 0x07, 0x47, 0x65, 0x74,
	0x54, 0x6f, 0x64, 0x6f, 0x12, 0x15, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x77, 0x6e, 0x49, 0x64, 0x1a, 0x1f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x6f, 0x64, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x00, 0x12, 0x60,
	0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x64, 0x6f, 0x12, 0x26, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x64, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x75, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x00,
	0x12, 0x59, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x64, 0x6f, 0x12, 0x28,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74,
	0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x64,
	0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x6f, 0x64, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x00, 0x12, 0x3d, 0x0a, 0x0a, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x6f, 0x64, 0x6f, 0x12, 0x15, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x77, 0x6e, 0x49, 0x64,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x4f, 0x0a, 0x09, 0x48, 0x65,
	0x6c, 0x6c, 0x6f, 0x4a, 0x65, 0x74, 0x74, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a,
	0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x4a, 0x65, 0x74,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x55, 0x0a, 0x06, 0x45,
	0x63, 0x68, 0x6f, 0x48, 0x7a, 0x12, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x63,
	0x68, 0x6f, 0x48, 0x7a, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f, 0x64, 0x6f,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x63, 0x68, 0x6f, 0x48, 0x7a, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x5b, 0x0a, 0x08, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x41, 0x72, 0x6b, 0x12, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74,
	0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x41, 0x72, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x48,
	0x65, 0x6c, 0x6c, 0x6f, 0x41, 0x72, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x64, 0x0a, 0x0b, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x42, 0x65, 0x74, 0x74, 0x65, 0x72, 0x12, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74,
	0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x42, 0x65, 0x74, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x42, 0x65, 0x74, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x0b, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x50, 0x65,
	0x72, 0x71, 0x69, 0x6e, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x65, 0x6c,
	0x6c, 0x6f, 0x50, 0x65, 0x72, 0x71, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x50, 0x65, 0x72,
	0x71, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x0b, 0x48,
	0x65, 0x6c, 0x6c, 0x6f, 0x59, 0x75, 0x65, 0x79, 0x75, 0x65, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x59, 0x75, 0x65, 0x79, 0x75, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x65,
	0x6c, 0x6c, 0x6f, 0x59, 0x75, 0x65, 0x79, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x5b, 0x0a, 0x08, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x4b, 0x61, 0x69, 0x12, 0x26, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f,
	0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x4b, 0x61, 0x69, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x65,
	0x6c, 0x6c, 0x6f, 0x4b, 0x61, 0x69, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64,
	0x0a, 0x0b, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x4b, 0x75, 0x72, 0x6f, 0x6b, 0x6f, 0x12, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f,
	0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x4b, 0x75, 0x72, 0x6f, 0x6b,
	0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x4b, 0x75, 0x72, 0x6f, 0x6b, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x0b, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x42, 0x72, 0x79,
	0x73, 0x6f, 0x6e, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x65, 0x6c, 0x6c,
	0x6f, 0x42, 0x72, 0x79, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74,
	0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x42, 0x72, 0x79, 0x73,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x0b, 0x48, 0x65,
	0x6c, 0x6c, 0x6f, 0x48, 0x61, 0x72, 0x76, 0x69, 0x65, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x48, 0x61, 0x72, 0x76, 0x69, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x65, 0x6c,
	0x6c, 0x6f, 0x48, 0x61, 0x72, 0x76, 0x69, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x77, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x6f, 0x64, 0x6f, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x54, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x74, 0x6f, 0x64, 0x6f, 0x2f, 0x76, 0x31, 0x3b,
	0x74, 0x6f, 0x64, 0x6f, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_service_todo_v1_todo_service_proto_rawDescOnce sync.Once
	file_moego_service_todo_v1_todo_service_proto_rawDescData = file_moego_service_todo_v1_todo_service_proto_rawDesc
)

func file_moego_service_todo_v1_todo_service_proto_rawDescGZIP() []byte {
	file_moego_service_todo_v1_todo_service_proto_rawDescOnce.Do(func() {
		file_moego_service_todo_v1_todo_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_todo_v1_todo_service_proto_rawDescData)
	})
	return file_moego_service_todo_v1_todo_service_proto_rawDescData
}

var file_moego_service_todo_v1_todo_service_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_moego_service_todo_v1_todo_service_proto_goTypes = []interface{}{
	(*AddTodoRequest)(nil),      // 0: moego.service.todo.v1.AddTodoRequest
	(*ListTodoRequest)(nil),     // 1: moego.service.todo.v1.ListTodoRequest
	(*UpdateTodoRequest)(nil),   // 2: moego.service.todo.v1.UpdateTodoRequest
	(*HelloChannyResponse)(nil), // 3: moego.service.todo.v1.HelloChannyResponse
	(*HelloDongResponse)(nil),   // 4: moego.service.todo.v1.HelloDongResponse
	(*HelloJettResponse)(nil),   // 5: moego.service.todo.v1.HelloJettResponse
	(*EchoHzRequest)(nil),       // 6: moego.service.todo.v1.EchoHzRequest
	(*EchoHzResponse)(nil),      // 7: moego.service.todo.v1.EchoHzResponse
	(*HelloArkRequest)(nil),     // 8: moego.service.todo.v1.HelloArkRequest
	(*HelloArkResponse)(nil),    // 9: moego.service.todo.v1.HelloArkResponse
	(*HelloBetterRequest)(nil),  // 10: moego.service.todo.v1.HelloBetterRequest
	(*HelloBetterResponse)(nil), // 11: moego.service.todo.v1.HelloBetterResponse
	(*HelloPerqinRequest)(nil),  // 12: moego.service.todo.v1.HelloPerqinRequest
	(*HelloPerqinResponse)(nil), // 13: moego.service.todo.v1.HelloPerqinResponse
	(*HelloYueyueRequest)(nil),  // 14: moego.service.todo.v1.HelloYueyueRequest
	(*HelloYueyueResponse)(nil), // 15: moego.service.todo.v1.HelloYueyueResponse
	(*HelloKaiRequest)(nil),     // 16: moego.service.todo.v1.HelloKaiRequest
	(*HelloKaiResponse)(nil),    // 17: moego.service.todo.v1.HelloKaiResponse
	(*HelloKurokoRequest)(nil),  // 18: moego.service.todo.v1.HelloKurokoRequest
	(*HelloKurokoResponse)(nil), // 19: moego.service.todo.v1.HelloKurokoResponse
	(*HelloBrysonRequest)(nil),  // 20: moego.service.todo.v1.HelloBrysonRequest
	(*HelloBrysonResponse)(nil), // 21: moego.service.todo.v1.HelloBrysonResponse
	(*HelloHarvieRequest)(nil),  // 22: moego.service.todo.v1.HelloHarvieRequest
	(*HelloHarvieResponse)(nil), // 23: moego.service.todo.v1.HelloHarvieResponse
	(v1.TodoModel_Status)(0),    // 24: moego.models.todo.v1.TodoModel.Status
	(*v11.OwnId)(nil),           // 25: moego.utils.v1.OwnId
	(*emptypb.Empty)(nil),       // 26: google.protobuf.Empty
	(*v1.TodoModel)(nil),        // 27: moego.models.todo.v1.TodoModel
	(*v12.EntityListModel)(nil), // 28: moego.models.universal.v1.EntityListModel
}
var file_moego_service_todo_v1_todo_service_proto_depIdxs = []int32{
	24, // 0: moego.service.todo.v1.UpdateTodoRequest.status:type_name -> moego.models.todo.v1.TodoModel.Status
	0,  // 1: moego.service.todo.v1.TodoService.AddTodo:input_type -> moego.service.todo.v1.AddTodoRequest
	25, // 2: moego.service.todo.v1.TodoService.GetTodo:input_type -> moego.utils.v1.OwnId
	1,  // 3: moego.service.todo.v1.TodoService.ListTodo:input_type -> moego.service.todo.v1.ListTodoRequest
	2,  // 4: moego.service.todo.v1.TodoService.UpdateTodo:input_type -> moego.service.todo.v1.UpdateTodoRequest
	25, // 5: moego.service.todo.v1.TodoService.DeleteTodo:input_type -> moego.utils.v1.OwnId
	26, // 6: moego.service.todo.v1.TodoService.HelloJett:input_type -> google.protobuf.Empty
	6,  // 7: moego.service.todo.v1.TodoService.EchoHz:input_type -> moego.service.todo.v1.EchoHzRequest
	8,  // 8: moego.service.todo.v1.TodoService.HelloArk:input_type -> moego.service.todo.v1.HelloArkRequest
	10, // 9: moego.service.todo.v1.TodoService.HelloBetter:input_type -> moego.service.todo.v1.HelloBetterRequest
	12, // 10: moego.service.todo.v1.TodoService.HelloPerqin:input_type -> moego.service.todo.v1.HelloPerqinRequest
	14, // 11: moego.service.todo.v1.TodoService.HelloYueyue:input_type -> moego.service.todo.v1.HelloYueyueRequest
	16, // 12: moego.service.todo.v1.TodoService.HelloKai:input_type -> moego.service.todo.v1.HelloKaiRequest
	18, // 13: moego.service.todo.v1.TodoService.HelloKuroko:input_type -> moego.service.todo.v1.HelloKurokoRequest
	20, // 14: moego.service.todo.v1.TodoService.HelloBryson:input_type -> moego.service.todo.v1.HelloBrysonRequest
	22, // 15: moego.service.todo.v1.TodoService.HelloHarvie:input_type -> moego.service.todo.v1.HelloHarvieRequest
	27, // 16: moego.service.todo.v1.TodoService.AddTodo:output_type -> moego.models.todo.v1.TodoModel
	27, // 17: moego.service.todo.v1.TodoService.GetTodo:output_type -> moego.models.todo.v1.TodoModel
	28, // 18: moego.service.todo.v1.TodoService.ListTodo:output_type -> moego.models.universal.v1.EntityListModel
	27, // 19: moego.service.todo.v1.TodoService.UpdateTodo:output_type -> moego.models.todo.v1.TodoModel
	26, // 20: moego.service.todo.v1.TodoService.DeleteTodo:output_type -> google.protobuf.Empty
	5,  // 21: moego.service.todo.v1.TodoService.HelloJett:output_type -> moego.service.todo.v1.HelloJettResponse
	7,  // 22: moego.service.todo.v1.TodoService.EchoHz:output_type -> moego.service.todo.v1.EchoHzResponse
	9,  // 23: moego.service.todo.v1.TodoService.HelloArk:output_type -> moego.service.todo.v1.HelloArkResponse
	11, // 24: moego.service.todo.v1.TodoService.HelloBetter:output_type -> moego.service.todo.v1.HelloBetterResponse
	13, // 25: moego.service.todo.v1.TodoService.HelloPerqin:output_type -> moego.service.todo.v1.HelloPerqinResponse
	15, // 26: moego.service.todo.v1.TodoService.HelloYueyue:output_type -> moego.service.todo.v1.HelloYueyueResponse
	17, // 27: moego.service.todo.v1.TodoService.HelloKai:output_type -> moego.service.todo.v1.HelloKaiResponse
	19, // 28: moego.service.todo.v1.TodoService.HelloKuroko:output_type -> moego.service.todo.v1.HelloKurokoResponse
	21, // 29: moego.service.todo.v1.TodoService.HelloBryson:output_type -> moego.service.todo.v1.HelloBrysonResponse
	23, // 30: moego.service.todo.v1.TodoService.HelloHarvie:output_type -> moego.service.todo.v1.HelloHarvieResponse
	16, // [16:31] is the sub-list for method output_type
	1,  // [1:16] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_moego_service_todo_v1_todo_service_proto_init() }
func file_moego_service_todo_v1_todo_service_proto_init() {
	if File_moego_service_todo_v1_todo_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_todo_v1_todo_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddTodoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTodoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTodoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloChannyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloDongResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloJettResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EchoHzRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EchoHzResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloArkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloArkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloBetterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloBetterResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloPerqinRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloPerqinResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloYueyueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloYueyueResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloKaiRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloKaiResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloKurokoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloKurokoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloBrysonRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloBrysonResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloHarvieRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_todo_v1_todo_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelloHarvieResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_todo_v1_todo_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_todo_v1_todo_service_proto_goTypes,
		DependencyIndexes: file_moego_service_todo_v1_todo_service_proto_depIdxs,
		MessageInfos:      file_moego_service_todo_v1_todo_service_proto_msgTypes,
	}.Build()
	File_moego_service_todo_v1_todo_service_proto = out.File
	file_moego_service_todo_v1_todo_service_proto_rawDesc = nil
	file_moego_service_todo_v1_todo_service_proto_goTypes = nil
	file_moego_service_todo_v1_todo_service_proto_depIdxs = nil
}
