// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/smart_scheduler/v1/smart_schedule_setting_service.proto

package smartschedulersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SmartScheduleSettingServiceClient is the client API for SmartScheduleSettingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SmartScheduleSettingServiceClient interface {
	// get smart schedule setting
	GetSmartScheduleSetting(ctx context.Context, in *GetSmartScheduleSettingRequest, opts ...grpc.CallOption) (*GetSmartScheduleSettingResponse, error)
	// update smart schedule setting
	UpdateSmartScheduleSetting(ctx context.Context, in *UpdateSmartScheduleSettingRequest, opts ...grpc.CallOption) (*UpdateSmartScheduleSettingResponse, error)
}

type smartScheduleSettingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSmartScheduleSettingServiceClient(cc grpc.ClientConnInterface) SmartScheduleSettingServiceClient {
	return &smartScheduleSettingServiceClient{cc}
}

func (c *smartScheduleSettingServiceClient) GetSmartScheduleSetting(ctx context.Context, in *GetSmartScheduleSettingRequest, opts ...grpc.CallOption) (*GetSmartScheduleSettingResponse, error) {
	out := new(GetSmartScheduleSettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.smart_scheduler.v1.SmartScheduleSettingService/GetSmartScheduleSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smartScheduleSettingServiceClient) UpdateSmartScheduleSetting(ctx context.Context, in *UpdateSmartScheduleSettingRequest, opts ...grpc.CallOption) (*UpdateSmartScheduleSettingResponse, error) {
	out := new(UpdateSmartScheduleSettingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.smart_scheduler.v1.SmartScheduleSettingService/UpdateSmartScheduleSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SmartScheduleSettingServiceServer is the server API for SmartScheduleSettingService service.
// All implementations must embed UnimplementedSmartScheduleSettingServiceServer
// for forward compatibility
type SmartScheduleSettingServiceServer interface {
	// get smart schedule setting
	GetSmartScheduleSetting(context.Context, *GetSmartScheduleSettingRequest) (*GetSmartScheduleSettingResponse, error)
	// update smart schedule setting
	UpdateSmartScheduleSetting(context.Context, *UpdateSmartScheduleSettingRequest) (*UpdateSmartScheduleSettingResponse, error)
	mustEmbedUnimplementedSmartScheduleSettingServiceServer()
}

// UnimplementedSmartScheduleSettingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSmartScheduleSettingServiceServer struct {
}

func (UnimplementedSmartScheduleSettingServiceServer) GetSmartScheduleSetting(context.Context, *GetSmartScheduleSettingRequest) (*GetSmartScheduleSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSmartScheduleSetting not implemented")
}
func (UnimplementedSmartScheduleSettingServiceServer) UpdateSmartScheduleSetting(context.Context, *UpdateSmartScheduleSettingRequest) (*UpdateSmartScheduleSettingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSmartScheduleSetting not implemented")
}
func (UnimplementedSmartScheduleSettingServiceServer) mustEmbedUnimplementedSmartScheduleSettingServiceServer() {
}

// UnsafeSmartScheduleSettingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SmartScheduleSettingServiceServer will
// result in compilation errors.
type UnsafeSmartScheduleSettingServiceServer interface {
	mustEmbedUnimplementedSmartScheduleSettingServiceServer()
}

func RegisterSmartScheduleSettingServiceServer(s grpc.ServiceRegistrar, srv SmartScheduleSettingServiceServer) {
	s.RegisterService(&SmartScheduleSettingService_ServiceDesc, srv)
}

func _SmartScheduleSettingService_GetSmartScheduleSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSmartScheduleSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmartScheduleSettingServiceServer).GetSmartScheduleSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.smart_scheduler.v1.SmartScheduleSettingService/GetSmartScheduleSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmartScheduleSettingServiceServer).GetSmartScheduleSetting(ctx, req.(*GetSmartScheduleSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SmartScheduleSettingService_UpdateSmartScheduleSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSmartScheduleSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmartScheduleSettingServiceServer).UpdateSmartScheduleSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.smart_scheduler.v1.SmartScheduleSettingService/UpdateSmartScheduleSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmartScheduleSettingServiceServer).UpdateSmartScheduleSetting(ctx, req.(*UpdateSmartScheduleSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SmartScheduleSettingService_ServiceDesc is the grpc.ServiceDesc for SmartScheduleSettingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SmartScheduleSettingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.smart_scheduler.v1.SmartScheduleSettingService",
	HandlerType: (*SmartScheduleSettingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSmartScheduleSetting",
			Handler:    _SmartScheduleSettingService_GetSmartScheduleSetting_Handler,
		},
		{
			MethodName: "UpdateSmartScheduleSetting",
			Handler:    _SmartScheduleSettingService_UpdateSmartScheduleSetting_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/smart_scheduler/v1/smart_schedule_setting_service.proto",
}
