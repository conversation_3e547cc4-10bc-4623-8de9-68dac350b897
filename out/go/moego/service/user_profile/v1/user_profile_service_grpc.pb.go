// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/user_profile/v1/user_profile_service.proto

package userprofilesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// UserProfileServiceClient is the client API for UserProfileService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserProfileServiceClient interface {
	// GetUserProfile 获取用户画像
	GetUserProfiles(ctx context.Context, in *GetUserProfilesRequest, opts ...grpc.CallOption) (*GetUserProfilesResponse, error)
}

type userProfileServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserProfileServiceClient(cc grpc.ClientConnInterface) UserProfileServiceClient {
	return &userProfileServiceClient{cc}
}

func (c *userProfileServiceClient) GetUserProfiles(ctx context.Context, in *GetUserProfilesRequest, opts ...grpc.CallOption) (*GetUserProfilesResponse, error) {
	out := new(GetUserProfilesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.user_profile.v1.UserProfileService/GetUserProfiles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserProfileServiceServer is the server API for UserProfileService service.
// All implementations must embed UnimplementedUserProfileServiceServer
// for forward compatibility
type UserProfileServiceServer interface {
	// GetUserProfile 获取用户画像
	GetUserProfiles(context.Context, *GetUserProfilesRequest) (*GetUserProfilesResponse, error)
	mustEmbedUnimplementedUserProfileServiceServer()
}

// UnimplementedUserProfileServiceServer must be embedded to have forward compatible implementations.
type UnimplementedUserProfileServiceServer struct {
}

func (UnimplementedUserProfileServiceServer) GetUserProfiles(context.Context, *GetUserProfilesRequest) (*GetUserProfilesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserProfiles not implemented")
}
func (UnimplementedUserProfileServiceServer) mustEmbedUnimplementedUserProfileServiceServer() {}

// UnsafeUserProfileServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserProfileServiceServer will
// result in compilation errors.
type UnsafeUserProfileServiceServer interface {
	mustEmbedUnimplementedUserProfileServiceServer()
}

func RegisterUserProfileServiceServer(s grpc.ServiceRegistrar, srv UserProfileServiceServer) {
	s.RegisterService(&UserProfileService_ServiceDesc, srv)
}

func _UserProfileService_GetUserProfiles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserProfilesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserProfileServiceServer).GetUserProfiles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.user_profile.v1.UserProfileService/GetUserProfiles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserProfileServiceServer).GetUserProfiles(ctx, req.(*GetUserProfilesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserProfileService_ServiceDesc is the grpc.ServiceDesc for UserProfileService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserProfileService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.user_profile.v1.UserProfileService",
	HandlerType: (*UserProfileServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserProfiles",
			Handler:    _UserProfileService_GetUserProfiles_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/user_profile/v1/user_profile_service.proto",
}
