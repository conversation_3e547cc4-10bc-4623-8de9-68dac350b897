// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/customer/v1/pet_breed_service.proto

package customersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PetBreedServiceClient is the client API for PetBreedService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PetBreedServiceClient interface {
	// get pet breed list
	GetPetBreedList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*PetBreedListOutput, error)
}

type petBreedServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPetBreedServiceClient(cc grpc.ClientConnInterface) PetBreedServiceClient {
	return &petBreedServiceClient{cc}
}

func (c *petBreedServiceClient) GetPetBreedList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*PetBreedListOutput, error) {
	out := new(PetBreedListOutput)
	err := c.cc.Invoke(ctx, "/moego.service.customer.v1.PetBreedService/GetPetBreedList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PetBreedServiceServer is the server API for PetBreedService service.
// All implementations must embed UnimplementedPetBreedServiceServer
// for forward compatibility
type PetBreedServiceServer interface {
	// get pet breed list
	GetPetBreedList(context.Context, *emptypb.Empty) (*PetBreedListOutput, error)
	mustEmbedUnimplementedPetBreedServiceServer()
}

// UnimplementedPetBreedServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPetBreedServiceServer struct {
}

func (UnimplementedPetBreedServiceServer) GetPetBreedList(context.Context, *emptypb.Empty) (*PetBreedListOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetBreedList not implemented")
}
func (UnimplementedPetBreedServiceServer) mustEmbedUnimplementedPetBreedServiceServer() {}

// UnsafePetBreedServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PetBreedServiceServer will
// result in compilation errors.
type UnsafePetBreedServiceServer interface {
	mustEmbedUnimplementedPetBreedServiceServer()
}

func RegisterPetBreedServiceServer(s grpc.ServiceRegistrar, srv PetBreedServiceServer) {
	s.RegisterService(&PetBreedService_ServiceDesc, srv)
}

func _PetBreedService_GetPetBreedList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetBreedServiceServer).GetPetBreedList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.customer.v1.PetBreedService/GetPetBreedList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetBreedServiceServer).GetPetBreedList(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// PetBreedService_ServiceDesc is the grpc.ServiceDesc for PetBreedService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PetBreedService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.customer.v1.PetBreedService",
	HandlerType: (*PetBreedServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPetBreedList",
			Handler:    _PetBreedService_GetPetBreedList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/customer/v1/pet_breed_service.proto",
}
