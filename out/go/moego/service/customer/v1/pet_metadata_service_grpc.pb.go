// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/customer/v1/pet_metadata_service.proto

package customersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PetMetadataServiceClient is the client API for PetMetadataService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PetMetadataServiceClient interface {
	// get pet metadata list
	GetPetMetadataList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetPetMetadataListOutput, error)
}

type petMetadataServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPetMetadataServiceClient(cc grpc.ClientConnInterface) PetMetadataServiceClient {
	return &petMetadataServiceClient{cc}
}

func (c *petMetadataServiceClient) GetPetMetadataList(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetPetMetadataListOutput, error) {
	out := new(GetPetMetadataListOutput)
	err := c.cc.Invoke(ctx, "/moego.service.customer.v1.PetMetadataService/GetPetMetadataList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PetMetadataServiceServer is the server API for PetMetadataService service.
// All implementations must embed UnimplementedPetMetadataServiceServer
// for forward compatibility
type PetMetadataServiceServer interface {
	// get pet metadata list
	GetPetMetadataList(context.Context, *emptypb.Empty) (*GetPetMetadataListOutput, error)
	mustEmbedUnimplementedPetMetadataServiceServer()
}

// UnimplementedPetMetadataServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPetMetadataServiceServer struct {
}

func (UnimplementedPetMetadataServiceServer) GetPetMetadataList(context.Context, *emptypb.Empty) (*GetPetMetadataListOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetMetadataList not implemented")
}
func (UnimplementedPetMetadataServiceServer) mustEmbedUnimplementedPetMetadataServiceServer() {}

// UnsafePetMetadataServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PetMetadataServiceServer will
// result in compilation errors.
type UnsafePetMetadataServiceServer interface {
	mustEmbedUnimplementedPetMetadataServiceServer()
}

func RegisterPetMetadataServiceServer(s grpc.ServiceRegistrar, srv PetMetadataServiceServer) {
	s.RegisterService(&PetMetadataService_ServiceDesc, srv)
}

func _PetMetadataService_GetPetMetadataList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetMetadataServiceServer).GetPetMetadataList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.customer.v1.PetMetadataService/GetPetMetadataList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetMetadataServiceServer).GetPetMetadataList(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// PetMetadataService_ServiceDesc is the grpc.ServiceDesc for PetMetadataService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PetMetadataService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.customer.v1.PetMetadataService",
	HandlerType: (*PetMetadataServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPetMetadataList",
			Handler:    _PetMetadataService_GetPetMetadataList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/customer/v1/pet_metadata_service.proto",
}
