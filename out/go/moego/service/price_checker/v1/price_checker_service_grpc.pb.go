// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/price_checker/v1/price_checker_service.proto

package pricecheckersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PriceCheckerServiceClient is the client API for PriceCheckerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PriceCheckerServiceClient interface {
	// get state
	GetState(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetStateOutput, error)
	// get city
	GetCity(ctx context.Context, in *GetCityInput, opts ...grpc.CallOption) (*GetCityOutput, error)
	// get basic info
	GetBasicInfo(ctx context.Context, in *GetBasicInfoInput, opts ...grpc.CallOption) (*GetBasicInfoOutput, error)
	// get price distribution
	GetPriceDistribution(ctx context.Context, in *GetPriceDistributionInput, opts ...grpc.CallOption) (*GetPriceDistributionOutput, error)
	// post price distribution
	PostPriceDistribution(ctx context.Context, in *PostPriceDistributionInput, opts ...grpc.CallOption) (*PostPriceDistributionOutput, error)
	// start task
	StartTask(ctx context.Context, in *StartTaskInput, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// unsubscribe email
	UnsubscribeEmail(ctx context.Context, in *UnsubscribeEmailInput, opts ...grpc.CallOption) (*UnsubscribeEmailOutput, error)
}

type priceCheckerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPriceCheckerServiceClient(cc grpc.ClientConnInterface) PriceCheckerServiceClient {
	return &priceCheckerServiceClient{cc}
}

func (c *priceCheckerServiceClient) GetState(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetStateOutput, error) {
	out := new(GetStateOutput)
	err := c.cc.Invoke(ctx, "/moego.service.price_checker.v1.PriceCheckerService/GetState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceCheckerServiceClient) GetCity(ctx context.Context, in *GetCityInput, opts ...grpc.CallOption) (*GetCityOutput, error) {
	out := new(GetCityOutput)
	err := c.cc.Invoke(ctx, "/moego.service.price_checker.v1.PriceCheckerService/GetCity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceCheckerServiceClient) GetBasicInfo(ctx context.Context, in *GetBasicInfoInput, opts ...grpc.CallOption) (*GetBasicInfoOutput, error) {
	out := new(GetBasicInfoOutput)
	err := c.cc.Invoke(ctx, "/moego.service.price_checker.v1.PriceCheckerService/GetBasicInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceCheckerServiceClient) GetPriceDistribution(ctx context.Context, in *GetPriceDistributionInput, opts ...grpc.CallOption) (*GetPriceDistributionOutput, error) {
	out := new(GetPriceDistributionOutput)
	err := c.cc.Invoke(ctx, "/moego.service.price_checker.v1.PriceCheckerService/GetPriceDistribution", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceCheckerServiceClient) PostPriceDistribution(ctx context.Context, in *PostPriceDistributionInput, opts ...grpc.CallOption) (*PostPriceDistributionOutput, error) {
	out := new(PostPriceDistributionOutput)
	err := c.cc.Invoke(ctx, "/moego.service.price_checker.v1.PriceCheckerService/PostPriceDistribution", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceCheckerServiceClient) StartTask(ctx context.Context, in *StartTaskInput, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.price_checker.v1.PriceCheckerService/StartTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *priceCheckerServiceClient) UnsubscribeEmail(ctx context.Context, in *UnsubscribeEmailInput, opts ...grpc.CallOption) (*UnsubscribeEmailOutput, error) {
	out := new(UnsubscribeEmailOutput)
	err := c.cc.Invoke(ctx, "/moego.service.price_checker.v1.PriceCheckerService/UnsubscribeEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PriceCheckerServiceServer is the server API for PriceCheckerService service.
// All implementations must embed UnimplementedPriceCheckerServiceServer
// for forward compatibility
type PriceCheckerServiceServer interface {
	// get state
	GetState(context.Context, *emptypb.Empty) (*GetStateOutput, error)
	// get city
	GetCity(context.Context, *GetCityInput) (*GetCityOutput, error)
	// get basic info
	GetBasicInfo(context.Context, *GetBasicInfoInput) (*GetBasicInfoOutput, error)
	// get price distribution
	GetPriceDistribution(context.Context, *GetPriceDistributionInput) (*GetPriceDistributionOutput, error)
	// post price distribution
	PostPriceDistribution(context.Context, *PostPriceDistributionInput) (*PostPriceDistributionOutput, error)
	// start task
	StartTask(context.Context, *StartTaskInput) (*emptypb.Empty, error)
	// unsubscribe email
	UnsubscribeEmail(context.Context, *UnsubscribeEmailInput) (*UnsubscribeEmailOutput, error)
	mustEmbedUnimplementedPriceCheckerServiceServer()
}

// UnimplementedPriceCheckerServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPriceCheckerServiceServer struct {
}

func (UnimplementedPriceCheckerServiceServer) GetState(context.Context, *emptypb.Empty) (*GetStateOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetState not implemented")
}
func (UnimplementedPriceCheckerServiceServer) GetCity(context.Context, *GetCityInput) (*GetCityOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCity not implemented")
}
func (UnimplementedPriceCheckerServiceServer) GetBasicInfo(context.Context, *GetBasicInfoInput) (*GetBasicInfoOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBasicInfo not implemented")
}
func (UnimplementedPriceCheckerServiceServer) GetPriceDistribution(context.Context, *GetPriceDistributionInput) (*GetPriceDistributionOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPriceDistribution not implemented")
}
func (UnimplementedPriceCheckerServiceServer) PostPriceDistribution(context.Context, *PostPriceDistributionInput) (*PostPriceDistributionOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PostPriceDistribution not implemented")
}
func (UnimplementedPriceCheckerServiceServer) StartTask(context.Context, *StartTaskInput) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartTask not implemented")
}
func (UnimplementedPriceCheckerServiceServer) UnsubscribeEmail(context.Context, *UnsubscribeEmailInput) (*UnsubscribeEmailOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnsubscribeEmail not implemented")
}
func (UnimplementedPriceCheckerServiceServer) mustEmbedUnimplementedPriceCheckerServiceServer() {}

// UnsafePriceCheckerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PriceCheckerServiceServer will
// result in compilation errors.
type UnsafePriceCheckerServiceServer interface {
	mustEmbedUnimplementedPriceCheckerServiceServer()
}

func RegisterPriceCheckerServiceServer(s grpc.ServiceRegistrar, srv PriceCheckerServiceServer) {
	s.RegisterService(&PriceCheckerService_ServiceDesc, srv)
}

func _PriceCheckerService_GetState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceCheckerServiceServer).GetState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.price_checker.v1.PriceCheckerService/GetState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceCheckerServiceServer).GetState(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceCheckerService_GetCity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCityInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceCheckerServiceServer).GetCity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.price_checker.v1.PriceCheckerService/GetCity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceCheckerServiceServer).GetCity(ctx, req.(*GetCityInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceCheckerService_GetBasicInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBasicInfoInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceCheckerServiceServer).GetBasicInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.price_checker.v1.PriceCheckerService/GetBasicInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceCheckerServiceServer).GetBasicInfo(ctx, req.(*GetBasicInfoInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceCheckerService_GetPriceDistribution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPriceDistributionInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceCheckerServiceServer).GetPriceDistribution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.price_checker.v1.PriceCheckerService/GetPriceDistribution",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceCheckerServiceServer).GetPriceDistribution(ctx, req.(*GetPriceDistributionInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceCheckerService_PostPriceDistribution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PostPriceDistributionInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceCheckerServiceServer).PostPriceDistribution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.price_checker.v1.PriceCheckerService/PostPriceDistribution",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceCheckerServiceServer).PostPriceDistribution(ctx, req.(*PostPriceDistributionInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceCheckerService_StartTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartTaskInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceCheckerServiceServer).StartTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.price_checker.v1.PriceCheckerService/StartTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceCheckerServiceServer).StartTask(ctx, req.(*StartTaskInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _PriceCheckerService_UnsubscribeEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnsubscribeEmailInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PriceCheckerServiceServer).UnsubscribeEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.price_checker.v1.PriceCheckerService/UnsubscribeEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PriceCheckerServiceServer).UnsubscribeEmail(ctx, req.(*UnsubscribeEmailInput))
	}
	return interceptor(ctx, in, info, handler)
}

// PriceCheckerService_ServiceDesc is the grpc.ServiceDesc for PriceCheckerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PriceCheckerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.price_checker.v1.PriceCheckerService",
	HandlerType: (*PriceCheckerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetState",
			Handler:    _PriceCheckerService_GetState_Handler,
		},
		{
			MethodName: "GetCity",
			Handler:    _PriceCheckerService_GetCity_Handler,
		},
		{
			MethodName: "GetBasicInfo",
			Handler:    _PriceCheckerService_GetBasicInfo_Handler,
		},
		{
			MethodName: "GetPriceDistribution",
			Handler:    _PriceCheckerService_GetPriceDistribution_Handler,
		},
		{
			MethodName: "PostPriceDistribution",
			Handler:    _PriceCheckerService_PostPriceDistribution_Handler,
		},
		{
			MethodName: "StartTask",
			Handler:    _PriceCheckerService_StartTask_Handler,
		},
		{
			MethodName: "UnsubscribeEmail",
			Handler:    _PriceCheckerService_UnsubscribeEmail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/price_checker/v1/price_checker_service.proto",
}
