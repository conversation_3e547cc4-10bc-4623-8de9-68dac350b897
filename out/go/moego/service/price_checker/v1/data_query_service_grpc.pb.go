// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/price_checker/v1/data_query_service.proto

package pricecheckersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// DataQueryServiceClient is the client API for DataQueryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DataQueryServiceClient interface {
	// get data_query
	GetDataQuery(ctx context.Context, in *GetDataQueryInput, opts ...grpc.CallOption) (*GetDataQueryOutput, error)
}

type dataQueryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDataQueryServiceClient(cc grpc.ClientConnInterface) DataQueryServiceClient {
	return &dataQueryServiceClient{cc}
}

func (c *dataQueryServiceClient) GetDataQuery(ctx context.Context, in *GetDataQueryInput, opts ...grpc.CallOption) (*GetDataQueryOutput, error) {
	out := new(GetDataQueryOutput)
	err := c.cc.Invoke(ctx, "/moego.service.price_checker.v1.DataQueryService/GetDataQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DataQueryServiceServer is the server API for DataQueryService service.
// All implementations must embed UnimplementedDataQueryServiceServer
// for forward compatibility
type DataQueryServiceServer interface {
	// get data_query
	GetDataQuery(context.Context, *GetDataQueryInput) (*GetDataQueryOutput, error)
	mustEmbedUnimplementedDataQueryServiceServer()
}

// UnimplementedDataQueryServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDataQueryServiceServer struct {
}

func (UnimplementedDataQueryServiceServer) GetDataQuery(context.Context, *GetDataQueryInput) (*GetDataQueryOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDataQuery not implemented")
}
func (UnimplementedDataQueryServiceServer) mustEmbedUnimplementedDataQueryServiceServer() {}

// UnsafeDataQueryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DataQueryServiceServer will
// result in compilation errors.
type UnsafeDataQueryServiceServer interface {
	mustEmbedUnimplementedDataQueryServiceServer()
}

func RegisterDataQueryServiceServer(s grpc.ServiceRegistrar, srv DataQueryServiceServer) {
	s.RegisterService(&DataQueryService_ServiceDesc, srv)
}

func _DataQueryService_GetDataQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDataQueryInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataQueryServiceServer).GetDataQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.price_checker.v1.DataQueryService/GetDataQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataQueryServiceServer).GetDataQuery(ctx, req.(*GetDataQueryInput))
	}
	return interceptor(ctx, in, info, handler)
}

// DataQueryService_ServiceDesc is the grpc.ServiceDesc for DataQueryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DataQueryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.price_checker.v1.DataQueryService",
	HandlerType: (*DataQueryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetDataQuery",
			Handler:    _DataQueryService_GetDataQuery_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/price_checker/v1/data_query_service.proto",
}
