// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/reporting/v2/custom_report_service.proto

package reportingsvcpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SaveCustomReportRequest
type SaveCustomReportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram id, exists when update
	DiagramId *string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3,oneof" json:"diagram_id,omitempty"`
	// name of custom report, should be trimmed
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// description of custom report
	Description *string `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// metric field keys
	MetricKeys []string `protobuf:"bytes,4,rep,name=metric_keys,json=metricKeys,proto3" json:"metric_keys,omitempty"`
	// dimension fields
	Dimensions []*v2.DimensionField `protobuf:"bytes,5,rep,name=dimensions,proto3" json:"dimensions,omitempty"`
	// saved filters
	SavedFilters []*v2.FilterRequest `protobuf:"bytes,6,rep,name=saved_filters,json=savedFilters,proto3" json:"saved_filters,omitempty"`
	// dynamic column mode, use final dimension to generate columns
	DynamicColumnMode *bool `protobuf:"varint,7,opt,name=dynamic_column_mode,json=dynamicColumnMode,proto3,oneof" json:"dynamic_column_mode,omitempty"`
	// reporting scene
	Scene v2.ReportingScene `protobuf:"varint,8,opt,name=scene,proto3,enum=moego.models.reporting.v2.ReportingScene" json:"scene,omitempty"`
}

func (x *SaveCustomReportRequest) Reset() {
	*x = SaveCustomReportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveCustomReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCustomReportRequest) ProtoMessage() {}

func (x *SaveCustomReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCustomReportRequest.ProtoReflect.Descriptor instead.
func (*SaveCustomReportRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_custom_report_service_proto_rawDescGZIP(), []int{0}
}

func (x *SaveCustomReportRequest) GetDiagramId() string {
	if x != nil && x.DiagramId != nil {
		return *x.DiagramId
	}
	return ""
}

func (x *SaveCustomReportRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *SaveCustomReportRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *SaveCustomReportRequest) GetMetricKeys() []string {
	if x != nil {
		return x.MetricKeys
	}
	return nil
}

func (x *SaveCustomReportRequest) GetDimensions() []*v2.DimensionField {
	if x != nil {
		return x.Dimensions
	}
	return nil
}

func (x *SaveCustomReportRequest) GetSavedFilters() []*v2.FilterRequest {
	if x != nil {
		return x.SavedFilters
	}
	return nil
}

func (x *SaveCustomReportRequest) GetDynamicColumnMode() bool {
	if x != nil && x.DynamicColumnMode != nil {
		return *x.DynamicColumnMode
	}
	return false
}

func (x *SaveCustomReportRequest) GetScene() v2.ReportingScene {
	if x != nil {
		return x.Scene
	}
	return v2.ReportingScene(0)
}

// SaveCustomReportResponse
type SaveCustomReportResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// return created or updated diagram_id
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
}

func (x *SaveCustomReportResponse) Reset() {
	*x = SaveCustomReportResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveCustomReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCustomReportResponse) ProtoMessage() {}

func (x *SaveCustomReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCustomReportResponse.ProtoReflect.Descriptor instead.
func (*SaveCustomReportResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_custom_report_service_proto_rawDescGZIP(), []int{1}
}

func (x *SaveCustomReportResponse) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

// ModifyCustomDiagramRequest
type ModifyCustomDiagramRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reporting scene
	Scene v2.ReportingScene `protobuf:"varint,1,opt,name=scene,proto3,enum=moego.models.reporting.v2.ReportingScene" json:"scene,omitempty"`
	// diagram id
	DiagramId string `protobuf:"bytes,2,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// new name
	Name *string `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// new description
	Description *string `protobuf:"bytes,4,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// dynamic column mode, use final dimension to generate columns
	DynamicColumnMode *bool `protobuf:"varint,5,opt,name=dynamic_column_mode,json=dynamicColumnMode,proto3,oneof" json:"dynamic_column_mode,omitempty"`
}

func (x *ModifyCustomDiagramRequest) Reset() {
	*x = ModifyCustomDiagramRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyCustomDiagramRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyCustomDiagramRequest) ProtoMessage() {}

func (x *ModifyCustomDiagramRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyCustomDiagramRequest.ProtoReflect.Descriptor instead.
func (*ModifyCustomDiagramRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_custom_report_service_proto_rawDescGZIP(), []int{2}
}

func (x *ModifyCustomDiagramRequest) GetScene() v2.ReportingScene {
	if x != nil {
		return x.Scene
	}
	return v2.ReportingScene(0)
}

func (x *ModifyCustomDiagramRequest) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *ModifyCustomDiagramRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ModifyCustomDiagramRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *ModifyCustomDiagramRequest) GetDynamicColumnMode() bool {
	if x != nil && x.DynamicColumnMode != nil {
		return *x.DynamicColumnMode
	}
	return false
}

// ModifyCustomDiagramResponse
type ModifyCustomDiagramResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// modify success or not
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *ModifyCustomDiagramResponse) Reset() {
	*x = ModifyCustomDiagramResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyCustomDiagramResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyCustomDiagramResponse) ProtoMessage() {}

func (x *ModifyCustomDiagramResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyCustomDiagramResponse.ProtoReflect.Descriptor instead.
func (*ModifyCustomDiagramResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_custom_report_service_proto_rawDescGZIP(), []int{3}
}

func (x *ModifyCustomDiagramResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// DuplicateCustomReportRequest
type DuplicateCustomReportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram ids
	DiagramIds []string `protobuf:"bytes,1,rep,name=diagram_ids,json=diagramIds,proto3" json:"diagram_ids,omitempty"`
	// reporting scene
	Scene v2.ReportingScene `protobuf:"varint,2,opt,name=scene,proto3,enum=moego.models.reporting.v2.ReportingScene" json:"scene,omitempty"`
}

func (x *DuplicateCustomReportRequest) Reset() {
	*x = DuplicateCustomReportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DuplicateCustomReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DuplicateCustomReportRequest) ProtoMessage() {}

func (x *DuplicateCustomReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DuplicateCustomReportRequest.ProtoReflect.Descriptor instead.
func (*DuplicateCustomReportRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_custom_report_service_proto_rawDescGZIP(), []int{4}
}

func (x *DuplicateCustomReportRequest) GetDiagramIds() []string {
	if x != nil {
		return x.DiagramIds
	}
	return nil
}

func (x *DuplicateCustomReportRequest) GetScene() v2.ReportingScene {
	if x != nil {
		return x.Scene
	}
	return v2.ReportingScene(0)
}

// DuplicateCustomReportResponse
type DuplicateCustomReportResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// duplicate success or not
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *DuplicateCustomReportResponse) Reset() {
	*x = DuplicateCustomReportResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DuplicateCustomReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DuplicateCustomReportResponse) ProtoMessage() {}

func (x *DuplicateCustomReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DuplicateCustomReportResponse.ProtoReflect.Descriptor instead.
func (*DuplicateCustomReportResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_custom_report_service_proto_rawDescGZIP(), []int{5}
}

func (x *DuplicateCustomReportResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// DeleteCustomReportRequest
type DeleteCustomReportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram ids
	DiagramIds []string `protobuf:"bytes,1,rep,name=diagram_ids,json=diagramIds,proto3" json:"diagram_ids,omitempty"`
	// reporting scene
	Scene v2.ReportingScene `protobuf:"varint,2,opt,name=scene,proto3,enum=moego.models.reporting.v2.ReportingScene" json:"scene,omitempty"`
}

func (x *DeleteCustomReportRequest) Reset() {
	*x = DeleteCustomReportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCustomReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomReportRequest) ProtoMessage() {}

func (x *DeleteCustomReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomReportRequest.ProtoReflect.Descriptor instead.
func (*DeleteCustomReportRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_custom_report_service_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteCustomReportRequest) GetDiagramIds() []string {
	if x != nil {
		return x.DiagramIds
	}
	return nil
}

func (x *DeleteCustomReportRequest) GetScene() v2.ReportingScene {
	if x != nil {
		return x.Scene
	}
	return v2.ReportingScene(0)
}

// DeleteCustomReportResponse
type DeleteCustomReportResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// delete success or not
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *DeleteCustomReportResponse) Reset() {
	*x = DeleteCustomReportResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCustomReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomReportResponse) ProtoMessage() {}

func (x *DeleteCustomReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomReportResponse.ProtoReflect.Descriptor instead.
func (*DeleteCustomReportResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_custom_report_service_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteCustomReportResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_moego_service_reporting_v2_custom_report_service_proto protoreflect.FileDescriptor

var file_moego_service_reporting_v2_custom_report_service_proto_rawDesc = []byte{
	0x0a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x65, 0x66, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb5, 0x04, 0x0a,
	0x17, 0x53, 0x61, 0x76, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67,
	0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52, 0x09, 0x64, 0x69, 0x61, 0x67,
	0x72, 0x61, 0x6d, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x10, 0x01, 0x18,
	0xff, 0x01, 0x32, 0x0e, 0x5e, 0x5b, 0x5e, 0x5c, 0x73, 0x5d, 0x2e, 0x2a, 0x5b, 0x5e, 0x5c, 0x73,
	0x5d, 0x24, 0x48, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x48, 0x02,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01,
	0x12, 0x32, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x18, 0x01, 0x22,
	0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x4b, 0x65, 0x79, 0x73, 0x12, 0x49, 0x0a, 0x0a, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x52, 0x0a, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x4d, 0x0a, 0x0d, 0x73, 0x61, 0x76, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x0c, 0x73, 0x61, 0x76, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x33,
	0x0a, 0x13, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52, 0x11, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x3f, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x05, 0x73,
	0x63, 0x65, 0x6e, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d,
	0x5f, 0x69, 0x64, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x16, 0x0a, 0x14,
	0x5f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x22, 0x39, 0x0a, 0x18, 0x53, 0x61, 0x76, 0x65, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x22,
	0xd6, 0x02, 0x0a, 0x1a, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x44, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3f,
	0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x12,
	0x29, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52,
	0x09, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x10,
	0x01, 0x18, 0xff, 0x01, 0x32, 0x0e, 0x5e, 0x5b, 0x5e, 0x5c, 0x73, 0x5d, 0x2e, 0x2a, 0x5b, 0x5e,
	0x5c, 0x73, 0x5d, 0x24, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x31, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01,
	0x48, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x33, 0x0a, 0x13, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x63, 0x6f,
	0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x02, 0x52, 0x11, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x16, 0x0a, 0x14, 0x5f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x63, 0x6f, 0x6c,
	0x75, 0x6d, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x22, 0x37, 0x0a, 0x1b, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x22, 0x93, 0x01, 0x0a, 0x1c, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x32, 0x0a, 0x0b, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x18,
	0x01, 0x22, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x0a, 0x64, 0x69, 0x61, 0x67,
	0x72, 0x61, 0x6d, 0x49, 0x64, 0x73, 0x12, 0x3f, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x65, 0x6e, 0x65,
	0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x22, 0x39, 0x0a, 0x1d, 0x44, 0x75, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x22, 0x90, 0x01, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x32, 0x0a, 0x0b, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x18, 0x01, 0x22,
	0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61,
	0x6d, 0x49, 0x64, 0x73, 0x12, 0x3f, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x05,
	0x73, 0x63, 0x65, 0x6e, 0x65, 0x22, 0x36, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x32, 0xb2, 0x04,
	0x0a, 0x13, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7d, 0x0a, 0x10, 0x53, 0x61, 0x76, 0x65, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x61, 0x76, 0x65,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x13, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44, 0x69,
	0x61, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c, 0x01,
	0x0a, 0x15, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44,
	0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x83, 0x01, 0x0a,
	0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x86, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_service_reporting_v2_custom_report_service_proto_rawDescOnce sync.Once
	file_moego_service_reporting_v2_custom_report_service_proto_rawDescData = file_moego_service_reporting_v2_custom_report_service_proto_rawDesc
)

func file_moego_service_reporting_v2_custom_report_service_proto_rawDescGZIP() []byte {
	file_moego_service_reporting_v2_custom_report_service_proto_rawDescOnce.Do(func() {
		file_moego_service_reporting_v2_custom_report_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_reporting_v2_custom_report_service_proto_rawDescData)
	})
	return file_moego_service_reporting_v2_custom_report_service_proto_rawDescData
}

var file_moego_service_reporting_v2_custom_report_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_service_reporting_v2_custom_report_service_proto_goTypes = []interface{}{
	(*SaveCustomReportRequest)(nil),       // 0: moego.service.reporting.v2.SaveCustomReportRequest
	(*SaveCustomReportResponse)(nil),      // 1: moego.service.reporting.v2.SaveCustomReportResponse
	(*ModifyCustomDiagramRequest)(nil),    // 2: moego.service.reporting.v2.ModifyCustomDiagramRequest
	(*ModifyCustomDiagramResponse)(nil),   // 3: moego.service.reporting.v2.ModifyCustomDiagramResponse
	(*DuplicateCustomReportRequest)(nil),  // 4: moego.service.reporting.v2.DuplicateCustomReportRequest
	(*DuplicateCustomReportResponse)(nil), // 5: moego.service.reporting.v2.DuplicateCustomReportResponse
	(*DeleteCustomReportRequest)(nil),     // 6: moego.service.reporting.v2.DeleteCustomReportRequest
	(*DeleteCustomReportResponse)(nil),    // 7: moego.service.reporting.v2.DeleteCustomReportResponse
	(*v2.DimensionField)(nil),             // 8: moego.models.reporting.v2.DimensionField
	(*v2.FilterRequest)(nil),              // 9: moego.models.reporting.v2.FilterRequest
	(v2.ReportingScene)(0),                // 10: moego.models.reporting.v2.ReportingScene
}
var file_moego_service_reporting_v2_custom_report_service_proto_depIdxs = []int32{
	8,  // 0: moego.service.reporting.v2.SaveCustomReportRequest.dimensions:type_name -> moego.models.reporting.v2.DimensionField
	9,  // 1: moego.service.reporting.v2.SaveCustomReportRequest.saved_filters:type_name -> moego.models.reporting.v2.FilterRequest
	10, // 2: moego.service.reporting.v2.SaveCustomReportRequest.scene:type_name -> moego.models.reporting.v2.ReportingScene
	10, // 3: moego.service.reporting.v2.ModifyCustomDiagramRequest.scene:type_name -> moego.models.reporting.v2.ReportingScene
	10, // 4: moego.service.reporting.v2.DuplicateCustomReportRequest.scene:type_name -> moego.models.reporting.v2.ReportingScene
	10, // 5: moego.service.reporting.v2.DeleteCustomReportRequest.scene:type_name -> moego.models.reporting.v2.ReportingScene
	0,  // 6: moego.service.reporting.v2.CustomReportService.SaveCustomReport:input_type -> moego.service.reporting.v2.SaveCustomReportRequest
	2,  // 7: moego.service.reporting.v2.CustomReportService.ModifyCustomDiagram:input_type -> moego.service.reporting.v2.ModifyCustomDiagramRequest
	4,  // 8: moego.service.reporting.v2.CustomReportService.DuplicateCustomReport:input_type -> moego.service.reporting.v2.DuplicateCustomReportRequest
	6,  // 9: moego.service.reporting.v2.CustomReportService.DeleteCustomReport:input_type -> moego.service.reporting.v2.DeleteCustomReportRequest
	1,  // 10: moego.service.reporting.v2.CustomReportService.SaveCustomReport:output_type -> moego.service.reporting.v2.SaveCustomReportResponse
	3,  // 11: moego.service.reporting.v2.CustomReportService.ModifyCustomDiagram:output_type -> moego.service.reporting.v2.ModifyCustomDiagramResponse
	5,  // 12: moego.service.reporting.v2.CustomReportService.DuplicateCustomReport:output_type -> moego.service.reporting.v2.DuplicateCustomReportResponse
	7,  // 13: moego.service.reporting.v2.CustomReportService.DeleteCustomReport:output_type -> moego.service.reporting.v2.DeleteCustomReportResponse
	10, // [10:14] is the sub-list for method output_type
	6,  // [6:10] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_moego_service_reporting_v2_custom_report_service_proto_init() }
func file_moego_service_reporting_v2_custom_report_service_proto_init() {
	if File_moego_service_reporting_v2_custom_report_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveCustomReportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveCustomReportResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyCustomDiagramRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyCustomDiagramResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DuplicateCustomReportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DuplicateCustomReportResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCustomReportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCustomReportResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_reporting_v2_custom_report_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_reporting_v2_custom_report_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_reporting_v2_custom_report_service_proto_goTypes,
		DependencyIndexes: file_moego_service_reporting_v2_custom_report_service_proto_depIdxs,
		MessageInfos:      file_moego_service_reporting_v2_custom_report_service_proto_msgTypes,
	}.Build()
	File_moego_service_reporting_v2_custom_report_service_proto = out.File
	file_moego_service_reporting_v2_custom_report_service_proto_rawDesc = nil
	file_moego_service_reporting_v2_custom_report_service_proto_goTypes = nil
	file_moego_service_reporting_v2_custom_report_service_proto_depIdxs = nil
}
