// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/google_partner/v1/google_reserve_integration_service.proto

package googlepartnersvcpb

import (
	context "context"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/google_partner/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// GoogleReserveIntegrationServiceClient is the client API for GoogleReserveIntegrationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GoogleReserveIntegrationServiceClient interface {
	// get Google reserve integration
	// if not found, throw NOT_FOUND(5)
	GetGoogleReserveIntegration(ctx context.Context, in *GetGoogleReserveIntegrationRequest, opts ...grpc.CallOption) (*v1.GoogleReserveIntegrationModel, error)
	// insert Google reserve integration
	InsertGoogleReserveIntegration(ctx context.Context, in *InsertGoogleReserveIntegrationRequest, opts ...grpc.CallOption) (*v1.GoogleReserveIntegrationModel, error)
	// update Google reserve integration
	UpdateGoogleReserveIntegration(ctx context.Context, in *UpdateGoogleReserveIntegrationRequest, opts ...grpc.CallOption) (*v1.GoogleReserveIntegrationModel, error)
	// refresh Google reserve integration status by Querying Merchant Status via the API
	// see https://developers.google.com/actions-center/verticals/appointments/redirect/partner-portal/inventory/merchant-status-query
	RefreshGoogleReserveIntegrationStatus(ctx context.Context, in *RefreshGoogleReserveIntegrationStatusRequest, opts ...grpc.CallOption) (*RefreshGoogleReserveIntegrationStatusResponse, error)
	// list Google reserve integration
	ListGoogleReserveIntegration(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListGoogleReserveIntegrationResponse, error)
	// delete Google reserve integration
	DeleteGoogleReserveIntegration(ctx context.Context, in *DeleteGoogleReserveIntegrationRequest, opts ...grpc.CallOption) (*DeleteGoogleReserveIntegrationResponse, error)
}

type googleReserveIntegrationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGoogleReserveIntegrationServiceClient(cc grpc.ClientConnInterface) GoogleReserveIntegrationServiceClient {
	return &googleReserveIntegrationServiceClient{cc}
}

func (c *googleReserveIntegrationServiceClient) GetGoogleReserveIntegration(ctx context.Context, in *GetGoogleReserveIntegrationRequest, opts ...grpc.CallOption) (*v1.GoogleReserveIntegrationModel, error) {
	out := new(v1.GoogleReserveIntegrationModel)
	err := c.cc.Invoke(ctx, "/moego.service.google_partner.v1.GoogleReserveIntegrationService/GetGoogleReserveIntegration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *googleReserveIntegrationServiceClient) InsertGoogleReserveIntegration(ctx context.Context, in *InsertGoogleReserveIntegrationRequest, opts ...grpc.CallOption) (*v1.GoogleReserveIntegrationModel, error) {
	out := new(v1.GoogleReserveIntegrationModel)
	err := c.cc.Invoke(ctx, "/moego.service.google_partner.v1.GoogleReserveIntegrationService/InsertGoogleReserveIntegration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *googleReserveIntegrationServiceClient) UpdateGoogleReserveIntegration(ctx context.Context, in *UpdateGoogleReserveIntegrationRequest, opts ...grpc.CallOption) (*v1.GoogleReserveIntegrationModel, error) {
	out := new(v1.GoogleReserveIntegrationModel)
	err := c.cc.Invoke(ctx, "/moego.service.google_partner.v1.GoogleReserveIntegrationService/UpdateGoogleReserveIntegration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *googleReserveIntegrationServiceClient) RefreshGoogleReserveIntegrationStatus(ctx context.Context, in *RefreshGoogleReserveIntegrationStatusRequest, opts ...grpc.CallOption) (*RefreshGoogleReserveIntegrationStatusResponse, error) {
	out := new(RefreshGoogleReserveIntegrationStatusResponse)
	err := c.cc.Invoke(ctx, "/moego.service.google_partner.v1.GoogleReserveIntegrationService/RefreshGoogleReserveIntegrationStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *googleReserveIntegrationServiceClient) ListGoogleReserveIntegration(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListGoogleReserveIntegrationResponse, error) {
	out := new(ListGoogleReserveIntegrationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.google_partner.v1.GoogleReserveIntegrationService/ListGoogleReserveIntegration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *googleReserveIntegrationServiceClient) DeleteGoogleReserveIntegration(ctx context.Context, in *DeleteGoogleReserveIntegrationRequest, opts ...grpc.CallOption) (*DeleteGoogleReserveIntegrationResponse, error) {
	out := new(DeleteGoogleReserveIntegrationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.google_partner.v1.GoogleReserveIntegrationService/DeleteGoogleReserveIntegration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GoogleReserveIntegrationServiceServer is the server API for GoogleReserveIntegrationService service.
// All implementations must embed UnimplementedGoogleReserveIntegrationServiceServer
// for forward compatibility
type GoogleReserveIntegrationServiceServer interface {
	// get Google reserve integration
	// if not found, throw NOT_FOUND(5)
	GetGoogleReserveIntegration(context.Context, *GetGoogleReserveIntegrationRequest) (*v1.GoogleReserveIntegrationModel, error)
	// insert Google reserve integration
	InsertGoogleReserveIntegration(context.Context, *InsertGoogleReserveIntegrationRequest) (*v1.GoogleReserveIntegrationModel, error)
	// update Google reserve integration
	UpdateGoogleReserveIntegration(context.Context, *UpdateGoogleReserveIntegrationRequest) (*v1.GoogleReserveIntegrationModel, error)
	// refresh Google reserve integration status by Querying Merchant Status via the API
	// see https://developers.google.com/actions-center/verticals/appointments/redirect/partner-portal/inventory/merchant-status-query
	RefreshGoogleReserveIntegrationStatus(context.Context, *RefreshGoogleReserveIntegrationStatusRequest) (*RefreshGoogleReserveIntegrationStatusResponse, error)
	// list Google reserve integration
	ListGoogleReserveIntegration(context.Context, *emptypb.Empty) (*ListGoogleReserveIntegrationResponse, error)
	// delete Google reserve integration
	DeleteGoogleReserveIntegration(context.Context, *DeleteGoogleReserveIntegrationRequest) (*DeleteGoogleReserveIntegrationResponse, error)
	mustEmbedUnimplementedGoogleReserveIntegrationServiceServer()
}

// UnimplementedGoogleReserveIntegrationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedGoogleReserveIntegrationServiceServer struct {
}

func (UnimplementedGoogleReserveIntegrationServiceServer) GetGoogleReserveIntegration(context.Context, *GetGoogleReserveIntegrationRequest) (*v1.GoogleReserveIntegrationModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoogleReserveIntegration not implemented")
}
func (UnimplementedGoogleReserveIntegrationServiceServer) InsertGoogleReserveIntegration(context.Context, *InsertGoogleReserveIntegrationRequest) (*v1.GoogleReserveIntegrationModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InsertGoogleReserveIntegration not implemented")
}
func (UnimplementedGoogleReserveIntegrationServiceServer) UpdateGoogleReserveIntegration(context.Context, *UpdateGoogleReserveIntegrationRequest) (*v1.GoogleReserveIntegrationModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGoogleReserveIntegration not implemented")
}
func (UnimplementedGoogleReserveIntegrationServiceServer) RefreshGoogleReserveIntegrationStatus(context.Context, *RefreshGoogleReserveIntegrationStatusRequest) (*RefreshGoogleReserveIntegrationStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshGoogleReserveIntegrationStatus not implemented")
}
func (UnimplementedGoogleReserveIntegrationServiceServer) ListGoogleReserveIntegration(context.Context, *emptypb.Empty) (*ListGoogleReserveIntegrationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGoogleReserveIntegration not implemented")
}
func (UnimplementedGoogleReserveIntegrationServiceServer) DeleteGoogleReserveIntegration(context.Context, *DeleteGoogleReserveIntegrationRequest) (*DeleteGoogleReserveIntegrationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGoogleReserveIntegration not implemented")
}
func (UnimplementedGoogleReserveIntegrationServiceServer) mustEmbedUnimplementedGoogleReserveIntegrationServiceServer() {
}

// UnsafeGoogleReserveIntegrationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GoogleReserveIntegrationServiceServer will
// result in compilation errors.
type UnsafeGoogleReserveIntegrationServiceServer interface {
	mustEmbedUnimplementedGoogleReserveIntegrationServiceServer()
}

func RegisterGoogleReserveIntegrationServiceServer(s grpc.ServiceRegistrar, srv GoogleReserveIntegrationServiceServer) {
	s.RegisterService(&GoogleReserveIntegrationService_ServiceDesc, srv)
}

func _GoogleReserveIntegrationService_GetGoogleReserveIntegration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGoogleReserveIntegrationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoogleReserveIntegrationServiceServer).GetGoogleReserveIntegration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.google_partner.v1.GoogleReserveIntegrationService/GetGoogleReserveIntegration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoogleReserveIntegrationServiceServer).GetGoogleReserveIntegration(ctx, req.(*GetGoogleReserveIntegrationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoogleReserveIntegrationService_InsertGoogleReserveIntegration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsertGoogleReserveIntegrationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoogleReserveIntegrationServiceServer).InsertGoogleReserveIntegration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.google_partner.v1.GoogleReserveIntegrationService/InsertGoogleReserveIntegration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoogleReserveIntegrationServiceServer).InsertGoogleReserveIntegration(ctx, req.(*InsertGoogleReserveIntegrationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoogleReserveIntegrationService_UpdateGoogleReserveIntegration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGoogleReserveIntegrationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoogleReserveIntegrationServiceServer).UpdateGoogleReserveIntegration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.google_partner.v1.GoogleReserveIntegrationService/UpdateGoogleReserveIntegration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoogleReserveIntegrationServiceServer).UpdateGoogleReserveIntegration(ctx, req.(*UpdateGoogleReserveIntegrationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoogleReserveIntegrationService_RefreshGoogleReserveIntegrationStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshGoogleReserveIntegrationStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoogleReserveIntegrationServiceServer).RefreshGoogleReserveIntegrationStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.google_partner.v1.GoogleReserveIntegrationService/RefreshGoogleReserveIntegrationStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoogleReserveIntegrationServiceServer).RefreshGoogleReserveIntegrationStatus(ctx, req.(*RefreshGoogleReserveIntegrationStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoogleReserveIntegrationService_ListGoogleReserveIntegration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoogleReserveIntegrationServiceServer).ListGoogleReserveIntegration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.google_partner.v1.GoogleReserveIntegrationService/ListGoogleReserveIntegration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoogleReserveIntegrationServiceServer).ListGoogleReserveIntegration(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoogleReserveIntegrationService_DeleteGoogleReserveIntegration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGoogleReserveIntegrationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoogleReserveIntegrationServiceServer).DeleteGoogleReserveIntegration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.google_partner.v1.GoogleReserveIntegrationService/DeleteGoogleReserveIntegration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoogleReserveIntegrationServiceServer).DeleteGoogleReserveIntegration(ctx, req.(*DeleteGoogleReserveIntegrationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// GoogleReserveIntegrationService_ServiceDesc is the grpc.ServiceDesc for GoogleReserveIntegrationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GoogleReserveIntegrationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.google_partner.v1.GoogleReserveIntegrationService",
	HandlerType: (*GoogleReserveIntegrationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGoogleReserveIntegration",
			Handler:    _GoogleReserveIntegrationService_GetGoogleReserveIntegration_Handler,
		},
		{
			MethodName: "InsertGoogleReserveIntegration",
			Handler:    _GoogleReserveIntegrationService_InsertGoogleReserveIntegration_Handler,
		},
		{
			MethodName: "UpdateGoogleReserveIntegration",
			Handler:    _GoogleReserveIntegrationService_UpdateGoogleReserveIntegration_Handler,
		},
		{
			MethodName: "RefreshGoogleReserveIntegrationStatus",
			Handler:    _GoogleReserveIntegrationService_RefreshGoogleReserveIntegrationStatus_Handler,
		},
		{
			MethodName: "ListGoogleReserveIntegration",
			Handler:    _GoogleReserveIntegrationService_ListGoogleReserveIntegration_Handler,
		},
		{
			MethodName: "DeleteGoogleReserveIntegration",
			Handler:    _GoogleReserveIntegrationService_DeleteGoogleReserveIntegration_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/google_partner/v1/google_reserve_integration_service.proto",
}
