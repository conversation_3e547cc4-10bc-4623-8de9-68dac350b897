// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/enterprise/v1/tenant_service.proto

package enterprisesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// TenantServiceClient is the client API for TenantService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TenantServiceClient interface {
	// get tenant by id
	GetTenant(ctx context.Context, in *GetTenantRequest, opts ...grpc.CallOption) (*GetTenantResponse, error)
	// create tenant
	CreateTenant(ctx context.Context, in *CreateTenantRequest, opts ...grpc.CallOption) (*CreateTenantResponse, error)
	// update tenant
	UpdateTenant(ctx context.Context, in *UpdateTenantRequest, opts ...grpc.CallOption) (*UpdateTenantResponse, error)
	// get tenant list
	ListTenant(ctx context.Context, in *ListTenantRequest, opts ...grpc.CallOption) (*ListTenantResponse, error)
	// delete tenant
	DeleteTenant(ctx context.Context, in *DeleteTenantRequest, opts ...grpc.CallOption) (*DeleteTenantResponse, error)
	// create tenant group
	CreateTenantGroup(ctx context.Context, in *CreateTenantGroupRequest, opts ...grpc.CallOption) (*CreateTenantGroupResponse, error)
	// list tenant groups
	ListTenantGroups(ctx context.Context, in *ListTenantGroupRequest, opts ...grpc.CallOption) (*ListTenantGroupResponse, error)
	// delete tenant group
	DeleteTenantGroup(ctx context.Context, in *DeleteTenantGroupRequest, opts ...grpc.CallOption) (*DeleteTenantGroupResponse, error)
	// modify tenant group mapping
	ModifyTenantGroupMapping(ctx context.Context, in *ModifyTenantGroupMappingRequest, opts ...grpc.CallOption) (*ModifyTenantGroupMappingResponse, error)
	// get tenant group mapping
	ListTenantGroupMapping(ctx context.Context, in *ListTenantGroupMappingRequest, opts ...grpc.CallOption) (*ListTenantGroupMappingResponse, error)
	// batch create tenant group
	BatchCreateTenantGroup(ctx context.Context, in *BatchCreateTenantGroupRequest, opts ...grpc.CallOption) (*BatchCreateTenantGroupResponse, error)
	// get tenant by company id
	GetTenantByCompanyId(ctx context.Context, in *GetTenantByCompanyIdRequest, opts ...grpc.CallOption) (*GetTenantResponse, error)
}

type tenantServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTenantServiceClient(cc grpc.ClientConnInterface) TenantServiceClient {
	return &tenantServiceClient{cc}
}

func (c *tenantServiceClient) GetTenant(ctx context.Context, in *GetTenantRequest, opts ...grpc.CallOption) (*GetTenantResponse, error) {
	out := new(GetTenantResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TenantService/GetTenant", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) CreateTenant(ctx context.Context, in *CreateTenantRequest, opts ...grpc.CallOption) (*CreateTenantResponse, error) {
	out := new(CreateTenantResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TenantService/CreateTenant", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) UpdateTenant(ctx context.Context, in *UpdateTenantRequest, opts ...grpc.CallOption) (*UpdateTenantResponse, error) {
	out := new(UpdateTenantResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TenantService/UpdateTenant", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) ListTenant(ctx context.Context, in *ListTenantRequest, opts ...grpc.CallOption) (*ListTenantResponse, error) {
	out := new(ListTenantResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TenantService/ListTenant", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) DeleteTenant(ctx context.Context, in *DeleteTenantRequest, opts ...grpc.CallOption) (*DeleteTenantResponse, error) {
	out := new(DeleteTenantResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TenantService/DeleteTenant", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) CreateTenantGroup(ctx context.Context, in *CreateTenantGroupRequest, opts ...grpc.CallOption) (*CreateTenantGroupResponse, error) {
	out := new(CreateTenantGroupResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TenantService/CreateTenantGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) ListTenantGroups(ctx context.Context, in *ListTenantGroupRequest, opts ...grpc.CallOption) (*ListTenantGroupResponse, error) {
	out := new(ListTenantGroupResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TenantService/ListTenantGroups", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) DeleteTenantGroup(ctx context.Context, in *DeleteTenantGroupRequest, opts ...grpc.CallOption) (*DeleteTenantGroupResponse, error) {
	out := new(DeleteTenantGroupResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TenantService/DeleteTenantGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) ModifyTenantGroupMapping(ctx context.Context, in *ModifyTenantGroupMappingRequest, opts ...grpc.CallOption) (*ModifyTenantGroupMappingResponse, error) {
	out := new(ModifyTenantGroupMappingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TenantService/ModifyTenantGroupMapping", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) ListTenantGroupMapping(ctx context.Context, in *ListTenantGroupMappingRequest, opts ...grpc.CallOption) (*ListTenantGroupMappingResponse, error) {
	out := new(ListTenantGroupMappingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TenantService/ListTenantGroupMapping", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) BatchCreateTenantGroup(ctx context.Context, in *BatchCreateTenantGroupRequest, opts ...grpc.CallOption) (*BatchCreateTenantGroupResponse, error) {
	out := new(BatchCreateTenantGroupResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TenantService/BatchCreateTenantGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tenantServiceClient) GetTenantByCompanyId(ctx context.Context, in *GetTenantByCompanyIdRequest, opts ...grpc.CallOption) (*GetTenantResponse, error) {
	out := new(GetTenantResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.TenantService/GetTenantByCompanyId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TenantServiceServer is the server API for TenantService service.
// All implementations must embed UnimplementedTenantServiceServer
// for forward compatibility
type TenantServiceServer interface {
	// get tenant by id
	GetTenant(context.Context, *GetTenantRequest) (*GetTenantResponse, error)
	// create tenant
	CreateTenant(context.Context, *CreateTenantRequest) (*CreateTenantResponse, error)
	// update tenant
	UpdateTenant(context.Context, *UpdateTenantRequest) (*UpdateTenantResponse, error)
	// get tenant list
	ListTenant(context.Context, *ListTenantRequest) (*ListTenantResponse, error)
	// delete tenant
	DeleteTenant(context.Context, *DeleteTenantRequest) (*DeleteTenantResponse, error)
	// create tenant group
	CreateTenantGroup(context.Context, *CreateTenantGroupRequest) (*CreateTenantGroupResponse, error)
	// list tenant groups
	ListTenantGroups(context.Context, *ListTenantGroupRequest) (*ListTenantGroupResponse, error)
	// delete tenant group
	DeleteTenantGroup(context.Context, *DeleteTenantGroupRequest) (*DeleteTenantGroupResponse, error)
	// modify tenant group mapping
	ModifyTenantGroupMapping(context.Context, *ModifyTenantGroupMappingRequest) (*ModifyTenantGroupMappingResponse, error)
	// get tenant group mapping
	ListTenantGroupMapping(context.Context, *ListTenantGroupMappingRequest) (*ListTenantGroupMappingResponse, error)
	// batch create tenant group
	BatchCreateTenantGroup(context.Context, *BatchCreateTenantGroupRequest) (*BatchCreateTenantGroupResponse, error)
	// get tenant by company id
	GetTenantByCompanyId(context.Context, *GetTenantByCompanyIdRequest) (*GetTenantResponse, error)
	mustEmbedUnimplementedTenantServiceServer()
}

// UnimplementedTenantServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTenantServiceServer struct {
}

func (UnimplementedTenantServiceServer) GetTenant(context.Context, *GetTenantRequest) (*GetTenantResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTenant not implemented")
}
func (UnimplementedTenantServiceServer) CreateTenant(context.Context, *CreateTenantRequest) (*CreateTenantResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTenant not implemented")
}
func (UnimplementedTenantServiceServer) UpdateTenant(context.Context, *UpdateTenantRequest) (*UpdateTenantResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTenant not implemented")
}
func (UnimplementedTenantServiceServer) ListTenant(context.Context, *ListTenantRequest) (*ListTenantResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTenant not implemented")
}
func (UnimplementedTenantServiceServer) DeleteTenant(context.Context, *DeleteTenantRequest) (*DeleteTenantResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTenant not implemented")
}
func (UnimplementedTenantServiceServer) CreateTenantGroup(context.Context, *CreateTenantGroupRequest) (*CreateTenantGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTenantGroup not implemented")
}
func (UnimplementedTenantServiceServer) ListTenantGroups(context.Context, *ListTenantGroupRequest) (*ListTenantGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTenantGroups not implemented")
}
func (UnimplementedTenantServiceServer) DeleteTenantGroup(context.Context, *DeleteTenantGroupRequest) (*DeleteTenantGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTenantGroup not implemented")
}
func (UnimplementedTenantServiceServer) ModifyTenantGroupMapping(context.Context, *ModifyTenantGroupMappingRequest) (*ModifyTenantGroupMappingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyTenantGroupMapping not implemented")
}
func (UnimplementedTenantServiceServer) ListTenantGroupMapping(context.Context, *ListTenantGroupMappingRequest) (*ListTenantGroupMappingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTenantGroupMapping not implemented")
}
func (UnimplementedTenantServiceServer) BatchCreateTenantGroup(context.Context, *BatchCreateTenantGroupRequest) (*BatchCreateTenantGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCreateTenantGroup not implemented")
}
func (UnimplementedTenantServiceServer) GetTenantByCompanyId(context.Context, *GetTenantByCompanyIdRequest) (*GetTenantResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTenantByCompanyId not implemented")
}
func (UnimplementedTenantServiceServer) mustEmbedUnimplementedTenantServiceServer() {}

// UnsafeTenantServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TenantServiceServer will
// result in compilation errors.
type UnsafeTenantServiceServer interface {
	mustEmbedUnimplementedTenantServiceServer()
}

func RegisterTenantServiceServer(s grpc.ServiceRegistrar, srv TenantServiceServer) {
	s.RegisterService(&TenantService_ServiceDesc, srv)
}

func _TenantService_GetTenant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTenantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).GetTenant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TenantService/GetTenant",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).GetTenant(ctx, req.(*GetTenantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_CreateTenant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTenantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).CreateTenant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TenantService/CreateTenant",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).CreateTenant(ctx, req.(*CreateTenantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_UpdateTenant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTenantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).UpdateTenant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TenantService/UpdateTenant",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).UpdateTenant(ctx, req.(*UpdateTenantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_ListTenant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTenantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).ListTenant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TenantService/ListTenant",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).ListTenant(ctx, req.(*ListTenantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_DeleteTenant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTenantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).DeleteTenant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TenantService/DeleteTenant",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).DeleteTenant(ctx, req.(*DeleteTenantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_CreateTenantGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTenantGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).CreateTenantGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TenantService/CreateTenantGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).CreateTenantGroup(ctx, req.(*CreateTenantGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_ListTenantGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTenantGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).ListTenantGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TenantService/ListTenantGroups",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).ListTenantGroups(ctx, req.(*ListTenantGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_DeleteTenantGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTenantGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).DeleteTenantGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TenantService/DeleteTenantGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).DeleteTenantGroup(ctx, req.(*DeleteTenantGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_ModifyTenantGroupMapping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyTenantGroupMappingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).ModifyTenantGroupMapping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TenantService/ModifyTenantGroupMapping",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).ModifyTenantGroupMapping(ctx, req.(*ModifyTenantGroupMappingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_ListTenantGroupMapping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTenantGroupMappingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).ListTenantGroupMapping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TenantService/ListTenantGroupMapping",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).ListTenantGroupMapping(ctx, req.(*ListTenantGroupMappingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_BatchCreateTenantGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCreateTenantGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).BatchCreateTenantGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TenantService/BatchCreateTenantGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).BatchCreateTenantGroup(ctx, req.(*BatchCreateTenantGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TenantService_GetTenantByCompanyId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTenantByCompanyIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TenantServiceServer).GetTenantByCompanyId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.TenantService/GetTenantByCompanyId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TenantServiceServer).GetTenantByCompanyId(ctx, req.(*GetTenantByCompanyIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TenantService_ServiceDesc is the grpc.ServiceDesc for TenantService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TenantService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.enterprise.v1.TenantService",
	HandlerType: (*TenantServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetTenant",
			Handler:    _TenantService_GetTenant_Handler,
		},
		{
			MethodName: "CreateTenant",
			Handler:    _TenantService_CreateTenant_Handler,
		},
		{
			MethodName: "UpdateTenant",
			Handler:    _TenantService_UpdateTenant_Handler,
		},
		{
			MethodName: "ListTenant",
			Handler:    _TenantService_ListTenant_Handler,
		},
		{
			MethodName: "DeleteTenant",
			Handler:    _TenantService_DeleteTenant_Handler,
		},
		{
			MethodName: "CreateTenantGroup",
			Handler:    _TenantService_CreateTenantGroup_Handler,
		},
		{
			MethodName: "ListTenantGroups",
			Handler:    _TenantService_ListTenantGroups_Handler,
		},
		{
			MethodName: "DeleteTenantGroup",
			Handler:    _TenantService_DeleteTenantGroup_Handler,
		},
		{
			MethodName: "ModifyTenantGroupMapping",
			Handler:    _TenantService_ModifyTenantGroupMapping_Handler,
		},
		{
			MethodName: "ListTenantGroupMapping",
			Handler:    _TenantService_ListTenantGroupMapping_Handler,
		},
		{
			MethodName: "BatchCreateTenantGroup",
			Handler:    _TenantService_BatchCreateTenantGroup_Handler,
		},
		{
			MethodName: "GetTenantByCompanyId",
			Handler:    _TenantService_GetTenantByCompanyId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/enterprise/v1/tenant_service.proto",
}
