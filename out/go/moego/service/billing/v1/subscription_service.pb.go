// @since 2024-06-06
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/billing/v1/subscription_service.proto

package billingsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/billing/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	interval "google.golang.org/genproto/googleapis/type/interval"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// proration behavior enum, ref: https://docs.stripe.com/api/subscriptions/update#update_subscription-proration_behavior
type UpdateSubscriptionRequest_ProrationBehavior int32

const (
	// Unknown proration behavior.
	UpdateSubscriptionRequest_PRORATION_BEHAVIOR_UNSPECIFIED UpdateSubscriptionRequest_ProrationBehavior = 0
	// No proration. Disable creating prorations in this request.
	UpdateSubscriptionRequest_NO_PRORATION UpdateSubscriptionRequest_ProrationBehavior = 1
	// Prorates the price based on the time remaining in the current cycle.
	UpdateSubscriptionRequest_CREATE_PRORATIONS UpdateSubscriptionRequest_ProrationBehavior = 2
	// Always invoice immediately for prorations.
	UpdateSubscriptionRequest_ALWAYS_INVOICE UpdateSubscriptionRequest_ProrationBehavior = 3
)

// Enum value maps for UpdateSubscriptionRequest_ProrationBehavior.
var (
	UpdateSubscriptionRequest_ProrationBehavior_name = map[int32]string{
		0: "PRORATION_BEHAVIOR_UNSPECIFIED",
		1: "NO_PRORATION",
		2: "CREATE_PRORATIONS",
		3: "ALWAYS_INVOICE",
	}
	UpdateSubscriptionRequest_ProrationBehavior_value = map[string]int32{
		"PRORATION_BEHAVIOR_UNSPECIFIED": 0,
		"NO_PRORATION":                   1,
		"CREATE_PRORATIONS":              2,
		"ALWAYS_INVOICE":                 3,
	}
)

func (x UpdateSubscriptionRequest_ProrationBehavior) Enum() *UpdateSubscriptionRequest_ProrationBehavior {
	p := new(UpdateSubscriptionRequest_ProrationBehavior)
	*p = x
	return p
}

func (x UpdateSubscriptionRequest_ProrationBehavior) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateSubscriptionRequest_ProrationBehavior) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_service_billing_v1_subscription_service_proto_enumTypes[0].Descriptor()
}

func (UpdateSubscriptionRequest_ProrationBehavior) Type() protoreflect.EnumType {
	return &file_moego_service_billing_v1_subscription_service_proto_enumTypes[0]
}

func (x UpdateSubscriptionRequest_ProrationBehavior) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateSubscriptionRequest_ProrationBehavior.Descriptor instead.
func (UpdateSubscriptionRequest_ProrationBehavior) EnumDescriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{2, 0}
}

// cancel type
type CancelSubscriptionRequest_CancelType int32

const (
	// cancel type unspecified
	CancelSubscriptionRequest_CANCEL_TYPE_UNSPECIFIED CancelSubscriptionRequest_CancelType = 0
	// cancel immediately
	CancelSubscriptionRequest_IMMEDIATELY CancelSubscriptionRequest_CancelType = 1
	// cancel at the end of the billing cycle
	CancelSubscriptionRequest_END_OF_CYCLE CancelSubscriptionRequest_CancelType = 2
)

// Enum value maps for CancelSubscriptionRequest_CancelType.
var (
	CancelSubscriptionRequest_CancelType_name = map[int32]string{
		0: "CANCEL_TYPE_UNSPECIFIED",
		1: "IMMEDIATELY",
		2: "END_OF_CYCLE",
	}
	CancelSubscriptionRequest_CancelType_value = map[string]int32{
		"CANCEL_TYPE_UNSPECIFIED": 0,
		"IMMEDIATELY":             1,
		"END_OF_CYCLE":            2,
	}
)

func (x CancelSubscriptionRequest_CancelType) Enum() *CancelSubscriptionRequest_CancelType {
	p := new(CancelSubscriptionRequest_CancelType)
	*p = x
	return p
}

func (x CancelSubscriptionRequest_CancelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CancelSubscriptionRequest_CancelType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_service_billing_v1_subscription_service_proto_enumTypes[1].Descriptor()
}

func (CancelSubscriptionRequest_CancelType) Type() protoreflect.EnumType {
	return &file_moego_service_billing_v1_subscription_service_proto_enumTypes[1]
}

func (x CancelSubscriptionRequest_CancelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CancelSubscriptionRequest_CancelType.Descriptor instead.
func (CancelSubscriptionRequest_CancelType) EnumDescriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{4, 0}
}

// create subscription request
type CreateSubscriptionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription items
	PlanUnits []*v1.PlanUnit `protobuf:"bytes,1,rep,name=plan_units,json=planUnits,proto3" json:"plan_units,omitempty"`
	// payment method
	PaymentMethod string `protobuf:"bytes,2,opt,name=payment_method,json=paymentMethod,proto3" json:"payment_method,omitempty"`
	// coupon relation id
	CouponId *int64 `protobuf:"varint,3,opt,name=coupon_id,json=couponId,proto3,oneof" json:"coupon_id,omitempty"`
	// application fee percent
	// A non-negative decimal between 0 and 10, with at most two decimal places. (stripe is 【0，100】
	// This represents the percentage of the subscription invoice total that will be transferred to the application owner’s Stripe account.
	// The request must be made by a platform account on a connected account in order to set an application fee percentage.
	ApplicationFeePercent *float64 `protobuf:"fixed64,4,opt,name=application_fee_percent,json=applicationFeePercent,proto3,oneof" json:"application_fee_percent,omitempty"`
	// on behalf of: The account on behalf of which to charge, for each of the subscription’s invoices.
	OnBehalfOf *string `protobuf:"bytes,5,opt,name=on_behalf_of,json=onBehalfOf,proto3,oneof" json:"on_behalf_of,omitempty"`
	// connected account only
	TransferData *v1.TransferData `protobuf:"bytes,6,opt,name=transfer_data,json=transferData,proto3,oneof" json:"transfer_data,omitempty"`
	// customer relation id, TODO: not impl currently
	CustomerId *int64 `protobuf:"varint,7,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// payment behavior
	PaymentBehavior v1.SubscriptionModel_PaymentBehavior `protobuf:"varint,8,opt,name=payment_behavior,json=paymentBehavior,proto3,enum=moego.models.billing.v1.SubscriptionModel_PaymentBehavior" json:"payment_behavior,omitempty"`
	// metadata
	Metadata map[string]string `protobuf:"bytes,10,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// description
	Description *string `protobuf:"bytes,11,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// vendor customer id
	VendorCustomerId string `protobuf:"bytes,100,opt,name=vendor_customer_id,json=vendorCustomerId,proto3" json:"vendor_customer_id,omitempty"`
	// idempotency key
	IdempotencyKey *string `protobuf:"bytes,12,opt,name=idempotency_key,json=idempotencyKey,proto3,oneof" json:"idempotency_key,omitempty"`
}

func (x *CreateSubscriptionRequest) Reset() {
	*x = CreateSubscriptionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSubscriptionRequest) ProtoMessage() {}

func (x *CreateSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*CreateSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateSubscriptionRequest) GetPlanUnits() []*v1.PlanUnit {
	if x != nil {
		return x.PlanUnits
	}
	return nil
}

func (x *CreateSubscriptionRequest) GetPaymentMethod() string {
	if x != nil {
		return x.PaymentMethod
	}
	return ""
}

func (x *CreateSubscriptionRequest) GetCouponId() int64 {
	if x != nil && x.CouponId != nil {
		return *x.CouponId
	}
	return 0
}

func (x *CreateSubscriptionRequest) GetApplicationFeePercent() float64 {
	if x != nil && x.ApplicationFeePercent != nil {
		return *x.ApplicationFeePercent
	}
	return 0
}

func (x *CreateSubscriptionRequest) GetOnBehalfOf() string {
	if x != nil && x.OnBehalfOf != nil {
		return *x.OnBehalfOf
	}
	return ""
}

func (x *CreateSubscriptionRequest) GetTransferData() *v1.TransferData {
	if x != nil {
		return x.TransferData
	}
	return nil
}

func (x *CreateSubscriptionRequest) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *CreateSubscriptionRequest) GetPaymentBehavior() v1.SubscriptionModel_PaymentBehavior {
	if x != nil {
		return x.PaymentBehavior
	}
	return v1.SubscriptionModel_PaymentBehavior(0)
}

func (x *CreateSubscriptionRequest) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CreateSubscriptionRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *CreateSubscriptionRequest) GetVendorCustomerId() string {
	if x != nil {
		return x.VendorCustomerId
	}
	return ""
}

func (x *CreateSubscriptionRequest) GetIdempotencyKey() string {
	if x != nil && x.IdempotencyKey != nil {
		return *x.IdempotencyKey
	}
	return ""
}

// create subscription response
type CreateSubscriptionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription relation ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// client secret
	ClientSecret string `protobuf:"bytes,2,opt,name=client_secret,json=clientSecret,proto3" json:"client_secret,omitempty"`
	// vendor subscription id
	VendorSubscriptionId string `protobuf:"bytes,3,opt,name=vendor_subscription_id,json=vendorSubscriptionId,proto3" json:"vendor_subscription_id,omitempty"`
	// vendor invoice id
	VendorInvoiceId string `protobuf:"bytes,4,opt,name=vendor_invoice_id,json=vendorInvoiceId,proto3" json:"vendor_invoice_id,omitempty"`
	// vendor payment intent id
	VendorPaymentIntentId string `protobuf:"bytes,5,opt,name=vendor_payment_intent_id,json=vendorPaymentIntentId,proto3" json:"vendor_payment_intent_id,omitempty"`
}

func (x *CreateSubscriptionResponse) Reset() {
	*x = CreateSubscriptionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSubscriptionResponse) ProtoMessage() {}

func (x *CreateSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*CreateSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateSubscriptionResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateSubscriptionResponse) GetClientSecret() string {
	if x != nil {
		return x.ClientSecret
	}
	return ""
}

func (x *CreateSubscriptionResponse) GetVendorSubscriptionId() string {
	if x != nil {
		return x.VendorSubscriptionId
	}
	return ""
}

func (x *CreateSubscriptionResponse) GetVendorInvoiceId() string {
	if x != nil {
		return x.VendorInvoiceId
	}
	return ""
}

func (x *CreateSubscriptionResponse) GetVendorPaymentIntentId() string {
	if x != nil {
		return x.VendorPaymentIntentId
	}
	return ""
}

// update subscription request
type UpdateSubscriptionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription relation ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// subscription items
	PlanUnits []*v1.PlanUnit `protobuf:"bytes,2,rep,name=plan_units,json=planUnits,proto3" json:"plan_units,omitempty"`
	// payment method
	PaymentMethod string `protobuf:"bytes,3,opt,name=payment_method,json=paymentMethod,proto3" json:"payment_method,omitempty"`
	// proration behavior
	ProrationBehavior UpdateSubscriptionRequest_ProrationBehavior `protobuf:"varint,4,opt,name=proration_behavior,json=prorationBehavior,proto3,enum=moego.service.billing.v1.UpdateSubscriptionRequest_ProrationBehavior" json:"proration_behavior,omitempty"`
	// application fee percent
	ApplicationFeePercent *float64 `protobuf:"fixed64,5,opt,name=application_fee_percent,json=applicationFeePercent,proto3,oneof" json:"application_fee_percent,omitempty"`
	// payment behavior
	PaymentBehavior v1.SubscriptionModel_PaymentBehavior `protobuf:"varint,6,opt,name=payment_behavior,json=paymentBehavior,proto3,enum=moego.models.billing.v1.SubscriptionModel_PaymentBehavior" json:"payment_behavior,omitempty"`
	// metadata
	Metadata map[string]string `protobuf:"bytes,10,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// pause collection，不传就不修改，传空结构是 resume
	PauseCollection *v1.PauseCollection `protobuf:"bytes,11,opt,name=pause_collection,json=pauseCollection,proto3" json:"pause_collection,omitempty"`
}

func (x *UpdateSubscriptionRequest) Reset() {
	*x = UpdateSubscriptionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSubscriptionRequest) ProtoMessage() {}

func (x *UpdateSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*UpdateSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateSubscriptionRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateSubscriptionRequest) GetPlanUnits() []*v1.PlanUnit {
	if x != nil {
		return x.PlanUnits
	}
	return nil
}

func (x *UpdateSubscriptionRequest) GetPaymentMethod() string {
	if x != nil {
		return x.PaymentMethod
	}
	return ""
}

func (x *UpdateSubscriptionRequest) GetProrationBehavior() UpdateSubscriptionRequest_ProrationBehavior {
	if x != nil {
		return x.ProrationBehavior
	}
	return UpdateSubscriptionRequest_PRORATION_BEHAVIOR_UNSPECIFIED
}

func (x *UpdateSubscriptionRequest) GetApplicationFeePercent() float64 {
	if x != nil && x.ApplicationFeePercent != nil {
		return *x.ApplicationFeePercent
	}
	return 0
}

func (x *UpdateSubscriptionRequest) GetPaymentBehavior() v1.SubscriptionModel_PaymentBehavior {
	if x != nil {
		return x.PaymentBehavior
	}
	return v1.SubscriptionModel_PaymentBehavior(0)
}

func (x *UpdateSubscriptionRequest) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *UpdateSubscriptionRequest) GetPauseCollection() *v1.PauseCollection {
	if x != nil {
		return x.PauseCollection
	}
	return nil
}

// update subscription response
type UpdateSubscriptionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription relation ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// vendor subscription id
	VendorSubscriptionId string `protobuf:"bytes,2,opt,name=vendor_subscription_id,json=vendorSubscriptionId,proto3" json:"vendor_subscription_id,omitempty"`
	// vendor invoice id
	VendorInvoiceId string `protobuf:"bytes,3,opt,name=vendor_invoice_id,json=vendorInvoiceId,proto3" json:"vendor_invoice_id,omitempty"`
	// vendor payment intent id
	VendorPaymentIntentId string `protobuf:"bytes,4,opt,name=vendor_payment_intent_id,json=vendorPaymentIntentId,proto3" json:"vendor_payment_intent_id,omitempty"`
}

func (x *UpdateSubscriptionResponse) Reset() {
	*x = UpdateSubscriptionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSubscriptionResponse) ProtoMessage() {}

func (x *UpdateSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*UpdateSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateSubscriptionResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateSubscriptionResponse) GetVendorSubscriptionId() string {
	if x != nil {
		return x.VendorSubscriptionId
	}
	return ""
}

func (x *UpdateSubscriptionResponse) GetVendorInvoiceId() string {
	if x != nil {
		return x.VendorInvoiceId
	}
	return ""
}

func (x *UpdateSubscriptionResponse) GetVendorPaymentIntentId() string {
	if x != nil {
		return x.VendorPaymentIntentId
	}
	return ""
}

// cancel subscription request
type CancelSubscriptionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription relation ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// cancel type, default end of cycle
	CancelType CancelSubscriptionRequest_CancelType `protobuf:"varint,2,opt,name=cancel_type,json=cancelType,proto3,enum=moego.service.billing.v1.CancelSubscriptionRequest_CancelType" json:"cancel_type,omitempty"`
	// revert cancel if not yet applied for cancel_at_period_end
	Revert *bool `protobuf:"varint,3,opt,name=revert,proto3,oneof" json:"revert,omitempty"`
	// prorate refund
	Refund *bool `protobuf:"varint,4,opt,name=refund,proto3,oneof" json:"refund,omitempty"`
}

func (x *CancelSubscriptionRequest) Reset() {
	*x = CancelSubscriptionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelSubscriptionRequest) ProtoMessage() {}

func (x *CancelSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*CancelSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{4}
}

func (x *CancelSubscriptionRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CancelSubscriptionRequest) GetCancelType() CancelSubscriptionRequest_CancelType {
	if x != nil {
		return x.CancelType
	}
	return CancelSubscriptionRequest_CANCEL_TYPE_UNSPECIFIED
}

func (x *CancelSubscriptionRequest) GetRevert() bool {
	if x != nil && x.Revert != nil {
		return *x.Revert
	}
	return false
}

func (x *CancelSubscriptionRequest) GetRefund() bool {
	if x != nil && x.Refund != nil {
		return *x.Refund
	}
	return false
}

// cancel subscription response
type CancelSubscriptionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription relation ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CancelSubscriptionResponse) Reset() {
	*x = CancelSubscriptionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelSubscriptionResponse) ProtoMessage() {}

func (x *CancelSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*CancelSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{5}
}

func (x *CancelSubscriptionResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get subscription request
type ConvertVendorSubscriptionIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription relation ID
	VendorSubscriptionIds []string `protobuf:"bytes,1,rep,name=vendor_subscription_ids,json=vendorSubscriptionIds,proto3" json:"vendor_subscription_ids,omitempty"`
}

func (x *ConvertVendorSubscriptionIdsRequest) Reset() {
	*x = ConvertVendorSubscriptionIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConvertVendorSubscriptionIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertVendorSubscriptionIdsRequest) ProtoMessage() {}

func (x *ConvertVendorSubscriptionIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertVendorSubscriptionIdsRequest.ProtoReflect.Descriptor instead.
func (*ConvertVendorSubscriptionIdsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{6}
}

func (x *ConvertVendorSubscriptionIdsRequest) GetVendorSubscriptionIds() []string {
	if x != nil {
		return x.VendorSubscriptionIds
	}
	return nil
}

// get subscription response
type ConvertVendorSubscriptionIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription relation ID
	SubscriptionIds []int64 `protobuf:"varint,1,rep,packed,name=subscription_ids,json=subscriptionIds,proto3" json:"subscription_ids,omitempty"`
}

func (x *ConvertVendorSubscriptionIdsResponse) Reset() {
	*x = ConvertVendorSubscriptionIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConvertVendorSubscriptionIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertVendorSubscriptionIdsResponse) ProtoMessage() {}

func (x *ConvertVendorSubscriptionIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertVendorSubscriptionIdsResponse.ProtoReflect.Descriptor instead.
func (*ConvertVendorSubscriptionIdsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{7}
}

func (x *ConvertVendorSubscriptionIdsResponse) GetSubscriptionIds() []int64 {
	if x != nil {
		return x.SubscriptionIds
	}
	return nil
}

// get subscription request
type GetSubscriptionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription relation ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetSubscriptionRequest) Reset() {
	*x = GetSubscriptionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubscriptionRequest) ProtoMessage() {}

func (x *GetSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*GetSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetSubscriptionRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get subscription response
type GetSubscriptionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription object
	Subscription *v1.SubscriptionModel `protobuf:"bytes,1,opt,name=subscription,proto3" json:"subscription,omitempty"`
}

func (x *GetSubscriptionResponse) Reset() {
	*x = GetSubscriptionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubscriptionResponse) ProtoMessage() {}

func (x *GetSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*GetSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetSubscriptionResponse) GetSubscription() *v1.SubscriptionModel {
	if x != nil {
		return x.Subscription
	}
	return nil
}

// schedule next billing cycle request
type ScheduleNextBillingCycleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription ID
	SubscriptionId int64 `protobuf:"varint,1,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	// plan units
	PlanUnits []*v1.PlanUnit `protobuf:"bytes,2,rep,name=plan_units,json=planUnits,proto3" json:"plan_units,omitempty"`
	// current billing cycle
	CurrentBillingCycle *interval.Interval `protobuf:"bytes,3,opt,name=current_billing_cycle,json=currentBillingCycle,proto3" json:"current_billing_cycle,omitempty"`
}

func (x *ScheduleNextBillingCycleRequest) Reset() {
	*x = ScheduleNextBillingCycleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScheduleNextBillingCycleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleNextBillingCycleRequest) ProtoMessage() {}

func (x *ScheduleNextBillingCycleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleNextBillingCycleRequest.ProtoReflect.Descriptor instead.
func (*ScheduleNextBillingCycleRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{10}
}

func (x *ScheduleNextBillingCycleRequest) GetSubscriptionId() int64 {
	if x != nil {
		return x.SubscriptionId
	}
	return 0
}

func (x *ScheduleNextBillingCycleRequest) GetPlanUnits() []*v1.PlanUnit {
	if x != nil {
		return x.PlanUnits
	}
	return nil
}

func (x *ScheduleNextBillingCycleRequest) GetCurrentBillingCycle() *interval.Interval {
	if x != nil {
		return x.CurrentBillingCycle
	}
	return nil
}

// schedule next billing cycle response
type ScheduleNextBillingCycleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription schedule object
	SubscriptionSchedule *v1.SubscriptionScheduleModel `protobuf:"bytes,1,opt,name=subscription_schedule,json=subscriptionSchedule,proto3" json:"subscription_schedule,omitempty"`
}

func (x *ScheduleNextBillingCycleResponse) Reset() {
	*x = ScheduleNextBillingCycleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScheduleNextBillingCycleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleNextBillingCycleResponse) ProtoMessage() {}

func (x *ScheduleNextBillingCycleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleNextBillingCycleResponse.ProtoReflect.Descriptor instead.
func (*ScheduleNextBillingCycleResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{11}
}

func (x *ScheduleNextBillingCycleResponse) GetSubscriptionSchedule() *v1.SubscriptionScheduleModel {
	if x != nil {
		return x.SubscriptionSchedule
	}
	return nil
}

// ListSubscriptionsRequest
type ListSubscriptionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *ListSubscriptionsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListSubscriptionsRequest) Reset() {
	*x = ListSubscriptionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSubscriptionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSubscriptionsRequest) ProtoMessage() {}

func (x *ListSubscriptionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSubscriptionsRequest.ProtoReflect.Descriptor instead.
func (*ListSubscriptionsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{12}
}

func (x *ListSubscriptionsRequest) GetFilter() *ListSubscriptionsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListCustomerSubscriptionsResponse
type ListSubscriptionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscriptions
	Subscriptions []*v1.SubscriptionModel `protobuf:"bytes,1,rep,name=subscriptions,proto3" json:"subscriptions,omitempty"`
}

func (x *ListSubscriptionsResponse) Reset() {
	*x = ListSubscriptionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSubscriptionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSubscriptionsResponse) ProtoMessage() {}

func (x *ListSubscriptionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSubscriptionsResponse.ProtoReflect.Descriptor instead.
func (*ListSubscriptionsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{13}
}

func (x *ListSubscriptionsResponse) GetSubscriptions() []*v1.SubscriptionModel {
	if x != nil {
		return x.Subscriptions
	}
	return nil
}

// filter
type ListSubscriptionsRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vendor_customer_ids
	VendorCustomerIds []string `protobuf:"bytes,1,rep,name=vendor_customer_ids,json=vendorCustomerIds,proto3" json:"vendor_customer_ids,omitempty"`
}

func (x *ListSubscriptionsRequest_Filter) Reset() {
	*x = ListSubscriptionsRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSubscriptionsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSubscriptionsRequest_Filter) ProtoMessage() {}

func (x *ListSubscriptionsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_billing_v1_subscription_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSubscriptionsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListSubscriptionsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP(), []int{12, 0}
}

func (x *ListSubscriptionsRequest_Filter) GetVendorCustomerIds() []string {
	if x != nil {
		return x.VendorCustomerIds
	}
	return nil
}

var File_moego_service_billing_v1_subscription_service_proto protoreflect.FileDescriptor

var file_moego_service_billing_v1_subscription_service_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a,
	0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbc, 0x07, 0x0a, 0x19, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x09, 0x70,
	0x6c, 0x61, 0x6e, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x40, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x63, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x08, 0x63,
	0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x17, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x70, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x42, 0x17, 0xfa, 0x42, 0x14,
	0x12, 0x12, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x40, 0x29, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x48, 0x01, 0x52, 0x15, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x2e, 0x0a, 0x0c, 0x6f, 0x6e, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x6c, 0x66, 0x5f, 0x6f, 0x66,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x40, 0x48,
	0x02, 0x52, 0x0a, 0x6f, 0x6e, 0x42, 0x65, 0x68, 0x61, 0x6c, 0x66, 0x4f, 0x66, 0x88, 0x01, 0x01,
	0x12, 0x4f, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x03,
	0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x88, 0x01,
	0x01, 0x12, 0x24, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x48, 0x04, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x65, 0x0a, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x52, 0x0f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x12, 0x5d,
	0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x25, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x05, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x12, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x64, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x40, 0x52, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0f, 0x69,
	0x64, 0x65, 0x6d, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x0e, 0x69, 0x64, 0x65, 0x6d, 0x70, 0x6f, 0x74, 0x65,
	0x6e, 0x63, 0x79, 0x4b, 0x65, 0x79, 0x88, 0x01, 0x01, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6f, 0x6e, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x6c, 0x66, 0x5f, 0x6f,
	0x66, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x69, 0x64, 0x65, 0x6d, 0x70, 0x6f, 0x74, 0x65,
	0x6e, 0x63, 0x79, 0x5f, 0x6b, 0x65, 0x79, 0x22, 0xec, 0x01, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x69, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x37, 0x0a,
	0x18, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x15, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xd3, 0x06, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x40, 0x0a,
	0x0a, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x6e,
	0x55, 0x6e, 0x69, 0x74, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12,
	0x25, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x74, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x52, 0x11, 0x70, 0x72, 0x6f, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x12, 0x54, 0x0a, 0x17,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x65, 0x65, 0x5f,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x42, 0x17, 0xfa,
	0x42, 0x14, 0x12, 0x12, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x40, 0x29, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x00, 0x52, 0x15, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x65, 0x0a, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x65,
	0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x12, 0x5d, 0x0a, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x53, 0x0a, 0x10, 0x70, 0x61, 0x75, 0x73,
	0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x75,
	0x73, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x70, 0x61,
	0x75, 0x73, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x3b, 0x0a,
	0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x74, 0x0a, 0x11, 0x50, 0x72,
	0x6f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x12,
	0x22, 0x0a, 0x1e, 0x50, 0x52, 0x4f, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x45, 0x48,
	0x41, 0x56, 0x49, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x4f, 0x5f, 0x50, 0x52, 0x4f, 0x52, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f,
	0x50, 0x52, 0x4f, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e,
	0x41, 0x4c, 0x57, 0x41, 0x59, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x4f, 0x49, 0x43, 0x45, 0x10, 0x03,
	0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x66, 0x65, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x22, 0xc7, 0x01, 0x0a,
	0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x34, 0x0a, 0x16, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x69, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x37, 0x0a,
	0x18, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x15, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xbf, 0x02, 0x0a, 0x19, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x6b, 0x0a,
	0x0b, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0a,
	0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x06, 0x72, 0x65,
	0x76, 0x65, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x06, 0x72, 0x65,
	0x76, 0x65, 0x72, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x06, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x88, 0x01, 0x01, 0x22, 0x4c, 0x0a, 0x0a, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0f, 0x0a, 0x0b, 0x49, 0x4d, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x54, 0x45, 0x4c, 0x59, 0x10, 0x01,
	0x12, 0x10, 0x0a, 0x0c, 0x45, 0x4e, 0x44, 0x5f, 0x4f, 0x46, 0x5f, 0x43, 0x59, 0x43, 0x4c, 0x45,
	0x10, 0x02, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x42, 0x09, 0x0a,
	0x07, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x22, 0x2c, 0x0a, 0x1a, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x5d, 0x0a, 0x23, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x74, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a,
	0x17, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x51, 0x0a, 0x24, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x29, 0x0a,
	0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x28, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x69, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a,
	0x0c, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x0c, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xd7, 0x01,
	0x0a, 0x1f, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x65, 0x78, 0x74, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0a, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x55, 0x6e, 0x69,
	0x74, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x49, 0x0a, 0x15,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f,
	0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x52, 0x13, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x22, 0x8b, 0x01, 0x0a, 0x20, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x4e, 0x65, 0x78, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43,
	0x79, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a, 0x15,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x14, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x22, 0xa7, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x51, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x38, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x2e, 0x0a, 0x13, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22,
	0x6d, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x0d,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x0d, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x32, 0xc2,
	0x07, 0x0a, 0x13, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7f, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x12, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9d, 0x01, 0x0a, 0x1c, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x74, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x56, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x56, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x91, 0x01, 0x0a, 0x18, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x65,
	0x78, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x39,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x4e, 0x65, 0x78, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x65, 0x78,
	0x74, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x80, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_billing_v1_subscription_service_proto_rawDescOnce sync.Once
	file_moego_service_billing_v1_subscription_service_proto_rawDescData = file_moego_service_billing_v1_subscription_service_proto_rawDesc
)

func file_moego_service_billing_v1_subscription_service_proto_rawDescGZIP() []byte {
	file_moego_service_billing_v1_subscription_service_proto_rawDescOnce.Do(func() {
		file_moego_service_billing_v1_subscription_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_billing_v1_subscription_service_proto_rawDescData)
	})
	return file_moego_service_billing_v1_subscription_service_proto_rawDescData
}

var file_moego_service_billing_v1_subscription_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_service_billing_v1_subscription_service_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_moego_service_billing_v1_subscription_service_proto_goTypes = []interface{}{
	(UpdateSubscriptionRequest_ProrationBehavior)(0), // 0: moego.service.billing.v1.UpdateSubscriptionRequest.ProrationBehavior
	(CancelSubscriptionRequest_CancelType)(0),        // 1: moego.service.billing.v1.CancelSubscriptionRequest.CancelType
	(*CreateSubscriptionRequest)(nil),                // 2: moego.service.billing.v1.CreateSubscriptionRequest
	(*CreateSubscriptionResponse)(nil),               // 3: moego.service.billing.v1.CreateSubscriptionResponse
	(*UpdateSubscriptionRequest)(nil),                // 4: moego.service.billing.v1.UpdateSubscriptionRequest
	(*UpdateSubscriptionResponse)(nil),               // 5: moego.service.billing.v1.UpdateSubscriptionResponse
	(*CancelSubscriptionRequest)(nil),                // 6: moego.service.billing.v1.CancelSubscriptionRequest
	(*CancelSubscriptionResponse)(nil),               // 7: moego.service.billing.v1.CancelSubscriptionResponse
	(*ConvertVendorSubscriptionIdsRequest)(nil),      // 8: moego.service.billing.v1.ConvertVendorSubscriptionIdsRequest
	(*ConvertVendorSubscriptionIdsResponse)(nil),     // 9: moego.service.billing.v1.ConvertVendorSubscriptionIdsResponse
	(*GetSubscriptionRequest)(nil),                   // 10: moego.service.billing.v1.GetSubscriptionRequest
	(*GetSubscriptionResponse)(nil),                  // 11: moego.service.billing.v1.GetSubscriptionResponse
	(*ScheduleNextBillingCycleRequest)(nil),          // 12: moego.service.billing.v1.ScheduleNextBillingCycleRequest
	(*ScheduleNextBillingCycleResponse)(nil),         // 13: moego.service.billing.v1.ScheduleNextBillingCycleResponse
	(*ListSubscriptionsRequest)(nil),                 // 14: moego.service.billing.v1.ListSubscriptionsRequest
	(*ListSubscriptionsResponse)(nil),                // 15: moego.service.billing.v1.ListSubscriptionsResponse
	nil,                                              // 16: moego.service.billing.v1.CreateSubscriptionRequest.MetadataEntry
	nil,                                              // 17: moego.service.billing.v1.UpdateSubscriptionRequest.MetadataEntry
	(*ListSubscriptionsRequest_Filter)(nil),          // 18: moego.service.billing.v1.ListSubscriptionsRequest.Filter
	(*v1.PlanUnit)(nil),                              // 19: moego.models.billing.v1.PlanUnit
	(*v1.TransferData)(nil),                          // 20: moego.models.billing.v1.TransferData
	(v1.SubscriptionModel_PaymentBehavior)(0),        // 21: moego.models.billing.v1.SubscriptionModel.PaymentBehavior
	(*v1.PauseCollection)(nil),                       // 22: moego.models.billing.v1.PauseCollection
	(*v1.SubscriptionModel)(nil),                     // 23: moego.models.billing.v1.SubscriptionModel
	(*interval.Interval)(nil),                        // 24: google.type.Interval
	(*v1.SubscriptionScheduleModel)(nil),             // 25: moego.models.billing.v1.SubscriptionScheduleModel
}
var file_moego_service_billing_v1_subscription_service_proto_depIdxs = []int32{
	19, // 0: moego.service.billing.v1.CreateSubscriptionRequest.plan_units:type_name -> moego.models.billing.v1.PlanUnit
	20, // 1: moego.service.billing.v1.CreateSubscriptionRequest.transfer_data:type_name -> moego.models.billing.v1.TransferData
	21, // 2: moego.service.billing.v1.CreateSubscriptionRequest.payment_behavior:type_name -> moego.models.billing.v1.SubscriptionModel.PaymentBehavior
	16, // 3: moego.service.billing.v1.CreateSubscriptionRequest.metadata:type_name -> moego.service.billing.v1.CreateSubscriptionRequest.MetadataEntry
	19, // 4: moego.service.billing.v1.UpdateSubscriptionRequest.plan_units:type_name -> moego.models.billing.v1.PlanUnit
	0,  // 5: moego.service.billing.v1.UpdateSubscriptionRequest.proration_behavior:type_name -> moego.service.billing.v1.UpdateSubscriptionRequest.ProrationBehavior
	21, // 6: moego.service.billing.v1.UpdateSubscriptionRequest.payment_behavior:type_name -> moego.models.billing.v1.SubscriptionModel.PaymentBehavior
	17, // 7: moego.service.billing.v1.UpdateSubscriptionRequest.metadata:type_name -> moego.service.billing.v1.UpdateSubscriptionRequest.MetadataEntry
	22, // 8: moego.service.billing.v1.UpdateSubscriptionRequest.pause_collection:type_name -> moego.models.billing.v1.PauseCollection
	1,  // 9: moego.service.billing.v1.CancelSubscriptionRequest.cancel_type:type_name -> moego.service.billing.v1.CancelSubscriptionRequest.CancelType
	23, // 10: moego.service.billing.v1.GetSubscriptionResponse.subscription:type_name -> moego.models.billing.v1.SubscriptionModel
	19, // 11: moego.service.billing.v1.ScheduleNextBillingCycleRequest.plan_units:type_name -> moego.models.billing.v1.PlanUnit
	24, // 12: moego.service.billing.v1.ScheduleNextBillingCycleRequest.current_billing_cycle:type_name -> google.type.Interval
	25, // 13: moego.service.billing.v1.ScheduleNextBillingCycleResponse.subscription_schedule:type_name -> moego.models.billing.v1.SubscriptionScheduleModel
	18, // 14: moego.service.billing.v1.ListSubscriptionsRequest.filter:type_name -> moego.service.billing.v1.ListSubscriptionsRequest.Filter
	23, // 15: moego.service.billing.v1.ListSubscriptionsResponse.subscriptions:type_name -> moego.models.billing.v1.SubscriptionModel
	2,  // 16: moego.service.billing.v1.SubscriptionService.CreateSubscription:input_type -> moego.service.billing.v1.CreateSubscriptionRequest
	4,  // 17: moego.service.billing.v1.SubscriptionService.UpdateSubscription:input_type -> moego.service.billing.v1.UpdateSubscriptionRequest
	6,  // 18: moego.service.billing.v1.SubscriptionService.CancelSubscription:input_type -> moego.service.billing.v1.CancelSubscriptionRequest
	8,  // 19: moego.service.billing.v1.SubscriptionService.ConvertVendorSubscriptionIds:input_type -> moego.service.billing.v1.ConvertVendorSubscriptionIdsRequest
	10, // 20: moego.service.billing.v1.SubscriptionService.GetSubscription:input_type -> moego.service.billing.v1.GetSubscriptionRequest
	12, // 21: moego.service.billing.v1.SubscriptionService.ScheduleNextBillingCycle:input_type -> moego.service.billing.v1.ScheduleNextBillingCycleRequest
	14, // 22: moego.service.billing.v1.SubscriptionService.ListSubscriptions:input_type -> moego.service.billing.v1.ListSubscriptionsRequest
	3,  // 23: moego.service.billing.v1.SubscriptionService.CreateSubscription:output_type -> moego.service.billing.v1.CreateSubscriptionResponse
	5,  // 24: moego.service.billing.v1.SubscriptionService.UpdateSubscription:output_type -> moego.service.billing.v1.UpdateSubscriptionResponse
	7,  // 25: moego.service.billing.v1.SubscriptionService.CancelSubscription:output_type -> moego.service.billing.v1.CancelSubscriptionResponse
	9,  // 26: moego.service.billing.v1.SubscriptionService.ConvertVendorSubscriptionIds:output_type -> moego.service.billing.v1.ConvertVendorSubscriptionIdsResponse
	11, // 27: moego.service.billing.v1.SubscriptionService.GetSubscription:output_type -> moego.service.billing.v1.GetSubscriptionResponse
	13, // 28: moego.service.billing.v1.SubscriptionService.ScheduleNextBillingCycle:output_type -> moego.service.billing.v1.ScheduleNextBillingCycleResponse
	15, // 29: moego.service.billing.v1.SubscriptionService.ListSubscriptions:output_type -> moego.service.billing.v1.ListSubscriptionsResponse
	23, // [23:30] is the sub-list for method output_type
	16, // [16:23] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_moego_service_billing_v1_subscription_service_proto_init() }
func file_moego_service_billing_v1_subscription_service_proto_init() {
	if File_moego_service_billing_v1_subscription_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_billing_v1_subscription_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSubscriptionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_billing_v1_subscription_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSubscriptionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_billing_v1_subscription_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSubscriptionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_billing_v1_subscription_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSubscriptionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_billing_v1_subscription_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelSubscriptionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_billing_v1_subscription_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelSubscriptionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_billing_v1_subscription_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConvertVendorSubscriptionIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_billing_v1_subscription_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConvertVendorSubscriptionIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_billing_v1_subscription_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubscriptionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_billing_v1_subscription_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubscriptionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_billing_v1_subscription_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScheduleNextBillingCycleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_billing_v1_subscription_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScheduleNextBillingCycleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_billing_v1_subscription_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSubscriptionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_billing_v1_subscription_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSubscriptionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_billing_v1_subscription_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSubscriptionsRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_billing_v1_subscription_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_billing_v1_subscription_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_service_billing_v1_subscription_service_proto_msgTypes[4].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_billing_v1_subscription_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_billing_v1_subscription_service_proto_goTypes,
		DependencyIndexes: file_moego_service_billing_v1_subscription_service_proto_depIdxs,
		EnumInfos:         file_moego_service_billing_v1_subscription_service_proto_enumTypes,
		MessageInfos:      file_moego_service_billing_v1_subscription_service_proto_msgTypes,
	}.Build()
	File_moego_service_billing_v1_subscription_service_proto = out.File
	file_moego_service_billing_v1_subscription_service_proto_rawDesc = nil
	file_moego_service_billing_v1_subscription_service_proto_goTypes = nil
	file_moego_service_billing_v1_subscription_service_proto_depIdxs = nil
}
