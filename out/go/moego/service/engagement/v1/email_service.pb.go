// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/engagement/v1/email_service.proto

package engagementsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/engagement/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetSenderEmailRequest is a request to get the sender email.
type GetSenderEmailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business_id is the ID of the business.
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetSenderEmailRequest) Reset() {
	*x = GetSenderEmailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSenderEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSenderEmailRequest) ProtoMessage() {}

func (x *GetSenderEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSenderEmailRequest.ProtoReflect.Descriptor instead.
func (*GetSenderEmailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_email_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetSenderEmailRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// GetSenderEmailResponse is a response to get the sender email.
type GetSenderEmailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// sender_email is the sender email.
	SenderEmail *v1.SenderEmailModel `protobuf:"bytes,1,opt,name=sender_email,json=senderEmail,proto3,oneof" json:"sender_email,omitempty"`
	// SenderEmailUsageType
	SenderEmailUsageType v1.SenderEmailUsageType `protobuf:"varint,2,opt,name=sender_email_usage_type,json=senderEmailUsageType,proto3,enum=moego.models.engagement.v1.SenderEmailUsageType" json:"sender_email_usage_type,omitempty"`
}

func (x *GetSenderEmailResponse) Reset() {
	*x = GetSenderEmailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSenderEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSenderEmailResponse) ProtoMessage() {}

func (x *GetSenderEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSenderEmailResponse.ProtoReflect.Descriptor instead.
func (*GetSenderEmailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_email_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetSenderEmailResponse) GetSenderEmail() *v1.SenderEmailModel {
	if x != nil {
		return x.SenderEmail
	}
	return nil
}

func (x *GetSenderEmailResponse) GetSenderEmailUsageType() v1.SenderEmailUsageType {
	if x != nil {
		return x.SenderEmailUsageType
	}
	return v1.SenderEmailUsageType(0)
}

// SendConfirmEmailRequest is a request to send a confirmation email.
type SendConfirmEmailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// email address
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
}

func (x *SendConfirmEmailRequest) Reset() {
	*x = SendConfirmEmailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendConfirmEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendConfirmEmailRequest) ProtoMessage() {}

func (x *SendConfirmEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendConfirmEmailRequest.ProtoReflect.Descriptor instead.
func (*SendConfirmEmailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_email_service_proto_rawDescGZIP(), []int{2}
}

func (x *SendConfirmEmailRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *SendConfirmEmailRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

// SendConfirmEmailResponse is a response to send a confirmation email.
type SendConfirmEmailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success is true if the email was sent successfully.
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	// error is the error message if the email was not sent successfully.
	Error *string `protobuf:"bytes,2,opt,name=error,proto3,oneof" json:"error,omitempty"`
}

func (x *SendConfirmEmailResponse) Reset() {
	*x = SendConfirmEmailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendConfirmEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendConfirmEmailResponse) ProtoMessage() {}

func (x *SendConfirmEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendConfirmEmailResponse.ProtoReflect.Descriptor instead.
func (*SendConfirmEmailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_email_service_proto_rawDescGZIP(), []int{3}
}

func (x *SendConfirmEmailResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SendConfirmEmailResponse) GetError() string {
	if x != nil && x.Error != nil {
		return *x.Error
	}
	return ""
}

// ConfirmSenderEmailRequest is a request to confirm the sender email.
type ConfirmSenderEmailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// email address
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// confirmation code
	ConfirmationCode string `protobuf:"bytes,3,opt,name=confirmation_code,json=confirmationCode,proto3" json:"confirmation_code,omitempty"`
}

func (x *ConfirmSenderEmailRequest) Reset() {
	*x = ConfirmSenderEmailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmSenderEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmSenderEmailRequest) ProtoMessage() {}

func (x *ConfirmSenderEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmSenderEmailRequest.ProtoReflect.Descriptor instead.
func (*ConfirmSenderEmailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_email_service_proto_rawDescGZIP(), []int{4}
}

func (x *ConfirmSenderEmailRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ConfirmSenderEmailRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ConfirmSenderEmailRequest) GetConfirmationCode() string {
	if x != nil {
		return x.ConfirmationCode
	}
	return ""
}

// ConfirmSenderEmailResponse is a response to confirm the sender email.
type ConfirmSenderEmailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success is true if the email was confirmed successfully.
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	// error is the error message if the email was not confirmed successfully.
	Error *string `protobuf:"bytes,2,opt,name=error,proto3,oneof" json:"error,omitempty"`
}

func (x *ConfirmSenderEmailResponse) Reset() {
	*x = ConfirmSenderEmailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmSenderEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmSenderEmailResponse) ProtoMessage() {}

func (x *ConfirmSenderEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmSenderEmailResponse.ProtoReflect.Descriptor instead.
func (*ConfirmSenderEmailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_email_service_proto_rawDescGZIP(), []int{5}
}

func (x *ConfirmSenderEmailResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ConfirmSenderEmailResponse) GetError() string {
	if x != nil && x.Error != nil {
		return *x.Error
	}
	return ""
}

// FetchDNSConfigsRequest is a request to fetch the DNS configurations.
type FetchDNSConfigsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// email address
	Email *string `protobuf:"bytes,2,opt,name=email,proto3,oneof" json:"email,omitempty"`
}

func (x *FetchDNSConfigsRequest) Reset() {
	*x = FetchDNSConfigsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDNSConfigsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDNSConfigsRequest) ProtoMessage() {}

func (x *FetchDNSConfigsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDNSConfigsRequest.ProtoReflect.Descriptor instead.
func (*FetchDNSConfigsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_email_service_proto_rawDescGZIP(), []int{6}
}

func (x *FetchDNSConfigsRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *FetchDNSConfigsRequest) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

// FetchDNSConfigsResponse is a response to fetch the DNS configurations.
type FetchDNSConfigsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// dns_records is the DNS records.
	DnsRecords []*v1.DNSRecordModel `protobuf:"bytes,1,rep,name=dns_records,json=dnsRecords,proto3" json:"dns_records,omitempty"`
}

func (x *FetchDNSConfigsResponse) Reset() {
	*x = FetchDNSConfigsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDNSConfigsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDNSConfigsResponse) ProtoMessage() {}

func (x *FetchDNSConfigsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDNSConfigsResponse.ProtoReflect.Descriptor instead.
func (*FetchDNSConfigsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_email_service_proto_rawDescGZIP(), []int{7}
}

func (x *FetchDNSConfigsResponse) GetDnsRecords() []*v1.DNSRecordModel {
	if x != nil {
		return x.DnsRecords
	}
	return nil
}

// VerifySenderEmailRequest is a request to verify the sender email.
type VerifySenderEmailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// email address, 传了的话就校验指定 email, 没传的话校验 business 当前 email
	Email *string `protobuf:"bytes,2,opt,name=email,proto3,oneof" json:"email,omitempty"`
}

func (x *VerifySenderEmailRequest) Reset() {
	*x = VerifySenderEmailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifySenderEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifySenderEmailRequest) ProtoMessage() {}

func (x *VerifySenderEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifySenderEmailRequest.ProtoReflect.Descriptor instead.
func (*VerifySenderEmailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_email_service_proto_rawDescGZIP(), []int{8}
}

func (x *VerifySenderEmailRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *VerifySenderEmailRequest) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

// VerifySenderEmailResponse is a response to verify the sender email.
type VerifySenderEmailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success is true if the email was verified successfully.
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	// error is the error message if the email was not verified successfully.
	Error *string `protobuf:"bytes,2,opt,name=error,proto3,oneof" json:"error,omitempty"`
}

func (x *VerifySenderEmailResponse) Reset() {
	*x = VerifySenderEmailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifySenderEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifySenderEmailResponse) ProtoMessage() {}

func (x *VerifySenderEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifySenderEmailResponse.ProtoReflect.Descriptor instead.
func (*VerifySenderEmailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_email_service_proto_rawDescGZIP(), []int{9}
}

func (x *VerifySenderEmailResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *VerifySenderEmailResponse) GetError() string {
	if x != nil && x.Error != nil {
		return *x.Error
	}
	return ""
}

// SaveSenderEmailRequest is a request to save the sender email.
type SaveSenderEmailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// email address
	Email *string `protobuf:"bytes,2,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// name
	Name *string `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// type: default 时上面两个字段可以不传，否则需要传 email 和 name
	Type v1.SenderEmailUsageType `protobuf:"varint,4,opt,name=type,proto3,enum=moego.models.engagement.v1.SenderEmailUsageType" json:"type,omitempty"`
}

func (x *SaveSenderEmailRequest) Reset() {
	*x = SaveSenderEmailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveSenderEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveSenderEmailRequest) ProtoMessage() {}

func (x *SaveSenderEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveSenderEmailRequest.ProtoReflect.Descriptor instead.
func (*SaveSenderEmailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_email_service_proto_rawDescGZIP(), []int{10}
}

func (x *SaveSenderEmailRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *SaveSenderEmailRequest) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *SaveSenderEmailRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *SaveSenderEmailRequest) GetType() v1.SenderEmailUsageType {
	if x != nil {
		return x.Type
	}
	return v1.SenderEmailUsageType(0)
}

// SaveSenderEmailResponse is a response to save the sender email.
type SaveSenderEmailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success is true if the email was saved successfully.
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	// error is the error message if the email was not saved successfully.
	Error *string `protobuf:"bytes,2,opt,name=error,proto3,oneof" json:"error,omitempty"`
	// sender_email is the sender email.
	SenderEmail *v1.SenderEmailModel `protobuf:"bytes,3,opt,name=sender_email,json=senderEmail,proto3,oneof" json:"sender_email,omitempty"`
}

func (x *SaveSenderEmailResponse) Reset() {
	*x = SaveSenderEmailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveSenderEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveSenderEmailResponse) ProtoMessage() {}

func (x *SaveSenderEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_email_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveSenderEmailResponse.ProtoReflect.Descriptor instead.
func (*SaveSenderEmailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_email_service_proto_rawDescGZIP(), []int{11}
}

func (x *SaveSenderEmailResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SaveSenderEmailResponse) GetError() string {
	if x != nil && x.Error != nil {
		return *x.Error
	}
	return ""
}

func (x *SaveSenderEmailResponse) GetSenderEmail() *v1.SenderEmailModel {
	if x != nil {
		return x.SenderEmail
	}
	return nil
}

var File_moego_service_engagement_v1_email_service_proto protoreflect.FileDescriptor

var file_moego_service_engagement_v1_email_service_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x2d,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x38, 0x0a,
	0x15, 0x47, 0x65, 0x74, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0xe8, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x53,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x54, 0x0a, 0x0c, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x67, 0x0a, 0x17, 0x73, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x14, 0x73, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x22, 0x50, 0x0a, 0x17, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x22, 0x59, 0x0a, 0x18, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x72, 0x6d, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x19, 0x0a, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22,
	0x7f, 0x0a, 0x19, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x2b, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x22, 0x5b, 0x0a, 0x1a, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x53, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x19, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x5e, 0x0a,
	0x16, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x4e, 0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x66, 0x0a,
	0x17, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x4e, 0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0b, 0x64, 0x6e, 0x73, 0x5f,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x4e, 0x53, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x64, 0x6e, 0x73, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x60, 0x0a, 0x18, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a,
	0x06, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x5a, 0x0a, 0x19, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x19,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x22, 0xc6, 0x01, 0x0a, 0x16, 0x53, 0x61, 0x76, 0x65, 0x53, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x44, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xbf, 0x01, 0x0a,
	0x17, 0x53, 0x61, 0x76, 0x65, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x12, 0x19, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a,
	0x0c, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x48, 0x01, 0x52, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x42, 0x0f, 0x0a,
	0x0d, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x32, 0xa0,
	0x06, 0x0a, 0x0c, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x7b, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x81, 0x01, 0x0a,
	0x10, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x87, 0x01, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x53, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x53, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x72, 0x6d, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7e, 0x0a, 0x0f, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x44, 0x4e, 0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x44, 0x4e, 0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x4e, 0x53, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x84, 0x01, 0x0a, 0x11, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x53, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x7e, 0x0a, 0x0f, 0x53, 0x61, 0x76, 0x65, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x53, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x42, 0x89, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_engagement_v1_email_service_proto_rawDescOnce sync.Once
	file_moego_service_engagement_v1_email_service_proto_rawDescData = file_moego_service_engagement_v1_email_service_proto_rawDesc
)

func file_moego_service_engagement_v1_email_service_proto_rawDescGZIP() []byte {
	file_moego_service_engagement_v1_email_service_proto_rawDescOnce.Do(func() {
		file_moego_service_engagement_v1_email_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_engagement_v1_email_service_proto_rawDescData)
	})
	return file_moego_service_engagement_v1_email_service_proto_rawDescData
}

var file_moego_service_engagement_v1_email_service_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_service_engagement_v1_email_service_proto_goTypes = []interface{}{
	(*GetSenderEmailRequest)(nil),      // 0: moego.service.engagement.v1.GetSenderEmailRequest
	(*GetSenderEmailResponse)(nil),     // 1: moego.service.engagement.v1.GetSenderEmailResponse
	(*SendConfirmEmailRequest)(nil),    // 2: moego.service.engagement.v1.SendConfirmEmailRequest
	(*SendConfirmEmailResponse)(nil),   // 3: moego.service.engagement.v1.SendConfirmEmailResponse
	(*ConfirmSenderEmailRequest)(nil),  // 4: moego.service.engagement.v1.ConfirmSenderEmailRequest
	(*ConfirmSenderEmailResponse)(nil), // 5: moego.service.engagement.v1.ConfirmSenderEmailResponse
	(*FetchDNSConfigsRequest)(nil),     // 6: moego.service.engagement.v1.FetchDNSConfigsRequest
	(*FetchDNSConfigsResponse)(nil),    // 7: moego.service.engagement.v1.FetchDNSConfigsResponse
	(*VerifySenderEmailRequest)(nil),   // 8: moego.service.engagement.v1.VerifySenderEmailRequest
	(*VerifySenderEmailResponse)(nil),  // 9: moego.service.engagement.v1.VerifySenderEmailResponse
	(*SaveSenderEmailRequest)(nil),     // 10: moego.service.engagement.v1.SaveSenderEmailRequest
	(*SaveSenderEmailResponse)(nil),    // 11: moego.service.engagement.v1.SaveSenderEmailResponse
	(*v1.SenderEmailModel)(nil),        // 12: moego.models.engagement.v1.SenderEmailModel
	(v1.SenderEmailUsageType)(0),       // 13: moego.models.engagement.v1.SenderEmailUsageType
	(*v1.DNSRecordModel)(nil),          // 14: moego.models.engagement.v1.DNSRecordModel
}
var file_moego_service_engagement_v1_email_service_proto_depIdxs = []int32{
	12, // 0: moego.service.engagement.v1.GetSenderEmailResponse.sender_email:type_name -> moego.models.engagement.v1.SenderEmailModel
	13, // 1: moego.service.engagement.v1.GetSenderEmailResponse.sender_email_usage_type:type_name -> moego.models.engagement.v1.SenderEmailUsageType
	14, // 2: moego.service.engagement.v1.FetchDNSConfigsResponse.dns_records:type_name -> moego.models.engagement.v1.DNSRecordModel
	13, // 3: moego.service.engagement.v1.SaveSenderEmailRequest.type:type_name -> moego.models.engagement.v1.SenderEmailUsageType
	12, // 4: moego.service.engagement.v1.SaveSenderEmailResponse.sender_email:type_name -> moego.models.engagement.v1.SenderEmailModel
	0,  // 5: moego.service.engagement.v1.EmailService.GetSenderEmail:input_type -> moego.service.engagement.v1.GetSenderEmailRequest
	2,  // 6: moego.service.engagement.v1.EmailService.SendConfirmEmail:input_type -> moego.service.engagement.v1.SendConfirmEmailRequest
	4,  // 7: moego.service.engagement.v1.EmailService.ConfirmSenderEmail:input_type -> moego.service.engagement.v1.ConfirmSenderEmailRequest
	6,  // 8: moego.service.engagement.v1.EmailService.FetchDNSConfigs:input_type -> moego.service.engagement.v1.FetchDNSConfigsRequest
	8,  // 9: moego.service.engagement.v1.EmailService.VerifySenderEmail:input_type -> moego.service.engagement.v1.VerifySenderEmailRequest
	10, // 10: moego.service.engagement.v1.EmailService.SaveSenderEmail:input_type -> moego.service.engagement.v1.SaveSenderEmailRequest
	1,  // 11: moego.service.engagement.v1.EmailService.GetSenderEmail:output_type -> moego.service.engagement.v1.GetSenderEmailResponse
	3,  // 12: moego.service.engagement.v1.EmailService.SendConfirmEmail:output_type -> moego.service.engagement.v1.SendConfirmEmailResponse
	5,  // 13: moego.service.engagement.v1.EmailService.ConfirmSenderEmail:output_type -> moego.service.engagement.v1.ConfirmSenderEmailResponse
	7,  // 14: moego.service.engagement.v1.EmailService.FetchDNSConfigs:output_type -> moego.service.engagement.v1.FetchDNSConfigsResponse
	9,  // 15: moego.service.engagement.v1.EmailService.VerifySenderEmail:output_type -> moego.service.engagement.v1.VerifySenderEmailResponse
	11, // 16: moego.service.engagement.v1.EmailService.SaveSenderEmail:output_type -> moego.service.engagement.v1.SaveSenderEmailResponse
	11, // [11:17] is the sub-list for method output_type
	5,  // [5:11] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_moego_service_engagement_v1_email_service_proto_init() }
func file_moego_service_engagement_v1_email_service_proto_init() {
	if File_moego_service_engagement_v1_email_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_engagement_v1_email_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSenderEmailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_email_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSenderEmailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_email_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendConfirmEmailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_email_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendConfirmEmailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_email_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmSenderEmailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_email_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmSenderEmailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_email_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDNSConfigsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_email_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDNSConfigsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_email_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifySenderEmailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_email_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifySenderEmailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_email_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveSenderEmailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_email_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveSenderEmailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_engagement_v1_email_service_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_service_engagement_v1_email_service_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_service_engagement_v1_email_service_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_service_engagement_v1_email_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_service_engagement_v1_email_service_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_service_engagement_v1_email_service_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_moego_service_engagement_v1_email_service_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_service_engagement_v1_email_service_proto_msgTypes[11].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_engagement_v1_email_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_engagement_v1_email_service_proto_goTypes,
		DependencyIndexes: file_moego_service_engagement_v1_email_service_proto_depIdxs,
		MessageInfos:      file_moego_service_engagement_v1_email_service_proto_msgTypes,
	}.Build()
	File_moego_service_engagement_v1_email_service_proto = out.File
	file_moego_service_engagement_v1_email_service_proto_rawDesc = nil
	file_moego_service_engagement_v1_email_service_proto_goTypes = nil
	file_moego_service_engagement_v1_email_service_proto_depIdxs = nil
}
