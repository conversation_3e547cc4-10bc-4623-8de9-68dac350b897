// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/engagement/v1/calling_service.proto

package engagementsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/engagement/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	interval "google.golang.org/genproto/googleapis/type/interval"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create calling log request
type CreateCallingLogRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// calling log
	CallingLog *v1.CreateCallingLogDef `protobuf:"bytes,1,opt,name=calling_log,json=callingLog,proto3" json:"calling_log,omitempty"`
}

func (x *CreateCallingLogRequest) Reset() {
	*x = CreateCallingLogRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCallingLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCallingLogRequest) ProtoMessage() {}

func (x *CreateCallingLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCallingLogRequest.ProtoReflect.Descriptor instead.
func (*CreateCallingLogRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCallingLogRequest) GetCallingLog() *v1.CreateCallingLogDef {
	if x != nil {
		return x.CallingLog
	}
	return nil
}

// create calling response
type CreateCallingLogResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// calling log
	CallingLogView *v1.CallingLogView `protobuf:"bytes,1,opt,name=calling_log_view,json=callingLogView,proto3" json:"calling_log_view,omitempty"`
}

func (x *CreateCallingLogResponse) Reset() {
	*x = CreateCallingLogResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCallingLogResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCallingLogResponse) ProtoMessage() {}

func (x *CreateCallingLogResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCallingLogResponse.ProtoReflect.Descriptor instead.
func (*CreateCallingLogResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateCallingLogResponse) GetCallingLogView() *v1.CallingLogView {
	if x != nil {
		return x.CallingLogView
	}
	return nil
}

// get calling log request
type GetCallingLogRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetCallingLogRequest) Reset() {
	*x = GetCallingLogRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCallingLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallingLogRequest) ProtoMessage() {}

func (x *GetCallingLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallingLogRequest.ProtoReflect.Descriptor instead.
func (*GetCallingLogRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetCallingLogRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get calling log response
type GetCallingLogResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// calling log.
	CallingLogView *v1.CallingLogView `protobuf:"bytes,1,opt,name=calling_log_view,json=callingLogView,proto3" json:"calling_log_view,omitempty"`
}

func (x *GetCallingLogResponse) Reset() {
	*x = GetCallingLogResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCallingLogResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallingLogResponse) ProtoMessage() {}

func (x *GetCallingLogResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallingLogResponse.ProtoReflect.Descriptor instead.
func (*GetCallingLogResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetCallingLogResponse) GetCallingLogView() *v1.CallingLogView {
	if x != nil {
		return x.CallingLogView
	}
	return nil
}

// list calling logs request
type ListCallingLogsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// order by
	OrderBy *v1.CallingLogOrderBy `protobuf:"bytes,2,opt,name=order_by,json=orderBy,proto3,oneof" json:"order_by,omitempty"`
	// deprecated, replace with CallingLogFilter
	//
	// Deprecated: Do not use.
	Filter *ListCallingLogsRequest_Filter `protobuf:"bytes,3,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// filter
	LogFilter *v1.CallingLogFilter `protobuf:"bytes,5,opt,name=log_filter,json=logFilter,proto3,oneof" json:"log_filter,omitempty"`
}

func (x *ListCallingLogsRequest) Reset() {
	*x = ListCallingLogsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCallingLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCallingLogsRequest) ProtoMessage() {}

func (x *ListCallingLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCallingLogsRequest.ProtoReflect.Descriptor instead.
func (*ListCallingLogsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListCallingLogsRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListCallingLogsRequest) GetOrderBy() *v1.CallingLogOrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

// Deprecated: Do not use.
func (x *ListCallingLogsRequest) GetFilter() *ListCallingLogsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListCallingLogsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListCallingLogsRequest) GetLogFilter() *v1.CallingLogFilter {
	if x != nil {
		return x.LogFilter
	}
	return nil
}

// list calling logs response
type ListCallingLogsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// calling logs
	CallingLogViews []*v1.CallingLogView `protobuf:"bytes,1,rep,name=calling_log_views,json=callingLogViews,proto3" json:"calling_log_views,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListCallingLogsResponse) Reset() {
	*x = ListCallingLogsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCallingLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCallingLogsResponse) ProtoMessage() {}

func (x *ListCallingLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCallingLogsResponse.ProtoReflect.Descriptor instead.
func (*ListCallingLogsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListCallingLogsResponse) GetCallingLogViews() []*v1.CallingLogView {
	if x != nil {
		return x.CallingLogViews
	}
	return nil
}

func (x *ListCallingLogsResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// update calling log request
type UpdateCallingLogRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID of the calling to update.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Company ID of the calling to update.
	UpdateCallingLogDef *v1.UpdateCallingLogDef `protobuf:"bytes,2,opt,name=update_calling_log_def,json=updateCallingLogDef,proto3" json:"update_calling_log_def,omitempty"`
}

func (x *UpdateCallingLogRequest) Reset() {
	*x = UpdateCallingLogRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCallingLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCallingLogRequest) ProtoMessage() {}

func (x *UpdateCallingLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCallingLogRequest.ProtoReflect.Descriptor instead.
func (*UpdateCallingLogRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateCallingLogRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCallingLogRequest) GetUpdateCallingLogDef() *v1.UpdateCallingLogDef {
	if x != nil {
		return x.UpdateCallingLogDef
	}
	return nil
}

// update calling log response
type UpdateCallingLogResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// calling log.
	CallingLogView *v1.CallingLogView `protobuf:"bytes,1,opt,name=calling_log_view,json=callingLogView,proto3" json:"calling_log_view,omitempty"`
}

func (x *UpdateCallingLogResponse) Reset() {
	*x = UpdateCallingLogResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCallingLogResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCallingLogResponse) ProtoMessage() {}

func (x *UpdateCallingLogResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCallingLogResponse.ProtoReflect.Descriptor instead.
func (*UpdateCallingLogResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateCallingLogResponse) GetCallingLogView() *v1.CallingLogView {
	if x != nil {
		return x.CallingLogView
	}
	return nil
}

// delete calling log request
type DeleteCallingLogRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID of the calling to delete.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Company ID
	CompanyId *int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
}

func (x *DeleteCallingLogRequest) Reset() {
	*x = DeleteCallingLogRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCallingLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCallingLogRequest) ProtoMessage() {}

func (x *DeleteCallingLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCallingLogRequest.ProtoReflect.Descriptor instead.
func (*DeleteCallingLogRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteCallingLogRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteCallingLogRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// delete calling log response
type DeleteCallingLogResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *DeleteCallingLogResponse) Reset() {
	*x = DeleteCallingLogResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCallingLogResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCallingLogResponse) ProtoMessage() {}

func (x *DeleteCallingLogResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCallingLogResponse.ProtoReflect.Descriptor instead.
func (*DeleteCallingLogResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteCallingLogResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// get token request
type GetTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// calling source
	CallingSource *v1.CallingSource `protobuf:"varint,1,opt,name=calling_source,json=callingSource,proto3,enum=moego.models.engagement.v1.CallingSource,oneof" json:"calling_source,omitempty"`
}

func (x *GetTokenRequest) Reset() {
	*x = GetTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTokenRequest) ProtoMessage() {}

func (x *GetTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTokenRequest.ProtoReflect.Descriptor instead.
func (*GetTokenRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetTokenRequest) GetCallingSource() v1.CallingSource {
	if x != nil && x.CallingSource != nil {
		return *x.CallingSource
	}
	return v1.CallingSource(0)
}

// get token response
type GetTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the token def
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	// staff permissions
	StaffPermissions []v1.StaffPermission `protobuf:"varint,2,rep,packed,name=staff_permissions,json=staffPermissions,proto3,enum=moego.models.engagement.v1.StaffPermission" json:"staff_permissions,omitempty"`
}

func (x *GetTokenResponse) Reset() {
	*x = GetTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTokenResponse) ProtoMessage() {}

func (x *GetTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTokenResponse.ProtoReflect.Descriptor instead.
func (*GetTokenResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetTokenResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GetTokenResponse) GetStaffPermissions() []v1.StaffPermission {
	if x != nil {
		return x.StaffPermissions
	}
	return nil
}

// get calling detail request
type GetCallingDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the identifier
	//
	// Types that are assignable to Identifier:
	//
	//	*GetCallingDetailRequest_CustomerId
	//	*GetCallingDetailRequest_ClientId
	Identifier isGetCallingDetailRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *GetCallingDetailRequest) Reset() {
	*x = GetCallingDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCallingDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallingDetailRequest) ProtoMessage() {}

func (x *GetCallingDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallingDetailRequest.ProtoReflect.Descriptor instead.
func (*GetCallingDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{12}
}

func (m *GetCallingDetailRequest) GetIdentifier() isGetCallingDetailRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetCallingDetailRequest) GetCustomerId() int64 {
	if x, ok := x.GetIdentifier().(*GetCallingDetailRequest_CustomerId); ok {
		return x.CustomerId
	}
	return 0
}

func (x *GetCallingDetailRequest) GetClientId() int64 {
	if x, ok := x.GetIdentifier().(*GetCallingDetailRequest_ClientId); ok {
		return x.ClientId
	}
	return 0
}

type isGetCallingDetailRequest_Identifier interface {
	isGetCallingDetailRequest_Identifier()
}

type GetCallingDetailRequest_CustomerId struct {
	// the customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3,oneof"`
}

type GetCallingDetailRequest_ClientId struct {
	// client id
	ClientId int64 `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3,oneof"`
}

func (*GetCallingDetailRequest_CustomerId) isGetCallingDetailRequest_Identifier() {}

func (*GetCallingDetailRequest_ClientId) isGetCallingDetailRequest_Identifier() {}

// get calling detail response
type GetCallingDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the customer
	Customer *v1.Customer `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
	// the pets
	Pets []*v1.Pet `protobuf:"bytes,2,rep,name=pets,proto3" json:"pets,omitempty"`
	// direction
	Direction v1.CallingDirection `protobuf:"varint,3,opt,name=direction,proto3,enum=moego.models.engagement.v1.CallingDirection" json:"direction,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,5,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// company name
	CompanyName string `protobuf:"bytes,6,opt,name=company_name,json=companyName,proto3" json:"company_name,omitempty"`
	// business name
	BusinessName string `protobuf:"bytes,7,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// client id
	ClientId int64 `protobuf:"varint,8,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// client name
	ClientName string `protobuf:"bytes,9,opt,name=client_name,json=clientName,proto3" json:"client_name,omitempty"`
	// is recording
	IsRecording bool `protobuf:"varint,10,opt,name=is_recording,json=isRecording,proto3" json:"is_recording,omitempty"`
}

func (x *GetCallingDetailResponse) Reset() {
	*x = GetCallingDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCallingDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallingDetailResponse) ProtoMessage() {}

func (x *GetCallingDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallingDetailResponse.ProtoReflect.Descriptor instead.
func (*GetCallingDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetCallingDetailResponse) GetCustomer() *v1.Customer {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *GetCallingDetailResponse) GetPets() []*v1.Pet {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *GetCallingDetailResponse) GetDirection() v1.CallingDirection {
	if x != nil {
		return x.Direction
	}
	return v1.CallingDirection(0)
}

func (x *GetCallingDetailResponse) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetCallingDetailResponse) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetCallingDetailResponse) GetCompanyName() string {
	if x != nil {
		return x.CompanyName
	}
	return ""
}

func (x *GetCallingDetailResponse) GetBusinessName() string {
	if x != nil {
		return x.BusinessName
	}
	return ""
}

func (x *GetCallingDetailResponse) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *GetCallingDetailResponse) GetClientName() string {
	if x != nil {
		return x.ClientName
	}
	return ""
}

func (x *GetCallingDetailResponse) GetIsRecording() bool {
	if x != nil {
		return x.IsRecording
	}
	return false
}

// get customer dial mask request
type GetCustomerDialMaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the identifier
	//
	// Types that are assignable to Identifier:
	//
	//	*GetCustomerDialMaskRequest_CustomerId
	//	*GetCustomerDialMaskRequest_ClientId
	Identifier isGetCustomerDialMaskRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *GetCustomerDialMaskRequest) Reset() {
	*x = GetCustomerDialMaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerDialMaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerDialMaskRequest) ProtoMessage() {}

func (x *GetCustomerDialMaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerDialMaskRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerDialMaskRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{14}
}

func (m *GetCustomerDialMaskRequest) GetIdentifier() isGetCustomerDialMaskRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetCustomerDialMaskRequest) GetCustomerId() int64 {
	if x, ok := x.GetIdentifier().(*GetCustomerDialMaskRequest_CustomerId); ok {
		return x.CustomerId
	}
	return 0
}

func (x *GetCustomerDialMaskRequest) GetClientId() int64 {
	if x, ok := x.GetIdentifier().(*GetCustomerDialMaskRequest_ClientId); ok {
		return x.ClientId
	}
	return 0
}

type isGetCustomerDialMaskRequest_Identifier interface {
	isGetCustomerDialMaskRequest_Identifier()
}

type GetCustomerDialMaskRequest_CustomerId struct {
	// the customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3,oneof"`
}

type GetCustomerDialMaskRequest_ClientId struct {
	// client id
	ClientId int64 `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3,oneof"`
}

func (*GetCustomerDialMaskRequest_CustomerId) isGetCustomerDialMaskRequest_Identifier() {}

func (*GetCustomerDialMaskRequest_ClientId) isGetCustomerDialMaskRequest_Identifier() {}

// get customer dial mask response
type GetCustomerDialMaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the mask
	Mask string `protobuf:"bytes,1,opt,name=mask,proto3" json:"mask,omitempty"`
}

func (x *GetCustomerDialMaskResponse) Reset() {
	*x = GetCustomerDialMaskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerDialMaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerDialMaskResponse) ProtoMessage() {}

func (x *GetCustomerDialMaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerDialMaskResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerDialMaskResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetCustomerDialMaskResponse) GetMask() string {
	if x != nil {
		return x.Mask
	}
	return ""
}

// search client
type SearchClientRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// search keyword search name
	Keyword string `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *SearchClientRequest) Reset() {
	*x = SearchClientRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchClientRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchClientRequest) ProtoMessage() {}

func (x *SearchClientRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchClientRequest.ProtoReflect.Descriptor instead.
func (*SearchClientRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{16}
}

func (x *SearchClientRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *SearchClientRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// search client
type SearchClientResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// calling logs
	Clients []*v1.Client `protobuf:"bytes,1,rep,name=clients,proto3" json:"clients,omitempty"`
}

func (x *SearchClientResponse) Reset() {
	*x = SearchClientResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchClientResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchClientResponse) ProtoMessage() {}

func (x *SearchClientResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchClientResponse.ProtoReflect.Descriptor instead.
func (*SearchClientResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{17}
}

func (x *SearchClientResponse) GetClients() []*v1.Client {
	if x != nil {
		return x.Clients
	}
	return nil
}

// get calling log overview request
type GetCallingLogOverviewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *GetCallingLogOverviewRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *GetCallingLogOverviewRequest) Reset() {
	*x = GetCallingLogOverviewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCallingLogOverviewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallingLogOverviewRequest) ProtoMessage() {}

func (x *GetCallingLogOverviewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallingLogOverviewRequest.ProtoReflect.Descriptor instead.
func (*GetCallingLogOverviewRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetCallingLogOverviewRequest) GetFilter() *GetCallingLogOverviewRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *GetCallingLogOverviewRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetCallingLogOverviewRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetCallingLogOverviewRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// get calling log overview response
type GetCallingLogOverviewResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// calling received overview
	CallReceived *GetCallingLogOverviewResponse_CallReceived `protobuf:"bytes,1,opt,name=call_received,json=callReceived,proto3" json:"call_received,omitempty"`
	// calling log overview
	CallInAfterHour *v1.IndicatorDef `protobuf:"bytes,2,opt,name=call_in_after_hour,json=callInAfterHour,proto3" json:"call_in_after_hour,omitempty"`
	// calling log overview
	VoicemailReceived *v1.IndicatorDef `protobuf:"bytes,3,opt,name=voicemail_received,json=voicemailReceived,proto3" json:"voicemail_received,omitempty"`
	// calling log overview
	AverageResponseTime *v1.IndicatorDef `protobuf:"bytes,4,opt,name=average_response_time,json=averageResponseTime,proto3" json:"average_response_time,omitempty"`
	// resolved
	Resolved *v1.IndicatorDef `protobuf:"bytes,5,opt,name=resolved,proto3" json:"resolved,omitempty"`
	// unresolved
	Unresolved *v1.IndicatorDef `protobuf:"bytes,6,opt,name=unresolved,proto3" json:"unresolved,omitempty"`
}

func (x *GetCallingLogOverviewResponse) Reset() {
	*x = GetCallingLogOverviewResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCallingLogOverviewResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallingLogOverviewResponse) ProtoMessage() {}

func (x *GetCallingLogOverviewResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallingLogOverviewResponse.ProtoReflect.Descriptor instead.
func (*GetCallingLogOverviewResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetCallingLogOverviewResponse) GetCallReceived() *GetCallingLogOverviewResponse_CallReceived {
	if x != nil {
		return x.CallReceived
	}
	return nil
}

func (x *GetCallingLogOverviewResponse) GetCallInAfterHour() *v1.IndicatorDef {
	if x != nil {
		return x.CallInAfterHour
	}
	return nil
}

func (x *GetCallingLogOverviewResponse) GetVoicemailReceived() *v1.IndicatorDef {
	if x != nil {
		return x.VoicemailReceived
	}
	return nil
}

func (x *GetCallingLogOverviewResponse) GetAverageResponseTime() *v1.IndicatorDef {
	if x != nil {
		return x.AverageResponseTime
	}
	return nil
}

func (x *GetCallingLogOverviewResponse) GetResolved() *v1.IndicatorDef {
	if x != nil {
		return x.Resolved
	}
	return nil
}

func (x *GetCallingLogOverviewResponse) GetUnresolved() *v1.IndicatorDef {
	if x != nil {
		return x.Unresolved
	}
	return nil
}

// get default local phone number result
type GetDefaultLocalPhoneNumberResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the phone number
	PhoneNumber string `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *GetDefaultLocalPhoneNumberResponse) Reset() {
	*x = GetDefaultLocalPhoneNumberResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDefaultLocalPhoneNumberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDefaultLocalPhoneNumberResponse) ProtoMessage() {}

func (x *GetDefaultLocalPhoneNumberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDefaultLocalPhoneNumberResponse.ProtoReflect.Descriptor instead.
func (*GetDefaultLocalPhoneNumberResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetDefaultLocalPhoneNumberResponse) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

// call from B-APP params
type CallFromBAppRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// local phone number, 可能为空，为空服务端会读取店家的坐席配置
	LocalPhoneNumber *string `protobuf:"bytes,1,opt,name=local_phone_number,json=localPhoneNumber,proto3,oneof" json:"local_phone_number,omitempty"`
	// call to identifier
	//
	// Types that are assignable to TargetIdentifier:
	//
	//	*CallFromBAppRequest_CustomerId
	//	*CallFromBAppRequest_ClientId
	//	*CallFromBAppRequest_PhoneNumber
	TargetIdentifier isCallFromBAppRequest_TargetIdentifier `protobuf_oneof:"target_identifier"`
}

func (x *CallFromBAppRequest) Reset() {
	*x = CallFromBAppRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallFromBAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallFromBAppRequest) ProtoMessage() {}

func (x *CallFromBAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallFromBAppRequest.ProtoReflect.Descriptor instead.
func (*CallFromBAppRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{21}
}

func (x *CallFromBAppRequest) GetLocalPhoneNumber() string {
	if x != nil && x.LocalPhoneNumber != nil {
		return *x.LocalPhoneNumber
	}
	return ""
}

func (m *CallFromBAppRequest) GetTargetIdentifier() isCallFromBAppRequest_TargetIdentifier {
	if m != nil {
		return m.TargetIdentifier
	}
	return nil
}

func (x *CallFromBAppRequest) GetCustomerId() int64 {
	if x, ok := x.GetTargetIdentifier().(*CallFromBAppRequest_CustomerId); ok {
		return x.CustomerId
	}
	return 0
}

func (x *CallFromBAppRequest) GetClientId() int64 {
	if x, ok := x.GetTargetIdentifier().(*CallFromBAppRequest_ClientId); ok {
		return x.ClientId
	}
	return 0
}

func (x *CallFromBAppRequest) GetPhoneNumber() string {
	if x, ok := x.GetTargetIdentifier().(*CallFromBAppRequest_PhoneNumber); ok {
		return x.PhoneNumber
	}
	return ""
}

type isCallFromBAppRequest_TargetIdentifier interface {
	isCallFromBAppRequest_TargetIdentifier()
}

type CallFromBAppRequest_CustomerId struct {
	// the customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3,oneof"`
}

type CallFromBAppRequest_ClientId struct {
	// the client id
	ClientId int64 `protobuf:"varint,3,opt,name=client_id,json=clientId,proto3,oneof"`
}

type CallFromBAppRequest_PhoneNumber struct {
	// the phone number
	PhoneNumber string `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3,oneof"`
}

func (*CallFromBAppRequest_CustomerId) isCallFromBAppRequest_TargetIdentifier() {}

func (*CallFromBAppRequest_ClientId) isCallFromBAppRequest_TargetIdentifier() {}

func (*CallFromBAppRequest_PhoneNumber) isCallFromBAppRequest_TargetIdentifier() {}

// ExistMultipleUnresolvedLogRequest
type ConfirmUnresolvedRangeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 待确认的 unresolved log 过滤范围
	Filter *v1.CallingLogFilter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ConfirmUnresolvedRangeRequest) Reset() {
	*x = ConfirmUnresolvedRangeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmUnresolvedRangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmUnresolvedRangeRequest) ProtoMessage() {}

func (x *ConfirmUnresolvedRangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmUnresolvedRangeRequest.ProtoReflect.Descriptor instead.
func (*ConfirmUnresolvedRangeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{22}
}

func (x *ConfirmUnresolvedRangeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ConfirmUnresolvedRangeRequest) GetFilter() *v1.CallingLogFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ExistMultipleUnresolvedLogResponse
type ConfirmUnresolvedRangeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 范围中 unresolved 状态的 log 数目
	UnresolvedCount int32 `protobuf:"varint,1,opt,name=unresolved_count,json=unresolvedCount,proto3" json:"unresolved_count,omitempty"`
}

func (x *ConfirmUnresolvedRangeResponse) Reset() {
	*x = ConfirmUnresolvedRangeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmUnresolvedRangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmUnresolvedRangeResponse) ProtoMessage() {}

func (x *ConfirmUnresolvedRangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmUnresolvedRangeResponse.ProtoReflect.Descriptor instead.
func (*ConfirmUnresolvedRangeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{23}
}

func (x *ConfirmUnresolvedRangeResponse) GetUnresolvedCount() int32 {
	if x != nil {
		return x.UnresolvedCount
	}
	return 0
}

// MarkLogAsResolvedRequest
type MarkLogResolveStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 待标记为 resolved 的 log 过滤范围
	Filter *v1.CallingLogFilter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// is resolved
	IsResolved bool `protobuf:"varint,3,opt,name=is_resolved,json=isResolved,proto3" json:"is_resolved,omitempty"`
}

func (x *MarkLogResolveStatusRequest) Reset() {
	*x = MarkLogResolveStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkLogResolveStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkLogResolveStatusRequest) ProtoMessage() {}

func (x *MarkLogResolveStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkLogResolveStatusRequest.ProtoReflect.Descriptor instead.
func (*MarkLogResolveStatusRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{24}
}

func (x *MarkLogResolveStatusRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *MarkLogResolveStatusRequest) GetFilter() *v1.CallingLogFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *MarkLogResolveStatusRequest) GetIsResolved() bool {
	if x != nil {
		return x.IsResolved
	}
	return false
}

// MarkLogAsResolvedResponse
type MarkLogResolveStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MarkLogResolveStatusResponse) Reset() {
	*x = MarkLogResolveStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkLogResolveStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkLogResolveStatusResponse) ProtoMessage() {}

func (x *MarkLogResolveStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkLogResolveStatusResponse.ProtoReflect.Descriptor instead.
func (*MarkLogResolveStatusResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{25}
}

// add company to calling feature whitelist request
type AddCompanyToWhitelistRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company identifier
	//
	// Types that are assignable to CompanyIdentifier:
	//
	//	*AddCompanyToWhitelistRequest_CompanyId
	//	*AddCompanyToWhitelistRequest_CompanyOwnerEmail
	CompanyIdentifier isAddCompanyToWhitelistRequest_CompanyIdentifier `protobuf_oneof:"company_identifier"`
	// api key
	ApiKey string `protobuf:"bytes,3,opt,name=api_key,json=apiKey,proto3" json:"api_key,omitempty"`
}

func (x *AddCompanyToWhitelistRequest) Reset() {
	*x = AddCompanyToWhitelistRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddCompanyToWhitelistRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCompanyToWhitelistRequest) ProtoMessage() {}

func (x *AddCompanyToWhitelistRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCompanyToWhitelistRequest.ProtoReflect.Descriptor instead.
func (*AddCompanyToWhitelistRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{26}
}

func (m *AddCompanyToWhitelistRequest) GetCompanyIdentifier() isAddCompanyToWhitelistRequest_CompanyIdentifier {
	if m != nil {
		return m.CompanyIdentifier
	}
	return nil
}

func (x *AddCompanyToWhitelistRequest) GetCompanyId() int64 {
	if x, ok := x.GetCompanyIdentifier().(*AddCompanyToWhitelistRequest_CompanyId); ok {
		return x.CompanyId
	}
	return 0
}

func (x *AddCompanyToWhitelistRequest) GetCompanyOwnerEmail() string {
	if x, ok := x.GetCompanyIdentifier().(*AddCompanyToWhitelistRequest_CompanyOwnerEmail); ok {
		return x.CompanyOwnerEmail
	}
	return ""
}

func (x *AddCompanyToWhitelistRequest) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

type isAddCompanyToWhitelistRequest_CompanyIdentifier interface {
	isAddCompanyToWhitelistRequest_CompanyIdentifier()
}

type AddCompanyToWhitelistRequest_CompanyId struct {
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof"`
}

type AddCompanyToWhitelistRequest_CompanyOwnerEmail struct {
	// company owner email
	CompanyOwnerEmail string `protobuf:"bytes,2,opt,name=company_owner_email,json=companyOwnerEmail,proto3,oneof"`
}

func (*AddCompanyToWhitelistRequest_CompanyId) isAddCompanyToWhitelistRequest_CompanyIdentifier() {}

func (*AddCompanyToWhitelistRequest_CompanyOwnerEmail) isAddCompanyToWhitelistRequest_CompanyIdentifier() {
}

// add company to calling feature whitelist response
type AddCompanyToWhitelistResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddCompanyToWhitelistResponse) Reset() {
	*x = AddCompanyToWhitelistResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddCompanyToWhitelistResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCompanyToWhitelistResponse) ProtoMessage() {}

func (x *AddCompanyToWhitelistResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCompanyToWhitelistResponse.ProtoReflect.Descriptor instead.
func (*AddCompanyToWhitelistResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{27}
}

// filter
type ListCallingLogsRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter client ids
	ClientIds []int64 `protobuf:"varint,1,rep,packed,name=client_ids,json=clientIds,proto3" json:"client_ids,omitempty"`
	// filter customer ids
	CustomerIds []int64 `protobuf:"varint,2,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// statuses
	Direction []v1.CallingDirection `protobuf:"varint,3,rep,packed,name=direction,proto3,enum=moego.models.engagement.v1.CallingDirection" json:"direction,omitempty"`
	// statuses
	Statuses []v1.Status `protobuf:"varint,4,rep,packed,name=statuses,proto3,enum=moego.models.engagement.v1.Status" json:"statuses,omitempty"`
	// categories
	Categories []v1.Category `protobuf:"varint,5,rep,packed,name=categories,proto3,enum=moego.models.engagement.v1.Category" json:"categories,omitempty"`
	// record types
	RecordTypes []v1.RecordType `protobuf:"varint,6,rep,packed,name=record_types,json=recordTypes,proto3,enum=moego.models.engagement.v1.RecordType" json:"record_types,omitempty"`
	// business ids
	BusinessIds []int64 `protobuf:"varint,7,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// init time period
	InitTimePeriod *interval.Interval `protobuf:"bytes,8,opt,name=init_time_period,json=initTimePeriod,proto3,oneof" json:"init_time_period,omitempty"`
	// is resolved
	IsResolved *bool `protobuf:"varint,9,opt,name=is_resolved,json=isResolved,proto3,oneof" json:"is_resolved,omitempty"`
}

func (x *ListCallingLogsRequest_Filter) Reset() {
	*x = ListCallingLogsRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCallingLogsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCallingLogsRequest_Filter) ProtoMessage() {}

func (x *ListCallingLogsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCallingLogsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListCallingLogsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ListCallingLogsRequest_Filter) GetClientIds() []int64 {
	if x != nil {
		return x.ClientIds
	}
	return nil
}

func (x *ListCallingLogsRequest_Filter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListCallingLogsRequest_Filter) GetDirection() []v1.CallingDirection {
	if x != nil {
		return x.Direction
	}
	return nil
}

func (x *ListCallingLogsRequest_Filter) GetStatuses() []v1.Status {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *ListCallingLogsRequest_Filter) GetCategories() []v1.Category {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *ListCallingLogsRequest_Filter) GetRecordTypes() []v1.RecordType {
	if x != nil {
		return x.RecordTypes
	}
	return nil
}

func (x *ListCallingLogsRequest_Filter) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ListCallingLogsRequest_Filter) GetInitTimePeriod() *interval.Interval {
	if x != nil {
		return x.InitTimePeriod
	}
	return nil
}

func (x *ListCallingLogsRequest_Filter) GetIsResolved() bool {
	if x != nil && x.IsResolved != nil {
		return *x.IsResolved
	}
	return false
}

// filter
type GetCallingLogOverviewRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// init time period
	InitTimePeriod *interval.Interval `protobuf:"bytes,2,opt,name=init_time_period,json=initTimePeriod,proto3,oneof" json:"init_time_period,omitempty"`
}

func (x *GetCallingLogOverviewRequest_Filter) Reset() {
	*x = GetCallingLogOverviewRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCallingLogOverviewRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallingLogOverviewRequest_Filter) ProtoMessage() {}

func (x *GetCallingLogOverviewRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallingLogOverviewRequest_Filter.ProtoReflect.Descriptor instead.
func (*GetCallingLogOverviewRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{18, 0}
}

func (x *GetCallingLogOverviewRequest_Filter) GetInitTimePeriod() *interval.Interval {
	if x != nil {
		return x.InitTimePeriod
	}
	return nil
}

// calling log overview
type GetCallingLogOverviewResponse_CallReceived struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// call received
	CallReceived *v1.IndicatorDef `protobuf:"bytes,1,opt,name=call_received,json=callReceived,proto3" json:"call_received,omitempty"`
	// calling answer overview
	CallAnswered *v1.IndicatorDef `protobuf:"bytes,2,opt,name=call_answered,json=callAnswered,proto3" json:"call_answered,omitempty"`
	// calling unanswered overview
	CallUnanswered *v1.IndicatorDef `protobuf:"bytes,3,opt,name=call_unanswered,json=callUnanswered,proto3" json:"call_unanswered,omitempty"`
}

func (x *GetCallingLogOverviewResponse_CallReceived) Reset() {
	*x = GetCallingLogOverviewResponse_CallReceived{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCallingLogOverviewResponse_CallReceived) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCallingLogOverviewResponse_CallReceived) ProtoMessage() {}

func (x *GetCallingLogOverviewResponse_CallReceived) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_engagement_v1_calling_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCallingLogOverviewResponse_CallReceived.ProtoReflect.Descriptor instead.
func (*GetCallingLogOverviewResponse_CallReceived) Descriptor() ([]byte, []int) {
	return file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP(), []int{19, 0}
}

func (x *GetCallingLogOverviewResponse_CallReceived) GetCallReceived() *v1.IndicatorDef {
	if x != nil {
		return x.CallReceived
	}
	return nil
}

func (x *GetCallingLogOverviewResponse_CallReceived) GetCallAnswered() *v1.IndicatorDef {
	if x != nil {
		return x.CallAnswered
	}
	return nil
}

func (x *GetCallingLogOverviewResponse_CallReceived) GetCallUnanswered() *v1.IndicatorDef {
	if x != nil {
		return x.CallUnanswered
	}
	return nil
}

var File_moego_service_engagement_v1_calling_service_proto protoreflect.FileDescriptor

var file_moego_service_engagement_v1_calling_service_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6b, 0x0a, 0x17, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x50, 0x0a, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x5f, 0x6c, 0x6f, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x63, 0x61,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x22, 0x70, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x10, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f,
	0x6c, 0x6f, 0x67, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0e, 0x63, 0x61, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65, 0x77, 0x22, 0x26, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x6d, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x10, 0x63,
	0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65,
	0x77, 0x22, 0x86, 0x08, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x50, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x4d,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x48,
	0x01, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x88, 0x01, 0x01, 0x12, 0x5b, 0x0a,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x48, 0x02, 0x52,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x50, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x48, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x88, 0x01, 0x01, 0x1a, 0xbd, 0x04, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x2e, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0xf4, 0x03, 0x22, 0x04,
	0x22, 0x02, 0x38, 0x00, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12,
	0x32, 0x0a, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0xf4, 0x03,
	0x22, 0x04, 0x22, 0x02, 0x38, 0x00, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x73, 0x12, 0x4a, 0x0a, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x3e, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x12,
	0x44, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x49, 0x0a, 0x0c, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x73, 0x12, 0x44, 0x0a, 0x10, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x48, 0x00, 0x52, 0x0e, 0x69, 0x6e, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x69, 0x73, 0x5f,
	0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01,
	0x52, 0x0a, 0x69, 0x73, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x88, 0x01, 0x01, 0x42,
	0x13, 0x0a, 0x11, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x6f,
	0x6c, 0x76, 0x65, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79,
	0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x6c, 0x6f, 0x67, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xb5, 0x01, 0x0a, 0x17, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x11, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0f, 0x63,
	0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65, 0x77, 0x73, 0x12, 0x42,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x98, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x64, 0x0a, 0x16, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x64, 0x65,
	0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x44, 0x65, 0x66, 0x52, 0x13, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x44, 0x65, 0x66, 0x22, 0x70, 0x0a,
	0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x10, 0x63, 0x61, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65, 0x77, 0x22,
	0x65, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x22, 0x34, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x7b, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x55, 0x0a, 0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x58, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x69,
	0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0b, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00,
	0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x00, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0xc6, 0x03, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x08,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x04, 0x70, 0x65, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x12, 0x4a, 0x0a,
	0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09,
	0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x22, 0x6c, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x44, 0x69, 0x61, 0x6c, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x21, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x22, 0x31, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44,
	0x69, 0x61, 0x6c, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d,
	0x61, 0x73, 0x6b, 0x22, 0x4e, 0x0a, 0x13, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x22, 0x54, 0x0a, 0x14, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x07, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x07, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xb8, 0x02, 0x0a, 0x1c, 0x47, 0x65,
	0x74, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x76,
	0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x58, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x1a,
	0x63, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x44, 0x0a, 0x10, 0x69, 0x6e, 0x69,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x48, 0x00, 0x52, 0x0e, 0x69, 0x6e,
	0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x42,
	0x13, 0x0a, 0x11, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x22, 0xad, 0x06, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65,
	0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x52, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x64, 0x12, 0x55, 0x0a, 0x12, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x6e, 0x5f,
	0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x66, 0x52, 0x0f, 0x63, 0x61, 0x6c, 0x6c,
	0x49, 0x6e, 0x41, 0x66, 0x74, 0x65, 0x72, 0x48, 0x6f, 0x75, 0x72, 0x12, 0x57, 0x0a, 0x12, 0x76,
	0x6f, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x44, 0x65,
	0x66, 0x52, 0x11, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x64, 0x12, 0x5c, 0x0a, 0x15, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x5f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x66, 0x52, 0x13, 0x61,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x44, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x44, 0x65, 0x66, 0x52, 0x08,
	0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x12, 0x48, 0x0a, 0x0a, 0x75, 0x6e, 0x72, 0x65,
	0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61,
	0x74, 0x6f, 0x72, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x75, 0x6e, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76,
	0x65, 0x64, 0x1a, 0xff, 0x01, 0x0a, 0x0c, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x64, 0x12, 0x4d, 0x0a, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f,
	0x72, 0x44, 0x65, 0x66, 0x52, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x64, 0x12, 0x4d, 0x0a, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x61, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72,
	0x44, 0x65, 0x66, 0x52, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x65,
	0x64, 0x12, 0x51, 0x0a, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x75, 0x6e, 0x61, 0x6e, 0x73, 0x77,
	0x65, 0x72, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f,
	0x72, 0x44, 0x65, 0x66, 0x52, 0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x55, 0x6e, 0x61, 0x6e, 0x73, 0x77,
	0x65, 0x72, 0x65, 0x64, 0x22, 0x47, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xdb, 0x01,
	0x0a, 0x13, 0x43, 0x61, 0x6c, 0x6c, 0x46, 0x72, 0x6f, 0x6d, 0x42, 0x41, 0x70, 0x70, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x12, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x01, 0x52, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52,
	0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00,
	0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0c, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42,
	0x13, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x8d, 0x01, 0x0a, 0x1d,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x55, 0x6e, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65,
	0x64, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x4b, 0x0a, 0x1e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x55, 0x6e, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x29, 0x0a,
	0x10, 0x75, 0x6e, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x75, 0x6e, 0x72, 0x65, 0x73, 0x6f, 0x6c,
	0x76, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xac, 0x01, 0x0a, 0x1b, 0x4d, 0x61, 0x72,
	0x6b, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x12, 0x44, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x73,
	0x6f, 0x6c, 0x76, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x52,
	0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x22, 0x1e, 0x0a, 0x1c, 0x4d, 0x61, 0x72, 0x6b, 0x4c,
	0x6f, 0x67, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xbb, 0x01, 0x0a, 0x1c, 0x41, 0x64, 0x64, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x54, 0x6f, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x39, 0x0a, 0x13, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x60, 0x01, 0x48, 0x00, 0x52, 0x11, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x20, 0x0a,
	0x07, 0x61, 0x70, 0x69, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x42,
	0x14, 0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x1f, 0x0a, 0x1d, 0x41, 0x64, 0x64, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x54, 0x6f, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xe4, 0x0f, 0x0a, 0x0e, 0x43, 0x61, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7f, 0x0a, 0x10, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x12, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c,
	0x6f, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x76, 0x0a, 0x0d, 0x47, 0x65,
	0x74, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x12, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65,
	0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x7f, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x56, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x7f, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x34,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65,
	0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x69, 0x61, 0x6c, 0x4d,
	0x61, 0x73, 0x6b, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x69, 0x61,
	0x6c, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x69, 0x61, 0x6c, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x0c, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8e, 0x01, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x4f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f,
	0x67, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f, 0x67, 0x4f, 0x76, 0x65, 0x72,
	0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x6c,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x0c, 0x43, 0x61, 0x6c, 0x6c, 0x46, 0x72, 0x6f, 0x6d, 0x42,
	0x41, 0x70, 0x70, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x46, 0x72, 0x6f, 0x6d, 0x42, 0x41, 0x70, 0x70, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x6b, 0x0a,
	0x0c, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x69, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x16, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x55, 0x6e, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x55, 0x6e, 0x72, 0x65, 0x73,
	0x6f, 0x6c, 0x76, 0x65, 0x64, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x55, 0x6e, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65,
	0x64, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8b,
	0x01, 0x0a, 0x14, 0x4d, 0x61, 0x72, 0x6b, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73,
	0x6f, 0x6c, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x61, 0x72, 0x6b, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8e, 0x01, 0x0a,
	0x15, 0x41, 0x64, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x54, 0x6f, 0x57, 0x68, 0x69,
	0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x54,
	0x6f, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x64, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x54, 0x6f, 0x57, 0x68, 0x69, 0x74,
	0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x89, 0x01,
	0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_service_engagement_v1_calling_service_proto_rawDescOnce sync.Once
	file_moego_service_engagement_v1_calling_service_proto_rawDescData = file_moego_service_engagement_v1_calling_service_proto_rawDesc
)

func file_moego_service_engagement_v1_calling_service_proto_rawDescGZIP() []byte {
	file_moego_service_engagement_v1_calling_service_proto_rawDescOnce.Do(func() {
		file_moego_service_engagement_v1_calling_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_engagement_v1_calling_service_proto_rawDescData)
	})
	return file_moego_service_engagement_v1_calling_service_proto_rawDescData
}

var file_moego_service_engagement_v1_calling_service_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_moego_service_engagement_v1_calling_service_proto_goTypes = []interface{}{
	(*CreateCallingLogRequest)(nil),                    // 0: moego.service.engagement.v1.CreateCallingLogRequest
	(*CreateCallingLogResponse)(nil),                   // 1: moego.service.engagement.v1.CreateCallingLogResponse
	(*GetCallingLogRequest)(nil),                       // 2: moego.service.engagement.v1.GetCallingLogRequest
	(*GetCallingLogResponse)(nil),                      // 3: moego.service.engagement.v1.GetCallingLogResponse
	(*ListCallingLogsRequest)(nil),                     // 4: moego.service.engagement.v1.ListCallingLogsRequest
	(*ListCallingLogsResponse)(nil),                    // 5: moego.service.engagement.v1.ListCallingLogsResponse
	(*UpdateCallingLogRequest)(nil),                    // 6: moego.service.engagement.v1.UpdateCallingLogRequest
	(*UpdateCallingLogResponse)(nil),                   // 7: moego.service.engagement.v1.UpdateCallingLogResponse
	(*DeleteCallingLogRequest)(nil),                    // 8: moego.service.engagement.v1.DeleteCallingLogRequest
	(*DeleteCallingLogResponse)(nil),                   // 9: moego.service.engagement.v1.DeleteCallingLogResponse
	(*GetTokenRequest)(nil),                            // 10: moego.service.engagement.v1.GetTokenRequest
	(*GetTokenResponse)(nil),                           // 11: moego.service.engagement.v1.GetTokenResponse
	(*GetCallingDetailRequest)(nil),                    // 12: moego.service.engagement.v1.GetCallingDetailRequest
	(*GetCallingDetailResponse)(nil),                   // 13: moego.service.engagement.v1.GetCallingDetailResponse
	(*GetCustomerDialMaskRequest)(nil),                 // 14: moego.service.engagement.v1.GetCustomerDialMaskRequest
	(*GetCustomerDialMaskResponse)(nil),                // 15: moego.service.engagement.v1.GetCustomerDialMaskResponse
	(*SearchClientRequest)(nil),                        // 16: moego.service.engagement.v1.SearchClientRequest
	(*SearchClientResponse)(nil),                       // 17: moego.service.engagement.v1.SearchClientResponse
	(*GetCallingLogOverviewRequest)(nil),               // 18: moego.service.engagement.v1.GetCallingLogOverviewRequest
	(*GetCallingLogOverviewResponse)(nil),              // 19: moego.service.engagement.v1.GetCallingLogOverviewResponse
	(*GetDefaultLocalPhoneNumberResponse)(nil),         // 20: moego.service.engagement.v1.GetDefaultLocalPhoneNumberResponse
	(*CallFromBAppRequest)(nil),                        // 21: moego.service.engagement.v1.CallFromBAppRequest
	(*ConfirmUnresolvedRangeRequest)(nil),              // 22: moego.service.engagement.v1.ConfirmUnresolvedRangeRequest
	(*ConfirmUnresolvedRangeResponse)(nil),             // 23: moego.service.engagement.v1.ConfirmUnresolvedRangeResponse
	(*MarkLogResolveStatusRequest)(nil),                // 24: moego.service.engagement.v1.MarkLogResolveStatusRequest
	(*MarkLogResolveStatusResponse)(nil),               // 25: moego.service.engagement.v1.MarkLogResolveStatusResponse
	(*AddCompanyToWhitelistRequest)(nil),               // 26: moego.service.engagement.v1.AddCompanyToWhitelistRequest
	(*AddCompanyToWhitelistResponse)(nil),              // 27: moego.service.engagement.v1.AddCompanyToWhitelistResponse
	(*ListCallingLogsRequest_Filter)(nil),              // 28: moego.service.engagement.v1.ListCallingLogsRequest.Filter
	(*GetCallingLogOverviewRequest_Filter)(nil),        // 29: moego.service.engagement.v1.GetCallingLogOverviewRequest.Filter
	(*GetCallingLogOverviewResponse_CallReceived)(nil), // 30: moego.service.engagement.v1.GetCallingLogOverviewResponse.CallReceived
	(*v1.CreateCallingLogDef)(nil),                     // 31: moego.models.engagement.v1.CreateCallingLogDef
	(*v1.CallingLogView)(nil),                          // 32: moego.models.engagement.v1.CallingLogView
	(*v2.PaginationRequest)(nil),                       // 33: moego.utils.v2.PaginationRequest
	(*v1.CallingLogOrderBy)(nil),                       // 34: moego.models.engagement.v1.CallingLogOrderBy
	(*v1.CallingLogFilter)(nil),                        // 35: moego.models.engagement.v1.CallingLogFilter
	(*v2.PaginationResponse)(nil),                      // 36: moego.utils.v2.PaginationResponse
	(*v1.UpdateCallingLogDef)(nil),                     // 37: moego.models.engagement.v1.UpdateCallingLogDef
	(v1.CallingSource)(0),                              // 38: moego.models.engagement.v1.CallingSource
	(v1.StaffPermission)(0),                            // 39: moego.models.engagement.v1.StaffPermission
	(*v1.Customer)(nil),                                // 40: moego.models.engagement.v1.Customer
	(*v1.Pet)(nil),                                     // 41: moego.models.engagement.v1.Pet
	(v1.CallingDirection)(0),                           // 42: moego.models.engagement.v1.CallingDirection
	(*v1.Client)(nil),                                  // 43: moego.models.engagement.v1.Client
	(*v1.IndicatorDef)(nil),                            // 44: moego.models.engagement.v1.IndicatorDef
	(v1.Status)(0),                                     // 45: moego.models.engagement.v1.Status
	(v1.Category)(0),                                   // 46: moego.models.engagement.v1.Category
	(v1.RecordType)(0),                                 // 47: moego.models.engagement.v1.RecordType
	(*interval.Interval)(nil),                          // 48: google.type.Interval
	(*emptypb.Empty)(nil),                              // 49: google.protobuf.Empty
}
var file_moego_service_engagement_v1_calling_service_proto_depIdxs = []int32{
	31, // 0: moego.service.engagement.v1.CreateCallingLogRequest.calling_log:type_name -> moego.models.engagement.v1.CreateCallingLogDef
	32, // 1: moego.service.engagement.v1.CreateCallingLogResponse.calling_log_view:type_name -> moego.models.engagement.v1.CallingLogView
	32, // 2: moego.service.engagement.v1.GetCallingLogResponse.calling_log_view:type_name -> moego.models.engagement.v1.CallingLogView
	33, // 3: moego.service.engagement.v1.ListCallingLogsRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	34, // 4: moego.service.engagement.v1.ListCallingLogsRequest.order_by:type_name -> moego.models.engagement.v1.CallingLogOrderBy
	28, // 5: moego.service.engagement.v1.ListCallingLogsRequest.filter:type_name -> moego.service.engagement.v1.ListCallingLogsRequest.Filter
	35, // 6: moego.service.engagement.v1.ListCallingLogsRequest.log_filter:type_name -> moego.models.engagement.v1.CallingLogFilter
	32, // 7: moego.service.engagement.v1.ListCallingLogsResponse.calling_log_views:type_name -> moego.models.engagement.v1.CallingLogView
	36, // 8: moego.service.engagement.v1.ListCallingLogsResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	37, // 9: moego.service.engagement.v1.UpdateCallingLogRequest.update_calling_log_def:type_name -> moego.models.engagement.v1.UpdateCallingLogDef
	32, // 10: moego.service.engagement.v1.UpdateCallingLogResponse.calling_log_view:type_name -> moego.models.engagement.v1.CallingLogView
	38, // 11: moego.service.engagement.v1.GetTokenRequest.calling_source:type_name -> moego.models.engagement.v1.CallingSource
	39, // 12: moego.service.engagement.v1.GetTokenResponse.staff_permissions:type_name -> moego.models.engagement.v1.StaffPermission
	40, // 13: moego.service.engagement.v1.GetCallingDetailResponse.customer:type_name -> moego.models.engagement.v1.Customer
	41, // 14: moego.service.engagement.v1.GetCallingDetailResponse.pets:type_name -> moego.models.engagement.v1.Pet
	42, // 15: moego.service.engagement.v1.GetCallingDetailResponse.direction:type_name -> moego.models.engagement.v1.CallingDirection
	43, // 16: moego.service.engagement.v1.SearchClientResponse.clients:type_name -> moego.models.engagement.v1.Client
	29, // 17: moego.service.engagement.v1.GetCallingLogOverviewRequest.filter:type_name -> moego.service.engagement.v1.GetCallingLogOverviewRequest.Filter
	30, // 18: moego.service.engagement.v1.GetCallingLogOverviewResponse.call_received:type_name -> moego.service.engagement.v1.GetCallingLogOverviewResponse.CallReceived
	44, // 19: moego.service.engagement.v1.GetCallingLogOverviewResponse.call_in_after_hour:type_name -> moego.models.engagement.v1.IndicatorDef
	44, // 20: moego.service.engagement.v1.GetCallingLogOverviewResponse.voicemail_received:type_name -> moego.models.engagement.v1.IndicatorDef
	44, // 21: moego.service.engagement.v1.GetCallingLogOverviewResponse.average_response_time:type_name -> moego.models.engagement.v1.IndicatorDef
	44, // 22: moego.service.engagement.v1.GetCallingLogOverviewResponse.resolved:type_name -> moego.models.engagement.v1.IndicatorDef
	44, // 23: moego.service.engagement.v1.GetCallingLogOverviewResponse.unresolved:type_name -> moego.models.engagement.v1.IndicatorDef
	35, // 24: moego.service.engagement.v1.ConfirmUnresolvedRangeRequest.filter:type_name -> moego.models.engagement.v1.CallingLogFilter
	35, // 25: moego.service.engagement.v1.MarkLogResolveStatusRequest.filter:type_name -> moego.models.engagement.v1.CallingLogFilter
	42, // 26: moego.service.engagement.v1.ListCallingLogsRequest.Filter.direction:type_name -> moego.models.engagement.v1.CallingDirection
	45, // 27: moego.service.engagement.v1.ListCallingLogsRequest.Filter.statuses:type_name -> moego.models.engagement.v1.Status
	46, // 28: moego.service.engagement.v1.ListCallingLogsRequest.Filter.categories:type_name -> moego.models.engagement.v1.Category
	47, // 29: moego.service.engagement.v1.ListCallingLogsRequest.Filter.record_types:type_name -> moego.models.engagement.v1.RecordType
	48, // 30: moego.service.engagement.v1.ListCallingLogsRequest.Filter.init_time_period:type_name -> google.type.Interval
	48, // 31: moego.service.engagement.v1.GetCallingLogOverviewRequest.Filter.init_time_period:type_name -> google.type.Interval
	44, // 32: moego.service.engagement.v1.GetCallingLogOverviewResponse.CallReceived.call_received:type_name -> moego.models.engagement.v1.IndicatorDef
	44, // 33: moego.service.engagement.v1.GetCallingLogOverviewResponse.CallReceived.call_answered:type_name -> moego.models.engagement.v1.IndicatorDef
	44, // 34: moego.service.engagement.v1.GetCallingLogOverviewResponse.CallReceived.call_unanswered:type_name -> moego.models.engagement.v1.IndicatorDef
	0,  // 35: moego.service.engagement.v1.CallingService.CreateCallingLog:input_type -> moego.service.engagement.v1.CreateCallingLogRequest
	2,  // 36: moego.service.engagement.v1.CallingService.GetCallingLog:input_type -> moego.service.engagement.v1.GetCallingLogRequest
	6,  // 37: moego.service.engagement.v1.CallingService.UpdateCallingLog:input_type -> moego.service.engagement.v1.UpdateCallingLogRequest
	8,  // 38: moego.service.engagement.v1.CallingService.DeleteCallingLog:input_type -> moego.service.engagement.v1.DeleteCallingLogRequest
	4,  // 39: moego.service.engagement.v1.CallingService.ListCallingLogs:input_type -> moego.service.engagement.v1.ListCallingLogsRequest
	49, // 40: moego.service.engagement.v1.CallingService.GetToken:input_type -> google.protobuf.Empty
	12, // 41: moego.service.engagement.v1.CallingService.GetCallingDetail:input_type -> moego.service.engagement.v1.GetCallingDetailRequest
	14, // 42: moego.service.engagement.v1.CallingService.GetCustomerDialMask:input_type -> moego.service.engagement.v1.GetCustomerDialMaskRequest
	16, // 43: moego.service.engagement.v1.CallingService.SearchClient:input_type -> moego.service.engagement.v1.SearchClientRequest
	18, // 44: moego.service.engagement.v1.CallingService.GetCallingLogOverview:input_type -> moego.service.engagement.v1.GetCallingLogOverviewRequest
	49, // 45: moego.service.engagement.v1.CallingService.GetDefaultLocalPhoneNumber:input_type -> google.protobuf.Empty
	21, // 46: moego.service.engagement.v1.CallingService.CallFromBApp:input_type -> moego.service.engagement.v1.CallFromBAppRequest
	10, // 47: moego.service.engagement.v1.CallingService.GetVoipToken:input_type -> moego.service.engagement.v1.GetTokenRequest
	22, // 48: moego.service.engagement.v1.CallingService.ConfirmUnresolvedRange:input_type -> moego.service.engagement.v1.ConfirmUnresolvedRangeRequest
	24, // 49: moego.service.engagement.v1.CallingService.MarkLogResolveStatus:input_type -> moego.service.engagement.v1.MarkLogResolveStatusRequest
	26, // 50: moego.service.engagement.v1.CallingService.AddCompanyToWhitelist:input_type -> moego.service.engagement.v1.AddCompanyToWhitelistRequest
	1,  // 51: moego.service.engagement.v1.CallingService.CreateCallingLog:output_type -> moego.service.engagement.v1.CreateCallingLogResponse
	3,  // 52: moego.service.engagement.v1.CallingService.GetCallingLog:output_type -> moego.service.engagement.v1.GetCallingLogResponse
	7,  // 53: moego.service.engagement.v1.CallingService.UpdateCallingLog:output_type -> moego.service.engagement.v1.UpdateCallingLogResponse
	9,  // 54: moego.service.engagement.v1.CallingService.DeleteCallingLog:output_type -> moego.service.engagement.v1.DeleteCallingLogResponse
	5,  // 55: moego.service.engagement.v1.CallingService.ListCallingLogs:output_type -> moego.service.engagement.v1.ListCallingLogsResponse
	11, // 56: moego.service.engagement.v1.CallingService.GetToken:output_type -> moego.service.engagement.v1.GetTokenResponse
	13, // 57: moego.service.engagement.v1.CallingService.GetCallingDetail:output_type -> moego.service.engagement.v1.GetCallingDetailResponse
	15, // 58: moego.service.engagement.v1.CallingService.GetCustomerDialMask:output_type -> moego.service.engagement.v1.GetCustomerDialMaskResponse
	17, // 59: moego.service.engagement.v1.CallingService.SearchClient:output_type -> moego.service.engagement.v1.SearchClientResponse
	19, // 60: moego.service.engagement.v1.CallingService.GetCallingLogOverview:output_type -> moego.service.engagement.v1.GetCallingLogOverviewResponse
	20, // 61: moego.service.engagement.v1.CallingService.GetDefaultLocalPhoneNumber:output_type -> moego.service.engagement.v1.GetDefaultLocalPhoneNumberResponse
	49, // 62: moego.service.engagement.v1.CallingService.CallFromBApp:output_type -> google.protobuf.Empty
	11, // 63: moego.service.engagement.v1.CallingService.GetVoipToken:output_type -> moego.service.engagement.v1.GetTokenResponse
	23, // 64: moego.service.engagement.v1.CallingService.ConfirmUnresolvedRange:output_type -> moego.service.engagement.v1.ConfirmUnresolvedRangeResponse
	25, // 65: moego.service.engagement.v1.CallingService.MarkLogResolveStatus:output_type -> moego.service.engagement.v1.MarkLogResolveStatusResponse
	27, // 66: moego.service.engagement.v1.CallingService.AddCompanyToWhitelist:output_type -> moego.service.engagement.v1.AddCompanyToWhitelistResponse
	51, // [51:67] is the sub-list for method output_type
	35, // [35:51] is the sub-list for method input_type
	35, // [35:35] is the sub-list for extension type_name
	35, // [35:35] is the sub-list for extension extendee
	0,  // [0:35] is the sub-list for field type_name
}

func init() { file_moego_service_engagement_v1_calling_service_proto_init() }
func file_moego_service_engagement_v1_calling_service_proto_init() {
	if File_moego_service_engagement_v1_calling_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCallingLogRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCallingLogResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCallingLogRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCallingLogResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCallingLogsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCallingLogsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCallingLogRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCallingLogResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCallingLogRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCallingLogResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCallingDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCallingDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerDialMaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerDialMaskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchClientRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchClientResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCallingLogOverviewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCallingLogOverviewResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDefaultLocalPhoneNumberResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallFromBAppRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmUnresolvedRangeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmUnresolvedRangeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkLogResolveStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkLogResolveStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddCompanyToWhitelistRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddCompanyToWhitelistResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCallingLogsRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCallingLogOverviewRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_engagement_v1_calling_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCallingLogOverviewResponse_CallReceived); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_engagement_v1_calling_service_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_service_engagement_v1_calling_service_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_service_engagement_v1_calling_service_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_service_engagement_v1_calling_service_proto_msgTypes[12].OneofWrappers = []interface{}{
		(*GetCallingDetailRequest_CustomerId)(nil),
		(*GetCallingDetailRequest_ClientId)(nil),
	}
	file_moego_service_engagement_v1_calling_service_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*GetCustomerDialMaskRequest_CustomerId)(nil),
		(*GetCustomerDialMaskRequest_ClientId)(nil),
	}
	file_moego_service_engagement_v1_calling_service_proto_msgTypes[21].OneofWrappers = []interface{}{
		(*CallFromBAppRequest_CustomerId)(nil),
		(*CallFromBAppRequest_ClientId)(nil),
		(*CallFromBAppRequest_PhoneNumber)(nil),
	}
	file_moego_service_engagement_v1_calling_service_proto_msgTypes[26].OneofWrappers = []interface{}{
		(*AddCompanyToWhitelistRequest_CompanyId)(nil),
		(*AddCompanyToWhitelistRequest_CompanyOwnerEmail)(nil),
	}
	file_moego_service_engagement_v1_calling_service_proto_msgTypes[28].OneofWrappers = []interface{}{}
	file_moego_service_engagement_v1_calling_service_proto_msgTypes[29].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_engagement_v1_calling_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_engagement_v1_calling_service_proto_goTypes,
		DependencyIndexes: file_moego_service_engagement_v1_calling_service_proto_depIdxs,
		MessageInfos:      file_moego_service_engagement_v1_calling_service_proto_msgTypes,
	}.Build()
	File_moego_service_engagement_v1_calling_service_proto = out.File
	file_moego_service_engagement_v1_calling_service_proto_rawDesc = nil
	file_moego_service_engagement_v1_calling_service_proto_goTypes = nil
	file_moego_service_engagement_v1_calling_service_proto_depIdxs = nil
}
