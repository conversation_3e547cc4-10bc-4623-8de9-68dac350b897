// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/metadata/v1/metadata_api.proto

package metadataapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// MetadataApiServiceClient is the client API for MetadataApiService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Deprecated: Do not use.
type MetadataApiServiceClient interface {
	// get all metadata related to current user
	// filter by group or key
	DescribeMetadata(ctx context.Context, in *DescribeMetadataParams, opts ...grpc.CallOption) (*DescribeMetadataResult, error)
	// update a metadata key
	// Note: if you make a ultra vires operation, will will return a 401 rather than 403 error code
	// TODO(junbao): allow update staff's value if is owner according to the permission
	UpdateMetadata(ctx context.Context, in *UpdateMetadataParams, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type metadataApiServiceClient struct {
	cc grpc.ClientConnInterface
}

// Deprecated: Do not use.
func NewMetadataApiServiceClient(cc grpc.ClientConnInterface) MetadataApiServiceClient {
	return &metadataApiServiceClient{cc}
}

func (c *metadataApiServiceClient) DescribeMetadata(ctx context.Context, in *DescribeMetadataParams, opts ...grpc.CallOption) (*DescribeMetadataResult, error) {
	out := new(DescribeMetadataResult)
	err := c.cc.Invoke(ctx, "/moego.api.metadata.v1.MetadataApiService/DescribeMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataApiServiceClient) UpdateMetadata(ctx context.Context, in *UpdateMetadataParams, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.api.metadata.v1.MetadataApiService/UpdateMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MetadataApiServiceServer is the server API for MetadataApiService service.
// All implementations must embed UnimplementedMetadataApiServiceServer
// for forward compatibility
//
// Deprecated: Do not use.
type MetadataApiServiceServer interface {
	// get all metadata related to current user
	// filter by group or key
	DescribeMetadata(context.Context, *DescribeMetadataParams) (*DescribeMetadataResult, error)
	// update a metadata key
	// Note: if you make a ultra vires operation, will will return a 401 rather than 403 error code
	// TODO(junbao): allow update staff's value if is owner according to the permission
	UpdateMetadata(context.Context, *UpdateMetadataParams) (*emptypb.Empty, error)
	mustEmbedUnimplementedMetadataApiServiceServer()
}

// UnimplementedMetadataApiServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMetadataApiServiceServer struct {
}

func (UnimplementedMetadataApiServiceServer) DescribeMetadata(context.Context, *DescribeMetadataParams) (*DescribeMetadataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeMetadata not implemented")
}
func (UnimplementedMetadataApiServiceServer) UpdateMetadata(context.Context, *UpdateMetadataParams) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMetadata not implemented")
}
func (UnimplementedMetadataApiServiceServer) mustEmbedUnimplementedMetadataApiServiceServer() {}

// UnsafeMetadataApiServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MetadataApiServiceServer will
// result in compilation errors.
type UnsafeMetadataApiServiceServer interface {
	mustEmbedUnimplementedMetadataApiServiceServer()
}

// Deprecated: Do not use.
func RegisterMetadataApiServiceServer(s grpc.ServiceRegistrar, srv MetadataApiServiceServer) {
	s.RegisterService(&MetadataApiService_ServiceDesc, srv)
}

func _MetadataApiService_DescribeMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeMetadataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataApiServiceServer).DescribeMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.metadata.v1.MetadataApiService/DescribeMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataApiServiceServer).DescribeMetadata(ctx, req.(*DescribeMetadataParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataApiService_UpdateMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMetadataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataApiServiceServer).UpdateMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.metadata.v1.MetadataApiService/UpdateMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataApiServiceServer).UpdateMetadata(ctx, req.(*UpdateMetadataParams))
	}
	return interceptor(ctx, in, info, handler)
}

// MetadataApiService_ServiceDesc is the grpc.ServiceDesc for MetadataApiService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MetadataApiService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.metadata.v1.MetadataApiService",
	HandlerType: (*MetadataApiServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DescribeMetadata",
			Handler:    _MetadataApiService_DescribeMetadata_Handler,
		},
		{
			MethodName: "UpdateMetadata",
			Handler:    _MetadataApiService_UpdateMetadata_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/metadata/v1/metadata_api.proto",
}
