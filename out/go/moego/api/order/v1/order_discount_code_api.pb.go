// @since 2023-10-20 17:20:50
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/order/v1/order_discount_code_api.proto

package orderapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/marketing/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get available discount code list request
type GetAvailableDiscountListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination request
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// invoice id
	InvoiceId int64 `protobuf:"varint,2,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
	// code name
	CodeName *string `protobuf:"bytes,3,opt,name=code_name,json=codeName,proto3,oneof" json:"code_name,omitempty"`
}

func (x *GetAvailableDiscountListRequest) Reset() {
	*x = GetAvailableDiscountListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_discount_code_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableDiscountListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableDiscountListRequest) ProtoMessage() {}

func (x *GetAvailableDiscountListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_discount_code_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableDiscountListRequest.ProtoReflect.Descriptor instead.
func (*GetAvailableDiscountListRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_discount_code_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetAvailableDiscountListRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetAvailableDiscountListRequest) GetInvoiceId() int64 {
	if x != nil {
		return x.InvoiceId
	}
	return 0
}

func (x *GetAvailableDiscountListRequest) GetCodeName() string {
	if x != nil && x.CodeName != nil {
		return *x.CodeName
	}
	return ""
}

// get available discount code list response
type GetAvailableDiscountListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination response
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// discount code list
	DiscountCodeCompositeViews []*v1.DiscountCodeCompositeView `protobuf:"bytes,2,rep,name=discount_code_composite_views,json=discountCodeCompositeViews,proto3" json:"discount_code_composite_views,omitempty"`
}

func (x *GetAvailableDiscountListResponse) Reset() {
	*x = GetAvailableDiscountListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_discount_code_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableDiscountListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableDiscountListResponse) ProtoMessage() {}

func (x *GetAvailableDiscountListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_discount_code_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableDiscountListResponse.ProtoReflect.Descriptor instead.
func (*GetAvailableDiscountListResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_discount_code_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetAvailableDiscountListResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetAvailableDiscountListResponse) GetDiscountCodeCompositeViews() []*v1.DiscountCodeCompositeView {
	if x != nil {
		return x.DiscountCodeCompositeViews
	}
	return nil
}

// ListApplicableLineItemsParams
type ListApplicableLineItemsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount id
	DiscountId int64 `protobuf:"varint,1,opt,name=discount_id,json=discountId,proto3" json:"discount_id,omitempty"`
	// order id
	OrderId int64 `protobuf:"varint,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *ListApplicableLineItemsParams) Reset() {
	*x = ListApplicableLineItemsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_discount_code_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListApplicableLineItemsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApplicableLineItemsParams) ProtoMessage() {}

func (x *ListApplicableLineItemsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_discount_code_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApplicableLineItemsParams.ProtoReflect.Descriptor instead.
func (*ListApplicableLineItemsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_discount_code_api_proto_rawDescGZIP(), []int{2}
}

func (x *ListApplicableLineItemsParams) GetDiscountId() int64 {
	if x != nil {
		return x.DiscountId
	}
	return 0
}

func (x *ListApplicableLineItemsParams) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

// ListApplicableLineItemsResult
type ListApplicableLineItemsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order Line Item Model
	OrderLineItems []*v11.OrderLineItemModel `protobuf:"bytes,1,rep,name=order_line_items,json=orderLineItems,proto3" json:"order_line_items,omitempty"`
	// 关联的宠物的基本信息.
	PetBriefs []*v11.OrderDetailView_PetBrief `protobuf:"bytes,2,rep,name=pet_briefs,json=petBriefs,proto3" json:"pet_briefs,omitempty"`
}

func (x *ListApplicableLineItemsResult) Reset() {
	*x = ListApplicableLineItemsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_discount_code_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListApplicableLineItemsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApplicableLineItemsResult) ProtoMessage() {}

func (x *ListApplicableLineItemsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_discount_code_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApplicableLineItemsResult.ProtoReflect.Descriptor instead.
func (*ListApplicableLineItemsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_discount_code_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListApplicableLineItemsResult) GetOrderLineItems() []*v11.OrderLineItemModel {
	if x != nil {
		return x.OrderLineItems
	}
	return nil
}

func (x *ListApplicableLineItemsResult) GetPetBriefs() []*v11.OrderDetailView_PetBrief {
	if x != nil {
		return x.PetBriefs
	}
	return nil
}

var File_moego_api_order_v1_order_discount_code_api_proto protoreflect.FileDescriptor

var file_moego_api_order_v1_order_discount_code_api_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x12, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76,
	0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcf, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a,
	0x09, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x14, 0x48, 0x00, 0x52, 0x08, 0x63, 0x6f, 0x64,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xdf, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x77, 0x0a, 0x1d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x1a, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x73, 0x22, 0x5b, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0xc4, 0x01, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x53, 0x0a, 0x10, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c,
	0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x4e, 0x0a, 0x0a,
	0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x69, 0x65, 0x66, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x69, 0x65,
	0x66, 0x52, 0x09, 0x70, 0x65, 0x74, 0x42, 0x72, 0x69, 0x65, 0x66, 0x73, 0x32, 0xa3, 0x02, 0x0a,
	0x18, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x7f, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x62, 0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x31, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65,
	0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x62, 0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x42, 0x72, 0x0a, 0x1a, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_order_v1_order_discount_code_api_proto_rawDescOnce sync.Once
	file_moego_api_order_v1_order_discount_code_api_proto_rawDescData = file_moego_api_order_v1_order_discount_code_api_proto_rawDesc
)

func file_moego_api_order_v1_order_discount_code_api_proto_rawDescGZIP() []byte {
	file_moego_api_order_v1_order_discount_code_api_proto_rawDescOnce.Do(func() {
		file_moego_api_order_v1_order_discount_code_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_order_v1_order_discount_code_api_proto_rawDescData)
	})
	return file_moego_api_order_v1_order_discount_code_api_proto_rawDescData
}

var file_moego_api_order_v1_order_discount_code_api_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_api_order_v1_order_discount_code_api_proto_goTypes = []interface{}{
	(*GetAvailableDiscountListRequest)(nil),  // 0: moego.api.order.v1.GetAvailableDiscountListRequest
	(*GetAvailableDiscountListResponse)(nil), // 1: moego.api.order.v1.GetAvailableDiscountListResponse
	(*ListApplicableLineItemsParams)(nil),    // 2: moego.api.order.v1.ListApplicableLineItemsParams
	(*ListApplicableLineItemsResult)(nil),    // 3: moego.api.order.v1.ListApplicableLineItemsResult
	(*v2.PaginationRequest)(nil),             // 4: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),            // 5: moego.utils.v2.PaginationResponse
	(*v1.DiscountCodeCompositeView)(nil),     // 6: moego.models.marketing.v1.DiscountCodeCompositeView
	(*v11.OrderLineItemModel)(nil),           // 7: moego.models.order.v1.OrderLineItemModel
	(*v11.OrderDetailView_PetBrief)(nil),     // 8: moego.models.order.v1.OrderDetailView.PetBrief
}
var file_moego_api_order_v1_order_discount_code_api_proto_depIdxs = []int32{
	4, // 0: moego.api.order.v1.GetAvailableDiscountListRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	5, // 1: moego.api.order.v1.GetAvailableDiscountListResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	6, // 2: moego.api.order.v1.GetAvailableDiscountListResponse.discount_code_composite_views:type_name -> moego.models.marketing.v1.DiscountCodeCompositeView
	7, // 3: moego.api.order.v1.ListApplicableLineItemsResult.order_line_items:type_name -> moego.models.order.v1.OrderLineItemModel
	8, // 4: moego.api.order.v1.ListApplicableLineItemsResult.pet_briefs:type_name -> moego.models.order.v1.OrderDetailView.PetBrief
	0, // 5: moego.api.order.v1.OrderDiscountCodeService.GetAvailableDiscountList:input_type -> moego.api.order.v1.GetAvailableDiscountListRequest
	2, // 6: moego.api.order.v1.OrderDiscountCodeService.ListApplicableLineItems:input_type -> moego.api.order.v1.ListApplicableLineItemsParams
	1, // 7: moego.api.order.v1.OrderDiscountCodeService.GetAvailableDiscountList:output_type -> moego.api.order.v1.GetAvailableDiscountListResponse
	3, // 8: moego.api.order.v1.OrderDiscountCodeService.ListApplicableLineItems:output_type -> moego.api.order.v1.ListApplicableLineItemsResult
	7, // [7:9] is the sub-list for method output_type
	5, // [5:7] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_api_order_v1_order_discount_code_api_proto_init() }
func file_moego_api_order_v1_order_discount_code_api_proto_init() {
	if File_moego_api_order_v1_order_discount_code_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_order_v1_order_discount_code_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableDiscountListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_discount_code_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableDiscountListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_discount_code_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListApplicableLineItemsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_discount_code_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListApplicableLineItemsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_order_v1_order_discount_code_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_order_v1_order_discount_code_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_order_v1_order_discount_code_api_proto_goTypes,
		DependencyIndexes: file_moego_api_order_v1_order_discount_code_api_proto_depIdxs,
		MessageInfos:      file_moego_api_order_v1_order_discount_code_api_proto_msgTypes,
	}.Build()
	File_moego_api_order_v1_order_discount_code_api_proto = out.File
	file_moego_api_order_v1_order_discount_code_api_proto_rawDesc = nil
	file_moego_api_order_v1_order_discount_code_api_proto_goTypes = nil
	file_moego_api_order_v1_order_discount_code_api_proto_depIdxs = nil
}
