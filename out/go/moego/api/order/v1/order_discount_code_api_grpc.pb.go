// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/order/v1/order_discount_code_api.proto

package orderapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// OrderDiscountCodeServiceClient is the client API for OrderDiscountCodeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OrderDiscountCodeServiceClient interface {
	// get available discount code list
	GetAvailableDiscountList(ctx context.Context, in *GetAvailableDiscountListRequest, opts ...grpc.CallOption) (*GetAvailableDiscountListResponse, error)
	// list applicable line items
	ListApplicableLineItems(ctx context.Context, in *ListApplicableLineItemsParams, opts ...grpc.CallOption) (*ListApplicableLineItemsResult, error)
}

type orderDiscountCodeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOrderDiscountCodeServiceClient(cc grpc.ClientConnInterface) OrderDiscountCodeServiceClient {
	return &orderDiscountCodeServiceClient{cc}
}

func (c *orderDiscountCodeServiceClient) GetAvailableDiscountList(ctx context.Context, in *GetAvailableDiscountListRequest, opts ...grpc.CallOption) (*GetAvailableDiscountListResponse, error) {
	out := new(GetAvailableDiscountListResponse)
	err := c.cc.Invoke(ctx, "/moego.api.order.v1.OrderDiscountCodeService/GetAvailableDiscountList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderDiscountCodeServiceClient) ListApplicableLineItems(ctx context.Context, in *ListApplicableLineItemsParams, opts ...grpc.CallOption) (*ListApplicableLineItemsResult, error) {
	out := new(ListApplicableLineItemsResult)
	err := c.cc.Invoke(ctx, "/moego.api.order.v1.OrderDiscountCodeService/ListApplicableLineItems", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrderDiscountCodeServiceServer is the server API for OrderDiscountCodeService service.
// All implementations must embed UnimplementedOrderDiscountCodeServiceServer
// for forward compatibility
type OrderDiscountCodeServiceServer interface {
	// get available discount code list
	GetAvailableDiscountList(context.Context, *GetAvailableDiscountListRequest) (*GetAvailableDiscountListResponse, error)
	// list applicable line items
	ListApplicableLineItems(context.Context, *ListApplicableLineItemsParams) (*ListApplicableLineItemsResult, error)
	mustEmbedUnimplementedOrderDiscountCodeServiceServer()
}

// UnimplementedOrderDiscountCodeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedOrderDiscountCodeServiceServer struct {
}

func (UnimplementedOrderDiscountCodeServiceServer) GetAvailableDiscountList(context.Context, *GetAvailableDiscountListRequest) (*GetAvailableDiscountListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAvailableDiscountList not implemented")
}
func (UnimplementedOrderDiscountCodeServiceServer) ListApplicableLineItems(context.Context, *ListApplicableLineItemsParams) (*ListApplicableLineItemsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListApplicableLineItems not implemented")
}
func (UnimplementedOrderDiscountCodeServiceServer) mustEmbedUnimplementedOrderDiscountCodeServiceServer() {
}

// UnsafeOrderDiscountCodeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OrderDiscountCodeServiceServer will
// result in compilation errors.
type UnsafeOrderDiscountCodeServiceServer interface {
	mustEmbedUnimplementedOrderDiscountCodeServiceServer()
}

func RegisterOrderDiscountCodeServiceServer(s grpc.ServiceRegistrar, srv OrderDiscountCodeServiceServer) {
	s.RegisterService(&OrderDiscountCodeService_ServiceDesc, srv)
}

func _OrderDiscountCodeService_GetAvailableDiscountList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvailableDiscountListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderDiscountCodeServiceServer).GetAvailableDiscountList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.order.v1.OrderDiscountCodeService/GetAvailableDiscountList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderDiscountCodeServiceServer).GetAvailableDiscountList(ctx, req.(*GetAvailableDiscountListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderDiscountCodeService_ListApplicableLineItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListApplicableLineItemsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderDiscountCodeServiceServer).ListApplicableLineItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.order.v1.OrderDiscountCodeService/ListApplicableLineItems",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderDiscountCodeServiceServer).ListApplicableLineItems(ctx, req.(*ListApplicableLineItemsParams))
	}
	return interceptor(ctx, in, info, handler)
}

// OrderDiscountCodeService_ServiceDesc is the grpc.ServiceDesc for OrderDiscountCodeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OrderDiscountCodeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.order.v1.OrderDiscountCodeService",
	HandlerType: (*OrderDiscountCodeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAvailableDiscountList",
			Handler:    _OrderDiscountCodeService_GetAvailableDiscountList_Handler,
		},
		{
			MethodName: "ListApplicableLineItems",
			Handler:    _OrderDiscountCodeService_ListApplicableLineItems_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/order/v1/order_discount_code_api.proto",
}
