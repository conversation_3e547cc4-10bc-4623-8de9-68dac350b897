// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/order/v1/order_api.proto

package orderapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// invoice status.
type GetInvoiceResult_InvoiceStatus int32

const (
	// Unspecified.
	GetInvoiceResult_INVOICE_STATUS_UNSPECIFIED GetInvoiceResult_InvoiceStatus = 0
	// Open.
	GetInvoiceResult_OPEN GetInvoiceResult_InvoiceStatus = 1
	// Close.
	GetInvoiceResult_CLOSE GetInvoiceResult_InvoiceStatus = 2
	// Void, 未使用.
	GetInvoiceResult_VOID GetInvoiceResult_InvoiceStatus = 3
)

// Enum value maps for GetInvoiceResult_InvoiceStatus.
var (
	GetInvoiceResult_InvoiceStatus_name = map[int32]string{
		0: "INVOICE_STATUS_UNSPECIFIED",
		1: "OPEN",
		2: "CLOSE",
		3: "VOID",
	}
	GetInvoiceResult_InvoiceStatus_value = map[string]int32{
		"INVOICE_STATUS_UNSPECIFIED": 0,
		"OPEN":                       1,
		"CLOSE":                      2,
		"VOID":                       3,
	}
)

func (x GetInvoiceResult_InvoiceStatus) Enum() *GetInvoiceResult_InvoiceStatus {
	p := new(GetInvoiceResult_InvoiceStatus)
	*p = x
	return p
}

func (x GetInvoiceResult_InvoiceStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetInvoiceResult_InvoiceStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_api_order_v1_order_api_proto_enumTypes[0].Descriptor()
}

func (GetInvoiceResult_InvoiceStatus) Type() protoreflect.EnumType {
	return &file_moego_api_order_v1_order_api_proto_enumTypes[0]
}

func (x GetInvoiceResult_InvoiceStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetInvoiceResult_InvoiceStatus.Descriptor instead.
func (GetInvoiceResult_InvoiceStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{51, 0}
}

// add/remove service charge request
type AddOrRemoveServiceChargeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// service charge id list
	ServiceChargeId []int64 `protobuf:"varint,2,rep,packed,name=service_charge_id,json=serviceChargeId,proto3" json:"service_charge_id,omitempty"`
	// check result
	CheckRefund *bool `protobuf:"varint,3,opt,name=check_refund,json=checkRefund,proto3,oneof" json:"check_refund,omitempty"`
}

func (x *AddOrRemoveServiceChargeRequest) Reset() {
	*x = AddOrRemoveServiceChargeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddOrRemoveServiceChargeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddOrRemoveServiceChargeRequest) ProtoMessage() {}

func (x *AddOrRemoveServiceChargeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddOrRemoveServiceChargeRequest.ProtoReflect.Descriptor instead.
func (*AddOrRemoveServiceChargeRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{0}
}

func (x *AddOrRemoveServiceChargeRequest) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *AddOrRemoveServiceChargeRequest) GetServiceChargeId() []int64 {
	if x != nil {
		return x.ServiceChargeId
	}
	return nil
}

func (x *AddOrRemoveServiceChargeRequest) GetCheckRefund() bool {
	if x != nil && x.CheckRefund != nil {
		return *x.CheckRefund
	}
	return false
}

// add service charge to order response
type OperateServiceChargeToOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// trigger refund
	RefundChannel *v1.RefundChannelResponse `protobuf:"bytes,2,opt,name=refund_channel,json=refundChannel,proto3" json:"refund_channel,omitempty"`
}

func (x *OperateServiceChargeToOrderResponse) Reset() {
	*x = OperateServiceChargeToOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperateServiceChargeToOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperateServiceChargeToOrderResponse) ProtoMessage() {}

func (x *OperateServiceChargeToOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperateServiceChargeToOrderResponse.ProtoReflect.Descriptor instead.
func (*OperateServiceChargeToOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{1}
}

func (x *OperateServiceChargeToOrderResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *OperateServiceChargeToOrderResponse) GetRefundChannel() *v1.RefundChannelResponse {
	if x != nil {
		return x.RefundChannel
	}
	return nil
}

// set tip request
type SetTipsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	InvoiceId int64 `protobuf:"varint,1,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
	// value type
	ValueType string `protobuf:"bytes,2,opt,name=value_type,json=valueType,proto3" json:"value_type,omitempty"`
	// value
	Value float64 `protobuf:"fixed64,3,opt,name=value,proto3" json:"value,omitempty"`
	// omit result
	OmitResult bool `protobuf:"varint,4,opt,name=omit_result,json=omitResult,proto3" json:"omit_result,omitempty"`
	// last modified time
	LastModifiedTime int64 `protobuf:"varint,5,opt,name=last_modified_time,json=lastModifiedTime,proto3" json:"last_modified_time,omitempty"`
	// check refund
	CheckRefund *bool `protobuf:"varint,6,opt,name=check_refund,json=checkRefund,proto3,oneof" json:"check_refund,omitempty"`
}

func (x *SetTipsRequest) Reset() {
	*x = SetTipsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTipsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTipsRequest) ProtoMessage() {}

func (x *SetTipsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTipsRequest.ProtoReflect.Descriptor instead.
func (*SetTipsRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{2}
}

func (x *SetTipsRequest) GetInvoiceId() int64 {
	if x != nil {
		return x.InvoiceId
	}
	return 0
}

func (x *SetTipsRequest) GetValueType() string {
	if x != nil {
		return x.ValueType
	}
	return ""
}

func (x *SetTipsRequest) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *SetTipsRequest) GetOmitResult() bool {
	if x != nil {
		return x.OmitResult
	}
	return false
}

func (x *SetTipsRequest) GetLastModifiedTime() int64 {
	if x != nil {
		return x.LastModifiedTime
	}
	return 0
}

func (x *SetTipsRequest) GetCheckRefund() bool {
	if x != nil && x.CheckRefund != nil {
		return *x.CheckRefund
	}
	return false
}

// set tip response
type SetTipsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// refund channels
	RefundChannel *v1.RefundChannelResponse `protobuf:"bytes,2,opt,name=refund_channel,json=refundChannel,proto3" json:"refund_channel,omitempty"`
}

func (x *SetTipsResponse) Reset() {
	*x = SetTipsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetTipsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTipsResponse) ProtoMessage() {}

func (x *SetTipsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTipsResponse.ProtoReflect.Descriptor instead.
func (*SetTipsResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{3}
}

func (x *SetTipsResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *SetTipsResponse) GetRefundChannel() *v1.RefundChannelResponse {
	if x != nil {
		return x.RefundChannel
	}
	return nil
}

// modify item tax request
type ModifyItemTaxRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	OrderId int32 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// service/product/service charge id
	ObjectId int32 `protobuf:"varint,2,opt,name=object_id,json=objectId,proto3" json:"object_id,omitempty"`
	// item type
	ItemType string `protobuf:"bytes,3,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	// tax id
	TaxId int32 `protobuf:"varint,4,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// trigger refund
	CheckRefund *bool `protobuf:"varint,5,opt,name=check_refund,json=checkRefund,proto3,oneof" json:"check_refund,omitempty"`
}

func (x *ModifyItemTaxRequest) Reset() {
	*x = ModifyItemTaxRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyItemTaxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyItemTaxRequest) ProtoMessage() {}

func (x *ModifyItemTaxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyItemTaxRequest.ProtoReflect.Descriptor instead.
func (*ModifyItemTaxRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{4}
}

func (x *ModifyItemTaxRequest) GetOrderId() int32 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *ModifyItemTaxRequest) GetObjectId() int32 {
	if x != nil {
		return x.ObjectId
	}
	return 0
}

func (x *ModifyItemTaxRequest) GetItemType() string {
	if x != nil {
		return x.ItemType
	}
	return ""
}

func (x *ModifyItemTaxRequest) GetTaxId() int32 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *ModifyItemTaxRequest) GetCheckRefund() bool {
	if x != nil && x.CheckRefund != nil {
		return *x.CheckRefund
	}
	return false
}

// modify item tax response
type ModifyItemTaxResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// trigger refund
	RefundChannel *v1.RefundChannelResponse `protobuf:"bytes,2,opt,name=refund_channel,json=refundChannel,proto3,oneof" json:"refund_channel,omitempty"`
}

func (x *ModifyItemTaxResponse) Reset() {
	*x = ModifyItemTaxResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyItemTaxResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyItemTaxResponse) ProtoMessage() {}

func (x *ModifyItemTaxResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyItemTaxResponse.ProtoReflect.Descriptor instead.
func (*ModifyItemTaxResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{5}
}

func (x *ModifyItemTaxResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *ModifyItemTaxResponse) GetRefundChannel() *v1.RefundChannelResponse {
	if x != nil {
		return x.RefundChannel
	}
	return nil
}

// set discount request
type SetDiscountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// discount id
	DiscountId int64 `protobuf:"varint,2,opt,name=discount_id,json=discountId,proto3" json:"discount_id,omitempty"`
	// discount type
	DiscountType string `protobuf:"bytes,3,opt,name=discount_type,json=discountType,proto3" json:"discount_type,omitempty"`
	// discount value
	DiscountValue float64 `protobuf:"fixed64,4,opt,name=discount_value,json=discountValue,proto3" json:"discount_value,omitempty"`
	// check result
	CheckRefund *bool `protobuf:"varint,5,opt,name=check_refund,json=checkRefund,proto3,oneof" json:"check_refund,omitempty"`
}

func (x *SetDiscountRequest) Reset() {
	*x = SetDiscountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetDiscountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDiscountRequest) ProtoMessage() {}

func (x *SetDiscountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDiscountRequest.ProtoReflect.Descriptor instead.
func (*SetDiscountRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{6}
}

func (x *SetDiscountRequest) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *SetDiscountRequest) GetDiscountId() int64 {
	if x != nil {
		return x.DiscountId
	}
	return 0
}

func (x *SetDiscountRequest) GetDiscountType() string {
	if x != nil {
		return x.DiscountType
	}
	return ""
}

func (x *SetDiscountRequest) GetDiscountValue() float64 {
	if x != nil {
		return x.DiscountValue
	}
	return 0
}

func (x *SetDiscountRequest) GetCheckRefund() bool {
	if x != nil && x.CheckRefund != nil {
		return *x.CheckRefund
	}
	return false
}

// set discount response
type SetDiscountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *SetDiscountResponse) Reset() {
	*x = SetDiscountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetDiscountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDiscountResponse) ProtoMessage() {}

func (x *SetDiscountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDiscountResponse.ProtoReflect.Descriptor instead.
func (*SetDiscountResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{7}
}

func (x *SetDiscountResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

// get order detail params
type GetOrderHistoryParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// origin order id
	OriginOrderId int64 `protobuf:"varint,1,opt,name=origin_order_id,json=originOrderId,proto3" json:"origin_order_id,omitempty"`
}

func (x *GetOrderHistoryParams) Reset() {
	*x = GetOrderHistoryParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderHistoryParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderHistoryParams) ProtoMessage() {}

func (x *GetOrderHistoryParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderHistoryParams.ProtoReflect.Descriptor instead.
func (*GetOrderHistoryParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{8}
}

func (x *GetOrderHistoryParams) GetOriginOrderId() int64 {
	if x != nil {
		return x.OriginOrderId
	}
	return 0
}

// get order detail result
type GetOrderHistoryResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order history view
	OrderView []*v1.OrderModelHistoryView `protobuf:"bytes,1,rep,name=order_view,json=orderView,proto3" json:"order_view,omitempty"` // TODO list refund order view
}

func (x *GetOrderHistoryResult) Reset() {
	*x = GetOrderHistoryResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderHistoryResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderHistoryResult) ProtoMessage() {}

func (x *GetOrderHistoryResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderHistoryResult.ProtoReflect.Descriptor instead.
func (*GetOrderHistoryResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{9}
}

func (x *GetOrderHistoryResult) GetOrderView() []*v1.OrderModelHistoryView {
	if x != nil {
		return x.OrderView
	}
	return nil
}

// complete order params
type CompleteOrderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *CompleteOrderParams) Reset() {
	*x = CompleteOrderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteOrderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteOrderParams) ProtoMessage() {}

func (x *CompleteOrderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteOrderParams.ProtoReflect.Descriptor instead.
func (*CompleteOrderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{10}
}

func (x *CompleteOrderParams) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

// complete order result
type CompleteOrderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *CompleteOrderResult) Reset() {
	*x = CompleteOrderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteOrderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteOrderResult) ProtoMessage() {}

func (x *CompleteOrderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteOrderResult.ProtoReflect.Descriptor instead.
func (*CompleteOrderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{11}
}

func (x *CompleteOrderResult) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

// create extra order params
type CreateExtraOrderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// origin order id
	OriginOrderId int64 `protobuf:"varint,1,opt,name=origin_order_id,json=originOrderId,proto3" json:"origin_order_id,omitempty"`
	// extra charge reason
	ExtraChargeReason *string `protobuf:"bytes,2,opt,name=extra_charge_reason,json=extraChargeReason,proto3,oneof" json:"extra_charge_reason,omitempty"`
}

func (x *CreateExtraOrderParams) Reset() {
	*x = CreateExtraOrderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExtraOrderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExtraOrderParams) ProtoMessage() {}

func (x *CreateExtraOrderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExtraOrderParams.ProtoReflect.Descriptor instead.
func (*CreateExtraOrderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{12}
}

func (x *CreateExtraOrderParams) GetOriginOrderId() int64 {
	if x != nil {
		return x.OriginOrderId
	}
	return 0
}

func (x *CreateExtraOrderParams) GetExtraChargeReason() string {
	if x != nil && x.ExtraChargeReason != nil {
		return *x.ExtraChargeReason
	}
	return ""
}

// create extra order result
type CreateExtraOrderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// extra order id
	ExtraOrderId int64 `protobuf:"varint,1,opt,name=extra_order_id,json=extraOrderId,proto3" json:"extra_order_id,omitempty"`
}

func (x *CreateExtraOrderResult) Reset() {
	*x = CreateExtraOrderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExtraOrderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExtraOrderResult) ProtoMessage() {}

func (x *CreateExtraOrderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExtraOrderResult.ProtoReflect.Descriptor instead.
func (*CreateExtraOrderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{13}
}

func (x *CreateExtraOrderResult) GetExtraOrderId() int64 {
	if x != nil {
		return x.ExtraOrderId
	}
	return 0
}

// update extra order params
type UpdateExtraOrderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// extra order id
	ExtraOrderId int64 `protobuf:"varint,1,opt,name=extra_order_id,json=extraOrderId,proto3" json:"extra_order_id,omitempty"`
	// pet detail def
	PetDetail *v11.PetDetailDef `protobuf:"bytes,2,opt,name=pet_detail,json=petDetail,proto3,oneof" json:"pet_detail,omitempty"`
	// extra charge reason
	ExtraChargeReason *string `protobuf:"bytes,3,opt,name=extra_charge_reason,json=extraChargeReason,proto3,oneof" json:"extra_charge_reason,omitempty"`
}

func (x *UpdateExtraOrderParams) Reset() {
	*x = UpdateExtraOrderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateExtraOrderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateExtraOrderParams) ProtoMessage() {}

func (x *UpdateExtraOrderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateExtraOrderParams.ProtoReflect.Descriptor instead.
func (*UpdateExtraOrderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateExtraOrderParams) GetExtraOrderId() int64 {
	if x != nil {
		return x.ExtraOrderId
	}
	return 0
}

func (x *UpdateExtraOrderParams) GetPetDetail() *v11.PetDetailDef {
	if x != nil {
		return x.PetDetail
	}
	return nil
}

func (x *UpdateExtraOrderParams) GetExtraChargeReason() string {
	if x != nil && x.ExtraChargeReason != nil {
		return *x.ExtraChargeReason
	}
	return ""
}

// update extra order result
type UpdateExtraOrderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *UpdateExtraOrderResult) Reset() {
	*x = UpdateExtraOrderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateExtraOrderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateExtraOrderResult) ProtoMessage() {}

func (x *UpdateExtraOrderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateExtraOrderResult.ProtoReflect.Descriptor instead.
func (*UpdateExtraOrderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateExtraOrderResult) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

// update pet detail params
type UpdateLineItemForExtraOrderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// service price
	ServicePrice *float64 `protobuf:"fixed64,3,opt,name=service_price,json=servicePrice,proto3,oneof" json:"service_price,omitempty"`
}

func (x *UpdateLineItemForExtraOrderParams) Reset() {
	*x = UpdateLineItemForExtraOrderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLineItemForExtraOrderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLineItemForExtraOrderParams) ProtoMessage() {}

func (x *UpdateLineItemForExtraOrderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLineItemForExtraOrderParams.ProtoReflect.Descriptor instead.
func (*UpdateLineItemForExtraOrderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateLineItemForExtraOrderParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateLineItemForExtraOrderParams) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *UpdateLineItemForExtraOrderParams) GetServicePrice() float64 {
	if x != nil && x.ServicePrice != nil {
		return *x.ServicePrice
	}
	return 0
}

// update pet detail result
type UpdateLineItemForExtraOrderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *UpdateLineItemForExtraOrderResult) Reset() {
	*x = UpdateLineItemForExtraOrderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLineItemForExtraOrderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLineItemForExtraOrderResult) ProtoMessage() {}

func (x *UpdateLineItemForExtraOrderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLineItemForExtraOrderResult.ProtoReflect.Descriptor instead.
func (*UpdateLineItemForExtraOrderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{17}
}

func (x *UpdateLineItemForExtraOrderResult) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

// delete pet detail params
type DeleteLineItemForExtraOrderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// extra order id
	ExtraOrderId int64 `protobuf:"varint,1,opt,name=extra_order_id,json=extraOrderId,proto3" json:"extra_order_id,omitempty"`
	// pet detail ids
	PetDetailId int64 `protobuf:"varint,2,opt,name=pet_detail_id,json=petDetailId,proto3" json:"pet_detail_id,omitempty"`
}

func (x *DeleteLineItemForExtraOrderParams) Reset() {
	*x = DeleteLineItemForExtraOrderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLineItemForExtraOrderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLineItemForExtraOrderParams) ProtoMessage() {}

func (x *DeleteLineItemForExtraOrderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLineItemForExtraOrderParams.ProtoReflect.Descriptor instead.
func (*DeleteLineItemForExtraOrderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{18}
}

func (x *DeleteLineItemForExtraOrderParams) GetExtraOrderId() int64 {
	if x != nil {
		return x.ExtraOrderId
	}
	return 0
}

func (x *DeleteLineItemForExtraOrderParams) GetPetDetailId() int64 {
	if x != nil {
		return x.PetDetailId
	}
	return 0
}

// delete pet detail result
type DeleteLineItemForExtraOrderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *DeleteLineItemForExtraOrderResult) Reset() {
	*x = DeleteLineItemForExtraOrderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLineItemForExtraOrderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLineItemForExtraOrderResult) ProtoMessage() {}

func (x *DeleteLineItemForExtraOrderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLineItemForExtraOrderResult.ProtoReflect.Descriptor instead.
func (*DeleteLineItemForExtraOrderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteLineItemForExtraOrderResult) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

// edit staff params
type EditStaffCommissionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// pet detail list
	EditStaffCommissionItems []*v1.EditStaffCommissionItem `protobuf:"bytes,2,rep,name=edit_staff_commission_items,json=editStaffCommissionItems,proto3" json:"edit_staff_commission_items,omitempty"`
}

func (x *EditStaffCommissionParams) Reset() {
	*x = EditStaffCommissionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditStaffCommissionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditStaffCommissionParams) ProtoMessage() {}

func (x *EditStaffCommissionParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditStaffCommissionParams.ProtoReflect.Descriptor instead.
func (*EditStaffCommissionParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{20}
}

func (x *EditStaffCommissionParams) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *EditStaffCommissionParams) GetEditStaffCommissionItems() []*v1.EditStaffCommissionItem {
	if x != nil {
		return x.EditStaffCommissionItems
	}
	return nil
}

// edit staff result
type EditStaffCommissionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EditStaffCommissionResult) Reset() {
	*x = EditStaffCommissionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditStaffCommissionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditStaffCommissionResult) ProtoMessage() {}

func (x *EditStaffCommissionResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditStaffCommissionResult.ProtoReflect.Descriptor instead.
func (*EditStaffCommissionResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{21}
}

// upgrade invoice reinvent params
// 不用数据，company id从token拿
type UpgradeInvoiceReinventParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpgradeInvoiceReinventParams) Reset() {
	*x = UpgradeInvoiceReinventParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeInvoiceReinventParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeInvoiceReinventParams) ProtoMessage() {}

func (x *UpgradeInvoiceReinventParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeInvoiceReinventParams.ProtoReflect.Descriptor instead.
func (*UpgradeInvoiceReinventParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{22}
}

// upgrade invoice reinvent result
type UpgradeInvoiceReinventResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpgradeInvoiceReinventResult) Reset() {
	*x = UpgradeInvoiceReinventResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeInvoiceReinventResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeInvoiceReinventResult) ProtoMessage() {}

func (x *UpgradeInvoiceReinventResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeInvoiceReinventResult.ProtoReflect.Descriptor instead.
func (*UpgradeInvoiceReinventResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{23}
}

// check invoice reinvent params
// 不用数据，company id从token拿
type CheckInvoiceReinventParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CheckInvoiceReinventParams) Reset() {
	*x = CheckInvoiceReinventParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckInvoiceReinventParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckInvoiceReinventParams) ProtoMessage() {}

func (x *CheckInvoiceReinventParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckInvoiceReinventParams.ProtoReflect.Descriptor instead.
func (*CheckInvoiceReinventParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{24}
}

// check invoice reinvent result
type CheckInvoiceReinventResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is in invoice reinvent whitelist
	IsAllBizInInvoiceReinventWhitelist bool `protobuf:"varint,1,opt,name=is_all_biz_in_invoice_reinvent_whitelist,json=isAllBizInInvoiceReinventWhitelist,proto3" json:"is_all_biz_in_invoice_reinvent_whitelist,omitempty"`
}

func (x *CheckInvoiceReinventResult) Reset() {
	*x = CheckInvoiceReinventResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckInvoiceReinventResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckInvoiceReinventResult) ProtoMessage() {}

func (x *CheckInvoiceReinventResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckInvoiceReinventResult.ProtoReflect.Descriptor instead.
func (*CheckInvoiceReinventResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{25}
}

func (x *CheckInvoiceReinventResult) GetIsAllBizInInvoiceReinventWhitelist() bool {
	if x != nil {
		return x.IsAllBizInInvoiceReinventWhitelist
	}
	return false
}

// Preview refund order.
type PreviewRefundOrderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order ID.
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// Refund mode.
	RefundMode v1.RefundMode `protobuf:"varint,2,opt,name=refund_mode,json=refundMode,proto3,enum=moego.models.order.v1.RefundMode" json:"refund_mode,omitempty"`
	// 从哪 一/几 笔 Order Payment 中退款.
	SourceOrderPayments []*v12.RefundOrderRequest_OrderPayment `protobuf:"bytes,3,rep,name=source_order_payments,json=sourceOrderPayments,proto3" json:"source_order_payments,omitempty"`
	// Refund by.
	//
	// Types that are assignable to RefundBy:
	//
	//	*PreviewRefundOrderParams_RefundByItem
	//	*PreviewRefundOrderParams_RefundByPayment
	RefundBy isPreviewRefundOrderParams_RefundBy `protobuf_oneof:"refund_by"`
}

func (x *PreviewRefundOrderParams) Reset() {
	*x = PreviewRefundOrderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewRefundOrderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewRefundOrderParams) ProtoMessage() {}

func (x *PreviewRefundOrderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewRefundOrderParams.ProtoReflect.Descriptor instead.
func (*PreviewRefundOrderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{26}
}

func (x *PreviewRefundOrderParams) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *PreviewRefundOrderParams) GetRefundMode() v1.RefundMode {
	if x != nil {
		return x.RefundMode
	}
	return v1.RefundMode(0)
}

func (x *PreviewRefundOrderParams) GetSourceOrderPayments() []*v12.RefundOrderRequest_OrderPayment {
	if x != nil {
		return x.SourceOrderPayments
	}
	return nil
}

func (m *PreviewRefundOrderParams) GetRefundBy() isPreviewRefundOrderParams_RefundBy {
	if m != nil {
		return m.RefundBy
	}
	return nil
}

func (x *PreviewRefundOrderParams) GetRefundByItem() *v12.RefundOrderRequest_RefundByItem {
	if x, ok := x.GetRefundBy().(*PreviewRefundOrderParams_RefundByItem); ok {
		return x.RefundByItem
	}
	return nil
}

func (x *PreviewRefundOrderParams) GetRefundByPayment() *v12.RefundOrderRequest_RefundByPayment {
	if x, ok := x.GetRefundBy().(*PreviewRefundOrderParams_RefundByPayment); ok {
		return x.RefundByPayment
	}
	return nil
}

type isPreviewRefundOrderParams_RefundBy interface {
	isPreviewRefundOrderParams_RefundBy()
}

type PreviewRefundOrderParams_RefundByItem struct {
	// Refund by item.
	RefundByItem *v12.RefundOrderRequest_RefundByItem `protobuf:"bytes,11,opt,name=refund_by_item,json=refundByItem,proto3,oneof"`
}

type PreviewRefundOrderParams_RefundByPayment struct {
	// Refund by payment.
	RefundByPayment *v12.RefundOrderRequest_RefundByPayment `protobuf:"bytes,12,opt,name=refund_by_payment,json=refundByPayment,proto3,oneof"`
}

func (*PreviewRefundOrderParams_RefundByItem) isPreviewRefundOrderParams_RefundBy() {}

func (*PreviewRefundOrderParams_RefundByPayment) isPreviewRefundOrderParams_RefundBy() {}

// Preview refund order result.
type PreviewRefundOrderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order detail.
	OrderDetail *v1.OrderDetailView `protobuf:"bytes,1,opt,name=order_detail,json=orderDetail,proto3" json:"order_detail,omitempty"`
	// Preview refund order.
	RefundOrderPreview *v1.RefundOrderDetailView `protobuf:"bytes,2,opt,name=refund_order_preview,json=refundOrderPreview,proto3" json:"refund_order_preview,omitempty"`
	// Previewed related refund orders. On refunding deducted deposit, multiple refund orders may be created. The related
	// refund orders will be set to this field.
	RelatedRefundOrders []*PreviewRefundOrderResult_RelatedRefundOrder `protobuf:"bytes,8,rep,name=related_refund_orders,json=relatedRefundOrders,proto3" json:"related_refund_orders,omitempty"`
	// 与 Order item 一一对应，表示每一个 item 可以退的数量/金额.
	// 特别的，如果退款模式是 By Payment，这里不会有内容.
	RefundableItems []*v12.PreviewRefundOrderResponse_RefundableItem `protobuf:"bytes,3,rep,name=refundable_items,json=refundableItems,proto3" json:"refundable_items,omitempty"`
	// 可以退的 Tips.
	RefundableTips *money.Money `protobuf:"bytes,4,opt,name=refundable_tips,json=refundableTips,proto3" json:"refundable_tips,omitempty"`
	// Refund flags.
	RefundFlags *v12.PreviewRefundOrderResponse_RefundFlags `protobuf:"bytes,5,opt,name=refund_flags,json=refundFlags,proto3" json:"refund_flags,omitempty"`
	// Refundable payment.
	RefundableOrderPayments []*v12.PreviewRefundOrderResponse_RefundableOrderPayment `protobuf:"bytes,6,rep,name=refundable_order_payments,json=refundableOrderPayments,proto3" json:"refundable_order_payments,omitempty"`
	// 可以退的 ConvenienceFee
	RefundableConvenienceFee *money.Money `protobuf:"bytes,7,opt,name=refundable_convenience_fee,json=refundableConvenienceFee,proto3" json:"refundable_convenience_fee,omitempty"`
}

func (x *PreviewRefundOrderResult) Reset() {
	*x = PreviewRefundOrderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewRefundOrderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewRefundOrderResult) ProtoMessage() {}

func (x *PreviewRefundOrderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewRefundOrderResult.ProtoReflect.Descriptor instead.
func (*PreviewRefundOrderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{27}
}

func (x *PreviewRefundOrderResult) GetOrderDetail() *v1.OrderDetailView {
	if x != nil {
		return x.OrderDetail
	}
	return nil
}

func (x *PreviewRefundOrderResult) GetRefundOrderPreview() *v1.RefundOrderDetailView {
	if x != nil {
		return x.RefundOrderPreview
	}
	return nil
}

func (x *PreviewRefundOrderResult) GetRelatedRefundOrders() []*PreviewRefundOrderResult_RelatedRefundOrder {
	if x != nil {
		return x.RelatedRefundOrders
	}
	return nil
}

func (x *PreviewRefundOrderResult) GetRefundableItems() []*v12.PreviewRefundOrderResponse_RefundableItem {
	if x != nil {
		return x.RefundableItems
	}
	return nil
}

func (x *PreviewRefundOrderResult) GetRefundableTips() *money.Money {
	if x != nil {
		return x.RefundableTips
	}
	return nil
}

func (x *PreviewRefundOrderResult) GetRefundFlags() *v12.PreviewRefundOrderResponse_RefundFlags {
	if x != nil {
		return x.RefundFlags
	}
	return nil
}

func (x *PreviewRefundOrderResult) GetRefundableOrderPayments() []*v12.PreviewRefundOrderResponse_RefundableOrderPayment {
	if x != nil {
		return x.RefundableOrderPayments
	}
	return nil
}

func (x *PreviewRefundOrderResult) GetRefundableConvenienceFee() *money.Money {
	if x != nil {
		return x.RefundableConvenienceFee
	}
	return nil
}

// Preview refund order payments params.
// 纯计算接口.
type PreviewRefundOrderPaymentsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 需要退款的金额.
	RefundAmount *money.Money `protobuf:"bytes,1,opt,name=refund_amount,json=refundAmount,proto3" json:"refund_amount,omitempty"`
	// 需要退款的金额的标志.
	RefundAmountFlag *v12.RefundOrderRequest_RefundByPayment_RefundAmountFlags `protobuf:"bytes,2,opt,name=refund_amount_flag,json=refundAmountFlag,proto3" json:"refund_amount_flag,omitempty"`
	// 需要分摊退款金额的 Order Payments.
	// 这里不需要真实的 Order Payments, 只是复用结构.
	// 只需要金额相关的字段齐全即可.
	SourceOrderPayments []*v1.OrderPaymentModel `protobuf:"bytes,3,rep,name=source_order_payments,json=sourceOrderPayments,proto3" json:"source_order_payments,omitempty"`
}

func (x *PreviewRefundOrderPaymentsParams) Reset() {
	*x = PreviewRefundOrderPaymentsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewRefundOrderPaymentsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewRefundOrderPaymentsParams) ProtoMessage() {}

func (x *PreviewRefundOrderPaymentsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewRefundOrderPaymentsParams.ProtoReflect.Descriptor instead.
func (*PreviewRefundOrderPaymentsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{28}
}

func (x *PreviewRefundOrderPaymentsParams) GetRefundAmount() *money.Money {
	if x != nil {
		return x.RefundAmount
	}
	return nil
}

func (x *PreviewRefundOrderPaymentsParams) GetRefundAmountFlag() *v12.RefundOrderRequest_RefundByPayment_RefundAmountFlags {
	if x != nil {
		return x.RefundAmountFlag
	}
	return nil
}

func (x *PreviewRefundOrderPaymentsParams) GetSourceOrderPayments() []*v1.OrderPaymentModel {
	if x != nil {
		return x.SourceOrderPayments
	}
	return nil
}

// Preview refund order payments result.
type PreviewRefundOrderPaymentsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 总的退款金额.
	RefundTotalAmount *money.Money `protobuf:"bytes,1,opt,name=refund_total_amount,json=refundTotalAmount,proto3" json:"refund_total_amount,omitempty"`
	// 总的退款金额中 **包含** 的 Convenience Fee.
	RefundConvenienceFee *money.Money `protobuf:"bytes,2,opt,name=refund_convenience_fee,json=refundConvenienceFee,proto3" json:"refund_convenience_fee,omitempty"`
	// 各 Order Payment 的退款明细.
	RefundOrderPayments []*v1.RefundOrderPaymentModel `protobuf:"bytes,3,rep,name=refund_order_payments,json=refundOrderPayments,proto3" json:"refund_order_payments,omitempty"`
}

func (x *PreviewRefundOrderPaymentsResult) Reset() {
	*x = PreviewRefundOrderPaymentsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewRefundOrderPaymentsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewRefundOrderPaymentsResult) ProtoMessage() {}

func (x *PreviewRefundOrderPaymentsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewRefundOrderPaymentsResult.ProtoReflect.Descriptor instead.
func (*PreviewRefundOrderPaymentsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{29}
}

func (x *PreviewRefundOrderPaymentsResult) GetRefundTotalAmount() *money.Money {
	if x != nil {
		return x.RefundTotalAmount
	}
	return nil
}

func (x *PreviewRefundOrderPaymentsResult) GetRefundConvenienceFee() *money.Money {
	if x != nil {
		return x.RefundConvenienceFee
	}
	return nil
}

func (x *PreviewRefundOrderPaymentsResult) GetRefundOrderPayments() []*v1.RefundOrderPaymentModel {
	if x != nil {
		return x.RefundOrderPayments
	}
	return nil
}

// Refund order param.
type RefundOrderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order ID.
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// Refund mode.
	RefundMode v1.RefundMode `protobuf:"varint,2,opt,name=refund_mode,json=refundMode,proto3,enum=moego.models.order.v1.RefundMode" json:"refund_mode,omitempty"`
	// Refund reason.
	RefundReason string `protobuf:"bytes,3,opt,name=refund_reason,json=refundReason,proto3" json:"refund_reason,omitempty"`
	// 从哪 一/几 笔 Order Payment 中退款.
	SourceOrderPayments []*v12.RefundOrderRequest_OrderPayment `protobuf:"bytes,4,rep,name=source_order_payments,json=sourceOrderPayments,proto3" json:"source_order_payments,omitempty"`
	// Refund by.
	//
	// Types that are assignable to RefundBy:
	//
	//	*RefundOrderParams_RefundByItem
	//	*RefundOrderParams_RefundByPayment
	RefundBy isRefundOrderParams_RefundBy `protobuf_oneof:"refund_by"`
}

func (x *RefundOrderParams) Reset() {
	*x = RefundOrderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundOrderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundOrderParams) ProtoMessage() {}

func (x *RefundOrderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundOrderParams.ProtoReflect.Descriptor instead.
func (*RefundOrderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{30}
}

func (x *RefundOrderParams) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *RefundOrderParams) GetRefundMode() v1.RefundMode {
	if x != nil {
		return x.RefundMode
	}
	return v1.RefundMode(0)
}

func (x *RefundOrderParams) GetRefundReason() string {
	if x != nil {
		return x.RefundReason
	}
	return ""
}

func (x *RefundOrderParams) GetSourceOrderPayments() []*v12.RefundOrderRequest_OrderPayment {
	if x != nil {
		return x.SourceOrderPayments
	}
	return nil
}

func (m *RefundOrderParams) GetRefundBy() isRefundOrderParams_RefundBy {
	if m != nil {
		return m.RefundBy
	}
	return nil
}

func (x *RefundOrderParams) GetRefundByItem() *v12.RefundOrderRequest_RefundByItem {
	if x, ok := x.GetRefundBy().(*RefundOrderParams_RefundByItem); ok {
		return x.RefundByItem
	}
	return nil
}

func (x *RefundOrderParams) GetRefundByPayment() *v12.RefundOrderRequest_RefundByPayment {
	if x, ok := x.GetRefundBy().(*RefundOrderParams_RefundByPayment); ok {
		return x.RefundByPayment
	}
	return nil
}

type isRefundOrderParams_RefundBy interface {
	isRefundOrderParams_RefundBy()
}

type RefundOrderParams_RefundByItem struct {
	// Refund by item.
	RefundByItem *v12.RefundOrderRequest_RefundByItem `protobuf:"bytes,11,opt,name=refund_by_item,json=refundByItem,proto3,oneof"`
}

type RefundOrderParams_RefundByPayment struct {
	// Refund by payment.
	RefundByPayment *v12.RefundOrderRequest_RefundByPayment `protobuf:"bytes,12,opt,name=refund_by_payment,json=refundByPayment,proto3,oneof"`
}

func (*RefundOrderParams_RefundByItem) isRefundOrderParams_RefundBy() {}

func (*RefundOrderParams_RefundByPayment) isRefundOrderParams_RefundBy() {}

// Refund order result.
type RefundOrderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Refund order detail.
	RefundOrderDetail *v1.RefundOrderDetailView `protobuf:"bytes,1,opt,name=refund_order_detail,json=refundOrderDetail,proto3" json:"refund_order_detail,omitempty"`
	// On refunding deducted deposit, multiple refund orders may be created. The related refund orders will be set to this
	// field.
	RelatedRefundOrderDetails []*v1.RefundOrderDetailView `protobuf:"bytes,2,rep,name=related_refund_order_details,json=relatedRefundOrderDetails,proto3" json:"related_refund_order_details,omitempty"`
}

func (x *RefundOrderResult) Reset() {
	*x = RefundOrderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundOrderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundOrderResult) ProtoMessage() {}

func (x *RefundOrderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundOrderResult.ProtoReflect.Descriptor instead.
func (*RefundOrderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{31}
}

func (x *RefundOrderResult) GetRefundOrderDetail() *v1.RefundOrderDetailView {
	if x != nil {
		return x.RefundOrderDetail
	}
	return nil
}

func (x *RefundOrderResult) GetRelatedRefundOrderDetails() []*v1.RefundOrderDetailView {
	if x != nil {
		return x.RelatedRefundOrderDetails
	}
	return nil
}

// List order param.
type ListOrdersParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business ID.
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Origin order ID.
	// TODO(yunxiang): 引入订单组概念？
	OriginOrderId int64 `protobuf:"varint,2,opt,name=origin_order_id,json=originOrderId,proto3" json:"origin_order_id,omitempty"`
}

func (x *ListOrdersParams) Reset() {
	*x = ListOrdersParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrdersParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersParams) ProtoMessage() {}

func (x *ListOrdersParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersParams.ProtoReflect.Descriptor instead.
func (*ListOrdersParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{32}
}

func (x *ListOrdersParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListOrdersParams) GetOriginOrderId() int64 {
	if x != nil {
		return x.OriginOrderId
	}
	return 0
}

// List order result.
type ListOrdersResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Orders.
	Orders []*v1.OrderModelV1 `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	// Refund orders.
	RefundOrders []*v1.RefundOrderModel `protobuf:"bytes,2,rep,name=refund_orders,json=refundOrders,proto3" json:"refund_orders,omitempty"`
}

func (x *ListOrdersResult) Reset() {
	*x = ListOrdersResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrdersResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersResult) ProtoMessage() {}

func (x *ListOrdersResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersResult.ProtoReflect.Descriptor instead.
func (*ListOrdersResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{33}
}

func (x *ListOrdersResult) GetOrders() []*v1.OrderModelV1 {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ListOrdersResult) GetRefundOrders() []*v1.RefundOrderModel {
	if x != nil {
		return x.RefundOrders
	}
	return nil
}

// List order detail param.
type ListOrderDetailParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order ID.
	// 在 Invoice 三期实际含义已经调整为任意订单 ID， 都会返回关联的所有订单。
	OriginOrderId int64 `protobuf:"varint,1,opt,name=origin_order_id,json=originOrderId,proto3" json:"origin_order_id,omitempty"`
}

func (x *ListOrderDetailParam) Reset() {
	*x = ListOrderDetailParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrderDetailParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrderDetailParam) ProtoMessage() {}

func (x *ListOrderDetailParam) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrderDetailParam.ProtoReflect.Descriptor instead.
func (*ListOrderDetailParam) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{34}
}

func (x *ListOrderDetailParam) GetOriginOrderId() int64 {
	if x != nil {
		return x.OriginOrderId
	}
	return 0
}

// List order detail result.
type ListOrderDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Orders.
	Orders []*v1.OrderDetailView `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	// Refund orders.
	RefundOrders []*v1.RefundOrderDetailView `protobuf:"bytes,2,rep,name=refund_orders,json=refundOrders,proto3" json:"refund_orders,omitempty"`
}

func (x *ListOrderDetailResult) Reset() {
	*x = ListOrderDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOrderDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrderDetailResult) ProtoMessage() {}

func (x *ListOrderDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrderDetailResult.ProtoReflect.Descriptor instead.
func (*ListOrderDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{35}
}

func (x *ListOrderDetailResult) GetOrders() []*v1.OrderDetailView {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ListOrderDetailResult) GetRefundOrders() []*v1.RefundOrderDetailView {
	if x != nil {
		return x.RefundOrders
	}
	return nil
}

// Query order detail params.
type QueryOrderDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business ID，为 0 时表示不限制.
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// SourceType.
	// 传 0 表示不限制.
	SourceType v1.OrderSourceType `protobuf:"varint,2,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// Source ID.
	SourceId int64 `protobuf:"varint,3,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
}

func (x *QueryOrderDetailParams) Reset() {
	*x = QueryOrderDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryOrderDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryOrderDetailParams) ProtoMessage() {}

func (x *QueryOrderDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryOrderDetailParams.ProtoReflect.Descriptor instead.
func (*QueryOrderDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{36}
}

func (x *QueryOrderDetailParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *QueryOrderDetailParams) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

func (x *QueryOrderDetailParams) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

// Query order detail result.
type QueryOrderDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Orders.
	Orders []*v1.OrderDetailView `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	// Related refund orders.
	RefundOrders []*v1.RefundOrderDetailView `protobuf:"bytes,2,rep,name=refund_orders,json=refundOrders,proto3" json:"refund_orders,omitempty"`
}

func (x *QueryOrderDetailResult) Reset() {
	*x = QueryOrderDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryOrderDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryOrderDetailResult) ProtoMessage() {}

func (x *QueryOrderDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryOrderDetailResult.ProtoReflect.Descriptor instead.
func (*QueryOrderDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{37}
}

func (x *QueryOrderDetailResult) GetOrders() []*v1.OrderDetailView {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *QueryOrderDetailResult) GetRefundOrders() []*v1.RefundOrderDetailView {
	if x != nil {
		return x.RefundOrders
	}
	return nil
}

// Get order detail param.
type GetOrderDetailParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order ID.
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *GetOrderDetailParam) Reset() {
	*x = GetOrderDetailParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderDetailParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderDetailParam) ProtoMessage() {}

func (x *GetOrderDetailParam) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderDetailParam.ProtoReflect.Descriptor instead.
func (*GetOrderDetailParam) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{38}
}

func (x *GetOrderDetailParam) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

// Get order detail result.
type GetOrderDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order detail.
	Order *v1.OrderDetailView `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *GetOrderDetailResult) Reset() {
	*x = GetOrderDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderDetailResult) ProtoMessage() {}

func (x *GetOrderDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderDetailResult.ProtoReflect.Descriptor instead.
func (*GetOrderDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{39}
}

func (x *GetOrderDetailResult) GetOrder() *v1.OrderDetailView {
	if x != nil {
		return x.Order
	}
	return nil
}

// Get refund order detail param.
type GetRefundOrderDetailParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Refund order ID.
	RefundOrderId int64 `protobuf:"varint,1,opt,name=refund_order_id,json=refundOrderId,proto3" json:"refund_order_id,omitempty"`
}

func (x *GetRefundOrderDetailParam) Reset() {
	*x = GetRefundOrderDetailParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRefundOrderDetailParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRefundOrderDetailParam) ProtoMessage() {}

func (x *GetRefundOrderDetailParam) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRefundOrderDetailParam.ProtoReflect.Descriptor instead.
func (*GetRefundOrderDetailParam) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{40}
}

func (x *GetRefundOrderDetailParam) GetRefundOrderId() int64 {
	if x != nil {
		return x.RefundOrderId
	}
	return 0
}

// Get refund order detail result.
type GetRefundOrderDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order detail.
	RefundOrder *v1.RefundOrderDetailView `protobuf:"bytes,1,opt,name=refund_order,json=refundOrder,proto3" json:"refund_order,omitempty"`
}

func (x *GetRefundOrderDetailResult) Reset() {
	*x = GetRefundOrderDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRefundOrderDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRefundOrderDetailResult) ProtoMessage() {}

func (x *GetRefundOrderDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRefundOrderDetailResult.ProtoReflect.Descriptor instead.
func (*GetRefundOrderDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{41}
}

func (x *GetRefundOrderDetailResult) GetRefundOrder() *v1.RefundOrderDetailView {
	if x != nil {
		return x.RefundOrder
	}
	return nil
}

// Create extra tip order params.
type CreateTipOrderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business ID.
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Customer ID.
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// Source type.
	SourceType v1.OrderSourceType `protobuf:"varint,11,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// Source ID.
	SourceId int64 `protobuf:"varint,12,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// Description.
	Description string `protobuf:"bytes,13,opt,name=description,proto3" json:"description,omitempty"`
	// Tip amount. This amount should be greater than ZERO.
	TipAmount *money.Money `protobuf:"bytes,14,opt,name=tip_amount,json=tipAmount,proto3" json:"tip_amount,omitempty"`
	// tips based amount
	TipBasedAmount *money.Money `protobuf:"bytes,15,opt,name=tip_based_amount,json=tipBasedAmount,proto3" json:"tip_based_amount,omitempty"`
}

func (x *CreateTipOrderParams) Reset() {
	*x = CreateTipOrderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTipOrderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTipOrderParams) ProtoMessage() {}

func (x *CreateTipOrderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTipOrderParams.ProtoReflect.Descriptor instead.
func (*CreateTipOrderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{42}
}

func (x *CreateTipOrderParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateTipOrderParams) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateTipOrderParams) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

func (x *CreateTipOrderParams) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *CreateTipOrderParams) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateTipOrderParams) GetTipAmount() *money.Money {
	if x != nil {
		return x.TipAmount
	}
	return nil
}

func (x *CreateTipOrderParams) GetTipBasedAmount() *money.Money {
	if x != nil {
		return x.TipBasedAmount
	}
	return nil
}

// Create extra tip order result.
type CreateTipOrderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order.
	Order *v1.OrderDetailView `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *CreateTipOrderResult) Reset() {
	*x = CreateTipOrderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTipOrderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTipOrderResult) ProtoMessage() {}

func (x *CreateTipOrderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTipOrderResult.ProtoReflect.Descriptor instead.
func (*CreateTipOrderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{43}
}

func (x *CreateTipOrderResult) GetOrder() *v1.OrderDetailView {
	if x != nil {
		return x.Order
	}
	return nil
}

// Create deposit order
type CreateDepositOrderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business ID.
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Staff ID.
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// Customer ID.
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// Source type.
	SourceType v1.OrderSourceType `protobuf:"varint,11,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// Source ID.
	SourceId int64 `protobuf:"varint,12,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// Deposit amount.
	DepositAmount *money.Money `protobuf:"bytes,13,opt,name=deposit_amount,json=depositAmount,proto3" json:"deposit_amount,omitempty"`
	// Description (like "By percentage, $250.00*30%").
	DepositDescription string `protobuf:"bytes,14,opt,name=deposit_description,json=depositDescription,proto3" json:"deposit_description,omitempty"`
}

func (x *CreateDepositOrderParams) Reset() {
	*x = CreateDepositOrderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDepositOrderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDepositOrderParams) ProtoMessage() {}

func (x *CreateDepositOrderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDepositOrderParams.ProtoReflect.Descriptor instead.
func (*CreateDepositOrderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{44}
}

func (x *CreateDepositOrderParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateDepositOrderParams) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateDepositOrderParams) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateDepositOrderParams) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

func (x *CreateDepositOrderParams) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *CreateDepositOrderParams) GetDepositAmount() *money.Money {
	if x != nil {
		return x.DepositAmount
	}
	return nil
}

func (x *CreateDepositOrderParams) GetDepositDescription() string {
	if x != nil {
		return x.DepositDescription
	}
	return ""
}

// Create deposit order result.
type CreateDepositOrderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order.
	Order *v1.OrderDetailView `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *CreateDepositOrderResult) Reset() {
	*x = CreateDepositOrderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDepositOrderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDepositOrderResult) ProtoMessage() {}

func (x *CreateDepositOrderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDepositOrderResult.ProtoReflect.Descriptor instead.
func (*CreateDepositOrderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{45}
}

func (x *CreateDepositOrderResult) GetOrder() *v1.OrderDetailView {
	if x != nil {
		return x.Order
	}
	return nil
}

// 临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
type SendInvoiceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 收件人.
	TargetEmailAddress string `protobuf:"bytes,1,opt,name=target_email_address,json=targetEmailAddress,proto3" json:"target_email_address,omitempty"`
	// Estimate image URL.
	EstimateImageUrl string `protobuf:"bytes,2,opt,name=estimate_image_url,json=estimateImageUrl,proto3" json:"estimate_image_url,omitempty"`
	// Params.
	//
	// Types that are assignable to EmailParams:
	//
	//	*SendInvoiceParams_Estimate_
	//	*SendInvoiceParams_Receipt_
	EmailParams isSendInvoiceParams_EmailParams `protobuf_oneof:"email_params"`
}

func (x *SendInvoiceParams) Reset() {
	*x = SendInvoiceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendInvoiceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendInvoiceParams) ProtoMessage() {}

func (x *SendInvoiceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendInvoiceParams.ProtoReflect.Descriptor instead.
func (*SendInvoiceParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{46}
}

func (x *SendInvoiceParams) GetTargetEmailAddress() string {
	if x != nil {
		return x.TargetEmailAddress
	}
	return ""
}

func (x *SendInvoiceParams) GetEstimateImageUrl() string {
	if x != nil {
		return x.EstimateImageUrl
	}
	return ""
}

func (m *SendInvoiceParams) GetEmailParams() isSendInvoiceParams_EmailParams {
	if m != nil {
		return m.EmailParams
	}
	return nil
}

func (x *SendInvoiceParams) GetEstimate() *SendInvoiceParams_Estimate {
	if x, ok := x.GetEmailParams().(*SendInvoiceParams_Estimate_); ok {
		return x.Estimate
	}
	return nil
}

func (x *SendInvoiceParams) GetReceipt() *SendInvoiceParams_Receipt {
	if x, ok := x.GetEmailParams().(*SendInvoiceParams_Receipt_); ok {
		return x.Receipt
	}
	return nil
}

type isSendInvoiceParams_EmailParams interface {
	isSendInvoiceParams_EmailParams()
}

type SendInvoiceParams_Estimate_ struct {
	// Estimate.
	Estimate *SendInvoiceParams_Estimate `protobuf:"bytes,3,opt,name=estimate,proto3,oneof"`
}

type SendInvoiceParams_Receipt_ struct {
	// Receipt.
	Receipt *SendInvoiceParams_Receipt `protobuf:"bytes,4,opt,name=receipt,proto3,oneof"`
}

func (*SendInvoiceParams_Estimate_) isSendInvoiceParams_EmailParams() {}

func (*SendInvoiceParams_Receipt_) isSendInvoiceParams_EmailParams() {}

// 临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
type SendInvoiceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendInvoiceResult) Reset() {
	*x = SendInvoiceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendInvoiceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendInvoiceResult) ProtoMessage() {}

func (x *SendInvoiceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendInvoiceResult.ProtoReflect.Descriptor instead.
func (*SendInvoiceResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{47}
}

// 临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
type GetEstimateParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Source type.
	SourceType v1.OrderSourceType `protobuf:"varint,1,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// Source
	SourceId int64 `protobuf:"varint,2,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
}

func (x *GetEstimateParams) Reset() {
	*x = GetEstimateParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEstimateParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEstimateParams) ProtoMessage() {}

func (x *GetEstimateParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEstimateParams.ProtoReflect.Descriptor instead.
func (*GetEstimateParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{48}
}

func (x *GetEstimateParams) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

func (x *GetEstimateParams) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

// 临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
type GetEstimateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Estimate.
	Estimate *v1.OrderDetailView `protobuf:"bytes,1,opt,name=estimate,proto3" json:"estimate,omitempty"`
	// Appointment brief.
	Appointment *GetInvoiceResult_AppointmentBrief `protobuf:"bytes,2,opt,name=appointment,proto3" json:"appointment,omitempty"`
	// Customer brief.
	Customer *GetInvoiceResult_CustomerBrief `protobuf:"bytes,3,opt,name=customer,proto3" json:"customer,omitempty"`
	// Order items' extra info
	OrderItemExtras []*GetInvoiceResult_OrderItemExtra `protobuf:"bytes,4,rep,name=order_item_extras,json=orderItemExtras,proto3" json:"order_item_extras,omitempty"`
	// Deposit. 需要在 CheckIn 之前能看到 deposit.
	Deposit *v1.OrderDetailView `protobuf:"bytes,5,opt,name=deposit,proto3" json:"deposit,omitempty"`
}

func (x *GetEstimateResult) Reset() {
	*x = GetEstimateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEstimateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEstimateResult) ProtoMessage() {}

func (x *GetEstimateResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEstimateResult.ProtoReflect.Descriptor instead.
func (*GetEstimateResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{49}
}

func (x *GetEstimateResult) GetEstimate() *v1.OrderDetailView {
	if x != nil {
		return x.Estimate
	}
	return nil
}

func (x *GetEstimateResult) GetAppointment() *GetInvoiceResult_AppointmentBrief {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *GetEstimateResult) GetCustomer() *GetInvoiceResult_CustomerBrief {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *GetEstimateResult) GetOrderItemExtras() []*GetInvoiceResult_OrderItemExtra {
	if x != nil {
		return x.OrderItemExtras
	}
	return nil
}

func (x *GetEstimateResult) GetDeposit() *v1.OrderDetailView {
	if x != nil {
		return x.Deposit
	}
	return nil
}

// 临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
type GetInvoiceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// invoice id.
	InvoiceId int64 `protobuf:"varint,1,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
}

func (x *GetInvoiceParams) Reset() {
	*x = GetInvoiceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvoiceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvoiceParams) ProtoMessage() {}

func (x *GetInvoiceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvoiceParams.ProtoReflect.Descriptor instead.
func (*GetInvoiceParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{50}
}

func (x *GetInvoiceParams) GetInvoiceId() int64 {
	if x != nil {
		return x.InvoiceId
	}
	return 0
}

// 临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
type GetInvoiceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// invoice.
	Invoice *v1.OrderDetailView `protobuf:"bytes,1,opt,name=invoice,proto3" json:"invoice,omitempty"`
	// Appointment brief.
	Appointment *GetInvoiceResult_AppointmentBrief `protobuf:"bytes,2,opt,name=appointment,proto3" json:"appointment,omitempty"`
	// Customer brief.
	Customer *GetInvoiceResult_CustomerBrief `protobuf:"bytes,3,opt,name=customer,proto3" json:"customer,omitempty"`
	// Order items' extra info
	OrderItemExtras []*GetInvoiceResult_OrderItemExtra `protobuf:"bytes,4,rep,name=order_item_extras,json=orderItemExtras,proto3" json:"order_item_extras,omitempty"`
	// Invoice status.
	InvoiceStatus GetInvoiceResult_InvoiceStatus `protobuf:"varint,5,opt,name=invoice_status,json=invoiceStatus,proto3,enum=moego.api.order.v1.GetInvoiceResult_InvoiceStatus" json:"invoice_status,omitempty"`
	// Deposit.
	Deposit *v1.OrderDetailView `protobuf:"bytes,6,opt,name=deposit,proto3" json:"deposit,omitempty"`
}

func (x *GetInvoiceResult) Reset() {
	*x = GetInvoiceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvoiceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvoiceResult) ProtoMessage() {}

func (x *GetInvoiceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvoiceResult.ProtoReflect.Descriptor instead.
func (*GetInvoiceResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{51}
}

func (x *GetInvoiceResult) GetInvoice() *v1.OrderDetailView {
	if x != nil {
		return x.Invoice
	}
	return nil
}

func (x *GetInvoiceResult) GetAppointment() *GetInvoiceResult_AppointmentBrief {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *GetInvoiceResult) GetCustomer() *GetInvoiceResult_CustomerBrief {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *GetInvoiceResult) GetOrderItemExtras() []*GetInvoiceResult_OrderItemExtra {
	if x != nil {
		return x.OrderItemExtras
	}
	return nil
}

func (x *GetInvoiceResult) GetInvoiceStatus() GetInvoiceResult_InvoiceStatus {
	if x != nil {
		return x.InvoiceStatus
	}
	return GetInvoiceResult_INVOICE_STATUS_UNSPECIFIED
}

func (x *GetInvoiceResult) GetDeposit() *v1.OrderDetailView {
	if x != nil {
		return x.Deposit
	}
	return nil
}

// Cancel order params.
type CancelOrderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Business ID.
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Staff ID.
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// Order ID.
	OrderId int64 `protobuf:"varint,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *CancelOrderParams) Reset() {
	*x = CancelOrderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelOrderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelOrderParams) ProtoMessage() {}

func (x *CancelOrderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelOrderParams.ProtoReflect.Descriptor instead.
func (*CancelOrderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{52}
}

func (x *CancelOrderParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CancelOrderParams) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CancelOrderParams) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

// Cancel order result.
type CancelOrderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CancelOrderResult) Reset() {
	*x = CancelOrderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelOrderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelOrderResult) ProtoMessage() {}

func (x *CancelOrderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelOrderResult.ProtoReflect.Descriptor instead.
func (*CancelOrderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{53}
}

// Related refund orders for this refund order.
type PreviewRefundOrderResult_RelatedRefundOrder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order detail.
	OrderDetail *v1.OrderDetailView `protobuf:"bytes,1,opt,name=order_detail,json=orderDetail,proto3" json:"order_detail,omitempty"`
	// Preview refund order.
	RefundOrderPreview *v1.RefundOrderDetailView `protobuf:"bytes,2,opt,name=refund_order_preview,json=refundOrderPreview,proto3" json:"refund_order_preview,omitempty"`
}

func (x *PreviewRefundOrderResult_RelatedRefundOrder) Reset() {
	*x = PreviewRefundOrderResult_RelatedRefundOrder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewRefundOrderResult_RelatedRefundOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewRefundOrderResult_RelatedRefundOrder) ProtoMessage() {}

func (x *PreviewRefundOrderResult_RelatedRefundOrder) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewRefundOrderResult_RelatedRefundOrder.ProtoReflect.Descriptor instead.
func (*PreviewRefundOrderResult_RelatedRefundOrder) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{27, 0}
}

func (x *PreviewRefundOrderResult_RelatedRefundOrder) GetOrderDetail() *v1.OrderDetailView {
	if x != nil {
		return x.OrderDetail
	}
	return nil
}

func (x *PreviewRefundOrderResult_RelatedRefundOrder) GetRefundOrderPreview() *v1.RefundOrderDetailView {
	if x != nil {
		return x.RefundOrderPreview
	}
	return nil
}

// Invoice receipt.
type SendInvoiceParams_Receipt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Invoice id.
	InvoiceId int64 `protobuf:"varint,1,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
}

func (x *SendInvoiceParams_Receipt) Reset() {
	*x = SendInvoiceParams_Receipt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendInvoiceParams_Receipt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendInvoiceParams_Receipt) ProtoMessage() {}

func (x *SendInvoiceParams_Receipt) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendInvoiceParams_Receipt.ProtoReflect.Descriptor instead.
func (*SendInvoiceParams_Receipt) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{46, 0}
}

func (x *SendInvoiceParams_Receipt) GetInvoiceId() int64 {
	if x != nil {
		return x.InvoiceId
	}
	return 0
}

// Estimate email.
type SendInvoiceParams_Estimate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Source type.
	SourceType v1.OrderSourceType `protobuf:"varint,1,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// Source
	SourceId int64 `protobuf:"varint,2,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
}

func (x *SendInvoiceParams_Estimate) Reset() {
	*x = SendInvoiceParams_Estimate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendInvoiceParams_Estimate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendInvoiceParams_Estimate) ProtoMessage() {}

func (x *SendInvoiceParams_Estimate) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendInvoiceParams_Estimate.ProtoReflect.Descriptor instead.
func (*SendInvoiceParams_Estimate) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{46, 1}
}

func (x *SendInvoiceParams_Estimate) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

func (x *SendInvoiceParams_Estimate) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

// Extra fields for each order item.
type GetInvoiceResult_OrderItemExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order item ID.
	OrderItemId int64 `protobuf:"varint,1,opt,name=order_item_id,json=orderItemId,proto3" json:"order_item_id,omitempty"`
	// Order ID.
	OrderId int64 `protobuf:"varint,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// A flag to indicate the item paid or not.
	IsPaid bool `protobuf:"varint,3,opt,name=is_paid,json=isPaid,proto3" json:"is_paid,omitempty"`
	// Service item type.
	ServiceItemType v13.ServiceItemType `protobuf:"varint,4,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// Service price unit.
	ServicePriceUnit v13.ServicePriceUnit `protobuf:"varint,5,opt,name=service_price_unit,json=servicePriceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"service_price_unit,omitempty"`
}

func (x *GetInvoiceResult_OrderItemExtra) Reset() {
	*x = GetInvoiceResult_OrderItemExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvoiceResult_OrderItemExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvoiceResult_OrderItemExtra) ProtoMessage() {}

func (x *GetInvoiceResult_OrderItemExtra) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvoiceResult_OrderItemExtra.ProtoReflect.Descriptor instead.
func (*GetInvoiceResult_OrderItemExtra) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{51, 0}
}

func (x *GetInvoiceResult_OrderItemExtra) GetOrderItemId() int64 {
	if x != nil {
		return x.OrderItemId
	}
	return 0
}

func (x *GetInvoiceResult_OrderItemExtra) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *GetInvoiceResult_OrderItemExtra) GetIsPaid() bool {
	if x != nil {
		return x.IsPaid
	}
	return false
}

func (x *GetInvoiceResult_OrderItemExtra) GetServiceItemType() v13.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v13.ServiceItemType(0)
}

func (x *GetInvoiceResult_OrderItemExtra) GetServicePriceUnit() v13.ServicePriceUnit {
	if x != nil {
		return x.ServicePriceUnit
	}
	return v13.ServicePriceUnit(0)
}

// Appointment Brief.
type GetInvoiceResult_AppointmentBrief struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// appointment status.
	Status v11.AppointmentStatus `protobuf:"varint,2,opt,name=status,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"status,omitempty"`
	// appointment date.
	AppointmentDate string `protobuf:"bytes,3,opt,name=appointment_date,json=appointmentDate,proto3" json:"appointment_date,omitempty"`
	// appointment end date.
	AppointmentEndDate string `protobuf:"bytes,4,opt,name=appointment_end_date,json=appointmentEndDate,proto3" json:"appointment_end_date,omitempty"`
	// appointment start time.
	AppointmentStartTime int32 `protobuf:"varint,5,opt,name=appointment_start_time,json=appointmentStartTime,proto3" json:"appointment_start_time,omitempty"`
	// appointment end time.
	AppointmentEndTime int32 `protobuf:"varint,6,opt,name=appointment_end_time,json=appointmentEndTime,proto3" json:"appointment_end_time,omitempty"`
	// appointment create time.
	CreateTime int64 `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
}

func (x *GetInvoiceResult_AppointmentBrief) Reset() {
	*x = GetInvoiceResult_AppointmentBrief{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvoiceResult_AppointmentBrief) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvoiceResult_AppointmentBrief) ProtoMessage() {}

func (x *GetInvoiceResult_AppointmentBrief) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvoiceResult_AppointmentBrief.ProtoReflect.Descriptor instead.
func (*GetInvoiceResult_AppointmentBrief) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{51, 1}
}

func (x *GetInvoiceResult_AppointmentBrief) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetInvoiceResult_AppointmentBrief) GetStatus() v11.AppointmentStatus {
	if x != nil {
		return x.Status
	}
	return v11.AppointmentStatus(0)
}

func (x *GetInvoiceResult_AppointmentBrief) GetAppointmentDate() string {
	if x != nil {
		return x.AppointmentDate
	}
	return ""
}

func (x *GetInvoiceResult_AppointmentBrief) GetAppointmentEndDate() string {
	if x != nil {
		return x.AppointmentEndDate
	}
	return ""
}

func (x *GetInvoiceResult_AppointmentBrief) GetAppointmentStartTime() int32 {
	if x != nil {
		return x.AppointmentStartTime
	}
	return 0
}

func (x *GetInvoiceResult_AppointmentBrief) GetAppointmentEndTime() int32 {
	if x != nil {
		return x.AppointmentEndTime
	}
	return 0
}

func (x *GetInvoiceResult_AppointmentBrief) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

// Customer Brief.
type GetInvoiceResult_CustomerBrief struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Customer ID.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// First name.
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// Last name.
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// EMail.
	Email string `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	// Phone number.
	PhoneNumber string `protobuf:"bytes,5,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *GetInvoiceResult_CustomerBrief) Reset() {
	*x = GetInvoiceResult_CustomerBrief{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_order_api_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvoiceResult_CustomerBrief) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvoiceResult_CustomerBrief) ProtoMessage() {}

func (x *GetInvoiceResult_CustomerBrief) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_order_api_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvoiceResult_CustomerBrief.ProtoReflect.Descriptor instead.
func (*GetInvoiceResult_CustomerBrief) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_order_api_proto_rawDescGZIP(), []int{51, 2}
}

func (x *GetInvoiceResult_CustomerBrief) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetInvoiceResult_CustomerBrief) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *GetInvoiceResult_CustomerBrief) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *GetInvoiceResult_CustomerBrief) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetInvoiceResult_CustomerBrief) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

var File_moego_api_order_v1_order_api_proto protoreflect.FileDescriptor

var file_moego_api_order_v1_order_api_proto_rawDesc = []byte{
	0x0a, 0x22, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x64,
	0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xb8, 0x01, 0x0a, 0x1f, 0x41, 0x64, 0x64, 0x4f, 0x72, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x49, 0x64, 0x12, 0x26, 0x0a, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0b, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x22, 0x92, 0x01, 0x0a, 0x23,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x54, 0x6f, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x53, 0x0a, 0x0e, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x22, 0xec, 0x01, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x54, 0x69, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x6d, 0x69, 0x74, 0x5f,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6f, 0x6d,
	0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0b,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0f,
	0x0a, 0x0d, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x22,
	0x7e, 0x0a, 0x0f, 0x53, 0x65, 0x74, 0x54, 0x69, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x53, 0x0a, 0x0e, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22,
	0xd6, 0x01, 0x0a, 0x14, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x61,
	0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x09,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x08, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1e, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x22, 0x9c, 0x01, 0x0a, 0x15, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x58, 0x0a, 0x0e, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x00, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0xe7, 0x01, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x26, 0x0a, 0x0c, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00,
	0x52, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x88, 0x01, 0x01,
	0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x22, 0x2d, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x48, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2f, 0x0a, 0x0f, 0x6f, 0x72, 0x69,
	0x67, 0x69, 0x6e, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x6f, 0x72, 0x69,
	0x67, 0x69, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x64, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x4b, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x76, 0x69, 0x65,
	0x77, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x56, 0x69, 0x65, 0x77, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77,
	0x22, 0x39, 0x0a, 0x13, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x2d, 0x0a, 0x13, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x9f, 0x01, 0x0a, 0x16, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2f, 0x0a, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x13, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x40, 0x48, 0x00, 0x52, 0x11,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x47, 0x0a, 0x16,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2d, 0x0a, 0x0e, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0xfb, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x2d, 0x0a, 0x0e, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x4d, 0x0a, 0x0a, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x48, 0x00,
	0x52, 0x09, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x3c,
	0x0a, 0x13, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x18, 0x40, 0x48, 0x01, 0x52, 0x11, 0x65, 0x78, 0x74, 0x72, 0x61, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x16, 0x0a, 0x14, 0x5f,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x22, 0x30, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xbe, 0x01, 0x0a, 0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48,
	0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x38, 0x0a,
	0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x48, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x69, 0x64, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x22, 0x3b, 0x0a, 0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x7f, 0x0a, 0x21, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x69, 0x6e,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2d, 0x0a, 0x0e, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x49, 0x64, 0x22, 0x3b, 0x0a, 0x21, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x69,
	0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0xae, 0x01, 0x0a, 0x19, 0x45, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43,
	0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x22, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x6d, 0x0a, 0x1b, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x18, 0x65, 0x64, 0x69, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x22, 0x1b, 0x0a, 0x19, 0x45, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43,
	0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x1e, 0x0a, 0x1c, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22,
	0x1e, 0x0a, 0x1c, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x1c, 0x0a, 0x1a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x72, 0x0a,
	0x1a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x69,
	0x6e, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x54, 0x0a, 0x28, 0x69,
	0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x76,
	0x6f, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x77, 0x68,
	0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x22, 0x69,
	0x73, 0x41, 0x6c, 0x6c, 0x42, 0x69, 0x7a, 0x49, 0x6e, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0xd8, 0x03, 0x0a, 0x18, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x22,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x4e, 0x0a, 0x0b, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0a, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x6b, 0x0a, 0x15, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x13, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x5f, 0x0a, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x49, 0x74, 0x65, 0x6d,
	0x48, 0x00, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x68, 0x0a, 0x11, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x42, 0x79,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0f, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x42, 0x79, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x10, 0x0a, 0x09, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x5f, 0x62, 0x79, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xe4, 0x07, 0x0a,
	0x18, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x49, 0x0a, 0x0c, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x5e, 0x0a, 0x14, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x12, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x12, 0x73, 0x0a, 0x15, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x13, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x6c, 0x0a, 0x10, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62,
	0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62,
	0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x3b, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65,
	0x54, 0x69, 0x70, 0x73, 0x12, 0x61, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x66,
	0x6c, 0x61, 0x67, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x85, 0x01, 0x0a, 0x19, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x17, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62,
	0x6c, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x50, 0x0a, 0x1a, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x18, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x61,
	0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65,
	0x65, 0x1a, 0xbf, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x0c, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x5e, 0x0a, 0x14, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x12, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x22, 0xb5, 0x02, 0x0a, 0x20, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x37, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x7a, 0x0a, 0x12, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x42, 0x79, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x52, 0x10, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x5c, 0x0a,
	0x15, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x13, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x94, 0x02, 0x0a, 0x20,
	0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x42, 0x0a, 0x13, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x11, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x48, 0x0a, 0x16, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x63,
	0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x12, 0x62,
	0x0a, 0x15, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x13, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x22, 0x80, 0x04, 0x0a, 0x11, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x0b,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4d, 0x6f, 0x64, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x52, 0x0a, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x75, 0x0a, 0x15, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01,
	0x02, 0x08, 0x01, 0x52, 0x13, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x5f, 0x0a, 0x0e, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x42, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x00, 0x52, 0x0c, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x42, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x68, 0x0a, 0x11, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x48, 0x00, 0x52, 0x0f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x42, 0x79, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x42, 0x10, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x62, 0x79,
	0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xe0, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5c, 0x0a, 0x13, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x11, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x6d, 0x0a, 0x1c, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x19, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x6d, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x9d, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3b, 0x0a, 0x06,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56,
	0x31, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x4c, 0x0a, 0x0d, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x22, 0x47, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12,
	0x2f, 0x0a, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0d, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64,
	0x22, 0xaa, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3e, 0x0a, 0x06, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x51, 0x0a, 0x0d, 0x72, 0x65,
	0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x22, 0xbb, 0x01,
	0x0a, 0x16, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x51, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x22, 0xab, 0x01, 0x0a, 0x16,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3e, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x06,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x51, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x22, 0x39, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x12, 0x22, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x22, 0x54, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x05,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x4c, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x2f, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x6d, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4f, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x22, 0xf8, 0x02, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x70, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0a, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x31, 0x0a, 0x0a, 0x74, 0x69, 0x70, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x74, 0x69, 0x70, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x10, 0x74, 0x69, 0x70, 0x5f, 0x62, 0x61, 0x73, 0x65,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0e, 0x74, 0x69, 0x70, 0x42, 0x61, 0x73, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x54, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x70, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x05, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0xf9, 0x02, 0x0a, 0x18, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x53, 0x0a,
	0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x13, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x58, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x3c, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0xf0,
	0x03, 0x0a, 0x11, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x3b, 0x0a, 0x14, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x60, 0x01, 0x52, 0x12, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x38, 0x0a, 0x12, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x88, 0x01, 0x01, 0x52, 0x10, 0x65, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x4c, 0x0a, 0x08, 0x65,
	0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x2e, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52,
	0x08, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x12, 0x49, 0x0a, 0x07, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x70, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x48, 0x00, 0x52, 0x07, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x70, 0x74, 0x1a, 0x31, 0x0a, 0x07, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x12,
	0x26, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x69, 0x6e,
	0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x64, 0x1a, 0x87, 0x01, 0x0a, 0x08, 0x45, 0x73, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x65, 0x12, 0x55, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x82, 0x01, 0x06, 0x10, 0x01, 0x20, 0x00, 0x20, 0x01, 0x52,
	0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x22, 0x13, 0x0a, 0x11, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x90, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x45, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x55, 0x0a, 0x0b,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x82, 0x01,
	0x06, 0x10, 0x01, 0x20, 0x00, 0x20, 0x01, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x22, 0xa3, 0x03, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x42, 0x0a, 0x08, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x65, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x65, 0x12, 0x57, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x72, 0x69, 0x65, 0x66, 0x52,
	0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x08,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x42, 0x72, 0x69,
	0x65, 0x66, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x5f, 0x0a, 0x11,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52, 0x0f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x45, 0x78, 0x74, 0x72, 0x61, 0x73, 0x12, 0x40, 0x0a,
	0x07, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x22,
	0x31, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x22, 0xd1, 0x0a, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a, 0x07, 0x69, 0x6e, 0x76, 0x6f, 0x69,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x07, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x57, 0x0a, 0x0b, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x42, 0x72, 0x69, 0x65, 0x66, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76,
	0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x42, 0x72, 0x69, 0x65, 0x66, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x12, 0x5f, 0x0a, 0x11, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x52, 0x0f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x73, 0x12, 0x59, 0x0a, 0x0e, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x2e, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x0d, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40,
	0x0a, 0x07, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x1a, 0x99, 0x02, 0x0a, 0x0e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x45, 0x78,
	0x74, 0x72, 0x61, 0x12, 0x22, 0x0a, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x50, 0x61, 0x69, 0x64, 0x12, 0x55, 0x0a, 0x11, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x58, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x1a, 0xd0, 0x02, 0x0a,
	0x10, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x72, 0x69, 0x65,
	0x66, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45,
	0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a,
	0x94, 0x01, 0x0a, 0x0d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x42, 0x72, 0x69, 0x65,
	0x66, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x4e, 0x0a, 0x0d, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x49, 0x4e, 0x56, 0x4f, 0x49,
	0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4f, 0x50, 0x45, 0x4e, 0x10,
	0x01, 0x12, 0x09, 0x0a, 0x05, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04,
	0x56, 0x4f, 0x49, 0x44, 0x10, 0x03, 0x22, 0x6a, 0x0a, 0x11, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x49, 0x64, 0x22, 0x13, 0x0a, 0x11, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0x99, 0x18, 0x0a, 0x0c, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x64, 0x0a, 0x0d, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x78, 0x12, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x61, 0x78, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x61, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87,
	0x01, 0x0a, 0x17, 0x41, 0x64, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x54, 0x6f, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x64, 0x64, 0x4f, 0x72, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x6f, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c, 0x01, 0x0a, 0x1c, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x46, 0x72, 0x6f, 0x6d, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x64, 0x64, 0x4f, 0x72, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x6f, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a, 0x07, 0x53, 0x65, 0x74, 0x54, 0x69,
	0x70, 0x73, 0x12, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x54, 0x69, 0x70, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x54,
	0x69, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x0b, 0x53,
	0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x61, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x27,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6a, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x2a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x6a, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x8b, 0x01, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8b, 0x01,
	0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x46, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x46, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x73, 0x0a, 0x13, 0x45,
	0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x7c, 0x0a, 0x16, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x76,
	0x0a, 0x14, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x70, 0x0a, 0x12, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x88, 0x01, 0x0a, 0x1a, 0x50, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x5b, 0x0a, 0x0b, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x12, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x58, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x66, 0x0a, 0x0f, 0x4c, 0x69,
	0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x28, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x6a, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x63,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x1a, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x75, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x64, 0x0a, 0x0e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x70, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x70, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x70, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x70, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x5b, 0x0a, 0x0b, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x5b, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x12, 0x25,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x58, 0x0a, 0x0a,
	0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5b, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e,
	0x76, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x49,
	0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x25, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x42, 0x72, 0x0a, 0x1a, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_order_v1_order_api_proto_rawDescOnce sync.Once
	file_moego_api_order_v1_order_api_proto_rawDescData = file_moego_api_order_v1_order_api_proto_rawDesc
)

func file_moego_api_order_v1_order_api_proto_rawDescGZIP() []byte {
	file_moego_api_order_v1_order_api_proto_rawDescOnce.Do(func() {
		file_moego_api_order_v1_order_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_order_v1_order_api_proto_rawDescData)
	})
	return file_moego_api_order_v1_order_api_proto_rawDescData
}

var file_moego_api_order_v1_order_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_api_order_v1_order_api_proto_msgTypes = make([]protoimpl.MessageInfo, 60)
var file_moego_api_order_v1_order_api_proto_goTypes = []interface{}{
	(GetInvoiceResult_InvoiceStatus)(0),                              // 0: moego.api.order.v1.GetInvoiceResult.InvoiceStatus
	(*AddOrRemoveServiceChargeRequest)(nil),                          // 1: moego.api.order.v1.AddOrRemoveServiceChargeRequest
	(*OperateServiceChargeToOrderResponse)(nil),                      // 2: moego.api.order.v1.OperateServiceChargeToOrderResponse
	(*SetTipsRequest)(nil),                                           // 3: moego.api.order.v1.SetTipsRequest
	(*SetTipsResponse)(nil),                                          // 4: moego.api.order.v1.SetTipsResponse
	(*ModifyItemTaxRequest)(nil),                                     // 5: moego.api.order.v1.ModifyItemTaxRequest
	(*ModifyItemTaxResponse)(nil),                                    // 6: moego.api.order.v1.ModifyItemTaxResponse
	(*SetDiscountRequest)(nil),                                       // 7: moego.api.order.v1.SetDiscountRequest
	(*SetDiscountResponse)(nil),                                      // 8: moego.api.order.v1.SetDiscountResponse
	(*GetOrderHistoryParams)(nil),                                    // 9: moego.api.order.v1.GetOrderHistoryParams
	(*GetOrderHistoryResult)(nil),                                    // 10: moego.api.order.v1.GetOrderHistoryResult
	(*CompleteOrderParams)(nil),                                      // 11: moego.api.order.v1.CompleteOrderParams
	(*CompleteOrderResult)(nil),                                      // 12: moego.api.order.v1.CompleteOrderResult
	(*CreateExtraOrderParams)(nil),                                   // 13: moego.api.order.v1.CreateExtraOrderParams
	(*CreateExtraOrderResult)(nil),                                   // 14: moego.api.order.v1.CreateExtraOrderResult
	(*UpdateExtraOrderParams)(nil),                                   // 15: moego.api.order.v1.UpdateExtraOrderParams
	(*UpdateExtraOrderResult)(nil),                                   // 16: moego.api.order.v1.UpdateExtraOrderResult
	(*UpdateLineItemForExtraOrderParams)(nil),                        // 17: moego.api.order.v1.UpdateLineItemForExtraOrderParams
	(*UpdateLineItemForExtraOrderResult)(nil),                        // 18: moego.api.order.v1.UpdateLineItemForExtraOrderResult
	(*DeleteLineItemForExtraOrderParams)(nil),                        // 19: moego.api.order.v1.DeleteLineItemForExtraOrderParams
	(*DeleteLineItemForExtraOrderResult)(nil),                        // 20: moego.api.order.v1.DeleteLineItemForExtraOrderResult
	(*EditStaffCommissionParams)(nil),                                // 21: moego.api.order.v1.EditStaffCommissionParams
	(*EditStaffCommissionResult)(nil),                                // 22: moego.api.order.v1.EditStaffCommissionResult
	(*UpgradeInvoiceReinventParams)(nil),                             // 23: moego.api.order.v1.UpgradeInvoiceReinventParams
	(*UpgradeInvoiceReinventResult)(nil),                             // 24: moego.api.order.v1.UpgradeInvoiceReinventResult
	(*CheckInvoiceReinventParams)(nil),                               // 25: moego.api.order.v1.CheckInvoiceReinventParams
	(*CheckInvoiceReinventResult)(nil),                               // 26: moego.api.order.v1.CheckInvoiceReinventResult
	(*PreviewRefundOrderParams)(nil),                                 // 27: moego.api.order.v1.PreviewRefundOrderParams
	(*PreviewRefundOrderResult)(nil),                                 // 28: moego.api.order.v1.PreviewRefundOrderResult
	(*PreviewRefundOrderPaymentsParams)(nil),                         // 29: moego.api.order.v1.PreviewRefundOrderPaymentsParams
	(*PreviewRefundOrderPaymentsResult)(nil),                         // 30: moego.api.order.v1.PreviewRefundOrderPaymentsResult
	(*RefundOrderParams)(nil),                                        // 31: moego.api.order.v1.RefundOrderParams
	(*RefundOrderResult)(nil),                                        // 32: moego.api.order.v1.RefundOrderResult
	(*ListOrdersParams)(nil),                                         // 33: moego.api.order.v1.ListOrdersParams
	(*ListOrdersResult)(nil),                                         // 34: moego.api.order.v1.ListOrdersResult
	(*ListOrderDetailParam)(nil),                                     // 35: moego.api.order.v1.ListOrderDetailParam
	(*ListOrderDetailResult)(nil),                                    // 36: moego.api.order.v1.ListOrderDetailResult
	(*QueryOrderDetailParams)(nil),                                   // 37: moego.api.order.v1.QueryOrderDetailParams
	(*QueryOrderDetailResult)(nil),                                   // 38: moego.api.order.v1.QueryOrderDetailResult
	(*GetOrderDetailParam)(nil),                                      // 39: moego.api.order.v1.GetOrderDetailParam
	(*GetOrderDetailResult)(nil),                                     // 40: moego.api.order.v1.GetOrderDetailResult
	(*GetRefundOrderDetailParam)(nil),                                // 41: moego.api.order.v1.GetRefundOrderDetailParam
	(*GetRefundOrderDetailResult)(nil),                               // 42: moego.api.order.v1.GetRefundOrderDetailResult
	(*CreateTipOrderParams)(nil),                                     // 43: moego.api.order.v1.CreateTipOrderParams
	(*CreateTipOrderResult)(nil),                                     // 44: moego.api.order.v1.CreateTipOrderResult
	(*CreateDepositOrderParams)(nil),                                 // 45: moego.api.order.v1.CreateDepositOrderParams
	(*CreateDepositOrderResult)(nil),                                 // 46: moego.api.order.v1.CreateDepositOrderResult
	(*SendInvoiceParams)(nil),                                        // 47: moego.api.order.v1.SendInvoiceParams
	(*SendInvoiceResult)(nil),                                        // 48: moego.api.order.v1.SendInvoiceResult
	(*GetEstimateParams)(nil),                                        // 49: moego.api.order.v1.GetEstimateParams
	(*GetEstimateResult)(nil),                                        // 50: moego.api.order.v1.GetEstimateResult
	(*GetInvoiceParams)(nil),                                         // 51: moego.api.order.v1.GetInvoiceParams
	(*GetInvoiceResult)(nil),                                         // 52: moego.api.order.v1.GetInvoiceResult
	(*CancelOrderParams)(nil),                                        // 53: moego.api.order.v1.CancelOrderParams
	(*CancelOrderResult)(nil),                                        // 54: moego.api.order.v1.CancelOrderResult
	(*PreviewRefundOrderResult_RelatedRefundOrder)(nil),              // 55: moego.api.order.v1.PreviewRefundOrderResult.RelatedRefundOrder
	(*SendInvoiceParams_Receipt)(nil),                                // 56: moego.api.order.v1.SendInvoiceParams.Receipt
	(*SendInvoiceParams_Estimate)(nil),                               // 57: moego.api.order.v1.SendInvoiceParams.Estimate
	(*GetInvoiceResult_OrderItemExtra)(nil),                          // 58: moego.api.order.v1.GetInvoiceResult.OrderItemExtra
	(*GetInvoiceResult_AppointmentBrief)(nil),                        // 59: moego.api.order.v1.GetInvoiceResult.AppointmentBrief
	(*GetInvoiceResult_CustomerBrief)(nil),                           // 60: moego.api.order.v1.GetInvoiceResult.CustomerBrief
	(*v1.RefundChannelResponse)(nil),                                 // 61: moego.models.order.v1.RefundChannelResponse
	(*v1.OrderModelHistoryView)(nil),                                 // 62: moego.models.order.v1.OrderModelHistoryView
	(*v11.PetDetailDef)(nil),                                         // 63: moego.models.appointment.v1.PetDetailDef
	(*v1.EditStaffCommissionItem)(nil),                               // 64: moego.models.order.v1.EditStaffCommissionItem
	(v1.RefundMode)(0),                                               // 65: moego.models.order.v1.RefundMode
	(*v12.RefundOrderRequest_OrderPayment)(nil),                      // 66: moego.service.order.v1.RefundOrderRequest.OrderPayment
	(*v12.RefundOrderRequest_RefundByItem)(nil),                      // 67: moego.service.order.v1.RefundOrderRequest.RefundByItem
	(*v12.RefundOrderRequest_RefundByPayment)(nil),                   // 68: moego.service.order.v1.RefundOrderRequest.RefundByPayment
	(*v1.OrderDetailView)(nil),                                       // 69: moego.models.order.v1.OrderDetailView
	(*v1.RefundOrderDetailView)(nil),                                 // 70: moego.models.order.v1.RefundOrderDetailView
	(*v12.PreviewRefundOrderResponse_RefundableItem)(nil),            // 71: moego.service.order.v1.PreviewRefundOrderResponse.RefundableItem
	(*money.Money)(nil),                                              // 72: google.type.Money
	(*v12.PreviewRefundOrderResponse_RefundFlags)(nil),               // 73: moego.service.order.v1.PreviewRefundOrderResponse.RefundFlags
	(*v12.PreviewRefundOrderResponse_RefundableOrderPayment)(nil),    // 74: moego.service.order.v1.PreviewRefundOrderResponse.RefundableOrderPayment
	(*v12.RefundOrderRequest_RefundByPayment_RefundAmountFlags)(nil), // 75: moego.service.order.v1.RefundOrderRequest.RefundByPayment.RefundAmountFlags
	(*v1.OrderPaymentModel)(nil),                                     // 76: moego.models.order.v1.OrderPaymentModel
	(*v1.RefundOrderPaymentModel)(nil),                               // 77: moego.models.order.v1.RefundOrderPaymentModel
	(*v1.OrderModelV1)(nil),                                          // 78: moego.models.order.v1.OrderModelV1
	(*v1.RefundOrderModel)(nil),                                      // 79: moego.models.order.v1.RefundOrderModel
	(v1.OrderSourceType)(0),                                          // 80: moego.models.order.v1.OrderSourceType
	(v13.ServiceItemType)(0),                                         // 81: moego.models.offering.v1.ServiceItemType
	(v13.ServicePriceUnit)(0),                                        // 82: moego.models.offering.v1.ServicePriceUnit
	(v11.AppointmentStatus)(0),                                       // 83: moego.models.appointment.v1.AppointmentStatus
}
var file_moego_api_order_v1_order_api_proto_depIdxs = []int32{
	61, // 0: moego.api.order.v1.OperateServiceChargeToOrderResponse.refund_channel:type_name -> moego.models.order.v1.RefundChannelResponse
	61, // 1: moego.api.order.v1.SetTipsResponse.refund_channel:type_name -> moego.models.order.v1.RefundChannelResponse
	61, // 2: moego.api.order.v1.ModifyItemTaxResponse.refund_channel:type_name -> moego.models.order.v1.RefundChannelResponse
	62, // 3: moego.api.order.v1.GetOrderHistoryResult.order_view:type_name -> moego.models.order.v1.OrderModelHistoryView
	63, // 4: moego.api.order.v1.UpdateExtraOrderParams.pet_detail:type_name -> moego.models.appointment.v1.PetDetailDef
	64, // 5: moego.api.order.v1.EditStaffCommissionParams.edit_staff_commission_items:type_name -> moego.models.order.v1.EditStaffCommissionItem
	65, // 6: moego.api.order.v1.PreviewRefundOrderParams.refund_mode:type_name -> moego.models.order.v1.RefundMode
	66, // 7: moego.api.order.v1.PreviewRefundOrderParams.source_order_payments:type_name -> moego.service.order.v1.RefundOrderRequest.OrderPayment
	67, // 8: moego.api.order.v1.PreviewRefundOrderParams.refund_by_item:type_name -> moego.service.order.v1.RefundOrderRequest.RefundByItem
	68, // 9: moego.api.order.v1.PreviewRefundOrderParams.refund_by_payment:type_name -> moego.service.order.v1.RefundOrderRequest.RefundByPayment
	69, // 10: moego.api.order.v1.PreviewRefundOrderResult.order_detail:type_name -> moego.models.order.v1.OrderDetailView
	70, // 11: moego.api.order.v1.PreviewRefundOrderResult.refund_order_preview:type_name -> moego.models.order.v1.RefundOrderDetailView
	55, // 12: moego.api.order.v1.PreviewRefundOrderResult.related_refund_orders:type_name -> moego.api.order.v1.PreviewRefundOrderResult.RelatedRefundOrder
	71, // 13: moego.api.order.v1.PreviewRefundOrderResult.refundable_items:type_name -> moego.service.order.v1.PreviewRefundOrderResponse.RefundableItem
	72, // 14: moego.api.order.v1.PreviewRefundOrderResult.refundable_tips:type_name -> google.type.Money
	73, // 15: moego.api.order.v1.PreviewRefundOrderResult.refund_flags:type_name -> moego.service.order.v1.PreviewRefundOrderResponse.RefundFlags
	74, // 16: moego.api.order.v1.PreviewRefundOrderResult.refundable_order_payments:type_name -> moego.service.order.v1.PreviewRefundOrderResponse.RefundableOrderPayment
	72, // 17: moego.api.order.v1.PreviewRefundOrderResult.refundable_convenience_fee:type_name -> google.type.Money
	72, // 18: moego.api.order.v1.PreviewRefundOrderPaymentsParams.refund_amount:type_name -> google.type.Money
	75, // 19: moego.api.order.v1.PreviewRefundOrderPaymentsParams.refund_amount_flag:type_name -> moego.service.order.v1.RefundOrderRequest.RefundByPayment.RefundAmountFlags
	76, // 20: moego.api.order.v1.PreviewRefundOrderPaymentsParams.source_order_payments:type_name -> moego.models.order.v1.OrderPaymentModel
	72, // 21: moego.api.order.v1.PreviewRefundOrderPaymentsResult.refund_total_amount:type_name -> google.type.Money
	72, // 22: moego.api.order.v1.PreviewRefundOrderPaymentsResult.refund_convenience_fee:type_name -> google.type.Money
	77, // 23: moego.api.order.v1.PreviewRefundOrderPaymentsResult.refund_order_payments:type_name -> moego.models.order.v1.RefundOrderPaymentModel
	65, // 24: moego.api.order.v1.RefundOrderParams.refund_mode:type_name -> moego.models.order.v1.RefundMode
	66, // 25: moego.api.order.v1.RefundOrderParams.source_order_payments:type_name -> moego.service.order.v1.RefundOrderRequest.OrderPayment
	67, // 26: moego.api.order.v1.RefundOrderParams.refund_by_item:type_name -> moego.service.order.v1.RefundOrderRequest.RefundByItem
	68, // 27: moego.api.order.v1.RefundOrderParams.refund_by_payment:type_name -> moego.service.order.v1.RefundOrderRequest.RefundByPayment
	70, // 28: moego.api.order.v1.RefundOrderResult.refund_order_detail:type_name -> moego.models.order.v1.RefundOrderDetailView
	70, // 29: moego.api.order.v1.RefundOrderResult.related_refund_order_details:type_name -> moego.models.order.v1.RefundOrderDetailView
	78, // 30: moego.api.order.v1.ListOrdersResult.orders:type_name -> moego.models.order.v1.OrderModelV1
	79, // 31: moego.api.order.v1.ListOrdersResult.refund_orders:type_name -> moego.models.order.v1.RefundOrderModel
	69, // 32: moego.api.order.v1.ListOrderDetailResult.orders:type_name -> moego.models.order.v1.OrderDetailView
	70, // 33: moego.api.order.v1.ListOrderDetailResult.refund_orders:type_name -> moego.models.order.v1.RefundOrderDetailView
	80, // 34: moego.api.order.v1.QueryOrderDetailParams.source_type:type_name -> moego.models.order.v1.OrderSourceType
	69, // 35: moego.api.order.v1.QueryOrderDetailResult.orders:type_name -> moego.models.order.v1.OrderDetailView
	70, // 36: moego.api.order.v1.QueryOrderDetailResult.refund_orders:type_name -> moego.models.order.v1.RefundOrderDetailView
	69, // 37: moego.api.order.v1.GetOrderDetailResult.order:type_name -> moego.models.order.v1.OrderDetailView
	70, // 38: moego.api.order.v1.GetRefundOrderDetailResult.refund_order:type_name -> moego.models.order.v1.RefundOrderDetailView
	80, // 39: moego.api.order.v1.CreateTipOrderParams.source_type:type_name -> moego.models.order.v1.OrderSourceType
	72, // 40: moego.api.order.v1.CreateTipOrderParams.tip_amount:type_name -> google.type.Money
	72, // 41: moego.api.order.v1.CreateTipOrderParams.tip_based_amount:type_name -> google.type.Money
	69, // 42: moego.api.order.v1.CreateTipOrderResult.order:type_name -> moego.models.order.v1.OrderDetailView
	80, // 43: moego.api.order.v1.CreateDepositOrderParams.source_type:type_name -> moego.models.order.v1.OrderSourceType
	72, // 44: moego.api.order.v1.CreateDepositOrderParams.deposit_amount:type_name -> google.type.Money
	69, // 45: moego.api.order.v1.CreateDepositOrderResult.order:type_name -> moego.models.order.v1.OrderDetailView
	57, // 46: moego.api.order.v1.SendInvoiceParams.estimate:type_name -> moego.api.order.v1.SendInvoiceParams.Estimate
	56, // 47: moego.api.order.v1.SendInvoiceParams.receipt:type_name -> moego.api.order.v1.SendInvoiceParams.Receipt
	80, // 48: moego.api.order.v1.GetEstimateParams.source_type:type_name -> moego.models.order.v1.OrderSourceType
	69, // 49: moego.api.order.v1.GetEstimateResult.estimate:type_name -> moego.models.order.v1.OrderDetailView
	59, // 50: moego.api.order.v1.GetEstimateResult.appointment:type_name -> moego.api.order.v1.GetInvoiceResult.AppointmentBrief
	60, // 51: moego.api.order.v1.GetEstimateResult.customer:type_name -> moego.api.order.v1.GetInvoiceResult.CustomerBrief
	58, // 52: moego.api.order.v1.GetEstimateResult.order_item_extras:type_name -> moego.api.order.v1.GetInvoiceResult.OrderItemExtra
	69, // 53: moego.api.order.v1.GetEstimateResult.deposit:type_name -> moego.models.order.v1.OrderDetailView
	69, // 54: moego.api.order.v1.GetInvoiceResult.invoice:type_name -> moego.models.order.v1.OrderDetailView
	59, // 55: moego.api.order.v1.GetInvoiceResult.appointment:type_name -> moego.api.order.v1.GetInvoiceResult.AppointmentBrief
	60, // 56: moego.api.order.v1.GetInvoiceResult.customer:type_name -> moego.api.order.v1.GetInvoiceResult.CustomerBrief
	58, // 57: moego.api.order.v1.GetInvoiceResult.order_item_extras:type_name -> moego.api.order.v1.GetInvoiceResult.OrderItemExtra
	0,  // 58: moego.api.order.v1.GetInvoiceResult.invoice_status:type_name -> moego.api.order.v1.GetInvoiceResult.InvoiceStatus
	69, // 59: moego.api.order.v1.GetInvoiceResult.deposit:type_name -> moego.models.order.v1.OrderDetailView
	69, // 60: moego.api.order.v1.PreviewRefundOrderResult.RelatedRefundOrder.order_detail:type_name -> moego.models.order.v1.OrderDetailView
	70, // 61: moego.api.order.v1.PreviewRefundOrderResult.RelatedRefundOrder.refund_order_preview:type_name -> moego.models.order.v1.RefundOrderDetailView
	80, // 62: moego.api.order.v1.SendInvoiceParams.Estimate.source_type:type_name -> moego.models.order.v1.OrderSourceType
	81, // 63: moego.api.order.v1.GetInvoiceResult.OrderItemExtra.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	82, // 64: moego.api.order.v1.GetInvoiceResult.OrderItemExtra.service_price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	83, // 65: moego.api.order.v1.GetInvoiceResult.AppointmentBrief.status:type_name -> moego.models.appointment.v1.AppointmentStatus
	5,  // 66: moego.api.order.v1.OrderService.ModifyItemTax:input_type -> moego.api.order.v1.ModifyItemTaxRequest
	1,  // 67: moego.api.order.v1.OrderService.AddServiceChargeToOrder:input_type -> moego.api.order.v1.AddOrRemoveServiceChargeRequest
	1,  // 68: moego.api.order.v1.OrderService.RemoveServiceChargeFromOrder:input_type -> moego.api.order.v1.AddOrRemoveServiceChargeRequest
	3,  // 69: moego.api.order.v1.OrderService.SetTips:input_type -> moego.api.order.v1.SetTipsRequest
	7,  // 70: moego.api.order.v1.OrderService.SetDiscount:input_type -> moego.api.order.v1.SetDiscountRequest
	9,  // 71: moego.api.order.v1.OrderService.GetOrderHistory:input_type -> moego.api.order.v1.GetOrderHistoryParams
	11, // 72: moego.api.order.v1.OrderService.CompleteOrder:input_type -> moego.api.order.v1.CompleteOrderParams
	13, // 73: moego.api.order.v1.OrderService.CreateExtraOrder:input_type -> moego.api.order.v1.CreateExtraOrderParams
	15, // 74: moego.api.order.v1.OrderService.UpdateExtraOrder:input_type -> moego.api.order.v1.UpdateExtraOrderParams
	17, // 75: moego.api.order.v1.OrderService.UpdateLineItemForExtraOrder:input_type -> moego.api.order.v1.UpdateLineItemForExtraOrderParams
	19, // 76: moego.api.order.v1.OrderService.DeleteLineItemForExtraOrder:input_type -> moego.api.order.v1.DeleteLineItemForExtraOrderParams
	21, // 77: moego.api.order.v1.OrderService.EditStaffCommission:input_type -> moego.api.order.v1.EditStaffCommissionParams
	23, // 78: moego.api.order.v1.OrderService.UpgradeInvoiceReinvent:input_type -> moego.api.order.v1.UpgradeInvoiceReinventParams
	25, // 79: moego.api.order.v1.OrderService.CheckInvoiceReinvent:input_type -> moego.api.order.v1.CheckInvoiceReinventParams
	27, // 80: moego.api.order.v1.OrderService.PreviewRefundOrder:input_type -> moego.api.order.v1.PreviewRefundOrderParams
	29, // 81: moego.api.order.v1.OrderService.PreviewRefundOrderPayments:input_type -> moego.api.order.v1.PreviewRefundOrderPaymentsParams
	31, // 82: moego.api.order.v1.OrderService.RefundOrder:input_type -> moego.api.order.v1.RefundOrderParams
	33, // 83: moego.api.order.v1.OrderService.ListOrders:input_type -> moego.api.order.v1.ListOrdersParams
	35, // 84: moego.api.order.v1.OrderService.ListOrderDetail:input_type -> moego.api.order.v1.ListOrderDetailParam
	37, // 85: moego.api.order.v1.OrderService.QueryOrderDetail:input_type -> moego.api.order.v1.QueryOrderDetailParams
	39, // 86: moego.api.order.v1.OrderService.GetOrderDetail:input_type -> moego.api.order.v1.GetOrderDetailParam
	41, // 87: moego.api.order.v1.OrderService.GetRefundOrderDetail:input_type -> moego.api.order.v1.GetRefundOrderDetailParam
	43, // 88: moego.api.order.v1.OrderService.CreateTipOrder:input_type -> moego.api.order.v1.CreateTipOrderParams
	45, // 89: moego.api.order.v1.OrderService.CreateDepositOrder:input_type -> moego.api.order.v1.CreateDepositOrderParams
	53, // 90: moego.api.order.v1.OrderService.CancelOrder:input_type -> moego.api.order.v1.CancelOrderParams
	49, // 91: moego.api.order.v1.OrderService.GetEstimate:input_type -> moego.api.order.v1.GetEstimateParams
	51, // 92: moego.api.order.v1.OrderService.GetInvoice:input_type -> moego.api.order.v1.GetInvoiceParams
	47, // 93: moego.api.order.v1.OrderService.SendInvoice:input_type -> moego.api.order.v1.SendInvoiceParams
	6,  // 94: moego.api.order.v1.OrderService.ModifyItemTax:output_type -> moego.api.order.v1.ModifyItemTaxResponse
	2,  // 95: moego.api.order.v1.OrderService.AddServiceChargeToOrder:output_type -> moego.api.order.v1.OperateServiceChargeToOrderResponse
	2,  // 96: moego.api.order.v1.OrderService.RemoveServiceChargeFromOrder:output_type -> moego.api.order.v1.OperateServiceChargeToOrderResponse
	4,  // 97: moego.api.order.v1.OrderService.SetTips:output_type -> moego.api.order.v1.SetTipsResponse
	8,  // 98: moego.api.order.v1.OrderService.SetDiscount:output_type -> moego.api.order.v1.SetDiscountResponse
	10, // 99: moego.api.order.v1.OrderService.GetOrderHistory:output_type -> moego.api.order.v1.GetOrderHistoryResult
	12, // 100: moego.api.order.v1.OrderService.CompleteOrder:output_type -> moego.api.order.v1.CompleteOrderResult
	14, // 101: moego.api.order.v1.OrderService.CreateExtraOrder:output_type -> moego.api.order.v1.CreateExtraOrderResult
	16, // 102: moego.api.order.v1.OrderService.UpdateExtraOrder:output_type -> moego.api.order.v1.UpdateExtraOrderResult
	18, // 103: moego.api.order.v1.OrderService.UpdateLineItemForExtraOrder:output_type -> moego.api.order.v1.UpdateLineItemForExtraOrderResult
	20, // 104: moego.api.order.v1.OrderService.DeleteLineItemForExtraOrder:output_type -> moego.api.order.v1.DeleteLineItemForExtraOrderResult
	22, // 105: moego.api.order.v1.OrderService.EditStaffCommission:output_type -> moego.api.order.v1.EditStaffCommissionResult
	24, // 106: moego.api.order.v1.OrderService.UpgradeInvoiceReinvent:output_type -> moego.api.order.v1.UpgradeInvoiceReinventResult
	26, // 107: moego.api.order.v1.OrderService.CheckInvoiceReinvent:output_type -> moego.api.order.v1.CheckInvoiceReinventResult
	28, // 108: moego.api.order.v1.OrderService.PreviewRefundOrder:output_type -> moego.api.order.v1.PreviewRefundOrderResult
	30, // 109: moego.api.order.v1.OrderService.PreviewRefundOrderPayments:output_type -> moego.api.order.v1.PreviewRefundOrderPaymentsResult
	32, // 110: moego.api.order.v1.OrderService.RefundOrder:output_type -> moego.api.order.v1.RefundOrderResult
	34, // 111: moego.api.order.v1.OrderService.ListOrders:output_type -> moego.api.order.v1.ListOrdersResult
	36, // 112: moego.api.order.v1.OrderService.ListOrderDetail:output_type -> moego.api.order.v1.ListOrderDetailResult
	38, // 113: moego.api.order.v1.OrderService.QueryOrderDetail:output_type -> moego.api.order.v1.QueryOrderDetailResult
	40, // 114: moego.api.order.v1.OrderService.GetOrderDetail:output_type -> moego.api.order.v1.GetOrderDetailResult
	42, // 115: moego.api.order.v1.OrderService.GetRefundOrderDetail:output_type -> moego.api.order.v1.GetRefundOrderDetailResult
	44, // 116: moego.api.order.v1.OrderService.CreateTipOrder:output_type -> moego.api.order.v1.CreateTipOrderResult
	46, // 117: moego.api.order.v1.OrderService.CreateDepositOrder:output_type -> moego.api.order.v1.CreateDepositOrderResult
	54, // 118: moego.api.order.v1.OrderService.CancelOrder:output_type -> moego.api.order.v1.CancelOrderResult
	50, // 119: moego.api.order.v1.OrderService.GetEstimate:output_type -> moego.api.order.v1.GetEstimateResult
	52, // 120: moego.api.order.v1.OrderService.GetInvoice:output_type -> moego.api.order.v1.GetInvoiceResult
	48, // 121: moego.api.order.v1.OrderService.SendInvoice:output_type -> moego.api.order.v1.SendInvoiceResult
	94, // [94:122] is the sub-list for method output_type
	66, // [66:94] is the sub-list for method input_type
	66, // [66:66] is the sub-list for extension type_name
	66, // [66:66] is the sub-list for extension extendee
	0,  // [0:66] is the sub-list for field type_name
}

func init() { file_moego_api_order_v1_order_api_proto_init() }
func file_moego_api_order_v1_order_api_proto_init() {
	if File_moego_api_order_v1_order_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_order_v1_order_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddOrRemoveServiceChargeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperateServiceChargeToOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTipsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetTipsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyItemTaxRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyItemTaxResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetDiscountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetDiscountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderHistoryParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderHistoryResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteOrderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteOrderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExtraOrderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExtraOrderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateExtraOrderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateExtraOrderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLineItemForExtraOrderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLineItemForExtraOrderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLineItemForExtraOrderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLineItemForExtraOrderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditStaffCommissionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditStaffCommissionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeInvoiceReinventParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeInvoiceReinventResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckInvoiceReinventParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckInvoiceReinventResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewRefundOrderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewRefundOrderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewRefundOrderPaymentsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewRefundOrderPaymentsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundOrderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundOrderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrdersParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrdersResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrderDetailParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOrderDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryOrderDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryOrderDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderDetailParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRefundOrderDetailParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRefundOrderDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTipOrderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTipOrderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDepositOrderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDepositOrderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendInvoiceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendInvoiceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEstimateParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEstimateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvoiceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvoiceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelOrderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelOrderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewRefundOrderResult_RelatedRefundOrder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendInvoiceParams_Receipt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendInvoiceParams_Estimate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvoiceResult_OrderItemExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvoiceResult_AppointmentBrief); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_order_api_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvoiceResult_CustomerBrief); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_order_v1_order_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_order_v1_order_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_api_order_v1_order_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_api_order_v1_order_api_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_api_order_v1_order_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_api_order_v1_order_api_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_api_order_v1_order_api_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_moego_api_order_v1_order_api_proto_msgTypes[16].OneofWrappers = []interface{}{}
	file_moego_api_order_v1_order_api_proto_msgTypes[26].OneofWrappers = []interface{}{
		(*PreviewRefundOrderParams_RefundByItem)(nil),
		(*PreviewRefundOrderParams_RefundByPayment)(nil),
	}
	file_moego_api_order_v1_order_api_proto_msgTypes[30].OneofWrappers = []interface{}{
		(*RefundOrderParams_RefundByItem)(nil),
		(*RefundOrderParams_RefundByPayment)(nil),
	}
	file_moego_api_order_v1_order_api_proto_msgTypes[46].OneofWrappers = []interface{}{
		(*SendInvoiceParams_Estimate_)(nil),
		(*SendInvoiceParams_Receipt_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_order_v1_order_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   60,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_order_v1_order_api_proto_goTypes,
		DependencyIndexes: file_moego_api_order_v1_order_api_proto_depIdxs,
		EnumInfos:         file_moego_api_order_v1_order_api_proto_enumTypes,
		MessageInfos:      file_moego_api_order_v1_order_api_proto_msgTypes,
	}.Build()
	File_moego_api_order_v1_order_api_proto = out.File
	file_moego_api_order_v1_order_api_proto_rawDesc = nil
	file_moego_api_order_v1_order_api_proto_goTypes = nil
	file_moego_api_order_v1_order_api_proto_depIdxs = nil
}
