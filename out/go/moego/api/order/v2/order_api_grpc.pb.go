// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/order/v2/order_api.proto

package orderapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// OrderServiceClient is the client API for OrderService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OrderServiceClient interface {
	// 预览创建 Sales 订单
	PreviewCreateOrder(ctx context.Context, in *PreviewCreateOrderParams, opts ...grpc.CallOption) (*PreviewCreateOrderResult, error)
	// 创建 Sales 订单.
	CreateOrder(ctx context.Context, in *CreateOrderParams, opts ...grpc.CallOption) (*CreateOrderResult, error)
	// 创建支付单据并且发起支付
	PayOrder(ctx context.Context, in *PayOrderParams, opts ...grpc.CallOption) (*PayOrderResult, error)
}

type orderServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOrderServiceClient(cc grpc.ClientConnInterface) OrderServiceClient {
	return &orderServiceClient{cc}
}

func (c *orderServiceClient) PreviewCreateOrder(ctx context.Context, in *PreviewCreateOrderParams, opts ...grpc.CallOption) (*PreviewCreateOrderResult, error) {
	out := new(PreviewCreateOrderResult)
	err := c.cc.Invoke(ctx, "/moego.api.order.v2.OrderService/PreviewCreateOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderServiceClient) CreateOrder(ctx context.Context, in *CreateOrderParams, opts ...grpc.CallOption) (*CreateOrderResult, error) {
	out := new(CreateOrderResult)
	err := c.cc.Invoke(ctx, "/moego.api.order.v2.OrderService/CreateOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderServiceClient) PayOrder(ctx context.Context, in *PayOrderParams, opts ...grpc.CallOption) (*PayOrderResult, error) {
	out := new(PayOrderResult)
	err := c.cc.Invoke(ctx, "/moego.api.order.v2.OrderService/PayOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrderServiceServer is the server API for OrderService service.
// All implementations must embed UnimplementedOrderServiceServer
// for forward compatibility
type OrderServiceServer interface {
	// 预览创建 Sales 订单
	PreviewCreateOrder(context.Context, *PreviewCreateOrderParams) (*PreviewCreateOrderResult, error)
	// 创建 Sales 订单.
	CreateOrder(context.Context, *CreateOrderParams) (*CreateOrderResult, error)
	// 创建支付单据并且发起支付
	PayOrder(context.Context, *PayOrderParams) (*PayOrderResult, error)
	mustEmbedUnimplementedOrderServiceServer()
}

// UnimplementedOrderServiceServer must be embedded to have forward compatible implementations.
type UnimplementedOrderServiceServer struct {
}

func (UnimplementedOrderServiceServer) PreviewCreateOrder(context.Context, *PreviewCreateOrderParams) (*PreviewCreateOrderResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreviewCreateOrder not implemented")
}
func (UnimplementedOrderServiceServer) CreateOrder(context.Context, *CreateOrderParams) (*CreateOrderResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrder not implemented")
}
func (UnimplementedOrderServiceServer) PayOrder(context.Context, *PayOrderParams) (*PayOrderResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayOrder not implemented")
}
func (UnimplementedOrderServiceServer) mustEmbedUnimplementedOrderServiceServer() {}

// UnsafeOrderServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OrderServiceServer will
// result in compilation errors.
type UnsafeOrderServiceServer interface {
	mustEmbedUnimplementedOrderServiceServer()
}

func RegisterOrderServiceServer(s grpc.ServiceRegistrar, srv OrderServiceServer) {
	s.RegisterService(&OrderService_ServiceDesc, srv)
}

func _OrderService_PreviewCreateOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreviewCreateOrderParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServiceServer).PreviewCreateOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.order.v2.OrderService/PreviewCreateOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServiceServer).PreviewCreateOrder(ctx, req.(*PreviewCreateOrderParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderService_CreateOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrderParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServiceServer).CreateOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.order.v2.OrderService/CreateOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServiceServer).CreateOrder(ctx, req.(*CreateOrderParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrderService_PayOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayOrderParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServiceServer).PayOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.order.v2.OrderService/PayOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServiceServer).PayOrder(ctx, req.(*PayOrderParams))
	}
	return interceptor(ctx, in, info, handler)
}

// OrderService_ServiceDesc is the grpc.ServiceDesc for OrderService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OrderService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.order.v2.OrderService",
	HandlerType: (*OrderServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PreviewCreateOrder",
			Handler:    _OrderService_PreviewCreateOrder_Handler,
		},
		{
			MethodName: "CreateOrder",
			Handler:    _OrderService_CreateOrder_Handler,
		},
		{
			MethodName: "PayOrder",
			Handler:    _OrderService_PayOrder_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/order/v2/order_api.proto",
}
