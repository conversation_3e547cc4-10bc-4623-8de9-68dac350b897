// @since 2023-06-29 16:13:41
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/ai_assistant/v1/business_conversation_api.proto

package aiassistantapipb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// conversation mass text scenario
type ConversationMassTextScenario struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ConversationMassTextScenario) Reset() {
	*x = ConversationMassTextScenario{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConversationMassTextScenario) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConversationMassTextScenario) ProtoMessage() {}

func (x *ConversationMassTextScenario) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConversationMassTextScenario.ProtoReflect.Descriptor instead.
func (*ConversationMassTextScenario) Descriptor() ([]byte, []int) {
	return file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescGZIP(), []int{0}
}

// conversation message scenario
type ConversationTwoWayMessageScenario struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
}

func (x *ConversationTwoWayMessageScenario) Reset() {
	*x = ConversationTwoWayMessageScenario{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConversationTwoWayMessageScenario) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConversationTwoWayMessageScenario) ProtoMessage() {}

func (x *ConversationTwoWayMessageScenario) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConversationTwoWayMessageScenario.ProtoReflect.Descriptor instead.
func (*ConversationTwoWayMessageScenario) Descriptor() ([]byte, []int) {
	return file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescGZIP(), []int{1}
}

func (x *ConversationTwoWayMessageScenario) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// conversation marketing email scenario
type ConversationMarketingEmailScenario struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ConversationMarketingEmailScenario) Reset() {
	*x = ConversationMarketingEmailScenario{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConversationMarketingEmailScenario) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConversationMarketingEmailScenario) ProtoMessage() {}

func (x *ConversationMarketingEmailScenario) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConversationMarketingEmailScenario.ProtoReflect.Descriptor instead.
func (*ConversationMarketingEmailScenario) Descriptor() ([]byte, []int) {
	return file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescGZIP(), []int{2}
}

// create business conversation Params
type CreateBusinessConversationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// prompt. If provided, the conversation will start based on this prompt
	Prompt string `protobuf:"bytes,1,opt,name=prompt,proto3" json:"prompt,omitempty"`
	// scenario
	//
	// Types that are assignable to Scenario:
	//
	//	*CreateBusinessConversationParams_MassText
	//	*CreateBusinessConversationParams_TwoWayMessage
	//	*CreateBusinessConversationParams_MarketingEmail
	Scenario isCreateBusinessConversationParams_Scenario `protobuf_oneof:"scenario"`
}

func (x *CreateBusinessConversationParams) Reset() {
	*x = CreateBusinessConversationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBusinessConversationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBusinessConversationParams) ProtoMessage() {}

func (x *CreateBusinessConversationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBusinessConversationParams.ProtoReflect.Descriptor instead.
func (*CreateBusinessConversationParams) Descriptor() ([]byte, []int) {
	return file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescGZIP(), []int{3}
}

func (x *CreateBusinessConversationParams) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (m *CreateBusinessConversationParams) GetScenario() isCreateBusinessConversationParams_Scenario {
	if m != nil {
		return m.Scenario
	}
	return nil
}

func (x *CreateBusinessConversationParams) GetMassText() *ConversationMassTextScenario {
	if x, ok := x.GetScenario().(*CreateBusinessConversationParams_MassText); ok {
		return x.MassText
	}
	return nil
}

func (x *CreateBusinessConversationParams) GetTwoWayMessage() *ConversationTwoWayMessageScenario {
	if x, ok := x.GetScenario().(*CreateBusinessConversationParams_TwoWayMessage); ok {
		return x.TwoWayMessage
	}
	return nil
}

func (x *CreateBusinessConversationParams) GetMarketingEmail() *ConversationMarketingEmailScenario {
	if x, ok := x.GetScenario().(*CreateBusinessConversationParams_MarketingEmail); ok {
		return x.MarketingEmail
	}
	return nil
}

type isCreateBusinessConversationParams_Scenario interface {
	isCreateBusinessConversationParams_Scenario()
}

type CreateBusinessConversationParams_MassText struct {
	// mass text scenario
	MassText *ConversationMassTextScenario `protobuf:"bytes,2,opt,name=mass_text,json=massText,proto3,oneof"`
}

type CreateBusinessConversationParams_TwoWayMessage struct {
	// message scenario
	TwoWayMessage *ConversationTwoWayMessageScenario `protobuf:"bytes,3,opt,name=two_way_message,json=twoWayMessage,proto3,oneof"`
}

type CreateBusinessConversationParams_MarketingEmail struct {
	// marketing email scenario
	MarketingEmail *ConversationMarketingEmailScenario `protobuf:"bytes,4,opt,name=marketing_email,json=marketingEmail,proto3,oneof"`
}

func (*CreateBusinessConversationParams_MassText) isCreateBusinessConversationParams_Scenario() {}

func (*CreateBusinessConversationParams_TwoWayMessage) isCreateBusinessConversationParams_Scenario() {
}

func (*CreateBusinessConversationParams_MarketingEmail) isCreateBusinessConversationParams_Scenario() {
}

// create business conversation Result
type CreateBusinessConversationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversation id
	ConversationId int64 `protobuf:"varint,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
}

func (x *CreateBusinessConversationResult) Reset() {
	*x = CreateBusinessConversationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBusinessConversationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBusinessConversationResult) ProtoMessage() {}

func (x *CreateBusinessConversationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBusinessConversationResult.ProtoReflect.Descriptor instead.
func (*CreateBusinessConversationResult) Descriptor() ([]byte, []int) {
	return file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescGZIP(), []int{4}
}

func (x *CreateBusinessConversationResult) GetConversationId() int64 {
	if x != nil {
		return x.ConversationId
	}
	return 0
}

// close business conversation Params
type CloseBusinessConversationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversation id
	ConversationId int64 `protobuf:"varint,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
}

func (x *CloseBusinessConversationParams) Reset() {
	*x = CloseBusinessConversationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseBusinessConversationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseBusinessConversationParams) ProtoMessage() {}

func (x *CloseBusinessConversationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseBusinessConversationParams.ProtoReflect.Descriptor instead.
func (*CloseBusinessConversationParams) Descriptor() ([]byte, []int) {
	return file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescGZIP(), []int{5}
}

func (x *CloseBusinessConversationParams) GetConversationId() int64 {
	if x != nil {
		return x.ConversationId
	}
	return 0
}

// close business conversation Result
type CloseBusinessConversationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CloseBusinessConversationResult) Reset() {
	*x = CloseBusinessConversationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseBusinessConversationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseBusinessConversationResult) ProtoMessage() {}

func (x *CloseBusinessConversationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseBusinessConversationResult.ProtoReflect.Descriptor instead.
func (*CloseBusinessConversationResult) Descriptor() ([]byte, []int) {
	return file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescGZIP(), []int{6}
}

// ask Params
type AskParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversation id
	ConversationId int64 `protobuf:"varint,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	// question
	Question string `protobuf:"bytes,2,opt,name=question,proto3" json:"question,omitempty"`
}

func (x *AskParams) Reset() {
	*x = AskParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AskParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AskParams) ProtoMessage() {}

func (x *AskParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AskParams.ProtoReflect.Descriptor instead.
func (*AskParams) Descriptor() ([]byte, []int) {
	return file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescGZIP(), []int{7}
}

func (x *AskParams) GetConversationId() int64 {
	if x != nil {
		return x.ConversationId
	}
	return 0
}

func (x *AskParams) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

// ask Result
type AskResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// question id
	QuestionId int64 `protobuf:"varint,1,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
	// answer
	Answer string `protobuf:"bytes,2,opt,name=answer,proto3" json:"answer,omitempty"`
}

func (x *AskResult) Reset() {
	*x = AskResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AskResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AskResult) ProtoMessage() {}

func (x *AskResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AskResult.ProtoReflect.Descriptor instead.
func (*AskResult) Descriptor() ([]byte, []int) {
	return file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescGZIP(), []int{8}
}

func (x *AskResult) GetQuestionId() int64 {
	if x != nil {
		return x.QuestionId
	}
	return 0
}

func (x *AskResult) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

// rephrase answer Params
type RephraseAnswerParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversation id
	ConversationId int64 `protobuf:"varint,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	// question id
	QuestionId int64 `protobuf:"varint,2,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
}

func (x *RephraseAnswerParams) Reset() {
	*x = RephraseAnswerParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RephraseAnswerParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RephraseAnswerParams) ProtoMessage() {}

func (x *RephraseAnswerParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RephraseAnswerParams.ProtoReflect.Descriptor instead.
func (*RephraseAnswerParams) Descriptor() ([]byte, []int) {
	return file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescGZIP(), []int{9}
}

func (x *RephraseAnswerParams) GetConversationId() int64 {
	if x != nil {
		return x.ConversationId
	}
	return 0
}

func (x *RephraseAnswerParams) GetQuestionId() int64 {
	if x != nil {
		return x.QuestionId
	}
	return 0
}

// rephrase answer Result
type RephraseAnswerResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// new question id, different from the one in RephraseAnswerRequest
	QuestionId int64 `protobuf:"varint,1,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
	// answer
	Answer string `protobuf:"bytes,2,opt,name=answer,proto3" json:"answer,omitempty"`
}

func (x *RephraseAnswerResult) Reset() {
	*x = RephraseAnswerResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RephraseAnswerResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RephraseAnswerResult) ProtoMessage() {}

func (x *RephraseAnswerResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RephraseAnswerResult.ProtoReflect.Descriptor instead.
func (*RephraseAnswerResult) Descriptor() ([]byte, []int) {
	return file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescGZIP(), []int{10}
}

func (x *RephraseAnswerResult) GetQuestionId() int64 {
	if x != nil {
		return x.QuestionId
	}
	return 0
}

func (x *RephraseAnswerResult) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

// adopt answer Params
type AdoptAnswerParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversation id
	ConversationId int64 `protobuf:"varint,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	// question id
	QuestionId int64 `protobuf:"varint,2,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
}

func (x *AdoptAnswerParams) Reset() {
	*x = AdoptAnswerParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdoptAnswerParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdoptAnswerParams) ProtoMessage() {}

func (x *AdoptAnswerParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdoptAnswerParams.ProtoReflect.Descriptor instead.
func (*AdoptAnswerParams) Descriptor() ([]byte, []int) {
	return file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescGZIP(), []int{11}
}

func (x *AdoptAnswerParams) GetConversationId() int64 {
	if x != nil {
		return x.ConversationId
	}
	return 0
}

func (x *AdoptAnswerParams) GetQuestionId() int64 {
	if x != nil {
		return x.QuestionId
	}
	return 0
}

// adopt answer Result
type AdoptAnswerResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AdoptAnswerResult) Reset() {
	*x = AdoptAnswerResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdoptAnswerResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdoptAnswerResult) ProtoMessage() {}

func (x *AdoptAnswerResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdoptAnswerResult.ProtoReflect.Descriptor instead.
func (*AdoptAnswerResult) Descriptor() ([]byte, []int) {
	return file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescGZIP(), []int{12}
}

// send answer Params
type SendAnswerParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversation id
	ConversationId int64 `protobuf:"varint,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	// question id
	QuestionId int64 `protobuf:"varint,2,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`
	// message id
	MessageId int64 `protobuf:"varint,3,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
}

func (x *SendAnswerParams) Reset() {
	*x = SendAnswerParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendAnswerParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendAnswerParams) ProtoMessage() {}

func (x *SendAnswerParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendAnswerParams.ProtoReflect.Descriptor instead.
func (*SendAnswerParams) Descriptor() ([]byte, []int) {
	return file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescGZIP(), []int{13}
}

func (x *SendAnswerParams) GetConversationId() int64 {
	if x != nil {
		return x.ConversationId
	}
	return 0
}

func (x *SendAnswerParams) GetQuestionId() int64 {
	if x != nil {
		return x.QuestionId
	}
	return 0
}

func (x *SendAnswerParams) GetMessageId() int64 {
	if x != nil {
		return x.MessageId
	}
	return 0
}

// send answer Result
type SendAnswerResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendAnswerResult) Reset() {
	*x = SendAnswerResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendAnswerResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendAnswerResult) ProtoMessage() {}

func (x *SendAnswerResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendAnswerResult.ProtoReflect.Descriptor instead.
func (*SendAnswerResult) Descriptor() ([]byte, []int) {
	return file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescGZIP(), []int{14}
}

var File_moego_api_ai_assistant_v1_business_conversation_api_proto protoreflect.FileDescriptor

var file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDesc = []byte{
	0x0a, 0x39, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x5f, 0x61,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x1e, 0x0a, 0x1c, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x61, 0x73, 0x73, 0x54, 0x65, 0x78, 0x74, 0x53, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x22,
	0x4d, 0x0a, 0x21, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x77, 0x6f, 0x57, 0x61, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x63, 0x65, 0x6e,
	0x61, 0x72, 0x69, 0x6f, 0x12, 0x28, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x22, 0x24,
	0x0a, 0x22, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x63, 0x65, 0x6e,
	0x61, 0x72, 0x69, 0x6f, 0x22, 0xff, 0x02, 0x0a, 0x20, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x06, 0x70, 0x72, 0x6f,
	0x6d, 0x70, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03,
	0x18, 0x80, 0x40, 0x52, 0x06, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x56, 0x0a, 0x09, 0x6d,
	0x61, 0x73, 0x73, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73,
	0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x73, 0x73, 0x54, 0x65, 0x78, 0x74, 0x53,
	0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x48, 0x00, 0x52, 0x08, 0x6d, 0x61, 0x73, 0x73, 0x54,
	0x65, 0x78, 0x74, 0x12, 0x66, 0x0a, 0x0f, 0x74, 0x77, 0x6f, 0x5f, 0x77, 0x61, 0x79, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x77, 0x6f, 0x57, 0x61, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x53, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f, 0x48, 0x00, 0x52, 0x0d, 0x74, 0x77,
	0x6f, 0x57, 0x61, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x68, 0x0a, 0x0f, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x63, 0x65, 0x6e, 0x61,
	0x72, 0x69, 0x6f, 0x48, 0x00, 0x52, 0x0e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x42, 0x0f, 0x0a, 0x08, 0x73, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69,
	0x6f, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x4b, 0x0a, 0x20, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x22, 0x4a, 0x0a, 0x1f, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22,
	0x21, 0x0a, 0x1f, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x5a, 0x0a, 0x09, 0x41, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72,
	0x03, 0x18, 0x80, 0x40, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x44,
	0x0a, 0x09, 0x41, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x22, 0x60, 0x0a, 0x14, 0x52, 0x65, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65,
	0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x27, 0x0a, 0x0f,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x4f, 0x0a, 0x14, 0x52, 0x65, 0x70, 0x68, 0x72, 0x61,
	0x73, 0x65, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x22, 0x5d, 0x0a, 0x11, 0x41, 0x64, 0x6f, 0x70, 0x74,
	0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x27, 0x0a, 0x0f,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x13, 0x0a, 0x11, 0x41, 0x64, 0x6f, 0x70, 0x74, 0x41,
	0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x7b, 0x0a, 0x10, 0x53,
	0x65, 0x6e, 0x64, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x12, 0x0a, 0x10, 0x53, 0x65, 0x6e, 0x64,
	0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xd6, 0x05, 0x0a,
	0x1b, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8e, 0x01, 0x0a,
	0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x5f,
	0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8b, 0x01,
	0x0a, 0x11, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6c, 0x6f, 0x73, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x5f, 0x61,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x6f, 0x73,
	0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x51, 0x0a, 0x03, 0x41,
	0x73, 0x6b, 0x12, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x72,
	0x0a, 0x0e, 0x52, 0x65, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72,
	0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x5f,
	0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70,
	0x68, 0x72, 0x61, 0x73, 0x65, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69,
	0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x69, 0x0a, 0x0b, 0x41, 0x64, 0x6f, 0x70, 0x74, 0x41, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69,
	0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64,
	0x6f, 0x70, 0x74, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x5f, 0x61,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6f, 0x70,
	0x74, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x66, 0x0a,
	0x0a, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x6e, 0x73, 0x77,
	0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x86, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x5f, 0x61,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5f, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69,
	0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x69,
	0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescOnce sync.Once
	file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescData = file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDesc
)

func file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescGZIP() []byte {
	file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescOnce.Do(func() {
		file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescData)
	})
	return file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDescData
}

var file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_moego_api_ai_assistant_v1_business_conversation_api_proto_goTypes = []interface{}{
	(*ConversationMassTextScenario)(nil),       // 0: moego.api.ai_assistant.v1.ConversationMassTextScenario
	(*ConversationTwoWayMessageScenario)(nil),  // 1: moego.api.ai_assistant.v1.ConversationTwoWayMessageScenario
	(*ConversationMarketingEmailScenario)(nil), // 2: moego.api.ai_assistant.v1.ConversationMarketingEmailScenario
	(*CreateBusinessConversationParams)(nil),   // 3: moego.api.ai_assistant.v1.CreateBusinessConversationParams
	(*CreateBusinessConversationResult)(nil),   // 4: moego.api.ai_assistant.v1.CreateBusinessConversationResult
	(*CloseBusinessConversationParams)(nil),    // 5: moego.api.ai_assistant.v1.CloseBusinessConversationParams
	(*CloseBusinessConversationResult)(nil),    // 6: moego.api.ai_assistant.v1.CloseBusinessConversationResult
	(*AskParams)(nil),                          // 7: moego.api.ai_assistant.v1.AskParams
	(*AskResult)(nil),                          // 8: moego.api.ai_assistant.v1.AskResult
	(*RephraseAnswerParams)(nil),               // 9: moego.api.ai_assistant.v1.RephraseAnswerParams
	(*RephraseAnswerResult)(nil),               // 10: moego.api.ai_assistant.v1.RephraseAnswerResult
	(*AdoptAnswerParams)(nil),                  // 11: moego.api.ai_assistant.v1.AdoptAnswerParams
	(*AdoptAnswerResult)(nil),                  // 12: moego.api.ai_assistant.v1.AdoptAnswerResult
	(*SendAnswerParams)(nil),                   // 13: moego.api.ai_assistant.v1.SendAnswerParams
	(*SendAnswerResult)(nil),                   // 14: moego.api.ai_assistant.v1.SendAnswerResult
}
var file_moego_api_ai_assistant_v1_business_conversation_api_proto_depIdxs = []int32{
	0,  // 0: moego.api.ai_assistant.v1.CreateBusinessConversationParams.mass_text:type_name -> moego.api.ai_assistant.v1.ConversationMassTextScenario
	1,  // 1: moego.api.ai_assistant.v1.CreateBusinessConversationParams.two_way_message:type_name -> moego.api.ai_assistant.v1.ConversationTwoWayMessageScenario
	2,  // 2: moego.api.ai_assistant.v1.CreateBusinessConversationParams.marketing_email:type_name -> moego.api.ai_assistant.v1.ConversationMarketingEmailScenario
	3,  // 3: moego.api.ai_assistant.v1.BusinessConversationService.CreateConversation:input_type -> moego.api.ai_assistant.v1.CreateBusinessConversationParams
	5,  // 4: moego.api.ai_assistant.v1.BusinessConversationService.CloseConversation:input_type -> moego.api.ai_assistant.v1.CloseBusinessConversationParams
	7,  // 5: moego.api.ai_assistant.v1.BusinessConversationService.Ask:input_type -> moego.api.ai_assistant.v1.AskParams
	9,  // 6: moego.api.ai_assistant.v1.BusinessConversationService.RephraseAnswer:input_type -> moego.api.ai_assistant.v1.RephraseAnswerParams
	11, // 7: moego.api.ai_assistant.v1.BusinessConversationService.AdoptAnswer:input_type -> moego.api.ai_assistant.v1.AdoptAnswerParams
	13, // 8: moego.api.ai_assistant.v1.BusinessConversationService.SendAnswer:input_type -> moego.api.ai_assistant.v1.SendAnswerParams
	4,  // 9: moego.api.ai_assistant.v1.BusinessConversationService.CreateConversation:output_type -> moego.api.ai_assistant.v1.CreateBusinessConversationResult
	6,  // 10: moego.api.ai_assistant.v1.BusinessConversationService.CloseConversation:output_type -> moego.api.ai_assistant.v1.CloseBusinessConversationResult
	8,  // 11: moego.api.ai_assistant.v1.BusinessConversationService.Ask:output_type -> moego.api.ai_assistant.v1.AskResult
	10, // 12: moego.api.ai_assistant.v1.BusinessConversationService.RephraseAnswer:output_type -> moego.api.ai_assistant.v1.RephraseAnswerResult
	12, // 13: moego.api.ai_assistant.v1.BusinessConversationService.AdoptAnswer:output_type -> moego.api.ai_assistant.v1.AdoptAnswerResult
	14, // 14: moego.api.ai_assistant.v1.BusinessConversationService.SendAnswer:output_type -> moego.api.ai_assistant.v1.SendAnswerResult
	9,  // [9:15] is the sub-list for method output_type
	3,  // [3:9] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_moego_api_ai_assistant_v1_business_conversation_api_proto_init() }
func file_moego_api_ai_assistant_v1_business_conversation_api_proto_init() {
	if File_moego_api_ai_assistant_v1_business_conversation_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConversationMassTextScenario); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConversationTwoWayMessageScenario); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConversationMarketingEmailScenario); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBusinessConversationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBusinessConversationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseBusinessConversationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseBusinessConversationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AskParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AskResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RephraseAnswerParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RephraseAnswerResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdoptAnswerParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdoptAnswerResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendAnswerParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendAnswerResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*CreateBusinessConversationParams_MassText)(nil),
		(*CreateBusinessConversationParams_TwoWayMessage)(nil),
		(*CreateBusinessConversationParams_MarketingEmail)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_ai_assistant_v1_business_conversation_api_proto_goTypes,
		DependencyIndexes: file_moego_api_ai_assistant_v1_business_conversation_api_proto_depIdxs,
		MessageInfos:      file_moego_api_ai_assistant_v1_business_conversation_api_proto_msgTypes,
	}.Build()
	File_moego_api_ai_assistant_v1_business_conversation_api_proto = out.File
	file_moego_api_ai_assistant_v1_business_conversation_api_proto_rawDesc = nil
	file_moego_api_ai_assistant_v1_business_conversation_api_proto_goTypes = nil
	file_moego_api_ai_assistant_v1_business_conversation_api_proto_depIdxs = nil
}
