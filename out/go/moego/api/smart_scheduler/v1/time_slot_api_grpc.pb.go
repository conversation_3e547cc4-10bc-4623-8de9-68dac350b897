// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/smart_scheduler/v1/time_slot_api.proto

package smartschedulerapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// TimeSlotServiceClient is the client API for TimeSlotService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TimeSlotServiceClient interface {
	// list time_slot
	ListAvailableTimeSlots(ctx context.Context, in *ListTimeSlotsParams, opts ...grpc.CallOption) (*ListTimeSlotsResult, error)
}

type timeSlotServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTimeSlotServiceClient(cc grpc.ClientConnInterface) TimeSlotServiceClient {
	return &timeSlotServiceClient{cc}
}

func (c *timeSlotServiceClient) ListAvailableTimeSlots(ctx context.Context, in *ListTimeSlotsParams, opts ...grpc.CallOption) (*ListTimeSlotsResult, error) {
	out := new(ListTimeSlotsResult)
	err := c.cc.Invoke(ctx, "/moego.api.smart_scheduler.v1.TimeSlotService/ListAvailableTimeSlots", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TimeSlotServiceServer is the server API for TimeSlotService service.
// All implementations must embed UnimplementedTimeSlotServiceServer
// for forward compatibility
type TimeSlotServiceServer interface {
	// list time_slot
	ListAvailableTimeSlots(context.Context, *ListTimeSlotsParams) (*ListTimeSlotsResult, error)
	mustEmbedUnimplementedTimeSlotServiceServer()
}

// UnimplementedTimeSlotServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTimeSlotServiceServer struct {
}

func (UnimplementedTimeSlotServiceServer) ListAvailableTimeSlots(context.Context, *ListTimeSlotsParams) (*ListTimeSlotsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAvailableTimeSlots not implemented")
}
func (UnimplementedTimeSlotServiceServer) mustEmbedUnimplementedTimeSlotServiceServer() {}

// UnsafeTimeSlotServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TimeSlotServiceServer will
// result in compilation errors.
type UnsafeTimeSlotServiceServer interface {
	mustEmbedUnimplementedTimeSlotServiceServer()
}

func RegisterTimeSlotServiceServer(s grpc.ServiceRegistrar, srv TimeSlotServiceServer) {
	s.RegisterService(&TimeSlotService_ServiceDesc, srv)
}

func _TimeSlotService_ListAvailableTimeSlots_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTimeSlotsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeSlotServiceServer).ListAvailableTimeSlots(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.smart_scheduler.v1.TimeSlotService/ListAvailableTimeSlots",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeSlotServiceServer).ListAvailableTimeSlots(ctx, req.(*ListTimeSlotsParams))
	}
	return interceptor(ctx, in, info, handler)
}

// TimeSlotService_ServiceDesc is the grpc.ServiceDesc for TimeSlotService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TimeSlotService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.smart_scheduler.v1.TimeSlotService",
	HandlerType: (*TimeSlotServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListAvailableTimeSlots",
			Handler:    _TimeSlotService_ListAvailableTimeSlots_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/smart_scheduler/v1/time_slot_api.proto",
}
