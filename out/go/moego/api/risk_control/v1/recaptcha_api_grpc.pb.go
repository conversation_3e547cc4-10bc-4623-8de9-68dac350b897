// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/risk_control/v1/recaptcha_api.proto

package riskcontrolapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// RecaptchaServiceClient is the client API for RecaptchaService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RecaptchaServiceClient interface {
	// challenge
	Challenge(ctx context.Context, in *RecaptchaChallengeRequest, opts ...grpc.CallOption) (*RecaptchaChallengeResponse, error)
}

type recaptchaServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRecaptchaServiceClient(cc grpc.ClientConnInterface) RecaptchaServiceClient {
	return &recaptchaServiceClient{cc}
}

func (c *recaptchaServiceClient) Challenge(ctx context.Context, in *RecaptchaChallengeRequest, opts ...grpc.CallOption) (*RecaptchaChallengeResponse, error) {
	out := new(RecaptchaChallengeResponse)
	err := c.cc.Invoke(ctx, "/moego.api.risk_control.v1.RecaptchaService/Challenge", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RecaptchaServiceServer is the server API for RecaptchaService service.
// All implementations must embed UnimplementedRecaptchaServiceServer
// for forward compatibility
type RecaptchaServiceServer interface {
	// challenge
	Challenge(context.Context, *RecaptchaChallengeRequest) (*RecaptchaChallengeResponse, error)
	mustEmbedUnimplementedRecaptchaServiceServer()
}

// UnimplementedRecaptchaServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRecaptchaServiceServer struct {
}

func (UnimplementedRecaptchaServiceServer) Challenge(context.Context, *RecaptchaChallengeRequest) (*RecaptchaChallengeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Challenge not implemented")
}
func (UnimplementedRecaptchaServiceServer) mustEmbedUnimplementedRecaptchaServiceServer() {}

// UnsafeRecaptchaServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RecaptchaServiceServer will
// result in compilation errors.
type UnsafeRecaptchaServiceServer interface {
	mustEmbedUnimplementedRecaptchaServiceServer()
}

func RegisterRecaptchaServiceServer(s grpc.ServiceRegistrar, srv RecaptchaServiceServer) {
	s.RegisterService(&RecaptchaService_ServiceDesc, srv)
}

func _RecaptchaService_Challenge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecaptchaChallengeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecaptchaServiceServer).Challenge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.risk_control.v1.RecaptchaService/Challenge",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecaptchaServiceServer).Challenge(ctx, req.(*RecaptchaChallengeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RecaptchaService_ServiceDesc is the grpc.ServiceDesc for RecaptchaService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RecaptchaService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.risk_control.v1.RecaptchaService",
	HandlerType: (*RecaptchaServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Challenge",
			Handler:    _RecaptchaService_Challenge_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/risk_control/v1/recaptcha_api.proto",
}
