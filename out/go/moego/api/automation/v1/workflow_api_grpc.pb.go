// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/automation/v1/workflow_api.proto

package automationapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// WorkflowServiceClient is the client API for WorkflowService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WorkflowServiceClient interface {
	// GetWorkflowConfig
	GetWorkflowConfig(ctx context.Context, in *GetWorkflowConfigParams, opts ...grpc.CallOption) (*GetWorkflowConfigResult, error)
	// CreateWorkflow
	CreateWorkflow(ctx context.Context, in *CreateWorkflowParams, opts ...grpc.CallOption) (*CreateWorkflowResult, error)
	// UpdateWorkflowContent
	UpdateWorkflowContent(ctx context.Context, in *UpdateWorkflowContentParams, opts ...grpc.CallOption) (*UpdateWorkflowContentResult, error)
	// UpdateWorkflowInfo
	UpdateWorkflowInfo(ctx context.Context, in *UpdateWorkflowInfoParams, opts ...grpc.CallOption) (*UpdateWorkflowInfoResult, error)
	// ListWorkflowCategories
	ListWorkflowCategories(ctx context.Context, in *ListWorkflowCategoriesParams, opts ...grpc.CallOption) (*ListWorkflowCategoriesResult, error)
	// ListWorkflows
	ListWorkflows(ctx context.Context, in *ListWorkflowsParams, opts ...grpc.CallOption) (*ListWorkflowsResult, error)
	// ListEnterpriseWorkflows
	ListEnterpriseWorkflows(ctx context.Context, in *ListEnterpriseWorkflowsParams, opts ...grpc.CallOption) (*ListEnterpriseWorkflowsResult, error)
	// ListWorkflowRecords
	ListWorkflowRecords(ctx context.Context, in *ListWorkflowRecordsParams, opts ...grpc.CallOption) (*ListWorkflowRecordsResult, error)
	// ListWorkflowTemplates
	ListWorkflowTemplates(ctx context.Context, in *ListWorkflowTemplatesParams, opts ...grpc.CallOption) (*ListWorkflowTemplatesResult, error)
	// GetWorkflowInfo
	GetWorkflowInfo(ctx context.Context, in *GetWorkflowInfoParams, opts ...grpc.CallOption) (*GetWorkflowInfoResult, error)
	// GetWorkflowTemplateInfo
	GetWorkflowTemplateInfo(ctx context.Context, in *GetWorkflowTemplateInfoParams, opts ...grpc.CallOption) (*GetWorkflowTemplateInfoResult, error)
	// UpdateWorkflowSetting
	UpdateWorkflowSetting(ctx context.Context, in *UpdateWorkflowSettingParams, opts ...grpc.CallOption) (*UpdateWorkflowSettingResult, error)
	// GetWorkflowSetting
	GetWorkflowSetting(ctx context.Context, in *GetWorkflowSettingParams, opts ...grpc.CallOption) (*GetWorkflowSettingResult, error)
	// opt:CreateWorkflowTemplate
	CreateWorkflowTemplate(ctx context.Context, in *CreateWorkflowTemplateParams, opts ...grpc.CallOption) (*CreateWorkflowTemplateResult, error)
	// opt:FilterCustomer
	FilterCustomer(ctx context.Context, in *FilterCustomerParams, opts ...grpc.CallOption) (*FilterCustomerResult, error)
}

type workflowServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWorkflowServiceClient(cc grpc.ClientConnInterface) WorkflowServiceClient {
	return &workflowServiceClient{cc}
}

func (c *workflowServiceClient) GetWorkflowConfig(ctx context.Context, in *GetWorkflowConfigParams, opts ...grpc.CallOption) (*GetWorkflowConfigResult, error) {
	out := new(GetWorkflowConfigResult)
	err := c.cc.Invoke(ctx, "/moego.api.automation.v1.WorkflowService/GetWorkflowConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) CreateWorkflow(ctx context.Context, in *CreateWorkflowParams, opts ...grpc.CallOption) (*CreateWorkflowResult, error) {
	out := new(CreateWorkflowResult)
	err := c.cc.Invoke(ctx, "/moego.api.automation.v1.WorkflowService/CreateWorkflow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) UpdateWorkflowContent(ctx context.Context, in *UpdateWorkflowContentParams, opts ...grpc.CallOption) (*UpdateWorkflowContentResult, error) {
	out := new(UpdateWorkflowContentResult)
	err := c.cc.Invoke(ctx, "/moego.api.automation.v1.WorkflowService/UpdateWorkflowContent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) UpdateWorkflowInfo(ctx context.Context, in *UpdateWorkflowInfoParams, opts ...grpc.CallOption) (*UpdateWorkflowInfoResult, error) {
	out := new(UpdateWorkflowInfoResult)
	err := c.cc.Invoke(ctx, "/moego.api.automation.v1.WorkflowService/UpdateWorkflowInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) ListWorkflowCategories(ctx context.Context, in *ListWorkflowCategoriesParams, opts ...grpc.CallOption) (*ListWorkflowCategoriesResult, error) {
	out := new(ListWorkflowCategoriesResult)
	err := c.cc.Invoke(ctx, "/moego.api.automation.v1.WorkflowService/ListWorkflowCategories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) ListWorkflows(ctx context.Context, in *ListWorkflowsParams, opts ...grpc.CallOption) (*ListWorkflowsResult, error) {
	out := new(ListWorkflowsResult)
	err := c.cc.Invoke(ctx, "/moego.api.automation.v1.WorkflowService/ListWorkflows", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) ListEnterpriseWorkflows(ctx context.Context, in *ListEnterpriseWorkflowsParams, opts ...grpc.CallOption) (*ListEnterpriseWorkflowsResult, error) {
	out := new(ListEnterpriseWorkflowsResult)
	err := c.cc.Invoke(ctx, "/moego.api.automation.v1.WorkflowService/ListEnterpriseWorkflows", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) ListWorkflowRecords(ctx context.Context, in *ListWorkflowRecordsParams, opts ...grpc.CallOption) (*ListWorkflowRecordsResult, error) {
	out := new(ListWorkflowRecordsResult)
	err := c.cc.Invoke(ctx, "/moego.api.automation.v1.WorkflowService/ListWorkflowRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) ListWorkflowTemplates(ctx context.Context, in *ListWorkflowTemplatesParams, opts ...grpc.CallOption) (*ListWorkflowTemplatesResult, error) {
	out := new(ListWorkflowTemplatesResult)
	err := c.cc.Invoke(ctx, "/moego.api.automation.v1.WorkflowService/ListWorkflowTemplates", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) GetWorkflowInfo(ctx context.Context, in *GetWorkflowInfoParams, opts ...grpc.CallOption) (*GetWorkflowInfoResult, error) {
	out := new(GetWorkflowInfoResult)
	err := c.cc.Invoke(ctx, "/moego.api.automation.v1.WorkflowService/GetWorkflowInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) GetWorkflowTemplateInfo(ctx context.Context, in *GetWorkflowTemplateInfoParams, opts ...grpc.CallOption) (*GetWorkflowTemplateInfoResult, error) {
	out := new(GetWorkflowTemplateInfoResult)
	err := c.cc.Invoke(ctx, "/moego.api.automation.v1.WorkflowService/GetWorkflowTemplateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) UpdateWorkflowSetting(ctx context.Context, in *UpdateWorkflowSettingParams, opts ...grpc.CallOption) (*UpdateWorkflowSettingResult, error) {
	out := new(UpdateWorkflowSettingResult)
	err := c.cc.Invoke(ctx, "/moego.api.automation.v1.WorkflowService/UpdateWorkflowSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) GetWorkflowSetting(ctx context.Context, in *GetWorkflowSettingParams, opts ...grpc.CallOption) (*GetWorkflowSettingResult, error) {
	out := new(GetWorkflowSettingResult)
	err := c.cc.Invoke(ctx, "/moego.api.automation.v1.WorkflowService/GetWorkflowSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) CreateWorkflowTemplate(ctx context.Context, in *CreateWorkflowTemplateParams, opts ...grpc.CallOption) (*CreateWorkflowTemplateResult, error) {
	out := new(CreateWorkflowTemplateResult)
	err := c.cc.Invoke(ctx, "/moego.api.automation.v1.WorkflowService/CreateWorkflowTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) FilterCustomer(ctx context.Context, in *FilterCustomerParams, opts ...grpc.CallOption) (*FilterCustomerResult, error) {
	out := new(FilterCustomerResult)
	err := c.cc.Invoke(ctx, "/moego.api.automation.v1.WorkflowService/FilterCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WorkflowServiceServer is the server API for WorkflowService service.
// All implementations must embed UnimplementedWorkflowServiceServer
// for forward compatibility
type WorkflowServiceServer interface {
	// GetWorkflowConfig
	GetWorkflowConfig(context.Context, *GetWorkflowConfigParams) (*GetWorkflowConfigResult, error)
	// CreateWorkflow
	CreateWorkflow(context.Context, *CreateWorkflowParams) (*CreateWorkflowResult, error)
	// UpdateWorkflowContent
	UpdateWorkflowContent(context.Context, *UpdateWorkflowContentParams) (*UpdateWorkflowContentResult, error)
	// UpdateWorkflowInfo
	UpdateWorkflowInfo(context.Context, *UpdateWorkflowInfoParams) (*UpdateWorkflowInfoResult, error)
	// ListWorkflowCategories
	ListWorkflowCategories(context.Context, *ListWorkflowCategoriesParams) (*ListWorkflowCategoriesResult, error)
	// ListWorkflows
	ListWorkflows(context.Context, *ListWorkflowsParams) (*ListWorkflowsResult, error)
	// ListEnterpriseWorkflows
	ListEnterpriseWorkflows(context.Context, *ListEnterpriseWorkflowsParams) (*ListEnterpriseWorkflowsResult, error)
	// ListWorkflowRecords
	ListWorkflowRecords(context.Context, *ListWorkflowRecordsParams) (*ListWorkflowRecordsResult, error)
	// ListWorkflowTemplates
	ListWorkflowTemplates(context.Context, *ListWorkflowTemplatesParams) (*ListWorkflowTemplatesResult, error)
	// GetWorkflowInfo
	GetWorkflowInfo(context.Context, *GetWorkflowInfoParams) (*GetWorkflowInfoResult, error)
	// GetWorkflowTemplateInfo
	GetWorkflowTemplateInfo(context.Context, *GetWorkflowTemplateInfoParams) (*GetWorkflowTemplateInfoResult, error)
	// UpdateWorkflowSetting
	UpdateWorkflowSetting(context.Context, *UpdateWorkflowSettingParams) (*UpdateWorkflowSettingResult, error)
	// GetWorkflowSetting
	GetWorkflowSetting(context.Context, *GetWorkflowSettingParams) (*GetWorkflowSettingResult, error)
	// opt:CreateWorkflowTemplate
	CreateWorkflowTemplate(context.Context, *CreateWorkflowTemplateParams) (*CreateWorkflowTemplateResult, error)
	// opt:FilterCustomer
	FilterCustomer(context.Context, *FilterCustomerParams) (*FilterCustomerResult, error)
	mustEmbedUnimplementedWorkflowServiceServer()
}

// UnimplementedWorkflowServiceServer must be embedded to have forward compatible implementations.
type UnimplementedWorkflowServiceServer struct {
}

func (UnimplementedWorkflowServiceServer) GetWorkflowConfig(context.Context, *GetWorkflowConfigParams) (*GetWorkflowConfigResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkflowConfig not implemented")
}
func (UnimplementedWorkflowServiceServer) CreateWorkflow(context.Context, *CreateWorkflowParams) (*CreateWorkflowResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWorkflow not implemented")
}
func (UnimplementedWorkflowServiceServer) UpdateWorkflowContent(context.Context, *UpdateWorkflowContentParams) (*UpdateWorkflowContentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWorkflowContent not implemented")
}
func (UnimplementedWorkflowServiceServer) UpdateWorkflowInfo(context.Context, *UpdateWorkflowInfoParams) (*UpdateWorkflowInfoResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWorkflowInfo not implemented")
}
func (UnimplementedWorkflowServiceServer) ListWorkflowCategories(context.Context, *ListWorkflowCategoriesParams) (*ListWorkflowCategoriesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkflowCategories not implemented")
}
func (UnimplementedWorkflowServiceServer) ListWorkflows(context.Context, *ListWorkflowsParams) (*ListWorkflowsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkflows not implemented")
}
func (UnimplementedWorkflowServiceServer) ListEnterpriseWorkflows(context.Context, *ListEnterpriseWorkflowsParams) (*ListEnterpriseWorkflowsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnterpriseWorkflows not implemented")
}
func (UnimplementedWorkflowServiceServer) ListWorkflowRecords(context.Context, *ListWorkflowRecordsParams) (*ListWorkflowRecordsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkflowRecords not implemented")
}
func (UnimplementedWorkflowServiceServer) ListWorkflowTemplates(context.Context, *ListWorkflowTemplatesParams) (*ListWorkflowTemplatesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkflowTemplates not implemented")
}
func (UnimplementedWorkflowServiceServer) GetWorkflowInfo(context.Context, *GetWorkflowInfoParams) (*GetWorkflowInfoResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkflowInfo not implemented")
}
func (UnimplementedWorkflowServiceServer) GetWorkflowTemplateInfo(context.Context, *GetWorkflowTemplateInfoParams) (*GetWorkflowTemplateInfoResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkflowTemplateInfo not implemented")
}
func (UnimplementedWorkflowServiceServer) UpdateWorkflowSetting(context.Context, *UpdateWorkflowSettingParams) (*UpdateWorkflowSettingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWorkflowSetting not implemented")
}
func (UnimplementedWorkflowServiceServer) GetWorkflowSetting(context.Context, *GetWorkflowSettingParams) (*GetWorkflowSettingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkflowSetting not implemented")
}
func (UnimplementedWorkflowServiceServer) CreateWorkflowTemplate(context.Context, *CreateWorkflowTemplateParams) (*CreateWorkflowTemplateResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWorkflowTemplate not implemented")
}
func (UnimplementedWorkflowServiceServer) FilterCustomer(context.Context, *FilterCustomerParams) (*FilterCustomerResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FilterCustomer not implemented")
}
func (UnimplementedWorkflowServiceServer) mustEmbedUnimplementedWorkflowServiceServer() {}

// UnsafeWorkflowServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WorkflowServiceServer will
// result in compilation errors.
type UnsafeWorkflowServiceServer interface {
	mustEmbedUnimplementedWorkflowServiceServer()
}

func RegisterWorkflowServiceServer(s grpc.ServiceRegistrar, srv WorkflowServiceServer) {
	s.RegisterService(&WorkflowService_ServiceDesc, srv)
}

func _WorkflowService_GetWorkflowConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkflowConfigParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).GetWorkflowConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.automation.v1.WorkflowService/GetWorkflowConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).GetWorkflowConfig(ctx, req.(*GetWorkflowConfigParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_CreateWorkflow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorkflowParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).CreateWorkflow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.automation.v1.WorkflowService/CreateWorkflow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).CreateWorkflow(ctx, req.(*CreateWorkflowParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_UpdateWorkflowContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWorkflowContentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).UpdateWorkflowContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.automation.v1.WorkflowService/UpdateWorkflowContent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).UpdateWorkflowContent(ctx, req.(*UpdateWorkflowContentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_UpdateWorkflowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWorkflowInfoParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).UpdateWorkflowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.automation.v1.WorkflowService/UpdateWorkflowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).UpdateWorkflowInfo(ctx, req.(*UpdateWorkflowInfoParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_ListWorkflowCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkflowCategoriesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).ListWorkflowCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.automation.v1.WorkflowService/ListWorkflowCategories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).ListWorkflowCategories(ctx, req.(*ListWorkflowCategoriesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_ListWorkflows_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkflowsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).ListWorkflows(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.automation.v1.WorkflowService/ListWorkflows",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).ListWorkflows(ctx, req.(*ListWorkflowsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_ListEnterpriseWorkflows_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnterpriseWorkflowsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).ListEnterpriseWorkflows(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.automation.v1.WorkflowService/ListEnterpriseWorkflows",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).ListEnterpriseWorkflows(ctx, req.(*ListEnterpriseWorkflowsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_ListWorkflowRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkflowRecordsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).ListWorkflowRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.automation.v1.WorkflowService/ListWorkflowRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).ListWorkflowRecords(ctx, req.(*ListWorkflowRecordsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_ListWorkflowTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkflowTemplatesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).ListWorkflowTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.automation.v1.WorkflowService/ListWorkflowTemplates",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).ListWorkflowTemplates(ctx, req.(*ListWorkflowTemplatesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_GetWorkflowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkflowInfoParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).GetWorkflowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.automation.v1.WorkflowService/GetWorkflowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).GetWorkflowInfo(ctx, req.(*GetWorkflowInfoParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_GetWorkflowTemplateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkflowTemplateInfoParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).GetWorkflowTemplateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.automation.v1.WorkflowService/GetWorkflowTemplateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).GetWorkflowTemplateInfo(ctx, req.(*GetWorkflowTemplateInfoParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_UpdateWorkflowSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWorkflowSettingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).UpdateWorkflowSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.automation.v1.WorkflowService/UpdateWorkflowSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).UpdateWorkflowSetting(ctx, req.(*UpdateWorkflowSettingParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_GetWorkflowSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkflowSettingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).GetWorkflowSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.automation.v1.WorkflowService/GetWorkflowSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).GetWorkflowSetting(ctx, req.(*GetWorkflowSettingParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_CreateWorkflowTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorkflowTemplateParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).CreateWorkflowTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.automation.v1.WorkflowService/CreateWorkflowTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).CreateWorkflowTemplate(ctx, req.(*CreateWorkflowTemplateParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_FilterCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterCustomerParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).FilterCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.automation.v1.WorkflowService/FilterCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).FilterCustomer(ctx, req.(*FilterCustomerParams))
	}
	return interceptor(ctx, in, info, handler)
}

// WorkflowService_ServiceDesc is the grpc.ServiceDesc for WorkflowService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WorkflowService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.automation.v1.WorkflowService",
	HandlerType: (*WorkflowServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetWorkflowConfig",
			Handler:    _WorkflowService_GetWorkflowConfig_Handler,
		},
		{
			MethodName: "CreateWorkflow",
			Handler:    _WorkflowService_CreateWorkflow_Handler,
		},
		{
			MethodName: "UpdateWorkflowContent",
			Handler:    _WorkflowService_UpdateWorkflowContent_Handler,
		},
		{
			MethodName: "UpdateWorkflowInfo",
			Handler:    _WorkflowService_UpdateWorkflowInfo_Handler,
		},
		{
			MethodName: "ListWorkflowCategories",
			Handler:    _WorkflowService_ListWorkflowCategories_Handler,
		},
		{
			MethodName: "ListWorkflows",
			Handler:    _WorkflowService_ListWorkflows_Handler,
		},
		{
			MethodName: "ListEnterpriseWorkflows",
			Handler:    _WorkflowService_ListEnterpriseWorkflows_Handler,
		},
		{
			MethodName: "ListWorkflowRecords",
			Handler:    _WorkflowService_ListWorkflowRecords_Handler,
		},
		{
			MethodName: "ListWorkflowTemplates",
			Handler:    _WorkflowService_ListWorkflowTemplates_Handler,
		},
		{
			MethodName: "GetWorkflowInfo",
			Handler:    _WorkflowService_GetWorkflowInfo_Handler,
		},
		{
			MethodName: "GetWorkflowTemplateInfo",
			Handler:    _WorkflowService_GetWorkflowTemplateInfo_Handler,
		},
		{
			MethodName: "UpdateWorkflowSetting",
			Handler:    _WorkflowService_UpdateWorkflowSetting_Handler,
		},
		{
			MethodName: "GetWorkflowSetting",
			Handler:    _WorkflowService_GetWorkflowSetting_Handler,
		},
		{
			MethodName: "CreateWorkflowTemplate",
			Handler:    _WorkflowService_CreateWorkflowTemplate_Handler,
		},
		{
			MethodName: "FilterCustomer",
			Handler:    _WorkflowService_FilterCustomer_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/automation/v1/workflow_api.proto",
}
