// @since 2023-09-11 14:34:19
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/marketing/v1/discount_code_api.proto

package marketingapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/marketing/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// generate discount code response
type GenerateDiscountCodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount code
	DiscountCode string `protobuf:"bytes,1,opt,name=discount_code,json=discountCode,proto3" json:"discount_code,omitempty"`
}

func (x *GenerateDiscountCodeResponse) Reset() {
	*x = GenerateDiscountCodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateDiscountCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateDiscountCodeResponse) ProtoMessage() {}

func (x *GenerateDiscountCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateDiscountCodeResponse.ProtoReflect.Descriptor instead.
func (*GenerateDiscountCodeResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{0}
}

func (x *GenerateDiscountCodeResponse) GetDiscountCode() string {
	if x != nil {
		return x.DiscountCode
	}
	return ""
}

// check discount code request
type CheckDiscountCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// code
	DiscountCode string `protobuf:"bytes,1,opt,name=discount_code,json=discountCode,proto3" json:"discount_code,omitempty"`
}

func (x *CheckDiscountCodeRequest) Reset() {
	*x = CheckDiscountCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckDiscountCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDiscountCodeRequest) ProtoMessage() {}

func (x *CheckDiscountCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDiscountCodeRequest.ProtoReflect.Descriptor instead.
func (*CheckDiscountCodeRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{1}
}

func (x *CheckDiscountCodeRequest) GetDiscountCode() string {
	if x != nil {
		return x.DiscountCode
	}
	return ""
}

// check discount code response
type CheckDiscountCodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is duplicate
	IsDuplicate bool `protobuf:"varint,1,opt,name=is_duplicate,json=isDuplicate,proto3" json:"is_duplicate,omitempty"`
}

func (x *CheckDiscountCodeResponse) Reset() {
	*x = CheckDiscountCodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckDiscountCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDiscountCodeResponse) ProtoMessage() {}

func (x *CheckDiscountCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDiscountCodeResponse.ProtoReflect.Descriptor instead.
func (*CheckDiscountCodeResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{2}
}

func (x *CheckDiscountCodeResponse) GetIsDuplicate() bool {
	if x != nil {
		return x.IsDuplicate
	}
	return false
}

// create discount_code request
type CreateDiscountCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount code
	DiscountCode string `protobuf:"bytes,1,opt,name=discount_code,json=discountCode,proto3" json:"discount_code,omitempty"`
	// description
	Description *string `protobuf:"bytes,2,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// discount amount
	Amount float64 `protobuf:"fixed64,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// discount type
	Type v1.DiscountCodeType `protobuf:"varint,4,opt,name=type,proto3,enum=moego.models.marketing.v1.DiscountCodeType" json:"type,omitempty"`
	// start date
	StartDate string `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *string `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// allowed all thing
	AllowedAllThing bool `protobuf:"varint,7,opt,name=allowed_all_thing,json=allowedAllThing,proto3" json:"allowed_all_thing,omitempty"`
	// allowed all services
	AllowedAllServices bool `protobuf:"varint,8,opt,name=allowed_all_services,json=allowedAllServices,proto3" json:"allowed_all_services,omitempty"`
	// allowed services
	ServiceIds []int64 `protobuf:"varint,9,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// allowed add ons
	AddOnIds []int64 `protobuf:"varint,10,rep,packed,name=add_on_ids,json=addOnIds,proto3" json:"add_on_ids,omitempty"`
	// allowed all products
	AllowedAllProducts bool `protobuf:"varint,11,opt,name=allowed_all_products,json=allowedAllProducts,proto3" json:"allowed_all_products,omitempty"`
	// allowed products
	ProductIds []int64 `protobuf:"varint,12,rep,packed,name=product_ids,json=productIds,proto3" json:"product_ids,omitempty"`
	// allowed all clients
	AllowedAllClients bool `protobuf:"varint,13,opt,name=allowed_all_clients,json=allowedAllClients,proto3" json:"allowed_all_clients,omitempty"`
	// allowed new clients
	AllowedNewClients bool `protobuf:"varint,14,opt,name=allowed_new_clients,json=allowedNewClients,proto3" json:"allowed_new_clients,omitempty"`
	// clients group
	ClientsGroup string `protobuf:"bytes,15,opt,name=clients_group,json=clientsGroup,proto3" json:"clients_group,omitempty"`
	// allowed clients
	ClientIds []int64 `protobuf:"varint,16,rep,packed,name=client_ids,json=clientIds,proto3" json:"client_ids,omitempty"`
	// limit usage
	LimitUsage int32 `protobuf:"varint,17,opt,name=limit_usage,json=limitUsage,proto3" json:"limit_usage,omitempty"`
	// limit number per client
	LimitNumberPerClient int32 `protobuf:"varint,18,opt,name=limit_number_per_client,json=limitNumberPerClient,proto3" json:"limit_number_per_client,omitempty"`
	// limit budget
	LimitBudget int32 `protobuf:"varint,19,opt,name=limit_budget,json=limitBudget,proto3" json:"limit_budget,omitempty"`
	// auto apply association
	AutoApplyAssociation bool `protobuf:"varint,20,opt,name=auto_apply_association,json=autoApplyAssociation,proto3" json:"auto_apply_association,omitempty"`
	// enable online booking
	EnableOnlineBooking bool `protobuf:"varint,21,opt,name=enable_online_booking,json=enableOnlineBooking,proto3" json:"enable_online_booking,omitempty"`
	// allowed locations
	LocationIds []int64 `protobuf:"varint,22,rep,packed,name=location_ids,json=locationIds,proto3" json:"location_ids,omitempty"`
	// expiry_def
	ExpiryDef *v1.ExpiryDef `protobuf:"bytes,23,opt,name=expiry_def,json=expiryDef,proto3" json:"expiry_def,omitempty"`
}

func (x *CreateDiscountCodeRequest) Reset() {
	*x = CreateDiscountCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDiscountCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDiscountCodeRequest) ProtoMessage() {}

func (x *CreateDiscountCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDiscountCodeRequest.ProtoReflect.Descriptor instead.
func (*CreateDiscountCodeRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{3}
}

func (x *CreateDiscountCodeRequest) GetDiscountCode() string {
	if x != nil {
		return x.DiscountCode
	}
	return ""
}

func (x *CreateDiscountCodeRequest) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *CreateDiscountCodeRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CreateDiscountCodeRequest) GetType() v1.DiscountCodeType {
	if x != nil {
		return x.Type
	}
	return v1.DiscountCodeType(0)
}

func (x *CreateDiscountCodeRequest) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *CreateDiscountCodeRequest) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

func (x *CreateDiscountCodeRequest) GetAllowedAllThing() bool {
	if x != nil {
		return x.AllowedAllThing
	}
	return false
}

func (x *CreateDiscountCodeRequest) GetAllowedAllServices() bool {
	if x != nil {
		return x.AllowedAllServices
	}
	return false
}

func (x *CreateDiscountCodeRequest) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *CreateDiscountCodeRequest) GetAddOnIds() []int64 {
	if x != nil {
		return x.AddOnIds
	}
	return nil
}

func (x *CreateDiscountCodeRequest) GetAllowedAllProducts() bool {
	if x != nil {
		return x.AllowedAllProducts
	}
	return false
}

func (x *CreateDiscountCodeRequest) GetProductIds() []int64 {
	if x != nil {
		return x.ProductIds
	}
	return nil
}

func (x *CreateDiscountCodeRequest) GetAllowedAllClients() bool {
	if x != nil {
		return x.AllowedAllClients
	}
	return false
}

func (x *CreateDiscountCodeRequest) GetAllowedNewClients() bool {
	if x != nil {
		return x.AllowedNewClients
	}
	return false
}

func (x *CreateDiscountCodeRequest) GetClientsGroup() string {
	if x != nil {
		return x.ClientsGroup
	}
	return ""
}

func (x *CreateDiscountCodeRequest) GetClientIds() []int64 {
	if x != nil {
		return x.ClientIds
	}
	return nil
}

func (x *CreateDiscountCodeRequest) GetLimitUsage() int32 {
	if x != nil {
		return x.LimitUsage
	}
	return 0
}

func (x *CreateDiscountCodeRequest) GetLimitNumberPerClient() int32 {
	if x != nil {
		return x.LimitNumberPerClient
	}
	return 0
}

func (x *CreateDiscountCodeRequest) GetLimitBudget() int32 {
	if x != nil {
		return x.LimitBudget
	}
	return 0
}

func (x *CreateDiscountCodeRequest) GetAutoApplyAssociation() bool {
	if x != nil {
		return x.AutoApplyAssociation
	}
	return false
}

func (x *CreateDiscountCodeRequest) GetEnableOnlineBooking() bool {
	if x != nil {
		return x.EnableOnlineBooking
	}
	return false
}

func (x *CreateDiscountCodeRequest) GetLocationIds() []int64 {
	if x != nil {
		return x.LocationIds
	}
	return nil
}

func (x *CreateDiscountCodeRequest) GetExpiryDef() *v1.ExpiryDef {
	if x != nil {
		return x.ExpiryDef
	}
	return nil
}

// create discount code response
type CreateDiscountCodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateDiscountCodeResponse) Reset() {
	*x = CreateDiscountCodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDiscountCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDiscountCodeResponse) ProtoMessage() {}

func (x *CreateDiscountCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDiscountCodeResponse.ProtoReflect.Descriptor instead.
func (*CreateDiscountCodeResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{4}
}

func (x *CreateDiscountCodeResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// edit discount_code request
type EditDiscountCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount code
	DiscountCode string `protobuf:"bytes,1,opt,name=discount_code,json=discountCode,proto3" json:"discount_code,omitempty"`
	// description
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// discount amount
	Amount float64 `protobuf:"fixed64,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// discount type
	Type v1.DiscountCodeType `protobuf:"varint,4,opt,name=type,proto3,enum=moego.models.marketing.v1.DiscountCodeType" json:"type,omitempty"`
	// start date
	StartDate string `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date (blank string for no end date)
	EndDate string `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// allowed all thing
	AllowedAllThing bool `protobuf:"varint,7,opt,name=allowed_all_thing,json=allowedAllThing,proto3" json:"allowed_all_thing,omitempty"`
	// allowed all services
	AllowedAllServices bool `protobuf:"varint,8,opt,name=allowed_all_services,json=allowedAllServices,proto3" json:"allowed_all_services,omitempty"`
	// allowed services
	ServiceIds []int64 `protobuf:"varint,9,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// allowed add ons
	AddOnIds []int64 `protobuf:"varint,10,rep,packed,name=add_on_ids,json=addOnIds,proto3" json:"add_on_ids,omitempty"`
	// allowed all products
	AllowedAllProducts bool `protobuf:"varint,11,opt,name=allowed_all_products,json=allowedAllProducts,proto3" json:"allowed_all_products,omitempty"`
	// allowed products
	ProductIds []int64 `protobuf:"varint,12,rep,packed,name=product_ids,json=productIds,proto3" json:"product_ids,omitempty"`
	// allowed all clients
	AllowedAllClients bool `protobuf:"varint,13,opt,name=allowed_all_clients,json=allowedAllClients,proto3" json:"allowed_all_clients,omitempty"`
	// allowed new clients
	AllowedNewClients bool `protobuf:"varint,14,opt,name=allowed_new_clients,json=allowedNewClients,proto3" json:"allowed_new_clients,omitempty"`
	// clients group
	ClientsGroup string `protobuf:"bytes,15,opt,name=clients_group,json=clientsGroup,proto3" json:"clients_group,omitempty"`
	// allowed clients
	ClientIds []int64 `protobuf:"varint,16,rep,packed,name=client_ids,json=clientIds,proto3" json:"client_ids,omitempty"`
	// limit usage
	LimitUsage int32 `protobuf:"varint,17,opt,name=limit_usage,json=limitUsage,proto3" json:"limit_usage,omitempty"`
	// limit number per client
	LimitNumberPerClient int32 `protobuf:"varint,18,opt,name=limit_number_per_client,json=limitNumberPerClient,proto3" json:"limit_number_per_client,omitempty"`
	// limit budget
	LimitBudget int32 `protobuf:"varint,19,opt,name=limit_budget,json=limitBudget,proto3" json:"limit_budget,omitempty"`
	// auto apply association
	AutoApplyAssociation bool `protobuf:"varint,20,opt,name=auto_apply_association,json=autoApplyAssociation,proto3" json:"auto_apply_association,omitempty"`
	// enable online booking
	EnableOnlineBooking bool `protobuf:"varint,21,opt,name=enable_online_booking,json=enableOnlineBooking,proto3" json:"enable_online_booking,omitempty"`
	// unique id
	Id int64 `protobuf:"varint,22,opt,name=id,proto3" json:"id,omitempty"`
	// allowed locations
	LocationIds []int64 `protobuf:"varint,23,rep,packed,name=location_ids,json=locationIds,proto3" json:"location_ids,omitempty"`
	// expiry def
	ExpiryDef *v1.ExpiryDef `protobuf:"bytes,24,opt,name=expiry_def,json=expiryDef,proto3,oneof" json:"expiry_def,omitempty"`
}

func (x *EditDiscountCodeRequest) Reset() {
	*x = EditDiscountCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditDiscountCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditDiscountCodeRequest) ProtoMessage() {}

func (x *EditDiscountCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditDiscountCodeRequest.ProtoReflect.Descriptor instead.
func (*EditDiscountCodeRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{5}
}

func (x *EditDiscountCodeRequest) GetDiscountCode() string {
	if x != nil {
		return x.DiscountCode
	}
	return ""
}

func (x *EditDiscountCodeRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *EditDiscountCodeRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *EditDiscountCodeRequest) GetType() v1.DiscountCodeType {
	if x != nil {
		return x.Type
	}
	return v1.DiscountCodeType(0)
}

func (x *EditDiscountCodeRequest) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *EditDiscountCodeRequest) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *EditDiscountCodeRequest) GetAllowedAllThing() bool {
	if x != nil {
		return x.AllowedAllThing
	}
	return false
}

func (x *EditDiscountCodeRequest) GetAllowedAllServices() bool {
	if x != nil {
		return x.AllowedAllServices
	}
	return false
}

func (x *EditDiscountCodeRequest) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *EditDiscountCodeRequest) GetAddOnIds() []int64 {
	if x != nil {
		return x.AddOnIds
	}
	return nil
}

func (x *EditDiscountCodeRequest) GetAllowedAllProducts() bool {
	if x != nil {
		return x.AllowedAllProducts
	}
	return false
}

func (x *EditDiscountCodeRequest) GetProductIds() []int64 {
	if x != nil {
		return x.ProductIds
	}
	return nil
}

func (x *EditDiscountCodeRequest) GetAllowedAllClients() bool {
	if x != nil {
		return x.AllowedAllClients
	}
	return false
}

func (x *EditDiscountCodeRequest) GetAllowedNewClients() bool {
	if x != nil {
		return x.AllowedNewClients
	}
	return false
}

func (x *EditDiscountCodeRequest) GetClientsGroup() string {
	if x != nil {
		return x.ClientsGroup
	}
	return ""
}

func (x *EditDiscountCodeRequest) GetClientIds() []int64 {
	if x != nil {
		return x.ClientIds
	}
	return nil
}

func (x *EditDiscountCodeRequest) GetLimitUsage() int32 {
	if x != nil {
		return x.LimitUsage
	}
	return 0
}

func (x *EditDiscountCodeRequest) GetLimitNumberPerClient() int32 {
	if x != nil {
		return x.LimitNumberPerClient
	}
	return 0
}

func (x *EditDiscountCodeRequest) GetLimitBudget() int32 {
	if x != nil {
		return x.LimitBudget
	}
	return 0
}

func (x *EditDiscountCodeRequest) GetAutoApplyAssociation() bool {
	if x != nil {
		return x.AutoApplyAssociation
	}
	return false
}

func (x *EditDiscountCodeRequest) GetEnableOnlineBooking() bool {
	if x != nil {
		return x.EnableOnlineBooking
	}
	return false
}

func (x *EditDiscountCodeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EditDiscountCodeRequest) GetLocationIds() []int64 {
	if x != nil {
		return x.LocationIds
	}
	return nil
}

func (x *EditDiscountCodeRequest) GetExpiryDef() *v1.ExpiryDef {
	if x != nil {
		return x.ExpiryDef
	}
	return nil
}

// edit discount code response
type EditDiscountCodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *EditDiscountCodeResponse) Reset() {
	*x = EditDiscountCodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditDiscountCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditDiscountCodeResponse) ProtoMessage() {}

func (x *EditDiscountCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditDiscountCodeResponse.ProtoReflect.Descriptor instead.
func (*EditDiscountCodeResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{6}
}

func (x *EditDiscountCodeResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get discount_code request
type GetDiscountCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// code id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetDiscountCodeRequest) Reset() {
	*x = GetDiscountCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiscountCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiscountCodeRequest) ProtoMessage() {}

func (x *GetDiscountCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiscountCodeRequest.ProtoReflect.Descriptor instead.
func (*GetDiscountCodeRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetDiscountCodeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get discount_code response
type GetDiscountCodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount code model
	DiscountCodeModel *v1.DiscountCodeModel `protobuf:"bytes,1,opt,name=discount_code_model,json=discountCodeModel,proto3" json:"discount_code_model,omitempty"`
	// discount code summary
	DiscountCodeSummaryDef *v1.DiscountCodeSummaryDef `protobuf:"bytes,2,opt,name=discount_code_summary_def,json=discountCodeSummaryDef,proto3" json:"discount_code_summary_def,omitempty"`
}

func (x *GetDiscountCodeResponse) Reset() {
	*x = GetDiscountCodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiscountCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiscountCodeResponse) ProtoMessage() {}

func (x *GetDiscountCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiscountCodeResponse.ProtoReflect.Descriptor instead.
func (*GetDiscountCodeResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{8}
}

func (x *GetDiscountCodeResponse) GetDiscountCodeModel() *v1.DiscountCodeModel {
	if x != nil {
		return x.DiscountCodeModel
	}
	return nil
}

func (x *GetDiscountCodeResponse) GetDiscountCodeSummaryDef() *v1.DiscountCodeSummaryDef {
	if x != nil {
		return x.DiscountCodeSummaryDef
	}
	return nil
}

// get discount code request
type GetDiscountCodeListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination request
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// status
	Status []v1.DiscountCodeStatus `protobuf:"varint,2,rep,packed,name=status,proto3,enum=moego.models.marketing.v1.DiscountCodeStatus" json:"status,omitempty"`
	// code
	DiscountCode *string `protobuf:"bytes,3,opt,name=discount_code,json=discountCode,proto3,oneof" json:"discount_code,omitempty"`
}

func (x *GetDiscountCodeListRequest) Reset() {
	*x = GetDiscountCodeListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiscountCodeListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiscountCodeListRequest) ProtoMessage() {}

func (x *GetDiscountCodeListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiscountCodeListRequest.ProtoReflect.Descriptor instead.
func (*GetDiscountCodeListRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{9}
}

func (x *GetDiscountCodeListRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetDiscountCodeListRequest) GetStatus() []v1.DiscountCodeStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetDiscountCodeListRequest) GetDiscountCode() string {
	if x != nil && x.DiscountCode != nil {
		return *x.DiscountCode
	}
	return ""
}

// get discount code response
type GetDiscountCodeListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination response
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// discount code list
	DiscountCodeCompositeViews []*v1.DiscountCodeCompositeView `protobuf:"bytes,2,rep,name=discount_code_composite_views,json=discountCodeCompositeViews,proto3" json:"discount_code_composite_views,omitempty"`
}

func (x *GetDiscountCodeListResponse) Reset() {
	*x = GetDiscountCodeListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiscountCodeListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiscountCodeListResponse) ProtoMessage() {}

func (x *GetDiscountCodeListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiscountCodeListResponse.ProtoReflect.Descriptor instead.
func (*GetDiscountCodeListResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{10}
}

func (x *GetDiscountCodeListResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetDiscountCodeListResponse) GetDiscountCodeCompositeViews() []*v1.DiscountCodeCompositeView {
	if x != nil {
		return x.DiscountCodeCompositeViews
	}
	return nil
}

// get discount code log request
type GetDiscountCodeLogListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination request
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// code id
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetDiscountCodeLogListRequest) Reset() {
	*x = GetDiscountCodeLogListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiscountCodeLogListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiscountCodeLogListRequest) ProtoMessage() {}

func (x *GetDiscountCodeLogListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiscountCodeLogListRequest.ProtoReflect.Descriptor instead.
func (*GetDiscountCodeLogListRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{11}
}

func (x *GetDiscountCodeLogListRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetDiscountCodeLogListRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get discount code log response
type GetDiscountCodeLogListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination response
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// discount code log list
	DiscountCodeLogCompositeViews []*v1.DiscountCodeLogCompositeView `protobuf:"bytes,2,rep,name=discount_code_log_composite_views,json=discountCodeLogCompositeViews,proto3" json:"discount_code_log_composite_views,omitempty"`
}

func (x *GetDiscountCodeLogListResponse) Reset() {
	*x = GetDiscountCodeLogListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiscountCodeLogListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiscountCodeLogListResponse) ProtoMessage() {}

func (x *GetDiscountCodeLogListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiscountCodeLogListResponse.ProtoReflect.Descriptor instead.
func (*GetDiscountCodeLogListResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{12}
}

func (x *GetDiscountCodeLogListResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetDiscountCodeLogListResponse) GetDiscountCodeLogCompositeViews() []*v1.DiscountCodeLogCompositeView {
	if x != nil {
		return x.DiscountCodeLogCompositeViews
	}
	return nil
}

// get discount code log request
type GetDiscountCodeLogOverviewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// code id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetDiscountCodeLogOverviewRequest) Reset() {
	*x = GetDiscountCodeLogOverviewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiscountCodeLogOverviewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiscountCodeLogOverviewRequest) ProtoMessage() {}

func (x *GetDiscountCodeLogOverviewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiscountCodeLogOverviewRequest.ProtoReflect.Descriptor instead.
func (*GetDiscountCodeLogOverviewRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{13}
}

func (x *GetDiscountCodeLogOverviewRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get discount code log response
type GetDiscountCodeLogOverviewResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total usage
	TotalUsage int32 `protobuf:"varint,1,opt,name=total_usage,json=totalUsage,proto3" json:"total_usage,omitempty"`
	// total client
	TotalClient int32 `protobuf:"varint,2,opt,name=total_client,json=totalClient,proto3" json:"total_client,omitempty"`
	// discount sales
	DiscountSales float64 `protobuf:"fixed64,3,opt,name=discount_sales,json=discountSales,proto3" json:"discount_sales,omitempty"`
	// invoice sales
	InvoiceSales float64 `protobuf:"fixed64,4,opt,name=invoice_sales,json=invoiceSales,proto3" json:"invoice_sales,omitempty"`
}

func (x *GetDiscountCodeLogOverviewResponse) Reset() {
	*x = GetDiscountCodeLogOverviewResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiscountCodeLogOverviewResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiscountCodeLogOverviewResponse) ProtoMessage() {}

func (x *GetDiscountCodeLogOverviewResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiscountCodeLogOverviewResponse.ProtoReflect.Descriptor instead.
func (*GetDiscountCodeLogOverviewResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{14}
}

func (x *GetDiscountCodeLogOverviewResponse) GetTotalUsage() int32 {
	if x != nil {
		return x.TotalUsage
	}
	return 0
}

func (x *GetDiscountCodeLogOverviewResponse) GetTotalClient() int32 {
	if x != nil {
		return x.TotalClient
	}
	return 0
}

func (x *GetDiscountCodeLogOverviewResponse) GetDiscountSales() float64 {
	if x != nil {
		return x.DiscountSales
	}
	return 0
}

func (x *GetDiscountCodeLogOverviewResponse) GetInvoiceSales() float64 {
	if x != nil {
		return x.InvoiceSales
	}
	return 0
}

// archive discount code request
type ChangeStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// code id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// status
	Status v1.DiscountCodeStatus `protobuf:"varint,2,opt,name=status,proto3,enum=moego.models.marketing.v1.DiscountCodeStatus" json:"status,omitempty"`
	// expiry def,if change status to active, it may need to reset expiry time
	ExpiryDef *v1.ExpiryDef `protobuf:"bytes,3,opt,name=expiry_def,json=expiryDef,proto3,oneof" json:"expiry_def,omitempty"`
}

func (x *ChangeStatusRequest) Reset() {
	*x = ChangeStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeStatusRequest) ProtoMessage() {}

func (x *ChangeStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeStatusRequest.ProtoReflect.Descriptor instead.
func (*ChangeStatusRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{15}
}

func (x *ChangeStatusRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChangeStatusRequest) GetStatus() v1.DiscountCodeStatus {
	if x != nil {
		return x.Status
	}
	return v1.DiscountCodeStatus(0)
}

func (x *ChangeStatusRequest) GetExpiryDef() *v1.ExpiryDef {
	if x != nil {
		return x.ExpiryDef
	}
	return nil
}

// archive discount code request
type ChangeStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *ChangeStatusResponse) Reset() {
	*x = ChangeStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeStatusResponse) ProtoMessage() {}

func (x *ChangeStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeStatusResponse.ProtoReflect.Descriptor instead.
func (*ChangeStatusResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{16}
}

func (x *ChangeStatusResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// check discount code valid for customer request
type CheckDiscountCodeValidForCustomerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// code name
	CodeName string `protobuf:"bytes,1,opt,name=code_name,json=codeName,proto3" json:"code_name,omitempty"`
	// service ids
	ServiceIds []int64 `protobuf:"varint,2,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*CheckDiscountCodeValidForCustomerRequest_Name
	//	*CheckDiscountCodeValidForCustomerRequest_Domain
	Anonymous isCheckDiscountCodeValidForCustomerRequest_Anonymous `protobuf_oneof:"anonymous"`
	// appointment date
	AppointmentDate *string `protobuf:"bytes,5,opt,name=appointment_date,json=appointmentDate,proto3,oneof" json:"appointment_date,omitempty"`
}

func (x *CheckDiscountCodeValidForCustomerRequest) Reset() {
	*x = CheckDiscountCodeValidForCustomerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckDiscountCodeValidForCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDiscountCodeValidForCustomerRequest) ProtoMessage() {}

func (x *CheckDiscountCodeValidForCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDiscountCodeValidForCustomerRequest.ProtoReflect.Descriptor instead.
func (*CheckDiscountCodeValidForCustomerRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{17}
}

func (x *CheckDiscountCodeValidForCustomerRequest) GetCodeName() string {
	if x != nil {
		return x.CodeName
	}
	return ""
}

func (x *CheckDiscountCodeValidForCustomerRequest) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (m *CheckDiscountCodeValidForCustomerRequest) GetAnonymous() isCheckDiscountCodeValidForCustomerRequest_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *CheckDiscountCodeValidForCustomerRequest) GetName() string {
	if x, ok := x.GetAnonymous().(*CheckDiscountCodeValidForCustomerRequest_Name); ok {
		return x.Name
	}
	return ""
}

func (x *CheckDiscountCodeValidForCustomerRequest) GetDomain() string {
	if x, ok := x.GetAnonymous().(*CheckDiscountCodeValidForCustomerRequest_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *CheckDiscountCodeValidForCustomerRequest) GetAppointmentDate() string {
	if x != nil && x.AppointmentDate != nil {
		return *x.AppointmentDate
	}
	return ""
}

type isCheckDiscountCodeValidForCustomerRequest_Anonymous interface {
	isCheckDiscountCodeValidForCustomerRequest_Anonymous()
}

type CheckDiscountCodeValidForCustomerRequest_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,3,opt,name=name,proto3,oneof"`
}

type CheckDiscountCodeValidForCustomerRequest_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,4,opt,name=domain,proto3,oneof"`
}

func (*CheckDiscountCodeValidForCustomerRequest_Name) isCheckDiscountCodeValidForCustomerRequest_Anonymous() {
}

func (*CheckDiscountCodeValidForCustomerRequest_Domain) isCheckDiscountCodeValidForCustomerRequest_Anonymous() {
}

// check discount code valid for customer response
type CheckDiscountCodeValidForCustomerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount code model online booking view
	DiscountCodeModelOnlineBookingView *v1.DiscountCodeModelOnlineBookingView `protobuf:"bytes,1,opt,name=discount_code_model_online_booking_view,json=discountCodeModelOnlineBookingView,proto3" json:"discount_code_model_online_booking_view,omitempty"`
}

func (x *CheckDiscountCodeValidForCustomerResponse) Reset() {
	*x = CheckDiscountCodeValidForCustomerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckDiscountCodeValidForCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDiscountCodeValidForCustomerResponse) ProtoMessage() {}

func (x *CheckDiscountCodeValidForCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDiscountCodeValidForCustomerResponse.ProtoReflect.Descriptor instead.
func (*CheckDiscountCodeValidForCustomerResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{18}
}

func (x *CheckDiscountCodeValidForCustomerResponse) GetDiscountCodeModelOnlineBookingView() *v1.DiscountCodeModelOnlineBookingView {
	if x != nil {
		return x.DiscountCodeModelOnlineBookingView
	}
	return nil
}

// get business discount code config request
type GetBusinessDiscountCodeConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetBusinessDiscountCodeConfigRequest_Name
	//	*GetBusinessDiscountCodeConfigRequest_Domain
	Anonymous isGetBusinessDiscountCodeConfigRequest_Anonymous `protobuf_oneof:"anonymous"`
}

func (x *GetBusinessDiscountCodeConfigRequest) Reset() {
	*x = GetBusinessDiscountCodeConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessDiscountCodeConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessDiscountCodeConfigRequest) ProtoMessage() {}

func (x *GetBusinessDiscountCodeConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessDiscountCodeConfigRequest.ProtoReflect.Descriptor instead.
func (*GetBusinessDiscountCodeConfigRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{19}
}

func (m *GetBusinessDiscountCodeConfigRequest) GetAnonymous() isGetBusinessDiscountCodeConfigRequest_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetBusinessDiscountCodeConfigRequest) GetName() string {
	if x, ok := x.GetAnonymous().(*GetBusinessDiscountCodeConfigRequest_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetBusinessDiscountCodeConfigRequest) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetBusinessDiscountCodeConfigRequest_Domain); ok {
		return x.Domain
	}
	return ""
}

type isGetBusinessDiscountCodeConfigRequest_Anonymous interface {
	isGetBusinessDiscountCodeConfigRequest_Anonymous()
}

type GetBusinessDiscountCodeConfigRequest_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetBusinessDiscountCodeConfigRequest_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetBusinessDiscountCodeConfigRequest_Name) isGetBusinessDiscountCodeConfigRequest_Anonymous() {
}

func (*GetBusinessDiscountCodeConfigRequest_Domain) isGetBusinessDiscountCodeConfigRequest_Anonymous() {
}

// get business discount code config response
type GetBusinessDiscountCodeConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business has valid discount code
	HasValidDiscountCode bool `protobuf:"varint,1,opt,name=has_valid_discount_code,json=hasValidDiscountCode,proto3" json:"has_valid_discount_code,omitempty"`
}

func (x *GetBusinessDiscountCodeConfigResponse) Reset() {
	*x = GetBusinessDiscountCodeConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessDiscountCodeConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessDiscountCodeConfigResponse) ProtoMessage() {}

func (x *GetBusinessDiscountCodeConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessDiscountCodeConfigResponse.ProtoReflect.Descriptor instead.
func (*GetBusinessDiscountCodeConfigResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{20}
}

func (x *GetBusinessDiscountCodeConfigResponse) GetHasValidDiscountCode() bool {
	if x != nil {
		return x.HasValidDiscountCode
	}
	return false
}

var File_moego_api_marketing_v1_discount_code_api_proto protoreflect.FileDescriptor

var file_moego_api_marketing_v1_discount_code_api_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x16, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x64,
	0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x43, 0x0a, 0x1c, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x4a, 0x0a, 0x18,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x14, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x3e, 0x0a, 0x19, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x64, 0x75, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x44,
	0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x22, 0xa2, 0x09, 0x0a, 0x19, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2f, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0xc8, 0x01, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x12, 0x12, 0x11, 0x00,
	0x00, 0x80, 0xff, 0x64, 0xcd, 0xcd, 0x41, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15,
	0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c,
	0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x3a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34,
	0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x01,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x11,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x68, 0x69, 0x6e,
	0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64,
	0x41, 0x6c, 0x6c, 0x54, 0x68, 0x69, 0x6e, 0x67, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41,
	0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x0a, 0x61,
	0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x08, 0x61, 0x64, 0x64, 0x4f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64,
	0x41, 0x6c, 0x6c, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x73, 0x12, 0x2e, 0x0a, 0x13,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x41, 0x6c, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2e, 0x0a, 0x13,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x77, 0x5f, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x4e, 0x65, 0x77, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2f, 0x0a, 0x0d,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0x90, 0x4e, 0x52,
	0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x0b,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x1a, 0x06, 0x10, 0xc0, 0x84, 0x3d, 0x28, 0x00, 0x52, 0x0a,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x42, 0x0a, 0x17, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08,
	0x1a, 0x06, 0x10, 0xc0, 0x84, 0x3d, 0x28, 0x00, 0x52, 0x14, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x50, 0x65, 0x72, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x2e,
	0x0a, 0x0c, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x1a, 0x06, 0x10, 0xc0, 0x84, 0x3d, 0x28,
	0x00, 0x52, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x75, 0x64, 0x67, 0x65, 0x74, 0x12, 0x34,
	0x0a, 0x16, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x61, 0x73, 0x73,
	0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14,
	0x61, 0x75, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x43, 0x0a, 0x0a, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x69,
	0x72, 0x79, 0x44, 0x65, 0x66, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x65, 0x66,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0x2c, 0x0a,
	0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x81, 0x09, 0x0a, 0x17,
	0x45, 0x64, 0x69, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x14, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x72, 0x03, 0x18, 0xc8, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x01, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x12, 0x12, 0x11, 0x00, 0x00, 0x80, 0xff, 0x64,
	0xcd, 0xcd, 0x41, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c,
	0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x24, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x68, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6c, 0x6c, 0x54, 0x68,
	0x69, 0x6e, 0x67, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x61,
	0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6c, 0x6c, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x0a, 0x61, 0x64, 0x64, 0x5f, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x61, 0x64, 0x64, 0x4f,
	0x6e, 0x49, 0x64, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f,
	0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6c, 0x6c, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x41, 0x6c, 0x6c,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x5f, 0x6e, 0x65, 0x77, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x4e, 0x65, 0x77,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2f, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x73, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0x90, 0x4e, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42,
	0x08, 0x1a, 0x06, 0x10, 0xc0, 0x84, 0x3d, 0x28, 0x00, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x42, 0x0a, 0x17, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x1a, 0x06, 0x10, 0xc0, 0x84,
	0x3d, 0x28, 0x00, 0x52, 0x14, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x50, 0x65, 0x72, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x0c, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x0b, 0xfa, 0x42, 0x08, 0x1a, 0x06, 0x10, 0xc0, 0x84, 0x3d, 0x28, 0x00, 0x52, 0x0b, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x42, 0x75, 0x64, 0x67, 0x65, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x41,
	0x70, 0x70, 0x6c, 0x79, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x32, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x48, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79,
	0x5f, 0x64, 0x65, 0x66, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x65, 0x66,
	0x48, 0x00, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x65, 0x66, 0x88, 0x01, 0x01,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x22,
	0x2a, 0x0a, 0x18, 0x45, 0x64, 0x69, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x31, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0xe5,
	0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x13, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x11, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x6c, 0x0a, 0x19, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x44, 0x65, 0x66, 0x52, 0x16,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x44, 0x65, 0x66, 0x22, 0xff, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x4f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x31, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x18, 0x14, 0x48, 0x00, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xda, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x77, 0x0a, 0x1d,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x1a, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65,
	0x56, 0x69, 0x65, 0x77, 0x73, 0x22, 0x85, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0xe8, 0x01,
	0x0a, 0x1e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x81, 0x01, 0x0a, 0x21, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x65, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x1d, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x73, 0x22, 0x3c, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x67, 0x4f, 0x76,
	0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0xb4, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x67, 0x4f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x61,
	0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0c, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x22, 0xda, 0x01,
	0x0a, 0x13, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x51,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x48, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x09, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x65, 0x66, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x22, 0x30, 0x0a, 0x14, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x94, 0x02, 0x0a,
	0x28, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x46, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x09, 0x63, 0x6f, 0x64,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x18, 0x14, 0x52, 0x08, 0x63, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73,
	0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x12, 0x4a, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72,
	0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d,
	0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x01, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x09,
	0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x13,
	0x0a, 0x11, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x22, 0xc0, 0x01, 0x0a, 0x29, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x46, 0x6f,
	0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x92, 0x01, 0x0a, 0x27, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x22, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x22, 0x7c, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22,
	0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12,
	0x03, 0xf8, 0x42, 0x01, 0x22, 0x5e, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a,
	0x17, 0x68, 0x61, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14,
	0x68, 0x61, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x32, 0xb2, 0x0b, 0x0a, 0x13, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x64, 0x0a, 0x14,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x78, 0x0a, 0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x12,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a, 0x10, 0x45, 0x64, 0x69,
	0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x72, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x32, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x76,
	0x69, 0x65, 0x77, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x67, 0x4f,
	0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69,
	0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f,
	0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f,
	0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x0c, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0xa8, 0x01, 0x0a, 0x21, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x46, 0x6f, 0x72, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x46, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x46, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9c, 0x01, 0x0a, 0x1d, 0x47,
	0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x7e, 0x0a, 0x1e, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_api_marketing_v1_discount_code_api_proto_rawDescOnce sync.Once
	file_moego_api_marketing_v1_discount_code_api_proto_rawDescData = file_moego_api_marketing_v1_discount_code_api_proto_rawDesc
)

func file_moego_api_marketing_v1_discount_code_api_proto_rawDescGZIP() []byte {
	file_moego_api_marketing_v1_discount_code_api_proto_rawDescOnce.Do(func() {
		file_moego_api_marketing_v1_discount_code_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_marketing_v1_discount_code_api_proto_rawDescData)
	})
	return file_moego_api_marketing_v1_discount_code_api_proto_rawDescData
}

var file_moego_api_marketing_v1_discount_code_api_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_moego_api_marketing_v1_discount_code_api_proto_goTypes = []interface{}{
	(*GenerateDiscountCodeResponse)(nil),              // 0: moego.api.marketing.v1.GenerateDiscountCodeResponse
	(*CheckDiscountCodeRequest)(nil),                  // 1: moego.api.marketing.v1.CheckDiscountCodeRequest
	(*CheckDiscountCodeResponse)(nil),                 // 2: moego.api.marketing.v1.CheckDiscountCodeResponse
	(*CreateDiscountCodeRequest)(nil),                 // 3: moego.api.marketing.v1.CreateDiscountCodeRequest
	(*CreateDiscountCodeResponse)(nil),                // 4: moego.api.marketing.v1.CreateDiscountCodeResponse
	(*EditDiscountCodeRequest)(nil),                   // 5: moego.api.marketing.v1.EditDiscountCodeRequest
	(*EditDiscountCodeResponse)(nil),                  // 6: moego.api.marketing.v1.EditDiscountCodeResponse
	(*GetDiscountCodeRequest)(nil),                    // 7: moego.api.marketing.v1.GetDiscountCodeRequest
	(*GetDiscountCodeResponse)(nil),                   // 8: moego.api.marketing.v1.GetDiscountCodeResponse
	(*GetDiscountCodeListRequest)(nil),                // 9: moego.api.marketing.v1.GetDiscountCodeListRequest
	(*GetDiscountCodeListResponse)(nil),               // 10: moego.api.marketing.v1.GetDiscountCodeListResponse
	(*GetDiscountCodeLogListRequest)(nil),             // 11: moego.api.marketing.v1.GetDiscountCodeLogListRequest
	(*GetDiscountCodeLogListResponse)(nil),            // 12: moego.api.marketing.v1.GetDiscountCodeLogListResponse
	(*GetDiscountCodeLogOverviewRequest)(nil),         // 13: moego.api.marketing.v1.GetDiscountCodeLogOverviewRequest
	(*GetDiscountCodeLogOverviewResponse)(nil),        // 14: moego.api.marketing.v1.GetDiscountCodeLogOverviewResponse
	(*ChangeStatusRequest)(nil),                       // 15: moego.api.marketing.v1.ChangeStatusRequest
	(*ChangeStatusResponse)(nil),                      // 16: moego.api.marketing.v1.ChangeStatusResponse
	(*CheckDiscountCodeValidForCustomerRequest)(nil),  // 17: moego.api.marketing.v1.CheckDiscountCodeValidForCustomerRequest
	(*CheckDiscountCodeValidForCustomerResponse)(nil), // 18: moego.api.marketing.v1.CheckDiscountCodeValidForCustomerResponse
	(*GetBusinessDiscountCodeConfigRequest)(nil),      // 19: moego.api.marketing.v1.GetBusinessDiscountCodeConfigRequest
	(*GetBusinessDiscountCodeConfigResponse)(nil),     // 20: moego.api.marketing.v1.GetBusinessDiscountCodeConfigResponse
	(v1.DiscountCodeType)(0),                          // 21: moego.models.marketing.v1.DiscountCodeType
	(*v1.ExpiryDef)(nil),                              // 22: moego.models.marketing.v1.ExpiryDef
	(*v1.DiscountCodeModel)(nil),                      // 23: moego.models.marketing.v1.DiscountCodeModel
	(*v1.DiscountCodeSummaryDef)(nil),                 // 24: moego.models.marketing.v1.DiscountCodeSummaryDef
	(*v2.PaginationRequest)(nil),                      // 25: moego.utils.v2.PaginationRequest
	(v1.DiscountCodeStatus)(0),                        // 26: moego.models.marketing.v1.DiscountCodeStatus
	(*v2.PaginationResponse)(nil),                     // 27: moego.utils.v2.PaginationResponse
	(*v1.DiscountCodeCompositeView)(nil),              // 28: moego.models.marketing.v1.DiscountCodeCompositeView
	(*v1.DiscountCodeLogCompositeView)(nil),           // 29: moego.models.marketing.v1.DiscountCodeLogCompositeView
	(*v1.DiscountCodeModelOnlineBookingView)(nil),     // 30: moego.models.marketing.v1.DiscountCodeModelOnlineBookingView
	(*emptypb.Empty)(nil),                             // 31: google.protobuf.Empty
}
var file_moego_api_marketing_v1_discount_code_api_proto_depIdxs = []int32{
	21, // 0: moego.api.marketing.v1.CreateDiscountCodeRequest.type:type_name -> moego.models.marketing.v1.DiscountCodeType
	22, // 1: moego.api.marketing.v1.CreateDiscountCodeRequest.expiry_def:type_name -> moego.models.marketing.v1.ExpiryDef
	21, // 2: moego.api.marketing.v1.EditDiscountCodeRequest.type:type_name -> moego.models.marketing.v1.DiscountCodeType
	22, // 3: moego.api.marketing.v1.EditDiscountCodeRequest.expiry_def:type_name -> moego.models.marketing.v1.ExpiryDef
	23, // 4: moego.api.marketing.v1.GetDiscountCodeResponse.discount_code_model:type_name -> moego.models.marketing.v1.DiscountCodeModel
	24, // 5: moego.api.marketing.v1.GetDiscountCodeResponse.discount_code_summary_def:type_name -> moego.models.marketing.v1.DiscountCodeSummaryDef
	25, // 6: moego.api.marketing.v1.GetDiscountCodeListRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	26, // 7: moego.api.marketing.v1.GetDiscountCodeListRequest.status:type_name -> moego.models.marketing.v1.DiscountCodeStatus
	27, // 8: moego.api.marketing.v1.GetDiscountCodeListResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	28, // 9: moego.api.marketing.v1.GetDiscountCodeListResponse.discount_code_composite_views:type_name -> moego.models.marketing.v1.DiscountCodeCompositeView
	25, // 10: moego.api.marketing.v1.GetDiscountCodeLogListRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	27, // 11: moego.api.marketing.v1.GetDiscountCodeLogListResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	29, // 12: moego.api.marketing.v1.GetDiscountCodeLogListResponse.discount_code_log_composite_views:type_name -> moego.models.marketing.v1.DiscountCodeLogCompositeView
	26, // 13: moego.api.marketing.v1.ChangeStatusRequest.status:type_name -> moego.models.marketing.v1.DiscountCodeStatus
	22, // 14: moego.api.marketing.v1.ChangeStatusRequest.expiry_def:type_name -> moego.models.marketing.v1.ExpiryDef
	30, // 15: moego.api.marketing.v1.CheckDiscountCodeValidForCustomerResponse.discount_code_model_online_booking_view:type_name -> moego.models.marketing.v1.DiscountCodeModelOnlineBookingView
	31, // 16: moego.api.marketing.v1.DiscountCodeService.GenerateDiscountCode:input_type -> google.protobuf.Empty
	1,  // 17: moego.api.marketing.v1.DiscountCodeService.CheckDiscountCode:input_type -> moego.api.marketing.v1.CheckDiscountCodeRequest
	3,  // 18: moego.api.marketing.v1.DiscountCodeService.CreateDiscountCode:input_type -> moego.api.marketing.v1.CreateDiscountCodeRequest
	5,  // 19: moego.api.marketing.v1.DiscountCodeService.EditDiscountCode:input_type -> moego.api.marketing.v1.EditDiscountCodeRequest
	7,  // 20: moego.api.marketing.v1.DiscountCodeService.GetDiscountCode:input_type -> moego.api.marketing.v1.GetDiscountCodeRequest
	9,  // 21: moego.api.marketing.v1.DiscountCodeService.GetDiscountCodeList:input_type -> moego.api.marketing.v1.GetDiscountCodeListRequest
	13, // 22: moego.api.marketing.v1.DiscountCodeService.GetDiscountCodeLogOverview:input_type -> moego.api.marketing.v1.GetDiscountCodeLogOverviewRequest
	11, // 23: moego.api.marketing.v1.DiscountCodeService.GetDiscountCodeLogList:input_type -> moego.api.marketing.v1.GetDiscountCodeLogListRequest
	15, // 24: moego.api.marketing.v1.DiscountCodeService.ChangeStatus:input_type -> moego.api.marketing.v1.ChangeStatusRequest
	17, // 25: moego.api.marketing.v1.DiscountCodeService.CheckDiscountCodeValidForCustomer:input_type -> moego.api.marketing.v1.CheckDiscountCodeValidForCustomerRequest
	19, // 26: moego.api.marketing.v1.DiscountCodeService.GetBusinessDiscountCodeConfig:input_type -> moego.api.marketing.v1.GetBusinessDiscountCodeConfigRequest
	0,  // 27: moego.api.marketing.v1.DiscountCodeService.GenerateDiscountCode:output_type -> moego.api.marketing.v1.GenerateDiscountCodeResponse
	2,  // 28: moego.api.marketing.v1.DiscountCodeService.CheckDiscountCode:output_type -> moego.api.marketing.v1.CheckDiscountCodeResponse
	4,  // 29: moego.api.marketing.v1.DiscountCodeService.CreateDiscountCode:output_type -> moego.api.marketing.v1.CreateDiscountCodeResponse
	6,  // 30: moego.api.marketing.v1.DiscountCodeService.EditDiscountCode:output_type -> moego.api.marketing.v1.EditDiscountCodeResponse
	8,  // 31: moego.api.marketing.v1.DiscountCodeService.GetDiscountCode:output_type -> moego.api.marketing.v1.GetDiscountCodeResponse
	10, // 32: moego.api.marketing.v1.DiscountCodeService.GetDiscountCodeList:output_type -> moego.api.marketing.v1.GetDiscountCodeListResponse
	14, // 33: moego.api.marketing.v1.DiscountCodeService.GetDiscountCodeLogOverview:output_type -> moego.api.marketing.v1.GetDiscountCodeLogOverviewResponse
	12, // 34: moego.api.marketing.v1.DiscountCodeService.GetDiscountCodeLogList:output_type -> moego.api.marketing.v1.GetDiscountCodeLogListResponse
	16, // 35: moego.api.marketing.v1.DiscountCodeService.ChangeStatus:output_type -> moego.api.marketing.v1.ChangeStatusResponse
	18, // 36: moego.api.marketing.v1.DiscountCodeService.CheckDiscountCodeValidForCustomer:output_type -> moego.api.marketing.v1.CheckDiscountCodeValidForCustomerResponse
	20, // 37: moego.api.marketing.v1.DiscountCodeService.GetBusinessDiscountCodeConfig:output_type -> moego.api.marketing.v1.GetBusinessDiscountCodeConfigResponse
	27, // [27:38] is the sub-list for method output_type
	16, // [16:27] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_moego_api_marketing_v1_discount_code_api_proto_init() }
func file_moego_api_marketing_v1_discount_code_api_proto_init() {
	if File_moego_api_marketing_v1_discount_code_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateDiscountCodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckDiscountCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckDiscountCodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDiscountCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDiscountCodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditDiscountCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditDiscountCodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiscountCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiscountCodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiscountCodeListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiscountCodeListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiscountCodeLogListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiscountCodeLogListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiscountCodeLogOverviewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiscountCodeLogOverviewResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckDiscountCodeValidForCustomerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckDiscountCodeValidForCustomerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessDiscountCodeConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessDiscountCodeConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[15].OneofWrappers = []interface{}{}
	file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[17].OneofWrappers = []interface{}{
		(*CheckDiscountCodeValidForCustomerRequest_Name)(nil),
		(*CheckDiscountCodeValidForCustomerRequest_Domain)(nil),
	}
	file_moego_api_marketing_v1_discount_code_api_proto_msgTypes[19].OneofWrappers = []interface{}{
		(*GetBusinessDiscountCodeConfigRequest_Name)(nil),
		(*GetBusinessDiscountCodeConfigRequest_Domain)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_marketing_v1_discount_code_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_marketing_v1_discount_code_api_proto_goTypes,
		DependencyIndexes: file_moego_api_marketing_v1_discount_code_api_proto_depIdxs,
		MessageInfos:      file_moego_api_marketing_v1_discount_code_api_proto_msgTypes,
	}.Build()
	File_moego_api_marketing_v1_discount_code_api_proto = out.File
	file_moego_api_marketing_v1_discount_code_api_proto_rawDesc = nil
	file_moego_api_marketing_v1_discount_code_api_proto_goTypes = nil
	file_moego_api_marketing_v1_discount_code_api_proto_depIdxs = nil
}
