// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/engagement/v1/setting_api.proto

package engagementapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/engagement/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetSettingParams
type GetSettingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetSettingParams) Reset() {
	*x = GetSettingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_engagement_v1_setting_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSettingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSettingParams) ProtoMessage() {}

func (x *GetSettingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_engagement_v1_setting_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSettingParams.ProtoReflect.Descriptor instead.
func (*GetSettingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_engagement_v1_setting_api_proto_rawDescGZIP(), []int{0}
}

// GetSettingResult
type GetSettingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Setting
	Setting *v1.Setting `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
	// seats setting
	SeatsSetting *v1.SeatsSetting `protobuf:"bytes,2,opt,name=seats_setting,json=seatsSetting,proto3" json:"seats_setting,omitempty"`
}

func (x *GetSettingResult) Reset() {
	*x = GetSettingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_engagement_v1_setting_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSettingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSettingResult) ProtoMessage() {}

func (x *GetSettingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_engagement_v1_setting_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSettingResult.ProtoReflect.Descriptor instead.
func (*GetSettingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_engagement_v1_setting_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetSettingResult) GetSetting() *v1.Setting {
	if x != nil {
		return x.Setting
	}
	return nil
}

func (x *GetSettingResult) GetSeatsSetting() *v1.SeatsSetting {
	if x != nil {
		return x.SeatsSetting
	}
	return nil
}

// 保存Setting值的请求对象
type UpdateSettingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 修改Setting的请求参数
	UpdateSetting *v1.UpdateSettingDef `protobuf:"bytes,1,opt,name=update_setting,json=updateSetting,proto3" json:"update_setting,omitempty"`
	// update seat settings
	UpdateSeatsSetting *v1.UpdateSeatsSettingDef `protobuf:"bytes,2,opt,name=update_seats_setting,json=updateSeatsSetting,proto3,oneof" json:"update_seats_setting,omitempty"`
}

func (x *UpdateSettingParams) Reset() {
	*x = UpdateSettingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_engagement_v1_setting_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSettingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSettingParams) ProtoMessage() {}

func (x *UpdateSettingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_engagement_v1_setting_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSettingParams.ProtoReflect.Descriptor instead.
func (*UpdateSettingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_engagement_v1_setting_api_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateSettingParams) GetUpdateSetting() *v1.UpdateSettingDef {
	if x != nil {
		return x.UpdateSetting
	}
	return nil
}

func (x *UpdateSettingParams) GetUpdateSeatsSetting() *v1.UpdateSeatsSettingDef {
	if x != nil {
		return x.UpdateSeatsSetting
	}
	return nil
}

// 保存Setting值的响应对象
type UpdateSettingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 修改Setting的响应参数
	Setting *v1.Setting `protobuf:"bytes,1,opt,name=setting,proto3" json:"setting,omitempty"`
	// seats setting
	SeatsSetting *v1.SeatsSetting `protobuf:"bytes,2,opt,name=seats_setting,json=seatsSetting,proto3" json:"seats_setting,omitempty"`
}

func (x *UpdateSettingResult) Reset() {
	*x = UpdateSettingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_engagement_v1_setting_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSettingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSettingResult) ProtoMessage() {}

func (x *UpdateSettingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_engagement_v1_setting_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSettingResult.ProtoReflect.Descriptor instead.
func (*UpdateSettingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_engagement_v1_setting_api_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateSettingResult) GetSetting() *v1.Setting {
	if x != nil {
		return x.Setting
	}
	return nil
}

func (x *UpdateSettingResult) GetSeatsSetting() *v1.SeatsSetting {
	if x != nil {
		return x.SeatsSetting
	}
	return nil
}

// TmpCallingSeatsParams
type TmpCallingSeatsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TmpCallingSeatsParams) Reset() {
	*x = TmpCallingSeatsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_engagement_v1_setting_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TmpCallingSeatsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TmpCallingSeatsParams) ProtoMessage() {}

func (x *TmpCallingSeatsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_engagement_v1_setting_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TmpCallingSeatsParams.ProtoReflect.Descriptor instead.
func (*TmpCallingSeatsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_engagement_v1_setting_api_proto_rawDescGZIP(), []int{4}
}

// TmpCallingSeatsResult
type TmpCallingSeatsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tmp calling seats
	TmpCallingSeats []*v1.TmpSeat `protobuf:"bytes,1,rep,name=tmp_calling_seats,json=tmpCallingSeats,proto3" json:"tmp_calling_seats,omitempty"`
	// seats limit
	SeatsLimit uint32 `protobuf:"varint,2,opt,name=seats_limit,json=seatsLimit,proto3" json:"seats_limit,omitempty"`
	// is seats limit reached
	IsSeatsLimitReached bool `protobuf:"varint,3,opt,name=is_seats_limit_reached,json=isSeatsLimitReached,proto3" json:"is_seats_limit_reached,omitempty"`
}

func (x *TmpCallingSeatsResult) Reset() {
	*x = TmpCallingSeatsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_engagement_v1_setting_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TmpCallingSeatsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TmpCallingSeatsResult) ProtoMessage() {}

func (x *TmpCallingSeatsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_engagement_v1_setting_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TmpCallingSeatsResult.ProtoReflect.Descriptor instead.
func (*TmpCallingSeatsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_engagement_v1_setting_api_proto_rawDescGZIP(), []int{5}
}

func (x *TmpCallingSeatsResult) GetTmpCallingSeats() []*v1.TmpSeat {
	if x != nil {
		return x.TmpCallingSeats
	}
	return nil
}

func (x *TmpCallingSeatsResult) GetSeatsLimit() uint32 {
	if x != nil {
		return x.SeatsLimit
	}
	return 0
}

func (x *TmpCallingSeatsResult) GetIsSeatsLimitReached() bool {
	if x != nil {
		return x.IsSeatsLimitReached
	}
	return false
}

var File_moego_api_engagement_v1_setting_api_proto protoreflect.FileDescriptor

var file_moego_api_engagement_v1_setting_api_proto_rawDesc = []byte{
	0x0a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x12, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x22, 0xa0, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3d, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x4d, 0x0a, 0x0d, 0x73, 0x65, 0x61, 0x74, 0x73, 0x5f, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x74, 0x73, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x0c, 0x73, 0x65, 0x61, 0x74, 0x73, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x22, 0xed, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x53, 0x0a, 0x0e,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x66, 0x52, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x12, 0x68, 0x0a, 0x14, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x61, 0x74,
	0x73, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x61, 0x74, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x66, 0x48, 0x00, 0x52, 0x12, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x61, 0x74,
	0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x42, 0x17, 0x0a, 0x15, 0x5f,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x61, 0x74, 0x73, 0x5f, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x22, 0xa3, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3d, 0x0a, 0x07,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x4d, 0x0a, 0x0d, 0x73,
	0x65, 0x61, 0x74, 0x73, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x61, 0x74, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x0c, 0x73, 0x65,
	0x61, 0x74, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x17, 0x0a, 0x15, 0x54, 0x6d,
	0x70, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x61, 0x74, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x22, 0xbe, 0x01, 0x0a, 0x15, 0x54, 0x6d, 0x70, 0x43, 0x61, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x53, 0x65, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4f, 0x0a,
	0x11, 0x74, 0x6d, 0x70, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x61,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6d, 0x70, 0x53, 0x65, 0x61, 0x74, 0x52, 0x0f, 0x74,
	0x6d, 0x70, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x61, 0x74, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x65, 0x61, 0x74, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x74, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12,
	0x33, 0x0a, 0x16, 0x69, 0x73, 0x5f, 0x73, 0x65, 0x61, 0x74, 0x73, 0x5f, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x5f, 0x72, 0x65, 0x61, 0x63, 0x68, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x13, 0x69, 0x73, 0x53, 0x65, 0x61, 0x74, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x61,
	0x63, 0x68, 0x65, 0x64, 0x32, 0xd7, 0x02, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x62, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6b, 0x0a, 0x0d, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x2c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x74, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x54,
	0x6d, 0x70, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x61, 0x74, 0x73, 0x12, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6d, 0x70, 0x43, 0x61, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x61, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6d, 0x70, 0x43, 0x61, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x81,
	0x01, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x3b, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_engagement_v1_setting_api_proto_rawDescOnce sync.Once
	file_moego_api_engagement_v1_setting_api_proto_rawDescData = file_moego_api_engagement_v1_setting_api_proto_rawDesc
)

func file_moego_api_engagement_v1_setting_api_proto_rawDescGZIP() []byte {
	file_moego_api_engagement_v1_setting_api_proto_rawDescOnce.Do(func() {
		file_moego_api_engagement_v1_setting_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_engagement_v1_setting_api_proto_rawDescData)
	})
	return file_moego_api_engagement_v1_setting_api_proto_rawDescData
}

var file_moego_api_engagement_v1_setting_api_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_moego_api_engagement_v1_setting_api_proto_goTypes = []interface{}{
	(*GetSettingParams)(nil),         // 0: moego.api.engagement.v1.GetSettingParams
	(*GetSettingResult)(nil),         // 1: moego.api.engagement.v1.GetSettingResult
	(*UpdateSettingParams)(nil),      // 2: moego.api.engagement.v1.UpdateSettingParams
	(*UpdateSettingResult)(nil),      // 3: moego.api.engagement.v1.UpdateSettingResult
	(*TmpCallingSeatsParams)(nil),    // 4: moego.api.engagement.v1.TmpCallingSeatsParams
	(*TmpCallingSeatsResult)(nil),    // 5: moego.api.engagement.v1.TmpCallingSeatsResult
	(*v1.Setting)(nil),               // 6: moego.models.engagement.v1.Setting
	(*v1.SeatsSetting)(nil),          // 7: moego.models.engagement.v1.SeatsSetting
	(*v1.UpdateSettingDef)(nil),      // 8: moego.models.engagement.v1.UpdateSettingDef
	(*v1.UpdateSeatsSettingDef)(nil), // 9: moego.models.engagement.v1.UpdateSeatsSettingDef
	(*v1.TmpSeat)(nil),               // 10: moego.models.engagement.v1.TmpSeat
}
var file_moego_api_engagement_v1_setting_api_proto_depIdxs = []int32{
	6,  // 0: moego.api.engagement.v1.GetSettingResult.setting:type_name -> moego.models.engagement.v1.Setting
	7,  // 1: moego.api.engagement.v1.GetSettingResult.seats_setting:type_name -> moego.models.engagement.v1.SeatsSetting
	8,  // 2: moego.api.engagement.v1.UpdateSettingParams.update_setting:type_name -> moego.models.engagement.v1.UpdateSettingDef
	9,  // 3: moego.api.engagement.v1.UpdateSettingParams.update_seats_setting:type_name -> moego.models.engagement.v1.UpdateSeatsSettingDef
	6,  // 4: moego.api.engagement.v1.UpdateSettingResult.setting:type_name -> moego.models.engagement.v1.Setting
	7,  // 5: moego.api.engagement.v1.UpdateSettingResult.seats_setting:type_name -> moego.models.engagement.v1.SeatsSetting
	10, // 6: moego.api.engagement.v1.TmpCallingSeatsResult.tmp_calling_seats:type_name -> moego.models.engagement.v1.TmpSeat
	0,  // 7: moego.api.engagement.v1.SettingService.GetSetting:input_type -> moego.api.engagement.v1.GetSettingParams
	2,  // 8: moego.api.engagement.v1.SettingService.UpdateSetting:input_type -> moego.api.engagement.v1.UpdateSettingParams
	4,  // 9: moego.api.engagement.v1.SettingService.GetTmpCallingSeats:input_type -> moego.api.engagement.v1.TmpCallingSeatsParams
	1,  // 10: moego.api.engagement.v1.SettingService.GetSetting:output_type -> moego.api.engagement.v1.GetSettingResult
	3,  // 11: moego.api.engagement.v1.SettingService.UpdateSetting:output_type -> moego.api.engagement.v1.UpdateSettingResult
	5,  // 12: moego.api.engagement.v1.SettingService.GetTmpCallingSeats:output_type -> moego.api.engagement.v1.TmpCallingSeatsResult
	10, // [10:13] is the sub-list for method output_type
	7,  // [7:10] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_moego_api_engagement_v1_setting_api_proto_init() }
func file_moego_api_engagement_v1_setting_api_proto_init() {
	if File_moego_api_engagement_v1_setting_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_engagement_v1_setting_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSettingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_engagement_v1_setting_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSettingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_engagement_v1_setting_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSettingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_engagement_v1_setting_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSettingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_engagement_v1_setting_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TmpCallingSeatsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_engagement_v1_setting_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TmpCallingSeatsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_engagement_v1_setting_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_engagement_v1_setting_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_engagement_v1_setting_api_proto_goTypes,
		DependencyIndexes: file_moego_api_engagement_v1_setting_api_proto_depIdxs,
		MessageInfos:      file_moego_api_engagement_v1_setting_api_proto_msgTypes,
	}.Build()
	File_moego_api_engagement_v1_setting_api_proto = out.File
	file_moego_api_engagement_v1_setting_api_proto_rawDesc = nil
	file_moego_api_engagement_v1_setting_api_proto_goTypes = nil
	file_moego_api_engagement_v1_setting_api_proto_depIdxs = nil
}
