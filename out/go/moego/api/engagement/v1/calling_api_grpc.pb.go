// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/engagement/v1/calling_api.proto

package engagementapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CallingServiceClient is the client API for CallingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CallingServiceClient interface {
	// create calling log
	CreateCallingLog(ctx context.Context, in *CreateCallingLogParams, opts ...grpc.CallOption) (*CreateCallingLogResult, error)
	// get calling log
	GetCallingLog(ctx context.Context, in *GetCallingLogParams, opts ...grpc.CallOption) (*GetCallingLogResult, error)
	// update calling log
	UpdateCallingLog(ctx context.Context, in *UpdateCallingLogParams, opts ...grpc.CallOption) (*UpdateCallingLogResult, error)
	// delete calling log
	DeleteCallingLog(ctx context.Context, in *DeleteCallingLogParams, opts ...grpc.CallOption) (*DeleteCallingLogResult, error)
	// list calling logs
	ListCallingLogs(ctx context.Context, in *ListCallingLogsParams, opts ...grpc.CallOption) (*ListCallingLogsResult, error)
	// get calling log overview
	GetCallingLogOverview(ctx context.Context, in *GetCallingLogOverviewParams, opts ...grpc.CallOption) (*GetCallingLogOverviewResult, error)
	// search customer
	SearchCustomer(ctx context.Context, in *SearchCustomerParams, opts ...grpc.CallOption) (*SearchCustomerResult, error)
	// Deprecated: Do not use.
	// get token
	GetToken(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetTokenResult, error)
	// get calling detail
	GetCallingDetail(ctx context.Context, in *GetCallingDetailParams, opts ...grpc.CallOption) (*GetCallingDetailResult, error)
	// get customer dial mask
	GetCustomerDialMask(ctx context.Context, in *GetCustomerDialMaskParams, opts ...grpc.CallOption) (*GetCustomerMaskResult, error)
	// get default local phone number for B-APP
	GetDefaultLocalPhoneNumber(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetDefaultLocalPhoneNumberResult, error)
	// make call on B-APP
	CallFromBApp(ctx context.Context, in *CallFromBAppParams, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// get token
	GetVoipToken(ctx context.Context, in *GetTokenParams, opts ...grpc.CallOption) (*GetTokenResult, error)
	// 是否存在多个相同号码处于 unresolved 状态的 activity log
	ConfirmUnresolvedRange(ctx context.Context, in *ConfirmUnresolvedRangeParams, opts ...grpc.CallOption) (*ConfirmUnresolvedRangeResult, error)
	// 将一个或多个 activity log 标记为 resolved
	MarkLogResolveStatus(ctx context.Context, in *MarkLogResolveStatusParams, opts ...grpc.CallOption) (*MarkLogResolveStatusResult, error)
}

type callingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCallingServiceClient(cc grpc.ClientConnInterface) CallingServiceClient {
	return &callingServiceClient{cc}
}

func (c *callingServiceClient) CreateCallingLog(ctx context.Context, in *CreateCallingLogParams, opts ...grpc.CallOption) (*CreateCallingLogResult, error) {
	out := new(CreateCallingLogResult)
	err := c.cc.Invoke(ctx, "/moego.api.engagement.v1.CallingService/CreateCallingLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callingServiceClient) GetCallingLog(ctx context.Context, in *GetCallingLogParams, opts ...grpc.CallOption) (*GetCallingLogResult, error) {
	out := new(GetCallingLogResult)
	err := c.cc.Invoke(ctx, "/moego.api.engagement.v1.CallingService/GetCallingLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callingServiceClient) UpdateCallingLog(ctx context.Context, in *UpdateCallingLogParams, opts ...grpc.CallOption) (*UpdateCallingLogResult, error) {
	out := new(UpdateCallingLogResult)
	err := c.cc.Invoke(ctx, "/moego.api.engagement.v1.CallingService/UpdateCallingLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callingServiceClient) DeleteCallingLog(ctx context.Context, in *DeleteCallingLogParams, opts ...grpc.CallOption) (*DeleteCallingLogResult, error) {
	out := new(DeleteCallingLogResult)
	err := c.cc.Invoke(ctx, "/moego.api.engagement.v1.CallingService/DeleteCallingLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callingServiceClient) ListCallingLogs(ctx context.Context, in *ListCallingLogsParams, opts ...grpc.CallOption) (*ListCallingLogsResult, error) {
	out := new(ListCallingLogsResult)
	err := c.cc.Invoke(ctx, "/moego.api.engagement.v1.CallingService/ListCallingLogs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callingServiceClient) GetCallingLogOverview(ctx context.Context, in *GetCallingLogOverviewParams, opts ...grpc.CallOption) (*GetCallingLogOverviewResult, error) {
	out := new(GetCallingLogOverviewResult)
	err := c.cc.Invoke(ctx, "/moego.api.engagement.v1.CallingService/GetCallingLogOverview", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callingServiceClient) SearchCustomer(ctx context.Context, in *SearchCustomerParams, opts ...grpc.CallOption) (*SearchCustomerResult, error) {
	out := new(SearchCustomerResult)
	err := c.cc.Invoke(ctx, "/moego.api.engagement.v1.CallingService/SearchCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *callingServiceClient) GetToken(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetTokenResult, error) {
	out := new(GetTokenResult)
	err := c.cc.Invoke(ctx, "/moego.api.engagement.v1.CallingService/GetToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callingServiceClient) GetCallingDetail(ctx context.Context, in *GetCallingDetailParams, opts ...grpc.CallOption) (*GetCallingDetailResult, error) {
	out := new(GetCallingDetailResult)
	err := c.cc.Invoke(ctx, "/moego.api.engagement.v1.CallingService/GetCallingDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callingServiceClient) GetCustomerDialMask(ctx context.Context, in *GetCustomerDialMaskParams, opts ...grpc.CallOption) (*GetCustomerMaskResult, error) {
	out := new(GetCustomerMaskResult)
	err := c.cc.Invoke(ctx, "/moego.api.engagement.v1.CallingService/GetCustomerDialMask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callingServiceClient) GetDefaultLocalPhoneNumber(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetDefaultLocalPhoneNumberResult, error) {
	out := new(GetDefaultLocalPhoneNumberResult)
	err := c.cc.Invoke(ctx, "/moego.api.engagement.v1.CallingService/GetDefaultLocalPhoneNumber", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callingServiceClient) CallFromBApp(ctx context.Context, in *CallFromBAppParams, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.api.engagement.v1.CallingService/CallFromBApp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callingServiceClient) GetVoipToken(ctx context.Context, in *GetTokenParams, opts ...grpc.CallOption) (*GetTokenResult, error) {
	out := new(GetTokenResult)
	err := c.cc.Invoke(ctx, "/moego.api.engagement.v1.CallingService/GetVoipToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callingServiceClient) ConfirmUnresolvedRange(ctx context.Context, in *ConfirmUnresolvedRangeParams, opts ...grpc.CallOption) (*ConfirmUnresolvedRangeResult, error) {
	out := new(ConfirmUnresolvedRangeResult)
	err := c.cc.Invoke(ctx, "/moego.api.engagement.v1.CallingService/ConfirmUnresolvedRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callingServiceClient) MarkLogResolveStatus(ctx context.Context, in *MarkLogResolveStatusParams, opts ...grpc.CallOption) (*MarkLogResolveStatusResult, error) {
	out := new(MarkLogResolveStatusResult)
	err := c.cc.Invoke(ctx, "/moego.api.engagement.v1.CallingService/MarkLogResolveStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CallingServiceServer is the server API for CallingService service.
// All implementations must embed UnimplementedCallingServiceServer
// for forward compatibility
type CallingServiceServer interface {
	// create calling log
	CreateCallingLog(context.Context, *CreateCallingLogParams) (*CreateCallingLogResult, error)
	// get calling log
	GetCallingLog(context.Context, *GetCallingLogParams) (*GetCallingLogResult, error)
	// update calling log
	UpdateCallingLog(context.Context, *UpdateCallingLogParams) (*UpdateCallingLogResult, error)
	// delete calling log
	DeleteCallingLog(context.Context, *DeleteCallingLogParams) (*DeleteCallingLogResult, error)
	// list calling logs
	ListCallingLogs(context.Context, *ListCallingLogsParams) (*ListCallingLogsResult, error)
	// get calling log overview
	GetCallingLogOverview(context.Context, *GetCallingLogOverviewParams) (*GetCallingLogOverviewResult, error)
	// search customer
	SearchCustomer(context.Context, *SearchCustomerParams) (*SearchCustomerResult, error)
	// Deprecated: Do not use.
	// get token
	GetToken(context.Context, *emptypb.Empty) (*GetTokenResult, error)
	// get calling detail
	GetCallingDetail(context.Context, *GetCallingDetailParams) (*GetCallingDetailResult, error)
	// get customer dial mask
	GetCustomerDialMask(context.Context, *GetCustomerDialMaskParams) (*GetCustomerMaskResult, error)
	// get default local phone number for B-APP
	GetDefaultLocalPhoneNumber(context.Context, *emptypb.Empty) (*GetDefaultLocalPhoneNumberResult, error)
	// make call on B-APP
	CallFromBApp(context.Context, *CallFromBAppParams) (*emptypb.Empty, error)
	// get token
	GetVoipToken(context.Context, *GetTokenParams) (*GetTokenResult, error)
	// 是否存在多个相同号码处于 unresolved 状态的 activity log
	ConfirmUnresolvedRange(context.Context, *ConfirmUnresolvedRangeParams) (*ConfirmUnresolvedRangeResult, error)
	// 将一个或多个 activity log 标记为 resolved
	MarkLogResolveStatus(context.Context, *MarkLogResolveStatusParams) (*MarkLogResolveStatusResult, error)
	mustEmbedUnimplementedCallingServiceServer()
}

// UnimplementedCallingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCallingServiceServer struct {
}

func (UnimplementedCallingServiceServer) CreateCallingLog(context.Context, *CreateCallingLogParams) (*CreateCallingLogResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCallingLog not implemented")
}
func (UnimplementedCallingServiceServer) GetCallingLog(context.Context, *GetCallingLogParams) (*GetCallingLogResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCallingLog not implemented")
}
func (UnimplementedCallingServiceServer) UpdateCallingLog(context.Context, *UpdateCallingLogParams) (*UpdateCallingLogResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCallingLog not implemented")
}
func (UnimplementedCallingServiceServer) DeleteCallingLog(context.Context, *DeleteCallingLogParams) (*DeleteCallingLogResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCallingLog not implemented")
}
func (UnimplementedCallingServiceServer) ListCallingLogs(context.Context, *ListCallingLogsParams) (*ListCallingLogsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCallingLogs not implemented")
}
func (UnimplementedCallingServiceServer) GetCallingLogOverview(context.Context, *GetCallingLogOverviewParams) (*GetCallingLogOverviewResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCallingLogOverview not implemented")
}
func (UnimplementedCallingServiceServer) SearchCustomer(context.Context, *SearchCustomerParams) (*SearchCustomerResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchCustomer not implemented")
}
func (UnimplementedCallingServiceServer) GetToken(context.Context, *emptypb.Empty) (*GetTokenResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetToken not implemented")
}
func (UnimplementedCallingServiceServer) GetCallingDetail(context.Context, *GetCallingDetailParams) (*GetCallingDetailResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCallingDetail not implemented")
}
func (UnimplementedCallingServiceServer) GetCustomerDialMask(context.Context, *GetCustomerDialMaskParams) (*GetCustomerMaskResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerDialMask not implemented")
}
func (UnimplementedCallingServiceServer) GetDefaultLocalPhoneNumber(context.Context, *emptypb.Empty) (*GetDefaultLocalPhoneNumberResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDefaultLocalPhoneNumber not implemented")
}
func (UnimplementedCallingServiceServer) CallFromBApp(context.Context, *CallFromBAppParams) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CallFromBApp not implemented")
}
func (UnimplementedCallingServiceServer) GetVoipToken(context.Context, *GetTokenParams) (*GetTokenResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVoipToken not implemented")
}
func (UnimplementedCallingServiceServer) ConfirmUnresolvedRange(context.Context, *ConfirmUnresolvedRangeParams) (*ConfirmUnresolvedRangeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmUnresolvedRange not implemented")
}
func (UnimplementedCallingServiceServer) MarkLogResolveStatus(context.Context, *MarkLogResolveStatusParams) (*MarkLogResolveStatusResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkLogResolveStatus not implemented")
}
func (UnimplementedCallingServiceServer) mustEmbedUnimplementedCallingServiceServer() {}

// UnsafeCallingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CallingServiceServer will
// result in compilation errors.
type UnsafeCallingServiceServer interface {
	mustEmbedUnimplementedCallingServiceServer()
}

func RegisterCallingServiceServer(s grpc.ServiceRegistrar, srv CallingServiceServer) {
	s.RegisterService(&CallingService_ServiceDesc, srv)
}

func _CallingService_CreateCallingLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCallingLogParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallingServiceServer).CreateCallingLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.engagement.v1.CallingService/CreateCallingLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallingServiceServer).CreateCallingLog(ctx, req.(*CreateCallingLogParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallingService_GetCallingLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCallingLogParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallingServiceServer).GetCallingLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.engagement.v1.CallingService/GetCallingLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallingServiceServer).GetCallingLog(ctx, req.(*GetCallingLogParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallingService_UpdateCallingLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCallingLogParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallingServiceServer).UpdateCallingLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.engagement.v1.CallingService/UpdateCallingLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallingServiceServer).UpdateCallingLog(ctx, req.(*UpdateCallingLogParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallingService_DeleteCallingLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCallingLogParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallingServiceServer).DeleteCallingLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.engagement.v1.CallingService/DeleteCallingLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallingServiceServer).DeleteCallingLog(ctx, req.(*DeleteCallingLogParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallingService_ListCallingLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCallingLogsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallingServiceServer).ListCallingLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.engagement.v1.CallingService/ListCallingLogs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallingServiceServer).ListCallingLogs(ctx, req.(*ListCallingLogsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallingService_GetCallingLogOverview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCallingLogOverviewParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallingServiceServer).GetCallingLogOverview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.engagement.v1.CallingService/GetCallingLogOverview",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallingServiceServer).GetCallingLogOverview(ctx, req.(*GetCallingLogOverviewParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallingService_SearchCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCustomerParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallingServiceServer).SearchCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.engagement.v1.CallingService/SearchCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallingServiceServer).SearchCustomer(ctx, req.(*SearchCustomerParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallingService_GetToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallingServiceServer).GetToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.engagement.v1.CallingService/GetToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallingServiceServer).GetToken(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallingService_GetCallingDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCallingDetailParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallingServiceServer).GetCallingDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.engagement.v1.CallingService/GetCallingDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallingServiceServer).GetCallingDetail(ctx, req.(*GetCallingDetailParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallingService_GetCustomerDialMask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerDialMaskParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallingServiceServer).GetCustomerDialMask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.engagement.v1.CallingService/GetCustomerDialMask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallingServiceServer).GetCustomerDialMask(ctx, req.(*GetCustomerDialMaskParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallingService_GetDefaultLocalPhoneNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallingServiceServer).GetDefaultLocalPhoneNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.engagement.v1.CallingService/GetDefaultLocalPhoneNumber",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallingServiceServer).GetDefaultLocalPhoneNumber(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallingService_CallFromBApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CallFromBAppParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallingServiceServer).CallFromBApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.engagement.v1.CallingService/CallFromBApp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallingServiceServer).CallFromBApp(ctx, req.(*CallFromBAppParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallingService_GetVoipToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTokenParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallingServiceServer).GetVoipToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.engagement.v1.CallingService/GetVoipToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallingServiceServer).GetVoipToken(ctx, req.(*GetTokenParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallingService_ConfirmUnresolvedRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmUnresolvedRangeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallingServiceServer).ConfirmUnresolvedRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.engagement.v1.CallingService/ConfirmUnresolvedRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallingServiceServer).ConfirmUnresolvedRange(ctx, req.(*ConfirmUnresolvedRangeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CallingService_MarkLogResolveStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkLogResolveStatusParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallingServiceServer).MarkLogResolveStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.engagement.v1.CallingService/MarkLogResolveStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallingServiceServer).MarkLogResolveStatus(ctx, req.(*MarkLogResolveStatusParams))
	}
	return interceptor(ctx, in, info, handler)
}

// CallingService_ServiceDesc is the grpc.ServiceDesc for CallingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CallingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.engagement.v1.CallingService",
	HandlerType: (*CallingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateCallingLog",
			Handler:    _CallingService_CreateCallingLog_Handler,
		},
		{
			MethodName: "GetCallingLog",
			Handler:    _CallingService_GetCallingLog_Handler,
		},
		{
			MethodName: "UpdateCallingLog",
			Handler:    _CallingService_UpdateCallingLog_Handler,
		},
		{
			MethodName: "DeleteCallingLog",
			Handler:    _CallingService_DeleteCallingLog_Handler,
		},
		{
			MethodName: "ListCallingLogs",
			Handler:    _CallingService_ListCallingLogs_Handler,
		},
		{
			MethodName: "GetCallingLogOverview",
			Handler:    _CallingService_GetCallingLogOverview_Handler,
		},
		{
			MethodName: "SearchCustomer",
			Handler:    _CallingService_SearchCustomer_Handler,
		},
		{
			MethodName: "GetToken",
			Handler:    _CallingService_GetToken_Handler,
		},
		{
			MethodName: "GetCallingDetail",
			Handler:    _CallingService_GetCallingDetail_Handler,
		},
		{
			MethodName: "GetCustomerDialMask",
			Handler:    _CallingService_GetCustomerDialMask_Handler,
		},
		{
			MethodName: "GetDefaultLocalPhoneNumber",
			Handler:    _CallingService_GetDefaultLocalPhoneNumber_Handler,
		},
		{
			MethodName: "CallFromBApp",
			Handler:    _CallingService_CallFromBApp_Handler,
		},
		{
			MethodName: "GetVoipToken",
			Handler:    _CallingService_GetVoipToken_Handler,
		},
		{
			MethodName: "ConfirmUnresolvedRange",
			Handler:    _CallingService_ConfirmUnresolvedRange_Handler,
		},
		{
			MethodName: "MarkLogResolveStatus",
			Handler:    _CallingService_MarkLogResolveStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/engagement/v1/calling_api.proto",
}
