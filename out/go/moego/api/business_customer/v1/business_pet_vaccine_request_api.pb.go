// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/business_customer/v1/business_pet_vaccine_request_api.proto

package businesscustomerapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ReviewPetVaccineRequestParams
type ReviewPetVaccineRequestParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet vaccine request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *ReviewPetVaccineRequestParams) Reset() {
	*x = ReviewPetVaccineRequestParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewPetVaccineRequestParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewPetVaccineRequestParams) ProtoMessage() {}

func (x *ReviewPetVaccineRequestParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewPetVaccineRequestParams.ProtoReflect.Descriptor instead.
func (*ReviewPetVaccineRequestParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDescGZIP(), []int{0}
}

func (x *ReviewPetVaccineRequestParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// ReviewPetVaccineRequestResult
type ReviewPetVaccineRequestResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// When expired = true, it means client / pet / vaccine has been deleted.
	// In this case, client, pet, pet_vaccine_after, pet_vaccine_before will not be set in the result.
	Expired bool `protobuf:"varint,1,opt,name=expired,proto3" json:"expired,omitempty"`
	// pet vaccine (after)
	PetVaccineAfter *ReviewPetVaccineRequestResult_Vaccine `protobuf:"bytes,2,opt,name=pet_vaccine_after,json=petVaccineAfter,proto3" json:"pet_vaccine_after,omitempty"`
	// pet vaccine (before)
	PetVaccineBefore *ReviewPetVaccineRequestResult_Vaccine `protobuf:"bytes,3,opt,name=pet_vaccine_before,json=petVaccineBefore,proto3" json:"pet_vaccine_before,omitempty"`
	// client
	Client *ReviewPetVaccineRequestResult_Client `protobuf:"bytes,4,opt,name=client,proto3" json:"client,omitempty"`
	// pet
	Pet *ReviewPetVaccineRequestResult_Pet `protobuf:"bytes,5,opt,name=pet,proto3" json:"pet,omitempty"`
	// create time of vaccine request
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
}

func (x *ReviewPetVaccineRequestResult) Reset() {
	*x = ReviewPetVaccineRequestResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewPetVaccineRequestResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewPetVaccineRequestResult) ProtoMessage() {}

func (x *ReviewPetVaccineRequestResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewPetVaccineRequestResult.ProtoReflect.Descriptor instead.
func (*ReviewPetVaccineRequestResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDescGZIP(), []int{1}
}

func (x *ReviewPetVaccineRequestResult) GetExpired() bool {
	if x != nil {
		return x.Expired
	}
	return false
}

func (x *ReviewPetVaccineRequestResult) GetPetVaccineAfter() *ReviewPetVaccineRequestResult_Vaccine {
	if x != nil {
		return x.PetVaccineAfter
	}
	return nil
}

func (x *ReviewPetVaccineRequestResult) GetPetVaccineBefore() *ReviewPetVaccineRequestResult_Vaccine {
	if x != nil {
		return x.PetVaccineBefore
	}
	return nil
}

func (x *ReviewPetVaccineRequestResult) GetClient() *ReviewPetVaccineRequestResult_Client {
	if x != nil {
		return x.Client
	}
	return nil
}

func (x *ReviewPetVaccineRequestResult) GetPet() *ReviewPetVaccineRequestResult_Pet {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *ReviewPetVaccineRequestResult) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

// ApprovePetVaccineRequestParams
type ApprovePetVaccineRequestParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet vaccine request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// notification id
	NotificationId int64 `protobuf:"varint,2,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	// expiration date, optional
	// if set, will override expiration date in pet vaccine request
	ExpirationDate *date.Date `protobuf:"bytes,3,opt,name=expiration_date,json=expirationDate,proto3" json:"expiration_date,omitempty"`
}

func (x *ApprovePetVaccineRequestParams) Reset() {
	*x = ApprovePetVaccineRequestParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApprovePetVaccineRequestParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApprovePetVaccineRequestParams) ProtoMessage() {}

func (x *ApprovePetVaccineRequestParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApprovePetVaccineRequestParams.ProtoReflect.Descriptor instead.
func (*ApprovePetVaccineRequestParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDescGZIP(), []int{2}
}

func (x *ApprovePetVaccineRequestParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ApprovePetVaccineRequestParams) GetNotificationId() int64 {
	if x != nil {
		return x.NotificationId
	}
	return 0
}

func (x *ApprovePetVaccineRequestParams) GetExpirationDate() *date.Date {
	if x != nil {
		return x.ExpirationDate
	}
	return nil
}

// ApprovePetVaccineRequestResult
type ApprovePetVaccineRequestResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ApprovePetVaccineRequestResult) Reset() {
	*x = ApprovePetVaccineRequestResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApprovePetVaccineRequestResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApprovePetVaccineRequestResult) ProtoMessage() {}

func (x *ApprovePetVaccineRequestResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApprovePetVaccineRequestResult.ProtoReflect.Descriptor instead.
func (*ApprovePetVaccineRequestResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDescGZIP(), []int{3}
}

// DeclinePetVaccineRequestParams
type DeclinePetVaccineRequestParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet vaccine request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// notification id
	NotificationId int64 `protobuf:"varint,2,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	// force to decline.
	// use case: pet vaccine request 可能已经被处理，或者 client / pet / vaccine 被删除，导致 pet vaccine request 无法被处理,
	// 但是 pending review notification 没清除, 此时需要传 force = true, 把通知清除, 相当于 dismiss 掉这个无效的 pet vaccine request.
	// 如果 pet vaccine request 已经被 approve, force = true 不会改变 approve 的结果, 也不会抛异常.
	// 如果 force = false, 已经 approve 的 pet vaccine request 不可被 decline, 会抛一个异常
	Force bool `protobuf:"varint,3,opt,name=force,proto3" json:"force,omitempty"`
}

func (x *DeclinePetVaccineRequestParams) Reset() {
	*x = DeclinePetVaccineRequestParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeclinePetVaccineRequestParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeclinePetVaccineRequestParams) ProtoMessage() {}

func (x *DeclinePetVaccineRequestParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeclinePetVaccineRequestParams.ProtoReflect.Descriptor instead.
func (*DeclinePetVaccineRequestParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDescGZIP(), []int{4}
}

func (x *DeclinePetVaccineRequestParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeclinePetVaccineRequestParams) GetNotificationId() int64 {
	if x != nil {
		return x.NotificationId
	}
	return 0
}

func (x *DeclinePetVaccineRequestParams) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

// DeclinePetVaccineRequestResult
type DeclinePetVaccineRequestResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeclinePetVaccineRequestResult) Reset() {
	*x = DeclinePetVaccineRequestResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeclinePetVaccineRequestResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeclinePetVaccineRequestResult) ProtoMessage() {}

func (x *DeclinePetVaccineRequestResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeclinePetVaccineRequestResult.ProtoReflect.Descriptor instead.
func (*DeclinePetVaccineRequestResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDescGZIP(), []int{5}
}

// vaccine
type ReviewPetVaccineRequestResult_Vaccine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vaccine binding id (pet_vaccine_before) or vaccine request id (pet_vaccine_after)
	RecordId int64 `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	// vaccine id
	VaccineId int64 `protobuf:"varint,2,opt,name=vaccine_id,json=vaccineId,proto3" json:"vaccine_id,omitempty"`
	// vaccine name
	VaccineName string `protobuf:"bytes,3,opt,name=vaccine_name,json=vaccineName,proto3" json:"vaccine_name,omitempty"`
	// expiration date, may not exist
	ExpirationDate *date.Date `protobuf:"bytes,4,opt,name=expiration_date,json=expirationDate,proto3,oneof" json:"expiration_date,omitempty"`
	// vaccine document urls
	DocumentUrls []string `protobuf:"bytes,5,rep,name=document_urls,json=documentUrls,proto3" json:"document_urls,omitempty"`
}

func (x *ReviewPetVaccineRequestResult_Vaccine) Reset() {
	*x = ReviewPetVaccineRequestResult_Vaccine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewPetVaccineRequestResult_Vaccine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewPetVaccineRequestResult_Vaccine) ProtoMessage() {}

func (x *ReviewPetVaccineRequestResult_Vaccine) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewPetVaccineRequestResult_Vaccine.ProtoReflect.Descriptor instead.
func (*ReviewPetVaccineRequestResult_Vaccine) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ReviewPetVaccineRequestResult_Vaccine) GetRecordId() int64 {
	if x != nil {
		return x.RecordId
	}
	return 0
}

func (x *ReviewPetVaccineRequestResult_Vaccine) GetVaccineId() int64 {
	if x != nil {
		return x.VaccineId
	}
	return 0
}

func (x *ReviewPetVaccineRequestResult_Vaccine) GetVaccineName() string {
	if x != nil {
		return x.VaccineName
	}
	return ""
}

func (x *ReviewPetVaccineRequestResult_Vaccine) GetExpirationDate() *date.Date {
	if x != nil {
		return x.ExpirationDate
	}
	return nil
}

func (x *ReviewPetVaccineRequestResult_Vaccine) GetDocumentUrls() []string {
	if x != nil {
		return x.DocumentUrls
	}
	return nil
}

// client
type ReviewPetVaccineRequestResult_Client struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// first name
	FirstName string `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *ReviewPetVaccineRequestResult_Client) Reset() {
	*x = ReviewPetVaccineRequestResult_Client{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewPetVaccineRequestResult_Client) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewPetVaccineRequestResult_Client) ProtoMessage() {}

func (x *ReviewPetVaccineRequestResult_Client) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewPetVaccineRequestResult_Client.ProtoReflect.Descriptor instead.
func (*ReviewPetVaccineRequestResult_Client) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDescGZIP(), []int{1, 1}
}

func (x *ReviewPetVaccineRequestResult_Client) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *ReviewPetVaccineRequestResult_Client) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *ReviewPetVaccineRequestResult_Client) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

// pet
type ReviewPetVaccineRequestResult_Pet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet name
	PetName string `protobuf:"bytes,1,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,2,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// pet type id
	PetType v1.PetType `protobuf:"varint,3,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// breed
	Breed string `protobuf:"bytes,4,opt,name=breed,proto3" json:"breed,omitempty"`
}

func (x *ReviewPetVaccineRequestResult_Pet) Reset() {
	*x = ReviewPetVaccineRequestResult_Pet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewPetVaccineRequestResult_Pet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewPetVaccineRequestResult_Pet) ProtoMessage() {}

func (x *ReviewPetVaccineRequestResult_Pet) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewPetVaccineRequestResult_Pet.ProtoReflect.Descriptor instead.
func (*ReviewPetVaccineRequestResult_Pet) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDescGZIP(), []int{1, 2}
}

func (x *ReviewPetVaccineRequestResult_Pet) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *ReviewPetVaccineRequestResult_Pet) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *ReviewPetVaccineRequestResult_Pet) GetPetType() v1.PetType {
	if x != nil {
		return x.PetType
	}
	return v1.PetType(0)
}

func (x *ReviewPetVaccineRequestResult_Pet) GetBreed() string {
	if x != nil {
		return x.Breed
	}
	return ""
}

var File_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto protoreflect.FileDescriptor

var file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDesc = []byte{
	0x0a, 0x45, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x76, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2f, 0x0a, 0x1d,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xf7, 0x07,
	0x0a, 0x1d, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x12, 0x71, 0x0a, 0x11, 0x70, 0x65, 0x74,
	0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x65, 0x74, 0x56,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x0f, 0x70, 0x65, 0x74,
	0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x41, 0x66, 0x74, 0x65, 0x72, 0x12, 0x73, 0x0a, 0x12,
	0x70, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x65, 0x66, 0x6f,
	0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52,
	0x10, 0x70, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x42, 0x65, 0x66, 0x6f, 0x72,
	0x65, 0x12, 0x5c, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12,
	0x53, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x52,
	0x03, 0x70, 0x65, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x1a, 0xe2, 0x01, 0x0a, 0x07, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x0f,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a,
	0x0d, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72,
	0x6c, 0x73, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x1a, 0x67, 0x0a, 0x06, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x1a,
	0x95, 0x01, 0x0a, 0x03, 0x50, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x65, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x22, 0xa7, 0x01, 0x0a, 0x1e, 0x41, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x0f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x65, 0x22, 0x20, 0x0a, 0x1e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x50, 0x65, 0x74, 0x56,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x81, 0x01, 0x0a, 0x1e, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x50,
	0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x30, 0x0a, 0x0f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x22, 0x20, 0x0a, 0x1e, 0x44, 0x65, 0x63, 0x6c, 0x69,
	0x6e, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xf6, 0x03, 0x0a, 0x20, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x97,
	0x01, 0x0a, 0x17, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9a, 0x01, 0x0a, 0x18, 0x41, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x50, 0x65,
	0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x50, 0x65,
	0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9a, 0x01, 0x0a, 0x18, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e,
	0x65, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x65, 0x74, 0x56, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x42, 0x95, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDescOnce sync.Once
	file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDescData = file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDesc
)

func file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDescGZIP() []byte {
	file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDescOnce.Do(func() {
		file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDescData)
	})
	return file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDescData
}

var file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_goTypes = []interface{}{
	(*ReviewPetVaccineRequestParams)(nil),         // 0: moego.api.business_customer.v1.ReviewPetVaccineRequestParams
	(*ReviewPetVaccineRequestResult)(nil),         // 1: moego.api.business_customer.v1.ReviewPetVaccineRequestResult
	(*ApprovePetVaccineRequestParams)(nil),        // 2: moego.api.business_customer.v1.ApprovePetVaccineRequestParams
	(*ApprovePetVaccineRequestResult)(nil),        // 3: moego.api.business_customer.v1.ApprovePetVaccineRequestResult
	(*DeclinePetVaccineRequestParams)(nil),        // 4: moego.api.business_customer.v1.DeclinePetVaccineRequestParams
	(*DeclinePetVaccineRequestResult)(nil),        // 5: moego.api.business_customer.v1.DeclinePetVaccineRequestResult
	(*ReviewPetVaccineRequestResult_Vaccine)(nil), // 6: moego.api.business_customer.v1.ReviewPetVaccineRequestResult.Vaccine
	(*ReviewPetVaccineRequestResult_Client)(nil),  // 7: moego.api.business_customer.v1.ReviewPetVaccineRequestResult.Client
	(*ReviewPetVaccineRequestResult_Pet)(nil),     // 8: moego.api.business_customer.v1.ReviewPetVaccineRequestResult.Pet
	(*timestamppb.Timestamp)(nil),                 // 9: google.protobuf.Timestamp
	(*date.Date)(nil),                             // 10: google.type.Date
	(v1.PetType)(0),                               // 11: moego.models.customer.v1.PetType
}
var file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_depIdxs = []int32{
	6,  // 0: moego.api.business_customer.v1.ReviewPetVaccineRequestResult.pet_vaccine_after:type_name -> moego.api.business_customer.v1.ReviewPetVaccineRequestResult.Vaccine
	6,  // 1: moego.api.business_customer.v1.ReviewPetVaccineRequestResult.pet_vaccine_before:type_name -> moego.api.business_customer.v1.ReviewPetVaccineRequestResult.Vaccine
	7,  // 2: moego.api.business_customer.v1.ReviewPetVaccineRequestResult.client:type_name -> moego.api.business_customer.v1.ReviewPetVaccineRequestResult.Client
	8,  // 3: moego.api.business_customer.v1.ReviewPetVaccineRequestResult.pet:type_name -> moego.api.business_customer.v1.ReviewPetVaccineRequestResult.Pet
	9,  // 4: moego.api.business_customer.v1.ReviewPetVaccineRequestResult.create_time:type_name -> google.protobuf.Timestamp
	10, // 5: moego.api.business_customer.v1.ApprovePetVaccineRequestParams.expiration_date:type_name -> google.type.Date
	10, // 6: moego.api.business_customer.v1.ReviewPetVaccineRequestResult.Vaccine.expiration_date:type_name -> google.type.Date
	11, // 7: moego.api.business_customer.v1.ReviewPetVaccineRequestResult.Pet.pet_type:type_name -> moego.models.customer.v1.PetType
	0,  // 8: moego.api.business_customer.v1.BusinessPetVaccineRequestService.ReviewPetVaccineRequest:input_type -> moego.api.business_customer.v1.ReviewPetVaccineRequestParams
	2,  // 9: moego.api.business_customer.v1.BusinessPetVaccineRequestService.ApprovePetVaccineRequest:input_type -> moego.api.business_customer.v1.ApprovePetVaccineRequestParams
	4,  // 10: moego.api.business_customer.v1.BusinessPetVaccineRequestService.DeclinePetVaccineRequest:input_type -> moego.api.business_customer.v1.DeclinePetVaccineRequestParams
	1,  // 11: moego.api.business_customer.v1.BusinessPetVaccineRequestService.ReviewPetVaccineRequest:output_type -> moego.api.business_customer.v1.ReviewPetVaccineRequestResult
	3,  // 12: moego.api.business_customer.v1.BusinessPetVaccineRequestService.ApprovePetVaccineRequest:output_type -> moego.api.business_customer.v1.ApprovePetVaccineRequestResult
	5,  // 13: moego.api.business_customer.v1.BusinessPetVaccineRequestService.DeclinePetVaccineRequest:output_type -> moego.api.business_customer.v1.DeclinePetVaccineRequestResult
	11, // [11:14] is the sub-list for method output_type
	8,  // [8:11] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_init() }
func file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_init() {
	if File_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewPetVaccineRequestParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewPetVaccineRequestResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApprovePetVaccineRequestParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApprovePetVaccineRequestResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeclinePetVaccineRequestParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeclinePetVaccineRequestResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewPetVaccineRequestResult_Vaccine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewPetVaccineRequestResult_Client); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewPetVaccineRequestResult_Pet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_goTypes,
		DependencyIndexes: file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_depIdxs,
		MessageInfos:      file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_msgTypes,
	}.Build()
	File_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto = out.File
	file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_rawDesc = nil
	file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_goTypes = nil
	file_moego_api_business_customer_v1_business_pet_vaccine_request_api_proto_depIdxs = nil
}
