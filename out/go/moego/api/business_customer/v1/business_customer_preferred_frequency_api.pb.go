// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/business_customer/v1/business_customer_preferred_frequency_api.proto

package businesscustomerapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get customer grooming frequency params
type GetCustomerGroomingFrequencyParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetCustomerGroomingFrequencyParams) Reset() {
	*x = GetCustomerGroomingFrequencyParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerGroomingFrequencyParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerGroomingFrequencyParams) ProtoMessage() {}

func (x *GetCustomerGroomingFrequencyParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerGroomingFrequencyParams.ProtoReflect.Descriptor instead.
func (*GetCustomerGroomingFrequencyParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_rawDescGZIP(), []int{0}
}

// get customer grooming frequency result
type GetCustomerGroomingFrequencyResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming frequency
	GroomingFrequency *v1.TimePeriod `protobuf:"bytes,1,opt,name=grooming_frequency,json=groomingFrequency,proto3" json:"grooming_frequency,omitempty"`
}

func (x *GetCustomerGroomingFrequencyResult) Reset() {
	*x = GetCustomerGroomingFrequencyResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerGroomingFrequencyResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerGroomingFrequencyResult) ProtoMessage() {}

func (x *GetCustomerGroomingFrequencyResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerGroomingFrequencyResult.ProtoReflect.Descriptor instead.
func (*GetCustomerGroomingFrequencyResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetCustomerGroomingFrequencyResult) GetGroomingFrequency() *v1.TimePeriod {
	if x != nil {
		return x.GroomingFrequency
	}
	return nil
}

// upsert customer grooming frequency params
type UpsertCustomerGroomingFrequencyParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming frequency to upsert, currently only support unit of DAY, WEEK, MONTH
	GroomingFrequency *v1.TimePeriod `protobuf:"bytes,1,opt,name=grooming_frequency,json=groomingFrequency,proto3" json:"grooming_frequency,omitempty"`
	// apply to all customers, default is false
	ApplyToAllCustomers bool `protobuf:"varint,2,opt,name=apply_to_all_customers,json=applyToAllCustomers,proto3" json:"apply_to_all_customers,omitempty"`
}

func (x *UpsertCustomerGroomingFrequencyParams) Reset() {
	*x = UpsertCustomerGroomingFrequencyParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertCustomerGroomingFrequencyParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertCustomerGroomingFrequencyParams) ProtoMessage() {}

func (x *UpsertCustomerGroomingFrequencyParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertCustomerGroomingFrequencyParams.ProtoReflect.Descriptor instead.
func (*UpsertCustomerGroomingFrequencyParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_rawDescGZIP(), []int{2}
}

func (x *UpsertCustomerGroomingFrequencyParams) GetGroomingFrequency() *v1.TimePeriod {
	if x != nil {
		return x.GroomingFrequency
	}
	return nil
}

func (x *UpsertCustomerGroomingFrequencyParams) GetApplyToAllCustomers() bool {
	if x != nil {
		return x.ApplyToAllCustomers
	}
	return false
}

// upsert customer grooming frequency result
type UpsertCustomerGroomingFrequencyResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpsertCustomerGroomingFrequencyResult) Reset() {
	*x = UpsertCustomerGroomingFrequencyResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertCustomerGroomingFrequencyResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertCustomerGroomingFrequencyResult) ProtoMessage() {}

func (x *UpsertCustomerGroomingFrequencyResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertCustomerGroomingFrequencyResult.ProtoReflect.Descriptor instead.
func (*UpsertCustomerGroomingFrequencyResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_rawDescGZIP(), []int{3}
}

var File_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto protoreflect.FileDescriptor

var file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_rawDesc = []byte{
	0x0a, 0x4e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x24, 0x0a, 0x22, 0x47,
	0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x22, 0x6f, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x49, 0x0a, 0x12, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52,
	0x11, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x79, 0x22, 0xb1, 0x01, 0x0a, 0x25, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x53, 0x0a, 0x12,
	0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x11,
	0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x12, 0x33, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x6f, 0x5f, 0x61, 0x6c,
	0x6c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x6f, 0x41, 0x6c, 0x6c, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x22, 0x27, 0x0a, 0x25, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32,
	0x86, 0x03, 0x0a, 0x29, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x46, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa6, 0x01,
	0x0a, 0x1c, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x42,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xaf, 0x01, 0x0a, 0x1f, 0x55, 0x70, 0x73, 0x65, 0x72,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x45, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65,
	0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x95, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x61, 0x70, 0x69, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_rawDescOnce sync.Once
	file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_rawDescData = file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_rawDesc
)

func file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_rawDescGZIP() []byte {
	file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_rawDescOnce.Do(func() {
		file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_rawDescData)
	})
	return file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_rawDescData
}

var file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_goTypes = []interface{}{
	(*GetCustomerGroomingFrequencyParams)(nil),    // 0: moego.api.business_customer.v1.GetCustomerGroomingFrequencyParams
	(*GetCustomerGroomingFrequencyResult)(nil),    // 1: moego.api.business_customer.v1.GetCustomerGroomingFrequencyResult
	(*UpsertCustomerGroomingFrequencyParams)(nil), // 2: moego.api.business_customer.v1.UpsertCustomerGroomingFrequencyParams
	(*UpsertCustomerGroomingFrequencyResult)(nil), // 3: moego.api.business_customer.v1.UpsertCustomerGroomingFrequencyResult
	(*v1.TimePeriod)(nil),                         // 4: moego.utils.v1.TimePeriod
}
var file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_depIdxs = []int32{
	4, // 0: moego.api.business_customer.v1.GetCustomerGroomingFrequencyResult.grooming_frequency:type_name -> moego.utils.v1.TimePeriod
	4, // 1: moego.api.business_customer.v1.UpsertCustomerGroomingFrequencyParams.grooming_frequency:type_name -> moego.utils.v1.TimePeriod
	0, // 2: moego.api.business_customer.v1.BusinessCustomerPreferredFrequencyService.GetCustomerGroomingFrequency:input_type -> moego.api.business_customer.v1.GetCustomerGroomingFrequencyParams
	2, // 3: moego.api.business_customer.v1.BusinessCustomerPreferredFrequencyService.UpsertCustomerGroomingFrequency:input_type -> moego.api.business_customer.v1.UpsertCustomerGroomingFrequencyParams
	1, // 4: moego.api.business_customer.v1.BusinessCustomerPreferredFrequencyService.GetCustomerGroomingFrequency:output_type -> moego.api.business_customer.v1.GetCustomerGroomingFrequencyResult
	3, // 5: moego.api.business_customer.v1.BusinessCustomerPreferredFrequencyService.UpsertCustomerGroomingFrequency:output_type -> moego.api.business_customer.v1.UpsertCustomerGroomingFrequencyResult
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() {
	file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_init()
}
func file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_init() {
	if File_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerGroomingFrequencyParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerGroomingFrequencyResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertCustomerGroomingFrequencyParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertCustomerGroomingFrequencyResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_goTypes,
		DependencyIndexes: file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_depIdxs,
		MessageInfos:      file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_msgTypes,
	}.Build()
	File_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto = out.File
	file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_rawDesc = nil
	file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_goTypes = nil
	file_moego_api_business_customer_v1_business_customer_preferred_frequency_api_proto_depIdxs = nil
}
