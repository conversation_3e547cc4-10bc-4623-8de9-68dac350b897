// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/business_customer/v1/business_pet_note_api.proto

package businesscustomerapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// pin pet note params
type PinPetNoteParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet note id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pin status
	IsPinned bool `protobuf:"varint,3,opt,name=is_pinned,json=isPinned,proto3" json:"is_pinned,omitempty"`
}

func (x *PinPetNoteParams) Reset() {
	*x = PinPetNoteParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_note_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PinPetNoteParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinPetNoteParams) ProtoMessage() {}

func (x *PinPetNoteParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_note_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinPetNoteParams.ProtoReflect.Descriptor instead.
func (*PinPetNoteParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_note_api_proto_rawDescGZIP(), []int{0}
}

func (x *PinPetNoteParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PinPetNoteParams) GetIsPinned() bool {
	if x != nil {
		return x.IsPinned
	}
	return false
}

// pin pet note result
type PinPetNoteResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet note
	Note *v1.BusinessPetNoteModel `protobuf:"bytes,1,opt,name=note,proto3" json:"note,omitempty"`
}

func (x *PinPetNoteResult) Reset() {
	*x = PinPetNoteResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_note_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PinPetNoteResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinPetNoteResult) ProtoMessage() {}

func (x *PinPetNoteResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_note_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinPetNoteResult.ProtoReflect.Descriptor instead.
func (*PinPetNoteResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_note_api_proto_rawDescGZIP(), []int{1}
}

func (x *PinPetNoteResult) GetNote() *v1.BusinessPetNoteModel {
	if x != nil {
		return x.Note
	}
	return nil
}

var File_moego_api_business_customer_v1_business_pet_note_api_proto protoreflect.FileDescriptor

var file_moego_api_business_customer_v1_business_pet_note_api_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x6f,
	0x74, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x40, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x6f, 0x74,
	0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x48, 0x0a, 0x10, 0x50, 0x69, 0x6e, 0x50, 0x65,
	0x74, 0x4e, 0x6f, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x70, 0x69, 0x6e, 0x6e, 0x65,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x50, 0x69, 0x6e, 0x6e, 0x65,
	0x64, 0x22, 0x5f, 0x0a, 0x10, 0x50, 0x69, 0x6e, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4b, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x6e, 0x6f,
	0x74, 0x65, 0x32, 0x8a, 0x01, 0x0a, 0x16, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50,
	0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x70, 0x0a,
	0x0a, 0x50, 0x69, 0x6e, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x12, 0x30, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x69, 0x6e,
	0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x69, 0x6e, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42,
	0x95, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_business_customer_v1_business_pet_note_api_proto_rawDescOnce sync.Once
	file_moego_api_business_customer_v1_business_pet_note_api_proto_rawDescData = file_moego_api_business_customer_v1_business_pet_note_api_proto_rawDesc
)

func file_moego_api_business_customer_v1_business_pet_note_api_proto_rawDescGZIP() []byte {
	file_moego_api_business_customer_v1_business_pet_note_api_proto_rawDescOnce.Do(func() {
		file_moego_api_business_customer_v1_business_pet_note_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_business_customer_v1_business_pet_note_api_proto_rawDescData)
	})
	return file_moego_api_business_customer_v1_business_pet_note_api_proto_rawDescData
}

var file_moego_api_business_customer_v1_business_pet_note_api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_api_business_customer_v1_business_pet_note_api_proto_goTypes = []interface{}{
	(*PinPetNoteParams)(nil),        // 0: moego.api.business_customer.v1.PinPetNoteParams
	(*PinPetNoteResult)(nil),        // 1: moego.api.business_customer.v1.PinPetNoteResult
	(*v1.BusinessPetNoteModel)(nil), // 2: moego.models.business_customer.v1.BusinessPetNoteModel
}
var file_moego_api_business_customer_v1_business_pet_note_api_proto_depIdxs = []int32{
	2, // 0: moego.api.business_customer.v1.PinPetNoteResult.note:type_name -> moego.models.business_customer.v1.BusinessPetNoteModel
	0, // 1: moego.api.business_customer.v1.BusinessPetNoteService.PinPetNote:input_type -> moego.api.business_customer.v1.PinPetNoteParams
	1, // 2: moego.api.business_customer.v1.BusinessPetNoteService.PinPetNote:output_type -> moego.api.business_customer.v1.PinPetNoteResult
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_api_business_customer_v1_business_pet_note_api_proto_init() }
func file_moego_api_business_customer_v1_business_pet_note_api_proto_init() {
	if File_moego_api_business_customer_v1_business_pet_note_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_business_customer_v1_business_pet_note_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PinPetNoteParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_note_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PinPetNoteResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_business_customer_v1_business_pet_note_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_business_customer_v1_business_pet_note_api_proto_goTypes,
		DependencyIndexes: file_moego_api_business_customer_v1_business_pet_note_api_proto_depIdxs,
		MessageInfos:      file_moego_api_business_customer_v1_business_pet_note_api_proto_msgTypes,
	}.Build()
	File_moego_api_business_customer_v1_business_pet_note_api_proto = out.File
	file_moego_api_business_customer_v1_business_pet_note_api_proto_rawDesc = nil
	file_moego_api_business_customer_v1_business_pet_note_api_proto_goTypes = nil
	file_moego_api_business_customer_v1_business_pet_note_api_proto_depIdxs = nil
}
