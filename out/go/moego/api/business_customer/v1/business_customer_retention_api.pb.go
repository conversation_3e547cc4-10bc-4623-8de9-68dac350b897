// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/business_customer/v1/business_customer_retention_api.proto

package businesscustomerapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// FetchRetentionDataParams
type FetchRetentionDataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter for retention
	RetentionFilter *v1.RetentionFilter `protobuf:"bytes,1,opt,name=retention_filter,json=retentionFilter,proto3" json:"retention_filter,omitempty"`
	// smart-client list filter params as json string
	ClientFilter *string `protobuf:"bytes,2,opt,name=client_filter,json=clientFilter,proto3,oneof" json:"client_filter,omitempty"`
	// pagination params
	Pagination *v2.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// order by columns
	OrderBys []*v2.OrderBy `protobuf:"bytes,4,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
}

func (x *FetchRetentionDataParams) Reset() {
	*x = FetchRetentionDataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_retention_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchRetentionDataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchRetentionDataParams) ProtoMessage() {}

func (x *FetchRetentionDataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_retention_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchRetentionDataParams.ProtoReflect.Descriptor instead.
func (*FetchRetentionDataParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_retention_api_proto_rawDescGZIP(), []int{0}
}

func (x *FetchRetentionDataParams) GetRetentionFilter() *v1.RetentionFilter {
	if x != nil {
		return x.RetentionFilter
	}
	return nil
}

func (x *FetchRetentionDataParams) GetClientFilter() string {
	if x != nil && x.ClientFilter != nil {
		return *x.ClientFilter
	}
	return ""
}

func (x *FetchRetentionDataParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *FetchRetentionDataParams) GetOrderBys() []*v2.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

// FetchRetentionDataResult
type FetchRetentionDataResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// retention data
	Data []*v1.CustomerRetentionData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	// pagination response
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *FetchRetentionDataResult) Reset() {
	*x = FetchRetentionDataResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_customer_retention_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchRetentionDataResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchRetentionDataResult) ProtoMessage() {}

func (x *FetchRetentionDataResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_customer_retention_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchRetentionDataResult.ProtoReflect.Descriptor instead.
func (*FetchRetentionDataResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_customer_retention_api_proto_rawDescGZIP(), []int{1}
}

func (x *FetchRetentionDataResult) GetData() []*v1.CustomerRetentionData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *FetchRetentionDataResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

var File_moego_api_business_customer_v1_business_customer_retention_api_proto protoreflect.FileDescriptor

var file_moego_api_business_customer_v1_business_customer_retention_api_proto_rawDesc = []byte{
	0x0a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x48, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x74, 0x65,
	0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xae, 0x02, 0x0a, 0x18, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x74,
	0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x5d, 0x0a, 0x10, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x0f,
	0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x28, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x09,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42,
	0x79, 0x73, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x22, 0xac, 0x01, 0x0a, 0x18, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x4c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x74, 0x65,
	0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x32, 0xad, 0x01, 0x0a, 0x20, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x12, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x42, 0x95, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_api_business_customer_v1_business_customer_retention_api_proto_rawDescOnce sync.Once
	file_moego_api_business_customer_v1_business_customer_retention_api_proto_rawDescData = file_moego_api_business_customer_v1_business_customer_retention_api_proto_rawDesc
)

func file_moego_api_business_customer_v1_business_customer_retention_api_proto_rawDescGZIP() []byte {
	file_moego_api_business_customer_v1_business_customer_retention_api_proto_rawDescOnce.Do(func() {
		file_moego_api_business_customer_v1_business_customer_retention_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_business_customer_v1_business_customer_retention_api_proto_rawDescData)
	})
	return file_moego_api_business_customer_v1_business_customer_retention_api_proto_rawDescData
}

var file_moego_api_business_customer_v1_business_customer_retention_api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_api_business_customer_v1_business_customer_retention_api_proto_goTypes = []interface{}{
	(*FetchRetentionDataParams)(nil), // 0: moego.api.business_customer.v1.FetchRetentionDataParams
	(*FetchRetentionDataResult)(nil), // 1: moego.api.business_customer.v1.FetchRetentionDataResult
	(*v1.RetentionFilter)(nil),       // 2: moego.models.business_customer.v1.RetentionFilter
	(*v2.PaginationRequest)(nil),     // 3: moego.utils.v2.PaginationRequest
	(*v2.OrderBy)(nil),               // 4: moego.utils.v2.OrderBy
	(*v1.CustomerRetentionData)(nil), // 5: moego.models.business_customer.v1.CustomerRetentionData
	(*v2.PaginationResponse)(nil),    // 6: moego.utils.v2.PaginationResponse
}
var file_moego_api_business_customer_v1_business_customer_retention_api_proto_depIdxs = []int32{
	2, // 0: moego.api.business_customer.v1.FetchRetentionDataParams.retention_filter:type_name -> moego.models.business_customer.v1.RetentionFilter
	3, // 1: moego.api.business_customer.v1.FetchRetentionDataParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	4, // 2: moego.api.business_customer.v1.FetchRetentionDataParams.order_bys:type_name -> moego.utils.v2.OrderBy
	5, // 3: moego.api.business_customer.v1.FetchRetentionDataResult.data:type_name -> moego.models.business_customer.v1.CustomerRetentionData
	6, // 4: moego.api.business_customer.v1.FetchRetentionDataResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	0, // 5: moego.api.business_customer.v1.BusinessCustomerRetentionService.FetchRetentionData:input_type -> moego.api.business_customer.v1.FetchRetentionDataParams
	1, // 6: moego.api.business_customer.v1.BusinessCustomerRetentionService.FetchRetentionData:output_type -> moego.api.business_customer.v1.FetchRetentionDataResult
	6, // [6:7] is the sub-list for method output_type
	5, // [5:6] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_api_business_customer_v1_business_customer_retention_api_proto_init() }
func file_moego_api_business_customer_v1_business_customer_retention_api_proto_init() {
	if File_moego_api_business_customer_v1_business_customer_retention_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_business_customer_v1_business_customer_retention_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchRetentionDataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_customer_retention_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchRetentionDataResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_business_customer_v1_business_customer_retention_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_business_customer_v1_business_customer_retention_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_business_customer_v1_business_customer_retention_api_proto_goTypes,
		DependencyIndexes: file_moego_api_business_customer_v1_business_customer_retention_api_proto_depIdxs,
		MessageInfos:      file_moego_api_business_customer_v1_business_customer_retention_api_proto_msgTypes,
	}.Build()
	File_moego_api_business_customer_v1_business_customer_retention_api_proto = out.File
	file_moego_api_business_customer_v1_business_customer_retention_api_proto_rawDesc = nil
	file_moego_api_business_customer_v1_business_customer_retention_api_proto_goTypes = nil
	file_moego_api_business_customer_v1_business_customer_retention_api_proto_depIdxs = nil
}
