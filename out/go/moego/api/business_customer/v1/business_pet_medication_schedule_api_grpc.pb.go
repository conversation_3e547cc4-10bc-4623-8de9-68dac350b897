// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/business_customer/v1/business_pet_medication_schedule_api.proto

package businesscustomerapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessPetMedicationScheduleServiceClient is the client API for BusinessPetMedicationScheduleService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessPetMedicationScheduleServiceClient interface {
	// Create medication schedule
	// Medication display rules: {Medication schedule} {Amount} {Medication unit} {Medication name} {Medication notes}
	CreateMedicationSchedule(ctx context.Context, in *CreateMedicationScheduleParams, opts ...grpc.CallOption) (*CreateMedicationScheduleResult, error)
	// Update medication schedule
	UpdateMedicationSchedule(ctx context.Context, in *UpdateMedicationScheduleParams, opts ...grpc.CallOption) (*UpdateMedicationScheduleResult, error)
	// Delete medication schedule
	DeleteMedicationSchedule(ctx context.Context, in *DeleteMedicationScheduleParams, opts ...grpc.CallOption) (*DeleteMedicationScheduleResult, error)
	// List pet's medication schedule
	ListPetMedicationSchedule(ctx context.Context, in *ListPetMedicationScheduleParams, opts ...grpc.CallOption) (*ListPetMedicationScheduleResult, error)
}

type businessPetMedicationScheduleServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessPetMedicationScheduleServiceClient(cc grpc.ClientConnInterface) BusinessPetMedicationScheduleServiceClient {
	return &businessPetMedicationScheduleServiceClient{cc}
}

func (c *businessPetMedicationScheduleServiceClient) CreateMedicationSchedule(ctx context.Context, in *CreateMedicationScheduleParams, opts ...grpc.CallOption) (*CreateMedicationScheduleResult, error) {
	out := new(CreateMedicationScheduleResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetMedicationScheduleService/CreateMedicationSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetMedicationScheduleServiceClient) UpdateMedicationSchedule(ctx context.Context, in *UpdateMedicationScheduleParams, opts ...grpc.CallOption) (*UpdateMedicationScheduleResult, error) {
	out := new(UpdateMedicationScheduleResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetMedicationScheduleService/UpdateMedicationSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetMedicationScheduleServiceClient) DeleteMedicationSchedule(ctx context.Context, in *DeleteMedicationScheduleParams, opts ...grpc.CallOption) (*DeleteMedicationScheduleResult, error) {
	out := new(DeleteMedicationScheduleResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetMedicationScheduleService/DeleteMedicationSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetMedicationScheduleServiceClient) ListPetMedicationSchedule(ctx context.Context, in *ListPetMedicationScheduleParams, opts ...grpc.CallOption) (*ListPetMedicationScheduleResult, error) {
	out := new(ListPetMedicationScheduleResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetMedicationScheduleService/ListPetMedicationSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessPetMedicationScheduleServiceServer is the server API for BusinessPetMedicationScheduleService service.
// All implementations must embed UnimplementedBusinessPetMedicationScheduleServiceServer
// for forward compatibility
type BusinessPetMedicationScheduleServiceServer interface {
	// Create medication schedule
	// Medication display rules: {Medication schedule} {Amount} {Medication unit} {Medication name} {Medication notes}
	CreateMedicationSchedule(context.Context, *CreateMedicationScheduleParams) (*CreateMedicationScheduleResult, error)
	// Update medication schedule
	UpdateMedicationSchedule(context.Context, *UpdateMedicationScheduleParams) (*UpdateMedicationScheduleResult, error)
	// Delete medication schedule
	DeleteMedicationSchedule(context.Context, *DeleteMedicationScheduleParams) (*DeleteMedicationScheduleResult, error)
	// List pet's medication schedule
	ListPetMedicationSchedule(context.Context, *ListPetMedicationScheduleParams) (*ListPetMedicationScheduleResult, error)
	mustEmbedUnimplementedBusinessPetMedicationScheduleServiceServer()
}

// UnimplementedBusinessPetMedicationScheduleServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessPetMedicationScheduleServiceServer struct {
}

func (UnimplementedBusinessPetMedicationScheduleServiceServer) CreateMedicationSchedule(context.Context, *CreateMedicationScheduleParams) (*CreateMedicationScheduleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMedicationSchedule not implemented")
}
func (UnimplementedBusinessPetMedicationScheduleServiceServer) UpdateMedicationSchedule(context.Context, *UpdateMedicationScheduleParams) (*UpdateMedicationScheduleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMedicationSchedule not implemented")
}
func (UnimplementedBusinessPetMedicationScheduleServiceServer) DeleteMedicationSchedule(context.Context, *DeleteMedicationScheduleParams) (*DeleteMedicationScheduleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMedicationSchedule not implemented")
}
func (UnimplementedBusinessPetMedicationScheduleServiceServer) ListPetMedicationSchedule(context.Context, *ListPetMedicationScheduleParams) (*ListPetMedicationScheduleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetMedicationSchedule not implemented")
}
func (UnimplementedBusinessPetMedicationScheduleServiceServer) mustEmbedUnimplementedBusinessPetMedicationScheduleServiceServer() {
}

// UnsafeBusinessPetMedicationScheduleServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessPetMedicationScheduleServiceServer will
// result in compilation errors.
type UnsafeBusinessPetMedicationScheduleServiceServer interface {
	mustEmbedUnimplementedBusinessPetMedicationScheduleServiceServer()
}

func RegisterBusinessPetMedicationScheduleServiceServer(s grpc.ServiceRegistrar, srv BusinessPetMedicationScheduleServiceServer) {
	s.RegisterService(&BusinessPetMedicationScheduleService_ServiceDesc, srv)
}

func _BusinessPetMedicationScheduleService_CreateMedicationSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMedicationScheduleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetMedicationScheduleServiceServer).CreateMedicationSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetMedicationScheduleService/CreateMedicationSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetMedicationScheduleServiceServer).CreateMedicationSchedule(ctx, req.(*CreateMedicationScheduleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetMedicationScheduleService_UpdateMedicationSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMedicationScheduleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetMedicationScheduleServiceServer).UpdateMedicationSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetMedicationScheduleService/UpdateMedicationSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetMedicationScheduleServiceServer).UpdateMedicationSchedule(ctx, req.(*UpdateMedicationScheduleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetMedicationScheduleService_DeleteMedicationSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMedicationScheduleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetMedicationScheduleServiceServer).DeleteMedicationSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetMedicationScheduleService/DeleteMedicationSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetMedicationScheduleServiceServer).DeleteMedicationSchedule(ctx, req.(*DeleteMedicationScheduleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetMedicationScheduleService_ListPetMedicationSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetMedicationScheduleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetMedicationScheduleServiceServer).ListPetMedicationSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetMedicationScheduleService/ListPetMedicationSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetMedicationScheduleServiceServer).ListPetMedicationSchedule(ctx, req.(*ListPetMedicationScheduleParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessPetMedicationScheduleService_ServiceDesc is the grpc.ServiceDesc for BusinessPetMedicationScheduleService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessPetMedicationScheduleService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.business_customer.v1.BusinessPetMedicationScheduleService",
	HandlerType: (*BusinessPetMedicationScheduleServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateMedicationSchedule",
			Handler:    _BusinessPetMedicationScheduleService_CreateMedicationSchedule_Handler,
		},
		{
			MethodName: "UpdateMedicationSchedule",
			Handler:    _BusinessPetMedicationScheduleService_UpdateMedicationSchedule_Handler,
		},
		{
			MethodName: "DeleteMedicationSchedule",
			Handler:    _BusinessPetMedicationScheduleService_DeleteMedicationSchedule_Handler,
		},
		{
			MethodName: "ListPetMedicationSchedule",
			Handler:    _BusinessPetMedicationScheduleService_ListPetMedicationSchedule_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/business_customer/v1/business_pet_medication_schedule_api.proto",
}
