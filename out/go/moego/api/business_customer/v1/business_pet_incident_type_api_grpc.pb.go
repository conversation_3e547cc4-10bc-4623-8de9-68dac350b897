// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/business_customer/v1/business_pet_incident_type_api.proto

package businesscustomerapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessPetIncidentTypeServiceClient is the client API for BusinessPetIncidentTypeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessPetIncidentTypeServiceClient interface {
	// List pet incident types of current company
	ListPetIncidentType(ctx context.Context, in *ListPetIncidentTypeParams, opts ...grpc.CallOption) (*ListPetIncidentTypeResult, error)
	// Create a pet incident type
	CreatePetIncidentType(ctx context.Context, in *CreatePetIncidentTypeParams, opts ...grpc.CallOption) (*CreatePetIncidentTypeResult, error)
	// Update a pet incident type
	UpdatePetIncidentType(ctx context.Context, in *UpdatePetIncidentTypeParams, opts ...grpc.CallOption) (*UpdatePetIncidentTypeResult, error)
	// Sort pet incident types
	SortPetIncidentType(ctx context.Context, in *SortPetIncidentTypeParams, opts ...grpc.CallOption) (*SortPetIncidentTypeResult, error)
	// Delete a pet incident type
	DeletePetIncidentType(ctx context.Context, in *DeletePetIncidentTypeParams, opts ...grpc.CallOption) (*DeletePetIncidentTypeResult, error)
}

type businessPetIncidentTypeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessPetIncidentTypeServiceClient(cc grpc.ClientConnInterface) BusinessPetIncidentTypeServiceClient {
	return &businessPetIncidentTypeServiceClient{cc}
}

func (c *businessPetIncidentTypeServiceClient) ListPetIncidentType(ctx context.Context, in *ListPetIncidentTypeParams, opts ...grpc.CallOption) (*ListPetIncidentTypeResult, error) {
	out := new(ListPetIncidentTypeResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetIncidentTypeService/ListPetIncidentType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetIncidentTypeServiceClient) CreatePetIncidentType(ctx context.Context, in *CreatePetIncidentTypeParams, opts ...grpc.CallOption) (*CreatePetIncidentTypeResult, error) {
	out := new(CreatePetIncidentTypeResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetIncidentTypeService/CreatePetIncidentType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetIncidentTypeServiceClient) UpdatePetIncidentType(ctx context.Context, in *UpdatePetIncidentTypeParams, opts ...grpc.CallOption) (*UpdatePetIncidentTypeResult, error) {
	out := new(UpdatePetIncidentTypeResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetIncidentTypeService/UpdatePetIncidentType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetIncidentTypeServiceClient) SortPetIncidentType(ctx context.Context, in *SortPetIncidentTypeParams, opts ...grpc.CallOption) (*SortPetIncidentTypeResult, error) {
	out := new(SortPetIncidentTypeResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetIncidentTypeService/SortPetIncidentType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetIncidentTypeServiceClient) DeletePetIncidentType(ctx context.Context, in *DeletePetIncidentTypeParams, opts ...grpc.CallOption) (*DeletePetIncidentTypeResult, error) {
	out := new(DeletePetIncidentTypeResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetIncidentTypeService/DeletePetIncidentType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessPetIncidentTypeServiceServer is the server API for BusinessPetIncidentTypeService service.
// All implementations must embed UnimplementedBusinessPetIncidentTypeServiceServer
// for forward compatibility
type BusinessPetIncidentTypeServiceServer interface {
	// List pet incident types of current company
	ListPetIncidentType(context.Context, *ListPetIncidentTypeParams) (*ListPetIncidentTypeResult, error)
	// Create a pet incident type
	CreatePetIncidentType(context.Context, *CreatePetIncidentTypeParams) (*CreatePetIncidentTypeResult, error)
	// Update a pet incident type
	UpdatePetIncidentType(context.Context, *UpdatePetIncidentTypeParams) (*UpdatePetIncidentTypeResult, error)
	// Sort pet incident types
	SortPetIncidentType(context.Context, *SortPetIncidentTypeParams) (*SortPetIncidentTypeResult, error)
	// Delete a pet incident type
	DeletePetIncidentType(context.Context, *DeletePetIncidentTypeParams) (*DeletePetIncidentTypeResult, error)
	mustEmbedUnimplementedBusinessPetIncidentTypeServiceServer()
}

// UnimplementedBusinessPetIncidentTypeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessPetIncidentTypeServiceServer struct {
}

func (UnimplementedBusinessPetIncidentTypeServiceServer) ListPetIncidentType(context.Context, *ListPetIncidentTypeParams) (*ListPetIncidentTypeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetIncidentType not implemented")
}
func (UnimplementedBusinessPetIncidentTypeServiceServer) CreatePetIncidentType(context.Context, *CreatePetIncidentTypeParams) (*CreatePetIncidentTypeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetIncidentType not implemented")
}
func (UnimplementedBusinessPetIncidentTypeServiceServer) UpdatePetIncidentType(context.Context, *UpdatePetIncidentTypeParams) (*UpdatePetIncidentTypeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetIncidentType not implemented")
}
func (UnimplementedBusinessPetIncidentTypeServiceServer) SortPetIncidentType(context.Context, *SortPetIncidentTypeParams) (*SortPetIncidentTypeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortPetIncidentType not implemented")
}
func (UnimplementedBusinessPetIncidentTypeServiceServer) DeletePetIncidentType(context.Context, *DeletePetIncidentTypeParams) (*DeletePetIncidentTypeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePetIncidentType not implemented")
}
func (UnimplementedBusinessPetIncidentTypeServiceServer) mustEmbedUnimplementedBusinessPetIncidentTypeServiceServer() {
}

// UnsafeBusinessPetIncidentTypeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessPetIncidentTypeServiceServer will
// result in compilation errors.
type UnsafeBusinessPetIncidentTypeServiceServer interface {
	mustEmbedUnimplementedBusinessPetIncidentTypeServiceServer()
}

func RegisterBusinessPetIncidentTypeServiceServer(s grpc.ServiceRegistrar, srv BusinessPetIncidentTypeServiceServer) {
	s.RegisterService(&BusinessPetIncidentTypeService_ServiceDesc, srv)
}

func _BusinessPetIncidentTypeService_ListPetIncidentType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetIncidentTypeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetIncidentTypeServiceServer).ListPetIncidentType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetIncidentTypeService/ListPetIncidentType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetIncidentTypeServiceServer).ListPetIncidentType(ctx, req.(*ListPetIncidentTypeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetIncidentTypeService_CreatePetIncidentType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetIncidentTypeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetIncidentTypeServiceServer).CreatePetIncidentType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetIncidentTypeService/CreatePetIncidentType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetIncidentTypeServiceServer).CreatePetIncidentType(ctx, req.(*CreatePetIncidentTypeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetIncidentTypeService_UpdatePetIncidentType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetIncidentTypeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetIncidentTypeServiceServer).UpdatePetIncidentType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetIncidentTypeService/UpdatePetIncidentType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetIncidentTypeServiceServer).UpdatePetIncidentType(ctx, req.(*UpdatePetIncidentTypeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetIncidentTypeService_SortPetIncidentType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortPetIncidentTypeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetIncidentTypeServiceServer).SortPetIncidentType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetIncidentTypeService/SortPetIncidentType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetIncidentTypeServiceServer).SortPetIncidentType(ctx, req.(*SortPetIncidentTypeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetIncidentTypeService_DeletePetIncidentType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetIncidentTypeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetIncidentTypeServiceServer).DeletePetIncidentType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetIncidentTypeService/DeletePetIncidentType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetIncidentTypeServiceServer).DeletePetIncidentType(ctx, req.(*DeletePetIncidentTypeParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessPetIncidentTypeService_ServiceDesc is the grpc.ServiceDesc for BusinessPetIncidentTypeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessPetIncidentTypeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.business_customer.v1.BusinessPetIncidentTypeService",
	HandlerType: (*BusinessPetIncidentTypeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListPetIncidentType",
			Handler:    _BusinessPetIncidentTypeService_ListPetIncidentType_Handler,
		},
		{
			MethodName: "CreatePetIncidentType",
			Handler:    _BusinessPetIncidentTypeService_CreatePetIncidentType_Handler,
		},
		{
			MethodName: "UpdatePetIncidentType",
			Handler:    _BusinessPetIncidentTypeService_UpdatePetIncidentType_Handler,
		},
		{
			MethodName: "SortPetIncidentType",
			Handler:    _BusinessPetIncidentTypeService_SortPetIncidentType_Handler,
		},
		{
			MethodName: "DeletePetIncidentType",
			Handler:    _BusinessPetIncidentTypeService_DeletePetIncidentType_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/business_customer/v1/business_pet_incident_type_api.proto",
}
