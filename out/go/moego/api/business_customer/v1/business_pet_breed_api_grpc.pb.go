// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/business_customer/v1/business_pet_breed_api.proto

package businesscustomerapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessPetBreedServiceClient is the client API for BusinessPetBreedService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessPetBreedServiceClient interface {
	// List pet breeds of current company and given pet type
	ListPetBreed(ctx context.Context, in *ListPetBreedParams, opts ...grpc.CallOption) (*ListPetBreedResult, error)
	// Batch upsert pet breeds
	BatchUpsertPetBreed(ctx context.Context, in *BatchUpsertPetBreedParams, opts ...grpc.CallOption) (*BatchUpsertPetBreedResult, error)
	// Add a pet breed
	CreatePetBreed(ctx context.Context, in *CreatePetBreedParams, opts ...grpc.CallOption) (*CreatePetBreedResult, error)
	// Update a pet breed
	UpdatePetBreed(ctx context.Context, in *UpdatePetBreedParams, opts ...grpc.CallOption) (*UpdatePetBreedResult, error)
	// Delete a pet breed
	DeletePetBreed(ctx context.Context, in *DeletePetBreedParams, opts ...grpc.CallOption) (*DeletePetBreedResult, error)
}

type businessPetBreedServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessPetBreedServiceClient(cc grpc.ClientConnInterface) BusinessPetBreedServiceClient {
	return &businessPetBreedServiceClient{cc}
}

func (c *businessPetBreedServiceClient) ListPetBreed(ctx context.Context, in *ListPetBreedParams, opts ...grpc.CallOption) (*ListPetBreedResult, error) {
	out := new(ListPetBreedResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetBreedService/ListPetBreed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetBreedServiceClient) BatchUpsertPetBreed(ctx context.Context, in *BatchUpsertPetBreedParams, opts ...grpc.CallOption) (*BatchUpsertPetBreedResult, error) {
	out := new(BatchUpsertPetBreedResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetBreedService/BatchUpsertPetBreed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetBreedServiceClient) CreatePetBreed(ctx context.Context, in *CreatePetBreedParams, opts ...grpc.CallOption) (*CreatePetBreedResult, error) {
	out := new(CreatePetBreedResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetBreedService/CreatePetBreed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetBreedServiceClient) UpdatePetBreed(ctx context.Context, in *UpdatePetBreedParams, opts ...grpc.CallOption) (*UpdatePetBreedResult, error) {
	out := new(UpdatePetBreedResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetBreedService/UpdatePetBreed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetBreedServiceClient) DeletePetBreed(ctx context.Context, in *DeletePetBreedParams, opts ...grpc.CallOption) (*DeletePetBreedResult, error) {
	out := new(DeletePetBreedResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetBreedService/DeletePetBreed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessPetBreedServiceServer is the server API for BusinessPetBreedService service.
// All implementations must embed UnimplementedBusinessPetBreedServiceServer
// for forward compatibility
type BusinessPetBreedServiceServer interface {
	// List pet breeds of current company and given pet type
	ListPetBreed(context.Context, *ListPetBreedParams) (*ListPetBreedResult, error)
	// Batch upsert pet breeds
	BatchUpsertPetBreed(context.Context, *BatchUpsertPetBreedParams) (*BatchUpsertPetBreedResult, error)
	// Add a pet breed
	CreatePetBreed(context.Context, *CreatePetBreedParams) (*CreatePetBreedResult, error)
	// Update a pet breed
	UpdatePetBreed(context.Context, *UpdatePetBreedParams) (*UpdatePetBreedResult, error)
	// Delete a pet breed
	DeletePetBreed(context.Context, *DeletePetBreedParams) (*DeletePetBreedResult, error)
	mustEmbedUnimplementedBusinessPetBreedServiceServer()
}

// UnimplementedBusinessPetBreedServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessPetBreedServiceServer struct {
}

func (UnimplementedBusinessPetBreedServiceServer) ListPetBreed(context.Context, *ListPetBreedParams) (*ListPetBreedResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetBreed not implemented")
}
func (UnimplementedBusinessPetBreedServiceServer) BatchUpsertPetBreed(context.Context, *BatchUpsertPetBreedParams) (*BatchUpsertPetBreedResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpsertPetBreed not implemented")
}
func (UnimplementedBusinessPetBreedServiceServer) CreatePetBreed(context.Context, *CreatePetBreedParams) (*CreatePetBreedResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetBreed not implemented")
}
func (UnimplementedBusinessPetBreedServiceServer) UpdatePetBreed(context.Context, *UpdatePetBreedParams) (*UpdatePetBreedResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetBreed not implemented")
}
func (UnimplementedBusinessPetBreedServiceServer) DeletePetBreed(context.Context, *DeletePetBreedParams) (*DeletePetBreedResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePetBreed not implemented")
}
func (UnimplementedBusinessPetBreedServiceServer) mustEmbedUnimplementedBusinessPetBreedServiceServer() {
}

// UnsafeBusinessPetBreedServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessPetBreedServiceServer will
// result in compilation errors.
type UnsafeBusinessPetBreedServiceServer interface {
	mustEmbedUnimplementedBusinessPetBreedServiceServer()
}

func RegisterBusinessPetBreedServiceServer(s grpc.ServiceRegistrar, srv BusinessPetBreedServiceServer) {
	s.RegisterService(&BusinessPetBreedService_ServiceDesc, srv)
}

func _BusinessPetBreedService_ListPetBreed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetBreedParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBreedServiceServer).ListPetBreed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetBreedService/ListPetBreed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBreedServiceServer).ListPetBreed(ctx, req.(*ListPetBreedParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetBreedService_BatchUpsertPetBreed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpsertPetBreedParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBreedServiceServer).BatchUpsertPetBreed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetBreedService/BatchUpsertPetBreed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBreedServiceServer).BatchUpsertPetBreed(ctx, req.(*BatchUpsertPetBreedParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetBreedService_CreatePetBreed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetBreedParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBreedServiceServer).CreatePetBreed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetBreedService/CreatePetBreed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBreedServiceServer).CreatePetBreed(ctx, req.(*CreatePetBreedParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetBreedService_UpdatePetBreed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetBreedParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBreedServiceServer).UpdatePetBreed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetBreedService/UpdatePetBreed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBreedServiceServer).UpdatePetBreed(ctx, req.(*UpdatePetBreedParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetBreedService_DeletePetBreed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetBreedParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetBreedServiceServer).DeletePetBreed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetBreedService/DeletePetBreed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetBreedServiceServer).DeletePetBreed(ctx, req.(*DeletePetBreedParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessPetBreedService_ServiceDesc is the grpc.ServiceDesc for BusinessPetBreedService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessPetBreedService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.business_customer.v1.BusinessPetBreedService",
	HandlerType: (*BusinessPetBreedServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListPetBreed",
			Handler:    _BusinessPetBreedService_ListPetBreed_Handler,
		},
		{
			MethodName: "BatchUpsertPetBreed",
			Handler:    _BusinessPetBreedService_BatchUpsertPetBreed_Handler,
		},
		{
			MethodName: "CreatePetBreed",
			Handler:    _BusinessPetBreedService_CreatePetBreed_Handler,
		},
		{
			MethodName: "UpdatePetBreed",
			Handler:    _BusinessPetBreedService_UpdatePetBreed_Handler,
		},
		{
			MethodName: "DeletePetBreed",
			Handler:    _BusinessPetBreedService_DeletePetBreed_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/business_customer/v1/business_pet_breed_api.proto",
}
