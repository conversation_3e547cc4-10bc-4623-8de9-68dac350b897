// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/business_customer/v1/business_pet_breed_api.proto

package businesscustomerapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list pet breed params
type ListPetBreedParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet type id
	PetTypeId v1.PetType `protobuf:"varint,1,opt,name=pet_type_id,json=petTypeId,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type_id,omitempty"`
}

func (x *ListPetBreedParams) Reset() {
	*x = ListPetBreedParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetBreedParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetBreedParams) ProtoMessage() {}

func (x *ListPetBreedParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetBreedParams.ProtoReflect.Descriptor instead.
func (*ListPetBreedParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescGZIP(), []int{0}
}

func (x *ListPetBreedParams) GetPetTypeId() v1.PetType {
	if x != nil {
		return x.PetTypeId
	}
	return v1.PetType(0)
}

// list pet breed result
type ListPetBreedResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet breed list
	Breeds []*v11.BusinessPetBreedModel `protobuf:"bytes,1,rep,name=breeds,proto3" json:"breeds,omitempty"`
}

func (x *ListPetBreedResult) Reset() {
	*x = ListPetBreedResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetBreedResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetBreedResult) ProtoMessage() {}

func (x *ListPetBreedResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetBreedResult.ProtoReflect.Descriptor instead.
func (*ListPetBreedResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListPetBreedResult) GetBreeds() []*v11.BusinessPetBreedModel {
	if x != nil {
		return x.Breeds
	}
	return nil
}

// batch upsert pet breed params
type BatchUpsertPetBreedParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet type id
	PetTypeId v1.PetType `protobuf:"varint,1,opt,name=pet_type_id,json=petTypeId,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type_id,omitempty"`
	// pet breeds to create, can be empty
	BreedsToCreate []*v11.BusinessPetBreedUpsertDef `protobuf:"bytes,5,rep,name=breeds_to_create,json=breedsToCreate,proto3" json:"breeds_to_create,omitempty"`
	// pet breeds to update, can be empty
	BreedsToUpdate map[int64]*v11.BusinessPetBreedUpsertDef `protobuf:"bytes,6,rep,name=breeds_to_update,json=breedsToUpdate,proto3" json:"breeds_to_update,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BatchUpsertPetBreedParams) Reset() {
	*x = BatchUpsertPetBreedParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpsertPetBreedParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpsertPetBreedParams) ProtoMessage() {}

func (x *BatchUpsertPetBreedParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpsertPetBreedParams.ProtoReflect.Descriptor instead.
func (*BatchUpsertPetBreedParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescGZIP(), []int{2}
}

func (x *BatchUpsertPetBreedParams) GetPetTypeId() v1.PetType {
	if x != nil {
		return x.PetTypeId
	}
	return v1.PetType(0)
}

func (x *BatchUpsertPetBreedParams) GetBreedsToCreate() []*v11.BusinessPetBreedUpsertDef {
	if x != nil {
		return x.BreedsToCreate
	}
	return nil
}

func (x *BatchUpsertPetBreedParams) GetBreedsToUpdate() map[int64]*v11.BusinessPetBreedUpsertDef {
	if x != nil {
		return x.BreedsToUpdate
	}
	return nil
}

// batch upsert pet breed result
type BatchUpsertPetBreedResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchUpsertPetBreedResult) Reset() {
	*x = BatchUpsertPetBreedResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpsertPetBreedResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpsertPetBreedResult) ProtoMessage() {}

func (x *BatchUpsertPetBreedResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpsertPetBreedResult.ProtoReflect.Descriptor instead.
func (*BatchUpsertPetBreedResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescGZIP(), []int{3}
}

// create pet breed params
type CreatePetBreedParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet type id
	PetTypeId v1.PetType `protobuf:"varint,1,opt,name=pet_type_id,json=petTypeId,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type_id,omitempty"`
	// pet breed to create
	Breed *v11.BusinessPetBreedCreateDef `protobuf:"bytes,2,opt,name=breed,proto3" json:"breed,omitempty"`
}

func (x *CreatePetBreedParams) Reset() {
	*x = CreatePetBreedParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetBreedParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetBreedParams) ProtoMessage() {}

func (x *CreatePetBreedParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetBreedParams.ProtoReflect.Descriptor instead.
func (*CreatePetBreedParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescGZIP(), []int{4}
}

func (x *CreatePetBreedParams) GetPetTypeId() v1.PetType {
	if x != nil {
		return x.PetTypeId
	}
	return v1.PetType(0)
}

func (x *CreatePetBreedParams) GetBreed() *v11.BusinessPetBreedCreateDef {
	if x != nil {
		return x.Breed
	}
	return nil
}

// create pet breed result
type CreatePetBreedResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet breed
	Breed *v11.BusinessPetBreedModel `protobuf:"bytes,1,opt,name=breed,proto3" json:"breed,omitempty"`
}

func (x *CreatePetBreedResult) Reset() {
	*x = CreatePetBreedResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetBreedResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetBreedResult) ProtoMessage() {}

func (x *CreatePetBreedResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetBreedResult.ProtoReflect.Descriptor instead.
func (*CreatePetBreedResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescGZIP(), []int{5}
}

func (x *CreatePetBreedResult) GetBreed() *v11.BusinessPetBreedModel {
	if x != nil {
		return x.Breed
	}
	return nil
}

// update pet breed params
type UpdatePetBreedParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet breed id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet breed to update
	Breed *v11.BusinessPetBreedUpdateDef `protobuf:"bytes,2,opt,name=breed,proto3" json:"breed,omitempty"`
}

func (x *UpdatePetBreedParams) Reset() {
	*x = UpdatePetBreedParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetBreedParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetBreedParams) ProtoMessage() {}

func (x *UpdatePetBreedParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetBreedParams.ProtoReflect.Descriptor instead.
func (*UpdatePetBreedParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescGZIP(), []int{6}
}

func (x *UpdatePetBreedParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePetBreedParams) GetBreed() *v11.BusinessPetBreedUpdateDef {
	if x != nil {
		return x.Breed
	}
	return nil
}

// update pet breed result
type UpdatePetBreedResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdatePetBreedResult) Reset() {
	*x = UpdatePetBreedResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetBreedResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetBreedResult) ProtoMessage() {}

func (x *UpdatePetBreedResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetBreedResult.ProtoReflect.Descriptor instead.
func (*UpdatePetBreedResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescGZIP(), []int{7}
}

// delete pet breed params
type DeletePetBreedParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet breed id
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletePetBreedParams) Reset() {
	*x = DeletePetBreedParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetBreedParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetBreedParams) ProtoMessage() {}

func (x *DeletePetBreedParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetBreedParams.ProtoReflect.Descriptor instead.
func (*DeletePetBreedParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescGZIP(), []int{8}
}

func (x *DeletePetBreedParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete pet breed result
type DeletePetBreedResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePetBreedResult) Reset() {
	*x = DeletePetBreedResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetBreedResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetBreedResult) ProtoMessage() {}

func (x *DeletePetBreedResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetBreedResult.ProtoReflect.Descriptor instead.
func (*DeletePetBreedResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescGZIP(), []int{9}
}

var File_moego_api_business_customer_v1_business_pet_breed_api_proto protoreflect.FileDescriptor

var file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72,
	0x65, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x3f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72,
	0x65, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x41,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x62,
	0x72, 0x65, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x63, 0x0a,
	0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x4d, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x09, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x49, 0x64, 0x22, 0x66, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65,
	0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x50, 0x0a, 0x06, 0x62, 0x72, 0x65, 0x65,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x06, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x22, 0xcc, 0x03, 0x0a, 0x19, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65,
	0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x4d, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x09, 0x70, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x66, 0x0a, 0x10, 0x62, 0x72, 0x65, 0x65, 0x64,
	0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65,
	0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x65, 0x66, 0x52,
	0x0e, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x54, 0x6f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12,
	0x77, 0x0a, 0x10, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x2e, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x54, 0x6f, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73,
	0x54, 0x6f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x1a, 0x7f, 0x0a, 0x13, 0x42, 0x72, 0x65, 0x65,
	0x64, 0x73, 0x54, 0x6f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x52, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74,
	0x42, 0x72, 0x65, 0x65, 0x64, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x65, 0x66, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x1b, 0x0a, 0x19, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xc3, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x4d, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x52, 0x09, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x5c,
	0x0a, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65,
	0x65, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x22, 0x66, 0x0a, 0x14,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x4e, 0x0a, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x62,
	0x72, 0x65, 0x65, 0x64, 0x22, 0x8d, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x5c, 0x0a, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x62,
	0x72, 0x65, 0x65, 0x64, 0x22, 0x16, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65,
	0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2f, 0x0a, 0x14,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x16, 0x0a,
	0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0x99, 0x05, 0x0a, 0x17, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x76, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65,
	0x64, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72,
	0x65, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8b, 0x01, 0x0a, 0x13, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65,
	0x64, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x65,
	0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65,
	0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7c, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7c, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x7c, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74,
	0x42, 0x72, 0x65, 0x65, 0x64, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74,
	0x42, 0x72, 0x65, 0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x42, 0x95, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescOnce sync.Once
	file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescData = file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDesc
)

func file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescGZIP() []byte {
	file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescOnce.Do(func() {
		file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescData)
	})
	return file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDescData
}

var file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_moego_api_business_customer_v1_business_pet_breed_api_proto_goTypes = []interface{}{
	(*ListPetBreedParams)(nil),            // 0: moego.api.business_customer.v1.ListPetBreedParams
	(*ListPetBreedResult)(nil),            // 1: moego.api.business_customer.v1.ListPetBreedResult
	(*BatchUpsertPetBreedParams)(nil),     // 2: moego.api.business_customer.v1.BatchUpsertPetBreedParams
	(*BatchUpsertPetBreedResult)(nil),     // 3: moego.api.business_customer.v1.BatchUpsertPetBreedResult
	(*CreatePetBreedParams)(nil),          // 4: moego.api.business_customer.v1.CreatePetBreedParams
	(*CreatePetBreedResult)(nil),          // 5: moego.api.business_customer.v1.CreatePetBreedResult
	(*UpdatePetBreedParams)(nil),          // 6: moego.api.business_customer.v1.UpdatePetBreedParams
	(*UpdatePetBreedResult)(nil),          // 7: moego.api.business_customer.v1.UpdatePetBreedResult
	(*DeletePetBreedParams)(nil),          // 8: moego.api.business_customer.v1.DeletePetBreedParams
	(*DeletePetBreedResult)(nil),          // 9: moego.api.business_customer.v1.DeletePetBreedResult
	nil,                                   // 10: moego.api.business_customer.v1.BatchUpsertPetBreedParams.BreedsToUpdateEntry
	(v1.PetType)(0),                       // 11: moego.models.customer.v1.PetType
	(*v11.BusinessPetBreedModel)(nil),     // 12: moego.models.business_customer.v1.BusinessPetBreedModel
	(*v11.BusinessPetBreedUpsertDef)(nil), // 13: moego.models.business_customer.v1.BusinessPetBreedUpsertDef
	(*v11.BusinessPetBreedCreateDef)(nil), // 14: moego.models.business_customer.v1.BusinessPetBreedCreateDef
	(*v11.BusinessPetBreedUpdateDef)(nil), // 15: moego.models.business_customer.v1.BusinessPetBreedUpdateDef
}
var file_moego_api_business_customer_v1_business_pet_breed_api_proto_depIdxs = []int32{
	11, // 0: moego.api.business_customer.v1.ListPetBreedParams.pet_type_id:type_name -> moego.models.customer.v1.PetType
	12, // 1: moego.api.business_customer.v1.ListPetBreedResult.breeds:type_name -> moego.models.business_customer.v1.BusinessPetBreedModel
	11, // 2: moego.api.business_customer.v1.BatchUpsertPetBreedParams.pet_type_id:type_name -> moego.models.customer.v1.PetType
	13, // 3: moego.api.business_customer.v1.BatchUpsertPetBreedParams.breeds_to_create:type_name -> moego.models.business_customer.v1.BusinessPetBreedUpsertDef
	10, // 4: moego.api.business_customer.v1.BatchUpsertPetBreedParams.breeds_to_update:type_name -> moego.api.business_customer.v1.BatchUpsertPetBreedParams.BreedsToUpdateEntry
	11, // 5: moego.api.business_customer.v1.CreatePetBreedParams.pet_type_id:type_name -> moego.models.customer.v1.PetType
	14, // 6: moego.api.business_customer.v1.CreatePetBreedParams.breed:type_name -> moego.models.business_customer.v1.BusinessPetBreedCreateDef
	12, // 7: moego.api.business_customer.v1.CreatePetBreedResult.breed:type_name -> moego.models.business_customer.v1.BusinessPetBreedModel
	15, // 8: moego.api.business_customer.v1.UpdatePetBreedParams.breed:type_name -> moego.models.business_customer.v1.BusinessPetBreedUpdateDef
	13, // 9: moego.api.business_customer.v1.BatchUpsertPetBreedParams.BreedsToUpdateEntry.value:type_name -> moego.models.business_customer.v1.BusinessPetBreedUpsertDef
	0,  // 10: moego.api.business_customer.v1.BusinessPetBreedService.ListPetBreed:input_type -> moego.api.business_customer.v1.ListPetBreedParams
	2,  // 11: moego.api.business_customer.v1.BusinessPetBreedService.BatchUpsertPetBreed:input_type -> moego.api.business_customer.v1.BatchUpsertPetBreedParams
	4,  // 12: moego.api.business_customer.v1.BusinessPetBreedService.CreatePetBreed:input_type -> moego.api.business_customer.v1.CreatePetBreedParams
	6,  // 13: moego.api.business_customer.v1.BusinessPetBreedService.UpdatePetBreed:input_type -> moego.api.business_customer.v1.UpdatePetBreedParams
	8,  // 14: moego.api.business_customer.v1.BusinessPetBreedService.DeletePetBreed:input_type -> moego.api.business_customer.v1.DeletePetBreedParams
	1,  // 15: moego.api.business_customer.v1.BusinessPetBreedService.ListPetBreed:output_type -> moego.api.business_customer.v1.ListPetBreedResult
	3,  // 16: moego.api.business_customer.v1.BusinessPetBreedService.BatchUpsertPetBreed:output_type -> moego.api.business_customer.v1.BatchUpsertPetBreedResult
	5,  // 17: moego.api.business_customer.v1.BusinessPetBreedService.CreatePetBreed:output_type -> moego.api.business_customer.v1.CreatePetBreedResult
	7,  // 18: moego.api.business_customer.v1.BusinessPetBreedService.UpdatePetBreed:output_type -> moego.api.business_customer.v1.UpdatePetBreedResult
	9,  // 19: moego.api.business_customer.v1.BusinessPetBreedService.DeletePetBreed:output_type -> moego.api.business_customer.v1.DeletePetBreedResult
	15, // [15:20] is the sub-list for method output_type
	10, // [10:15] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_moego_api_business_customer_v1_business_pet_breed_api_proto_init() }
func file_moego_api_business_customer_v1_business_pet_breed_api_proto_init() {
	if File_moego_api_business_customer_v1_business_pet_breed_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetBreedParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetBreedResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpsertPetBreedParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpsertPetBreedResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetBreedParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetBreedResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetBreedParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetBreedResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetBreedParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetBreedResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_business_customer_v1_business_pet_breed_api_proto_goTypes,
		DependencyIndexes: file_moego_api_business_customer_v1_business_pet_breed_api_proto_depIdxs,
		MessageInfos:      file_moego_api_business_customer_v1_business_pet_breed_api_proto_msgTypes,
	}.Build()
	File_moego_api_business_customer_v1_business_pet_breed_api_proto = out.File
	file_moego_api_business_customer_v1_business_pet_breed_api_proto_rawDesc = nil
	file_moego_api_business_customer_v1_business_pet_breed_api_proto_goTypes = nil
	file_moego_api_business_customer_v1_business_pet_breed_api_proto_depIdxs = nil
}
