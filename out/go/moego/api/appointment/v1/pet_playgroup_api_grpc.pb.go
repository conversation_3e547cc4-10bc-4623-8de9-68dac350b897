// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/appointment/v1/pet_playgroup_api.proto

package appointmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PetPlaygroupServiceClient is the client API for PetPlaygroupService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PetPlaygroupServiceClient interface {
	// list playgroup calendar view
	ListPlaygroupCalendarView(ctx context.Context, in *ListPlaygroupCalendarViewParams, opts ...grpc.CallOption) (*ListPlaygroupCalendarViewResult, error)
	// reschedule pet playgroup or date
	ReschedulePetPlaygroup(ctx context.Context, in *ReschedulePetPlaygroupParams, opts ...grpc.CallOption) (*ReschedulePetPlaygroupResult, error)
}

type petPlaygroupServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPetPlaygroupServiceClient(cc grpc.ClientConnInterface) PetPlaygroupServiceClient {
	return &petPlaygroupServiceClient{cc}
}

func (c *petPlaygroupServiceClient) ListPlaygroupCalendarView(ctx context.Context, in *ListPlaygroupCalendarViewParams, opts ...grpc.CallOption) (*ListPlaygroupCalendarViewResult, error) {
	out := new(ListPlaygroupCalendarViewResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.PetPlaygroupService/ListPlaygroupCalendarView", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petPlaygroupServiceClient) ReschedulePetPlaygroup(ctx context.Context, in *ReschedulePetPlaygroupParams, opts ...grpc.CallOption) (*ReschedulePetPlaygroupResult, error) {
	out := new(ReschedulePetPlaygroupResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.PetPlaygroupService/ReschedulePetPlaygroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PetPlaygroupServiceServer is the server API for PetPlaygroupService service.
// All implementations must embed UnimplementedPetPlaygroupServiceServer
// for forward compatibility
type PetPlaygroupServiceServer interface {
	// list playgroup calendar view
	ListPlaygroupCalendarView(context.Context, *ListPlaygroupCalendarViewParams) (*ListPlaygroupCalendarViewResult, error)
	// reschedule pet playgroup or date
	ReschedulePetPlaygroup(context.Context, *ReschedulePetPlaygroupParams) (*ReschedulePetPlaygroupResult, error)
	mustEmbedUnimplementedPetPlaygroupServiceServer()
}

// UnimplementedPetPlaygroupServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPetPlaygroupServiceServer struct {
}

func (UnimplementedPetPlaygroupServiceServer) ListPlaygroupCalendarView(context.Context, *ListPlaygroupCalendarViewParams) (*ListPlaygroupCalendarViewResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPlaygroupCalendarView not implemented")
}
func (UnimplementedPetPlaygroupServiceServer) ReschedulePetPlaygroup(context.Context, *ReschedulePetPlaygroupParams) (*ReschedulePetPlaygroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReschedulePetPlaygroup not implemented")
}
func (UnimplementedPetPlaygroupServiceServer) mustEmbedUnimplementedPetPlaygroupServiceServer() {}

// UnsafePetPlaygroupServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PetPlaygroupServiceServer will
// result in compilation errors.
type UnsafePetPlaygroupServiceServer interface {
	mustEmbedUnimplementedPetPlaygroupServiceServer()
}

func RegisterPetPlaygroupServiceServer(s grpc.ServiceRegistrar, srv PetPlaygroupServiceServer) {
	s.RegisterService(&PetPlaygroupService_ServiceDesc, srv)
}

func _PetPlaygroupService_ListPlaygroupCalendarView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPlaygroupCalendarViewParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetPlaygroupServiceServer).ListPlaygroupCalendarView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.PetPlaygroupService/ListPlaygroupCalendarView",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetPlaygroupServiceServer).ListPlaygroupCalendarView(ctx, req.(*ListPlaygroupCalendarViewParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetPlaygroupService_ReschedulePetPlaygroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReschedulePetPlaygroupParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetPlaygroupServiceServer).ReschedulePetPlaygroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.PetPlaygroupService/ReschedulePetPlaygroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetPlaygroupServiceServer).ReschedulePetPlaygroup(ctx, req.(*ReschedulePetPlaygroupParams))
	}
	return interceptor(ctx, in, info, handler)
}

// PetPlaygroupService_ServiceDesc is the grpc.ServiceDesc for PetPlaygroupService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PetPlaygroupService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.appointment.v1.PetPlaygroupService",
	HandlerType: (*PetPlaygroupServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListPlaygroupCalendarView",
			Handler:    _PetPlaygroupService_ListPlaygroupCalendarView_Handler,
		},
		{
			MethodName: "ReschedulePetPlaygroup",
			Handler:    _PetPlaygroupService_ReschedulePetPlaygroup_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/appointment/v1/pet_playgroup_api.proto",
}
