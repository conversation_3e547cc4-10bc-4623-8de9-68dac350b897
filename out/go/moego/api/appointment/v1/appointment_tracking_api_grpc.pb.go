// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/appointment/v1/appointment_tracking_api.proto

package appointmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AppointmentTrackingServiceClient is the client API for AppointmentTrackingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppointmentTrackingServiceClient interface {
	// get appointment tracking
	GetAppointmentTracking(ctx context.Context, in *GetAppointmentTrackingParams, opts ...grpc.CallOption) (*GetAppointmentTrackingResult, error)
	// update appointment tracking status
	UpdateAppointmentTrackingStatus(ctx context.Context, in *UpdateAppointmentTrackingStatusParams, opts ...grpc.CallOption) (*UpdateAppointmentTrackingStatusResult, error)
	// list staff appointment tracking
	ListStaffAppointmentTracking(ctx context.Context, in *ListStaffAppointmentTrackingParams, opts ...grpc.CallOption) (*ListStaffAppointmentTrackingResult, error)
}

type appointmentTrackingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppointmentTrackingServiceClient(cc grpc.ClientConnInterface) AppointmentTrackingServiceClient {
	return &appointmentTrackingServiceClient{cc}
}

func (c *appointmentTrackingServiceClient) GetAppointmentTracking(ctx context.Context, in *GetAppointmentTrackingParams, opts ...grpc.CallOption) (*GetAppointmentTrackingResult, error) {
	out := new(GetAppointmentTrackingResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentTrackingService/GetAppointmentTracking", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTrackingServiceClient) UpdateAppointmentTrackingStatus(ctx context.Context, in *UpdateAppointmentTrackingStatusParams, opts ...grpc.CallOption) (*UpdateAppointmentTrackingStatusResult, error) {
	out := new(UpdateAppointmentTrackingStatusResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentTrackingService/UpdateAppointmentTrackingStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentTrackingServiceClient) ListStaffAppointmentTracking(ctx context.Context, in *ListStaffAppointmentTrackingParams, opts ...grpc.CallOption) (*ListStaffAppointmentTrackingResult, error) {
	out := new(ListStaffAppointmentTrackingResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentTrackingService/ListStaffAppointmentTracking", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppointmentTrackingServiceServer is the server API for AppointmentTrackingService service.
// All implementations must embed UnimplementedAppointmentTrackingServiceServer
// for forward compatibility
type AppointmentTrackingServiceServer interface {
	// get appointment tracking
	GetAppointmentTracking(context.Context, *GetAppointmentTrackingParams) (*GetAppointmentTrackingResult, error)
	// update appointment tracking status
	UpdateAppointmentTrackingStatus(context.Context, *UpdateAppointmentTrackingStatusParams) (*UpdateAppointmentTrackingStatusResult, error)
	// list staff appointment tracking
	ListStaffAppointmentTracking(context.Context, *ListStaffAppointmentTrackingParams) (*ListStaffAppointmentTrackingResult, error)
	mustEmbedUnimplementedAppointmentTrackingServiceServer()
}

// UnimplementedAppointmentTrackingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAppointmentTrackingServiceServer struct {
}

func (UnimplementedAppointmentTrackingServiceServer) GetAppointmentTracking(context.Context, *GetAppointmentTrackingParams) (*GetAppointmentTrackingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppointmentTracking not implemented")
}
func (UnimplementedAppointmentTrackingServiceServer) UpdateAppointmentTrackingStatus(context.Context, *UpdateAppointmentTrackingStatusParams) (*UpdateAppointmentTrackingStatusResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAppointmentTrackingStatus not implemented")
}
func (UnimplementedAppointmentTrackingServiceServer) ListStaffAppointmentTracking(context.Context, *ListStaffAppointmentTrackingParams) (*ListStaffAppointmentTrackingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStaffAppointmentTracking not implemented")
}
func (UnimplementedAppointmentTrackingServiceServer) mustEmbedUnimplementedAppointmentTrackingServiceServer() {
}

// UnsafeAppointmentTrackingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppointmentTrackingServiceServer will
// result in compilation errors.
type UnsafeAppointmentTrackingServiceServer interface {
	mustEmbedUnimplementedAppointmentTrackingServiceServer()
}

func RegisterAppointmentTrackingServiceServer(s grpc.ServiceRegistrar, srv AppointmentTrackingServiceServer) {
	s.RegisterService(&AppointmentTrackingService_ServiceDesc, srv)
}

func _AppointmentTrackingService_GetAppointmentTracking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointmentTrackingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTrackingServiceServer).GetAppointmentTracking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentTrackingService/GetAppointmentTracking",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTrackingServiceServer).GetAppointmentTracking(ctx, req.(*GetAppointmentTrackingParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTrackingService_UpdateAppointmentTrackingStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAppointmentTrackingStatusParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTrackingServiceServer).UpdateAppointmentTrackingStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentTrackingService/UpdateAppointmentTrackingStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTrackingServiceServer).UpdateAppointmentTrackingStatus(ctx, req.(*UpdateAppointmentTrackingStatusParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentTrackingService_ListStaffAppointmentTracking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListStaffAppointmentTrackingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentTrackingServiceServer).ListStaffAppointmentTracking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentTrackingService/ListStaffAppointmentTracking",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentTrackingServiceServer).ListStaffAppointmentTracking(ctx, req.(*ListStaffAppointmentTrackingParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AppointmentTrackingService_ServiceDesc is the grpc.ServiceDesc for AppointmentTrackingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppointmentTrackingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.appointment.v1.AppointmentTrackingService",
	HandlerType: (*AppointmentTrackingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAppointmentTracking",
			Handler:    _AppointmentTrackingService_GetAppointmentTracking_Handler,
		},
		{
			MethodName: "UpdateAppointmentTrackingStatus",
			Handler:    _AppointmentTrackingService_UpdateAppointmentTrackingStatus_Handler,
		},
		{
			MethodName: "ListStaffAppointmentTracking",
			Handler:    _AppointmentTrackingService_ListStaffAppointmentTracking_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/appointment/v1/appointment_tracking_api.proto",
}
