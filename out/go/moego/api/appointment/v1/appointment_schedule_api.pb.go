// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/appointment/v1/appointment_schedule_api.proto

package appointmentapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Reschedule All-in-One service params
type RescheduleAllInOneServiceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// Selected pet and services schedule list
	PetServiceSchedules []*v1.PetServiceScheduleDef `protobuf:"bytes,2,rep,name=pet_service_schedules,json=petServiceSchedules,proto3" json:"pet_service_schedules,omitempty"`
	// Repeat appointment modify scope
	RepeatAppointmentModifyScope *v1.RepeatAppointmentModifyScope `protobuf:"varint,3,opt,name=repeat_appointment_modify_scope,json=repeatAppointmentModifyScope,proto3,enum=moego.models.appointment.v1.RepeatAppointmentModifyScope,oneof" json:"repeat_appointment_modify_scope,omitempty"`
}

func (x *RescheduleAllInOneServiceParams) Reset() {
	*x = RescheduleAllInOneServiceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleAllInOneServiceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleAllInOneServiceParams) ProtoMessage() {}

func (x *RescheduleAllInOneServiceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleAllInOneServiceParams.ProtoReflect.Descriptor instead.
func (*RescheduleAllInOneServiceParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{0}
}

func (x *RescheduleAllInOneServiceParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *RescheduleAllInOneServiceParams) GetPetServiceSchedules() []*v1.PetServiceScheduleDef {
	if x != nil {
		return x.PetServiceSchedules
	}
	return nil
}

func (x *RescheduleAllInOneServiceParams) GetRepeatAppointmentModifyScope() v1.RepeatAppointmentModifyScope {
	if x != nil && x.RepeatAppointmentModifyScope != nil {
		return *x.RepeatAppointmentModifyScope
	}
	return v1.RepeatAppointmentModifyScope(0)
}

// Reschedule All-in-One service result
type RescheduleAllInOneServiceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RescheduleAllInOneServiceResult) Reset() {
	*x = RescheduleAllInOneServiceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleAllInOneServiceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleAllInOneServiceResult) ProtoMessage() {}

func (x *RescheduleAllInOneServiceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleAllInOneServiceResult.ProtoReflect.Descriptor instead.
func (*RescheduleAllInOneServiceResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{1}
}

// Reschedule boarding service params
type RescheduleBoardingServiceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// Selected pet and lodging schedule
	BoardingServiceSchedules []*v1.BoardingServiceScheduleDef `protobuf:"bytes,2,rep,name=boarding_service_schedules,json=boardingServiceSchedules,proto3" json:"boarding_service_schedules,omitempty"`
}

func (x *RescheduleBoardingServiceParams) Reset() {
	*x = RescheduleBoardingServiceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleBoardingServiceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleBoardingServiceParams) ProtoMessage() {}

func (x *RescheduleBoardingServiceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleBoardingServiceParams.ProtoReflect.Descriptor instead.
func (*RescheduleBoardingServiceParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{2}
}

func (x *RescheduleBoardingServiceParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *RescheduleBoardingServiceParams) GetBoardingServiceSchedules() []*v1.BoardingServiceScheduleDef {
	if x != nil {
		return x.BoardingServiceSchedules
	}
	return nil
}

// Reschedule boarding service result
type RescheduleBoardingServiceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conflict service names, if there are no conflicts, the list is empty
	ConflictServiceNames []string `protobuf:"bytes,1,rep,name=conflict_service_names,json=conflictServiceNames,proto3" json:"conflict_service_names,omitempty"`
}

func (x *RescheduleBoardingServiceResult) Reset() {
	*x = RescheduleBoardingServiceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleBoardingServiceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleBoardingServiceResult) ProtoMessage() {}

func (x *RescheduleBoardingServiceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleBoardingServiceResult.ProtoReflect.Descriptor instead.
func (*RescheduleBoardingServiceResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{3}
}

func (x *RescheduleBoardingServiceResult) GetConflictServiceNames() []string {
	if x != nil {
		return x.ConflictServiceNames
	}
	return nil
}

// Reschedule grooming service params
type RescheduleGroomingServiceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// card ids corresponding to different types of cards
	// APPOINTMENT: appt id
	// SERVICE: pet detail id
	// OPERATION: multi-staff service operation id
	// BLOCK: appt id
	// BOOKING_REQUEST: booking request id
	Id *int64 `protobuf:"varint,2,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// card type
	CardType v1.CalendarCardType `protobuf:"varint,3,opt,name=card_type,json=cardType,proto3,enum=moego.models.appointment.v1.CalendarCardType" json:"card_type,omitempty"`
	// rescheduled staff id, if not specified, the original staff id will be inherited.
	StaffId *int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// rescheduled start date, in the format of "YYYY-MM-DD"
	StartDate string `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// rescheduled start time, in minutes
	StartTime int32 `protobuf:"varint,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// rescheduled end time, in minutes
	EndTime *int32 `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// rescheduled pet detail ids, when card type is SERVICE
	PetDetailIds []int64 `protobuf:"varint,8,rep,packed,name=pet_detail_ids,json=petDetailIds,proto3" json:"pet_detail_ids,omitempty"`
	// The scope of impact of this modification on repeat appointments
	RepeatType *v1.RepeatAppointmentModifyScope `protobuf:"varint,10,opt,name=repeat_type,json=repeatType,proto3,enum=moego.models.appointment.v1.RepeatAppointmentModifyScope,oneof" json:"repeat_type,omitempty"`
}

func (x *RescheduleGroomingServiceParams) Reset() {
	*x = RescheduleGroomingServiceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleGroomingServiceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleGroomingServiceParams) ProtoMessage() {}

func (x *RescheduleGroomingServiceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleGroomingServiceParams.ProtoReflect.Descriptor instead.
func (*RescheduleGroomingServiceParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{4}
}

func (x *RescheduleGroomingServiceParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *RescheduleGroomingServiceParams) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *RescheduleGroomingServiceParams) GetCardType() v1.CalendarCardType {
	if x != nil {
		return x.CardType
	}
	return v1.CalendarCardType(0)
}

func (x *RescheduleGroomingServiceParams) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *RescheduleGroomingServiceParams) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *RescheduleGroomingServiceParams) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *RescheduleGroomingServiceParams) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *RescheduleGroomingServiceParams) GetPetDetailIds() []int64 {
	if x != nil {
		return x.PetDetailIds
	}
	return nil
}

func (x *RescheduleGroomingServiceParams) GetRepeatType() v1.RepeatAppointmentModifyScope {
	if x != nil && x.RepeatType != nil {
		return *x.RepeatType
	}
	return v1.RepeatAppointmentModifyScope(0)
}

// Reschedule grooming service result
type RescheduleGroomingServiceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RescheduleGroomingServiceResult) Reset() {
	*x = RescheduleGroomingServiceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleGroomingServiceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleGroomingServiceResult) ProtoMessage() {}

func (x *RescheduleGroomingServiceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleGroomingServiceResult.ProtoReflect.Descriptor instead.
func (*RescheduleGroomingServiceResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{5}
}

// Reschedule evaluation service params
type RescheduleEvaluationServiceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// Selected pet and lodging schedule
	EvaluationServiceSchedules []*v1.EvaluationServiceScheduleDef `protobuf:"bytes,2,rep,name=evaluation_service_schedules,json=evaluationServiceSchedules,proto3" json:"evaluation_service_schedules,omitempty"`
}

func (x *RescheduleEvaluationServiceParams) Reset() {
	*x = RescheduleEvaluationServiceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleEvaluationServiceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleEvaluationServiceParams) ProtoMessage() {}

func (x *RescheduleEvaluationServiceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleEvaluationServiceParams.ProtoReflect.Descriptor instead.
func (*RescheduleEvaluationServiceParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{6}
}

func (x *RescheduleEvaluationServiceParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *RescheduleEvaluationServiceParams) GetEvaluationServiceSchedules() []*v1.EvaluationServiceScheduleDef {
	if x != nil {
		return x.EvaluationServiceSchedules
	}
	return nil
}

// Reschedule evaluation service result
type RescheduleEvaluationServiceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RescheduleEvaluationServiceResult) Reset() {
	*x = RescheduleEvaluationServiceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleEvaluationServiceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleEvaluationServiceResult) ProtoMessage() {}

func (x *RescheduleEvaluationServiceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleEvaluationServiceResult.ProtoReflect.Descriptor instead.
func (*RescheduleEvaluationServiceResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{7}
}

// Lodging assign params
type LodgingAssignParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// lodging for services to be assigned
	Services []*v1.ServiceLodgingAssignDef `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
	// lodging for evaluations to be assigned
	Evaluations []*v1.ServiceLodgingAssignDef `protobuf:"bytes,3,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
}

func (x *LodgingAssignParams) Reset() {
	*x = LodgingAssignParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingAssignParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingAssignParams) ProtoMessage() {}

func (x *LodgingAssignParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingAssignParams.ProtoReflect.Descriptor instead.
func (*LodgingAssignParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{8}
}

func (x *LodgingAssignParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *LodgingAssignParams) GetServices() []*v1.ServiceLodgingAssignDef {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *LodgingAssignParams) GetEvaluations() []*v1.ServiceLodgingAssignDef {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

// Lodging assign result
type LodgingAssignResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LodgingAssignResult) Reset() {
	*x = LodgingAssignResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingAssignResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingAssignResult) ProtoMessage() {}

func (x *LodgingAssignResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingAssignResult.ProtoReflect.Descriptor instead.
func (*LodgingAssignResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{9}
}

// Get pet feeding and medication schedules params
type GetPetFeedingMedicationSchedulesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *GetPetFeedingMedicationSchedulesParams) Reset() {
	*x = GetPetFeedingMedicationSchedulesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetFeedingMedicationSchedulesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetFeedingMedicationSchedulesParams) ProtoMessage() {}

func (x *GetPetFeedingMedicationSchedulesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetFeedingMedicationSchedulesParams.ProtoReflect.Descriptor instead.
func (*GetPetFeedingMedicationSchedulesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{10}
}

func (x *GetPetFeedingMedicationSchedulesParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// Get pet feeding and medication schedules result
type GetPetFeedingMedicationSchedulesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pet's schedules
	Schedules []*v1.PetScheduleDef `protobuf:"bytes,1,rep,name=schedules,proto3" json:"schedules,omitempty"`
}

func (x *GetPetFeedingMedicationSchedulesResult) Reset() {
	*x = GetPetFeedingMedicationSchedulesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetFeedingMedicationSchedulesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetFeedingMedicationSchedulesResult) ProtoMessage() {}

func (x *GetPetFeedingMedicationSchedulesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetFeedingMedicationSchedulesResult.ProtoReflect.Descriptor instead.
func (*GetPetFeedingMedicationSchedulesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{11}
}

func (x *GetPetFeedingMedicationSchedulesResult) GetSchedules() []*v1.PetScheduleDef {
	if x != nil {
		return x.Schedules
	}
	return nil
}

// Update pet feeding medication params
type ReschedulePetFeedingMedicationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// Pet's schedules
	Schedules []*v1.PetScheduleDef `protobuf:"bytes,2,rep,name=schedules,proto3" json:"schedules,omitempty"`
}

func (x *ReschedulePetFeedingMedicationParams) Reset() {
	*x = ReschedulePetFeedingMedicationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReschedulePetFeedingMedicationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReschedulePetFeedingMedicationParams) ProtoMessage() {}

func (x *ReschedulePetFeedingMedicationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReschedulePetFeedingMedicationParams.ProtoReflect.Descriptor instead.
func (*ReschedulePetFeedingMedicationParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{12}
}

func (x *ReschedulePetFeedingMedicationParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ReschedulePetFeedingMedicationParams) GetSchedules() []*v1.PetScheduleDef {
	if x != nil {
		return x.Schedules
	}
	return nil
}

// Update pet feeding medication result
type ReschedulePetFeedingMedicationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReschedulePetFeedingMedicationResult) Reset() {
	*x = ReschedulePetFeedingMedicationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReschedulePetFeedingMedicationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReschedulePetFeedingMedicationResult) ProtoMessage() {}

func (x *ReschedulePetFeedingMedicationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReschedulePetFeedingMedicationResult.ProtoReflect.Descriptor instead.
func (*ReschedulePetFeedingMedicationResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{13}
}

// Calculate appointment and pet details schedule params
type CalculateAppointmentScheduleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment params
	Appointment *v1.AppointmentCreateDef `protobuf:"bytes,2,opt,name=appointment,proto3" json:"appointment,omitempty"`
	// Selected pet and services
	PetServiceSchedules []*v1.PetServiceScheduleDef `protobuf:"bytes,3,rep,name=pet_service_schedules,json=petServiceSchedules,proto3" json:"pet_service_schedules,omitempty"`
}

func (x *CalculateAppointmentScheduleParams) Reset() {
	*x = CalculateAppointmentScheduleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculateAppointmentScheduleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateAppointmentScheduleParams) ProtoMessage() {}

func (x *CalculateAppointmentScheduleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateAppointmentScheduleParams.ProtoReflect.Descriptor instead.
func (*CalculateAppointmentScheduleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{14}
}

func (x *CalculateAppointmentScheduleParams) GetAppointment() *v1.AppointmentCreateDef {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *CalculateAppointmentScheduleParams) GetPetServiceSchedules() []*v1.PetServiceScheduleDef {
	if x != nil {
		return x.PetServiceSchedules
	}
	return nil
}

// Calculate appointment and pet details schedule result
type CalculateAppointmentScheduleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment schedule
	AppointmentSchedule *v1.AppointmentScheduleDef `protobuf:"bytes,1,opt,name=appointment_schedule,json=appointmentSchedule,proto3" json:"appointment_schedule,omitempty"`
	// Selected pet and services
	PetServiceSchedules []*v1.PetServiceScheduleDef `protobuf:"bytes,2,rep,name=pet_service_schedules,json=petServiceSchedules,proto3" json:"pet_service_schedules,omitempty"`
}

func (x *CalculateAppointmentScheduleResult) Reset() {
	*x = CalculateAppointmentScheduleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculateAppointmentScheduleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateAppointmentScheduleResult) ProtoMessage() {}

func (x *CalculateAppointmentScheduleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateAppointmentScheduleResult.ProtoReflect.Descriptor instead.
func (*CalculateAppointmentScheduleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{15}
}

func (x *CalculateAppointmentScheduleResult) GetAppointmentSchedule() *v1.AppointmentScheduleDef {
	if x != nil {
		return x.AppointmentSchedule
	}
	return nil
}

func (x *CalculateAppointmentScheduleResult) GetPetServiceSchedules() []*v1.PetServiceScheduleDef {
	if x != nil {
		return x.PetServiceSchedules
	}
	return nil
}

// Reschedule daycare service params
type RescheduleDaycareServiceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// Daycare service pet detail
	DaycareServiceSchedules []*RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule `protobuf:"bytes,2,rep,name=daycare_service_schedules,json=daycareServiceSchedules,proto3" json:"daycare_service_schedules,omitempty"`
}

func (x *RescheduleDaycareServiceParams) Reset() {
	*x = RescheduleDaycareServiceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleDaycareServiceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleDaycareServiceParams) ProtoMessage() {}

func (x *RescheduleDaycareServiceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleDaycareServiceParams.ProtoReflect.Descriptor instead.
func (*RescheduleDaycareServiceParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{16}
}

func (x *RescheduleDaycareServiceParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *RescheduleDaycareServiceParams) GetDaycareServiceSchedules() []*RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule {
	if x != nil {
		return x.DaycareServiceSchedules
	}
	return nil
}

// Reschedule daycare service result
type RescheduleDaycareServiceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conflict service names, if there are no conflicts, the list is empty
	ConflictServiceNames []string `protobuf:"bytes,1,rep,name=conflict_service_names,json=conflictServiceNames,proto3" json:"conflict_service_names,omitempty"`
}

func (x *RescheduleDaycareServiceResult) Reset() {
	*x = RescheduleDaycareServiceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleDaycareServiceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleDaycareServiceResult) ProtoMessage() {}

func (x *RescheduleDaycareServiceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleDaycareServiceResult.ProtoReflect.Descriptor instead.
func (*RescheduleDaycareServiceResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{17}
}

func (x *RescheduleDaycareServiceResult) GetConflictServiceNames() []string {
	if x != nil {
		return x.ConflictServiceNames
	}
	return nil
}

// Batch reschedule appointment by staff and date params
type BatchRescheduleAppointmentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Selected staff id
	SourceStaffId int64 `protobuf:"varint,2,opt,name=source_staff_id,json=sourceStaffId,proto3" json:"source_staff_id,omitempty"`
	// Selected date
	SourceDate *date.Date `protobuf:"bytes,3,opt,name=source_date,json=sourceDate,proto3" json:"source_date,omitempty"`
	// Target staff id (optional, but either this or target_date must be provided)
	TargetStaffId *int64 `protobuf:"varint,4,opt,name=target_staff_id,json=targetStaffId,proto3,oneof" json:"target_staff_id,omitempty"`
	// Target date (optional, but either this or target_staff_id must be provided)
	TargetDate *date.Date `protobuf:"bytes,5,opt,name=target_date,json=targetDate,proto3,oneof" json:"target_date,omitempty"`
}

func (x *BatchRescheduleAppointmentParams) Reset() {
	*x = BatchRescheduleAppointmentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchRescheduleAppointmentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchRescheduleAppointmentParams) ProtoMessage() {}

func (x *BatchRescheduleAppointmentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchRescheduleAppointmentParams.ProtoReflect.Descriptor instead.
func (*BatchRescheduleAppointmentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{18}
}

func (x *BatchRescheduleAppointmentParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchRescheduleAppointmentParams) GetSourceStaffId() int64 {
	if x != nil {
		return x.SourceStaffId
	}
	return 0
}

func (x *BatchRescheduleAppointmentParams) GetSourceDate() *date.Date {
	if x != nil {
		return x.SourceDate
	}
	return nil
}

func (x *BatchRescheduleAppointmentParams) GetTargetStaffId() int64 {
	if x != nil && x.TargetStaffId != nil {
		return *x.TargetStaffId
	}
	return 0
}

func (x *BatchRescheduleAppointmentParams) GetTargetDate() *date.Date {
	if x != nil {
		return x.TargetDate
	}
	return nil
}

// Batch reschedule appointment by staff and date result
type BatchRescheduleAppointmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reschedule appointments detail
	Appointments []*v1.AppointmentModel `protobuf:"bytes,1,rep,name=appointments,proto3" json:"appointments,omitempty"`
}

func (x *BatchRescheduleAppointmentResult) Reset() {
	*x = BatchRescheduleAppointmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchRescheduleAppointmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchRescheduleAppointmentResult) ProtoMessage() {}

func (x *BatchRescheduleAppointmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchRescheduleAppointmentResult.ProtoReflect.Descriptor instead.
func (*BatchRescheduleAppointmentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{19}
}

func (x *BatchRescheduleAppointmentResult) GetAppointments() []*v1.AppointmentModel {
	if x != nil {
		return x.Appointments
	}
	return nil
}

// Reschedule calendar card params
type RescheduleCalendarCardParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Reschedule appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// Reschedule card type
	CardType v1.CalendarCardType `protobuf:"varint,2,opt,name=card_type,json=cardType,proto3,enum=moego.models.appointment.v1.CalendarCardType" json:"card_type,omitempty"`
	// Rescheduled pet detail ids, dragged cards objects, used to identify the dragged pet details
	// SERVICE/OPERATION/SERVICE_AND_OPERATION card type, the pet_detail_ids field is required
	PetDetailIds []int64 `protobuf:"varint,3,rep,packed,name=pet_detail_ids,json=petDetailIds,proto3" json:"pet_detail_ids,omitempty"`
	// Original staff id, current card location staff id
	OriginalStaffId *int64 `protobuf:"varint,4,opt,name=original_staff_id,json=originalStaffId,proto3,oneof" json:"original_staff_id,omitempty"`
	// Target staff id, if not specified, the original staff id will be inherited.
	StaffId *int64 `protobuf:"varint,5,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// Target start date, in the format of "YYYY-MM-DD"
	StartDate string `protobuf:"bytes,6,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// Target start time, in minutes
	StartTime *int32 `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// Stretch end time, in minutes
	// The end time that can be used to indicate stretching service hours when only one service is included for a pet selection
	EndTime *int32 `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// The scope of impact of this modification on repeat appointments
	RepeatModifyType *v1.RepeatAppointmentModifyScope `protobuf:"varint,9,opt,name=repeat_modify_type,json=repeatModifyType,proto3,enum=moego.models.appointment.v1.RepeatAppointmentModifyScope,oneof" json:"repeat_modify_type,omitempty"`
	// The flag of move all cards
	MoveAllCards bool `protobuf:"varint,10,opt,name=move_all_cards,json=moveAllCards,proto3" json:"move_all_cards,omitempty"`
}

func (x *RescheduleCalendarCardParams) Reset() {
	*x = RescheduleCalendarCardParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleCalendarCardParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleCalendarCardParams) ProtoMessage() {}

func (x *RescheduleCalendarCardParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleCalendarCardParams.ProtoReflect.Descriptor instead.
func (*RescheduleCalendarCardParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{20}
}

func (x *RescheduleCalendarCardParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *RescheduleCalendarCardParams) GetCardType() v1.CalendarCardType {
	if x != nil {
		return x.CardType
	}
	return v1.CalendarCardType(0)
}

func (x *RescheduleCalendarCardParams) GetPetDetailIds() []int64 {
	if x != nil {
		return x.PetDetailIds
	}
	return nil
}

func (x *RescheduleCalendarCardParams) GetOriginalStaffId() int64 {
	if x != nil && x.OriginalStaffId != nil {
		return *x.OriginalStaffId
	}
	return 0
}

func (x *RescheduleCalendarCardParams) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *RescheduleCalendarCardParams) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *RescheduleCalendarCardParams) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *RescheduleCalendarCardParams) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *RescheduleCalendarCardParams) GetRepeatModifyType() v1.RepeatAppointmentModifyScope {
	if x != nil && x.RepeatModifyType != nil {
		return *x.RepeatModifyType
	}
	return v1.RepeatAppointmentModifyScope(0)
}

func (x *RescheduleCalendarCardParams) GetMoveAllCards() bool {
	if x != nil {
		return x.MoveAllCards
	}
	return false
}

// Reschedule calendar card result
type RescheduleCalendarCardResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RescheduleCalendarCardResult) Reset() {
	*x = RescheduleCalendarCardResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleCalendarCardResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleCalendarCardResult) ProtoMessage() {}

func (x *RescheduleCalendarCardResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleCalendarCardResult.ProtoReflect.Descriptor instead.
func (*RescheduleCalendarCardResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{21}
}

// Reschedule appointment
type RescheduleAppointmentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// Reschedule start date
	StartDate *string `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// Reschedule start time, in minutes
	StartTime *int32 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// The scope of impact of this modification on repeat appointments
	RepeatModifyType *v1.RepeatAppointmentModifyScope `protobuf:"varint,9,opt,name=repeat_modify_type,json=repeatModifyType,proto3,enum=moego.models.appointment.v1.RepeatAppointmentModifyScope,oneof" json:"repeat_modify_type,omitempty"`
}

func (x *RescheduleAppointmentParams) Reset() {
	*x = RescheduleAppointmentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleAppointmentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleAppointmentParams) ProtoMessage() {}

func (x *RescheduleAppointmentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleAppointmentParams.ProtoReflect.Descriptor instead.
func (*RescheduleAppointmentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{22}
}

func (x *RescheduleAppointmentParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *RescheduleAppointmentParams) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *RescheduleAppointmentParams) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *RescheduleAppointmentParams) GetRepeatModifyType() v1.RepeatAppointmentModifyScope {
	if x != nil && x.RepeatModifyType != nil {
		return *x.RepeatModifyType
	}
	return v1.RepeatAppointmentModifyScope(0)
}

// Reschedule appointment result
type RescheduleAppointmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RescheduleAppointmentResult) Reset() {
	*x = RescheduleAppointmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleAppointmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleAppointmentResult) ProtoMessage() {}

func (x *RescheduleAppointmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleAppointmentResult.ProtoReflect.Descriptor instead.
func (*RescheduleAppointmentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{23}
}

// Switch all pets start at the same time params
type SwitchAllPetsStartAtSameTimeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// All pets start at the same time
	AllPetsStartAtSameTime bool `protobuf:"varint,2,opt,name=all_pets_start_at_same_time,json=allPetsStartAtSameTime,proto3" json:"all_pets_start_at_same_time,omitempty"`
}

func (x *SwitchAllPetsStartAtSameTimeParams) Reset() {
	*x = SwitchAllPetsStartAtSameTimeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwitchAllPetsStartAtSameTimeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwitchAllPetsStartAtSameTimeParams) ProtoMessage() {}

func (x *SwitchAllPetsStartAtSameTimeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwitchAllPetsStartAtSameTimeParams.ProtoReflect.Descriptor instead.
func (*SwitchAllPetsStartAtSameTimeParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{24}
}

func (x *SwitchAllPetsStartAtSameTimeParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *SwitchAllPetsStartAtSameTimeParams) GetAllPetsStartAtSameTime() bool {
	if x != nil {
		return x.AllPetsStartAtSameTime
	}
	return false
}

// Switch all pets start at the same time result
type SwitchAllPetsStartAtSameTimeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SwitchAllPetsStartAtSameTimeResult) Reset() {
	*x = SwitchAllPetsStartAtSameTimeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwitchAllPetsStartAtSameTimeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwitchAllPetsStartAtSameTimeResult) ProtoMessage() {}

func (x *SwitchAllPetsStartAtSameTimeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwitchAllPetsStartAtSameTimeResult.ProtoReflect.Descriptor instead.
func (*SwitchAllPetsStartAtSameTimeResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{25}
}

// The params message for reschedule pet details
type ReschedulePetDetailsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// Pet details schedule
	PetDetails []*v1.PetDetailScheduleDef `protobuf:"bytes,2,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// Repeat appointment modify scope
	RepeatAppointmentModifyScope *v1.RepeatAppointmentModifyScope `protobuf:"varint,3,opt,name=repeat_appointment_modify_scope,json=repeatAppointmentModifyScope,proto3,enum=moego.models.appointment.v1.RepeatAppointmentModifyScope,oneof" json:"repeat_appointment_modify_scope,omitempty"`
}

func (x *ReschedulePetDetailsParams) Reset() {
	*x = ReschedulePetDetailsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReschedulePetDetailsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReschedulePetDetailsParams) ProtoMessage() {}

func (x *ReschedulePetDetailsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReschedulePetDetailsParams.ProtoReflect.Descriptor instead.
func (*ReschedulePetDetailsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{26}
}

func (x *ReschedulePetDetailsParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ReschedulePetDetailsParams) GetPetDetails() []*v1.PetDetailScheduleDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *ReschedulePetDetailsParams) GetRepeatAppointmentModifyScope() v1.RepeatAppointmentModifyScope {
	if x != nil && x.RepeatAppointmentModifyScope != nil {
		return *x.RepeatAppointmentModifyScope
	}
	return v1.RepeatAppointmentModifyScope(0)
}

// The result message for reschedule pet details
type ReschedulePetDetailsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReschedulePetDetailsResult) Reset() {
	*x = ReschedulePetDetailsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReschedulePetDetailsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReschedulePetDetailsResult) ProtoMessage() {}

func (x *ReschedulePetDetailsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReschedulePetDetailsResult.ProtoReflect.Descriptor instead.
func (*ReschedulePetDetailsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{27}
}

// Reschedule daycare service pet detail
type RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service start date, in yyyy-MM-dd format. empty for daycare associated to boarding with specific days
	StartDate string `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// service end date, in yyyy-MM-dd format. empty for daycare associated to boarding with specific days
	EndDate string `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// start time, in minutes
	StartTime int32 `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// end time, in minutes
	EndTime int32 `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// selected lodging id
	LodgingId int64 `protobuf:"varint,6,opt,name=lodging_id,json=lodgingId,proto3" json:"lodging_id,omitempty"`
	// Certain dates
	// support daycare associated to boarding with specific days
	SpecificDates []string `protobuf:"bytes,7,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// pet detail date type
	DateType *v1.PetDetailDateType `protobuf:"varint,8,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.PetDetailDateType,oneof" json:"date_type,omitempty"`
}

func (x *RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule) Reset() {
	*x = RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule) ProtoMessage() {}

func (x *RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule.ProtoReflect.Descriptor instead.
func (*RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP(), []int{16, 0}
}

func (x *RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule) GetLodgingId() int64 {
	if x != nil {
		return x.LodgingId
	}
	return 0
}

func (x *RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule) GetDateType() v1.PetDetailDateType {
	if x != nil && x.DateType != nil {
		return *x.DateType
	}
	return v1.PetDetailDateType(0)
}

var File_moego_api_appointment_v1_appointment_schedule_api_proto protoreflect.FileDescriptor

var file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDesc = []byte{
	0x0a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x82, 0x03, 0x0a, 0x1f,
	0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x6c, 0x6c, 0x49, 0x6e, 0x4f,
	0x6e, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x77, 0x0a, 0x15, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44,
	0x65, 0x66, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x08, 0x01, 0x22, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x13, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x91, 0x01, 0x0a, 0x1f, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x1c, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x22, 0x0a, 0x20,
	0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65,
	0x22, 0x21, 0x0a, 0x1f, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x6c,
	0x6c, 0x49, 0x6e, 0x4f, 0x6e, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0xda, 0x01, 0x0a, 0x1f, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x86, 0x01, 0x0a, 0x1a, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x44, 0x65, 0x66, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x08, 0x01, 0x22,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x18, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73,
	0x22, 0x57, 0x0a, 0x1f, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x14, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0xd6, 0x04, 0x0a, 0x1f, 0x52, 0x65,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x56, 0x0a, 0x09, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01,
	0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d,
	0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x29, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00,
	0x48, 0x02, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x34,
	0x0a, 0x0e, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x08, 0x00,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x49, 0x64, 0x73, 0x12, 0x6b, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53,
	0x63, 0x6f, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x48, 0x03, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01,
	0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x22, 0x21, 0x0a, 0x1f, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xe2, 0x01, 0x0a, 0x21, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x8c, 0x01, 0x0a, 0x1c,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x42, 0x0f, 0xfa,
	0x42, 0x0c, 0x92, 0x01, 0x09, 0x08, 0x01, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x1a,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x22, 0x23, 0x0a, 0x21, 0x52, 0x65,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x91, 0x02, 0x0a, 0x13, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x61, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x44, 0x65, 0x66, 0x42,
	0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x67, 0x0a, 0x0b, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x44, 0x65, 0x66, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0x64, 0x22,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0x15, 0x0a, 0x13, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x58, 0x0a, 0x26, 0x47, 0x65,
	0x74, 0x50, 0x65, 0x74, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x22, 0x86, 0x01, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x46,
	0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x5c, 0x0a, 0x09, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x42,
	0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x00, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x09, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x22, 0xb4, 0x01,
	0x0a, 0x24, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x46,
	0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x5c, 0x0a, 0x09, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x00,
	0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x09, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x73, 0x22, 0x26, 0x0a, 0x24, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x50, 0x65, 0x74, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xfe, 0x01, 0x0a,
	0x22, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x5d, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x79, 0x0a, 0x15, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x44, 0x65, 0x66, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10,
	0x64, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x13, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x22, 0x91, 0x02,
	0x0a, 0x22, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x70, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x79, 0x0a, 0x15, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01,
	0x0b, 0x08, 0x01, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x13, 0x70, 0x65,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x73, 0x22, 0xdf, 0x05, 0x0a, 0x1e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x9f, 0x01, 0x0a, 0x19, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x59, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x61,
	0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x79,
	0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x17, 0x64,
	0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x1a, 0xea, 0x03, 0x0a, 0x20, 0x52, 0x65, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x06, 0x70,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x1d, 0xfa, 0x42, 0x1a, 0x72, 0x18, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c,
	0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0xd0, 0x01, 0x01, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x38, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0xfa, 0x42, 0x1a,
	0x72, 0x18, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0xd0, 0x01, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x29, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0,
	0x0b, 0x28, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0a, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x28, 0x00, 0x52, 0x09, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x48, 0x0a,
	0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x09, 0x42, 0x21, 0xfa, 0x42, 0x1e, 0x92, 0x01, 0x1b, 0x10, 0x64, 0x22,
	0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32,
	0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x5c, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x56, 0x0a, 0x1e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63,
	0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0xc4, 0x02, 0x0a, 0x20,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x0b, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x34, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x01, 0x52,
	0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x12,
	0x0a, 0x10, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x22, 0x75, 0x0a, 0x20, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x51, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xcc, 0x05, 0x0a, 0x1c, 0x52, 0x65,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72,
	0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x56, 0x0a, 0x09, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65,
	0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x34, 0x0a, 0x0e, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92,
	0x01, 0x08, 0x08, 0x00, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x70, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x73, 0x12, 0x38, 0x0a, 0x11, 0x6f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0f,
	0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x27, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c,
	0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a,
	0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x02, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18,
	0xa0, 0x0b, 0x28, 0x00, 0x48, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x78, 0x0a, 0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01,
	0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x04, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0e,
	0x6d, 0x6f, 0x76, 0x65, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6d, 0x6f, 0x76, 0x65, 0x41, 0x6c, 0x6c, 0x43, 0x61, 0x72,
	0x64, 0x73, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x1e, 0x0a, 0x1c, 0x52, 0x65, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xec, 0x02, 0x0a, 0x1b, 0x52, 0x65, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42,
	0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32,
	0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x78, 0x0a, 0x12, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x02, 0x52, 0x10, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x42, 0x15, 0x0a, 0x13, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x1d, 0x0a, 0x1b, 0x52, 0x65, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x91, 0x01, 0x0a, 0x22, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x41, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x73, 0x53, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x53,
	0x61, 0x6d, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3b, 0x0a,
	0x1b, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x61, 0x74, 0x5f, 0x73, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x16, 0x61, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x73, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x41, 0x74, 0x53, 0x61, 0x6d, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x24, 0x0a, 0x22, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x41, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x73, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x41, 0x74, 0x53, 0x61, 0x6d, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0xeb, 0x02, 0x0a, 0x1a, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x65, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08,
	0x01, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x91, 0x01, 0x0a, 0x1f, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x1c, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x22, 0x0a, 0x20, 0x5f, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x22, 0x1c,
	0x0a, 0x1a, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xb6, 0x10, 0x0a,
	0x1a, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x96, 0x01, 0x0a, 0x19,
	0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x6c, 0x6c, 0x49, 0x6e, 0x4f,
	0x6e, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x41,
	0x6c, 0x6c, 0x49, 0x6e, 0x4f, 0x6e, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x6c, 0x6c, 0x49, 0x6e, 0x4f,
	0x6e, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x03, 0x88, 0x02, 0x01, 0x12, 0x91, 0x01, 0x0a, 0x19, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8e, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x79, 0x63, 0x61,
	0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x96, 0x01, 0x0a, 0x19, 0x52, 0x65,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x47, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x03, 0x88,
	0x02, 0x01, 0x12, 0x97, 0x01, 0x0a, 0x1b, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6d, 0x0a, 0x0d,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xa6, 0x01, 0x0a, 0x20,
	0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73,
	0x12, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x65, 0x74, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x65, 0x74, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0xa0, 0x01, 0x0a, 0x1e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x74,
	0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x74,
	0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9f, 0x01, 0x0a, 0x1c, 0x43, 0x61, 0x6c, 0x63,
	0x75, 0x6c, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x94, 0x01, 0x0a, 0x1a, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x88, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x43,
	0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x12, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61,
	0x72, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x15,
	0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x9a, 0x01, 0x0a, 0x1c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x41, 0x6c,
	0x6c, 0x50, 0x65, 0x74, 0x73, 0x53, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x53, 0x61, 0x6d, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x41, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x73, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x41, 0x74, 0x53, 0x61, 0x6d, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x41, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x73, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x41, 0x74, 0x53, 0x61, 0x6d, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x82, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x84, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescOnce sync.Once
	file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescData = file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDesc
)

func file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescGZIP() []byte {
	file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescOnce.Do(func() {
		file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescData)
	})
	return file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDescData
}

var file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_moego_api_appointment_v1_appointment_schedule_api_proto_goTypes = []interface{}{
	(*RescheduleAllInOneServiceParams)(nil),                                 // 0: moego.api.appointment.v1.RescheduleAllInOneServiceParams
	(*RescheduleAllInOneServiceResult)(nil),                                 // 1: moego.api.appointment.v1.RescheduleAllInOneServiceResult
	(*RescheduleBoardingServiceParams)(nil),                                 // 2: moego.api.appointment.v1.RescheduleBoardingServiceParams
	(*RescheduleBoardingServiceResult)(nil),                                 // 3: moego.api.appointment.v1.RescheduleBoardingServiceResult
	(*RescheduleGroomingServiceParams)(nil),                                 // 4: moego.api.appointment.v1.RescheduleGroomingServiceParams
	(*RescheduleGroomingServiceResult)(nil),                                 // 5: moego.api.appointment.v1.RescheduleGroomingServiceResult
	(*RescheduleEvaluationServiceParams)(nil),                               // 6: moego.api.appointment.v1.RescheduleEvaluationServiceParams
	(*RescheduleEvaluationServiceResult)(nil),                               // 7: moego.api.appointment.v1.RescheduleEvaluationServiceResult
	(*LodgingAssignParams)(nil),                                             // 8: moego.api.appointment.v1.LodgingAssignParams
	(*LodgingAssignResult)(nil),                                             // 9: moego.api.appointment.v1.LodgingAssignResult
	(*GetPetFeedingMedicationSchedulesParams)(nil),                          // 10: moego.api.appointment.v1.GetPetFeedingMedicationSchedulesParams
	(*GetPetFeedingMedicationSchedulesResult)(nil),                          // 11: moego.api.appointment.v1.GetPetFeedingMedicationSchedulesResult
	(*ReschedulePetFeedingMedicationParams)(nil),                            // 12: moego.api.appointment.v1.ReschedulePetFeedingMedicationParams
	(*ReschedulePetFeedingMedicationResult)(nil),                            // 13: moego.api.appointment.v1.ReschedulePetFeedingMedicationResult
	(*CalculateAppointmentScheduleParams)(nil),                              // 14: moego.api.appointment.v1.CalculateAppointmentScheduleParams
	(*CalculateAppointmentScheduleResult)(nil),                              // 15: moego.api.appointment.v1.CalculateAppointmentScheduleResult
	(*RescheduleDaycareServiceParams)(nil),                                  // 16: moego.api.appointment.v1.RescheduleDaycareServiceParams
	(*RescheduleDaycareServiceResult)(nil),                                  // 17: moego.api.appointment.v1.RescheduleDaycareServiceResult
	(*BatchRescheduleAppointmentParams)(nil),                                // 18: moego.api.appointment.v1.BatchRescheduleAppointmentParams
	(*BatchRescheduleAppointmentResult)(nil),                                // 19: moego.api.appointment.v1.BatchRescheduleAppointmentResult
	(*RescheduleCalendarCardParams)(nil),                                    // 20: moego.api.appointment.v1.RescheduleCalendarCardParams
	(*RescheduleCalendarCardResult)(nil),                                    // 21: moego.api.appointment.v1.RescheduleCalendarCardResult
	(*RescheduleAppointmentParams)(nil),                                     // 22: moego.api.appointment.v1.RescheduleAppointmentParams
	(*RescheduleAppointmentResult)(nil),                                     // 23: moego.api.appointment.v1.RescheduleAppointmentResult
	(*SwitchAllPetsStartAtSameTimeParams)(nil),                              // 24: moego.api.appointment.v1.SwitchAllPetsStartAtSameTimeParams
	(*SwitchAllPetsStartAtSameTimeResult)(nil),                              // 25: moego.api.appointment.v1.SwitchAllPetsStartAtSameTimeResult
	(*ReschedulePetDetailsParams)(nil),                                      // 26: moego.api.appointment.v1.ReschedulePetDetailsParams
	(*ReschedulePetDetailsResult)(nil),                                      // 27: moego.api.appointment.v1.ReschedulePetDetailsResult
	(*RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule)(nil), // 28: moego.api.appointment.v1.RescheduleDaycareServiceParams.RescheduleDaycareServiceSchedule
	(*v1.PetServiceScheduleDef)(nil),                                        // 29: moego.models.appointment.v1.PetServiceScheduleDef
	(v1.RepeatAppointmentModifyScope)(0),                                    // 30: moego.models.appointment.v1.RepeatAppointmentModifyScope
	(*v1.BoardingServiceScheduleDef)(nil),                                   // 31: moego.models.appointment.v1.BoardingServiceScheduleDef
	(v1.CalendarCardType)(0),                                                // 32: moego.models.appointment.v1.CalendarCardType
	(*v1.EvaluationServiceScheduleDef)(nil),                                 // 33: moego.models.appointment.v1.EvaluationServiceScheduleDef
	(*v1.ServiceLodgingAssignDef)(nil),                                      // 34: moego.models.appointment.v1.ServiceLodgingAssignDef
	(*v1.PetScheduleDef)(nil),                                               // 35: moego.models.appointment.v1.PetScheduleDef
	(*v1.AppointmentCreateDef)(nil),                                         // 36: moego.models.appointment.v1.AppointmentCreateDef
	(*v1.AppointmentScheduleDef)(nil),                                       // 37: moego.models.appointment.v1.AppointmentScheduleDef
	(*date.Date)(nil),                                                       // 38: google.type.Date
	(*v1.AppointmentModel)(nil),                                             // 39: moego.models.appointment.v1.AppointmentModel
	(*v1.PetDetailScheduleDef)(nil),                                         // 40: moego.models.appointment.v1.PetDetailScheduleDef
	(v1.PetDetailDateType)(0),                                               // 41: moego.models.appointment.v1.PetDetailDateType
}
var file_moego_api_appointment_v1_appointment_schedule_api_proto_depIdxs = []int32{
	29, // 0: moego.api.appointment.v1.RescheduleAllInOneServiceParams.pet_service_schedules:type_name -> moego.models.appointment.v1.PetServiceScheduleDef
	30, // 1: moego.api.appointment.v1.RescheduleAllInOneServiceParams.repeat_appointment_modify_scope:type_name -> moego.models.appointment.v1.RepeatAppointmentModifyScope
	31, // 2: moego.api.appointment.v1.RescheduleBoardingServiceParams.boarding_service_schedules:type_name -> moego.models.appointment.v1.BoardingServiceScheduleDef
	32, // 3: moego.api.appointment.v1.RescheduleGroomingServiceParams.card_type:type_name -> moego.models.appointment.v1.CalendarCardType
	30, // 4: moego.api.appointment.v1.RescheduleGroomingServiceParams.repeat_type:type_name -> moego.models.appointment.v1.RepeatAppointmentModifyScope
	33, // 5: moego.api.appointment.v1.RescheduleEvaluationServiceParams.evaluation_service_schedules:type_name -> moego.models.appointment.v1.EvaluationServiceScheduleDef
	34, // 6: moego.api.appointment.v1.LodgingAssignParams.services:type_name -> moego.models.appointment.v1.ServiceLodgingAssignDef
	34, // 7: moego.api.appointment.v1.LodgingAssignParams.evaluations:type_name -> moego.models.appointment.v1.ServiceLodgingAssignDef
	35, // 8: moego.api.appointment.v1.GetPetFeedingMedicationSchedulesResult.schedules:type_name -> moego.models.appointment.v1.PetScheduleDef
	35, // 9: moego.api.appointment.v1.ReschedulePetFeedingMedicationParams.schedules:type_name -> moego.models.appointment.v1.PetScheduleDef
	36, // 10: moego.api.appointment.v1.CalculateAppointmentScheduleParams.appointment:type_name -> moego.models.appointment.v1.AppointmentCreateDef
	29, // 11: moego.api.appointment.v1.CalculateAppointmentScheduleParams.pet_service_schedules:type_name -> moego.models.appointment.v1.PetServiceScheduleDef
	37, // 12: moego.api.appointment.v1.CalculateAppointmentScheduleResult.appointment_schedule:type_name -> moego.models.appointment.v1.AppointmentScheduleDef
	29, // 13: moego.api.appointment.v1.CalculateAppointmentScheduleResult.pet_service_schedules:type_name -> moego.models.appointment.v1.PetServiceScheduleDef
	28, // 14: moego.api.appointment.v1.RescheduleDaycareServiceParams.daycare_service_schedules:type_name -> moego.api.appointment.v1.RescheduleDaycareServiceParams.RescheduleDaycareServiceSchedule
	38, // 15: moego.api.appointment.v1.BatchRescheduleAppointmentParams.source_date:type_name -> google.type.Date
	38, // 16: moego.api.appointment.v1.BatchRescheduleAppointmentParams.target_date:type_name -> google.type.Date
	39, // 17: moego.api.appointment.v1.BatchRescheduleAppointmentResult.appointments:type_name -> moego.models.appointment.v1.AppointmentModel
	32, // 18: moego.api.appointment.v1.RescheduleCalendarCardParams.card_type:type_name -> moego.models.appointment.v1.CalendarCardType
	30, // 19: moego.api.appointment.v1.RescheduleCalendarCardParams.repeat_modify_type:type_name -> moego.models.appointment.v1.RepeatAppointmentModifyScope
	30, // 20: moego.api.appointment.v1.RescheduleAppointmentParams.repeat_modify_type:type_name -> moego.models.appointment.v1.RepeatAppointmentModifyScope
	40, // 21: moego.api.appointment.v1.ReschedulePetDetailsParams.pet_details:type_name -> moego.models.appointment.v1.PetDetailScheduleDef
	30, // 22: moego.api.appointment.v1.ReschedulePetDetailsParams.repeat_appointment_modify_scope:type_name -> moego.models.appointment.v1.RepeatAppointmentModifyScope
	41, // 23: moego.api.appointment.v1.RescheduleDaycareServiceParams.RescheduleDaycareServiceSchedule.date_type:type_name -> moego.models.appointment.v1.PetDetailDateType
	0,  // 24: moego.api.appointment.v1.AppointmentScheduleService.RescheduleAllInOneService:input_type -> moego.api.appointment.v1.RescheduleAllInOneServiceParams
	2,  // 25: moego.api.appointment.v1.AppointmentScheduleService.RescheduleBoardingService:input_type -> moego.api.appointment.v1.RescheduleBoardingServiceParams
	16, // 26: moego.api.appointment.v1.AppointmentScheduleService.RescheduleDaycareService:input_type -> moego.api.appointment.v1.RescheduleDaycareServiceParams
	4,  // 27: moego.api.appointment.v1.AppointmentScheduleService.RescheduleGroomingService:input_type -> moego.api.appointment.v1.RescheduleGroomingServiceParams
	6,  // 28: moego.api.appointment.v1.AppointmentScheduleService.RescheduleEvaluationService:input_type -> moego.api.appointment.v1.RescheduleEvaluationServiceParams
	8,  // 29: moego.api.appointment.v1.AppointmentScheduleService.LodgingAssign:input_type -> moego.api.appointment.v1.LodgingAssignParams
	10, // 30: moego.api.appointment.v1.AppointmentScheduleService.GetPetFeedingMedicationSchedules:input_type -> moego.api.appointment.v1.GetPetFeedingMedicationSchedulesParams
	12, // 31: moego.api.appointment.v1.AppointmentScheduleService.ReschedulePetFeedingMedication:input_type -> moego.api.appointment.v1.ReschedulePetFeedingMedicationParams
	14, // 32: moego.api.appointment.v1.AppointmentScheduleService.CalculateAppointmentSchedule:input_type -> moego.api.appointment.v1.CalculateAppointmentScheduleParams
	18, // 33: moego.api.appointment.v1.AppointmentScheduleService.BatchRescheduleAppointment:input_type -> moego.api.appointment.v1.BatchRescheduleAppointmentParams
	20, // 34: moego.api.appointment.v1.AppointmentScheduleService.RescheduleCalendarCard:input_type -> moego.api.appointment.v1.RescheduleCalendarCardParams
	22, // 35: moego.api.appointment.v1.AppointmentScheduleService.RescheduleAppointment:input_type -> moego.api.appointment.v1.RescheduleAppointmentParams
	24, // 36: moego.api.appointment.v1.AppointmentScheduleService.SwitchAllPetsStartAtSameTime:input_type -> moego.api.appointment.v1.SwitchAllPetsStartAtSameTimeParams
	26, // 37: moego.api.appointment.v1.AppointmentScheduleService.ReschedulePetDetails:input_type -> moego.api.appointment.v1.ReschedulePetDetailsParams
	1,  // 38: moego.api.appointment.v1.AppointmentScheduleService.RescheduleAllInOneService:output_type -> moego.api.appointment.v1.RescheduleAllInOneServiceResult
	3,  // 39: moego.api.appointment.v1.AppointmentScheduleService.RescheduleBoardingService:output_type -> moego.api.appointment.v1.RescheduleBoardingServiceResult
	17, // 40: moego.api.appointment.v1.AppointmentScheduleService.RescheduleDaycareService:output_type -> moego.api.appointment.v1.RescheduleDaycareServiceResult
	5,  // 41: moego.api.appointment.v1.AppointmentScheduleService.RescheduleGroomingService:output_type -> moego.api.appointment.v1.RescheduleGroomingServiceResult
	7,  // 42: moego.api.appointment.v1.AppointmentScheduleService.RescheduleEvaluationService:output_type -> moego.api.appointment.v1.RescheduleEvaluationServiceResult
	9,  // 43: moego.api.appointment.v1.AppointmentScheduleService.LodgingAssign:output_type -> moego.api.appointment.v1.LodgingAssignResult
	11, // 44: moego.api.appointment.v1.AppointmentScheduleService.GetPetFeedingMedicationSchedules:output_type -> moego.api.appointment.v1.GetPetFeedingMedicationSchedulesResult
	13, // 45: moego.api.appointment.v1.AppointmentScheduleService.ReschedulePetFeedingMedication:output_type -> moego.api.appointment.v1.ReschedulePetFeedingMedicationResult
	15, // 46: moego.api.appointment.v1.AppointmentScheduleService.CalculateAppointmentSchedule:output_type -> moego.api.appointment.v1.CalculateAppointmentScheduleResult
	19, // 47: moego.api.appointment.v1.AppointmentScheduleService.BatchRescheduleAppointment:output_type -> moego.api.appointment.v1.BatchRescheduleAppointmentResult
	21, // 48: moego.api.appointment.v1.AppointmentScheduleService.RescheduleCalendarCard:output_type -> moego.api.appointment.v1.RescheduleCalendarCardResult
	23, // 49: moego.api.appointment.v1.AppointmentScheduleService.RescheduleAppointment:output_type -> moego.api.appointment.v1.RescheduleAppointmentResult
	25, // 50: moego.api.appointment.v1.AppointmentScheduleService.SwitchAllPetsStartAtSameTime:output_type -> moego.api.appointment.v1.SwitchAllPetsStartAtSameTimeResult
	27, // 51: moego.api.appointment.v1.AppointmentScheduleService.ReschedulePetDetails:output_type -> moego.api.appointment.v1.ReschedulePetDetailsResult
	38, // [38:52] is the sub-list for method output_type
	24, // [24:38] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_moego_api_appointment_v1_appointment_schedule_api_proto_init() }
func file_moego_api_appointment_v1_appointment_schedule_api_proto_init() {
	if File_moego_api_appointment_v1_appointment_schedule_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleAllInOneServiceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleAllInOneServiceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleBoardingServiceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleBoardingServiceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleGroomingServiceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleGroomingServiceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleEvaluationServiceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleEvaluationServiceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingAssignParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingAssignResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetFeedingMedicationSchedulesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetFeedingMedicationSchedulesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReschedulePetFeedingMedicationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReschedulePetFeedingMedicationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculateAppointmentScheduleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculateAppointmentScheduleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleDaycareServiceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleDaycareServiceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchRescheduleAppointmentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchRescheduleAppointmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleCalendarCardParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleCalendarCardResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleAppointmentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleAppointmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SwitchAllPetsStartAtSameTimeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SwitchAllPetsStartAtSameTimeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReschedulePetDetailsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReschedulePetDetailsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleDaycareServiceParams_RescheduleDaycareServiceSchedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[18].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[20].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[22].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[26].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes[28].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_appointment_v1_appointment_schedule_api_proto_goTypes,
		DependencyIndexes: file_moego_api_appointment_v1_appointment_schedule_api_proto_depIdxs,
		MessageInfos:      file_moego_api_appointment_v1_appointment_schedule_api_proto_msgTypes,
	}.Build()
	File_moego_api_appointment_v1_appointment_schedule_api_proto = out.File
	file_moego_api_appointment_v1_appointment_schedule_api_proto_rawDesc = nil
	file_moego_api_appointment_v1_appointment_schedule_api_proto_goTypes = nil
	file_moego_api_appointment_v1_appointment_schedule_api_proto_depIdxs = nil
}
