// @since 2024-11-13 15:32:15
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/appointment/v1/print_card_api.proto

package appointmentapipb

import (
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get print appointment card list params
type ListAppointmentCardParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// target date
	Date *date.Date `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	// service item type
	//
	// Deprecated: Do not use.
	Types []v1.ServiceItemType `protobuf:"varint,3,rep,packed,name=types,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"types,omitempty"`
	// is pet check in
	IsPetsCheckedInOnly *bool `protobuf:"varint,4,opt,name=is_pets_checked_in_only,json=isPetsCheckedInOnly,proto3,oneof" json:"is_pets_checked_in_only,omitempty"`
	// service filter
	ServiceFilters []*ServiceFilter `protobuf:"bytes,5,rep,name=service_filters,json=serviceFilters,proto3" json:"service_filters,omitempty"`
}

func (x *ListAppointmentCardParams) Reset() {
	*x = ListAppointmentCardParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentCardParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentCardParams) ProtoMessage() {}

func (x *ListAppointmentCardParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentCardParams.ProtoReflect.Descriptor instead.
func (*ListAppointmentCardParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{0}
}

func (x *ListAppointmentCardParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListAppointmentCardParams) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

// Deprecated: Do not use.
func (x *ListAppointmentCardParams) GetTypes() []v1.ServiceItemType {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *ListAppointmentCardParams) GetIsPetsCheckedInOnly() bool {
	if x != nil && x.IsPetsCheckedInOnly != nil {
		return *x.IsPetsCheckedInOnly
	}
	return false
}

func (x *ListAppointmentCardParams) GetServiceFilters() []*ServiceFilter {
	if x != nil {
		return x.ServiceFilters
	}
	return nil
}

// service filter
type ServiceFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// is all service
	IsAllService bool `protobuf:"varint,2,opt,name=is_all_service,json=isAllService,proto3" json:"is_all_service,omitempty"`
	// service ids
	ServiceIds []int64 `protobuf:"varint,3,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
}

func (x *ServiceFilter) Reset() {
	*x = ServiceFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceFilter) ProtoMessage() {}

func (x *ServiceFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceFilter.ProtoReflect.Descriptor instead.
func (*ServiceFilter) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceFilter) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *ServiceFilter) GetIsAllService() bool {
	if x != nil {
		return x.IsAllService
	}
	return false
}

func (x *ServiceFilter) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

// get appointment card list result
type ListAppointmentCardResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// boarding view list
	Boardings []*ListAppointmentCardResult_BoardingView `protobuf:"bytes,1,rep,name=boardings,proto3" json:"boardings,omitempty"`
	// daycare view list
	Daycares []*ListAppointmentCardResult_DaycareView `protobuf:"bytes,2,rep,name=daycares,proto3" json:"daycares,omitempty"`
	// grooming view list
	Groomings []*ListAppointmentCardResult_GroomingView `protobuf:"bytes,3,rep,name=groomings,proto3" json:"groomings,omitempty"`
	// evaluation view list
	Evaluations []*ListAppointmentCardResult_EvaluationView `protobuf:"bytes,4,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
	// dog walking view list
	DogWalkings []*ListAppointmentCardResult_DogWalkingView `protobuf:"bytes,5,rep,name=dog_walkings,json=dogWalkings,proto3" json:"dog_walkings,omitempty"`
	// pet view list
	Pets []*ListAppointmentCardResult_PetView `protobuf:"bytes,11,rep,name=pets,proto3" json:"pets,omitempty"`
	// appointment view list
	Appointments []*ListAppointmentCardResult_AppointmentView `protobuf:"bytes,12,rep,name=appointments,proto3" json:"appointments,omitempty"`
	// lodging type list view
	LodgingTypes []*ListAppointmentCardResult_LodgingTypeDayView `protobuf:"bytes,13,rep,name=lodging_types,json=lodgingTypes,proto3" json:"lodging_types,omitempty"`
	// lodging unit list
	LodgingUnits []*v1.LodgingUnitModel `protobuf:"bytes,14,rep,name=lodging_units,json=lodgingUnits,proto3" json:"lodging_units,omitempty"`
}

func (x *ListAppointmentCardResult) Reset() {
	*x = ListAppointmentCardResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentCardResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentCardResult) ProtoMessage() {}

func (x *ListAppointmentCardResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentCardResult.ProtoReflect.Descriptor instead.
func (*ListAppointmentCardResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{2}
}

func (x *ListAppointmentCardResult) GetBoardings() []*ListAppointmentCardResult_BoardingView {
	if x != nil {
		return x.Boardings
	}
	return nil
}

func (x *ListAppointmentCardResult) GetDaycares() []*ListAppointmentCardResult_DaycareView {
	if x != nil {
		return x.Daycares
	}
	return nil
}

func (x *ListAppointmentCardResult) GetGroomings() []*ListAppointmentCardResult_GroomingView {
	if x != nil {
		return x.Groomings
	}
	return nil
}

func (x *ListAppointmentCardResult) GetEvaluations() []*ListAppointmentCardResult_EvaluationView {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

func (x *ListAppointmentCardResult) GetDogWalkings() []*ListAppointmentCardResult_DogWalkingView {
	if x != nil {
		return x.DogWalkings
	}
	return nil
}

func (x *ListAppointmentCardResult) GetPets() []*ListAppointmentCardResult_PetView {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *ListAppointmentCardResult) GetAppointments() []*ListAppointmentCardResult_AppointmentView {
	if x != nil {
		return x.Appointments
	}
	return nil
}

func (x *ListAppointmentCardResult) GetLodgingTypes() []*ListAppointmentCardResult_LodgingTypeDayView {
	if x != nil {
		return x.LodgingTypes
	}
	return nil
}

func (x *ListAppointmentCardResult) GetLodgingUnits() []*v1.LodgingUnitModel {
	if x != nil {
		return x.LodgingUnits
	}
	return nil
}

// The params message for ListDailyTasks
type ListDailyTasksParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// target date
	Date *date.Date `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	// The service item types
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,3,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
}

func (x *ListDailyTasksParams) Reset() {
	*x = ListDailyTasksParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyTasksParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyTasksParams) ProtoMessage() {}

func (x *ListDailyTasksParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyTasksParams.ProtoReflect.Descriptor instead.
func (*ListDailyTasksParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListDailyTasksParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListDailyTasksParams) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *ListDailyTasksParams) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

// The result message for ListDailyTasks
type ListDailyTasksResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of feeding tasks
	Feedings []*ListDailyTasksResult_TaskRow `protobuf:"bytes,1,rep,name=feedings,proto3" json:"feedings,omitempty"`
	// The list of medication tasks
	Medications []*ListDailyTasksResult_TaskRow `protobuf:"bytes,2,rep,name=medications,proto3" json:"medications,omitempty"`
	// The list of add-on groups
	AddOns []*ListDailyTasksResult_AddOnGroup `protobuf:"bytes,3,rep,name=add_ons,json=addOns,proto3" json:"add_ons,omitempty"`
}

func (x *ListDailyTasksResult) Reset() {
	*x = ListDailyTasksResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyTasksResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyTasksResult) ProtoMessage() {}

func (x *ListDailyTasksResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyTasksResult.ProtoReflect.Descriptor instead.
func (*ListDailyTasksResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{4}
}

func (x *ListDailyTasksResult) GetFeedings() []*ListDailyTasksResult_TaskRow {
	if x != nil {
		return x.Feedings
	}
	return nil
}

func (x *ListDailyTasksResult) GetMedications() []*ListDailyTasksResult_TaskRow {
	if x != nil {
		return x.Medications
	}
	return nil
}

func (x *ListDailyTasksResult) GetAddOns() []*ListDailyTasksResult_AddOnGroup {
	if x != nil {
		return x.AddOns
	}
	return nil
}

// The params message for ListBoardingDepartureCard
type ListBoardingDepartureCardParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// target date
	Date *date.Date `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	// is pet check in
	IsPetsCheckedInOnly *bool `protobuf:"varint,3,opt,name=is_pets_checked_in_only,json=isPetsCheckedInOnly,proto3,oneof" json:"is_pets_checked_in_only,omitempty"`
}

func (x *ListBoardingDepartureCardParams) Reset() {
	*x = ListBoardingDepartureCardParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBoardingDepartureCardParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBoardingDepartureCardParams) ProtoMessage() {}

func (x *ListBoardingDepartureCardParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBoardingDepartureCardParams.ProtoReflect.Descriptor instead.
func (*ListBoardingDepartureCardParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{5}
}

func (x *ListBoardingDepartureCardParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListBoardingDepartureCardParams) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *ListBoardingDepartureCardParams) GetIsPetsCheckedInOnly() bool {
	if x != nil && x.IsPetsCheckedInOnly != nil {
		return *x.IsPetsCheckedInOnly
	}
	return false
}

// The result message for ListBoardingDepartureCard
type ListBoardingDepartureCardResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// boarding view list
	Boardings []*ListBoardingDepartureCardResult_BoardingView `protobuf:"bytes,1,rep,name=boardings,proto3" json:"boardings,omitempty"`
	// pet view list
	Pets []*ListAppointmentCardResult_PetView `protobuf:"bytes,11,rep,name=pets,proto3" json:"pets,omitempty"`
	// appointment view list
	Appointments []*ListAppointmentCardResult_AppointmentView `protobuf:"bytes,12,rep,name=appointments,proto3" json:"appointments,omitempty"`
	// lodging type list view
	LodgingTypes []*ListAppointmentCardResult_LodgingTypeDayView `protobuf:"bytes,13,rep,name=lodging_types,json=lodgingTypes,proto3" json:"lodging_types,omitempty"`
	// lodging unit list
	LodgingUnits []*v1.LodgingUnitModel `protobuf:"bytes,14,rep,name=lodging_units,json=lodgingUnits,proto3" json:"lodging_units,omitempty"`
}

func (x *ListBoardingDepartureCardResult) Reset() {
	*x = ListBoardingDepartureCardResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBoardingDepartureCardResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBoardingDepartureCardResult) ProtoMessage() {}

func (x *ListBoardingDepartureCardResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBoardingDepartureCardResult.ProtoReflect.Descriptor instead.
func (*ListBoardingDepartureCardResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{6}
}

func (x *ListBoardingDepartureCardResult) GetBoardings() []*ListBoardingDepartureCardResult_BoardingView {
	if x != nil {
		return x.Boardings
	}
	return nil
}

func (x *ListBoardingDepartureCardResult) GetPets() []*ListAppointmentCardResult_PetView {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *ListBoardingDepartureCardResult) GetAppointments() []*ListAppointmentCardResult_AppointmentView {
	if x != nil {
		return x.Appointments
	}
	return nil
}

func (x *ListBoardingDepartureCardResult) GetLodgingTypes() []*ListAppointmentCardResult_LodgingTypeDayView {
	if x != nil {
		return x.LodgingTypes
	}
	return nil
}

func (x *ListBoardingDepartureCardResult) GetLodgingUnits() []*v1.LodgingUnitModel {
	if x != nil {
		return x.LodgingUnits
	}
	return nil
}

// The params message for list daily playgroup params
type ListDailyPlaygroupCardParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// target date
	Date *date.Date `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	// is pet check in
	IsPetsCheckedInOnly *bool `protobuf:"varint,3,opt,name=is_pets_checked_in_only,json=isPetsCheckedInOnly,proto3,oneof" json:"is_pets_checked_in_only,omitempty"`
}

func (x *ListDailyPlaygroupCardParams) Reset() {
	*x = ListDailyPlaygroupCardParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyPlaygroupCardParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyPlaygroupCardParams) ProtoMessage() {}

func (x *ListDailyPlaygroupCardParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyPlaygroupCardParams.ProtoReflect.Descriptor instead.
func (*ListDailyPlaygroupCardParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{7}
}

func (x *ListDailyPlaygroupCardParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListDailyPlaygroupCardParams) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *ListDailyPlaygroupCardParams) GetIsPetsCheckedInOnly() bool {
	if x != nil && x.IsPetsCheckedInOnly != nil {
		return *x.IsPetsCheckedInOnly
	}
	return false
}

// The result message for list daily playgroup result
type ListDailyPlaygroupCardResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// playgroup model list
	Playgroups []*v1.PlaygroupModel `protobuf:"bytes,1,rep,name=playgroups,proto3" json:"playgroups,omitempty"`
	// playgroup view
	PlaygroupViews []*ListDailyPlaygroupCardResult_PlaygroupView `protobuf:"bytes,2,rep,name=playgroup_views,json=playgroupViews,proto3" json:"playgroup_views,omitempty"`
	// pet view list
	Pets []*ListDailyPlaygroupCardResult_PetView `protobuf:"bytes,3,rep,name=pets,proto3" json:"pets,omitempty"`
}

func (x *ListDailyPlaygroupCardResult) Reset() {
	*x = ListDailyPlaygroupCardResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyPlaygroupCardResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyPlaygroupCardResult) ProtoMessage() {}

func (x *ListDailyPlaygroupCardResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyPlaygroupCardResult.ProtoReflect.Descriptor instead.
func (*ListDailyPlaygroupCardResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{8}
}

func (x *ListDailyPlaygroupCardResult) GetPlaygroups() []*v1.PlaygroupModel {
	if x != nil {
		return x.Playgroups
	}
	return nil
}

func (x *ListDailyPlaygroupCardResult) GetPlaygroupViews() []*ListDailyPlaygroupCardResult_PlaygroupView {
	if x != nil {
		return x.PlaygroupViews
	}
	return nil
}

func (x *ListDailyPlaygroupCardResult) GetPets() []*ListDailyPlaygroupCardResult_PetView {
	if x != nil {
		return x.Pets
	}
	return nil
}

// The params message for ListBoardingArrivalCard
type ListBoardingArrivalCardParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// target date
	Date *date.Date `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	// is pet check in
	IsPetsCheckedInOnly *bool `protobuf:"varint,3,opt,name=is_pets_checked_in_only,json=isPetsCheckedInOnly,proto3,oneof" json:"is_pets_checked_in_only,omitempty"`
}

func (x *ListBoardingArrivalCardParams) Reset() {
	*x = ListBoardingArrivalCardParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBoardingArrivalCardParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBoardingArrivalCardParams) ProtoMessage() {}

func (x *ListBoardingArrivalCardParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBoardingArrivalCardParams.ProtoReflect.Descriptor instead.
func (*ListBoardingArrivalCardParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{9}
}

func (x *ListBoardingArrivalCardParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListBoardingArrivalCardParams) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *ListBoardingArrivalCardParams) GetIsPetsCheckedInOnly() bool {
	if x != nil && x.IsPetsCheckedInOnly != nil {
		return *x.IsPetsCheckedInOnly
	}
	return false
}

// The result message for ListBoardingArrivalCard
type ListBoardingArrivalCardResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// boarding view list
	Boardings []*ListBoardingArrivalCardResult_BoardingView `protobuf:"bytes,1,rep,name=boardings,proto3" json:"boardings,omitempty"`
	// pet view list
	Pets []*ListAppointmentCardResult_PetView `protobuf:"bytes,11,rep,name=pets,proto3" json:"pets,omitempty"`
	// appointment view list
	Appointments []*ListAppointmentCardResult_AppointmentView `protobuf:"bytes,12,rep,name=appointments,proto3" json:"appointments,omitempty"`
	// lodging type list view
	LodgingTypes []*ListAppointmentCardResult_LodgingTypeDayView `protobuf:"bytes,13,rep,name=lodging_types,json=lodgingTypes,proto3" json:"lodging_types,omitempty"`
	// lodging unit list
	LodgingUnits []*v1.LodgingUnitModel `protobuf:"bytes,14,rep,name=lodging_units,json=lodgingUnits,proto3" json:"lodging_units,omitempty"`
}

func (x *ListBoardingArrivalCardResult) Reset() {
	*x = ListBoardingArrivalCardResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBoardingArrivalCardResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBoardingArrivalCardResult) ProtoMessage() {}

func (x *ListBoardingArrivalCardResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBoardingArrivalCardResult.ProtoReflect.Descriptor instead.
func (*ListBoardingArrivalCardResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{10}
}

func (x *ListBoardingArrivalCardResult) GetBoardings() []*ListBoardingArrivalCardResult_BoardingView {
	if x != nil {
		return x.Boardings
	}
	return nil
}

func (x *ListBoardingArrivalCardResult) GetPets() []*ListAppointmentCardResult_PetView {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *ListBoardingArrivalCardResult) GetAppointments() []*ListAppointmentCardResult_AppointmentView {
	if x != nil {
		return x.Appointments
	}
	return nil
}

func (x *ListBoardingArrivalCardResult) GetLodgingTypes() []*ListAppointmentCardResult_LodgingTypeDayView {
	if x != nil {
		return x.LodgingTypes
	}
	return nil
}

func (x *ListBoardingArrivalCardResult) GetLodgingUnits() []*v1.LodgingUnitModel {
	if x != nil {
		return x.LodgingUnits
	}
	return nil
}

// boarding view
type ListAppointmentCardResult_BoardingView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet service list
	PetServices []*ListAppointmentCardResult_BoardingView_PetServiceView `protobuf:"bytes,3,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
}

func (x *ListAppointmentCardResult_BoardingView) Reset() {
	*x = ListAppointmentCardResult_BoardingView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentCardResult_BoardingView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentCardResult_BoardingView) ProtoMessage() {}

func (x *ListAppointmentCardResult_BoardingView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentCardResult_BoardingView.ProtoReflect.Descriptor instead.
func (*ListAppointmentCardResult_BoardingView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ListAppointmentCardResult_BoardingView) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ListAppointmentCardResult_BoardingView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListAppointmentCardResult_BoardingView) GetPetServices() []*ListAppointmentCardResult_BoardingView_PetServiceView {
	if x != nil {
		return x.PetServices
	}
	return nil
}

// daycare view
type ListAppointmentCardResult_DaycareView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet service
	PetServices []*ListAppointmentCardResult_DaycareView_PetServiceView `protobuf:"bytes,3,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
}

func (x *ListAppointmentCardResult_DaycareView) Reset() {
	*x = ListAppointmentCardResult_DaycareView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentCardResult_DaycareView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentCardResult_DaycareView) ProtoMessage() {}

func (x *ListAppointmentCardResult_DaycareView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentCardResult_DaycareView.ProtoReflect.Descriptor instead.
func (*ListAppointmentCardResult_DaycareView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{2, 1}
}

func (x *ListAppointmentCardResult_DaycareView) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ListAppointmentCardResult_DaycareView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListAppointmentCardResult_DaycareView) GetPetServices() []*ListAppointmentCardResult_DaycareView_PetServiceView {
	if x != nil {
		return x.PetServices
	}
	return nil
}

// grooming view
type ListAppointmentCardResult_GroomingView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet service
	PetServices []*ListAppointmentCardResult_GroomingView_PetServiceView `protobuf:"bytes,3,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
}

func (x *ListAppointmentCardResult_GroomingView) Reset() {
	*x = ListAppointmentCardResult_GroomingView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentCardResult_GroomingView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentCardResult_GroomingView) ProtoMessage() {}

func (x *ListAppointmentCardResult_GroomingView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentCardResult_GroomingView.ProtoReflect.Descriptor instead.
func (*ListAppointmentCardResult_GroomingView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{2, 2}
}

func (x *ListAppointmentCardResult_GroomingView) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ListAppointmentCardResult_GroomingView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListAppointmentCardResult_GroomingView) GetPetServices() []*ListAppointmentCardResult_GroomingView_PetServiceView {
	if x != nil {
		return x.PetServices
	}
	return nil
}

// evaluation view
type ListAppointmentCardResult_EvaluationView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet service
	PetServices []*ListAppointmentCardResult_EvaluationView_PetServiceView `protobuf:"bytes,3,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
}

func (x *ListAppointmentCardResult_EvaluationView) Reset() {
	*x = ListAppointmentCardResult_EvaluationView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentCardResult_EvaluationView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentCardResult_EvaluationView) ProtoMessage() {}

func (x *ListAppointmentCardResult_EvaluationView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentCardResult_EvaluationView.ProtoReflect.Descriptor instead.
func (*ListAppointmentCardResult_EvaluationView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{2, 3}
}

func (x *ListAppointmentCardResult_EvaluationView) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ListAppointmentCardResult_EvaluationView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListAppointmentCardResult_EvaluationView) GetPetServices() []*ListAppointmentCardResult_EvaluationView_PetServiceView {
	if x != nil {
		return x.PetServices
	}
	return nil
}

// dog walking view
type ListAppointmentCardResult_DogWalkingView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet service
	PetServices []*ListAppointmentCardResult_DogWalkingView_PetServiceView `protobuf:"bytes,3,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
}

func (x *ListAppointmentCardResult_DogWalkingView) Reset() {
	*x = ListAppointmentCardResult_DogWalkingView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentCardResult_DogWalkingView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentCardResult_DogWalkingView) ProtoMessage() {}

func (x *ListAppointmentCardResult_DogWalkingView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentCardResult_DogWalkingView.ProtoReflect.Descriptor instead.
func (*ListAppointmentCardResult_DogWalkingView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{2, 4}
}

func (x *ListAppointmentCardResult_DogWalkingView) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ListAppointmentCardResult_DogWalkingView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListAppointmentCardResult_DogWalkingView) GetPetServices() []*ListAppointmentCardResult_DogWalkingView_PetServiceView {
	if x != nil {
		return x.PetServices
	}
	return nil
}

// pet view
type ListAppointmentCardResult_PetView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// gender
	Gender v11.PetGender `protobuf:"varint,4,opt,name=gender,proto3,enum=moego.models.customer.v1.PetGender" json:"gender,omitempty"`
	// breed
	Breed string `protobuf:"bytes,5,opt,name=breed,proto3" json:"breed,omitempty"`
	// pet type id
	PetType v11.PetType `protobuf:"varint,6,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// weight
	Weight string `protobuf:"bytes,7,opt,name=weight,proto3" json:"weight,omitempty"`
	// pet code id list
	PetCodeIds []int64 `protobuf:"varint,8,rep,packed,name=pet_code_ids,json=petCodeIds,proto3" json:"pet_code_ids,omitempty"`
	// pet notes
	PetNotes []string `protobuf:"bytes,9,rep,name=pet_notes,json=petNotes,proto3" json:"pet_notes,omitempty"`
	// pet appearance color
	PetAppearanceColor string `protobuf:"bytes,10,opt,name=pet_appearance_color,json=petAppearanceColor,proto3" json:"pet_appearance_color,omitempty"`
	// pet appearance notes
	PetAppearanceNotes string `protobuf:"bytes,11,opt,name=pet_appearance_notes,json=petAppearanceNotes,proto3" json:"pet_appearance_notes,omitempty"`
	// pet fixed
	PetFixed string `protobuf:"bytes,12,opt,name=pet_fixed,json=petFixed,proto3" json:"pet_fixed,omitempty"`
	// pet owner name
	OwnerName string `protobuf:"bytes,13,opt,name=owner_name,json=ownerName,proto3" json:"owner_name,omitempty"`
	// pet owner first name
	OwnerFirstName string `protobuf:"bytes,14,opt,name=owner_first_name,json=ownerFirstName,proto3" json:"owner_first_name,omitempty"`
	// pet owner last name
	OwnerLastName string `protobuf:"bytes,15,opt,name=owner_last_name,json=ownerLastName,proto3" json:"owner_last_name,omitempty"`
	// pet code bindings
	PetCodeBindings []*ListAppointmentCardResult_PetCodeBindingsView `protobuf:"bytes,16,rep,name=pet_code_bindings,json=petCodeBindings,proto3" json:"pet_code_bindings,omitempty"`
}

func (x *ListAppointmentCardResult_PetView) Reset() {
	*x = ListAppointmentCardResult_PetView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentCardResult_PetView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentCardResult_PetView) ProtoMessage() {}

func (x *ListAppointmentCardResult_PetView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentCardResult_PetView.ProtoReflect.Descriptor instead.
func (*ListAppointmentCardResult_PetView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{2, 5}
}

func (x *ListAppointmentCardResult_PetView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListAppointmentCardResult_PetView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListAppointmentCardResult_PetView) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *ListAppointmentCardResult_PetView) GetGender() v11.PetGender {
	if x != nil {
		return x.Gender
	}
	return v11.PetGender(0)
}

func (x *ListAppointmentCardResult_PetView) GetBreed() string {
	if x != nil {
		return x.Breed
	}
	return ""
}

func (x *ListAppointmentCardResult_PetView) GetPetType() v11.PetType {
	if x != nil {
		return x.PetType
	}
	return v11.PetType(0)
}

func (x *ListAppointmentCardResult_PetView) GetWeight() string {
	if x != nil {
		return x.Weight
	}
	return ""
}

func (x *ListAppointmentCardResult_PetView) GetPetCodeIds() []int64 {
	if x != nil {
		return x.PetCodeIds
	}
	return nil
}

func (x *ListAppointmentCardResult_PetView) GetPetNotes() []string {
	if x != nil {
		return x.PetNotes
	}
	return nil
}

func (x *ListAppointmentCardResult_PetView) GetPetAppearanceColor() string {
	if x != nil {
		return x.PetAppearanceColor
	}
	return ""
}

func (x *ListAppointmentCardResult_PetView) GetPetAppearanceNotes() string {
	if x != nil {
		return x.PetAppearanceNotes
	}
	return ""
}

func (x *ListAppointmentCardResult_PetView) GetPetFixed() string {
	if x != nil {
		return x.PetFixed
	}
	return ""
}

func (x *ListAppointmentCardResult_PetView) GetOwnerName() string {
	if x != nil {
		return x.OwnerName
	}
	return ""
}

func (x *ListAppointmentCardResult_PetView) GetOwnerFirstName() string {
	if x != nil {
		return x.OwnerFirstName
	}
	return ""
}

func (x *ListAppointmentCardResult_PetView) GetOwnerLastName() string {
	if x != nil {
		return x.OwnerLastName
	}
	return ""
}

func (x *ListAppointmentCardResult_PetView) GetPetCodeBindings() []*ListAppointmentCardResult_PetCodeBindingsView {
	if x != nil {
		return x.PetCodeBindings
	}
	return nil
}

// appointment view
type ListAppointmentCardResult_AppointmentView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// appointment comment
	Comment string `protobuf:"bytes,2,opt,name=comment,proto3" json:"comment,omitempty"`
	// appointment alert
	Alert string `protobuf:"bytes,3,opt,name=alert,proto3" json:"alert,omitempty"`
}

func (x *ListAppointmentCardResult_AppointmentView) Reset() {
	*x = ListAppointmentCardResult_AppointmentView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentCardResult_AppointmentView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentCardResult_AppointmentView) ProtoMessage() {}

func (x *ListAppointmentCardResult_AppointmentView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentCardResult_AppointmentView.ProtoReflect.Descriptor instead.
func (*ListAppointmentCardResult_AppointmentView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{2, 6}
}

func (x *ListAppointmentCardResult_AppointmentView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListAppointmentCardResult_AppointmentView) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *ListAppointmentCardResult_AppointmentView) GetAlert() string {
	if x != nil {
		return x.Alert
	}
	return ""
}

// lodging type day view
type ListAppointmentCardResult_LodgingTypeDayView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the lodging type
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name of the lodging type
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// max pet number of this lodging type
	MaxPetNum int32 `protobuf:"varint,3,opt,name=max_pet_num,json=maxPetNum,proto3" json:"max_pet_num,omitempty"`
	// max pet num for all lodging types
	MaxPetTotalNum int32 `protobuf:"varint,4,opt,name=max_pet_total_num,json=maxPetTotalNum,proto3" json:"max_pet_total_num,omitempty"`
	// occupied pet num
	OccupiedPetNum int32 `protobuf:"varint,5,opt,name=occupied_pet_num,json=occupiedPetNum,proto3" json:"occupied_pet_num,omitempty"`
	// sort
	Sort int32 `protobuf:"varint,6,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *ListAppointmentCardResult_LodgingTypeDayView) Reset() {
	*x = ListAppointmentCardResult_LodgingTypeDayView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentCardResult_LodgingTypeDayView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentCardResult_LodgingTypeDayView) ProtoMessage() {}

func (x *ListAppointmentCardResult_LodgingTypeDayView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentCardResult_LodgingTypeDayView.ProtoReflect.Descriptor instead.
func (*ListAppointmentCardResult_LodgingTypeDayView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{2, 7}
}

func (x *ListAppointmentCardResult_LodgingTypeDayView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListAppointmentCardResult_LodgingTypeDayView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListAppointmentCardResult_LodgingTypeDayView) GetMaxPetNum() int32 {
	if x != nil {
		return x.MaxPetNum
	}
	return 0
}

func (x *ListAppointmentCardResult_LodgingTypeDayView) GetMaxPetTotalNum() int32 {
	if x != nil {
		return x.MaxPetTotalNum
	}
	return 0
}

func (x *ListAppointmentCardResult_LodgingTypeDayView) GetOccupiedPetNum() int32 {
	if x != nil {
		return x.OccupiedPetNum
	}
	return 0
}

func (x *ListAppointmentCardResult_LodgingTypeDayView) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

// pet code bindings view
type ListAppointmentCardResult_PetCodeBindingsView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code id
	PetCodeId int64 `protobuf:"varint,1,opt,name=pet_code_id,json=petCodeId,proto3" json:"pet_code_id,omitempty"`
	// pet code abbreviation
	Abbreviation string `protobuf:"bytes,2,opt,name=abbreviation,proto3" json:"abbreviation,omitempty"`
	// pet code color
	Color string `protobuf:"bytes,3,opt,name=color,proto3" json:"color,omitempty"`
	// pet code unique comment
	UniqueComment string `protobuf:"bytes,4,opt,name=unique_comment,json=uniqueComment,proto3" json:"unique_comment,omitempty"`
}

func (x *ListAppointmentCardResult_PetCodeBindingsView) Reset() {
	*x = ListAppointmentCardResult_PetCodeBindingsView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentCardResult_PetCodeBindingsView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentCardResult_PetCodeBindingsView) ProtoMessage() {}

func (x *ListAppointmentCardResult_PetCodeBindingsView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentCardResult_PetCodeBindingsView.ProtoReflect.Descriptor instead.
func (*ListAppointmentCardResult_PetCodeBindingsView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{2, 8}
}

func (x *ListAppointmentCardResult_PetCodeBindingsView) GetPetCodeId() int64 {
	if x != nil {
		return x.PetCodeId
	}
	return 0
}

func (x *ListAppointmentCardResult_PetCodeBindingsView) GetAbbreviation() string {
	if x != nil {
		return x.Abbreviation
	}
	return ""
}

func (x *ListAppointmentCardResult_PetCodeBindingsView) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *ListAppointmentCardResult_PetCodeBindingsView) GetUniqueComment() string {
	if x != nil {
		return x.UniqueComment
	}
	return ""
}

// pet service view
type ListAppointmentCardResult_BoardingView_PetServiceView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet service id
	PetServiceId int64 `protobuf:"varint,1,opt,name=pet_service_id,json=petServiceId,proto3" json:"pet_service_id,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// appointment start date
	StartDate string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// appointment start time
	StartTime int32 `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// appointment end date
	EndDate string `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// appointment end time
	EndTime int32 `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// lodging unit name (if has split lodgings, this filed means the last lodging unit name)
	LodgingUnitName string `protobuf:"bytes,7,opt,name=lodging_unit_name,json=lodgingUnitName,proto3" json:"lodging_unit_name,omitempty"`
	// lodging type name (if has split lodgings, this filed means the last lodging type name)
	LodgingTypeName string `protobuf:"bytes,8,opt,name=lodging_type_name,json=lodgingTypeName,proto3" json:"lodging_type_name,omitempty"`
	// lodging type id (if has split lodgings, this filed means the last lodging type id)
	LodgingTypeId int64 `protobuf:"varint,9,opt,name=lodging_type_id,json=lodgingTypeId,proto3" json:"lodging_type_id,omitempty"`
	// split lodging list
	SplitLodgings []*v12.BoardingSplitLodgingDetailDef `protobuf:"bytes,10,rep,name=split_lodgings,json=splitLodgings,proto3" json:"split_lodgings,omitempty"`
	// lodging unit id (if has split lodgings, this filed means the last lodging unit id)
	LodgingUnitId int64 `protobuf:"varint,11,opt,name=lodging_unit_id,json=lodgingUnitId,proto3" json:"lodging_unit_id,omitempty"`
}

func (x *ListAppointmentCardResult_BoardingView_PetServiceView) Reset() {
	*x = ListAppointmentCardResult_BoardingView_PetServiceView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentCardResult_BoardingView_PetServiceView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentCardResult_BoardingView_PetServiceView) ProtoMessage() {}

func (x *ListAppointmentCardResult_BoardingView_PetServiceView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentCardResult_BoardingView_PetServiceView.ProtoReflect.Descriptor instead.
func (*ListAppointmentCardResult_BoardingView_PetServiceView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{2, 0, 0}
}

func (x *ListAppointmentCardResult_BoardingView_PetServiceView) GetPetServiceId() int64 {
	if x != nil {
		return x.PetServiceId
	}
	return 0
}

func (x *ListAppointmentCardResult_BoardingView_PetServiceView) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ListAppointmentCardResult_BoardingView_PetServiceView) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *ListAppointmentCardResult_BoardingView_PetServiceView) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ListAppointmentCardResult_BoardingView_PetServiceView) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *ListAppointmentCardResult_BoardingView_PetServiceView) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ListAppointmentCardResult_BoardingView_PetServiceView) GetLodgingUnitName() string {
	if x != nil {
		return x.LodgingUnitName
	}
	return ""
}

func (x *ListAppointmentCardResult_BoardingView_PetServiceView) GetLodgingTypeName() string {
	if x != nil {
		return x.LodgingTypeName
	}
	return ""
}

func (x *ListAppointmentCardResult_BoardingView_PetServiceView) GetLodgingTypeId() int64 {
	if x != nil {
		return x.LodgingTypeId
	}
	return 0
}

func (x *ListAppointmentCardResult_BoardingView_PetServiceView) GetSplitLodgings() []*v12.BoardingSplitLodgingDetailDef {
	if x != nil {
		return x.SplitLodgings
	}
	return nil
}

func (x *ListAppointmentCardResult_BoardingView_PetServiceView) GetLodgingUnitId() int64 {
	if x != nil {
		return x.LodgingUnitId
	}
	return 0
}

// pet service view
type ListAppointmentCardResult_DaycareView_PetServiceView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet service id
	PetServiceId int64 `protobuf:"varint,1,opt,name=pet_service_id,json=petServiceId,proto3" json:"pet_service_id,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// appointment start time
	StartTime int32 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// appointment end time
	EndTime int32 `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// lodging unit name
	LodgingUnitName string `protobuf:"bytes,5,opt,name=lodging_unit_name,json=lodgingUnitName,proto3" json:"lodging_unit_name,omitempty"`
	// lodging type name
	LodgingTypeName string `protobuf:"bytes,6,opt,name=lodging_type_name,json=lodgingTypeName,proto3" json:"lodging_type_name,omitempty"`
	// lodging type id
	LodgingTypeId int64 `protobuf:"varint,7,opt,name=lodging_type_id,json=lodgingTypeId,proto3" json:"lodging_type_id,omitempty"`
	// lodging unit id
	LodgingUnitId int64 `protobuf:"varint,8,opt,name=lodging_unit_id,json=lodgingUnitId,proto3" json:"lodging_unit_id,omitempty"`
}

func (x *ListAppointmentCardResult_DaycareView_PetServiceView) Reset() {
	*x = ListAppointmentCardResult_DaycareView_PetServiceView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentCardResult_DaycareView_PetServiceView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentCardResult_DaycareView_PetServiceView) ProtoMessage() {}

func (x *ListAppointmentCardResult_DaycareView_PetServiceView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentCardResult_DaycareView_PetServiceView.ProtoReflect.Descriptor instead.
func (*ListAppointmentCardResult_DaycareView_PetServiceView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{2, 1, 0}
}

func (x *ListAppointmentCardResult_DaycareView_PetServiceView) GetPetServiceId() int64 {
	if x != nil {
		return x.PetServiceId
	}
	return 0
}

func (x *ListAppointmentCardResult_DaycareView_PetServiceView) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ListAppointmentCardResult_DaycareView_PetServiceView) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ListAppointmentCardResult_DaycareView_PetServiceView) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ListAppointmentCardResult_DaycareView_PetServiceView) GetLodgingUnitName() string {
	if x != nil {
		return x.LodgingUnitName
	}
	return ""
}

func (x *ListAppointmentCardResult_DaycareView_PetServiceView) GetLodgingTypeName() string {
	if x != nil {
		return x.LodgingTypeName
	}
	return ""
}

func (x *ListAppointmentCardResult_DaycareView_PetServiceView) GetLodgingTypeId() int64 {
	if x != nil {
		return x.LodgingTypeId
	}
	return 0
}

func (x *ListAppointmentCardResult_DaycareView_PetServiceView) GetLodgingUnitId() int64 {
	if x != nil {
		return x.LodgingUnitId
	}
	return 0
}

// pet service view
type ListAppointmentCardResult_GroomingView_PetServiceView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet service id
	PetServiceId int64 `protobuf:"varint,1,opt,name=pet_service_id,json=petServiceId,proto3" json:"pet_service_id,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// appointment start time
	StartTime int32 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// appointment end time
	EndTime int32 `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// staff first name
	StaffFirstName string `protobuf:"bytes,5,opt,name=staff_first_name,json=staffFirstName,proto3" json:"staff_first_name,omitempty"`
	// staff last name
	StaffLastName string `protobuf:"bytes,6,opt,name=staff_last_name,json=staffLastName,proto3" json:"staff_last_name,omitempty"`
	// service type
	ServiceType v1.ServiceType `protobuf:"varint,7,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_type,omitempty"`
}

func (x *ListAppointmentCardResult_GroomingView_PetServiceView) Reset() {
	*x = ListAppointmentCardResult_GroomingView_PetServiceView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentCardResult_GroomingView_PetServiceView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentCardResult_GroomingView_PetServiceView) ProtoMessage() {}

func (x *ListAppointmentCardResult_GroomingView_PetServiceView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentCardResult_GroomingView_PetServiceView.ProtoReflect.Descriptor instead.
func (*ListAppointmentCardResult_GroomingView_PetServiceView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{2, 2, 0}
}

func (x *ListAppointmentCardResult_GroomingView_PetServiceView) GetPetServiceId() int64 {
	if x != nil {
		return x.PetServiceId
	}
	return 0
}

func (x *ListAppointmentCardResult_GroomingView_PetServiceView) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ListAppointmentCardResult_GroomingView_PetServiceView) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ListAppointmentCardResult_GroomingView_PetServiceView) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ListAppointmentCardResult_GroomingView_PetServiceView) GetStaffFirstName() string {
	if x != nil {
		return x.StaffFirstName
	}
	return ""
}

func (x *ListAppointmentCardResult_GroomingView_PetServiceView) GetStaffLastName() string {
	if x != nil {
		return x.StaffLastName
	}
	return ""
}

func (x *ListAppointmentCardResult_GroomingView_PetServiceView) GetServiceType() v1.ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return v1.ServiceType(0)
}

// pet service view
type ListAppointmentCardResult_EvaluationView_PetServiceView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet service id
	PetServiceId int64 `protobuf:"varint,1,opt,name=pet_service_id,json=petServiceId,proto3" json:"pet_service_id,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// appointment start time
	StartTime int32 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// appointment end time
	EndTime int32 `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// lodging unit name
	LodgingUnitName string `protobuf:"bytes,5,opt,name=lodging_unit_name,json=lodgingUnitName,proto3" json:"lodging_unit_name,omitempty"`
	// lodging type name
	LodgingTypeName string `protobuf:"bytes,6,opt,name=lodging_type_name,json=lodgingTypeName,proto3" json:"lodging_type_name,omitempty"`
	// lodging type id
	LodgingTypeId int64 `protobuf:"varint,7,opt,name=lodging_type_id,json=lodgingTypeId,proto3" json:"lodging_type_id,omitempty"`
	// lodging unit id
	LodgingUnitId int64 `protobuf:"varint,8,opt,name=lodging_unit_id,json=lodgingUnitId,proto3" json:"lodging_unit_id,omitempty"`
}

func (x *ListAppointmentCardResult_EvaluationView_PetServiceView) Reset() {
	*x = ListAppointmentCardResult_EvaluationView_PetServiceView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentCardResult_EvaluationView_PetServiceView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentCardResult_EvaluationView_PetServiceView) ProtoMessage() {}

func (x *ListAppointmentCardResult_EvaluationView_PetServiceView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentCardResult_EvaluationView_PetServiceView.ProtoReflect.Descriptor instead.
func (*ListAppointmentCardResult_EvaluationView_PetServiceView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{2, 3, 0}
}

func (x *ListAppointmentCardResult_EvaluationView_PetServiceView) GetPetServiceId() int64 {
	if x != nil {
		return x.PetServiceId
	}
	return 0
}

func (x *ListAppointmentCardResult_EvaluationView_PetServiceView) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ListAppointmentCardResult_EvaluationView_PetServiceView) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ListAppointmentCardResult_EvaluationView_PetServiceView) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ListAppointmentCardResult_EvaluationView_PetServiceView) GetLodgingUnitName() string {
	if x != nil {
		return x.LodgingUnitName
	}
	return ""
}

func (x *ListAppointmentCardResult_EvaluationView_PetServiceView) GetLodgingTypeName() string {
	if x != nil {
		return x.LodgingTypeName
	}
	return ""
}

func (x *ListAppointmentCardResult_EvaluationView_PetServiceView) GetLodgingTypeId() int64 {
	if x != nil {
		return x.LodgingTypeId
	}
	return 0
}

func (x *ListAppointmentCardResult_EvaluationView_PetServiceView) GetLodgingUnitId() int64 {
	if x != nil {
		return x.LodgingUnitId
	}
	return 0
}

// pet service view
type ListAppointmentCardResult_DogWalkingView_PetServiceView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet service id
	PetServiceId int64 `protobuf:"varint,1,opt,name=pet_service_id,json=petServiceId,proto3" json:"pet_service_id,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// appointment start time
	StartTime int32 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// appointment end time
	EndTime int32 `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// staff first name
	StaffFirstName string `protobuf:"bytes,5,opt,name=staff_first_name,json=staffFirstName,proto3" json:"staff_first_name,omitempty"`
	// staff last name
	StaffLastName string `protobuf:"bytes,6,opt,name=staff_last_name,json=staffLastName,proto3" json:"staff_last_name,omitempty"`
}

func (x *ListAppointmentCardResult_DogWalkingView_PetServiceView) Reset() {
	*x = ListAppointmentCardResult_DogWalkingView_PetServiceView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentCardResult_DogWalkingView_PetServiceView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentCardResult_DogWalkingView_PetServiceView) ProtoMessage() {}

func (x *ListAppointmentCardResult_DogWalkingView_PetServiceView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentCardResult_DogWalkingView_PetServiceView.ProtoReflect.Descriptor instead.
func (*ListAppointmentCardResult_DogWalkingView_PetServiceView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{2, 4, 0}
}

func (x *ListAppointmentCardResult_DogWalkingView_PetServiceView) GetPetServiceId() int64 {
	if x != nil {
		return x.PetServiceId
	}
	return 0
}

func (x *ListAppointmentCardResult_DogWalkingView_PetServiceView) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ListAppointmentCardResult_DogWalkingView_PetServiceView) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ListAppointmentCardResult_DogWalkingView_PetServiceView) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ListAppointmentCardResult_DogWalkingView_PetServiceView) GetStaffFirstName() string {
	if x != nil {
		return x.StaffFirstName
	}
	return ""
}

func (x *ListAppointmentCardResult_DogWalkingView_PetServiceView) GetStaffLastName() string {
	if x != nil {
		return x.StaffLastName
	}
	return ""
}

// The add-on group
type ListDailyTasksResult_AddOnGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The add-on id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The add-on name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// The list of add-on tasks
	Tasks []*ListDailyTasksResult_TaskRow `protobuf:"bytes,3,rep,name=tasks,proto3" json:"tasks,omitempty"`
}

func (x *ListDailyTasksResult_AddOnGroup) Reset() {
	*x = ListDailyTasksResult_AddOnGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyTasksResult_AddOnGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyTasksResult_AddOnGroup) ProtoMessage() {}

func (x *ListDailyTasksResult_AddOnGroup) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyTasksResult_AddOnGroup.ProtoReflect.Descriptor instead.
func (*ListDailyTasksResult_AddOnGroup) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ListDailyTasksResult_AddOnGroup) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListDailyTasksResult_AddOnGroup) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListDailyTasksResult_AddOnGroup) GetTasks() []*ListDailyTasksResult_TaskRow {
	if x != nil {
		return x.Tasks
	}
	return nil
}

// The task row
type ListDailyTasksResult_TaskRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The task type
	Category v12.AppointmentTaskCategory `protobuf:"varint,1,opt,name=category,proto3,enum=moego.models.appointment.v1.AppointmentTaskCategory" json:"category,omitempty"`
	// The pet column
	Pet *ListDailyTasksResult_PetColumn `protobuf:"bytes,2,opt,name=pet,proto3" json:"pet,omitempty"`
	// The service column
	Service *ListDailyTasksResult_ServiceColumn `protobuf:"bytes,3,opt,name=service,proto3" json:"service,omitempty"`
	// The feeding instruction
	Instruction string `protobuf:"bytes,4,opt,name=instruction,proto3" json:"instruction,omitempty"`
	// The feeding time
	Time *int32 `protobuf:"varint,5,opt,name=time,proto3,oneof" json:"time,omitempty"`
	// The assign staff
	Staff *ListDailyTasksResult_StaffColumn `protobuf:"bytes,6,opt,name=staff,proto3" json:"staff,omitempty"`
}

func (x *ListDailyTasksResult_TaskRow) Reset() {
	*x = ListDailyTasksResult_TaskRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyTasksResult_TaskRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyTasksResult_TaskRow) ProtoMessage() {}

func (x *ListDailyTasksResult_TaskRow) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyTasksResult_TaskRow.ProtoReflect.Descriptor instead.
func (*ListDailyTasksResult_TaskRow) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{4, 1}
}

func (x *ListDailyTasksResult_TaskRow) GetCategory() v12.AppointmentTaskCategory {
	if x != nil {
		return x.Category
	}
	return v12.AppointmentTaskCategory(0)
}

func (x *ListDailyTasksResult_TaskRow) GetPet() *ListDailyTasksResult_PetColumn {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *ListDailyTasksResult_TaskRow) GetService() *ListDailyTasksResult_ServiceColumn {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *ListDailyTasksResult_TaskRow) GetInstruction() string {
	if x != nil {
		return x.Instruction
	}
	return ""
}

func (x *ListDailyTasksResult_TaskRow) GetTime() int32 {
	if x != nil && x.Time != nil {
		return *x.Time
	}
	return 0
}

func (x *ListDailyTasksResult_TaskRow) GetStaff() *ListDailyTasksResult_StaffColumn {
	if x != nil {
		return x.Staff
	}
	return nil
}

// The pet column
type ListDailyTasksResult_PetColumn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The pet id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The pet name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// The pet type id
	PetType v11.PetType `protobuf:"varint,3,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// The breed
	Breed string `protobuf:"bytes,4,opt,name=breed,proto3" json:"breed,omitempty"`
	// The gender
	Gender v11.PetGender `protobuf:"varint,5,opt,name=gender,proto3,enum=moego.models.customer.v1.PetGender" json:"gender,omitempty"`
	// The avatar path
	AvatarPath string `protobuf:"bytes,6,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// The weight
	Weight string `protobuf:"bytes,7,opt,name=weight,proto3" json:"weight,omitempty"`
}

func (x *ListDailyTasksResult_PetColumn) Reset() {
	*x = ListDailyTasksResult_PetColumn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyTasksResult_PetColumn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyTasksResult_PetColumn) ProtoMessage() {}

func (x *ListDailyTasksResult_PetColumn) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyTasksResult_PetColumn.ProtoReflect.Descriptor instead.
func (*ListDailyTasksResult_PetColumn) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{4, 2}
}

func (x *ListDailyTasksResult_PetColumn) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListDailyTasksResult_PetColumn) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListDailyTasksResult_PetColumn) GetPetType() v11.PetType {
	if x != nil {
		return x.PetType
	}
	return v11.PetType(0)
}

func (x *ListDailyTasksResult_PetColumn) GetBreed() string {
	if x != nil {
		return x.Breed
	}
	return ""
}

func (x *ListDailyTasksResult_PetColumn) GetGender() v11.PetGender {
	if x != nil {
		return x.Gender
	}
	return v11.PetGender(0)
}

func (x *ListDailyTasksResult_PetColumn) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *ListDailyTasksResult_PetColumn) GetWeight() string {
	if x != nil {
		return x.Weight
	}
	return ""
}

// The service column
type ListDailyTasksResult_ServiceColumn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The service id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The service name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// The service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,3,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
}

func (x *ListDailyTasksResult_ServiceColumn) Reset() {
	*x = ListDailyTasksResult_ServiceColumn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyTasksResult_ServiceColumn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyTasksResult_ServiceColumn) ProtoMessage() {}

func (x *ListDailyTasksResult_ServiceColumn) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyTasksResult_ServiceColumn.ProtoReflect.Descriptor instead.
func (*ListDailyTasksResult_ServiceColumn) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{4, 3}
}

func (x *ListDailyTasksResult_ServiceColumn) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListDailyTasksResult_ServiceColumn) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListDailyTasksResult_ServiceColumn) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

// The staff column
type ListDailyTasksResult_StaffColumn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The staff id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The first name
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// The last name
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
}

func (x *ListDailyTasksResult_StaffColumn) Reset() {
	*x = ListDailyTasksResult_StaffColumn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyTasksResult_StaffColumn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyTasksResult_StaffColumn) ProtoMessage() {}

func (x *ListDailyTasksResult_StaffColumn) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyTasksResult_StaffColumn.ProtoReflect.Descriptor instead.
func (*ListDailyTasksResult_StaffColumn) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{4, 4}
}

func (x *ListDailyTasksResult_StaffColumn) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListDailyTasksResult_StaffColumn) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *ListDailyTasksResult_StaffColumn) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

// boarding view
type ListBoardingDepartureCardResult_BoardingView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet service list
	PetServices []*ListAppointmentCardResult_BoardingView_PetServiceView `protobuf:"bytes,3,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
	// Pet belongings
	PetBelongings []*ListBoardingDepartureCardResult_BoardingView_PetBelonging `protobuf:"bytes,4,rep,name=pet_belongings,json=petBelongings,proto3" json:"pet_belongings,omitempty"`
}

func (x *ListBoardingDepartureCardResult_BoardingView) Reset() {
	*x = ListBoardingDepartureCardResult_BoardingView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBoardingDepartureCardResult_BoardingView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBoardingDepartureCardResult_BoardingView) ProtoMessage() {}

func (x *ListBoardingDepartureCardResult_BoardingView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBoardingDepartureCardResult_BoardingView.ProtoReflect.Descriptor instead.
func (*ListBoardingDepartureCardResult_BoardingView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{6, 0}
}

func (x *ListBoardingDepartureCardResult_BoardingView) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ListBoardingDepartureCardResult_BoardingView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListBoardingDepartureCardResult_BoardingView) GetPetServices() []*ListAppointmentCardResult_BoardingView_PetServiceView {
	if x != nil {
		return x.PetServices
	}
	return nil
}

func (x *ListBoardingDepartureCardResult_BoardingView) GetPetBelongings() []*ListBoardingDepartureCardResult_BoardingView_PetBelonging {
	if x != nil {
		return x.PetBelongings
	}
	return nil
}

// Pet belonging
type ListBoardingDepartureCardResult_BoardingView_PetBelonging struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// pet area
	Area *string `protobuf:"bytes,2,opt,name=area,proto3,oneof" json:"area,omitempty"`
}

func (x *ListBoardingDepartureCardResult_BoardingView_PetBelonging) Reset() {
	*x = ListBoardingDepartureCardResult_BoardingView_PetBelonging{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBoardingDepartureCardResult_BoardingView_PetBelonging) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBoardingDepartureCardResult_BoardingView_PetBelonging) ProtoMessage() {}

func (x *ListBoardingDepartureCardResult_BoardingView_PetBelonging) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBoardingDepartureCardResult_BoardingView_PetBelonging.ProtoReflect.Descriptor instead.
func (*ListBoardingDepartureCardResult_BoardingView_PetBelonging) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{6, 0, 0}
}

func (x *ListBoardingDepartureCardResult_BoardingView_PetBelonging) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListBoardingDepartureCardResult_BoardingView_PetBelonging) GetArea() string {
	if x != nil && x.Area != nil {
		return *x.Area
	}
	return ""
}

// playgroup view
type ListDailyPlaygroupCardResult_PlaygroupView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// playgroup id
	PlaygroupId int64 `protobuf:"varint,1,opt,name=playgroup_id,json=playgroupId,proto3" json:"playgroup_id,omitempty"`
	// pet number
	PetNumber int32 `protobuf:"varint,2,opt,name=pet_number,json=petNumber,proto3" json:"pet_number,omitempty"`
	// pet list
	PetPlaygroups []*ListDailyPlaygroupCardResult_PetPlaygroupView `protobuf:"bytes,3,rep,name=pet_playgroups,json=petPlaygroups,proto3" json:"pet_playgroups,omitempty"`
}

func (x *ListDailyPlaygroupCardResult_PlaygroupView) Reset() {
	*x = ListDailyPlaygroupCardResult_PlaygroupView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyPlaygroupCardResult_PlaygroupView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyPlaygroupCardResult_PlaygroupView) ProtoMessage() {}

func (x *ListDailyPlaygroupCardResult_PlaygroupView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyPlaygroupCardResult_PlaygroupView.ProtoReflect.Descriptor instead.
func (*ListDailyPlaygroupCardResult_PlaygroupView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ListDailyPlaygroupCardResult_PlaygroupView) GetPlaygroupId() int64 {
	if x != nil {
		return x.PlaygroupId
	}
	return 0
}

func (x *ListDailyPlaygroupCardResult_PlaygroupView) GetPetNumber() int32 {
	if x != nil {
		return x.PetNumber
	}
	return 0
}

func (x *ListDailyPlaygroupCardResult_PlaygroupView) GetPetPlaygroups() []*ListDailyPlaygroupCardResult_PetPlaygroupView {
	if x != nil {
		return x.PetPlaygroups
	}
	return nil
}

// pet playgroup view
type ListDailyPlaygroupCardResult_PetPlaygroupView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet playgroup id
	PetPlaygroupId int64 `protobuf:"varint,1,opt,name=pet_playgroup_id,json=petPlaygroupId,proto3" json:"pet_playgroup_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet playgroup list sort. start with 1 and put the smallest first
	Sort int32 `protobuf:"varint,3,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *ListDailyPlaygroupCardResult_PetPlaygroupView) Reset() {
	*x = ListDailyPlaygroupCardResult_PetPlaygroupView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyPlaygroupCardResult_PetPlaygroupView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyPlaygroupCardResult_PetPlaygroupView) ProtoMessage() {}

func (x *ListDailyPlaygroupCardResult_PetPlaygroupView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyPlaygroupCardResult_PetPlaygroupView.ProtoReflect.Descriptor instead.
func (*ListDailyPlaygroupCardResult_PetPlaygroupView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{8, 1}
}

func (x *ListDailyPlaygroupCardResult_PetPlaygroupView) GetPetPlaygroupId() int64 {
	if x != nil {
		return x.PetPlaygroupId
	}
	return 0
}

func (x *ListDailyPlaygroupCardResult_PetPlaygroupView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListDailyPlaygroupCardResult_PetPlaygroupView) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

// pet view
type ListDailyPlaygroupCardResult_PetView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet name
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// pet type
	PetType v11.PetType `protobuf:"varint,4,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// pet playgroup color
	PetPlaygroupColor string `protobuf:"bytes,5,opt,name=pet_playgroup_color,json=petPlaygroupColor,proto3" json:"pet_playgroup_color,omitempty"`
	// customer
	Customer *ListDailyPlaygroupCardResult_CustomerView `protobuf:"bytes,6,opt,name=customer,proto3" json:"customer,omitempty"`
	// pet codes
	PetCodeBindings []*ListDailyPlaygroupCardResult_PetCodeBindingsView `protobuf:"bytes,7,rep,name=pet_code_bindings,json=petCodeBindings,proto3" json:"pet_code_bindings,omitempty"`
	// pet breed name
	PetBreed string `protobuf:"bytes,8,opt,name=pet_breed,json=petBreed,proto3" json:"pet_breed,omitempty"`
}

func (x *ListDailyPlaygroupCardResult_PetView) Reset() {
	*x = ListDailyPlaygroupCardResult_PetView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyPlaygroupCardResult_PetView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyPlaygroupCardResult_PetView) ProtoMessage() {}

func (x *ListDailyPlaygroupCardResult_PetView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyPlaygroupCardResult_PetView.ProtoReflect.Descriptor instead.
func (*ListDailyPlaygroupCardResult_PetView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{8, 2}
}

func (x *ListDailyPlaygroupCardResult_PetView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListDailyPlaygroupCardResult_PetView) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *ListDailyPlaygroupCardResult_PetView) GetPetType() v11.PetType {
	if x != nil {
		return x.PetType
	}
	return v11.PetType(0)
}

func (x *ListDailyPlaygroupCardResult_PetView) GetPetPlaygroupColor() string {
	if x != nil {
		return x.PetPlaygroupColor
	}
	return ""
}

func (x *ListDailyPlaygroupCardResult_PetView) GetCustomer() *ListDailyPlaygroupCardResult_CustomerView {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *ListDailyPlaygroupCardResult_PetView) GetPetCodeBindings() []*ListDailyPlaygroupCardResult_PetCodeBindingsView {
	if x != nil {
		return x.PetCodeBindings
	}
	return nil
}

func (x *ListDailyPlaygroupCardResult_PetView) GetPetBreed() string {
	if x != nil {
		return x.PetBreed
	}
	return ""
}

// customer view
type ListDailyPlaygroupCardResult_CustomerView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
}

func (x *ListDailyPlaygroupCardResult_CustomerView) Reset() {
	*x = ListDailyPlaygroupCardResult_CustomerView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyPlaygroupCardResult_CustomerView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyPlaygroupCardResult_CustomerView) ProtoMessage() {}

func (x *ListDailyPlaygroupCardResult_CustomerView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyPlaygroupCardResult_CustomerView.ProtoReflect.Descriptor instead.
func (*ListDailyPlaygroupCardResult_CustomerView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{8, 3}
}

func (x *ListDailyPlaygroupCardResult_CustomerView) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ListDailyPlaygroupCardResult_CustomerView) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *ListDailyPlaygroupCardResult_CustomerView) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

// pet code bindings view
type ListDailyPlaygroupCardResult_PetCodeBindingsView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code abbreviation
	Abbreviation string `protobuf:"bytes,1,opt,name=abbreviation,proto3" json:"abbreviation,omitempty"`
	// pet code color
	Color string `protobuf:"bytes,2,opt,name=color,proto3" json:"color,omitempty"`
	// pet code unique comment
	UniqueComment string `protobuf:"bytes,3,opt,name=unique_comment,json=uniqueComment,proto3" json:"unique_comment,omitempty"`
}

func (x *ListDailyPlaygroupCardResult_PetCodeBindingsView) Reset() {
	*x = ListDailyPlaygroupCardResult_PetCodeBindingsView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyPlaygroupCardResult_PetCodeBindingsView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyPlaygroupCardResult_PetCodeBindingsView) ProtoMessage() {}

func (x *ListDailyPlaygroupCardResult_PetCodeBindingsView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyPlaygroupCardResult_PetCodeBindingsView.ProtoReflect.Descriptor instead.
func (*ListDailyPlaygroupCardResult_PetCodeBindingsView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{8, 4}
}

func (x *ListDailyPlaygroupCardResult_PetCodeBindingsView) GetAbbreviation() string {
	if x != nil {
		return x.Abbreviation
	}
	return ""
}

func (x *ListDailyPlaygroupCardResult_PetCodeBindingsView) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *ListDailyPlaygroupCardResult_PetCodeBindingsView) GetUniqueComment() string {
	if x != nil {
		return x.UniqueComment
	}
	return ""
}

// boarding view
type ListBoardingArrivalCardResult_BoardingView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet service list
	PetServices []*ListAppointmentCardResult_BoardingView_PetServiceView `protobuf:"bytes,3,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
	// Pet belongings
	PetBelongings []*ListBoardingArrivalCardResult_BoardingView_PetBelonging `protobuf:"bytes,4,rep,name=pet_belongings,json=petBelongings,proto3" json:"pet_belongings,omitempty"`
}

func (x *ListBoardingArrivalCardResult_BoardingView) Reset() {
	*x = ListBoardingArrivalCardResult_BoardingView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBoardingArrivalCardResult_BoardingView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBoardingArrivalCardResult_BoardingView) ProtoMessage() {}

func (x *ListBoardingArrivalCardResult_BoardingView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBoardingArrivalCardResult_BoardingView.ProtoReflect.Descriptor instead.
func (*ListBoardingArrivalCardResult_BoardingView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{10, 0}
}

func (x *ListBoardingArrivalCardResult_BoardingView) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ListBoardingArrivalCardResult_BoardingView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListBoardingArrivalCardResult_BoardingView) GetPetServices() []*ListAppointmentCardResult_BoardingView_PetServiceView {
	if x != nil {
		return x.PetServices
	}
	return nil
}

func (x *ListBoardingArrivalCardResult_BoardingView) GetPetBelongings() []*ListBoardingArrivalCardResult_BoardingView_PetBelonging {
	if x != nil {
		return x.PetBelongings
	}
	return nil
}

// Pet belonging
type ListBoardingArrivalCardResult_BoardingView_PetBelonging struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// pet area
	Area *string `protobuf:"bytes,2,opt,name=area,proto3,oneof" json:"area,omitempty"`
}

func (x *ListBoardingArrivalCardResult_BoardingView_PetBelonging) Reset() {
	*x = ListBoardingArrivalCardResult_BoardingView_PetBelonging{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBoardingArrivalCardResult_BoardingView_PetBelonging) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBoardingArrivalCardResult_BoardingView_PetBelonging) ProtoMessage() {}

func (x *ListBoardingArrivalCardResult_BoardingView_PetBelonging) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_print_card_api_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBoardingArrivalCardResult_BoardingView_PetBelonging.ProtoReflect.Descriptor instead.
func (*ListBoardingArrivalCardResult_BoardingView_PetBelonging) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP(), []int{10, 0, 0}
}

func (x *ListBoardingArrivalCardResult_BoardingView_PetBelonging) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListBoardingArrivalCardResult_BoardingView_PetBelonging) GetArea() string {
	if x != nil && x.Area != nil {
		return *x.Area
	}
	return ""
}

var File_moego_api_appointment_v1_print_card_api_proto protoreflect.FileDescriptor

var file_moego_api_appointment_v1_print_card_api_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x69, 0x6e, 0x74,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3d, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f,
	0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f,
	0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x79,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xeb, 0x02, 0x0a, 0x19, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x54, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x13, 0x18, 0x01, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x18, 0x01, 0x22, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x39, 0x0a,
	0x17, 0x69, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64,
	0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00,
	0x52, 0x13, 0x69, 0x73, 0x50, 0x65, 0x74, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x49,
	0x6e, 0x4f, 0x6e, 0x6c, 0x79, 0x88, 0x01, 0x01, 0x12, 0x50, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x69,
	0x73, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69,
	0x6e, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x22, 0xc9, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x69,
	0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x2f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x73, 0x22, 0x93, 0x24, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x5e, 0x0a, 0x09, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x56, 0x69, 0x65, 0x77, 0x52, 0x09, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x5b, 0x0a, 0x08, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x08, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x73, 0x12, 0x5e, 0x0a,
	0x09, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x64, 0x0a,
	0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x65, 0x0a, 0x0c, 0x64, 0x6f, 0x67, 0x5f, 0x77, 0x61, 0x6c, 0x6b, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x44,
	0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x64,
	0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x4f, 0x0a, 0x04, 0x70, 0x65,
	0x74, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65,
	0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x12, 0x67, 0x0a, 0x0c, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x6b, 0x0a, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x44, 0x61, 0x79, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x0c, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x4f, 0x0a, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69,
	0x74, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69,
	0x74, 0x73, 0x1a, 0x9b, 0x05, 0x0a, 0x0c, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x56,
	0x69, 0x65, 0x77, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x72, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x42, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x1a, 0xd8, 0x03, 0x0a, 0x0e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x65, 0x74, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a,
	0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x61, 0x0a, 0x0e, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x52, 0x0d, 0x73, 0x70, 0x6c, 0x69, 0x74,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x64,
	0x1a, 0xfc, 0x03, 0x0a, 0x0b, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x71,
	0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72,
	0x65, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x1a, 0xbb, 0x02, 0x0a, 0x0e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x70, 0x65,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x1a,
	0xf2, 0x03, 0x0a, 0x0c, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x72,
	0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x1a, 0xaf, 0x02, 0x0a, 0x0e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x70,
	0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x46, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x4c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x48, 0x0a, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x1a, 0x82, 0x04, 0x0a, 0x0e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x74, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x2e,
	0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b,
	0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x1a, 0xbb, 0x02, 0x0a, 0x0e,
	0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x24,
	0x0a, 0x0e, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a,
	0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x49,
	0x64, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x1a, 0xac, 0x03, 0x0a, 0x0e, 0x44, 0x6f,
	0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x74, 0x0a, 0x0c, 0x70, 0x65,
	0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x51, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67,
	0x56, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x1a, 0xe5, 0x01, 0x0a, 0x0e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56,
	0x69, 0x65, 0x77, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x70, 0x65, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x46, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x26, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x4c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x9d, 0x05, 0x0a, 0x07, 0x50, 0x65, 0x74,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x3b, 0x0a, 0x06, 0x67, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x12, 0x3c, 0x0a, 0x08,
	0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x07, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65,
	0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65,
	0x73, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x65, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x65, 0x61, 0x72, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x70, 0x65, 0x74, 0x41, 0x70, 0x70, 0x65, 0x61, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x6f,
	0x6c, 0x6f, 0x72, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x65, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x65, 0x61,
	0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x70, 0x65, 0x74, 0x41, 0x70, 0x70, 0x65, 0x61, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x4e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x65, 0x74, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x28, 0x0a, 0x10, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x46, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4c, 0x61, 0x73, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x73, 0x0a, 0x11, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f,
	0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x73, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0f, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x1a, 0x51, 0x0a, 0x0f, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x1a, 0xc1, 0x01, 0x0a, 0x12,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x44, 0x61, 0x79, 0x56, 0x69,
	0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x61, 0x78,
	0x50, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x29, 0x0a, 0x11, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x50, 0x65, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4e, 0x75,
	0x6d, 0x12, 0x28, 0x0a, 0x10, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x69, 0x65, 0x64, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x6f, 0x63, 0x63,
	0x75, 0x70, 0x69, 0x65, 0x64, 0x50, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x1a,
	0x96, 0x01, 0x0a, 0x13, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x73, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1e, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x65,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x62, 0x62, 0x72, 0x65,
	0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61,
	0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x25, 0x0a, 0x0e, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x75, 0x6e, 0x69, 0x71, 0x75,
	0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xd3, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73,
	0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x6a, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01,
	0x0b, 0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x10, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x94,
	0x0a, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x52, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x64, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61,
	0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x6f,
	0x77, 0x52, 0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x58, 0x0a, 0x0b, 0x6d,
	0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x6f, 0x77, 0x52, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x52, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x06, 0x61, 0x64, 0x64, 0x4f, 0x6e, 0x73, 0x1a, 0x7e, 0x0a, 0x0a, 0x41, 0x64, 0x64,
	0x4f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x4c, 0x0a, 0x05, 0x74,
	0x61, 0x73, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54,
	0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x6f, 0x77, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x1a, 0x95, 0x03, 0x0a, 0x07, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x6f, 0x77, 0x12, 0x50, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x4a, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x52, 0x03,
	0x70, 0x65, 0x74, 0x12, 0x56, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6c, 0x75,
	0x6d, 0x6e, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x69,
	0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a,
	0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x50, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x6f, 0x6c, 0x75, 0x6d,
	0x6e, 0x52, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x1a, 0xf9, 0x01, 0x0a, 0x09, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x1a, 0x8a, 0x01,
	0x0a, 0x0d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x59, 0x0a, 0x0b, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xd3, 0x01, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x75, 0x72, 0x65, 0x43,
	0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x17, 0x69, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x13, 0x69, 0x73, 0x50, 0x65, 0x74, 0x73, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x4f, 0x6e, 0x6c, 0x79, 0x88, 0x01, 0x01, 0x42,
	0x1a, 0x0a, 0x18, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x22, 0x84, 0x07, 0x0a, 0x1f,
	0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x64, 0x0a, 0x09, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x42, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x52, 0x09, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x4f, 0x0a, 0x04, 0x70, 0x65, 0x74, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x12, 0x67, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x6b, 0x0a, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x44, 0x61, 0x79, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x4f, 0x0a, 0x0d,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x0e, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x0c, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x1a, 0x82, 0x03,
	0x0a, 0x0c, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x72, 0x0a, 0x0c,
	0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x56, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x12, 0x7a, 0x0a, 0x0e, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x6e,
	0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x53, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77,
	0x2e, 0x50, 0x65, 0x74, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x67, 0x52, 0x0d, 0x70,
	0x65, 0x74, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x1a, 0x44, 0x0a, 0x0c,
	0x50, 0x65, 0x74, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x17, 0x0a, 0x04, 0x61, 0x72, 0x65, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x04, 0x61, 0x72, 0x65, 0x61, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x61, 0x72,
	0x65, 0x61, 0x22, 0xc6, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79,
	0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x25, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x17, 0x69, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x13, 0x69, 0x73, 0x50, 0x65, 0x74, 0x73, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x4f, 0x6e, 0x6c, 0x79, 0x88, 0x01, 0x01, 0x42,
	0x1a, 0x0a, 0x18, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x22, 0xdf, 0x09, 0x0a, 0x1c,
	0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x48, 0x0a, 0x0a,
	0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x79,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x79,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x6d, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44,
	0x61, 0x69, 0x6c, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0e, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x56, 0x69, 0x65, 0x77, 0x73, 0x12, 0x52, 0x0a, 0x04, 0x70, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x1a, 0xc1, 0x01, 0x0a, 0x0d, 0x50, 0x6c,
	0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x69, 0x65, 0x77, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x70, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x6e, 0x0a,
	0x0e, 0x70, 0x65, 0x74, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65,
	0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0d,
	0x70, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x1a, 0x67, 0x0a,
	0x10, 0x50, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x65, 0x74, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x70, 0x65, 0x74,
	0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x1a, 0x9f, 0x03, 0x0a, 0x07, 0x50, 0x65, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x65, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x70, 0x65, 0x74, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x70, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x12, 0x5f, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x12, 0x76, 0x0a, 0x11, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f,
	0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61,
	0x69, 0x6c, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0f, 0x70, 0x65, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x1a, 0x6b, 0x0a, 0x0c, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x76, 0x0a, 0x13, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x56, 0x69, 0x65, 0x77, 0x12, 0x22, 0x0a, 0x0c,
	0x61, 0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65,
	0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xd1, 0x01,
	0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x72,
	0x72, 0x69, 0x76, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x17, 0x69, 0x73,
	0x5f, 0x70, 0x65, 0x74, 0x73, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e,
	0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x13, 0x69,
	0x73, 0x50, 0x65, 0x74, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x4f, 0x6e,
	0x6c, 0x79, 0x88, 0x01, 0x01, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x65, 0x74,
	0x73, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x6e, 0x6c,
	0x79, 0x22, 0xfe, 0x06, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x62, 0x0a, 0x09, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x72,
	0x72, 0x69, 0x76, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e,
	0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x52, 0x09, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x4f, 0x0a, 0x04, 0x70, 0x65, 0x74, 0x73, 0x18,
	0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x12, 0x67, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x6b, 0x0a, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x4c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x44, 0x61, 0x79, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x0c, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x4f,
	0x0a, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18,
	0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x0c, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x1a,
	0x80, 0x03, 0x0a, 0x0c, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x72,
	0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x12, 0x78, 0x0a, 0x0e, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x65, 0x6c, 0x6f, 0x6e, 0x67,
	0x69, 0x6e, 0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77,
	0x2e, 0x50, 0x65, 0x74, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x67, 0x52, 0x0d, 0x70,
	0x65, 0x74, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x1a, 0x44, 0x0a, 0x0c,
	0x50, 0x65, 0x74, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x17, 0x0a, 0x04, 0x61, 0x72, 0x65, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x04, 0x61, 0x72, 0x65, 0x61, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x61, 0x72,
	0x65, 0x61, 0x32, 0xb2, 0x05, 0x0a, 0x10, 0x50, 0x72, 0x69, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7f, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x12, 0x33,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x70, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x61, 0x69, 0x6c, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54,
	0x61, 0x73, 0x6b, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x54,
	0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x91, 0x01, 0x0a, 0x19, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x70, 0x61, 0x72,
	0x74, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x88,
	0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x50, 0x6c, 0x61, 0x79,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x61, 0x72, 0x64, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x50, 0x6c,
	0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8b, 0x01, 0x0a, 0x17, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x43, 0x61, 0x72, 0x64, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x72, 0x72,
	0x69, 0x76, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x84, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_appointment_v1_print_card_api_proto_rawDescOnce sync.Once
	file_moego_api_appointment_v1_print_card_api_proto_rawDescData = file_moego_api_appointment_v1_print_card_api_proto_rawDesc
)

func file_moego_api_appointment_v1_print_card_api_proto_rawDescGZIP() []byte {
	file_moego_api_appointment_v1_print_card_api_proto_rawDescOnce.Do(func() {
		file_moego_api_appointment_v1_print_card_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_appointment_v1_print_card_api_proto_rawDescData)
	})
	return file_moego_api_appointment_v1_print_card_api_proto_rawDescData
}

var file_moego_api_appointment_v1_print_card_api_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_moego_api_appointment_v1_print_card_api_proto_goTypes = []interface{}{
	(*ListAppointmentCardParams)(nil),                                 // 0: moego.api.appointment.v1.ListAppointmentCardParams
	(*ServiceFilter)(nil),                                             // 1: moego.api.appointment.v1.ServiceFilter
	(*ListAppointmentCardResult)(nil),                                 // 2: moego.api.appointment.v1.ListAppointmentCardResult
	(*ListDailyTasksParams)(nil),                                      // 3: moego.api.appointment.v1.ListDailyTasksParams
	(*ListDailyTasksResult)(nil),                                      // 4: moego.api.appointment.v1.ListDailyTasksResult
	(*ListBoardingDepartureCardParams)(nil),                           // 5: moego.api.appointment.v1.ListBoardingDepartureCardParams
	(*ListBoardingDepartureCardResult)(nil),                           // 6: moego.api.appointment.v1.ListBoardingDepartureCardResult
	(*ListDailyPlaygroupCardParams)(nil),                              // 7: moego.api.appointment.v1.ListDailyPlaygroupCardParams
	(*ListDailyPlaygroupCardResult)(nil),                              // 8: moego.api.appointment.v1.ListDailyPlaygroupCardResult
	(*ListBoardingArrivalCardParams)(nil),                             // 9: moego.api.appointment.v1.ListBoardingArrivalCardParams
	(*ListBoardingArrivalCardResult)(nil),                             // 10: moego.api.appointment.v1.ListBoardingArrivalCardResult
	(*ListAppointmentCardResult_BoardingView)(nil),                    // 11: moego.api.appointment.v1.ListAppointmentCardResult.BoardingView
	(*ListAppointmentCardResult_DaycareView)(nil),                     // 12: moego.api.appointment.v1.ListAppointmentCardResult.DaycareView
	(*ListAppointmentCardResult_GroomingView)(nil),                    // 13: moego.api.appointment.v1.ListAppointmentCardResult.GroomingView
	(*ListAppointmentCardResult_EvaluationView)(nil),                  // 14: moego.api.appointment.v1.ListAppointmentCardResult.EvaluationView
	(*ListAppointmentCardResult_DogWalkingView)(nil),                  // 15: moego.api.appointment.v1.ListAppointmentCardResult.DogWalkingView
	(*ListAppointmentCardResult_PetView)(nil),                         // 16: moego.api.appointment.v1.ListAppointmentCardResult.PetView
	(*ListAppointmentCardResult_AppointmentView)(nil),                 // 17: moego.api.appointment.v1.ListAppointmentCardResult.AppointmentView
	(*ListAppointmentCardResult_LodgingTypeDayView)(nil),              // 18: moego.api.appointment.v1.ListAppointmentCardResult.LodgingTypeDayView
	(*ListAppointmentCardResult_PetCodeBindingsView)(nil),             // 19: moego.api.appointment.v1.ListAppointmentCardResult.PetCodeBindingsView
	(*ListAppointmentCardResult_BoardingView_PetServiceView)(nil),     // 20: moego.api.appointment.v1.ListAppointmentCardResult.BoardingView.PetServiceView
	(*ListAppointmentCardResult_DaycareView_PetServiceView)(nil),      // 21: moego.api.appointment.v1.ListAppointmentCardResult.DaycareView.PetServiceView
	(*ListAppointmentCardResult_GroomingView_PetServiceView)(nil),     // 22: moego.api.appointment.v1.ListAppointmentCardResult.GroomingView.PetServiceView
	(*ListAppointmentCardResult_EvaluationView_PetServiceView)(nil),   // 23: moego.api.appointment.v1.ListAppointmentCardResult.EvaluationView.PetServiceView
	(*ListAppointmentCardResult_DogWalkingView_PetServiceView)(nil),   // 24: moego.api.appointment.v1.ListAppointmentCardResult.DogWalkingView.PetServiceView
	(*ListDailyTasksResult_AddOnGroup)(nil),                           // 25: moego.api.appointment.v1.ListDailyTasksResult.AddOnGroup
	(*ListDailyTasksResult_TaskRow)(nil),                              // 26: moego.api.appointment.v1.ListDailyTasksResult.TaskRow
	(*ListDailyTasksResult_PetColumn)(nil),                            // 27: moego.api.appointment.v1.ListDailyTasksResult.PetColumn
	(*ListDailyTasksResult_ServiceColumn)(nil),                        // 28: moego.api.appointment.v1.ListDailyTasksResult.ServiceColumn
	(*ListDailyTasksResult_StaffColumn)(nil),                          // 29: moego.api.appointment.v1.ListDailyTasksResult.StaffColumn
	(*ListBoardingDepartureCardResult_BoardingView)(nil),              // 30: moego.api.appointment.v1.ListBoardingDepartureCardResult.BoardingView
	(*ListBoardingDepartureCardResult_BoardingView_PetBelonging)(nil), // 31: moego.api.appointment.v1.ListBoardingDepartureCardResult.BoardingView.PetBelonging
	(*ListDailyPlaygroupCardResult_PlaygroupView)(nil),                // 32: moego.api.appointment.v1.ListDailyPlaygroupCardResult.PlaygroupView
	(*ListDailyPlaygroupCardResult_PetPlaygroupView)(nil),             // 33: moego.api.appointment.v1.ListDailyPlaygroupCardResult.PetPlaygroupView
	(*ListDailyPlaygroupCardResult_PetView)(nil),                      // 34: moego.api.appointment.v1.ListDailyPlaygroupCardResult.PetView
	(*ListDailyPlaygroupCardResult_CustomerView)(nil),                 // 35: moego.api.appointment.v1.ListDailyPlaygroupCardResult.CustomerView
	(*ListDailyPlaygroupCardResult_PetCodeBindingsView)(nil),          // 36: moego.api.appointment.v1.ListDailyPlaygroupCardResult.PetCodeBindingsView
	(*ListBoardingArrivalCardResult_BoardingView)(nil),                // 37: moego.api.appointment.v1.ListBoardingArrivalCardResult.BoardingView
	(*ListBoardingArrivalCardResult_BoardingView_PetBelonging)(nil),   // 38: moego.api.appointment.v1.ListBoardingArrivalCardResult.BoardingView.PetBelonging
	(*date.Date)(nil),                         // 39: google.type.Date
	(v1.ServiceItemType)(0),                   // 40: moego.models.offering.v1.ServiceItemType
	(*v1.LodgingUnitModel)(nil),               // 41: moego.models.offering.v1.LodgingUnitModel
	(*v1.PlaygroupModel)(nil),                 // 42: moego.models.offering.v1.PlaygroupModel
	(v11.PetGender)(0),                        // 43: moego.models.customer.v1.PetGender
	(v11.PetType)(0),                          // 44: moego.models.customer.v1.PetType
	(*v12.BoardingSplitLodgingDetailDef)(nil), // 45: moego.models.appointment.v1.BoardingSplitLodgingDetailDef
	(v1.ServiceType)(0),                       // 46: moego.models.offering.v1.ServiceType
	(v12.AppointmentTaskCategory)(0),          // 47: moego.models.appointment.v1.AppointmentTaskCategory
}
var file_moego_api_appointment_v1_print_card_api_proto_depIdxs = []int32{
	39, // 0: moego.api.appointment.v1.ListAppointmentCardParams.date:type_name -> google.type.Date
	40, // 1: moego.api.appointment.v1.ListAppointmentCardParams.types:type_name -> moego.models.offering.v1.ServiceItemType
	1,  // 2: moego.api.appointment.v1.ListAppointmentCardParams.service_filters:type_name -> moego.api.appointment.v1.ServiceFilter
	40, // 3: moego.api.appointment.v1.ServiceFilter.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	11, // 4: moego.api.appointment.v1.ListAppointmentCardResult.boardings:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.BoardingView
	12, // 5: moego.api.appointment.v1.ListAppointmentCardResult.daycares:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.DaycareView
	13, // 6: moego.api.appointment.v1.ListAppointmentCardResult.groomings:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.GroomingView
	14, // 7: moego.api.appointment.v1.ListAppointmentCardResult.evaluations:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.EvaluationView
	15, // 8: moego.api.appointment.v1.ListAppointmentCardResult.dog_walkings:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.DogWalkingView
	16, // 9: moego.api.appointment.v1.ListAppointmentCardResult.pets:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.PetView
	17, // 10: moego.api.appointment.v1.ListAppointmentCardResult.appointments:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.AppointmentView
	18, // 11: moego.api.appointment.v1.ListAppointmentCardResult.lodging_types:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.LodgingTypeDayView
	41, // 12: moego.api.appointment.v1.ListAppointmentCardResult.lodging_units:type_name -> moego.models.offering.v1.LodgingUnitModel
	39, // 13: moego.api.appointment.v1.ListDailyTasksParams.date:type_name -> google.type.Date
	40, // 14: moego.api.appointment.v1.ListDailyTasksParams.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	26, // 15: moego.api.appointment.v1.ListDailyTasksResult.feedings:type_name -> moego.api.appointment.v1.ListDailyTasksResult.TaskRow
	26, // 16: moego.api.appointment.v1.ListDailyTasksResult.medications:type_name -> moego.api.appointment.v1.ListDailyTasksResult.TaskRow
	25, // 17: moego.api.appointment.v1.ListDailyTasksResult.add_ons:type_name -> moego.api.appointment.v1.ListDailyTasksResult.AddOnGroup
	39, // 18: moego.api.appointment.v1.ListBoardingDepartureCardParams.date:type_name -> google.type.Date
	30, // 19: moego.api.appointment.v1.ListBoardingDepartureCardResult.boardings:type_name -> moego.api.appointment.v1.ListBoardingDepartureCardResult.BoardingView
	16, // 20: moego.api.appointment.v1.ListBoardingDepartureCardResult.pets:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.PetView
	17, // 21: moego.api.appointment.v1.ListBoardingDepartureCardResult.appointments:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.AppointmentView
	18, // 22: moego.api.appointment.v1.ListBoardingDepartureCardResult.lodging_types:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.LodgingTypeDayView
	41, // 23: moego.api.appointment.v1.ListBoardingDepartureCardResult.lodging_units:type_name -> moego.models.offering.v1.LodgingUnitModel
	39, // 24: moego.api.appointment.v1.ListDailyPlaygroupCardParams.date:type_name -> google.type.Date
	42, // 25: moego.api.appointment.v1.ListDailyPlaygroupCardResult.playgroups:type_name -> moego.models.offering.v1.PlaygroupModel
	32, // 26: moego.api.appointment.v1.ListDailyPlaygroupCardResult.playgroup_views:type_name -> moego.api.appointment.v1.ListDailyPlaygroupCardResult.PlaygroupView
	34, // 27: moego.api.appointment.v1.ListDailyPlaygroupCardResult.pets:type_name -> moego.api.appointment.v1.ListDailyPlaygroupCardResult.PetView
	39, // 28: moego.api.appointment.v1.ListBoardingArrivalCardParams.date:type_name -> google.type.Date
	37, // 29: moego.api.appointment.v1.ListBoardingArrivalCardResult.boardings:type_name -> moego.api.appointment.v1.ListBoardingArrivalCardResult.BoardingView
	16, // 30: moego.api.appointment.v1.ListBoardingArrivalCardResult.pets:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.PetView
	17, // 31: moego.api.appointment.v1.ListBoardingArrivalCardResult.appointments:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.AppointmentView
	18, // 32: moego.api.appointment.v1.ListBoardingArrivalCardResult.lodging_types:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.LodgingTypeDayView
	41, // 33: moego.api.appointment.v1.ListBoardingArrivalCardResult.lodging_units:type_name -> moego.models.offering.v1.LodgingUnitModel
	20, // 34: moego.api.appointment.v1.ListAppointmentCardResult.BoardingView.pet_services:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.BoardingView.PetServiceView
	21, // 35: moego.api.appointment.v1.ListAppointmentCardResult.DaycareView.pet_services:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.DaycareView.PetServiceView
	22, // 36: moego.api.appointment.v1.ListAppointmentCardResult.GroomingView.pet_services:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.GroomingView.PetServiceView
	23, // 37: moego.api.appointment.v1.ListAppointmentCardResult.EvaluationView.pet_services:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.EvaluationView.PetServiceView
	24, // 38: moego.api.appointment.v1.ListAppointmentCardResult.DogWalkingView.pet_services:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.DogWalkingView.PetServiceView
	43, // 39: moego.api.appointment.v1.ListAppointmentCardResult.PetView.gender:type_name -> moego.models.customer.v1.PetGender
	44, // 40: moego.api.appointment.v1.ListAppointmentCardResult.PetView.pet_type:type_name -> moego.models.customer.v1.PetType
	19, // 41: moego.api.appointment.v1.ListAppointmentCardResult.PetView.pet_code_bindings:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.PetCodeBindingsView
	45, // 42: moego.api.appointment.v1.ListAppointmentCardResult.BoardingView.PetServiceView.split_lodgings:type_name -> moego.models.appointment.v1.BoardingSplitLodgingDetailDef
	46, // 43: moego.api.appointment.v1.ListAppointmentCardResult.GroomingView.PetServiceView.service_type:type_name -> moego.models.offering.v1.ServiceType
	26, // 44: moego.api.appointment.v1.ListDailyTasksResult.AddOnGroup.tasks:type_name -> moego.api.appointment.v1.ListDailyTasksResult.TaskRow
	47, // 45: moego.api.appointment.v1.ListDailyTasksResult.TaskRow.category:type_name -> moego.models.appointment.v1.AppointmentTaskCategory
	27, // 46: moego.api.appointment.v1.ListDailyTasksResult.TaskRow.pet:type_name -> moego.api.appointment.v1.ListDailyTasksResult.PetColumn
	28, // 47: moego.api.appointment.v1.ListDailyTasksResult.TaskRow.service:type_name -> moego.api.appointment.v1.ListDailyTasksResult.ServiceColumn
	29, // 48: moego.api.appointment.v1.ListDailyTasksResult.TaskRow.staff:type_name -> moego.api.appointment.v1.ListDailyTasksResult.StaffColumn
	44, // 49: moego.api.appointment.v1.ListDailyTasksResult.PetColumn.pet_type:type_name -> moego.models.customer.v1.PetType
	43, // 50: moego.api.appointment.v1.ListDailyTasksResult.PetColumn.gender:type_name -> moego.models.customer.v1.PetGender
	40, // 51: moego.api.appointment.v1.ListDailyTasksResult.ServiceColumn.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	20, // 52: moego.api.appointment.v1.ListBoardingDepartureCardResult.BoardingView.pet_services:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.BoardingView.PetServiceView
	31, // 53: moego.api.appointment.v1.ListBoardingDepartureCardResult.BoardingView.pet_belongings:type_name -> moego.api.appointment.v1.ListBoardingDepartureCardResult.BoardingView.PetBelonging
	33, // 54: moego.api.appointment.v1.ListDailyPlaygroupCardResult.PlaygroupView.pet_playgroups:type_name -> moego.api.appointment.v1.ListDailyPlaygroupCardResult.PetPlaygroupView
	44, // 55: moego.api.appointment.v1.ListDailyPlaygroupCardResult.PetView.pet_type:type_name -> moego.models.customer.v1.PetType
	35, // 56: moego.api.appointment.v1.ListDailyPlaygroupCardResult.PetView.customer:type_name -> moego.api.appointment.v1.ListDailyPlaygroupCardResult.CustomerView
	36, // 57: moego.api.appointment.v1.ListDailyPlaygroupCardResult.PetView.pet_code_bindings:type_name -> moego.api.appointment.v1.ListDailyPlaygroupCardResult.PetCodeBindingsView
	20, // 58: moego.api.appointment.v1.ListBoardingArrivalCardResult.BoardingView.pet_services:type_name -> moego.api.appointment.v1.ListAppointmentCardResult.BoardingView.PetServiceView
	38, // 59: moego.api.appointment.v1.ListBoardingArrivalCardResult.BoardingView.pet_belongings:type_name -> moego.api.appointment.v1.ListBoardingArrivalCardResult.BoardingView.PetBelonging
	0,  // 60: moego.api.appointment.v1.PrintCardService.ListAppointmentCard:input_type -> moego.api.appointment.v1.ListAppointmentCardParams
	3,  // 61: moego.api.appointment.v1.PrintCardService.ListDailyTasks:input_type -> moego.api.appointment.v1.ListDailyTasksParams
	5,  // 62: moego.api.appointment.v1.PrintCardService.ListBoardingDepartureCard:input_type -> moego.api.appointment.v1.ListBoardingDepartureCardParams
	7,  // 63: moego.api.appointment.v1.PrintCardService.ListDailyPlaygroupCard:input_type -> moego.api.appointment.v1.ListDailyPlaygroupCardParams
	9,  // 64: moego.api.appointment.v1.PrintCardService.ListBoardingArrivalCard:input_type -> moego.api.appointment.v1.ListBoardingArrivalCardParams
	2,  // 65: moego.api.appointment.v1.PrintCardService.ListAppointmentCard:output_type -> moego.api.appointment.v1.ListAppointmentCardResult
	4,  // 66: moego.api.appointment.v1.PrintCardService.ListDailyTasks:output_type -> moego.api.appointment.v1.ListDailyTasksResult
	6,  // 67: moego.api.appointment.v1.PrintCardService.ListBoardingDepartureCard:output_type -> moego.api.appointment.v1.ListBoardingDepartureCardResult
	8,  // 68: moego.api.appointment.v1.PrintCardService.ListDailyPlaygroupCard:output_type -> moego.api.appointment.v1.ListDailyPlaygroupCardResult
	10, // 69: moego.api.appointment.v1.PrintCardService.ListBoardingArrivalCard:output_type -> moego.api.appointment.v1.ListBoardingArrivalCardResult
	65, // [65:70] is the sub-list for method output_type
	60, // [60:65] is the sub-list for method input_type
	60, // [60:60] is the sub-list for extension type_name
	60, // [60:60] is the sub-list for extension extendee
	0,  // [0:60] is the sub-list for field type_name
}

func init() { file_moego_api_appointment_v1_print_card_api_proto_init() }
func file_moego_api_appointment_v1_print_card_api_proto_init() {
	if File_moego_api_appointment_v1_print_card_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentCardParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentCardResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyTasksParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyTasksResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBoardingDepartureCardParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBoardingDepartureCardResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyPlaygroupCardParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyPlaygroupCardResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBoardingArrivalCardParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBoardingArrivalCardResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentCardResult_BoardingView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentCardResult_DaycareView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentCardResult_GroomingView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentCardResult_EvaluationView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentCardResult_DogWalkingView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentCardResult_PetView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentCardResult_AppointmentView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentCardResult_LodgingTypeDayView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentCardResult_PetCodeBindingsView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentCardResult_BoardingView_PetServiceView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentCardResult_DaycareView_PetServiceView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentCardResult_GroomingView_PetServiceView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentCardResult_EvaluationView_PetServiceView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentCardResult_DogWalkingView_PetServiceView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyTasksResult_AddOnGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyTasksResult_TaskRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyTasksResult_PetColumn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyTasksResult_ServiceColumn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyTasksResult_StaffColumn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBoardingDepartureCardResult_BoardingView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBoardingDepartureCardResult_BoardingView_PetBelonging); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyPlaygroupCardResult_PlaygroupView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyPlaygroupCardResult_PetPlaygroupView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyPlaygroupCardResult_PetView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyPlaygroupCardResult_CustomerView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyPlaygroupCardResult_PetCodeBindingsView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBoardingArrivalCardResult_BoardingView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_print_card_api_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBoardingArrivalCardResult_BoardingView_PetBelonging); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_appointment_v1_print_card_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_print_card_api_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_print_card_api_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_print_card_api_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_print_card_api_proto_msgTypes[26].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_print_card_api_proto_msgTypes[31].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_print_card_api_proto_msgTypes[38].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_appointment_v1_print_card_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_appointment_v1_print_card_api_proto_goTypes,
		DependencyIndexes: file_moego_api_appointment_v1_print_card_api_proto_depIdxs,
		MessageInfos:      file_moego_api_appointment_v1_print_card_api_proto_msgTypes,
	}.Build()
	File_moego_api_appointment_v1_print_card_api_proto = out.File
	file_moego_api_appointment_v1_print_card_api_proto_rawDesc = nil
	file_moego_api_appointment_v1_print_card_api_proto_goTypes = nil
	file_moego_api_appointment_v1_print_card_api_proto_depIdxs = nil
}
