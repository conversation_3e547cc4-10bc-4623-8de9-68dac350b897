// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/appointment/v1/appointment_api.proto

package appointmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AppointmentServiceClient is the client API for AppointmentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppointmentServiceClient interface {
	// Create a appointment
	// Applicable to quick add and advanced add appointment scenarios
	CreateAppointment(ctx context.Context, in *CreateAppointmentParams, opts ...grpc.CallOption) (*CreateAppointmentResult, error)
	// Incremental update appointment
	// Applicable to add/modify pet&service, switch on multi-pets start at the same time.
	UpdateAppointment(ctx context.Context, in *UpdateAppointmentParams, opts ...grpc.CallOption) (*UpdateAppointmentResult, error)
	// Get appointment detail
	GetAppointment(ctx context.Context, in *GetAppointmentParams, opts ...grpc.CallOption) (*GetAppointmentResult, error)
	// get customer last appointment
	GetCustomerLastAppointment(ctx context.Context, in *GetCustomerLastAppointmentParams, opts ...grpc.CallOption) (*GetCustomerLastAppointmentResult, error)
	// Calculate appointment invoice
	// Estimated total, including service, add-on, tax, discount etc.
	CalculateAppointmentInvoice(ctx context.Context, in *CalculateAppointmentInvoiceParams, opts ...grpc.CallOption) (*CalculateAppointmentInvoiceResult, error)
	// Get in progress evaluation appointment
	GetInProgressEvaluationAppointment(ctx context.Context, in *GetInProgressEvaluationAppointmentParams, opts ...grpc.CallOption) (*GetInProgressEvaluationAppointmentResult, error)
	// Get day summary
	GetDaySummary(ctx context.Context, in *GetDaySummaryParams, opts ...grpc.CallOption) (*GetDaySummaryResult, error)
	// Get service summary
	GetServiceSummary(ctx context.Context, in *GetServiceSummaryParams, opts ...grpc.CallOption) (*GetServiceSummaryResult, error)
	// get appointment lodging info
	GetAppointmentLodging(ctx context.Context, in *GetAppointmentLodgingParams, opts ...grpc.CallOption) (*GetAppointmentLodgingResult, error)
	// Batch quick check in multi-pets for appointments <br>
	// Pets belonging to the same client are in one appointment <br>
	// Pets of different clients create different appointments <br>
	// Update the appointment status to check in
	BatchQuickCheckIn(ctx context.Context, in *BatchQuickCheckInParams, opts ...grpc.CallOption) (*BatchQuickCheckInResult, error)
	// ListStaffAppointments, used for getting staff appointment list in Mobile reports
	ListStaffAppointments(ctx context.Context, in *ListStaffAppointmentsParams, opts ...grpc.CallOption) (*ListStaffAppointmentsResult, error)
	// book again appointment by staff and date
	BatchBookAgainAppointment(ctx context.Context, in *BatchBookAgainAppointmentParams, opts ...grpc.CallOption) (*BatchBookAgainAppointmentResult, error)
	// Batch cancel appointment by staff and date
	BatchCancelAppointment(ctx context.Context, in *BatchCancelAppointmentParams, opts ...grpc.CallOption) (*BatchCancelAppointmentResult, error)
	// Reschedule boarding appointment
	// 处理与 boarding 相关的附加数据，如 daycare、grooming、addon 等服务。
	// 如果传入的 end_date 早于当前 end_date，则删除 boarding 关联的所有 daycare、grooming、addon 数据。
	// 如果传入的 end_date 晚于当前 end_date，则在 boarding 上添加相应的新增 daycare、grooming、addon 数据。
	RescheduleBoardingAppointment(ctx context.Context, in *RescheduleBoardingAppointmentParams, opts ...grpc.CallOption) (*RescheduleBoardingAppointmentResult, error)
}

type appointmentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppointmentServiceClient(cc grpc.ClientConnInterface) AppointmentServiceClient {
	return &appointmentServiceClient{cc}
}

func (c *appointmentServiceClient) CreateAppointment(ctx context.Context, in *CreateAppointmentParams, opts ...grpc.CallOption) (*CreateAppointmentResult, error) {
	out := new(CreateAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentService/CreateAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) UpdateAppointment(ctx context.Context, in *UpdateAppointmentParams, opts ...grpc.CallOption) (*UpdateAppointmentResult, error) {
	out := new(UpdateAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentService/UpdateAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) GetAppointment(ctx context.Context, in *GetAppointmentParams, opts ...grpc.CallOption) (*GetAppointmentResult, error) {
	out := new(GetAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentService/GetAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) GetCustomerLastAppointment(ctx context.Context, in *GetCustomerLastAppointmentParams, opts ...grpc.CallOption) (*GetCustomerLastAppointmentResult, error) {
	out := new(GetCustomerLastAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentService/GetCustomerLastAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) CalculateAppointmentInvoice(ctx context.Context, in *CalculateAppointmentInvoiceParams, opts ...grpc.CallOption) (*CalculateAppointmentInvoiceResult, error) {
	out := new(CalculateAppointmentInvoiceResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentService/CalculateAppointmentInvoice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) GetInProgressEvaluationAppointment(ctx context.Context, in *GetInProgressEvaluationAppointmentParams, opts ...grpc.CallOption) (*GetInProgressEvaluationAppointmentResult, error) {
	out := new(GetInProgressEvaluationAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentService/GetInProgressEvaluationAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) GetDaySummary(ctx context.Context, in *GetDaySummaryParams, opts ...grpc.CallOption) (*GetDaySummaryResult, error) {
	out := new(GetDaySummaryResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentService/GetDaySummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) GetServiceSummary(ctx context.Context, in *GetServiceSummaryParams, opts ...grpc.CallOption) (*GetServiceSummaryResult, error) {
	out := new(GetServiceSummaryResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentService/GetServiceSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) GetAppointmentLodging(ctx context.Context, in *GetAppointmentLodgingParams, opts ...grpc.CallOption) (*GetAppointmentLodgingResult, error) {
	out := new(GetAppointmentLodgingResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentService/GetAppointmentLodging", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) BatchQuickCheckIn(ctx context.Context, in *BatchQuickCheckInParams, opts ...grpc.CallOption) (*BatchQuickCheckInResult, error) {
	out := new(BatchQuickCheckInResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentService/BatchQuickCheckIn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) ListStaffAppointments(ctx context.Context, in *ListStaffAppointmentsParams, opts ...grpc.CallOption) (*ListStaffAppointmentsResult, error) {
	out := new(ListStaffAppointmentsResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentService/ListStaffAppointments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) BatchBookAgainAppointment(ctx context.Context, in *BatchBookAgainAppointmentParams, opts ...grpc.CallOption) (*BatchBookAgainAppointmentResult, error) {
	out := new(BatchBookAgainAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentService/BatchBookAgainAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) BatchCancelAppointment(ctx context.Context, in *BatchCancelAppointmentParams, opts ...grpc.CallOption) (*BatchCancelAppointmentResult, error) {
	out := new(BatchCancelAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentService/BatchCancelAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentServiceClient) RescheduleBoardingAppointment(ctx context.Context, in *RescheduleBoardingAppointmentParams, opts ...grpc.CallOption) (*RescheduleBoardingAppointmentResult, error) {
	out := new(RescheduleBoardingAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.AppointmentService/RescheduleBoardingAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppointmentServiceServer is the server API for AppointmentService service.
// All implementations must embed UnimplementedAppointmentServiceServer
// for forward compatibility
type AppointmentServiceServer interface {
	// Create a appointment
	// Applicable to quick add and advanced add appointment scenarios
	CreateAppointment(context.Context, *CreateAppointmentParams) (*CreateAppointmentResult, error)
	// Incremental update appointment
	// Applicable to add/modify pet&service, switch on multi-pets start at the same time.
	UpdateAppointment(context.Context, *UpdateAppointmentParams) (*UpdateAppointmentResult, error)
	// Get appointment detail
	GetAppointment(context.Context, *GetAppointmentParams) (*GetAppointmentResult, error)
	// get customer last appointment
	GetCustomerLastAppointment(context.Context, *GetCustomerLastAppointmentParams) (*GetCustomerLastAppointmentResult, error)
	// Calculate appointment invoice
	// Estimated total, including service, add-on, tax, discount etc.
	CalculateAppointmentInvoice(context.Context, *CalculateAppointmentInvoiceParams) (*CalculateAppointmentInvoiceResult, error)
	// Get in progress evaluation appointment
	GetInProgressEvaluationAppointment(context.Context, *GetInProgressEvaluationAppointmentParams) (*GetInProgressEvaluationAppointmentResult, error)
	// Get day summary
	GetDaySummary(context.Context, *GetDaySummaryParams) (*GetDaySummaryResult, error)
	// Get service summary
	GetServiceSummary(context.Context, *GetServiceSummaryParams) (*GetServiceSummaryResult, error)
	// get appointment lodging info
	GetAppointmentLodging(context.Context, *GetAppointmentLodgingParams) (*GetAppointmentLodgingResult, error)
	// Batch quick check in multi-pets for appointments <br>
	// Pets belonging to the same client are in one appointment <br>
	// Pets of different clients create different appointments <br>
	// Update the appointment status to check in
	BatchQuickCheckIn(context.Context, *BatchQuickCheckInParams) (*BatchQuickCheckInResult, error)
	// ListStaffAppointments, used for getting staff appointment list in Mobile reports
	ListStaffAppointments(context.Context, *ListStaffAppointmentsParams) (*ListStaffAppointmentsResult, error)
	// book again appointment by staff and date
	BatchBookAgainAppointment(context.Context, *BatchBookAgainAppointmentParams) (*BatchBookAgainAppointmentResult, error)
	// Batch cancel appointment by staff and date
	BatchCancelAppointment(context.Context, *BatchCancelAppointmentParams) (*BatchCancelAppointmentResult, error)
	// Reschedule boarding appointment
	// 处理与 boarding 相关的附加数据，如 daycare、grooming、addon 等服务。
	// 如果传入的 end_date 早于当前 end_date，则删除 boarding 关联的所有 daycare、grooming、addon 数据。
	// 如果传入的 end_date 晚于当前 end_date，则在 boarding 上添加相应的新增 daycare、grooming、addon 数据。
	RescheduleBoardingAppointment(context.Context, *RescheduleBoardingAppointmentParams) (*RescheduleBoardingAppointmentResult, error)
	mustEmbedUnimplementedAppointmentServiceServer()
}

// UnimplementedAppointmentServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAppointmentServiceServer struct {
}

func (UnimplementedAppointmentServiceServer) CreateAppointment(context.Context, *CreateAppointmentParams) (*CreateAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) UpdateAppointment(context.Context, *UpdateAppointmentParams) (*UpdateAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) GetAppointment(context.Context, *GetAppointmentParams) (*GetAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) GetCustomerLastAppointment(context.Context, *GetCustomerLastAppointmentParams) (*GetCustomerLastAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerLastAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) CalculateAppointmentInvoice(context.Context, *CalculateAppointmentInvoiceParams) (*CalculateAppointmentInvoiceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculateAppointmentInvoice not implemented")
}
func (UnimplementedAppointmentServiceServer) GetInProgressEvaluationAppointment(context.Context, *GetInProgressEvaluationAppointmentParams) (*GetInProgressEvaluationAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInProgressEvaluationAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) GetDaySummary(context.Context, *GetDaySummaryParams) (*GetDaySummaryResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDaySummary not implemented")
}
func (UnimplementedAppointmentServiceServer) GetServiceSummary(context.Context, *GetServiceSummaryParams) (*GetServiceSummaryResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceSummary not implemented")
}
func (UnimplementedAppointmentServiceServer) GetAppointmentLodging(context.Context, *GetAppointmentLodgingParams) (*GetAppointmentLodgingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppointmentLodging not implemented")
}
func (UnimplementedAppointmentServiceServer) BatchQuickCheckIn(context.Context, *BatchQuickCheckInParams) (*BatchQuickCheckInResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchQuickCheckIn not implemented")
}
func (UnimplementedAppointmentServiceServer) ListStaffAppointments(context.Context, *ListStaffAppointmentsParams) (*ListStaffAppointmentsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStaffAppointments not implemented")
}
func (UnimplementedAppointmentServiceServer) BatchBookAgainAppointment(context.Context, *BatchBookAgainAppointmentParams) (*BatchBookAgainAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchBookAgainAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) BatchCancelAppointment(context.Context, *BatchCancelAppointmentParams) (*BatchCancelAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCancelAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) RescheduleBoardingAppointment(context.Context, *RescheduleBoardingAppointmentParams) (*RescheduleBoardingAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RescheduleBoardingAppointment not implemented")
}
func (UnimplementedAppointmentServiceServer) mustEmbedUnimplementedAppointmentServiceServer() {}

// UnsafeAppointmentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppointmentServiceServer will
// result in compilation errors.
type UnsafeAppointmentServiceServer interface {
	mustEmbedUnimplementedAppointmentServiceServer()
}

func RegisterAppointmentServiceServer(s grpc.ServiceRegistrar, srv AppointmentServiceServer) {
	s.RegisterService(&AppointmentService_ServiceDesc, srv)
}

func _AppointmentService_CreateAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).CreateAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentService/CreateAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).CreateAppointment(ctx, req.(*CreateAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_UpdateAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).UpdateAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentService/UpdateAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).UpdateAppointment(ctx, req.(*UpdateAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_GetAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).GetAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentService/GetAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).GetAppointment(ctx, req.(*GetAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_GetCustomerLastAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerLastAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).GetCustomerLastAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentService/GetCustomerLastAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).GetCustomerLastAppointment(ctx, req.(*GetCustomerLastAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_CalculateAppointmentInvoice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculateAppointmentInvoiceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).CalculateAppointmentInvoice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentService/CalculateAppointmentInvoice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).CalculateAppointmentInvoice(ctx, req.(*CalculateAppointmentInvoiceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_GetInProgressEvaluationAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInProgressEvaluationAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).GetInProgressEvaluationAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentService/GetInProgressEvaluationAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).GetInProgressEvaluationAppointment(ctx, req.(*GetInProgressEvaluationAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_GetDaySummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDaySummaryParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).GetDaySummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentService/GetDaySummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).GetDaySummary(ctx, req.(*GetDaySummaryParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_GetServiceSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceSummaryParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).GetServiceSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentService/GetServiceSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).GetServiceSummary(ctx, req.(*GetServiceSummaryParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_GetAppointmentLodging_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointmentLodgingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).GetAppointmentLodging(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentService/GetAppointmentLodging",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).GetAppointmentLodging(ctx, req.(*GetAppointmentLodgingParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_BatchQuickCheckIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchQuickCheckInParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).BatchQuickCheckIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentService/BatchQuickCheckIn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).BatchQuickCheckIn(ctx, req.(*BatchQuickCheckInParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_ListStaffAppointments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListStaffAppointmentsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).ListStaffAppointments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentService/ListStaffAppointments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).ListStaffAppointments(ctx, req.(*ListStaffAppointmentsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_BatchBookAgainAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchBookAgainAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).BatchBookAgainAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentService/BatchBookAgainAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).BatchBookAgainAppointment(ctx, req.(*BatchBookAgainAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_BatchCancelAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCancelAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).BatchCancelAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentService/BatchCancelAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).BatchCancelAppointment(ctx, req.(*BatchCancelAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentService_RescheduleBoardingAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RescheduleBoardingAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentServiceServer).RescheduleBoardingAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.AppointmentService/RescheduleBoardingAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentServiceServer).RescheduleBoardingAppointment(ctx, req.(*RescheduleBoardingAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AppointmentService_ServiceDesc is the grpc.ServiceDesc for AppointmentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppointmentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.appointment.v1.AppointmentService",
	HandlerType: (*AppointmentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAppointment",
			Handler:    _AppointmentService_CreateAppointment_Handler,
		},
		{
			MethodName: "UpdateAppointment",
			Handler:    _AppointmentService_UpdateAppointment_Handler,
		},
		{
			MethodName: "GetAppointment",
			Handler:    _AppointmentService_GetAppointment_Handler,
		},
		{
			MethodName: "GetCustomerLastAppointment",
			Handler:    _AppointmentService_GetCustomerLastAppointment_Handler,
		},
		{
			MethodName: "CalculateAppointmentInvoice",
			Handler:    _AppointmentService_CalculateAppointmentInvoice_Handler,
		},
		{
			MethodName: "GetInProgressEvaluationAppointment",
			Handler:    _AppointmentService_GetInProgressEvaluationAppointment_Handler,
		},
		{
			MethodName: "GetDaySummary",
			Handler:    _AppointmentService_GetDaySummary_Handler,
		},
		{
			MethodName: "GetServiceSummary",
			Handler:    _AppointmentService_GetServiceSummary_Handler,
		},
		{
			MethodName: "GetAppointmentLodging",
			Handler:    _AppointmentService_GetAppointmentLodging_Handler,
		},
		{
			MethodName: "BatchQuickCheckIn",
			Handler:    _AppointmentService_BatchQuickCheckIn_Handler,
		},
		{
			MethodName: "ListStaffAppointments",
			Handler:    _AppointmentService_ListStaffAppointments_Handler,
		},
		{
			MethodName: "BatchBookAgainAppointment",
			Handler:    _AppointmentService_BatchBookAgainAppointment_Handler,
		},
		{
			MethodName: "BatchCancelAppointment",
			Handler:    _AppointmentService_BatchCancelAppointment_Handler,
		},
		{
			MethodName: "RescheduleBoardingAppointment",
			Handler:    _AppointmentService_RescheduleBoardingAppointment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/appointment/v1/appointment_api.proto",
}
