// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/appointment/v1/calendar_api.proto

package appointmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CalendarServiceClient is the client API for CalendarService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CalendarServiceClient interface {
	// List day or week calendar view cards
	// Only the following specific types of cards are returned
	//   - APPOINTMENT
	//   - SERVICE (one)
	//   - OPERATION (one)
	//   - BLOCK
	//   - BOOKING_REQUEST
	ListDayCards(ctx context.Context, in *ListDayCardsParams, opts ...grpc.CallOption) (*ListDayCardsResult, error)
	// List month calendar view cards
	// fields: start_date, start_time, client_first_name, color_code, booking_type(pending, scheduled)
	ListMonthCards(ctx context.Context, in *ListMonthCardsParams, opts ...grpc.CallOption) (*ListMonthCardsResult, error)
	// Preview calendar view cards
	PreviewCalendarCards(ctx context.Context, in *PreviewCalendarCardsParams, opts ...grpc.CallOption) (*PreviewCalendarCardsResult, error)
	// List day or week calendar view cards with mix type
	// Compared to the @see [ListDayCards](#moego.api.appointment.v1.CalendarService.ListDayCards)
	// There are more of the following types of cards
	//   - APPOINTMENT
	//   - SERVICE (one or more services)
	//   - OPERATION (one or more operations)
	//   - SERVICE_AND_OPERATION (one or more services and operations)
	//   - BLOCK
	//   - BOOKING_REQUEST
	ListDayCardsWithMixType(ctx context.Context, in *ListDayCardsWithMixTypeParams, opts ...grpc.CallOption) (*ListDayCardsWithMixTypeResult, error)
	// List day slot infos
	ListDaySlotInfos(ctx context.Context, in *ListDaySlotInfosParams, opts ...grpc.CallOption) (*ListDaySlotInfosResult, error)
}

type calendarServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCalendarServiceClient(cc grpc.ClientConnInterface) CalendarServiceClient {
	return &calendarServiceClient{cc}
}

func (c *calendarServiceClient) ListDayCards(ctx context.Context, in *ListDayCardsParams, opts ...grpc.CallOption) (*ListDayCardsResult, error) {
	out := new(ListDayCardsResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.CalendarService/ListDayCards", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *calendarServiceClient) ListMonthCards(ctx context.Context, in *ListMonthCardsParams, opts ...grpc.CallOption) (*ListMonthCardsResult, error) {
	out := new(ListMonthCardsResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.CalendarService/ListMonthCards", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *calendarServiceClient) PreviewCalendarCards(ctx context.Context, in *PreviewCalendarCardsParams, opts ...grpc.CallOption) (*PreviewCalendarCardsResult, error) {
	out := new(PreviewCalendarCardsResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.CalendarService/PreviewCalendarCards", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *calendarServiceClient) ListDayCardsWithMixType(ctx context.Context, in *ListDayCardsWithMixTypeParams, opts ...grpc.CallOption) (*ListDayCardsWithMixTypeResult, error) {
	out := new(ListDayCardsWithMixTypeResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.CalendarService/ListDayCardsWithMixType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *calendarServiceClient) ListDaySlotInfos(ctx context.Context, in *ListDaySlotInfosParams, opts ...grpc.CallOption) (*ListDaySlotInfosResult, error) {
	out := new(ListDaySlotInfosResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.CalendarService/ListDaySlotInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CalendarServiceServer is the server API for CalendarService service.
// All implementations must embed UnimplementedCalendarServiceServer
// for forward compatibility
type CalendarServiceServer interface {
	// List day or week calendar view cards
	// Only the following specific types of cards are returned
	//   - APPOINTMENT
	//   - SERVICE (one)
	//   - OPERATION (one)
	//   - BLOCK
	//   - BOOKING_REQUEST
	ListDayCards(context.Context, *ListDayCardsParams) (*ListDayCardsResult, error)
	// List month calendar view cards
	// fields: start_date, start_time, client_first_name, color_code, booking_type(pending, scheduled)
	ListMonthCards(context.Context, *ListMonthCardsParams) (*ListMonthCardsResult, error)
	// Preview calendar view cards
	PreviewCalendarCards(context.Context, *PreviewCalendarCardsParams) (*PreviewCalendarCardsResult, error)
	// List day or week calendar view cards with mix type
	// Compared to the @see [ListDayCards](#moego.api.appointment.v1.CalendarService.ListDayCards)
	// There are more of the following types of cards
	//   - APPOINTMENT
	//   - SERVICE (one or more services)
	//   - OPERATION (one or more operations)
	//   - SERVICE_AND_OPERATION (one or more services and operations)
	//   - BLOCK
	//   - BOOKING_REQUEST
	ListDayCardsWithMixType(context.Context, *ListDayCardsWithMixTypeParams) (*ListDayCardsWithMixTypeResult, error)
	// List day slot infos
	ListDaySlotInfos(context.Context, *ListDaySlotInfosParams) (*ListDaySlotInfosResult, error)
	mustEmbedUnimplementedCalendarServiceServer()
}

// UnimplementedCalendarServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCalendarServiceServer struct {
}

func (UnimplementedCalendarServiceServer) ListDayCards(context.Context, *ListDayCardsParams) (*ListDayCardsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDayCards not implemented")
}
func (UnimplementedCalendarServiceServer) ListMonthCards(context.Context, *ListMonthCardsParams) (*ListMonthCardsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMonthCards not implemented")
}
func (UnimplementedCalendarServiceServer) PreviewCalendarCards(context.Context, *PreviewCalendarCardsParams) (*PreviewCalendarCardsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreviewCalendarCards not implemented")
}
func (UnimplementedCalendarServiceServer) ListDayCardsWithMixType(context.Context, *ListDayCardsWithMixTypeParams) (*ListDayCardsWithMixTypeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDayCardsWithMixType not implemented")
}
func (UnimplementedCalendarServiceServer) ListDaySlotInfos(context.Context, *ListDaySlotInfosParams) (*ListDaySlotInfosResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDaySlotInfos not implemented")
}
func (UnimplementedCalendarServiceServer) mustEmbedUnimplementedCalendarServiceServer() {}

// UnsafeCalendarServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CalendarServiceServer will
// result in compilation errors.
type UnsafeCalendarServiceServer interface {
	mustEmbedUnimplementedCalendarServiceServer()
}

func RegisterCalendarServiceServer(s grpc.ServiceRegistrar, srv CalendarServiceServer) {
	s.RegisterService(&CalendarService_ServiceDesc, srv)
}

func _CalendarService_ListDayCards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDayCardsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CalendarServiceServer).ListDayCards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.CalendarService/ListDayCards",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CalendarServiceServer).ListDayCards(ctx, req.(*ListDayCardsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CalendarService_ListMonthCards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMonthCardsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CalendarServiceServer).ListMonthCards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.CalendarService/ListMonthCards",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CalendarServiceServer).ListMonthCards(ctx, req.(*ListMonthCardsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CalendarService_PreviewCalendarCards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreviewCalendarCardsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CalendarServiceServer).PreviewCalendarCards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.CalendarService/PreviewCalendarCards",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CalendarServiceServer).PreviewCalendarCards(ctx, req.(*PreviewCalendarCardsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CalendarService_ListDayCardsWithMixType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDayCardsWithMixTypeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CalendarServiceServer).ListDayCardsWithMixType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.CalendarService/ListDayCardsWithMixType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CalendarServiceServer).ListDayCardsWithMixType(ctx, req.(*ListDayCardsWithMixTypeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _CalendarService_ListDaySlotInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDaySlotInfosParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CalendarServiceServer).ListDaySlotInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.CalendarService/ListDaySlotInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CalendarServiceServer).ListDaySlotInfos(ctx, req.(*ListDaySlotInfosParams))
	}
	return interceptor(ctx, in, info, handler)
}

// CalendarService_ServiceDesc is the grpc.ServiceDesc for CalendarService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CalendarService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.appointment.v1.CalendarService",
	HandlerType: (*CalendarServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListDayCards",
			Handler:    _CalendarService_ListDayCards_Handler,
		},
		{
			MethodName: "ListMonthCards",
			Handler:    _CalendarService_ListMonthCards_Handler,
		},
		{
			MethodName: "PreviewCalendarCards",
			Handler:    _CalendarService_PreviewCalendarCards_Handler,
		},
		{
			MethodName: "ListDayCardsWithMixType",
			Handler:    _CalendarService_ListDayCardsWithMixType_Handler,
		},
		{
			MethodName: "ListDaySlotInfos",
			Handler:    _CalendarService_ListDaySlotInfos_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/appointment/v1/calendar_api.proto",
}
