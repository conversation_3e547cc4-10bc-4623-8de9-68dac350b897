// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/agreement/v1/agreement_record_api.proto

package agreementapipb

import (
	context "context"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AgreementRecordServiceClient is the client API for AgreementRecordService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AgreementRecordServiceClient interface {
	// create an unsigned agreement record
	AddRecord(ctx context.Context, in *AddRecordRequest, opts ...grpc.CallOption) (*v1.AgreementRecordModel, error)
	// upload signed agreement files
	UploadSignedFile(ctx context.Context, in *UploadSignedFileRequest, opts ...grpc.CallOption) (*v1.AgreementRecordModel, error)
	// get agreement record list for business
	GetRecordListForBusiness(ctx context.Context, in *GetRecordListForBusinessRequest, opts ...grpc.CallOption) (*GetRecordListForBusinessResponse, error)
	// get agreement record list for customer
	GetRecordListForCustomer(ctx context.Context, in *GetRecordListForCustomerRequest, opts ...grpc.CallOption) (*GetRecordListForCustomerResponse, error)
	// get agreement with recent signed record list
	GetRecentSignedAgreementList(ctx context.Context, in *GetRecentSignedAgreementListRequest, opts ...grpc.CallOption) (*GetRecentSignedAgreementListResponse, error)
	// get an agreement record by id
	GetRecordById(ctx context.Context, in *v11.Id, opts ...grpc.CallOption) (*v1.AgreementRecordModel, error)
	// get an agreement record by uuid
	GetRecordByUUID(ctx context.Context, in *GetRecordByUUIDRequest, opts ...grpc.CallOption) (*GetRecordByUUIDResponse, error)
	// get an agreement record by target id
	GetRecordByTarget(ctx context.Context, in *GetRecordByTargetRequest, opts ...grpc.CallOption) (*v1.AgreementRecordModel, error)
	// get an agreement record detail, include business and customer simple info
	GetRecordDetail(ctx context.Context, in *v11.Id, opts ...grpc.CallOption) (*GetRecordDetailResponse, error)
	// sign an agreement
	SignRecord(ctx context.Context, in *SignRecordRequest, opts ...grpc.CallOption) (*v1.AgreementRecordSimpleView, error)
	// sign an already created agreement record
	SendSignRequest(ctx context.Context, in *SendSignRequestRequest, opts ...grpc.CallOption) (*SendSignRequestResponse, error)
	// delete an agreement record
	DeleteRecord(ctx context.Context, in *v11.Id, opts ...grpc.CallOption) (*DeleteRecordResponse, error)
	// direct sign a record from an agreement
	SignAgreement(ctx context.Context, in *SignAgreementParams, opts ...grpc.CallOption) (*v1.AgreementRecordSimpleView, error)
}

type agreementRecordServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAgreementRecordServiceClient(cc grpc.ClientConnInterface) AgreementRecordServiceClient {
	return &agreementRecordServiceClient{cc}
}

func (c *agreementRecordServiceClient) AddRecord(ctx context.Context, in *AddRecordRequest, opts ...grpc.CallOption) (*v1.AgreementRecordModel, error) {
	out := new(v1.AgreementRecordModel)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementRecordService/AddRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) UploadSignedFile(ctx context.Context, in *UploadSignedFileRequest, opts ...grpc.CallOption) (*v1.AgreementRecordModel, error) {
	out := new(v1.AgreementRecordModel)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementRecordService/UploadSignedFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) GetRecordListForBusiness(ctx context.Context, in *GetRecordListForBusinessRequest, opts ...grpc.CallOption) (*GetRecordListForBusinessResponse, error) {
	out := new(GetRecordListForBusinessResponse)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementRecordService/GetRecordListForBusiness", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) GetRecordListForCustomer(ctx context.Context, in *GetRecordListForCustomerRequest, opts ...grpc.CallOption) (*GetRecordListForCustomerResponse, error) {
	out := new(GetRecordListForCustomerResponse)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementRecordService/GetRecordListForCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) GetRecentSignedAgreementList(ctx context.Context, in *GetRecentSignedAgreementListRequest, opts ...grpc.CallOption) (*GetRecentSignedAgreementListResponse, error) {
	out := new(GetRecentSignedAgreementListResponse)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementRecordService/GetRecentSignedAgreementList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) GetRecordById(ctx context.Context, in *v11.Id, opts ...grpc.CallOption) (*v1.AgreementRecordModel, error) {
	out := new(v1.AgreementRecordModel)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementRecordService/GetRecordById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) GetRecordByUUID(ctx context.Context, in *GetRecordByUUIDRequest, opts ...grpc.CallOption) (*GetRecordByUUIDResponse, error) {
	out := new(GetRecordByUUIDResponse)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementRecordService/GetRecordByUUID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) GetRecordByTarget(ctx context.Context, in *GetRecordByTargetRequest, opts ...grpc.CallOption) (*v1.AgreementRecordModel, error) {
	out := new(v1.AgreementRecordModel)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementRecordService/GetRecordByTarget", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) GetRecordDetail(ctx context.Context, in *v11.Id, opts ...grpc.CallOption) (*GetRecordDetailResponse, error) {
	out := new(GetRecordDetailResponse)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementRecordService/GetRecordDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) SignRecord(ctx context.Context, in *SignRecordRequest, opts ...grpc.CallOption) (*v1.AgreementRecordSimpleView, error) {
	out := new(v1.AgreementRecordSimpleView)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementRecordService/SignRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) SendSignRequest(ctx context.Context, in *SendSignRequestRequest, opts ...grpc.CallOption) (*SendSignRequestResponse, error) {
	out := new(SendSignRequestResponse)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementRecordService/SendSignRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) DeleteRecord(ctx context.Context, in *v11.Id, opts ...grpc.CallOption) (*DeleteRecordResponse, error) {
	out := new(DeleteRecordResponse)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementRecordService/DeleteRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) SignAgreement(ctx context.Context, in *SignAgreementParams, opts ...grpc.CallOption) (*v1.AgreementRecordSimpleView, error) {
	out := new(v1.AgreementRecordSimpleView)
	err := c.cc.Invoke(ctx, "/moego.api.agreement.v1.AgreementRecordService/SignAgreement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AgreementRecordServiceServer is the server API for AgreementRecordService service.
// All implementations must embed UnimplementedAgreementRecordServiceServer
// for forward compatibility
type AgreementRecordServiceServer interface {
	// create an unsigned agreement record
	AddRecord(context.Context, *AddRecordRequest) (*v1.AgreementRecordModel, error)
	// upload signed agreement files
	UploadSignedFile(context.Context, *UploadSignedFileRequest) (*v1.AgreementRecordModel, error)
	// get agreement record list for business
	GetRecordListForBusiness(context.Context, *GetRecordListForBusinessRequest) (*GetRecordListForBusinessResponse, error)
	// get agreement record list for customer
	GetRecordListForCustomer(context.Context, *GetRecordListForCustomerRequest) (*GetRecordListForCustomerResponse, error)
	// get agreement with recent signed record list
	GetRecentSignedAgreementList(context.Context, *GetRecentSignedAgreementListRequest) (*GetRecentSignedAgreementListResponse, error)
	// get an agreement record by id
	GetRecordById(context.Context, *v11.Id) (*v1.AgreementRecordModel, error)
	// get an agreement record by uuid
	GetRecordByUUID(context.Context, *GetRecordByUUIDRequest) (*GetRecordByUUIDResponse, error)
	// get an agreement record by target id
	GetRecordByTarget(context.Context, *GetRecordByTargetRequest) (*v1.AgreementRecordModel, error)
	// get an agreement record detail, include business and customer simple info
	GetRecordDetail(context.Context, *v11.Id) (*GetRecordDetailResponse, error)
	// sign an agreement
	SignRecord(context.Context, *SignRecordRequest) (*v1.AgreementRecordSimpleView, error)
	// sign an already created agreement record
	SendSignRequest(context.Context, *SendSignRequestRequest) (*SendSignRequestResponse, error)
	// delete an agreement record
	DeleteRecord(context.Context, *v11.Id) (*DeleteRecordResponse, error)
	// direct sign a record from an agreement
	SignAgreement(context.Context, *SignAgreementParams) (*v1.AgreementRecordSimpleView, error)
	mustEmbedUnimplementedAgreementRecordServiceServer()
}

// UnimplementedAgreementRecordServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAgreementRecordServiceServer struct {
}

func (UnimplementedAgreementRecordServiceServer) AddRecord(context.Context, *AddRecordRequest) (*v1.AgreementRecordModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddRecord not implemented")
}
func (UnimplementedAgreementRecordServiceServer) UploadSignedFile(context.Context, *UploadSignedFileRequest) (*v1.AgreementRecordModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadSignedFile not implemented")
}
func (UnimplementedAgreementRecordServiceServer) GetRecordListForBusiness(context.Context, *GetRecordListForBusinessRequest) (*GetRecordListForBusinessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecordListForBusiness not implemented")
}
func (UnimplementedAgreementRecordServiceServer) GetRecordListForCustomer(context.Context, *GetRecordListForCustomerRequest) (*GetRecordListForCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecordListForCustomer not implemented")
}
func (UnimplementedAgreementRecordServiceServer) GetRecentSignedAgreementList(context.Context, *GetRecentSignedAgreementListRequest) (*GetRecentSignedAgreementListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecentSignedAgreementList not implemented")
}
func (UnimplementedAgreementRecordServiceServer) GetRecordById(context.Context, *v11.Id) (*v1.AgreementRecordModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecordById not implemented")
}
func (UnimplementedAgreementRecordServiceServer) GetRecordByUUID(context.Context, *GetRecordByUUIDRequest) (*GetRecordByUUIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecordByUUID not implemented")
}
func (UnimplementedAgreementRecordServiceServer) GetRecordByTarget(context.Context, *GetRecordByTargetRequest) (*v1.AgreementRecordModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecordByTarget not implemented")
}
func (UnimplementedAgreementRecordServiceServer) GetRecordDetail(context.Context, *v11.Id) (*GetRecordDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecordDetail not implemented")
}
func (UnimplementedAgreementRecordServiceServer) SignRecord(context.Context, *SignRecordRequest) (*v1.AgreementRecordSimpleView, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignRecord not implemented")
}
func (UnimplementedAgreementRecordServiceServer) SendSignRequest(context.Context, *SendSignRequestRequest) (*SendSignRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSignRequest not implemented")
}
func (UnimplementedAgreementRecordServiceServer) DeleteRecord(context.Context, *v11.Id) (*DeleteRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRecord not implemented")
}
func (UnimplementedAgreementRecordServiceServer) SignAgreement(context.Context, *SignAgreementParams) (*v1.AgreementRecordSimpleView, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignAgreement not implemented")
}
func (UnimplementedAgreementRecordServiceServer) mustEmbedUnimplementedAgreementRecordServiceServer() {
}

// UnsafeAgreementRecordServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AgreementRecordServiceServer will
// result in compilation errors.
type UnsafeAgreementRecordServiceServer interface {
	mustEmbedUnimplementedAgreementRecordServiceServer()
}

func RegisterAgreementRecordServiceServer(s grpc.ServiceRegistrar, srv AgreementRecordServiceServer) {
	s.RegisterService(&AgreementRecordService_ServiceDesc, srv)
}

func _AgreementRecordService_AddRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).AddRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementRecordService/AddRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).AddRecord(ctx, req.(*AddRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_UploadSignedFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadSignedFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).UploadSignedFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementRecordService/UploadSignedFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).UploadSignedFile(ctx, req.(*UploadSignedFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_GetRecordListForBusiness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecordListForBusinessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecordListForBusiness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementRecordService/GetRecordListForBusiness",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecordListForBusiness(ctx, req.(*GetRecordListForBusinessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_GetRecordListForCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecordListForCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecordListForCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementRecordService/GetRecordListForCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecordListForCustomer(ctx, req.(*GetRecordListForCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_GetRecentSignedAgreementList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecentSignedAgreementListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecentSignedAgreementList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementRecordService/GetRecentSignedAgreementList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecentSignedAgreementList(ctx, req.(*GetRecentSignedAgreementListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_GetRecordById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.Id)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecordById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementRecordService/GetRecordById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecordById(ctx, req.(*v11.Id))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_GetRecordByUUID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecordByUUIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecordByUUID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementRecordService/GetRecordByUUID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecordByUUID(ctx, req.(*GetRecordByUUIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_GetRecordByTarget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecordByTargetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecordByTarget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementRecordService/GetRecordByTarget",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecordByTarget(ctx, req.(*GetRecordByTargetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_GetRecordDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.Id)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecordDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementRecordService/GetRecordDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecordDetail(ctx, req.(*v11.Id))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_SignRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).SignRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementRecordService/SignRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).SignRecord(ctx, req.(*SignRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_SendSignRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSignRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).SendSignRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementRecordService/SendSignRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).SendSignRequest(ctx, req.(*SendSignRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_DeleteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.Id)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).DeleteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementRecordService/DeleteRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).DeleteRecord(ctx, req.(*v11.Id))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_SignAgreement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignAgreementParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).SignAgreement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.agreement.v1.AgreementRecordService/SignAgreement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).SignAgreement(ctx, req.(*SignAgreementParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AgreementRecordService_ServiceDesc is the grpc.ServiceDesc for AgreementRecordService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AgreementRecordService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.agreement.v1.AgreementRecordService",
	HandlerType: (*AgreementRecordServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddRecord",
			Handler:    _AgreementRecordService_AddRecord_Handler,
		},
		{
			MethodName: "UploadSignedFile",
			Handler:    _AgreementRecordService_UploadSignedFile_Handler,
		},
		{
			MethodName: "GetRecordListForBusiness",
			Handler:    _AgreementRecordService_GetRecordListForBusiness_Handler,
		},
		{
			MethodName: "GetRecordListForCustomer",
			Handler:    _AgreementRecordService_GetRecordListForCustomer_Handler,
		},
		{
			MethodName: "GetRecentSignedAgreementList",
			Handler:    _AgreementRecordService_GetRecentSignedAgreementList_Handler,
		},
		{
			MethodName: "GetRecordById",
			Handler:    _AgreementRecordService_GetRecordById_Handler,
		},
		{
			MethodName: "GetRecordByUUID",
			Handler:    _AgreementRecordService_GetRecordByUUID_Handler,
		},
		{
			MethodName: "GetRecordByTarget",
			Handler:    _AgreementRecordService_GetRecordByTarget_Handler,
		},
		{
			MethodName: "GetRecordDetail",
			Handler:    _AgreementRecordService_GetRecordDetail_Handler,
		},
		{
			MethodName: "SignRecord",
			Handler:    _AgreementRecordService_SignRecord_Handler,
		},
		{
			MethodName: "SendSignRequest",
			Handler:    _AgreementRecordService_SendSignRequest_Handler,
		},
		{
			MethodName: "DeleteRecord",
			Handler:    _AgreementRecordService_DeleteRecord_Handler,
		},
		{
			MethodName: "SignAgreement",
			Handler:    _AgreementRecordService_SignAgreement_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/agreement/v1/agreement_record_api.proto",
}
