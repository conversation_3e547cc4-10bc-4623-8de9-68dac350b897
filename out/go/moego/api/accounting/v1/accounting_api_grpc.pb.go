// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/accounting/v1/accounting_api.proto

package accountingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AccountingServiceClient is the client API for AccountingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccountingServiceClient interface {
	// get visibility
	GetVisibility(ctx context.Context, in *GetVisibilityParams, opts ...grpc.CallOption) (*GetVisibilityResult, error)
	// get onboarding status
	GetOnboardingStatus(ctx context.Context, in *GetOnboardingStatusParams, opts ...grpc.CallOption) (*GetOnboardingStatusResult, error)
	// get businesses
	GetBusinesses(ctx context.Context, in *GetBusinessesParams, opts ...grpc.CallOption) (*GetBusinessesResult, error)
	// set businesses
	SetBusinesses(ctx context.Context, in *SetBusinessesParams, opts ...grpc.CallOption) (*SetBusinessesResult, error)
	// add businesses
	AddBusinesses(ctx context.Context, in *AddBusinessesParams, opts ...grpc.CallOption) (*AddBusinessesResult, error)
	// remove businesses
	RemoveBusinesses(ctx context.Context, in *RemoveBusinessesParams, opts ...grpc.CallOption) (*RemoveBusinessesResult, error)
	// get auth token
	GetAuthToken(ctx context.Context, in *GetAuthTokenParams, opts ...grpc.CallOption) (*GetAuthTokenResult, error)
	// retry sync data
	RetrySync(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type accountingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountingServiceClient(cc grpc.ClientConnInterface) AccountingServiceClient {
	return &accountingServiceClient{cc}
}

func (c *accountingServiceClient) GetVisibility(ctx context.Context, in *GetVisibilityParams, opts ...grpc.CallOption) (*GetVisibilityResult, error) {
	out := new(GetVisibilityResult)
	err := c.cc.Invoke(ctx, "/moego.api.accounting.v1.AccountingService/GetVisibility", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) GetOnboardingStatus(ctx context.Context, in *GetOnboardingStatusParams, opts ...grpc.CallOption) (*GetOnboardingStatusResult, error) {
	out := new(GetOnboardingStatusResult)
	err := c.cc.Invoke(ctx, "/moego.api.accounting.v1.AccountingService/GetOnboardingStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) GetBusinesses(ctx context.Context, in *GetBusinessesParams, opts ...grpc.CallOption) (*GetBusinessesResult, error) {
	out := new(GetBusinessesResult)
	err := c.cc.Invoke(ctx, "/moego.api.accounting.v1.AccountingService/GetBusinesses", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) SetBusinesses(ctx context.Context, in *SetBusinessesParams, opts ...grpc.CallOption) (*SetBusinessesResult, error) {
	out := new(SetBusinessesResult)
	err := c.cc.Invoke(ctx, "/moego.api.accounting.v1.AccountingService/SetBusinesses", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) AddBusinesses(ctx context.Context, in *AddBusinessesParams, opts ...grpc.CallOption) (*AddBusinessesResult, error) {
	out := new(AddBusinessesResult)
	err := c.cc.Invoke(ctx, "/moego.api.accounting.v1.AccountingService/AddBusinesses", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) RemoveBusinesses(ctx context.Context, in *RemoveBusinessesParams, opts ...grpc.CallOption) (*RemoveBusinessesResult, error) {
	out := new(RemoveBusinessesResult)
	err := c.cc.Invoke(ctx, "/moego.api.accounting.v1.AccountingService/RemoveBusinesses", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) GetAuthToken(ctx context.Context, in *GetAuthTokenParams, opts ...grpc.CallOption) (*GetAuthTokenResult, error) {
	out := new(GetAuthTokenResult)
	err := c.cc.Invoke(ctx, "/moego.api.accounting.v1.AccountingService/GetAuthToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountingServiceClient) RetrySync(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.api.accounting.v1.AccountingService/RetrySync", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountingServiceServer is the server API for AccountingService service.
// All implementations must embed UnimplementedAccountingServiceServer
// for forward compatibility
type AccountingServiceServer interface {
	// get visibility
	GetVisibility(context.Context, *GetVisibilityParams) (*GetVisibilityResult, error)
	// get onboarding status
	GetOnboardingStatus(context.Context, *GetOnboardingStatusParams) (*GetOnboardingStatusResult, error)
	// get businesses
	GetBusinesses(context.Context, *GetBusinessesParams) (*GetBusinessesResult, error)
	// set businesses
	SetBusinesses(context.Context, *SetBusinessesParams) (*SetBusinessesResult, error)
	// add businesses
	AddBusinesses(context.Context, *AddBusinessesParams) (*AddBusinessesResult, error)
	// remove businesses
	RemoveBusinesses(context.Context, *RemoveBusinessesParams) (*RemoveBusinessesResult, error)
	// get auth token
	GetAuthToken(context.Context, *GetAuthTokenParams) (*GetAuthTokenResult, error)
	// retry sync data
	RetrySync(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	mustEmbedUnimplementedAccountingServiceServer()
}

// UnimplementedAccountingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAccountingServiceServer struct {
}

func (UnimplementedAccountingServiceServer) GetVisibility(context.Context, *GetVisibilityParams) (*GetVisibilityResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVisibility not implemented")
}
func (UnimplementedAccountingServiceServer) GetOnboardingStatus(context.Context, *GetOnboardingStatusParams) (*GetOnboardingStatusResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnboardingStatus not implemented")
}
func (UnimplementedAccountingServiceServer) GetBusinesses(context.Context, *GetBusinessesParams) (*GetBusinessesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBusinesses not implemented")
}
func (UnimplementedAccountingServiceServer) SetBusinesses(context.Context, *SetBusinessesParams) (*SetBusinessesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetBusinesses not implemented")
}
func (UnimplementedAccountingServiceServer) AddBusinesses(context.Context, *AddBusinessesParams) (*AddBusinessesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBusinesses not implemented")
}
func (UnimplementedAccountingServiceServer) RemoveBusinesses(context.Context, *RemoveBusinessesParams) (*RemoveBusinessesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveBusinesses not implemented")
}
func (UnimplementedAccountingServiceServer) GetAuthToken(context.Context, *GetAuthTokenParams) (*GetAuthTokenResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthToken not implemented")
}
func (UnimplementedAccountingServiceServer) RetrySync(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetrySync not implemented")
}
func (UnimplementedAccountingServiceServer) mustEmbedUnimplementedAccountingServiceServer() {}

// UnsafeAccountingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountingServiceServer will
// result in compilation errors.
type UnsafeAccountingServiceServer interface {
	mustEmbedUnimplementedAccountingServiceServer()
}

func RegisterAccountingServiceServer(s grpc.ServiceRegistrar, srv AccountingServiceServer) {
	s.RegisterService(&AccountingService_ServiceDesc, srv)
}

func _AccountingService_GetVisibility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVisibilityParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).GetVisibility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.accounting.v1.AccountingService/GetVisibility",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).GetVisibility(ctx, req.(*GetVisibilityParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_GetOnboardingStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnboardingStatusParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).GetOnboardingStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.accounting.v1.AccountingService/GetOnboardingStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).GetOnboardingStatus(ctx, req.(*GetOnboardingStatusParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_GetBusinesses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).GetBusinesses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.accounting.v1.AccountingService/GetBusinesses",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).GetBusinesses(ctx, req.(*GetBusinessesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_SetBusinesses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBusinessesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).SetBusinesses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.accounting.v1.AccountingService/SetBusinesses",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).SetBusinesses(ctx, req.(*SetBusinessesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_AddBusinesses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBusinessesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).AddBusinesses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.accounting.v1.AccountingService/AddBusinesses",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).AddBusinesses(ctx, req.(*AddBusinessesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_RemoveBusinesses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveBusinessesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).RemoveBusinesses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.accounting.v1.AccountingService/RemoveBusinesses",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).RemoveBusinesses(ctx, req.(*RemoveBusinessesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_GetAuthToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthTokenParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).GetAuthToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.accounting.v1.AccountingService/GetAuthToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).GetAuthToken(ctx, req.(*GetAuthTokenParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountingService_RetrySync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).RetrySync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.accounting.v1.AccountingService/RetrySync",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).RetrySync(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// AccountingService_ServiceDesc is the grpc.ServiceDesc for AccountingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AccountingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.accounting.v1.AccountingService",
	HandlerType: (*AccountingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetVisibility",
			Handler:    _AccountingService_GetVisibility_Handler,
		},
		{
			MethodName: "GetOnboardingStatus",
			Handler:    _AccountingService_GetOnboardingStatus_Handler,
		},
		{
			MethodName: "GetBusinesses",
			Handler:    _AccountingService_GetBusinesses_Handler,
		},
		{
			MethodName: "SetBusinesses",
			Handler:    _AccountingService_SetBusinesses_Handler,
		},
		{
			MethodName: "AddBusinesses",
			Handler:    _AccountingService_AddBusinesses_Handler,
		},
		{
			MethodName: "RemoveBusinesses",
			Handler:    _AccountingService_RemoveBusinesses_Handler,
		},
		{
			MethodName: "GetAuthToken",
			Handler:    _AccountingService_GetAuthToken_Handler,
		},
		{
			MethodName: "RetrySync",
			Handler:    _AccountingService_RetrySync_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/accounting/v1/accounting_api.proto",
}
