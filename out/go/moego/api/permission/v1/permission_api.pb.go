// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/permission/v1/permission_api.proto

package permissionapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/permission/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get role list request
type GetRoleListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetRoleListParams) Reset() {
	*x = GetRoleListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoleListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoleListParams) ProtoMessage() {}

func (x *GetRoleListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoleListParams.ProtoReflect.Descriptor instead.
func (*GetRoleListParams) Descriptor() ([]byte, []int) {
	return file_moego_api_permission_v1_permission_api_proto_rawDescGZIP(), []int{0}
}

// get role list response
type GetRoleListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role list
	RoleList []*v1.RoleBriefView `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
}

func (x *GetRoleListResult) Reset() {
	*x = GetRoleListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoleListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoleListResult) ProtoMessage() {}

func (x *GetRoleListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoleListResult.ProtoReflect.Descriptor instead.
func (*GetRoleListResult) Descriptor() ([]byte, []int) {
	return file_moego_api_permission_v1_permission_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetRoleListResult) GetRoleList() []*v1.RoleBriefView {
	if x != nil {
		return x.RoleList
	}
	return nil
}

// get role detail request
type GetRoleDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role id, 0 for owner
	RoleId int64 `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
}

func (x *GetRoleDetailParams) Reset() {
	*x = GetRoleDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoleDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoleDetailParams) ProtoMessage() {}

func (x *GetRoleDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoleDetailParams.ProtoReflect.Descriptor instead.
func (*GetRoleDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_api_permission_v1_permission_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetRoleDetailParams) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

// get role detail response
type GetRoleDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role detail
	RoleDetail *v1.RoleModel `protobuf:"bytes,1,opt,name=role_detail,json=roleDetail,proto3" json:"role_detail,omitempty"`
}

func (x *GetRoleDetailResult) Reset() {
	*x = GetRoleDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoleDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoleDetailResult) ProtoMessage() {}

func (x *GetRoleDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoleDetailResult.ProtoReflect.Descriptor instead.
func (*GetRoleDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_api_permission_v1_permission_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetRoleDetailResult) GetRoleDetail() *v1.RoleModel {
	if x != nil {
		return x.RoleDetail
	}
	return nil
}

// create role request
type CreateRoleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role name
	RoleName string `protobuf:"bytes,1,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
}

func (x *CreateRoleParams) Reset() {
	*x = CreateRoleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRoleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoleParams) ProtoMessage() {}

func (x *CreateRoleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoleParams.ProtoReflect.Descriptor instead.
func (*CreateRoleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_permission_v1_permission_api_proto_rawDescGZIP(), []int{4}
}

func (x *CreateRoleParams) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

// create role response
type CreateRoleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role list after created
	RoleList []*v1.RoleBriefView `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	// role id
	RoleId int64 `protobuf:"varint,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
}

func (x *CreateRoleResult) Reset() {
	*x = CreateRoleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRoleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoleResult) ProtoMessage() {}

func (x *CreateRoleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoleResult.ProtoReflect.Descriptor instead.
func (*CreateRoleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_permission_v1_permission_api_proto_rawDescGZIP(), []int{5}
}

func (x *CreateRoleResult) GetRoleList() []*v1.RoleBriefView {
	if x != nil {
		return x.RoleList
	}
	return nil
}

func (x *CreateRoleResult) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

// update role request
type UpdateRoleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role id
	RoleId int64 `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// role name
	RoleName string `protobuf:"bytes,2,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
}

func (x *UpdateRoleParams) Reset() {
	*x = UpdateRoleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRoleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoleParams) ProtoMessage() {}

func (x *UpdateRoleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoleParams.ProtoReflect.Descriptor instead.
func (*UpdateRoleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_permission_v1_permission_api_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateRoleParams) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *UpdateRoleParams) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

// update role response
type UpdateRoleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role list after updated
	RoleList []*v1.RoleBriefView `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
}

func (x *UpdateRoleResult) Reset() {
	*x = UpdateRoleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRoleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoleResult) ProtoMessage() {}

func (x *UpdateRoleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoleResult.ProtoReflect.Descriptor instead.
func (*UpdateRoleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_permission_v1_permission_api_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateRoleResult) GetRoleList() []*v1.RoleBriefView {
	if x != nil {
		return x.RoleList
	}
	return nil
}

// delete role request
type DeleteRoleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role id
	RoleId int64 `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
}

func (x *DeleteRoleParams) Reset() {
	*x = DeleteRoleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRoleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRoleParams) ProtoMessage() {}

func (x *DeleteRoleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRoleParams.ProtoReflect.Descriptor instead.
func (*DeleteRoleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_permission_v1_permission_api_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteRoleParams) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

// delete role response
type DeleteRoleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role list after deleted
	RoleList []*v1.RoleBriefView `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
}

func (x *DeleteRoleResult) Reset() {
	*x = DeleteRoleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRoleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRoleResult) ProtoMessage() {}

func (x *DeleteRoleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRoleResult.ProtoReflect.Descriptor instead.
func (*DeleteRoleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_permission_v1_permission_api_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteRoleResult) GetRoleList() []*v1.RoleBriefView {
	if x != nil {
		return x.RoleList
	}
	return nil
}

// duplicate role request
type DuplicateRoleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role id
	RoleId int64 `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// role name
	RoleName string `protobuf:"bytes,2,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
}

func (x *DuplicateRoleParams) Reset() {
	*x = DuplicateRoleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DuplicateRoleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DuplicateRoleParams) ProtoMessage() {}

func (x *DuplicateRoleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DuplicateRoleParams.ProtoReflect.Descriptor instead.
func (*DuplicateRoleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_permission_v1_permission_api_proto_rawDescGZIP(), []int{10}
}

func (x *DuplicateRoleParams) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *DuplicateRoleParams) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

// duplicate role response
type DuplicateRoleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role list after duplicated
	RoleList []*v1.RoleBriefView `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
}

func (x *DuplicateRoleResult) Reset() {
	*x = DuplicateRoleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DuplicateRoleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DuplicateRoleResult) ProtoMessage() {}

func (x *DuplicateRoleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DuplicateRoleResult.ProtoReflect.Descriptor instead.
func (*DuplicateRoleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_permission_v1_permission_api_proto_rawDescGZIP(), []int{11}
}

func (x *DuplicateRoleResult) GetRoleList() []*v1.RoleBriefView {
	if x != nil {
		return x.RoleList
	}
	return nil
}

// edit permissions request
type EditPermissionsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role id
	RoleId int64 `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// permission list in a category
	PermissionCategoryList []*v1.EditCategoryPermissionDef `protobuf:"bytes,2,rep,name=permission_category_list,json=permissionCategoryList,proto3" json:"permission_category_list,omitempty"`
}

func (x *EditPermissionsParams) Reset() {
	*x = EditPermissionsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditPermissionsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditPermissionsParams) ProtoMessage() {}

func (x *EditPermissionsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditPermissionsParams.ProtoReflect.Descriptor instead.
func (*EditPermissionsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_permission_v1_permission_api_proto_rawDescGZIP(), []int{12}
}

func (x *EditPermissionsParams) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *EditPermissionsParams) GetPermissionCategoryList() []*v1.EditCategoryPermissionDef {
	if x != nil {
		return x.PermissionCategoryList
	}
	return nil
}

// edit permissions response
type EditPermissionsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// permission list after edited
	PermissionCategoryList []*v1.PermissionCategoryModel `protobuf:"bytes,1,rep,name=permission_category_list,json=permissionCategoryList,proto3" json:"permission_category_list,omitempty"`
}

func (x *EditPermissionsResult) Reset() {
	*x = EditPermissionsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditPermissionsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditPermissionsResult) ProtoMessage() {}

func (x *EditPermissionsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_permission_v1_permission_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditPermissionsResult.ProtoReflect.Descriptor instead.
func (*EditPermissionsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_permission_v1_permission_api_proto_rawDescGZIP(), []int{13}
}

func (x *EditPermissionsResult) GetPermissionCategoryList() []*v1.PermissionCategoryModel {
	if x != nil {
		return x.PermissionCategoryList
	}
	return nil
}

var File_moego_api_permission_v1_permission_api_proto protoreflect.FileDescriptor

var file_moego_api_permission_v1_permission_api_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x64,
	0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x13, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x5b, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x46, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08,
	0x72, 0x6f, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x37, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x52,
	0x6f, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x20, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49,
	0x64, 0x22, 0x5d, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a, 0x0b, 0x72, 0x6f, 0x6c, 0x65,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x72, 0x6f, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x22, 0x3b, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x27, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01,
	0x18, 0xff, 0x01, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x73, 0x0a,
	0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x46, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6c,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65,
	0x49, 0x64, 0x22, 0x5d, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0x5a, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x34, 0x0a,
	0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x20, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x72, 0x6f, 0x6c,
	0x65, 0x49, 0x64, 0x22, 0x5a, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x72, 0x69, 0x65,
	0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x5f, 0x0a, 0x13, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x5d, 0x0a, 0x13, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a, 0x09, 0x72, 0x6f, 0x6c, 0x65, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x72, 0x69, 0x65,
	0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0xaa, 0x01, 0x0a, 0x15, 0x45, 0x64, 0x69, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x07, 0x72, 0x6f, 0x6c,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x6f, 0x0a, 0x18, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x66, 0x52, 0x16, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x86, 0x01, 0x0a,
	0x15, 0x45, 0x64, 0x69, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6d, 0x0a, 0x18, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x16, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x4c, 0x69, 0x73, 0x74, 0x32, 0x81, 0x06, 0x0a, 0x11, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x67, 0x0a, 0x0b, 0x47,
	0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x00, 0x12, 0x6d, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x52, 0x6f, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x0a, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12,
	0x64, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f,
	0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x6d, 0x0a, 0x0d, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x00, 0x12, 0x73, 0x0a, 0x0f, 0x45, 0x64, 0x69, 0x74, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x42, 0x81, 0x01, 0x0a, 0x1f, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_permission_v1_permission_api_proto_rawDescOnce sync.Once
	file_moego_api_permission_v1_permission_api_proto_rawDescData = file_moego_api_permission_v1_permission_api_proto_rawDesc
)

func file_moego_api_permission_v1_permission_api_proto_rawDescGZIP() []byte {
	file_moego_api_permission_v1_permission_api_proto_rawDescOnce.Do(func() {
		file_moego_api_permission_v1_permission_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_permission_v1_permission_api_proto_rawDescData)
	})
	return file_moego_api_permission_v1_permission_api_proto_rawDescData
}

var file_moego_api_permission_v1_permission_api_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_moego_api_permission_v1_permission_api_proto_goTypes = []interface{}{
	(*GetRoleListParams)(nil),            // 0: moego.api.permission.v1.GetRoleListParams
	(*GetRoleListResult)(nil),            // 1: moego.api.permission.v1.GetRoleListResult
	(*GetRoleDetailParams)(nil),          // 2: moego.api.permission.v1.GetRoleDetailParams
	(*GetRoleDetailResult)(nil),          // 3: moego.api.permission.v1.GetRoleDetailResult
	(*CreateRoleParams)(nil),             // 4: moego.api.permission.v1.CreateRoleParams
	(*CreateRoleResult)(nil),             // 5: moego.api.permission.v1.CreateRoleResult
	(*UpdateRoleParams)(nil),             // 6: moego.api.permission.v1.UpdateRoleParams
	(*UpdateRoleResult)(nil),             // 7: moego.api.permission.v1.UpdateRoleResult
	(*DeleteRoleParams)(nil),             // 8: moego.api.permission.v1.DeleteRoleParams
	(*DeleteRoleResult)(nil),             // 9: moego.api.permission.v1.DeleteRoleResult
	(*DuplicateRoleParams)(nil),          // 10: moego.api.permission.v1.DuplicateRoleParams
	(*DuplicateRoleResult)(nil),          // 11: moego.api.permission.v1.DuplicateRoleResult
	(*EditPermissionsParams)(nil),        // 12: moego.api.permission.v1.EditPermissionsParams
	(*EditPermissionsResult)(nil),        // 13: moego.api.permission.v1.EditPermissionsResult
	(*v1.RoleBriefView)(nil),             // 14: moego.models.permission.v1.RoleBriefView
	(*v1.RoleModel)(nil),                 // 15: moego.models.permission.v1.RoleModel
	(*v1.EditCategoryPermissionDef)(nil), // 16: moego.models.permission.v1.EditCategoryPermissionDef
	(*v1.PermissionCategoryModel)(nil),   // 17: moego.models.permission.v1.PermissionCategoryModel
}
var file_moego_api_permission_v1_permission_api_proto_depIdxs = []int32{
	14, // 0: moego.api.permission.v1.GetRoleListResult.role_list:type_name -> moego.models.permission.v1.RoleBriefView
	15, // 1: moego.api.permission.v1.GetRoleDetailResult.role_detail:type_name -> moego.models.permission.v1.RoleModel
	14, // 2: moego.api.permission.v1.CreateRoleResult.role_list:type_name -> moego.models.permission.v1.RoleBriefView
	14, // 3: moego.api.permission.v1.UpdateRoleResult.role_list:type_name -> moego.models.permission.v1.RoleBriefView
	14, // 4: moego.api.permission.v1.DeleteRoleResult.role_list:type_name -> moego.models.permission.v1.RoleBriefView
	14, // 5: moego.api.permission.v1.DuplicateRoleResult.role_list:type_name -> moego.models.permission.v1.RoleBriefView
	16, // 6: moego.api.permission.v1.EditPermissionsParams.permission_category_list:type_name -> moego.models.permission.v1.EditCategoryPermissionDef
	17, // 7: moego.api.permission.v1.EditPermissionsResult.permission_category_list:type_name -> moego.models.permission.v1.PermissionCategoryModel
	0,  // 8: moego.api.permission.v1.PermissionService.GetRoleList:input_type -> moego.api.permission.v1.GetRoleListParams
	2,  // 9: moego.api.permission.v1.PermissionService.GetRoleDetail:input_type -> moego.api.permission.v1.GetRoleDetailParams
	4,  // 10: moego.api.permission.v1.PermissionService.CreateRole:input_type -> moego.api.permission.v1.CreateRoleParams
	6,  // 11: moego.api.permission.v1.PermissionService.UpdateRole:input_type -> moego.api.permission.v1.UpdateRoleParams
	8,  // 12: moego.api.permission.v1.PermissionService.DeleteRole:input_type -> moego.api.permission.v1.DeleteRoleParams
	10, // 13: moego.api.permission.v1.PermissionService.DuplicateRole:input_type -> moego.api.permission.v1.DuplicateRoleParams
	12, // 14: moego.api.permission.v1.PermissionService.EditPermissions:input_type -> moego.api.permission.v1.EditPermissionsParams
	1,  // 15: moego.api.permission.v1.PermissionService.GetRoleList:output_type -> moego.api.permission.v1.GetRoleListResult
	3,  // 16: moego.api.permission.v1.PermissionService.GetRoleDetail:output_type -> moego.api.permission.v1.GetRoleDetailResult
	5,  // 17: moego.api.permission.v1.PermissionService.CreateRole:output_type -> moego.api.permission.v1.CreateRoleResult
	7,  // 18: moego.api.permission.v1.PermissionService.UpdateRole:output_type -> moego.api.permission.v1.UpdateRoleResult
	9,  // 19: moego.api.permission.v1.PermissionService.DeleteRole:output_type -> moego.api.permission.v1.DeleteRoleResult
	11, // 20: moego.api.permission.v1.PermissionService.DuplicateRole:output_type -> moego.api.permission.v1.DuplicateRoleResult
	13, // 21: moego.api.permission.v1.PermissionService.EditPermissions:output_type -> moego.api.permission.v1.EditPermissionsResult
	15, // [15:22] is the sub-list for method output_type
	8,  // [8:15] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_moego_api_permission_v1_permission_api_proto_init() }
func file_moego_api_permission_v1_permission_api_proto_init() {
	if File_moego_api_permission_v1_permission_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_permission_v1_permission_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoleListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_permission_v1_permission_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoleListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_permission_v1_permission_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoleDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_permission_v1_permission_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoleDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_permission_v1_permission_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRoleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_permission_v1_permission_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRoleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_permission_v1_permission_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRoleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_permission_v1_permission_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRoleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_permission_v1_permission_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRoleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_permission_v1_permission_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRoleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_permission_v1_permission_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DuplicateRoleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_permission_v1_permission_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DuplicateRoleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_permission_v1_permission_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditPermissionsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_permission_v1_permission_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditPermissionsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_permission_v1_permission_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_permission_v1_permission_api_proto_goTypes,
		DependencyIndexes: file_moego_api_permission_v1_permission_api_proto_depIdxs,
		MessageInfos:      file_moego_api_permission_v1_permission_api_proto_msgTypes,
	}.Build()
	File_moego_api_permission_v1_permission_api_proto = out.File
	file_moego_api_permission_v1_permission_api_proto_rawDesc = nil
	file_moego_api_permission_v1_permission_api_proto_goTypes = nil
	file_moego_api_permission_v1_permission_api_proto_depIdxs = nil
}
