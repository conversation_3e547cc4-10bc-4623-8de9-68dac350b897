// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/online_booking/v1/customer_availability_api.proto

package onlinebookingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CustomerAvailabilityServiceClient is the client API for CustomerAvailabilityService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CustomerAvailabilityServiceClient interface {
	// set customer block status
	SetCustomerBlockStatus(ctx context.Context, in *SetCustomerBlockStatusParams, opts ...grpc.CallOption) (*SetCustomerBlockStatusResult, error)
}

type customerAvailabilityServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCustomerAvailabilityServiceClient(cc grpc.ClientConnInterface) CustomerAvailabilityServiceClient {
	return &customerAvailabilityServiceClient{cc}
}

func (c *customerAvailabilityServiceClient) SetCustomerBlockStatus(ctx context.Context, in *SetCustomerBlockStatusParams, opts ...grpc.CallOption) (*SetCustomerBlockStatusResult, error) {
	out := new(SetCustomerBlockStatusResult)
	err := c.cc.Invoke(ctx, "/moego.api.online_booking.v1.CustomerAvailabilityService/SetCustomerBlockStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CustomerAvailabilityServiceServer is the server API for CustomerAvailabilityService service.
// All implementations must embed UnimplementedCustomerAvailabilityServiceServer
// for forward compatibility
type CustomerAvailabilityServiceServer interface {
	// set customer block status
	SetCustomerBlockStatus(context.Context, *SetCustomerBlockStatusParams) (*SetCustomerBlockStatusResult, error)
	mustEmbedUnimplementedCustomerAvailabilityServiceServer()
}

// UnimplementedCustomerAvailabilityServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCustomerAvailabilityServiceServer struct {
}

func (UnimplementedCustomerAvailabilityServiceServer) SetCustomerBlockStatus(context.Context, *SetCustomerBlockStatusParams) (*SetCustomerBlockStatusResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCustomerBlockStatus not implemented")
}
func (UnimplementedCustomerAvailabilityServiceServer) mustEmbedUnimplementedCustomerAvailabilityServiceServer() {
}

// UnsafeCustomerAvailabilityServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CustomerAvailabilityServiceServer will
// result in compilation errors.
type UnsafeCustomerAvailabilityServiceServer interface {
	mustEmbedUnimplementedCustomerAvailabilityServiceServer()
}

func RegisterCustomerAvailabilityServiceServer(s grpc.ServiceRegistrar, srv CustomerAvailabilityServiceServer) {
	s.RegisterService(&CustomerAvailabilityService_ServiceDesc, srv)
}

func _CustomerAvailabilityService_SetCustomerBlockStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCustomerBlockStatusParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerAvailabilityServiceServer).SetCustomerBlockStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.online_booking.v1.CustomerAvailabilityService/SetCustomerBlockStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerAvailabilityServiceServer).SetCustomerBlockStatus(ctx, req.(*SetCustomerBlockStatusParams))
	}
	return interceptor(ctx, in, info, handler)
}

// CustomerAvailabilityService_ServiceDesc is the grpc.ServiceDesc for CustomerAvailabilityService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CustomerAvailabilityService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.online_booking.v1.CustomerAvailabilityService",
	HandlerType: (*CustomerAvailabilityServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetCustomerBlockStatus",
			Handler:    _CustomerAvailabilityService_SetCustomerBlockStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/online_booking/v1/customer_availability_api.proto",
}
