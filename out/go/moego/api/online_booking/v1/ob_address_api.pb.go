// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/online_booking/v1/ob_address_api.proto

package onlinebookingapipb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Parameters for updating an address.
// Fields are optional to allow for partial updates.
type UpsertAddressParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Online booking name or domain.
	//
	// Types that are assignable to BusinessIdentifier:
	//
	//	*UpsertAddressParams_Name
	//	*UpsertAddressParams_Domain
	BusinessIdentifier isUpsertAddressParams_BusinessIdentifier `protobuf_oneof:"business_identifier"`
	// Unique identifier for the address, must be greater than 0 if provided.
	// If not provided, a new address will be created.
	Id *int64 `protobuf:"varint,3,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// Primary address line, required if provided.
	Address1 *string `protobuf:"bytes,4,opt,name=address1,proto3,oneof" json:"address1,omitempty"`
	// Secondary address line, optional and can be empty.
	Address2 *string `protobuf:"bytes,5,opt,name=address2,proto3,oneof" json:"address2,omitempty"`
	// City name, required if provided.
	City *string `protobuf:"bytes,6,opt,name=city,proto3,oneof" json:"city,omitempty"`
	// Country name, required if provided.
	Country *string `protobuf:"bytes,7,opt,name=country,proto3,oneof" json:"country,omitempty"`
	// Latitude, must be within valid global coordinates if provided.
	Lat *string `protobuf:"bytes,8,opt,name=lat,proto3,oneof" json:"lat,omitempty"`
	// Longitude, must be within valid global coordinates if provided.
	Lng *string `protobuf:"bytes,9,opt,name=lng,proto3,oneof" json:"lng,omitempty"`
	// State or province.
	State *string `protobuf:"bytes,10,opt,name=state,proto3,oneof" json:"state,omitempty"`
	// Postal or ZIP code, required if provided.
	Zipcode *string `protobuf:"bytes,11,opt,name=zipcode,proto3,oneof" json:"zipcode,omitempty"`
	// Whether this address is the primary address for the customer.
	IsPrimary *int32 `protobuf:"varint,12,opt,name=is_primary,json=isPrimary,proto3,oneof" json:"is_primary,omitempty"`
	// Whether this address is the profile request address.
	IsProfileRequestAddress *bool `protobuf:"varint,13,opt,name=is_profile_request_address,json=isProfileRequestAddress,proto3,oneof" json:"is_profile_request_address,omitempty"`
}

func (x *UpsertAddressParams) Reset() {
	*x = UpsertAddressParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_address_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertAddressParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertAddressParams) ProtoMessage() {}

func (x *UpsertAddressParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_address_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertAddressParams.ProtoReflect.Descriptor instead.
func (*UpsertAddressParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_address_api_proto_rawDescGZIP(), []int{0}
}

func (m *UpsertAddressParams) GetBusinessIdentifier() isUpsertAddressParams_BusinessIdentifier {
	if m != nil {
		return m.BusinessIdentifier
	}
	return nil
}

func (x *UpsertAddressParams) GetName() string {
	if x, ok := x.GetBusinessIdentifier().(*UpsertAddressParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *UpsertAddressParams) GetDomain() string {
	if x, ok := x.GetBusinessIdentifier().(*UpsertAddressParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *UpsertAddressParams) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *UpsertAddressParams) GetAddress1() string {
	if x != nil && x.Address1 != nil {
		return *x.Address1
	}
	return ""
}

func (x *UpsertAddressParams) GetAddress2() string {
	if x != nil && x.Address2 != nil {
		return *x.Address2
	}
	return ""
}

func (x *UpsertAddressParams) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *UpsertAddressParams) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

func (x *UpsertAddressParams) GetLat() string {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return ""
}

func (x *UpsertAddressParams) GetLng() string {
	if x != nil && x.Lng != nil {
		return *x.Lng
	}
	return ""
}

func (x *UpsertAddressParams) GetState() string {
	if x != nil && x.State != nil {
		return *x.State
	}
	return ""
}

func (x *UpsertAddressParams) GetZipcode() string {
	if x != nil && x.Zipcode != nil {
		return *x.Zipcode
	}
	return ""
}

func (x *UpsertAddressParams) GetIsPrimary() int32 {
	if x != nil && x.IsPrimary != nil {
		return *x.IsPrimary
	}
	return 0
}

func (x *UpsertAddressParams) GetIsProfileRequestAddress() bool {
	if x != nil && x.IsProfileRequestAddress != nil {
		return *x.IsProfileRequestAddress
	}
	return false
}

type isUpsertAddressParams_BusinessIdentifier interface {
	isUpsertAddressParams_BusinessIdentifier()
}

type UpsertAddressParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type UpsertAddressParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*UpsertAddressParams_Name) isUpsertAddressParams_BusinessIdentifier() {}

func (*UpsertAddressParams_Domain) isUpsertAddressParams_BusinessIdentifier() {}

// Result of an address upsert.
type UpsertAddressResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Id of the address.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Unique identifier for the customer associated with this address.
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// Primary address line.
	Address1 string `protobuf:"bytes,3,opt,name=address1,proto3" json:"address1,omitempty"`
	// Secondary address line.
	Address2 string `protobuf:"bytes,4,opt,name=address2,proto3" json:"address2,omitempty"`
	// City name.
	City string `protobuf:"bytes,5,opt,name=city,proto3" json:"city,omitempty"`
	// Country name.
	Country string `protobuf:"bytes,6,opt,name=country,proto3" json:"country,omitempty"`
	// Latitude.
	Lat string `protobuf:"bytes,7,opt,name=lat,proto3" json:"lat,omitempty"`
	// Longitude.
	Lng string `protobuf:"bytes,8,opt,name=lng,proto3" json:"lng,omitempty"`
	// State or province.
	State string `protobuf:"bytes,9,opt,name=state,proto3" json:"state,omitempty"`
	// Postal or ZIP code.
	Zipcode string `protobuf:"bytes,10,opt,name=zipcode,proto3" json:"zipcode,omitempty"`
	// Whether this address is the primary address for the customer.
	IsPrimary int32 `protobuf:"varint,11,opt,name=is_primary,json=isPrimary,proto3" json:"is_primary,omitempty"`
	// Whether this address is the profile request address,
	// if true, id uses profile_request_address id,
	// if false, id uses business_customer_address id.
	IsProfileRequestAddress bool `protobuf:"varint,12,opt,name=is_profile_request_address,json=isProfileRequestAddress,proto3" json:"is_profile_request_address,omitempty"`
}

func (x *UpsertAddressResult) Reset() {
	*x = UpsertAddressResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_address_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertAddressResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertAddressResult) ProtoMessage() {}

func (x *UpsertAddressResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_address_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertAddressResult.ProtoReflect.Descriptor instead.
func (*UpsertAddressResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_address_api_proto_rawDescGZIP(), []int{1}
}

func (x *UpsertAddressResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpsertAddressResult) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *UpsertAddressResult) GetAddress1() string {
	if x != nil {
		return x.Address1
	}
	return ""
}

func (x *UpsertAddressResult) GetAddress2() string {
	if x != nil {
		return x.Address2
	}
	return ""
}

func (x *UpsertAddressResult) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *UpsertAddressResult) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *UpsertAddressResult) GetLat() string {
	if x != nil {
		return x.Lat
	}
	return ""
}

func (x *UpsertAddressResult) GetLng() string {
	if x != nil {
		return x.Lng
	}
	return ""
}

func (x *UpsertAddressResult) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *UpsertAddressResult) GetZipcode() string {
	if x != nil {
		return x.Zipcode
	}
	return ""
}

func (x *UpsertAddressResult) GetIsPrimary() int32 {
	if x != nil {
		return x.IsPrimary
	}
	return 0
}

func (x *UpsertAddressResult) GetIsProfileRequestAddress() bool {
	if x != nil {
		return x.IsProfileRequestAddress
	}
	return false
}

var File_moego_api_online_booking_v1_ob_address_api_proto protoreflect.FileDescriptor

var file_moego_api_online_booking_v1_ob_address_api_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa9, 0x05, 0x0a, 0x13, 0x55, 0x70, 0x73,
	0x65, 0x72, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x12, 0x1c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x29,
	0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x02, 0x52, 0x08, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x08, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x03, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x32, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x04, 0x52, 0x04,
	0x63, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18,
	0xff, 0x01, 0x48, 0x05, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x88, 0x01, 0x01,
	0x12, 0x1e, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x48, 0x06, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x1e, 0x0a, 0x03, 0x6c, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x48, 0x07, 0x52, 0x03, 0x6c, 0x6e, 0x67, 0x88, 0x01, 0x01,
	0x12, 0x23, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x08, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48,
	0x09, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a,
	0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x30, 0x00, 0x30, 0x01, 0x48, 0x0a, 0x52, 0x09,
	0x69, 0x73, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x1a,
	0x69, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x0b, 0x52, 0x17, 0x69, 0x73, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x42, 0x1a,
	0x0a, 0x13, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69,
	0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x42, 0x0b,
	0x0a, 0x09, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x42, 0x07, 0x0a, 0x05, 0x5f,
	0x63, 0x69, 0x74, 0x79, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x42, 0x06, 0x0a, 0x04, 0x5f, 0x6c, 0x61, 0x74, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x6c, 0x6e, 0x67,
	0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x7a,
	0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x72,
	0x69, 0x6d, 0x61, 0x72, 0x79, 0x42, 0x1d, 0x0a, 0x1b, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x22, 0xdc, 0x02, 0x0a, 0x13, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x32, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6c, 0x6e, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72,
	0x69, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x73, 0x50,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x3b, 0x0a, 0x1a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x69, 0x73, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x32, 0x8c, 0x01, 0x0a, 0x10, 0x4f, 0x42, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x78, 0x0a, 0x0d, 0x55, 0x70, 0x73, 0x65,
	0x72, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x03, 0x88,
	0x02, 0x01, 0x42, 0x8c, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_online_booking_v1_ob_address_api_proto_rawDescOnce sync.Once
	file_moego_api_online_booking_v1_ob_address_api_proto_rawDescData = file_moego_api_online_booking_v1_ob_address_api_proto_rawDesc
)

func file_moego_api_online_booking_v1_ob_address_api_proto_rawDescGZIP() []byte {
	file_moego_api_online_booking_v1_ob_address_api_proto_rawDescOnce.Do(func() {
		file_moego_api_online_booking_v1_ob_address_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_online_booking_v1_ob_address_api_proto_rawDescData)
	})
	return file_moego_api_online_booking_v1_ob_address_api_proto_rawDescData
}

var file_moego_api_online_booking_v1_ob_address_api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_api_online_booking_v1_ob_address_api_proto_goTypes = []interface{}{
	(*UpsertAddressParams)(nil), // 0: moego.api.online_booking.v1.UpsertAddressParams
	(*UpsertAddressResult)(nil), // 1: moego.api.online_booking.v1.UpsertAddressResult
}
var file_moego_api_online_booking_v1_ob_address_api_proto_depIdxs = []int32{
	0, // 0: moego.api.online_booking.v1.OBAddressService.UpsertAddress:input_type -> moego.api.online_booking.v1.UpsertAddressParams
	1, // 1: moego.api.online_booking.v1.OBAddressService.UpsertAddress:output_type -> moego.api.online_booking.v1.UpsertAddressResult
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_api_online_booking_v1_ob_address_api_proto_init() }
func file_moego_api_online_booking_v1_ob_address_api_proto_init() {
	if File_moego_api_online_booking_v1_ob_address_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_online_booking_v1_ob_address_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertAddressParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_address_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertAddressResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_online_booking_v1_ob_address_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*UpsertAddressParams_Name)(nil),
		(*UpsertAddressParams_Domain)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_online_booking_v1_ob_address_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_online_booking_v1_ob_address_api_proto_goTypes,
		DependencyIndexes: file_moego_api_online_booking_v1_ob_address_api_proto_depIdxs,
		MessageInfos:      file_moego_api_online_booking_v1_ob_address_api_proto_msgTypes,
	}.Build()
	File_moego_api_online_booking_v1_ob_address_api_proto = out.File
	file_moego_api_online_booking_v1_ob_address_api_proto_rawDesc = nil
	file_moego_api_online_booking_v1_ob_address_api_proto_goTypes = nil
	file_moego_api_online_booking_v1_ob_address_api_proto_depIdxs = nil
}
