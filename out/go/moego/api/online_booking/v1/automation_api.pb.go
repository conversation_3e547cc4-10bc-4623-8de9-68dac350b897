// @since 2024-10-11 12:09:44
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/online_booking/v1/automation_api.proto

package onlinebookingapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get automation setting params
type GetAutomationSettingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// service item types
	ServiceItemType v1.ServiceItemType `protobuf:"varint,2,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
}

func (x *GetAutomationSettingParams) Reset() {
	*x = GetAutomationSettingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_automation_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAutomationSettingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAutomationSettingParams) ProtoMessage() {}

func (x *GetAutomationSettingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_automation_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAutomationSettingParams.ProtoReflect.Descriptor instead.
func (*GetAutomationSettingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_automation_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetAutomationSettingParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetAutomationSettingParams) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

// get automation setting result
type GetAutomationSettingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the automation
	AutomationSetting *v11.AutomationSettingModel `protobuf:"bytes,1,opt,name=automation_setting,json=automationSetting,proto3" json:"automation_setting,omitempty"`
}

func (x *GetAutomationSettingResult) Reset() {
	*x = GetAutomationSettingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_automation_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAutomationSettingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAutomationSettingResult) ProtoMessage() {}

func (x *GetAutomationSettingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_automation_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAutomationSettingResult.ProtoReflect.Descriptor instead.
func (*GetAutomationSettingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_automation_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetAutomationSettingResult) GetAutomationSetting() *v11.AutomationSettingModel {
	if x != nil {
		return x.AutomationSetting
	}
	return nil
}

// create automation setting params
type UpdateAutomationSettingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// service item types
	ServiceItemType v1.ServiceItemType `protobuf:"varint,2,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// enable auto accept
	EnableAutoAccept bool `protobuf:"varint,3,opt,name=enable_auto_accept,json=enableAutoAccept,proto3" json:"enable_auto_accept,omitempty"`
	// auto accept condition
	AutoAcceptCondition *v11.AutomationConditionDef `protobuf:"bytes,4,opt,name=auto_accept_condition,json=autoAcceptCondition,proto3" json:"auto_accept_condition,omitempty"`
}

func (x *UpdateAutomationSettingParams) Reset() {
	*x = UpdateAutomationSettingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_automation_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAutomationSettingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAutomationSettingParams) ProtoMessage() {}

func (x *UpdateAutomationSettingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_automation_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAutomationSettingParams.ProtoReflect.Descriptor instead.
func (*UpdateAutomationSettingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_automation_api_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateAutomationSettingParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateAutomationSettingParams) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *UpdateAutomationSettingParams) GetEnableAutoAccept() bool {
	if x != nil {
		return x.EnableAutoAccept
	}
	return false
}

func (x *UpdateAutomationSettingParams) GetAutoAcceptCondition() *v11.AutomationConditionDef {
	if x != nil {
		return x.AutoAcceptCondition
	}
	return nil
}

// create automation setting result
type UpdateAutomationSettingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the updated automation
	AutomationSetting *v11.AutomationSettingModel `protobuf:"bytes,1,opt,name=automation_setting,json=automationSetting,proto3" json:"automation_setting,omitempty"`
}

func (x *UpdateAutomationSettingResult) Reset() {
	*x = UpdateAutomationSettingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_automation_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAutomationSettingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAutomationSettingResult) ProtoMessage() {}

func (x *UpdateAutomationSettingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_automation_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAutomationSettingResult.ProtoReflect.Descriptor instead.
func (*UpdateAutomationSettingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_automation_api_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateAutomationSettingResult) GetAutomationSetting() *v11.AutomationSettingModel {
	if x != nil {
		return x.AutomationSetting
	}
	return nil
}

var File_moego_api_online_booking_v1_automation_api_proto protoreflect.FileDescriptor

var file_moego_api_online_booking_v1_automation_api_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a,
	0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x75, 0x74,
	0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xa9, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x61, 0x0a, 0x11,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x83, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x65,
	0x0a, 0x12, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0xc6, 0x02, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x10, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x63, 0x63, 0x65,
	0x70, 0x74, 0x12, 0x6a, 0x0a, 0x15, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70,
	0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52, 0x13, 0x61, 0x75, 0x74, 0x6f, 0x41,
	0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x86,
	0x01, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x65, 0x0a, 0x12, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x32, 0xb2, 0x02, 0x0a, 0x11, 0x41, 0x75, 0x74, 0x6f,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x88, 0x01,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x91, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x8c, 0x01, 0x0a,
	0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_api_online_booking_v1_automation_api_proto_rawDescOnce sync.Once
	file_moego_api_online_booking_v1_automation_api_proto_rawDescData = file_moego_api_online_booking_v1_automation_api_proto_rawDesc
)

func file_moego_api_online_booking_v1_automation_api_proto_rawDescGZIP() []byte {
	file_moego_api_online_booking_v1_automation_api_proto_rawDescOnce.Do(func() {
		file_moego_api_online_booking_v1_automation_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_online_booking_v1_automation_api_proto_rawDescData)
	})
	return file_moego_api_online_booking_v1_automation_api_proto_rawDescData
}

var file_moego_api_online_booking_v1_automation_api_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_api_online_booking_v1_automation_api_proto_goTypes = []interface{}{
	(*GetAutomationSettingParams)(nil),    // 0: moego.api.online_booking.v1.GetAutomationSettingParams
	(*GetAutomationSettingResult)(nil),    // 1: moego.api.online_booking.v1.GetAutomationSettingResult
	(*UpdateAutomationSettingParams)(nil), // 2: moego.api.online_booking.v1.UpdateAutomationSettingParams
	(*UpdateAutomationSettingResult)(nil), // 3: moego.api.online_booking.v1.UpdateAutomationSettingResult
	(v1.ServiceItemType)(0),               // 4: moego.models.offering.v1.ServiceItemType
	(*v11.AutomationSettingModel)(nil),    // 5: moego.models.online_booking.v1.AutomationSettingModel
	(*v11.AutomationConditionDef)(nil),    // 6: moego.models.online_booking.v1.AutomationConditionDef
}
var file_moego_api_online_booking_v1_automation_api_proto_depIdxs = []int32{
	4, // 0: moego.api.online_booking.v1.GetAutomationSettingParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	5, // 1: moego.api.online_booking.v1.GetAutomationSettingResult.automation_setting:type_name -> moego.models.online_booking.v1.AutomationSettingModel
	4, // 2: moego.api.online_booking.v1.UpdateAutomationSettingParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	6, // 3: moego.api.online_booking.v1.UpdateAutomationSettingParams.auto_accept_condition:type_name -> moego.models.online_booking.v1.AutomationConditionDef
	5, // 4: moego.api.online_booking.v1.UpdateAutomationSettingResult.automation_setting:type_name -> moego.models.online_booking.v1.AutomationSettingModel
	0, // 5: moego.api.online_booking.v1.AutomationService.GetAutomationSetting:input_type -> moego.api.online_booking.v1.GetAutomationSettingParams
	2, // 6: moego.api.online_booking.v1.AutomationService.UpdateAutomationSetting:input_type -> moego.api.online_booking.v1.UpdateAutomationSettingParams
	1, // 7: moego.api.online_booking.v1.AutomationService.GetAutomationSetting:output_type -> moego.api.online_booking.v1.GetAutomationSettingResult
	3, // 8: moego.api.online_booking.v1.AutomationService.UpdateAutomationSetting:output_type -> moego.api.online_booking.v1.UpdateAutomationSettingResult
	7, // [7:9] is the sub-list for method output_type
	5, // [5:7] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_api_online_booking_v1_automation_api_proto_init() }
func file_moego_api_online_booking_v1_automation_api_proto_init() {
	if File_moego_api_online_booking_v1_automation_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_online_booking_v1_automation_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAutomationSettingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_automation_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAutomationSettingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_automation_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAutomationSettingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_automation_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAutomationSettingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_online_booking_v1_automation_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_online_booking_v1_automation_api_proto_goTypes,
		DependencyIndexes: file_moego_api_online_booking_v1_automation_api_proto_depIdxs,
		MessageInfos:      file_moego_api_online_booking_v1_automation_api_proto_msgTypes,
	}.Build()
	File_moego_api_online_booking_v1_automation_api_proto = out.File
	file_moego_api_online_booking_v1_automation_api_proto_rawDesc = nil
	file_moego_api_online_booking_v1_automation_api_proto_goTypes = nil
	file_moego_api_online_booking_v1_automation_api_proto_depIdxs = nil
}
