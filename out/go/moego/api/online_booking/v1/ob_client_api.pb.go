// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/online_booking/v1/ob_client_api.proto

package onlinebookingapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// update client request params
type UpdateOBClientRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*UpdateOBClientRequest_Name
	//	*UpdateOBClientRequest_Domain
	Anonymous isUpdateOBClientRequest_Anonymous `protobuf_oneof:"anonymous"`
	// customer info
	Customer *v1.CustomerDef `protobuf:"bytes,3,opt,name=customer,proto3" json:"customer,omitempty"`
	// custom questions
	CustomQuestions map[string]string `protobuf:"bytes,4,rep,name=custom_questions,json=customQuestions,proto3" json:"custom_questions,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// address
	Address *UpdateOBClientRequest_UpdateOBClientRequestAddress `protobuf:"bytes,5,opt,name=address,proto3,oneof" json:"address,omitempty"`
}

func (x *UpdateOBClientRequest) Reset() {
	*x = UpdateOBClientRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOBClientRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOBClientRequest) ProtoMessage() {}

func (x *UpdateOBClientRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOBClientRequest.ProtoReflect.Descriptor instead.
func (*UpdateOBClientRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_client_api_proto_rawDescGZIP(), []int{0}
}

func (m *UpdateOBClientRequest) GetAnonymous() isUpdateOBClientRequest_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *UpdateOBClientRequest) GetName() string {
	if x, ok := x.GetAnonymous().(*UpdateOBClientRequest_Name); ok {
		return x.Name
	}
	return ""
}

func (x *UpdateOBClientRequest) GetDomain() string {
	if x, ok := x.GetAnonymous().(*UpdateOBClientRequest_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *UpdateOBClientRequest) GetCustomer() *v1.CustomerDef {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *UpdateOBClientRequest) GetCustomQuestions() map[string]string {
	if x != nil {
		return x.CustomQuestions
	}
	return nil
}

func (x *UpdateOBClientRequest) GetAddress() *UpdateOBClientRequest_UpdateOBClientRequestAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

type isUpdateOBClientRequest_Anonymous interface {
	isUpdateOBClientRequest_Anonymous()
}

type UpdateOBClientRequest_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type UpdateOBClientRequest_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*UpdateOBClientRequest_Name) isUpdateOBClientRequest_Anonymous() {}

func (*UpdateOBClientRequest_Domain) isUpdateOBClientRequest_Anonymous() {}

// get client request params
type GetOBClientRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetOBClientRequest_Name
	//	*GetOBClientRequest_Domain
	Anonymous isGetOBClientRequest_Anonymous `protobuf_oneof:"anonymous"`
}

func (x *GetOBClientRequest) Reset() {
	*x = GetOBClientRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOBClientRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOBClientRequest) ProtoMessage() {}

func (x *GetOBClientRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOBClientRequest.ProtoReflect.Descriptor instead.
func (*GetOBClientRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_client_api_proto_rawDescGZIP(), []int{1}
}

func (m *GetOBClientRequest) GetAnonymous() isGetOBClientRequest_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetOBClientRequest) GetName() string {
	if x, ok := x.GetAnonymous().(*GetOBClientRequest_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetOBClientRequest) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetOBClientRequest_Domain); ok {
		return x.Domain
	}
	return ""
}

type isGetOBClientRequest_Anonymous interface {
	isGetOBClientRequest_Anonymous()
}

type GetOBClientRequest_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetOBClientRequest_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetOBClientRequest_Name) isGetOBClientRequest_Anonymous() {}

func (*GetOBClientRequest_Domain) isGetOBClientRequest_Anonymous() {}

// online booking client info response
type OBClientInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer info
	Customer *v1.CustomerModelOnlineBookingView `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
	// credit card list
	CreditCardList []*v11.CreditCardModel `protobuf:"bytes,2,rep,name=credit_card_list,json=creditCardList,proto3" json:"credit_card_list,omitempty"`
	// ob custom question answers
	CustomQuestions map[string]string `protobuf:"bytes,3,rep,name=custom_questions,json=customQuestions,proto3" json:"custom_questions,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// necessary problem information needs to be updated
	RequiredUpdate bool `protobuf:"varint,4,opt,name=required_update,json=requiredUpdate,proto3" json:"required_update,omitempty"`
	// blocked service item types
	BlockedServiceItemTypes []v12.ServiceItemType `protobuf:"varint,5,rep,packed,name=blocked_service_item_types,json=blockedServiceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"blocked_service_item_types,omitempty"`
}

func (x *OBClientInfoResponse) Reset() {
	*x = OBClientInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBClientInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBClientInfoResponse) ProtoMessage() {}

func (x *OBClientInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBClientInfoResponse.ProtoReflect.Descriptor instead.
func (*OBClientInfoResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_client_api_proto_rawDescGZIP(), []int{2}
}

func (x *OBClientInfoResponse) GetCustomer() *v1.CustomerModelOnlineBookingView {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *OBClientInfoResponse) GetCreditCardList() []*v11.CreditCardModel {
	if x != nil {
		return x.CreditCardList
	}
	return nil
}

func (x *OBClientInfoResponse) GetCustomQuestions() map[string]string {
	if x != nil {
		return x.CustomQuestions
	}
	return nil
}

func (x *OBClientInfoResponse) GetRequiredUpdate() bool {
	if x != nil {
		return x.RequiredUpdate
	}
	return false
}

func (x *OBClientInfoResponse) GetBlockedServiceItemTypes() []v12.ServiceItemType {
	if x != nil {
		return x.BlockedServiceItemTypes
	}
	return nil
}

// get client request params
type UpdateOBClientResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// address response
	Address *UpdateOBClientResponse_UpdateOBClientAddressResponse `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *UpdateOBClientResponse) Reset() {
	*x = UpdateOBClientResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOBClientResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOBClientResponse) ProtoMessage() {}

func (x *UpdateOBClientResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOBClientResponse.ProtoReflect.Descriptor instead.
func (*UpdateOBClientResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_client_api_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateOBClientResponse) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateOBClientResponse) GetAddress() *UpdateOBClientResponse_UpdateOBClientAddressResponse {
	if x != nil {
		return x.Address
	}
	return nil
}

// payment type
type OBClientPaymentTypeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pt
	PaymentType v13.PaymentType `protobuf:"varint,1,opt,name=payment_type,json=paymentType,proto3,enum=moego.models.online_booking.v1.PaymentType" json:"payment_type,omitempty"`
	// accept client
	AcceptClient int32 `protobuf:"varint,2,opt,name=accept_client,json=acceptClient,proto3" json:"accept_client,omitempty"`
	// accept rule
	AcceptRule string `protobuf:"bytes,3,opt,name=accept_rule,json=acceptRule,proto3" json:"accept_rule,omitempty"`
	// prepay type
	PrepayType int32 `protobuf:"varint,4,opt,name=prepay_type,json=prepayType,proto3" json:"prepay_type,omitempty"`
	// prepay tip enable
	PrepayTipEnable int32 `protobuf:"varint,5,opt,name=prepay_tip_enable,json=prepayTipEnable,proto3" json:"prepay_tip_enable,omitempty"`
	// deposit type
	DepositType int32 `protobuf:"varint,6,opt,name=deposit_type,json=depositType,proto3" json:"deposit_type,omitempty"`
	// deposit percentage
	DepositPercentage int32 `protobuf:"varint,7,opt,name=deposit_percentage,json=depositPercentage,proto3" json:"deposit_percentage,omitempty"`
	// deposit amount
	DepositAmount float64 `protobuf:"fixed64,8,opt,name=deposit_amount,json=depositAmount,proto3" json:"deposit_amount,omitempty"`
	// pre auth tip enable
	PreAuthTipEnable int32 `protobuf:"varint,9,opt,name=pre_auth_tip_enable,json=preAuthTipEnable,proto3" json:"pre_auth_tip_enable,omitempty"`
	// prepay policy
	PrepayPolicy string `protobuf:"bytes,10,opt,name=prepay_policy,json=prepayPolicy,proto3" json:"prepay_policy,omitempty"`
	// pre auth policy
	PreAuthPolicy string `protobuf:"bytes,11,opt,name=pre_auth_policy,json=preAuthPolicy,proto3" json:"pre_auth_policy,omitempty"`
	// cancellation policy
	CancellationPolicy string `protobuf:"bytes,12,opt,name=cancellation_policy,json=cancellationPolicy,proto3" json:"cancellation_policy,omitempty"`
}

func (x *OBClientPaymentTypeResponse) Reset() {
	*x = OBClientPaymentTypeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBClientPaymentTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBClientPaymentTypeResponse) ProtoMessage() {}

func (x *OBClientPaymentTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBClientPaymentTypeResponse.ProtoReflect.Descriptor instead.
func (*OBClientPaymentTypeResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_client_api_proto_rawDescGZIP(), []int{4}
}

func (x *OBClientPaymentTypeResponse) GetPaymentType() v13.PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return v13.PaymentType(0)
}

func (x *OBClientPaymentTypeResponse) GetAcceptClient() int32 {
	if x != nil {
		return x.AcceptClient
	}
	return 0
}

func (x *OBClientPaymentTypeResponse) GetAcceptRule() string {
	if x != nil {
		return x.AcceptRule
	}
	return ""
}

func (x *OBClientPaymentTypeResponse) GetPrepayType() int32 {
	if x != nil {
		return x.PrepayType
	}
	return 0
}

func (x *OBClientPaymentTypeResponse) GetPrepayTipEnable() int32 {
	if x != nil {
		return x.PrepayTipEnable
	}
	return 0
}

func (x *OBClientPaymentTypeResponse) GetDepositType() int32 {
	if x != nil {
		return x.DepositType
	}
	return 0
}

func (x *OBClientPaymentTypeResponse) GetDepositPercentage() int32 {
	if x != nil {
		return x.DepositPercentage
	}
	return 0
}

func (x *OBClientPaymentTypeResponse) GetDepositAmount() float64 {
	if x != nil {
		return x.DepositAmount
	}
	return 0
}

func (x *OBClientPaymentTypeResponse) GetPreAuthTipEnable() int32 {
	if x != nil {
		return x.PreAuthTipEnable
	}
	return 0
}

func (x *OBClientPaymentTypeResponse) GetPrepayPolicy() string {
	if x != nil {
		return x.PrepayPolicy
	}
	return ""
}

func (x *OBClientPaymentTypeResponse) GetPreAuthPolicy() string {
	if x != nil {
		return x.PreAuthPolicy
	}
	return ""
}

func (x *OBClientPaymentTypeResponse) GetCancellationPolicy() string {
	if x != nil {
		return x.CancellationPolicy
	}
	return ""
}

// Update OB client request address
type UpdateOBClientRequest_UpdateOBClientRequestAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier for the address, must be greater than 0 if provided.
	// If not provided, a new address will be created.
	Id *int64 `protobuf:"varint,3,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// Primary address line, required if provided.
	Address1 *string `protobuf:"bytes,4,opt,name=address1,proto3,oneof" json:"address1,omitempty"`
	// Secondary address line, optional and can be empty.
	Address2 *string `protobuf:"bytes,5,opt,name=address2,proto3,oneof" json:"address2,omitempty"`
	// City name, required if provided.
	City *string `protobuf:"bytes,6,opt,name=city,proto3,oneof" json:"city,omitempty"`
	// Country name, required if provided.
	Country *string `protobuf:"bytes,7,opt,name=country,proto3,oneof" json:"country,omitempty"`
	// Latitude, must be within valid global coordinates if provided.
	Lat *string `protobuf:"bytes,8,opt,name=lat,proto3,oneof" json:"lat,omitempty"`
	// Longitude, must be within valid global coordinates if provided.
	Lng *string `protobuf:"bytes,9,opt,name=lng,proto3,oneof" json:"lng,omitempty"`
	// State or province.
	State *string `protobuf:"bytes,10,opt,name=state,proto3,oneof" json:"state,omitempty"`
	// Postal or ZIP code, required if provided.
	Zipcode *string `protobuf:"bytes,11,opt,name=zipcode,proto3,oneof" json:"zipcode,omitempty"`
	// Whether this address is the primary address for the customer.
	IsPrimary *int32 `protobuf:"varint,12,opt,name=is_primary,json=isPrimary,proto3,oneof" json:"is_primary,omitempty"`
	// Whether this address is the profile request address.
	IsProfileRequestAddress *bool `protobuf:"varint,13,opt,name=is_profile_request_address,json=isProfileRequestAddress,proto3,oneof" json:"is_profile_request_address,omitempty"`
}

func (x *UpdateOBClientRequest_UpdateOBClientRequestAddress) Reset() {
	*x = UpdateOBClientRequest_UpdateOBClientRequestAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOBClientRequest_UpdateOBClientRequestAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOBClientRequest_UpdateOBClientRequestAddress) ProtoMessage() {}

func (x *UpdateOBClientRequest_UpdateOBClientRequestAddress) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOBClientRequest_UpdateOBClientRequestAddress.ProtoReflect.Descriptor instead.
func (*UpdateOBClientRequest_UpdateOBClientRequestAddress) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_client_api_proto_rawDescGZIP(), []int{0, 1}
}

func (x *UpdateOBClientRequest_UpdateOBClientRequestAddress) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *UpdateOBClientRequest_UpdateOBClientRequestAddress) GetAddress1() string {
	if x != nil && x.Address1 != nil {
		return *x.Address1
	}
	return ""
}

func (x *UpdateOBClientRequest_UpdateOBClientRequestAddress) GetAddress2() string {
	if x != nil && x.Address2 != nil {
		return *x.Address2
	}
	return ""
}

func (x *UpdateOBClientRequest_UpdateOBClientRequestAddress) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *UpdateOBClientRequest_UpdateOBClientRequestAddress) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

func (x *UpdateOBClientRequest_UpdateOBClientRequestAddress) GetLat() string {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return ""
}

func (x *UpdateOBClientRequest_UpdateOBClientRequestAddress) GetLng() string {
	if x != nil && x.Lng != nil {
		return *x.Lng
	}
	return ""
}

func (x *UpdateOBClientRequest_UpdateOBClientRequestAddress) GetState() string {
	if x != nil && x.State != nil {
		return *x.State
	}
	return ""
}

func (x *UpdateOBClientRequest_UpdateOBClientRequestAddress) GetZipcode() string {
	if x != nil && x.Zipcode != nil {
		return *x.Zipcode
	}
	return ""
}

func (x *UpdateOBClientRequest_UpdateOBClientRequestAddress) GetIsPrimary() int32 {
	if x != nil && x.IsPrimary != nil {
		return *x.IsPrimary
	}
	return 0
}

func (x *UpdateOBClientRequest_UpdateOBClientRequestAddress) GetIsProfileRequestAddress() bool {
	if x != nil && x.IsProfileRequestAddress != nil {
		return *x.IsProfileRequestAddress
	}
	return false
}

// Update OB client address response
type UpdateOBClientResponse_UpdateOBClientAddressResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Id of the address.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Unique identifier for the customer associated with this address.
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// Primary address line.
	Address1 string `protobuf:"bytes,3,opt,name=address1,proto3" json:"address1,omitempty"`
	// Secondary address line.
	Address2 string `protobuf:"bytes,4,opt,name=address2,proto3" json:"address2,omitempty"`
	// City name.
	City string `protobuf:"bytes,5,opt,name=city,proto3" json:"city,omitempty"`
	// Country name.
	Country string `protobuf:"bytes,6,opt,name=country,proto3" json:"country,omitempty"`
	// Latitude.
	Lat string `protobuf:"bytes,7,opt,name=lat,proto3" json:"lat,omitempty"`
	// Longitude.
	Lng string `protobuf:"bytes,8,opt,name=lng,proto3" json:"lng,omitempty"`
	// State or province.
	State string `protobuf:"bytes,9,opt,name=state,proto3" json:"state,omitempty"`
	// Postal or ZIP code.
	Zipcode string `protobuf:"bytes,10,opt,name=zipcode,proto3" json:"zipcode,omitempty"`
	// Whether this address is the primary address for the customer.
	IsPrimary int32 `protobuf:"varint,11,opt,name=is_primary,json=isPrimary,proto3" json:"is_primary,omitempty"`
	// Whether this address is the profile request address,
	// if true, id uses profile_request_address id,
	// if false, id uses business_customer_address id.
	IsProfileRequestAddress bool `protobuf:"varint,12,opt,name=is_profile_request_address,json=isProfileRequestAddress,proto3" json:"is_profile_request_address,omitempty"`
}

func (x *UpdateOBClientResponse_UpdateOBClientAddressResponse) Reset() {
	*x = UpdateOBClientResponse_UpdateOBClientAddressResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateOBClientResponse_UpdateOBClientAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOBClientResponse_UpdateOBClientAddressResponse) ProtoMessage() {}

func (x *UpdateOBClientResponse_UpdateOBClientAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOBClientResponse_UpdateOBClientAddressResponse.ProtoReflect.Descriptor instead.
func (*UpdateOBClientResponse_UpdateOBClientAddressResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_online_booking_v1_ob_client_api_proto_rawDescGZIP(), []int{3, 0}
}

func (x *UpdateOBClientResponse_UpdateOBClientAddressResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateOBClientResponse_UpdateOBClientAddressResponse) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *UpdateOBClientResponse_UpdateOBClientAddressResponse) GetAddress1() string {
	if x != nil {
		return x.Address1
	}
	return ""
}

func (x *UpdateOBClientResponse_UpdateOBClientAddressResponse) GetAddress2() string {
	if x != nil {
		return x.Address2
	}
	return ""
}

func (x *UpdateOBClientResponse_UpdateOBClientAddressResponse) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *UpdateOBClientResponse_UpdateOBClientAddressResponse) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *UpdateOBClientResponse_UpdateOBClientAddressResponse) GetLat() string {
	if x != nil {
		return x.Lat
	}
	return ""
}

func (x *UpdateOBClientResponse_UpdateOBClientAddressResponse) GetLng() string {
	if x != nil {
		return x.Lng
	}
	return ""
}

func (x *UpdateOBClientResponse_UpdateOBClientAddressResponse) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *UpdateOBClientResponse_UpdateOBClientAddressResponse) GetZipcode() string {
	if x != nil {
		return x.Zipcode
	}
	return ""
}

func (x *UpdateOBClientResponse_UpdateOBClientAddressResponse) GetIsPrimary() int32 {
	if x != nil {
		return x.IsPrimary
	}
	return 0
}

func (x *UpdateOBClientResponse_UpdateOBClientAddressResponse) GetIsProfileRequestAddress() bool {
	if x != nil {
		return x.IsProfileRequestAddress
	}
	return false
}

var File_moego_api_online_booking_v1_ob_client_api_proto protoreflect.FileDescriptor

var file_moego_api_online_booking_v1_ob_client_api_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62,
	0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x2c,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb9, 0x08, 0x0a, 0x15, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x42, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x12, 0x41, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x66, 0x52, 0x08, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x72, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x42, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x6e, 0x0a, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4f, 0x42, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x42, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x48, 0x01, 0x52,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0xe6, 0x04, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x42, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x1c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x29,
	0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x01, 0x52, 0x08, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x08, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x02, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x32, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x03, 0x52, 0x04,
	0x63, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18,
	0xff, 0x01, 0x48, 0x04, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x88, 0x01, 0x01,
	0x12, 0x1e, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x48, 0x05, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x1e, 0x0a, 0x03, 0x6c, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x48, 0x06, 0x52, 0x03, 0x6c, 0x6e, 0x67, 0x88, 0x01, 0x01,
	0x12, 0x23, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x07, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48,
	0x08, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a,
	0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x30, 0x00, 0x30, 0x01, 0x48, 0x09, 0x52, 0x09,
	0x69, 0x73, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x1a,
	0x69, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x0a, 0x52, 0x17, 0x69, 0x73, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x42, 0x05,
	0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x31, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x42,
	0x07, 0x0a, 0x05, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x6c, 0x61, 0x74, 0x42, 0x06, 0x0a, 0x04,
	0x5f, 0x6c, 0x6e, 0x67, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x42, 0x0a,
	0x0a, 0x08, 0x5f, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x69,
	0x73, 0x5f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x42, 0x1d, 0x0a, 0x1b, 0x5f, 0x69, 0x73,
	0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e,
	0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x56, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4f, 0x42, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x42, 0x10, 0x0a, 0x09,
	0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x88,
	0x04, 0x0a, 0x14, 0x4f, 0x42, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x52, 0x0a,
	0x10, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x0e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x71, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x42, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x66, 0x0a,
	0x1a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x17, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x1a, 0x42, 0x0a, 0x14, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xfe, 0x03, 0x0a, 0x16, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4f, 0x42, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x6b, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x42, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4f, 0x42, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x1a, 0xe6, 0x02, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x42, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31,
	0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x6c, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6c, 0x6e, 0x67, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x69, 0x73, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x3b, 0x0a,
	0x1a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x17, 0x69, 0x73, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xa6, 0x04, 0x0a, 0x1b, 0x4f,
	0x42, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4e, 0x0a, 0x0c, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x5f, 0x74, 0x69, 0x70, 0x5f,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x70, 0x72,
	0x65, 0x70, 0x61, 0x79, 0x54, 0x69, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2d, 0x0a, 0x12, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x64, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x13, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x74, 0x69, 0x70, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x10, 0x70, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x54, 0x69, 0x70, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x5f,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72,
	0x65, 0x70, 0x61, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x72,
	0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x12, 0x2f, 0x0a, 0x13, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x32, 0x94, 0x03, 0x0a, 0x0f, 0x4f, 0x42, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x75, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4f, 0x42,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x42, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x42, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x79,
	0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x42, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x42, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x42, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8e, 0x01, 0x0a, 0x21, 0x47, 0x65,
	0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x4f, 0x42, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x42, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8c, 0x01, 0x0a, 0x23, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_api_online_booking_v1_ob_client_api_proto_rawDescOnce sync.Once
	file_moego_api_online_booking_v1_ob_client_api_proto_rawDescData = file_moego_api_online_booking_v1_ob_client_api_proto_rawDesc
)

func file_moego_api_online_booking_v1_ob_client_api_proto_rawDescGZIP() []byte {
	file_moego_api_online_booking_v1_ob_client_api_proto_rawDescOnce.Do(func() {
		file_moego_api_online_booking_v1_ob_client_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_online_booking_v1_ob_client_api_proto_rawDescData)
	})
	return file_moego_api_online_booking_v1_ob_client_api_proto_rawDescData
}

var file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_moego_api_online_booking_v1_ob_client_api_proto_goTypes = []interface{}{
	(*UpdateOBClientRequest)(nil),       // 0: moego.api.online_booking.v1.UpdateOBClientRequest
	(*GetOBClientRequest)(nil),          // 1: moego.api.online_booking.v1.GetOBClientRequest
	(*OBClientInfoResponse)(nil),        // 2: moego.api.online_booking.v1.OBClientInfoResponse
	(*UpdateOBClientResponse)(nil),      // 3: moego.api.online_booking.v1.UpdateOBClientResponse
	(*OBClientPaymentTypeResponse)(nil), // 4: moego.api.online_booking.v1.OBClientPaymentTypeResponse
	nil,                                 // 5: moego.api.online_booking.v1.UpdateOBClientRequest.CustomQuestionsEntry
	(*UpdateOBClientRequest_UpdateOBClientRequestAddress)(nil), // 6: moego.api.online_booking.v1.UpdateOBClientRequest.UpdateOBClientRequestAddress
	nil, // 7: moego.api.online_booking.v1.OBClientInfoResponse.CustomQuestionsEntry
	(*UpdateOBClientResponse_UpdateOBClientAddressResponse)(nil), // 8: moego.api.online_booking.v1.UpdateOBClientResponse.UpdateOBClientAddressResponse
	(*v1.CustomerDef)(nil),                    // 9: moego.models.customer.v1.CustomerDef
	(*v1.CustomerModelOnlineBookingView)(nil), // 10: moego.models.customer.v1.CustomerModelOnlineBookingView
	(*v11.CreditCardModel)(nil),               // 11: moego.models.payment.v1.CreditCardModel
	(v12.ServiceItemType)(0),                  // 12: moego.models.offering.v1.ServiceItemType
	(v13.PaymentType)(0),                      // 13: moego.models.online_booking.v1.PaymentType
}
var file_moego_api_online_booking_v1_ob_client_api_proto_depIdxs = []int32{
	9,  // 0: moego.api.online_booking.v1.UpdateOBClientRequest.customer:type_name -> moego.models.customer.v1.CustomerDef
	5,  // 1: moego.api.online_booking.v1.UpdateOBClientRequest.custom_questions:type_name -> moego.api.online_booking.v1.UpdateOBClientRequest.CustomQuestionsEntry
	6,  // 2: moego.api.online_booking.v1.UpdateOBClientRequest.address:type_name -> moego.api.online_booking.v1.UpdateOBClientRequest.UpdateOBClientRequestAddress
	10, // 3: moego.api.online_booking.v1.OBClientInfoResponse.customer:type_name -> moego.models.customer.v1.CustomerModelOnlineBookingView
	11, // 4: moego.api.online_booking.v1.OBClientInfoResponse.credit_card_list:type_name -> moego.models.payment.v1.CreditCardModel
	7,  // 5: moego.api.online_booking.v1.OBClientInfoResponse.custom_questions:type_name -> moego.api.online_booking.v1.OBClientInfoResponse.CustomQuestionsEntry
	12, // 6: moego.api.online_booking.v1.OBClientInfoResponse.blocked_service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	8,  // 7: moego.api.online_booking.v1.UpdateOBClientResponse.address:type_name -> moego.api.online_booking.v1.UpdateOBClientResponse.UpdateOBClientAddressResponse
	13, // 8: moego.api.online_booking.v1.OBClientPaymentTypeResponse.payment_type:type_name -> moego.models.online_booking.v1.PaymentType
	1,  // 9: moego.api.online_booking.v1.OBClientService.GetOBClientInfo:input_type -> moego.api.online_booking.v1.GetOBClientRequest
	0,  // 10: moego.api.online_booking.v1.OBClientService.UpdateOBClient:input_type -> moego.api.online_booking.v1.UpdateOBClientRequest
	1,  // 11: moego.api.online_booking.v1.OBClientService.GetOnlineBookingClientPaymentType:input_type -> moego.api.online_booking.v1.GetOBClientRequest
	2,  // 12: moego.api.online_booking.v1.OBClientService.GetOBClientInfo:output_type -> moego.api.online_booking.v1.OBClientInfoResponse
	3,  // 13: moego.api.online_booking.v1.OBClientService.UpdateOBClient:output_type -> moego.api.online_booking.v1.UpdateOBClientResponse
	4,  // 14: moego.api.online_booking.v1.OBClientService.GetOnlineBookingClientPaymentType:output_type -> moego.api.online_booking.v1.OBClientPaymentTypeResponse
	12, // [12:15] is the sub-list for method output_type
	9,  // [9:12] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_moego_api_online_booking_v1_ob_client_api_proto_init() }
func file_moego_api_online_booking_v1_ob_client_api_proto_init() {
	if File_moego_api_online_booking_v1_ob_client_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOBClientRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOBClientRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBClientInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOBClientResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBClientPaymentTypeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOBClientRequest_UpdateOBClientRequestAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateOBClientResponse_UpdateOBClientAddressResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*UpdateOBClientRequest_Name)(nil),
		(*UpdateOBClientRequest_Domain)(nil),
	}
	file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*GetOBClientRequest_Name)(nil),
		(*GetOBClientRequest_Domain)(nil),
	}
	file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_online_booking_v1_ob_client_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_online_booking_v1_ob_client_api_proto_goTypes,
		DependencyIndexes: file_moego_api_online_booking_v1_ob_client_api_proto_depIdxs,
		MessageInfos:      file_moego_api_online_booking_v1_ob_client_api_proto_msgTypes,
	}.Build()
	File_moego_api_online_booking_v1_ob_client_api_proto = out.File
	file_moego_api_online_booking_v1_ob_client_api_proto_rawDesc = nil
	file_moego_api_online_booking_v1_ob_client_api_proto_goTypes = nil
	file_moego_api_online_booking_v1_ob_client_api_proto_depIdxs = nil
}
