// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/payment/v2/payment_api.proto

package paymentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PaymentServiceClient is the client API for PaymentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PaymentServiceClient interface {
	// 获取支付版本
	GetPaymentVersion(ctx context.Context, in *GetPaymentVersionParams, opts ...grpc.CallOption) (*GetPaymentVersionResult, error)
	// GetPayData 获取支付数据，用于前端加载第三方支付组件
	GetPayData(ctx context.Context, in *GetPayDataParams, opts ...grpc.CallOption) (*GetPayDataResult, error)
	// submit action detail
	SubmitActionDetail(ctx context.Context, in *SubmitActionDetailParams, opts ...grpc.CallOption) (*SubmitActionDetailResult, error)
	// 查询payment
	GetPayment(ctx context.Context, in *GetPaymentParams, opts ...grpc.CallOption) (*GetPaymentResult, error)
	// 获取支付列表
	ListPayment(ctx context.Context, in *ListPaymentParams, opts ...grpc.CallOption) (*ListPaymentResult, error)
}

type paymentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPaymentServiceClient(cc grpc.ClientConnInterface) PaymentServiceClient {
	return &paymentServiceClient{cc}
}

func (c *paymentServiceClient) GetPaymentVersion(ctx context.Context, in *GetPaymentVersionParams, opts ...grpc.CallOption) (*GetPaymentVersionResult, error) {
	out := new(GetPaymentVersionResult)
	err := c.cc.Invoke(ctx, "/moego.api.payment.v2.PaymentService/GetPaymentVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentServiceClient) GetPayData(ctx context.Context, in *GetPayDataParams, opts ...grpc.CallOption) (*GetPayDataResult, error) {
	out := new(GetPayDataResult)
	err := c.cc.Invoke(ctx, "/moego.api.payment.v2.PaymentService/GetPayData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentServiceClient) SubmitActionDetail(ctx context.Context, in *SubmitActionDetailParams, opts ...grpc.CallOption) (*SubmitActionDetailResult, error) {
	out := new(SubmitActionDetailResult)
	err := c.cc.Invoke(ctx, "/moego.api.payment.v2.PaymentService/SubmitActionDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentServiceClient) GetPayment(ctx context.Context, in *GetPaymentParams, opts ...grpc.CallOption) (*GetPaymentResult, error) {
	out := new(GetPaymentResult)
	err := c.cc.Invoke(ctx, "/moego.api.payment.v2.PaymentService/GetPayment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentServiceClient) ListPayment(ctx context.Context, in *ListPaymentParams, opts ...grpc.CallOption) (*ListPaymentResult, error) {
	out := new(ListPaymentResult)
	err := c.cc.Invoke(ctx, "/moego.api.payment.v2.PaymentService/ListPayment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PaymentServiceServer is the server API for PaymentService service.
// All implementations must embed UnimplementedPaymentServiceServer
// for forward compatibility
type PaymentServiceServer interface {
	// 获取支付版本
	GetPaymentVersion(context.Context, *GetPaymentVersionParams) (*GetPaymentVersionResult, error)
	// GetPayData 获取支付数据，用于前端加载第三方支付组件
	GetPayData(context.Context, *GetPayDataParams) (*GetPayDataResult, error)
	// submit action detail
	SubmitActionDetail(context.Context, *SubmitActionDetailParams) (*SubmitActionDetailResult, error)
	// 查询payment
	GetPayment(context.Context, *GetPaymentParams) (*GetPaymentResult, error)
	// 获取支付列表
	ListPayment(context.Context, *ListPaymentParams) (*ListPaymentResult, error)
	mustEmbedUnimplementedPaymentServiceServer()
}

// UnimplementedPaymentServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPaymentServiceServer struct {
}

func (UnimplementedPaymentServiceServer) GetPaymentVersion(context.Context, *GetPaymentVersionParams) (*GetPaymentVersionResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPaymentVersion not implemented")
}
func (UnimplementedPaymentServiceServer) GetPayData(context.Context, *GetPayDataParams) (*GetPayDataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPayData not implemented")
}
func (UnimplementedPaymentServiceServer) SubmitActionDetail(context.Context, *SubmitActionDetailParams) (*SubmitActionDetailResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitActionDetail not implemented")
}
func (UnimplementedPaymentServiceServer) GetPayment(context.Context, *GetPaymentParams) (*GetPaymentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPayment not implemented")
}
func (UnimplementedPaymentServiceServer) ListPayment(context.Context, *ListPaymentParams) (*ListPaymentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPayment not implemented")
}
func (UnimplementedPaymentServiceServer) mustEmbedUnimplementedPaymentServiceServer() {}

// UnsafePaymentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PaymentServiceServer will
// result in compilation errors.
type UnsafePaymentServiceServer interface {
	mustEmbedUnimplementedPaymentServiceServer()
}

func RegisterPaymentServiceServer(s grpc.ServiceRegistrar, srv PaymentServiceServer) {
	s.RegisterService(&PaymentService_ServiceDesc, srv)
}

func _PaymentService_GetPaymentVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaymentVersionParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServiceServer).GetPaymentVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.payment.v2.PaymentService/GetPaymentVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServiceServer).GetPaymentVersion(ctx, req.(*GetPaymentVersionParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentService_GetPayData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPayDataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServiceServer).GetPayData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.payment.v2.PaymentService/GetPayData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServiceServer).GetPayData(ctx, req.(*GetPayDataParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentService_SubmitActionDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitActionDetailParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServiceServer).SubmitActionDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.payment.v2.PaymentService/SubmitActionDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServiceServer).SubmitActionDetail(ctx, req.(*SubmitActionDetailParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentService_GetPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaymentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServiceServer).GetPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.payment.v2.PaymentService/GetPayment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServiceServer).GetPayment(ctx, req.(*GetPaymentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentService_ListPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPaymentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServiceServer).ListPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.payment.v2.PaymentService/ListPayment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServiceServer).ListPayment(ctx, req.(*ListPaymentParams))
	}
	return interceptor(ctx, in, info, handler)
}

// PaymentService_ServiceDesc is the grpc.ServiceDesc for PaymentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PaymentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.payment.v2.PaymentService",
	HandlerType: (*PaymentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPaymentVersion",
			Handler:    _PaymentService_GetPaymentVersion_Handler,
		},
		{
			MethodName: "GetPayData",
			Handler:    _PaymentService_GetPayData_Handler,
		},
		{
			MethodName: "SubmitActionDetail",
			Handler:    _PaymentService_SubmitActionDetail_Handler,
		},
		{
			MethodName: "GetPayment",
			Handler:    _PaymentService_GetPayment_Handler,
		},
		{
			MethodName: "ListPayment",
			Handler:    _PaymentService_ListPayment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/payment/v2/payment_api.proto",
}
