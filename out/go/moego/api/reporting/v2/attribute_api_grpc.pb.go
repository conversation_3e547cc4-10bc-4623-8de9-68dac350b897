// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/reporting/v2/attribute_api.proto

package reportingapipb

import (
	context "context"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AttributeServiceClient is the client API for AttributeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AttributeServiceClient interface {
	// Get dimensions
	GetDimensions(ctx context.Context, in *v2.GetDimensionsParams, opts ...grpc.CallOption) (*v2.GetDimensionsResult, error)
	// Get metrics categories
	GetMetricsCategories(ctx context.Context, in *v2.GetMetricsCategoriesParams, opts ...grpc.CallOption) (*v2.GetMetricsCategoriesResult, error)
}

type attributeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAttributeServiceClient(cc grpc.ClientConnInterface) AttributeServiceClient {
	return &attributeServiceClient{cc}
}

func (c *attributeServiceClient) GetDimensions(ctx context.Context, in *v2.GetDimensionsParams, opts ...grpc.CallOption) (*v2.GetDimensionsResult, error) {
	out := new(v2.GetDimensionsResult)
	err := c.cc.Invoke(ctx, "/moego.api.reporting.v2.AttributeService/GetDimensions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *attributeServiceClient) GetMetricsCategories(ctx context.Context, in *v2.GetMetricsCategoriesParams, opts ...grpc.CallOption) (*v2.GetMetricsCategoriesResult, error) {
	out := new(v2.GetMetricsCategoriesResult)
	err := c.cc.Invoke(ctx, "/moego.api.reporting.v2.AttributeService/GetMetricsCategories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AttributeServiceServer is the server API for AttributeService service.
// All implementations must embed UnimplementedAttributeServiceServer
// for forward compatibility
type AttributeServiceServer interface {
	// Get dimensions
	GetDimensions(context.Context, *v2.GetDimensionsParams) (*v2.GetDimensionsResult, error)
	// Get metrics categories
	GetMetricsCategories(context.Context, *v2.GetMetricsCategoriesParams) (*v2.GetMetricsCategoriesResult, error)
	mustEmbedUnimplementedAttributeServiceServer()
}

// UnimplementedAttributeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAttributeServiceServer struct {
}

func (UnimplementedAttributeServiceServer) GetDimensions(context.Context, *v2.GetDimensionsParams) (*v2.GetDimensionsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDimensions not implemented")
}
func (UnimplementedAttributeServiceServer) GetMetricsCategories(context.Context, *v2.GetMetricsCategoriesParams) (*v2.GetMetricsCategoriesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMetricsCategories not implemented")
}
func (UnimplementedAttributeServiceServer) mustEmbedUnimplementedAttributeServiceServer() {}

// UnsafeAttributeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AttributeServiceServer will
// result in compilation errors.
type UnsafeAttributeServiceServer interface {
	mustEmbedUnimplementedAttributeServiceServer()
}

func RegisterAttributeServiceServer(s grpc.ServiceRegistrar, srv AttributeServiceServer) {
	s.RegisterService(&AttributeService_ServiceDesc, srv)
}

func _AttributeService_GetDimensions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.GetDimensionsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AttributeServiceServer).GetDimensions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.reporting.v2.AttributeService/GetDimensions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AttributeServiceServer).GetDimensions(ctx, req.(*v2.GetDimensionsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AttributeService_GetMetricsCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.GetMetricsCategoriesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AttributeServiceServer).GetMetricsCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.reporting.v2.AttributeService/GetMetricsCategories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AttributeServiceServer).GetMetricsCategories(ctx, req.(*v2.GetMetricsCategoriesParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AttributeService_ServiceDesc is the grpc.ServiceDesc for AttributeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AttributeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.reporting.v2.AttributeService",
	HandlerType: (*AttributeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetDimensions",
			Handler:    _AttributeService_GetDimensions_Handler,
		},
		{
			MethodName: "GetMetricsCategories",
			Handler:    _AttributeService_GetMetricsCategories_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/reporting/v2/attribute_api.proto",
}
