// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/reporting/v2/reports_api.proto

package reportingapipb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	calendarperiod "google.golang.org/genproto/googleapis/type/calendarperiod"
	interval "google.golang.org/genproto/googleapis/type/interval"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The favorite action enum
type MarkReportFavoriteRequest_Action int32

const (
	// Unspecified favorite actions
	MarkReportFavoriteRequest_FAVORITE_ACTION_UNSPECIFIED MarkReportFavoriteRequest_Action = 0
	// A favorite action to add a report to favorites
	MarkReportFavoriteRequest_ADD MarkReportFavoriteRequest_Action = 1
	// A favorite action to remove a report from favorites
	MarkReportFavoriteRequest_REMOVE MarkReportFavoriteRequest_Action = 2
)

// Enum value maps for MarkReportFavoriteRequest_Action.
var (
	MarkReportFavoriteRequest_Action_name = map[int32]string{
		0: "FAVORITE_ACTION_UNSPECIFIED",
		1: "ADD",
		2: "REMOVE",
	}
	MarkReportFavoriteRequest_Action_value = map[string]int32{
		"FAVORITE_ACTION_UNSPECIFIED": 0,
		"ADD":                         1,
		"REMOVE":                      2,
	}
)

func (x MarkReportFavoriteRequest_Action) Enum() *MarkReportFavoriteRequest_Action {
	p := new(MarkReportFavoriteRequest_Action)
	*p = x
	return p
}

func (x MarkReportFavoriteRequest_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MarkReportFavoriteRequest_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_api_reporting_v2_reports_api_proto_enumTypes[0].Descriptor()
}

func (MarkReportFavoriteRequest_Action) Type() protoreflect.EnumType {
	return &file_moego_api_reporting_v2_reports_api_proto_enumTypes[0]
}

func (x MarkReportFavoriteRequest_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MarkReportFavoriteRequest_Action.Descriptor instead.
func (MarkReportFavoriteRequest_Action) EnumDescriptor() ([]byte, []int) {
	return file_moego_api_reporting_v2_reports_api_proto_rawDescGZIP(), []int{2, 0}
}

// QueryReportsRequest
type QueryReportPagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The tabs to query reports page
	Tabs []v2.ReportPage_Tab `protobuf:"varint,1,rep,packed,name=tabs,proto3,enum=moego.models.reporting.v2.ReportPage_Tab" json:"tabs,omitempty"`
	// 区分不同的 reporting 场景
	ReportingType *v2.ReportingScene `protobuf:"varint,2,opt,name=reporting_type,json=reportingType,proto3,enum=moego.models.reporting.v2.ReportingScene,oneof" json:"reporting_type,omitempty"`
}

func (x *QueryReportPagesRequest) Reset() {
	*x = QueryReportPagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryReportPagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryReportPagesRequest) ProtoMessage() {}

func (x *QueryReportPagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryReportPagesRequest.ProtoReflect.Descriptor instead.
func (*QueryReportPagesRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_reporting_v2_reports_api_proto_rawDescGZIP(), []int{0}
}

func (x *QueryReportPagesRequest) GetTabs() []v2.ReportPage_Tab {
	if x != nil {
		return x.Tabs
	}
	return nil
}

func (x *QueryReportPagesRequest) GetReportingType() v2.ReportingScene {
	if x != nil && x.ReportingType != nil {
		return *x.ReportingType
	}
	return v2.ReportingScene(0)
}

// QueryReportsResponse
type QueryReportPagesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of report pages
	Pages []*v2.ReportPage `protobuf:"bytes,1,rep,name=pages,proto3" json:"pages,omitempty"`
	// Report data last synced time
	LastSyncedTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=last_synced_time,json=lastSyncedTime,proto3" json:"last_synced_time,omitempty"`
}

func (x *QueryReportPagesResponse) Reset() {
	*x = QueryReportPagesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryReportPagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryReportPagesResponse) ProtoMessage() {}

func (x *QueryReportPagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryReportPagesResponse.ProtoReflect.Descriptor instead.
func (*QueryReportPagesResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_reporting_v2_reports_api_proto_rawDescGZIP(), []int{1}
}

func (x *QueryReportPagesResponse) GetPages() []*v2.ReportPage {
	if x != nil {
		return x.Pages
	}
	return nil
}

func (x *QueryReportPagesResponse) GetLastSyncedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSyncedTime
	}
	return nil
}

// MarkReportAsFavoriteRequest
type MarkReportFavoriteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The report to mark as favorite
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// The action to take
	Action MarkReportFavoriteRequest_Action `protobuf:"varint,2,opt,name=action,proto3,enum=moego.api.reporting.v2.MarkReportFavoriteRequest_Action" json:"action,omitempty"`
}

func (x *MarkReportFavoriteRequest) Reset() {
	*x = MarkReportFavoriteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkReportFavoriteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkReportFavoriteRequest) ProtoMessage() {}

func (x *MarkReportFavoriteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkReportFavoriteRequest.ProtoReflect.Descriptor instead.
func (*MarkReportFavoriteRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_reporting_v2_reports_api_proto_rawDescGZIP(), []int{2}
}

func (x *MarkReportFavoriteRequest) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *MarkReportFavoriteRequest) GetAction() MarkReportFavoriteRequest_Action {
	if x != nil {
		return x.Action
	}
	return MarkReportFavoriteRequest_FAVORITE_ACTION_UNSPECIFIED
}

// MarkReportFavoriteResponse
type MarkReportFavoriteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Mark result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *MarkReportFavoriteResponse) Reset() {
	*x = MarkReportFavoriteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkReportFavoriteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkReportFavoriteResponse) ProtoMessage() {}

func (x *MarkReportFavoriteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkReportFavoriteResponse.ProtoReflect.Descriptor instead.
func (*MarkReportFavoriteResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_reporting_v2_reports_api_proto_rawDescGZIP(), []int{3}
}

func (x *MarkReportFavoriteResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

// SaveReportCustomizeConfigRequest
type SaveReportCustomizeConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The report to save customized configs
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// Customize configs to save
	CustomizedConfig *v2.TableCustomizedConfig `protobuf:"bytes,2,opt,name=customized_config,json=customizedConfig,proto3" json:"customized_config,omitempty"`
}

func (x *SaveReportCustomizeConfigRequest) Reset() {
	*x = SaveReportCustomizeConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveReportCustomizeConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveReportCustomizeConfigRequest) ProtoMessage() {}

func (x *SaveReportCustomizeConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveReportCustomizeConfigRequest.ProtoReflect.Descriptor instead.
func (*SaveReportCustomizeConfigRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_reporting_v2_reports_api_proto_rawDescGZIP(), []int{4}
}

func (x *SaveReportCustomizeConfigRequest) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *SaveReportCustomizeConfigRequest) GetCustomizedConfig() *v2.TableCustomizedConfig {
	if x != nil {
		return x.CustomizedConfig
	}
	return nil
}

// QueryReportsMetaRequest
type QueryReportMetasRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The report to query meta data, if empty, return all reports' meta data
	DiagramIds []string `protobuf:"bytes,1,rep,name=diagram_ids,json=diagramIds,proto3" json:"diagram_ids,omitempty"`
}

func (x *QueryReportMetasRequest) Reset() {
	*x = QueryReportMetasRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryReportMetasRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryReportMetasRequest) ProtoMessage() {}

func (x *QueryReportMetasRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryReportMetasRequest.ProtoReflect.Descriptor instead.
func (*QueryReportMetasRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_reporting_v2_reports_api_proto_rawDescGZIP(), []int{5}
}

func (x *QueryReportMetasRequest) GetDiagramIds() []string {
	if x != nil {
		return x.DiagramIds
	}
	return nil
}

// QueryReportsMetaRequest
type QueryReportsMetasResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of report metadata
	ReportMetas []*v2.TableMeta `protobuf:"bytes,1,rep,name=report_metas,json=reportMetas,proto3" json:"report_metas,omitempty"`
}

func (x *QueryReportsMetasResponse) Reset() {
	*x = QueryReportsMetasResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryReportsMetasResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryReportsMetasResponse) ProtoMessage() {}

func (x *QueryReportsMetasResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryReportsMetasResponse.ProtoReflect.Descriptor instead.
func (*QueryReportsMetasResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_reporting_v2_reports_api_proto_rawDescGZIP(), []int{6}
}

func (x *QueryReportsMetasResponse) GetReportMetas() []*v2.TableMeta {
	if x != nil {
		return x.ReportMetas
	}
	return nil
}

// Describe a request to fetch report data
type FetchReportDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram id
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// The business id
	BusinessIds []uint64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// current period
	CurrentPeriod *interval.Interval `protobuf:"bytes,4,opt,name=current_period,json=currentPeriod,proto3" json:"current_period,omitempty"`
	// previous period
	PreviousPeriod *interval.Interval `protobuf:"bytes,5,opt,name=previous_period,json=previousPeriod,proto3,oneof" json:"previous_period,omitempty"`
	// Filters
	Filters []*v2.FilterRequest `protobuf:"bytes,6,rep,name=filters,proto3" json:"filters,omitempty"`
	// The group by field key
	GroupByFieldKeys []string `protobuf:"bytes,7,rep,name=group_by_field_keys,json=groupByFieldKeys,proto3" json:"group_by_field_keys,omitempty"`
	// The pagination request
	Pagination *v21.PaginationRequest `protobuf:"bytes,8,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// The order by config
	OrderBys []*v21.OrderBy `protobuf:"bytes,9,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// The group by period, could be day, week, month, year etc., default day.
	GroupByPeriod *calendarperiod.CalendarPeriod `protobuf:"varint,10,opt,name=group_by_period,json=groupByPeriod,proto3,enum=google.type.CalendarPeriod,oneof" json:"group_by_period,omitempty"`
}

func (x *FetchReportDataRequest) Reset() {
	*x = FetchReportDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchReportDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchReportDataRequest) ProtoMessage() {}

func (x *FetchReportDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchReportDataRequest.ProtoReflect.Descriptor instead.
func (*FetchReportDataRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_reporting_v2_reports_api_proto_rawDescGZIP(), []int{7}
}

func (x *FetchReportDataRequest) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *FetchReportDataRequest) GetBusinessIds() []uint64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *FetchReportDataRequest) GetCurrentPeriod() *interval.Interval {
	if x != nil {
		return x.CurrentPeriod
	}
	return nil
}

func (x *FetchReportDataRequest) GetPreviousPeriod() *interval.Interval {
	if x != nil {
		return x.PreviousPeriod
	}
	return nil
}

func (x *FetchReportDataRequest) GetFilters() []*v2.FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *FetchReportDataRequest) GetGroupByFieldKeys() []string {
	if x != nil {
		return x.GroupByFieldKeys
	}
	return nil
}

func (x *FetchReportDataRequest) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *FetchReportDataRequest) GetOrderBys() []*v21.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *FetchReportDataRequest) GetGroupByPeriod() calendarperiod.CalendarPeriod {
	if x != nil && x.GroupByPeriod != nil {
		return *x.GroupByPeriod
	}
	return calendarperiod.CalendarPeriod(0)
}

// Describe a response to fetch report data
type FetchReportDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The report data
	TableData *v2.TableData `protobuf:"bytes,1,opt,name=table_data,json=tableData,proto3" json:"table_data,omitempty"`
	// The pagination response
	Pagination *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// Report data last synced time
	LastSyncedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=last_synced_time,json=lastSyncedTime,proto3" json:"last_synced_time,omitempty"`
}

func (x *FetchReportDataResponse) Reset() {
	*x = FetchReportDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchReportDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchReportDataResponse) ProtoMessage() {}

func (x *FetchReportDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchReportDataResponse.ProtoReflect.Descriptor instead.
func (*FetchReportDataResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_reporting_v2_reports_api_proto_rawDescGZIP(), []int{8}
}

func (x *FetchReportDataResponse) GetTableData() *v2.TableData {
	if x != nil {
		return x.TableData
	}
	return nil
}

func (x *FetchReportDataResponse) GetPagination() *v21.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *FetchReportDataResponse) GetLastSyncedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSyncedTime
	}
	return nil
}

// ExportReportDataRequest
type ExportReportDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram id
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// Filters
	Filters []*v2.FilterRequest `protobuf:"bytes,2,rep,name=filters,proto3" json:"filters,omitempty"`
	// business ids
	BusinessIds []uint64 `protobuf:"varint,3,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// current period
	CurrentPeriod *interval.Interval `protobuf:"bytes,4,opt,name=current_period,json=currentPeriod,proto3" json:"current_period,omitempty"`
	// previous period
	PreviousPeriod *interval.Interval `protobuf:"bytes,5,opt,name=previous_period,json=previousPeriod,proto3,oneof" json:"previous_period,omitempty"`
	// group by field key
	GroupByFieldKeys []string `protobuf:"bytes,6,rep,name=group_by_field_keys,json=groupByFieldKeys,proto3" json:"group_by_field_keys,omitempty"`
	// order by params
	OrderBys []*v21.OrderBy `protobuf:"bytes,7,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// The group by period, could be day, week, month, year etc., default day.
	GroupByPeriod *calendarperiod.CalendarPeriod `protobuf:"varint,8,opt,name=group_by_period,json=groupByPeriod,proto3,enum=google.type.CalendarPeriod,oneof" json:"group_by_period,omitempty"`
}

func (x *ExportReportDataRequest) Reset() {
	*x = ExportReportDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportReportDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportReportDataRequest) ProtoMessage() {}

func (x *ExportReportDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportReportDataRequest.ProtoReflect.Descriptor instead.
func (*ExportReportDataRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_reporting_v2_reports_api_proto_rawDescGZIP(), []int{9}
}

func (x *ExportReportDataRequest) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *ExportReportDataRequest) GetFilters() []*v2.FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *ExportReportDataRequest) GetBusinessIds() []uint64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ExportReportDataRequest) GetCurrentPeriod() *interval.Interval {
	if x != nil {
		return x.CurrentPeriod
	}
	return nil
}

func (x *ExportReportDataRequest) GetPreviousPeriod() *interval.Interval {
	if x != nil {
		return x.PreviousPeriod
	}
	return nil
}

func (x *ExportReportDataRequest) GetGroupByFieldKeys() []string {
	if x != nil {
		return x.GroupByFieldKeys
	}
	return nil
}

func (x *ExportReportDataRequest) GetOrderBys() []*v21.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *ExportReportDataRequest) GetGroupByPeriod() calendarperiod.CalendarPeriod {
	if x != nil && x.GroupByPeriod != nil {
		return *x.GroupByPeriod
	}
	return calendarperiod.CalendarPeriod(0)
}

// ExportReportDataResponse
type ExportReportDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file id
	FileId int64 `protobuf:"varint,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
}

func (x *ExportReportDataResponse) Reset() {
	*x = ExportReportDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportReportDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportReportDataResponse) ProtoMessage() {}

func (x *ExportReportDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_reporting_v2_reports_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportReportDataResponse.ProtoReflect.Descriptor instead.
func (*ExportReportDataResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_reporting_v2_reports_api_proto_rawDescGZIP(), []int{10}
}

func (x *ExportReportDataResponse) GetFileId() int64 {
	if x != nil {
		return x.FileId
	}
	return 0
}

var File_moego_api_reporting_v2_reports_api_proto protoreflect.FileDescriptor

var file_moego_api_reporting_v2_reports_api_proto_rawDesc = []byte{
	0x0a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x21, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x63, 0x61,
	0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64,
	0x65, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f,
	0x64, 0x65, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76,
	0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe1, 0x01, 0x0a, 0x17, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x50, 0x0a, 0x04, 0x74, 0x61, 0x62, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x50, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x61, 0x62, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01,
	0x0b, 0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x61,
	0x62, 0x73, 0x12, 0x61, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x53, 0x63, 0x65, 0x6e, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x9d, 0x01, 0x0a, 0x18, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x05, 0x70, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x67, 0x65, 0x52, 0x05, 0x70, 0x61, 0x67,
	0x65, 0x73, 0x12, 0x44, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x53, 0x79,
	0x6e, 0x63, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xe3, 0x01, 0x0a, 0x19, 0x4d, 0x61, 0x72,
	0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61,
	0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x09, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64,
	0x12, 0x5c, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x3e,
	0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x41, 0x56, 0x4f,
	0x52, 0x49, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x44, 0x44,
	0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x4d, 0x4f, 0x56, 0x45, 0x10, 0x02, 0x22, 0x34,
	0x0a, 0x1a, 0x4d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x61, 0x76, 0x6f,
	0x72, 0x69, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0xab, 0x01, 0x0a, 0x20, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0a, 0x64, 0x69, 0x61,
	0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x09, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61,
	0x6d, 0x49, 0x64, 0x12, 0x5d, 0x0a, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65,
	0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x22, 0x4a, 0x0a, 0x17, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x4d, 0x65, 0x74, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a,
	0x0b, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x22, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x64, 0x52, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x73, 0x22, 0x64,
	0x0a, 0x19, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x4d, 0x65,
	0x74, 0x61, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0c, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61,
	0x62, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d,
	0x65, 0x74, 0x61, 0x73, 0x22, 0xe0, 0x04, 0x0a, 0x16, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x28, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x09,
	0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0c, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x42,
	0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x46, 0x0a, 0x0e,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x12, 0x43, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73,
	0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x48, 0x00, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73,
	0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x12, 0x42, 0x0a, 0x07, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x2d, 0x0a,
	0x13, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62, 0x79, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x6b, 0x65, 0x79, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x42, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x41, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x34, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x42, 0x79, 0x73, 0x12, 0x48, 0x0a, 0x0f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62,
	0x79, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x43, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x61, 0x72, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x48, 0x01, 0x52, 0x0d, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x42,
	0x12, 0x0a, 0x10, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62, 0x79,
	0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0xe8, 0x01, 0x0a, 0x17, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x10,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0x9e, 0x04, 0x0a, 0x17, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28,
	0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x09, 0x64,
	0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x31, 0x0a, 0x0c,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x04, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x32, 0x02,
	0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12,
	0x46, 0x0a, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x43, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x76, 0x69,
	0x6f, 0x75, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x48, 0x00, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x76, 0x69,
	0x6f, 0x75, 0x73, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x13,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62, 0x79, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6b,
	0x65, 0x79, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x42, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x34, 0x0a, 0x09, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79,
	0x73, 0x12, 0x48, 0x0a, 0x0f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62, 0x79, 0x5f, 0x70, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61,
	0x72, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x48, 0x01, 0x52, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x42, 0x79, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f,
	0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x42,
	0x12, 0x0a, 0x10, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62, 0x79, 0x5f, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x22, 0x33, 0x0a, 0x18, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x32, 0xf8, 0x08, 0x0a, 0x0d, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x75, 0x0a, 0x10, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x50, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x7b, 0x0a, 0x12, 0x4d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46,
	0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x61, 0x76, 0x6f, 0x72,
	0x69, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x61,
	0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6d,
	0x0a, 0x19, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x38, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x76, 0x0a,
	0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x73, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x0f, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a, 0x10, 0x45, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x6c, 0x0a, 0x0a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x50, 0x61, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x50, 0x61, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x66,
	0x0a, 0x0a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x73, 0x12, 0x2b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x65,
	0x74, 0x61, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x63, 0x0a, 0x09, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x66, 0x0a, 0x0a, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x42, 0x7e, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x32, 0x3b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x61, 0x70,
	0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_reporting_v2_reports_api_proto_rawDescOnce sync.Once
	file_moego_api_reporting_v2_reports_api_proto_rawDescData = file_moego_api_reporting_v2_reports_api_proto_rawDesc
)

func file_moego_api_reporting_v2_reports_api_proto_rawDescGZIP() []byte {
	file_moego_api_reporting_v2_reports_api_proto_rawDescOnce.Do(func() {
		file_moego_api_reporting_v2_reports_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_reporting_v2_reports_api_proto_rawDescData)
	})
	return file_moego_api_reporting_v2_reports_api_proto_rawDescData
}

var file_moego_api_reporting_v2_reports_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_api_reporting_v2_reports_api_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_moego_api_reporting_v2_reports_api_proto_goTypes = []interface{}{
	(MarkReportFavoriteRequest_Action)(0),    // 0: moego.api.reporting.v2.MarkReportFavoriteRequest.Action
	(*QueryReportPagesRequest)(nil),          // 1: moego.api.reporting.v2.QueryReportPagesRequest
	(*QueryReportPagesResponse)(nil),         // 2: moego.api.reporting.v2.QueryReportPagesResponse
	(*MarkReportFavoriteRequest)(nil),        // 3: moego.api.reporting.v2.MarkReportFavoriteRequest
	(*MarkReportFavoriteResponse)(nil),       // 4: moego.api.reporting.v2.MarkReportFavoriteResponse
	(*SaveReportCustomizeConfigRequest)(nil), // 5: moego.api.reporting.v2.SaveReportCustomizeConfigRequest
	(*QueryReportMetasRequest)(nil),          // 6: moego.api.reporting.v2.QueryReportMetasRequest
	(*QueryReportsMetasResponse)(nil),        // 7: moego.api.reporting.v2.QueryReportsMetasResponse
	(*FetchReportDataRequest)(nil),           // 8: moego.api.reporting.v2.FetchReportDataRequest
	(*FetchReportDataResponse)(nil),          // 9: moego.api.reporting.v2.FetchReportDataResponse
	(*ExportReportDataRequest)(nil),          // 10: moego.api.reporting.v2.ExportReportDataRequest
	(*ExportReportDataResponse)(nil),         // 11: moego.api.reporting.v2.ExportReportDataResponse
	(v2.ReportPage_Tab)(0),                   // 12: moego.models.reporting.v2.ReportPage.Tab
	(v2.ReportingScene)(0),                   // 13: moego.models.reporting.v2.ReportingScene
	(*v2.ReportPage)(nil),                    // 14: moego.models.reporting.v2.ReportPage
	(*timestamppb.Timestamp)(nil),            // 15: google.protobuf.Timestamp
	(*v2.TableCustomizedConfig)(nil),         // 16: moego.models.reporting.v2.TableCustomizedConfig
	(*v2.TableMeta)(nil),                     // 17: moego.models.reporting.v2.TableMeta
	(*interval.Interval)(nil),                // 18: google.type.Interval
	(*v2.FilterRequest)(nil),                 // 19: moego.models.reporting.v2.FilterRequest
	(*v21.PaginationRequest)(nil),            // 20: moego.utils.v2.PaginationRequest
	(*v21.OrderBy)(nil),                      // 21: moego.utils.v2.OrderBy
	(calendarperiod.CalendarPeriod)(0),       // 22: google.type.CalendarPeriod
	(*v2.TableData)(nil),                     // 23: moego.models.reporting.v2.TableData
	(*v21.PaginationResponse)(nil),           // 24: moego.utils.v2.PaginationResponse
	(*v2.QueryPageMetaParams)(nil),           // 25: moego.models.reporting.v2.QueryPageMetaParams
	(*v2.QueryMetasParams)(nil),              // 26: moego.models.reporting.v2.QueryMetasParams
	(*v2.FetchDataParams)(nil),               // 27: moego.models.reporting.v2.FetchDataParams
	(*v2.ExportDataParams)(nil),              // 28: moego.models.reporting.v2.ExportDataParams
	(*emptypb.Empty)(nil),                    // 29: google.protobuf.Empty
	(*v2.QueryPageMetaResult)(nil),           // 30: moego.models.reporting.v2.QueryPageMetaResult
	(*v2.QueryMetasResult)(nil),              // 31: moego.models.reporting.v2.QueryMetasResult
	(*v2.FetchDataResult)(nil),               // 32: moego.models.reporting.v2.FetchDataResult
	(*v2.ExportDataResult)(nil),              // 33: moego.models.reporting.v2.ExportDataResult
}
var file_moego_api_reporting_v2_reports_api_proto_depIdxs = []int32{
	12, // 0: moego.api.reporting.v2.QueryReportPagesRequest.tabs:type_name -> moego.models.reporting.v2.ReportPage.Tab
	13, // 1: moego.api.reporting.v2.QueryReportPagesRequest.reporting_type:type_name -> moego.models.reporting.v2.ReportingScene
	14, // 2: moego.api.reporting.v2.QueryReportPagesResponse.pages:type_name -> moego.models.reporting.v2.ReportPage
	15, // 3: moego.api.reporting.v2.QueryReportPagesResponse.last_synced_time:type_name -> google.protobuf.Timestamp
	0,  // 4: moego.api.reporting.v2.MarkReportFavoriteRequest.action:type_name -> moego.api.reporting.v2.MarkReportFavoriteRequest.Action
	16, // 5: moego.api.reporting.v2.SaveReportCustomizeConfigRequest.customized_config:type_name -> moego.models.reporting.v2.TableCustomizedConfig
	17, // 6: moego.api.reporting.v2.QueryReportsMetasResponse.report_metas:type_name -> moego.models.reporting.v2.TableMeta
	18, // 7: moego.api.reporting.v2.FetchReportDataRequest.current_period:type_name -> google.type.Interval
	18, // 8: moego.api.reporting.v2.FetchReportDataRequest.previous_period:type_name -> google.type.Interval
	19, // 9: moego.api.reporting.v2.FetchReportDataRequest.filters:type_name -> moego.models.reporting.v2.FilterRequest
	20, // 10: moego.api.reporting.v2.FetchReportDataRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	21, // 11: moego.api.reporting.v2.FetchReportDataRequest.order_bys:type_name -> moego.utils.v2.OrderBy
	22, // 12: moego.api.reporting.v2.FetchReportDataRequest.group_by_period:type_name -> google.type.CalendarPeriod
	23, // 13: moego.api.reporting.v2.FetchReportDataResponse.table_data:type_name -> moego.models.reporting.v2.TableData
	24, // 14: moego.api.reporting.v2.FetchReportDataResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	15, // 15: moego.api.reporting.v2.FetchReportDataResponse.last_synced_time:type_name -> google.protobuf.Timestamp
	19, // 16: moego.api.reporting.v2.ExportReportDataRequest.filters:type_name -> moego.models.reporting.v2.FilterRequest
	18, // 17: moego.api.reporting.v2.ExportReportDataRequest.current_period:type_name -> google.type.Interval
	18, // 18: moego.api.reporting.v2.ExportReportDataRequest.previous_period:type_name -> google.type.Interval
	21, // 19: moego.api.reporting.v2.ExportReportDataRequest.order_bys:type_name -> moego.utils.v2.OrderBy
	22, // 20: moego.api.reporting.v2.ExportReportDataRequest.group_by_period:type_name -> google.type.CalendarPeriod
	1,  // 21: moego.api.reporting.v2.ReportService.QueryReportPages:input_type -> moego.api.reporting.v2.QueryReportPagesRequest
	3,  // 22: moego.api.reporting.v2.ReportService.MarkReportFavorite:input_type -> moego.api.reporting.v2.MarkReportFavoriteRequest
	5,  // 23: moego.api.reporting.v2.ReportService.SaveReportCustomizeConfig:input_type -> moego.api.reporting.v2.SaveReportCustomizeConfigRequest
	6,  // 24: moego.api.reporting.v2.ReportService.QueryReportMetas:input_type -> moego.api.reporting.v2.QueryReportMetasRequest
	8,  // 25: moego.api.reporting.v2.ReportService.FetchReportData:input_type -> moego.api.reporting.v2.FetchReportDataRequest
	10, // 26: moego.api.reporting.v2.ReportService.ExportReportData:input_type -> moego.api.reporting.v2.ExportReportDataRequest
	25, // 27: moego.api.reporting.v2.ReportService.QueryPages:input_type -> moego.models.reporting.v2.QueryPageMetaParams
	26, // 28: moego.api.reporting.v2.ReportService.QueryMetas:input_type -> moego.models.reporting.v2.QueryMetasParams
	27, // 29: moego.api.reporting.v2.ReportService.FetchData:input_type -> moego.models.reporting.v2.FetchDataParams
	28, // 30: moego.api.reporting.v2.ReportService.ExportData:input_type -> moego.models.reporting.v2.ExportDataParams
	2,  // 31: moego.api.reporting.v2.ReportService.QueryReportPages:output_type -> moego.api.reporting.v2.QueryReportPagesResponse
	4,  // 32: moego.api.reporting.v2.ReportService.MarkReportFavorite:output_type -> moego.api.reporting.v2.MarkReportFavoriteResponse
	29, // 33: moego.api.reporting.v2.ReportService.SaveReportCustomizeConfig:output_type -> google.protobuf.Empty
	7,  // 34: moego.api.reporting.v2.ReportService.QueryReportMetas:output_type -> moego.api.reporting.v2.QueryReportsMetasResponse
	9,  // 35: moego.api.reporting.v2.ReportService.FetchReportData:output_type -> moego.api.reporting.v2.FetchReportDataResponse
	11, // 36: moego.api.reporting.v2.ReportService.ExportReportData:output_type -> moego.api.reporting.v2.ExportReportDataResponse
	30, // 37: moego.api.reporting.v2.ReportService.QueryPages:output_type -> moego.models.reporting.v2.QueryPageMetaResult
	31, // 38: moego.api.reporting.v2.ReportService.QueryMetas:output_type -> moego.models.reporting.v2.QueryMetasResult
	32, // 39: moego.api.reporting.v2.ReportService.FetchData:output_type -> moego.models.reporting.v2.FetchDataResult
	33, // 40: moego.api.reporting.v2.ReportService.ExportData:output_type -> moego.models.reporting.v2.ExportDataResult
	31, // [31:41] is the sub-list for method output_type
	21, // [21:31] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_moego_api_reporting_v2_reports_api_proto_init() }
func file_moego_api_reporting_v2_reports_api_proto_init() {
	if File_moego_api_reporting_v2_reports_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_reporting_v2_reports_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryReportPagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_reporting_v2_reports_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryReportPagesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_reporting_v2_reports_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkReportFavoriteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_reporting_v2_reports_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkReportFavoriteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_reporting_v2_reports_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveReportCustomizeConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_reporting_v2_reports_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryReportMetasRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_reporting_v2_reports_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryReportsMetasResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_reporting_v2_reports_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchReportDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_reporting_v2_reports_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchReportDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_reporting_v2_reports_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportReportDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_reporting_v2_reports_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportReportDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_reporting_v2_reports_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_reporting_v2_reports_api_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_api_reporting_v2_reports_api_proto_msgTypes[9].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_reporting_v2_reports_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_reporting_v2_reports_api_proto_goTypes,
		DependencyIndexes: file_moego_api_reporting_v2_reports_api_proto_depIdxs,
		EnumInfos:         file_moego_api_reporting_v2_reports_api_proto_enumTypes,
		MessageInfos:      file_moego_api_reporting_v2_reports_api_proto_msgTypes,
	}.Build()
	File_moego_api_reporting_v2_reports_api_proto = out.File
	file_moego_api_reporting_v2_reports_api_proto_rawDesc = nil
	file_moego_api_reporting_v2_reports_api_proto_goTypes = nil
	file_moego_api_reporting_v2_reports_api_proto_depIdxs = nil
}
