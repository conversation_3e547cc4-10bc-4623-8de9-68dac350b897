// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/zendesk/v1/zendesk_api.proto

package zendeskapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ZendeskServiceClient is the client API for ZendeskService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ZendeskServiceClient interface {
	// Get zendesk JWT string.
	GetZendeskJwt(ctx context.Context, in *GetZendeskJwtRequest, opts ...grpc.CallOption) (*GetZendeskJwtResponse, error)
}

type zendeskServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewZendeskServiceClient(cc grpc.ClientConnInterface) ZendeskServiceClient {
	return &zendeskServiceClient{cc}
}

func (c *zendeskServiceClient) GetZendeskJwt(ctx context.Context, in *GetZendeskJwtRequest, opts ...grpc.CallOption) (*GetZendeskJwtResponse, error) {
	out := new(GetZendeskJwtResponse)
	err := c.cc.Invoke(ctx, "/moego.api.zendesk.v1.ZendeskService/GetZendeskJwt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ZendeskServiceServer is the server API for ZendeskService service.
// All implementations must embed UnimplementedZendeskServiceServer
// for forward compatibility
type ZendeskServiceServer interface {
	// Get zendesk JWT string.
	GetZendeskJwt(context.Context, *GetZendeskJwtRequest) (*GetZendeskJwtResponse, error)
	mustEmbedUnimplementedZendeskServiceServer()
}

// UnimplementedZendeskServiceServer must be embedded to have forward compatible implementations.
type UnimplementedZendeskServiceServer struct {
}

func (UnimplementedZendeskServiceServer) GetZendeskJwt(context.Context, *GetZendeskJwtRequest) (*GetZendeskJwtResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetZendeskJwt not implemented")
}
func (UnimplementedZendeskServiceServer) mustEmbedUnimplementedZendeskServiceServer() {}

// UnsafeZendeskServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ZendeskServiceServer will
// result in compilation errors.
type UnsafeZendeskServiceServer interface {
	mustEmbedUnimplementedZendeskServiceServer()
}

func RegisterZendeskServiceServer(s grpc.ServiceRegistrar, srv ZendeskServiceServer) {
	s.RegisterService(&ZendeskService_ServiceDesc, srv)
}

func _ZendeskService_GetZendeskJwt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetZendeskJwtRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZendeskServiceServer).GetZendeskJwt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.zendesk.v1.ZendeskService/GetZendeskJwt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZendeskServiceServer).GetZendeskJwt(ctx, req.(*GetZendeskJwtRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ZendeskService_ServiceDesc is the grpc.ServiceDesc for ZendeskService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ZendeskService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.zendesk.v1.ZendeskService",
	HandlerType: (*ZendeskServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetZendeskJwt",
			Handler:    _ZendeskService_GetZendeskJwt_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/zendesk/v1/zendesk_api.proto",
}
