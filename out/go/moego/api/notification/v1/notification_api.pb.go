// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/notification/v1/notification_api.proto

package notificationapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// String tab, Integer startingAfter, Integer limit
// get notification param
type GetNotificationsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// specific business ids, empty for all
	BusinessIds []int64 `protobuf:"varint,1,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// tab type
	TabType v1.NotificationTabType `protobuf:"varint,2,opt,name=tab_type,json=tabType,proto3,enum=moego.models.message.v1.NotificationTabType" json:"tab_type,omitempty"`
	// starting after
	StartingAfter *int32 `protobuf:"varint,3,opt,name=starting_after,json=startingAfter,proto3,oneof" json:"starting_after,omitempty"`
	// limit
	Limit int32 `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *GetNotificationsParams) Reset() {
	*x = GetNotificationsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNotificationsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationsParams) ProtoMessage() {}

func (x *GetNotificationsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationsParams.ProtoReflect.Descriptor instead.
func (*GetNotificationsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_notification_v1_notification_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetNotificationsParams) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *GetNotificationsParams) GetTabType() v1.NotificationTabType {
	if x != nil {
		return x.TabType
	}
	return v1.NotificationTabType(0)
}

func (x *GetNotificationsParams) GetStartingAfter() int32 {
	if x != nil && x.StartingAfter != nil {
		return *x.StartingAfter
	}
	return 0
}

func (x *GetNotificationsParams) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

// get notification result
type GetNotificationsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// notification list
	NotificationListData []*GetNotificationsResult_NotificationListData `protobuf:"bytes,1,rep,name=notification_list_data,json=notificationListData,proto3" json:"notification_list_data,omitempty"`
	// has more
	HasMore bool `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
}

func (x *GetNotificationsResult) Reset() {
	*x = GetNotificationsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNotificationsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationsResult) ProtoMessage() {}

func (x *GetNotificationsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationsResult.ProtoReflect.Descriptor instead.
func (*GetNotificationsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_notification_v1_notification_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetNotificationsResult) GetNotificationListData() []*GetNotificationsResult_NotificationListData {
	if x != nil {
		return x.NotificationListData
	}
	return nil
}

func (x *GetNotificationsResult) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

// read notification params
type NotificationReadParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// notification record id
	NotificationId int64 `protobuf:"varint,1,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	// source
	Source *string `protobuf:"bytes,2,opt,name=source,proto3,oneof" json:"source,omitempty"`
}

func (x *NotificationReadParams) Reset() {
	*x = NotificationReadParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotificationReadParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationReadParams) ProtoMessage() {}

func (x *NotificationReadParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationReadParams.ProtoReflect.Descriptor instead.
func (*NotificationReadParams) Descriptor() ([]byte, []int) {
	return file_moego_api_notification_v1_notification_api_proto_rawDescGZIP(), []int{2}
}

func (x *NotificationReadParams) GetNotificationId() int64 {
	if x != nil {
		return x.NotificationId
	}
	return 0
}

func (x *NotificationReadParams) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

// read notification result
type NotificationReadResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NotificationReadResult) Reset() {
	*x = NotificationReadResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotificationReadResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationReadResult) ProtoMessage() {}

func (x *NotificationReadResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationReadResult.ProtoReflect.Descriptor instead.
func (*NotificationReadResult) Descriptor() ([]byte, []int) {
	return file_moego_api_notification_v1_notification_api_proto_rawDescGZIP(), []int{3}
}

// dismiss notification params
type NotificationDismissParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// notification record id
	NotificationId int64 `protobuf:"varint,1,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
}

func (x *NotificationDismissParams) Reset() {
	*x = NotificationDismissParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotificationDismissParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationDismissParams) ProtoMessage() {}

func (x *NotificationDismissParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationDismissParams.ProtoReflect.Descriptor instead.
func (*NotificationDismissParams) Descriptor() ([]byte, []int) {
	return file_moego_api_notification_v1_notification_api_proto_rawDescGZIP(), []int{4}
}

func (x *NotificationDismissParams) GetNotificationId() int64 {
	if x != nil {
		return x.NotificationId
	}
	return 0
}

// dismiss notification result
type NotificationDismissResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NotificationDismissResult) Reset() {
	*x = NotificationDismissResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotificationDismissResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationDismissResult) ProtoMessage() {}

func (x *NotificationDismissResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationDismissResult.ProtoReflect.Descriptor instead.
func (*NotificationDismissResult) Descriptor() ([]byte, []int) {
	return file_moego_api_notification_v1_notification_api_proto_rawDescGZIP(), []int{5}
}

// read all notification params
type NotificationReadAllParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// specific business ids, empty means all business in the token company
	BusinessIds []int64 `protobuf:"varint,1,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// tab type
	TabType v1.NotificationTabType `protobuf:"varint,2,opt,name=tab_type,json=tabType,proto3,enum=moego.models.message.v1.NotificationTabType" json:"tab_type,omitempty"`
	// source
	Source *string `protobuf:"bytes,3,opt,name=source,proto3,oneof" json:"source,omitempty"`
	// specific type,empty for all
	Type []string `protobuf:"bytes,4,rep,name=type,proto3" json:"type,omitempty"`
}

func (x *NotificationReadAllParams) Reset() {
	*x = NotificationReadAllParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotificationReadAllParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationReadAllParams) ProtoMessage() {}

func (x *NotificationReadAllParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationReadAllParams.ProtoReflect.Descriptor instead.
func (*NotificationReadAllParams) Descriptor() ([]byte, []int) {
	return file_moego_api_notification_v1_notification_api_proto_rawDescGZIP(), []int{6}
}

func (x *NotificationReadAllParams) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *NotificationReadAllParams) GetTabType() v1.NotificationTabType {
	if x != nil {
		return x.TabType
	}
	return v1.NotificationTabType(0)
}

func (x *NotificationReadAllParams) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *NotificationReadAllParams) GetType() []string {
	if x != nil {
		return x.Type
	}
	return nil
}

// read all notification result
type NotificationReadAllResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// read count
	ReadCount int32 `protobuf:"varint,1,opt,name=read_count,json=readCount,proto3" json:"read_count,omitempty"`
}

func (x *NotificationReadAllResult) Reset() {
	*x = NotificationReadAllResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotificationReadAllResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationReadAllResult) ProtoMessage() {}

func (x *NotificationReadAllResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationReadAllResult.ProtoReflect.Descriptor instead.
func (*NotificationReadAllResult) Descriptor() ([]byte, []int) {
	return file_moego_api_notification_v1_notification_api_proto_rawDescGZIP(), []int{7}
}

func (x *NotificationReadAllResult) GetReadCount() int32 {
	if x != nil {
		return x.ReadCount
	}
	return 0
}

// get notification unread count params
type GetNotificationUnreadCountParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// specific business ids
	BusinessIds []int64 `protobuf:"varint,1,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *GetNotificationUnreadCountParams) Reset() {
	*x = GetNotificationUnreadCountParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNotificationUnreadCountParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationUnreadCountParams) ProtoMessage() {}

func (x *GetNotificationUnreadCountParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationUnreadCountParams.ProtoReflect.Descriptor instead.
func (*GetNotificationUnreadCountParams) Descriptor() ([]byte, []int) {
	return file_moego_api_notification_v1_notification_api_proto_rawDescGZIP(), []int{8}
}

func (x *GetNotificationUnreadCountParams) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// get notification unread count result
type GetNotificationUnreadCountResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id to unread count map
	BusinessIdToUnreadCount map[int64]*v1.NotificationUnreadCountInfoDef `protobuf:"bytes,1,rep,name=business_id_to_unread_count,json=businessIdToUnreadCount,proto3" json:"business_id_to_unread_count,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetNotificationUnreadCountResult) Reset() {
	*x = GetNotificationUnreadCountResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNotificationUnreadCountResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationUnreadCountResult) ProtoMessage() {}

func (x *GetNotificationUnreadCountResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationUnreadCountResult.ProtoReflect.Descriptor instead.
func (*GetNotificationUnreadCountResult) Descriptor() ([]byte, []int) {
	return file_moego_api_notification_v1_notification_api_proto_rawDescGZIP(), []int{9}
}

func (x *GetNotificationUnreadCountResult) GetBusinessIdToUnreadCount() map[int64]*v1.NotificationUnreadCountInfoDef {
	if x != nil {
		return x.BusinessIdToUnreadCount
	}
	return nil
}

// notification list  data
type GetNotificationsResult_NotificationListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// notification record model view
	// notification id
	NotificationId int64 `protobuf:"varint,1,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// send time
	SendTime int32 `protobuf:"varint,3,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	// read time
	ReadTime int32 `protobuf:"varint,4,opt,name=read_time,json=readTime,proto3" json:"read_time,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,5,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// create time
	CreateTime int32 `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// notification model view begin 30
	// type
	Type string `protobuf:"bytes,30,opt,name=type,proto3" json:"type,omitempty"`
	// title
	Title string `protobuf:"bytes,31,opt,name=title,proto3" json:"title,omitempty"`
	// body
	Body string `protobuf:"bytes,32,opt,name=body,proto3" json:"body,omitempty"`
	// extra
	Extra *structpb.Struct `protobuf:"bytes,33,opt,name=extra,proto3" json:"extra,omitempty"`
}

func (x *GetNotificationsResult_NotificationListData) Reset() {
	*x = GetNotificationsResult_NotificationListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNotificationsResult_NotificationListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationsResult_NotificationListData) ProtoMessage() {}

func (x *GetNotificationsResult_NotificationListData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_notification_v1_notification_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationsResult_NotificationListData.ProtoReflect.Descriptor instead.
func (*GetNotificationsResult_NotificationListData) Descriptor() ([]byte, []int) {
	return file_moego_api_notification_v1_notification_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *GetNotificationsResult_NotificationListData) GetNotificationId() int64 {
	if x != nil {
		return x.NotificationId
	}
	return 0
}

func (x *GetNotificationsResult_NotificationListData) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetNotificationsResult_NotificationListData) GetSendTime() int32 {
	if x != nil {
		return x.SendTime
	}
	return 0
}

func (x *GetNotificationsResult_NotificationListData) GetReadTime() int32 {
	if x != nil {
		return x.ReadTime
	}
	return 0
}

func (x *GetNotificationsResult_NotificationListData) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetNotificationsResult_NotificationListData) GetCreateTime() int32 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *GetNotificationsResult_NotificationListData) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetNotificationsResult_NotificationListData) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GetNotificationsResult_NotificationListData) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *GetNotificationsResult_NotificationListData) GetExtra() *structpb.Struct {
	if x != nil {
		return x.Extra
	}
	return nil
}

var File_moego_api_notification_v1_notification_api_proto protoreflect.FileDescriptor

var file_moego_api_notification_v1_notification_api_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8b, 0x02, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x31, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08,
	0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x53, 0x0a, 0x08, 0x74, 0x61, 0x62, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61,
	0x62, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20,
	0x00, 0x52, 0x07, 0x74, 0x61, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x0a, 0x0e, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x20, 0x00, 0x40, 0x01, 0x48, 0x00, 0x52,
	0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x41, 0x66, 0x74, 0x65, 0x72, 0x88, 0x01,
	0x01, 0x12, 0x1f, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x10, 0x64, 0x20, 0x00, 0x52, 0x05, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f,
	0x61, 0x66, 0x74, 0x65, 0x72, 0x22, 0xfb, 0x03, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x7c, 0x0a, 0x16, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x14, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19,
	0x0a, 0x08, 0x68, 0x61, 0x73, 0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x1a, 0xc7, 0x02, 0x0a, 0x14, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x27, 0x0a, 0x0f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x73, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x61,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x65,
	0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x62, 0x6f, 0x64, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x21, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x05, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x22, 0x7c, 0x0a, 0x16, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x30, 0x0a,
	0x0f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x25, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x08, 0x48, 0x00, 0x52, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x22, 0x18, 0x0a, 0x16, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x4d, 0x0a, 0x19, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x73, 0x6d, 0x69,
	0x73, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x30, 0x0a, 0x0f, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x1b, 0x0a, 0x19, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x73, 0x6d, 0x69, 0x73,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xfe, 0x01, 0x0a, 0x19, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x41, 0x6c, 0x6c, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x31, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b,
	0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x53, 0x0a, 0x08, 0x74, 0x61, 0x62, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x61, 0x62, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x07, 0x74, 0x61, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x08, 0x48, 0x00, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x10, 0x64, 0x18, 0x01, 0x22, 0x05,
	0x72, 0x03, 0x18, 0x80, 0x08, 0x28, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x42, 0x09, 0x0a,
	0x07, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x3a, 0x0a, 0x19, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x41, 0x6c, 0x6c, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x72, 0x65, 0x61, 0x64, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x57, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x33, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10,
	0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x22, 0xc1, 0x02,
	0x0a, 0x20, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x96, 0x01, 0x0a, 0x1b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x58, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x54, 0x6f, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x17, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x54, 0x6f,
	0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x83, 0x01, 0x0a, 0x1c,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x54, 0x6f, 0x55, 0x6e, 0x72, 0x65,
	0x61, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4d,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x44, 0x65, 0x66, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x32, 0xb4, 0x05, 0x0a, 0x13, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7a, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x7a, 0x0a, 0x10, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x00, 0x12, 0x83, 0x01, 0x0a, 0x13, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x73, 0x6d, 0x69, 0x73, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x83, 0x01, 0x0a, 0x13, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x41, 0x6c, 0x6c, 0x12,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x41, 0x6c, 0x6c, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x61, 0x64, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x98, 0x01,
	0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x42, 0x87, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x3b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x70, 0x69,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_notification_v1_notification_api_proto_rawDescOnce sync.Once
	file_moego_api_notification_v1_notification_api_proto_rawDescData = file_moego_api_notification_v1_notification_api_proto_rawDesc
)

func file_moego_api_notification_v1_notification_api_proto_rawDescGZIP() []byte {
	file_moego_api_notification_v1_notification_api_proto_rawDescOnce.Do(func() {
		file_moego_api_notification_v1_notification_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_notification_v1_notification_api_proto_rawDescData)
	})
	return file_moego_api_notification_v1_notification_api_proto_rawDescData
}

var file_moego_api_notification_v1_notification_api_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_api_notification_v1_notification_api_proto_goTypes = []interface{}{
	(*GetNotificationsParams)(nil),                      // 0: moego.api.notification.v1.GetNotificationsParams
	(*GetNotificationsResult)(nil),                      // 1: moego.api.notification.v1.GetNotificationsResult
	(*NotificationReadParams)(nil),                      // 2: moego.api.notification.v1.NotificationReadParams
	(*NotificationReadResult)(nil),                      // 3: moego.api.notification.v1.NotificationReadResult
	(*NotificationDismissParams)(nil),                   // 4: moego.api.notification.v1.NotificationDismissParams
	(*NotificationDismissResult)(nil),                   // 5: moego.api.notification.v1.NotificationDismissResult
	(*NotificationReadAllParams)(nil),                   // 6: moego.api.notification.v1.NotificationReadAllParams
	(*NotificationReadAllResult)(nil),                   // 7: moego.api.notification.v1.NotificationReadAllResult
	(*GetNotificationUnreadCountParams)(nil),            // 8: moego.api.notification.v1.GetNotificationUnreadCountParams
	(*GetNotificationUnreadCountResult)(nil),            // 9: moego.api.notification.v1.GetNotificationUnreadCountResult
	(*GetNotificationsResult_NotificationListData)(nil), // 10: moego.api.notification.v1.GetNotificationsResult.NotificationListData
	nil,                         // 11: moego.api.notification.v1.GetNotificationUnreadCountResult.BusinessIdToUnreadCountEntry
	(v1.NotificationTabType)(0), // 12: moego.models.message.v1.NotificationTabType
	(*structpb.Struct)(nil),     // 13: google.protobuf.Struct
	(*v1.NotificationUnreadCountInfoDef)(nil), // 14: moego.models.message.v1.NotificationUnreadCountInfoDef
}
var file_moego_api_notification_v1_notification_api_proto_depIdxs = []int32{
	12, // 0: moego.api.notification.v1.GetNotificationsParams.tab_type:type_name -> moego.models.message.v1.NotificationTabType
	10, // 1: moego.api.notification.v1.GetNotificationsResult.notification_list_data:type_name -> moego.api.notification.v1.GetNotificationsResult.NotificationListData
	12, // 2: moego.api.notification.v1.NotificationReadAllParams.tab_type:type_name -> moego.models.message.v1.NotificationTabType
	11, // 3: moego.api.notification.v1.GetNotificationUnreadCountResult.business_id_to_unread_count:type_name -> moego.api.notification.v1.GetNotificationUnreadCountResult.BusinessIdToUnreadCountEntry
	13, // 4: moego.api.notification.v1.GetNotificationsResult.NotificationListData.extra:type_name -> google.protobuf.Struct
	14, // 5: moego.api.notification.v1.GetNotificationUnreadCountResult.BusinessIdToUnreadCountEntry.value:type_name -> moego.models.message.v1.NotificationUnreadCountInfoDef
	0,  // 6: moego.api.notification.v1.NotificationService.GetNotifications:input_type -> moego.api.notification.v1.GetNotificationsParams
	2,  // 7: moego.api.notification.v1.NotificationService.NotificationRead:input_type -> moego.api.notification.v1.NotificationReadParams
	4,  // 8: moego.api.notification.v1.NotificationService.NotificationDismiss:input_type -> moego.api.notification.v1.NotificationDismissParams
	6,  // 9: moego.api.notification.v1.NotificationService.NotificationReadAll:input_type -> moego.api.notification.v1.NotificationReadAllParams
	8,  // 10: moego.api.notification.v1.NotificationService.GetNotificationUnreadCount:input_type -> moego.api.notification.v1.GetNotificationUnreadCountParams
	1,  // 11: moego.api.notification.v1.NotificationService.GetNotifications:output_type -> moego.api.notification.v1.GetNotificationsResult
	3,  // 12: moego.api.notification.v1.NotificationService.NotificationRead:output_type -> moego.api.notification.v1.NotificationReadResult
	5,  // 13: moego.api.notification.v1.NotificationService.NotificationDismiss:output_type -> moego.api.notification.v1.NotificationDismissResult
	7,  // 14: moego.api.notification.v1.NotificationService.NotificationReadAll:output_type -> moego.api.notification.v1.NotificationReadAllResult
	9,  // 15: moego.api.notification.v1.NotificationService.GetNotificationUnreadCount:output_type -> moego.api.notification.v1.GetNotificationUnreadCountResult
	11, // [11:16] is the sub-list for method output_type
	6,  // [6:11] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_moego_api_notification_v1_notification_api_proto_init() }
func file_moego_api_notification_v1_notification_api_proto_init() {
	if File_moego_api_notification_v1_notification_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_notification_v1_notification_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNotificationsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_notification_v1_notification_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNotificationsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_notification_v1_notification_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotificationReadParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_notification_v1_notification_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotificationReadResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_notification_v1_notification_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotificationDismissParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_notification_v1_notification_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotificationDismissResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_notification_v1_notification_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotificationReadAllParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_notification_v1_notification_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotificationReadAllResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_notification_v1_notification_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNotificationUnreadCountParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_notification_v1_notification_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNotificationUnreadCountResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_notification_v1_notification_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNotificationsResult_NotificationListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_notification_v1_notification_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_notification_v1_notification_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_api_notification_v1_notification_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_notification_v1_notification_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_notification_v1_notification_api_proto_goTypes,
		DependencyIndexes: file_moego_api_notification_v1_notification_api_proto_depIdxs,
		MessageInfos:      file_moego_api_notification_v1_notification_api_proto_msgTypes,
	}.Build()
	File_moego_api_notification_v1_notification_api_proto = out.File
	file_moego_api_notification_v1_notification_api_proto_rawDesc = nil
	file_moego_api_notification_v1_notification_api_proto_goTypes = nil
	file_moego_api_notification_v1_notification_api_proto_depIdxs = nil
}
