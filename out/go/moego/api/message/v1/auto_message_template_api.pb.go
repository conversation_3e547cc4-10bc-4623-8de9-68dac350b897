// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/message/v1/auto_message_template_api.proto

package messageapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Get preview template message content params
type GetPreviewTemplateParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto message for appointment
	ForAppointment *v1.AutoMessageAppointmentDef `protobuf:"bytes,1,opt,name=for_appointment,json=forAppointment,proto3" json:"for_appointment,omitempty"`
}

func (x *GetPreviewTemplateParams) Reset() {
	*x = GetPreviewTemplateParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_auto_message_template_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPreviewTemplateParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPreviewTemplateParams) ProtoMessage() {}

func (x *GetPreviewTemplateParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_auto_message_template_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPreviewTemplateParams.ProtoReflect.Descriptor instead.
func (*GetPreviewTemplateParams) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_auto_message_template_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetPreviewTemplateParams) GetForAppointment() *v1.AutoMessageAppointmentDef {
	if x != nil {
		return x.ForAppointment
	}
	return nil
}

// Get preview template message content result
type GetPreviewTemplateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template message content, contains template variables
	TemplateContent string `protobuf:"bytes,1,opt,name=template_content,json=templateContent,proto3" json:"template_content,omitempty"`
	// filled message content, template variables replaced with values
	PreviewContent string `protobuf:"bytes,2,opt,name=preview_content,json=previewContent,proto3" json:"preview_content,omitempty"`
}

func (x *GetPreviewTemplateResult) Reset() {
	*x = GetPreviewTemplateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_auto_message_template_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPreviewTemplateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPreviewTemplateResult) ProtoMessage() {}

func (x *GetPreviewTemplateResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_auto_message_template_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPreviewTemplateResult.ProtoReflect.Descriptor instead.
func (*GetPreviewTemplateResult) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_auto_message_template_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetPreviewTemplateResult) GetTemplateContent() string {
	if x != nil {
		return x.TemplateContent
	}
	return ""
}

func (x *GetPreviewTemplateResult) GetPreviewContent() string {
	if x != nil {
		return x.PreviewContent
	}
	return ""
}

var File_moego_api_message_v1_auto_message_template_api_proto protoreflect.FileDescriptor

var file_moego_api_message_v1_auto_message_template_api_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x38, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x77, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x5b, 0x0a, 0x0f, 0x66, 0x6f, 0x72, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x66, 0x52,
	0x0e, 0x66, 0x6f, 0x72, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x22,
	0x6e, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x32,
	0x92, 0x01, 0x0a, 0x1a, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x74,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x42, 0x78, 0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76,
	0x31, 0x3b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_message_v1_auto_message_template_api_proto_rawDescOnce sync.Once
	file_moego_api_message_v1_auto_message_template_api_proto_rawDescData = file_moego_api_message_v1_auto_message_template_api_proto_rawDesc
)

func file_moego_api_message_v1_auto_message_template_api_proto_rawDescGZIP() []byte {
	file_moego_api_message_v1_auto_message_template_api_proto_rawDescOnce.Do(func() {
		file_moego_api_message_v1_auto_message_template_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_message_v1_auto_message_template_api_proto_rawDescData)
	})
	return file_moego_api_message_v1_auto_message_template_api_proto_rawDescData
}

var file_moego_api_message_v1_auto_message_template_api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_api_message_v1_auto_message_template_api_proto_goTypes = []interface{}{
	(*GetPreviewTemplateParams)(nil),     // 0: moego.api.message.v1.GetPreviewTemplateParams
	(*GetPreviewTemplateResult)(nil),     // 1: moego.api.message.v1.GetPreviewTemplateResult
	(*v1.AutoMessageAppointmentDef)(nil), // 2: moego.models.message.v1.AutoMessageAppointmentDef
}
var file_moego_api_message_v1_auto_message_template_api_proto_depIdxs = []int32{
	2, // 0: moego.api.message.v1.GetPreviewTemplateParams.for_appointment:type_name -> moego.models.message.v1.AutoMessageAppointmentDef
	0, // 1: moego.api.message.v1.AutoMessageTemplateService.GetPreviewTemplate:input_type -> moego.api.message.v1.GetPreviewTemplateParams
	1, // 2: moego.api.message.v1.AutoMessageTemplateService.GetPreviewTemplate:output_type -> moego.api.message.v1.GetPreviewTemplateResult
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_api_message_v1_auto_message_template_api_proto_init() }
func file_moego_api_message_v1_auto_message_template_api_proto_init() {
	if File_moego_api_message_v1_auto_message_template_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_message_v1_auto_message_template_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPreviewTemplateParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_auto_message_template_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPreviewTemplateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_message_v1_auto_message_template_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_message_v1_auto_message_template_api_proto_goTypes,
		DependencyIndexes: file_moego_api_message_v1_auto_message_template_api_proto_depIdxs,
		MessageInfos:      file_moego_api_message_v1_auto_message_template_api_proto_msgTypes,
	}.Build()
	File_moego_api_message_v1_auto_message_template_api_proto = out.File
	file_moego_api_message_v1_auto_message_template_api_proto_rawDesc = nil
	file_moego_api_message_v1_auto_message_template_api_proto_goTypes = nil
	file_moego_api_message_v1_auto_message_template_api_proto_depIdxs = nil
}
