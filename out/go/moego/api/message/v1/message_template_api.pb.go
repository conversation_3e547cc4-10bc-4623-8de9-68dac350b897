// @since 2023-12-07 11:36:34
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/message/v1/message_template_api.proto

package messageapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// check message template name exist params
type CheckMessageTemplateNameExistParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template name
	TemplateName string `protobuf:"bytes,1,opt,name=template_name,json=templateName,proto3" json:"template_name,omitempty"`
}

func (x *CheckMessageTemplateNameExistParams) Reset() {
	*x = CheckMessageTemplateNameExistParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckMessageTemplateNameExistParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckMessageTemplateNameExistParams) ProtoMessage() {}

func (x *CheckMessageTemplateNameExistParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckMessageTemplateNameExistParams.ProtoReflect.Descriptor instead.
func (*CheckMessageTemplateNameExistParams) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_message_template_api_proto_rawDescGZIP(), []int{0}
}

func (x *CheckMessageTemplateNameExistParams) GetTemplateName() string {
	if x != nil {
		return x.TemplateName
	}
	return ""
}

// check message template name exist result
type CheckMessageTemplateNameExistResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// exist
	Exist bool `protobuf:"varint,1,opt,name=exist,proto3" json:"exist,omitempty"`
}

func (x *CheckMessageTemplateNameExistResult) Reset() {
	*x = CheckMessageTemplateNameExistResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckMessageTemplateNameExistResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckMessageTemplateNameExistResult) ProtoMessage() {}

func (x *CheckMessageTemplateNameExistResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckMessageTemplateNameExistResult.ProtoReflect.Descriptor instead.
func (*CheckMessageTemplateNameExistResult) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_message_template_api_proto_rawDescGZIP(), []int{1}
}

func (x *CheckMessageTemplateNameExistResult) GetExist() bool {
	if x != nil {
		return x.Exist
	}
	return false
}

// create message_template params
type CreateMessageTemplateParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the message_template def
	MessageTemplateDef *v1.MessageTemplateDef `protobuf:"bytes,1,opt,name=message_template_def,json=messageTemplateDef,proto3" json:"message_template_def,omitempty"`
}

func (x *CreateMessageTemplateParams) Reset() {
	*x = CreateMessageTemplateParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMessageTemplateParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMessageTemplateParams) ProtoMessage() {}

func (x *CreateMessageTemplateParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMessageTemplateParams.ProtoReflect.Descriptor instead.
func (*CreateMessageTemplateParams) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_message_template_api_proto_rawDescGZIP(), []int{2}
}

func (x *CreateMessageTemplateParams) GetMessageTemplateDef() *v1.MessageTemplateDef {
	if x != nil {
		return x.MessageTemplateDef
	}
	return nil
}

// create message_template result
type CreateMessageTemplateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateMessageTemplateResult) Reset() {
	*x = CreateMessageTemplateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMessageTemplateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMessageTemplateResult) ProtoMessage() {}

func (x *CreateMessageTemplateResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMessageTemplateResult.ProtoReflect.Descriptor instead.
func (*CreateMessageTemplateResult) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_message_template_api_proto_rawDescGZIP(), []int{3}
}

func (x *CreateMessageTemplateResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get message template params
type GetMessageTemplateParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetMessageTemplateParams) Reset() {
	*x = GetMessageTemplateParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMessageTemplateParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMessageTemplateParams) ProtoMessage() {}

func (x *GetMessageTemplateParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMessageTemplateParams.ProtoReflect.Descriptor instead.
func (*GetMessageTemplateParams) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_message_template_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetMessageTemplateParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get message template result
type GetMessageTemplateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the message_template detail view
	MessageTemplateDetailView *v1.MessageTemplateDetailView `protobuf:"bytes,1,opt,name=message_template_detail_view,json=messageTemplateDetailView,proto3" json:"message_template_detail_view,omitempty"`
}

func (x *GetMessageTemplateResult) Reset() {
	*x = GetMessageTemplateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMessageTemplateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMessageTemplateResult) ProtoMessage() {}

func (x *GetMessageTemplateResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMessageTemplateResult.ProtoReflect.Descriptor instead.
func (*GetMessageTemplateResult) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_message_template_api_proto_rawDescGZIP(), []int{5}
}

func (x *GetMessageTemplateResult) GetMessageTemplateDetailView() *v1.MessageTemplateDetailView {
	if x != nil {
		return x.MessageTemplateDetailView
	}
	return nil
}

// update message template params
type UpdateMessageTemplateParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the message_template def
	MessageTemplateDef *v1.MessageTemplateDef `protobuf:"bytes,2,opt,name=message_template_def,json=messageTemplateDef,proto3" json:"message_template_def,omitempty"`
}

func (x *UpdateMessageTemplateParams) Reset() {
	*x = UpdateMessageTemplateParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMessageTemplateParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMessageTemplateParams) ProtoMessage() {}

func (x *UpdateMessageTemplateParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMessageTemplateParams.ProtoReflect.Descriptor instead.
func (*UpdateMessageTemplateParams) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_message_template_api_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateMessageTemplateParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateMessageTemplateParams) GetMessageTemplateDef() *v1.MessageTemplateDef {
	if x != nil {
		return x.MessageTemplateDef
	}
	return nil
}

// update message_template result
type UpdateMessageTemplateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *UpdateMessageTemplateResult) Reset() {
	*x = UpdateMessageTemplateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMessageTemplateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMessageTemplateResult) ProtoMessage() {}

func (x *UpdateMessageTemplateResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMessageTemplateResult.ProtoReflect.Descriptor instead.
func (*UpdateMessageTemplateResult) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_message_template_api_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateMessageTemplateResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete message template params
type DeleteMessageTemplateParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteMessageTemplateParams) Reset() {
	*x = DeleteMessageTemplateParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteMessageTemplateParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMessageTemplateParams) ProtoMessage() {}

func (x *DeleteMessageTemplateParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMessageTemplateParams.ProtoReflect.Descriptor instead.
func (*DeleteMessageTemplateParams) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_message_template_api_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteMessageTemplateParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete message_template result
type DeleteMessageTemplateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteMessageTemplateResult) Reset() {
	*x = DeleteMessageTemplateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteMessageTemplateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMessageTemplateResult) ProtoMessage() {}

func (x *DeleteMessageTemplateResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMessageTemplateResult.ProtoReflect.Descriptor instead.
func (*DeleteMessageTemplateResult) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_message_template_api_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteMessageTemplateResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get message template params
// Return customize only when types is empty and with_system is null.
type GetMessageTemplatesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// keyword template name
	Keyword *string `protobuf:"bytes,1,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// if results contains system templates. Use types instead.
	//
	// Deprecated: Do not use.
	WithSystem *bool `protobuf:"varint,2,opt,name=with_system,json=withSystem,proto3,oneof" json:"with_system,omitempty"`
	// list of message template type enum.
	// In order to be compatible with older versions, customize templates returned when empty.
	Types []v1.MessageTemplateType `protobuf:"varint,3,rep,packed,name=types,proto3,enum=moego.models.message.v1.MessageTemplateType" json:"types,omitempty"`
}

func (x *GetMessageTemplatesParams) Reset() {
	*x = GetMessageTemplatesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMessageTemplatesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMessageTemplatesParams) ProtoMessage() {}

func (x *GetMessageTemplatesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMessageTemplatesParams.ProtoReflect.Descriptor instead.
func (*GetMessageTemplatesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_message_template_api_proto_rawDescGZIP(), []int{10}
}

func (x *GetMessageTemplatesParams) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

// Deprecated: Do not use.
func (x *GetMessageTemplatesParams) GetWithSystem() bool {
	if x != nil && x.WithSystem != nil {
		return *x.WithSystem
	}
	return false
}

func (x *GetMessageTemplatesParams) GetTypes() []v1.MessageTemplateType {
	if x != nil {
		return x.Types
	}
	return nil
}

// get message template holders result
type GetMessageTemplatesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// message template simple views
	MessageTemplateSimpleViews []*v1.MessageTemplateSimpleView `protobuf:"bytes,1,rep,name=message_template_simple_views,json=messageTemplateSimpleViews,proto3" json:"message_template_simple_views,omitempty"`
}

func (x *GetMessageTemplatesResult) Reset() {
	*x = GetMessageTemplatesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMessageTemplatesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMessageTemplatesResult) ProtoMessage() {}

func (x *GetMessageTemplatesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMessageTemplatesResult.ProtoReflect.Descriptor instead.
func (*GetMessageTemplatesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_message_template_api_proto_rawDescGZIP(), []int{11}
}

func (x *GetMessageTemplatesResult) GetMessageTemplateSimpleViews() []*v1.MessageTemplateSimpleView {
	if x != nil {
		return x.MessageTemplateSimpleViews
	}
	return nil
}

// get message template placeholders params
type GetMessageTemplatePlaceholdersParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// keyword template placeholder name
	Keyword *string `protobuf:"bytes,1,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// use case enum, default for USE_CASE_SAVED_REPLY
	UseCase *v1.MessageTemplateUseCase `protobuf:"varint,2,opt,name=use_case,json=useCase,proto3,enum=moego.models.message.v1.MessageTemplateUseCase,oneof" json:"use_case,omitempty"`
}

func (x *GetMessageTemplatePlaceholdersParams) Reset() {
	*x = GetMessageTemplatePlaceholdersParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMessageTemplatePlaceholdersParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMessageTemplatePlaceholdersParams) ProtoMessage() {}

func (x *GetMessageTemplatePlaceholdersParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMessageTemplatePlaceholdersParams.ProtoReflect.Descriptor instead.
func (*GetMessageTemplatePlaceholdersParams) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_message_template_api_proto_rawDescGZIP(), []int{12}
}

func (x *GetMessageTemplatePlaceholdersParams) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *GetMessageTemplatePlaceholdersParams) GetUseCase() v1.MessageTemplateUseCase {
	if x != nil && x.UseCase != nil {
		return *x.UseCase
	}
	return v1.MessageTemplateUseCase(0)
}

// get message template holders result
type GetMessageTemplatePlaceholdersResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// message template holders
	MessageTemplatePlaceholderSimpleViews []*v1.MessageTemplatePlaceholderSimpleView `protobuf:"bytes,1,rep,name=message_template_placeholder_simple_views,json=messageTemplatePlaceholderSimpleViews,proto3" json:"message_template_placeholder_simple_views,omitempty"`
}

func (x *GetMessageTemplatePlaceholdersResult) Reset() {
	*x = GetMessageTemplatePlaceholdersResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMessageTemplatePlaceholdersResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMessageTemplatePlaceholdersResult) ProtoMessage() {}

func (x *GetMessageTemplatePlaceholdersResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMessageTemplatePlaceholdersResult.ProtoReflect.Descriptor instead.
func (*GetMessageTemplatePlaceholdersResult) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_message_template_api_proto_rawDescGZIP(), []int{13}
}

func (x *GetMessageTemplatePlaceholdersResult) GetMessageTemplatePlaceholderSimpleViews() []*v1.MessageTemplatePlaceholderSimpleView {
	if x != nil {
		return x.MessageTemplatePlaceholderSimpleViews
	}
	return nil
}

// get rendered message params
type GetRenderedMessageParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// message template id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
}

func (x *GetRenderedMessageParams) Reset() {
	*x = GetRenderedMessageParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRenderedMessageParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRenderedMessageParams) ProtoMessage() {}

func (x *GetRenderedMessageParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRenderedMessageParams.ProtoReflect.Descriptor instead.
func (*GetRenderedMessageParams) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_message_template_api_proto_rawDescGZIP(), []int{14}
}

func (x *GetRenderedMessageParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetRenderedMessageParams) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// get rendered message result
type GetRenderedMessageResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// message after rendering
	RenderedMessage string `protobuf:"bytes,1,opt,name=rendered_message,json=renderedMessage,proto3" json:"rendered_message,omitempty"`
}

func (x *GetRenderedMessageResult) Reset() {
	*x = GetRenderedMessageResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRenderedMessageResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRenderedMessageResult) ProtoMessage() {}

func (x *GetRenderedMessageResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_message_v1_message_template_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRenderedMessageResult.ProtoReflect.Descriptor instead.
func (*GetRenderedMessageResult) Descriptor() ([]byte, []int) {
	return file_moego_api_message_v1_message_template_api_proto_rawDescGZIP(), []int{15}
}

func (x *GetRenderedMessageResult) GetRenderedMessage() string {
	if x != nil {
		return x.RenderedMessage
	}
	return ""
}

var File_moego_api_message_v1_message_template_api_proto protoreflect.FileDescriptor

var file_moego_api_message_v1_message_template_api_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x14, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31,
	0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x53, 0x0a, 0x23, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2c, 0x0a, 0x0d, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x1e, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x3b, 0x0a, 0x23, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x45, 0x78, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x65,
	0x78, 0x69, 0x73, 0x74, 0x22, 0x86, 0x01, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x67, 0x0a, 0x14, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x12, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x22, 0x36, 0x0a,
	0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x17, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x33, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x8f, 0x01, 0x0a, 0x18, 0x47,
	0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x73, 0x0a, 0x1c, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x19, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x22, 0x9f, 0x01, 0x0a,
	0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x67, 0x0a, 0x14, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x12, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x22, 0x36,
	0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x36, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x36,
	0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0xde, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x26, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x1e, 0x48, 0x00,
	0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0b,
	0x77, 0x69, 0x74, 0x68, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x02, 0x18, 0x01, 0x48, 0x01, 0x52, 0x0a, 0x77, 0x69, 0x74, 0x68, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x53, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x42, 0x0a, 0x0a, 0x08, 0x5f,
	0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x77, 0x69, 0x74, 0x68,
	0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x22, 0x92, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x75, 0x0a, 0x1d, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65,
	0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x1a, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x73, 0x22, 0xc4, 0x01, 0x0a,
	0x24, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x26, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x1e, 0x48,
	0x00, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x12, 0x5b, 0x0a,
	0x08, 0x75, 0x73, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x43, 0x61, 0x73, 0x65,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52, 0x07,
	0x75, 0x73, 0x65, 0x43, 0x61, 0x73, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x6b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x75, 0x73, 0x65, 0x5f, 0x63,
	0x61, 0x73, 0x65, 0x22, 0xc0, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x97, 0x01, 0x0a,
	0x29, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x69,
	0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x25, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x53, 0x69, 0x6d, 0x70, 0x6c,
	0x65, 0x56, 0x69, 0x65, 0x77, 0x73, 0x22, 0x5d, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x49, 0x64, 0x22, 0x45, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x32, 0xad, 0x08, 0x0a,
	0x16, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x95, 0x01, 0x0a, 0x1d, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x45, 0x78, 0x69, 0x73, 0x74, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x45, 0x78, 0x69, 0x73, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x45, 0x78, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x7d, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x74,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x7d, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x7d, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x31, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x77, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x98, 0x01, 0x0a, 0x1e,
	0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x12, 0x3a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x74, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x65, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x65, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x78, 0x0a, 0x1c,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_message_v1_message_template_api_proto_rawDescOnce sync.Once
	file_moego_api_message_v1_message_template_api_proto_rawDescData = file_moego_api_message_v1_message_template_api_proto_rawDesc
)

func file_moego_api_message_v1_message_template_api_proto_rawDescGZIP() []byte {
	file_moego_api_message_v1_message_template_api_proto_rawDescOnce.Do(func() {
		file_moego_api_message_v1_message_template_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_message_v1_message_template_api_proto_rawDescData)
	})
	return file_moego_api_message_v1_message_template_api_proto_rawDescData
}

var file_moego_api_message_v1_message_template_api_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_moego_api_message_v1_message_template_api_proto_goTypes = []interface{}{
	(*CheckMessageTemplateNameExistParams)(nil),     // 0: moego.api.message.v1.CheckMessageTemplateNameExistParams
	(*CheckMessageTemplateNameExistResult)(nil),     // 1: moego.api.message.v1.CheckMessageTemplateNameExistResult
	(*CreateMessageTemplateParams)(nil),             // 2: moego.api.message.v1.CreateMessageTemplateParams
	(*CreateMessageTemplateResult)(nil),             // 3: moego.api.message.v1.CreateMessageTemplateResult
	(*GetMessageTemplateParams)(nil),                // 4: moego.api.message.v1.GetMessageTemplateParams
	(*GetMessageTemplateResult)(nil),                // 5: moego.api.message.v1.GetMessageTemplateResult
	(*UpdateMessageTemplateParams)(nil),             // 6: moego.api.message.v1.UpdateMessageTemplateParams
	(*UpdateMessageTemplateResult)(nil),             // 7: moego.api.message.v1.UpdateMessageTemplateResult
	(*DeleteMessageTemplateParams)(nil),             // 8: moego.api.message.v1.DeleteMessageTemplateParams
	(*DeleteMessageTemplateResult)(nil),             // 9: moego.api.message.v1.DeleteMessageTemplateResult
	(*GetMessageTemplatesParams)(nil),               // 10: moego.api.message.v1.GetMessageTemplatesParams
	(*GetMessageTemplatesResult)(nil),               // 11: moego.api.message.v1.GetMessageTemplatesResult
	(*GetMessageTemplatePlaceholdersParams)(nil),    // 12: moego.api.message.v1.GetMessageTemplatePlaceholdersParams
	(*GetMessageTemplatePlaceholdersResult)(nil),    // 13: moego.api.message.v1.GetMessageTemplatePlaceholdersResult
	(*GetRenderedMessageParams)(nil),                // 14: moego.api.message.v1.GetRenderedMessageParams
	(*GetRenderedMessageResult)(nil),                // 15: moego.api.message.v1.GetRenderedMessageResult
	(*v1.MessageTemplateDef)(nil),                   // 16: moego.models.message.v1.MessageTemplateDef
	(*v1.MessageTemplateDetailView)(nil),            // 17: moego.models.message.v1.MessageTemplateDetailView
	(v1.MessageTemplateType)(0),                     // 18: moego.models.message.v1.MessageTemplateType
	(*v1.MessageTemplateSimpleView)(nil),            // 19: moego.models.message.v1.MessageTemplateSimpleView
	(v1.MessageTemplateUseCase)(0),                  // 20: moego.models.message.v1.MessageTemplateUseCase
	(*v1.MessageTemplatePlaceholderSimpleView)(nil), // 21: moego.models.message.v1.MessageTemplatePlaceholderSimpleView
}
var file_moego_api_message_v1_message_template_api_proto_depIdxs = []int32{
	16, // 0: moego.api.message.v1.CreateMessageTemplateParams.message_template_def:type_name -> moego.models.message.v1.MessageTemplateDef
	17, // 1: moego.api.message.v1.GetMessageTemplateResult.message_template_detail_view:type_name -> moego.models.message.v1.MessageTemplateDetailView
	16, // 2: moego.api.message.v1.UpdateMessageTemplateParams.message_template_def:type_name -> moego.models.message.v1.MessageTemplateDef
	18, // 3: moego.api.message.v1.GetMessageTemplatesParams.types:type_name -> moego.models.message.v1.MessageTemplateType
	19, // 4: moego.api.message.v1.GetMessageTemplatesResult.message_template_simple_views:type_name -> moego.models.message.v1.MessageTemplateSimpleView
	20, // 5: moego.api.message.v1.GetMessageTemplatePlaceholdersParams.use_case:type_name -> moego.models.message.v1.MessageTemplateUseCase
	21, // 6: moego.api.message.v1.GetMessageTemplatePlaceholdersResult.message_template_placeholder_simple_views:type_name -> moego.models.message.v1.MessageTemplatePlaceholderSimpleView
	0,  // 7: moego.api.message.v1.MessageTemplateService.CheckMessageTemplateNameExist:input_type -> moego.api.message.v1.CheckMessageTemplateNameExistParams
	2,  // 8: moego.api.message.v1.MessageTemplateService.CreateMessageTemplate:input_type -> moego.api.message.v1.CreateMessageTemplateParams
	4,  // 9: moego.api.message.v1.MessageTemplateService.GetMessageTemplate:input_type -> moego.api.message.v1.GetMessageTemplateParams
	6,  // 10: moego.api.message.v1.MessageTemplateService.UpdateMessageTemplate:input_type -> moego.api.message.v1.UpdateMessageTemplateParams
	8,  // 11: moego.api.message.v1.MessageTemplateService.DeleteMessageTemplate:input_type -> moego.api.message.v1.DeleteMessageTemplateParams
	10, // 12: moego.api.message.v1.MessageTemplateService.GetMessageTemplates:input_type -> moego.api.message.v1.GetMessageTemplatesParams
	12, // 13: moego.api.message.v1.MessageTemplateService.GetMessageTemplatePlaceholders:input_type -> moego.api.message.v1.GetMessageTemplatePlaceholdersParams
	14, // 14: moego.api.message.v1.MessageTemplateService.GetRenderedMessage:input_type -> moego.api.message.v1.GetRenderedMessageParams
	1,  // 15: moego.api.message.v1.MessageTemplateService.CheckMessageTemplateNameExist:output_type -> moego.api.message.v1.CheckMessageTemplateNameExistResult
	3,  // 16: moego.api.message.v1.MessageTemplateService.CreateMessageTemplate:output_type -> moego.api.message.v1.CreateMessageTemplateResult
	5,  // 17: moego.api.message.v1.MessageTemplateService.GetMessageTemplate:output_type -> moego.api.message.v1.GetMessageTemplateResult
	7,  // 18: moego.api.message.v1.MessageTemplateService.UpdateMessageTemplate:output_type -> moego.api.message.v1.UpdateMessageTemplateResult
	9,  // 19: moego.api.message.v1.MessageTemplateService.DeleteMessageTemplate:output_type -> moego.api.message.v1.DeleteMessageTemplateResult
	11, // 20: moego.api.message.v1.MessageTemplateService.GetMessageTemplates:output_type -> moego.api.message.v1.GetMessageTemplatesResult
	13, // 21: moego.api.message.v1.MessageTemplateService.GetMessageTemplatePlaceholders:output_type -> moego.api.message.v1.GetMessageTemplatePlaceholdersResult
	15, // 22: moego.api.message.v1.MessageTemplateService.GetRenderedMessage:output_type -> moego.api.message.v1.GetRenderedMessageResult
	15, // [15:23] is the sub-list for method output_type
	7,  // [7:15] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_moego_api_message_v1_message_template_api_proto_init() }
func file_moego_api_message_v1_message_template_api_proto_init() {
	if File_moego_api_message_v1_message_template_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_message_v1_message_template_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckMessageTemplateNameExistParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_message_template_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckMessageTemplateNameExistResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_message_template_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMessageTemplateParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_message_template_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMessageTemplateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_message_template_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMessageTemplateParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_message_template_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMessageTemplateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_message_template_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMessageTemplateParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_message_template_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMessageTemplateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_message_template_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteMessageTemplateParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_message_template_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteMessageTemplateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_message_template_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMessageTemplatesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_message_template_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMessageTemplatesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_message_template_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMessageTemplatePlaceholdersParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_message_template_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMessageTemplatePlaceholdersResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_message_template_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRenderedMessageParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_message_v1_message_template_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRenderedMessageResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_message_v1_message_template_api_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_api_message_v1_message_template_api_proto_msgTypes[12].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_message_v1_message_template_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_message_v1_message_template_api_proto_goTypes,
		DependencyIndexes: file_moego_api_message_v1_message_template_api_proto_depIdxs,
		MessageInfos:      file_moego_api_message_v1_message_template_api_proto_msgTypes,
	}.Build()
	File_moego_api_message_v1_message_template_api_proto = out.File
	file_moego_api_message_v1_message_template_api_proto_rawDesc = nil
	file_moego_api_message_v1_message_template_api_proto_goTypes = nil
	file_moego_api_message_v1_message_template_api_proto_depIdxs = nil
}
