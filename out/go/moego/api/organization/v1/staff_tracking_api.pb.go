// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/organization/v1/staff_tracking_api.proto

package organizationapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// upload staff location params
type CreateStaffTrackingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// coordinate, include latitude and longitude
	Coordinate *latlng.LatLng `protobuf:"bytes,1,opt,name=coordinate,proto3" json:"coordinate,omitempty"`
}

func (x *CreateStaffTrackingParams) Reset() {
	*x = CreateStaffTrackingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStaffTrackingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStaffTrackingParams) ProtoMessage() {}

func (x *CreateStaffTrackingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStaffTrackingParams.ProtoReflect.Descriptor instead.
func (*CreateStaffTrackingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_tracking_api_proto_rawDescGZIP(), []int{0}
}

func (x *CreateStaffTrackingParams) GetCoordinate() *latlng.LatLng {
	if x != nil {
		return x.Coordinate
	}
	return nil
}

// upload staff location result
type CreateStaffTrackingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *CreateStaffTrackingResult) Reset() {
	*x = CreateStaffTrackingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStaffTrackingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStaffTrackingResult) ProtoMessage() {}

func (x *CreateStaffTrackingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStaffTrackingResult.ProtoReflect.Descriptor instead.
func (*CreateStaffTrackingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_tracking_api_proto_rawDescGZIP(), []int{1}
}

func (x *CreateStaffTrackingResult) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

// list staff location params
type ListStaffTrackingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id list
	StaffIds []int64 `protobuf:"varint,1,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
}

func (x *ListStaffTrackingParams) Reset() {
	*x = ListStaffTrackingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffTrackingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffTrackingParams) ProtoMessage() {}

func (x *ListStaffTrackingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffTrackingParams.ProtoReflect.Descriptor instead.
func (*ListStaffTrackingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_tracking_api_proto_rawDescGZIP(), []int{2}
}

func (x *ListStaffTrackingParams) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

// list staff location result
type ListStaffTrackingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff_trackings
	StaffTrackings []*v1.StaffTrackingModel `protobuf:"bytes,1,rep,name=staff_trackings,json=staffTrackings,proto3" json:"staff_trackings,omitempty"`
}

func (x *ListStaffTrackingResult) Reset() {
	*x = ListStaffTrackingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStaffTrackingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStaffTrackingResult) ProtoMessage() {}

func (x *ListStaffTrackingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStaffTrackingResult.ProtoReflect.Descriptor instead.
func (*ListStaffTrackingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_tracking_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListStaffTrackingResult) GetStaffTrackings() []*v1.StaffTrackingModel {
	if x != nil {
		return x.StaffTrackings
	}
	return nil
}

// batch create staff tracking params
type BatchCreateStaffTrackingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff tracking list
	Params []*BatchCreateStaffTrackingParams_Param `protobuf:"bytes,1,rep,name=params,proto3" json:"params,omitempty"`
}

func (x *BatchCreateStaffTrackingParams) Reset() {
	*x = BatchCreateStaffTrackingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCreateStaffTrackingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateStaffTrackingParams) ProtoMessage() {}

func (x *BatchCreateStaffTrackingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateStaffTrackingParams.ProtoReflect.Descriptor instead.
func (*BatchCreateStaffTrackingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_tracking_api_proto_rawDescGZIP(), []int{4}
}

func (x *BatchCreateStaffTrackingParams) GetParams() []*BatchCreateStaffTrackingParams_Param {
	if x != nil {
		return x.Params
	}
	return nil
}

// batch create staff tracking result
type BatchCreateStaffTrackingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchCreateStaffTrackingResult) Reset() {
	*x = BatchCreateStaffTrackingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCreateStaffTrackingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateStaffTrackingResult) ProtoMessage() {}

func (x *BatchCreateStaffTrackingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateStaffTrackingResult.ProtoReflect.Descriptor instead.
func (*BatchCreateStaffTrackingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_tracking_api_proto_rawDescGZIP(), []int{5}
}

// param
type BatchCreateStaffTrackingParams_Param struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// time
	Timestamp *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// coordinate, include latitude and longitude
	Coordinate *latlng.LatLng `protobuf:"bytes,2,opt,name=coordinate,proto3" json:"coordinate,omitempty"`
}

func (x *BatchCreateStaffTrackingParams_Param) Reset() {
	*x = BatchCreateStaffTrackingParams_Param{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCreateStaffTrackingParams_Param) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreateStaffTrackingParams_Param) ProtoMessage() {}

func (x *BatchCreateStaffTrackingParams_Param) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreateStaffTrackingParams_Param.ProtoReflect.Descriptor instead.
func (*BatchCreateStaffTrackingParams_Param) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_staff_tracking_api_proto_rawDescGZIP(), []int{4, 0}
}

func (x *BatchCreateStaffTrackingParams_Param) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *BatchCreateStaffTrackingParams_Param) GetCoordinate() *latlng.LatLng {
	if x != nil {
		return x.Coordinate
	}
	return nil
}

var File_moego_api_organization_v1_staff_tracking_api_proto protoreflect.FileDescriptor

var file_moego_api_organization_v1_staff_tracking_api_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6c, 0x61,
	0x74, 0x6c, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5a, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x3d, 0x0a, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61,
	0x74, 0x65, 0x22, 0x33, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x40, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x25, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52,
	0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x22, 0x74, 0x0a, 0x17, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x59, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x0e, 0x73, 0x74, 0x61, 0x66, 0x66, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x73, 0x22,
	0x93, 0x02, 0x0a, 0x1e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x64, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01, 0x05, 0x08, 0x01, 0x10, 0xa0, 0x0b,
	0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x8a, 0x01, 0x0a, 0x05, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x12, 0x42, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x3d, 0x0a, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69,
	0x6e, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64,
	0x69, 0x6e, 0x61, 0x74, 0x65, 0x22, 0x20, 0x0a, 0x1e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xaa, 0x03, 0x0a, 0x14, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x81, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x7b, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x90, 0x01, 0x0a, 0x18, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x39,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x42, 0x87, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_organization_v1_staff_tracking_api_proto_rawDescOnce sync.Once
	file_moego_api_organization_v1_staff_tracking_api_proto_rawDescData = file_moego_api_organization_v1_staff_tracking_api_proto_rawDesc
)

func file_moego_api_organization_v1_staff_tracking_api_proto_rawDescGZIP() []byte {
	file_moego_api_organization_v1_staff_tracking_api_proto_rawDescOnce.Do(func() {
		file_moego_api_organization_v1_staff_tracking_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_organization_v1_staff_tracking_api_proto_rawDescData)
	})
	return file_moego_api_organization_v1_staff_tracking_api_proto_rawDescData
}

var file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_api_organization_v1_staff_tracking_api_proto_goTypes = []interface{}{
	(*CreateStaffTrackingParams)(nil),            // 0: moego.api.organization.v1.CreateStaffTrackingParams
	(*CreateStaffTrackingResult)(nil),            // 1: moego.api.organization.v1.CreateStaffTrackingResult
	(*ListStaffTrackingParams)(nil),              // 2: moego.api.organization.v1.ListStaffTrackingParams
	(*ListStaffTrackingResult)(nil),              // 3: moego.api.organization.v1.ListStaffTrackingResult
	(*BatchCreateStaffTrackingParams)(nil),       // 4: moego.api.organization.v1.BatchCreateStaffTrackingParams
	(*BatchCreateStaffTrackingResult)(nil),       // 5: moego.api.organization.v1.BatchCreateStaffTrackingResult
	(*BatchCreateStaffTrackingParams_Param)(nil), // 6: moego.api.organization.v1.BatchCreateStaffTrackingParams.Param
	(*latlng.LatLng)(nil),                        // 7: google.type.LatLng
	(*v1.StaffTrackingModel)(nil),                // 8: moego.models.organization.v1.StaffTrackingModel
	(*timestamppb.Timestamp)(nil),                // 9: google.protobuf.Timestamp
}
var file_moego_api_organization_v1_staff_tracking_api_proto_depIdxs = []int32{
	7, // 0: moego.api.organization.v1.CreateStaffTrackingParams.coordinate:type_name -> google.type.LatLng
	8, // 1: moego.api.organization.v1.ListStaffTrackingResult.staff_trackings:type_name -> moego.models.organization.v1.StaffTrackingModel
	6, // 2: moego.api.organization.v1.BatchCreateStaffTrackingParams.params:type_name -> moego.api.organization.v1.BatchCreateStaffTrackingParams.Param
	9, // 3: moego.api.organization.v1.BatchCreateStaffTrackingParams.Param.timestamp:type_name -> google.protobuf.Timestamp
	7, // 4: moego.api.organization.v1.BatchCreateStaffTrackingParams.Param.coordinate:type_name -> google.type.LatLng
	0, // 5: moego.api.organization.v1.StaffTrackingService.CreateStaffTracking:input_type -> moego.api.organization.v1.CreateStaffTrackingParams
	2, // 6: moego.api.organization.v1.StaffTrackingService.ListStaffTracking:input_type -> moego.api.organization.v1.ListStaffTrackingParams
	4, // 7: moego.api.organization.v1.StaffTrackingService.BatchCreateStaffTracking:input_type -> moego.api.organization.v1.BatchCreateStaffTrackingParams
	1, // 8: moego.api.organization.v1.StaffTrackingService.CreateStaffTracking:output_type -> moego.api.organization.v1.CreateStaffTrackingResult
	3, // 9: moego.api.organization.v1.StaffTrackingService.ListStaffTracking:output_type -> moego.api.organization.v1.ListStaffTrackingResult
	5, // 10: moego.api.organization.v1.StaffTrackingService.BatchCreateStaffTracking:output_type -> moego.api.organization.v1.BatchCreateStaffTrackingResult
	8, // [8:11] is the sub-list for method output_type
	5, // [5:8] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_api_organization_v1_staff_tracking_api_proto_init() }
func file_moego_api_organization_v1_staff_tracking_api_proto_init() {
	if File_moego_api_organization_v1_staff_tracking_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStaffTrackingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStaffTrackingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffTrackingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStaffTrackingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCreateStaffTrackingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCreateStaffTrackingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCreateStaffTrackingParams_Param); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_organization_v1_staff_tracking_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_organization_v1_staff_tracking_api_proto_goTypes,
		DependencyIndexes: file_moego_api_organization_v1_staff_tracking_api_proto_depIdxs,
		MessageInfos:      file_moego_api_organization_v1_staff_tracking_api_proto_msgTypes,
	}.Build()
	File_moego_api_organization_v1_staff_tracking_api_proto = out.File
	file_moego_api_organization_v1_staff_tracking_api_proto_rawDesc = nil
	file_moego_api_organization_v1_staff_tracking_api_proto_goTypes = nil
	file_moego_api_organization_v1_staff_tracking_api_proto_depIdxs = nil
}
