// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/organization/v1/company_api.proto

package organizationapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create company request
type CreateCompanyParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location info
	Location *v1.CreateLocationDef `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
	// how to know us
	Source *v1.SourceType `protobuf:"varint,2,opt,name=source,proto3,enum=moego.models.organization.v1.SourceType,oneof" json:"source,omitempty"`
	// country
	Country *v1.CountryDef `protobuf:"bytes,3,opt,name=country,proto3" json:"country,omitempty"`
	// time zone
	TimeZone *v1.TimeZone `protobuf:"bytes,4,opt,name=time_zone,json=timeZone,proto3" json:"time_zone,omitempty"`
	// know about us, if source is other, this field is required
	KnowAboutUs *string `protobuf:"bytes,5,opt,name=know_about_us,json=knowAboutUs,proto3,oneof" json:"know_about_us,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,6,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// currency code
	CurrencyCode string `protobuf:"bytes,7,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// currency symbol
	CurrencySymbol string `protobuf:"bytes,8,opt,name=currency_symbol,json=currencySymbol,proto3" json:"currency_symbol,omitempty"`
	// company type
	CompanyType *int32 `protobuf:"varint,9,opt,name=company_type,json=companyType,proto3,oneof" json:"company_type,omitempty"`
}

func (x *CreateCompanyParams) Reset() {
	*x = CreateCompanyParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCompanyParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCompanyParams) ProtoMessage() {}

func (x *CreateCompanyParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCompanyParams.ProtoReflect.Descriptor instead.
func (*CreateCompanyParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCompanyParams) GetLocation() *v1.CreateLocationDef {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *CreateCompanyParams) GetSource() v1.SourceType {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return v1.SourceType(0)
}

func (x *CreateCompanyParams) GetCountry() *v1.CountryDef {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *CreateCompanyParams) GetTimeZone() *v1.TimeZone {
	if x != nil {
		return x.TimeZone
	}
	return nil
}

func (x *CreateCompanyParams) GetKnowAboutUs() string {
	if x != nil && x.KnowAboutUs != nil {
		return *x.KnowAboutUs
	}
	return ""
}

func (x *CreateCompanyParams) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *CreateCompanyParams) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *CreateCompanyParams) GetCurrencySymbol() string {
	if x != nil {
		return x.CurrencySymbol
	}
	return ""
}

func (x *CreateCompanyParams) GetCompanyType() int32 {
	if x != nil && x.CompanyType != nil {
		return *x.CompanyType
	}
	return 0
}

// create company response
type CreateCompanyResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *CreateCompanyResult) Reset() {
	*x = CreateCompanyResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCompanyResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCompanyResult) ProtoMessage() {}

func (x *CreateCompanyResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCompanyResult.ProtoReflect.Descriptor instead.
func (*CreateCompanyResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{1}
}

func (x *CreateCompanyResult) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateCompanyResult) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// query company staff by account request
type QueryCompanyStaffByAccountParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *QueryCompanyStaffByAccountParams) Reset() {
	*x = QueryCompanyStaffByAccountParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCompanyStaffByAccountParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCompanyStaffByAccountParams) ProtoMessage() {}

func (x *QueryCompanyStaffByAccountParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCompanyStaffByAccountParams.ProtoReflect.Descriptor instead.
func (*QueryCompanyStaffByAccountParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{2}
}

// query company staff by account response
type QueryCompanyStaffByAccountResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company staffs
	CompanyStaffs []*QueryCompanyStaffByAccountResult_CompanyStaff `protobuf:"bytes,1,rep,name=company_staffs,json=companyStaffs,proto3" json:"company_staffs,omitempty"`
}

func (x *QueryCompanyStaffByAccountResult) Reset() {
	*x = QueryCompanyStaffByAccountResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCompanyStaffByAccountResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCompanyStaffByAccountResult) ProtoMessage() {}

func (x *QueryCompanyStaffByAccountResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCompanyStaffByAccountResult.ProtoReflect.Descriptor instead.
func (*QueryCompanyStaffByAccountResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{3}
}

func (x *QueryCompanyStaffByAccountResult) GetCompanyStaffs() []*QueryCompanyStaffByAccountResult_CompanyStaff {
	if x != nil {
		return x.CompanyStaffs
	}
	return nil
}

// update company preference setting request
type UpdateCompanyPreferenceSettingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// preference setting
	PreferenceSetting *v1.UpdateCompanyPreferenceSettingDef `protobuf:"bytes,1,opt,name=preference_setting,json=preferenceSetting,proto3" json:"preference_setting,omitempty"`
}

func (x *UpdateCompanyPreferenceSettingParams) Reset() {
	*x = UpdateCompanyPreferenceSettingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCompanyPreferenceSettingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCompanyPreferenceSettingParams) ProtoMessage() {}

func (x *UpdateCompanyPreferenceSettingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCompanyPreferenceSettingParams.ProtoReflect.Descriptor instead.
func (*UpdateCompanyPreferenceSettingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateCompanyPreferenceSettingParams) GetPreferenceSetting() *v1.UpdateCompanyPreferenceSettingDef {
	if x != nil {
		return x.PreferenceSetting
	}
	return nil
}

// update company preference setting response
type UpdateCompanyPreferenceSettingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success or not
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UpdateCompanyPreferenceSettingResult) Reset() {
	*x = UpdateCompanyPreferenceSettingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCompanyPreferenceSettingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCompanyPreferenceSettingResult) ProtoMessage() {}

func (x *UpdateCompanyPreferenceSettingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCompanyPreferenceSettingResult.ProtoReflect.Descriptor instead.
func (*UpdateCompanyPreferenceSettingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateCompanyPreferenceSettingResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// get company preference setting request
type GetCompanyPreferenceSettingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetCompanyPreferenceSettingParams) Reset() {
	*x = GetCompanyPreferenceSettingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompanyPreferenceSettingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompanyPreferenceSettingParams) ProtoMessage() {}

func (x *GetCompanyPreferenceSettingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompanyPreferenceSettingParams.ProtoReflect.Descriptor instead.
func (*GetCompanyPreferenceSettingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{6}
}

// get company preference setting response
type GetCompanyPreferenceSettingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// preference setting
	PreferenceSetting *v1.CompanyPreferenceSettingModel `protobuf:"bytes,1,opt,name=preference_setting,json=preferenceSetting,proto3" json:"preference_setting,omitempty"`
}

func (x *GetCompanyPreferenceSettingResult) Reset() {
	*x = GetCompanyPreferenceSettingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompanyPreferenceSettingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompanyPreferenceSettingResult) ProtoMessage() {}

func (x *GetCompanyPreferenceSettingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompanyPreferenceSettingResult.ProtoReflect.Descriptor instead.
func (*GetCompanyPreferenceSettingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetCompanyPreferenceSettingResult) GetPreferenceSetting() *v1.CompanyPreferenceSettingModel {
	if x != nil {
		return x.PreferenceSetting
	}
	return nil
}

// switch company request params
type SwitchCompanyParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business_id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *SwitchCompanyParams) Reset() {
	*x = SwitchCompanyParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwitchCompanyParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwitchCompanyParams) ProtoMessage() {}

func (x *SwitchCompanyParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwitchCompanyParams.ProtoReflect.Descriptor instead.
func (*SwitchCompanyParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{8}
}

func (x *SwitchCompanyParams) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SwitchCompanyParams) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// switch company result
type SwitchCompanyResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SwitchCompanyResult) Reset() {
	*x = SwitchCompanyResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwitchCompanyResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwitchCompanyResult) ProtoMessage() {}

func (x *SwitchCompanyResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwitchCompanyResult.ProtoReflect.Descriptor instead.
func (*SwitchCompanyResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{9}
}

// get companies extra info request params
type GetCompaniesExtraInfoParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetCompaniesExtraInfoParams) Reset() {
	*x = GetCompaniesExtraInfoParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompaniesExtraInfoParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompaniesExtraInfoParams) ProtoMessage() {}

func (x *GetCompaniesExtraInfoParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompaniesExtraInfoParams.ProtoReflect.Descriptor instead.
func (*GetCompaniesExtraInfoParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{10}
}

// get companies extra info response
type GetCompaniesExtraInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company extra info map,key is company id
	CompanyExtraInfoMap map[int64]*v1.CompanyExtraInfoDef `protobuf:"bytes,1,rep,name=company_extra_info_map,json=companyExtraInfoMap,proto3" json:"company_extra_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetCompaniesExtraInfoResponse) Reset() {
	*x = GetCompaniesExtraInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCompaniesExtraInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCompaniesExtraInfoResponse) ProtoMessage() {}

func (x *GetCompaniesExtraInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCompaniesExtraInfoResponse.ProtoReflect.Descriptor instead.
func (*GetCompaniesExtraInfoResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{11}
}

func (x *GetCompaniesExtraInfoResponse) GetCompanyExtraInfoMap() map[int64]*v1.CompanyExtraInfoDef {
	if x != nil {
		return x.CompanyExtraInfoMap
	}
	return nil
}

// query company staff by account with locations request
type QueryCompanyStaffByAccountWithLocationsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *QueryCompanyStaffByAccountWithLocationsParams) Reset() {
	*x = QueryCompanyStaffByAccountWithLocationsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCompanyStaffByAccountWithLocationsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCompanyStaffByAccountWithLocationsParams) ProtoMessage() {}

func (x *QueryCompanyStaffByAccountWithLocationsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCompanyStaffByAccountWithLocationsParams.ProtoReflect.Descriptor instead.
func (*QueryCompanyStaffByAccountWithLocationsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{12}
}

// query company staff by account with locations response
type QueryCompanyStaffByAccountWithLocationsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company staffs with locations
	CompanyStaffsWithLocations []*QueryCompanyStaffByAccountWithLocationsResult_CompanyStaffWithLocations `protobuf:"bytes,1,rep,name=company_staffs_with_locations,json=companyStaffsWithLocations,proto3" json:"company_staffs_with_locations,omitempty"`
}

func (x *QueryCompanyStaffByAccountWithLocationsResult) Reset() {
	*x = QueryCompanyStaffByAccountWithLocationsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCompanyStaffByAccountWithLocationsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCompanyStaffByAccountWithLocationsResult) ProtoMessage() {}

func (x *QueryCompanyStaffByAccountWithLocationsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCompanyStaffByAccountWithLocationsResult.ProtoReflect.Descriptor instead.
func (*QueryCompanyStaffByAccountWithLocationsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{13}
}

func (x *QueryCompanyStaffByAccountWithLocationsResult) GetCompanyStaffsWithLocations() []*QueryCompanyStaffByAccountWithLocationsResult_CompanyStaffWithLocations {
	if x != nil {
		return x.CompanyStaffsWithLocations
	}
	return nil
}

// add tax rule
type AddTaxRuleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tax rule to add
	TaxRule *v1.TaxRuleDef `protobuf:"bytes,1,opt,name=tax_rule,json=taxRule,proto3" json:"tax_rule,omitempty"`
}

func (x *AddTaxRuleParams) Reset() {
	*x = AddTaxRuleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddTaxRuleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTaxRuleParams) ProtoMessage() {}

func (x *AddTaxRuleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTaxRuleParams.ProtoReflect.Descriptor instead.
func (*AddTaxRuleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{14}
}

func (x *AddTaxRuleParams) GetTaxRule() *v1.TaxRuleDef {
	if x != nil {
		return x.TaxRule
	}
	return nil
}

// add tax rule response
type AddTaxRuleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tax rule id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *AddTaxRuleResult) Reset() {
	*x = AddTaxRuleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddTaxRuleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTaxRuleResult) ProtoMessage() {}

func (x *AddTaxRuleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTaxRuleResult.ProtoReflect.Descriptor instead.
func (*AddTaxRuleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{15}
}

func (x *AddTaxRuleResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// update tax rule
type UpdateTaxRuleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tax id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// tax rule to update
	TaxRule *v1.TaxRuleDef `protobuf:"bytes,2,opt,name=tax_rule,json=taxRule,proto3" json:"tax_rule,omitempty"`
}

func (x *UpdateTaxRuleParams) Reset() {
	*x = UpdateTaxRuleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTaxRuleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTaxRuleParams) ProtoMessage() {}

func (x *UpdateTaxRuleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTaxRuleParams.ProtoReflect.Descriptor instead.
func (*UpdateTaxRuleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateTaxRuleParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateTaxRuleParams) GetTaxRule() *v1.TaxRuleDef {
	if x != nil {
		return x.TaxRule
	}
	return nil
}

// update tax rule response
type UpdateTaxRuleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UpdateTaxRuleResult) Reset() {
	*x = UpdateTaxRuleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTaxRuleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTaxRuleResult) ProtoMessage() {}

func (x *UpdateTaxRuleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTaxRuleResult.ProtoReflect.Descriptor instead.
func (*UpdateTaxRuleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{17}
}

func (x *UpdateTaxRuleResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// delete tax rule
type DeleteTaxRuleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tax id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteTaxRuleParams) Reset() {
	*x = DeleteTaxRuleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteTaxRuleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTaxRuleParams) ProtoMessage() {}

func (x *DeleteTaxRuleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTaxRuleParams.ProtoReflect.Descriptor instead.
func (*DeleteTaxRuleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{18}
}

func (x *DeleteTaxRuleParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete tax rule response
type DeleteTaxRuleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// delete result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *DeleteTaxRuleResult) Reset() {
	*x = DeleteTaxRuleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteTaxRuleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTaxRuleResult) ProtoMessage() {}

func (x *DeleteTaxRuleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTaxRuleResult.ProtoReflect.Descriptor instead.
func (*DeleteTaxRuleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteTaxRuleResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// get tax rule list
type GetTaxRuleListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetTaxRuleListParams) Reset() {
	*x = GetTaxRuleListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaxRuleListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaxRuleListParams) ProtoMessage() {}

func (x *GetTaxRuleListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaxRuleListParams.ProtoReflect.Descriptor instead.
func (*GetTaxRuleListParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{20}
}

// get tax rule list response
type GetTaxRuleListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tax rule list
	TaxRule []*v1.TaxRuleModel `protobuf:"bytes,1,rep,name=tax_rule,json=taxRule,proto3" json:"tax_rule,omitempty"`
}

func (x *GetTaxRuleListResult) Reset() {
	*x = GetTaxRuleListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaxRuleListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaxRuleListResult) ProtoMessage() {}

func (x *GetTaxRuleListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaxRuleListResult.ProtoReflect.Descriptor instead.
func (*GetTaxRuleListResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{21}
}

func (x *GetTaxRuleListResult) GetTaxRule() []*v1.TaxRuleModel {
	if x != nil {
		return x.TaxRule
	}
	return nil
}

// get clock in/out setting params
type GetClockInOutSettingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetClockInOutSettingParams) Reset() {
	*x = GetClockInOutSettingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClockInOutSettingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClockInOutSettingParams) ProtoMessage() {}

func (x *GetClockInOutSettingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClockInOutSettingParams.ProtoReflect.Descriptor instead.
func (*GetClockInOutSettingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{22}
}

// get clock in out setting result
type GetClockInOutSettingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// clock in/out setting
	ClockInOutSetting *v1.ClockInOutSettingModel `protobuf:"bytes,1,opt,name=clock_in_out_setting,json=clockInOutSetting,proto3" json:"clock_in_out_setting,omitempty"`
}

func (x *GetClockInOutSettingResult) Reset() {
	*x = GetClockInOutSettingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClockInOutSettingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClockInOutSettingResult) ProtoMessage() {}

func (x *GetClockInOutSettingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClockInOutSettingResult.ProtoReflect.Descriptor instead.
func (*GetClockInOutSettingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{23}
}

func (x *GetClockInOutSettingResult) GetClockInOutSetting() *v1.ClockInOutSettingModel {
	if x != nil {
		return x.ClockInOutSetting
	}
	return nil
}

// update clock in/out setting params
type UpdateClockInOutSettingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update clock in/out setting params
	ClockInOutSetting *v1.UpdateClockInOutSettingDef `protobuf:"bytes,6,opt,name=clock_in_out_setting,json=clockInOutSetting,proto3" json:"clock_in_out_setting,omitempty"`
}

func (x *UpdateClockInOutSettingParams) Reset() {
	*x = UpdateClockInOutSettingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateClockInOutSettingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateClockInOutSettingParams) ProtoMessage() {}

func (x *UpdateClockInOutSettingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateClockInOutSettingParams.ProtoReflect.Descriptor instead.
func (*UpdateClockInOutSettingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{24}
}

func (x *UpdateClockInOutSettingParams) GetClockInOutSetting() *v1.UpdateClockInOutSettingDef {
	if x != nil {
		return x.ClockInOutSetting
	}
	return nil
}

// update clock in/out setting result
type UpdateClockInOutSettingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *UpdateClockInOutSettingResult) Reset() {
	*x = UpdateClockInOutSettingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateClockInOutSettingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateClockInOutSettingResult) ProtoMessage() {}

func (x *UpdateClockInOutSettingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateClockInOutSettingResult.ProtoReflect.Descriptor instead.
func (*UpdateClockInOutSettingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateClockInOutSettingResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// company question query
type CompanyQuestionRecordQueryParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// query company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *CompanyQuestionRecordQueryParams) Reset() {
	*x = CompanyQuestionRecordQueryParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyQuestionRecordQueryParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyQuestionRecordQueryParams) ProtoMessage() {}

func (x *CompanyQuestionRecordQueryParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyQuestionRecordQueryParams.ProtoReflect.Descriptor instead.
func (*CompanyQuestionRecordQueryParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{26}
}

func (x *CompanyQuestionRecordQueryParams) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// company question result
type CompanyQuestionRecordQueryResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is have file question
	IsFillQuestion bool `protobuf:"varint,1,opt,name=is_fill_question,json=isFillQuestion,proto3" json:"is_fill_question,omitempty"`
}

func (x *CompanyQuestionRecordQueryResult) Reset() {
	*x = CompanyQuestionRecordQueryResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyQuestionRecordQueryResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyQuestionRecordQueryResult) ProtoMessage() {}

func (x *CompanyQuestionRecordQueryResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyQuestionRecordQueryResult.ProtoReflect.Descriptor instead.
func (*CompanyQuestionRecordQueryResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{27}
}

func (x *CompanyQuestionRecordQueryResult) GetIsFillQuestion() bool {
	if x != nil {
		return x.IsFillQuestion
	}
	return false
}

// save company question record
type CompanyQuestionRecordParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pets per month
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// pets per month
	PetPerMonth string `protobuf:"bytes,2,opt,name=pet_per_month,json=petPerMonth,proto3" json:"pet_per_month,omitempty"`
	// total location number
	TotalLocations *string `protobuf:"bytes,3,opt,name=total_locations,json=totalLocations,proto3,oneof" json:"total_locations,omitempty"`
	// total van number
	TotalVans *string `protobuf:"bytes,4,opt,name=total_vans,json=totalVans,proto3,oneof" json:"total_vans,omitempty"`
	// move from other software
	MoveFrom string `protobuf:"bytes,5,opt,name=move_from,json=moveFrom,proto3" json:"move_from,omitempty"`
	// source from
	SourceFrom string `protobuf:"bytes,6,opt,name=source_from,json=sourceFrom,proto3" json:"source_from,omitempty"`
	// other source from
	SourceFromOther *string `protobuf:"bytes,7,opt,name=source_from_other,json=sourceFromOther,proto3,oneof" json:"source_from_other,omitempty"`
}

func (x *CompanyQuestionRecordParams) Reset() {
	*x = CompanyQuestionRecordParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyQuestionRecordParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyQuestionRecordParams) ProtoMessage() {}

func (x *CompanyQuestionRecordParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyQuestionRecordParams.ProtoReflect.Descriptor instead.
func (*CompanyQuestionRecordParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{28}
}

func (x *CompanyQuestionRecordParams) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CompanyQuestionRecordParams) GetPetPerMonth() string {
	if x != nil {
		return x.PetPerMonth
	}
	return ""
}

func (x *CompanyQuestionRecordParams) GetTotalLocations() string {
	if x != nil && x.TotalLocations != nil {
		return *x.TotalLocations
	}
	return ""
}

func (x *CompanyQuestionRecordParams) GetTotalVans() string {
	if x != nil && x.TotalVans != nil {
		return *x.TotalVans
	}
	return ""
}

func (x *CompanyQuestionRecordParams) GetMoveFrom() string {
	if x != nil {
		return x.MoveFrom
	}
	return ""
}

func (x *CompanyQuestionRecordParams) GetSourceFrom() string {
	if x != nil {
		return x.SourceFrom
	}
	return ""
}

func (x *CompanyQuestionRecordParams) GetSourceFromOther() string {
	if x != nil && x.SourceFromOther != nil {
		return *x.SourceFromOther
	}
	return ""
}

// save company question record result
type CompanyQuestionRecordResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// update result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *CompanyQuestionRecordResult) Reset() {
	*x = CompanyQuestionRecordResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyQuestionRecordResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyQuestionRecordResult) ProtoMessage() {}

func (x *CompanyQuestionRecordResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyQuestionRecordResult.ProtoReflect.Descriptor instead.
func (*CompanyQuestionRecordResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{29}
}

func (x *CompanyQuestionRecordResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// sort company params
type SortCompanyParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortCompanyParams) Reset() {
	*x = SortCompanyParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortCompanyParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortCompanyParams) ProtoMessage() {}

func (x *SortCompanyParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortCompanyParams.ProtoReflect.Descriptor instead.
func (*SortCompanyParams) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{30}
}

func (x *SortCompanyParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// sort company result
type SortCompanyResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortCompanyResult) Reset() {
	*x = SortCompanyResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortCompanyResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortCompanyResult) ProtoMessage() {}

func (x *SortCompanyResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortCompanyResult.ProtoReflect.Descriptor instead.
func (*SortCompanyResult) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{31}
}

// company staff
type QueryCompanyStaffByAccountResult_CompanyStaff struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff
	Staff *v1.StaffBriefView `protobuf:"bytes,1,opt,name=staff,proto3" json:"staff,omitempty"`
	// company
	Company *v1.CompanyBriefView `protobuf:"bytes,2,opt,name=company,proto3" json:"company,omitempty"`
}

func (x *QueryCompanyStaffByAccountResult_CompanyStaff) Reset() {
	*x = QueryCompanyStaffByAccountResult_CompanyStaff{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCompanyStaffByAccountResult_CompanyStaff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCompanyStaffByAccountResult_CompanyStaff) ProtoMessage() {}

func (x *QueryCompanyStaffByAccountResult_CompanyStaff) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCompanyStaffByAccountResult_CompanyStaff.ProtoReflect.Descriptor instead.
func (*QueryCompanyStaffByAccountResult_CompanyStaff) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{3, 0}
}

func (x *QueryCompanyStaffByAccountResult_CompanyStaff) GetStaff() *v1.StaffBriefView {
	if x != nil {
		return x.Staff
	}
	return nil
}

func (x *QueryCompanyStaffByAccountResult_CompanyStaff) GetCompany() *v1.CompanyBriefView {
	if x != nil {
		return x.Company
	}
	return nil
}

// company staff with locations
type QueryCompanyStaffByAccountWithLocationsResult_CompanyStaffWithLocations struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff
	Staff *v1.StaffBriefView `protobuf:"bytes,1,opt,name=staff,proto3" json:"staff,omitempty"`
	// company
	Company *v1.CompanyBriefView `protobuf:"bytes,2,opt,name=company,proto3" json:"company,omitempty"`
	// locations
	Locations []*v1.LocationBriefView `protobuf:"bytes,3,rep,name=locations,proto3" json:"locations,omitempty"`
}

func (x *QueryCompanyStaffByAccountWithLocationsResult_CompanyStaffWithLocations) Reset() {
	*x = QueryCompanyStaffByAccountWithLocationsResult_CompanyStaffWithLocations{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCompanyStaffByAccountWithLocationsResult_CompanyStaffWithLocations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCompanyStaffByAccountWithLocationsResult_CompanyStaffWithLocations) ProtoMessage() {}

func (x *QueryCompanyStaffByAccountWithLocationsResult_CompanyStaffWithLocations) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_organization_v1_company_api_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCompanyStaffByAccountWithLocationsResult_CompanyStaffWithLocations.ProtoReflect.Descriptor instead.
func (*QueryCompanyStaffByAccountWithLocationsResult_CompanyStaffWithLocations) Descriptor() ([]byte, []int) {
	return file_moego_api_organization_v1_company_api_proto_rawDescGZIP(), []int{13, 0}
}

func (x *QueryCompanyStaffByAccountWithLocationsResult_CompanyStaffWithLocations) GetStaff() *v1.StaffBriefView {
	if x != nil {
		return x.Staff
	}
	return nil
}

func (x *QueryCompanyStaffByAccountWithLocationsResult_CompanyStaffWithLocations) GetCompany() *v1.CompanyBriefView {
	if x != nil {
		return x.Company
	}
	return nil
}

func (x *QueryCompanyStaffByAccountWithLocationsResult_CompanyStaffWithLocations) GetLocations() []*v1.LocationBriefView {
	if x != nil {
		return x.Locations
	}
	return nil
}

var File_moego_api_organization_v1_company_api_proto protoreflect.FileDescriptor

var file_moego_api_organization_v1_company_api_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x3c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f,
	0x6f, 0x75, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75,
	0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f,
	0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x2f, 0x74, 0x61, 0x78, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x74, 0x61, 0x78, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xea, 0x04, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x55, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x48, 0x00, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x4c, 0x0a,
	0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x4d, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x33, 0x0a, 0x0d, 0x6b, 0x6e,
	0x6f, 0x77, 0x5f, 0x61, 0x62, 0x6f, 0x75, 0x74, 0x5f, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x01, 0x52,
	0x0b, 0x6b, 0x6e, 0x6f, 0x77, 0x41, 0x62, 0x6f, 0x75, 0x74, 0x55, 0x73, 0x88, 0x01, 0x01, 0x12,
	0x2c, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32,
	0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2d, 0x0a,
	0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x98, 0x01, 0x03, 0x52, 0x0c,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x0f,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0e,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x26,
	0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x48, 0x02, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6b, 0x6e, 0x6f, 0x77, 0x5f, 0x61, 0x62, 0x6f, 0x75, 0x74,
	0x5f, 0x75, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x22, 0x55, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x22, 0x0a, 0x20, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22,
	0xb2, 0x02, 0x0a, 0x20, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x6f, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x73, 0x1a, 0x9c, 0x01, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x42, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x12, 0x48, 0x0a, 0x07, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x22, 0xa0, 0x01, 0x0a, 0x24, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x78, 0x0a,
	0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x11, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x40, 0x0a, 0x24, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x23, 0x0a, 0x21, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x8f,
	0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x6a, 0x0a, 0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x11, 0x70,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x22, 0x7c, 0x0a, 0x13, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x15,
	0x0a, 0x13, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1d, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x69, 0x65, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x22, 0xa3, 0x02, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x69, 0x65, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6d, 0x61,
	0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e,
	0x66, 0x6f, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x61, 0x70, 0x1a,
	0x79, 0x0a, 0x18, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49,
	0x6e, 0x66, 0x6f, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x47, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65, 0x66, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x2f, 0x0a, 0x2d, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42,
	0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x57, 0x69, 0x74, 0x68, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0xd2, 0x03, 0x0a, 0x2d,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x57, 0x69, 0x74, 0x68, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xa5, 0x01,
	0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73,
	0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x62, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x57, 0x69, 0x74, 0x68,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x57, 0x69, 0x74, 0x68,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x1a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x57, 0x69, 0x74, 0x68, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0xf8, 0x01, 0x0a, 0x19, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x57, 0x69, 0x74, 0x68, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x42, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x12, 0x48, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x42,
	0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x12, 0x4d, 0x0a, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x72, 0x69, 0x65,
	0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0x61, 0x0a, 0x10, 0x41, 0x64, 0x64, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x4d, 0x0a, 0x08, 0x74, 0x61, 0x78, 0x5f, 0x72, 0x75, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x74, 0x61, 0x78, 0x52,
	0x75, 0x6c, 0x65, 0x22, 0x22, 0x0a, 0x10, 0x41, 0x64, 0x64, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x7d, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x4d, 0x0a, 0x08, 0x74, 0x61, 0x78, 0x5f, 0x72,
	0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65,
	0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x74,
	0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x22, 0x2f, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x2e, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x2f, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x16, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54,
	0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x22, 0x5d, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x45, 0x0a, 0x08, 0x74, 0x61, 0x78, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x07, 0x74, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x22,
	0x1c, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x83, 0x01,
	0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x65, 0x0a, 0x14,
	0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49,
	0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x11, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x22, 0x94, 0x01, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c,
	0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x73, 0x0a, 0x14, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x69,
	0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e,
	0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x11, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e,
	0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x39, 0x0a, 0x1d, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x4a, 0x0a, 0x20, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x22, 0x4c, 0x0a, 0x20, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x6c,
	0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0e, 0x69, 0x73, 0x46, 0x69, 0x6c, 0x6c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x99, 0x03, 0x0a, 0x1b, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x70,
	0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x50, 0x65, 0x72, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x12, 0x35, 0x0a, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x76, 0x61, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x01, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x56, 0x61, 0x6e, 0x73, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x09, 0x6d, 0x6f, 0x76, 0x65,
	0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x18, 0x32, 0x52, 0x08, 0x6d, 0x6f, 0x76, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x28,
	0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x0a, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x38, 0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x02, 0x52, 0x0f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x88,
	0x01, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x76, 0x61, 0x6e, 0x73, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x22, 0x37, 0x0a, 0x1b, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x22, 0x35, 0x0a, 0x11, 0x53, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x03, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x13, 0x0a, 0x11, 0x53,
	0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x32, 0x95, 0x11, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x6f, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x96, 0x01, 0x0a, 0x1a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xa2, 0x01,
	0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x12, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x99, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x71,
	0x0a, 0x0d, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x00, 0x12, 0x8b, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69,
	0x65, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x69, 0x65, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x69, 0x65, 0x73, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0xbf, 0x01, 0x0a, 0x27, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x57, 0x69,
	0x74, 0x68, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x48, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x57, 0x69, 0x74, 0x68, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x57, 0x69, 0x74, 0x68,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x00, 0x12, 0x68, 0x0a, 0x0a, 0x41, 0x64, 0x64, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x12,
	0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x54,
	0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x61, 0x78, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x71, 0x0a, 0x0d, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x71,
	0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x12,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x00, 0x12, 0x74, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x78, 0x52, 0x75, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43,
	0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75,
	0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00,
	0x12, 0x8f, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b,
	0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x38, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x49, 0x6e,
	0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x00, 0x12, 0x96, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x8d, 0x01, 0x0a, 0x19,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x61, 0x76, 0x65, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x0b, 0x53,
	0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x42, 0x87, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x70, 0x69,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_organization_v1_company_api_proto_rawDescOnce sync.Once
	file_moego_api_organization_v1_company_api_proto_rawDescData = file_moego_api_organization_v1_company_api_proto_rawDesc
)

func file_moego_api_organization_v1_company_api_proto_rawDescGZIP() []byte {
	file_moego_api_organization_v1_company_api_proto_rawDescOnce.Do(func() {
		file_moego_api_organization_v1_company_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_organization_v1_company_api_proto_rawDescData)
	})
	return file_moego_api_organization_v1_company_api_proto_rawDescData
}

var file_moego_api_organization_v1_company_api_proto_msgTypes = make([]protoimpl.MessageInfo, 35)
var file_moego_api_organization_v1_company_api_proto_goTypes = []interface{}{
	(*CreateCompanyParams)(nil),                           // 0: moego.api.organization.v1.CreateCompanyParams
	(*CreateCompanyResult)(nil),                           // 1: moego.api.organization.v1.CreateCompanyResult
	(*QueryCompanyStaffByAccountParams)(nil),              // 2: moego.api.organization.v1.QueryCompanyStaffByAccountParams
	(*QueryCompanyStaffByAccountResult)(nil),              // 3: moego.api.organization.v1.QueryCompanyStaffByAccountResult
	(*UpdateCompanyPreferenceSettingParams)(nil),          // 4: moego.api.organization.v1.UpdateCompanyPreferenceSettingParams
	(*UpdateCompanyPreferenceSettingResult)(nil),          // 5: moego.api.organization.v1.UpdateCompanyPreferenceSettingResult
	(*GetCompanyPreferenceSettingParams)(nil),             // 6: moego.api.organization.v1.GetCompanyPreferenceSettingParams
	(*GetCompanyPreferenceSettingResult)(nil),             // 7: moego.api.organization.v1.GetCompanyPreferenceSettingResult
	(*SwitchCompanyParams)(nil),                           // 8: moego.api.organization.v1.SwitchCompanyParams
	(*SwitchCompanyResult)(nil),                           // 9: moego.api.organization.v1.SwitchCompanyResult
	(*GetCompaniesExtraInfoParams)(nil),                   // 10: moego.api.organization.v1.GetCompaniesExtraInfoParams
	(*GetCompaniesExtraInfoResponse)(nil),                 // 11: moego.api.organization.v1.GetCompaniesExtraInfoResponse
	(*QueryCompanyStaffByAccountWithLocationsParams)(nil), // 12: moego.api.organization.v1.QueryCompanyStaffByAccountWithLocationsParams
	(*QueryCompanyStaffByAccountWithLocationsResult)(nil), // 13: moego.api.organization.v1.QueryCompanyStaffByAccountWithLocationsResult
	(*AddTaxRuleParams)(nil),                              // 14: moego.api.organization.v1.AddTaxRuleParams
	(*AddTaxRuleResult)(nil),                              // 15: moego.api.organization.v1.AddTaxRuleResult
	(*UpdateTaxRuleParams)(nil),                           // 16: moego.api.organization.v1.UpdateTaxRuleParams
	(*UpdateTaxRuleResult)(nil),                           // 17: moego.api.organization.v1.UpdateTaxRuleResult
	(*DeleteTaxRuleParams)(nil),                           // 18: moego.api.organization.v1.DeleteTaxRuleParams
	(*DeleteTaxRuleResult)(nil),                           // 19: moego.api.organization.v1.DeleteTaxRuleResult
	(*GetTaxRuleListParams)(nil),                          // 20: moego.api.organization.v1.GetTaxRuleListParams
	(*GetTaxRuleListResult)(nil),                          // 21: moego.api.organization.v1.GetTaxRuleListResult
	(*GetClockInOutSettingParams)(nil),                    // 22: moego.api.organization.v1.GetClockInOutSettingParams
	(*GetClockInOutSettingResult)(nil),                    // 23: moego.api.organization.v1.GetClockInOutSettingResult
	(*UpdateClockInOutSettingParams)(nil),                 // 24: moego.api.organization.v1.UpdateClockInOutSettingParams
	(*UpdateClockInOutSettingResult)(nil),                 // 25: moego.api.organization.v1.UpdateClockInOutSettingResult
	(*CompanyQuestionRecordQueryParams)(nil),              // 26: moego.api.organization.v1.CompanyQuestionRecordQueryParams
	(*CompanyQuestionRecordQueryResult)(nil),              // 27: moego.api.organization.v1.CompanyQuestionRecordQueryResult
	(*CompanyQuestionRecordParams)(nil),                   // 28: moego.api.organization.v1.CompanyQuestionRecordParams
	(*CompanyQuestionRecordResult)(nil),                   // 29: moego.api.organization.v1.CompanyQuestionRecordResult
	(*SortCompanyParams)(nil),                             // 30: moego.api.organization.v1.SortCompanyParams
	(*SortCompanyResult)(nil),                             // 31: moego.api.organization.v1.SortCompanyResult
	(*QueryCompanyStaffByAccountResult_CompanyStaff)(nil), // 32: moego.api.organization.v1.QueryCompanyStaffByAccountResult.CompanyStaff
	nil, // 33: moego.api.organization.v1.GetCompaniesExtraInfoResponse.CompanyExtraInfoMapEntry
	(*QueryCompanyStaffByAccountWithLocationsResult_CompanyStaffWithLocations)(nil), // 34: moego.api.organization.v1.QueryCompanyStaffByAccountWithLocationsResult.CompanyStaffWithLocations
	(*v1.CreateLocationDef)(nil),                 // 35: moego.models.organization.v1.CreateLocationDef
	(v1.SourceType)(0),                           // 36: moego.models.organization.v1.SourceType
	(*v1.CountryDef)(nil),                        // 37: moego.models.organization.v1.CountryDef
	(*v1.TimeZone)(nil),                          // 38: moego.models.organization.v1.TimeZone
	(*v1.UpdateCompanyPreferenceSettingDef)(nil), // 39: moego.models.organization.v1.UpdateCompanyPreferenceSettingDef
	(*v1.CompanyPreferenceSettingModel)(nil),     // 40: moego.models.organization.v1.CompanyPreferenceSettingModel
	(*v1.TaxRuleDef)(nil),                        // 41: moego.models.organization.v1.TaxRuleDef
	(*v1.TaxRuleModel)(nil),                      // 42: moego.models.organization.v1.TaxRuleModel
	(*v1.ClockInOutSettingModel)(nil),            // 43: moego.models.organization.v1.ClockInOutSettingModel
	(*v1.UpdateClockInOutSettingDef)(nil),        // 44: moego.models.organization.v1.UpdateClockInOutSettingDef
	(*v1.StaffBriefView)(nil),                    // 45: moego.models.organization.v1.StaffBriefView
	(*v1.CompanyBriefView)(nil),                  // 46: moego.models.organization.v1.CompanyBriefView
	(*v1.CompanyExtraInfoDef)(nil),               // 47: moego.models.organization.v1.CompanyExtraInfoDef
	(*v1.LocationBriefView)(nil),                 // 48: moego.models.organization.v1.LocationBriefView
}
var file_moego_api_organization_v1_company_api_proto_depIdxs = []int32{
	35, // 0: moego.api.organization.v1.CreateCompanyParams.location:type_name -> moego.models.organization.v1.CreateLocationDef
	36, // 1: moego.api.organization.v1.CreateCompanyParams.source:type_name -> moego.models.organization.v1.SourceType
	37, // 2: moego.api.organization.v1.CreateCompanyParams.country:type_name -> moego.models.organization.v1.CountryDef
	38, // 3: moego.api.organization.v1.CreateCompanyParams.time_zone:type_name -> moego.models.organization.v1.TimeZone
	32, // 4: moego.api.organization.v1.QueryCompanyStaffByAccountResult.company_staffs:type_name -> moego.api.organization.v1.QueryCompanyStaffByAccountResult.CompanyStaff
	39, // 5: moego.api.organization.v1.UpdateCompanyPreferenceSettingParams.preference_setting:type_name -> moego.models.organization.v1.UpdateCompanyPreferenceSettingDef
	40, // 6: moego.api.organization.v1.GetCompanyPreferenceSettingResult.preference_setting:type_name -> moego.models.organization.v1.CompanyPreferenceSettingModel
	33, // 7: moego.api.organization.v1.GetCompaniesExtraInfoResponse.company_extra_info_map:type_name -> moego.api.organization.v1.GetCompaniesExtraInfoResponse.CompanyExtraInfoMapEntry
	34, // 8: moego.api.organization.v1.QueryCompanyStaffByAccountWithLocationsResult.company_staffs_with_locations:type_name -> moego.api.organization.v1.QueryCompanyStaffByAccountWithLocationsResult.CompanyStaffWithLocations
	41, // 9: moego.api.organization.v1.AddTaxRuleParams.tax_rule:type_name -> moego.models.organization.v1.TaxRuleDef
	41, // 10: moego.api.organization.v1.UpdateTaxRuleParams.tax_rule:type_name -> moego.models.organization.v1.TaxRuleDef
	42, // 11: moego.api.organization.v1.GetTaxRuleListResult.tax_rule:type_name -> moego.models.organization.v1.TaxRuleModel
	43, // 12: moego.api.organization.v1.GetClockInOutSettingResult.clock_in_out_setting:type_name -> moego.models.organization.v1.ClockInOutSettingModel
	44, // 13: moego.api.organization.v1.UpdateClockInOutSettingParams.clock_in_out_setting:type_name -> moego.models.organization.v1.UpdateClockInOutSettingDef
	45, // 14: moego.api.organization.v1.QueryCompanyStaffByAccountResult.CompanyStaff.staff:type_name -> moego.models.organization.v1.StaffBriefView
	46, // 15: moego.api.organization.v1.QueryCompanyStaffByAccountResult.CompanyStaff.company:type_name -> moego.models.organization.v1.CompanyBriefView
	47, // 16: moego.api.organization.v1.GetCompaniesExtraInfoResponse.CompanyExtraInfoMapEntry.value:type_name -> moego.models.organization.v1.CompanyExtraInfoDef
	45, // 17: moego.api.organization.v1.QueryCompanyStaffByAccountWithLocationsResult.CompanyStaffWithLocations.staff:type_name -> moego.models.organization.v1.StaffBriefView
	46, // 18: moego.api.organization.v1.QueryCompanyStaffByAccountWithLocationsResult.CompanyStaffWithLocations.company:type_name -> moego.models.organization.v1.CompanyBriefView
	48, // 19: moego.api.organization.v1.QueryCompanyStaffByAccountWithLocationsResult.CompanyStaffWithLocations.locations:type_name -> moego.models.organization.v1.LocationBriefView
	0,  // 20: moego.api.organization.v1.CompanyService.CreateCompany:input_type -> moego.api.organization.v1.CreateCompanyParams
	2,  // 21: moego.api.organization.v1.CompanyService.QueryCompanyStaffByAccount:input_type -> moego.api.organization.v1.QueryCompanyStaffByAccountParams
	4,  // 22: moego.api.organization.v1.CompanyService.UpdateCompanyPreferenceSetting:input_type -> moego.api.organization.v1.UpdateCompanyPreferenceSettingParams
	6,  // 23: moego.api.organization.v1.CompanyService.GetCompanyPreferenceSetting:input_type -> moego.api.organization.v1.GetCompanyPreferenceSettingParams
	8,  // 24: moego.api.organization.v1.CompanyService.SwitchCompany:input_type -> moego.api.organization.v1.SwitchCompanyParams
	10, // 25: moego.api.organization.v1.CompanyService.GetCompaniesExtraInfo:input_type -> moego.api.organization.v1.GetCompaniesExtraInfoParams
	12, // 26: moego.api.organization.v1.CompanyService.QueryCompanyStaffByAccountWithLocations:input_type -> moego.api.organization.v1.QueryCompanyStaffByAccountWithLocationsParams
	14, // 27: moego.api.organization.v1.CompanyService.AddTaxRule:input_type -> moego.api.organization.v1.AddTaxRuleParams
	16, // 28: moego.api.organization.v1.CompanyService.UpdateTaxRule:input_type -> moego.api.organization.v1.UpdateTaxRuleParams
	18, // 29: moego.api.organization.v1.CompanyService.DeleteTaxRule:input_type -> moego.api.organization.v1.DeleteTaxRuleParams
	20, // 30: moego.api.organization.v1.CompanyService.GetTaxRuleList:input_type -> moego.api.organization.v1.GetTaxRuleListParams
	22, // 31: moego.api.organization.v1.CompanyService.GetClockInOutSetting:input_type -> moego.api.organization.v1.GetClockInOutSettingParams
	24, // 32: moego.api.organization.v1.CompanyService.UpdateClockInOutSetting:input_type -> moego.api.organization.v1.UpdateClockInOutSettingParams
	26, // 33: moego.api.organization.v1.CompanyService.GetCompanyQuestionRecord:input_type -> moego.api.organization.v1.CompanyQuestionRecordQueryParams
	28, // 34: moego.api.organization.v1.CompanyService.CompanyQuestionRecordSave:input_type -> moego.api.organization.v1.CompanyQuestionRecordParams
	30, // 35: moego.api.organization.v1.CompanyService.SortCompany:input_type -> moego.api.organization.v1.SortCompanyParams
	1,  // 36: moego.api.organization.v1.CompanyService.CreateCompany:output_type -> moego.api.organization.v1.CreateCompanyResult
	3,  // 37: moego.api.organization.v1.CompanyService.QueryCompanyStaffByAccount:output_type -> moego.api.organization.v1.QueryCompanyStaffByAccountResult
	5,  // 38: moego.api.organization.v1.CompanyService.UpdateCompanyPreferenceSetting:output_type -> moego.api.organization.v1.UpdateCompanyPreferenceSettingResult
	7,  // 39: moego.api.organization.v1.CompanyService.GetCompanyPreferenceSetting:output_type -> moego.api.organization.v1.GetCompanyPreferenceSettingResult
	9,  // 40: moego.api.organization.v1.CompanyService.SwitchCompany:output_type -> moego.api.organization.v1.SwitchCompanyResult
	11, // 41: moego.api.organization.v1.CompanyService.GetCompaniesExtraInfo:output_type -> moego.api.organization.v1.GetCompaniesExtraInfoResponse
	13, // 42: moego.api.organization.v1.CompanyService.QueryCompanyStaffByAccountWithLocations:output_type -> moego.api.organization.v1.QueryCompanyStaffByAccountWithLocationsResult
	15, // 43: moego.api.organization.v1.CompanyService.AddTaxRule:output_type -> moego.api.organization.v1.AddTaxRuleResult
	17, // 44: moego.api.organization.v1.CompanyService.UpdateTaxRule:output_type -> moego.api.organization.v1.UpdateTaxRuleResult
	19, // 45: moego.api.organization.v1.CompanyService.DeleteTaxRule:output_type -> moego.api.organization.v1.DeleteTaxRuleResult
	21, // 46: moego.api.organization.v1.CompanyService.GetTaxRuleList:output_type -> moego.api.organization.v1.GetTaxRuleListResult
	23, // 47: moego.api.organization.v1.CompanyService.GetClockInOutSetting:output_type -> moego.api.organization.v1.GetClockInOutSettingResult
	25, // 48: moego.api.organization.v1.CompanyService.UpdateClockInOutSetting:output_type -> moego.api.organization.v1.UpdateClockInOutSettingResult
	27, // 49: moego.api.organization.v1.CompanyService.GetCompanyQuestionRecord:output_type -> moego.api.organization.v1.CompanyQuestionRecordQueryResult
	29, // 50: moego.api.organization.v1.CompanyService.CompanyQuestionRecordSave:output_type -> moego.api.organization.v1.CompanyQuestionRecordResult
	31, // 51: moego.api.organization.v1.CompanyService.SortCompany:output_type -> moego.api.organization.v1.SortCompanyResult
	36, // [36:52] is the sub-list for method output_type
	20, // [20:36] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_moego_api_organization_v1_company_api_proto_init() }
func file_moego_api_organization_v1_company_api_proto_init() {
	if File_moego_api_organization_v1_company_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_organization_v1_company_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCompanyParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCompanyResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCompanyStaffByAccountParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCompanyStaffByAccountResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCompanyPreferenceSettingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCompanyPreferenceSettingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompanyPreferenceSettingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompanyPreferenceSettingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SwitchCompanyParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SwitchCompanyResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompaniesExtraInfoParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCompaniesExtraInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCompanyStaffByAccountWithLocationsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCompanyStaffByAccountWithLocationsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddTaxRuleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddTaxRuleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTaxRuleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTaxRuleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteTaxRuleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteTaxRuleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTaxRuleListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTaxRuleListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClockInOutSettingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClockInOutSettingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateClockInOutSettingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateClockInOutSettingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyQuestionRecordQueryParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyQuestionRecordQueryResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyQuestionRecordParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyQuestionRecordResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortCompanyParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortCompanyResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCompanyStaffByAccountResult_CompanyStaff); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_organization_v1_company_api_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCompanyStaffByAccountWithLocationsResult_CompanyStaffWithLocations); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_organization_v1_company_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_organization_v1_company_api_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_api_organization_v1_company_api_proto_msgTypes[28].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_organization_v1_company_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   35,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_organization_v1_company_api_proto_goTypes,
		DependencyIndexes: file_moego_api_organization_v1_company_api_proto_depIdxs,
		MessageInfos:      file_moego_api_organization_v1_company_api_proto_msgTypes,
	}.Build()
	File_moego_api_organization_v1_company_api_proto = out.File
	file_moego_api_organization_v1_company_api_proto_rawDesc = nil
	file_moego_api_organization_v1_company_api_proto_goTypes = nil
	file_moego_api_organization_v1_company_api_proto_depIdxs = nil
}
