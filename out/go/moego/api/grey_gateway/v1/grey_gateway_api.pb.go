// @since 2022/5/31 3:21 PM
// <AUTHOR>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/grey_gateway/v1/grey_gateway_api.proto

package greygatewayapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grey_gateway/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetServiceBranchMapResponse
type GetServiceBranchMapResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// moego-service-mesage -> ["feature-a", "bugfix-b"]
	SvcBranchesMap map[string]*GetServiceBranchMapResponse_BranchList `protobuf:"bytes,1,rep,name=svc_branches_map,json=svcBranchesMap,proto3" json:"svc_branches_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetServiceBranchMapResponse) Reset() {
	*x = GetServiceBranchMapResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceBranchMapResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceBranchMapResponse) ProtoMessage() {}

func (x *GetServiceBranchMapResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceBranchMapResponse.ProtoReflect.Descriptor instead.
func (*GetServiceBranchMapResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetServiceBranchMapResponse) GetSvcBranchesMap() map[string]*GetServiceBranchMapResponse_BranchList {
	if x != nil {
		return x.SvcBranchesMap
	}
	return nil
}

// GetServiceBranchMapRequest
type GetServiceBranchMapRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// k8s namespace
	Namespace string `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
}

func (x *GetServiceBranchMapRequest) Reset() {
	*x = GetServiceBranchMapRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceBranchMapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceBranchMapRequest) ProtoMessage() {}

func (x *GetServiceBranchMapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceBranchMapRequest.ProtoReflect.Descriptor instead.
func (*GetServiceBranchMapRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetServiceBranchMapRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

// list grey item
type GetGreyItemListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// k8s namespace
	Namespace string `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
}

func (x *GetGreyItemListRequest) Reset() {
	*x = GetGreyItemListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGreyItemListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGreyItemListRequest) ProtoMessage() {}

func (x *GetGreyItemListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGreyItemListRequest.ProtoReflect.Descriptor instead.
func (*GetGreyItemListRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetGreyItemListRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

// insert
type InsertGreyItemRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// item version, the format is "MMddxxx" (e.g. 0501001)
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// kubernetes namespace
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// description
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// jira tickets
	JiraTickets []string `protobuf:"bytes,10,rep,name=jira_tickets,json=jiraTickets,proto3" json:"jira_tickets,omitempty"`
	// service branch map
	SvcBranchMap map[string]string `protobuf:"bytes,15,rep,name=svc_branch_map,json=svcBranchMap,proto3" json:"svc_branch_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *InsertGreyItemRequest) Reset() {
	*x = InsertGreyItemRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsertGreyItemRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsertGreyItemRequest) ProtoMessage() {}

func (x *InsertGreyItemRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsertGreyItemRequest.ProtoReflect.Descriptor instead.
func (*InsertGreyItemRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDescGZIP(), []int{3}
}

func (x *InsertGreyItemRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InsertGreyItemRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *InsertGreyItemRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *InsertGreyItemRequest) GetJiraTickets() []string {
	if x != nil {
		return x.JiraTickets
	}
	return nil
}

func (x *InsertGreyItemRequest) GetSvcBranchMap() map[string]string {
	if x != nil {
		return x.SvcBranchMap
	}
	return nil
}

// update
type UpdateGreyItemRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// item version, the format is "MMddxxx" (e.g. 0501001)
	Name *wrapperspb.StringValue `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// description (nullable)
	Description *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// jira tickets
	JiraTickets []string `protobuf:"bytes,10,rep,name=jira_tickets,json=jiraTickets,proto3" json:"jira_tickets,omitempty"`
	// service branch map
	SvcBranchMap map[string]string `protobuf:"bytes,15,rep,name=svc_branch_map,json=svcBranchMap,proto3" json:"svc_branch_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UpdateGreyItemRequest) Reset() {
	*x = UpdateGreyItemRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateGreyItemRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGreyItemRequest) ProtoMessage() {}

func (x *UpdateGreyItemRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGreyItemRequest.ProtoReflect.Descriptor instead.
func (*UpdateGreyItemRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateGreyItemRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateGreyItemRequest) GetName() *wrapperspb.StringValue {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *UpdateGreyItemRequest) GetDescription() *wrapperspb.StringValue {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *UpdateGreyItemRequest) GetJiraTickets() []string {
	if x != nil {
		return x.JiraTickets
	}
	return nil
}

func (x *UpdateGreyItemRequest) GetSvcBranchMap() map[string]string {
	if x != nil {
		return x.SvcBranchMap
	}
	return nil
}

// grey item list response
type GetGreyItemListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grey item list
	Items []*v1.GreyItemModel `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *GetGreyItemListResponse) Reset() {
	*x = GetGreyItemListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGreyItemListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGreyItemListResponse) ProtoMessage() {}

func (x *GetGreyItemListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGreyItemListResponse.ProtoReflect.Descriptor instead.
func (*GetGreyItemListResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDescGZIP(), []int{5}
}

func (x *GetGreyItemListResponse) GetItems() []*v1.GreyItemModel {
	if x != nil {
		return x.Items
	}
	return nil
}

// GetGreyItemByName request
type GetGreyItemByNameRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grey item name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetGreyItemByNameRequest) Reset() {
	*x = GetGreyItemByNameRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGreyItemByNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGreyItemByNameRequest) ProtoMessage() {}

func (x *GetGreyItemByNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGreyItemByNameRequest.ProtoReflect.Descriptor instead.
func (*GetGreyItemByNameRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetGreyItemByNameRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// branch list
type GetServiceBranchMapResponse_BranchList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// branches
	Name []string `protobuf:"bytes,1,rep,name=name,proto3" json:"name,omitempty"`
}

func (x *GetServiceBranchMapResponse_BranchList) Reset() {
	*x = GetServiceBranchMapResponse_BranchList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceBranchMapResponse_BranchList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceBranchMapResponse_BranchList) ProtoMessage() {}

func (x *GetServiceBranchMapResponse_BranchList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceBranchMapResponse_BranchList.ProtoReflect.Descriptor instead.
func (*GetServiceBranchMapResponse_BranchList) Descriptor() ([]byte, []int) {
	return file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDescGZIP(), []int{0, 0}
}

func (x *GetServiceBranchMapResponse_BranchList) GetName() []string {
	if x != nil {
		return x.Name
	}
	return nil
}

var File_moego_api_grey_gateway_v1_grey_gateway_api_proto protoreflect.FileDescriptor

var file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x67, 0x72, 0x65, 0x79,
	0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x65, 0x79,
	0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x72,
	0x65, 0x79, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70,
	0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72, 0x65, 0x79, 0x5f, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x65, 0x79, 0x5f, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x69, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbc, 0x02,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x72, 0x61, 0x6e,
	0x63, 0x68, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x74, 0x0a,
	0x10, 0x73, 0x76, 0x63, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x65, 0x73, 0x5f, 0x6d, 0x61,
	0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x65, 0x79, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x72,
	0x61, 0x6e, 0x63, 0x68, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x53, 0x76, 0x63, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x65, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0e, 0x73, 0x76, 0x63, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x65, 0x73,
	0x4d, 0x61, 0x70, 0x1a, 0x20, 0x0a, 0x0a, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x1a, 0x84, 0x01, 0x0a, 0x13, 0x53, 0x76, 0x63, 0x42, 0x72, 0x61,
	0x6e, 0x63, 0x68, 0x65, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x57, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x65, 0x79, 0x5f,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4d, 0x61, 0x70, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x45, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x20, 0x01, 0x28, 0x1e, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x22, 0x41, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x47, 0x72, 0x65, 0x79, 0x49, 0x74,
	0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x20, 0x01, 0x28, 0x1e, 0x52, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x22, 0xf3, 0x02, 0x0a, 0x15, 0x49, 0x6e, 0x73, 0x65, 0x72,
	0x74, 0x47, 0x72, 0x65, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x20, 0x01, 0x28, 0x1e, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x27, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x20, 0x01, 0x28, 0x1e, 0x52, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x72, 0x05, 0x20, 0x00, 0x28, 0xc8, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x0c, 0x6a, 0x69, 0x72, 0x61, 0x5f, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x92, 0x01, 0x04, 0x08, 0x00, 0x28, 0x01, 0x52, 0x0b, 0x6a, 0x69, 0x72, 0x61, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x73, 0x12, 0x74, 0x0a, 0x0e, 0x73, 0x76, 0x63, 0x5f, 0x62, 0x72, 0x61,
	0x6e, 0x63, 0x68, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x65, 0x79, 0x5f, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74,
	0x47, 0x72, 0x65, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x53, 0x76, 0x63, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x9a, 0x01, 0x04, 0x08, 0x01, 0x30, 0x01, 0x52, 0x0c, 0x73,
	0x76, 0x63, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4d, 0x61, 0x70, 0x1a, 0x3f, 0x0a, 0x11, 0x53,
	0x76, 0x63, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x9f, 0x03, 0x0a,
	0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x65, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x3b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x20, 0x01, 0x28, 0x1e, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x4a, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x20, 0x00, 0x28, 0xc8, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x0c, 0x6a, 0x69, 0x72, 0x61,
	0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x00, 0x28, 0x01, 0x52, 0x0b, 0x6a, 0x69, 0x72, 0x61,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x12, 0x74, 0x0a, 0x0e, 0x73, 0x76, 0x63, 0x5f, 0x62,
	0x72, 0x61, 0x6e, 0x63, 0x68, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x65, 0x79,
	0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x47, 0x72, 0x65, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x53, 0x76, 0x63, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x9a, 0x01, 0x04, 0x08, 0x00, 0x30, 0x01, 0x52,
	0x0c, 0x73, 0x76, 0x63, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4d, 0x61, 0x70, 0x1a, 0x3f, 0x0a,
	0x11, 0x53, 0x76, 0x63, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x5c,
	0x0a, 0x17, 0x47, 0x65, 0x74, 0x47, 0x72, 0x65, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x65, 0x79, 0x5f, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x65, 0x79, 0x49, 0x74, 0x65, 0x6d,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x39, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x47, 0x72, 0x65, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x20, 0x01, 0x28,
	0x1e, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x32, 0xfc, 0x05, 0x0a, 0x12, 0x47, 0x72, 0x65, 0x79,
	0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x84,
	0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x72, 0x61,
	0x6e, 0x63, 0x68, 0x4d, 0x61, 0x70, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x67, 0x72, 0x65, 0x79, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x72, 0x61,
	0x6e, 0x63, 0x68, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x65, 0x79, 0x5f, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x47, 0x72, 0x65, 0x79,
	0x49, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x65, 0x79, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x65, 0x79, 0x49, 0x74, 0x65, 0x6d,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x65, 0x79, 0x5f, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x65, 0x79, 0x49,
	0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4e, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x47, 0x72, 0x65, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x64, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x67, 0x72, 0x65, 0x79, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x72, 0x65, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12,
	0x75, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x47, 0x72, 0x65, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x72, 0x65, 0x79, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x65, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x65, 0x79, 0x5f, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x65, 0x79, 0x49, 0x74, 0x65,
	0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x6f, 0x0a, 0x0e, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74,
	0x47, 0x72, 0x65, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x65, 0x79, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x47, 0x72, 0x65, 0x79, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x65, 0x79, 0x5f, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x65, 0x79, 0x49, 0x74,
	0x65, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x6f, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x47, 0x72, 0x65, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x65, 0x79, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x65, 0x79,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x65, 0x79, 0x5f,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x65, 0x79, 0x49,
	0x74, 0x65, 0x6d, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x3c, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x47, 0x72, 0x65, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x64, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x86, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x72, 0x65,
	0x79, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5f,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x67,
	0x72, 0x65, 0x79, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x31, 0x3b, 0x67,
	0x72, 0x65, 0x79, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDescOnce sync.Once
	file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDescData = file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDesc
)

func file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDescGZIP() []byte {
	file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDescOnce.Do(func() {
		file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDescData)
	})
	return file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDescData
}

var file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_moego_api_grey_gateway_v1_grey_gateway_api_proto_goTypes = []interface{}{
	(*GetServiceBranchMapResponse)(nil),            // 0: moego.api.grey_gateway.v1.GetServiceBranchMapResponse
	(*GetServiceBranchMapRequest)(nil),             // 1: moego.api.grey_gateway.v1.GetServiceBranchMapRequest
	(*GetGreyItemListRequest)(nil),                 // 2: moego.api.grey_gateway.v1.GetGreyItemListRequest
	(*InsertGreyItemRequest)(nil),                  // 3: moego.api.grey_gateway.v1.InsertGreyItemRequest
	(*UpdateGreyItemRequest)(nil),                  // 4: moego.api.grey_gateway.v1.UpdateGreyItemRequest
	(*GetGreyItemListResponse)(nil),                // 5: moego.api.grey_gateway.v1.GetGreyItemListResponse
	(*GetGreyItemByNameRequest)(nil),               // 6: moego.api.grey_gateway.v1.GetGreyItemByNameRequest
	(*GetServiceBranchMapResponse_BranchList)(nil), // 7: moego.api.grey_gateway.v1.GetServiceBranchMapResponse.BranchList
	nil,                            // 8: moego.api.grey_gateway.v1.GetServiceBranchMapResponse.SvcBranchesMapEntry
	nil,                            // 9: moego.api.grey_gateway.v1.InsertGreyItemRequest.SvcBranchMapEntry
	nil,                            // 10: moego.api.grey_gateway.v1.UpdateGreyItemRequest.SvcBranchMapEntry
	(*wrapperspb.StringValue)(nil), // 11: google.protobuf.StringValue
	(*v1.GreyItemModel)(nil),       // 12: moego.models.grey_gateway.v1.GreyItemModel
	(*v11.Id)(nil),                 // 13: moego.utils.v1.Id
	(*emptypb.Empty)(nil),          // 14: google.protobuf.Empty
}
var file_moego_api_grey_gateway_v1_grey_gateway_api_proto_depIdxs = []int32{
	8,  // 0: moego.api.grey_gateway.v1.GetServiceBranchMapResponse.svc_branches_map:type_name -> moego.api.grey_gateway.v1.GetServiceBranchMapResponse.SvcBranchesMapEntry
	9,  // 1: moego.api.grey_gateway.v1.InsertGreyItemRequest.svc_branch_map:type_name -> moego.api.grey_gateway.v1.InsertGreyItemRequest.SvcBranchMapEntry
	11, // 2: moego.api.grey_gateway.v1.UpdateGreyItemRequest.name:type_name -> google.protobuf.StringValue
	11, // 3: moego.api.grey_gateway.v1.UpdateGreyItemRequest.description:type_name -> google.protobuf.StringValue
	10, // 4: moego.api.grey_gateway.v1.UpdateGreyItemRequest.svc_branch_map:type_name -> moego.api.grey_gateway.v1.UpdateGreyItemRequest.SvcBranchMapEntry
	12, // 5: moego.api.grey_gateway.v1.GetGreyItemListResponse.items:type_name -> moego.models.grey_gateway.v1.GreyItemModel
	7,  // 6: moego.api.grey_gateway.v1.GetServiceBranchMapResponse.SvcBranchesMapEntry.value:type_name -> moego.api.grey_gateway.v1.GetServiceBranchMapResponse.BranchList
	1,  // 7: moego.api.grey_gateway.v1.GreyGatewayService.GetServiceBranchMap:input_type -> moego.api.grey_gateway.v1.GetServiceBranchMapRequest
	2,  // 8: moego.api.grey_gateway.v1.GreyGatewayService.GetGreyItemList:input_type -> moego.api.grey_gateway.v1.GetGreyItemListRequest
	13, // 9: moego.api.grey_gateway.v1.GreyGatewayService.GetGreyItem:input_type -> moego.utils.v1.Id
	6,  // 10: moego.api.grey_gateway.v1.GreyGatewayService.GetGreyItemByName:input_type -> moego.api.grey_gateway.v1.GetGreyItemByNameRequest
	3,  // 11: moego.api.grey_gateway.v1.GreyGatewayService.InsertGreyItem:input_type -> moego.api.grey_gateway.v1.InsertGreyItemRequest
	4,  // 12: moego.api.grey_gateway.v1.GreyGatewayService.UpdateGreyItem:input_type -> moego.api.grey_gateway.v1.UpdateGreyItemRequest
	13, // 13: moego.api.grey_gateway.v1.GreyGatewayService.DeleteGreyItem:input_type -> moego.utils.v1.Id
	0,  // 14: moego.api.grey_gateway.v1.GreyGatewayService.GetServiceBranchMap:output_type -> moego.api.grey_gateway.v1.GetServiceBranchMapResponse
	5,  // 15: moego.api.grey_gateway.v1.GreyGatewayService.GetGreyItemList:output_type -> moego.api.grey_gateway.v1.GetGreyItemListResponse
	12, // 16: moego.api.grey_gateway.v1.GreyGatewayService.GetGreyItem:output_type -> moego.models.grey_gateway.v1.GreyItemModel
	12, // 17: moego.api.grey_gateway.v1.GreyGatewayService.GetGreyItemByName:output_type -> moego.models.grey_gateway.v1.GreyItemModel
	12, // 18: moego.api.grey_gateway.v1.GreyGatewayService.InsertGreyItem:output_type -> moego.models.grey_gateway.v1.GreyItemModel
	12, // 19: moego.api.grey_gateway.v1.GreyGatewayService.UpdateGreyItem:output_type -> moego.models.grey_gateway.v1.GreyItemModel
	14, // 20: moego.api.grey_gateway.v1.GreyGatewayService.DeleteGreyItem:output_type -> google.protobuf.Empty
	14, // [14:21] is the sub-list for method output_type
	7,  // [7:14] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_moego_api_grey_gateway_v1_grey_gateway_api_proto_init() }
func file_moego_api_grey_gateway_v1_grey_gateway_api_proto_init() {
	if File_moego_api_grey_gateway_v1_grey_gateway_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceBranchMapResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceBranchMapRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGreyItemListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsertGreyItemRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateGreyItemRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGreyItemListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGreyItemByNameRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceBranchMapResponse_BranchList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_grey_gateway_v1_grey_gateway_api_proto_goTypes,
		DependencyIndexes: file_moego_api_grey_gateway_v1_grey_gateway_api_proto_depIdxs,
		MessageInfos:      file_moego_api_grey_gateway_v1_grey_gateway_api_proto_msgTypes,
	}.Build()
	File_moego_api_grey_gateway_v1_grey_gateway_api_proto = out.File
	file_moego_api_grey_gateway_v1_grey_gateway_api_proto_rawDesc = nil
	file_moego_api_grey_gateway_v1_grey_gateway_api_proto_goTypes = nil
	file_moego_api_grey_gateway_v1_grey_gateway_api_proto_depIdxs = nil
}
