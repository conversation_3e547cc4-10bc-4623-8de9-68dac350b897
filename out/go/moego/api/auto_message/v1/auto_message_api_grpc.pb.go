// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/auto_message/v1/auto_message_api.proto

package automessageapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AutoMessageServiceClient is the client API for AutoMessageService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AutoMessageServiceClient interface {
	// transfer auto message config by business. for auto message service initialization
	MessageTransferByBusiness(ctx context.Context, in *MessageTransferByBusinessParams, opts ...grpc.CallOption) (*MessageTransferByBusinessResult, error)
	// get ob auto message list
	GetOBAutoMessageList(ctx context.Context, in *GetOBAutoMessageListParams, opts ...grpc.CallOption) (*GetOBAutoMessageListResult, error)
	// get appointment auto message list
	GetAppointmentAutoMessageList(ctx context.Context, in *GetAppointmentAutoMessageListParams, opts ...grpc.CallOption) (*GetAppointmentAutoMessageListResult, error)
	// get appointment auto message detail
	GetAppointmentAutoMessageDetail(ctx context.Context, in *GetAppointmentAutoMessageDetailParams, opts ...grpc.CallOption) (*GetAppointmentAutoMessageDetailResult, error)
	// update appointment auto message
	UpdateAppointmentAutoMessage(ctx context.Context, in *UpdateAppointmentAutoMessageParams, opts ...grpc.CallOption) (*UpdateAppointmentAutoMessageResult, error)
	// get payment auto message list
	GetPayAutoMessageList(ctx context.Context, in *GetPayAutoMessageListParams, opts ...grpc.CallOption) (*GetPayAutoMessageListResult, error)
	// get payment auto message detail
	GetPayAutoMessageDetail(ctx context.Context, in *GetPayAutoMessageDetailParams, opts ...grpc.CallOption) (*GetPayAutoMessageDetailResult, error)
	// update payment auto message
	UpdatePayAutoMessage(ctx context.Context, in *UpdatePayAutoMessageParams, opts ...grpc.CallOption) (*UpdatePayAutoMessageResult, error)
	// get appointment reminder list
	GetAppointmentReminderList(ctx context.Context, in *GetAppointmentReminderListParams, opts ...grpc.CallOption) (*GetAppointmentReminderListResult, error)
	// get appointment reminder detail
	GetAppointmentReminderDetail(ctx context.Context, in *GetAppointmentReminderDetailParams, opts ...grpc.CallOption) (*GetAppointmentReminderDetailResult, error)
	// update appointment reminder
	UpdateAppointmentReminder(ctx context.Context, in *UpdateAppointmentReminderParams, opts ...grpc.CallOption) (*UpdateAppointmentReminderResult, error)
	// get default reminder list
	GetReminderList(ctx context.Context, in *GetReminderListParams, opts ...grpc.CallOption) (*GetReminderListResult, error)
	// get default reminder detail
	GetReminderDetail(ctx context.Context, in *GetReminderDetailParams, opts ...grpc.CallOption) (*GetReminderDetailResult, error)
	// update default reminder
	UpdateReminder(ctx context.Context, in *UpdateReminderParams, opts ...grpc.CallOption) (*UpdateReminderResult, error)
	// list appointment reminder task
	ListAppointmentReminderTask(ctx context.Context, in *ListAppointmentReminderTaskParams, opts ...grpc.CallOption) (*ListAppointmentReminderTaskResult, error)
	// list pet birthday reminder task
	ListPetBirthdayReminderTask(ctx context.Context, in *ListPetBirthdayReminderTaskParams, opts ...grpc.CallOption) (*ListPetBirthdayReminderTaskResult, error)
	// list rebook reminder task
	ListRebookReminderTask(ctx context.Context, in *ListRebookReminderTaskParams, opts ...grpc.CallOption) (*ListRebookReminderTaskResult, error)
}

type autoMessageServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAutoMessageServiceClient(cc grpc.ClientConnInterface) AutoMessageServiceClient {
	return &autoMessageServiceClient{cc}
}

func (c *autoMessageServiceClient) MessageTransferByBusiness(ctx context.Context, in *MessageTransferByBusinessParams, opts ...grpc.CallOption) (*MessageTransferByBusinessResult, error) {
	out := new(MessageTransferByBusinessResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/MessageTransferByBusiness", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageServiceClient) GetOBAutoMessageList(ctx context.Context, in *GetOBAutoMessageListParams, opts ...grpc.CallOption) (*GetOBAutoMessageListResult, error) {
	out := new(GetOBAutoMessageListResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/GetOBAutoMessageList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageServiceClient) GetAppointmentAutoMessageList(ctx context.Context, in *GetAppointmentAutoMessageListParams, opts ...grpc.CallOption) (*GetAppointmentAutoMessageListResult, error) {
	out := new(GetAppointmentAutoMessageListResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/GetAppointmentAutoMessageList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageServiceClient) GetAppointmentAutoMessageDetail(ctx context.Context, in *GetAppointmentAutoMessageDetailParams, opts ...grpc.CallOption) (*GetAppointmentAutoMessageDetailResult, error) {
	out := new(GetAppointmentAutoMessageDetailResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/GetAppointmentAutoMessageDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageServiceClient) UpdateAppointmentAutoMessage(ctx context.Context, in *UpdateAppointmentAutoMessageParams, opts ...grpc.CallOption) (*UpdateAppointmentAutoMessageResult, error) {
	out := new(UpdateAppointmentAutoMessageResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/UpdateAppointmentAutoMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageServiceClient) GetPayAutoMessageList(ctx context.Context, in *GetPayAutoMessageListParams, opts ...grpc.CallOption) (*GetPayAutoMessageListResult, error) {
	out := new(GetPayAutoMessageListResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/GetPayAutoMessageList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageServiceClient) GetPayAutoMessageDetail(ctx context.Context, in *GetPayAutoMessageDetailParams, opts ...grpc.CallOption) (*GetPayAutoMessageDetailResult, error) {
	out := new(GetPayAutoMessageDetailResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/GetPayAutoMessageDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageServiceClient) UpdatePayAutoMessage(ctx context.Context, in *UpdatePayAutoMessageParams, opts ...grpc.CallOption) (*UpdatePayAutoMessageResult, error) {
	out := new(UpdatePayAutoMessageResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/UpdatePayAutoMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageServiceClient) GetAppointmentReminderList(ctx context.Context, in *GetAppointmentReminderListParams, opts ...grpc.CallOption) (*GetAppointmentReminderListResult, error) {
	out := new(GetAppointmentReminderListResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/GetAppointmentReminderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageServiceClient) GetAppointmentReminderDetail(ctx context.Context, in *GetAppointmentReminderDetailParams, opts ...grpc.CallOption) (*GetAppointmentReminderDetailResult, error) {
	out := new(GetAppointmentReminderDetailResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/GetAppointmentReminderDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageServiceClient) UpdateAppointmentReminder(ctx context.Context, in *UpdateAppointmentReminderParams, opts ...grpc.CallOption) (*UpdateAppointmentReminderResult, error) {
	out := new(UpdateAppointmentReminderResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/UpdateAppointmentReminder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageServiceClient) GetReminderList(ctx context.Context, in *GetReminderListParams, opts ...grpc.CallOption) (*GetReminderListResult, error) {
	out := new(GetReminderListResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/GetReminderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageServiceClient) GetReminderDetail(ctx context.Context, in *GetReminderDetailParams, opts ...grpc.CallOption) (*GetReminderDetailResult, error) {
	out := new(GetReminderDetailResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/GetReminderDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageServiceClient) UpdateReminder(ctx context.Context, in *UpdateReminderParams, opts ...grpc.CallOption) (*UpdateReminderResult, error) {
	out := new(UpdateReminderResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/UpdateReminder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageServiceClient) ListAppointmentReminderTask(ctx context.Context, in *ListAppointmentReminderTaskParams, opts ...grpc.CallOption) (*ListAppointmentReminderTaskResult, error) {
	out := new(ListAppointmentReminderTaskResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/ListAppointmentReminderTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageServiceClient) ListPetBirthdayReminderTask(ctx context.Context, in *ListPetBirthdayReminderTaskParams, opts ...grpc.CallOption) (*ListPetBirthdayReminderTaskResult, error) {
	out := new(ListPetBirthdayReminderTaskResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/ListPetBirthdayReminderTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageServiceClient) ListRebookReminderTask(ctx context.Context, in *ListRebookReminderTaskParams, opts ...grpc.CallOption) (*ListRebookReminderTaskResult, error) {
	out := new(ListRebookReminderTaskResult)
	err := c.cc.Invoke(ctx, "/moego.api.auto_message.v1.AutoMessageService/ListRebookReminderTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AutoMessageServiceServer is the server API for AutoMessageService service.
// All implementations must embed UnimplementedAutoMessageServiceServer
// for forward compatibility
type AutoMessageServiceServer interface {
	// transfer auto message config by business. for auto message service initialization
	MessageTransferByBusiness(context.Context, *MessageTransferByBusinessParams) (*MessageTransferByBusinessResult, error)
	// get ob auto message list
	GetOBAutoMessageList(context.Context, *GetOBAutoMessageListParams) (*GetOBAutoMessageListResult, error)
	// get appointment auto message list
	GetAppointmentAutoMessageList(context.Context, *GetAppointmentAutoMessageListParams) (*GetAppointmentAutoMessageListResult, error)
	// get appointment auto message detail
	GetAppointmentAutoMessageDetail(context.Context, *GetAppointmentAutoMessageDetailParams) (*GetAppointmentAutoMessageDetailResult, error)
	// update appointment auto message
	UpdateAppointmentAutoMessage(context.Context, *UpdateAppointmentAutoMessageParams) (*UpdateAppointmentAutoMessageResult, error)
	// get payment auto message list
	GetPayAutoMessageList(context.Context, *GetPayAutoMessageListParams) (*GetPayAutoMessageListResult, error)
	// get payment auto message detail
	GetPayAutoMessageDetail(context.Context, *GetPayAutoMessageDetailParams) (*GetPayAutoMessageDetailResult, error)
	// update payment auto message
	UpdatePayAutoMessage(context.Context, *UpdatePayAutoMessageParams) (*UpdatePayAutoMessageResult, error)
	// get appointment reminder list
	GetAppointmentReminderList(context.Context, *GetAppointmentReminderListParams) (*GetAppointmentReminderListResult, error)
	// get appointment reminder detail
	GetAppointmentReminderDetail(context.Context, *GetAppointmentReminderDetailParams) (*GetAppointmentReminderDetailResult, error)
	// update appointment reminder
	UpdateAppointmentReminder(context.Context, *UpdateAppointmentReminderParams) (*UpdateAppointmentReminderResult, error)
	// get default reminder list
	GetReminderList(context.Context, *GetReminderListParams) (*GetReminderListResult, error)
	// get default reminder detail
	GetReminderDetail(context.Context, *GetReminderDetailParams) (*GetReminderDetailResult, error)
	// update default reminder
	UpdateReminder(context.Context, *UpdateReminderParams) (*UpdateReminderResult, error)
	// list appointment reminder task
	ListAppointmentReminderTask(context.Context, *ListAppointmentReminderTaskParams) (*ListAppointmentReminderTaskResult, error)
	// list pet birthday reminder task
	ListPetBirthdayReminderTask(context.Context, *ListPetBirthdayReminderTaskParams) (*ListPetBirthdayReminderTaskResult, error)
	// list rebook reminder task
	ListRebookReminderTask(context.Context, *ListRebookReminderTaskParams) (*ListRebookReminderTaskResult, error)
	mustEmbedUnimplementedAutoMessageServiceServer()
}

// UnimplementedAutoMessageServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAutoMessageServiceServer struct {
}

func (UnimplementedAutoMessageServiceServer) MessageTransferByBusiness(context.Context, *MessageTransferByBusinessParams) (*MessageTransferByBusinessResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MessageTransferByBusiness not implemented")
}
func (UnimplementedAutoMessageServiceServer) GetOBAutoMessageList(context.Context, *GetOBAutoMessageListParams) (*GetOBAutoMessageListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOBAutoMessageList not implemented")
}
func (UnimplementedAutoMessageServiceServer) GetAppointmentAutoMessageList(context.Context, *GetAppointmentAutoMessageListParams) (*GetAppointmentAutoMessageListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppointmentAutoMessageList not implemented")
}
func (UnimplementedAutoMessageServiceServer) GetAppointmentAutoMessageDetail(context.Context, *GetAppointmentAutoMessageDetailParams) (*GetAppointmentAutoMessageDetailResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppointmentAutoMessageDetail not implemented")
}
func (UnimplementedAutoMessageServiceServer) UpdateAppointmentAutoMessage(context.Context, *UpdateAppointmentAutoMessageParams) (*UpdateAppointmentAutoMessageResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAppointmentAutoMessage not implemented")
}
func (UnimplementedAutoMessageServiceServer) GetPayAutoMessageList(context.Context, *GetPayAutoMessageListParams) (*GetPayAutoMessageListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPayAutoMessageList not implemented")
}
func (UnimplementedAutoMessageServiceServer) GetPayAutoMessageDetail(context.Context, *GetPayAutoMessageDetailParams) (*GetPayAutoMessageDetailResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPayAutoMessageDetail not implemented")
}
func (UnimplementedAutoMessageServiceServer) UpdatePayAutoMessage(context.Context, *UpdatePayAutoMessageParams) (*UpdatePayAutoMessageResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePayAutoMessage not implemented")
}
func (UnimplementedAutoMessageServiceServer) GetAppointmentReminderList(context.Context, *GetAppointmentReminderListParams) (*GetAppointmentReminderListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppointmentReminderList not implemented")
}
func (UnimplementedAutoMessageServiceServer) GetAppointmentReminderDetail(context.Context, *GetAppointmentReminderDetailParams) (*GetAppointmentReminderDetailResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAppointmentReminderDetail not implemented")
}
func (UnimplementedAutoMessageServiceServer) UpdateAppointmentReminder(context.Context, *UpdateAppointmentReminderParams) (*UpdateAppointmentReminderResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAppointmentReminder not implemented")
}
func (UnimplementedAutoMessageServiceServer) GetReminderList(context.Context, *GetReminderListParams) (*GetReminderListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReminderList not implemented")
}
func (UnimplementedAutoMessageServiceServer) GetReminderDetail(context.Context, *GetReminderDetailParams) (*GetReminderDetailResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReminderDetail not implemented")
}
func (UnimplementedAutoMessageServiceServer) UpdateReminder(context.Context, *UpdateReminderParams) (*UpdateReminderResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReminder not implemented")
}
func (UnimplementedAutoMessageServiceServer) ListAppointmentReminderTask(context.Context, *ListAppointmentReminderTaskParams) (*ListAppointmentReminderTaskResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAppointmentReminderTask not implemented")
}
func (UnimplementedAutoMessageServiceServer) ListPetBirthdayReminderTask(context.Context, *ListPetBirthdayReminderTaskParams) (*ListPetBirthdayReminderTaskResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetBirthdayReminderTask not implemented")
}
func (UnimplementedAutoMessageServiceServer) ListRebookReminderTask(context.Context, *ListRebookReminderTaskParams) (*ListRebookReminderTaskResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRebookReminderTask not implemented")
}
func (UnimplementedAutoMessageServiceServer) mustEmbedUnimplementedAutoMessageServiceServer() {}

// UnsafeAutoMessageServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AutoMessageServiceServer will
// result in compilation errors.
type UnsafeAutoMessageServiceServer interface {
	mustEmbedUnimplementedAutoMessageServiceServer()
}

func RegisterAutoMessageServiceServer(s grpc.ServiceRegistrar, srv AutoMessageServiceServer) {
	s.RegisterService(&AutoMessageService_ServiceDesc, srv)
}

func _AutoMessageService_MessageTransferByBusiness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MessageTransferByBusinessParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).MessageTransferByBusiness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/MessageTransferByBusiness",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).MessageTransferByBusiness(ctx, req.(*MessageTransferByBusinessParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageService_GetOBAutoMessageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOBAutoMessageListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).GetOBAutoMessageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/GetOBAutoMessageList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).GetOBAutoMessageList(ctx, req.(*GetOBAutoMessageListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageService_GetAppointmentAutoMessageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointmentAutoMessageListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).GetAppointmentAutoMessageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/GetAppointmentAutoMessageList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).GetAppointmentAutoMessageList(ctx, req.(*GetAppointmentAutoMessageListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageService_GetAppointmentAutoMessageDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointmentAutoMessageDetailParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).GetAppointmentAutoMessageDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/GetAppointmentAutoMessageDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).GetAppointmentAutoMessageDetail(ctx, req.(*GetAppointmentAutoMessageDetailParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageService_UpdateAppointmentAutoMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAppointmentAutoMessageParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).UpdateAppointmentAutoMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/UpdateAppointmentAutoMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).UpdateAppointmentAutoMessage(ctx, req.(*UpdateAppointmentAutoMessageParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageService_GetPayAutoMessageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPayAutoMessageListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).GetPayAutoMessageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/GetPayAutoMessageList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).GetPayAutoMessageList(ctx, req.(*GetPayAutoMessageListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageService_GetPayAutoMessageDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPayAutoMessageDetailParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).GetPayAutoMessageDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/GetPayAutoMessageDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).GetPayAutoMessageDetail(ctx, req.(*GetPayAutoMessageDetailParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageService_UpdatePayAutoMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePayAutoMessageParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).UpdatePayAutoMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/UpdatePayAutoMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).UpdatePayAutoMessage(ctx, req.(*UpdatePayAutoMessageParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageService_GetAppointmentReminderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointmentReminderListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).GetAppointmentReminderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/GetAppointmentReminderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).GetAppointmentReminderList(ctx, req.(*GetAppointmentReminderListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageService_GetAppointmentReminderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAppointmentReminderDetailParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).GetAppointmentReminderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/GetAppointmentReminderDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).GetAppointmentReminderDetail(ctx, req.(*GetAppointmentReminderDetailParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageService_UpdateAppointmentReminder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAppointmentReminderParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).UpdateAppointmentReminder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/UpdateAppointmentReminder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).UpdateAppointmentReminder(ctx, req.(*UpdateAppointmentReminderParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageService_GetReminderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReminderListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).GetReminderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/GetReminderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).GetReminderList(ctx, req.(*GetReminderListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageService_GetReminderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReminderDetailParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).GetReminderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/GetReminderDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).GetReminderDetail(ctx, req.(*GetReminderDetailParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageService_UpdateReminder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReminderParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).UpdateReminder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/UpdateReminder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).UpdateReminder(ctx, req.(*UpdateReminderParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageService_ListAppointmentReminderTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAppointmentReminderTaskParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).ListAppointmentReminderTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/ListAppointmentReminderTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).ListAppointmentReminderTask(ctx, req.(*ListAppointmentReminderTaskParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageService_ListPetBirthdayReminderTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetBirthdayReminderTaskParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).ListPetBirthdayReminderTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/ListPetBirthdayReminderTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).ListPetBirthdayReminderTask(ctx, req.(*ListPetBirthdayReminderTaskParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageService_ListRebookReminderTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRebookReminderTaskParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageServiceServer).ListRebookReminderTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.auto_message.v1.AutoMessageService/ListRebookReminderTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageServiceServer).ListRebookReminderTask(ctx, req.(*ListRebookReminderTaskParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AutoMessageService_ServiceDesc is the grpc.ServiceDesc for AutoMessageService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AutoMessageService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.auto_message.v1.AutoMessageService",
	HandlerType: (*AutoMessageServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MessageTransferByBusiness",
			Handler:    _AutoMessageService_MessageTransferByBusiness_Handler,
		},
		{
			MethodName: "GetOBAutoMessageList",
			Handler:    _AutoMessageService_GetOBAutoMessageList_Handler,
		},
		{
			MethodName: "GetAppointmentAutoMessageList",
			Handler:    _AutoMessageService_GetAppointmentAutoMessageList_Handler,
		},
		{
			MethodName: "GetAppointmentAutoMessageDetail",
			Handler:    _AutoMessageService_GetAppointmentAutoMessageDetail_Handler,
		},
		{
			MethodName: "UpdateAppointmentAutoMessage",
			Handler:    _AutoMessageService_UpdateAppointmentAutoMessage_Handler,
		},
		{
			MethodName: "GetPayAutoMessageList",
			Handler:    _AutoMessageService_GetPayAutoMessageList_Handler,
		},
		{
			MethodName: "GetPayAutoMessageDetail",
			Handler:    _AutoMessageService_GetPayAutoMessageDetail_Handler,
		},
		{
			MethodName: "UpdatePayAutoMessage",
			Handler:    _AutoMessageService_UpdatePayAutoMessage_Handler,
		},
		{
			MethodName: "GetAppointmentReminderList",
			Handler:    _AutoMessageService_GetAppointmentReminderList_Handler,
		},
		{
			MethodName: "GetAppointmentReminderDetail",
			Handler:    _AutoMessageService_GetAppointmentReminderDetail_Handler,
		},
		{
			MethodName: "UpdateAppointmentReminder",
			Handler:    _AutoMessageService_UpdateAppointmentReminder_Handler,
		},
		{
			MethodName: "GetReminderList",
			Handler:    _AutoMessageService_GetReminderList_Handler,
		},
		{
			MethodName: "GetReminderDetail",
			Handler:    _AutoMessageService_GetReminderDetail_Handler,
		},
		{
			MethodName: "UpdateReminder",
			Handler:    _AutoMessageService_UpdateReminder_Handler,
		},
		{
			MethodName: "ListAppointmentReminderTask",
			Handler:    _AutoMessageService_ListAppointmentReminderTask_Handler,
		},
		{
			MethodName: "ListPetBirthdayReminderTask",
			Handler:    _AutoMessageService_ListPetBirthdayReminderTask_Handler,
		},
		{
			MethodName: "ListRebookReminderTask",
			Handler:    _AutoMessageService_ListRebookReminderTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/auto_message/v1/auto_message_api.proto",
}
