// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/auto_message/v1/auto_message_api.proto

package automessageapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/auto_message/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1"
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// transfer msg config by business params
type MessageTransferByBusinessParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business ids to transfer
	BusinessIds []int64 `protobuf:"varint,1,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// token to verify the request
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *MessageTransferByBusinessParams) Reset() {
	*x = MessageTransferByBusinessParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageTransferByBusinessParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageTransferByBusinessParams) ProtoMessage() {}

func (x *MessageTransferByBusinessParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageTransferByBusinessParams.ProtoReflect.Descriptor instead.
func (*MessageTransferByBusinessParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{0}
}

func (x *MessageTransferByBusinessParams) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *MessageTransferByBusinessParams) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// transfer msg config by business result
type MessageTransferByBusinessResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MessageTransferByBusinessResult) Reset() {
	*x = MessageTransferByBusinessResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageTransferByBusinessResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageTransferByBusinessResult) ProtoMessage() {}

func (x *MessageTransferByBusinessResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageTransferByBusinessResult.ProtoReflect.Descriptor instead.
func (*MessageTransferByBusinessResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{1}
}

// get ob auto message list params
type GetOBAutoMessageListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetOBAutoMessageListParams) Reset() {
	*x = GetOBAutoMessageListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOBAutoMessageListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOBAutoMessageListParams) ProtoMessage() {}

func (x *GetOBAutoMessageListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOBAutoMessageListParams.ProtoReflect.Descriptor instead.
func (*GetOBAutoMessageListParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetOBAutoMessageListParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// get ob auto message list result
type GetOBAutoMessageListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto message list
	AutoMessages []*v1.OBAutoMessageListView `protobuf:"bytes,1,rep,name=auto_messages,json=autoMessages,proto3" json:"auto_messages,omitempty"`
}

func (x *GetOBAutoMessageListResult) Reset() {
	*x = GetOBAutoMessageListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOBAutoMessageListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOBAutoMessageListResult) ProtoMessage() {}

func (x *GetOBAutoMessageListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOBAutoMessageListResult.ProtoReflect.Descriptor instead.
func (*GetOBAutoMessageListResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetOBAutoMessageListResult) GetAutoMessages() []*v1.OBAutoMessageListView {
	if x != nil {
		return x.AutoMessages
	}
	return nil
}

// get appointment auto message list params
type GetAppointmentAutoMessageListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetAppointmentAutoMessageListParams) Reset() {
	*x = GetAppointmentAutoMessageListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentAutoMessageListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentAutoMessageListParams) ProtoMessage() {}

func (x *GetAppointmentAutoMessageListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentAutoMessageListParams.ProtoReflect.Descriptor instead.
func (*GetAppointmentAutoMessageListParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetAppointmentAutoMessageListParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// get appointment auto message list result
type GetAppointmentAutoMessageListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto message list
	AutoMessages []*v1.AppointmentAutoMessageListView `protobuf:"bytes,1,rep,name=auto_messages,json=autoMessages,proto3" json:"auto_messages,omitempty"`
}

func (x *GetAppointmentAutoMessageListResult) Reset() {
	*x = GetAppointmentAutoMessageListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentAutoMessageListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentAutoMessageListResult) ProtoMessage() {}

func (x *GetAppointmentAutoMessageListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentAutoMessageListResult.ProtoReflect.Descriptor instead.
func (*GetAppointmentAutoMessageListResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{5}
}

func (x *GetAppointmentAutoMessageListResult) GetAutoMessages() []*v1.AppointmentAutoMessageListView {
	if x != nil {
		return x.AutoMessages
	}
	return nil
}

// get appointment auto message detail params
type GetAppointmentAutoMessageDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetAppointmentAutoMessageDetailParams) Reset() {
	*x = GetAppointmentAutoMessageDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentAutoMessageDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentAutoMessageDetailParams) ProtoMessage() {}

func (x *GetAppointmentAutoMessageDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentAutoMessageDetailParams.ProtoReflect.Descriptor instead.
func (*GetAppointmentAutoMessageDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetAppointmentAutoMessageDetailParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get appointment auto message detail result
type GetAppointmentAutoMessageDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto message detail
	AutoMessage *v1.AppointmentAutoMessageDetailView `protobuf:"bytes,1,opt,name=auto_message,json=autoMessage,proto3" json:"auto_message,omitempty"`
}

func (x *GetAppointmentAutoMessageDetailResult) Reset() {
	*x = GetAppointmentAutoMessageDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentAutoMessageDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentAutoMessageDetailResult) ProtoMessage() {}

func (x *GetAppointmentAutoMessageDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentAutoMessageDetailResult.ProtoReflect.Descriptor instead.
func (*GetAppointmentAutoMessageDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetAppointmentAutoMessageDetailResult) GetAutoMessage() *v1.AppointmentAutoMessageDetailView {
	if x != nil {
		return x.AutoMessage
	}
	return nil
}

// update appointment auto message params
type UpdateAppointmentAutoMessageParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto message id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// is enabled
	IsEnabled *bool `protobuf:"varint,2,opt,name=is_enabled,json=isEnabled,proto3,oneof" json:"is_enabled,omitempty"`
	// client receive
	ClientReceive *v11.MessageTypeList `protobuf:"bytes,3,opt,name=client_receive,json=clientReceive,proto3,oneof" json:"client_receive,omitempty"`
	// templates for service types
	Template *v1.ServiceTypeTemplateDefList `protobuf:"bytes,4,opt,name=template,proto3,oneof" json:"template,omitempty"`
}

func (x *UpdateAppointmentAutoMessageParams) Reset() {
	*x = UpdateAppointmentAutoMessageParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentAutoMessageParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentAutoMessageParams) ProtoMessage() {}

func (x *UpdateAppointmentAutoMessageParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentAutoMessageParams.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentAutoMessageParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateAppointmentAutoMessageParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAppointmentAutoMessageParams) GetIsEnabled() bool {
	if x != nil && x.IsEnabled != nil {
		return *x.IsEnabled
	}
	return false
}

func (x *UpdateAppointmentAutoMessageParams) GetClientReceive() *v11.MessageTypeList {
	if x != nil {
		return x.ClientReceive
	}
	return nil
}

func (x *UpdateAppointmentAutoMessageParams) GetTemplate() *v1.ServiceTypeTemplateDefList {
	if x != nil {
		return x.Template
	}
	return nil
}

// update appointment auto message result
type UpdateAppointmentAutoMessageResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto message
	AutoMessage *v1.AppointmentAutoMessageDetailView `protobuf:"bytes,1,opt,name=auto_message,json=autoMessage,proto3" json:"auto_message,omitempty"`
}

func (x *UpdateAppointmentAutoMessageResult) Reset() {
	*x = UpdateAppointmentAutoMessageResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentAutoMessageResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentAutoMessageResult) ProtoMessage() {}

func (x *UpdateAppointmentAutoMessageResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentAutoMessageResult.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentAutoMessageResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateAppointmentAutoMessageResult) GetAutoMessage() *v1.AppointmentAutoMessageDetailView {
	if x != nil {
		return x.AutoMessage
	}
	return nil
}

// get payment auto message list params
type GetPayAutoMessageListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetPayAutoMessageListParams) Reset() {
	*x = GetPayAutoMessageListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayAutoMessageListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayAutoMessageListParams) ProtoMessage() {}

func (x *GetPayAutoMessageListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayAutoMessageListParams.ProtoReflect.Descriptor instead.
func (*GetPayAutoMessageListParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{10}
}

func (x *GetPayAutoMessageListParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// get payment auto message list result
type GetPayAutoMessageListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto message list
	AutoMessages []*v1.PayAutoMessageListView `protobuf:"bytes,1,rep,name=auto_messages,json=autoMessages,proto3" json:"auto_messages,omitempty"`
}

func (x *GetPayAutoMessageListResult) Reset() {
	*x = GetPayAutoMessageListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayAutoMessageListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayAutoMessageListResult) ProtoMessage() {}

func (x *GetPayAutoMessageListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayAutoMessageListResult.ProtoReflect.Descriptor instead.
func (*GetPayAutoMessageListResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{11}
}

func (x *GetPayAutoMessageListResult) GetAutoMessages() []*v1.PayAutoMessageListView {
	if x != nil {
		return x.AutoMessages
	}
	return nil
}

// get payment auto message detail params
type GetPayAutoMessageDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetPayAutoMessageDetailParams) Reset() {
	*x = GetPayAutoMessageDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayAutoMessageDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayAutoMessageDetailParams) ProtoMessage() {}

func (x *GetPayAutoMessageDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayAutoMessageDetailParams.ProtoReflect.Descriptor instead.
func (*GetPayAutoMessageDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{12}
}

func (x *GetPayAutoMessageDetailParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get payment auto message detail result
type GetPayAutoMessageDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto message detail
	AutoMessage *v1.PayAutoMessageDetailView `protobuf:"bytes,1,opt,name=auto_message,json=autoMessage,proto3" json:"auto_message,omitempty"`
}

func (x *GetPayAutoMessageDetailResult) Reset() {
	*x = GetPayAutoMessageDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPayAutoMessageDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPayAutoMessageDetailResult) ProtoMessage() {}

func (x *GetPayAutoMessageDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPayAutoMessageDetailResult.ProtoReflect.Descriptor instead.
func (*GetPayAutoMessageDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{13}
}

func (x *GetPayAutoMessageDetailResult) GetAutoMessage() *v1.PayAutoMessageDetailView {
	if x != nil {
		return x.AutoMessage
	}
	return nil
}

// update payment auto message params
type UpdatePayAutoMessageParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto message id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// is enabled
	IsEnabled *bool `protobuf:"varint,2,opt,name=is_enabled,json=isEnabled,proto3,oneof" json:"is_enabled,omitempty"`
	// email subject
	EmailSubject *string `protobuf:"bytes,3,opt,name=email_subject,json=emailSubject,proto3,oneof" json:"email_subject,omitempty"`
	// email body
	EmailBody *string `protobuf:"bytes,4,opt,name=email_body,json=emailBody,proto3,oneof" json:"email_body,omitempty"`
	// sms body
	SmsBody *string `protobuf:"bytes,5,opt,name=sms_body,json=smsBody,proto3,oneof" json:"sms_body,omitempty"`
	// app body
	AppBody *string `protobuf:"bytes,6,opt,name=app_body,json=appBody,proto3,oneof" json:"app_body,omitempty"`
}

func (x *UpdatePayAutoMessageParams) Reset() {
	*x = UpdatePayAutoMessageParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePayAutoMessageParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePayAutoMessageParams) ProtoMessage() {}

func (x *UpdatePayAutoMessageParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePayAutoMessageParams.ProtoReflect.Descriptor instead.
func (*UpdatePayAutoMessageParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{14}
}

func (x *UpdatePayAutoMessageParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePayAutoMessageParams) GetIsEnabled() bool {
	if x != nil && x.IsEnabled != nil {
		return *x.IsEnabled
	}
	return false
}

func (x *UpdatePayAutoMessageParams) GetEmailSubject() string {
	if x != nil && x.EmailSubject != nil {
		return *x.EmailSubject
	}
	return ""
}

func (x *UpdatePayAutoMessageParams) GetEmailBody() string {
	if x != nil && x.EmailBody != nil {
		return *x.EmailBody
	}
	return ""
}

func (x *UpdatePayAutoMessageParams) GetSmsBody() string {
	if x != nil && x.SmsBody != nil {
		return *x.SmsBody
	}
	return ""
}

func (x *UpdatePayAutoMessageParams) GetAppBody() string {
	if x != nil && x.AppBody != nil {
		return *x.AppBody
	}
	return ""
}

// update payment auto message result
type UpdatePayAutoMessageResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto message
	AutoMessage *v1.PayAutoMessageDetailView `protobuf:"bytes,1,opt,name=auto_message,json=autoMessage,proto3" json:"auto_message,omitempty"`
}

func (x *UpdatePayAutoMessageResult) Reset() {
	*x = UpdatePayAutoMessageResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePayAutoMessageResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePayAutoMessageResult) ProtoMessage() {}

func (x *UpdatePayAutoMessageResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePayAutoMessageResult.ProtoReflect.Descriptor instead.
func (*UpdatePayAutoMessageResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{15}
}

func (x *UpdatePayAutoMessageResult) GetAutoMessage() *v1.PayAutoMessageDetailView {
	if x != nil {
		return x.AutoMessage
	}
	return nil
}

// get appointment reminder list params
type GetAppointmentReminderListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// list of message template use case enum. Ignored when empty.
	UseCases []v11.MessageTemplateUseCase `protobuf:"varint,2,rep,packed,name=use_cases,json=useCases,proto3,enum=moego.models.message.v1.MessageTemplateUseCase" json:"use_cases,omitempty"`
}

func (x *GetAppointmentReminderListParams) Reset() {
	*x = GetAppointmentReminderListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentReminderListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentReminderListParams) ProtoMessage() {}

func (x *GetAppointmentReminderListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentReminderListParams.ProtoReflect.Descriptor instead.
func (*GetAppointmentReminderListParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{16}
}

func (x *GetAppointmentReminderListParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetAppointmentReminderListParams) GetUseCases() []v11.MessageTemplateUseCase {
	if x != nil {
		return x.UseCases
	}
	return nil
}

// get appointment reminder list result
type GetAppointmentReminderListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reminder message list
	Reminders []*v1.AppointmentReminderListView `protobuf:"bytes,1,rep,name=reminders,proto3" json:"reminders,omitempty"`
}

func (x *GetAppointmentReminderListResult) Reset() {
	*x = GetAppointmentReminderListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentReminderListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentReminderListResult) ProtoMessage() {}

func (x *GetAppointmentReminderListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentReminderListResult.ProtoReflect.Descriptor instead.
func (*GetAppointmentReminderListResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{17}
}

func (x *GetAppointmentReminderListResult) GetReminders() []*v1.AppointmentReminderListView {
	if x != nil {
		return x.Reminders
	}
	return nil
}

// get appointment reminder detail params
type GetAppointmentReminderDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetAppointmentReminderDetailParams) Reset() {
	*x = GetAppointmentReminderDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentReminderDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentReminderDetailParams) ProtoMessage() {}

func (x *GetAppointmentReminderDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentReminderDetailParams.ProtoReflect.Descriptor instead.
func (*GetAppointmentReminderDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{18}
}

func (x *GetAppointmentReminderDetailParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get appointment reminder detail result
type GetAppointmentReminderDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reminder message detail
	Reminder *v1.AppointmentReminderDetailView `protobuf:"bytes,1,opt,name=reminder,proto3" json:"reminder,omitempty"`
}

func (x *GetAppointmentReminderDetailResult) Reset() {
	*x = GetAppointmentReminderDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentReminderDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentReminderDetailResult) ProtoMessage() {}

func (x *GetAppointmentReminderDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentReminderDetailResult.ProtoReflect.Descriptor instead.
func (*GetAppointmentReminderDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{19}
}

func (x *GetAppointmentReminderDetailResult) GetReminder() *v1.AppointmentReminderDetailView {
	if x != nil {
		return x.Reminder
	}
	return nil
}

// update appointment reminder params
type UpdateAppointmentReminderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reminder message id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// is enabled
	IsEnabled *bool `protobuf:"varint,2,opt,name=is_enabled,json=isEnabled,proto3,oneof" json:"is_enabled,omitempty"`
	// days before
	DaysBefore *int32 `protobuf:"varint,3,opt,name=days_before,json=daysBefore,proto3,oneof" json:"days_before,omitempty"`
	// minutes at
	MinutesAt *int32 `protobuf:"varint,4,opt,name=minutes_at,json=minutesAt,proto3,oneof" json:"minutes_at,omitempty"`
	// template for appointment reminder
	Template *v1.ServiceTypeTemplateDefList `protobuf:"bytes,5,opt,name=template,proto3,oneof" json:"template,omitempty"`
}

func (x *UpdateAppointmentReminderParams) Reset() {
	*x = UpdateAppointmentReminderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentReminderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentReminderParams) ProtoMessage() {}

func (x *UpdateAppointmentReminderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentReminderParams.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentReminderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{20}
}

func (x *UpdateAppointmentReminderParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAppointmentReminderParams) GetIsEnabled() bool {
	if x != nil && x.IsEnabled != nil {
		return *x.IsEnabled
	}
	return false
}

func (x *UpdateAppointmentReminderParams) GetDaysBefore() int32 {
	if x != nil && x.DaysBefore != nil {
		return *x.DaysBefore
	}
	return 0
}

func (x *UpdateAppointmentReminderParams) GetMinutesAt() int32 {
	if x != nil && x.MinutesAt != nil {
		return *x.MinutesAt
	}
	return 0
}

func (x *UpdateAppointmentReminderParams) GetTemplate() *v1.ServiceTypeTemplateDefList {
	if x != nil {
		return x.Template
	}
	return nil
}

// update appointment reminder result
type UpdateAppointmentReminderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reminder message
	Reminder *v1.AppointmentReminderDetailView `protobuf:"bytes,1,opt,name=reminder,proto3" json:"reminder,omitempty"`
}

func (x *UpdateAppointmentReminderResult) Reset() {
	*x = UpdateAppointmentReminderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentReminderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentReminderResult) ProtoMessage() {}

func (x *UpdateAppointmentReminderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentReminderResult.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentReminderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{21}
}

func (x *UpdateAppointmentReminderResult) GetReminder() *v1.AppointmentReminderDetailView {
	if x != nil {
		return x.Reminder
	}
	return nil
}

// get default reminder list params
type GetReminderListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetReminderListParams) Reset() {
	*x = GetReminderListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReminderListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReminderListParams) ProtoMessage() {}

func (x *GetReminderListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReminderListParams.ProtoReflect.Descriptor instead.
func (*GetReminderListParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{22}
}

func (x *GetReminderListParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// get default reminder list result
type GetReminderListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reminder message list
	Reminders []*v1.ReminderListView `protobuf:"bytes,1,rep,name=reminders,proto3" json:"reminders,omitempty"`
}

func (x *GetReminderListResult) Reset() {
	*x = GetReminderListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReminderListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReminderListResult) ProtoMessage() {}

func (x *GetReminderListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReminderListResult.ProtoReflect.Descriptor instead.
func (*GetReminderListResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{23}
}

func (x *GetReminderListResult) GetReminders() []*v1.ReminderListView {
	if x != nil {
		return x.Reminders
	}
	return nil
}

// get default reminder detail params
type GetReminderDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetReminderDetailParams) Reset() {
	*x = GetReminderDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReminderDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReminderDetailParams) ProtoMessage() {}

func (x *GetReminderDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReminderDetailParams.ProtoReflect.Descriptor instead.
func (*GetReminderDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{24}
}

func (x *GetReminderDetailParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get default reminder detail result
type GetReminderDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reminder message detail
	Reminder *v1.ReminderDetailView `protobuf:"bytes,1,opt,name=reminder,proto3" json:"reminder,omitempty"`
}

func (x *GetReminderDetailResult) Reset() {
	*x = GetReminderDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReminderDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReminderDetailResult) ProtoMessage() {}

func (x *GetReminderDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReminderDetailResult.ProtoReflect.Descriptor instead.
func (*GetReminderDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{25}
}

func (x *GetReminderDetailResult) GetReminder() *v1.ReminderDetailView {
	if x != nil {
		return x.Reminder
	}
	return nil
}

// update default reminder params
type UpdateReminderParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto message id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// is enabled
	IsEnabled *bool `protobuf:"varint,2,opt,name=is_enabled,json=isEnabled,proto3,oneof" json:"is_enabled,omitempty"`
	// days before
	DaysBefore *int32 `protobuf:"varint,3,opt,name=days_before,json=daysBefore,proto3,oneof" json:"days_before,omitempty"`
	// hours after
	HoursAfter *int32 `protobuf:"varint,4,opt,name=hours_after,json=hoursAfter,proto3,oneof" json:"hours_after,omitempty"`
	// minutes at
	MinutesAt *int32 `protobuf:"varint,5,opt,name=minutes_at,json=minutesAt,proto3,oneof" json:"minutes_at,omitempty"`
	// email subject
	EmailSubject *string `protobuf:"bytes,6,opt,name=email_subject,json=emailSubject,proto3,oneof" json:"email_subject,omitempty"`
	// email body
	EmailBody *string `protobuf:"bytes,7,opt,name=email_body,json=emailBody,proto3,oneof" json:"email_body,omitempty"`
	// sms body
	SmsBody *string `protobuf:"bytes,8,opt,name=sms_body,json=smsBody,proto3,oneof" json:"sms_body,omitempty"`
	// app body
	AppBody *string `protobuf:"bytes,9,opt,name=app_body,json=appBody,proto3,oneof" json:"app_body,omitempty"`
}

func (x *UpdateReminderParams) Reset() {
	*x = UpdateReminderParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateReminderParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateReminderParams) ProtoMessage() {}

func (x *UpdateReminderParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateReminderParams.ProtoReflect.Descriptor instead.
func (*UpdateReminderParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{26}
}

func (x *UpdateReminderParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateReminderParams) GetIsEnabled() bool {
	if x != nil && x.IsEnabled != nil {
		return *x.IsEnabled
	}
	return false
}

func (x *UpdateReminderParams) GetDaysBefore() int32 {
	if x != nil && x.DaysBefore != nil {
		return *x.DaysBefore
	}
	return 0
}

func (x *UpdateReminderParams) GetHoursAfter() int32 {
	if x != nil && x.HoursAfter != nil {
		return *x.HoursAfter
	}
	return 0
}

func (x *UpdateReminderParams) GetMinutesAt() int32 {
	if x != nil && x.MinutesAt != nil {
		return *x.MinutesAt
	}
	return 0
}

func (x *UpdateReminderParams) GetEmailSubject() string {
	if x != nil && x.EmailSubject != nil {
		return *x.EmailSubject
	}
	return ""
}

func (x *UpdateReminderParams) GetEmailBody() string {
	if x != nil && x.EmailBody != nil {
		return *x.EmailBody
	}
	return ""
}

func (x *UpdateReminderParams) GetSmsBody() string {
	if x != nil && x.SmsBody != nil {
		return *x.SmsBody
	}
	return ""
}

func (x *UpdateReminderParams) GetAppBody() string {
	if x != nil && x.AppBody != nil {
		return *x.AppBody
	}
	return ""
}

// update default reminder result
type UpdateReminderResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto message
	Reminder *v1.ReminderDetailView `protobuf:"bytes,1,opt,name=reminder,proto3" json:"reminder,omitempty"`
}

func (x *UpdateReminderResult) Reset() {
	*x = UpdateReminderResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateReminderResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateReminderResult) ProtoMessage() {}

func (x *UpdateReminderResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateReminderResult.ProtoReflect.Descriptor instead.
func (*UpdateReminderResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{27}
}

func (x *UpdateReminderResult) GetReminder() *v1.ReminderDetailView {
	if x != nil {
		return x.Reminder
	}
	return nil
}

// list appointment reminder task params
type ListAppointmentReminderTaskParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListAppointmentReminderTaskParams) Reset() {
	*x = ListAppointmentReminderTaskParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentReminderTaskParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentReminderTaskParams) ProtoMessage() {}

func (x *ListAppointmentReminderTaskParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentReminderTaskParams.ProtoReflect.Descriptor instead.
func (*ListAppointmentReminderTaskParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{28}
}

func (x *ListAppointmentReminderTaskParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListAppointmentReminderTaskParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list appointment reminder task result
type ListAppointmentReminderTaskResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment reminder task list
	AppointmentReminderTasks []*ListAppointmentReminderTaskResult_AppointmentReminderTaskView `protobuf:"bytes,1,rep,name=appointment_reminder_tasks,json=appointmentReminderTasks,proto3" json:"appointment_reminder_tasks,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListAppointmentReminderTaskResult) Reset() {
	*x = ListAppointmentReminderTaskResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentReminderTaskResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentReminderTaskResult) ProtoMessage() {}

func (x *ListAppointmentReminderTaskResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentReminderTaskResult.ProtoReflect.Descriptor instead.
func (*ListAppointmentReminderTaskResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{29}
}

func (x *ListAppointmentReminderTaskResult) GetAppointmentReminderTasks() []*ListAppointmentReminderTaskResult_AppointmentReminderTaskView {
	if x != nil {
		return x.AppointmentReminderTasks
	}
	return nil
}

func (x *ListAppointmentReminderTaskResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list pet birthday reminder task params
type ListPetBirthdayReminderTaskParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListPetBirthdayReminderTaskParams) Reset() {
	*x = ListPetBirthdayReminderTaskParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetBirthdayReminderTaskParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetBirthdayReminderTaskParams) ProtoMessage() {}

func (x *ListPetBirthdayReminderTaskParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetBirthdayReminderTaskParams.ProtoReflect.Descriptor instead.
func (*ListPetBirthdayReminderTaskParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{30}
}

func (x *ListPetBirthdayReminderTaskParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListPetBirthdayReminderTaskParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list pet birthday reminder task result
type ListPetBirthdayReminderTaskResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet birthday reminder task list
	PetBirthdayReminderTasks []*ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView `protobuf:"bytes,1,rep,name=pet_birthday_reminder_tasks,json=petBirthdayReminderTasks,proto3" json:"pet_birthday_reminder_tasks,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListPetBirthdayReminderTaskResult) Reset() {
	*x = ListPetBirthdayReminderTaskResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetBirthdayReminderTaskResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetBirthdayReminderTaskResult) ProtoMessage() {}

func (x *ListPetBirthdayReminderTaskResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetBirthdayReminderTaskResult.ProtoReflect.Descriptor instead.
func (*ListPetBirthdayReminderTaskResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{31}
}

func (x *ListPetBirthdayReminderTaskResult) GetPetBirthdayReminderTasks() []*ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView {
	if x != nil {
		return x.PetBirthdayReminderTasks
	}
	return nil
}

func (x *ListPetBirthdayReminderTaskResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list rebook reminder task params
type ListRebookReminderTaskParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListRebookReminderTaskParams) Reset() {
	*x = ListRebookReminderTaskParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRebookReminderTaskParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRebookReminderTaskParams) ProtoMessage() {}

func (x *ListRebookReminderTaskParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRebookReminderTaskParams.ProtoReflect.Descriptor instead.
func (*ListRebookReminderTaskParams) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{32}
}

func (x *ListRebookReminderTaskParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListRebookReminderTaskParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list rebook reminder task result
type ListRebookReminderTaskResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rebook reminder task list
	RebookReminderTasks []*ListRebookReminderTaskResult_RebookReminderTaskView `protobuf:"bytes,1,rep,name=rebook_reminder_tasks,json=rebookReminderTasks,proto3" json:"rebook_reminder_tasks,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListRebookReminderTaskResult) Reset() {
	*x = ListRebookReminderTaskResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRebookReminderTaskResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRebookReminderTaskResult) ProtoMessage() {}

func (x *ListRebookReminderTaskResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRebookReminderTaskResult.ProtoReflect.Descriptor instead.
func (*ListRebookReminderTaskResult) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{33}
}

func (x *ListRebookReminderTaskResult) GetRebookReminderTasks() []*ListRebookReminderTaskResult_RebookReminderTaskView {
	if x != nil {
		return x.RebookReminderTasks
	}
	return nil
}

func (x *ListRebookReminderTaskResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// appointment reminder task view
type ListAppointmentReminderTaskResult_AppointmentReminderTaskView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// task id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// customer name
	CustomerName string `protobuf:"bytes,2,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	// appointment date, in yyyy-MM-dd format
	AppointmentStartDate string `protobuf:"bytes,3,opt,name=appointment_start_date,json=appointmentStartDate,proto3" json:"appointment_start_date,omitempty"`
	// end date
	AppointmentEndDate string `protobuf:"bytes,4,opt,name=appointment_end_date,json=appointmentEndDate,proto3" json:"appointment_end_date,omitempty"`
	// appointment start time, the number of minutes of the day
	AppointmentStartTime int32 `protobuf:"varint,5,opt,name=appointment_start_time,json=appointmentStartTime,proto3" json:"appointment_start_time,omitempty"`
	// appointment end time, the number of minutes of the day
	AppointmentEndTime int32 `protobuf:"varint,6,opt,name=appointment_end_time,json=appointmentEndTime,proto3" json:"appointment_end_time,omitempty"`
	// status: scheduled, failed, sent
	Status string `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	// method
	Methods []v11.MessageType `protobuf:"varint,8,rep,packed,name=methods,proto3,enum=moego.models.message.v1.MessageType" json:"methods,omitempty"`
	// use case
	UseCase v11.MessageTemplateUseCase `protobuf:"varint,9,opt,name=use_case,json=useCase,proto3,enum=moego.models.message.v1.MessageTemplateUseCase" json:"use_case,omitempty"`
	// scheduled time
	ScheduledTime *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=scheduled_time,json=scheduledTime,proto3" json:"scheduled_time,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,11,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,12,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
}

func (x *ListAppointmentReminderTaskResult_AppointmentReminderTaskView) Reset() {
	*x = ListAppointmentReminderTaskResult_AppointmentReminderTaskView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentReminderTaskResult_AppointmentReminderTaskView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentReminderTaskResult_AppointmentReminderTaskView) ProtoMessage() {}

func (x *ListAppointmentReminderTaskResult_AppointmentReminderTaskView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentReminderTaskResult_AppointmentReminderTaskView.ProtoReflect.Descriptor instead.
func (*ListAppointmentReminderTaskResult_AppointmentReminderTaskView) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{29, 0}
}

func (x *ListAppointmentReminderTaskResult_AppointmentReminderTaskView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListAppointmentReminderTaskResult_AppointmentReminderTaskView) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *ListAppointmentReminderTaskResult_AppointmentReminderTaskView) GetAppointmentStartDate() string {
	if x != nil {
		return x.AppointmentStartDate
	}
	return ""
}

func (x *ListAppointmentReminderTaskResult_AppointmentReminderTaskView) GetAppointmentEndDate() string {
	if x != nil {
		return x.AppointmentEndDate
	}
	return ""
}

func (x *ListAppointmentReminderTaskResult_AppointmentReminderTaskView) GetAppointmentStartTime() int32 {
	if x != nil {
		return x.AppointmentStartTime
	}
	return 0
}

func (x *ListAppointmentReminderTaskResult_AppointmentReminderTaskView) GetAppointmentEndTime() int32 {
	if x != nil {
		return x.AppointmentEndTime
	}
	return 0
}

func (x *ListAppointmentReminderTaskResult_AppointmentReminderTaskView) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ListAppointmentReminderTaskResult_AppointmentReminderTaskView) GetMethods() []v11.MessageType {
	if x != nil {
		return x.Methods
	}
	return nil
}

func (x *ListAppointmentReminderTaskResult_AppointmentReminderTaskView) GetUseCase() v11.MessageTemplateUseCase {
	if x != nil {
		return x.UseCase
	}
	return v11.MessageTemplateUseCase(0)
}

func (x *ListAppointmentReminderTaskResult_AppointmentReminderTaskView) GetScheduledTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledTime
	}
	return nil
}

func (x *ListAppointmentReminderTaskResult_AppointmentReminderTaskView) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ListAppointmentReminderTaskResult_AppointmentReminderTaskView) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// pet birthday reminder task view
type ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// task id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// customer name
	CustomerName string `protobuf:"bytes,2,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	// pet name
	PetName string `protobuf:"bytes,3,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// pet type
	PetType v12.PetType `protobuf:"varint,4,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// pet avatar
	PetAvatar string `protobuf:"bytes,5,opt,name=pet_avatar,json=petAvatar,proto3" json:"pet_avatar,omitempty"`
	// pet breed
	PetBreed string `protobuf:"bytes,6,opt,name=pet_breed,json=petBreed,proto3" json:"pet_breed,omitempty"`
	// pet birthday
	PetBirthday *date.Date `protobuf:"bytes,7,opt,name=pet_birthday,json=petBirthday,proto3" json:"pet_birthday,omitempty"`
	// scheduled time
	ScheduledTime *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=scheduled_time,json=scheduledTime,proto3" json:"scheduled_time,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,11,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,12,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// use case
	UseCase v11.MessageTemplateUseCase `protobuf:"varint,13,opt,name=use_case,json=useCase,proto3,enum=moego.models.message.v1.MessageTemplateUseCase" json:"use_case,omitempty"`
}

func (x *ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView) Reset() {
	*x = ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView) ProtoMessage() {}

func (x *ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView.ProtoReflect.Descriptor instead.
func (*ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{31, 0}
}

func (x *ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView) GetPetType() v12.PetType {
	if x != nil {
		return x.PetType
	}
	return v12.PetType(0)
}

func (x *ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView) GetPetAvatar() string {
	if x != nil {
		return x.PetAvatar
	}
	return ""
}

func (x *ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView) GetPetBreed() string {
	if x != nil {
		return x.PetBreed
	}
	return ""
}

func (x *ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView) GetPetBirthday() *date.Date {
	if x != nil {
		return x.PetBirthday
	}
	return nil
}

func (x *ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView) GetScheduledTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledTime
	}
	return nil
}

func (x *ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView) GetUseCase() v11.MessageTemplateUseCase {
	if x != nil {
		return x.UseCase
	}
	return v11.MessageTemplateUseCase(0)
}

// rebook reminder task view
type ListRebookReminderTaskResult_RebookReminderTaskView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// task id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// customer name
	CustomerName string `protobuf:"bytes,2,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	// pet name
	PetNames []string `protobuf:"bytes,3,rep,name=pet_names,json=petNames,proto3" json:"pet_names,omitempty"`
	// customer avatar path
	CustomerAvatar string `protobuf:"bytes,4,opt,name=customer_avatar,json=customerAvatar,proto3" json:"customer_avatar,omitempty"`
	// expected date
	ExpectedDate *date.Date `protobuf:"bytes,6,opt,name=expected_date,json=expectedDate,proto3" json:"expected_date,omitempty"`
	// last appointment date time
	LastAppointmentDateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=last_appointment_date_time,json=lastAppointmentDateTime,proto3" json:"last_appointment_date_time,omitempty"`
	// service frequency
	PreferredGroomingFrequency *v13.TimePeriod `protobuf:"bytes,8,opt,name=preferred_grooming_frequency,json=preferredGroomingFrequency,proto3" json:"preferred_grooming_frequency,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,9,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// use case
	UseCase v11.MessageTemplateUseCase `protobuf:"varint,10,opt,name=use_case,json=useCase,proto3,enum=moego.models.message.v1.MessageTemplateUseCase" json:"use_case,omitempty"`
}

func (x *ListRebookReminderTaskResult_RebookReminderTaskView) Reset() {
	*x = ListRebookReminderTaskResult_RebookReminderTaskView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRebookReminderTaskResult_RebookReminderTaskView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRebookReminderTaskResult_RebookReminderTaskView) ProtoMessage() {}

func (x *ListRebookReminderTaskResult_RebookReminderTaskView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRebookReminderTaskResult_RebookReminderTaskView.ProtoReflect.Descriptor instead.
func (*ListRebookReminderTaskResult_RebookReminderTaskView) Descriptor() ([]byte, []int) {
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP(), []int{33, 0}
}

func (x *ListRebookReminderTaskResult_RebookReminderTaskView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListRebookReminderTaskResult_RebookReminderTaskView) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *ListRebookReminderTaskResult_RebookReminderTaskView) GetPetNames() []string {
	if x != nil {
		return x.PetNames
	}
	return nil
}

func (x *ListRebookReminderTaskResult_RebookReminderTaskView) GetCustomerAvatar() string {
	if x != nil {
		return x.CustomerAvatar
	}
	return ""
}

func (x *ListRebookReminderTaskResult_RebookReminderTaskView) GetExpectedDate() *date.Date {
	if x != nil {
		return x.ExpectedDate
	}
	return nil
}

func (x *ListRebookReminderTaskResult_RebookReminderTaskView) GetLastAppointmentDateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastAppointmentDateTime
	}
	return nil
}

func (x *ListRebookReminderTaskResult_RebookReminderTaskView) GetPreferredGroomingFrequency() *v13.TimePeriod {
	if x != nil {
		return x.PreferredGroomingFrequency
	}
	return nil
}

func (x *ListRebookReminderTaskResult_RebookReminderTaskView) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ListRebookReminderTaskResult_RebookReminderTaskView) GetUseCase() v11.MessageTemplateUseCase {
	if x != nil {
		return x.UseCase
	}
	return v11.MessageTemplateUseCase(0)
}

var File_moego_api_auto_message_v1_auto_message_api_proto protoreflect.FileDescriptor

var file_moego_api_auto_message_v1_auto_message_api_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x6f,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x75, 0x74, 0x6f,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31,
	0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x72, 0x0a, 0x1f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x42, 0x79, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x2f, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x73, 0x12, 0x1e, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x02, 0x52, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x22, 0x21, 0x0a, 0x1f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x42, 0x79, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x46, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4f, 0x42, 0x41,
	0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x76,
	0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4f, 0x42, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x58, 0x0a, 0x0d,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x42, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x22, 0x4f, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x88, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x61, 0x0a, 0x0d, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x22, 0x40, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x8a, 0x01, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x61,
	0x0a, 0x0c, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41,
	0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0xc1, 0x02, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x22, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x0e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x01, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x08, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x02, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x69, 0x73, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x61, 0x0a, 0x0c,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74,
	0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0x47, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x78, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x50,
	0x61, 0x79, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x59, 0x0a, 0x0d, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61,
	0x79, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x22, 0x38, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x41, 0x75, 0x74, 0x6f,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x7a, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x59, 0x0a,
	0x0c, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x61, 0x75, 0x74,
	0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xdc, 0x02, 0x0a, 0x1a, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x22, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x32, 0x0a, 0x0d, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x75,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0x80, 0x02, 0x48, 0x01, 0x52, 0x0c, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x75,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0a, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x18, 0x80, 0x80, 0x08, 0x48, 0x02, 0x52, 0x09, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x42, 0x6f, 0x64, 0x79, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x08, 0x73, 0x6d, 0x73, 0x5f, 0x62,
	0x6f, 0x64, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x18, 0x80, 0x80, 0x04, 0x48, 0x03, 0x52, 0x07, 0x73, 0x6d, 0x73, 0x42, 0x6f, 0x64, 0x79, 0x88,
	0x01, 0x01, 0x12, 0x29, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x18, 0x80, 0x80, 0x04, 0x48,
	0x04, 0x52, 0x07, 0x61, 0x70, 0x70, 0x42, 0x6f, 0x64, 0x79, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x42, 0x0b, 0x0a,
	0x09, 0x5f, 0x73, 0x6d, 0x73, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x61,
	0x70, 0x70, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x22, 0x77, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x61, 0x79, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x59, 0x0a, 0x0c, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x41, 0x75,
	0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x22, 0xab, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x5d, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x43,
	0x61, 0x73, 0x65, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x75, 0x73, 0x65, 0x43, 0x61, 0x73, 0x65, 0x73, 0x22, 0x7b,
	0x0a, 0x20, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x57, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x09, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x73, 0x22, 0x3d, 0x0a, 0x22, 0x47,
	0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d,
	0x69, 0x6e, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x7d, 0x0a, 0x22, 0x47, 0x65,
	0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69,
	0x6e, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x57, 0x0a, 0x08, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d,
	0x69, 0x6e, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x08, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x22, 0xd5, 0x02, 0x0a, 0x1f, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x69, 0x73,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x64, 0x61,
	0x79, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x1c, 0x28, 0x00, 0x48, 0x01, 0x52, 0x0a, 0x64, 0x61,
	0x79, 0x73, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x0a, 0x6d,
	0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x10, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x02, 0x52, 0x09, 0x6d,
	0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x08, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x03, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x69, 0x73, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x5f, 0x62,
	0x65, 0x66, 0x6f, 0x72, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65,
	0x73, 0x5f, 0x61, 0x74, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x22, 0x7a, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x57, 0x0a, 0x08, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x08, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x22, 0x41, 0x0a,
	0x15, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x22, 0x65, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4c, 0x0a, 0x09, 0x72, 0x65, 0x6d,
	0x69, 0x6e, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x69,
	0x6e, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x09, 0x72, 0x65,
	0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x73, 0x22, 0x32, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x67, 0x0a, 0x17, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4c, 0x0a, 0x08, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x72, 0x65, 0x6d, 0x69,
	0x6e, 0x64, 0x65, 0x72, 0x22, 0x97, 0x04, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x69, 0x73,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x64, 0x61,
	0x79, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x1c, 0x28, 0x00, 0x48, 0x01, 0x52, 0x0a, 0x64, 0x61,
	0x79, 0x73, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x68,
	0x6f, 0x75, 0x72, 0x73, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x10, 0x18, 0x28, 0x00, 0x48, 0x02, 0x52, 0x0a, 0x68,
	0x6f, 0x75, 0x72, 0x73, 0x41, 0x66, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x0a,
	0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x10, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x03, 0x52, 0x09,
	0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x32, 0x0a, 0x0d,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x02, 0x48, 0x04, 0x52,
	0x0c, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x2d, 0x0a, 0x0a, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x18, 0x80, 0x80, 0x08, 0x48,
	0x05, 0x52, 0x09, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x42, 0x6f, 0x64, 0x79, 0x88, 0x01, 0x01, 0x12,
	0x29, 0x0a, 0x08, 0x73, 0x6d, 0x73, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x18, 0x80, 0x80, 0x04, 0x48, 0x06, 0x52, 0x07,
	0x73, 0x6d, 0x73, 0x42, 0x6f, 0x64, 0x79, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x08, 0x61, 0x70,
	0x70, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x18, 0x80, 0x80, 0x04, 0x48, 0x07, 0x52, 0x07, 0x61, 0x70, 0x70, 0x42, 0x6f,
	0x64, 0x79, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x5f, 0x62, 0x65,
	0x66, 0x6f, 0x72, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x73, 0x5f, 0x61,
	0x66, 0x74, 0x65, 0x72, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73,
	0x5f, 0x61, 0x74, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x75,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f,
	0x62, 0x6f, 0x64, 0x79, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x6d, 0x73, 0x5f, 0x62, 0x6f, 0x64,
	0x79, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x22, 0x64,
	0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4c, 0x0a, 0x08, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x72, 0x65, 0x6d, 0x69,
	0x6e, 0x64, 0x65, 0x72, 0x22, 0x9a, 0x01, 0x0a, 0x21, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0xd4, 0x06, 0x0a, 0x21, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x96, 0x01, 0x0a, 0x1a, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x58, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x61,
	0x73, 0x6b, 0x56, 0x69, 0x65, 0x77, 0x52, 0x18, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x73,
	0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xd1, 0x04, 0x0a, 0x1b, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x30, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x3e, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x73, 0x12, 0x4a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x43, 0x61, 0x73, 0x65, 0x52, 0x07, 0x75, 0x73, 0x65, 0x43, 0x61, 0x73, 0x65, 0x12, 0x41, 0x0a,
	0x0e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x22, 0x9a, 0x01, 0x0a, 0x21, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x65, 0x74, 0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x52, 0x65, 0x6d, 0x69,
	0x6e, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe8, 0x05, 0x0a, 0x21, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65,
	0x74, 0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65,
	0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x97, 0x01, 0x0a, 0x1b,
	0x70, 0x65, 0x74, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x5f, 0x72, 0x65, 0x6d,
	0x69, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x58, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x52, 0x65, 0x6d,
	0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e,
	0x50, 0x65, 0x74, 0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x52, 0x65, 0x6d, 0x69, 0x6e,
	0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x56, 0x69, 0x65, 0x77, 0x52, 0x18, 0x70, 0x65, 0x74,
	0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xe4, 0x03, 0x0a, 0x1b, 0x50, 0x65,
	0x74, 0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65,
	0x72, 0x54, 0x61, 0x73, 0x6b, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x70, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07,
	0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x65, 0x74, 0x5f, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72,
	0x65, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x65, 0x74, 0x42, 0x72,
	0x65, 0x65, 0x64, 0x12, 0x34, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68,
	0x64, 0x61, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x70, 0x65,
	0x74, 0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x12, 0x41, 0x0a, 0x0e, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70,
	0x65, 0x74, 0x49, 0x64, 0x12, 0x4a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x55, 0x73, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x07, 0x75, 0x73, 0x65, 0x43, 0x61, 0x73, 0x65,
	0x22, 0x95, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x52,
	0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xd9, 0x05, 0x0a, 0x1c, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x82, 0x01, 0x0a, 0x15, 0x72, 0x65,
	0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x62, 0x6f, 0x6f, 0x6b,
	0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x52, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65,
	0x72, 0x54, 0x61, 0x73, 0x6b, 0x56, 0x69, 0x65, 0x77, 0x52, 0x13, 0x72, 0x65, 0x62, 0x6f, 0x6f,
	0x6b, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x42,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x1a, 0xef, 0x03, 0x0a, 0x16, 0x52, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x6d,
	0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12,
	0x27, 0x0a, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x36, 0x0a, 0x0d, 0x65, 0x78, 0x70, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x0c, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x57, 0x0a, 0x1a, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x17, 0x6c, 0x61, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x5c, 0x0a, 0x1c, 0x70, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f,
	0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x1a, 0x70, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x4a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x5f,
	0x63, 0x61, 0x73, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x07, 0x75, 0x73, 0x65,
	0x43, 0x61, 0x73, 0x65, 0x32, 0xb6, 0x13, 0x0a, 0x12, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x19,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x42,
	0x79, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x42, 0x79, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x42, 0x79, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x84, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4f, 0x42, 0x41, 0x75, 0x74, 0x6f, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x42, 0x41, 0x75, 0x74, 0x6f,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x4f, 0x42, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9f, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xa5, 0x01, 0x0a, 0x1f, 0x47,
	0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74,
	0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x40,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x9c, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x87, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x41, 0x75, 0x74, 0x6f,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x41, 0x75,
	0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8d, 0x01, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x61, 0x79, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x84, 0x01, 0x0a, 0x14,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61,
	0x79, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x96, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69,
	0x6e, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9c, 0x01, 0x0a, 0x1c,
	0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x3d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x93, 0x01, 0x0a, 0x19, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x75, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7b, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6d, 0x69,
	0x6e, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x72, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65,
	0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x99, 0x01, 0x0a, 0x1b, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69,
	0x6e, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x99, 0x01, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74,
	0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x54, 0x61, 0x73, 0x6b, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79,
	0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x52, 0x65,
	0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x8a, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x52,
	0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x62, 0x6f,
	0x6f, 0x6b, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x6d, 0x69, 0x6e,
	0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x86, 0x01,
	0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_auto_message_v1_auto_message_api_proto_rawDescOnce sync.Once
	file_moego_api_auto_message_v1_auto_message_api_proto_rawDescData = file_moego_api_auto_message_v1_auto_message_api_proto_rawDesc
)

func file_moego_api_auto_message_v1_auto_message_api_proto_rawDescGZIP() []byte {
	file_moego_api_auto_message_v1_auto_message_api_proto_rawDescOnce.Do(func() {
		file_moego_api_auto_message_v1_auto_message_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_auto_message_v1_auto_message_api_proto_rawDescData)
	})
	return file_moego_api_auto_message_v1_auto_message_api_proto_rawDescData
}

var file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes = make([]protoimpl.MessageInfo, 37)
var file_moego_api_auto_message_v1_auto_message_api_proto_goTypes = []interface{}{
	(*MessageTransferByBusinessParams)(nil),                               // 0: moego.api.auto_message.v1.MessageTransferByBusinessParams
	(*MessageTransferByBusinessResult)(nil),                               // 1: moego.api.auto_message.v1.MessageTransferByBusinessResult
	(*GetOBAutoMessageListParams)(nil),                                    // 2: moego.api.auto_message.v1.GetOBAutoMessageListParams
	(*GetOBAutoMessageListResult)(nil),                                    // 3: moego.api.auto_message.v1.GetOBAutoMessageListResult
	(*GetAppointmentAutoMessageListParams)(nil),                           // 4: moego.api.auto_message.v1.GetAppointmentAutoMessageListParams
	(*GetAppointmentAutoMessageListResult)(nil),                           // 5: moego.api.auto_message.v1.GetAppointmentAutoMessageListResult
	(*GetAppointmentAutoMessageDetailParams)(nil),                         // 6: moego.api.auto_message.v1.GetAppointmentAutoMessageDetailParams
	(*GetAppointmentAutoMessageDetailResult)(nil),                         // 7: moego.api.auto_message.v1.GetAppointmentAutoMessageDetailResult
	(*UpdateAppointmentAutoMessageParams)(nil),                            // 8: moego.api.auto_message.v1.UpdateAppointmentAutoMessageParams
	(*UpdateAppointmentAutoMessageResult)(nil),                            // 9: moego.api.auto_message.v1.UpdateAppointmentAutoMessageResult
	(*GetPayAutoMessageListParams)(nil),                                   // 10: moego.api.auto_message.v1.GetPayAutoMessageListParams
	(*GetPayAutoMessageListResult)(nil),                                   // 11: moego.api.auto_message.v1.GetPayAutoMessageListResult
	(*GetPayAutoMessageDetailParams)(nil),                                 // 12: moego.api.auto_message.v1.GetPayAutoMessageDetailParams
	(*GetPayAutoMessageDetailResult)(nil),                                 // 13: moego.api.auto_message.v1.GetPayAutoMessageDetailResult
	(*UpdatePayAutoMessageParams)(nil),                                    // 14: moego.api.auto_message.v1.UpdatePayAutoMessageParams
	(*UpdatePayAutoMessageResult)(nil),                                    // 15: moego.api.auto_message.v1.UpdatePayAutoMessageResult
	(*GetAppointmentReminderListParams)(nil),                              // 16: moego.api.auto_message.v1.GetAppointmentReminderListParams
	(*GetAppointmentReminderListResult)(nil),                              // 17: moego.api.auto_message.v1.GetAppointmentReminderListResult
	(*GetAppointmentReminderDetailParams)(nil),                            // 18: moego.api.auto_message.v1.GetAppointmentReminderDetailParams
	(*GetAppointmentReminderDetailResult)(nil),                            // 19: moego.api.auto_message.v1.GetAppointmentReminderDetailResult
	(*UpdateAppointmentReminderParams)(nil),                               // 20: moego.api.auto_message.v1.UpdateAppointmentReminderParams
	(*UpdateAppointmentReminderResult)(nil),                               // 21: moego.api.auto_message.v1.UpdateAppointmentReminderResult
	(*GetReminderListParams)(nil),                                         // 22: moego.api.auto_message.v1.GetReminderListParams
	(*GetReminderListResult)(nil),                                         // 23: moego.api.auto_message.v1.GetReminderListResult
	(*GetReminderDetailParams)(nil),                                       // 24: moego.api.auto_message.v1.GetReminderDetailParams
	(*GetReminderDetailResult)(nil),                                       // 25: moego.api.auto_message.v1.GetReminderDetailResult
	(*UpdateReminderParams)(nil),                                          // 26: moego.api.auto_message.v1.UpdateReminderParams
	(*UpdateReminderResult)(nil),                                          // 27: moego.api.auto_message.v1.UpdateReminderResult
	(*ListAppointmentReminderTaskParams)(nil),                             // 28: moego.api.auto_message.v1.ListAppointmentReminderTaskParams
	(*ListAppointmentReminderTaskResult)(nil),                             // 29: moego.api.auto_message.v1.ListAppointmentReminderTaskResult
	(*ListPetBirthdayReminderTaskParams)(nil),                             // 30: moego.api.auto_message.v1.ListPetBirthdayReminderTaskParams
	(*ListPetBirthdayReminderTaskResult)(nil),                             // 31: moego.api.auto_message.v1.ListPetBirthdayReminderTaskResult
	(*ListRebookReminderTaskParams)(nil),                                  // 32: moego.api.auto_message.v1.ListRebookReminderTaskParams
	(*ListRebookReminderTaskResult)(nil),                                  // 33: moego.api.auto_message.v1.ListRebookReminderTaskResult
	(*ListAppointmentReminderTaskResult_AppointmentReminderTaskView)(nil), // 34: moego.api.auto_message.v1.ListAppointmentReminderTaskResult.AppointmentReminderTaskView
	(*ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView)(nil), // 35: moego.api.auto_message.v1.ListPetBirthdayReminderTaskResult.PetBirthdayReminderTaskView
	(*ListRebookReminderTaskResult_RebookReminderTaskView)(nil),           // 36: moego.api.auto_message.v1.ListRebookReminderTaskResult.RebookReminderTaskView
	(*v1.OBAutoMessageListView)(nil),                                      // 37: moego.models.auto_message.v1.OBAutoMessageListView
	(*v1.AppointmentAutoMessageListView)(nil),                             // 38: moego.models.auto_message.v1.AppointmentAutoMessageListView
	(*v1.AppointmentAutoMessageDetailView)(nil),                           // 39: moego.models.auto_message.v1.AppointmentAutoMessageDetailView
	(*v11.MessageTypeList)(nil),                                           // 40: moego.models.message.v1.MessageTypeList
	(*v1.ServiceTypeTemplateDefList)(nil),                                 // 41: moego.models.auto_message.v1.ServiceTypeTemplateDefList
	(*v1.PayAutoMessageListView)(nil),                                     // 42: moego.models.auto_message.v1.PayAutoMessageListView
	(*v1.PayAutoMessageDetailView)(nil),                                   // 43: moego.models.auto_message.v1.PayAutoMessageDetailView
	(v11.MessageTemplateUseCase)(0),                                       // 44: moego.models.message.v1.MessageTemplateUseCase
	(*v1.AppointmentReminderListView)(nil),                                // 45: moego.models.auto_message.v1.AppointmentReminderListView
	(*v1.AppointmentReminderDetailView)(nil),                              // 46: moego.models.auto_message.v1.AppointmentReminderDetailView
	(*v1.ReminderListView)(nil),                                           // 47: moego.models.auto_message.v1.ReminderListView
	(*v1.ReminderDetailView)(nil),                                         // 48: moego.models.auto_message.v1.ReminderDetailView
	(*v2.PaginationRequest)(nil),                                          // 49: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),                                         // 50: moego.utils.v2.PaginationResponse
	(v11.MessageType)(0),                                                  // 51: moego.models.message.v1.MessageType
	(*timestamppb.Timestamp)(nil),                                         // 52: google.protobuf.Timestamp
	(v12.PetType)(0),                                                      // 53: moego.models.customer.v1.PetType
	(*date.Date)(nil),                                                     // 54: google.type.Date
	(*v13.TimePeriod)(nil),                                                // 55: moego.utils.v1.TimePeriod
}
var file_moego_api_auto_message_v1_auto_message_api_proto_depIdxs = []int32{
	37, // 0: moego.api.auto_message.v1.GetOBAutoMessageListResult.auto_messages:type_name -> moego.models.auto_message.v1.OBAutoMessageListView
	38, // 1: moego.api.auto_message.v1.GetAppointmentAutoMessageListResult.auto_messages:type_name -> moego.models.auto_message.v1.AppointmentAutoMessageListView
	39, // 2: moego.api.auto_message.v1.GetAppointmentAutoMessageDetailResult.auto_message:type_name -> moego.models.auto_message.v1.AppointmentAutoMessageDetailView
	40, // 3: moego.api.auto_message.v1.UpdateAppointmentAutoMessageParams.client_receive:type_name -> moego.models.message.v1.MessageTypeList
	41, // 4: moego.api.auto_message.v1.UpdateAppointmentAutoMessageParams.template:type_name -> moego.models.auto_message.v1.ServiceTypeTemplateDefList
	39, // 5: moego.api.auto_message.v1.UpdateAppointmentAutoMessageResult.auto_message:type_name -> moego.models.auto_message.v1.AppointmentAutoMessageDetailView
	42, // 6: moego.api.auto_message.v1.GetPayAutoMessageListResult.auto_messages:type_name -> moego.models.auto_message.v1.PayAutoMessageListView
	43, // 7: moego.api.auto_message.v1.GetPayAutoMessageDetailResult.auto_message:type_name -> moego.models.auto_message.v1.PayAutoMessageDetailView
	43, // 8: moego.api.auto_message.v1.UpdatePayAutoMessageResult.auto_message:type_name -> moego.models.auto_message.v1.PayAutoMessageDetailView
	44, // 9: moego.api.auto_message.v1.GetAppointmentReminderListParams.use_cases:type_name -> moego.models.message.v1.MessageTemplateUseCase
	45, // 10: moego.api.auto_message.v1.GetAppointmentReminderListResult.reminders:type_name -> moego.models.auto_message.v1.AppointmentReminderListView
	46, // 11: moego.api.auto_message.v1.GetAppointmentReminderDetailResult.reminder:type_name -> moego.models.auto_message.v1.AppointmentReminderDetailView
	41, // 12: moego.api.auto_message.v1.UpdateAppointmentReminderParams.template:type_name -> moego.models.auto_message.v1.ServiceTypeTemplateDefList
	46, // 13: moego.api.auto_message.v1.UpdateAppointmentReminderResult.reminder:type_name -> moego.models.auto_message.v1.AppointmentReminderDetailView
	47, // 14: moego.api.auto_message.v1.GetReminderListResult.reminders:type_name -> moego.models.auto_message.v1.ReminderListView
	48, // 15: moego.api.auto_message.v1.GetReminderDetailResult.reminder:type_name -> moego.models.auto_message.v1.ReminderDetailView
	48, // 16: moego.api.auto_message.v1.UpdateReminderResult.reminder:type_name -> moego.models.auto_message.v1.ReminderDetailView
	49, // 17: moego.api.auto_message.v1.ListAppointmentReminderTaskParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	34, // 18: moego.api.auto_message.v1.ListAppointmentReminderTaskResult.appointment_reminder_tasks:type_name -> moego.api.auto_message.v1.ListAppointmentReminderTaskResult.AppointmentReminderTaskView
	50, // 19: moego.api.auto_message.v1.ListAppointmentReminderTaskResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	49, // 20: moego.api.auto_message.v1.ListPetBirthdayReminderTaskParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	35, // 21: moego.api.auto_message.v1.ListPetBirthdayReminderTaskResult.pet_birthday_reminder_tasks:type_name -> moego.api.auto_message.v1.ListPetBirthdayReminderTaskResult.PetBirthdayReminderTaskView
	50, // 22: moego.api.auto_message.v1.ListPetBirthdayReminderTaskResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	49, // 23: moego.api.auto_message.v1.ListRebookReminderTaskParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	36, // 24: moego.api.auto_message.v1.ListRebookReminderTaskResult.rebook_reminder_tasks:type_name -> moego.api.auto_message.v1.ListRebookReminderTaskResult.RebookReminderTaskView
	50, // 25: moego.api.auto_message.v1.ListRebookReminderTaskResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	51, // 26: moego.api.auto_message.v1.ListAppointmentReminderTaskResult.AppointmentReminderTaskView.methods:type_name -> moego.models.message.v1.MessageType
	44, // 27: moego.api.auto_message.v1.ListAppointmentReminderTaskResult.AppointmentReminderTaskView.use_case:type_name -> moego.models.message.v1.MessageTemplateUseCase
	52, // 28: moego.api.auto_message.v1.ListAppointmentReminderTaskResult.AppointmentReminderTaskView.scheduled_time:type_name -> google.protobuf.Timestamp
	53, // 29: moego.api.auto_message.v1.ListPetBirthdayReminderTaskResult.PetBirthdayReminderTaskView.pet_type:type_name -> moego.models.customer.v1.PetType
	54, // 30: moego.api.auto_message.v1.ListPetBirthdayReminderTaskResult.PetBirthdayReminderTaskView.pet_birthday:type_name -> google.type.Date
	52, // 31: moego.api.auto_message.v1.ListPetBirthdayReminderTaskResult.PetBirthdayReminderTaskView.scheduled_time:type_name -> google.protobuf.Timestamp
	44, // 32: moego.api.auto_message.v1.ListPetBirthdayReminderTaskResult.PetBirthdayReminderTaskView.use_case:type_name -> moego.models.message.v1.MessageTemplateUseCase
	54, // 33: moego.api.auto_message.v1.ListRebookReminderTaskResult.RebookReminderTaskView.expected_date:type_name -> google.type.Date
	52, // 34: moego.api.auto_message.v1.ListRebookReminderTaskResult.RebookReminderTaskView.last_appointment_date_time:type_name -> google.protobuf.Timestamp
	55, // 35: moego.api.auto_message.v1.ListRebookReminderTaskResult.RebookReminderTaskView.preferred_grooming_frequency:type_name -> moego.utils.v1.TimePeriod
	44, // 36: moego.api.auto_message.v1.ListRebookReminderTaskResult.RebookReminderTaskView.use_case:type_name -> moego.models.message.v1.MessageTemplateUseCase
	0,  // 37: moego.api.auto_message.v1.AutoMessageService.MessageTransferByBusiness:input_type -> moego.api.auto_message.v1.MessageTransferByBusinessParams
	2,  // 38: moego.api.auto_message.v1.AutoMessageService.GetOBAutoMessageList:input_type -> moego.api.auto_message.v1.GetOBAutoMessageListParams
	4,  // 39: moego.api.auto_message.v1.AutoMessageService.GetAppointmentAutoMessageList:input_type -> moego.api.auto_message.v1.GetAppointmentAutoMessageListParams
	6,  // 40: moego.api.auto_message.v1.AutoMessageService.GetAppointmentAutoMessageDetail:input_type -> moego.api.auto_message.v1.GetAppointmentAutoMessageDetailParams
	8,  // 41: moego.api.auto_message.v1.AutoMessageService.UpdateAppointmentAutoMessage:input_type -> moego.api.auto_message.v1.UpdateAppointmentAutoMessageParams
	10, // 42: moego.api.auto_message.v1.AutoMessageService.GetPayAutoMessageList:input_type -> moego.api.auto_message.v1.GetPayAutoMessageListParams
	12, // 43: moego.api.auto_message.v1.AutoMessageService.GetPayAutoMessageDetail:input_type -> moego.api.auto_message.v1.GetPayAutoMessageDetailParams
	14, // 44: moego.api.auto_message.v1.AutoMessageService.UpdatePayAutoMessage:input_type -> moego.api.auto_message.v1.UpdatePayAutoMessageParams
	16, // 45: moego.api.auto_message.v1.AutoMessageService.GetAppointmentReminderList:input_type -> moego.api.auto_message.v1.GetAppointmentReminderListParams
	18, // 46: moego.api.auto_message.v1.AutoMessageService.GetAppointmentReminderDetail:input_type -> moego.api.auto_message.v1.GetAppointmentReminderDetailParams
	20, // 47: moego.api.auto_message.v1.AutoMessageService.UpdateAppointmentReminder:input_type -> moego.api.auto_message.v1.UpdateAppointmentReminderParams
	22, // 48: moego.api.auto_message.v1.AutoMessageService.GetReminderList:input_type -> moego.api.auto_message.v1.GetReminderListParams
	24, // 49: moego.api.auto_message.v1.AutoMessageService.GetReminderDetail:input_type -> moego.api.auto_message.v1.GetReminderDetailParams
	26, // 50: moego.api.auto_message.v1.AutoMessageService.UpdateReminder:input_type -> moego.api.auto_message.v1.UpdateReminderParams
	28, // 51: moego.api.auto_message.v1.AutoMessageService.ListAppointmentReminderTask:input_type -> moego.api.auto_message.v1.ListAppointmentReminderTaskParams
	30, // 52: moego.api.auto_message.v1.AutoMessageService.ListPetBirthdayReminderTask:input_type -> moego.api.auto_message.v1.ListPetBirthdayReminderTaskParams
	32, // 53: moego.api.auto_message.v1.AutoMessageService.ListRebookReminderTask:input_type -> moego.api.auto_message.v1.ListRebookReminderTaskParams
	1,  // 54: moego.api.auto_message.v1.AutoMessageService.MessageTransferByBusiness:output_type -> moego.api.auto_message.v1.MessageTransferByBusinessResult
	3,  // 55: moego.api.auto_message.v1.AutoMessageService.GetOBAutoMessageList:output_type -> moego.api.auto_message.v1.GetOBAutoMessageListResult
	5,  // 56: moego.api.auto_message.v1.AutoMessageService.GetAppointmentAutoMessageList:output_type -> moego.api.auto_message.v1.GetAppointmentAutoMessageListResult
	7,  // 57: moego.api.auto_message.v1.AutoMessageService.GetAppointmentAutoMessageDetail:output_type -> moego.api.auto_message.v1.GetAppointmentAutoMessageDetailResult
	9,  // 58: moego.api.auto_message.v1.AutoMessageService.UpdateAppointmentAutoMessage:output_type -> moego.api.auto_message.v1.UpdateAppointmentAutoMessageResult
	11, // 59: moego.api.auto_message.v1.AutoMessageService.GetPayAutoMessageList:output_type -> moego.api.auto_message.v1.GetPayAutoMessageListResult
	13, // 60: moego.api.auto_message.v1.AutoMessageService.GetPayAutoMessageDetail:output_type -> moego.api.auto_message.v1.GetPayAutoMessageDetailResult
	15, // 61: moego.api.auto_message.v1.AutoMessageService.UpdatePayAutoMessage:output_type -> moego.api.auto_message.v1.UpdatePayAutoMessageResult
	17, // 62: moego.api.auto_message.v1.AutoMessageService.GetAppointmentReminderList:output_type -> moego.api.auto_message.v1.GetAppointmentReminderListResult
	19, // 63: moego.api.auto_message.v1.AutoMessageService.GetAppointmentReminderDetail:output_type -> moego.api.auto_message.v1.GetAppointmentReminderDetailResult
	21, // 64: moego.api.auto_message.v1.AutoMessageService.UpdateAppointmentReminder:output_type -> moego.api.auto_message.v1.UpdateAppointmentReminderResult
	23, // 65: moego.api.auto_message.v1.AutoMessageService.GetReminderList:output_type -> moego.api.auto_message.v1.GetReminderListResult
	25, // 66: moego.api.auto_message.v1.AutoMessageService.GetReminderDetail:output_type -> moego.api.auto_message.v1.GetReminderDetailResult
	27, // 67: moego.api.auto_message.v1.AutoMessageService.UpdateReminder:output_type -> moego.api.auto_message.v1.UpdateReminderResult
	29, // 68: moego.api.auto_message.v1.AutoMessageService.ListAppointmentReminderTask:output_type -> moego.api.auto_message.v1.ListAppointmentReminderTaskResult
	31, // 69: moego.api.auto_message.v1.AutoMessageService.ListPetBirthdayReminderTask:output_type -> moego.api.auto_message.v1.ListPetBirthdayReminderTaskResult
	33, // 70: moego.api.auto_message.v1.AutoMessageService.ListRebookReminderTask:output_type -> moego.api.auto_message.v1.ListRebookReminderTaskResult
	54, // [54:71] is the sub-list for method output_type
	37, // [37:54] is the sub-list for method input_type
	37, // [37:37] is the sub-list for extension type_name
	37, // [37:37] is the sub-list for extension extendee
	0,  // [0:37] is the sub-list for field type_name
}

func init() { file_moego_api_auto_message_v1_auto_message_api_proto_init() }
func file_moego_api_auto_message_v1_auto_message_api_proto_init() {
	if File_moego_api_auto_message_v1_auto_message_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageTransferByBusinessParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageTransferByBusinessResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOBAutoMessageListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOBAutoMessageListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentAutoMessageListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentAutoMessageListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentAutoMessageDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentAutoMessageDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentAutoMessageParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentAutoMessageResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayAutoMessageListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayAutoMessageListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayAutoMessageDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPayAutoMessageDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePayAutoMessageParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePayAutoMessageResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentReminderListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentReminderListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentReminderDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentReminderDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentReminderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentReminderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReminderListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReminderListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReminderDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReminderDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateReminderParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateReminderResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentReminderTaskParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentReminderTaskResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetBirthdayReminderTaskParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetBirthdayReminderTaskResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRebookReminderTaskParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRebookReminderTaskResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentReminderTaskResult_AppointmentReminderTaskView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetBirthdayReminderTaskResult_PetBirthdayReminderTaskView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRebookReminderTaskResult_RebookReminderTaskView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[20].OneofWrappers = []interface{}{}
	file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes[26].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_auto_message_v1_auto_message_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   37,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_auto_message_v1_auto_message_api_proto_goTypes,
		DependencyIndexes: file_moego_api_auto_message_v1_auto_message_api_proto_depIdxs,
		MessageInfos:      file_moego_api_auto_message_v1_auto_message_api_proto_msgTypes,
	}.Build()
	File_moego_api_auto_message_v1_auto_message_api_proto = out.File
	file_moego_api_auto_message_v1_auto_message_api_proto_rawDesc = nil
	file_moego_api_auto_message_v1_auto_message_api_proto_goTypes = nil
	file_moego_api_auto_message_v1_auto_message_api_proto_depIdxs = nil
}
