// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/offering/v1/group_class_api.proto

package offeringapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// GroupClassServiceClient is the client API for GroupClassService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GroupClassServiceClient interface {
	// create sessions
	CreateInstance(ctx context.Context, in *CreateInstanceParams, opts ...grpc.CallOption) (*CreateInstanceResult, error)
	// count group class status
	CountInstancesGroupByStatus(ctx context.Context, in *CountInstancesGroupByStatusParams, opts ...grpc.CallOption) (*CountInstancesGroupByStatusResult, error)
	// count group class statuses by group class id
	CountInstancesGroupByClass(ctx context.Context, in *CountInstancesGroupByClassParams, opts ...grpc.CallOption) (*CountInstancesGroupByClassResult, error)
	// GetTrainingClassBatch
	GetInstance(ctx context.Context, in *GetInstanceParams, opts ...grpc.CallOption) (*GetInstanceResult, error)
	// ListTrainingClassBatch
	ListInstances(ctx context.Context, in *ListInstancesParams, opts ...grpc.CallOption) (*ListInstancesResult, error)
	// UpdateGroupClassInstance
	UpdateInstance(ctx context.Context, in *UpdateInstanceParams, opts ...grpc.CallOption) (*UpdateInstanceResult, error)
	// DeleteInstanceAndSessions
	DeleteInstance(ctx context.Context, in *DeleteInstanceParams, opts ...grpc.CallOption) (*DeleteInstanceResult, error)
	// edit session
	UpdateSession(ctx context.Context, in *UpdateSessionParams, opts ...grpc.CallOption) (*UpdateSessionResult, error)
	// list sessions by batch id
	ListSessions(ctx context.Context, in *ListSessionsParams, opts ...grpc.CallOption) (*ListSessionsResult, error)
}

type groupClassServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGroupClassServiceClient(cc grpc.ClientConnInterface) GroupClassServiceClient {
	return &groupClassServiceClient{cc}
}

func (c *groupClassServiceClient) CreateInstance(ctx context.Context, in *CreateInstanceParams, opts ...grpc.CallOption) (*CreateInstanceResult, error) {
	out := new(CreateInstanceResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.GroupClassService/CreateInstance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) CountInstancesGroupByStatus(ctx context.Context, in *CountInstancesGroupByStatusParams, opts ...grpc.CallOption) (*CountInstancesGroupByStatusResult, error) {
	out := new(CountInstancesGroupByStatusResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.GroupClassService/CountInstancesGroupByStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) CountInstancesGroupByClass(ctx context.Context, in *CountInstancesGroupByClassParams, opts ...grpc.CallOption) (*CountInstancesGroupByClassResult, error) {
	out := new(CountInstancesGroupByClassResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.GroupClassService/CountInstancesGroupByClass", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) GetInstance(ctx context.Context, in *GetInstanceParams, opts ...grpc.CallOption) (*GetInstanceResult, error) {
	out := new(GetInstanceResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.GroupClassService/GetInstance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) ListInstances(ctx context.Context, in *ListInstancesParams, opts ...grpc.CallOption) (*ListInstancesResult, error) {
	out := new(ListInstancesResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.GroupClassService/ListInstances", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) UpdateInstance(ctx context.Context, in *UpdateInstanceParams, opts ...grpc.CallOption) (*UpdateInstanceResult, error) {
	out := new(UpdateInstanceResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.GroupClassService/UpdateInstance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) DeleteInstance(ctx context.Context, in *DeleteInstanceParams, opts ...grpc.CallOption) (*DeleteInstanceResult, error) {
	out := new(DeleteInstanceResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.GroupClassService/DeleteInstance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) UpdateSession(ctx context.Context, in *UpdateSessionParams, opts ...grpc.CallOption) (*UpdateSessionResult, error) {
	out := new(UpdateSessionResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.GroupClassService/UpdateSession", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupClassServiceClient) ListSessions(ctx context.Context, in *ListSessionsParams, opts ...grpc.CallOption) (*ListSessionsResult, error) {
	out := new(ListSessionsResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.GroupClassService/ListSessions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GroupClassServiceServer is the server API for GroupClassService service.
// All implementations must embed UnimplementedGroupClassServiceServer
// for forward compatibility
type GroupClassServiceServer interface {
	// create sessions
	CreateInstance(context.Context, *CreateInstanceParams) (*CreateInstanceResult, error)
	// count group class status
	CountInstancesGroupByStatus(context.Context, *CountInstancesGroupByStatusParams) (*CountInstancesGroupByStatusResult, error)
	// count group class statuses by group class id
	CountInstancesGroupByClass(context.Context, *CountInstancesGroupByClassParams) (*CountInstancesGroupByClassResult, error)
	// GetTrainingClassBatch
	GetInstance(context.Context, *GetInstanceParams) (*GetInstanceResult, error)
	// ListTrainingClassBatch
	ListInstances(context.Context, *ListInstancesParams) (*ListInstancesResult, error)
	// UpdateGroupClassInstance
	UpdateInstance(context.Context, *UpdateInstanceParams) (*UpdateInstanceResult, error)
	// DeleteInstanceAndSessions
	DeleteInstance(context.Context, *DeleteInstanceParams) (*DeleteInstanceResult, error)
	// edit session
	UpdateSession(context.Context, *UpdateSessionParams) (*UpdateSessionResult, error)
	// list sessions by batch id
	ListSessions(context.Context, *ListSessionsParams) (*ListSessionsResult, error)
	mustEmbedUnimplementedGroupClassServiceServer()
}

// UnimplementedGroupClassServiceServer must be embedded to have forward compatible implementations.
type UnimplementedGroupClassServiceServer struct {
}

func (UnimplementedGroupClassServiceServer) CreateInstance(context.Context, *CreateInstanceParams) (*CreateInstanceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateInstance not implemented")
}
func (UnimplementedGroupClassServiceServer) CountInstancesGroupByStatus(context.Context, *CountInstancesGroupByStatusParams) (*CountInstancesGroupByStatusResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountInstancesGroupByStatus not implemented")
}
func (UnimplementedGroupClassServiceServer) CountInstancesGroupByClass(context.Context, *CountInstancesGroupByClassParams) (*CountInstancesGroupByClassResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountInstancesGroupByClass not implemented")
}
func (UnimplementedGroupClassServiceServer) GetInstance(context.Context, *GetInstanceParams) (*GetInstanceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInstance not implemented")
}
func (UnimplementedGroupClassServiceServer) ListInstances(context.Context, *ListInstancesParams) (*ListInstancesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListInstances not implemented")
}
func (UnimplementedGroupClassServiceServer) UpdateInstance(context.Context, *UpdateInstanceParams) (*UpdateInstanceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInstance not implemented")
}
func (UnimplementedGroupClassServiceServer) DeleteInstance(context.Context, *DeleteInstanceParams) (*DeleteInstanceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteInstance not implemented")
}
func (UnimplementedGroupClassServiceServer) UpdateSession(context.Context, *UpdateSessionParams) (*UpdateSessionResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSession not implemented")
}
func (UnimplementedGroupClassServiceServer) ListSessions(context.Context, *ListSessionsParams) (*ListSessionsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSessions not implemented")
}
func (UnimplementedGroupClassServiceServer) mustEmbedUnimplementedGroupClassServiceServer() {}

// UnsafeGroupClassServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GroupClassServiceServer will
// result in compilation errors.
type UnsafeGroupClassServiceServer interface {
	mustEmbedUnimplementedGroupClassServiceServer()
}

func RegisterGroupClassServiceServer(s grpc.ServiceRegistrar, srv GroupClassServiceServer) {
	s.RegisterService(&GroupClassService_ServiceDesc, srv)
}

func _GroupClassService_CreateInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInstanceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).CreateInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.GroupClassService/CreateInstance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).CreateInstance(ctx, req.(*CreateInstanceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_CountInstancesGroupByStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountInstancesGroupByStatusParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).CountInstancesGroupByStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.GroupClassService/CountInstancesGroupByStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).CountInstancesGroupByStatus(ctx, req.(*CountInstancesGroupByStatusParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_CountInstancesGroupByClass_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountInstancesGroupByClassParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).CountInstancesGroupByClass(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.GroupClassService/CountInstancesGroupByClass",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).CountInstancesGroupByClass(ctx, req.(*CountInstancesGroupByClassParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_GetInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInstanceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).GetInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.GroupClassService/GetInstance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).GetInstance(ctx, req.(*GetInstanceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_ListInstances_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListInstancesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).ListInstances(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.GroupClassService/ListInstances",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).ListInstances(ctx, req.(*ListInstancesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_UpdateInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInstanceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).UpdateInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.GroupClassService/UpdateInstance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).UpdateInstance(ctx, req.(*UpdateInstanceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_DeleteInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteInstanceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).DeleteInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.GroupClassService/DeleteInstance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).DeleteInstance(ctx, req.(*DeleteInstanceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_UpdateSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSessionParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).UpdateSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.GroupClassService/UpdateSession",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).UpdateSession(ctx, req.(*UpdateSessionParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupClassService_ListSessions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSessionsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupClassServiceServer).ListSessions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.GroupClassService/ListSessions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupClassServiceServer).ListSessions(ctx, req.(*ListSessionsParams))
	}
	return interceptor(ctx, in, info, handler)
}

// GroupClassService_ServiceDesc is the grpc.ServiceDesc for GroupClassService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GroupClassService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.offering.v1.GroupClassService",
	HandlerType: (*GroupClassServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateInstance",
			Handler:    _GroupClassService_CreateInstance_Handler,
		},
		{
			MethodName: "CountInstancesGroupByStatus",
			Handler:    _GroupClassService_CountInstancesGroupByStatus_Handler,
		},
		{
			MethodName: "CountInstancesGroupByClass",
			Handler:    _GroupClassService_CountInstancesGroupByClass_Handler,
		},
		{
			MethodName: "GetInstance",
			Handler:    _GroupClassService_GetInstance_Handler,
		},
		{
			MethodName: "ListInstances",
			Handler:    _GroupClassService_ListInstances_Handler,
		},
		{
			MethodName: "UpdateInstance",
			Handler:    _GroupClassService_UpdateInstance_Handler,
		},
		{
			MethodName: "DeleteInstance",
			Handler:    _GroupClassService_DeleteInstance_Handler,
		},
		{
			MethodName: "UpdateSession",
			Handler:    _GroupClassService_UpdateSession_Handler,
		},
		{
			MethodName: "ListSessions",
			Handler:    _GroupClassService_ListSessions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/offering/v1/group_class_api.proto",
}
