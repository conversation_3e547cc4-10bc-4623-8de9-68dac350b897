// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/offering/v1/service_staff_override_rule.proto

package offeringapipb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ListServiceStaffOverrideRule params
type ListServiceStaffOverrideRuleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id，staff 和 location 有关联，需要传 business_id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// service id list
	ServiceIdList []int64 `protobuf:"varint,2,rep,packed,name=service_id_list,json=serviceIdList,proto3" json:"service_id_list,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListServiceStaffOverrideRuleParams) Reset() {
	*x = ListServiceStaffOverrideRuleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_staff_override_rule_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceStaffOverrideRuleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceStaffOverrideRuleParams) ProtoMessage() {}

func (x *ListServiceStaffOverrideRuleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_staff_override_rule_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceStaffOverrideRuleParams.ProtoReflect.Descriptor instead.
func (*ListServiceStaffOverrideRuleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_staff_override_rule_proto_rawDescGZIP(), []int{0}
}

func (x *ListServiceStaffOverrideRuleParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListServiceStaffOverrideRuleParams) GetServiceIdList() []int64 {
	if x != nil {
		return x.ServiceIdList
	}
	return nil
}

func (x *ListServiceStaffOverrideRuleParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// ListServiceStaffOverrideRule result
type ListServiceStaffOverrideRuleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service staff override rule list
	ServiceStaffOverrideRuleList []*ListServiceStaffOverrideRuleResult_ServiceStaffOverrideRule `protobuf:"bytes,1,rep,name=service_staff_override_rule_list,json=serviceStaffOverrideRuleList,proto3" json:"service_staff_override_rule_list,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListServiceStaffOverrideRuleResult) Reset() {
	*x = ListServiceStaffOverrideRuleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_staff_override_rule_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceStaffOverrideRuleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceStaffOverrideRuleResult) ProtoMessage() {}

func (x *ListServiceStaffOverrideRuleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_staff_override_rule_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceStaffOverrideRuleResult.ProtoReflect.Descriptor instead.
func (*ListServiceStaffOverrideRuleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_staff_override_rule_proto_rawDescGZIP(), []int{1}
}

func (x *ListServiceStaffOverrideRuleResult) GetServiceStaffOverrideRuleList() []*ListServiceStaffOverrideRuleResult_ServiceStaffOverrideRule {
	if x != nil {
		return x.ServiceStaffOverrideRuleList
	}
	return nil
}

func (x *ListServiceStaffOverrideRuleResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// service staff override rule
type ListServiceStaffOverrideRuleResult_ServiceStaffOverrideRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// price
	Price *float64 `protobuf:"fixed64,3,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// duration
	Duration *int32 `protobuf:"varint,4,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
}

func (x *ListServiceStaffOverrideRuleResult_ServiceStaffOverrideRule) Reset() {
	*x = ListServiceStaffOverrideRuleResult_ServiceStaffOverrideRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_service_staff_override_rule_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceStaffOverrideRuleResult_ServiceStaffOverrideRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceStaffOverrideRuleResult_ServiceStaffOverrideRule) ProtoMessage() {}

func (x *ListServiceStaffOverrideRuleResult_ServiceStaffOverrideRule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_service_staff_override_rule_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceStaffOverrideRuleResult_ServiceStaffOverrideRule.ProtoReflect.Descriptor instead.
func (*ListServiceStaffOverrideRuleResult_ServiceStaffOverrideRule) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_service_staff_override_rule_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ListServiceStaffOverrideRuleResult_ServiceStaffOverrideRule) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ListServiceStaffOverrideRuleResult_ServiceStaffOverrideRule) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *ListServiceStaffOverrideRuleResult_ServiceStaffOverrideRule) GetPrice() float64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *ListServiceStaffOverrideRuleResult_ServiceStaffOverrideRule) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

var File_moego_api_offering_v1_service_staff_override_rule_proto protoreflect.FileDescriptor

var file_moego_api_offering_v1_service_staff_override_rule_proto_rawDesc = []byte{
	0x0a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x72,
	0x75, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32,
	0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xce, 0x01, 0x0a, 0x22, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x92, 0x01, 0x03, 0x10, 0xe8, 0x07, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xaf, 0x03, 0x0a, 0x22, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9a, 0x01, 0x0a, 0x20,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x52, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x1c, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x52, 0x75, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xa7, 0x01, 0x0a,
	0x18, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x01, 0x48, 0x00, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1f,
	0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x48, 0x01, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42,
	0x08, 0x0a, 0x06, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0xb8, 0x01, 0x0a, 0x1f, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52,
	0x75, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x94, 0x01, 0x0a, 0x1c, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x39, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x75, 0x6c, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x42, 0x7b, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_offering_v1_service_staff_override_rule_proto_rawDescOnce sync.Once
	file_moego_api_offering_v1_service_staff_override_rule_proto_rawDescData = file_moego_api_offering_v1_service_staff_override_rule_proto_rawDesc
)

func file_moego_api_offering_v1_service_staff_override_rule_proto_rawDescGZIP() []byte {
	file_moego_api_offering_v1_service_staff_override_rule_proto_rawDescOnce.Do(func() {
		file_moego_api_offering_v1_service_staff_override_rule_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_offering_v1_service_staff_override_rule_proto_rawDescData)
	})
	return file_moego_api_offering_v1_service_staff_override_rule_proto_rawDescData
}

var file_moego_api_offering_v1_service_staff_override_rule_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_api_offering_v1_service_staff_override_rule_proto_goTypes = []interface{}{
	(*ListServiceStaffOverrideRuleParams)(nil),                          // 0: moego.api.offering.v1.ListServiceStaffOverrideRuleParams
	(*ListServiceStaffOverrideRuleResult)(nil),                          // 1: moego.api.offering.v1.ListServiceStaffOverrideRuleResult
	(*ListServiceStaffOverrideRuleResult_ServiceStaffOverrideRule)(nil), // 2: moego.api.offering.v1.ListServiceStaffOverrideRuleResult.ServiceStaffOverrideRule
	(*v2.PaginationRequest)(nil),                                        // 3: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),                                       // 4: moego.utils.v2.PaginationResponse
}
var file_moego_api_offering_v1_service_staff_override_rule_proto_depIdxs = []int32{
	3, // 0: moego.api.offering.v1.ListServiceStaffOverrideRuleParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	2, // 1: moego.api.offering.v1.ListServiceStaffOverrideRuleResult.service_staff_override_rule_list:type_name -> moego.api.offering.v1.ListServiceStaffOverrideRuleResult.ServiceStaffOverrideRule
	4, // 2: moego.api.offering.v1.ListServiceStaffOverrideRuleResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	0, // 3: moego.api.offering.v1.ServiceStaffOverrideRuleService.ListServiceStaffOverrideRule:input_type -> moego.api.offering.v1.ListServiceStaffOverrideRuleParams
	1, // 4: moego.api.offering.v1.ServiceStaffOverrideRuleService.ListServiceStaffOverrideRule:output_type -> moego.api.offering.v1.ListServiceStaffOverrideRuleResult
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_api_offering_v1_service_staff_override_rule_proto_init() }
func file_moego_api_offering_v1_service_staff_override_rule_proto_init() {
	if File_moego_api_offering_v1_service_staff_override_rule_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_offering_v1_service_staff_override_rule_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceStaffOverrideRuleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_staff_override_rule_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceStaffOverrideRuleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_service_staff_override_rule_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceStaffOverrideRuleResult_ServiceStaffOverrideRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_offering_v1_service_staff_override_rule_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_offering_v1_service_staff_override_rule_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_offering_v1_service_staff_override_rule_proto_goTypes,
		DependencyIndexes: file_moego_api_offering_v1_service_staff_override_rule_proto_depIdxs,
		MessageInfos:      file_moego_api_offering_v1_service_staff_override_rule_proto_msgTypes,
	}.Build()
	File_moego_api_offering_v1_service_staff_override_rule_proto = out.File
	file_moego_api_offering_v1_service_staff_override_rule_proto_rawDesc = nil
	file_moego_api_offering_v1_service_staff_override_rule_proto_goTypes = nil
	file_moego_api_offering_v1_service_staff_override_rule_proto_depIdxs = nil
}
