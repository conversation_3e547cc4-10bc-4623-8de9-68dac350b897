// @since 2024-07-30 11:14:10
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/offering/v1/pricing_rule_api.proto

package offeringapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// upsert pricing rule params
type UpsertPricingRuleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pricing_rule def
	PricingRuleUpsertDef *v1.PricingRuleUpsertDef `protobuf:"bytes,1,opt,name=pricing_rule_upsert_def,json=pricingRuleUpsertDef,proto3" json:"pricing_rule_upsert_def,omitempty"`
	// apply to upcoming appointments
	ApplyToUpcomingAppointments bool `protobuf:"varint,2,opt,name=apply_to_upcoming_appointments,json=applyToUpcomingAppointments,proto3" json:"apply_to_upcoming_appointments,omitempty"`
}

func (x *UpsertPricingRuleParams) Reset() {
	*x = UpsertPricingRuleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertPricingRuleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertPricingRuleParams) ProtoMessage() {}

func (x *UpsertPricingRuleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertPricingRuleParams.ProtoReflect.Descriptor instead.
func (*UpsertPricingRuleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{0}
}

func (x *UpsertPricingRuleParams) GetPricingRuleUpsertDef() *v1.PricingRuleUpsertDef {
	if x != nil {
		return x.PricingRuleUpsertDef
	}
	return nil
}

func (x *UpsertPricingRuleParams) GetApplyToUpcomingAppointments() bool {
	if x != nil {
		return x.ApplyToUpcomingAppointments
	}
	return false
}

// upsert pricing rule result
type UpsertPricingRuleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the created pricing_rule
	PricingRule *v1.PricingRuleModel `protobuf:"bytes,1,opt,name=pricing_rule,json=pricingRule,proto3" json:"pricing_rule,omitempty"`
}

func (x *UpsertPricingRuleResult) Reset() {
	*x = UpsertPricingRuleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertPricingRuleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertPricingRuleResult) ProtoMessage() {}

func (x *UpsertPricingRuleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertPricingRuleResult.ProtoReflect.Descriptor instead.
func (*UpsertPricingRuleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{1}
}

func (x *UpsertPricingRuleResult) GetPricingRule() *v1.PricingRuleModel {
	if x != nil {
		return x.PricingRule
	}
	return nil
}

// get pricing rule params
type GetPricingRuleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetPricingRuleParams) Reset() {
	*x = GetPricingRuleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPricingRuleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPricingRuleParams) ProtoMessage() {}

func (x *GetPricingRuleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPricingRuleParams.ProtoReflect.Descriptor instead.
func (*GetPricingRuleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetPricingRuleParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get pricing rule result
type GetPricingRuleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pricing rule
	PricingRule *v1.PricingRuleModel `protobuf:"bytes,1,opt,name=pricing_rule,json=pricingRule,proto3" json:"pricing_rule,omitempty"`
}

func (x *GetPricingRuleResult) Reset() {
	*x = GetPricingRuleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPricingRuleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPricingRuleResult) ProtoMessage() {}

func (x *GetPricingRuleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPricingRuleResult.ProtoReflect.Descriptor instead.
func (*GetPricingRuleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetPricingRuleResult) GetPricingRule() *v1.PricingRuleModel {
	if x != nil {
		return x.PricingRule
	}
	return nil
}

// list pricing rule params
type ListPricingRulesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *v1.ListPricingRuleFilter `protobuf:"bytes,3,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,9,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListPricingRulesParams) Reset() {
	*x = ListPricingRulesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRulesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRulesParams) ProtoMessage() {}

func (x *ListPricingRulesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRulesParams.ProtoReflect.Descriptor instead.
func (*ListPricingRulesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{4}
}

func (x *ListPricingRulesParams) GetFilter() *v1.ListPricingRuleFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListPricingRulesParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list pricing rule result
type ListPricingRulesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pricing rule list
	PricingRules []*v1.PricingRuleModel `protobuf:"bytes,1,rep,name=pricing_rules,json=pricingRules,proto3" json:"pricing_rules,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListPricingRulesResult) Reset() {
	*x = ListPricingRulesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRulesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRulesResult) ProtoMessage() {}

func (x *ListPricingRulesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRulesResult.ProtoReflect.Descriptor instead.
func (*ListPricingRulesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{5}
}

func (x *ListPricingRulesResult) GetPricingRules() []*v1.PricingRuleModel {
	if x != nil {
		return x.PricingRules
	}
	return nil
}

func (x *ListPricingRulesResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// calculate pricing rule params
type CalculatePricingRuleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail list
	PetDetails []*v1.PetDetailCalculateDef `protobuf:"bytes,1,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// pricing rule def, if empty, will use the best pricing rule
	PricingRuleUpsertDef *v1.PricingRuleUpsertDef `protobuf:"bytes,2,opt,name=pricing_rule_upsert_def,json=pricingRuleUpsertDef,proto3,oneof" json:"pricing_rule_upsert_def,omitempty"`
	// calculate for preview
	IsPreview bool `protobuf:"varint,3,opt,name=is_preview,json=isPreview,proto3" json:"is_preview,omitempty"`
}

func (x *CalculatePricingRuleParams) Reset() {
	*x = CalculatePricingRuleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculatePricingRuleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculatePricingRuleParams) ProtoMessage() {}

func (x *CalculatePricingRuleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculatePricingRuleParams.ProtoReflect.Descriptor instead.
func (*CalculatePricingRuleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{6}
}

func (x *CalculatePricingRuleParams) GetPetDetails() []*v1.PetDetailCalculateDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *CalculatePricingRuleParams) GetPricingRuleUpsertDef() *v1.PricingRuleUpsertDef {
	if x != nil {
		return x.PricingRuleUpsertDef
	}
	return nil
}

func (x *CalculatePricingRuleParams) GetIsPreview() bool {
	if x != nil {
		return x.IsPreview
	}
	return false
}

// calculate pricing rule result
type CalculatePricingRuleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail list
	PetDetails []*v1.PetDetailCalculateResultDef `protobuf:"bytes,1,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// formula for preview calculation
	Formula *string `protobuf:"bytes,2,opt,name=formula,proto3,oneof" json:"formula,omitempty"`
	// used pricing rule list
	PricingRules []*v1.PricingRuleModel `protobuf:"bytes,3,rep,name=pricing_rules,json=pricingRules,proto3" json:"pricing_rules,omitempty"`
}

func (x *CalculatePricingRuleResult) Reset() {
	*x = CalculatePricingRuleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculatePricingRuleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculatePricingRuleResult) ProtoMessage() {}

func (x *CalculatePricingRuleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculatePricingRuleResult.ProtoReflect.Descriptor instead.
func (*CalculatePricingRuleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{7}
}

func (x *CalculatePricingRuleResult) GetPetDetails() []*v1.PetDetailCalculateResultDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *CalculatePricingRuleResult) GetFormula() string {
	if x != nil && x.Formula != nil {
		return *x.Formula
	}
	return ""
}

func (x *CalculatePricingRuleResult) GetPricingRules() []*v1.PricingRuleModel {
	if x != nil {
		return x.PricingRules
	}
	return nil
}

// delete pricing rule params
type DeletePricingRuleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// apply to upcoming appointments
	ApplyToUpcomingAppointments bool `protobuf:"varint,2,opt,name=apply_to_upcoming_appointments,json=applyToUpcomingAppointments,proto3" json:"apply_to_upcoming_appointments,omitempty"`
}

func (x *DeletePricingRuleParams) Reset() {
	*x = DeletePricingRuleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePricingRuleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePricingRuleParams) ProtoMessage() {}

func (x *DeletePricingRuleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePricingRuleParams.ProtoReflect.Descriptor instead.
func (*DeletePricingRuleParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{8}
}

func (x *DeletePricingRuleParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeletePricingRuleParams) GetApplyToUpcomingAppointments() bool {
	if x != nil {
		return x.ApplyToUpcomingAppointments
	}
	return false
}

// delete pricing rule result
type DeletePricingRuleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePricingRuleResult) Reset() {
	*x = DeletePricingRuleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePricingRuleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePricingRuleResult) ProtoMessage() {}

func (x *DeletePricingRuleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePricingRuleResult.ProtoReflect.Descriptor instead.
func (*DeletePricingRuleResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{9}
}

// get associated services params
type ListAssociatedServicesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item: grooming/boarding/daycare
	ServiceItemType v1.ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// service type: service/addon
	ServiceType v1.ServiceType `protobuf:"varint,2,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_type,omitempty"`
	// filter
	Filter *ListAssociatedServicesParams_Filter `protobuf:"bytes,3,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *ListAssociatedServicesParams) Reset() {
	*x = ListAssociatedServicesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAssociatedServicesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssociatedServicesParams) ProtoMessage() {}

func (x *ListAssociatedServicesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssociatedServicesParams.ProtoReflect.Descriptor instead.
func (*ListAssociatedServicesParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{10}
}

func (x *ListAssociatedServicesParams) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *ListAssociatedServicesParams) GetServiceType() v1.ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return v1.ServiceType(0)
}

func (x *ListAssociatedServicesParams) GetFilter() *ListAssociatedServicesParams_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// get associated services result
type ListAssociatedServicesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// associated service ids
	AssociatedServiceIds []int64 `protobuf:"varint,1,rep,packed,name=associated_service_ids,json=associatedServiceIds,proto3" json:"associated_service_ids,omitempty"`
	// all service associated
	AllServiceAssociated bool `protobuf:"varint,2,opt,name=all_service_associated,json=allServiceAssociated,proto3" json:"all_service_associated,omitempty"`
}

func (x *ListAssociatedServicesResult) Reset() {
	*x = ListAssociatedServicesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAssociatedServicesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssociatedServicesResult) ProtoMessage() {}

func (x *ListAssociatedServicesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssociatedServicesResult.ProtoReflect.Descriptor instead.
func (*ListAssociatedServicesResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{11}
}

func (x *ListAssociatedServicesResult) GetAssociatedServiceIds() []int64 {
	if x != nil {
		return x.AssociatedServiceIds
	}
	return nil
}

func (x *ListAssociatedServicesResult) GetAllServiceAssociated() bool {
	if x != nil {
		return x.AllServiceAssociated
	}
	return false
}

// check rule name is exist params
type CheckRuleNameParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rule name
	RuleName string `protobuf:"bytes,1,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`
	// exclude pricing rule id
	ExcludePricingRuleId *int64 `protobuf:"varint,2,opt,name=exclude_pricing_rule_id,json=excludePricingRuleId,proto3,oneof" json:"exclude_pricing_rule_id,omitempty"`
}

func (x *CheckRuleNameParams) Reset() {
	*x = CheckRuleNameParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckRuleNameParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckRuleNameParams) ProtoMessage() {}

func (x *CheckRuleNameParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckRuleNameParams.ProtoReflect.Descriptor instead.
func (*CheckRuleNameParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{12}
}

func (x *CheckRuleNameParams) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *CheckRuleNameParams) GetExcludePricingRuleId() int64 {
	if x != nil && x.ExcludePricingRuleId != nil {
		return *x.ExcludePricingRuleId
	}
	return 0
}

// check rule name is exist result
type CheckRuleNameResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rule name is exist
	IsExist bool `protobuf:"varint,1,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"`
}

func (x *CheckRuleNameResult) Reset() {
	*x = CheckRuleNameResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckRuleNameResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckRuleNameResult) ProtoMessage() {}

func (x *CheckRuleNameResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckRuleNameResult.ProtoReflect.Descriptor instead.
func (*CheckRuleNameResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{13}
}

func (x *CheckRuleNameResult) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

// check configuration params
type CheckConfigurationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pricing_rule def
	PricingRuleUpsertDef *v1.PricingRuleUpsertDef `protobuf:"bytes,1,opt,name=pricing_rule_upsert_def,json=pricingRuleUpsertDef,proto3" json:"pricing_rule_upsert_def,omitempty"`
}

func (x *CheckConfigurationParams) Reset() {
	*x = CheckConfigurationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckConfigurationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckConfigurationParams) ProtoMessage() {}

func (x *CheckConfigurationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckConfigurationParams.ProtoReflect.Descriptor instead.
func (*CheckConfigurationParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{14}
}

func (x *CheckConfigurationParams) GetPricingRuleUpsertDef() *v1.PricingRuleUpsertDef {
	if x != nil {
		return x.PricingRuleUpsertDef
	}
	return nil
}

// check configuration result
type CheckConfigurationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is valid
	IsValid bool `protobuf:"varint,1,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
	// error message
	ErrorMessage *string `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3,oneof" json:"error_message,omitempty"`
}

func (x *CheckConfigurationResult) Reset() {
	*x = CheckConfigurationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckConfigurationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckConfigurationResult) ProtoMessage() {}

func (x *CheckConfigurationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckConfigurationResult.ProtoReflect.Descriptor instead.
func (*CheckConfigurationResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{15}
}

func (x *CheckConfigurationResult) GetIsValid() bool {
	if x != nil {
		return x.IsValid
	}
	return false
}

func (x *CheckConfigurationResult) GetErrorMessage() string {
	if x != nil && x.ErrorMessage != nil {
		return *x.ErrorMessage
	}
	return ""
}

// filter
type ListAssociatedServicesParams_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// exclude pricing rule id
	ExcludePricingRuleId *int64 `protobuf:"varint,1,opt,name=exclude_pricing_rule_id,json=excludePricingRuleId,proto3,oneof" json:"exclude_pricing_rule_id,omitempty"`
	// rule group type
	RuleGroupType *v1.RuleGroupType `protobuf:"varint,2,opt,name=rule_group_type,json=ruleGroupType,proto3,enum=moego.models.offering.v1.RuleGroupType,oneof" json:"rule_group_type,omitempty"`
}

func (x *ListAssociatedServicesParams_Filter) Reset() {
	*x = ListAssociatedServicesParams_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAssociatedServicesParams_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAssociatedServicesParams_Filter) ProtoMessage() {}

func (x *ListAssociatedServicesParams_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAssociatedServicesParams_Filter.ProtoReflect.Descriptor instead.
func (*ListAssociatedServicesParams_Filter) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP(), []int{10, 0}
}

func (x *ListAssociatedServicesParams_Filter) GetExcludePricingRuleId() int64 {
	if x != nil && x.ExcludePricingRuleId != nil {
		return *x.ExcludePricingRuleId
	}
	return 0
}

func (x *ListAssociatedServicesParams_Filter) GetRuleGroupType() v1.RuleGroupType {
	if x != nil && x.RuleGroupType != nil {
		return *x.RuleGroupType
	}
	return v1.RuleGroupType(0)
}

var File_moego_api_offering_v1_pricing_rule_api_proto protoreflect.FileDescriptor

var file_moego_api_offering_v1_pricing_rule_api_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c,
	0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcf,
	0x01, 0x0a, 0x17, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x6f, 0x0a, 0x17, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x75, 0x70, 0x73, 0x65, 0x72,
	0x74, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x14, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x65, 0x66, 0x12, 0x43, 0x0a, 0x1e, 0x61,
	0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x1b, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x6f, 0x55, 0x70, 0x63, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x22, 0x68, 0x0a, 0x17, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4d, 0x0a, 0x0c, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x22, 0x2f, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x65, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x4d, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72,
	0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x22, 0xb4, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x4c, 0x0a,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x00,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xad, 0x01, 0x0a, 0x16, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x4f, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9f, 0x02, 0x0a, 0x1a, 0x43, 0x61,
	0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x5a, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x6a, 0x0a, 0x17, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x5f, 0x75, 0x70, 0x73, 0x65, 0x72, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x55, 0x70, 0x73, 0x65,
	0x72, 0x74, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x14, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x65, 0x66, 0x88, 0x01, 0x01,
	0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42,
	0x1a, 0x0a, 0x18, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65,
	0x5f, 0x75, 0x70, 0x73, 0x65, 0x72, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x22, 0xf0, 0x01, 0x0a, 0x1a,
	0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x56, 0x0a, 0x0b, 0x70, 0x65,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x75, 0x6c, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x75, 0x6c, 0x61, 0x88, 0x01,
	0x01, 0x12, 0x4f, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c,
	0x65, 0x73, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x75, 0x6c, 0x61, 0x22, 0x77,
	0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x43, 0x0a, 0x1e, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x6f, 0x5f, 0x75,
	0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x61, 0x70, 0x70, 0x6c,
	0x79, 0x54, 0x6f, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x19, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x9d, 0x04, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01,
	0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x54, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x57, 0x0a, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x88, 0x01, 0x01, 0x1a, 0xdf, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x43, 0x0a, 0x17, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x14, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x60, 0x0a, 0x0f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0d, 0x72, 0x75, 0x6c, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x65, 0x78, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x22, 0x8a, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x14, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x6c, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x61, 0x6c, 0x6c, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x22,
	0x9f, 0x01, 0x0a, 0x13, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x27, 0x0a, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72,
	0x05, 0x10, 0x01, 0x18, 0x96, 0x01, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x43, 0x0a, 0x17, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x14, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0x22, 0x30, 0x0a, 0x13, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x65,
	0x78, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x22, 0x8b, 0x01, 0x0a, 0x18, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x6f, 0x0a, 0x17, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65,
	0x5f, 0x75, 0x70, 0x73, 0x65, 0x72, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x65,
	0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x14, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x44, 0x65,
	0x66, 0x22, 0x71, 0x0a, 0x18, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a,
	0x08, 0x69, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x69, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x88,
	0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x32, 0xee, 0x07, 0x0a, 0x12, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x78, 0x0a, 0x11, 0x55,
	0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x6f, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x75, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x2d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x81, 0x01,
	0x0a, 0x14, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x03, 0x88, 0x02,
	0x01, 0x12, 0x78, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x87, 0x01, 0x0a, 0x16,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74,
	0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x6c, 0x0a, 0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x75,
	0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x52, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x03,
	0x88, 0x02, 0x01, 0x12, 0x7b, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x03, 0x88, 0x02, 0x01,
	0x1a, 0x03, 0x88, 0x02, 0x01, 0x42, 0x7b, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_offering_v1_pricing_rule_api_proto_rawDescOnce sync.Once
	file_moego_api_offering_v1_pricing_rule_api_proto_rawDescData = file_moego_api_offering_v1_pricing_rule_api_proto_rawDesc
)

func file_moego_api_offering_v1_pricing_rule_api_proto_rawDescGZIP() []byte {
	file_moego_api_offering_v1_pricing_rule_api_proto_rawDescOnce.Do(func() {
		file_moego_api_offering_v1_pricing_rule_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_offering_v1_pricing_rule_api_proto_rawDescData)
	})
	return file_moego_api_offering_v1_pricing_rule_api_proto_rawDescData
}

var file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_moego_api_offering_v1_pricing_rule_api_proto_goTypes = []interface{}{
	(*UpsertPricingRuleParams)(nil),             // 0: moego.api.offering.v1.UpsertPricingRuleParams
	(*UpsertPricingRuleResult)(nil),             // 1: moego.api.offering.v1.UpsertPricingRuleResult
	(*GetPricingRuleParams)(nil),                // 2: moego.api.offering.v1.GetPricingRuleParams
	(*GetPricingRuleResult)(nil),                // 3: moego.api.offering.v1.GetPricingRuleResult
	(*ListPricingRulesParams)(nil),              // 4: moego.api.offering.v1.ListPricingRulesParams
	(*ListPricingRulesResult)(nil),              // 5: moego.api.offering.v1.ListPricingRulesResult
	(*CalculatePricingRuleParams)(nil),          // 6: moego.api.offering.v1.CalculatePricingRuleParams
	(*CalculatePricingRuleResult)(nil),          // 7: moego.api.offering.v1.CalculatePricingRuleResult
	(*DeletePricingRuleParams)(nil),             // 8: moego.api.offering.v1.DeletePricingRuleParams
	(*DeletePricingRuleResult)(nil),             // 9: moego.api.offering.v1.DeletePricingRuleResult
	(*ListAssociatedServicesParams)(nil),        // 10: moego.api.offering.v1.ListAssociatedServicesParams
	(*ListAssociatedServicesResult)(nil),        // 11: moego.api.offering.v1.ListAssociatedServicesResult
	(*CheckRuleNameParams)(nil),                 // 12: moego.api.offering.v1.CheckRuleNameParams
	(*CheckRuleNameResult)(nil),                 // 13: moego.api.offering.v1.CheckRuleNameResult
	(*CheckConfigurationParams)(nil),            // 14: moego.api.offering.v1.CheckConfigurationParams
	(*CheckConfigurationResult)(nil),            // 15: moego.api.offering.v1.CheckConfigurationResult
	(*ListAssociatedServicesParams_Filter)(nil), // 16: moego.api.offering.v1.ListAssociatedServicesParams.Filter
	(*v1.PricingRuleUpsertDef)(nil),             // 17: moego.models.offering.v1.PricingRuleUpsertDef
	(*v1.PricingRuleModel)(nil),                 // 18: moego.models.offering.v1.PricingRuleModel
	(*v1.ListPricingRuleFilter)(nil),            // 19: moego.models.offering.v1.ListPricingRuleFilter
	(*v2.PaginationRequest)(nil),                // 20: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),               // 21: moego.utils.v2.PaginationResponse
	(*v1.PetDetailCalculateDef)(nil),            // 22: moego.models.offering.v1.PetDetailCalculateDef
	(*v1.PetDetailCalculateResultDef)(nil),      // 23: moego.models.offering.v1.PetDetailCalculateResultDef
	(v1.ServiceItemType)(0),                     // 24: moego.models.offering.v1.ServiceItemType
	(v1.ServiceType)(0),                         // 25: moego.models.offering.v1.ServiceType
	(v1.RuleGroupType)(0),                       // 26: moego.models.offering.v1.RuleGroupType
}
var file_moego_api_offering_v1_pricing_rule_api_proto_depIdxs = []int32{
	17, // 0: moego.api.offering.v1.UpsertPricingRuleParams.pricing_rule_upsert_def:type_name -> moego.models.offering.v1.PricingRuleUpsertDef
	18, // 1: moego.api.offering.v1.UpsertPricingRuleResult.pricing_rule:type_name -> moego.models.offering.v1.PricingRuleModel
	18, // 2: moego.api.offering.v1.GetPricingRuleResult.pricing_rule:type_name -> moego.models.offering.v1.PricingRuleModel
	19, // 3: moego.api.offering.v1.ListPricingRulesParams.filter:type_name -> moego.models.offering.v1.ListPricingRuleFilter
	20, // 4: moego.api.offering.v1.ListPricingRulesParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	18, // 5: moego.api.offering.v1.ListPricingRulesResult.pricing_rules:type_name -> moego.models.offering.v1.PricingRuleModel
	21, // 6: moego.api.offering.v1.ListPricingRulesResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	22, // 7: moego.api.offering.v1.CalculatePricingRuleParams.pet_details:type_name -> moego.models.offering.v1.PetDetailCalculateDef
	17, // 8: moego.api.offering.v1.CalculatePricingRuleParams.pricing_rule_upsert_def:type_name -> moego.models.offering.v1.PricingRuleUpsertDef
	23, // 9: moego.api.offering.v1.CalculatePricingRuleResult.pet_details:type_name -> moego.models.offering.v1.PetDetailCalculateResultDef
	18, // 10: moego.api.offering.v1.CalculatePricingRuleResult.pricing_rules:type_name -> moego.models.offering.v1.PricingRuleModel
	24, // 11: moego.api.offering.v1.ListAssociatedServicesParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	25, // 12: moego.api.offering.v1.ListAssociatedServicesParams.service_type:type_name -> moego.models.offering.v1.ServiceType
	16, // 13: moego.api.offering.v1.ListAssociatedServicesParams.filter:type_name -> moego.api.offering.v1.ListAssociatedServicesParams.Filter
	17, // 14: moego.api.offering.v1.CheckConfigurationParams.pricing_rule_upsert_def:type_name -> moego.models.offering.v1.PricingRuleUpsertDef
	26, // 15: moego.api.offering.v1.ListAssociatedServicesParams.Filter.rule_group_type:type_name -> moego.models.offering.v1.RuleGroupType
	0,  // 16: moego.api.offering.v1.PricingRuleService.UpsertPricingRule:input_type -> moego.api.offering.v1.UpsertPricingRuleParams
	2,  // 17: moego.api.offering.v1.PricingRuleService.GetPricingRule:input_type -> moego.api.offering.v1.GetPricingRuleParams
	4,  // 18: moego.api.offering.v1.PricingRuleService.ListPricingRules:input_type -> moego.api.offering.v1.ListPricingRulesParams
	6,  // 19: moego.api.offering.v1.PricingRuleService.CalculatePricingRule:input_type -> moego.api.offering.v1.CalculatePricingRuleParams
	8,  // 20: moego.api.offering.v1.PricingRuleService.DeletePricingRule:input_type -> moego.api.offering.v1.DeletePricingRuleParams
	10, // 21: moego.api.offering.v1.PricingRuleService.ListAssociatedServices:input_type -> moego.api.offering.v1.ListAssociatedServicesParams
	12, // 22: moego.api.offering.v1.PricingRuleService.CheckRuleName:input_type -> moego.api.offering.v1.CheckRuleNameParams
	14, // 23: moego.api.offering.v1.PricingRuleService.CheckConfiguration:input_type -> moego.api.offering.v1.CheckConfigurationParams
	1,  // 24: moego.api.offering.v1.PricingRuleService.UpsertPricingRule:output_type -> moego.api.offering.v1.UpsertPricingRuleResult
	3,  // 25: moego.api.offering.v1.PricingRuleService.GetPricingRule:output_type -> moego.api.offering.v1.GetPricingRuleResult
	5,  // 26: moego.api.offering.v1.PricingRuleService.ListPricingRules:output_type -> moego.api.offering.v1.ListPricingRulesResult
	7,  // 27: moego.api.offering.v1.PricingRuleService.CalculatePricingRule:output_type -> moego.api.offering.v1.CalculatePricingRuleResult
	9,  // 28: moego.api.offering.v1.PricingRuleService.DeletePricingRule:output_type -> moego.api.offering.v1.DeletePricingRuleResult
	11, // 29: moego.api.offering.v1.PricingRuleService.ListAssociatedServices:output_type -> moego.api.offering.v1.ListAssociatedServicesResult
	13, // 30: moego.api.offering.v1.PricingRuleService.CheckRuleName:output_type -> moego.api.offering.v1.CheckRuleNameResult
	15, // 31: moego.api.offering.v1.PricingRuleService.CheckConfiguration:output_type -> moego.api.offering.v1.CheckConfigurationResult
	24, // [24:32] is the sub-list for method output_type
	16, // [16:24] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_moego_api_offering_v1_pricing_rule_api_proto_init() }
func file_moego_api_offering_v1_pricing_rule_api_proto_init() {
	if File_moego_api_offering_v1_pricing_rule_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertPricingRuleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertPricingRuleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPricingRuleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPricingRuleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRulesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRulesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculatePricingRuleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculatePricingRuleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePricingRuleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePricingRuleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAssociatedServicesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAssociatedServicesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckRuleNameParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckRuleNameResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckConfigurationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckConfigurationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAssociatedServicesParams_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[15].OneofWrappers = []interface{}{}
	file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes[16].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_offering_v1_pricing_rule_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_offering_v1_pricing_rule_api_proto_goTypes,
		DependencyIndexes: file_moego_api_offering_v1_pricing_rule_api_proto_depIdxs,
		MessageInfos:      file_moego_api_offering_v1_pricing_rule_api_proto_msgTypes,
	}.Build()
	File_moego_api_offering_v1_pricing_rule_api_proto = out.File
	file_moego_api_offering_v1_pricing_rule_api_proto_rawDesc = nil
	file_moego_api_offering_v1_pricing_rule_api_proto_goTypes = nil
	file_moego_api_offering_v1_pricing_rule_api_proto_depIdxs = nil
}
