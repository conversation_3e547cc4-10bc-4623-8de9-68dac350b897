// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/offering/v1/playgroup_api.proto

package offeringapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list playgroup params
type ListPlaygroupParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListPlaygroupParams) Reset() {
	*x = ListPlaygroupParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPlaygroupParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlaygroupParams) ProtoMessage() {}

func (x *ListPlaygroupParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlaygroupParams.ProtoReflect.Descriptor instead.
func (*ListPlaygroupParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_playgroup_api_proto_rawDescGZIP(), []int{0}
}

func (x *ListPlaygroupParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list playgroup result
type ListPlaygroupResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// playgroups
	Playgroups []*v1.PlaygroupModel `protobuf:"bytes,1,rep,name=playgroups,proto3" json:"playgroups,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListPlaygroupResult) Reset() {
	*x = ListPlaygroupResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPlaygroupResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlaygroupResult) ProtoMessage() {}

func (x *ListPlaygroupResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlaygroupResult.ProtoReflect.Descriptor instead.
func (*ListPlaygroupResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_playgroup_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListPlaygroupResult) GetPlaygroups() []*v1.PlaygroupModel {
	if x != nil {
		return x.Playgroups
	}
	return nil
}

func (x *ListPlaygroupResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// create playgroup params
type CreatePlaygroupParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// playgroup def
	Playgroup *v1.CreatePlaygroupDef `protobuf:"bytes,1,opt,name=playgroup,proto3" json:"playgroup,omitempty"`
}

func (x *CreatePlaygroupParams) Reset() {
	*x = CreatePlaygroupParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePlaygroupParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePlaygroupParams) ProtoMessage() {}

func (x *CreatePlaygroupParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePlaygroupParams.ProtoReflect.Descriptor instead.
func (*CreatePlaygroupParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_playgroup_api_proto_rawDescGZIP(), []int{2}
}

func (x *CreatePlaygroupParams) GetPlaygroup() *v1.CreatePlaygroupDef {
	if x != nil {
		return x.Playgroup
	}
	return nil
}

// create playgroup result
type CreatePlaygroupResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// playgroup
	Playgroup *v1.PlaygroupModel `protobuf:"bytes,1,opt,name=playgroup,proto3" json:"playgroup,omitempty"`
}

func (x *CreatePlaygroupResult) Reset() {
	*x = CreatePlaygroupResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePlaygroupResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePlaygroupResult) ProtoMessage() {}

func (x *CreatePlaygroupResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePlaygroupResult.ProtoReflect.Descriptor instead.
func (*CreatePlaygroupResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_playgroup_api_proto_rawDescGZIP(), []int{3}
}

func (x *CreatePlaygroupResult) GetPlaygroup() *v1.PlaygroupModel {
	if x != nil {
		return x.Playgroup
	}
	return nil
}

// update playgroup params
type UpdatePlaygroupParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// playgroup def
	Playgroup *v1.UpdatePlaygroupDef `protobuf:"bytes,1,opt,name=playgroup,proto3" json:"playgroup,omitempty"`
}

func (x *UpdatePlaygroupParams) Reset() {
	*x = UpdatePlaygroupParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePlaygroupParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePlaygroupParams) ProtoMessage() {}

func (x *UpdatePlaygroupParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePlaygroupParams.ProtoReflect.Descriptor instead.
func (*UpdatePlaygroupParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_playgroup_api_proto_rawDescGZIP(), []int{4}
}

func (x *UpdatePlaygroupParams) GetPlaygroup() *v1.UpdatePlaygroupDef {
	if x != nil {
		return x.Playgroup
	}
	return nil
}

// update playgroup result
type UpdatePlaygroupResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// playgroup
	Playgroup *v1.PlaygroupModel `protobuf:"bytes,1,opt,name=playgroup,proto3" json:"playgroup,omitempty"`
}

func (x *UpdatePlaygroupResult) Reset() {
	*x = UpdatePlaygroupResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePlaygroupResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePlaygroupResult) ProtoMessage() {}

func (x *UpdatePlaygroupResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePlaygroupResult.ProtoReflect.Descriptor instead.
func (*UpdatePlaygroupResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_playgroup_api_proto_rawDescGZIP(), []int{5}
}

func (x *UpdatePlaygroupResult) GetPlaygroup() *v1.PlaygroupModel {
	if x != nil {
		return x.Playgroup
	}
	return nil
}

// delete playgroup params
type DeletePlaygroupParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// playgroup id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletePlaygroupParams) Reset() {
	*x = DeletePlaygroupParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePlaygroupParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePlaygroupParams) ProtoMessage() {}

func (x *DeletePlaygroupParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePlaygroupParams.ProtoReflect.Descriptor instead.
func (*DeletePlaygroupParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_playgroup_api_proto_rawDescGZIP(), []int{6}
}

func (x *DeletePlaygroupParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete playgroup result
type DeletePlaygroupResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePlaygroupResult) Reset() {
	*x = DeletePlaygroupResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePlaygroupResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePlaygroupResult) ProtoMessage() {}

func (x *DeletePlaygroupResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePlaygroupResult.ProtoReflect.Descriptor instead.
func (*DeletePlaygroupResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_playgroup_api_proto_rawDescGZIP(), []int{7}
}

// sort playgroup params
type SortPlaygroupParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// playgroup id list, should contain all playgroup ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortPlaygroupParams) Reset() {
	*x = SortPlaygroupParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPlaygroupParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPlaygroupParams) ProtoMessage() {}

func (x *SortPlaygroupParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPlaygroupParams.ProtoReflect.Descriptor instead.
func (*SortPlaygroupParams) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_playgroup_api_proto_rawDescGZIP(), []int{8}
}

func (x *SortPlaygroupParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// sort playgroup result
type SortPlaygroupResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortPlaygroupResult) Reset() {
	*x = SortPlaygroupResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPlaygroupResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPlaygroupResult) ProtoMessage() {}

func (x *SortPlaygroupResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_offering_v1_playgroup_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPlaygroupResult.ProtoReflect.Descriptor instead.
func (*SortPlaygroupResult) Descriptor() ([]byte, []int) {
	return file_moego_api_offering_v1_playgroup_api_proto_rawDescGZIP(), []int{9}
}

var File_moego_api_offering_v1_playgroup_api_proto protoreflect.FileDescriptor

var file_moego_api_offering_v1_playgroup_api_proto_rawDesc = []byte{
	0x0a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61,
	0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x79,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f,
	0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x58, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x61,
	0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x41, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0xa3, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x48, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x63, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x4a,
	0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x66, 0x52,
	0x09, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x22, 0x5f, 0x0a, 0x15, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x22, 0x63, 0x0a, 0x15, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x4a, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x44, 0x65, 0x66, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x22, 0x5f, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a, 0x09, 0x70, 0x6c, 0x61,
	0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x22, 0x30, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x17, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6c, 0x61,
	0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x39, 0x0a, 0x13,
	0x53, 0x6f, 0x72, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x22, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x08, 0x01, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x15, 0x0a, 0x13, 0x53, 0x6f, 0x72, 0x74, 0x50,
	0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xb1,
	0x04, 0x0a, 0x10, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x67, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x61,
	0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6d, 0x0a, 0x0f,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6c,
	0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6d, 0x0a, 0x0f, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x2c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61,
	0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6d, 0x0a, 0x0f, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x2c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x67, 0x0a, 0x0d, 0x53, 0x6f, 0x72,
	0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x6f, 0x72, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x42, 0x7b, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_offering_v1_playgroup_api_proto_rawDescOnce sync.Once
	file_moego_api_offering_v1_playgroup_api_proto_rawDescData = file_moego_api_offering_v1_playgroup_api_proto_rawDesc
)

func file_moego_api_offering_v1_playgroup_api_proto_rawDescGZIP() []byte {
	file_moego_api_offering_v1_playgroup_api_proto_rawDescOnce.Do(func() {
		file_moego_api_offering_v1_playgroup_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_offering_v1_playgroup_api_proto_rawDescData)
	})
	return file_moego_api_offering_v1_playgroup_api_proto_rawDescData
}

var file_moego_api_offering_v1_playgroup_api_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_api_offering_v1_playgroup_api_proto_goTypes = []interface{}{
	(*ListPlaygroupParams)(nil),   // 0: moego.api.offering.v1.ListPlaygroupParams
	(*ListPlaygroupResult)(nil),   // 1: moego.api.offering.v1.ListPlaygroupResult
	(*CreatePlaygroupParams)(nil), // 2: moego.api.offering.v1.CreatePlaygroupParams
	(*CreatePlaygroupResult)(nil), // 3: moego.api.offering.v1.CreatePlaygroupResult
	(*UpdatePlaygroupParams)(nil), // 4: moego.api.offering.v1.UpdatePlaygroupParams
	(*UpdatePlaygroupResult)(nil), // 5: moego.api.offering.v1.UpdatePlaygroupResult
	(*DeletePlaygroupParams)(nil), // 6: moego.api.offering.v1.DeletePlaygroupParams
	(*DeletePlaygroupResult)(nil), // 7: moego.api.offering.v1.DeletePlaygroupResult
	(*SortPlaygroupParams)(nil),   // 8: moego.api.offering.v1.SortPlaygroupParams
	(*SortPlaygroupResult)(nil),   // 9: moego.api.offering.v1.SortPlaygroupResult
	(*v2.PaginationRequest)(nil),  // 10: moego.utils.v2.PaginationRequest
	(*v1.PlaygroupModel)(nil),     // 11: moego.models.offering.v1.PlaygroupModel
	(*v2.PaginationResponse)(nil), // 12: moego.utils.v2.PaginationResponse
	(*v1.CreatePlaygroupDef)(nil), // 13: moego.models.offering.v1.CreatePlaygroupDef
	(*v1.UpdatePlaygroupDef)(nil), // 14: moego.models.offering.v1.UpdatePlaygroupDef
}
var file_moego_api_offering_v1_playgroup_api_proto_depIdxs = []int32{
	10, // 0: moego.api.offering.v1.ListPlaygroupParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	11, // 1: moego.api.offering.v1.ListPlaygroupResult.playgroups:type_name -> moego.models.offering.v1.PlaygroupModel
	12, // 2: moego.api.offering.v1.ListPlaygroupResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	13, // 3: moego.api.offering.v1.CreatePlaygroupParams.playgroup:type_name -> moego.models.offering.v1.CreatePlaygroupDef
	11, // 4: moego.api.offering.v1.CreatePlaygroupResult.playgroup:type_name -> moego.models.offering.v1.PlaygroupModel
	14, // 5: moego.api.offering.v1.UpdatePlaygroupParams.playgroup:type_name -> moego.models.offering.v1.UpdatePlaygroupDef
	11, // 6: moego.api.offering.v1.UpdatePlaygroupResult.playgroup:type_name -> moego.models.offering.v1.PlaygroupModel
	0,  // 7: moego.api.offering.v1.PlaygroupService.ListPlaygroup:input_type -> moego.api.offering.v1.ListPlaygroupParams
	2,  // 8: moego.api.offering.v1.PlaygroupService.CreatePlaygroup:input_type -> moego.api.offering.v1.CreatePlaygroupParams
	4,  // 9: moego.api.offering.v1.PlaygroupService.UpdatePlaygroup:input_type -> moego.api.offering.v1.UpdatePlaygroupParams
	6,  // 10: moego.api.offering.v1.PlaygroupService.DeletePlaygroup:input_type -> moego.api.offering.v1.DeletePlaygroupParams
	8,  // 11: moego.api.offering.v1.PlaygroupService.SortPlaygroup:input_type -> moego.api.offering.v1.SortPlaygroupParams
	1,  // 12: moego.api.offering.v1.PlaygroupService.ListPlaygroup:output_type -> moego.api.offering.v1.ListPlaygroupResult
	3,  // 13: moego.api.offering.v1.PlaygroupService.CreatePlaygroup:output_type -> moego.api.offering.v1.CreatePlaygroupResult
	5,  // 14: moego.api.offering.v1.PlaygroupService.UpdatePlaygroup:output_type -> moego.api.offering.v1.UpdatePlaygroupResult
	7,  // 15: moego.api.offering.v1.PlaygroupService.DeletePlaygroup:output_type -> moego.api.offering.v1.DeletePlaygroupResult
	9,  // 16: moego.api.offering.v1.PlaygroupService.SortPlaygroup:output_type -> moego.api.offering.v1.SortPlaygroupResult
	12, // [12:17] is the sub-list for method output_type
	7,  // [7:12] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_moego_api_offering_v1_playgroup_api_proto_init() }
func file_moego_api_offering_v1_playgroup_api_proto_init() {
	if File_moego_api_offering_v1_playgroup_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_offering_v1_playgroup_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPlaygroupParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_playgroup_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPlaygroupResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_playgroup_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePlaygroupParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_playgroup_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePlaygroupResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_playgroup_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePlaygroupParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_playgroup_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePlaygroupResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_playgroup_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePlaygroupParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_playgroup_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePlaygroupResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_playgroup_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPlaygroupParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_offering_v1_playgroup_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPlaygroupResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_offering_v1_playgroup_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_offering_v1_playgroup_api_proto_goTypes,
		DependencyIndexes: file_moego_api_offering_v1_playgroup_api_proto_depIdxs,
		MessageInfos:      file_moego_api_offering_v1_playgroup_api_proto_msgTypes,
	}.Build()
	File_moego_api_offering_v1_playgroup_api_proto = out.File
	file_moego_api_offering_v1_playgroup_api_proto_rawDesc = nil
	file_moego_api_offering_v1_playgroup_api_proto_goTypes = nil
	file_moego_api_offering_v1_playgroup_api_proto_depIdxs = nil
}
