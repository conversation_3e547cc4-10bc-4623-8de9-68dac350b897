// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/fulfillment/v1/fulfillment_api.proto

package fulfillmentapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The prams for enrolling a pet into a group class instance
type EnrollPetParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The business ID
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The training group class instance ID
	InstanceId int64 `protobuf:"varint,2,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
	// The pet's ID
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// The fulfillment source
	Source v1.Source `protobuf:"varint,4,opt,name=source,proto3,enum=moego.models.fulfillment.v1.Source" json:"source,omitempty"`
}

func (x *EnrollPetParams) Reset() {
	*x = EnrollPetParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnrollPetParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnrollPetParams) ProtoMessage() {}

func (x *EnrollPetParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnrollPetParams.ProtoReflect.Descriptor instead.
func (*EnrollPetParams) Descriptor() ([]byte, []int) {
	return file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDescGZIP(), []int{0}
}

func (x *EnrollPetParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *EnrollPetParams) GetInstanceId() int64 {
	if x != nil {
		return x.InstanceId
	}
	return 0
}

func (x *EnrollPetParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *EnrollPetParams) GetSource() v1.Source {
	if x != nil {
		return x.Source
	}
	return v1.Source(0)
}

// The result of enrolling a pet into a group class instance
type EnrollPetResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The order ID
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *EnrollPetResult) Reset() {
	*x = EnrollPetResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnrollPetResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnrollPetResult) ProtoMessage() {}

func (x *EnrollPetResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnrollPetResult.ProtoReflect.Descriptor instead.
func (*EnrollPetResult) Descriptor() ([]byte, []int) {
	return file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDescGZIP(), []int{1}
}

func (x *EnrollPetResult) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

// The params for checking in a group class session
type CheckInGroupClassSessionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The business ID
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The pet's ID
	PetIds []int64 `protobuf:"varint,2,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// The group class session ID
	GroupClassSessionId *int64 `protobuf:"varint,3,opt,name=group_class_session_id,json=groupClassSessionId,proto3,oneof" json:"group_class_session_id,omitempty"`
}

func (x *CheckInGroupClassSessionParams) Reset() {
	*x = CheckInGroupClassSessionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckInGroupClassSessionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckInGroupClassSessionParams) ProtoMessage() {}

func (x *CheckInGroupClassSessionParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckInGroupClassSessionParams.ProtoReflect.Descriptor instead.
func (*CheckInGroupClassSessionParams) Descriptor() ([]byte, []int) {
	return file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDescGZIP(), []int{2}
}

func (x *CheckInGroupClassSessionParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CheckInGroupClassSessionParams) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *CheckInGroupClassSessionParams) GetGroupClassSessionId() int64 {
	if x != nil && x.GroupClassSessionId != nil {
		return *x.GroupClassSessionId
	}
	return 0
}

// The result of checking in a group class session
type CheckInGroupClassSessionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Check in count
	CheckInCount int32 `protobuf:"varint,1,opt,name=check_in_count,json=checkInCount,proto3" json:"check_in_count,omitempty"`
	// Checked in group class instance ID
	CheckedInInstanceIds []int64 `protobuf:"varint,2,rep,packed,name=checked_in_instance_ids,json=checkedInInstanceIds,proto3" json:"checked_in_instance_ids,omitempty"`
}

func (x *CheckInGroupClassSessionResult) Reset() {
	*x = CheckInGroupClassSessionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckInGroupClassSessionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckInGroupClassSessionResult) ProtoMessage() {}

func (x *CheckInGroupClassSessionResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckInGroupClassSessionResult.ProtoReflect.Descriptor instead.
func (*CheckInGroupClassSessionResult) Descriptor() ([]byte, []int) {
	return file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDescGZIP(), []int{3}
}

func (x *CheckInGroupClassSessionResult) GetCheckInCount() int32 {
	if x != nil {
		return x.CheckInCount
	}
	return 0
}

func (x *CheckInGroupClassSessionResult) GetCheckedInInstanceIds() []int64 {
	if x != nil {
		return x.CheckedInInstanceIds
	}
	return nil
}

// The params of preview order line items
type PreviewOrderLineItemsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The fulfillment ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *PreviewOrderLineItemsParams) Reset() {
	*x = PreviewOrderLineItemsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewOrderLineItemsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewOrderLineItemsParams) ProtoMessage() {}

func (x *PreviewOrderLineItemsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewOrderLineItemsParams.ProtoReflect.Descriptor instead.
func (*PreviewOrderLineItemsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDescGZIP(), []int{4}
}

func (x *PreviewOrderLineItemsParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// The result of preview order line items
type PreviewOrderLineItemsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The pet service details with line item
	PetServices []*v1.PetService `protobuf:"bytes,1,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
	// The surcharge details
	Surcharges []*v1.SurchargeItem `protobuf:"bytes,2,rep,name=surcharges,proto3" json:"surcharges,omitempty"`
}

func (x *PreviewOrderLineItemsResult) Reset() {
	*x = PreviewOrderLineItemsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewOrderLineItemsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewOrderLineItemsResult) ProtoMessage() {}

func (x *PreviewOrderLineItemsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewOrderLineItemsResult.ProtoReflect.Descriptor instead.
func (*PreviewOrderLineItemsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDescGZIP(), []int{5}
}

func (x *PreviewOrderLineItemsResult) GetPetServices() []*v1.PetService {
	if x != nil {
		return x.PetServices
	}
	return nil
}

func (x *PreviewOrderLineItemsResult) GetSurcharges() []*v1.SurchargeItem {
	if x != nil {
		return x.Surcharges
	}
	return nil
}

var File_moego_api_fulfillment_v1_fulfillment_api_proto protoreflect.FileDescriptor

var file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x75, 0x6c, 0x66,
	0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x75, 0x6c, 0x66, 0x69,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x75, 0x6c, 0x66,
	0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x75, 0x6c,
	0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x75, 0x6c, 0x66,
	0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xce, 0x01, 0x0a,
	0x0f, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70,
	0x65, 0x74, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01,
	0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x2c, 0x0a,
	0x0f, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0xd5, 0x01, 0x0a, 0x1e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x12, 0xfa, 0x42, 0x0f, 0x92, 0x01,
	0x0c, 0x08, 0x01, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x70,
	0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x41, 0x0a, 0x16, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00,
	0x52, 0x13, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x22, 0x7d, 0x0a, 0x1e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x69,
	0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x17, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x14, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49,
	0x64, 0x73, 0x22, 0x36, 0x0a, 0x1b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0xb5, 0x01, 0x0a, 0x1b, 0x50,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4a, 0x0a, 0x0c, 0x70, 0x65,
	0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x4a, 0x0a, 0x0a, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x32, 0x90, 0x03, 0x0a, 0x12, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x61, 0x0a, 0x09, 0x45, 0x6e, 0x72,
	0x6f, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x6e, 0x72, 0x6f, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x75,
	0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x72,
	0x6f, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8e, 0x01, 0x0a,
	0x18, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01,
	0x0a, 0x15, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69,
	0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c,
	0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x84, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x75, 0x6c, 0x66,
	0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x75, 0x6c,
	0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x66, 0x75, 0x6c, 0x66,
	0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDescOnce sync.Once
	file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDescData = file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDesc
)

func file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDescGZIP() []byte {
	file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDescOnce.Do(func() {
		file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDescData)
	})
	return file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDescData
}

var file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_moego_api_fulfillment_v1_fulfillment_api_proto_goTypes = []interface{}{
	(*EnrollPetParams)(nil),                // 0: moego.api.fulfillment.v1.EnrollPetParams
	(*EnrollPetResult)(nil),                // 1: moego.api.fulfillment.v1.EnrollPetResult
	(*CheckInGroupClassSessionParams)(nil), // 2: moego.api.fulfillment.v1.CheckInGroupClassSessionParams
	(*CheckInGroupClassSessionResult)(nil), // 3: moego.api.fulfillment.v1.CheckInGroupClassSessionResult
	(*PreviewOrderLineItemsParams)(nil),    // 4: moego.api.fulfillment.v1.PreviewOrderLineItemsParams
	(*PreviewOrderLineItemsResult)(nil),    // 5: moego.api.fulfillment.v1.PreviewOrderLineItemsResult
	(v1.Source)(0),                         // 6: moego.models.fulfillment.v1.Source
	(*v1.PetService)(nil),                  // 7: moego.models.fulfillment.v1.PetService
	(*v1.SurchargeItem)(nil),               // 8: moego.models.fulfillment.v1.SurchargeItem
}
var file_moego_api_fulfillment_v1_fulfillment_api_proto_depIdxs = []int32{
	6, // 0: moego.api.fulfillment.v1.EnrollPetParams.source:type_name -> moego.models.fulfillment.v1.Source
	7, // 1: moego.api.fulfillment.v1.PreviewOrderLineItemsResult.pet_services:type_name -> moego.models.fulfillment.v1.PetService
	8, // 2: moego.api.fulfillment.v1.PreviewOrderLineItemsResult.surcharges:type_name -> moego.models.fulfillment.v1.SurchargeItem
	0, // 3: moego.api.fulfillment.v1.FulfillmentService.EnrollPet:input_type -> moego.api.fulfillment.v1.EnrollPetParams
	2, // 4: moego.api.fulfillment.v1.FulfillmentService.CheckInGroupClassSession:input_type -> moego.api.fulfillment.v1.CheckInGroupClassSessionParams
	4, // 5: moego.api.fulfillment.v1.FulfillmentService.PreviewOrderLineItems:input_type -> moego.api.fulfillment.v1.PreviewOrderLineItemsParams
	1, // 6: moego.api.fulfillment.v1.FulfillmentService.EnrollPet:output_type -> moego.api.fulfillment.v1.EnrollPetResult
	3, // 7: moego.api.fulfillment.v1.FulfillmentService.CheckInGroupClassSession:output_type -> moego.api.fulfillment.v1.CheckInGroupClassSessionResult
	5, // 8: moego.api.fulfillment.v1.FulfillmentService.PreviewOrderLineItems:output_type -> moego.api.fulfillment.v1.PreviewOrderLineItemsResult
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_api_fulfillment_v1_fulfillment_api_proto_init() }
func file_moego_api_fulfillment_v1_fulfillment_api_proto_init() {
	if File_moego_api_fulfillment_v1_fulfillment_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnrollPetParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnrollPetResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckInGroupClassSessionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckInGroupClassSessionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewOrderLineItemsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewOrderLineItemsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_fulfillment_v1_fulfillment_api_proto_goTypes,
		DependencyIndexes: file_moego_api_fulfillment_v1_fulfillment_api_proto_depIdxs,
		MessageInfos:      file_moego_api_fulfillment_v1_fulfillment_api_proto_msgTypes,
	}.Build()
	File_moego_api_fulfillment_v1_fulfillment_api_proto = out.File
	file_moego_api_fulfillment_v1_fulfillment_api_proto_rawDesc = nil
	file_moego_api_fulfillment_v1_fulfillment_api_proto_goTypes = nil
	file_moego_api_fulfillment_v1_fulfillment_api_proto_depIdxs = nil
}
