// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/membership/v1/subscription_api.proto

package membershipapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SubscriptionServiceClient is the client API for SubscriptionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SubscriptionServiceClient interface {
	// get sell link information
	GetSellLinkPublicInfo(ctx context.Context, in *GetSellLinkPublicInfoParams, opts ...grpc.CallOption) (*GetSellLinkPublicInfoResult, error)
	// buy membership
	BuyMembership(ctx context.Context, in *BuyMembershipParams, opts ...grpc.CallOption) (*BuyMembershipResult, error)
	// get subscription by sell link
	GetBuyResult(ctx context.Context, in *GetBuyResultParams, opts ...grpc.CallOption) (*GetBuyResultResult, error)
	// list subscriptions
	ListSubscriptions(ctx context.Context, in *ListSubscriptionsParams, opts ...grpc.CallOption) (*ListSubscriptionsResult, error)
	// list subscriptions for app
	ListSubscriptionsForApp(ctx context.Context, in *ListSubscriptionsForAppParams, opts ...grpc.CallOption) (*ListSubscriptionsForAppResult, error)
	// list payment history
	ListPaymentHistory(ctx context.Context, in *ListPaymentHistoryParams, opts ...grpc.CallOption) (*ListPaymentHistoryResult, error)
	// Create OB Request setting
	CreateOBRequestSetting(ctx context.Context, in *CreateOBRequestSettingParams, opts ...grpc.CallOption) (*CreateOBRequestSettingResult, error)
	// Get OB Request setting
	GetOBRequestSetting(ctx context.Context, in *GetOBRequestSettingParams, opts ...grpc.CallOption) (*GetOBRequestSettingResult, error)
}

type subscriptionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSubscriptionServiceClient(cc grpc.ClientConnInterface) SubscriptionServiceClient {
	return &subscriptionServiceClient{cc}
}

func (c *subscriptionServiceClient) GetSellLinkPublicInfo(ctx context.Context, in *GetSellLinkPublicInfoParams, opts ...grpc.CallOption) (*GetSellLinkPublicInfoResult, error) {
	out := new(GetSellLinkPublicInfoResult)
	err := c.cc.Invoke(ctx, "/moego.client.membership.v1.SubscriptionService/GetSellLinkPublicInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) BuyMembership(ctx context.Context, in *BuyMembershipParams, opts ...grpc.CallOption) (*BuyMembershipResult, error) {
	out := new(BuyMembershipResult)
	err := c.cc.Invoke(ctx, "/moego.client.membership.v1.SubscriptionService/BuyMembership", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) GetBuyResult(ctx context.Context, in *GetBuyResultParams, opts ...grpc.CallOption) (*GetBuyResultResult, error) {
	out := new(GetBuyResultResult)
	err := c.cc.Invoke(ctx, "/moego.client.membership.v1.SubscriptionService/GetBuyResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListSubscriptions(ctx context.Context, in *ListSubscriptionsParams, opts ...grpc.CallOption) (*ListSubscriptionsResult, error) {
	out := new(ListSubscriptionsResult)
	err := c.cc.Invoke(ctx, "/moego.client.membership.v1.SubscriptionService/ListSubscriptions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListSubscriptionsForApp(ctx context.Context, in *ListSubscriptionsForAppParams, opts ...grpc.CallOption) (*ListSubscriptionsForAppResult, error) {
	out := new(ListSubscriptionsForAppResult)
	err := c.cc.Invoke(ctx, "/moego.client.membership.v1.SubscriptionService/ListSubscriptionsForApp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) ListPaymentHistory(ctx context.Context, in *ListPaymentHistoryParams, opts ...grpc.CallOption) (*ListPaymentHistoryResult, error) {
	out := new(ListPaymentHistoryResult)
	err := c.cc.Invoke(ctx, "/moego.client.membership.v1.SubscriptionService/ListPaymentHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) CreateOBRequestSetting(ctx context.Context, in *CreateOBRequestSettingParams, opts ...grpc.CallOption) (*CreateOBRequestSettingResult, error) {
	out := new(CreateOBRequestSettingResult)
	err := c.cc.Invoke(ctx, "/moego.client.membership.v1.SubscriptionService/CreateOBRequestSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) GetOBRequestSetting(ctx context.Context, in *GetOBRequestSettingParams, opts ...grpc.CallOption) (*GetOBRequestSettingResult, error) {
	out := new(GetOBRequestSettingResult)
	err := c.cc.Invoke(ctx, "/moego.client.membership.v1.SubscriptionService/GetOBRequestSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SubscriptionServiceServer is the server API for SubscriptionService service.
// All implementations must embed UnimplementedSubscriptionServiceServer
// for forward compatibility
type SubscriptionServiceServer interface {
	// get sell link information
	GetSellLinkPublicInfo(context.Context, *GetSellLinkPublicInfoParams) (*GetSellLinkPublicInfoResult, error)
	// buy membership
	BuyMembership(context.Context, *BuyMembershipParams) (*BuyMembershipResult, error)
	// get subscription by sell link
	GetBuyResult(context.Context, *GetBuyResultParams) (*GetBuyResultResult, error)
	// list subscriptions
	ListSubscriptions(context.Context, *ListSubscriptionsParams) (*ListSubscriptionsResult, error)
	// list subscriptions for app
	ListSubscriptionsForApp(context.Context, *ListSubscriptionsForAppParams) (*ListSubscriptionsForAppResult, error)
	// list payment history
	ListPaymentHistory(context.Context, *ListPaymentHistoryParams) (*ListPaymentHistoryResult, error)
	// Create OB Request setting
	CreateOBRequestSetting(context.Context, *CreateOBRequestSettingParams) (*CreateOBRequestSettingResult, error)
	// Get OB Request setting
	GetOBRequestSetting(context.Context, *GetOBRequestSettingParams) (*GetOBRequestSettingResult, error)
	mustEmbedUnimplementedSubscriptionServiceServer()
}

// UnimplementedSubscriptionServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSubscriptionServiceServer struct {
}

func (UnimplementedSubscriptionServiceServer) GetSellLinkPublicInfo(context.Context, *GetSellLinkPublicInfoParams) (*GetSellLinkPublicInfoResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSellLinkPublicInfo not implemented")
}
func (UnimplementedSubscriptionServiceServer) BuyMembership(context.Context, *BuyMembershipParams) (*BuyMembershipResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuyMembership not implemented")
}
func (UnimplementedSubscriptionServiceServer) GetBuyResult(context.Context, *GetBuyResultParams) (*GetBuyResultResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBuyResult not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListSubscriptions(context.Context, *ListSubscriptionsParams) (*ListSubscriptionsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSubscriptions not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListSubscriptionsForApp(context.Context, *ListSubscriptionsForAppParams) (*ListSubscriptionsForAppResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSubscriptionsForApp not implemented")
}
func (UnimplementedSubscriptionServiceServer) ListPaymentHistory(context.Context, *ListPaymentHistoryParams) (*ListPaymentHistoryResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPaymentHistory not implemented")
}
func (UnimplementedSubscriptionServiceServer) CreateOBRequestSetting(context.Context, *CreateOBRequestSettingParams) (*CreateOBRequestSettingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOBRequestSetting not implemented")
}
func (UnimplementedSubscriptionServiceServer) GetOBRequestSetting(context.Context, *GetOBRequestSettingParams) (*GetOBRequestSettingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOBRequestSetting not implemented")
}
func (UnimplementedSubscriptionServiceServer) mustEmbedUnimplementedSubscriptionServiceServer() {}

// UnsafeSubscriptionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SubscriptionServiceServer will
// result in compilation errors.
type UnsafeSubscriptionServiceServer interface {
	mustEmbedUnimplementedSubscriptionServiceServer()
}

func RegisterSubscriptionServiceServer(s grpc.ServiceRegistrar, srv SubscriptionServiceServer) {
	s.RegisterService(&SubscriptionService_ServiceDesc, srv)
}

func _SubscriptionService_GetSellLinkPublicInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSellLinkPublicInfoParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).GetSellLinkPublicInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.membership.v1.SubscriptionService/GetSellLinkPublicInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).GetSellLinkPublicInfo(ctx, req.(*GetSellLinkPublicInfoParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_BuyMembership_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuyMembershipParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).BuyMembership(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.membership.v1.SubscriptionService/BuyMembership",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).BuyMembership(ctx, req.(*BuyMembershipParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_GetBuyResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBuyResultParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).GetBuyResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.membership.v1.SubscriptionService/GetBuyResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).GetBuyResult(ctx, req.(*GetBuyResultParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListSubscriptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSubscriptionsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListSubscriptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.membership.v1.SubscriptionService/ListSubscriptions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListSubscriptions(ctx, req.(*ListSubscriptionsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListSubscriptionsForApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSubscriptionsForAppParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListSubscriptionsForApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.membership.v1.SubscriptionService/ListSubscriptionsForApp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListSubscriptionsForApp(ctx, req.(*ListSubscriptionsForAppParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_ListPaymentHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPaymentHistoryParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListPaymentHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.membership.v1.SubscriptionService/ListPaymentHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListPaymentHistory(ctx, req.(*ListPaymentHistoryParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_CreateOBRequestSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOBRequestSettingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).CreateOBRequestSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.membership.v1.SubscriptionService/CreateOBRequestSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).CreateOBRequestSetting(ctx, req.(*CreateOBRequestSettingParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_GetOBRequestSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOBRequestSettingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).GetOBRequestSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.membership.v1.SubscriptionService/GetOBRequestSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).GetOBRequestSetting(ctx, req.(*GetOBRequestSettingParams))
	}
	return interceptor(ctx, in, info, handler)
}

// SubscriptionService_ServiceDesc is the grpc.ServiceDesc for SubscriptionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SubscriptionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.membership.v1.SubscriptionService",
	HandlerType: (*SubscriptionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSellLinkPublicInfo",
			Handler:    _SubscriptionService_GetSellLinkPublicInfo_Handler,
		},
		{
			MethodName: "BuyMembership",
			Handler:    _SubscriptionService_BuyMembership_Handler,
		},
		{
			MethodName: "GetBuyResult",
			Handler:    _SubscriptionService_GetBuyResult_Handler,
		},
		{
			MethodName: "ListSubscriptions",
			Handler:    _SubscriptionService_ListSubscriptions_Handler,
		},
		{
			MethodName: "ListSubscriptionsForApp",
			Handler:    _SubscriptionService_ListSubscriptionsForApp_Handler,
		},
		{
			MethodName: "ListPaymentHistory",
			Handler:    _SubscriptionService_ListPaymentHistory_Handler,
		},
		{
			MethodName: "CreateOBRequestSetting",
			Handler:    _SubscriptionService_CreateOBRequestSetting_Handler,
		},
		{
			MethodName: "GetOBRequestSetting",
			Handler:    _SubscriptionService_GetOBRequestSetting_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/membership/v1/subscription_api.proto",
}
