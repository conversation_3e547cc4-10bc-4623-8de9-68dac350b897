// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/grooming/v2/appointment_api.proto

package groomingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BookingRequestServiceClient is the client API for BookingRequestService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BookingRequestServiceClient interface {
	// pre submit online booking request
	// If staff and date time are selected, the corresponding time will be locked to prevent other clients from selecting it.
	// It can prevent multiple clients from choosing the same time and causing some order payment failures.
	PreSubmitBookingRequest(ctx context.Context, in *CreateBookingRequestParams, opts ...grpc.CallOption) (*PreSubmitBookingRequestResult, error)
	// create booking request
	CreateBookingRequest(ctx context.Context, in *CreateBookingRequestParams, opts ...grpc.CallOption) (*CreateBookingRequestResult, error)
}

type bookingRequestServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBookingRequestServiceClient(cc grpc.ClientConnInterface) BookingRequestServiceClient {
	return &bookingRequestServiceClient{cc}
}

func (c *bookingRequestServiceClient) PreSubmitBookingRequest(ctx context.Context, in *CreateBookingRequestParams, opts ...grpc.CallOption) (*PreSubmitBookingRequestResult, error) {
	out := new(PreSubmitBookingRequestResult)
	err := c.cc.Invoke(ctx, "/moego.client.grooming.v2.BookingRequestService/PreSubmitBookingRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) CreateBookingRequest(ctx context.Context, in *CreateBookingRequestParams, opts ...grpc.CallOption) (*CreateBookingRequestResult, error) {
	out := new(CreateBookingRequestResult)
	err := c.cc.Invoke(ctx, "/moego.client.grooming.v2.BookingRequestService/CreateBookingRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BookingRequestServiceServer is the server API for BookingRequestService service.
// All implementations must embed UnimplementedBookingRequestServiceServer
// for forward compatibility
type BookingRequestServiceServer interface {
	// pre submit online booking request
	// If staff and date time are selected, the corresponding time will be locked to prevent other clients from selecting it.
	// It can prevent multiple clients from choosing the same time and causing some order payment failures.
	PreSubmitBookingRequest(context.Context, *CreateBookingRequestParams) (*PreSubmitBookingRequestResult, error)
	// create booking request
	CreateBookingRequest(context.Context, *CreateBookingRequestParams) (*CreateBookingRequestResult, error)
	mustEmbedUnimplementedBookingRequestServiceServer()
}

// UnimplementedBookingRequestServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBookingRequestServiceServer struct {
}

func (UnimplementedBookingRequestServiceServer) PreSubmitBookingRequest(context.Context, *CreateBookingRequestParams) (*PreSubmitBookingRequestResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreSubmitBookingRequest not implemented")
}
func (UnimplementedBookingRequestServiceServer) CreateBookingRequest(context.Context, *CreateBookingRequestParams) (*CreateBookingRequestResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBookingRequest not implemented")
}
func (UnimplementedBookingRequestServiceServer) mustEmbedUnimplementedBookingRequestServiceServer() {}

// UnsafeBookingRequestServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BookingRequestServiceServer will
// result in compilation errors.
type UnsafeBookingRequestServiceServer interface {
	mustEmbedUnimplementedBookingRequestServiceServer()
}

func RegisterBookingRequestServiceServer(s grpc.ServiceRegistrar, srv BookingRequestServiceServer) {
	s.RegisterService(&BookingRequestService_ServiceDesc, srv)
}

func _BookingRequestService_PreSubmitBookingRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBookingRequestParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).PreSubmitBookingRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.grooming.v2.BookingRequestService/PreSubmitBookingRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).PreSubmitBookingRequest(ctx, req.(*CreateBookingRequestParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_CreateBookingRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBookingRequestParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).CreateBookingRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.grooming.v2.BookingRequestService/CreateBookingRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).CreateBookingRequest(ctx, req.(*CreateBookingRequestParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BookingRequestService_ServiceDesc is the grpc.ServiceDesc for BookingRequestService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BookingRequestService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.grooming.v2.BookingRequestService",
	HandlerType: (*BookingRequestServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PreSubmitBookingRequest",
			Handler:    _BookingRequestService_PreSubmitBookingRequest_Handler,
		},
		{
			MethodName: "CreateBookingRequest",
			Handler:    _BookingRequestService_CreateBookingRequest_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/grooming/v2/appointment_api.proto",
}
