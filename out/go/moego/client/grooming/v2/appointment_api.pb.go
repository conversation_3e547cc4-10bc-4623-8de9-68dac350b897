// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/grooming/v2/appointment_api.proto

package groomingapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create booking request params
type CreateBookingRequestParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pet selected service list
	// Contains the main service and multiple add-ons selected by each pet
	SelectedPetServices []*v1.SelectedPetServiceDef `protobuf:"bytes,2,rep,name=selected_pet_services,json=selectedPetServices,proto3" json:"selected_pet_services,omitempty"`
	// selected staff id
	// This param may be empty if staff selection is not enabled or the address is not within the service area and
	// the "Allow all clients to submit request without date and time" switch is turned on.
	StaffId *int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// selected appointment date
	// This param may be empty if the address is not within the service area and
	// the "Allow all clients to submit request without date and time" switch is turned on.
	AppointmentDate *string `protobuf:"bytes,4,opt,name=appointment_date,json=appointmentDate,proto3,oneof" json:"appointment_date,omitempty"`
	// selected appointment start time, in minutes
	// This param may be empty if display "Date only" or the address is not within the service area and
	// the "Allow all clients to submit request without date and time" switch is turned on.
	AppointmentStartTime *int32 `protobuf:"varint,5,opt,name=appointment_start_time,json=appointmentStartTime,proto3,oneof" json:"appointment_start_time,omitempty"`
	// additional note
	AdditionalNote *string `protobuf:"bytes,7,opt,name=additional_note,json=additionalNote,proto3,oneof" json:"additional_note,omitempty"`
	// sign agreements
	SignAgreements []*v11.AgreementRecordDef `protobuf:"bytes,6,rep,name=sign_agreements,json=signAgreements,proto3" json:"sign_agreements,omitempty"`
	// payment, If payments is not turned on, it will not be required.
	//
	// Types that are assignable to Payment:
	//
	//	*CreateBookingRequestParams_CardOnFile
	//	*CreateBookingRequestParams_Prepay
	//	*CreateBookingRequestParams_PreAuth
	Payment isCreateBookingRequestParams_Payment `protobuf_oneof:"payment"`
	// discount code
	DiscountCode *string `protobuf:"bytes,8,opt,name=discount_code,json=discountCode,proto3,oneof" json:"discount_code,omitempty"`
	// selected address id, default is primary address
	AddressId *int64 `protobuf:"varint,9,opt,name=address_id,json=addressId,proto3,oneof" json:"address_id,omitempty"`
}

func (x *CreateBookingRequestParams) Reset() {
	*x = CreateBookingRequestParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v2_appointment_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingRequestParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingRequestParams) ProtoMessage() {}

func (x *CreateBookingRequestParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v2_appointment_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingRequestParams.ProtoReflect.Descriptor instead.
func (*CreateBookingRequestParams) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v2_appointment_api_proto_rawDescGZIP(), []int{0}
}

func (x *CreateBookingRequestParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateBookingRequestParams) GetSelectedPetServices() []*v1.SelectedPetServiceDef {
	if x != nil {
		return x.SelectedPetServices
	}
	return nil
}

func (x *CreateBookingRequestParams) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *CreateBookingRequestParams) GetAppointmentDate() string {
	if x != nil && x.AppointmentDate != nil {
		return *x.AppointmentDate
	}
	return ""
}

func (x *CreateBookingRequestParams) GetAppointmentStartTime() int32 {
	if x != nil && x.AppointmentStartTime != nil {
		return *x.AppointmentStartTime
	}
	return 0
}

func (x *CreateBookingRequestParams) GetAdditionalNote() string {
	if x != nil && x.AdditionalNote != nil {
		return *x.AdditionalNote
	}
	return ""
}

func (x *CreateBookingRequestParams) GetSignAgreements() []*v11.AgreementRecordDef {
	if x != nil {
		return x.SignAgreements
	}
	return nil
}

func (m *CreateBookingRequestParams) GetPayment() isCreateBookingRequestParams_Payment {
	if m != nil {
		return m.Payment
	}
	return nil
}

func (x *CreateBookingRequestParams) GetCardOnFile() *v1.CardOnFileDef {
	if x, ok := x.GetPayment().(*CreateBookingRequestParams_CardOnFile); ok {
		return x.CardOnFile
	}
	return nil
}

func (x *CreateBookingRequestParams) GetPrepay() *v1.PrepayDef {
	if x, ok := x.GetPayment().(*CreateBookingRequestParams_Prepay); ok {
		return x.Prepay
	}
	return nil
}

func (x *CreateBookingRequestParams) GetPreAuth() *v1.PreAuthDef {
	if x, ok := x.GetPayment().(*CreateBookingRequestParams_PreAuth); ok {
		return x.PreAuth
	}
	return nil
}

func (x *CreateBookingRequestParams) GetDiscountCode() string {
	if x != nil && x.DiscountCode != nil {
		return *x.DiscountCode
	}
	return ""
}

func (x *CreateBookingRequestParams) GetAddressId() int64 {
	if x != nil && x.AddressId != nil {
		return *x.AddressId
	}
	return 0
}

type isCreateBookingRequestParams_Payment interface {
	isCreateBookingRequestParams_Payment()
}

type CreateBookingRequestParams_CardOnFile struct {
	// card on file, one star shield
	// To require clients to input valid credit card on file when submitting booking requests.
	CardOnFile *v1.CardOnFileDef `protobuf:"bytes,11,opt,name=card_on_file,json=cardOnFile,proto3,oneof"`
}

type CreateBookingRequestParams_Prepay struct {
	// prepay, four stars shield
	// To require clients to pay up to the full amount of your service fee when submitting booking requests.
	Prepay *v1.PrepayDef `protobuf:"bytes,12,opt,name=prepay,proto3,oneof"`
}

type CreateBookingRequestParams_PreAuth struct {
	// pre-auth, three stars shield
	// To require a valid card on file upon booking.
	// The card will be pre-authorized for the client's ticket amount 24 hours prior to the appointment, safeguarding revenue and reducing no-shows.
	PreAuth *v1.PreAuthDef `protobuf:"bytes,13,opt,name=pre_auth,json=preAuth,proto3,oneof"`
}

func (*CreateBookingRequestParams_CardOnFile) isCreateBookingRequestParams_Payment() {}

func (*CreateBookingRequestParams_Prepay) isCreateBookingRequestParams_Payment() {}

func (*CreateBookingRequestParams_PreAuth) isCreateBookingRequestParams_Payment() {}

// create booking request result
type CreateBookingRequestResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto accept flag
	IsAutoAccept bool `protobuf:"varint,1,opt,name=is_auto_accept,json=isAutoAccept,proto3" json:"is_auto_accept,omitempty"`
}

func (x *CreateBookingRequestResult) Reset() {
	*x = CreateBookingRequestResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v2_appointment_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingRequestResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingRequestResult) ProtoMessage() {}

func (x *CreateBookingRequestResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v2_appointment_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingRequestResult.ProtoReflect.Descriptor instead.
func (*CreateBookingRequestResult) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v2_appointment_api_proto_rawDescGZIP(), []int{1}
}

func (x *CreateBookingRequestResult) GetIsAutoAccept() bool {
	if x != nil {
		return x.IsAutoAccept
	}
	return false
}

// pre submit booking request result
type PreSubmitBookingRequestResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *PreSubmitBookingRequestResult) Reset() {
	*x = PreSubmitBookingRequestResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v2_appointment_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreSubmitBookingRequestResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreSubmitBookingRequestResult) ProtoMessage() {}

func (x *PreSubmitBookingRequestResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v2_appointment_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreSubmitBookingRequestResult.ProtoReflect.Descriptor instead.
func (*PreSubmitBookingRequestResult) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v2_appointment_api_proto_rawDescGZIP(), []int{2}
}

func (x *PreSubmitBookingRequestResult) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

var File_moego_client_grooming_v2_appointment_api_proto protoreflect.FileDescriptor

var file_moego_client_grooming_v2_appointment_api_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x1a, 0x35, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64,
	0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xc6, 0x07, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x73, 0x0a, 0x15, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65,
	0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x13, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x12, 0x27, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4a, 0x0a, 0x10, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b,
	0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48,
	0x02, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x45, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28,
	0x00, 0x48, 0x03, 0x52, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x0f,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x18, 0xff, 0xff, 0x03,
	0x48, 0x04, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x6f,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x56, 0x0a, 0x0f, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x66, 0x52, 0x0e, 0x73,
	0x69, 0x67, 0x6e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x51, 0x0a,
	0x0c, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x44,
	0x65, 0x66, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x4f, 0x6e, 0x46, 0x69, 0x6c, 0x65,
	0x12, 0x43, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x65, 0x70, 0x61, 0x79, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x06, 0x70,
	0x72, 0x65, 0x70, 0x61, 0x79, 0x12, 0x47, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74,
	0x68, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68,
	0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x07, 0x70, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x12, 0x31,
	0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x14, 0x48, 0x05,
	0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x06,
	0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x09,
	0x0a, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x19, 0x0a, 0x17, 0x5f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x42, 0x0a, 0x1a, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f,
	0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x22,
	0x37, 0x0a, 0x1d, 0x50, 0x72, 0x65, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xa7, 0x02, 0x0a, 0x15, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x17, 0x50, 0x72, 0x65, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x72, 0x65, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x82, 0x01,
	0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x42, 0x81, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5b, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_grooming_v2_appointment_api_proto_rawDescOnce sync.Once
	file_moego_client_grooming_v2_appointment_api_proto_rawDescData = file_moego_client_grooming_v2_appointment_api_proto_rawDesc
)

func file_moego_client_grooming_v2_appointment_api_proto_rawDescGZIP() []byte {
	file_moego_client_grooming_v2_appointment_api_proto_rawDescOnce.Do(func() {
		file_moego_client_grooming_v2_appointment_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_grooming_v2_appointment_api_proto_rawDescData)
	})
	return file_moego_client_grooming_v2_appointment_api_proto_rawDescData
}

var file_moego_client_grooming_v2_appointment_api_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_client_grooming_v2_appointment_api_proto_goTypes = []interface{}{
	(*CreateBookingRequestParams)(nil),    // 0: moego.client.grooming.v2.CreateBookingRequestParams
	(*CreateBookingRequestResult)(nil),    // 1: moego.client.grooming.v2.CreateBookingRequestResult
	(*PreSubmitBookingRequestResult)(nil), // 2: moego.client.grooming.v2.PreSubmitBookingRequestResult
	(*v1.SelectedPetServiceDef)(nil),      // 3: moego.models.online_booking.v1.SelectedPetServiceDef
	(*v11.AgreementRecordDef)(nil),        // 4: moego.models.agreement.v1.AgreementRecordDef
	(*v1.CardOnFileDef)(nil),              // 5: moego.models.online_booking.v1.CardOnFileDef
	(*v1.PrepayDef)(nil),                  // 6: moego.models.online_booking.v1.PrepayDef
	(*v1.PreAuthDef)(nil),                 // 7: moego.models.online_booking.v1.PreAuthDef
}
var file_moego_client_grooming_v2_appointment_api_proto_depIdxs = []int32{
	3, // 0: moego.client.grooming.v2.CreateBookingRequestParams.selected_pet_services:type_name -> moego.models.online_booking.v1.SelectedPetServiceDef
	4, // 1: moego.client.grooming.v2.CreateBookingRequestParams.sign_agreements:type_name -> moego.models.agreement.v1.AgreementRecordDef
	5, // 2: moego.client.grooming.v2.CreateBookingRequestParams.card_on_file:type_name -> moego.models.online_booking.v1.CardOnFileDef
	6, // 3: moego.client.grooming.v2.CreateBookingRequestParams.prepay:type_name -> moego.models.online_booking.v1.PrepayDef
	7, // 4: moego.client.grooming.v2.CreateBookingRequestParams.pre_auth:type_name -> moego.models.online_booking.v1.PreAuthDef
	0, // 5: moego.client.grooming.v2.BookingRequestService.PreSubmitBookingRequest:input_type -> moego.client.grooming.v2.CreateBookingRequestParams
	0, // 6: moego.client.grooming.v2.BookingRequestService.CreateBookingRequest:input_type -> moego.client.grooming.v2.CreateBookingRequestParams
	2, // 7: moego.client.grooming.v2.BookingRequestService.PreSubmitBookingRequest:output_type -> moego.client.grooming.v2.PreSubmitBookingRequestResult
	1, // 8: moego.client.grooming.v2.BookingRequestService.CreateBookingRequest:output_type -> moego.client.grooming.v2.CreateBookingRequestResult
	7, // [7:9] is the sub-list for method output_type
	5, // [5:7] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_client_grooming_v2_appointment_api_proto_init() }
func file_moego_client_grooming_v2_appointment_api_proto_init() {
	if File_moego_client_grooming_v2_appointment_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_grooming_v2_appointment_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingRequestParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v2_appointment_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingRequestResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v2_appointment_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreSubmitBookingRequestResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_grooming_v2_appointment_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*CreateBookingRequestParams_CardOnFile)(nil),
		(*CreateBookingRequestParams_Prepay)(nil),
		(*CreateBookingRequestParams_PreAuth)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_grooming_v2_appointment_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_grooming_v2_appointment_api_proto_goTypes,
		DependencyIndexes: file_moego_client_grooming_v2_appointment_api_proto_depIdxs,
		MessageInfos:      file_moego_client_grooming_v2_appointment_api_proto_msgTypes,
	}.Build()
	File_moego_client_grooming_v2_appointment_api_proto = out.File
	file_moego_client_grooming_v2_appointment_api_proto_rawDesc = nil
	file_moego_client_grooming_v2_appointment_api_proto_goTypes = nil
	file_moego_client_grooming_v2_appointment_api_proto_depIdxs = nil
}
