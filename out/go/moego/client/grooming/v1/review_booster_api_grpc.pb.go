// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/grooming/v1/review_booster_api.proto

package groomingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ReviewBoosterServiceClient is the client API for ReviewBoosterService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReviewBoosterServiceClient interface {
	// create review booster record
	CreateReviewBooster(ctx context.Context, in *CreateReviewBoosterRequest, opts ...grpc.CallOption) (*CreateReviewBoosterResponse, error)
	// get review booster record
	GetReviewBoosterList(ctx context.Context, in *GetReviewBoosterListRequest, opts ...grpc.CallOption) (*GetReviewBoosterListResponse, error)
	// get review booster config for specific business
	GetReviewBoosterConfig(ctx context.Context, in *GetReviewBoosterConfigParams, opts ...grpc.CallOption) (*GetReviewBoosterConfigResult, error)
}

type reviewBoosterServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewReviewBoosterServiceClient(cc grpc.ClientConnInterface) ReviewBoosterServiceClient {
	return &reviewBoosterServiceClient{cc}
}

func (c *reviewBoosterServiceClient) CreateReviewBooster(ctx context.Context, in *CreateReviewBoosterRequest, opts ...grpc.CallOption) (*CreateReviewBoosterResponse, error) {
	out := new(CreateReviewBoosterResponse)
	err := c.cc.Invoke(ctx, "/moego.client.grooming.v1.ReviewBoosterService/CreateReviewBooster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reviewBoosterServiceClient) GetReviewBoosterList(ctx context.Context, in *GetReviewBoosterListRequest, opts ...grpc.CallOption) (*GetReviewBoosterListResponse, error) {
	out := new(GetReviewBoosterListResponse)
	err := c.cc.Invoke(ctx, "/moego.client.grooming.v1.ReviewBoosterService/GetReviewBoosterList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *reviewBoosterServiceClient) GetReviewBoosterConfig(ctx context.Context, in *GetReviewBoosterConfigParams, opts ...grpc.CallOption) (*GetReviewBoosterConfigResult, error) {
	out := new(GetReviewBoosterConfigResult)
	err := c.cc.Invoke(ctx, "/moego.client.grooming.v1.ReviewBoosterService/GetReviewBoosterConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReviewBoosterServiceServer is the server API for ReviewBoosterService service.
// All implementations must embed UnimplementedReviewBoosterServiceServer
// for forward compatibility
type ReviewBoosterServiceServer interface {
	// create review booster record
	CreateReviewBooster(context.Context, *CreateReviewBoosterRequest) (*CreateReviewBoosterResponse, error)
	// get review booster record
	GetReviewBoosterList(context.Context, *GetReviewBoosterListRequest) (*GetReviewBoosterListResponse, error)
	// get review booster config for specific business
	GetReviewBoosterConfig(context.Context, *GetReviewBoosterConfigParams) (*GetReviewBoosterConfigResult, error)
	mustEmbedUnimplementedReviewBoosterServiceServer()
}

// UnimplementedReviewBoosterServiceServer must be embedded to have forward compatible implementations.
type UnimplementedReviewBoosterServiceServer struct {
}

func (UnimplementedReviewBoosterServiceServer) CreateReviewBooster(context.Context, *CreateReviewBoosterRequest) (*CreateReviewBoosterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateReviewBooster not implemented")
}
func (UnimplementedReviewBoosterServiceServer) GetReviewBoosterList(context.Context, *GetReviewBoosterListRequest) (*GetReviewBoosterListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReviewBoosterList not implemented")
}
func (UnimplementedReviewBoosterServiceServer) GetReviewBoosterConfig(context.Context, *GetReviewBoosterConfigParams) (*GetReviewBoosterConfigResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReviewBoosterConfig not implemented")
}
func (UnimplementedReviewBoosterServiceServer) mustEmbedUnimplementedReviewBoosterServiceServer() {}

// UnsafeReviewBoosterServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReviewBoosterServiceServer will
// result in compilation errors.
type UnsafeReviewBoosterServiceServer interface {
	mustEmbedUnimplementedReviewBoosterServiceServer()
}

func RegisterReviewBoosterServiceServer(s grpc.ServiceRegistrar, srv ReviewBoosterServiceServer) {
	s.RegisterService(&ReviewBoosterService_ServiceDesc, srv)
}

func _ReviewBoosterService_CreateReviewBooster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateReviewBoosterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReviewBoosterServiceServer).CreateReviewBooster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.grooming.v1.ReviewBoosterService/CreateReviewBooster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReviewBoosterServiceServer).CreateReviewBooster(ctx, req.(*CreateReviewBoosterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReviewBoosterService_GetReviewBoosterList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReviewBoosterListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReviewBoosterServiceServer).GetReviewBoosterList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.grooming.v1.ReviewBoosterService/GetReviewBoosterList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReviewBoosterServiceServer).GetReviewBoosterList(ctx, req.(*GetReviewBoosterListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReviewBoosterService_GetReviewBoosterConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReviewBoosterConfigParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReviewBoosterServiceServer).GetReviewBoosterConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.grooming.v1.ReviewBoosterService/GetReviewBoosterConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReviewBoosterServiceServer).GetReviewBoosterConfig(ctx, req.(*GetReviewBoosterConfigParams))
	}
	return interceptor(ctx, in, info, handler)
}

// ReviewBoosterService_ServiceDesc is the grpc.ServiceDesc for ReviewBoosterService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ReviewBoosterService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.grooming.v1.ReviewBoosterService",
	HandlerType: (*ReviewBoosterServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateReviewBooster",
			Handler:    _ReviewBoosterService_CreateReviewBooster_Handler,
		},
		{
			MethodName: "GetReviewBoosterList",
			Handler:    _ReviewBoosterService_GetReviewBoosterList_Handler,
		},
		{
			MethodName: "GetReviewBoosterConfig",
			Handler:    _ReviewBoosterService_GetReviewBoosterConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/grooming/v1/review_booster_api.proto",
}
