// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/grooming/v1/online_booking_api.proto

package groomingapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// select available pet list request
type GetAvailablePetListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetAvailablePetListRequest) Reset() {
	*x = GetAvailablePetListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailablePetListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailablePetListRequest) ProtoMessage() {}

func (x *GetAvailablePetListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailablePetListRequest.ProtoReflect.Descriptor instead.
func (*GetAvailablePetListRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetAvailablePetListRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// select available pet list response
type GetAvailablePetListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking available pet list
	BusinessPets []*v1.BusinessCustomerPetModelClientView `protobuf:"bytes,1,rep,name=business_pets,json=businessPets,proto3" json:"business_pets,omitempty"`
	// pet's available meta data
	AvailablePets []*v11.AvailablePetMetadataDef `protobuf:"bytes,2,rep,name=available_pets,json=availablePets,proto3" json:"available_pets,omitempty"`
}

func (x *GetAvailablePetListResponse) Reset() {
	*x = GetAvailablePetListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailablePetListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailablePetListResponse) ProtoMessage() {}

func (x *GetAvailablePetListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailablePetListResponse.ProtoReflect.Descriptor instead.
func (*GetAvailablePetListResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetAvailablePetListResponse) GetBusinessPets() []*v1.BusinessCustomerPetModelClientView {
	if x != nil {
		return x.BusinessPets
	}
	return nil
}

func (x *GetAvailablePetListResponse) GetAvailablePets() []*v11.AvailablePetMetadataDef {
	if x != nil {
		return x.AvailablePets
	}
	return nil
}

// select service list request
type GetAvailableServiceListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// selected pet id list
	PetIds []int64 `protobuf:"varint,2,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
}

func (x *GetAvailableServiceListRequest) Reset() {
	*x = GetAvailableServiceListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableServiceListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableServiceListRequest) ProtoMessage() {}

func (x *GetAvailableServiceListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableServiceListRequest.ProtoReflect.Descriptor instead.
func (*GetAvailableServiceListRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetAvailableServiceListRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetAvailableServiceListRequest) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

// select available service list response
type GetAvailableServiceListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// category list
	Categories []*v12.CategorySelectServiceView `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"`
	// category with service list
	Services []*v12.ServiceModelSelectServiceView `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
	// customer's custom service list
	CustomerServices []*v12.CustomerServiceModelSelectServiceView `protobuf:"bytes,3,rep,name=customer_services,json=customerServices,proto3" json:"customer_services,omitempty"`
	// pet's applicable service list
	AvailableServices []*v11.AvailableServiceDef `protobuf:"bytes,4,rep,name=available_services,json=availableServices,proto3" json:"available_services,omitempty"`
}

func (x *GetAvailableServiceListResponse) Reset() {
	*x = GetAvailableServiceListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableServiceListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableServiceListResponse) ProtoMessage() {}

func (x *GetAvailableServiceListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableServiceListResponse.ProtoReflect.Descriptor instead.
func (*GetAvailableServiceListResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetAvailableServiceListResponse) GetCategories() []*v12.CategorySelectServiceView {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *GetAvailableServiceListResponse) GetServices() []*v12.ServiceModelSelectServiceView {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *GetAvailableServiceListResponse) GetCustomerServices() []*v12.CustomerServiceModelSelectServiceView {
	if x != nil {
		return x.CustomerServices
	}
	return nil
}

func (x *GetAvailableServiceListResponse) GetAvailableServices() []*v11.AvailableServiceDef {
	if x != nil {
		return x.AvailableServices
	}
	return nil
}

// select available staff list request
type GetAvailableStaffListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pet selected service list
	SelectedPetServices []*v11.SelectedPetServiceDef `protobuf:"bytes,2,rep,name=selected_pet_services,json=selectedPetServices,proto3" json:"selected_pet_services,omitempty"`
}

func (x *GetAvailableStaffListRequest) Reset() {
	*x = GetAvailableStaffListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableStaffListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableStaffListRequest) ProtoMessage() {}

func (x *GetAvailableStaffListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableStaffListRequest.ProtoReflect.Descriptor instead.
func (*GetAvailableStaffListRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetAvailableStaffListRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetAvailableStaffListRequest) GetSelectedPetServices() []*v11.SelectedPetServiceDef {
	if x != nil {
		return x.SelectedPetServices
	}
	return nil
}

// select available staff list response
type GetAvailableStaffListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available staff list
	AvailableStaffs []*v11.AvailableStaffDef `protobuf:"bytes,1,rep,name=available_staffs,json=availableStaffs,proto3" json:"available_staffs,omitempty"`
}

func (x *GetAvailableStaffListResponse) Reset() {
	*x = GetAvailableStaffListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableStaffListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableStaffListResponse) ProtoMessage() {}

func (x *GetAvailableStaffListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableStaffListResponse.ProtoReflect.Descriptor instead.
func (*GetAvailableStaffListResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{5}
}

func (x *GetAvailableStaffListResponse) GetAvailableStaffs() []*v11.AvailableStaffDef {
	if x != nil {
		return x.AvailableStaffs
	}
	return nil
}

// get the first available date request
type GetFirstAvailableDateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pet selected service list
	SelectedPetServices []*v11.SelectedPetServiceDef `protobuf:"bytes,2,rep,name=selected_pet_services,json=selectedPetServices,proto3" json:"selected_pet_services,omitempty"`
	// selected staff id list
	StaffIds []int64 `protobuf:"varint,3,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// start search date
	StartDate *string `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// selected address id, default is primary address
	AddressId *int64 `protobuf:"varint,5,opt,name=address_id,json=addressId,proto3,oneof" json:"address_id,omitempty"`
}

func (x *GetFirstAvailableDateRequest) Reset() {
	*x = GetFirstAvailableDateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFirstAvailableDateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFirstAvailableDateRequest) ProtoMessage() {}

func (x *GetFirstAvailableDateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFirstAvailableDateRequest.ProtoReflect.Descriptor instead.
func (*GetFirstAvailableDateRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetFirstAvailableDateRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetFirstAvailableDateRequest) GetSelectedPetServices() []*v11.SelectedPetServiceDef {
	if x != nil {
		return x.SelectedPetServices
	}
	return nil
}

func (x *GetFirstAvailableDateRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *GetFirstAvailableDateRequest) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *GetFirstAvailableDateRequest) GetAddressId() int64 {
	if x != nil && x.AddressId != nil {
		return *x.AddressId
	}
	return 0
}

// get the first available date response
type GetFirstAvailableDateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff's first available date list
	AvailableDates []*v11.AvailableStaffDateDef `protobuf:"bytes,1,rep,name=available_dates,json=availableDates,proto3" json:"available_dates,omitempty"`
}

func (x *GetFirstAvailableDateResponse) Reset() {
	*x = GetFirstAvailableDateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFirstAvailableDateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFirstAvailableDateResponse) ProtoMessage() {}

func (x *GetFirstAvailableDateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFirstAvailableDateResponse.ProtoReflect.Descriptor instead.
func (*GetFirstAvailableDateResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetFirstAvailableDateResponse) GetAvailableDates() []*v11.AvailableStaffDateDef {
	if x != nil {
		return x.AvailableDates
	}
	return nil
}

// select available date list request
type GetAvailableDateListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pet selected service list
	SelectedPetServices []*v11.SelectedPetServiceDef `protobuf:"bytes,2,rep,name=selected_pet_services,json=selectedPetServices,proto3" json:"selected_pet_services,omitempty"`
	// selected staff id list
	StaffIds []int64 `protobuf:"varint,3,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// selected date
	Date string `protobuf:"bytes,4,opt,name=date,proto3" json:"date,omitempty"`
	// search date range
	//
	// Types that are assignable to SearchDateRange:
	//
	//	*GetAvailableDateListRequest_SearchDays
	//	*GetAvailableDateListRequest_IsEndOfMonth
	SearchDateRange isGetAvailableDateListRequest_SearchDateRange `protobuf_oneof:"search_date_range"`
	// selected address id, default is primary address
	AddressId *int64 `protobuf:"varint,7,opt,name=address_id,json=addressId,proto3,oneof" json:"address_id,omitempty"`
}

func (x *GetAvailableDateListRequest) Reset() {
	*x = GetAvailableDateListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableDateListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableDateListRequest) ProtoMessage() {}

func (x *GetAvailableDateListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableDateListRequest.ProtoReflect.Descriptor instead.
func (*GetAvailableDateListRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{8}
}

func (x *GetAvailableDateListRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetAvailableDateListRequest) GetSelectedPetServices() []*v11.SelectedPetServiceDef {
	if x != nil {
		return x.SelectedPetServices
	}
	return nil
}

func (x *GetAvailableDateListRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *GetAvailableDateListRequest) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (m *GetAvailableDateListRequest) GetSearchDateRange() isGetAvailableDateListRequest_SearchDateRange {
	if m != nil {
		return m.SearchDateRange
	}
	return nil
}

func (x *GetAvailableDateListRequest) GetSearchDays() int32 {
	if x, ok := x.GetSearchDateRange().(*GetAvailableDateListRequest_SearchDays); ok {
		return x.SearchDays
	}
	return 0
}

func (x *GetAvailableDateListRequest) GetIsEndOfMonth() bool {
	if x, ok := x.GetSearchDateRange().(*GetAvailableDateListRequest_IsEndOfMonth); ok {
		return x.IsEndOfMonth
	}
	return false
}

func (x *GetAvailableDateListRequest) GetAddressId() int64 {
	if x != nil && x.AddressId != nil {
		return *x.AddressId
	}
	return 0
}

type isGetAvailableDateListRequest_SearchDateRange interface {
	isGetAvailableDateListRequest_SearchDateRange()
}

type GetAvailableDateListRequest_SearchDays struct {
	// search the specified number of days starting from the first available day
	SearchDays int32 `protobuf:"varint,5,opt,name=search_days,json=searchDays,proto3,oneof"`
}

type GetAvailableDateListRequest_IsEndOfMonth struct {
	// search from the first available day to the end of the month
	IsEndOfMonth bool `protobuf:"varint,6,opt,name=is_end_of_month,json=isEndOfMonth,proto3,oneof"`
}

func (*GetAvailableDateListRequest_SearchDays) isGetAvailableDateListRequest_SearchDateRange() {}

func (*GetAvailableDateListRequest_IsEndOfMonth) isGetAvailableDateListRequest_SearchDateRange() {}

// select available date list response
type GetAvailableDateListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available date list
	AvailableDates []*v11.AvailableDateDef `protobuf:"bytes,1,rep,name=available_dates,json=availableDates,proto3" json:"available_dates,omitempty"`
}

func (x *GetAvailableDateListResponse) Reset() {
	*x = GetAvailableDateListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableDateListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableDateListResponse) ProtoMessage() {}

func (x *GetAvailableDateListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableDateListResponse.ProtoReflect.Descriptor instead.
func (*GetAvailableDateListResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{9}
}

func (x *GetAvailableDateListResponse) GetAvailableDates() []*v11.AvailableDateDef {
	if x != nil {
		return x.AvailableDates
	}
	return nil
}

// get the available timeslot on a specified day request
type GetAvailableTimeslotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pet selected service list
	SelectedPetServices []*v11.SelectedPetServiceDef `protobuf:"bytes,2,rep,name=selected_pet_services,json=selectedPetServices,proto3" json:"selected_pet_services,omitempty"`
	// selected staff id list
	StaffIds []int64 `protobuf:"varint,3,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// selected date
	Date string `protobuf:"bytes,4,opt,name=date,proto3" json:"date,omitempty"`
	// selected address id, default is primary address
	AddressId *int64 `protobuf:"varint,5,opt,name=address_id,json=addressId,proto3,oneof" json:"address_id,omitempty"`
}

func (x *GetAvailableTimeslotRequest) Reset() {
	*x = GetAvailableTimeslotRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableTimeslotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableTimeslotRequest) ProtoMessage() {}

func (x *GetAvailableTimeslotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableTimeslotRequest.ProtoReflect.Descriptor instead.
func (*GetAvailableTimeslotRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{10}
}

func (x *GetAvailableTimeslotRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetAvailableTimeslotRequest) GetSelectedPetServices() []*v11.SelectedPetServiceDef {
	if x != nil {
		return x.SelectedPetServices
	}
	return nil
}

func (x *GetAvailableTimeslotRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *GetAvailableTimeslotRequest) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *GetAvailableTimeslotRequest) GetAddressId() int64 {
	if x != nil && x.AddressId != nil {
		return *x.AddressId
	}
	return 0
}

// get the available timeslot on a specified day response
type GetAvailableTimeslotResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available timeslot list
	AvailableTimeslots []*v11.AvailableTimeslotDef `protobuf:"bytes,1,rep,name=available_timeslots,json=availableTimeslots,proto3" json:"available_timeslots,omitempty"`
}

func (x *GetAvailableTimeslotResponse) Reset() {
	*x = GetAvailableTimeslotResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableTimeslotResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableTimeslotResponse) ProtoMessage() {}

func (x *GetAvailableTimeslotResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableTimeslotResponse.ProtoReflect.Descriptor instead.
func (*GetAvailableTimeslotResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{11}
}

func (x *GetAvailableTimeslotResponse) GetAvailableTimeslots() []*v11.AvailableTimeslotDef {
	if x != nil {
		return x.AvailableTimeslots
	}
	return nil
}

// can book online request
type CanBookOnlineRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *CanBookOnlineRequest) Reset() {
	*x = CanBookOnlineRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CanBookOnlineRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CanBookOnlineRequest) ProtoMessage() {}

func (x *CanBookOnlineRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CanBookOnlineRequest.ProtoReflect.Descriptor instead.
func (*CanBookOnlineRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{12}
}

func (x *CanBookOnlineRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// can book online response
type CanBookOnlineResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// can book online
	IsCanBookOnline bool `protobuf:"varint,1,opt,name=is_can_book_online,json=isCanBookOnline,proto3" json:"is_can_book_online,omitempty"`
	// is book online enable
	IsBookOnlineEnable bool `protobuf:"varint,2,opt,name=is_book_online_enable,json=isBookOnlineEnable,proto3" json:"is_book_online_enable,omitempty"`
	// is client type accepted
	IsAcceptedClientType bool `protobuf:"varint,3,opt,name=is_accepted_client_type,json=isAcceptedClientType,proto3" json:"is_accepted_client_type,omitempty"`
	// is blocked online booking
	IsBlockedOnlineBooking bool `protobuf:"varint,4,opt,name=is_blocked_online_booking,json=isBlockedOnlineBooking,proto3" json:"is_blocked_online_booking,omitempty"`
	// primary address out of service area
	IsOutOfServiceArea bool `protobuf:"varint,5,opt,name=is_out_of_service_area,json=isOutOfServiceArea,proto3" json:"is_out_of_service_area,omitempty"`
	// has primary address
	IsHasPrimaryAddress bool `protobuf:"varint,6,opt,name=is_has_primary_address,json=isHasPrimaryAddress,proto3" json:"is_has_primary_address,omitempty"`
	// is need address
	IsNeedAddress bool `protobuf:"varint,7,opt,name=is_need_address,json=isNeedAddress,proto3" json:"is_need_address,omitempty"`
	// is allowed simplify submit
	IsAllowedSimplifySubmit bool `protobuf:"varint,8,opt,name=is_allowed_simplify_submit,json=isAllowedSimplifySubmit,proto3" json:"is_allowed_simplify_submit,omitempty"`
	// is check existing client's address
	IsCheckExistingClient bool `protobuf:"varint,9,opt,name=is_check_existing_client,json=isCheckExistingClient,proto3" json:"is_check_existing_client,omitempty"`
}

func (x *CanBookOnlineResponse) Reset() {
	*x = CanBookOnlineResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CanBookOnlineResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CanBookOnlineResponse) ProtoMessage() {}

func (x *CanBookOnlineResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CanBookOnlineResponse.ProtoReflect.Descriptor instead.
func (*CanBookOnlineResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{13}
}

func (x *CanBookOnlineResponse) GetIsCanBookOnline() bool {
	if x != nil {
		return x.IsCanBookOnline
	}
	return false
}

func (x *CanBookOnlineResponse) GetIsBookOnlineEnable() bool {
	if x != nil {
		return x.IsBookOnlineEnable
	}
	return false
}

func (x *CanBookOnlineResponse) GetIsAcceptedClientType() bool {
	if x != nil {
		return x.IsAcceptedClientType
	}
	return false
}

func (x *CanBookOnlineResponse) GetIsBlockedOnlineBooking() bool {
	if x != nil {
		return x.IsBlockedOnlineBooking
	}
	return false
}

func (x *CanBookOnlineResponse) GetIsOutOfServiceArea() bool {
	if x != nil {
		return x.IsOutOfServiceArea
	}
	return false
}

func (x *CanBookOnlineResponse) GetIsHasPrimaryAddress() bool {
	if x != nil {
		return x.IsHasPrimaryAddress
	}
	return false
}

func (x *CanBookOnlineResponse) GetIsNeedAddress() bool {
	if x != nil {
		return x.IsNeedAddress
	}
	return false
}

func (x *CanBookOnlineResponse) GetIsAllowedSimplifySubmit() bool {
	if x != nil {
		return x.IsAllowedSimplifySubmit
	}
	return false
}

func (x *CanBookOnlineResponse) GetIsCheckExistingClient() bool {
	if x != nil {
		return x.IsCheckExistingClient
	}
	return false
}

// get staff working hour range list request
type GetStaffWorkingHourRangeListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// selected staff id list
	StaffIds []int64 `protobuf:"varint,2,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// start date
	StartDate string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate string `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
}

func (x *GetStaffWorkingHourRangeListRequest) Reset() {
	*x = GetStaffWorkingHourRangeListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffWorkingHourRangeListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffWorkingHourRangeListRequest) ProtoMessage() {}

func (x *GetStaffWorkingHourRangeListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffWorkingHourRangeListRequest.ProtoReflect.Descriptor instead.
func (*GetStaffWorkingHourRangeListRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{14}
}

func (x *GetStaffWorkingHourRangeListRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetStaffWorkingHourRangeListRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *GetStaffWorkingHourRangeListRequest) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *GetStaffWorkingHourRangeListRequest) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

// get staff working hour range list response
type GetStaffWorkingHourRangeListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff working hour range list
	Staffs []*v11.AvailableStaffWorkingHourRangeDef `protobuf:"bytes,1,rep,name=staffs,proto3" json:"staffs,omitempty"`
}

func (x *GetStaffWorkingHourRangeListResponse) Reset() {
	*x = GetStaffWorkingHourRangeListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStaffWorkingHourRangeListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStaffWorkingHourRangeListResponse) ProtoMessage() {}

func (x *GetStaffWorkingHourRangeListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStaffWorkingHourRangeListResponse.ProtoReflect.Descriptor instead.
func (*GetStaffWorkingHourRangeListResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{15}
}

func (x *GetStaffWorkingHourRangeListResponse) GetStaffs() []*v11.AvailableStaffWorkingHourRangeDef {
	if x != nil {
		return x.Staffs
	}
	return nil
}

// get book online address request
type GetBookOnlineAddressRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetBookOnlineAddressRequest) Reset() {
	*x = GetBookOnlineAddressRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBookOnlineAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookOnlineAddressRequest) ProtoMessage() {}

func (x *GetBookOnlineAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookOnlineAddressRequest.ProtoReflect.Descriptor instead.
func (*GetBookOnlineAddressRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{16}
}

func (x *GetBookOnlineAddressRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// get book online address response
type GetBookOnlineAddressResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// primary address
	PrimaryAddress *v1.BusinessCustomerAddressModelClientView `protobuf:"bytes,1,opt,name=primary_address,json=primaryAddress,proto3" json:"primary_address,omitempty"`
}

func (x *GetBookOnlineAddressResponse) Reset() {
	*x = GetBookOnlineAddressResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBookOnlineAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookOnlineAddressResponse) ProtoMessage() {}

func (x *GetBookOnlineAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookOnlineAddressResponse.ProtoReflect.Descriptor instead.
func (*GetBookOnlineAddressResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{17}
}

func (x *GetBookOnlineAddressResponse) GetPrimaryAddress() *v1.BusinessCustomerAddressModelClientView {
	if x != nil {
		return x.PrimaryAddress
	}
	return nil
}

// list book online addresses params
type ListBookOnlineAddressesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *ListBookOnlineAddressesParams) Reset() {
	*x = ListBookOnlineAddressesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBookOnlineAddressesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBookOnlineAddressesParams) ProtoMessage() {}

func (x *ListBookOnlineAddressesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBookOnlineAddressesParams.ProtoReflect.Descriptor instead.
func (*ListBookOnlineAddressesParams) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{18}
}

func (x *ListBookOnlineAddressesParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// list book online addresses result
type ListBookOnlineAddressesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of account address.
	Addresses []*v1.BusinessCustomerAddressView `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
	// address extra info
	ExtraInfos []*ListBookOnlineAddressesResult_AddressExtraInfo `protobuf:"bytes,2,rep,name=extra_infos,json=extraInfos,proto3" json:"extra_infos,omitempty"`
}

func (x *ListBookOnlineAddressesResult) Reset() {
	*x = ListBookOnlineAddressesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBookOnlineAddressesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBookOnlineAddressesResult) ProtoMessage() {}

func (x *ListBookOnlineAddressesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBookOnlineAddressesResult.ProtoReflect.Descriptor instead.
func (*ListBookOnlineAddressesResult) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{19}
}

func (x *ListBookOnlineAddressesResult) GetAddresses() []*v1.BusinessCustomerAddressView {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *ListBookOnlineAddressesResult) GetExtraInfos() []*ListBookOnlineAddressesResult_AddressExtraInfo {
	if x != nil {
		return x.ExtraInfos
	}
	return nil
}

// The params message for the CheckBookOnlineAddress
type CheckBookOnlineAddressParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// address coordinate
	Coordinate *latlng.LatLng `protobuf:"bytes,2,opt,name=coordinate,proto3" json:"coordinate,omitempty"`
	// zipcode
	Zipcode string `protobuf:"bytes,3,opt,name=zipcode,proto3" json:"zipcode,omitempty"`
}

func (x *CheckBookOnlineAddressParams) Reset() {
	*x = CheckBookOnlineAddressParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckBookOnlineAddressParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckBookOnlineAddressParams) ProtoMessage() {}

func (x *CheckBookOnlineAddressParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckBookOnlineAddressParams.ProtoReflect.Descriptor instead.
func (*CheckBookOnlineAddressParams) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{20}
}

func (x *CheckBookOnlineAddressParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CheckBookOnlineAddressParams) GetCoordinate() *latlng.LatLng {
	if x != nil {
		return x.Coordinate
	}
	return nil
}

func (x *CheckBookOnlineAddressParams) GetZipcode() string {
	if x != nil {
		return x.Zipcode
	}
	return ""
}

// The result message for the CheckBookOnlineAddress
type CheckBookOnlineAddressResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is available
	IsAvailable bool `protobuf:"varint,1,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
}

func (x *CheckBookOnlineAddressResult) Reset() {
	*x = CheckBookOnlineAddressResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckBookOnlineAddressResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckBookOnlineAddressResult) ProtoMessage() {}

func (x *CheckBookOnlineAddressResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckBookOnlineAddressResult.ProtoReflect.Descriptor instead.
func (*CheckBookOnlineAddressResult) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{21}
}

func (x *CheckBookOnlineAddressResult) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

// The params message for the GetBookingStatus
type GetBookingStatusParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetBookingStatusParams) Reset() {
	*x = GetBookingStatusParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBookingStatusParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookingStatusParams) ProtoMessage() {}

func (x *GetBookingStatusParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookingStatusParams.ProtoReflect.Descriptor instead.
func (*GetBookingStatusParams) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{22}
}

func (x *GetBookingStatusParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// The result message for the GetBookingStatus
type GetBookingStatusResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// can book online
	IsCanBookOnline bool `protobuf:"varint,1,opt,name=is_can_book_online,json=isCanBookOnline,proto3" json:"is_can_book_online,omitempty"`
}

func (x *GetBookingStatusResult) Reset() {
	*x = GetBookingStatusResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBookingStatusResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookingStatusResult) ProtoMessage() {}

func (x *GetBookingStatusResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookingStatusResult.ProtoReflect.Descriptor instead.
func (*GetBookingStatusResult) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{23}
}

func (x *GetBookingStatusResult) GetIsCanBookOnline() bool {
	if x != nil {
		return x.IsCanBookOnline
	}
	return false
}

// The params message for the GetEstimatedPaymentInfo
type GetEstimatedPaymentInfoParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pet selected service list
	SelectedPetServices []*v11.SelectedPetServiceDef `protobuf:"bytes,2,rep,name=selected_pet_services,json=selectedPetServices,proto3" json:"selected_pet_services,omitempty"`
	// discount code
	DiscountCode *string `protobuf:"bytes,3,opt,name=discount_code,json=discountCode,proto3,oneof" json:"discount_code,omitempty"`
}

func (x *GetEstimatedPaymentInfoParams) Reset() {
	*x = GetEstimatedPaymentInfoParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEstimatedPaymentInfoParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEstimatedPaymentInfoParams) ProtoMessage() {}

func (x *GetEstimatedPaymentInfoParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEstimatedPaymentInfoParams.ProtoReflect.Descriptor instead.
func (*GetEstimatedPaymentInfoParams) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{24}
}

func (x *GetEstimatedPaymentInfoParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetEstimatedPaymentInfoParams) GetSelectedPetServices() []*v11.SelectedPetServiceDef {
	if x != nil {
		return x.SelectedPetServices
	}
	return nil
}

func (x *GetEstimatedPaymentInfoParams) GetDiscountCode() string {
	if x != nil && x.DiscountCode != nil {
		return *x.DiscountCode
	}
	return ""
}

// The result message for the GetEstimatedPaymentInfo
type GetEstimatedPaymentInfoResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// book online payment
	PaymentType v11.PaymentType `protobuf:"varint,1,opt,name=payment_type,json=paymentType,proto3,enum=moego.models.online_booking.v1.PaymentType" json:"payment_type,omitempty"`
	// prepay type
	PrepayType v11.PrepayType `protobuf:"varint,2,opt,name=prepay_type,json=prepayType,proto3,enum=moego.models.online_booking.v1.PrepayType" json:"prepay_type,omitempty"`
	// subtotal, service total + service charge
	Subtotal float64 `protobuf:"fixed64,3,opt,name=subtotal,proto3" json:"subtotal,omitempty"`
	// tax and fees, tax + (convenience fee + booking fee)
	TaxAndFees float64 `protobuf:"fixed64,4,opt,name=tax_and_fees,json=taxAndFees,proto3" json:"tax_and_fees,omitempty"`
	// discount amount, all payment type support
	Discount float64 `protobuf:"fixed64,6,opt,name=discount,proto3" json:"discount,omitempty"`
	// total amount, subtotal + tax + fees - discount, exclude tip
	Total float64 `protobuf:"fixed64,7,opt,name=total,proto3" json:"total,omitempty"`
	// deposit amount, prepayment deposit only
	Deposit *float64 `protobuf:"fixed64,8,opt,name=deposit,proto3,oneof" json:"deposit,omitempty"`
	// subtotal item
	SubtotalItem *GetEstimatedPaymentInfoResult_SubtotalItem `protobuf:"bytes,9,opt,name=subtotal_item,json=subtotalItem,proto3" json:"subtotal_item,omitempty"`
}

func (x *GetEstimatedPaymentInfoResult) Reset() {
	*x = GetEstimatedPaymentInfoResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEstimatedPaymentInfoResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEstimatedPaymentInfoResult) ProtoMessage() {}

func (x *GetEstimatedPaymentInfoResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEstimatedPaymentInfoResult.ProtoReflect.Descriptor instead.
func (*GetEstimatedPaymentInfoResult) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{25}
}

func (x *GetEstimatedPaymentInfoResult) GetPaymentType() v11.PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return v11.PaymentType(0)
}

func (x *GetEstimatedPaymentInfoResult) GetPrepayType() v11.PrepayType {
	if x != nil {
		return x.PrepayType
	}
	return v11.PrepayType(0)
}

func (x *GetEstimatedPaymentInfoResult) GetSubtotal() float64 {
	if x != nil {
		return x.Subtotal
	}
	return 0
}

func (x *GetEstimatedPaymentInfoResult) GetTaxAndFees() float64 {
	if x != nil {
		return x.TaxAndFees
	}
	return 0
}

func (x *GetEstimatedPaymentInfoResult) GetDiscount() float64 {
	if x != nil {
		return x.Discount
	}
	return 0
}

func (x *GetEstimatedPaymentInfoResult) GetTotal() float64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetEstimatedPaymentInfoResult) GetDeposit() float64 {
	if x != nil && x.Deposit != nil {
		return *x.Deposit
	}
	return 0
}

func (x *GetEstimatedPaymentInfoResult) GetSubtotalItem() *GetEstimatedPaymentInfoResult_SubtotalItem {
	if x != nil {
		return x.SubtotalItem
	}
	return nil
}

// AddressExtraInfo is the extra info for address.
type ListBookOnlineAddressesResult_AddressExtraInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// address id
	AddressId int64 `protobuf:"varint,1,opt,name=address_id,json=addressId,proto3" json:"address_id,omitempty"`
	// require address & out of service area: false
	// no require address: true
	IsAvailable bool `protobuf:"varint,2,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
}

func (x *ListBookOnlineAddressesResult_AddressExtraInfo) Reset() {
	*x = ListBookOnlineAddressesResult_AddressExtraInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBookOnlineAddressesResult_AddressExtraInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBookOnlineAddressesResult_AddressExtraInfo) ProtoMessage() {}

func (x *ListBookOnlineAddressesResult_AddressExtraInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBookOnlineAddressesResult_AddressExtraInfo.ProtoReflect.Descriptor instead.
func (*ListBookOnlineAddressesResult_AddressExtraInfo) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{19, 0}
}

func (x *ListBookOnlineAddressesResult_AddressExtraInfo) GetAddressId() int64 {
	if x != nil {
		return x.AddressId
	}
	return 0
}

func (x *ListBookOnlineAddressesResult_AddressExtraInfo) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

// subtotal item. service & add-ons, service charges
type GetEstimatedPaymentInfoResult_SubtotalItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Service items
	ServiceItems []*GetEstimatedPaymentInfoResult_ServiceItem `protobuf:"bytes,1,rep,name=service_items,json=serviceItems,proto3" json:"service_items,omitempty"`
	// Service charge items
	ServiceChargeItems []*GetEstimatedPaymentInfoResult_ServiceChargeItem `protobuf:"bytes,2,rep,name=service_charge_items,json=serviceChargeItems,proto3" json:"service_charge_items,omitempty"`
}

func (x *GetEstimatedPaymentInfoResult_SubtotalItem) Reset() {
	*x = GetEstimatedPaymentInfoResult_SubtotalItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEstimatedPaymentInfoResult_SubtotalItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEstimatedPaymentInfoResult_SubtotalItem) ProtoMessage() {}

func (x *GetEstimatedPaymentInfoResult_SubtotalItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEstimatedPaymentInfoResult_SubtotalItem.ProtoReflect.Descriptor instead.
func (*GetEstimatedPaymentInfoResult_SubtotalItem) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{25, 0}
}

func (x *GetEstimatedPaymentInfoResult_SubtotalItem) GetServiceItems() []*GetEstimatedPaymentInfoResult_ServiceItem {
	if x != nil {
		return x.ServiceItems
	}
	return nil
}

func (x *GetEstimatedPaymentInfoResult_SubtotalItem) GetServiceChargeItems() []*GetEstimatedPaymentInfoResult_ServiceChargeItem {
	if x != nil {
		return x.ServiceChargeItems
	}
	return nil
}

// service item. service & add-ons
type GetEstimatedPaymentInfoResult_ServiceItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// service name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// quantity
	Quantity int32 `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// unit price
	UnitPrice float64 `protobuf:"fixed64,4,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
}

func (x *GetEstimatedPaymentInfoResult_ServiceItem) Reset() {
	*x = GetEstimatedPaymentInfoResult_ServiceItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEstimatedPaymentInfoResult_ServiceItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEstimatedPaymentInfoResult_ServiceItem) ProtoMessage() {}

func (x *GetEstimatedPaymentInfoResult_ServiceItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEstimatedPaymentInfoResult_ServiceItem.ProtoReflect.Descriptor instead.
func (*GetEstimatedPaymentInfoResult_ServiceItem) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{25, 1}
}

func (x *GetEstimatedPaymentInfoResult_ServiceItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetEstimatedPaymentInfoResult_ServiceItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetEstimatedPaymentInfoResult_ServiceItem) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *GetEstimatedPaymentInfoResult_ServiceItem) GetUnitPrice() float64 {
	if x != nil {
		return x.UnitPrice
	}
	return 0
}

// service charge item.
type GetEstimatedPaymentInfoResult_ServiceChargeItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service charge id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// service charge name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// quantity
	Quantity int32 `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// unit price
	UnitPrice float64 `protobuf:"fixed64,4,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
}

func (x *GetEstimatedPaymentInfoResult_ServiceChargeItem) Reset() {
	*x = GetEstimatedPaymentInfoResult_ServiceChargeItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEstimatedPaymentInfoResult_ServiceChargeItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEstimatedPaymentInfoResult_ServiceChargeItem) ProtoMessage() {}

func (x *GetEstimatedPaymentInfoResult_ServiceChargeItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEstimatedPaymentInfoResult_ServiceChargeItem.ProtoReflect.Descriptor instead.
func (*GetEstimatedPaymentInfoResult_ServiceChargeItem) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP(), []int{25, 2}
}

func (x *GetEstimatedPaymentInfoResult_ServiceChargeItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetEstimatedPaymentInfoResult_ServiceChargeItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetEstimatedPaymentInfoResult_ServiceChargeItem) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *GetEstimatedPaymentInfoResult_ServiceChargeItem) GetUnitPrice() float64 {
	if x != nil {
		return x.UnitPrice
	}
	return 0
}

var File_moego_client_grooming_v1_online_booking_api_proto protoreflect.FileDescriptor

var file_moego_client_grooming_v1_online_booking_api_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x18, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6c, 0x61, 0x74, 0x6c, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x48, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x46, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0xe9,
	0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50,
	0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6a,
	0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x65, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x73, 0x12, 0x5e, 0x0a, 0x0e, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x74,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x44, 0x65, 0x66, 0x52, 0x0d, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x73, 0x22, 0x77, 0x0a, 0x1e, 0x47, 0x65,
	0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x12, 0xfa, 0x42, 0x0f, 0x92, 0x01, 0x0c, 0x08,
	0x01, 0x10, 0x0a, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x70, 0x65, 0x74,
	0x49, 0x64, 0x73, 0x22, 0x9d, 0x03, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x53, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x53, 0x0a, 0x08,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x12, 0x6c, 0x0a, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x10, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12,
	0x62, 0x0a, 0x12, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66,
	0x52, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x22, 0xb3, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x69,
	0x0a, 0x15, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x66, 0x52, 0x13, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x50, 0x65,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0x7d, 0x0a, 0x1d, 0x47, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x10, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x44, 0x65, 0x66, 0x52, 0x0f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x22, 0xf0, 0x02, 0x0a, 0x1c, 0x47, 0x65, 0x74,
	0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x69, 0x0a, 0x15, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66, 0x52, 0x13, 0x73, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x30,
	0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x08, 0x00, 0x10, 0xe8, 0x07, 0x18, 0x01,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73,
	0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64,
	0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24,
	0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x2b, 0x0a, 0x0a, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52,
	0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x7f, 0x0a, 0x1d, 0x47,
	0x65, 0x74, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x0f,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0e, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x73, 0x22, 0xb6, 0x03, 0x0a,
	0x1b, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x69, 0x0a, 0x15, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x50,
	0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66, 0x52, 0x13, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x12, 0x30, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x08, 0x00, 0x10, 0xe8,
	0x07, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x73, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d,
	0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x64, 0x61,
	0x79, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x44, 0x61, 0x79, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x64,
	0x5f, 0x6f, 0x66, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x00, 0x52, 0x0c, 0x69, 0x73, 0x45, 0x6e, 0x64, 0x4f, 0x66, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12,
	0x2b, 0x0a, 0x0a, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x09,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x18, 0x0a, 0x11,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x79, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x59, 0x0a, 0x0f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66,
	0x52, 0x0e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x73,
	0x22, 0xd0, 0x02, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x69, 0x0a, 0x15, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66,
	0x52, 0x13, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d,
	0x08, 0x00, 0x10, 0xe8, 0x07, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c,
	0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x24, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x2b, 0x0a, 0x0a, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x22, 0x85, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x13, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x6c, 0x6f, 0x74, 0x44, 0x65, 0x66, 0x52, 0x12, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x73, 0x22, 0x40, 0x0a, 0x14, 0x43,
	0x61, 0x6e, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0xf0, 0x03,
	0x0a, 0x15, 0x43, 0x61, 0x6e, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x63, 0x61,
	0x6e, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x43, 0x61, 0x6e, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x69, 0x73, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x17, 0x69, 0x73, 0x5f, 0x61, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x69, 0x73, 0x41, 0x63, 0x63, 0x65,
	0x70, 0x74, 0x65, 0x64, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39,
	0x0a, 0x19, 0x69, 0x73, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x16, 0x69, 0x73, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x4f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x32, 0x0a, 0x16, 0x69, 0x73, 0x5f,
	0x6f, 0x75, 0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61,
	0x72, 0x65, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x4f, 0x75, 0x74,
	0x4f, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x72, 0x65, 0x61, 0x12, 0x33, 0x0a,
	0x16, 0x69, 0x73, 0x5f, 0x68, 0x61, 0x73, 0x5f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x69,
	0x73, 0x48, 0x61, 0x73, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x4e,
	0x65, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3b, 0x0a, 0x1a, 0x69, 0x73,
	0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x69, 0x66,
	0x79, 0x5f, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17,
	0x69, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x69, 0x66,
	0x79, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x12, 0x37, 0x0a, 0x18, 0x69, 0x73, 0x5f, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x69, 0x73, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x45, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x22, 0xf3, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x57, 0x6f, 0x72,
	0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x30, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x08, 0x00, 0x10,
	0xe8, 0x07, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32,
	0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64,
	0x7b, 0x32, 0x7d, 0x24, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d,
	0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x22, 0x81, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x59, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x57,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44,
	0x65, 0x66, 0x52, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x22, 0x47, 0x0a, 0x1b, 0x47, 0x65,
	0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x22, 0x92, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x4f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x49, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72,
	0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x49, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x22, 0xbe, 0x02, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b,
	0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5c, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x56, 0x69, 0x65, 0x77, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x65, 0x73, 0x12, 0x69, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x54,
	0x0a, 0x10, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x22, 0xaa, 0x01, 0x0a, 0x1c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x6f,
	0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x3d, 0x0a, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x12, 0x21,
	0x0a, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x0a, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64,
	0x65, 0x22, 0x41, 0x0a, 0x1c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x22, 0x42, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x45, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x2b, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x63, 0x61, 0x6e, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f,
	0x69, 0x73, 0x43, 0x61, 0x6e, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x22,
	0x8c, 0x02, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x7c, 0x0a, 0x15, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65,
	0x66, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0x0a, 0x22, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x13, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x50, 0x65,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x31, 0x0a, 0x0d, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x14, 0x48, 0x00, 0x52, 0x0c, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x9c,
	0x07, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x4e, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x4b, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x73, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x08, 0x73, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x20, 0x0a, 0x0c, 0x74, 0x61, 0x78,
	0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x66, 0x65, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0a, 0x74, 0x61, 0x78, 0x41, 0x6e, 0x64, 0x46, 0x65, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1d, 0x0a,
	0x07, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00,
	0x52, 0x07, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x88, 0x01, 0x01, 0x12, 0x69, 0x0a, 0x0d,
	0x73, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x75, 0x62,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x1a, 0xf5, 0x01, 0x0a, 0x0c, 0x53, 0x75, 0x62, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x68, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x12, 0x7b, 0x0a, 0x14, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x12, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x1a,
	0x6c, 0x0a, 0x0b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x09, 0x75, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x1a, 0x72, 0x0a,
	0x11, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x75, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x32, 0x85, 0x0e,
	0x0a, 0x0e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x82, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x50, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x50, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8e, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x88, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x46, 0x69, 0x72, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x85, 0x01, 0x0a,
	0x14, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x12, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x6c, 0x6f, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x70, 0x0a, 0x0d,
	0x43, 0x61, 0x6e, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e, 0x42, 0x6f, 0x6f, 0x6b,
	0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e, 0x42, 0x6f, 0x6f, 0x6b,
	0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9d,
	0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x57, 0x6f, 0x72, 0x6b, 0x69,
	0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x85,
	0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f,
	0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8b, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x42,
	0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x65, 0x73, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x4f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x88, 0x01, 0x0a, 0x16, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x6f,
	0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x76, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8b, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x45,
	0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x81, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5b, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f,
	0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_client_grooming_v1_online_booking_api_proto_rawDescOnce sync.Once
	file_moego_client_grooming_v1_online_booking_api_proto_rawDescData = file_moego_client_grooming_v1_online_booking_api_proto_rawDesc
)

func file_moego_client_grooming_v1_online_booking_api_proto_rawDescGZIP() []byte {
	file_moego_client_grooming_v1_online_booking_api_proto_rawDescOnce.Do(func() {
		file_moego_client_grooming_v1_online_booking_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_grooming_v1_online_booking_api_proto_rawDescData)
	})
	return file_moego_client_grooming_v1_online_booking_api_proto_rawDescData
}

var file_moego_client_grooming_v1_online_booking_api_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_moego_client_grooming_v1_online_booking_api_proto_goTypes = []interface{}{
	(*GetAvailablePetListRequest)(nil),                      // 0: moego.client.grooming.v1.GetAvailablePetListRequest
	(*GetAvailablePetListResponse)(nil),                     // 1: moego.client.grooming.v1.GetAvailablePetListResponse
	(*GetAvailableServiceListRequest)(nil),                  // 2: moego.client.grooming.v1.GetAvailableServiceListRequest
	(*GetAvailableServiceListResponse)(nil),                 // 3: moego.client.grooming.v1.GetAvailableServiceListResponse
	(*GetAvailableStaffListRequest)(nil),                    // 4: moego.client.grooming.v1.GetAvailableStaffListRequest
	(*GetAvailableStaffListResponse)(nil),                   // 5: moego.client.grooming.v1.GetAvailableStaffListResponse
	(*GetFirstAvailableDateRequest)(nil),                    // 6: moego.client.grooming.v1.GetFirstAvailableDateRequest
	(*GetFirstAvailableDateResponse)(nil),                   // 7: moego.client.grooming.v1.GetFirstAvailableDateResponse
	(*GetAvailableDateListRequest)(nil),                     // 8: moego.client.grooming.v1.GetAvailableDateListRequest
	(*GetAvailableDateListResponse)(nil),                    // 9: moego.client.grooming.v1.GetAvailableDateListResponse
	(*GetAvailableTimeslotRequest)(nil),                     // 10: moego.client.grooming.v1.GetAvailableTimeslotRequest
	(*GetAvailableTimeslotResponse)(nil),                    // 11: moego.client.grooming.v1.GetAvailableTimeslotResponse
	(*CanBookOnlineRequest)(nil),                            // 12: moego.client.grooming.v1.CanBookOnlineRequest
	(*CanBookOnlineResponse)(nil),                           // 13: moego.client.grooming.v1.CanBookOnlineResponse
	(*GetStaffWorkingHourRangeListRequest)(nil),             // 14: moego.client.grooming.v1.GetStaffWorkingHourRangeListRequest
	(*GetStaffWorkingHourRangeListResponse)(nil),            // 15: moego.client.grooming.v1.GetStaffWorkingHourRangeListResponse
	(*GetBookOnlineAddressRequest)(nil),                     // 16: moego.client.grooming.v1.GetBookOnlineAddressRequest
	(*GetBookOnlineAddressResponse)(nil),                    // 17: moego.client.grooming.v1.GetBookOnlineAddressResponse
	(*ListBookOnlineAddressesParams)(nil),                   // 18: moego.client.grooming.v1.ListBookOnlineAddressesParams
	(*ListBookOnlineAddressesResult)(nil),                   // 19: moego.client.grooming.v1.ListBookOnlineAddressesResult
	(*CheckBookOnlineAddressParams)(nil),                    // 20: moego.client.grooming.v1.CheckBookOnlineAddressParams
	(*CheckBookOnlineAddressResult)(nil),                    // 21: moego.client.grooming.v1.CheckBookOnlineAddressResult
	(*GetBookingStatusParams)(nil),                          // 22: moego.client.grooming.v1.GetBookingStatusParams
	(*GetBookingStatusResult)(nil),                          // 23: moego.client.grooming.v1.GetBookingStatusResult
	(*GetEstimatedPaymentInfoParams)(nil),                   // 24: moego.client.grooming.v1.GetEstimatedPaymentInfoParams
	(*GetEstimatedPaymentInfoResult)(nil),                   // 25: moego.client.grooming.v1.GetEstimatedPaymentInfoResult
	(*ListBookOnlineAddressesResult_AddressExtraInfo)(nil),  // 26: moego.client.grooming.v1.ListBookOnlineAddressesResult.AddressExtraInfo
	(*GetEstimatedPaymentInfoResult_SubtotalItem)(nil),      // 27: moego.client.grooming.v1.GetEstimatedPaymentInfoResult.SubtotalItem
	(*GetEstimatedPaymentInfoResult_ServiceItem)(nil),       // 28: moego.client.grooming.v1.GetEstimatedPaymentInfoResult.ServiceItem
	(*GetEstimatedPaymentInfoResult_ServiceChargeItem)(nil), // 29: moego.client.grooming.v1.GetEstimatedPaymentInfoResult.ServiceChargeItem
	(*v1.BusinessCustomerPetModelClientView)(nil),           // 30: moego.models.business_customer.v1.BusinessCustomerPetModelClientView
	(*v11.AvailablePetMetadataDef)(nil),                     // 31: moego.models.online_booking.v1.AvailablePetMetadataDef
	(*v12.CategorySelectServiceView)(nil),                   // 32: moego.models.grooming.v1.CategorySelectServiceView
	(*v12.ServiceModelSelectServiceView)(nil),               // 33: moego.models.grooming.v1.ServiceModelSelectServiceView
	(*v12.CustomerServiceModelSelectServiceView)(nil),       // 34: moego.models.grooming.v1.CustomerServiceModelSelectServiceView
	(*v11.AvailableServiceDef)(nil),                         // 35: moego.models.online_booking.v1.AvailableServiceDef
	(*v11.SelectedPetServiceDef)(nil),                       // 36: moego.models.online_booking.v1.SelectedPetServiceDef
	(*v11.AvailableStaffDef)(nil),                           // 37: moego.models.online_booking.v1.AvailableStaffDef
	(*v11.AvailableStaffDateDef)(nil),                       // 38: moego.models.online_booking.v1.AvailableStaffDateDef
	(*v11.AvailableDateDef)(nil),                            // 39: moego.models.online_booking.v1.AvailableDateDef
	(*v11.AvailableTimeslotDef)(nil),                        // 40: moego.models.online_booking.v1.AvailableTimeslotDef
	(*v11.AvailableStaffWorkingHourRangeDef)(nil),           // 41: moego.models.online_booking.v1.AvailableStaffWorkingHourRangeDef
	(*v1.BusinessCustomerAddressModelClientView)(nil),       // 42: moego.models.business_customer.v1.BusinessCustomerAddressModelClientView
	(*v1.BusinessCustomerAddressView)(nil),                  // 43: moego.models.business_customer.v1.BusinessCustomerAddressView
	(*latlng.LatLng)(nil),                                   // 44: google.type.LatLng
	(v11.PaymentType)(0),                                    // 45: moego.models.online_booking.v1.PaymentType
	(v11.PrepayType)(0),                                     // 46: moego.models.online_booking.v1.PrepayType
}
var file_moego_client_grooming_v1_online_booking_api_proto_depIdxs = []int32{
	30, // 0: moego.client.grooming.v1.GetAvailablePetListResponse.business_pets:type_name -> moego.models.business_customer.v1.BusinessCustomerPetModelClientView
	31, // 1: moego.client.grooming.v1.GetAvailablePetListResponse.available_pets:type_name -> moego.models.online_booking.v1.AvailablePetMetadataDef
	32, // 2: moego.client.grooming.v1.GetAvailableServiceListResponse.categories:type_name -> moego.models.grooming.v1.CategorySelectServiceView
	33, // 3: moego.client.grooming.v1.GetAvailableServiceListResponse.services:type_name -> moego.models.grooming.v1.ServiceModelSelectServiceView
	34, // 4: moego.client.grooming.v1.GetAvailableServiceListResponse.customer_services:type_name -> moego.models.grooming.v1.CustomerServiceModelSelectServiceView
	35, // 5: moego.client.grooming.v1.GetAvailableServiceListResponse.available_services:type_name -> moego.models.online_booking.v1.AvailableServiceDef
	36, // 6: moego.client.grooming.v1.GetAvailableStaffListRequest.selected_pet_services:type_name -> moego.models.online_booking.v1.SelectedPetServiceDef
	37, // 7: moego.client.grooming.v1.GetAvailableStaffListResponse.available_staffs:type_name -> moego.models.online_booking.v1.AvailableStaffDef
	36, // 8: moego.client.grooming.v1.GetFirstAvailableDateRequest.selected_pet_services:type_name -> moego.models.online_booking.v1.SelectedPetServiceDef
	38, // 9: moego.client.grooming.v1.GetFirstAvailableDateResponse.available_dates:type_name -> moego.models.online_booking.v1.AvailableStaffDateDef
	36, // 10: moego.client.grooming.v1.GetAvailableDateListRequest.selected_pet_services:type_name -> moego.models.online_booking.v1.SelectedPetServiceDef
	39, // 11: moego.client.grooming.v1.GetAvailableDateListResponse.available_dates:type_name -> moego.models.online_booking.v1.AvailableDateDef
	36, // 12: moego.client.grooming.v1.GetAvailableTimeslotRequest.selected_pet_services:type_name -> moego.models.online_booking.v1.SelectedPetServiceDef
	40, // 13: moego.client.grooming.v1.GetAvailableTimeslotResponse.available_timeslots:type_name -> moego.models.online_booking.v1.AvailableTimeslotDef
	41, // 14: moego.client.grooming.v1.GetStaffWorkingHourRangeListResponse.staffs:type_name -> moego.models.online_booking.v1.AvailableStaffWorkingHourRangeDef
	42, // 15: moego.client.grooming.v1.GetBookOnlineAddressResponse.primary_address:type_name -> moego.models.business_customer.v1.BusinessCustomerAddressModelClientView
	43, // 16: moego.client.grooming.v1.ListBookOnlineAddressesResult.addresses:type_name -> moego.models.business_customer.v1.BusinessCustomerAddressView
	26, // 17: moego.client.grooming.v1.ListBookOnlineAddressesResult.extra_infos:type_name -> moego.client.grooming.v1.ListBookOnlineAddressesResult.AddressExtraInfo
	44, // 18: moego.client.grooming.v1.CheckBookOnlineAddressParams.coordinate:type_name -> google.type.LatLng
	36, // 19: moego.client.grooming.v1.GetEstimatedPaymentInfoParams.selected_pet_services:type_name -> moego.models.online_booking.v1.SelectedPetServiceDef
	45, // 20: moego.client.grooming.v1.GetEstimatedPaymentInfoResult.payment_type:type_name -> moego.models.online_booking.v1.PaymentType
	46, // 21: moego.client.grooming.v1.GetEstimatedPaymentInfoResult.prepay_type:type_name -> moego.models.online_booking.v1.PrepayType
	27, // 22: moego.client.grooming.v1.GetEstimatedPaymentInfoResult.subtotal_item:type_name -> moego.client.grooming.v1.GetEstimatedPaymentInfoResult.SubtotalItem
	28, // 23: moego.client.grooming.v1.GetEstimatedPaymentInfoResult.SubtotalItem.service_items:type_name -> moego.client.grooming.v1.GetEstimatedPaymentInfoResult.ServiceItem
	29, // 24: moego.client.grooming.v1.GetEstimatedPaymentInfoResult.SubtotalItem.service_charge_items:type_name -> moego.client.grooming.v1.GetEstimatedPaymentInfoResult.ServiceChargeItem
	0,  // 25: moego.client.grooming.v1.BookingService.GetAvailablePetList:input_type -> moego.client.grooming.v1.GetAvailablePetListRequest
	2,  // 26: moego.client.grooming.v1.BookingService.GetAvailableServiceList:input_type -> moego.client.grooming.v1.GetAvailableServiceListRequest
	4,  // 27: moego.client.grooming.v1.BookingService.GetAvailableStaffList:input_type -> moego.client.grooming.v1.GetAvailableStaffListRequest
	6,  // 28: moego.client.grooming.v1.BookingService.GetFirstAvailableDate:input_type -> moego.client.grooming.v1.GetFirstAvailableDateRequest
	8,  // 29: moego.client.grooming.v1.BookingService.GetAvailableDateList:input_type -> moego.client.grooming.v1.GetAvailableDateListRequest
	10, // 30: moego.client.grooming.v1.BookingService.GetAvailableTimeslot:input_type -> moego.client.grooming.v1.GetAvailableTimeslotRequest
	12, // 31: moego.client.grooming.v1.BookingService.CanBookOnline:input_type -> moego.client.grooming.v1.CanBookOnlineRequest
	14, // 32: moego.client.grooming.v1.BookingService.GetStaffWorkingHourRangeList:input_type -> moego.client.grooming.v1.GetStaffWorkingHourRangeListRequest
	16, // 33: moego.client.grooming.v1.BookingService.GetBookOnlineAddress:input_type -> moego.client.grooming.v1.GetBookOnlineAddressRequest
	18, // 34: moego.client.grooming.v1.BookingService.ListBookOnlineAddresses:input_type -> moego.client.grooming.v1.ListBookOnlineAddressesParams
	20, // 35: moego.client.grooming.v1.BookingService.CheckBookOnlineAddress:input_type -> moego.client.grooming.v1.CheckBookOnlineAddressParams
	22, // 36: moego.client.grooming.v1.BookingService.GetBookingStatus:input_type -> moego.client.grooming.v1.GetBookingStatusParams
	24, // 37: moego.client.grooming.v1.BookingService.GetEstimatedPaymentInfo:input_type -> moego.client.grooming.v1.GetEstimatedPaymentInfoParams
	1,  // 38: moego.client.grooming.v1.BookingService.GetAvailablePetList:output_type -> moego.client.grooming.v1.GetAvailablePetListResponse
	3,  // 39: moego.client.grooming.v1.BookingService.GetAvailableServiceList:output_type -> moego.client.grooming.v1.GetAvailableServiceListResponse
	5,  // 40: moego.client.grooming.v1.BookingService.GetAvailableStaffList:output_type -> moego.client.grooming.v1.GetAvailableStaffListResponse
	7,  // 41: moego.client.grooming.v1.BookingService.GetFirstAvailableDate:output_type -> moego.client.grooming.v1.GetFirstAvailableDateResponse
	9,  // 42: moego.client.grooming.v1.BookingService.GetAvailableDateList:output_type -> moego.client.grooming.v1.GetAvailableDateListResponse
	11, // 43: moego.client.grooming.v1.BookingService.GetAvailableTimeslot:output_type -> moego.client.grooming.v1.GetAvailableTimeslotResponse
	13, // 44: moego.client.grooming.v1.BookingService.CanBookOnline:output_type -> moego.client.grooming.v1.CanBookOnlineResponse
	15, // 45: moego.client.grooming.v1.BookingService.GetStaffWorkingHourRangeList:output_type -> moego.client.grooming.v1.GetStaffWorkingHourRangeListResponse
	17, // 46: moego.client.grooming.v1.BookingService.GetBookOnlineAddress:output_type -> moego.client.grooming.v1.GetBookOnlineAddressResponse
	19, // 47: moego.client.grooming.v1.BookingService.ListBookOnlineAddresses:output_type -> moego.client.grooming.v1.ListBookOnlineAddressesResult
	21, // 48: moego.client.grooming.v1.BookingService.CheckBookOnlineAddress:output_type -> moego.client.grooming.v1.CheckBookOnlineAddressResult
	23, // 49: moego.client.grooming.v1.BookingService.GetBookingStatus:output_type -> moego.client.grooming.v1.GetBookingStatusResult
	25, // 50: moego.client.grooming.v1.BookingService.GetEstimatedPaymentInfo:output_type -> moego.client.grooming.v1.GetEstimatedPaymentInfoResult
	38, // [38:51] is the sub-list for method output_type
	25, // [25:38] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_moego_client_grooming_v1_online_booking_api_proto_init() }
func file_moego_client_grooming_v1_online_booking_api_proto_init() {
	if File_moego_client_grooming_v1_online_booking_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailablePetListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailablePetListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableServiceListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableServiceListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableStaffListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableStaffListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFirstAvailableDateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFirstAvailableDateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableDateListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableDateListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableTimeslotRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableTimeslotResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CanBookOnlineRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CanBookOnlineResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffWorkingHourRangeListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStaffWorkingHourRangeListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBookOnlineAddressRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBookOnlineAddressResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBookOnlineAddressesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBookOnlineAddressesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckBookOnlineAddressParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckBookOnlineAddressResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBookingStatusParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBookingStatusResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEstimatedPaymentInfoParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEstimatedPaymentInfoResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBookOnlineAddressesResult_AddressExtraInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEstimatedPaymentInfoResult_SubtotalItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEstimatedPaymentInfoResult_ServiceItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEstimatedPaymentInfoResult_ServiceChargeItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*GetAvailableDateListRequest_SearchDays)(nil),
		(*GetAvailableDateListRequest_IsEndOfMonth)(nil),
	}
	file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[24].OneofWrappers = []interface{}{}
	file_moego_client_grooming_v1_online_booking_api_proto_msgTypes[25].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_grooming_v1_online_booking_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_grooming_v1_online_booking_api_proto_goTypes,
		DependencyIndexes: file_moego_client_grooming_v1_online_booking_api_proto_depIdxs,
		MessageInfos:      file_moego_client_grooming_v1_online_booking_api_proto_msgTypes,
	}.Build()
	File_moego_client_grooming_v1_online_booking_api_proto = out.File
	file_moego_client_grooming_v1_online_booking_api_proto_rawDesc = nil
	file_moego_client_grooming_v1_online_booking_api_proto_goTypes = nil
	file_moego_client_grooming_v1_online_booking_api_proto_depIdxs = nil
}
