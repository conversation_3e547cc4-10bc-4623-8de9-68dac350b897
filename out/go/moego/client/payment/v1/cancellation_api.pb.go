// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/payment/v1/cancellation_api.proto

package paymentapipb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The params message for GetCancellationFee
type GetCancellationFeeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Cancelled appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *GetCancellationFeeParams) Reset() {
	*x = GetCancellationFeeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_payment_v1_cancellation_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCancellationFeeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCancellationFeeParams) ProtoMessage() {}

func (x *GetCancellationFeeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_payment_v1_cancellation_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCancellationFeeParams.ProtoReflect.Descriptor instead.
func (*GetCancellationFeeParams) Descriptor() ([]byte, []int) {
	return file_moego_client_payment_v1_cancellation_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetCancellationFeeParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// The result message for GetCancellationFee
type GetCancellationFeeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// cancellation fee
	CancellationFee float64 `protobuf:"fixed64,1,opt,name=cancellation_fee,json=cancellationFee,proto3" json:"cancellation_fee,omitempty"`
	// convenience fee
	TaxAndFees float64 `protobuf:"fixed64,2,opt,name=tax_and_fees,json=taxAndFees,proto3" json:"tax_and_fees,omitempty"`
	// total fee
	Total float64 `protobuf:"fixed64,3,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *GetCancellationFeeResult) Reset() {
	*x = GetCancellationFeeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_payment_v1_cancellation_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCancellationFeeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCancellationFeeResult) ProtoMessage() {}

func (x *GetCancellationFeeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_payment_v1_cancellation_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCancellationFeeResult.ProtoReflect.Descriptor instead.
func (*GetCancellationFeeResult) Descriptor() ([]byte, []int) {
	return file_moego_client_payment_v1_cancellation_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetCancellationFeeResult) GetCancellationFee() float64 {
	if x != nil {
		return x.CancellationFee
	}
	return 0
}

func (x *GetCancellationFeeResult) GetTaxAndFees() float64 {
	if x != nil {
		return x.TaxAndFees
	}
	return 0
}

func (x *GetCancellationFeeResult) GetTotal() float64 {
	if x != nil {
		return x.Total
	}
	return 0
}

var File_moego_client_payment_v1_cancellation_api_proto protoreflect.FileDescriptor

var file_moego_client_payment_v1_cancellation_api_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x4a, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e,
	0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x7d,
	0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x46, 0x65, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x46, 0x65, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6e, 0x64,
	0x5f, 0x66, 0x65, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x74, 0x61, 0x78,
	0x41, 0x6e, 0x64, 0x46, 0x65, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x32, 0x91, 0x01,
	0x0a, 0x13, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7a, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x42, 0x7e, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x59, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_payment_v1_cancellation_api_proto_rawDescOnce sync.Once
	file_moego_client_payment_v1_cancellation_api_proto_rawDescData = file_moego_client_payment_v1_cancellation_api_proto_rawDesc
)

func file_moego_client_payment_v1_cancellation_api_proto_rawDescGZIP() []byte {
	file_moego_client_payment_v1_cancellation_api_proto_rawDescOnce.Do(func() {
		file_moego_client_payment_v1_cancellation_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_payment_v1_cancellation_api_proto_rawDescData)
	})
	return file_moego_client_payment_v1_cancellation_api_proto_rawDescData
}

var file_moego_client_payment_v1_cancellation_api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_client_payment_v1_cancellation_api_proto_goTypes = []interface{}{
	(*GetCancellationFeeParams)(nil), // 0: moego.client.payment.v1.GetCancellationFeeParams
	(*GetCancellationFeeResult)(nil), // 1: moego.client.payment.v1.GetCancellationFeeResult
}
var file_moego_client_payment_v1_cancellation_api_proto_depIdxs = []int32{
	0, // 0: moego.client.payment.v1.CancellationService.GetCancellationFee:input_type -> moego.client.payment.v1.GetCancellationFeeParams
	1, // 1: moego.client.payment.v1.CancellationService.GetCancellationFee:output_type -> moego.client.payment.v1.GetCancellationFeeResult
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_client_payment_v1_cancellation_api_proto_init() }
func file_moego_client_payment_v1_cancellation_api_proto_init() {
	if File_moego_client_payment_v1_cancellation_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_payment_v1_cancellation_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCancellationFeeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_payment_v1_cancellation_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCancellationFeeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_payment_v1_cancellation_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_payment_v1_cancellation_api_proto_goTypes,
		DependencyIndexes: file_moego_client_payment_v1_cancellation_api_proto_depIdxs,
		MessageInfos:      file_moego_client_payment_v1_cancellation_api_proto_msgTypes,
	}.Build()
	File_moego_client_payment_v1_cancellation_api_proto = out.File
	file_moego_client_payment_v1_cancellation_api_proto_rawDesc = nil
	file_moego_client_payment_v1_cancellation_api_proto_goTypes = nil
	file_moego_client_payment_v1_cancellation_api_proto_depIdxs = nil
}
