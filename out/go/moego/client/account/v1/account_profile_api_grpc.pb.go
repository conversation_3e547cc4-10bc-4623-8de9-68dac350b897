// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/account/v1/account_profile_api.proto

package accountapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AccountProfileServiceClient is the client API for AccountProfileService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccountProfileServiceClient interface {
	// Get account profile.
	GetAccountProfile(ctx context.Context, in *GetAccountProfileParams, opts ...grpc.CallOption) (*GetAccountProfileResult, error)
	// Get account link status.
	GetAccountLinkStatus(ctx context.Context, in *GetAccountLinkStatusParams, opts ...grpc.CallOption) (*GetAccountLinkStatusResult, error)
	// Link account to the branded client.
	// If the client is not existed, create and link it, and return CREATED_NEW_CLIENT.
	// If the client is existed, link it, and return LINKED_EXISTING_CLIENT.
	// If the client is linked, return ALREADY_LINKED.
	LinkAccountToBrandedClient(ctx context.Context, in *LinkAccountToBrandedClientParams, opts ...grpc.CallOption) (*LinkAccountToBrandedClientResult, error)
}

type accountProfileServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountProfileServiceClient(cc grpc.ClientConnInterface) AccountProfileServiceClient {
	return &accountProfileServiceClient{cc}
}

func (c *accountProfileServiceClient) GetAccountProfile(ctx context.Context, in *GetAccountProfileParams, opts ...grpc.CallOption) (*GetAccountProfileResult, error) {
	out := new(GetAccountProfileResult)
	err := c.cc.Invoke(ctx, "/moego.client.account.v1.AccountProfileService/GetAccountProfile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountProfileServiceClient) GetAccountLinkStatus(ctx context.Context, in *GetAccountLinkStatusParams, opts ...grpc.CallOption) (*GetAccountLinkStatusResult, error) {
	out := new(GetAccountLinkStatusResult)
	err := c.cc.Invoke(ctx, "/moego.client.account.v1.AccountProfileService/GetAccountLinkStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountProfileServiceClient) LinkAccountToBrandedClient(ctx context.Context, in *LinkAccountToBrandedClientParams, opts ...grpc.CallOption) (*LinkAccountToBrandedClientResult, error) {
	out := new(LinkAccountToBrandedClientResult)
	err := c.cc.Invoke(ctx, "/moego.client.account.v1.AccountProfileService/LinkAccountToBrandedClient", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountProfileServiceServer is the server API for AccountProfileService service.
// All implementations must embed UnimplementedAccountProfileServiceServer
// for forward compatibility
type AccountProfileServiceServer interface {
	// Get account profile.
	GetAccountProfile(context.Context, *GetAccountProfileParams) (*GetAccountProfileResult, error)
	// Get account link status.
	GetAccountLinkStatus(context.Context, *GetAccountLinkStatusParams) (*GetAccountLinkStatusResult, error)
	// Link account to the branded client.
	// If the client is not existed, create and link it, and return CREATED_NEW_CLIENT.
	// If the client is existed, link it, and return LINKED_EXISTING_CLIENT.
	// If the client is linked, return ALREADY_LINKED.
	LinkAccountToBrandedClient(context.Context, *LinkAccountToBrandedClientParams) (*LinkAccountToBrandedClientResult, error)
	mustEmbedUnimplementedAccountProfileServiceServer()
}

// UnimplementedAccountProfileServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAccountProfileServiceServer struct {
}

func (UnimplementedAccountProfileServiceServer) GetAccountProfile(context.Context, *GetAccountProfileParams) (*GetAccountProfileResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountProfile not implemented")
}
func (UnimplementedAccountProfileServiceServer) GetAccountLinkStatus(context.Context, *GetAccountLinkStatusParams) (*GetAccountLinkStatusResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountLinkStatus not implemented")
}
func (UnimplementedAccountProfileServiceServer) LinkAccountToBrandedClient(context.Context, *LinkAccountToBrandedClientParams) (*LinkAccountToBrandedClientResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkAccountToBrandedClient not implemented")
}
func (UnimplementedAccountProfileServiceServer) mustEmbedUnimplementedAccountProfileServiceServer() {}

// UnsafeAccountProfileServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountProfileServiceServer will
// result in compilation errors.
type UnsafeAccountProfileServiceServer interface {
	mustEmbedUnimplementedAccountProfileServiceServer()
}

func RegisterAccountProfileServiceServer(s grpc.ServiceRegistrar, srv AccountProfileServiceServer) {
	s.RegisterService(&AccountProfileService_ServiceDesc, srv)
}

func _AccountProfileService_GetAccountProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountProfileParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountProfileServiceServer).GetAccountProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.account.v1.AccountProfileService/GetAccountProfile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountProfileServiceServer).GetAccountProfile(ctx, req.(*GetAccountProfileParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountProfileService_GetAccountLinkStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountLinkStatusParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountProfileServiceServer).GetAccountLinkStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.account.v1.AccountProfileService/GetAccountLinkStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountProfileServiceServer).GetAccountLinkStatus(ctx, req.(*GetAccountLinkStatusParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountProfileService_LinkAccountToBrandedClient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LinkAccountToBrandedClientParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountProfileServiceServer).LinkAccountToBrandedClient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.account.v1.AccountProfileService/LinkAccountToBrandedClient",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountProfileServiceServer).LinkAccountToBrandedClient(ctx, req.(*LinkAccountToBrandedClientParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AccountProfileService_ServiceDesc is the grpc.ServiceDesc for AccountProfileService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AccountProfileService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.account.v1.AccountProfileService",
	HandlerType: (*AccountProfileServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAccountProfile",
			Handler:    _AccountProfileService_GetAccountProfile_Handler,
		},
		{
			MethodName: "GetAccountLinkStatus",
			Handler:    _AccountProfileService_GetAccountLinkStatus_Handler,
		},
		{
			MethodName: "LinkAccountToBrandedClient",
			Handler:    _AccountProfileService_LinkAccountToBrandedClient_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/account/v1/account_profile_api.proto",
}
