// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/account/v1/account_preference_api.proto

package accountapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AccountPreferenceServiceClient is the client API for AccountPreferenceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccountPreferenceServiceClient interface {
	// get account preference detail, contains currency, date format, time format, weight unit, distance unit etc.
	GetAccountPreference(ctx context.Context, in *GetAccountPreferenceParams, opts ...grpc.CallOption) (*GetAccountPreferenceResult, error)
}

type accountPreferenceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountPreferenceServiceClient(cc grpc.ClientConnInterface) AccountPreferenceServiceClient {
	return &accountPreferenceServiceClient{cc}
}

func (c *accountPreferenceServiceClient) GetAccountPreference(ctx context.Context, in *GetAccountPreferenceParams, opts ...grpc.CallOption) (*GetAccountPreferenceResult, error) {
	out := new(GetAccountPreferenceResult)
	err := c.cc.Invoke(ctx, "/moego.client.account.v1.AccountPreferenceService/GetAccountPreference", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountPreferenceServiceServer is the server API for AccountPreferenceService service.
// All implementations must embed UnimplementedAccountPreferenceServiceServer
// for forward compatibility
type AccountPreferenceServiceServer interface {
	// get account preference detail, contains currency, date format, time format, weight unit, distance unit etc.
	GetAccountPreference(context.Context, *GetAccountPreferenceParams) (*GetAccountPreferenceResult, error)
	mustEmbedUnimplementedAccountPreferenceServiceServer()
}

// UnimplementedAccountPreferenceServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAccountPreferenceServiceServer struct {
}

func (UnimplementedAccountPreferenceServiceServer) GetAccountPreference(context.Context, *GetAccountPreferenceParams) (*GetAccountPreferenceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountPreference not implemented")
}
func (UnimplementedAccountPreferenceServiceServer) mustEmbedUnimplementedAccountPreferenceServiceServer() {
}

// UnsafeAccountPreferenceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountPreferenceServiceServer will
// result in compilation errors.
type UnsafeAccountPreferenceServiceServer interface {
	mustEmbedUnimplementedAccountPreferenceServiceServer()
}

func RegisterAccountPreferenceServiceServer(s grpc.ServiceRegistrar, srv AccountPreferenceServiceServer) {
	s.RegisterService(&AccountPreferenceService_ServiceDesc, srv)
}

func _AccountPreferenceService_GetAccountPreference_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountPreferenceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountPreferenceServiceServer).GetAccountPreference(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.account.v1.AccountPreferenceService/GetAccountPreference",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountPreferenceServiceServer).GetAccountPreference(ctx, req.(*GetAccountPreferenceParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AccountPreferenceService_ServiceDesc is the grpc.ServiceDesc for AccountPreferenceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AccountPreferenceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.account.v1.AccountPreferenceService",
	HandlerType: (*AccountPreferenceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAccountPreference",
			Handler:    _AccountPreferenceService_GetAccountPreference_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/account/v1/account_preference_api.proto",
}
