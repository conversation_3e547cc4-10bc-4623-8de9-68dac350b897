// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/online_booking/v1/map_api.proto

package onlinebookingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// MapServiceClient is the client API for MapService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MapServiceClient interface {
	// get countries info, NOTE: if no parameters are specified, all countries will be returned.
	GetCountries(ctx context.Context, in *GetCountriesParams, opts ...grpc.CallOption) (*GetCountriesResult, error)
	// get an address
	GetAddress(ctx context.Context, in *GetAddressParams, opts ...grpc.CallOption) (*GetAddressResult, error)
	// predict addresses by term
	AutocompleteAddress(ctx context.Context, in *AutocompleteAddressParams, opts ...grpc.CallOption) (*AutocompleteAddressResult, error)
}

type mapServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMapServiceClient(cc grpc.ClientConnInterface) MapServiceClient {
	return &mapServiceClient{cc}
}

func (c *mapServiceClient) GetCountries(ctx context.Context, in *GetCountriesParams, opts ...grpc.CallOption) (*GetCountriesResult, error) {
	out := new(GetCountriesResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.MapService/GetCountries", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mapServiceClient) GetAddress(ctx context.Context, in *GetAddressParams, opts ...grpc.CallOption) (*GetAddressResult, error) {
	out := new(GetAddressResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.MapService/GetAddress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mapServiceClient) AutocompleteAddress(ctx context.Context, in *AutocompleteAddressParams, opts ...grpc.CallOption) (*AutocompleteAddressResult, error) {
	out := new(AutocompleteAddressResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.MapService/AutocompleteAddress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MapServiceServer is the server API for MapService service.
// All implementations must embed UnimplementedMapServiceServer
// for forward compatibility
type MapServiceServer interface {
	// get countries info, NOTE: if no parameters are specified, all countries will be returned.
	GetCountries(context.Context, *GetCountriesParams) (*GetCountriesResult, error)
	// get an address
	GetAddress(context.Context, *GetAddressParams) (*GetAddressResult, error)
	// predict addresses by term
	AutocompleteAddress(context.Context, *AutocompleteAddressParams) (*AutocompleteAddressResult, error)
	mustEmbedUnimplementedMapServiceServer()
}

// UnimplementedMapServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMapServiceServer struct {
}

func (UnimplementedMapServiceServer) GetCountries(context.Context, *GetCountriesParams) (*GetCountriesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCountries not implemented")
}
func (UnimplementedMapServiceServer) GetAddress(context.Context, *GetAddressParams) (*GetAddressResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAddress not implemented")
}
func (UnimplementedMapServiceServer) AutocompleteAddress(context.Context, *AutocompleteAddressParams) (*AutocompleteAddressResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AutocompleteAddress not implemented")
}
func (UnimplementedMapServiceServer) mustEmbedUnimplementedMapServiceServer() {}

// UnsafeMapServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MapServiceServer will
// result in compilation errors.
type UnsafeMapServiceServer interface {
	mustEmbedUnimplementedMapServiceServer()
}

func RegisterMapServiceServer(s grpc.ServiceRegistrar, srv MapServiceServer) {
	s.RegisterService(&MapService_ServiceDesc, srv)
}

func _MapService_GetCountries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCountriesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MapServiceServer).GetCountries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.MapService/GetCountries",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MapServiceServer).GetCountries(ctx, req.(*GetCountriesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MapService_GetAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAddressParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MapServiceServer).GetAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.MapService/GetAddress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MapServiceServer).GetAddress(ctx, req.(*GetAddressParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MapService_AutocompleteAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AutocompleteAddressParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MapServiceServer).AutocompleteAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.MapService/AutocompleteAddress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MapServiceServer).AutocompleteAddress(ctx, req.(*AutocompleteAddressParams))
	}
	return interceptor(ctx, in, info, handler)
}

// MapService_ServiceDesc is the grpc.ServiceDesc for MapService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MapService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.online_booking.v1.MapService",
	HandlerType: (*MapServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCountries",
			Handler:    _MapService_GetCountries_Handler,
		},
		{
			MethodName: "GetAddress",
			Handler:    _MapService_GetAddress_Handler,
		},
		{
			MethodName: "AutocompleteAddress",
			Handler:    _MapService_AutocompleteAddress_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/online_booking/v1/map_api.proto",
}
