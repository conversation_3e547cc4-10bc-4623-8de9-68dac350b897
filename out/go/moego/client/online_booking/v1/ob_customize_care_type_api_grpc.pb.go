// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/online_booking/v1/ob_customize_care_type_api.proto

package onlinebookingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BookingCareTypeServiceClient is the client API for BookingCareTypeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BookingCareTypeServiceClient interface {
	// List booking care types
	ListBookingCareTypes(ctx context.Context, in *ListBookingCareTypesParams, opts ...grpc.CallOption) (*ListBookingCareTypesResult, error)
}

type bookingCareTypeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBookingCareTypeServiceClient(cc grpc.ClientConnInterface) BookingCareTypeServiceClient {
	return &bookingCareTypeServiceClient{cc}
}

func (c *bookingCareTypeServiceClient) ListBookingCareTypes(ctx context.Context, in *ListBookingCareTypesParams, opts ...grpc.CallOption) (*ListBookingCareTypesResult, error) {
	out := new(ListBookingCareTypesResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.BookingCareTypeService/ListBookingCareTypes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BookingCareTypeServiceServer is the server API for BookingCareTypeService service.
// All implementations must embed UnimplementedBookingCareTypeServiceServer
// for forward compatibility
type BookingCareTypeServiceServer interface {
	// List booking care types
	ListBookingCareTypes(context.Context, *ListBookingCareTypesParams) (*ListBookingCareTypesResult, error)
	mustEmbedUnimplementedBookingCareTypeServiceServer()
}

// UnimplementedBookingCareTypeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBookingCareTypeServiceServer struct {
}

func (UnimplementedBookingCareTypeServiceServer) ListBookingCareTypes(context.Context, *ListBookingCareTypesParams) (*ListBookingCareTypesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBookingCareTypes not implemented")
}
func (UnimplementedBookingCareTypeServiceServer) mustEmbedUnimplementedBookingCareTypeServiceServer() {
}

// UnsafeBookingCareTypeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BookingCareTypeServiceServer will
// result in compilation errors.
type UnsafeBookingCareTypeServiceServer interface {
	mustEmbedUnimplementedBookingCareTypeServiceServer()
}

func RegisterBookingCareTypeServiceServer(s grpc.ServiceRegistrar, srv BookingCareTypeServiceServer) {
	s.RegisterService(&BookingCareTypeService_ServiceDesc, srv)
}

func _BookingCareTypeService_ListBookingCareTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBookingCareTypesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingCareTypeServiceServer).ListBookingCareTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.BookingCareTypeService/ListBookingCareTypes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingCareTypeServiceServer).ListBookingCareTypes(ctx, req.(*ListBookingCareTypesParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BookingCareTypeService_ServiceDesc is the grpc.ServiceDesc for BookingCareTypeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BookingCareTypeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.online_booking.v1.BookingCareTypeService",
	HandlerType: (*BookingCareTypeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListBookingCareTypes",
			Handler:    _BookingCareTypeService_ListBookingCareTypes_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/online_booking/v1/ob_customize_care_type_api.proto",
}
