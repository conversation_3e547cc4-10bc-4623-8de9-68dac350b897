// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/online_booking/v1/booking_request_api.proto

package onlinebookingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BookingRequestServiceClient is the client API for BookingRequestService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BookingRequestServiceClient interface {
	// Submit booking request
	// 当提交的 service 里同时包含 waitlist 和正常 service 时，接口会创建两个 BookingRequest，一个 status 是 SUBMITTED，另一个 status 是 WAIT_LIST
	SubmitBookingRequest(ctx context.Context, in *SubmitBookingRequestParams, opts ...grpc.CallOption) (*SubmitBookingRequestResult, error)
	// Reschedule booking request
	RescheduleBookingRequest(ctx context.Context, in *RescheduleBookingRequestParams, opts ...grpc.CallOption) (*RescheduleBookingRequestResult, error)
	// Cancel booking request
	CancelBookingRequest(ctx context.Context, in *CancelBookingRequestParams, opts ...grpc.CallOption) (*CancelBookingRequestResult, error)
	// calculate booking request
	CalculateBookingRequest(ctx context.Context, in *CalculateBookingRequestParams, opts ...grpc.CallOption) (*CalculateBookingRequestResult, error)
	// Update booking request - allows C-end users to modify selected service items
	UpdateBookingRequest(ctx context.Context, in *UpdateBookingRequestParams, opts ...grpc.CallOption) (*UpdateBookingRequestResult, error)
}

type bookingRequestServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBookingRequestServiceClient(cc grpc.ClientConnInterface) BookingRequestServiceClient {
	return &bookingRequestServiceClient{cc}
}

func (c *bookingRequestServiceClient) SubmitBookingRequest(ctx context.Context, in *SubmitBookingRequestParams, opts ...grpc.CallOption) (*SubmitBookingRequestResult, error) {
	out := new(SubmitBookingRequestResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.BookingRequestService/SubmitBookingRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) RescheduleBookingRequest(ctx context.Context, in *RescheduleBookingRequestParams, opts ...grpc.CallOption) (*RescheduleBookingRequestResult, error) {
	out := new(RescheduleBookingRequestResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.BookingRequestService/RescheduleBookingRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) CancelBookingRequest(ctx context.Context, in *CancelBookingRequestParams, opts ...grpc.CallOption) (*CancelBookingRequestResult, error) {
	out := new(CancelBookingRequestResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.BookingRequestService/CancelBookingRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) CalculateBookingRequest(ctx context.Context, in *CalculateBookingRequestParams, opts ...grpc.CallOption) (*CalculateBookingRequestResult, error) {
	out := new(CalculateBookingRequestResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.BookingRequestService/CalculateBookingRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) UpdateBookingRequest(ctx context.Context, in *UpdateBookingRequestParams, opts ...grpc.CallOption) (*UpdateBookingRequestResult, error) {
	out := new(UpdateBookingRequestResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v1.BookingRequestService/UpdateBookingRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BookingRequestServiceServer is the server API for BookingRequestService service.
// All implementations must embed UnimplementedBookingRequestServiceServer
// for forward compatibility
type BookingRequestServiceServer interface {
	// Submit booking request
	// 当提交的 service 里同时包含 waitlist 和正常 service 时，接口会创建两个 BookingRequest，一个 status 是 SUBMITTED，另一个 status 是 WAIT_LIST
	SubmitBookingRequest(context.Context, *SubmitBookingRequestParams) (*SubmitBookingRequestResult, error)
	// Reschedule booking request
	RescheduleBookingRequest(context.Context, *RescheduleBookingRequestParams) (*RescheduleBookingRequestResult, error)
	// Cancel booking request
	CancelBookingRequest(context.Context, *CancelBookingRequestParams) (*CancelBookingRequestResult, error)
	// calculate booking request
	CalculateBookingRequest(context.Context, *CalculateBookingRequestParams) (*CalculateBookingRequestResult, error)
	// Update booking request - allows C-end users to modify selected service items
	UpdateBookingRequest(context.Context, *UpdateBookingRequestParams) (*UpdateBookingRequestResult, error)
	mustEmbedUnimplementedBookingRequestServiceServer()
}

// UnimplementedBookingRequestServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBookingRequestServiceServer struct {
}

func (UnimplementedBookingRequestServiceServer) SubmitBookingRequest(context.Context, *SubmitBookingRequestParams) (*SubmitBookingRequestResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitBookingRequest not implemented")
}
func (UnimplementedBookingRequestServiceServer) RescheduleBookingRequest(context.Context, *RescheduleBookingRequestParams) (*RescheduleBookingRequestResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RescheduleBookingRequest not implemented")
}
func (UnimplementedBookingRequestServiceServer) CancelBookingRequest(context.Context, *CancelBookingRequestParams) (*CancelBookingRequestResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelBookingRequest not implemented")
}
func (UnimplementedBookingRequestServiceServer) CalculateBookingRequest(context.Context, *CalculateBookingRequestParams) (*CalculateBookingRequestResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculateBookingRequest not implemented")
}
func (UnimplementedBookingRequestServiceServer) UpdateBookingRequest(context.Context, *UpdateBookingRequestParams) (*UpdateBookingRequestResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBookingRequest not implemented")
}
func (UnimplementedBookingRequestServiceServer) mustEmbedUnimplementedBookingRequestServiceServer() {}

// UnsafeBookingRequestServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BookingRequestServiceServer will
// result in compilation errors.
type UnsafeBookingRequestServiceServer interface {
	mustEmbedUnimplementedBookingRequestServiceServer()
}

func RegisterBookingRequestServiceServer(s grpc.ServiceRegistrar, srv BookingRequestServiceServer) {
	s.RegisterService(&BookingRequestService_ServiceDesc, srv)
}

func _BookingRequestService_SubmitBookingRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitBookingRequestParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).SubmitBookingRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.BookingRequestService/SubmitBookingRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).SubmitBookingRequest(ctx, req.(*SubmitBookingRequestParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_RescheduleBookingRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RescheduleBookingRequestParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).RescheduleBookingRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.BookingRequestService/RescheduleBookingRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).RescheduleBookingRequest(ctx, req.(*RescheduleBookingRequestParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_CancelBookingRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelBookingRequestParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).CancelBookingRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.BookingRequestService/CancelBookingRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).CancelBookingRequest(ctx, req.(*CancelBookingRequestParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_CalculateBookingRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculateBookingRequestParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).CalculateBookingRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.BookingRequestService/CalculateBookingRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).CalculateBookingRequest(ctx, req.(*CalculateBookingRequestParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_UpdateBookingRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBookingRequestParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).UpdateBookingRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v1.BookingRequestService/UpdateBookingRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).UpdateBookingRequest(ctx, req.(*UpdateBookingRequestParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BookingRequestService_ServiceDesc is the grpc.ServiceDesc for BookingRequestService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BookingRequestService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.online_booking.v1.BookingRequestService",
	HandlerType: (*BookingRequestServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SubmitBookingRequest",
			Handler:    _BookingRequestService_SubmitBookingRequest_Handler,
		},
		{
			MethodName: "RescheduleBookingRequest",
			Handler:    _BookingRequestService_RescheduleBookingRequest_Handler,
		},
		{
			MethodName: "CancelBookingRequest",
			Handler:    _BookingRequestService_CancelBookingRequest_Handler,
		},
		{
			MethodName: "CalculateBookingRequest",
			Handler:    _BookingRequestService_CalculateBookingRequest_Handler,
		},
		{
			MethodName: "UpdateBookingRequest",
			Handler:    _BookingRequestService_UpdateBookingRequest_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/online_booking/v1/booking_request_api.proto",
}
