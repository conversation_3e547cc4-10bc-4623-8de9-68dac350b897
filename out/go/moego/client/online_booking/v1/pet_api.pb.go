// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/online_booking/v1/pet_api.proto

package onlinebookingapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// pet vaccine expiration status
// 这个枚举是给 OBC portal 定制的，不是 vaccine record 里定义的，所以放在这里
type PetVaccineExpirationStatus int32

const (
	// unspecified
	PetVaccineExpirationStatus_PET_VACCINE_EXPIRATION_STATUS_UNSPECIFIED PetVaccineExpirationStatus = 0
	// no record
	PetVaccineExpirationStatus_PET_VACCINE_EXPIRATION_STATUS_NO_RECORD PetVaccineExpirationStatus = 1
	// not expired
	PetVaccineExpirationStatus_PET_VACCINE_EXPIRATION_STATUS_NOT_EXPIRED PetVaccineExpirationStatus = 2
	// expired
	PetVaccineExpirationStatus_PET_VACCINE_EXPIRATION_STATUS_EXPIRED PetVaccineExpirationStatus = 3
	// expired soon (expired before next appointment)
	PetVaccineExpirationStatus_PET_VACCINE_EXPIRATION_STATUS_EXPIRED_BEFORE_NEXT_APPOINTMENT PetVaccineExpirationStatus = 4
	// expired soon (expired in 30 days)
	PetVaccineExpirationStatus_PET_VACCINE_EXPIRATION_STATUS_EXPIRED_IN_30_DAYS PetVaccineExpirationStatus = 5
)

// Enum value maps for PetVaccineExpirationStatus.
var (
	PetVaccineExpirationStatus_name = map[int32]string{
		0: "PET_VACCINE_EXPIRATION_STATUS_UNSPECIFIED",
		1: "PET_VACCINE_EXPIRATION_STATUS_NO_RECORD",
		2: "PET_VACCINE_EXPIRATION_STATUS_NOT_EXPIRED",
		3: "PET_VACCINE_EXPIRATION_STATUS_EXPIRED",
		4: "PET_VACCINE_EXPIRATION_STATUS_EXPIRED_BEFORE_NEXT_APPOINTMENT",
		5: "PET_VACCINE_EXPIRATION_STATUS_EXPIRED_IN_30_DAYS",
	}
	PetVaccineExpirationStatus_value = map[string]int32{
		"PET_VACCINE_EXPIRATION_STATUS_UNSPECIFIED":                     0,
		"PET_VACCINE_EXPIRATION_STATUS_NO_RECORD":                       1,
		"PET_VACCINE_EXPIRATION_STATUS_NOT_EXPIRED":                     2,
		"PET_VACCINE_EXPIRATION_STATUS_EXPIRED":                         3,
		"PET_VACCINE_EXPIRATION_STATUS_EXPIRED_BEFORE_NEXT_APPOINTMENT": 4,
		"PET_VACCINE_EXPIRATION_STATUS_EXPIRED_IN_30_DAYS":              5,
	}
)

func (x PetVaccineExpirationStatus) Enum() *PetVaccineExpirationStatus {
	p := new(PetVaccineExpirationStatus)
	*p = x
	return p
}

func (x PetVaccineExpirationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PetVaccineExpirationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_client_online_booking_v1_pet_api_proto_enumTypes[0].Descriptor()
}

func (PetVaccineExpirationStatus) Type() protoreflect.EnumType {
	return &file_moego_client_online_booking_v1_pet_api_proto_enumTypes[0]
}

func (x PetVaccineExpirationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PetVaccineExpirationStatus.Descriptor instead.
func (PetVaccineExpirationStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_pet_api_proto_rawDescGZIP(), []int{0}
}

// CreatePetParams is the params for creating a pet.
type CreatePetParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*CreatePetParams_Name
	//	*CreatePetParams_Domain
	Anonymous isCreatePetParams_Anonymous `protobuf_oneof:"anonymous"`
	// pet with additional info
	Def *v1.BusinessCustomerPetWithAdditionalInfoCreateDef `protobuf:"bytes,3,opt,name=def,proto3" json:"def,omitempty"`
}

func (x *CreatePetParams) Reset() {
	*x = CreatePetParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetParams) ProtoMessage() {}

func (x *CreatePetParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetParams.ProtoReflect.Descriptor instead.
func (*CreatePetParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_pet_api_proto_rawDescGZIP(), []int{0}
}

func (m *CreatePetParams) GetAnonymous() isCreatePetParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *CreatePetParams) GetName() string {
	if x, ok := x.GetAnonymous().(*CreatePetParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *CreatePetParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*CreatePetParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *CreatePetParams) GetDef() *v1.BusinessCustomerPetWithAdditionalInfoCreateDef {
	if x != nil {
		return x.Def
	}
	return nil
}

type isCreatePetParams_Anonymous interface {
	isCreatePetParams_Anonymous()
}

type CreatePetParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type CreatePetParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*CreatePetParams_Name) isCreatePetParams_Anonymous() {}

func (*CreatePetParams_Domain) isCreatePetParams_Anonymous() {}

// CreatePetResult is the result for creating a pet.
type CreatePetResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet model
	Pet *v1.BusinessCustomerPetModel `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
}

func (x *CreatePetResult) Reset() {
	*x = CreatePetResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetResult) ProtoMessage() {}

func (x *CreatePetResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetResult.ProtoReflect.Descriptor instead.
func (*CreatePetResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_pet_api_proto_rawDescGZIP(), []int{1}
}

func (x *CreatePetResult) GetPet() *v1.BusinessCustomerPetModel {
	if x != nil {
		return x.Pet
	}
	return nil
}

// GetPetParams is the params for getting a pet.
type GetPetParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetPetParams_Name
	//	*GetPetParams_Domain
	Anonymous isGetPetParams_Anonymous `protobuf_oneof:"anonymous"`
	// pet id
	Id int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetPetParams) Reset() {
	*x = GetPetParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetParams) ProtoMessage() {}

func (x *GetPetParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetParams.ProtoReflect.Descriptor instead.
func (*GetPetParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_pet_api_proto_rawDescGZIP(), []int{2}
}

func (m *GetPetParams) GetAnonymous() isGetPetParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetPetParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetPetParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetPetParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetPetParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *GetPetParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type isGetPetParams_Anonymous interface {
	isGetPetParams_Anonymous()
}

type GetPetParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetPetParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetPetParams_Name) isGetPetParams_Anonymous() {}

func (*GetPetParams_Domain) isGetPetParams_Anonymous() {}

// GetPetResult is the result for getting a pet.
type GetPetResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet model
	Pet *v1.BusinessCustomerPetModel `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
}

func (x *GetPetResult) Reset() {
	*x = GetPetResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetResult) ProtoMessage() {}

func (x *GetPetResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetResult.ProtoReflect.Descriptor instead.
func (*GetPetResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_pet_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetPetResult) GetPet() *v1.BusinessCustomerPetModel {
	if x != nil {
		return x.Pet
	}
	return nil
}

// ListPetsParams is the params for listing
type ListPetsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*ListPetsParams_Name
	//	*ListPetsParams_Domain
	Anonymous isListPetsParams_Anonymous `protobuf_oneof:"anonymous"`
	// include vaccine records in the result, default is false (not include)
	IncludeVaccineRecords bool `protobuf:"varint,3,opt,name=include_vaccine_records,json=includeVaccineRecords,proto3" json:"include_vaccine_records,omitempty"`
	// include pending vaccine requests in the result, default is false (not include)
	IncludePendingVaccineRequests bool `protobuf:"varint,4,opt,name=include_pending_vaccine_requests,json=includePendingVaccineRequests,proto3" json:"include_pending_vaccine_requests,omitempty"`
	// include passed away pets in the result, default is false (not include)
	IncludePassedAway bool `protobuf:"varint,5,opt,name=include_passed_away,json=includePassedAway,proto3" json:"include_passed_away,omitempty"`
}

func (x *ListPetsParams) Reset() {
	*x = ListPetsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetsParams) ProtoMessage() {}

func (x *ListPetsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetsParams.ProtoReflect.Descriptor instead.
func (*ListPetsParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_pet_api_proto_rawDescGZIP(), []int{4}
}

func (m *ListPetsParams) GetAnonymous() isListPetsParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *ListPetsParams) GetName() string {
	if x, ok := x.GetAnonymous().(*ListPetsParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *ListPetsParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*ListPetsParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *ListPetsParams) GetIncludeVaccineRecords() bool {
	if x != nil {
		return x.IncludeVaccineRecords
	}
	return false
}

func (x *ListPetsParams) GetIncludePendingVaccineRequests() bool {
	if x != nil {
		return x.IncludePendingVaccineRequests
	}
	return false
}

func (x *ListPetsParams) GetIncludePassedAway() bool {
	if x != nil {
		return x.IncludePassedAway
	}
	return false
}

type isListPetsParams_Anonymous interface {
	isListPetsParams_Anonymous()
}

type ListPetsParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type ListPetsParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*ListPetsParams_Name) isListPetsParams_Anonymous() {}

func (*ListPetsParams_Domain) isListPetsParams_Anonymous() {}

// ListPetsResult is the result for listing
type ListPetsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet models
	Pets []*v1.BusinessCustomerPetModel `protobuf:"bytes,1,rep,name=pets,proto3" json:"pets,omitempty"`
	// vaccine records
	VaccineRecords []*v1.BusinessPetVaccineRecordBindingModel `protobuf:"bytes,2,rep,name=vaccine_records,json=vaccineRecords,proto3" json:"vaccine_records,omitempty"`
	// pending vaccine requests
	PendingVaccineRequests []*v1.BusinessPetVaccineRequestBindingModel `protobuf:"bytes,3,rep,name=pending_vaccine_requests,json=pendingVaccineRequests,proto3" json:"pending_vaccine_requests,omitempty"`
}

func (x *ListPetsResult) Reset() {
	*x = ListPetsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetsResult) ProtoMessage() {}

func (x *ListPetsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetsResult.ProtoReflect.Descriptor instead.
func (*ListPetsResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_pet_api_proto_rawDescGZIP(), []int{5}
}

func (x *ListPetsResult) GetPets() []*v1.BusinessCustomerPetModel {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *ListPetsResult) GetVaccineRecords() []*v1.BusinessPetVaccineRecordBindingModel {
	if x != nil {
		return x.VaccineRecords
	}
	return nil
}

func (x *ListPetsResult) GetPendingVaccineRequests() []*v1.BusinessPetVaccineRequestBindingModel {
	if x != nil {
		return x.PendingVaccineRequests
	}
	return nil
}

// UpdatePetParams is the params for updating a pet.
type UpdatePetParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*UpdatePetParams_Name
	//	*UpdatePetParams_Domain
	Anonymous isUpdatePetParams_Anonymous `protobuf_oneof:"anonymous"`
	// pet id
	Id int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// pet with additional info
	Def *v1.BusinessCustomerPetUpdateDef `protobuf:"bytes,4,opt,name=def,proto3" json:"def,omitempty"`
	// pet vaccine list
	// deprecated. vaccines should be submitted via SubmitPetVaccineRecordRequest method
	//
	// Deprecated: Do not use.
	Vaccines []*v11.VaccineDef `protobuf:"bytes,5,rep,name=vaccines,proto3" json:"vaccines,omitempty"`
}

func (x *UpdatePetParams) Reset() {
	*x = UpdatePetParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetParams) ProtoMessage() {}

func (x *UpdatePetParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetParams.ProtoReflect.Descriptor instead.
func (*UpdatePetParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_pet_api_proto_rawDescGZIP(), []int{6}
}

func (m *UpdatePetParams) GetAnonymous() isUpdatePetParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *UpdatePetParams) GetName() string {
	if x, ok := x.GetAnonymous().(*UpdatePetParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *UpdatePetParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*UpdatePetParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *UpdatePetParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePetParams) GetDef() *v1.BusinessCustomerPetUpdateDef {
	if x != nil {
		return x.Def
	}
	return nil
}

// Deprecated: Do not use.
func (x *UpdatePetParams) GetVaccines() []*v11.VaccineDef {
	if x != nil {
		return x.Vaccines
	}
	return nil
}

type isUpdatePetParams_Anonymous interface {
	isUpdatePetParams_Anonymous()
}

type UpdatePetParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type UpdatePetParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*UpdatePetParams_Name) isUpdatePetParams_Anonymous() {}

func (*UpdatePetParams_Domain) isUpdatePetParams_Anonymous() {}

// UpdatePetResult is the result for updating a pet.
type UpdatePetResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet model
	Pet *v1.BusinessCustomerPetModel `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
}

func (x *UpdatePetResult) Reset() {
	*x = UpdatePetResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetResult) ProtoMessage() {}

func (x *UpdatePetResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetResult.ProtoReflect.Descriptor instead.
func (*UpdatePetResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_pet_api_proto_rawDescGZIP(), []int{7}
}

func (x *UpdatePetResult) GetPet() *v1.BusinessCustomerPetModel {
	if x != nil {
		return x.Pet
	}
	return nil
}

// SubmitPetVaccineRequestParams is the params for submitting pet vaccine request
type SubmitPetVaccineRequestParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*SubmitPetVaccineRequestParams_Name
	//	*SubmitPetVaccineRequestParams_Domain
	Anonymous isSubmitPetVaccineRequestParams_Anonymous `protobuf_oneof:"anonymous"`
	// pet id
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet vaccine request id.
	// if set, update the existing pet vaccine request
	Id *int64 `protobuf:"varint,4,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// pet vaccine request
	PetVaccineRequest *v1.BusinessPetVaccineRequestCreateDef `protobuf:"bytes,5,opt,name=pet_vaccine_request,json=petVaccineRequest,proto3" json:"pet_vaccine_request,omitempty"`
}

func (x *SubmitPetVaccineRequestParams) Reset() {
	*x = SubmitPetVaccineRequestParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitPetVaccineRequestParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitPetVaccineRequestParams) ProtoMessage() {}

func (x *SubmitPetVaccineRequestParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitPetVaccineRequestParams.ProtoReflect.Descriptor instead.
func (*SubmitPetVaccineRequestParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_pet_api_proto_rawDescGZIP(), []int{8}
}

func (m *SubmitPetVaccineRequestParams) GetAnonymous() isSubmitPetVaccineRequestParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *SubmitPetVaccineRequestParams) GetName() string {
	if x, ok := x.GetAnonymous().(*SubmitPetVaccineRequestParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *SubmitPetVaccineRequestParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*SubmitPetVaccineRequestParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *SubmitPetVaccineRequestParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *SubmitPetVaccineRequestParams) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *SubmitPetVaccineRequestParams) GetPetVaccineRequest() *v1.BusinessPetVaccineRequestCreateDef {
	if x != nil {
		return x.PetVaccineRequest
	}
	return nil
}

type isSubmitPetVaccineRequestParams_Anonymous interface {
	isSubmitPetVaccineRequestParams_Anonymous()
}

type SubmitPetVaccineRequestParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type SubmitPetVaccineRequestParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*SubmitPetVaccineRequestParams_Name) isSubmitPetVaccineRequestParams_Anonymous() {}

func (*SubmitPetVaccineRequestParams_Domain) isSubmitPetVaccineRequestParams_Anonymous() {}

// SubmitPetVaccineRequestResult is the result for submitting pet vaccine request
type SubmitPetVaccineRequestResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SubmitPetVaccineRequestResult) Reset() {
	*x = SubmitPetVaccineRequestResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitPetVaccineRequestResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitPetVaccineRequestResult) ProtoMessage() {}

func (x *SubmitPetVaccineRequestResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitPetVaccineRequestResult.ProtoReflect.Descriptor instead.
func (*SubmitPetVaccineRequestResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_pet_api_proto_rawDescGZIP(), []int{9}
}

// ListPetVaccineExpirationStatusParams is the params for listing pet vaccine expiration status
type ListPetVaccineExpirationStatusParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*ListPetVaccineExpirationStatusParams_Name
	//	*ListPetVaccineExpirationStatusParams_Domain
	Anonymous isListPetVaccineExpirationStatusParams_Anonymous `protobuf_oneof:"anonymous"`
}

func (x *ListPetVaccineExpirationStatusParams) Reset() {
	*x = ListPetVaccineExpirationStatusParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetVaccineExpirationStatusParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetVaccineExpirationStatusParams) ProtoMessage() {}

func (x *ListPetVaccineExpirationStatusParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetVaccineExpirationStatusParams.ProtoReflect.Descriptor instead.
func (*ListPetVaccineExpirationStatusParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_pet_api_proto_rawDescGZIP(), []int{10}
}

func (m *ListPetVaccineExpirationStatusParams) GetAnonymous() isListPetVaccineExpirationStatusParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *ListPetVaccineExpirationStatusParams) GetName() string {
	if x, ok := x.GetAnonymous().(*ListPetVaccineExpirationStatusParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *ListPetVaccineExpirationStatusParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*ListPetVaccineExpirationStatusParams_Domain); ok {
		return x.Domain
	}
	return ""
}

type isListPetVaccineExpirationStatusParams_Anonymous interface {
	isListPetVaccineExpirationStatusParams_Anonymous()
}

type ListPetVaccineExpirationStatusParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type ListPetVaccineExpirationStatusParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*ListPetVaccineExpirationStatusParams_Name) isListPetVaccineExpirationStatusParams_Anonymous() {
}

func (*ListPetVaccineExpirationStatusParams_Domain) isListPetVaccineExpirationStatusParams_Anonymous() {
}

// ListPetVaccineExpirationStatusResult is the result for listing pet vaccine expiration status
type ListPetVaccineExpirationStatusResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet vaccine expiration status map (pet 维度),
	// key is pet id, value is pet vaccine expiration status
	PetVaccineExpirationStatus map[int64]PetVaccineExpirationStatus `protobuf:"bytes,1,rep,name=pet_vaccine_expiration_status,json=petVaccineExpirationStatus,proto3" json:"pet_vaccine_expiration_status,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=moego.client.online_booking.v1.PetVaccineExpirationStatus"`
	// pet vaccine record expiration status map (pet vaccine record 维度),
	// key is pet vaccine record id, value is pet vaccine expiration status
	PetVaccineRecordExpirationStatus map[int64]PetVaccineExpirationStatus `protobuf:"bytes,2,rep,name=pet_vaccine_record_expiration_status,json=petVaccineRecordExpirationStatus,proto3" json:"pet_vaccine_record_expiration_status,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=moego.client.online_booking.v1.PetVaccineExpirationStatus"`
}

func (x *ListPetVaccineExpirationStatusResult) Reset() {
	*x = ListPetVaccineExpirationStatusResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetVaccineExpirationStatusResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetVaccineExpirationStatusResult) ProtoMessage() {}

func (x *ListPetVaccineExpirationStatusResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_pet_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetVaccineExpirationStatusResult.ProtoReflect.Descriptor instead.
func (*ListPetVaccineExpirationStatusResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_pet_api_proto_rawDescGZIP(), []int{11}
}

func (x *ListPetVaccineExpirationStatusResult) GetPetVaccineExpirationStatus() map[int64]PetVaccineExpirationStatus {
	if x != nil {
		return x.PetVaccineExpirationStatus
	}
	return nil
}

func (x *ListPetVaccineExpirationStatusResult) GetPetVaccineRecordExpirationStatus() map[int64]PetVaccineExpirationStatus {
	if x != nil {
		return x.PetVaccineRecordExpirationStatus
	}
	return nil
}

var File_moego_client_online_booking_v1_pet_api_proto protoreflect.FileDescriptor

var file_moego_client_online_booking_v1_pet_api_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x70, 0x65, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x42,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x4a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x49, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x70, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x4b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x70, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xc2, 0x01, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x12, 0x6d, 0x0a, 0x03, 0x64, 0x65, 0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x51, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x65, 0x74, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x64,
	0x65, 0x66, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12,
	0x03, 0xf8, 0x42, 0x01, 0x22, 0x60, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4d, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x03, 0x70, 0x65, 0x74, 0x22, 0x69, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x42,
	0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42,
	0x01, 0x22, 0x5d, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x4d, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x50, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x03, 0x70, 0x65, 0x74,
	0x22, 0x83, 0x02, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x12, 0x36, 0x0a, 0x17, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x76,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x56, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x47, 0x0a, 0x20, 0x69,
	0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1d, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x50, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f,
	0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x77, 0x61, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x11, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x50, 0x61, 0x73, 0x73, 0x65, 0x64,
	0x41, 0x77, 0x61, 0x79, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75,
	0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xd8, 0x02, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4f, 0x0a, 0x04, 0x70, 0x65, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x65, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x70, 0x65, 0x74, 0x73, 0x12, 0x70, 0x0a, 0x0f, 0x76, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0e, 0x76, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x82, 0x01, 0x0a,
	0x18, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x56,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x16, 0x70, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x73, 0x22, 0x8f, 0x02, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x5b,
	0x0a, 0x03, 0x64, 0x65, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x50, 0x65, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x64, 0x65, 0x66, 0x12, 0x44, 0x0a, 0x08, 0x76,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x44, 0x65, 0x66, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x73, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03,
	0xf8, 0x42, 0x01, 0x22, 0x60, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4d, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x03, 0x70, 0x65, 0x74, 0x22, 0xa7, 0x02, 0x0a, 0x1d, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x02,
	0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x7f, 0x0a, 0x13, 0x70, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50,
	0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x11, 0x70, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d,
	0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x22,
	0x1f, 0x0a, 0x1d, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x68, 0x0a, 0x24, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e,
	0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xab, 0x05, 0x0a, 0x24, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0xa7, 0x01, 0x0a, 0x1d, 0x70, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x64, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x2e, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x70, 0x69,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x1a, 0x70, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0xba, 0x01,
	0x0a, 0x24, 0x70, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x6a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x70, 0x69,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x20, 0x70, 0x65, 0x74, 0x56, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x89, 0x01, 0x0a, 0x1f, 0x50,
	0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x50, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x8f, 0x01, 0x0a, 0x25, 0x50, 0x65, 0x74, 0x56, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x45, 0x78, 0x70, 0x69, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x50, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a, 0xcb, 0x02, 0x0a, 0x1a, 0x50, 0x65, 0x74,
	0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2d, 0x0a, 0x29, 0x50, 0x45, 0x54, 0x5f, 0x56,
	0x41, 0x43, 0x43, 0x49, 0x4e, 0x45, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2b, 0x0a, 0x27, 0x50, 0x45, 0x54, 0x5f, 0x56, 0x41,
	0x43, 0x43, 0x49, 0x4e, 0x45, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52,
	0x44, 0x10, 0x01, 0x12, 0x2d, 0x0a, 0x29, 0x50, 0x45, 0x54, 0x5f, 0x56, 0x41, 0x43, 0x43, 0x49,
	0x4e, 0x45, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44,
	0x10, 0x02, 0x12, 0x29, 0x0a, 0x25, 0x50, 0x45, 0x54, 0x5f, 0x56, 0x41, 0x43, 0x43, 0x49, 0x4e,
	0x45, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x03, 0x12, 0x41, 0x0a,
	0x3d, 0x50, 0x45, 0x54, 0x5f, 0x56, 0x41, 0x43, 0x43, 0x49, 0x4e, 0x45, 0x5f, 0x45, 0x58, 0x50,
	0x49, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45,
	0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x5f, 0x42, 0x45, 0x46, 0x4f, 0x52, 0x45, 0x5f, 0x4e, 0x45,
	0x58, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x04,
	0x12, 0x34, 0x0a, 0x30, 0x50, 0x45, 0x54, 0x5f, 0x56, 0x41, 0x43, 0x43, 0x49, 0x4e, 0x45, 0x5f,
	0x45, 0x58, 0x50, 0x49, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x5f, 0x33, 0x30, 0x5f,
	0x44, 0x41, 0x59, 0x53, 0x10, 0x05, 0x32, 0x8d, 0x06, 0x0a, 0x0a, 0x50, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6f, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74,
	0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x6c,
	0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x73, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x65, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x6f, 0x0a, 0x09,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x50, 0x65, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x97, 0x01,
	0x0a, 0x17, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69,
	0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xac, 0x01, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x44, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x92, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x66, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_client_online_booking_v1_pet_api_proto_rawDescOnce sync.Once
	file_moego_client_online_booking_v1_pet_api_proto_rawDescData = file_moego_client_online_booking_v1_pet_api_proto_rawDesc
)

func file_moego_client_online_booking_v1_pet_api_proto_rawDescGZIP() []byte {
	file_moego_client_online_booking_v1_pet_api_proto_rawDescOnce.Do(func() {
		file_moego_client_online_booking_v1_pet_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_online_booking_v1_pet_api_proto_rawDescData)
	})
	return file_moego_client_online_booking_v1_pet_api_proto_rawDescData
}

var file_moego_client_online_booking_v1_pet_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_client_online_booking_v1_pet_api_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_moego_client_online_booking_v1_pet_api_proto_goTypes = []interface{}{
	(PetVaccineExpirationStatus)(0),              // 0: moego.client.online_booking.v1.PetVaccineExpirationStatus
	(*CreatePetParams)(nil),                      // 1: moego.client.online_booking.v1.CreatePetParams
	(*CreatePetResult)(nil),                      // 2: moego.client.online_booking.v1.CreatePetResult
	(*GetPetParams)(nil),                         // 3: moego.client.online_booking.v1.GetPetParams
	(*GetPetResult)(nil),                         // 4: moego.client.online_booking.v1.GetPetResult
	(*ListPetsParams)(nil),                       // 5: moego.client.online_booking.v1.ListPetsParams
	(*ListPetsResult)(nil),                       // 6: moego.client.online_booking.v1.ListPetsResult
	(*UpdatePetParams)(nil),                      // 7: moego.client.online_booking.v1.UpdatePetParams
	(*UpdatePetResult)(nil),                      // 8: moego.client.online_booking.v1.UpdatePetResult
	(*SubmitPetVaccineRequestParams)(nil),        // 9: moego.client.online_booking.v1.SubmitPetVaccineRequestParams
	(*SubmitPetVaccineRequestResult)(nil),        // 10: moego.client.online_booking.v1.SubmitPetVaccineRequestResult
	(*ListPetVaccineExpirationStatusParams)(nil), // 11: moego.client.online_booking.v1.ListPetVaccineExpirationStatusParams
	(*ListPetVaccineExpirationStatusResult)(nil), // 12: moego.client.online_booking.v1.ListPetVaccineExpirationStatusResult
	nil, // 13: moego.client.online_booking.v1.ListPetVaccineExpirationStatusResult.PetVaccineExpirationStatusEntry
	nil, // 14: moego.client.online_booking.v1.ListPetVaccineExpirationStatusResult.PetVaccineRecordExpirationStatusEntry
	(*v1.BusinessCustomerPetWithAdditionalInfoCreateDef)(nil), // 15: moego.models.business_customer.v1.BusinessCustomerPetWithAdditionalInfoCreateDef
	(*v1.BusinessCustomerPetModel)(nil),                       // 16: moego.models.business_customer.v1.BusinessCustomerPetModel
	(*v1.BusinessPetVaccineRecordBindingModel)(nil),           // 17: moego.models.business_customer.v1.BusinessPetVaccineRecordBindingModel
	(*v1.BusinessPetVaccineRequestBindingModel)(nil),          // 18: moego.models.business_customer.v1.BusinessPetVaccineRequestBindingModel
	(*v1.BusinessCustomerPetUpdateDef)(nil),                   // 19: moego.models.business_customer.v1.BusinessCustomerPetUpdateDef
	(*v11.VaccineDef)(nil),                                    // 20: moego.models.customer.v1.VaccineDef
	(*v1.BusinessPetVaccineRequestCreateDef)(nil),             // 21: moego.models.business_customer.v1.BusinessPetVaccineRequestCreateDef
}
var file_moego_client_online_booking_v1_pet_api_proto_depIdxs = []int32{
	15, // 0: moego.client.online_booking.v1.CreatePetParams.def:type_name -> moego.models.business_customer.v1.BusinessCustomerPetWithAdditionalInfoCreateDef
	16, // 1: moego.client.online_booking.v1.CreatePetResult.pet:type_name -> moego.models.business_customer.v1.BusinessCustomerPetModel
	16, // 2: moego.client.online_booking.v1.GetPetResult.pet:type_name -> moego.models.business_customer.v1.BusinessCustomerPetModel
	16, // 3: moego.client.online_booking.v1.ListPetsResult.pets:type_name -> moego.models.business_customer.v1.BusinessCustomerPetModel
	17, // 4: moego.client.online_booking.v1.ListPetsResult.vaccine_records:type_name -> moego.models.business_customer.v1.BusinessPetVaccineRecordBindingModel
	18, // 5: moego.client.online_booking.v1.ListPetsResult.pending_vaccine_requests:type_name -> moego.models.business_customer.v1.BusinessPetVaccineRequestBindingModel
	19, // 6: moego.client.online_booking.v1.UpdatePetParams.def:type_name -> moego.models.business_customer.v1.BusinessCustomerPetUpdateDef
	20, // 7: moego.client.online_booking.v1.UpdatePetParams.vaccines:type_name -> moego.models.customer.v1.VaccineDef
	16, // 8: moego.client.online_booking.v1.UpdatePetResult.pet:type_name -> moego.models.business_customer.v1.BusinessCustomerPetModel
	21, // 9: moego.client.online_booking.v1.SubmitPetVaccineRequestParams.pet_vaccine_request:type_name -> moego.models.business_customer.v1.BusinessPetVaccineRequestCreateDef
	13, // 10: moego.client.online_booking.v1.ListPetVaccineExpirationStatusResult.pet_vaccine_expiration_status:type_name -> moego.client.online_booking.v1.ListPetVaccineExpirationStatusResult.PetVaccineExpirationStatusEntry
	14, // 11: moego.client.online_booking.v1.ListPetVaccineExpirationStatusResult.pet_vaccine_record_expiration_status:type_name -> moego.client.online_booking.v1.ListPetVaccineExpirationStatusResult.PetVaccineRecordExpirationStatusEntry
	0,  // 12: moego.client.online_booking.v1.ListPetVaccineExpirationStatusResult.PetVaccineExpirationStatusEntry.value:type_name -> moego.client.online_booking.v1.PetVaccineExpirationStatus
	0,  // 13: moego.client.online_booking.v1.ListPetVaccineExpirationStatusResult.PetVaccineRecordExpirationStatusEntry.value:type_name -> moego.client.online_booking.v1.PetVaccineExpirationStatus
	1,  // 14: moego.client.online_booking.v1.PetService.CreatePet:input_type -> moego.client.online_booking.v1.CreatePetParams
	3,  // 15: moego.client.online_booking.v1.PetService.GetPet:input_type -> moego.client.online_booking.v1.GetPetParams
	5,  // 16: moego.client.online_booking.v1.PetService.ListPets:input_type -> moego.client.online_booking.v1.ListPetsParams
	7,  // 17: moego.client.online_booking.v1.PetService.UpdatePet:input_type -> moego.client.online_booking.v1.UpdatePetParams
	9,  // 18: moego.client.online_booking.v1.PetService.SubmitPetVaccineRequest:input_type -> moego.client.online_booking.v1.SubmitPetVaccineRequestParams
	11, // 19: moego.client.online_booking.v1.PetService.ListPetVaccineExpirationStatus:input_type -> moego.client.online_booking.v1.ListPetVaccineExpirationStatusParams
	2,  // 20: moego.client.online_booking.v1.PetService.CreatePet:output_type -> moego.client.online_booking.v1.CreatePetResult
	4,  // 21: moego.client.online_booking.v1.PetService.GetPet:output_type -> moego.client.online_booking.v1.GetPetResult
	6,  // 22: moego.client.online_booking.v1.PetService.ListPets:output_type -> moego.client.online_booking.v1.ListPetsResult
	8,  // 23: moego.client.online_booking.v1.PetService.UpdatePet:output_type -> moego.client.online_booking.v1.UpdatePetResult
	10, // 24: moego.client.online_booking.v1.PetService.SubmitPetVaccineRequest:output_type -> moego.client.online_booking.v1.SubmitPetVaccineRequestResult
	12, // 25: moego.client.online_booking.v1.PetService.ListPetVaccineExpirationStatus:output_type -> moego.client.online_booking.v1.ListPetVaccineExpirationStatusResult
	20, // [20:26] is the sub-list for method output_type
	14, // [14:20] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_moego_client_online_booking_v1_pet_api_proto_init() }
func file_moego_client_online_booking_v1_pet_api_proto_init() {
	if File_moego_client_online_booking_v1_pet_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_online_booking_v1_pet_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_pet_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_pet_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_pet_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_pet_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_pet_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_pet_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_pet_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_pet_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitPetVaccineRequestParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_pet_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitPetVaccineRequestResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_pet_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetVaccineExpirationStatusParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_pet_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetVaccineExpirationStatusResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_online_booking_v1_pet_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*CreatePetParams_Name)(nil),
		(*CreatePetParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_pet_api_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*GetPetParams_Name)(nil),
		(*GetPetParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_pet_api_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*ListPetsParams_Name)(nil),
		(*ListPetsParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_pet_api_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*UpdatePetParams_Name)(nil),
		(*UpdatePetParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_pet_api_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*SubmitPetVaccineRequestParams_Name)(nil),
		(*SubmitPetVaccineRequestParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_pet_api_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*ListPetVaccineExpirationStatusParams_Name)(nil),
		(*ListPetVaccineExpirationStatusParams_Domain)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_online_booking_v1_pet_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_online_booking_v1_pet_api_proto_goTypes,
		DependencyIndexes: file_moego_client_online_booking_v1_pet_api_proto_depIdxs,
		EnumInfos:         file_moego_client_online_booking_v1_pet_api_proto_enumTypes,
		MessageInfos:      file_moego_client_online_booking_v1_pet_api_proto_msgTypes,
	}.Build()
	File_moego_client_online_booking_v1_pet_api_proto = out.File
	file_moego_client_online_booking_v1_pet_api_proto_rawDesc = nil
	file_moego_client_online_booking_v1_pet_api_proto_goTypes = nil
	file_moego_client_online_booking_v1_pet_api_proto_depIdxs = nil
}
