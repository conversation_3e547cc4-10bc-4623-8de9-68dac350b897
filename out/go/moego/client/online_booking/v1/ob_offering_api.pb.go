// @since 2024-04-11 12:16:15
// <AUTHOR> <zhang<PERSON>@moego.pet>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/online_booking/v1/ob_offering_api.proto

package onlinebookingapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get applicable offerings request
type GetApplicableOfferingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetApplicableOfferingsRequest_Name
	//	*GetApplicableOfferingsRequest_Domain
	Anonymous isGetApplicableOfferingsRequest_Anonymous `protobuf_oneof:"anonymous"`
	// service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,3,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
}

func (x *GetApplicableOfferingsRequest) Reset() {
	*x = GetApplicableOfferingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_ob_offering_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicableOfferingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicableOfferingsRequest) ProtoMessage() {}

func (x *GetApplicableOfferingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_ob_offering_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicableOfferingsRequest.ProtoReflect.Descriptor instead.
func (*GetApplicableOfferingsRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_ob_offering_api_proto_rawDescGZIP(), []int{0}
}

func (m *GetApplicableOfferingsRequest) GetAnonymous() isGetApplicableOfferingsRequest_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetApplicableOfferingsRequest) GetName() string {
	if x, ok := x.GetAnonymous().(*GetApplicableOfferingsRequest_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetApplicableOfferingsRequest) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetApplicableOfferingsRequest_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *GetApplicableOfferingsRequest) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

type isGetApplicableOfferingsRequest_Anonymous interface {
	isGetApplicableOfferingsRequest_Anonymous()
}

type GetApplicableOfferingsRequest_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetApplicableOfferingsRequest_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetApplicableOfferingsRequest_Name) isGetApplicableOfferingsRequest_Anonymous() {}

func (*GetApplicableOfferingsRequest_Domain) isGetApplicableOfferingsRequest_Anonymous() {}

// get applicable offerings response
type GetApplicableOfferingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service category list
	ServiceCategories []*v11.OBOfferingCategoryView `protobuf:"bytes,1,rep,name=service_categories,json=serviceCategories,proto3" json:"service_categories,omitempty"`
	// add on list
	AddonCategories []*v11.OBOfferingCategoryView `protobuf:"bytes,2,rep,name=addon_categories,json=addonCategories,proto3" json:"addon_categories,omitempty"`
}

func (x *GetApplicableOfferingsResponse) Reset() {
	*x = GetApplicableOfferingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_ob_offering_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicableOfferingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicableOfferingsResponse) ProtoMessage() {}

func (x *GetApplicableOfferingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_ob_offering_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicableOfferingsResponse.ProtoReflect.Descriptor instead.
func (*GetApplicableOfferingsResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_ob_offering_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetApplicableOfferingsResponse) GetServiceCategories() []*v11.OBOfferingCategoryView {
	if x != nil {
		return x.ServiceCategories
	}
	return nil
}

func (x *GetApplicableOfferingsResponse) GetAddonCategories() []*v11.OBOfferingCategoryView {
	if x != nil {
		return x.AddonCategories
	}
	return nil
}

// list pricing rule params
type ListPricingRuleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*ListPricingRuleParams_Name
	//	*ListPricingRuleParams_Domain
	Anonymous isListPricingRuleParams_Anonymous `protobuf_oneof:"anonymous"`
	// filter
	Filter *v1.ListPricingRuleFilter `protobuf:"bytes,3,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// date selection type
	//
	// Types that are assignable to DateSelectionType:
	//
	//	*ListPricingRuleParams_DateRange
	//	*ListPricingRuleParams_DateList
	DateSelectionType isListPricingRuleParams_DateSelectionType `protobuf_oneof:"date_selection_type"`
}

func (x *ListPricingRuleParams) Reset() {
	*x = ListPricingRuleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_ob_offering_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRuleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRuleParams) ProtoMessage() {}

func (x *ListPricingRuleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_ob_offering_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRuleParams.ProtoReflect.Descriptor instead.
func (*ListPricingRuleParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_ob_offering_api_proto_rawDescGZIP(), []int{2}
}

func (m *ListPricingRuleParams) GetAnonymous() isListPricingRuleParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *ListPricingRuleParams) GetName() string {
	if x, ok := x.GetAnonymous().(*ListPricingRuleParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *ListPricingRuleParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*ListPricingRuleParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *ListPricingRuleParams) GetFilter() *v1.ListPricingRuleFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (m *ListPricingRuleParams) GetDateSelectionType() isListPricingRuleParams_DateSelectionType {
	if m != nil {
		return m.DateSelectionType
	}
	return nil
}

func (x *ListPricingRuleParams) GetDateRange() *v11.ServiceDateRangeDef {
	if x, ok := x.GetDateSelectionType().(*ListPricingRuleParams_DateRange); ok {
		return x.DateRange
	}
	return nil
}

func (x *ListPricingRuleParams) GetDateList() *v11.ServiceDateListDef {
	if x, ok := x.GetDateSelectionType().(*ListPricingRuleParams_DateList); ok {
		return x.DateList
	}
	return nil
}

type isListPricingRuleParams_Anonymous interface {
	isListPricingRuleParams_Anonymous()
}

type ListPricingRuleParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type ListPricingRuleParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*ListPricingRuleParams_Name) isListPricingRuleParams_Anonymous() {}

func (*ListPricingRuleParams_Domain) isListPricingRuleParams_Anonymous() {}

type isListPricingRuleParams_DateSelectionType interface {
	isListPricingRuleParams_DateSelectionType()
}

type ListPricingRuleParams_DateRange struct {
	// date range
	DateRange *v11.ServiceDateRangeDef `protobuf:"bytes,11,opt,name=date_range,json=dateRange,proto3,oneof"`
}

type ListPricingRuleParams_DateList struct {
	// date list
	DateList *v11.ServiceDateListDef `protobuf:"bytes,12,opt,name=date_list,json=dateList,proto3,oneof"`
}

func (*ListPricingRuleParams_DateRange) isListPricingRuleParams_DateSelectionType() {}

func (*ListPricingRuleParams_DateList) isListPricingRuleParams_DateSelectionType() {}

// list pricing rule result
type ListPricingRuleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the pricing rule list
	//
	// Deprecated: Do not use.
	PricingRules []*v1.PricingRuleModel `protobuf:"bytes,1,rep,name=pricing_rules,json=pricingRules,proto3" json:"pricing_rules,omitempty"`
	// the pricing rule list
	PricingRulesV2 []*v2.PricingRule `protobuf:"bytes,2,rep,name=pricing_rules_v2,json=pricingRulesV2,proto3" json:"pricing_rules_v2,omitempty"`
	// service models
	Services []*v1.ServiceModel `protobuf:"bytes,3,rep,name=services,proto3" json:"services,omitempty"`
}

func (x *ListPricingRuleResult) Reset() {
	*x = ListPricingRuleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_ob_offering_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRuleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRuleResult) ProtoMessage() {}

func (x *ListPricingRuleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_ob_offering_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRuleResult.ProtoReflect.Descriptor instead.
func (*ListPricingRuleResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_ob_offering_api_proto_rawDescGZIP(), []int{3}
}

// Deprecated: Do not use.
func (x *ListPricingRuleResult) GetPricingRules() []*v1.PricingRuleModel {
	if x != nil {
		return x.PricingRules
	}
	return nil
}

func (x *ListPricingRuleResult) GetPricingRulesV2() []*v2.PricingRule {
	if x != nil {
		return x.PricingRulesV2
	}
	return nil
}

func (x *ListPricingRuleResult) GetServices() []*v1.ServiceModel {
	if x != nil {
		return x.Services
	}
	return nil
}

var File_moego_client_online_booking_v1_ob_offering_api_proto protoreflect.FileDescriptor

var file_moego_client_online_booking_v1_ob_offering_api_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x6f, 0x62, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65,
	0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x31, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x32, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62, 0x5f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb8, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x10,
	0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01,
	0x22, 0xea, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62,
	0x6c, 0x65, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x42, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x61, 0x0a, 0x10, 0x61, 0x64,
	0x64, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x42, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0f, 0x61, 0x64,
	0x64, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x22, 0xf2, 0x02,
	0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x4c, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x02, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x48, 0x01,
	0x52, 0x09, 0x64, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x51, 0x0a, 0x09, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x44,
	0x65, 0x66, 0x48, 0x01, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x10,
	0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01,
	0x42, 0x15, 0x0a, 0x13, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x22, 0x81, 0x02, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x53, 0x0a, 0x0d,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x73, 0x12, 0x4f, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c,
	0x65, 0x73, 0x5f, 0x76, 0x32, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x56, 0x32, 0x12, 0x42, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x32, 0xae, 0x02, 0x0a, 0x11, 0x4f, 0x42, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x97, 0x01, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x92, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x66, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_online_booking_v1_ob_offering_api_proto_rawDescOnce sync.Once
	file_moego_client_online_booking_v1_ob_offering_api_proto_rawDescData = file_moego_client_online_booking_v1_ob_offering_api_proto_rawDesc
)

func file_moego_client_online_booking_v1_ob_offering_api_proto_rawDescGZIP() []byte {
	file_moego_client_online_booking_v1_ob_offering_api_proto_rawDescOnce.Do(func() {
		file_moego_client_online_booking_v1_ob_offering_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_online_booking_v1_ob_offering_api_proto_rawDescData)
	})
	return file_moego_client_online_booking_v1_ob_offering_api_proto_rawDescData
}

var file_moego_client_online_booking_v1_ob_offering_api_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_client_online_booking_v1_ob_offering_api_proto_goTypes = []interface{}{
	(*GetApplicableOfferingsRequest)(nil),  // 0: moego.client.online_booking.v1.GetApplicableOfferingsRequest
	(*GetApplicableOfferingsResponse)(nil), // 1: moego.client.online_booking.v1.GetApplicableOfferingsResponse
	(*ListPricingRuleParams)(nil),          // 2: moego.client.online_booking.v1.ListPricingRuleParams
	(*ListPricingRuleResult)(nil),          // 3: moego.client.online_booking.v1.ListPricingRuleResult
	(v1.ServiceItemType)(0),                // 4: moego.models.offering.v1.ServiceItemType
	(*v11.OBOfferingCategoryView)(nil),     // 5: moego.models.online_booking.v1.OBOfferingCategoryView
	(*v1.ListPricingRuleFilter)(nil),       // 6: moego.models.offering.v1.ListPricingRuleFilter
	(*v11.ServiceDateRangeDef)(nil),        // 7: moego.models.online_booking.v1.ServiceDateRangeDef
	(*v11.ServiceDateListDef)(nil),         // 8: moego.models.online_booking.v1.ServiceDateListDef
	(*v1.PricingRuleModel)(nil),            // 9: moego.models.offering.v1.PricingRuleModel
	(*v2.PricingRule)(nil),                 // 10: moego.models.offering.v2.PricingRule
	(*v1.ServiceModel)(nil),                // 11: moego.models.offering.v1.ServiceModel
}
var file_moego_client_online_booking_v1_ob_offering_api_proto_depIdxs = []int32{
	4,  // 0: moego.client.online_booking.v1.GetApplicableOfferingsRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	5,  // 1: moego.client.online_booking.v1.GetApplicableOfferingsResponse.service_categories:type_name -> moego.models.online_booking.v1.OBOfferingCategoryView
	5,  // 2: moego.client.online_booking.v1.GetApplicableOfferingsResponse.addon_categories:type_name -> moego.models.online_booking.v1.OBOfferingCategoryView
	6,  // 3: moego.client.online_booking.v1.ListPricingRuleParams.filter:type_name -> moego.models.offering.v1.ListPricingRuleFilter
	7,  // 4: moego.client.online_booking.v1.ListPricingRuleParams.date_range:type_name -> moego.models.online_booking.v1.ServiceDateRangeDef
	8,  // 5: moego.client.online_booking.v1.ListPricingRuleParams.date_list:type_name -> moego.models.online_booking.v1.ServiceDateListDef
	9,  // 6: moego.client.online_booking.v1.ListPricingRuleResult.pricing_rules:type_name -> moego.models.offering.v1.PricingRuleModel
	10, // 7: moego.client.online_booking.v1.ListPricingRuleResult.pricing_rules_v2:type_name -> moego.models.offering.v2.PricingRule
	11, // 8: moego.client.online_booking.v1.ListPricingRuleResult.services:type_name -> moego.models.offering.v1.ServiceModel
	0,  // 9: moego.client.online_booking.v1.OBOfferingService.GetApplicableOfferings:input_type -> moego.client.online_booking.v1.GetApplicableOfferingsRequest
	2,  // 10: moego.client.online_booking.v1.OBOfferingService.ListPricingRule:input_type -> moego.client.online_booking.v1.ListPricingRuleParams
	1,  // 11: moego.client.online_booking.v1.OBOfferingService.GetApplicableOfferings:output_type -> moego.client.online_booking.v1.GetApplicableOfferingsResponse
	3,  // 12: moego.client.online_booking.v1.OBOfferingService.ListPricingRule:output_type -> moego.client.online_booking.v1.ListPricingRuleResult
	11, // [11:13] is the sub-list for method output_type
	9,  // [9:11] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_moego_client_online_booking_v1_ob_offering_api_proto_init() }
func file_moego_client_online_booking_v1_ob_offering_api_proto_init() {
	if File_moego_client_online_booking_v1_ob_offering_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_online_booking_v1_ob_offering_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicableOfferingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_ob_offering_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicableOfferingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_ob_offering_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRuleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_ob_offering_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRuleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_online_booking_v1_ob_offering_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*GetApplicableOfferingsRequest_Name)(nil),
		(*GetApplicableOfferingsRequest_Domain)(nil),
	}
	file_moego_client_online_booking_v1_ob_offering_api_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*ListPricingRuleParams_Name)(nil),
		(*ListPricingRuleParams_Domain)(nil),
		(*ListPricingRuleParams_DateRange)(nil),
		(*ListPricingRuleParams_DateList)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_online_booking_v1_ob_offering_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_online_booking_v1_ob_offering_api_proto_goTypes,
		DependencyIndexes: file_moego_client_online_booking_v1_ob_offering_api_proto_depIdxs,
		MessageInfos:      file_moego_client_online_booking_v1_ob_offering_api_proto_msgTypes,
	}.Build()
	File_moego_client_online_booking_v1_ob_offering_api_proto = out.File
	file_moego_client_online_booking_v1_ob_offering_api_proto_rawDesc = nil
	file_moego_client_online_booking_v1_ob_offering_api_proto_goTypes = nil
	file_moego_client_online_booking_v1_ob_offering_api_proto_depIdxs = nil
}
