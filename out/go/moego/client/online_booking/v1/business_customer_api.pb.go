// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/online_booking/v1/business_customer_api.proto

package onlinebookingapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Create customer params
type CreateCustomerParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*CreateCustomerParams_Name
	//	*CreateCustomerParams_Domain
	Anonymous isCreateCustomerParams_Anonymous `protobuf_oneof:"anonymous"`
	// customer
	Customer *CreateCustomerParams_Customer `protobuf:"bytes,3,opt,name=customer,proto3" json:"customer,omitempty"`
}

func (x *CreateCustomerParams) Reset() {
	*x = CreateCustomerParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCustomerParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerParams) ProtoMessage() {}

func (x *CreateCustomerParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerParams.ProtoReflect.Descriptor instead.
func (*CreateCustomerParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_business_customer_api_proto_rawDescGZIP(), []int{0}
}

func (m *CreateCustomerParams) GetAnonymous() isCreateCustomerParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *CreateCustomerParams) GetName() string {
	if x, ok := x.GetAnonymous().(*CreateCustomerParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *CreateCustomerParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*CreateCustomerParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *CreateCustomerParams) GetCustomer() *CreateCustomerParams_Customer {
	if x != nil {
		return x.Customer
	}
	return nil
}

type isCreateCustomerParams_Anonymous interface {
	isCreateCustomerParams_Anonymous()
}

type CreateCustomerParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type CreateCustomerParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*CreateCustomerParams_Name) isCreateCustomerParams_Anonymous() {}

func (*CreateCustomerParams_Domain) isCreateCustomerParams_Anonymous() {}

// Create customer result
type CreateCustomerResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateCustomerResult) Reset() {
	*x = CreateCustomerResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCustomerResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerResult) ProtoMessage() {}

func (x *CreateCustomerResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerResult.ProtoReflect.Descriptor instead.
func (*CreateCustomerResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_business_customer_api_proto_rawDescGZIP(), []int{1}
}

// Update customer params
type UpdateCustomerParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*UpdateCustomerParams_Name
	//	*UpdateCustomerParams_Domain
	Anonymous isUpdateCustomerParams_Anonymous `protobuf_oneof:"anonymous"`
	// customer id
	Id int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// customer update def
	Customer *v1.BusinessCustomerUpdateDef `protobuf:"bytes,4,opt,name=customer,proto3" json:"customer,omitempty"`
}

func (x *UpdateCustomerParams) Reset() {
	*x = UpdateCustomerParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCustomerParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerParams) ProtoMessage() {}

func (x *UpdateCustomerParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerParams.ProtoReflect.Descriptor instead.
func (*UpdateCustomerParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_business_customer_api_proto_rawDescGZIP(), []int{2}
}

func (m *UpdateCustomerParams) GetAnonymous() isUpdateCustomerParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *UpdateCustomerParams) GetName() string {
	if x, ok := x.GetAnonymous().(*UpdateCustomerParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *UpdateCustomerParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*UpdateCustomerParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *UpdateCustomerParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCustomerParams) GetCustomer() *v1.BusinessCustomerUpdateDef {
	if x != nil {
		return x.Customer
	}
	return nil
}

type isUpdateCustomerParams_Anonymous interface {
	isUpdateCustomerParams_Anonymous()
}

type UpdateCustomerParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type UpdateCustomerParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*UpdateCustomerParams_Name) isUpdateCustomerParams_Anonymous() {}

func (*UpdateCustomerParams_Domain) isUpdateCustomerParams_Anonymous() {}

// Update customer result
type UpdateCustomerResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateCustomerResult) Reset() {
	*x = UpdateCustomerResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCustomerResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerResult) ProtoMessage() {}

func (x *UpdateCustomerResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerResult.ProtoReflect.Descriptor instead.
func (*UpdateCustomerResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_business_customer_api_proto_rawDescGZIP(), []int{3}
}

// Verify phone number params
type VerifyPhoneNumberParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*VerifyPhoneNumberParams_Name
	//	*VerifyPhoneNumberParams_Domain
	Anonymous isVerifyPhoneNumberParams_Anonymous `protobuf_oneof:"anonymous"`
	// customer id, optional
	// 如果传了，则会校验 primary contact 是否跟 phone number 一致
	Id *int64 `protobuf:"varint,3,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *VerifyPhoneNumberParams) Reset() {
	*x = VerifyPhoneNumberParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyPhoneNumberParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyPhoneNumberParams) ProtoMessage() {}

func (x *VerifyPhoneNumberParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyPhoneNumberParams.ProtoReflect.Descriptor instead.
func (*VerifyPhoneNumberParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_business_customer_api_proto_rawDescGZIP(), []int{4}
}

func (m *VerifyPhoneNumberParams) GetAnonymous() isVerifyPhoneNumberParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *VerifyPhoneNumberParams) GetName() string {
	if x, ok := x.GetAnonymous().(*VerifyPhoneNumberParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *VerifyPhoneNumberParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*VerifyPhoneNumberParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *VerifyPhoneNumberParams) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *VerifyPhoneNumberParams) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

type isVerifyPhoneNumberParams_Anonymous interface {
	isVerifyPhoneNumberParams_Anonymous()
}

type VerifyPhoneNumberParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type VerifyPhoneNumberParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*VerifyPhoneNumberParams_Name) isVerifyPhoneNumberParams_Anonymous() {}

func (*VerifyPhoneNumberParams_Domain) isVerifyPhoneNumberParams_Anonymous() {}

// Verify phone number result
type VerifyPhoneNumberResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// verification token, used for verification together with the verification code
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *VerifyPhoneNumberResult) Reset() {
	*x = VerifyPhoneNumberResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyPhoneNumberResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyPhoneNumberResult) ProtoMessage() {}

func (x *VerifyPhoneNumberResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyPhoneNumberResult.ProtoReflect.Descriptor instead.
func (*VerifyPhoneNumberResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_business_customer_api_proto_rawDescGZIP(), []int{5}
}

func (x *VerifyPhoneNumberResult) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// Confirm phone number params
type ConfirmPhoneNumberParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*ConfirmPhoneNumberParams_Name
	//	*ConfirmPhoneNumberParams_Domain
	Anonymous isConfirmPhoneNumberParams_Anonymous `protobuf_oneof:"anonymous"`
	// phone number
	PhoneNumber string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// verification token, used for verification together with the verification code
	Token string `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
	// verification code
	Code string `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *ConfirmPhoneNumberParams) Reset() {
	*x = ConfirmPhoneNumberParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmPhoneNumberParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmPhoneNumberParams) ProtoMessage() {}

func (x *ConfirmPhoneNumberParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmPhoneNumberParams.ProtoReflect.Descriptor instead.
func (*ConfirmPhoneNumberParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_business_customer_api_proto_rawDescGZIP(), []int{6}
}

func (m *ConfirmPhoneNumberParams) GetAnonymous() isConfirmPhoneNumberParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *ConfirmPhoneNumberParams) GetName() string {
	if x, ok := x.GetAnonymous().(*ConfirmPhoneNumberParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *ConfirmPhoneNumberParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*ConfirmPhoneNumberParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *ConfirmPhoneNumberParams) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *ConfirmPhoneNumberParams) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *ConfirmPhoneNumberParams) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type isConfirmPhoneNumberParams_Anonymous interface {
	isConfirmPhoneNumberParams_Anonymous()
}

type ConfirmPhoneNumberParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type ConfirmPhoneNumberParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*ConfirmPhoneNumberParams_Name) isConfirmPhoneNumberParams_Anonymous() {}

func (*ConfirmPhoneNumberParams_Domain) isConfirmPhoneNumberParams_Anonymous() {}

// Confirm phone number result
type ConfirmPhoneNumberResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *ConfirmPhoneNumberResult) Reset() {
	*x = ConfirmPhoneNumberResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmPhoneNumberResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmPhoneNumberResult) ProtoMessage() {}

func (x *ConfirmPhoneNumberResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmPhoneNumberResult.ProtoReflect.Descriptor instead.
func (*ConfirmPhoneNumberResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_business_customer_api_proto_rawDescGZIP(), []int{7}
}

func (x *ConfirmPhoneNumberResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Change login phone number params
type ChangeLoginPhoneNumberParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*ChangeLoginPhoneNumberParams_Name
	//	*ChangeLoginPhoneNumberParams_Domain
	Anonymous isChangeLoginPhoneNumberParams_Anonymous `protobuf_oneof:"anonymous"`
	// phone number
	PhoneNumber string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// verification token, used for verification together with the verification code
	Token string `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
	// verification code
	Code string `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *ChangeLoginPhoneNumberParams) Reset() {
	*x = ChangeLoginPhoneNumberParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeLoginPhoneNumberParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeLoginPhoneNumberParams) ProtoMessage() {}

func (x *ChangeLoginPhoneNumberParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeLoginPhoneNumberParams.ProtoReflect.Descriptor instead.
func (*ChangeLoginPhoneNumberParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_business_customer_api_proto_rawDescGZIP(), []int{8}
}

func (m *ChangeLoginPhoneNumberParams) GetAnonymous() isChangeLoginPhoneNumberParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *ChangeLoginPhoneNumberParams) GetName() string {
	if x, ok := x.GetAnonymous().(*ChangeLoginPhoneNumberParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *ChangeLoginPhoneNumberParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*ChangeLoginPhoneNumberParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *ChangeLoginPhoneNumberParams) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *ChangeLoginPhoneNumberParams) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *ChangeLoginPhoneNumberParams) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type isChangeLoginPhoneNumberParams_Anonymous interface {
	isChangeLoginPhoneNumberParams_Anonymous()
}

type ChangeLoginPhoneNumberParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type ChangeLoginPhoneNumberParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*ChangeLoginPhoneNumberParams_Name) isChangeLoginPhoneNumberParams_Anonymous() {}

func (*ChangeLoginPhoneNumberParams_Domain) isChangeLoginPhoneNumberParams_Anonymous() {}

// Change login phone number result
type ChangeLoginPhoneNumberResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *ChangeLoginPhoneNumberResult) Reset() {
	*x = ChangeLoginPhoneNumberResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeLoginPhoneNumberResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeLoginPhoneNumberResult) ProtoMessage() {}

func (x *ChangeLoginPhoneNumberResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeLoginPhoneNumberResult.ProtoReflect.Descriptor instead.
func (*ChangeLoginPhoneNumberResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_business_customer_api_proto_rawDescGZIP(), []int{9}
}

func (x *ChangeLoginPhoneNumberResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// customer def
type CreateCustomerParams_Customer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// first name
	FirstName string `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// email
	Email string `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	// Birthday
	Birthday *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=birthday,proto3,oneof" json:"birthday,omitempty"`
	// Additional info
	AdditionalInfo *CreateCustomerParams_Customer_AdditionalInfo `protobuf:"bytes,6,opt,name=additional_info,json=additionalInfo,proto3,oneof" json:"additional_info,omitempty"`
	// Address
	Address *CreateCustomerParams_Customer_Address `protobuf:"bytes,7,opt,name=address,proto3,oneof" json:"address,omitempty"`
	// Answers map
	AnswersMap map[string]*structpb.Value `protobuf:"bytes,8,rep,name=answers_map,json=answersMap,proto3" json:"answers_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CreateCustomerParams_Customer) Reset() {
	*x = CreateCustomerParams_Customer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCustomerParams_Customer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerParams_Customer) ProtoMessage() {}

func (x *CreateCustomerParams_Customer) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerParams_Customer.ProtoReflect.Descriptor instead.
func (*CreateCustomerParams_Customer) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_business_customer_api_proto_rawDescGZIP(), []int{0, 0}
}

func (x *CreateCustomerParams_Customer) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *CreateCustomerParams_Customer) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *CreateCustomerParams_Customer) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *CreateCustomerParams_Customer) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateCustomerParams_Customer) GetBirthday() *timestamppb.Timestamp {
	if x != nil {
		return x.Birthday
	}
	return nil
}

func (x *CreateCustomerParams_Customer) GetAdditionalInfo() *CreateCustomerParams_Customer_AdditionalInfo {
	if x != nil {
		return x.AdditionalInfo
	}
	return nil
}

func (x *CreateCustomerParams_Customer) GetAddress() *CreateCustomerParams_Customer_Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *CreateCustomerParams_Customer) GetAnswersMap() map[string]*structpb.Value {
	if x != nil {
		return x.AnswersMap
	}
	return nil
}

// OB additional info
type CreateCustomerParams_Customer_AdditionalInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Referral source
	ReferralSourceId *int32 `protobuf:"varint,1,opt,name=referral_source_id,json=referralSourceId,proto3,oneof" json:"referral_source_id,omitempty"`
	// Referral source desc
	ReferralSourceDesc *string `protobuf:"bytes,2,opt,name=referral_source_desc,json=referralSourceDesc,proto3,oneof" json:"referral_source_desc,omitempty"`
	// Preferred groomer
	PreferredGroomerId *int32 `protobuf:"varint,3,opt,name=preferred_groomer_id,json=preferredGroomerId,proto3,oneof" json:"preferred_groomer_id,omitempty"`
	// Preferred frequency
	PreferredFrequencyDay *int32 `protobuf:"varint,4,opt,name=preferred_frequency_day,json=preferredFrequencyDay,proto3,oneof" json:"preferred_frequency_day,omitempty"`
	// Preferred frequency type (0-by days, 1-by weeks)
	PreferredFrequencyType *int32 `protobuf:"varint,5,opt,name=preferred_frequency_type,json=preferredFrequencyType,proto3,oneof" json:"preferred_frequency_type,omitempty"`
	// Preferred days of the week
	PreferredDay []int32 `protobuf:"varint,6,rep,packed,name=preferred_day,json=preferredDay,proto3" json:"preferred_day,omitempty"`
	// Preferred times of the day
	PreferredTime []int32 `protobuf:"varint,7,rep,packed,name=preferred_time,json=preferredTime,proto3" json:"preferred_time,omitempty"`
}

func (x *CreateCustomerParams_Customer_AdditionalInfo) Reset() {
	*x = CreateCustomerParams_Customer_AdditionalInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCustomerParams_Customer_AdditionalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerParams_Customer_AdditionalInfo) ProtoMessage() {}

func (x *CreateCustomerParams_Customer_AdditionalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerParams_Customer_AdditionalInfo.ProtoReflect.Descriptor instead.
func (*CreateCustomerParams_Customer_AdditionalInfo) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_business_customer_api_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *CreateCustomerParams_Customer_AdditionalInfo) GetReferralSourceId() int32 {
	if x != nil && x.ReferralSourceId != nil {
		return *x.ReferralSourceId
	}
	return 0
}

func (x *CreateCustomerParams_Customer_AdditionalInfo) GetReferralSourceDesc() string {
	if x != nil && x.ReferralSourceDesc != nil {
		return *x.ReferralSourceDesc
	}
	return ""
}

func (x *CreateCustomerParams_Customer_AdditionalInfo) GetPreferredGroomerId() int32 {
	if x != nil && x.PreferredGroomerId != nil {
		return *x.PreferredGroomerId
	}
	return 0
}

func (x *CreateCustomerParams_Customer_AdditionalInfo) GetPreferredFrequencyDay() int32 {
	if x != nil && x.PreferredFrequencyDay != nil {
		return *x.PreferredFrequencyDay
	}
	return 0
}

func (x *CreateCustomerParams_Customer_AdditionalInfo) GetPreferredFrequencyType() int32 {
	if x != nil && x.PreferredFrequencyType != nil {
		return *x.PreferredFrequencyType
	}
	return 0
}

func (x *CreateCustomerParams_Customer_AdditionalInfo) GetPreferredDay() []int32 {
	if x != nil {
		return x.PreferredDay
	}
	return nil
}

func (x *CreateCustomerParams_Customer_AdditionalInfo) GetPreferredTime() []int32 {
	if x != nil {
		return x.PreferredTime
	}
	return nil
}

// address
type CreateCustomerParams_Customer_Address struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// address 1
	Address1 *string `protobuf:"bytes,1,opt,name=address1,proto3,oneof" json:"address1,omitempty"`
	// address 2
	Address2 *string `protobuf:"bytes,2,opt,name=address2,proto3,oneof" json:"address2,omitempty"`
	// city
	City *string `protobuf:"bytes,3,opt,name=city,proto3,oneof" json:"city,omitempty"`
	// state
	State *string `protobuf:"bytes,4,opt,name=state,proto3,oneof" json:"state,omitempty"`
	// zip code
	Zipcode *string `protobuf:"bytes,5,opt,name=zipcode,proto3,oneof" json:"zipcode,omitempty"`
	// country
	Country *string `protobuf:"bytes,6,opt,name=country,proto3,oneof" json:"country,omitempty"`
	// lat
	Lat *string `protobuf:"bytes,7,opt,name=lat,proto3,oneof" json:"lat,omitempty"`
	// lng
	Lng *string `protobuf:"bytes,8,opt,name=lng,proto3,oneof" json:"lng,omitempty"`
}

func (x *CreateCustomerParams_Customer_Address) Reset() {
	*x = CreateCustomerParams_Customer_Address{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCustomerParams_Customer_Address) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerParams_Customer_Address) ProtoMessage() {}

func (x *CreateCustomerParams_Customer_Address) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerParams_Customer_Address.ProtoReflect.Descriptor instead.
func (*CreateCustomerParams_Customer_Address) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_business_customer_api_proto_rawDescGZIP(), []int{0, 0, 2}
}

func (x *CreateCustomerParams_Customer_Address) GetAddress1() string {
	if x != nil && x.Address1 != nil {
		return *x.Address1
	}
	return ""
}

func (x *CreateCustomerParams_Customer_Address) GetAddress2() string {
	if x != nil && x.Address2 != nil {
		return *x.Address2
	}
	return ""
}

func (x *CreateCustomerParams_Customer_Address) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *CreateCustomerParams_Customer_Address) GetState() string {
	if x != nil && x.State != nil {
		return *x.State
	}
	return ""
}

func (x *CreateCustomerParams_Customer_Address) GetZipcode() string {
	if x != nil && x.Zipcode != nil {
		return *x.Zipcode
	}
	return ""
}

func (x *CreateCustomerParams_Customer_Address) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

func (x *CreateCustomerParams_Customer_Address) GetLat() string {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return ""
}

func (x *CreateCustomerParams_Customer_Address) GetLng() string {
	if x != nil && x.Lng != nil {
		return *x.Lng
	}
	return ""
}

var File_moego_client_online_booking_v1_business_customer_api_proto protoreflect.FileDescriptor

var file_moego_client_online_booking_v1_business_customer_api_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc3, 0x0e, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x63, 0x0a,
	0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x1a, 0x83, 0x0d, 0x0a, 0x08, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12,
	0x26, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52, 0x09, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x18, 0x32, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a,
	0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x14, 0x52, 0x0b, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x72, 0x07, 0x18,
	0x32, 0xd0, 0x01, 0x01, 0x60, 0x01, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x3b, 0x0a,
	0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x08, 0x62,
	0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x7a, 0x0a, 0x0f, 0x61, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x48, 0x01, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x64, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x48, 0x02,
	0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x6e, 0x0a, 0x0b,
	0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x08, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0a, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x4d, 0x61, 0x70, 0x1a, 0x55, 0x0a, 0x0f,
	0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0xa9, 0x04, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3a, 0x0a, 0x12, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x10, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x3f, 0x0a, 0x14, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x01, 0x52, 0x12, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x73, 0x63,
	0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x14, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64,
	0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x02, 0x52, 0x12, 0x70, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x44, 0x0a, 0x17, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64,
	0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x03, 0x52,
	0x15, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x44, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x46, 0x0a, 0x18, 0x70, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x1a, 0x02, 0x28, 0x00, 0x48, 0x04, 0x52, 0x16, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65,
	0x64, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x64,
	0x61, 0x79, 0x18, 0x06, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x65, 0x64, 0x44, 0x61, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0d,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x15, 0x0a,
	0x13, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61,
	0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x42, 0x17, 0x0a,
	0x15, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x64,
	0x61, 0x79, 0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64,
	0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x1a,
	0x99, 0x03, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2b, 0x0a, 0x08, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52, 0x08, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x31, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72,
	0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x48, 0x01, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x32, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x48,
	0x02, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05,
	0x10, 0x00, 0x18, 0xff, 0x01, 0x48, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x28, 0x0a, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x32, 0x48, 0x04, 0x52,
	0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x07, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x05, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x06, 0x52, 0x03,
	0x6c, 0x61, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x03, 0x6c, 0x6e, 0x67, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x07, 0x52, 0x03,
	0x6c, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x31, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32,
	0x42, 0x07, 0x0a, 0x05, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x42,
	0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x06, 0x0a, 0x04, 0x5f,
	0x6c, 0x61, 0x74, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x6c, 0x6e, 0x67, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x61, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x0a, 0x0a, 0x08,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e,
	0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0xd5, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x62, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e,
	0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x16, 0x0a, 0x14, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0xb8, 0x01, 0x0a, 0x17, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x22,
	0x04, 0x20, 0x00, 0x40, 0x01, 0x48, 0x01, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x34,
	0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x72, 0x0c, 0x32, 0x0a, 0x5e, 0x5c, 0x64,
	0x7b, 0x31, 0x2c, 0x31, 0x38, 0x7d, 0x24, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75,
	0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x22, 0x2f, 0x0a,
	0x17, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xcf,
	0x01, 0x0a, 0x18, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x34, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x72, 0x0c, 0x32, 0x0a, 0x5e, 0x5c, 0x64, 0x7b, 0x31, 0x2c,
	0x31, 0x38, 0x7d, 0x24, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x25, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x72, 0x0c, 0x32, 0x0a, 0x5e, 0x5b,
	0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x36, 0x7d, 0x24, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x10,
	0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01,
	0x22, 0x34, 0x0a, 0x18, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0xd3, 0x01, 0x0a, 0x1c, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x34, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0xfa,
	0x42, 0x0e, 0x72, 0x0c, 0x32, 0x0a, 0x5e, 0x5c, 0x64, 0x7b, 0x31, 0x2c, 0x31, 0x38, 0x7d, 0x24,
	0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x25, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x72, 0x0c, 0x32, 0x0a, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d,
	0x7b, 0x36, 0x7d, 0x24, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e,
	0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x38, 0x0a, 0x1c,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x32, 0xbf, 0x05, 0x0a, 0x17, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x7c, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x7c, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x85,
	0x01, 0x0a, 0x11, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x68, 0x6f, 0x6e,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x88, 0x01, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x94, 0x01, 0x0a, 0x16, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x3c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x92, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x66, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_online_booking_v1_business_customer_api_proto_rawDescOnce sync.Once
	file_moego_client_online_booking_v1_business_customer_api_proto_rawDescData = file_moego_client_online_booking_v1_business_customer_api_proto_rawDesc
)

func file_moego_client_online_booking_v1_business_customer_api_proto_rawDescGZIP() []byte {
	file_moego_client_online_booking_v1_business_customer_api_proto_rawDescOnce.Do(func() {
		file_moego_client_online_booking_v1_business_customer_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_online_booking_v1_business_customer_api_proto_rawDescData)
	})
	return file_moego_client_online_booking_v1_business_customer_api_proto_rawDescData
}

var file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_moego_client_online_booking_v1_business_customer_api_proto_goTypes = []interface{}{
	(*CreateCustomerParams)(nil),                         // 0: moego.client.online_booking.v1.CreateCustomerParams
	(*CreateCustomerResult)(nil),                         // 1: moego.client.online_booking.v1.CreateCustomerResult
	(*UpdateCustomerParams)(nil),                         // 2: moego.client.online_booking.v1.UpdateCustomerParams
	(*UpdateCustomerResult)(nil),                         // 3: moego.client.online_booking.v1.UpdateCustomerResult
	(*VerifyPhoneNumberParams)(nil),                      // 4: moego.client.online_booking.v1.VerifyPhoneNumberParams
	(*VerifyPhoneNumberResult)(nil),                      // 5: moego.client.online_booking.v1.VerifyPhoneNumberResult
	(*ConfirmPhoneNumberParams)(nil),                     // 6: moego.client.online_booking.v1.ConfirmPhoneNumberParams
	(*ConfirmPhoneNumberResult)(nil),                     // 7: moego.client.online_booking.v1.ConfirmPhoneNumberResult
	(*ChangeLoginPhoneNumberParams)(nil),                 // 8: moego.client.online_booking.v1.ChangeLoginPhoneNumberParams
	(*ChangeLoginPhoneNumberResult)(nil),                 // 9: moego.client.online_booking.v1.ChangeLoginPhoneNumberResult
	(*CreateCustomerParams_Customer)(nil),                // 10: moego.client.online_booking.v1.CreateCustomerParams.Customer
	nil,                                                  // 11: moego.client.online_booking.v1.CreateCustomerParams.Customer.AnswersMapEntry
	(*CreateCustomerParams_Customer_AdditionalInfo)(nil), // 12: moego.client.online_booking.v1.CreateCustomerParams.Customer.AdditionalInfo
	(*CreateCustomerParams_Customer_Address)(nil),        // 13: moego.client.online_booking.v1.CreateCustomerParams.Customer.Address
	(*v1.BusinessCustomerUpdateDef)(nil),                 // 14: moego.models.business_customer.v1.BusinessCustomerUpdateDef
	(*timestamppb.Timestamp)(nil),                        // 15: google.protobuf.Timestamp
	(*structpb.Value)(nil),                               // 16: google.protobuf.Value
}
var file_moego_client_online_booking_v1_business_customer_api_proto_depIdxs = []int32{
	10, // 0: moego.client.online_booking.v1.CreateCustomerParams.customer:type_name -> moego.client.online_booking.v1.CreateCustomerParams.Customer
	14, // 1: moego.client.online_booking.v1.UpdateCustomerParams.customer:type_name -> moego.models.business_customer.v1.BusinessCustomerUpdateDef
	15, // 2: moego.client.online_booking.v1.CreateCustomerParams.Customer.birthday:type_name -> google.protobuf.Timestamp
	12, // 3: moego.client.online_booking.v1.CreateCustomerParams.Customer.additional_info:type_name -> moego.client.online_booking.v1.CreateCustomerParams.Customer.AdditionalInfo
	13, // 4: moego.client.online_booking.v1.CreateCustomerParams.Customer.address:type_name -> moego.client.online_booking.v1.CreateCustomerParams.Customer.Address
	11, // 5: moego.client.online_booking.v1.CreateCustomerParams.Customer.answers_map:type_name -> moego.client.online_booking.v1.CreateCustomerParams.Customer.AnswersMapEntry
	16, // 6: moego.client.online_booking.v1.CreateCustomerParams.Customer.AnswersMapEntry.value:type_name -> google.protobuf.Value
	0,  // 7: moego.client.online_booking.v1.BusinessCustomerService.CreateCustomer:input_type -> moego.client.online_booking.v1.CreateCustomerParams
	2,  // 8: moego.client.online_booking.v1.BusinessCustomerService.UpdateCustomer:input_type -> moego.client.online_booking.v1.UpdateCustomerParams
	4,  // 9: moego.client.online_booking.v1.BusinessCustomerService.VerifyPhoneNumber:input_type -> moego.client.online_booking.v1.VerifyPhoneNumberParams
	6,  // 10: moego.client.online_booking.v1.BusinessCustomerService.ConfirmPhoneNumber:input_type -> moego.client.online_booking.v1.ConfirmPhoneNumberParams
	8,  // 11: moego.client.online_booking.v1.BusinessCustomerService.ChangeLoginPhoneNumber:input_type -> moego.client.online_booking.v1.ChangeLoginPhoneNumberParams
	1,  // 12: moego.client.online_booking.v1.BusinessCustomerService.CreateCustomer:output_type -> moego.client.online_booking.v1.CreateCustomerResult
	3,  // 13: moego.client.online_booking.v1.BusinessCustomerService.UpdateCustomer:output_type -> moego.client.online_booking.v1.UpdateCustomerResult
	5,  // 14: moego.client.online_booking.v1.BusinessCustomerService.VerifyPhoneNumber:output_type -> moego.client.online_booking.v1.VerifyPhoneNumberResult
	7,  // 15: moego.client.online_booking.v1.BusinessCustomerService.ConfirmPhoneNumber:output_type -> moego.client.online_booking.v1.ConfirmPhoneNumberResult
	9,  // 16: moego.client.online_booking.v1.BusinessCustomerService.ChangeLoginPhoneNumber:output_type -> moego.client.online_booking.v1.ChangeLoginPhoneNumberResult
	12, // [12:17] is the sub-list for method output_type
	7,  // [7:12] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_moego_client_online_booking_v1_business_customer_api_proto_init() }
func file_moego_client_online_booking_v1_business_customer_api_proto_init() {
	if File_moego_client_online_booking_v1_business_customer_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCustomerParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCustomerResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCustomerParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCustomerResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyPhoneNumberParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyPhoneNumberResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmPhoneNumberParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmPhoneNumberResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeLoginPhoneNumberParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeLoginPhoneNumberResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCustomerParams_Customer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCustomerParams_Customer_AdditionalInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCustomerParams_Customer_Address); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*CreateCustomerParams_Name)(nil),
		(*CreateCustomerParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*UpdateCustomerParams_Name)(nil),
		(*UpdateCustomerParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*VerifyPhoneNumberParams_Name)(nil),
		(*VerifyPhoneNumberParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*ConfirmPhoneNumberParams_Name)(nil),
		(*ConfirmPhoneNumberParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*ChangeLoginPhoneNumberParams_Name)(nil),
		(*ChangeLoginPhoneNumberParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes[13].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_online_booking_v1_business_customer_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_online_booking_v1_business_customer_api_proto_goTypes,
		DependencyIndexes: file_moego_client_online_booking_v1_business_customer_api_proto_depIdxs,
		MessageInfos:      file_moego_client_online_booking_v1_business_customer_api_proto_msgTypes,
	}.Build()
	File_moego_client_online_booking_v1_business_customer_api_proto = out.File
	file_moego_client_online_booking_v1_business_customer_api_proto_rawDesc = nil
	file_moego_client_online_booking_v1_business_customer_api_proto_goTypes = nil
	file_moego_client_online_booking_v1_business_customer_api_proto_depIdxs = nil
}
