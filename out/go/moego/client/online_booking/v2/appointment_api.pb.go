// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/online_booking/v2/appointment_api.proto

package onlinebookingapipb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Update appointment params
type UpdateAppointmentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*UpdateAppointmentParams_Name
	//	*UpdateAppointmentParams_Domain
	Anonymous isUpdateAppointmentParams_Anonymous `protobuf_oneof:"anonymous"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// appointment to update
	Appointment *v2.UpdateAppointmentRequest_Appointment `protobuf:"bytes,5,opt,name=appointment,proto3,oneof" json:"appointment,omitempty"`
	// pet details to update
	PetDetails []*v2.UpdateAppointmentRequest_PetDetail `protobuf:"bytes,4,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
}

func (x *UpdateAppointmentParams) Reset() {
	*x = UpdateAppointmentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v2_appointment_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentParams) ProtoMessage() {}

func (x *UpdateAppointmentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v2_appointment_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentParams.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v2_appointment_api_proto_rawDescGZIP(), []int{0}
}

func (m *UpdateAppointmentParams) GetAnonymous() isUpdateAppointmentParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *UpdateAppointmentParams) GetName() string {
	if x, ok := x.GetAnonymous().(*UpdateAppointmentParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *UpdateAppointmentParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*UpdateAppointmentParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *UpdateAppointmentParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *UpdateAppointmentParams) GetAppointment() *v2.UpdateAppointmentRequest_Appointment {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *UpdateAppointmentParams) GetPetDetails() []*v2.UpdateAppointmentRequest_PetDetail {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

type isUpdateAppointmentParams_Anonymous interface {
	isUpdateAppointmentParams_Anonymous()
}

type UpdateAppointmentParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type UpdateAppointmentParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*UpdateAppointmentParams_Name) isUpdateAppointmentParams_Anonymous() {}

func (*UpdateAppointmentParams_Domain) isUpdateAppointmentParams_Anonymous() {}

// Update appointment result
type UpdateAppointmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateAppointmentResult) Reset() {
	*x = UpdateAppointmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v2_appointment_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentResult) ProtoMessage() {}

func (x *UpdateAppointmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v2_appointment_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentResult.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v2_appointment_api_proto_rawDescGZIP(), []int{1}
}

var File_moego_client_online_booking_v2_appointment_api_proto protoreflect.FileDescriptor

var file_moego_client_online_booking_v2_appointment_api_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32,
	0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x76, 0x32, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe9, 0x02, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x69, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x01, 0x52, 0x0b,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x61,
	0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03,
	0xf8, 0x42, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x22, 0x19, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0x98,
	0x01, 0x0a, 0x0e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x50,
	0x49, 0x12, 0x85, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x92, 0x01, 0x0a, 0x26, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x66, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_online_booking_v2_appointment_api_proto_rawDescOnce sync.Once
	file_moego_client_online_booking_v2_appointment_api_proto_rawDescData = file_moego_client_online_booking_v2_appointment_api_proto_rawDesc
)

func file_moego_client_online_booking_v2_appointment_api_proto_rawDescGZIP() []byte {
	file_moego_client_online_booking_v2_appointment_api_proto_rawDescOnce.Do(func() {
		file_moego_client_online_booking_v2_appointment_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_online_booking_v2_appointment_api_proto_rawDescData)
	})
	return file_moego_client_online_booking_v2_appointment_api_proto_rawDescData
}

var file_moego_client_online_booking_v2_appointment_api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_client_online_booking_v2_appointment_api_proto_goTypes = []interface{}{
	(*UpdateAppointmentParams)(nil),                 // 0: moego.client.online_booking.v2.UpdateAppointmentParams
	(*UpdateAppointmentResult)(nil),                 // 1: moego.client.online_booking.v2.UpdateAppointmentResult
	(*v2.UpdateAppointmentRequest_Appointment)(nil), // 2: moego.service.appointment.v2.UpdateAppointmentRequest.Appointment
	(*v2.UpdateAppointmentRequest_PetDetail)(nil),   // 3: moego.service.appointment.v2.UpdateAppointmentRequest.PetDetail
}
var file_moego_client_online_booking_v2_appointment_api_proto_depIdxs = []int32{
	2, // 0: moego.client.online_booking.v2.UpdateAppointmentParams.appointment:type_name -> moego.service.appointment.v2.UpdateAppointmentRequest.Appointment
	3, // 1: moego.client.online_booking.v2.UpdateAppointmentParams.pet_details:type_name -> moego.service.appointment.v2.UpdateAppointmentRequest.PetDetail
	0, // 2: moego.client.online_booking.v2.AppointmentAPI.UpdateAppointment:input_type -> moego.client.online_booking.v2.UpdateAppointmentParams
	1, // 3: moego.client.online_booking.v2.AppointmentAPI.UpdateAppointment:output_type -> moego.client.online_booking.v2.UpdateAppointmentResult
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_client_online_booking_v2_appointment_api_proto_init() }
func file_moego_client_online_booking_v2_appointment_api_proto_init() {
	if File_moego_client_online_booking_v2_appointment_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_online_booking_v2_appointment_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v2_appointment_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_online_booking_v2_appointment_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*UpdateAppointmentParams_Name)(nil),
		(*UpdateAppointmentParams_Domain)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_online_booking_v2_appointment_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_online_booking_v2_appointment_api_proto_goTypes,
		DependencyIndexes: file_moego_client_online_booking_v2_appointment_api_proto_depIdxs,
		MessageInfos:      file_moego_client_online_booking_v2_appointment_api_proto_msgTypes,
	}.Build()
	File_moego_client_online_booking_v2_appointment_api_proto = out.File
	file_moego_client_online_booking_v2_appointment_api_proto_rawDesc = nil
	file_moego_client_online_booking_v2_appointment_api_proto_goTypes = nil
	file_moego_client_online_booking_v2_appointment_api_proto_depIdxs = nil
}
