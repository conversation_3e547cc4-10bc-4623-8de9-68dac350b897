// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/online_booking/v2/appointment_api.proto

package onlinebookingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AppointmentAPIClient is the client API for AppointmentAPI service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppointmentAPIClient interface {
	// Update appointment
	UpdateAppointment(ctx context.Context, in *UpdateAppointmentParams, opts ...grpc.CallOption) (*UpdateAppointmentResult, error)
}

type appointmentAPIClient struct {
	cc grpc.ClientConnInterface
}

func NewAppointmentAPIClient(cc grpc.ClientConnInterface) AppointmentAPIClient {
	return &appointmentAPIClient{cc}
}

func (c *appointmentAPIClient) UpdateAppointment(ctx context.Context, in *UpdateAppointmentParams, opts ...grpc.CallOption) (*UpdateAppointmentResult, error) {
	out := new(UpdateAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.client.online_booking.v2.AppointmentAPI/UpdateAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppointmentAPIServer is the server API for AppointmentAPI service.
// All implementations must embed UnimplementedAppointmentAPIServer
// for forward compatibility
type AppointmentAPIServer interface {
	// Update appointment
	UpdateAppointment(context.Context, *UpdateAppointmentParams) (*UpdateAppointmentResult, error)
	mustEmbedUnimplementedAppointmentAPIServer()
}

// UnimplementedAppointmentAPIServer must be embedded to have forward compatible implementations.
type UnimplementedAppointmentAPIServer struct {
}

func (UnimplementedAppointmentAPIServer) UpdateAppointment(context.Context, *UpdateAppointmentParams) (*UpdateAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAppointment not implemented")
}
func (UnimplementedAppointmentAPIServer) mustEmbedUnimplementedAppointmentAPIServer() {}

// UnsafeAppointmentAPIServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppointmentAPIServer will
// result in compilation errors.
type UnsafeAppointmentAPIServer interface {
	mustEmbedUnimplementedAppointmentAPIServer()
}

func RegisterAppointmentAPIServer(s grpc.ServiceRegistrar, srv AppointmentAPIServer) {
	s.RegisterService(&AppointmentAPI_ServiceDesc, srv)
}

func _AppointmentAPI_UpdateAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentAPIServer).UpdateAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.online_booking.v2.AppointmentAPI/UpdateAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentAPIServer).UpdateAppointment(ctx, req.(*UpdateAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AppointmentAPI_ServiceDesc is the grpc.ServiceDesc for AppointmentAPI service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppointmentAPI_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.online_booking.v2.AppointmentAPI",
	HandlerType: (*AppointmentAPIServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateAppointment",
			Handler:    _AppointmentAPI_UpdateAppointment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/online_booking/v2/appointment_api.proto",
}
