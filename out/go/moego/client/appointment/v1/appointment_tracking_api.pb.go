// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/appointment/v1/appointment_tracking_api.proto

package appointmentapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get appointment tracking request
type GetAppointmentTrackingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *GetAppointmentTrackingParams) Reset() {
	*x = GetAppointmentTrackingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_appointment_v1_appointment_tracking_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentTrackingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentTrackingParams) ProtoMessage() {}

func (x *GetAppointmentTrackingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_appointment_v1_appointment_tracking_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentTrackingParams.ProtoReflect.Descriptor instead.
func (*GetAppointmentTrackingParams) Descriptor() ([]byte, []int) {
	return file_moego_client_appointment_v1_appointment_tracking_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetAppointmentTrackingParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// get appointment tracking response
type GetAppointmentTrackingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment tracking
	AppointmentTracking *v1.AppointmentTrackingView `protobuf:"bytes,1,opt,name=appointment_tracking,json=appointmentTracking,proto3" json:"appointment_tracking,omitempty"`
}

func (x *GetAppointmentTrackingResult) Reset() {
	*x = GetAppointmentTrackingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_appointment_v1_appointment_tracking_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentTrackingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentTrackingResult) ProtoMessage() {}

func (x *GetAppointmentTrackingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_appointment_v1_appointment_tracking_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentTrackingResult.ProtoReflect.Descriptor instead.
func (*GetAppointmentTrackingResult) Descriptor() ([]byte, []int) {
	return file_moego_client_appointment_v1_appointment_tracking_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetAppointmentTrackingResult) GetAppointmentTracking() *v1.AppointmentTrackingView {
	if x != nil {
		return x.AppointmentTracking
	}
	return nil
}

var File_moego_client_appointment_v1_appointment_tracking_api_proto protoreflect.FileDescriptor

var file_moego_client_appointment_v1_appointment_tracking_api_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x45, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x87, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x67, 0x0a, 0x14, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x52, 0x13, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x32, 0xad, 0x01, 0x0a, 0x1a, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x8e, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x39, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x42, 0x8a, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x61, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_appointment_v1_appointment_tracking_api_proto_rawDescOnce sync.Once
	file_moego_client_appointment_v1_appointment_tracking_api_proto_rawDescData = file_moego_client_appointment_v1_appointment_tracking_api_proto_rawDesc
)

func file_moego_client_appointment_v1_appointment_tracking_api_proto_rawDescGZIP() []byte {
	file_moego_client_appointment_v1_appointment_tracking_api_proto_rawDescOnce.Do(func() {
		file_moego_client_appointment_v1_appointment_tracking_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_appointment_v1_appointment_tracking_api_proto_rawDescData)
	})
	return file_moego_client_appointment_v1_appointment_tracking_api_proto_rawDescData
}

var file_moego_client_appointment_v1_appointment_tracking_api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_client_appointment_v1_appointment_tracking_api_proto_goTypes = []interface{}{
	(*GetAppointmentTrackingParams)(nil), // 0: moego.client.appointment.v1.GetAppointmentTrackingParams
	(*GetAppointmentTrackingResult)(nil), // 1: moego.client.appointment.v1.GetAppointmentTrackingResult
	(*v1.AppointmentTrackingView)(nil),   // 2: moego.models.appointment.v1.AppointmentTrackingView
}
var file_moego_client_appointment_v1_appointment_tracking_api_proto_depIdxs = []int32{
	2, // 0: moego.client.appointment.v1.GetAppointmentTrackingResult.appointment_tracking:type_name -> moego.models.appointment.v1.AppointmentTrackingView
	0, // 1: moego.client.appointment.v1.AppointmentTrackingService.GetAppointmentTracking:input_type -> moego.client.appointment.v1.GetAppointmentTrackingParams
	1, // 2: moego.client.appointment.v1.AppointmentTrackingService.GetAppointmentTracking:output_type -> moego.client.appointment.v1.GetAppointmentTrackingResult
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_client_appointment_v1_appointment_tracking_api_proto_init() }
func file_moego_client_appointment_v1_appointment_tracking_api_proto_init() {
	if File_moego_client_appointment_v1_appointment_tracking_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_appointment_v1_appointment_tracking_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentTrackingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_appointment_v1_appointment_tracking_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentTrackingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_appointment_v1_appointment_tracking_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_appointment_v1_appointment_tracking_api_proto_goTypes,
		DependencyIndexes: file_moego_client_appointment_v1_appointment_tracking_api_proto_depIdxs,
		MessageInfos:      file_moego_client_appointment_v1_appointment_tracking_api_proto_msgTypes,
	}.Build()
	File_moego_client_appointment_v1_appointment_tracking_api_proto = out.File
	file_moego_client_appointment_v1_appointment_tracking_api_proto_rawDesc = nil
	file_moego_client_appointment_v1_appointment_tracking_api_proto_goTypes = nil
	file_moego_client_appointment_v1_appointment_tracking_api_proto_depIdxs = nil
}
