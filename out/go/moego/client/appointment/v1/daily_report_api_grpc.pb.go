// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/appointment/v1/daily_report_api.proto

package appointmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// DailyReportServiceClient is the client API for DailyReportService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DailyReportServiceClient interface {
	// list daily report config
	ListDailyReportConfig(ctx context.Context, in *ListDailyReportConfigParams, opts ...grpc.CallOption) (*ListDailyReportConfigResult, error)
}

type dailyReportServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDailyReportServiceClient(cc grpc.ClientConnInterface) DailyReportServiceClient {
	return &dailyReportServiceClient{cc}
}

func (c *dailyReportServiceClient) ListDailyReportConfig(ctx context.Context, in *ListDailyReportConfigParams, opts ...grpc.CallOption) (*ListDailyReportConfigResult, error) {
	out := new(ListDailyReportConfigResult)
	err := c.cc.Invoke(ctx, "/moego.client.appointment.v1.DailyReportService/ListDailyReportConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DailyReportServiceServer is the server API for DailyReportService service.
// All implementations must embed UnimplementedDailyReportServiceServer
// for forward compatibility
type DailyReportServiceServer interface {
	// list daily report config
	ListDailyReportConfig(context.Context, *ListDailyReportConfigParams) (*ListDailyReportConfigResult, error)
	mustEmbedUnimplementedDailyReportServiceServer()
}

// UnimplementedDailyReportServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDailyReportServiceServer struct {
}

func (UnimplementedDailyReportServiceServer) ListDailyReportConfig(context.Context, *ListDailyReportConfigParams) (*ListDailyReportConfigResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDailyReportConfig not implemented")
}
func (UnimplementedDailyReportServiceServer) mustEmbedUnimplementedDailyReportServiceServer() {}

// UnsafeDailyReportServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DailyReportServiceServer will
// result in compilation errors.
type UnsafeDailyReportServiceServer interface {
	mustEmbedUnimplementedDailyReportServiceServer()
}

func RegisterDailyReportServiceServer(s grpc.ServiceRegistrar, srv DailyReportServiceServer) {
	s.RegisterService(&DailyReportService_ServiceDesc, srv)
}

func _DailyReportService_ListDailyReportConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDailyReportConfigParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).ListDailyReportConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.appointment.v1.DailyReportService/ListDailyReportConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).ListDailyReportConfig(ctx, req.(*ListDailyReportConfigParams))
	}
	return interceptor(ctx, in, info, handler)
}

// DailyReportService_ServiceDesc is the grpc.ServiceDesc for DailyReportService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DailyReportService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.appointment.v1.DailyReportService",
	HandlerType: (*DailyReportServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListDailyReportConfig",
			Handler:    _DailyReportService_ListDailyReportConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/appointment/v1/daily_report_api.proto",
}
