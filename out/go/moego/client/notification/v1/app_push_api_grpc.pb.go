// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/notification/v1/app_push_api.proto

package notificationapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AppPushServiceClient is the client API for AppPushService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppPushServiceClient interface {
	// Register device, there is a session registration refresh device, but there is no session offline device.
	RefreshDevice(ctx context.Context, in *RefreshDeviceParams, opts ...grpc.CallOption) (*RefreshDeviceResult, error)
}

type appPushServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppPushServiceClient(cc grpc.ClientConnInterface) AppPushServiceClient {
	return &appPushServiceClient{cc}
}

func (c *appPushServiceClient) RefreshDevice(ctx context.Context, in *RefreshDeviceParams, opts ...grpc.CallOption) (*RefreshDeviceResult, error) {
	out := new(RefreshDeviceResult)
	err := c.cc.Invoke(ctx, "/moego.client.notification.v1.AppPushService/RefreshDevice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppPushServiceServer is the server API for AppPushService service.
// All implementations must embed UnimplementedAppPushServiceServer
// for forward compatibility
type AppPushServiceServer interface {
	// Register device, there is a session registration refresh device, but there is no session offline device.
	RefreshDevice(context.Context, *RefreshDeviceParams) (*RefreshDeviceResult, error)
	mustEmbedUnimplementedAppPushServiceServer()
}

// UnimplementedAppPushServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAppPushServiceServer struct {
}

func (UnimplementedAppPushServiceServer) RefreshDevice(context.Context, *RefreshDeviceParams) (*RefreshDeviceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshDevice not implemented")
}
func (UnimplementedAppPushServiceServer) mustEmbedUnimplementedAppPushServiceServer() {}

// UnsafeAppPushServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppPushServiceServer will
// result in compilation errors.
type UnsafeAppPushServiceServer interface {
	mustEmbedUnimplementedAppPushServiceServer()
}

func RegisterAppPushServiceServer(s grpc.ServiceRegistrar, srv AppPushServiceServer) {
	s.RegisterService(&AppPushService_ServiceDesc, srv)
}

func _AppPushService_RefreshDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshDeviceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppPushServiceServer).RefreshDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.notification.v1.AppPushService/RefreshDevice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppPushServiceServer).RefreshDevice(ctx, req.(*RefreshDeviceParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AppPushService_ServiceDesc is the grpc.ServiceDesc for AppPushService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppPushService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.notification.v1.AppPushService",
	HandlerType: (*AppPushServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RefreshDevice",
			Handler:    _AppPushService_RefreshDevice_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/notification/v1/app_push_api.proto",
}
