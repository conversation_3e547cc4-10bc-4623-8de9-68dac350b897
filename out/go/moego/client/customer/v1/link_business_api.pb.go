// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/customer/v1/link_business_api.proto

package customerapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business/v1"
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/risk_control/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get link business list params
type GetLinkBusinessListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetLinkBusinessListParams) Reset() {
	*x = GetLinkBusinessListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLinkBusinessListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLinkBusinessListParams) ProtoMessage() {}

func (x *GetLinkBusinessListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLinkBusinessListParams.ProtoReflect.Descriptor instead.
func (*GetLinkBusinessListParams) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_link_business_api_proto_rawDescGZIP(), []int{0}
}

// get link business list result
type GetLinkBusinessListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// link business list
	LinkBusinesses []*GetLinkBusinessResult `protobuf:"bytes,1,rep,name=link_businesses,json=linkBusinesses,proto3" json:"link_businesses,omitempty"`
}

func (x *GetLinkBusinessListResult) Reset() {
	*x = GetLinkBusinessListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLinkBusinessListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLinkBusinessListResult) ProtoMessage() {}

func (x *GetLinkBusinessListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLinkBusinessListResult.ProtoReflect.Descriptor instead.
func (*GetLinkBusinessListResult) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_link_business_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetLinkBusinessListResult) GetLinkBusinesses() []*GetLinkBusinessResult {
	if x != nil {
		return x.LinkBusinesses
	}
	return nil
}

// get link business result
type GetLinkBusinessResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business info
	Business *v1.BusinessModel `protobuf:"bytes,1,opt,name=business,proto3" json:"business,omitempty"`
	// online booking gallery first image
	ObGallery *v11.BusinessOBGalleryClientPortalView `protobuf:"bytes,3,opt,name=ob_gallery,json=obGallery,proto3" json:"ob_gallery,omitempty"`
}

func (x *GetLinkBusinessResult) Reset() {
	*x = GetLinkBusinessResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLinkBusinessResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLinkBusinessResult) ProtoMessage() {}

func (x *GetLinkBusinessResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLinkBusinessResult.ProtoReflect.Descriptor instead.
func (*GetLinkBusinessResult) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_link_business_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetLinkBusinessResult) GetBusiness() *v1.BusinessModel {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *GetLinkBusinessResult) GetObGallery() *v11.BusinessOBGalleryClientPortalView {
	if x != nil {
		return x.ObGallery
	}
	return nil
}

// link business params
type LinkBusinessParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// invitation code
	InvitationCode string `protobuf:"bytes,2,opt,name=invitation_code,json=invitationCode,proto3" json:"invitation_code,omitempty"`
}

func (x *LinkBusinessParams) Reset() {
	*x = LinkBusinessParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LinkBusinessParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkBusinessParams) ProtoMessage() {}

func (x *LinkBusinessParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkBusinessParams.ProtoReflect.Descriptor instead.
func (*LinkBusinessParams) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_link_business_api_proto_rawDescGZIP(), []int{3}
}

func (x *LinkBusinessParams) GetInvitationCode() string {
	if x != nil {
		return x.InvitationCode
	}
	return ""
}

// link business result
type LinkBusinessResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LinkBusinessResult) Reset() {
	*x = LinkBusinessResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LinkBusinessResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkBusinessResult) ProtoMessage() {}

func (x *LinkBusinessResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkBusinessResult.ProtoReflect.Descriptor instead.
func (*LinkBusinessResult) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_link_business_api_proto_rawDescGZIP(), []int{4}
}

// send verification code params
type SendVerificationCodeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account identifier
	//
	// Types that are assignable to Identifier:
	//
	//	*SendVerificationCodeParams_Email
	//	*SendVerificationCodeParams_PhoneNumber
	Identifier isSendVerificationCodeParams_Identifier `protobuf_oneof:"identifier"`
}

func (x *SendVerificationCodeParams) Reset() {
	*x = SendVerificationCodeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendVerificationCodeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVerificationCodeParams) ProtoMessage() {}

func (x *SendVerificationCodeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVerificationCodeParams.ProtoReflect.Descriptor instead.
func (*SendVerificationCodeParams) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_link_business_api_proto_rawDescGZIP(), []int{5}
}

func (m *SendVerificationCodeParams) GetIdentifier() isSendVerificationCodeParams_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *SendVerificationCodeParams) GetEmail() string {
	if x, ok := x.GetIdentifier().(*SendVerificationCodeParams_Email); ok {
		return x.Email
	}
	return ""
}

func (x *SendVerificationCodeParams) GetPhoneNumber() string {
	if x, ok := x.GetIdentifier().(*SendVerificationCodeParams_PhoneNumber); ok {
		return x.PhoneNumber
	}
	return ""
}

type isSendVerificationCodeParams_Identifier interface {
	isSendVerificationCodeParams_Identifier()
}

type SendVerificationCodeParams_Email struct {
	// email
	Email string `protobuf:"bytes,1,opt,name=email,proto3,oneof"`
}

type SendVerificationCodeParams_PhoneNumber struct {
	// phone number
	PhoneNumber string `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3,oneof"`
}

func (*SendVerificationCodeParams_Email) isSendVerificationCodeParams_Identifier() {}

func (*SendVerificationCodeParams_PhoneNumber) isSendVerificationCodeParams_Identifier() {}

// send verification code result
type SendVerificationCodeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// verification token
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *SendVerificationCodeResult) Reset() {
	*x = SendVerificationCodeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendVerificationCodeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVerificationCodeResult) ProtoMessage() {}

func (x *SendVerificationCodeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVerificationCodeResult.ProtoReflect.Descriptor instead.
func (*SendVerificationCodeResult) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_link_business_api_proto_rawDescGZIP(), []int{6}
}

func (x *SendVerificationCodeResult) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// check verification code params
type CheckVerificationCodeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// verification code
	Verification *v12.VerificationCodeDef `protobuf:"bytes,1,opt,name=verification,proto3" json:"verification,omitempty"`
}

func (x *CheckVerificationCodeParams) Reset() {
	*x = CheckVerificationCodeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckVerificationCodeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVerificationCodeParams) ProtoMessage() {}

func (x *CheckVerificationCodeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVerificationCodeParams.ProtoReflect.Descriptor instead.
func (*CheckVerificationCodeParams) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_link_business_api_proto_rawDescGZIP(), []int{7}
}

func (x *CheckVerificationCodeParams) GetVerification() *v12.VerificationCodeDef {
	if x != nil {
		return x.Verification
	}
	return nil
}

// check verification code result
type CheckVerificationCodeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CheckVerificationCodeResult) Reset() {
	*x = CheckVerificationCodeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckVerificationCodeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVerificationCodeResult) ProtoMessage() {}

func (x *CheckVerificationCodeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVerificationCodeResult.ProtoReflect.Descriptor instead.
func (*CheckVerificationCodeResult) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_link_business_api_proto_rawDescGZIP(), []int{8}
}

// get invitation business info params
type GetInvitationBusinessInfoParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// invitation code
	InvitationCode string `protobuf:"bytes,1,opt,name=invitation_code,json=invitationCode,proto3" json:"invitation_code,omitempty"`
}

func (x *GetInvitationBusinessInfoParams) Reset() {
	*x = GetInvitationBusinessInfoParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvitationBusinessInfoParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvitationBusinessInfoParams) ProtoMessage() {}

func (x *GetInvitationBusinessInfoParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvitationBusinessInfoParams.ProtoReflect.Descriptor instead.
func (*GetInvitationBusinessInfoParams) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_link_business_api_proto_rawDescGZIP(), []int{9}
}

func (x *GetInvitationBusinessInfoParams) GetInvitationCode() string {
	if x != nil {
		return x.InvitationCode
	}
	return ""
}

// get invitation business info result
type GetInvitationBusinessInfoResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business country code
	CountryCode string `protobuf:"bytes,1,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
}

func (x *GetInvitationBusinessInfoResult) Reset() {
	*x = GetInvitationBusinessInfoResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvitationBusinessInfoResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvitationBusinessInfoResult) ProtoMessage() {}

func (x *GetInvitationBusinessInfoResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvitationBusinessInfoResult.ProtoReflect.Descriptor instead.
func (*GetInvitationBusinessInfoResult) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_link_business_api_proto_rawDescGZIP(), []int{10}
}

func (x *GetInvitationBusinessInfoResult) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

// get customer profile params
type GetCustomerProfileParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// invitation code
	InvitationCode string `protobuf:"bytes,1,opt,name=invitation_code,json=invitationCode,proto3" json:"invitation_code,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *GetCustomerProfileParams) Reset() {
	*x = GetCustomerProfileParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerProfileParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerProfileParams) ProtoMessage() {}

func (x *GetCustomerProfileParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerProfileParams.ProtoReflect.Descriptor instead.
func (*GetCustomerProfileParams) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_link_business_api_proto_rawDescGZIP(), []int{11}
}

func (x *GetCustomerProfileParams) GetInvitationCode() string {
	if x != nil {
		return x.InvitationCode
	}
	return ""
}

func (x *GetCustomerProfileParams) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

// get customer profile result
type GetCustomerProfileResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer profile
	Customer *v13.BusinessCustomerModelLinkView `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
}

func (x *GetCustomerProfileResult) Reset() {
	*x = GetCustomerProfileResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerProfileResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerProfileResult) ProtoMessage() {}

func (x *GetCustomerProfileResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_customer_v1_link_business_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerProfileResult.ProtoReflect.Descriptor instead.
func (*GetCustomerProfileResult) Descriptor() ([]byte, []int) {
	return file_moego_client_customer_v1_link_business_api_proto_rawDescGZIP(), []int{12}
}

func (x *GetCustomerProfileResult) GetCustomer() *v13.BusinessCustomerModelLinkView {
	if x != nil {
		return x.Customer
	}
	return nil
}

var File_moego_client_customer_v1_link_business_api_proto protoreflect.FileDescriptor

var file_moego_client_customer_v1_link_business_api_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x62, 0x5f, 0x67, 0x61, 0x6c, 0x6c, 0x65,
	0x72, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x39, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x76, 0x65,
	0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f,
	0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x1b, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x22, 0x75, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x58, 0x0a,
	0x0f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0e, 0x6c, 0x69, 0x6e, 0x6b, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x22, 0xbe, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4c,
	0x69, 0x6e, 0x6b, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x43, 0x0a, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x60, 0x0a, 0x0a, 0x6f, 0x62, 0x5f, 0x67, 0x61, 0x6c,
	0x6c, 0x65, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x4f, 0x42, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x09, 0x6f,
	0x62, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x22, 0x3d, 0x0a, 0x12, 0x4c, 0x69, 0x6e, 0x6b,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x27,
	0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x14, 0x0a, 0x12, 0x4c, 0x69, 0x6e, 0x6b, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x91, 0x01,
	0x0a, 0x1a, 0x53, 0x65, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x18, 0x64, 0x60, 0x01, 0x48, 0x00, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12,
	0x3d, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x32, 0x11, 0x5e, 0x5c,
	0x2b, 0x5b, 0x31, 0x2d, 0x39, 0x5d, 0x5c, 0x64, 0x7b, 0x31, 0x2c, 0x31, 0x38, 0x7d, 0x24, 0x48,
	0x00, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x11,
	0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42,
	0x01, 0x22, 0x32, 0x0a, 0x1a, 0x53, 0x65, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x74, 0x0a, 0x1b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x55, 0x0a, 0x0c, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0c, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x1d, 0x0a, 0x1b, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x4a, 0x0a, 0x1f, 0x47, 0x65,
	0x74, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x27, 0x0a,
	0x0f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x44, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x80, 0x01, 0x0a,
	0x18, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6e, 0x76,
	0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x3b, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x32,
	0x11, 0x5e, 0x5c, 0x2b, 0x5b, 0x31, 0x2d, 0x39, 0x5d, 0x5c, 0x64, 0x7b, 0x31, 0x2c, 0x31, 0x38,
	0x7d, 0x24, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22,
	0x78, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5c, 0x0a, 0x08, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x32, 0xa1, 0x06, 0x0a, 0x13, 0x4c, 0x69,
	0x6e, 0x6b, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x7f, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x6b,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x6a, 0x0a, 0x0c, 0x4c, 0x69, 0x6e, 0x6b, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x6e, 0x6b, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x6e, 0x6b,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x91,
	0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x7c, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x82, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x15, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x81, 0x01,
	0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x5b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x3b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x61, 0x70, 0x69, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_customer_v1_link_business_api_proto_rawDescOnce sync.Once
	file_moego_client_customer_v1_link_business_api_proto_rawDescData = file_moego_client_customer_v1_link_business_api_proto_rawDesc
)

func file_moego_client_customer_v1_link_business_api_proto_rawDescGZIP() []byte {
	file_moego_client_customer_v1_link_business_api_proto_rawDescOnce.Do(func() {
		file_moego_client_customer_v1_link_business_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_customer_v1_link_business_api_proto_rawDescData)
	})
	return file_moego_client_customer_v1_link_business_api_proto_rawDescData
}

var file_moego_client_customer_v1_link_business_api_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_moego_client_customer_v1_link_business_api_proto_goTypes = []interface{}{
	(*GetLinkBusinessListParams)(nil),             // 0: moego.client.customer.v1.GetLinkBusinessListParams
	(*GetLinkBusinessListResult)(nil),             // 1: moego.client.customer.v1.GetLinkBusinessListResult
	(*GetLinkBusinessResult)(nil),                 // 2: moego.client.customer.v1.GetLinkBusinessResult
	(*LinkBusinessParams)(nil),                    // 3: moego.client.customer.v1.LinkBusinessParams
	(*LinkBusinessResult)(nil),                    // 4: moego.client.customer.v1.LinkBusinessResult
	(*SendVerificationCodeParams)(nil),            // 5: moego.client.customer.v1.SendVerificationCodeParams
	(*SendVerificationCodeResult)(nil),            // 6: moego.client.customer.v1.SendVerificationCodeResult
	(*CheckVerificationCodeParams)(nil),           // 7: moego.client.customer.v1.CheckVerificationCodeParams
	(*CheckVerificationCodeResult)(nil),           // 8: moego.client.customer.v1.CheckVerificationCodeResult
	(*GetInvitationBusinessInfoParams)(nil),       // 9: moego.client.customer.v1.GetInvitationBusinessInfoParams
	(*GetInvitationBusinessInfoResult)(nil),       // 10: moego.client.customer.v1.GetInvitationBusinessInfoResult
	(*GetCustomerProfileParams)(nil),              // 11: moego.client.customer.v1.GetCustomerProfileParams
	(*GetCustomerProfileResult)(nil),              // 12: moego.client.customer.v1.GetCustomerProfileResult
	(*v1.BusinessModel)(nil),                      // 13: moego.models.business.v1.BusinessModel
	(*v11.BusinessOBGalleryClientPortalView)(nil), // 14: moego.models.online_booking.v1.BusinessOBGalleryClientPortalView
	(*v12.VerificationCodeDef)(nil),               // 15: moego.models.risk_control.v1.VerificationCodeDef
	(*v13.BusinessCustomerModelLinkView)(nil),     // 16: moego.models.business_customer.v1.BusinessCustomerModelLinkView
}
var file_moego_client_customer_v1_link_business_api_proto_depIdxs = []int32{
	2,  // 0: moego.client.customer.v1.GetLinkBusinessListResult.link_businesses:type_name -> moego.client.customer.v1.GetLinkBusinessResult
	13, // 1: moego.client.customer.v1.GetLinkBusinessResult.business:type_name -> moego.models.business.v1.BusinessModel
	14, // 2: moego.client.customer.v1.GetLinkBusinessResult.ob_gallery:type_name -> moego.models.online_booking.v1.BusinessOBGalleryClientPortalView
	15, // 3: moego.client.customer.v1.CheckVerificationCodeParams.verification:type_name -> moego.models.risk_control.v1.VerificationCodeDef
	16, // 4: moego.client.customer.v1.GetCustomerProfileResult.customer:type_name -> moego.models.business_customer.v1.BusinessCustomerModelLinkView
	0,  // 5: moego.client.customer.v1.LinkBusinessService.GetLinkBusinessList:input_type -> moego.client.customer.v1.GetLinkBusinessListParams
	3,  // 6: moego.client.customer.v1.LinkBusinessService.LinkBusiness:input_type -> moego.client.customer.v1.LinkBusinessParams
	9,  // 7: moego.client.customer.v1.LinkBusinessService.GetInvitationBusinessInfo:input_type -> moego.client.customer.v1.GetInvitationBusinessInfoParams
	11, // 8: moego.client.customer.v1.LinkBusinessService.GetCustomerProfile:input_type -> moego.client.customer.v1.GetCustomerProfileParams
	5,  // 9: moego.client.customer.v1.LinkBusinessService.SendVerificationCode:input_type -> moego.client.customer.v1.SendVerificationCodeParams
	7,  // 10: moego.client.customer.v1.LinkBusinessService.CheckVerificationCode:input_type -> moego.client.customer.v1.CheckVerificationCodeParams
	1,  // 11: moego.client.customer.v1.LinkBusinessService.GetLinkBusinessList:output_type -> moego.client.customer.v1.GetLinkBusinessListResult
	4,  // 12: moego.client.customer.v1.LinkBusinessService.LinkBusiness:output_type -> moego.client.customer.v1.LinkBusinessResult
	10, // 13: moego.client.customer.v1.LinkBusinessService.GetInvitationBusinessInfo:output_type -> moego.client.customer.v1.GetInvitationBusinessInfoResult
	12, // 14: moego.client.customer.v1.LinkBusinessService.GetCustomerProfile:output_type -> moego.client.customer.v1.GetCustomerProfileResult
	6,  // 15: moego.client.customer.v1.LinkBusinessService.SendVerificationCode:output_type -> moego.client.customer.v1.SendVerificationCodeResult
	8,  // 16: moego.client.customer.v1.LinkBusinessService.CheckVerificationCode:output_type -> moego.client.customer.v1.CheckVerificationCodeResult
	11, // [11:17] is the sub-list for method output_type
	5,  // [5:11] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_moego_client_customer_v1_link_business_api_proto_init() }
func file_moego_client_customer_v1_link_business_api_proto_init() {
	if File_moego_client_customer_v1_link_business_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_customer_v1_link_business_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLinkBusinessListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_customer_v1_link_business_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLinkBusinessListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_customer_v1_link_business_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLinkBusinessResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_customer_v1_link_business_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LinkBusinessParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_customer_v1_link_business_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LinkBusinessResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_customer_v1_link_business_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendVerificationCodeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_customer_v1_link_business_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendVerificationCodeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_customer_v1_link_business_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckVerificationCodeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_customer_v1_link_business_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckVerificationCodeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_customer_v1_link_business_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvitationBusinessInfoParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_customer_v1_link_business_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvitationBusinessInfoResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_customer_v1_link_business_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerProfileParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_customer_v1_link_business_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerProfileResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_customer_v1_link_business_api_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*SendVerificationCodeParams_Email)(nil),
		(*SendVerificationCodeParams_PhoneNumber)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_customer_v1_link_business_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_customer_v1_link_business_api_proto_goTypes,
		DependencyIndexes: file_moego_client_customer_v1_link_business_api_proto_depIdxs,
		MessageInfos:      file_moego_client_customer_v1_link_business_api_proto_msgTypes,
	}.Build()
	File_moego_client_customer_v1_link_business_api_proto = out.File
	file_moego_client_customer_v1_link_business_api_proto_rawDesc = nil
	file_moego_client_customer_v1_link_business_api_proto_goTypes = nil
	file_moego_client_customer_v1_link_business_api_proto_depIdxs = nil
}
