// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/business/v1/business_api.proto

package businessapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessServiceClient is the client API for BusinessService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessServiceClient interface {
	// get business info
	GetBusinessInfo(ctx context.Context, in *GetBusinessInfoRequest, opts ...grpc.CallOption) (*GetBusinessInfoResponse, error)
	// Get all the businesses of the companies with the current account
	// require c-side account session
	ListBusinesses(ctx context.Context, in *ListBusinessesParams, opts ...grpc.CallOption) (*ListBusinessesResult, error)
}

type businessServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessServiceClient(cc grpc.ClientConnInterface) BusinessServiceClient {
	return &businessServiceClient{cc}
}

func (c *businessServiceClient) GetBusinessInfo(ctx context.Context, in *GetBusinessInfoRequest, opts ...grpc.CallOption) (*GetBusinessInfoResponse, error) {
	out := new(GetBusinessInfoResponse)
	err := c.cc.Invoke(ctx, "/moego.client.business.v1.BusinessService/GetBusinessInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessServiceClient) ListBusinesses(ctx context.Context, in *ListBusinessesParams, opts ...grpc.CallOption) (*ListBusinessesResult, error) {
	out := new(ListBusinessesResult)
	err := c.cc.Invoke(ctx, "/moego.client.business.v1.BusinessService/ListBusinesses", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessServiceServer is the server API for BusinessService service.
// All implementations must embed UnimplementedBusinessServiceServer
// for forward compatibility
type BusinessServiceServer interface {
	// get business info
	GetBusinessInfo(context.Context, *GetBusinessInfoRequest) (*GetBusinessInfoResponse, error)
	// Get all the businesses of the companies with the current account
	// require c-side account session
	ListBusinesses(context.Context, *ListBusinessesParams) (*ListBusinessesResult, error)
	mustEmbedUnimplementedBusinessServiceServer()
}

// UnimplementedBusinessServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessServiceServer struct {
}

func (UnimplementedBusinessServiceServer) GetBusinessInfo(context.Context, *GetBusinessInfoRequest) (*GetBusinessInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBusinessInfo not implemented")
}
func (UnimplementedBusinessServiceServer) ListBusinesses(context.Context, *ListBusinessesParams) (*ListBusinessesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBusinesses not implemented")
}
func (UnimplementedBusinessServiceServer) mustEmbedUnimplementedBusinessServiceServer() {}

// UnsafeBusinessServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessServiceServer will
// result in compilation errors.
type UnsafeBusinessServiceServer interface {
	mustEmbedUnimplementedBusinessServiceServer()
}

func RegisterBusinessServiceServer(s grpc.ServiceRegistrar, srv BusinessServiceServer) {
	s.RegisterService(&BusinessService_ServiceDesc, srv)
}

func _BusinessService_GetBusinessInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessServiceServer).GetBusinessInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.business.v1.BusinessService/GetBusinessInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessServiceServer).GetBusinessInfo(ctx, req.(*GetBusinessInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessService_ListBusinesses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBusinessesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessServiceServer).ListBusinesses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.business.v1.BusinessService/ListBusinesses",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessServiceServer).ListBusinesses(ctx, req.(*ListBusinessesParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessService_ServiceDesc is the grpc.ServiceDesc for BusinessService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.business.v1.BusinessService",
	HandlerType: (*BusinessServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetBusinessInfo",
			Handler:    _BusinessService_GetBusinessInfo_Handler,
		},
		{
			MethodName: "ListBusinesses",
			Handler:    _BusinessService_ListBusinesses_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/business/v1/business_api.proto",
}
