// @since 2022-05-30 17:46:23
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/utils/v1/id_messages.proto

package utilsV1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// limit by id
// Deprecated: force use local defined params
//
// Deprecated: Do not use.
type Id struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id value
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *Id) Reset() {
	*x = Id{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_utils_v1_id_messages_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Id) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Id) ProtoMessage() {}

func (x *Id) ProtoReflect() protoreflect.Message {
	mi := &file_moego_utils_v1_id_messages_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Id.ProtoReflect.Descriptor instead.
func (*Id) Descriptor() ([]byte, []int) {
	return file_moego_utils_v1_id_messages_proto_rawDescGZIP(), []int{0}
}

func (x *Id) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// limit by owner id
// Deprecated: force use local defined params
//
// Deprecated: Do not use.
type Own struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// owner id
	OwnerId int64 `protobuf:"varint,1,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
}

func (x *Own) Reset() {
	*x = Own{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_utils_v1_id_messages_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Own) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Own) ProtoMessage() {}

func (x *Own) ProtoReflect() protoreflect.Message {
	mi := &file_moego_utils_v1_id_messages_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Own.ProtoReflect.Descriptor instead.
func (*Own) Descriptor() ([]byte, []int) {
	return file_moego_utils_v1_id_messages_proto_rawDescGZIP(), []int{1}
}

func (x *Own) GetOwnerId() int64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

// limit by owner id and id
// Deprecated: force use local defined params
//
// Deprecated: Do not use.
type OwnId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// owner id
	OwnerId int64 `protobuf:"varint,2,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
}

func (x *OwnId) Reset() {
	*x = OwnId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_utils_v1_id_messages_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OwnId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OwnId) ProtoMessage() {}

func (x *OwnId) ProtoReflect() protoreflect.Message {
	mi := &file_moego_utils_v1_id_messages_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OwnId.ProtoReflect.Descriptor instead.
func (*OwnId) Descriptor() ([]byte, []int) {
	return file_moego_utils_v1_id_messages_proto_rawDescGZIP(), []int{2}
}

func (x *OwnId) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OwnId) GetOwnerId() int64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

var File_moego_utils_v1_id_messages_proto protoreflect.FileDescriptor

var file_moego_utils_v1_id_messages_proto_rawDesc = []byte{
	0x0a, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x69, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x21, 0x0a, 0x02, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x3a, 0x02, 0x18, 0x01, 0x22, 0x2d,
	0x0a, 0x03, 0x4f, 0x77, 0x6e, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x3a, 0x02, 0x18, 0x01, 0x22, 0x48, 0x0a,
	0x05, 0x4f, 0x77, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x22, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x49, 0x64, 0x3a, 0x02, 0x18, 0x01, 0x42, 0x67, 0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x4b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x56, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_utils_v1_id_messages_proto_rawDescOnce sync.Once
	file_moego_utils_v1_id_messages_proto_rawDescData = file_moego_utils_v1_id_messages_proto_rawDesc
)

func file_moego_utils_v1_id_messages_proto_rawDescGZIP() []byte {
	file_moego_utils_v1_id_messages_proto_rawDescOnce.Do(func() {
		file_moego_utils_v1_id_messages_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_utils_v1_id_messages_proto_rawDescData)
	})
	return file_moego_utils_v1_id_messages_proto_rawDescData
}

var file_moego_utils_v1_id_messages_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_utils_v1_id_messages_proto_goTypes = []interface{}{
	(*Id)(nil),    // 0: moego.utils.v1.Id
	(*Own)(nil),   // 1: moego.utils.v1.Own
	(*OwnId)(nil), // 2: moego.utils.v1.OwnId
}
var file_moego_utils_v1_id_messages_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_utils_v1_id_messages_proto_init() }
func file_moego_utils_v1_id_messages_proto_init() {
	if File_moego_utils_v1_id_messages_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_utils_v1_id_messages_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Id); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_utils_v1_id_messages_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Own); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_utils_v1_id_messages_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OwnId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_utils_v1_id_messages_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_utils_v1_id_messages_proto_goTypes,
		DependencyIndexes: file_moego_utils_v1_id_messages_proto_depIdxs,
		MessageInfos:      file_moego_utils_v1_id_messages_proto_msgTypes,
	}.Build()
	File_moego_utils_v1_id_messages_proto = out.File
	file_moego_utils_v1_id_messages_proto_rawDesc = nil
	file_moego_utils_v1_id_messages_proto_goTypes = nil
	file_moego_utils_v1_id_messages_proto_depIdxs = nil
}
