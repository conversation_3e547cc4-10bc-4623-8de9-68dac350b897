// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/open_platform/v1/webhook_models.proto

package openplatformpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ContentType defines the format of the payload.
type Webhook_ContentType int32

const (
	// Default value.
	Webhook_CONTENT_TYPE_UNSPECIFIED Webhook_ContentType = 0
	// application/json
	Webhook_CONTENT_TYPE_JSON Webhook_ContentType = 1
	// application/x-www-form-urlencoded
	Webhook_CONTENT_TYPE_FORM_URLENCODED Webhook_ContentType = 2
	// application/grpc, application/grpc+proto
	Webhook_CONTENT_TYPE_GRPC Webhook_ContentType = 3
)

// Enum value maps for Webhook_ContentType.
var (
	Webhook_ContentType_name = map[int32]string{
		0: "CONTENT_TYPE_UNSPECIFIED",
		1: "CONTENT_TYPE_JSON",
		2: "CONTENT_TYPE_FORM_URLENCODED",
		3: "CONTENT_TYPE_GRPC",
	}
	Webhook_ContentType_value = map[string]int32{
		"CONTENT_TYPE_UNSPECIFIED":     0,
		"CONTENT_TYPE_JSON":            1,
		"CONTENT_TYPE_FORM_URLENCODED": 2,
		"CONTENT_TYPE_GRPC":            3,
	}
)

func (x Webhook_ContentType) Enum() *Webhook_ContentType {
	p := new(Webhook_ContentType)
	*p = x
	return p
}

func (x Webhook_ContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Webhook_ContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_open_platform_v1_webhook_models_proto_enumTypes[0].Descriptor()
}

func (Webhook_ContentType) Type() protoreflect.EnumType {
	return &file_moego_models_open_platform_v1_webhook_models_proto_enumTypes[0]
}

func (x Webhook_ContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Webhook_ContentType.Descriptor instead.
func (Webhook_ContentType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_open_platform_v1_webhook_models_proto_rawDescGZIP(), []int{0, 0}
}

// Webhook represents a webhook configuration for event notifications.
type Webhook struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier of the webhook.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The enterprise ID associated with this webhook.
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// The client ID (UUID) associated with this webhook.
	ClientId string `protobuf:"bytes,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// List of organizations the webhook is subscribed to.
	// If empty, the webhook is subscribed to all organizations.
	Organizations []*Organization `protobuf:"bytes,4,rep,name=organizations,proto3" json:"organizations,omitempty"`
	// List of event types the webhook is subscribed to.
	// If it is empty, no events will be subscribed to
	EventTypes []v1.EventType `protobuf:"varint,5,rep,packed,name=event_types,json=eventTypes,proto3,enum=moego.models.event_bus.v1.EventType" json:"event_types,omitempty"`
	// URL to receive webhook payloads.
	EndpointUrl string `protobuf:"bytes,6,opt,name=endpoint_url,json=endpointUrl,proto3" json:"endpoint_url,omitempty"`
	// Optional secret token used to sign payloads (e.g., HMAC).
	SecretToken string `protobuf:"bytes,7,opt,name=secret_token,json=secretToken,proto3" json:"secret_token,omitempty"`
	// Content-Type header used when delivering payloads.
	// Default: CONTENT_TYPE_JSON
	ContentType Webhook_ContentType `protobuf:"varint,8,opt,name=content_type,json=contentType,proto3,enum=moego.models.open_platform.v1.Webhook_ContentType" json:"content_type,omitempty"`
	// Whether the webhook is currently active.
	IsActive bool `protobuf:"varint,9,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// Whether to verify SSL certificates when delivering payloads.
	// Default: true (recommended for security)
	VerifySsl bool `protobuf:"varint,10,opt,name=verify_ssl,json=verifySsl,proto3" json:"verify_ssl,omitempty"`
	// Custom HTTP headers to include when delivering payloads.
	Headers map[string]*Webhook_HeaderValues `protobuf:"bytes,11,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Timestamp when the webhook was created.
	CreatedTime *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	// Timestamp when the webhook was last updated.
	UpdatedTime *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=updated_time,json=updatedTime,proto3" json:"updated_time,omitempty"`
}

func (x *Webhook) Reset() {
	*x = Webhook{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_open_platform_v1_webhook_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Webhook) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Webhook) ProtoMessage() {}

func (x *Webhook) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_open_platform_v1_webhook_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Webhook.ProtoReflect.Descriptor instead.
func (*Webhook) Descriptor() ([]byte, []int) {
	return file_moego_models_open_platform_v1_webhook_models_proto_rawDescGZIP(), []int{0}
}

func (x *Webhook) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Webhook) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *Webhook) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *Webhook) GetOrganizations() []*Organization {
	if x != nil {
		return x.Organizations
	}
	return nil
}

func (x *Webhook) GetEventTypes() []v1.EventType {
	if x != nil {
		return x.EventTypes
	}
	return nil
}

func (x *Webhook) GetEndpointUrl() string {
	if x != nil {
		return x.EndpointUrl
	}
	return ""
}

func (x *Webhook) GetSecretToken() string {
	if x != nil {
		return x.SecretToken
	}
	return ""
}

func (x *Webhook) GetContentType() Webhook_ContentType {
	if x != nil {
		return x.ContentType
	}
	return Webhook_CONTENT_TYPE_UNSPECIFIED
}

func (x *Webhook) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *Webhook) GetVerifySsl() bool {
	if x != nil {
		return x.VerifySsl
	}
	return false
}

func (x *Webhook) GetHeaders() map[string]*Webhook_HeaderValues {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *Webhook) GetCreatedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedTime
	}
	return nil
}

func (x *Webhook) GetUpdatedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedTime
	}
	return nil
}

// WebhookQuotaConfig represents the quota configuration for a specific client.
type WebhookQuotaConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique ID for this quota configuration.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The client ID (UUID) associated with this quota configuration.
	ClientId string `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// Maximum number of webhooks allowed for this client.
	MaxWebhooks int32 `protobuf:"varint,3,opt,name=max_webhooks,json=maxWebhooks,proto3" json:"max_webhooks,omitempty"`
	// Maximum number of days to retain delivery logs for this client.
	MaxDeliveryRetentionDays int32 `protobuf:"varint,4,opt,name=max_delivery_retention_days,json=maxDeliveryRetentionDays,proto3" json:"max_delivery_retention_days,omitempty"`
	// Maximum number of push events allowed per minute for this client.
	MaxPushPerMinute int32 `protobuf:"varint,5,opt,name=max_push_per_minute,json=maxPushPerMinute,proto3" json:"max_push_per_minute,omitempty"`
	// Maximum number of push events allowed per day for this client.
	MaxPushPerDay int32 `protobuf:"varint,6,opt,name=max_push_per_day,json=maxPushPerDay,proto3" json:"max_push_per_day,omitempty"`
	// Maximum number of push events allowed per month for this client.
	MaxPushPerMonth int32 `protobuf:"varint,7,opt,name=max_push_per_month,json=maxPushPerMonth,proto3" json:"max_push_per_month,omitempty"`
	// Timestamp when the quota configuration was created.
	CreatedTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	// Timestamp when the quota configuration was last updated.
	UpdatedTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_time,json=updatedTime,proto3" json:"updated_time,omitempty"`
}

func (x *WebhookQuotaConfig) Reset() {
	*x = WebhookQuotaConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_open_platform_v1_webhook_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebhookQuotaConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebhookQuotaConfig) ProtoMessage() {}

func (x *WebhookQuotaConfig) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_open_platform_v1_webhook_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebhookQuotaConfig.ProtoReflect.Descriptor instead.
func (*WebhookQuotaConfig) Descriptor() ([]byte, []int) {
	return file_moego_models_open_platform_v1_webhook_models_proto_rawDescGZIP(), []int{1}
}

func (x *WebhookQuotaConfig) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WebhookQuotaConfig) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *WebhookQuotaConfig) GetMaxWebhooks() int32 {
	if x != nil {
		return x.MaxWebhooks
	}
	return 0
}

func (x *WebhookQuotaConfig) GetMaxDeliveryRetentionDays() int32 {
	if x != nil {
		return x.MaxDeliveryRetentionDays
	}
	return 0
}

func (x *WebhookQuotaConfig) GetMaxPushPerMinute() int32 {
	if x != nil {
		return x.MaxPushPerMinute
	}
	return 0
}

func (x *WebhookQuotaConfig) GetMaxPushPerDay() int32 {
	if x != nil {
		return x.MaxPushPerDay
	}
	return 0
}

func (x *WebhookQuotaConfig) GetMaxPushPerMonth() int32 {
	if x != nil {
		return x.MaxPushPerMonth
	}
	return 0
}

func (x *WebhookQuotaConfig) GetCreatedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedTime
	}
	return nil
}

func (x *WebhookQuotaConfig) GetUpdatedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedTime
	}
	return nil
}

// WebhookDelivery represents a log entry for a webhook event delivery attempt.
type WebhookDelivery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique ID for this delivery attempt.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Reference to the webhook that triggered this delivery.
	WebhookId int64 `protobuf:"varint,2,opt,name=webhook_id,json=webhookId,proto3" json:"webhook_id,omitempty"`
	// Type of the event being delivered.
	EventType v1.EventType `protobuf:"varint,3,opt,name=event_type,json=eventType,proto3,enum=moego.models.event_bus.v1.EventType" json:"event_type,omitempty"`
	// Unique message/event ID from the message queue or event source.
	EventId string `protobuf:"bytes,4,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty"`
	// URL where the event was delivered.
	RequestUrl string `protobuf:"bytes,5,opt,name=request_url,json=requestUrl,proto3" json:"request_url,omitempty"`
	// Actual destination the request was delivered to (e.g., IP, domain, or URL).
	DeliveredTo string `protobuf:"bytes,6,opt,name=delivered_to,json=deliveredTo,proto3" json:"delivered_to,omitempty"`
	// HTTP headers sent with the delivery request.
	RequestHeaders map[string]*Webhook_HeaderValues `protobuf:"bytes,7,rep,name=request_headers,json=requestHeaders,proto3" json:"request_headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Payload of the event delivery request.
	RequestBody []byte `protobuf:"bytes,8,opt,name=request_body,json=requestBody,proto3" json:"request_body,omitempty"`
	// HTTP status code returned by the endpoint.
	ResponseStatus int32 `protobuf:"varint,9,opt,name=response_status,json=responseStatus,proto3" json:"response_status,omitempty"`
	// HTTP headers received in the response.
	ResponseHeaders map[string]*Webhook_HeaderValues `protobuf:"bytes,10,rep,name=response_headers,json=responseHeaders,proto3" json:"response_headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Body of the response, may be truncated.
	ResponseBody []byte `protobuf:"bytes,11,opt,name=response_body,json=responseBody,proto3" json:"response_body,omitempty"`
	// Timestamp when the event was delivered.
	DeliveredAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=delivered_at,json=deliveredAt,proto3" json:"delivered_at,omitempty"`
	// Time taken to complete the delivery in milliseconds.
	DurationMs int64 `protobuf:"varint,13,opt,name=duration_ms,json=durationMs,proto3" json:"duration_ms,omitempty"`
	// Whether the delivery was successful (HTTP 2xx).
	Success bool `protobuf:"varint,14,opt,name=success,proto3" json:"success,omitempty"`
	// Error message in case of failure.
	Error string `protobuf:"bytes,15,opt,name=error,proto3" json:"error,omitempty"`
	// Number of retries before final delivery outcome.
	RetryCount int32 `protobuf:"varint,16,opt,name=retry_count,json=retryCount,proto3" json:"retry_count,omitempty"`
	// Format of the request body
	RequestFormat Webhook_ContentType `protobuf:"varint,17,opt,name=request_format,json=requestFormat,proto3,enum=moego.models.open_platform.v1.Webhook_ContentType" json:"request_format,omitempty"`
	// Format of the response body
	ResponseFormat Webhook_ContentType `protobuf:"varint,18,opt,name=response_format,json=responseFormat,proto3,enum=moego.models.open_platform.v1.Webhook_ContentType" json:"response_format,omitempty"`
}

func (x *WebhookDelivery) Reset() {
	*x = WebhookDelivery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_open_platform_v1_webhook_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebhookDelivery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebhookDelivery) ProtoMessage() {}

func (x *WebhookDelivery) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_open_platform_v1_webhook_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebhookDelivery.ProtoReflect.Descriptor instead.
func (*WebhookDelivery) Descriptor() ([]byte, []int) {
	return file_moego_models_open_platform_v1_webhook_models_proto_rawDescGZIP(), []int{2}
}

func (x *WebhookDelivery) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WebhookDelivery) GetWebhookId() int64 {
	if x != nil {
		return x.WebhookId
	}
	return 0
}

func (x *WebhookDelivery) GetEventType() v1.EventType {
	if x != nil {
		return x.EventType
	}
	return v1.EventType(0)
}

func (x *WebhookDelivery) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *WebhookDelivery) GetRequestUrl() string {
	if x != nil {
		return x.RequestUrl
	}
	return ""
}

func (x *WebhookDelivery) GetDeliveredTo() string {
	if x != nil {
		return x.DeliveredTo
	}
	return ""
}

func (x *WebhookDelivery) GetRequestHeaders() map[string]*Webhook_HeaderValues {
	if x != nil {
		return x.RequestHeaders
	}
	return nil
}

func (x *WebhookDelivery) GetRequestBody() []byte {
	if x != nil {
		return x.RequestBody
	}
	return nil
}

func (x *WebhookDelivery) GetResponseStatus() int32 {
	if x != nil {
		return x.ResponseStatus
	}
	return 0
}

func (x *WebhookDelivery) GetResponseHeaders() map[string]*Webhook_HeaderValues {
	if x != nil {
		return x.ResponseHeaders
	}
	return nil
}

func (x *WebhookDelivery) GetResponseBody() []byte {
	if x != nil {
		return x.ResponseBody
	}
	return nil
}

func (x *WebhookDelivery) GetDeliveredAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeliveredAt
	}
	return nil
}

func (x *WebhookDelivery) GetDurationMs() int64 {
	if x != nil {
		return x.DurationMs
	}
	return 0
}

func (x *WebhookDelivery) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *WebhookDelivery) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *WebhookDelivery) GetRetryCount() int32 {
	if x != nil {
		return x.RetryCount
	}
	return 0
}

func (x *WebhookDelivery) GetRequestFormat() Webhook_ContentType {
	if x != nil {
		return x.RequestFormat
	}
	return Webhook_CONTENT_TYPE_UNSPECIFIED
}

func (x *WebhookDelivery) GetResponseFormat() Webhook_ContentType {
	if x != nil {
		return x.ResponseFormat
	}
	return Webhook_CONTENT_TYPE_UNSPECIFIED
}

// A list of values for a header key.
type Webhook_HeaderValues struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The values
	Values []string `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *Webhook_HeaderValues) Reset() {
	*x = Webhook_HeaderValues{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_open_platform_v1_webhook_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Webhook_HeaderValues) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Webhook_HeaderValues) ProtoMessage() {}

func (x *Webhook_HeaderValues) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_open_platform_v1_webhook_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Webhook_HeaderValues.ProtoReflect.Descriptor instead.
func (*Webhook_HeaderValues) Descriptor() ([]byte, []int) {
	return file_moego_models_open_platform_v1_webhook_models_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Webhook_HeaderValues) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

var File_moego_models_open_platform_v1_webhook_models_proto protoreflect.FileDescriptor

var file_moego_models_open_platform_v1_webhook_models_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x76, 0x31, 0x2f,
	0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x76, 0x31, 0x2f,
	0x6f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xb1, 0x07, 0x0a, 0x07, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x51, 0x0a, 0x0d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x45, 0x0a, 0x0b, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a,
	0x0c, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x55, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x2e, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x73,
	0x73, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x53, 0x73, 0x6c, 0x12, 0x4d, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x0b,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x2e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x1a, 0x26, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x1a, 0x6f, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x49, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x7b, 0x0a, 0x0b, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4f, 0x4e, 0x54,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x53, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x20, 0x0a,
	0x1c, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x4f,
	0x52, 0x4d, 0x5f, 0x55, 0x52, 0x4c, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x44, 0x10, 0x02, 0x12,
	0x15, 0x0a, 0x11, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x47, 0x52, 0x50, 0x43, 0x10, 0x03, 0x22, 0xa6, 0x03, 0x0a, 0x12, 0x57, 0x65, 0x62, 0x68, 0x6f,
	0x6f, 0x6b, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61,
	0x78, 0x5f, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x6d, 0x61, 0x78, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x12, 0x3d, 0x0a,
	0x1b, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x72, 0x65,
	0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x18, 0x6d, 0x61, 0x78, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x79, 0x73, 0x12, 0x2d, 0x0a, 0x13,
	0x6d, 0x61, 0x78, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x69, 0x6e,
	0x75, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x6d, 0x61, 0x78, 0x50, 0x75,
	0x73, 0x68, 0x50, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x10, 0x6d,
	0x61, 0x78, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6d, 0x61, 0x78, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65,
	0x72, 0x44, 0x61, 0x79, 0x12, 0x2b, 0x0a, 0x12, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x75, 0x73, 0x68,
	0x5f, 0x70, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0f, 0x6d, 0x61, 0x78, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65, 0x72, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x3d, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x8c, 0x09, 0x0a, 0x0f, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x49, 0x64, 0x12, 0x43, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x65, 0x64,
	0x5f, 0x74, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x65, 0x64, 0x54, 0x6f, 0x12, 0x6b, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x62,
	0x6f, 0x64, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x6e, 0x0a, 0x10, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12,
	0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x62, 0x6f, 0x64, 0x79,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x6f, 0x64, 0x79, 0x12, 0x3d, 0x0a, 0x0c, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6d, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x72, 0x79,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x59, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65,
	0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x12, 0x5b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x1a, 0x76, 0x0a,
	0x13, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x49, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x2e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x77, 0x0a, 0x14, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x49, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x8c,
	0x01, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x61, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x76, 0x31, 0x3b, 0x6f,
	0x70, 0x65, 0x6e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_open_platform_v1_webhook_models_proto_rawDescOnce sync.Once
	file_moego_models_open_platform_v1_webhook_models_proto_rawDescData = file_moego_models_open_platform_v1_webhook_models_proto_rawDesc
)

func file_moego_models_open_platform_v1_webhook_models_proto_rawDescGZIP() []byte {
	file_moego_models_open_platform_v1_webhook_models_proto_rawDescOnce.Do(func() {
		file_moego_models_open_platform_v1_webhook_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_open_platform_v1_webhook_models_proto_rawDescData)
	})
	return file_moego_models_open_platform_v1_webhook_models_proto_rawDescData
}

var file_moego_models_open_platform_v1_webhook_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_open_platform_v1_webhook_models_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_models_open_platform_v1_webhook_models_proto_goTypes = []interface{}{
	(Webhook_ContentType)(0),      // 0: moego.models.open_platform.v1.Webhook.ContentType
	(*Webhook)(nil),               // 1: moego.models.open_platform.v1.Webhook
	(*WebhookQuotaConfig)(nil),    // 2: moego.models.open_platform.v1.WebhookQuotaConfig
	(*WebhookDelivery)(nil),       // 3: moego.models.open_platform.v1.WebhookDelivery
	(*Webhook_HeaderValues)(nil),  // 4: moego.models.open_platform.v1.Webhook.HeaderValues
	nil,                           // 5: moego.models.open_platform.v1.Webhook.HeadersEntry
	nil,                           // 6: moego.models.open_platform.v1.WebhookDelivery.RequestHeadersEntry
	nil,                           // 7: moego.models.open_platform.v1.WebhookDelivery.ResponseHeadersEntry
	(*Organization)(nil),          // 8: moego.models.open_platform.v1.Organization
	(v1.EventType)(0),             // 9: moego.models.event_bus.v1.EventType
	(*timestamppb.Timestamp)(nil), // 10: google.protobuf.Timestamp
}
var file_moego_models_open_platform_v1_webhook_models_proto_depIdxs = []int32{
	8,  // 0: moego.models.open_platform.v1.Webhook.organizations:type_name -> moego.models.open_platform.v1.Organization
	9,  // 1: moego.models.open_platform.v1.Webhook.event_types:type_name -> moego.models.event_bus.v1.EventType
	0,  // 2: moego.models.open_platform.v1.Webhook.content_type:type_name -> moego.models.open_platform.v1.Webhook.ContentType
	5,  // 3: moego.models.open_platform.v1.Webhook.headers:type_name -> moego.models.open_platform.v1.Webhook.HeadersEntry
	10, // 4: moego.models.open_platform.v1.Webhook.created_time:type_name -> google.protobuf.Timestamp
	10, // 5: moego.models.open_platform.v1.Webhook.updated_time:type_name -> google.protobuf.Timestamp
	10, // 6: moego.models.open_platform.v1.WebhookQuotaConfig.created_time:type_name -> google.protobuf.Timestamp
	10, // 7: moego.models.open_platform.v1.WebhookQuotaConfig.updated_time:type_name -> google.protobuf.Timestamp
	9,  // 8: moego.models.open_platform.v1.WebhookDelivery.event_type:type_name -> moego.models.event_bus.v1.EventType
	6,  // 9: moego.models.open_platform.v1.WebhookDelivery.request_headers:type_name -> moego.models.open_platform.v1.WebhookDelivery.RequestHeadersEntry
	7,  // 10: moego.models.open_platform.v1.WebhookDelivery.response_headers:type_name -> moego.models.open_platform.v1.WebhookDelivery.ResponseHeadersEntry
	10, // 11: moego.models.open_platform.v1.WebhookDelivery.delivered_at:type_name -> google.protobuf.Timestamp
	0,  // 12: moego.models.open_platform.v1.WebhookDelivery.request_format:type_name -> moego.models.open_platform.v1.Webhook.ContentType
	0,  // 13: moego.models.open_platform.v1.WebhookDelivery.response_format:type_name -> moego.models.open_platform.v1.Webhook.ContentType
	4,  // 14: moego.models.open_platform.v1.Webhook.HeadersEntry.value:type_name -> moego.models.open_platform.v1.Webhook.HeaderValues
	4,  // 15: moego.models.open_platform.v1.WebhookDelivery.RequestHeadersEntry.value:type_name -> moego.models.open_platform.v1.Webhook.HeaderValues
	4,  // 16: moego.models.open_platform.v1.WebhookDelivery.ResponseHeadersEntry.value:type_name -> moego.models.open_platform.v1.Webhook.HeaderValues
	17, // [17:17] is the sub-list for method output_type
	17, // [17:17] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_moego_models_open_platform_v1_webhook_models_proto_init() }
func file_moego_models_open_platform_v1_webhook_models_proto_init() {
	if File_moego_models_open_platform_v1_webhook_models_proto != nil {
		return
	}
	file_moego_models_open_platform_v1_oauth_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_open_platform_v1_webhook_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Webhook); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_open_platform_v1_webhook_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebhookQuotaConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_open_platform_v1_webhook_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebhookDelivery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_open_platform_v1_webhook_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Webhook_HeaderValues); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_open_platform_v1_webhook_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_open_platform_v1_webhook_models_proto_goTypes,
		DependencyIndexes: file_moego_models_open_platform_v1_webhook_models_proto_depIdxs,
		EnumInfos:         file_moego_models_open_platform_v1_webhook_models_proto_enumTypes,
		MessageInfos:      file_moego_models_open_platform_v1_webhook_models_proto_msgTypes,
	}.Build()
	File_moego_models_open_platform_v1_webhook_models_proto = out.File
	file_moego_models_open_platform_v1_webhook_models_proto_rawDesc = nil
	file_moego_models_open_platform_v1_webhook_models_proto_goTypes = nil
	file_moego_models_open_platform_v1_webhook_models_proto_depIdxs = nil
}
