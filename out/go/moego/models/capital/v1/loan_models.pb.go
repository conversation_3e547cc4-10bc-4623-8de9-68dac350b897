// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/capital/v1/loan_models.proto

package capitalpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Update type
type NotableOfferUpdate_NotableOfferUpdateType int32

const (
	// Unspecified
	NotableOfferUpdate_NOTABLE_OFFER_UPDATE_TYPE_UNSPECIFIED NotableOfferUpdate_NotableOfferUpdateType = 0
	// Offer is rejected
	NotableOfferUpdate_REJECTED NotableOfferUpdate_NotableOfferUpdateType = 1
)

// Enum value maps for NotableOfferUpdate_NotableOfferUpdateType.
var (
	NotableOfferUpdate_NotableOfferUpdateType_name = map[int32]string{
		0: "NOTABLE_OFFER_UPDATE_TYPE_UNSPECIFIED",
		1: "REJECTED",
	}
	NotableOfferUpdate_NotableOfferUpdateType_value = map[string]int32{
		"NOTABLE_OFFER_UPDATE_TYPE_UNSPECIFIED": 0,
		"REJECTED":                              1,
	}
)

func (x NotableOfferUpdate_NotableOfferUpdateType) Enum() *NotableOfferUpdate_NotableOfferUpdateType {
	p := new(NotableOfferUpdate_NotableOfferUpdateType)
	*p = x
	return p
}

func (x NotableOfferUpdate_NotableOfferUpdateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotableOfferUpdate_NotableOfferUpdateType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_capital_v1_loan_models_proto_enumTypes[0].Descriptor()
}

func (NotableOfferUpdate_NotableOfferUpdateType) Type() protoreflect.EnumType {
	return &file_moego_models_capital_v1_loan_models_proto_enumTypes[0]
}

func (x NotableOfferUpdate_NotableOfferUpdateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotableOfferUpdate_NotableOfferUpdateType.Descriptor instead.
func (NotableOfferUpdate_NotableOfferUpdateType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_capital_v1_loan_models_proto_rawDescGZIP(), []int{4, 0}
}

// LoadOfferModel
type LoanOfferModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the offer.
	OfferId string `protobuf:"bytes,1,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	// The name of the channel that created the offer.
	ChannelName LoanChannel `protobuf:"varint,2,opt,name=channel_name,json=channelName,proto3,enum=moego.models.capital.v1.LoanChannel" json:"channel_name,omitempty"`
	// The ID of the account in the channel.
	ChannelAccountId string `protobuf:"bytes,3,opt,name=channel_account_id,json=channelAccountId,proto3" json:"channel_account_id,omitempty"`
	// The ID of the offer in the channel.
	ChannelOfferId string `protobuf:"bytes,4,opt,name=channel_offer_id,json=channelOfferId,proto3" json:"channel_offer_id,omitempty"`
	// The type of the offer, e.g. MCA, Term Loan.
	OfferType LoanOfferType `protobuf:"varint,5,opt,name=offer_type,json=offerType,proto3,enum=moego.models.capital.v1.LoanOfferType" json:"offer_type,omitempty"`
	// The unix timestamp in seconds when the offer was created.
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// The unix timestamp in seconds when the offer expires.
	ExpireAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	// The unix timestamp in seconds when the offer is closed (that is, changed to a terminal status).
	ClosedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=closed_at,json=closedAt,proto3" json:"closed_at,omitempty"`
	// The offered terms of the offer.
	OfferedTerms *LoanTerms `protobuf:"bytes,9,opt,name=offered_terms,json=offeredTerms,proto3" json:"offered_terms,omitempty"`
	// The accepted terms of the offer.
	AcceptedTerms *LoanTerms `protobuf:"bytes,10,opt,name=accepted_terms,json=acceptedTerms,proto3,oneof" json:"accepted_terms,omitempty"`
	// Financing product identifier. e.g. Standard, Refill.
	ProductType LoanProductType `protobuf:"varint,11,opt,name=product_type,json=productType,proto3,enum=moego.models.capital.v1.LoanProductType" json:"product_type,omitempty"`
	// Offer status.
	Status LoanOfferStatus `protobuf:"varint,12,opt,name=status,proto3,enum=moego.models.capital.v1.LoanOfferStatus" json:"status,omitempty"`
	// The status of the offer in the channel.
	// This field is a backup for original status field from the channel, so we use string type instead of enum.
	// Possible values for Stripe channel: "delivered", "accepted", "canceled", "expired", "fully_repaid", "paid_out",
	// "rejected", "replaced", "undelivered".
	ChannelStatus string `protobuf:"bytes,13,opt,name=channel_status,json=channelStatus,proto3" json:"channel_status,omitempty"`
	// Remaining amount of financing offered;
	RemainingAmount float64 `protobuf:"fixed64,14,opt,name=remaining_amount,json=remainingAmount,proto3" json:"remaining_amount,omitempty"`
	// The ID of the company
	CompanyId int64 `protobuf:"varint,15,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// The type of the entity
	EntityType string `protobuf:"bytes,16,opt,name=entity_type,json=entityType,proto3" json:"entity_type,omitempty"`
	// The ID of the entity
	EntityId int64 `protobuf:"varint,17,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	// The unix timestamp in seconds when the repayment starts.
	RepaymentStartAt *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=repayment_start_at,json=repaymentStartAt,proto3" json:"repayment_start_at,omitempty"`
	// Remaining amount of principal part. For MCA, this is the remaining advanced amount.
	RemainingPrincipalAmount float64 `protobuf:"fixed64,19,opt,name=remaining_principal_amount,json=remainingPrincipalAmount,proto3" json:"remaining_principal_amount,omitempty"`
}

func (x *LoanOfferModel) Reset() {
	*x = LoanOfferModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_capital_v1_loan_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanOfferModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanOfferModel) ProtoMessage() {}

func (x *LoanOfferModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_capital_v1_loan_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanOfferModel.ProtoReflect.Descriptor instead.
func (*LoanOfferModel) Descriptor() ([]byte, []int) {
	return file_moego_models_capital_v1_loan_models_proto_rawDescGZIP(), []int{0}
}

func (x *LoanOfferModel) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *LoanOfferModel) GetChannelName() LoanChannel {
	if x != nil {
		return x.ChannelName
	}
	return LoanChannel_LOAN_CHANNEL_UNSPECIFIED
}

func (x *LoanOfferModel) GetChannelAccountId() string {
	if x != nil {
		return x.ChannelAccountId
	}
	return ""
}

func (x *LoanOfferModel) GetChannelOfferId() string {
	if x != nil {
		return x.ChannelOfferId
	}
	return ""
}

func (x *LoanOfferModel) GetOfferType() LoanOfferType {
	if x != nil {
		return x.OfferType
	}
	return LoanOfferType_LOAN_OFFER_TYPE_UNSPECIFIED
}

func (x *LoanOfferModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *LoanOfferModel) GetExpireAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireAt
	}
	return nil
}

func (x *LoanOfferModel) GetClosedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ClosedAt
	}
	return nil
}

func (x *LoanOfferModel) GetOfferedTerms() *LoanTerms {
	if x != nil {
		return x.OfferedTerms
	}
	return nil
}

func (x *LoanOfferModel) GetAcceptedTerms() *LoanTerms {
	if x != nil {
		return x.AcceptedTerms
	}
	return nil
}

func (x *LoanOfferModel) GetProductType() LoanProductType {
	if x != nil {
		return x.ProductType
	}
	return LoanProductType_LOAN_PRODUCT_TYPE_UNSPECIFIED
}

func (x *LoanOfferModel) GetStatus() LoanOfferStatus {
	if x != nil {
		return x.Status
	}
	return LoanOfferStatus_LOAN_OFFER_STATUS_UNSPECIFIED
}

func (x *LoanOfferModel) GetChannelStatus() string {
	if x != nil {
		return x.ChannelStatus
	}
	return ""
}

func (x *LoanOfferModel) GetRemainingAmount() float64 {
	if x != nil {
		return x.RemainingAmount
	}
	return 0
}

func (x *LoanOfferModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *LoanOfferModel) GetEntityType() string {
	if x != nil {
		return x.EntityType
	}
	return ""
}

func (x *LoanOfferModel) GetEntityId() int64 {
	if x != nil {
		return x.EntityId
	}
	return 0
}

func (x *LoanOfferModel) GetRepaymentStartAt() *timestamppb.Timestamp {
	if x != nil {
		return x.RepaymentStartAt
	}
	return nil
}

func (x *LoanOfferModel) GetRemainingPrincipalAmount() float64 {
	if x != nil {
		return x.RemainingPrincipalAmount
	}
	return 0
}

// OfferedTerms
type LoanTerms struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Amount of financing offered, in minor units.
	AdvanceAmount float64 `protobuf:"fixed64,1,opt,name=advance_amount,json=advanceAmount,proto3" json:"advance_amount,omitempty"`
	// Type of campaign, e.g. "newly_eligible_user", "previously_eligible_user", "repeat_user"
	CampaignType LoanCampaignType `protobuf:"varint,2,opt,name=campaign_type,json=campaignType,proto3,enum=moego.models.capital.v1.LoanCampaignType" json:"campaign_type,omitempty"`
	// Currency code, e.g. "usd", "cad"
	Currency string `protobuf:"bytes,3,opt,name=currency,proto3" json:"currency,omitempty"`
	// Fixed fee amount, in minor units.
	FeeAmount float64 `protobuf:"fixed64,4,opt,name=fee_amount,json=feeAmount,proto3" json:"fee_amount,omitempty"`
	// The full amount to repay.
	FullRepayAmount float64 `protobuf:"fixed64,5,opt,name=full_repay_amount,json=fullRepayAmount,proto3" json:"full_repay_amount,omitempty"`
	// Populated when the product_type is refill.
	// Represents the discount rate percentage on remaining fee on the existing loan.
	// When the financing_offer is paid out, the previous_financing_fee_discount_amount
	// will be computed as the multiple of this rate and the remaining fee.
	// Not available for the accepted terms.
	PreviousFinancingFeeDiscountRate *float64 `protobuf:"fixed64,6,opt,name=previous_financing_fee_discount_rate,json=previousFinancingFeeDiscountRate,proto3,oneof" json:"previous_financing_fee_discount_rate,omitempty"`
	// Per-transaction rate at which Stripe will withhold funds to repay the financing.
	WithholdRate float64 `protobuf:"fixed64,7,opt,name=withhold_rate,json=withholdRate,proto3" json:"withhold_rate,omitempty"`
	// Populated when the product type of the offer is refill. Represents the discount amount on remaining premium for the
	// existing loan at payout time.
	// Not available for the offered terms.
	PreviousFinancingFeeDiscountAmount *float64 `protobuf:"fixed64,8,opt,name=previous_financing_fee_discount_amount,json=previousFinancingFeeDiscountAmount,proto3,oneof" json:"previous_financing_fee_discount_amount,omitempty"`
	// Interest rate, available for Term Loan.
	InterestRate float64 `protobuf:"fixed64,9,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
}

func (x *LoanTerms) Reset() {
	*x = LoanTerms{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_capital_v1_loan_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanTerms) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanTerms) ProtoMessage() {}

func (x *LoanTerms) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_capital_v1_loan_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanTerms.ProtoReflect.Descriptor instead.
func (*LoanTerms) Descriptor() ([]byte, []int) {
	return file_moego_models_capital_v1_loan_models_proto_rawDescGZIP(), []int{1}
}

func (x *LoanTerms) GetAdvanceAmount() float64 {
	if x != nil {
		return x.AdvanceAmount
	}
	return 0
}

func (x *LoanTerms) GetCampaignType() LoanCampaignType {
	if x != nil {
		return x.CampaignType
	}
	return LoanCampaignType_LOAN_CAMPAIGN_TYPE_UNSPECIFIED
}

func (x *LoanTerms) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *LoanTerms) GetFeeAmount() float64 {
	if x != nil {
		return x.FeeAmount
	}
	return 0
}

func (x *LoanTerms) GetFullRepayAmount() float64 {
	if x != nil {
		return x.FullRepayAmount
	}
	return 0
}

func (x *LoanTerms) GetPreviousFinancingFeeDiscountRate() float64 {
	if x != nil && x.PreviousFinancingFeeDiscountRate != nil {
		return *x.PreviousFinancingFeeDiscountRate
	}
	return 0
}

func (x *LoanTerms) GetWithholdRate() float64 {
	if x != nil {
		return x.WithholdRate
	}
	return 0
}

func (x *LoanTerms) GetPreviousFinancingFeeDiscountAmount() float64 {
	if x != nil && x.PreviousFinancingFeeDiscountAmount != nil {
		return *x.PreviousFinancingFeeDiscountAmount
	}
	return 0
}

func (x *LoanTerms) GetInterestRate() float64 {
	if x != nil {
		return x.InterestRate
	}
	return 0
}

// Interval information for a loan offer, if applicable.
type LoanOfferIntervalModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the loan offer
	OfferId string `protobuf:"bytes,1,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	// The sequence number of the current interval
	Sequence int64 `protobuf:"varint,2,opt,name=sequence,proto3" json:"sequence,omitempty"`
	// The begin time of the current interval
	BeginAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=begin_at,json=beginAt,proto3" json:"begin_at,omitempty"`
	// The end time of the current interval
	DueAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=due_at,json=dueAt,proto3" json:"due_at,omitempty"`
	// The minimum amount of the current interval
	MinimumAmount float64 `protobuf:"fixed64,5,opt,name=minimum_amount,json=minimumAmount,proto3" json:"minimum_amount,omitempty"`
	// The paid amount of the current interval
	PaidAmount float64 `protobuf:"fixed64,6,opt,name=paid_amount,json=paidAmount,proto3" json:"paid_amount,omitempty"`
}

func (x *LoanOfferIntervalModel) Reset() {
	*x = LoanOfferIntervalModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_capital_v1_loan_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanOfferIntervalModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanOfferIntervalModel) ProtoMessage() {}

func (x *LoanOfferIntervalModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_capital_v1_loan_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanOfferIntervalModel.ProtoReflect.Descriptor instead.
func (*LoanOfferIntervalModel) Descriptor() ([]byte, []int) {
	return file_moego_models_capital_v1_loan_models_proto_rawDescGZIP(), []int{2}
}

func (x *LoanOfferIntervalModel) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *LoanOfferIntervalModel) GetSequence() int64 {
	if x != nil {
		return x.Sequence
	}
	return 0
}

func (x *LoanOfferIntervalModel) GetBeginAt() *timestamppb.Timestamp {
	if x != nil {
		return x.BeginAt
	}
	return nil
}

func (x *LoanOfferIntervalModel) GetDueAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DueAt
	}
	return nil
}

func (x *LoanOfferIntervalModel) GetMinimumAmount() float64 {
	if x != nil {
		return x.MinimumAmount
	}
	return 0
}

func (x *LoanOfferIntervalModel) GetPaidAmount() float64 {
	if x != nil {
		return x.PaidAmount
	}
	return 0
}

// LoanOfferRepaymentTransactionModel
type LoanOfferRepaymentTransactionModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the repayment transaction.
	TransactionId string `protobuf:"bytes,1,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	// The ID of the repayment transaction in the channel.
	ChannelTransactionId string `protobuf:"bytes,2,opt,name=channel_transaction_id,json=channelTransactionId,proto3" json:"channel_transaction_id,omitempty"`
	// The type of the repayment
	TransactionType LoanTransactionType `protobuf:"varint,3,opt,name=transaction_type,json=transactionType,proto3,enum=moego.models.capital.v1.LoanTransactionType" json:"transaction_type,omitempty"`
	// The reason of the transaction.
	TransactionReason LoanTransactionReason `protobuf:"varint,4,opt,name=transaction_reason,json=transactionReason,proto3,enum=moego.models.capital.v1.LoanTransactionReason" json:"transaction_reason,omitempty"`
	// The name of the channel that created the repayment transaction.
	ChannelName LoanChannel `protobuf:"varint,5,opt,name=channel_name,json=channelName,proto3,enum=moego.models.capital.v1.LoanChannel" json:"channel_name,omitempty"`
	// The ID of the channel payment that is linked to this repayment transaction.
	LinkedChannelPaymentId string `protobuf:"bytes,6,opt,name=linked_channel_payment_id,json=linkedChannelPaymentId,proto3" json:"linked_channel_payment_id,omitempty"`
	// The ID of the payment that is linked to this repayment transaction.
	LinkedPaymentId int64 `protobuf:"varint,7,opt,name=linked_payment_id,json=linkedPaymentId,proto3" json:"linked_payment_id,omitempty"`
	// The ID of the order that is linked to this repayment transaction.
	LinkedOrderId int64 `protobuf:"varint,8,opt,name=linked_order_id,json=linkedOrderId,proto3" json:"linked_order_id,omitempty"`
	// The unix timestamp in seconds when the repayment transaction was effective.
	EffectiveTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=effective_time,json=effectiveTime,proto3" json:"effective_time,omitempty"`
	// The amount of the repayment transaction that is used to pay the advance amount.
	AdvancePaymentAmount float64 `protobuf:"fixed64,10,opt,name=advance_payment_amount,json=advancePaymentAmount,proto3" json:"advance_payment_amount,omitempty"`
	// The amount of the repayment transaction that is used to pay the fee.
	FeePaymentAmount float64 `protobuf:"fixed64,11,opt,name=fee_payment_amount,json=feePaymentAmount,proto3" json:"fee_payment_amount,omitempty"`
	// The total amount of the repayment transaction.
	TotalPaymentAmount float64 `protobuf:"fixed64,12,opt,name=total_payment_amount,json=totalPaymentAmount,proto3" json:"total_payment_amount,omitempty"`
	// The status of the repayment transaction. Should be one of: PROCESSING, SUCCESSFUL, FAILED.
	// For Stripe pay down transaction, this field is unfilled and unused.
	Status string `protobuf:"bytes,13,opt,name=status,proto3" json:"status,omitempty"`
	// the id of the repayment transaction that is reversed by this transaction.
	ReversedTransactionId string `protobuf:"bytes,14,opt,name=reversed_transaction_id,json=reversedTransactionId,proto3" json:"reversed_transaction_id,omitempty"`
	// the description of the repayment transaction.
	Description string `protobuf:"bytes,15,opt,name=description,proto3" json:"description,omitempty"`
	// The repaid amount to the interest. Only applicable for Term Loan.
	InterestPaymentAmount *money.Money `protobuf:"bytes,16,opt,name=interest_payment_amount,json=interestPaymentAmount,proto3" json:"interest_payment_amount,omitempty"`
	// The fee type.
	FeeType LoanTransactionFeeType `protobuf:"varint,17,opt,name=fee_type,json=feeType,proto3,enum=moego.models.capital.v1.LoanTransactionFeeType" json:"fee_type,omitempty"`
	// The unix timestamp when the repayment transaction was settled.
	// TODO(Perqin, P0): Confirm will it be set for failed?
	SettledTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=settled_time,json=settledTime,proto3" json:"settled_time,omitempty"`
}

func (x *LoanOfferRepaymentTransactionModel) Reset() {
	*x = LoanOfferRepaymentTransactionModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_capital_v1_loan_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanOfferRepaymentTransactionModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanOfferRepaymentTransactionModel) ProtoMessage() {}

func (x *LoanOfferRepaymentTransactionModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_capital_v1_loan_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanOfferRepaymentTransactionModel.ProtoReflect.Descriptor instead.
func (*LoanOfferRepaymentTransactionModel) Descriptor() ([]byte, []int) {
	return file_moego_models_capital_v1_loan_models_proto_rawDescGZIP(), []int{3}
}

func (x *LoanOfferRepaymentTransactionModel) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *LoanOfferRepaymentTransactionModel) GetChannelTransactionId() string {
	if x != nil {
		return x.ChannelTransactionId
	}
	return ""
}

func (x *LoanOfferRepaymentTransactionModel) GetTransactionType() LoanTransactionType {
	if x != nil {
		return x.TransactionType
	}
	return LoanTransactionType_LOAN_TRANSACTION_TYPE_UNSPECIFIED
}

func (x *LoanOfferRepaymentTransactionModel) GetTransactionReason() LoanTransactionReason {
	if x != nil {
		return x.TransactionReason
	}
	return LoanTransactionReason_LOAN_TRANSACTION_REASON_UNSPECIFIED
}

func (x *LoanOfferRepaymentTransactionModel) GetChannelName() LoanChannel {
	if x != nil {
		return x.ChannelName
	}
	return LoanChannel_LOAN_CHANNEL_UNSPECIFIED
}

func (x *LoanOfferRepaymentTransactionModel) GetLinkedChannelPaymentId() string {
	if x != nil {
		return x.LinkedChannelPaymentId
	}
	return ""
}

func (x *LoanOfferRepaymentTransactionModel) GetLinkedPaymentId() int64 {
	if x != nil {
		return x.LinkedPaymentId
	}
	return 0
}

func (x *LoanOfferRepaymentTransactionModel) GetLinkedOrderId() int64 {
	if x != nil {
		return x.LinkedOrderId
	}
	return 0
}

func (x *LoanOfferRepaymentTransactionModel) GetEffectiveTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveTime
	}
	return nil
}

func (x *LoanOfferRepaymentTransactionModel) GetAdvancePaymentAmount() float64 {
	if x != nil {
		return x.AdvancePaymentAmount
	}
	return 0
}

func (x *LoanOfferRepaymentTransactionModel) GetFeePaymentAmount() float64 {
	if x != nil {
		return x.FeePaymentAmount
	}
	return 0
}

func (x *LoanOfferRepaymentTransactionModel) GetTotalPaymentAmount() float64 {
	if x != nil {
		return x.TotalPaymentAmount
	}
	return 0
}

func (x *LoanOfferRepaymentTransactionModel) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *LoanOfferRepaymentTransactionModel) GetReversedTransactionId() string {
	if x != nil {
		return x.ReversedTransactionId
	}
	return ""
}

func (x *LoanOfferRepaymentTransactionModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LoanOfferRepaymentTransactionModel) GetInterestPaymentAmount() *money.Money {
	if x != nil {
		return x.InterestPaymentAmount
	}
	return nil
}

func (x *LoanOfferRepaymentTransactionModel) GetFeeType() LoanTransactionFeeType {
	if x != nil {
		return x.FeeType
	}
	return LoanTransactionFeeType_LOAN_TRANSACTION_FEE_TYPE_UNSPECIFIED
}

func (x *LoanOfferRepaymentTransactionModel) GetSettledTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SettledTime
	}
	return nil
}

// Notable offer update
type NotableOfferUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the offer
	OfferId string `protobuf:"bytes,1,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	// update type
	UpdateType NotableOfferUpdate_NotableOfferUpdateType `protobuf:"varint,2,opt,name=update_type,json=updateType,proto3,enum=moego.models.capital.v1.NotableOfferUpdate_NotableOfferUpdateType" json:"update_type,omitempty"`
}

func (x *NotableOfferUpdate) Reset() {
	*x = NotableOfferUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_capital_v1_loan_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotableOfferUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotableOfferUpdate) ProtoMessage() {}

func (x *NotableOfferUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_capital_v1_loan_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotableOfferUpdate.ProtoReflect.Descriptor instead.
func (*NotableOfferUpdate) Descriptor() ([]byte, []int) {
	return file_moego_models_capital_v1_loan_models_proto_rawDescGZIP(), []int{4}
}

func (x *NotableOfferUpdate) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *NotableOfferUpdate) GetUpdateType() NotableOfferUpdate_NotableOfferUpdateType {
	if x != nil {
		return x.UpdateType
	}
	return NotableOfferUpdate_NOTABLE_OFFER_UPDATE_TYPE_UNSPECIFIED
}

var File_moego_models_capital_v1_loan_models_proto protoreflect.FileDescriptor

var file_moego_models_capital_v1_loan_models_proto_rawDesc = []byte{
	0x0a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63,
	0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61,
	0x6c, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x61, 0x70,
	0x69, 0x74, 0x61, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x9e, 0x0a, 0x0a, 0x0e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x40, 0x52,
	0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61,
	0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x35, 0x0a, 0x12, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x18, 0x40, 0x52, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x40, 0x52, 0x0e, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x0a, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63,
	0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x43, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x32, 0x00, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x41, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x32, 0x00,
	0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x41, 0x74, 0x12, 0x41, 0x0a, 0x09, 0x63, 0x6c,
	0x6f, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01,
	0x02, 0x32, 0x00, 0x52, 0x08, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x74, 0x12, 0x47, 0x0a,
	0x0d, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x6f, 0x61, 0x6e, 0x54, 0x65, 0x72, 0x6d, 0x73, 0x52, 0x0c, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x65,
	0x64, 0x54, 0x65, 0x72, 0x6d, 0x73, 0x12, 0x4e, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74,
	0x65, 0x64, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61,
	0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x54, 0x65, 0x72,
	0x6d, 0x73, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x54, 0x65,
	0x72, 0x6d, 0x73, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69,
	0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f,
	0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x8f, 0x01, 0x0a, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x68,
	0xfa, 0x42, 0x65, 0x72, 0x63, 0x18, 0x14, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x65, 0x64, 0x52, 0x08, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x52, 0x08, 0x63, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64, 0x52, 0x07, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x52,
	0x0c, 0x66, 0x75, 0x6c, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x70, 0x61, 0x69, 0x64, 0x52, 0x08, 0x70,
	0x61, 0x69, 0x64, 0x5f, 0x6f, 0x75, 0x74, 0x52, 0x08, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x64, 0x52, 0x0b, 0x75, 0x6e, 0x64,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x65, 0x64, 0x52, 0x0d, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x6d, 0x61, 0x69,
	0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x18, 0x40, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x52, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x0a, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x52, 0x0a,
	0x12, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x32, 0x00, 0x52,
	0x10, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x41,
	0x74, 0x12, 0x4c, 0x0a, 0x1a, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x70,
	0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x18, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67,
	0x50, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42,
	0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x72,
	0x6d, 0x73, 0x22, 0xc5, 0x04, 0x0a, 0x09, 0x4c, 0x6f, 0x61, 0x6e, 0x54, 0x65, 0x72, 0x6d, 0x73,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63,
	0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61,
	0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x65, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x66, 0x65, 0x65, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x70, 0x61, 0x79,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x66,
	0x75, 0x6c, 0x6c, 0x52, 0x65, 0x70, 0x61, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x53,
	0x0a, 0x24, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6e,
	0x63, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x20,
	0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x6e,
	0x67, 0x46, 0x65, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0d, 0x77, 0x69, 0x74, 0x68, 0x68, 0x6f, 0x6c, 0x64, 0x5f,
	0x72, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x77, 0x69, 0x74, 0x68,
	0x68, 0x6f, 0x6c, 0x64, 0x52, 0x61, 0x74, 0x65, 0x12, 0x57, 0x0a, 0x26, 0x70, 0x72, 0x65, 0x76,
	0x69, 0x6f, 0x75, 0x73, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x66,
	0x65, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x48, 0x01, 0x52, 0x22, 0x70, 0x72, 0x65, 0x76,
	0x69, 0x6f, 0x75, 0x73, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01,
	0x01, 0x12, 0x33, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x61,
	0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65,
	0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x42, 0x27, 0x0a, 0x25, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69,
	0x6f, 0x75, 0x73, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65,
	0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x42,
	0x29, 0x0a, 0x27, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x66, 0x69, 0x6e,
	0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xa7, 0x02, 0x0a, 0x16, 0x4c,
	0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x40,
	0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x08, 0x73, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x28, 0x00, 0x52, 0x08, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x3f,
	0x0a, 0x08, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0xb2, 0x01, 0x02, 0x32, 0x00, 0x52, 0x07, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x41, 0x74, 0x12,
	0x3b, 0x0a, 0x06, 0x64, 0x75, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0xb2, 0x01, 0x02, 0x32, 0x00, 0x52, 0x05, 0x64, 0x75, 0x65, 0x41, 0x74, 0x12, 0x25, 0x0a, 0x0e,
	0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x69, 0x64, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0xbf, 0x09, 0x0a, 0x22, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x2e, 0x0a, 0x0e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x40, 0x52, 0x0d, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x16, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x18, 0x40, 0x52, 0x14, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x57, 0x0a, 0x10, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x6f, 0x61, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x5d, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63,
	0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52,
	0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x0b,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x19, 0x6c,
	0x69, 0x6e, 0x6b, 0x65, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x40, 0x52, 0x16, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x33, 0x0a, 0x11, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x28, 0x00, 0x52, 0x0f, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0f, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0d, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0e, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01,
	0x02, 0x32, 0x00, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x14, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x65, 0x65, 0x5f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x66, 0x65, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x44, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2c, 0xfa, 0x42, 0x29, 0x72, 0x27, 0x18,
	0x10, 0x52, 0x00, 0x52, 0x0a, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x52,
	0x0a, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x52, 0x06, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0xd0, 0x01, 0x01, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f,
	0x0a, 0x17, 0x72, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x40, 0x52, 0x15, 0x72, 0x65, 0x76, 0x65, 0x72, 0x73,
	0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x01, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x17, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x15, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x54, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x66, 0x65, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x47, 0x0a,
	0x0c, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x32, 0x00, 0x52, 0x0b, 0x73, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xfe, 0x01, 0x0a, 0x12, 0x4e, 0x6f, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a,
	0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x6f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e,
	0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x4e, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x51, 0x0a, 0x16, 0x4e, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29,
	0x0a, 0x25, 0x4e, 0x4f, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x4a,
	0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x01, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x61, 0x70, 0x69, 0x74,
	0x61, 0x6c, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_capital_v1_loan_models_proto_rawDescOnce sync.Once
	file_moego_models_capital_v1_loan_models_proto_rawDescData = file_moego_models_capital_v1_loan_models_proto_rawDesc
)

func file_moego_models_capital_v1_loan_models_proto_rawDescGZIP() []byte {
	file_moego_models_capital_v1_loan_models_proto_rawDescOnce.Do(func() {
		file_moego_models_capital_v1_loan_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_capital_v1_loan_models_proto_rawDescData)
	})
	return file_moego_models_capital_v1_loan_models_proto_rawDescData
}

var file_moego_models_capital_v1_loan_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_capital_v1_loan_models_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_models_capital_v1_loan_models_proto_goTypes = []interface{}{
	(NotableOfferUpdate_NotableOfferUpdateType)(0), // 0: moego.models.capital.v1.NotableOfferUpdate.NotableOfferUpdateType
	(*LoanOfferModel)(nil),                         // 1: moego.models.capital.v1.LoanOfferModel
	(*LoanTerms)(nil),                              // 2: moego.models.capital.v1.LoanTerms
	(*LoanOfferIntervalModel)(nil),                 // 3: moego.models.capital.v1.LoanOfferIntervalModel
	(*LoanOfferRepaymentTransactionModel)(nil),     // 4: moego.models.capital.v1.LoanOfferRepaymentTransactionModel
	(*NotableOfferUpdate)(nil),                     // 5: moego.models.capital.v1.NotableOfferUpdate
	(LoanChannel)(0),                               // 6: moego.models.capital.v1.LoanChannel
	(LoanOfferType)(0),                             // 7: moego.models.capital.v1.LoanOfferType
	(*timestamppb.Timestamp)(nil),                  // 8: google.protobuf.Timestamp
	(LoanProductType)(0),                           // 9: moego.models.capital.v1.LoanProductType
	(LoanOfferStatus)(0),                           // 10: moego.models.capital.v1.LoanOfferStatus
	(LoanCampaignType)(0),                          // 11: moego.models.capital.v1.LoanCampaignType
	(LoanTransactionType)(0),                       // 12: moego.models.capital.v1.LoanTransactionType
	(LoanTransactionReason)(0),                     // 13: moego.models.capital.v1.LoanTransactionReason
	(*money.Money)(nil),                            // 14: google.type.Money
	(LoanTransactionFeeType)(0),                    // 15: moego.models.capital.v1.LoanTransactionFeeType
}
var file_moego_models_capital_v1_loan_models_proto_depIdxs = []int32{
	6,  // 0: moego.models.capital.v1.LoanOfferModel.channel_name:type_name -> moego.models.capital.v1.LoanChannel
	7,  // 1: moego.models.capital.v1.LoanOfferModel.offer_type:type_name -> moego.models.capital.v1.LoanOfferType
	8,  // 2: moego.models.capital.v1.LoanOfferModel.created_at:type_name -> google.protobuf.Timestamp
	8,  // 3: moego.models.capital.v1.LoanOfferModel.expire_at:type_name -> google.protobuf.Timestamp
	8,  // 4: moego.models.capital.v1.LoanOfferModel.closed_at:type_name -> google.protobuf.Timestamp
	2,  // 5: moego.models.capital.v1.LoanOfferModel.offered_terms:type_name -> moego.models.capital.v1.LoanTerms
	2,  // 6: moego.models.capital.v1.LoanOfferModel.accepted_terms:type_name -> moego.models.capital.v1.LoanTerms
	9,  // 7: moego.models.capital.v1.LoanOfferModel.product_type:type_name -> moego.models.capital.v1.LoanProductType
	10, // 8: moego.models.capital.v1.LoanOfferModel.status:type_name -> moego.models.capital.v1.LoanOfferStatus
	8,  // 9: moego.models.capital.v1.LoanOfferModel.repayment_start_at:type_name -> google.protobuf.Timestamp
	11, // 10: moego.models.capital.v1.LoanTerms.campaign_type:type_name -> moego.models.capital.v1.LoanCampaignType
	8,  // 11: moego.models.capital.v1.LoanOfferIntervalModel.begin_at:type_name -> google.protobuf.Timestamp
	8,  // 12: moego.models.capital.v1.LoanOfferIntervalModel.due_at:type_name -> google.protobuf.Timestamp
	12, // 13: moego.models.capital.v1.LoanOfferRepaymentTransactionModel.transaction_type:type_name -> moego.models.capital.v1.LoanTransactionType
	13, // 14: moego.models.capital.v1.LoanOfferRepaymentTransactionModel.transaction_reason:type_name -> moego.models.capital.v1.LoanTransactionReason
	6,  // 15: moego.models.capital.v1.LoanOfferRepaymentTransactionModel.channel_name:type_name -> moego.models.capital.v1.LoanChannel
	8,  // 16: moego.models.capital.v1.LoanOfferRepaymentTransactionModel.effective_time:type_name -> google.protobuf.Timestamp
	14, // 17: moego.models.capital.v1.LoanOfferRepaymentTransactionModel.interest_payment_amount:type_name -> google.type.Money
	15, // 18: moego.models.capital.v1.LoanOfferRepaymentTransactionModel.fee_type:type_name -> moego.models.capital.v1.LoanTransactionFeeType
	8,  // 19: moego.models.capital.v1.LoanOfferRepaymentTransactionModel.settled_time:type_name -> google.protobuf.Timestamp
	0,  // 20: moego.models.capital.v1.NotableOfferUpdate.update_type:type_name -> moego.models.capital.v1.NotableOfferUpdate.NotableOfferUpdateType
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_moego_models_capital_v1_loan_models_proto_init() }
func file_moego_models_capital_v1_loan_models_proto_init() {
	if File_moego_models_capital_v1_loan_models_proto != nil {
		return
	}
	file_moego_models_capital_v1_loan_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_capital_v1_loan_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanOfferModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_capital_v1_loan_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanTerms); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_capital_v1_loan_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanOfferIntervalModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_capital_v1_loan_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanOfferRepaymentTransactionModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_capital_v1_loan_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotableOfferUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_capital_v1_loan_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_capital_v1_loan_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_capital_v1_loan_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_capital_v1_loan_models_proto_goTypes,
		DependencyIndexes: file_moego_models_capital_v1_loan_models_proto_depIdxs,
		EnumInfos:         file_moego_models_capital_v1_loan_models_proto_enumTypes,
		MessageInfos:      file_moego_models_capital_v1_loan_models_proto_msgTypes,
	}.Build()
	File_moego_models_capital_v1_loan_models_proto = out.File
	file_moego_models_capital_v1_loan_models_proto_rawDesc = nil
	file_moego_models_capital_v1_loan_models_proto_goTypes = nil
	file_moego_models_capital_v1_loan_models_proto_depIdxs = nil
}
