// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/automation/v1/step.proto

package automationpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	date "google.golang.org/genproto/googleapis/type/date"
	dayofweek "google.golang.org/genproto/googleapis/type/dayofweek"
	timeofday "google.golang.org/genproto/googleapis/type/timeofday"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ActionResultFilter
type ActionResultFilter int32

const (
	// TYPE_UNSPECIFIED
	ActionResultFilter_ACTION_FILTER_UNSPECIFIED ActionResultFilter = 0
	// INTAKE_FORM_SUBMIT_SUCCESS
	ActionResultFilter_INTAKE_FORM_SUBMIT_SUCCESS ActionResultFilter = 1
)

// Enum value maps for ActionResultFilter.
var (
	ActionResultFilter_name = map[int32]string{
		0: "ACTION_FILTER_UNSPECIFIED",
		1: "INTAKE_FORM_SUBMIT_SUCCESS",
	}
	ActionResultFilter_value = map[string]int32{
		"ACTION_FILTER_UNSPECIFIED":  0,
		"INTAKE_FORM_SUBMIT_SUCCESS": 1,
	}
)

func (x ActionResultFilter) Enum() *ActionResultFilter {
	p := new(ActionResultFilter)
	*p = x
	return p
}

func (x ActionResultFilter) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActionResultFilter) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_step_proto_enumTypes[0].Descriptor()
}

func (ActionResultFilter) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_step_proto_enumTypes[0]
}

func (x ActionResultFilter) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActionResultFilter.Descriptor instead.
func (ActionResultFilter) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{0}
}

// Type
type Step_Type int32

const (
	// TYPE_UNSPECIFIED
	Step_TYPE_UNSPECIFIED Step_Type = 0
	// TRIGGER
	Step_TRIGGER Step_Type = 1
	// WAIT
	Step_WAIT Step_Type = 2
	// MESSAGE
	Step_MESSAGE Step_Type = 3
	// CONDITION
	Step_CONDITION Step_Type = 4
	// ADVANCED ACTION
	Step_ADVANCED_ACTION Step_Type = 5
)

// Enum value maps for Step_Type.
var (
	Step_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "TRIGGER",
		2: "WAIT",
		3: "MESSAGE",
		4: "CONDITION",
		5: "ADVANCED_ACTION",
	}
	Step_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"TRIGGER":          1,
		"WAIT":             2,
		"MESSAGE":          3,
		"CONDITION":        4,
		"ADVANCED_ACTION":  5,
	}
)

func (x Step_Type) Enum() *Step_Type {
	p := new(Step_Type)
	*p = x
	return p
}

func (x Step_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Step_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_step_proto_enumTypes[1].Descriptor()
}

func (Step_Type) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_step_proto_enumTypes[1]
}

func (x Step_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Step_Type.Descriptor instead.
func (Step_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{0, 0}
}

// Type
type Trigger_Type int32

const (
	// TYPE_UNSPECIFIED
	Trigger_TYPE_UNSPECIFIED Trigger_Type = 0
	// SCHEDULED
	Trigger_SCHEDULED Trigger_Type = 1
	// EVENT
	Trigger_EVENT Trigger_Type = 2
	// BEFORE
	Trigger_BEFORE Trigger_Type = 3
)

// Enum value maps for Trigger_Type.
var (
	Trigger_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "SCHEDULED",
		2: "EVENT",
		3: "BEFORE",
	}
	Trigger_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"SCHEDULED":        1,
		"EVENT":            2,
		"BEFORE":           3,
	}
)

func (x Trigger_Type) Enum() *Trigger_Type {
	p := new(Trigger_Type)
	*p = x
	return p
}

func (x Trigger_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Trigger_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_step_proto_enumTypes[2].Descriptor()
}

func (Trigger_Type) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_step_proto_enumTypes[2]
}

func (x Trigger_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Trigger_Type.Descriptor instead.
func (Trigger_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{1, 0}
}

// Frequency
type Scheduled_Frequency int32

const (
	// FREQUENCY_UNSPECIFIED
	Scheduled_FREQUENCY_UNSPECIFIED Scheduled_Frequency = 0
	// DAILY
	Scheduled_DAILY Scheduled_Frequency = 1 // 每天
	// WEEKLY
	Scheduled_WEEKLY Scheduled_Frequency = 2 // 每周
	// MONTHLY
	Scheduled_MONTHLY Scheduled_Frequency = 3 // 每月
)

// Enum value maps for Scheduled_Frequency.
var (
	Scheduled_Frequency_name = map[int32]string{
		0: "FREQUENCY_UNSPECIFIED",
		1: "DAILY",
		2: "WEEKLY",
		3: "MONTHLY",
	}
	Scheduled_Frequency_value = map[string]int32{
		"FREQUENCY_UNSPECIFIED": 0,
		"DAILY":                 1,
		"WEEKLY":                2,
		"MONTHLY":               3,
	}
)

func (x Scheduled_Frequency) Enum() *Scheduled_Frequency {
	p := new(Scheduled_Frequency)
	*p = x
	return p
}

func (x Scheduled_Frequency) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Scheduled_Frequency) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_step_proto_enumTypes[3].Descriptor()
}

func (Scheduled_Frequency) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_step_proto_enumTypes[3]
}

func (x Scheduled_Frequency) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Scheduled_Frequency.Descriptor instead.
func (Scheduled_Frequency) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{2, 0}
}

// Category
type Event_Category int32

const (
	// CATEGORY_UNSPECIFIED
	Event_CATEGORY_UNSPECIFIED Event_Category = 0
	// ONLINE_BOOKINGS
	Event_ONLINE_BOOKINGS Event_Category = 1
	// APPOINTMENT
	Event_APPOINTMENT Event_Category = 2
	// INTAKE_FROM
	Event_INTAKE_FROM Event_Category = 3
	// PACKAGE
	Event_PACKAGE Event_Category = 4
	// MEMBERSHIP
	Event_MEMBERSHIP Event_Category = 5
	// DISCOUNT
	Event_DISCOUNT Event_Category = 6
	// CLIENT
	Event_CLIENT Event_Category = 7
)

// Enum value maps for Event_Category.
var (
	Event_Category_name = map[int32]string{
		0: "CATEGORY_UNSPECIFIED",
		1: "ONLINE_BOOKINGS",
		2: "APPOINTMENT",
		3: "INTAKE_FROM",
		4: "PACKAGE",
		5: "MEMBERSHIP",
		6: "DISCOUNT",
		7: "CLIENT",
	}
	Event_Category_value = map[string]int32{
		"CATEGORY_UNSPECIFIED": 0,
		"ONLINE_BOOKINGS":      1,
		"APPOINTMENT":          2,
		"INTAKE_FROM":          3,
		"PACKAGE":              4,
		"MEMBERSHIP":           5,
		"DISCOUNT":             6,
		"CLIENT":               7,
	}
)

func (x Event_Category) Enum() *Event_Category {
	p := new(Event_Category)
	*p = x
	return p
}

func (x Event_Category) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Event_Category) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_step_proto_enumTypes[4].Descriptor()
}

func (Event_Category) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_step_proto_enumTypes[4]
}

func (x Event_Category) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Event_Category.Descriptor instead.
func (Event_Category) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{3, 0}
}

// EntityTrigger
type Event_EntityTrigger int32

const (
	// ENTITY_UNSPECIFIED
	Event_ENTITY_UNSPECIFIED Event_EntityTrigger = 0
	// ONLINE_BOOKINGS_SUBMITTED
	Event_ONLINE_BOOKINGS_SUBMITTED Event_EntityTrigger = 100
	// ONLINE_BOOKINGS_ABANDONED
	Event_ONLINE_BOOKINGS_ABANDONED Event_EntityTrigger = 101
	// ONLINE_BOOKINGS_ACCEPTED
	Event_ONLINE_BOOKINGS_ACCEPTED Event_EntityTrigger = 102
	// EVALUATION_BOOKING_SUBMITTED
	Event_EVALUATION_BOOKING_SUBMITTED Event_EntityTrigger = 103
	// APPOINTMENT_CREATED
	Event_APPOINTMENT_CREATED Event_EntityTrigger = 200
	// APPOINTMENT_CANCELED
	Event_APPOINTMENT_CANCELED Event_EntityTrigger = 201
	// APPOINTMENT_FINISHED
	Event_APPOINTMENT_FINISHED Event_EntityTrigger = 202
	// APPOINTMENT_FULLY_PAID
	Event_APPOINTMENT_FULLY_PAID Event_EntityTrigger = 203
	// EVALUATION_FINISHED
	Event_EVALUATION_FINISHED Event_EntityTrigger = 204
	// PACKAGE_PURCHASED
	Event_PACKAGE_PURCHASED Event_EntityTrigger = 400
	// PACKAGE_REDEEMED
	Event_PACKAGE_REDEEMED Event_EntityTrigger = 401
	// MEMBERSHIP_PURCHASED
	Event_MEMBERSHIP_PURCHASED Event_EntityTrigger = 500
	// MEMBERSHIP_CANCELED
	Event_MEMBERSHIP_CANCELED Event_EntityTrigger = 501
	// DISCOUNT_CODE_REDEEMED
	Event_DISCOUNT_CODE_REDEEMED Event_EntityTrigger = 600
	// CLIENT_CREATED
	Event_CLIENT_CREATED Event_EntityTrigger = 700
)

// Enum value maps for Event_EntityTrigger.
var (
	Event_EntityTrigger_name = map[int32]string{
		0:   "ENTITY_UNSPECIFIED",
		100: "ONLINE_BOOKINGS_SUBMITTED",
		101: "ONLINE_BOOKINGS_ABANDONED",
		102: "ONLINE_BOOKINGS_ACCEPTED",
		103: "EVALUATION_BOOKING_SUBMITTED",
		200: "APPOINTMENT_CREATED",
		201: "APPOINTMENT_CANCELED",
		202: "APPOINTMENT_FINISHED",
		203: "APPOINTMENT_FULLY_PAID",
		204: "EVALUATION_FINISHED",
		400: "PACKAGE_PURCHASED",
		401: "PACKAGE_REDEEMED",
		500: "MEMBERSHIP_PURCHASED",
		501: "MEMBERSHIP_CANCELED",
		600: "DISCOUNT_CODE_REDEEMED",
		700: "CLIENT_CREATED",
	}
	Event_EntityTrigger_value = map[string]int32{
		"ENTITY_UNSPECIFIED":           0,
		"ONLINE_BOOKINGS_SUBMITTED":    100,
		"ONLINE_BOOKINGS_ABANDONED":    101,
		"ONLINE_BOOKINGS_ACCEPTED":     102,
		"EVALUATION_BOOKING_SUBMITTED": 103,
		"APPOINTMENT_CREATED":          200,
		"APPOINTMENT_CANCELED":         201,
		"APPOINTMENT_FINISHED":         202,
		"APPOINTMENT_FULLY_PAID":       203,
		"EVALUATION_FINISHED":          204,
		"PACKAGE_PURCHASED":            400,
		"PACKAGE_REDEEMED":             401,
		"MEMBERSHIP_PURCHASED":         500,
		"MEMBERSHIP_CANCELED":          501,
		"DISCOUNT_CODE_REDEEMED":       600,
		"CLIENT_CREATED":               700,
	}
)

func (x Event_EntityTrigger) Enum() *Event_EntityTrigger {
	p := new(Event_EntityTrigger)
	*p = x
	return p
}

func (x Event_EntityTrigger) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Event_EntityTrigger) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_step_proto_enumTypes[5].Descriptor()
}

func (Event_EntityTrigger) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_step_proto_enumTypes[5]
}

func (x Event_EntityTrigger) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Event_EntityTrigger.Descriptor instead.
func (Event_EntityTrigger) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{3, 1}
}

// Type
type Wait_Type int32

const (
	// TYPE_UNSPECIFIED
	Wait_TYPE_UNSPECIFIED Wait_Type = 0
	// DURATION
	Wait_DURATION Wait_Type = 1
)

// Enum value maps for Wait_Type.
var (
	Wait_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "DURATION",
	}
	Wait_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"DURATION":         1,
	}
)

func (x Wait_Type) Enum() *Wait_Type {
	p := new(Wait_Type)
	*p = x
	return p
}

func (x Wait_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Wait_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_step_proto_enumTypes[6].Descriptor()
}

func (Wait_Type) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_step_proto_enumTypes[6]
}

func (x Wait_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Wait_Type.Descriptor instead.
func (Wait_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{5, 0}
}

// Type
type Condition_Type int32

const (
	// TYPE_UNSPECIFIED
	Condition_TYPE_UNSPECIFIED Condition_Type = 0
	// Filter
	Condition_FILTER Condition_Type = 1
	// Action Result Filter
	Condition_ACTION_FILTER Condition_Type = 2
)

// Enum value maps for Condition_Type.
var (
	Condition_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "FILTER",
		2: "ACTION_FILTER",
	}
	Condition_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"FILTER":           1,
		"ACTION_FILTER":    2,
	}
)

func (x Condition_Type) Enum() *Condition_Type {
	p := new(Condition_Type)
	*p = x
	return p
}

func (x Condition_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Condition_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_step_proto_enumTypes[7].Descriptor()
}

func (Condition_Type) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_step_proto_enumTypes[7]
}

func (x Condition_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Condition_Type.Descriptor instead.
func (Condition_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{6, 0}
}

// Type
type Message_Type int32

const (
	// TYPE_UNSPECIFIED
	Message_TYPE_UNSPECIFIED Message_Type = 0
	// SMS
	Message_SMS Message_Type = 1
	// EMAIL
	Message_EMAIL Message_Type = 2
)

// Enum value maps for Message_Type.
var (
	Message_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "SMS",
		2: "EMAIL",
	}
	Message_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"SMS":              1,
		"EMAIL":            2,
	}
)

func (x Message_Type) Enum() *Message_Type {
	p := new(Message_Type)
	*p = x
	return p
}

func (x Message_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Message_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_step_proto_enumTypes[8].Descriptor()
}

func (Message_Type) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_step_proto_enumTypes[8]
}

func (x Message_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Message_Type.Descriptor instead.
func (Message_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{7, 0}
}

// Category
type Message_Category int32

const (
	// CATEGORY_UNSPECIFIED
	Message_CATEGORY_UNSPECIFIED Message_Category = 0
	// NOTIFICATION
	Message_NOTIFICATION Message_Category = 1
	// CAMPAIGN
	Message_CAMPAIGN Message_Category = 2
)

// Enum value maps for Message_Category.
var (
	Message_Category_name = map[int32]string{
		0: "CATEGORY_UNSPECIFIED",
		1: "NOTIFICATION",
		2: "CAMPAIGN",
	}
	Message_Category_value = map[string]int32{
		"CATEGORY_UNSPECIFIED": 0,
		"NOTIFICATION":         1,
		"CAMPAIGN":             2,
	}
)

func (x Message_Category) Enum() *Message_Category {
	p := new(Message_Category)
	*p = x
	return p
}

func (x Message_Category) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Message_Category) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_step_proto_enumTypes[9].Descriptor()
}

func (Message_Category) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_step_proto_enumTypes[9]
}

func (x Message_Category) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Message_Category.Descriptor instead.
func (Message_Category) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{7, 1}
}

// Type
type AdvancedAction_Type int32

const (
	// TYPE_UNSPECIFIED
	AdvancedAction_TYPE_UNSPECIFIED AdvancedAction_Type = 0
	// ADD_CLIENT_TAG
	AdvancedAction_ADD_CLIENT_TAG AdvancedAction_Type = 1
	// DELETE_CLIENT_TAG
	AdvancedAction_DELETE_CLIENT_TAG AdvancedAction_Type = 2
	// CHANGE_CLIENT_TAG
	AdvancedAction_CHANGE_CLIENT_TAG AdvancedAction_Type = 3
	// ADD_PET_CODE
	AdvancedAction_ADD_PET_CODE AdvancedAction_Type = 4
	// CHANGE_CUSTOMER_LIFE_CYCLE
	AdvancedAction_CHANGE_CUSTOMER_LIFE_CYCLE AdvancedAction_Type = 5
	// CHANGE_CUSTOMER_ACTION_STATUS
	AdvancedAction_CHANGE_CUSTOMER_ACTION_STATUS AdvancedAction_Type = 6
	// ADD_CUSTOMER_TASK
	AdvancedAction_ADD_CUSTOMER_TASK AdvancedAction_Type = 7
)

// Enum value maps for AdvancedAction_Type.
var (
	AdvancedAction_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "ADD_CLIENT_TAG",
		2: "DELETE_CLIENT_TAG",
		3: "CHANGE_CLIENT_TAG",
		4: "ADD_PET_CODE",
		5: "CHANGE_CUSTOMER_LIFE_CYCLE",
		6: "CHANGE_CUSTOMER_ACTION_STATUS",
		7: "ADD_CUSTOMER_TASK",
	}
	AdvancedAction_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED":              0,
		"ADD_CLIENT_TAG":                1,
		"DELETE_CLIENT_TAG":             2,
		"CHANGE_CLIENT_TAG":             3,
		"ADD_PET_CODE":                  4,
		"CHANGE_CUSTOMER_LIFE_CYCLE":    5,
		"CHANGE_CUSTOMER_ACTION_STATUS": 6,
		"ADD_CUSTOMER_TASK":             7,
	}
)

func (x AdvancedAction_Type) Enum() *AdvancedAction_Type {
	p := new(AdvancedAction_Type)
	*p = x
	return p
}

func (x AdvancedAction_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdvancedAction_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_step_proto_enumTypes[10].Descriptor()
}

func (AdvancedAction_Type) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_step_proto_enumTypes[10]
}

func (x AdvancedAction_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AdvancedAction_Type.Descriptor instead.
func (AdvancedAction_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{10, 0}
}

// Step
type Step struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// meta data
	// Step ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Workflow ID
	WorkflowId int64 `protobuf:"varint,2,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	// Step name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// Step description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// adjacency data
	// Parent step ID
	ParentId string `protobuf:"bytes,5,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// Children step IDs
	ChildrenIds []string `protobuf:"bytes,6,rep,name=children_ids,json=childrenIds,proto3" json:"children_ids,omitempty"`
	// Hierarchical path
	HierarchicalPath string `protobuf:"bytes,7,opt,name=hierarchical_path,json=hierarchicalPath,proto3" json:"hierarchical_path,omitempty"`
	// content data
	// Step type
	Type Step_Type `protobuf:"varint,8,opt,name=type,proto3,enum=moego.models.automation.v1.Step_Type" json:"type,omitempty"`
	// Step data
	Data *Step_Data `protobuf:"bytes,9,opt,name=data,proto3" json:"data,omitempty"`
	// Step data preview
	PreviewData *Step_PreviewData `protobuf:"bytes,10,opt,name=preview_data,json=previewData,proto3,oneof" json:"preview_data,omitempty"`
}

func (x *Step) Reset() {
	*x = Step{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Step) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Step) ProtoMessage() {}

func (x *Step) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Step.ProtoReflect.Descriptor instead.
func (*Step) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{0}
}

func (x *Step) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Step) GetWorkflowId() int64 {
	if x != nil {
		return x.WorkflowId
	}
	return 0
}

func (x *Step) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Step) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Step) GetParentId() string {
	if x != nil {
		return x.ParentId
	}
	return ""
}

func (x *Step) GetChildrenIds() []string {
	if x != nil {
		return x.ChildrenIds
	}
	return nil
}

func (x *Step) GetHierarchicalPath() string {
	if x != nil {
		return x.HierarchicalPath
	}
	return ""
}

func (x *Step) GetType() Step_Type {
	if x != nil {
		return x.Type
	}
	return Step_TYPE_UNSPECIFIED
}

func (x *Step) GetData() *Step_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Step) GetPreviewData() *Step_PreviewData {
	if x != nil {
		return x.PreviewData
	}
	return nil
}

// Trigger
type Trigger struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Trigger type
	Type Trigger_Type `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.automation.v1.Trigger_Type" json:"type,omitempty"`
	// Trigger data
	Data *Trigger_Data `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	// Filters
	Filters []*v2.FilterRequest `protobuf:"bytes,3,rep,name=filters,proto3" json:"filters,omitempty"`
	// Trigger name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Trigger) Reset() {
	*x = Trigger{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Trigger) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Trigger) ProtoMessage() {}

func (x *Trigger) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Trigger.ProtoReflect.Descriptor instead.
func (*Trigger) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{1}
}

func (x *Trigger) GetType() Trigger_Type {
	if x != nil {
		return x.Type
	}
	return Trigger_TYPE_UNSPECIFIED
}

func (x *Trigger) GetData() *Trigger_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Trigger) GetFilters() []*v2.FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *Trigger) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Scheduled
type Scheduled struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Day of week
	DayOfWeek dayofweek.DayOfWeek `protobuf:"varint,1,opt,name=day_of_week,json=dayOfWeek,proto3,enum=google.type.DayOfWeek" json:"day_of_week,omitempty"` // 适用于每周调度，表示星期几（0=周日, 1=周一, ..., 6=周六）
	// Time of day
	TimeOfDay *timeofday.TimeOfDay `protobuf:"bytes,2,opt,name=time_of_day,json=timeOfDay,proto3" json:"time_of_day,omitempty"` // 适用于每天调度, 一天中的具体时间
	// Date
	Date *date.Date `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty"` // 适用于每月调度，表示几号（1-31）
	// Frequency
	Frequency Scheduled_Frequency `protobuf:"varint,4,opt,name=frequency,proto3,enum=moego.models.automation.v1.Scheduled_Frequency" json:"frequency,omitempty"` // 调度频率
}

func (x *Scheduled) Reset() {
	*x = Scheduled{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Scheduled) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scheduled) ProtoMessage() {}

func (x *Scheduled) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scheduled.ProtoReflect.Descriptor instead.
func (*Scheduled) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{2}
}

func (x *Scheduled) GetDayOfWeek() dayofweek.DayOfWeek {
	if x != nil {
		return x.DayOfWeek
	}
	return dayofweek.DayOfWeek(0)
}

func (x *Scheduled) GetTimeOfDay() *timeofday.TimeOfDay {
	if x != nil {
		return x.TimeOfDay
	}
	return nil
}

func (x *Scheduled) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *Scheduled) GetFrequency() Scheduled_Frequency {
	if x != nil {
		return x.Frequency
	}
	return Scheduled_FREQUENCY_UNSPECIFIED
}

// Event
type Event struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Event category
	Category Event_Category `protobuf:"varint,1,opt,name=category,proto3,enum=moego.models.automation.v1.Event_Category" json:"category,omitempty"`
	// Event trigger
	Trigger Event_EntityTrigger `protobuf:"varint,2,opt,name=trigger,proto3,enum=moego.models.automation.v1.Event_EntityTrigger" json:"trigger,omitempty"`
	// Event Filters
	Filters []*v2.FilterRequest `protobuf:"bytes,3,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *Event) Reset() {
	*x = Event{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Event) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Event) ProtoMessage() {}

func (x *Event) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Event.ProtoReflect.Descriptor instead.
func (*Event) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{3}
}

func (x *Event) GetCategory() Event_Category {
	if x != nil {
		return x.Category
	}
	return Event_CATEGORY_UNSPECIFIED
}

func (x *Event) GetTrigger() Event_EntityTrigger {
	if x != nil {
		return x.Trigger
	}
	return Event_ENTITY_UNSPECIFIED
}

func (x *Event) GetFilters() []*v2.FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

// Before
type Before struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Before) Reset() {
	*x = Before{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Before) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Before) ProtoMessage() {}

func (x *Before) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Before.ProtoReflect.Descriptor instead.
func (*Before) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{4}
}

// Wait
type Wait struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Wait type
	Type Wait_Type `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.automation.v1.Wait_Type" json:"type,omitempty"`
	// Time duration
	TimeDuration *TimeDuration `protobuf:"bytes,2,opt,name=time_duration,json=timeDuration,proto3" json:"time_duration,omitempty"`
}

func (x *Wait) Reset() {
	*x = Wait{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Wait) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Wait) ProtoMessage() {}

func (x *Wait) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Wait.ProtoReflect.Descriptor instead.
func (*Wait) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{5}
}

func (x *Wait) GetType() Wait_Type {
	if x != nil {
		return x.Type
	}
	return Wait_TYPE_UNSPECIFIED
}

func (x *Wait) GetTimeDuration() *TimeDuration {
	if x != nil {
		return x.TimeDuration
	}
	return nil
}

// Condition
type Condition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Filters
	Filters []*v2.FilterRequest `protobuf:"bytes,1,rep,name=filters,proto3" json:"filters,omitempty"`
	// Next step ID if true
	NextStepIdTrue string `protobuf:"bytes,2,opt,name=next_step_id_true,json=nextStepIdTrue,proto3" json:"next_step_id_true,omitempty"`
	// Next step ID if false
	NextStepIdFalse string `protobuf:"bytes,3,opt,name=next_step_id_false,json=nextStepIdFalse,proto3" json:"next_step_id_false,omitempty"`
	// Type
	Type Condition_Type `protobuf:"varint,4,opt,name=type,proto3,enum=moego.models.automation.v1.Condition_Type" json:"type,omitempty"`
	// Action Filters
	ActionResultFilter ActionResultFilter `protobuf:"varint,5,opt,name=action_result_filter,json=actionResultFilter,proto3,enum=moego.models.automation.v1.ActionResultFilter" json:"action_result_filter,omitempty"`
}

func (x *Condition) Reset() {
	*x = Condition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Condition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Condition) ProtoMessage() {}

func (x *Condition) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Condition.ProtoReflect.Descriptor instead.
func (*Condition) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{6}
}

func (x *Condition) GetFilters() []*v2.FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *Condition) GetNextStepIdTrue() string {
	if x != nil {
		return x.NextStepIdTrue
	}
	return ""
}

func (x *Condition) GetNextStepIdFalse() string {
	if x != nil {
		return x.NextStepIdFalse
	}
	return ""
}

func (x *Condition) GetType() Condition_Type {
	if x != nil {
		return x.Type
	}
	return Condition_TYPE_UNSPECIFIED
}

func (x *Condition) GetActionResultFilter() ActionResultFilter {
	if x != nil {
		return x.ActionResultFilter
	}
	return ActionResultFilter_ACTION_FILTER_UNSPECIFIED
}

// Message
type Message struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Action type
	Type Message_Type `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.automation.v1.Message_Type" json:"type,omitempty"`
	// Action category
	Category Message_Category `protobuf:"varint,2,opt,name=category,proto3,enum=moego.models.automation.v1.Message_Category" json:"category,omitempty"`
	// Action data
	Data *Message_Data `protobuf:"bytes,10,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *Message) Reset() {
	*x = Message{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{7}
}

func (x *Message) GetType() Message_Type {
	if x != nil {
		return x.Type
	}
	return Message_TYPE_UNSPECIFIED
}

func (x *Message) GetCategory() Message_Category {
	if x != nil {
		return x.Category
	}
	return Message_CATEGORY_UNSPECIFIED
}

func (x *Message) GetData() *Message_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

// SMSData
type SMSData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Content
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *SMSData) Reset() {
	*x = SMSData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SMSData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SMSData) ProtoMessage() {}

func (x *SMSData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SMSData.ProtoReflect.Descriptor instead.
func (*SMSData) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{8}
}

func (x *SMSData) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

// EmailData
type EmailData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Content
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	// Title
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
}

func (x *EmailData) Reset() {
	*x = EmailData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailData) ProtoMessage() {}

func (x *EmailData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailData.ProtoReflect.Descriptor instead.
func (*EmailData) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{9}
}

func (x *EmailData) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *EmailData) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

// AdvancedAction
type AdvancedAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// type
	Type AdvancedAction_Type `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.automation.v1.AdvancedAction_Type" json:"type,omitempty"`
	// data
	Data *AdvancedAction_Data `protobuf:"bytes,10,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *AdvancedAction) Reset() {
	*x = AdvancedAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdvancedAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdvancedAction) ProtoMessage() {}

func (x *AdvancedAction) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdvancedAction.ProtoReflect.Descriptor instead.
func (*AdvancedAction) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{10}
}

func (x *AdvancedAction) GetType() AdvancedAction_Type {
	if x != nil {
		return x.Type
	}
	return AdvancedAction_TYPE_UNSPECIFIED
}

func (x *AdvancedAction) GetData() *AdvancedAction_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

// AddClientData
type AddClientTagData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Customer Tag ids
	CustomerTagIds []int64 `protobuf:"varint,1,rep,packed,name=customer_tag_ids,json=customerTagIds,proto3" json:"customer_tag_ids,omitempty"`
}

func (x *AddClientTagData) Reset() {
	*x = AddClientTagData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddClientTagData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddClientTagData) ProtoMessage() {}

func (x *AddClientTagData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddClientTagData.ProtoReflect.Descriptor instead.
func (*AddClientTagData) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{11}
}

func (x *AddClientTagData) GetCustomerTagIds() []int64 {
	if x != nil {
		return x.CustomerTagIds
	}
	return nil
}

// DeleteClientData
type DeleteClientTagData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Customer Tag ids
	CustomerTagIds []int64 `protobuf:"varint,1,rep,packed,name=customer_tag_ids,json=customerTagIds,proto3" json:"customer_tag_ids,omitempty"`
}

func (x *DeleteClientTagData) Reset() {
	*x = DeleteClientTagData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteClientTagData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteClientTagData) ProtoMessage() {}

func (x *DeleteClientTagData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteClientTagData.ProtoReflect.Descriptor instead.
func (*DeleteClientTagData) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteClientTagData) GetCustomerTagIds() []int64 {
	if x != nil {
		return x.CustomerTagIds
	}
	return nil
}

// ChangeClientData
type ChangeClientTagData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Source Customer Tag ids
	SourceCustomerTagIds []int64 `protobuf:"varint,1,rep,packed,name=source_customer_tag_ids,json=sourceCustomerTagIds,proto3" json:"source_customer_tag_ids,omitempty"`
	// Target Customer Tag ids
	TargetCustomerTagIds []int64 `protobuf:"varint,2,rep,packed,name=target_customer_tag_ids,json=targetCustomerTagIds,proto3" json:"target_customer_tag_ids,omitempty"`
}

func (x *ChangeClientTagData) Reset() {
	*x = ChangeClientTagData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeClientTagData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeClientTagData) ProtoMessage() {}

func (x *ChangeClientTagData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeClientTagData.ProtoReflect.Descriptor instead.
func (*ChangeClientTagData) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{13}
}

func (x *ChangeClientTagData) GetSourceCustomerTagIds() []int64 {
	if x != nil {
		return x.SourceCustomerTagIds
	}
	return nil
}

func (x *ChangeClientTagData) GetTargetCustomerTagIds() []int64 {
	if x != nil {
		return x.TargetCustomerTagIds
	}
	return nil
}

// AddPetCodeData
type AddPetCodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pet Code ids
	PetCodeIds []int64 `protobuf:"varint,1,rep,packed,name=pet_code_ids,json=petCodeIds,proto3" json:"pet_code_ids,omitempty"`
}

func (x *AddPetCodeData) Reset() {
	*x = AddPetCodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPetCodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPetCodeData) ProtoMessage() {}

func (x *AddPetCodeData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPetCodeData.ProtoReflect.Descriptor instead.
func (*AddPetCodeData) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{14}
}

func (x *AddPetCodeData) GetPetCodeIds() []int64 {
	if x != nil {
		return x.PetCodeIds
	}
	return nil
}

// ChangeCustomerLifeCycle
type ChangeCustomerLifeCycleData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Life Cycle, definition on https://github.com/MoeGolibrary/moego/blob/main/backend/proto/customer/v1/customer_models.proto
	LifeCycle int32 `protobuf:"varint,1,opt,name=life_cycle,json=lifeCycle,proto3" json:"life_cycle,omitempty"`
}

func (x *ChangeCustomerLifeCycleData) Reset() {
	*x = ChangeCustomerLifeCycleData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeCustomerLifeCycleData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeCustomerLifeCycleData) ProtoMessage() {}

func (x *ChangeCustomerLifeCycleData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeCustomerLifeCycleData.ProtoReflect.Descriptor instead.
func (*ChangeCustomerLifeCycleData) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{15}
}

func (x *ChangeCustomerLifeCycleData) GetLifeCycle() int32 {
	if x != nil {
		return x.LifeCycle
	}
	return 0
}

// ChangeCustomerActionStatus
type ChangeCustomerActionStatusData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Action Status, definition on https://github.com/MoeGolibrary/moego/blob/main/backend/proto/customer/v1/customer_models.proto
	ActionStatus int32 `protobuf:"varint,1,opt,name=action_status,json=actionStatus,proto3" json:"action_status,omitempty"`
}

func (x *ChangeCustomerActionStatusData) Reset() {
	*x = ChangeCustomerActionStatusData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeCustomerActionStatusData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeCustomerActionStatusData) ProtoMessage() {}

func (x *ChangeCustomerActionStatusData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeCustomerActionStatusData.ProtoReflect.Descriptor instead.
func (*ChangeCustomerActionStatusData) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{16}
}

func (x *ChangeCustomerActionStatusData) GetActionStatus() int32 {
	if x != nil {
		return x.ActionStatus
	}
	return 0
}

// AddCustomerTaskData
type AddCustomerTaskData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 任务名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 分配员工
	AllocateStaffId *int64 `protobuf:"varint,2,opt,name=allocate_staff_id,json=allocateStaffId,proto3,oneof" json:"allocate_staff_id,omitempty"`
}

func (x *AddCustomerTaskData) Reset() {
	*x = AddCustomerTaskData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddCustomerTaskData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCustomerTaskData) ProtoMessage() {}

func (x *AddCustomerTaskData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCustomerTaskData.ProtoReflect.Descriptor instead.
func (*AddCustomerTaskData) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{17}
}

func (x *AddCustomerTaskData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddCustomerTaskData) GetAllocateStaffId() int64 {
	if x != nil && x.AllocateStaffId != nil {
		return *x.AllocateStaffId
	}
	return 0
}

// Branch
type Branch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Branch) Reset() {
	*x = Branch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Branch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Branch) ProtoMessage() {}

func (x *Branch) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Branch.ProtoReflect.Descriptor instead.
func (*Branch) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{18}
}

// Data
type Step_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Step data
	//
	// Types that are assignable to Data:
	//
	//	*Step_Data_Trigger
	//	*Step_Data_Wait
	//	*Step_Data_Message
	//	*Step_Data_Condition
	//	*Step_Data_AdvancedAction
	Data isStep_Data_Data `protobuf_oneof:"data"`
}

func (x *Step_Data) Reset() {
	*x = Step_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Step_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Step_Data) ProtoMessage() {}

func (x *Step_Data) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Step_Data.ProtoReflect.Descriptor instead.
func (*Step_Data) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{0, 0}
}

func (m *Step_Data) GetData() isStep_Data_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *Step_Data) GetTrigger() *Trigger {
	if x, ok := x.GetData().(*Step_Data_Trigger); ok {
		return x.Trigger
	}
	return nil
}

func (x *Step_Data) GetWait() *Wait {
	if x, ok := x.GetData().(*Step_Data_Wait); ok {
		return x.Wait
	}
	return nil
}

func (x *Step_Data) GetMessage() *Message {
	if x, ok := x.GetData().(*Step_Data_Message); ok {
		return x.Message
	}
	return nil
}

func (x *Step_Data) GetCondition() *Condition {
	if x, ok := x.GetData().(*Step_Data_Condition); ok {
		return x.Condition
	}
	return nil
}

func (x *Step_Data) GetAdvancedAction() *AdvancedAction {
	if x, ok := x.GetData().(*Step_Data_AdvancedAction); ok {
		return x.AdvancedAction
	}
	return nil
}

type isStep_Data_Data interface {
	isStep_Data_Data()
}

type Step_Data_Trigger struct {
	// Trigger
	Trigger *Trigger `protobuf:"bytes,1,opt,name=trigger,proto3,oneof"`
}

type Step_Data_Wait struct {
	// Wait
	Wait *Wait `protobuf:"bytes,2,opt,name=wait,proto3,oneof"`
}

type Step_Data_Message struct {
	// Message
	Message *Message `protobuf:"bytes,3,opt,name=message,proto3,oneof"`
}

type Step_Data_Condition struct {
	// Condition
	Condition *Condition `protobuf:"bytes,4,opt,name=condition,proto3,oneof"`
}

type Step_Data_AdvancedAction struct {
	// Advanced action
	AdvancedAction *AdvancedAction `protobuf:"bytes,5,opt,name=advanced_action,json=advancedAction,proto3,oneof"`
}

func (*Step_Data_Trigger) isStep_Data_Data() {}

func (*Step_Data_Wait) isStep_Data_Data() {}

func (*Step_Data_Message) isStep_Data_Data() {}

func (*Step_Data_Condition) isStep_Data_Data() {}

func (*Step_Data_AdvancedAction) isStep_Data_Data() {}

// Preview Data
type Step_PreviewData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// preview description
	Description string `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *Step_PreviewData) Reset() {
	*x = Step_PreviewData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Step_PreviewData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Step_PreviewData) ProtoMessage() {}

func (x *Step_PreviewData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Step_PreviewData.ProtoReflect.Descriptor instead.
func (*Step_PreviewData) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Step_PreviewData) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// Data
type Trigger_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Trigger data
	//
	// Types that are assignable to Data:
	//
	//	*Trigger_Data_Scheduled
	//	*Trigger_Data_Event
	Data isTrigger_Data_Data `protobuf_oneof:"data"`
}

func (x *Trigger_Data) Reset() {
	*x = Trigger_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Trigger_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Trigger_Data) ProtoMessage() {}

func (x *Trigger_Data) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Trigger_Data.ProtoReflect.Descriptor instead.
func (*Trigger_Data) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{1, 0}
}

func (m *Trigger_Data) GetData() isTrigger_Data_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *Trigger_Data) GetScheduled() *Scheduled {
	if x, ok := x.GetData().(*Trigger_Data_Scheduled); ok {
		return x.Scheduled
	}
	return nil
}

func (x *Trigger_Data) GetEvent() *Event {
	if x, ok := x.GetData().(*Trigger_Data_Event); ok {
		return x.Event
	}
	return nil
}

type isTrigger_Data_Data interface {
	isTrigger_Data_Data()
}

type Trigger_Data_Scheduled struct {
	// Scheduled
	Scheduled *Scheduled `protobuf:"bytes,1,opt,name=scheduled,proto3,oneof"`
}

type Trigger_Data_Event struct {
	// Event
	Event *Event `protobuf:"bytes,2,opt,name=event,proto3,oneof"`
}

func (*Trigger_Data_Scheduled) isTrigger_Data_Data() {}

func (*Trigger_Data_Event) isTrigger_Data_Data() {}

// Data
type Message_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Action data
	//
	// Types that are assignable to Data:
	//
	//	*Message_Data_SmsData
	//	*Message_Data_EmailData
	Data isMessage_Data_Data `protobuf_oneof:"data"`
}

func (x *Message_Data) Reset() {
	*x = Message_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Message_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message_Data) ProtoMessage() {}

func (x *Message_Data) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message_Data.ProtoReflect.Descriptor instead.
func (*Message_Data) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{7, 0}
}

func (m *Message_Data) GetData() isMessage_Data_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *Message_Data) GetSmsData() *SMSData {
	if x, ok := x.GetData().(*Message_Data_SmsData); ok {
		return x.SmsData
	}
	return nil
}

func (x *Message_Data) GetEmailData() *EmailData {
	if x, ok := x.GetData().(*Message_Data_EmailData); ok {
		return x.EmailData
	}
	return nil
}

type isMessage_Data_Data interface {
	isMessage_Data_Data()
}

type Message_Data_SmsData struct {
	// SMS data
	SmsData *SMSData `protobuf:"bytes,1,opt,name=sms_data,json=smsData,proto3,oneof"`
}

type Message_Data_EmailData struct {
	// Email data
	EmailData *EmailData `protobuf:"bytes,2,opt,name=email_data,json=emailData,proto3,oneof"`
}

func (*Message_Data_SmsData) isMessage_Data_Data() {}

func (*Message_Data_EmailData) isMessage_Data_Data() {}

// Data
type AdvancedAction_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// AdvancedAction data
	//
	// Types that are assignable to Data:
	//
	//	*AdvancedAction_Data_AddClientTagData
	//	*AdvancedAction_Data_DeleteClientTagData
	//	*AdvancedAction_Data_ChangeClientTagData
	//	*AdvancedAction_Data_AddPetCodeData
	//	*AdvancedAction_Data_ChangeCustomerLifeCycleData
	//	*AdvancedAction_Data_ChangeCustomerActionStatusData
	//	*AdvancedAction_Data_AddCustomerTaskData
	Data isAdvancedAction_Data_Data `protobuf_oneof:"data"`
}

func (x *AdvancedAction_Data) Reset() {
	*x = AdvancedAction_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_step_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdvancedAction_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdvancedAction_Data) ProtoMessage() {}

func (x *AdvancedAction_Data) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_step_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdvancedAction_Data.ProtoReflect.Descriptor instead.
func (*AdvancedAction_Data) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_step_proto_rawDescGZIP(), []int{10, 0}
}

func (m *AdvancedAction_Data) GetData() isAdvancedAction_Data_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *AdvancedAction_Data) GetAddClientTagData() *AddClientTagData {
	if x, ok := x.GetData().(*AdvancedAction_Data_AddClientTagData); ok {
		return x.AddClientTagData
	}
	return nil
}

func (x *AdvancedAction_Data) GetDeleteClientTagData() *DeleteClientTagData {
	if x, ok := x.GetData().(*AdvancedAction_Data_DeleteClientTagData); ok {
		return x.DeleteClientTagData
	}
	return nil
}

func (x *AdvancedAction_Data) GetChangeClientTagData() *ChangeClientTagData {
	if x, ok := x.GetData().(*AdvancedAction_Data_ChangeClientTagData); ok {
		return x.ChangeClientTagData
	}
	return nil
}

func (x *AdvancedAction_Data) GetAddPetCodeData() *AddPetCodeData {
	if x, ok := x.GetData().(*AdvancedAction_Data_AddPetCodeData); ok {
		return x.AddPetCodeData
	}
	return nil
}

func (x *AdvancedAction_Data) GetChangeCustomerLifeCycleData() *ChangeCustomerLifeCycleData {
	if x, ok := x.GetData().(*AdvancedAction_Data_ChangeCustomerLifeCycleData); ok {
		return x.ChangeCustomerLifeCycleData
	}
	return nil
}

func (x *AdvancedAction_Data) GetChangeCustomerActionStatusData() *ChangeCustomerActionStatusData {
	if x, ok := x.GetData().(*AdvancedAction_Data_ChangeCustomerActionStatusData); ok {
		return x.ChangeCustomerActionStatusData
	}
	return nil
}

func (x *AdvancedAction_Data) GetAddCustomerTaskData() *AddCustomerTaskData {
	if x, ok := x.GetData().(*AdvancedAction_Data_AddCustomerTaskData); ok {
		return x.AddCustomerTaskData
	}
	return nil
}

type isAdvancedAction_Data_Data interface {
	isAdvancedAction_Data_Data()
}

type AdvancedAction_Data_AddClientTagData struct {
	// Add Client Tag
	AddClientTagData *AddClientTagData `protobuf:"bytes,1,opt,name=add_client_tag_data,json=addClientTagData,proto3,oneof"`
}

type AdvancedAction_Data_DeleteClientTagData struct {
	// Delete Client Tag
	DeleteClientTagData *DeleteClientTagData `protobuf:"bytes,2,opt,name=delete_client_tag_data,json=deleteClientTagData,proto3,oneof"`
}

type AdvancedAction_Data_ChangeClientTagData struct {
	// Change Client Tag
	ChangeClientTagData *ChangeClientTagData `protobuf:"bytes,3,opt,name=change_client_tag_data,json=changeClientTagData,proto3,oneof"`
}

type AdvancedAction_Data_AddPetCodeData struct {
	// Add Pet Code
	AddPetCodeData *AddPetCodeData `protobuf:"bytes,4,opt,name=add_pet_code_data,json=addPetCodeData,proto3,oneof"`
}

type AdvancedAction_Data_ChangeCustomerLifeCycleData struct {
	// Change Customer Life Cycle
	ChangeCustomerLifeCycleData *ChangeCustomerLifeCycleData `protobuf:"bytes,5,opt,name=change_customer_life_cycle_data,json=changeCustomerLifeCycleData,proto3,oneof"`
}

type AdvancedAction_Data_ChangeCustomerActionStatusData struct {
	// Change Customer Action Status
	ChangeCustomerActionStatusData *ChangeCustomerActionStatusData `protobuf:"bytes,6,opt,name=change_customer_action_status_data,json=changeCustomerActionStatusData,proto3,oneof"`
}

type AdvancedAction_Data_AddCustomerTaskData struct {
	// Add Customer Task
	AddCustomerTaskData *AddCustomerTaskData `protobuf:"bytes,7,opt,name=add_customer_task_data,json=addCustomerTaskData,proto3,oneof"`
}

func (*AdvancedAction_Data_AddClientTagData) isAdvancedAction_Data_Data() {}

func (*AdvancedAction_Data_DeleteClientTagData) isAdvancedAction_Data_Data() {}

func (*AdvancedAction_Data_ChangeClientTagData) isAdvancedAction_Data_Data() {}

func (*AdvancedAction_Data_AddPetCodeData) isAdvancedAction_Data_Data() {}

func (*AdvancedAction_Data_ChangeCustomerLifeCycleData) isAdvancedAction_Data_Data() {}

func (*AdvancedAction_Data_ChangeCustomerActionStatusData) isAdvancedAction_Data_Data() {}

func (*AdvancedAction_Data_AddCustomerTaskData) isAdvancedAction_Data_Data() {}

var File_moego_models_automation_v1_step_proto protoreflect.FileDescriptor

var file_moego_models_automation_v1_step_proto_rawDesc = []byte{
	0x0a, 0x25, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x65,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x79, 0x6f, 0x66, 0x77, 0x65,
	0x65, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x66, 0x64, 0x61, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb7, 0x07, 0x0a,
	0x04, 0x53, 0x74, 0x65, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x69,
	0x6c, 0x64, 0x72, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x2b, 0x0a, 0x11,
	0x68, 0x69, 0x65, 0x72, 0x61, 0x72, 0x63, 0x68, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x68, 0x69, 0x65, 0x72, 0x61, 0x72, 0x63,
	0x68, 0x69, 0x63, 0x61, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x65, 0x70, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x54, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44,
	0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x61,
	0x74, 0x61, 0x88, 0x01, 0x01, 0x1a, 0xe6, 0x02, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3f,
	0x0a, 0x07, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x48, 0x00, 0x52, 0x07, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x12,
	0x36, 0x0a, 0x04, 0x77, 0x61, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74,
	0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x69, 0x74, 0x48,
	0x00, 0x52, 0x04, 0x77, 0x61, 0x69, 0x74, 0x12, 0x3f, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x45, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x55, 0x0a, 0x0f, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0e, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x2f,
	0x0a, 0x0b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x61, 0x74, 0x61, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x64, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x54, 0x52, 0x49, 0x47, 0x47, 0x45, 0x52, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x57, 0x41,
	0x49, 0x54, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10,
	0x03, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04,
	0x12, 0x13, 0x0a, 0x0f, 0x41, 0x44, 0x56, 0x41, 0x4e, 0x43, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x05, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x22, 0xb4, 0x03, 0x0a, 0x07, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x12, 0x3c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x3c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x42,
	0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x1a, 0x90, 0x01, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x45, 0x0a, 0x09, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x48, 0x00, 0x52, 0x09, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x12, 0x39, 0x0a, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x05, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x42, 0x0a, 0x04, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x43, 0x48, 0x45, 0x44,
	0x55, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x10,
	0x02, 0x12, 0x0a, 0x0a, 0x06, 0x42, 0x45, 0x46, 0x4f, 0x52, 0x45, 0x10, 0x03, 0x22, 0xbd, 0x02,
	0x0a, 0x09, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x12, 0x36, 0x0a, 0x0b, 0x64,
	0x61, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x09, 0x64, 0x61, 0x79, 0x4f, 0x66, 0x57,
	0x65, 0x65, 0x6b, 0x12, 0x36, 0x0a, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x64,
	0x61, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x66, 0x44, 0x61, 0x79,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x4f, 0x66, 0x44, 0x61, 0x79, 0x12, 0x25, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x4d, 0x0a, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x2e, 0x46, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x22, 0x4a, 0x0a, 0x09, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x19,
	0x0a, 0x15, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x44, 0x41, 0x49,
	0x4c, 0x59, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x57, 0x45, 0x45, 0x4b, 0x4c, 0x59, 0x10, 0x02,
	0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x4c, 0x59, 0x10, 0x03, 0x22, 0xb8, 0x06,
	0x0a, 0x05, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x46, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x49, 0x0a, 0x07, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x52, 0x07, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0x92,
	0x01, 0x0a, 0x08, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x14, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f,
	0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x53, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x50,
	0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x49,
	0x4e, 0x54, 0x41, 0x4b, 0x45, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07,
	0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x45, 0x4d,
	0x42, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x49, 0x53,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x06, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x4c, 0x49, 0x45, 0x4e,
	0x54, 0x10, 0x07, 0x22, 0xc2, 0x03, 0x0a, 0x0d, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a,
	0x19, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x53,
	0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x44, 0x10, 0x64, 0x12, 0x1d, 0x0a, 0x19,
	0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x53, 0x5f,
	0x41, 0x42, 0x41, 0x4e, 0x44, 0x4f, 0x4e, 0x45, 0x44, 0x10, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x4f,
	0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41,
	0x43, 0x43, 0x45, 0x50, 0x54, 0x45, 0x44, 0x10, 0x66, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x56, 0x41,
	0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f,
	0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x44, 0x10, 0x67, 0x12, 0x18, 0x0a, 0x13, 0x41,
	0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x44, 0x10, 0xc8, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0xc9, 0x01,
	0x12, 0x19, 0x0a, 0x14, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0xca, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x41,
	0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x59,
	0x5f, 0x50, 0x41, 0x49, 0x44, 0x10, 0xcb, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x45, 0x56, 0x41, 0x4c,
	0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10,
	0xcc, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x5f, 0x50, 0x55,
	0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x44, 0x10, 0x90, 0x03, 0x12, 0x15, 0x0a, 0x10, 0x50, 0x41,
	0x43, 0x4b, 0x41, 0x47, 0x45, 0x5f, 0x52, 0x45, 0x44, 0x45, 0x45, 0x4d, 0x45, 0x44, 0x10, 0x91,
	0x03, 0x12, 0x19, 0x0a, 0x14, 0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x5f,
	0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x44, 0x10, 0xf4, 0x03, 0x12, 0x18, 0x0a, 0x13,
	0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45,
	0x4c, 0x45, 0x44, 0x10, 0xf5, 0x03, 0x12, 0x1b, 0x0a, 0x16, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x45, 0x44, 0x45, 0x45, 0x4d, 0x45, 0x44,
	0x10, 0xd8, 0x04, 0x12, 0x13, 0x0a, 0x0e, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0xbc, 0x05, 0x22, 0x08, 0x0a, 0x06, 0x42, 0x65, 0x66, 0x6f,
	0x72, 0x65, 0x22, 0xbc, 0x01, 0x0a, 0x04, 0x57, 0x61, 0x69, 0x74, 0x12, 0x39, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x69, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x4d, 0x0a, 0x0d, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74,
	0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x2a, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x01, 0x22, 0x86, 0x03, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x42, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x12, 0x29, 0x0a, 0x11, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x74, 0x65, 0x70,
	0x5f, 0x69, 0x64, 0x5f, 0x74, 0x72, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x6e, 0x65, 0x78, 0x74, 0x53, 0x74, 0x65, 0x70, 0x49, 0x64, 0x54, 0x72, 0x75, 0x65, 0x12, 0x2b,
	0x0a, 0x12, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x69, 0x64, 0x5f, 0x66,
	0x61, 0x6c, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6e, 0x65, 0x78, 0x74,
	0x53, 0x74, 0x65, 0x70, 0x49, 0x64, 0x46, 0x61, 0x6c, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x60, 0x0a, 0x14, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x12, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x3b, 0x0a,
	0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x46,
	0x49, 0x4c, 0x54, 0x45, 0x52, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x46, 0x49, 0x4c, 0x54, 0x45, 0x52, 0x10, 0x02, 0x22, 0xe2, 0x03, 0x0a, 0x07, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x3c,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x98, 0x01, 0x0a,
	0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x08, 0x73, 0x6d, 0x73, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4d, 0x53, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x07,
	0x73, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x46, 0x0a, 0x0a, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x61,
	0x74, 0x61, 0x48, 0x00, 0x52, 0x09, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x42,
	0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x30, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x53, 0x4d, 0x53, 0x10, 0x01, 0x12, 0x09,
	0x0a, 0x05, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x22, 0x44, 0x0a, 0x08, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x10, 0x0a, 0x0c, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x01, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e, 0x10, 0x02, 0x22,
	0x23, 0x0a, 0x07, 0x53, 0x4d, 0x53, 0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x22, 0x3b, 0x0a, 0x09, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x22, 0xf4, 0x08, 0x0a, 0x0e, 0x41, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x8a,
	0x06, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x5d, 0x0a, 0x13, 0x61, 0x64, 0x64, 0x5f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x44, 0x61,
	0x74, 0x61, 0x48, 0x00, 0x52, 0x10, 0x61, 0x64, 0x64, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54,
	0x61, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x66, 0x0a, 0x16, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x54, 0x61, 0x67, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x13, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x66,
	0x0a, 0x16, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x74, 0x61, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x44, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x13, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54,
	0x61, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x57, 0x0a, 0x11, 0x61, 0x64, 0x64, 0x5f, 0x70, 0x65,
	0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x64, 0x64, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52,
	0x0e, 0x61, 0x64, 0x64, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x7f, 0x0a, 0x1f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x6c, 0x69, 0x66, 0x65, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x4c, 0x69, 0x66, 0x65, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x1b, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x4c, 0x69, 0x66, 0x65, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x88, 0x01, 0x0a, 0x22, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74,
	0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1e, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x66, 0x0a, 0x16, 0x61,
	0x64, 0x64, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x13,
	0x61, 0x64, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x44,
	0x61, 0x74, 0x61, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xca, 0x01, 0x0a, 0x04,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x44,
	0x44, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x41, 0x47, 0x10, 0x01, 0x12, 0x15,
	0x0a, 0x11, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f,
	0x54, 0x41, 0x47, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f,
	0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x41, 0x47, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c,
	0x41, 0x44, 0x44, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x04, 0x12, 0x1e,
	0x0a, 0x1a, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45,
	0x52, 0x5f, 0x4c, 0x49, 0x46, 0x45, 0x5f, 0x43, 0x59, 0x43, 0x4c, 0x45, 0x10, 0x05, 0x12, 0x21,
	0x0a, 0x1d, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45,
	0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10,
	0x06, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x44, 0x44, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45,
	0x52, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x10, 0x07, 0x22, 0x3c, 0x0a, 0x10, 0x41, 0x64, 0x64, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x10,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x54, 0x61, 0x67, 0x49, 0x64, 0x73, 0x22, 0x3f, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a,
	0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x54, 0x61, 0x67, 0x49, 0x64, 0x73, 0x22, 0x83, 0x01, 0x0a, 0x13, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x35, 0x0a, 0x17, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x14, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x54, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x35, 0x0a, 0x17, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x14, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x49, 0x64, 0x73, 0x22, 0x32, 0x0a,
	0x0e, 0x41, 0x64, 0x64, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x20, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64,
	0x73, 0x22, 0x3c, 0x0a, 0x1b, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x4c, 0x69, 0x66, 0x65, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x69, 0x66, 0x65, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6c, 0x69, 0x66, 0x65, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x22,
	0x45, 0x0a, 0x1e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x70, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x2f, 0x0a, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0f,
	0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x22, 0x08, 0x0a, 0x06, 0x42, 0x72, 0x61, 0x6e,
	0x63, 0x68, 0x2a, 0x53, 0x0a, 0x12, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x4c, 0x54, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x49, 0x4e, 0x54, 0x41, 0x4b,
	0x45, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x5f, 0x53, 0x55,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x3b, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_automation_v1_step_proto_rawDescOnce sync.Once
	file_moego_models_automation_v1_step_proto_rawDescData = file_moego_models_automation_v1_step_proto_rawDesc
)

func file_moego_models_automation_v1_step_proto_rawDescGZIP() []byte {
	file_moego_models_automation_v1_step_proto_rawDescOnce.Do(func() {
		file_moego_models_automation_v1_step_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_automation_v1_step_proto_rawDescData)
	})
	return file_moego_models_automation_v1_step_proto_rawDescData
}

var file_moego_models_automation_v1_step_proto_enumTypes = make([]protoimpl.EnumInfo, 11)
var file_moego_models_automation_v1_step_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_moego_models_automation_v1_step_proto_goTypes = []interface{}{
	(ActionResultFilter)(0),                // 0: moego.models.automation.v1.ActionResultFilter
	(Step_Type)(0),                         // 1: moego.models.automation.v1.Step.Type
	(Trigger_Type)(0),                      // 2: moego.models.automation.v1.Trigger.Type
	(Scheduled_Frequency)(0),               // 3: moego.models.automation.v1.Scheduled.Frequency
	(Event_Category)(0),                    // 4: moego.models.automation.v1.Event.Category
	(Event_EntityTrigger)(0),               // 5: moego.models.automation.v1.Event.EntityTrigger
	(Wait_Type)(0),                         // 6: moego.models.automation.v1.Wait.Type
	(Condition_Type)(0),                    // 7: moego.models.automation.v1.Condition.Type
	(Message_Type)(0),                      // 8: moego.models.automation.v1.Message.Type
	(Message_Category)(0),                  // 9: moego.models.automation.v1.Message.Category
	(AdvancedAction_Type)(0),               // 10: moego.models.automation.v1.AdvancedAction.Type
	(*Step)(nil),                           // 11: moego.models.automation.v1.Step
	(*Trigger)(nil),                        // 12: moego.models.automation.v1.Trigger
	(*Scheduled)(nil),                      // 13: moego.models.automation.v1.Scheduled
	(*Event)(nil),                          // 14: moego.models.automation.v1.Event
	(*Before)(nil),                         // 15: moego.models.automation.v1.Before
	(*Wait)(nil),                           // 16: moego.models.automation.v1.Wait
	(*Condition)(nil),                      // 17: moego.models.automation.v1.Condition
	(*Message)(nil),                        // 18: moego.models.automation.v1.Message
	(*SMSData)(nil),                        // 19: moego.models.automation.v1.SMSData
	(*EmailData)(nil),                      // 20: moego.models.automation.v1.EmailData
	(*AdvancedAction)(nil),                 // 21: moego.models.automation.v1.AdvancedAction
	(*AddClientTagData)(nil),               // 22: moego.models.automation.v1.AddClientTagData
	(*DeleteClientTagData)(nil),            // 23: moego.models.automation.v1.DeleteClientTagData
	(*ChangeClientTagData)(nil),            // 24: moego.models.automation.v1.ChangeClientTagData
	(*AddPetCodeData)(nil),                 // 25: moego.models.automation.v1.AddPetCodeData
	(*ChangeCustomerLifeCycleData)(nil),    // 26: moego.models.automation.v1.ChangeCustomerLifeCycleData
	(*ChangeCustomerActionStatusData)(nil), // 27: moego.models.automation.v1.ChangeCustomerActionStatusData
	(*AddCustomerTaskData)(nil),            // 28: moego.models.automation.v1.AddCustomerTaskData
	(*Branch)(nil),                         // 29: moego.models.automation.v1.Branch
	(*Step_Data)(nil),                      // 30: moego.models.automation.v1.Step.Data
	(*Step_PreviewData)(nil),               // 31: moego.models.automation.v1.Step.PreviewData
	(*Trigger_Data)(nil),                   // 32: moego.models.automation.v1.Trigger.Data
	(*Message_Data)(nil),                   // 33: moego.models.automation.v1.Message.Data
	(*AdvancedAction_Data)(nil),            // 34: moego.models.automation.v1.AdvancedAction.Data
	(*v2.FilterRequest)(nil),               // 35: moego.models.reporting.v2.FilterRequest
	(dayofweek.DayOfWeek)(0),               // 36: google.type.DayOfWeek
	(*timeofday.TimeOfDay)(nil),            // 37: google.type.TimeOfDay
	(*date.Date)(nil),                      // 38: google.type.Date
	(*TimeDuration)(nil),                   // 39: moego.models.automation.v1.TimeDuration
}
var file_moego_models_automation_v1_step_proto_depIdxs = []int32{
	1,  // 0: moego.models.automation.v1.Step.type:type_name -> moego.models.automation.v1.Step.Type
	30, // 1: moego.models.automation.v1.Step.data:type_name -> moego.models.automation.v1.Step.Data
	31, // 2: moego.models.automation.v1.Step.preview_data:type_name -> moego.models.automation.v1.Step.PreviewData
	2,  // 3: moego.models.automation.v1.Trigger.type:type_name -> moego.models.automation.v1.Trigger.Type
	32, // 4: moego.models.automation.v1.Trigger.data:type_name -> moego.models.automation.v1.Trigger.Data
	35, // 5: moego.models.automation.v1.Trigger.filters:type_name -> moego.models.reporting.v2.FilterRequest
	36, // 6: moego.models.automation.v1.Scheduled.day_of_week:type_name -> google.type.DayOfWeek
	37, // 7: moego.models.automation.v1.Scheduled.time_of_day:type_name -> google.type.TimeOfDay
	38, // 8: moego.models.automation.v1.Scheduled.date:type_name -> google.type.Date
	3,  // 9: moego.models.automation.v1.Scheduled.frequency:type_name -> moego.models.automation.v1.Scheduled.Frequency
	4,  // 10: moego.models.automation.v1.Event.category:type_name -> moego.models.automation.v1.Event.Category
	5,  // 11: moego.models.automation.v1.Event.trigger:type_name -> moego.models.automation.v1.Event.EntityTrigger
	35, // 12: moego.models.automation.v1.Event.filters:type_name -> moego.models.reporting.v2.FilterRequest
	6,  // 13: moego.models.automation.v1.Wait.type:type_name -> moego.models.automation.v1.Wait.Type
	39, // 14: moego.models.automation.v1.Wait.time_duration:type_name -> moego.models.automation.v1.TimeDuration
	35, // 15: moego.models.automation.v1.Condition.filters:type_name -> moego.models.reporting.v2.FilterRequest
	7,  // 16: moego.models.automation.v1.Condition.type:type_name -> moego.models.automation.v1.Condition.Type
	0,  // 17: moego.models.automation.v1.Condition.action_result_filter:type_name -> moego.models.automation.v1.ActionResultFilter
	8,  // 18: moego.models.automation.v1.Message.type:type_name -> moego.models.automation.v1.Message.Type
	9,  // 19: moego.models.automation.v1.Message.category:type_name -> moego.models.automation.v1.Message.Category
	33, // 20: moego.models.automation.v1.Message.data:type_name -> moego.models.automation.v1.Message.Data
	10, // 21: moego.models.automation.v1.AdvancedAction.type:type_name -> moego.models.automation.v1.AdvancedAction.Type
	34, // 22: moego.models.automation.v1.AdvancedAction.data:type_name -> moego.models.automation.v1.AdvancedAction.Data
	12, // 23: moego.models.automation.v1.Step.Data.trigger:type_name -> moego.models.automation.v1.Trigger
	16, // 24: moego.models.automation.v1.Step.Data.wait:type_name -> moego.models.automation.v1.Wait
	18, // 25: moego.models.automation.v1.Step.Data.message:type_name -> moego.models.automation.v1.Message
	17, // 26: moego.models.automation.v1.Step.Data.condition:type_name -> moego.models.automation.v1.Condition
	21, // 27: moego.models.automation.v1.Step.Data.advanced_action:type_name -> moego.models.automation.v1.AdvancedAction
	13, // 28: moego.models.automation.v1.Trigger.Data.scheduled:type_name -> moego.models.automation.v1.Scheduled
	14, // 29: moego.models.automation.v1.Trigger.Data.event:type_name -> moego.models.automation.v1.Event
	19, // 30: moego.models.automation.v1.Message.Data.sms_data:type_name -> moego.models.automation.v1.SMSData
	20, // 31: moego.models.automation.v1.Message.Data.email_data:type_name -> moego.models.automation.v1.EmailData
	22, // 32: moego.models.automation.v1.AdvancedAction.Data.add_client_tag_data:type_name -> moego.models.automation.v1.AddClientTagData
	23, // 33: moego.models.automation.v1.AdvancedAction.Data.delete_client_tag_data:type_name -> moego.models.automation.v1.DeleteClientTagData
	24, // 34: moego.models.automation.v1.AdvancedAction.Data.change_client_tag_data:type_name -> moego.models.automation.v1.ChangeClientTagData
	25, // 35: moego.models.automation.v1.AdvancedAction.Data.add_pet_code_data:type_name -> moego.models.automation.v1.AddPetCodeData
	26, // 36: moego.models.automation.v1.AdvancedAction.Data.change_customer_life_cycle_data:type_name -> moego.models.automation.v1.ChangeCustomerLifeCycleData
	27, // 37: moego.models.automation.v1.AdvancedAction.Data.change_customer_action_status_data:type_name -> moego.models.automation.v1.ChangeCustomerActionStatusData
	28, // 38: moego.models.automation.v1.AdvancedAction.Data.add_customer_task_data:type_name -> moego.models.automation.v1.AddCustomerTaskData
	39, // [39:39] is the sub-list for method output_type
	39, // [39:39] is the sub-list for method input_type
	39, // [39:39] is the sub-list for extension type_name
	39, // [39:39] is the sub-list for extension extendee
	0,  // [0:39] is the sub-list for field type_name
}

func init() { file_moego_models_automation_v1_step_proto_init() }
func file_moego_models_automation_v1_step_proto_init() {
	if File_moego_models_automation_v1_step_proto != nil {
		return
	}
	file_moego_models_automation_v1_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_automation_v1_step_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Step); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Trigger); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Scheduled); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Event); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Before); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Wait); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Condition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Message); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SMSData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmailData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdvancedAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddClientTagData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteClientTagData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeClientTagData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPetCodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeCustomerLifeCycleData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeCustomerActionStatusData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddCustomerTaskData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Branch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Step_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Step_PreviewData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Trigger_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Message_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_step_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdvancedAction_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_automation_v1_step_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_automation_v1_step_proto_msgTypes[17].OneofWrappers = []interface{}{}
	file_moego_models_automation_v1_step_proto_msgTypes[19].OneofWrappers = []interface{}{
		(*Step_Data_Trigger)(nil),
		(*Step_Data_Wait)(nil),
		(*Step_Data_Message)(nil),
		(*Step_Data_Condition)(nil),
		(*Step_Data_AdvancedAction)(nil),
	}
	file_moego_models_automation_v1_step_proto_msgTypes[21].OneofWrappers = []interface{}{
		(*Trigger_Data_Scheduled)(nil),
		(*Trigger_Data_Event)(nil),
	}
	file_moego_models_automation_v1_step_proto_msgTypes[22].OneofWrappers = []interface{}{
		(*Message_Data_SmsData)(nil),
		(*Message_Data_EmailData)(nil),
	}
	file_moego_models_automation_v1_step_proto_msgTypes[23].OneofWrappers = []interface{}{
		(*AdvancedAction_Data_AddClientTagData)(nil),
		(*AdvancedAction_Data_DeleteClientTagData)(nil),
		(*AdvancedAction_Data_ChangeClientTagData)(nil),
		(*AdvancedAction_Data_AddPetCodeData)(nil),
		(*AdvancedAction_Data_ChangeCustomerLifeCycleData)(nil),
		(*AdvancedAction_Data_ChangeCustomerActionStatusData)(nil),
		(*AdvancedAction_Data_AddCustomerTaskData)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_automation_v1_step_proto_rawDesc,
			NumEnums:      11,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_automation_v1_step_proto_goTypes,
		DependencyIndexes: file_moego_models_automation_v1_step_proto_depIdxs,
		EnumInfos:         file_moego_models_automation_v1_step_proto_enumTypes,
		MessageInfos:      file_moego_models_automation_v1_step_proto_msgTypes,
	}.Build()
	File_moego_models_automation_v1_step_proto = out.File
	file_moego_models_automation_v1_step_proto_rawDesc = nil
	file_moego_models_automation_v1_step_proto_goTypes = nil
	file_moego_models_automation_v1_step_proto_depIdxs = nil
}
