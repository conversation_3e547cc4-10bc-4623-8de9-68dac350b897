// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/automation/v1/workflow.proto

package automationpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Status
type Workflow_Status int32

const (
	// STATUS_UNSPECIFIED
	Workflow_STATUS_UNSPECIFIED Workflow_Status = 0
	// DRAFT
	Workflow_DRAFT Workflow_Status = 1
	// ACTIVE
	Workflow_ACTIVE Workflow_Status = 2
	// INACTIVE
	Workflow_INACTIVE Workflow_Status = 3
	// DELETED
	Workflow_DELETED Workflow_Status = 4
)

// Enum value maps for Workflow_Status.
var (
	Workflow_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "DRAFT",
		2: "ACTIVE",
		3: "INACTIVE",
		4: "DELETED",
	}
	Workflow_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"DRAFT":              1,
		"ACTIVE":             2,
		"INACTIVE":           3,
		"DELETED":            4,
	}
)

func (x Workflow_Status) Enum() *Workflow_Status {
	p := new(Workflow_Status)
	*p = x
	return p
}

func (x Workflow_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Workflow_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_workflow_proto_enumTypes[0].Descriptor()
}

func (Workflow_Status) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_workflow_proto_enumTypes[0]
}

func (x Workflow_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Workflow_Status.Descriptor instead.
func (Workflow_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{0, 0}
}

// Type
type Workflow_Type int32

const (
	// TYPE_UNSPECIFIED
	Workflow_TYPE_UNSPECIFIED Workflow_Type = 0
	// WORKFLOW
	Workflow_WORKFLOW Workflow_Type = 1
	// TEMPLATE
	Workflow_TEMPLATE Workflow_Type = 2
)

// Enum value maps for Workflow_Type.
var (
	Workflow_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "WORKFLOW",
		2: "TEMPLATE",
	}
	Workflow_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"WORKFLOW":         1,
		"TEMPLATE":         2,
	}
)

func (x Workflow_Type) Enum() *Workflow_Type {
	p := new(Workflow_Type)
	*p = x
	return p
}

func (x Workflow_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Workflow_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_workflow_proto_enumTypes[1].Descriptor()
}

func (Workflow_Type) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_workflow_proto_enumTypes[1]
}

func (x Workflow_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Workflow_Type.Descriptor instead.
func (Workflow_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{0, 1}
}

// RecommendType
type Workflow_RecommendType int32

const (
	// recommend type unspecified
	Workflow_RECOMMEND_TYPE_UNSPECIFIED Workflow_RecommendType = 0
	// HOME_PAGE
	Workflow_HOME_PAGE Workflow_RecommendType = 1
)

// Enum value maps for Workflow_RecommendType.
var (
	Workflow_RecommendType_name = map[int32]string{
		0: "RECOMMEND_TYPE_UNSPECIFIED",
		1: "HOME_PAGE",
	}
	Workflow_RecommendType_value = map[string]int32{
		"RECOMMEND_TYPE_UNSPECIFIED": 0,
		"HOME_PAGE":                  1,
	}
)

func (x Workflow_RecommendType) Enum() *Workflow_RecommendType {
	p := new(Workflow_RecommendType)
	*p = x
	return p
}

func (x Workflow_RecommendType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Workflow_RecommendType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_workflow_proto_enumTypes[2].Descriptor()
}

func (Workflow_RecommendType) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_workflow_proto_enumTypes[2]
}

func (x Workflow_RecommendType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Workflow_RecommendType.Descriptor instead.
func (Workflow_RecommendType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{0, 2}
}

// Type
type WorkflowSetting_Type int32

const (
	// TYPE_UNSPECIFIED
	WorkflowSetting_TYPE_UNSPECIFIED WorkflowSetting_Type = 0
	// NOT_LIMIT
	WorkflowSetting_NOT_LIMIT WorkflowSetting_Type = 1
	// TIME_FREQUENCY
	WorkflowSetting_TIME_FREQUENCY WorkflowSetting_Type = 2
)

// Enum value maps for WorkflowSetting_Type.
var (
	WorkflowSetting_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "NOT_LIMIT",
		2: "TIME_FREQUENCY",
	}
	WorkflowSetting_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"NOT_LIMIT":        1,
		"TIME_FREQUENCY":   2,
	}
)

func (x WorkflowSetting_Type) Enum() *WorkflowSetting_Type {
	p := new(WorkflowSetting_Type)
	*p = x
	return p
}

func (x WorkflowSetting_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkflowSetting_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_workflow_proto_enumTypes[3].Descriptor()
}

func (WorkflowSetting_Type) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_workflow_proto_enumTypes[3]
}

func (x WorkflowSetting_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkflowSetting_Type.Descriptor instead.
func (WorkflowSetting_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{2, 0}
}

// Status
type WorkflowRecord_Status int32

const (
	// STATUS_UNSPECIFIED
	WorkflowRecord_STATUS_UNSPECIFIED WorkflowRecord_Status = 0
	// READY
	WorkflowRecord_READY WorkflowRecord_Status = 1
	// RUNNING
	WorkflowRecord_RUNNING WorkflowRecord_Status = 2
	// PENDING
	WorkflowRecord_PENDING WorkflowRecord_Status = 3
	// SUCCESS
	WorkflowRecord_SUCCESS WorkflowRecord_Status = 4
	// FAIL
	WorkflowRecord_FAIL WorkflowRecord_Status = 5
)

// Enum value maps for WorkflowRecord_Status.
var (
	WorkflowRecord_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "READY",
		2: "RUNNING",
		3: "PENDING",
		4: "SUCCESS",
		5: "FAIL",
	}
	WorkflowRecord_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"READY":              1,
		"RUNNING":            2,
		"PENDING":            3,
		"SUCCESS":            4,
		"FAIL":               5,
	}
)

func (x WorkflowRecord_Status) Enum() *WorkflowRecord_Status {
	p := new(WorkflowRecord_Status)
	*p = x
	return p
}

func (x WorkflowRecord_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkflowRecord_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_workflow_proto_enumTypes[4].Descriptor()
}

func (WorkflowRecord_Status) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_workflow_proto_enumTypes[4]
}

func (x WorkflowRecord_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkflowRecord_Status.Descriptor instead.
func (WorkflowRecord_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{4, 0}
}

// Status
type StepRecord_Status int32

const (
	// STATUS_UNSPECIFIED
	StepRecord_STATUS_UNSPECIFIED StepRecord_Status = 0
	// PENDING
	StepRecord_PENDING StepRecord_Status = 1
	// SUCCESS
	StepRecord_SUCCESS StepRecord_Status = 2
	// FAIL
	StepRecord_FAIL StepRecord_Status = 3
)

// Enum value maps for StepRecord_Status.
var (
	StepRecord_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "PENDING",
		2: "SUCCESS",
		3: "FAIL",
	}
	StepRecord_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"PENDING":            1,
		"SUCCESS":            2,
		"FAIL":               3,
	}
)

func (x StepRecord_Status) Enum() *StepRecord_Status {
	p := new(StepRecord_Status)
	*p = x
	return p
}

func (x StepRecord_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StepRecord_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_workflow_proto_enumTypes[5].Descriptor()
}

func (StepRecord_Status) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_workflow_proto_enumTypes[5]
}

func (x StepRecord_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StepRecord_Status.Descriptor instead.
func (StepRecord_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{5, 0}
}

// Type
type WorkflowEnterpriseApply_Type int32

const (
	// TYPE_UNSPECIFIED
	WorkflowEnterpriseApply_TYPE_UNSPECIFIED WorkflowEnterpriseApply_Type = 0
	// ALL_FRANCHISEES
	WorkflowEnterpriseApply_ALL_FRANCHISEES WorkflowEnterpriseApply_Type = 1
	// FRANCHISEES_GROUPS, fill tenants_group_ids && tenants_ids
	WorkflowEnterpriseApply_FRANCHISEES_GROUPS WorkflowEnterpriseApply_Type = 2
	// FRANCHISEES, fill tenants_ids
	WorkflowEnterpriseApply_FRANCHISEES WorkflowEnterpriseApply_Type = 3
)

// Enum value maps for WorkflowEnterpriseApply_Type.
var (
	WorkflowEnterpriseApply_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "ALL_FRANCHISEES",
		2: "FRANCHISEES_GROUPS",
		3: "FRANCHISEES",
	}
	WorkflowEnterpriseApply_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED":   0,
		"ALL_FRANCHISEES":    1,
		"FRANCHISEES_GROUPS": 2,
		"FRANCHISEES":        3,
	}
)

func (x WorkflowEnterpriseApply_Type) Enum() *WorkflowEnterpriseApply_Type {
	p := new(WorkflowEnterpriseApply_Type)
	*p = x
	return p
}

func (x WorkflowEnterpriseApply_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkflowEnterpriseApply_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_automation_v1_workflow_proto_enumTypes[6].Descriptor()
}

func (WorkflowEnterpriseApply_Type) Type() protoreflect.EnumType {
	return &file_moego_models_automation_v1_workflow_proto_enumTypes[6]
}

func (x WorkflowEnterpriseApply_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkflowEnterpriseApply_Type.Descriptor instead.
func (WorkflowEnterpriseApply_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{7, 0}
}

// Workflow
type Workflow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// meta data
	// Workflow ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Company ID
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Workflow name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// Workflow description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// Workflow status
	Status Workflow_Status `protobuf:"varint,5,opt,name=status,proto3,enum=moego.models.automation.v1.Workflow_Status" json:"status,omitempty"`
	// Workflow type
	Type Workflow_Type `protobuf:"varint,6,opt,name=type,proto3,enum=moego.models.automation.v1.Workflow_Type" json:"type,omitempty"`
	// Created at timestamp
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// Updated at timestamp
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Created by user ID
	CreatedBy int64 `protobuf:"varint,9,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	// Updated by user ID
	UpdatedBy int64 `protobuf:"varint,10,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	// Workflow image URL
	PhotoGallery *Workflow_PhotoGallery `protobuf:"bytes,11,opt,name=photo_gallery,json=photoGallery,proto3" json:"photo_gallery,omitempty"`
	// Enterprise ID
	EnterpriseId int64 `protobuf:"varint,12,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// Tenants IDs
	TenantsIds []int64 `protobuf:"varint,13,rep,packed,name=tenants_ids,json=tenantsIds,proto3" json:"tenants_ids,omitempty"`
	// Version
	Version int64 `protobuf:"varint,14,opt,name=version,proto3" json:"version,omitempty"`
	// content data
	// Steps
	Steps []*Step `protobuf:"bytes,20,rep,name=steps,proto3" json:"steps,omitempty"`
	// Trigger step ID
	TriggerStepId string `protobuf:"bytes,21,opt,name=trigger_step_id,json=triggerStepId,proto3" json:"trigger_step_id,omitempty"`
	// Trigger type
	TriggerType Trigger_Type `protobuf:"varint,22,opt,name=trigger_type,json=triggerType,proto3,enum=moego.models.automation.v1.Trigger_Type" json:"trigger_type,omitempty"`
	// Trigger schedule
	TriggerSchedule *Scheduled `protobuf:"bytes,23,opt,name=trigger_schedule,json=triggerSchedule,proto3" json:"trigger_schedule,omitempty"`
	// Consumer data
	ConsumerData *Workflow_ConsumerData `protobuf:"bytes,24,opt,name=consumer_data,json=consumerData,proto3" json:"consumer_data,omitempty"`
	// record
	// Last trigger time
	LastTriggerTime *timestamppb.Timestamp `protobuf:"bytes,30,opt,name=last_trigger_time,json=lastTriggerTime,proto3" json:"last_trigger_time,omitempty"`
	// category
	// Categories
	Category []*WorkflowCategory `protobuf:"bytes,40,rep,name=category,proto3" json:"category,omitempty"`
	// Recommend type
	RecommendType Workflow_RecommendType `protobuf:"varint,41,opt,name=recommend_type,json=recommendType,proto3,enum=moego.models.automation.v1.Workflow_RecommendType" json:"recommend_type,omitempty"`
	// counter
	// Number of reaches
	ReachNum int32 `protobuf:"varint,50,opt,name=reach_num,json=reachNum,proto3" json:"reach_num,omitempty"`
	// Number of new bookings
	NewBookNum int32 `protobuf:"varint,51,opt,name=new_book_num,json=newBookNum,proto3" json:"new_book_num,omitempty"`
	// Number of apply franchisees
	ApplyTenantsNum int32 `protobuf:"varint,52,opt,name=apply_tenants_num,json=applyTenantsNum,proto3" json:"apply_tenants_num,omitempty"`
	// enterprise apply to
	WorkflowEnterpriseApply *WorkflowEnterpriseApply `protobuf:"bytes,60,opt,name=workflow_enterprise_apply,json=workflowEnterpriseApply,proto3" json:"workflow_enterprise_apply,omitempty"`
}

func (x *Workflow) Reset() {
	*x = Workflow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Workflow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Workflow) ProtoMessage() {}

func (x *Workflow) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Workflow.ProtoReflect.Descriptor instead.
func (*Workflow) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{0}
}

func (x *Workflow) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Workflow) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *Workflow) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Workflow) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Workflow) GetStatus() Workflow_Status {
	if x != nil {
		return x.Status
	}
	return Workflow_STATUS_UNSPECIFIED
}

func (x *Workflow) GetType() Workflow_Type {
	if x != nil {
		return x.Type
	}
	return Workflow_TYPE_UNSPECIFIED
}

func (x *Workflow) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Workflow) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Workflow) GetCreatedBy() int64 {
	if x != nil {
		return x.CreatedBy
	}
	return 0
}

func (x *Workflow) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *Workflow) GetPhotoGallery() *Workflow_PhotoGallery {
	if x != nil {
		return x.PhotoGallery
	}
	return nil
}

func (x *Workflow) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *Workflow) GetTenantsIds() []int64 {
	if x != nil {
		return x.TenantsIds
	}
	return nil
}

func (x *Workflow) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *Workflow) GetSteps() []*Step {
	if x != nil {
		return x.Steps
	}
	return nil
}

func (x *Workflow) GetTriggerStepId() string {
	if x != nil {
		return x.TriggerStepId
	}
	return ""
}

func (x *Workflow) GetTriggerType() Trigger_Type {
	if x != nil {
		return x.TriggerType
	}
	return Trigger_TYPE_UNSPECIFIED
}

func (x *Workflow) GetTriggerSchedule() *Scheduled {
	if x != nil {
		return x.TriggerSchedule
	}
	return nil
}

func (x *Workflow) GetConsumerData() *Workflow_ConsumerData {
	if x != nil {
		return x.ConsumerData
	}
	return nil
}

func (x *Workflow) GetLastTriggerTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastTriggerTime
	}
	return nil
}

func (x *Workflow) GetCategory() []*WorkflowCategory {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *Workflow) GetRecommendType() Workflow_RecommendType {
	if x != nil {
		return x.RecommendType
	}
	return Workflow_RECOMMEND_TYPE_UNSPECIFIED
}

func (x *Workflow) GetReachNum() int32 {
	if x != nil {
		return x.ReachNum
	}
	return 0
}

func (x *Workflow) GetNewBookNum() int32 {
	if x != nil {
		return x.NewBookNum
	}
	return 0
}

func (x *Workflow) GetApplyTenantsNum() int32 {
	if x != nil {
		return x.ApplyTenantsNum
	}
	return 0
}

func (x *Workflow) GetWorkflowEnterpriseApply() *WorkflowEnterpriseApply {
	if x != nil {
		return x.WorkflowEnterpriseApply
	}
	return nil
}

// WorkflowCategory
type WorkflowCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Category ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Category name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// Category description
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	// Category icon URL
	Icon string `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
}

func (x *WorkflowCategory) Reset() {
	*x = WorkflowCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowCategory) ProtoMessage() {}

func (x *WorkflowCategory) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowCategory.ProtoReflect.Descriptor instead.
func (*WorkflowCategory) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{1}
}

func (x *WorkflowCategory) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WorkflowCategory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WorkflowCategory) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *WorkflowCategory) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

// WorkflowSetting
type WorkflowSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Setting type
	Type WorkflowSetting_Type `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.automation.v1.WorkflowSetting_Type" json:"type,omitempty"`
	// Time frequency settings
	TimeFrequency *WorkflowSetting_TimeFrequency `protobuf:"bytes,2,opt,name=time_frequency,json=timeFrequency,proto3" json:"time_frequency,omitempty"`
	// Company ID
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *WorkflowSetting) Reset() {
	*x = WorkflowSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowSetting) ProtoMessage() {}

func (x *WorkflowSetting) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowSetting.ProtoReflect.Descriptor instead.
func (*WorkflowSetting) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{2}
}

func (x *WorkflowSetting) GetType() WorkflowSetting_Type {
	if x != nil {
		return x.Type
	}
	return WorkflowSetting_TYPE_UNSPECIFIED
}

func (x *WorkflowSetting) GetTimeFrequency() *WorkflowSetting_TimeFrequency {
	if x != nil {
		return x.TimeFrequency
	}
	return nil
}

func (x *WorkflowSetting) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// WorkflowOverview
type WorkflowOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Total outreach clients
	TotalOutreachClients int32 `protobuf:"varint,1,opt,name=total_outreach_clients,json=totalOutreachClients,proto3" json:"total_outreach_clients,omitempty"`
	// Response clients
	ResponseClients int32 `protobuf:"varint,2,opt,name=response_clients,json=responseClients,proto3" json:"response_clients,omitempty"`
	// Booked clients
	BookClients int32 `protobuf:"varint,3,opt,name=book_clients,json=bookClients,proto3" json:"book_clients,omitempty"`
	// Total sales grooming booked
	TotalSalesGroomingBooked int32 `protobuf:"varint,4,opt,name=total_sales_grooming_booked,json=totalSalesGroomingBooked,proto3" json:"total_sales_grooming_booked,omitempty"`
}

func (x *WorkflowOverview) Reset() {
	*x = WorkflowOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowOverview) ProtoMessage() {}

func (x *WorkflowOverview) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowOverview.ProtoReflect.Descriptor instead.
func (*WorkflowOverview) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{3}
}

func (x *WorkflowOverview) GetTotalOutreachClients() int32 {
	if x != nil {
		return x.TotalOutreachClients
	}
	return 0
}

func (x *WorkflowOverview) GetResponseClients() int32 {
	if x != nil {
		return x.ResponseClients
	}
	return 0
}

func (x *WorkflowOverview) GetBookClients() int32 {
	if x != nil {
		return x.BookClients
	}
	return 0
}

func (x *WorkflowOverview) GetTotalSalesGroomingBooked() int32 {
	if x != nil {
		return x.TotalSalesGroomingBooked
	}
	return 0
}

// WorkflowRecord
type WorkflowRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Record ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Workflow ID
	WorkflowId int64 `protobuf:"varint,2,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	// Record status
	Status WorkflowRecord_Status `protobuf:"varint,3,opt,name=status,proto3,enum=moego.models.automation.v1.WorkflowRecord_Status" json:"status,omitempty"`
	// Customer ID
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// pack data
	// Workflow data
	Workflow *Workflow `protobuf:"bytes,10,opt,name=workflow,proto3" json:"workflow,omitempty"`
	// Step records
	StepRecords []*StepRecord `protobuf:"bytes,11,rep,name=step_records,json=stepRecords,proto3" json:"step_records,omitempty"`
	// Customer data
	Customer *v1.BusinessCustomerInfoModel `protobuf:"bytes,12,opt,name=customer,proto3" json:"customer,omitempty"`
}

func (x *WorkflowRecord) Reset() {
	*x = WorkflowRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowRecord) ProtoMessage() {}

func (x *WorkflowRecord) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowRecord.ProtoReflect.Descriptor instead.
func (*WorkflowRecord) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{4}
}

func (x *WorkflowRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WorkflowRecord) GetWorkflowId() int64 {
	if x != nil {
		return x.WorkflowId
	}
	return 0
}

func (x *WorkflowRecord) GetStatus() WorkflowRecord_Status {
	if x != nil {
		return x.Status
	}
	return WorkflowRecord_STATUS_UNSPECIFIED
}

func (x *WorkflowRecord) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *WorkflowRecord) GetWorkflow() *Workflow {
	if x != nil {
		return x.Workflow
	}
	return nil
}

func (x *WorkflowRecord) GetStepRecords() []*StepRecord {
	if x != nil {
		return x.StepRecords
	}
	return nil
}

func (x *WorkflowRecord) GetCustomer() *v1.BusinessCustomerInfoModel {
	if x != nil {
		return x.Customer
	}
	return nil
}

// StepRecord
type StepRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Step record ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Step ID
	StepId string `protobuf:"bytes,2,opt,name=step_id,json=stepId,proto3" json:"step_id,omitempty"`
	// Record status
	Status StepRecord_Status `protobuf:"varint,3,opt,name=status,proto3,enum=moego.models.automation.v1.StepRecord_Status" json:"status,omitempty"`
	// Workflow record ID
	WorkflowRecordId int64 `protobuf:"varint,4,opt,name=workflow_record_id,json=workflowRecordId,proto3" json:"workflow_record_id,omitempty"`
	// pack data
	// Step data
	Step *Step `protobuf:"bytes,10,opt,name=step,proto3" json:"step,omitempty"`
}

func (x *StepRecord) Reset() {
	*x = StepRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StepRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StepRecord) ProtoMessage() {}

func (x *StepRecord) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StepRecord.ProtoReflect.Descriptor instead.
func (*StepRecord) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{5}
}

func (x *StepRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StepRecord) GetStepId() string {
	if x != nil {
		return x.StepId
	}
	return ""
}

func (x *StepRecord) GetStatus() StepRecord_Status {
	if x != nil {
		return x.Status
	}
	return StepRecord_STATUS_UNSPECIFIED
}

func (x *StepRecord) GetWorkflowRecordId() int64 {
	if x != nil {
		return x.WorkflowRecordId
	}
	return 0
}

func (x *StepRecord) GetStep() *Step {
	if x != nil {
		return x.Step
	}
	return nil
}

// WorkflowConfig
type WorkflowConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Trigger
	Trigger *Trigger `protobuf:"bytes,1,opt,name=trigger,proto3" json:"trigger,omitempty"`
	// Placeholders
	PlaceHolders []*Field `protobuf:"bytes,2,rep,name=place_holders,json=placeHolders,proto3" json:"place_holders,omitempty"`
	// Filter groups
	FilterGroups []*v2.FilterGroup `protobuf:"bytes,3,rep,name=filter_groups,json=filterGroups,proto3" json:"filter_groups,omitempty"`
	// Event filter groups
	EventFilterGroups []*v2.FilterGroup `protobuf:"bytes,4,rep,name=event_filter_groups,json=eventFilterGroups,proto3" json:"event_filter_groups,omitempty"`
}

func (x *WorkflowConfig) Reset() {
	*x = WorkflowConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowConfig) ProtoMessage() {}

func (x *WorkflowConfig) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowConfig.ProtoReflect.Descriptor instead.
func (*WorkflowConfig) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{6}
}

func (x *WorkflowConfig) GetTrigger() *Trigger {
	if x != nil {
		return x.Trigger
	}
	return nil
}

func (x *WorkflowConfig) GetPlaceHolders() []*Field {
	if x != nil {
		return x.PlaceHolders
	}
	return nil
}

func (x *WorkflowConfig) GetFilterGroups() []*v2.FilterGroup {
	if x != nil {
		return x.FilterGroups
	}
	return nil
}

func (x *WorkflowConfig) GetEventFilterGroups() []*v2.FilterGroup {
	if x != nil {
		return x.EventFilterGroups
	}
	return nil
}

// WorkflowEnterpriseApply
type WorkflowEnterpriseApply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Apply Type
	Type WorkflowEnterpriseApply_Type `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.automation.v1.WorkflowEnterpriseApply_Type" json:"type,omitempty"`
	// Tenants Group IDs
	TenantsGroupIds []int64 `protobuf:"varint,2,rep,packed,name=tenants_group_ids,json=tenantsGroupIds,proto3" json:"tenants_group_ids,omitempty"`
	// Tenants IDs
	TenantsIds []int64 `protobuf:"varint,3,rep,packed,name=tenants_ids,json=tenantsIds,proto3" json:"tenants_ids,omitempty"`
}

func (x *WorkflowEnterpriseApply) Reset() {
	*x = WorkflowEnterpriseApply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowEnterpriseApply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowEnterpriseApply) ProtoMessage() {}

func (x *WorkflowEnterpriseApply) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowEnterpriseApply.ProtoReflect.Descriptor instead.
func (*WorkflowEnterpriseApply) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{7}
}

func (x *WorkflowEnterpriseApply) GetType() WorkflowEnterpriseApply_Type {
	if x != nil {
		return x.Type
	}
	return WorkflowEnterpriseApply_TYPE_UNSPECIFIED
}

func (x *WorkflowEnterpriseApply) GetTenantsGroupIds() []int64 {
	if x != nil {
		return x.TenantsGroupIds
	}
	return nil
}

func (x *WorkflowEnterpriseApply) GetTenantsIds() []int64 {
	if x != nil {
		return x.TenantsIds
	}
	return nil
}

// Goal
type Goal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Goal) Reset() {
	*x = Goal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Goal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Goal) ProtoMessage() {}

func (x *Goal) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Goal.ProtoReflect.Descriptor instead.
func (*Goal) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{8}
}

// PhotoGallery
type Workflow_PhotoGallery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// image
	Image string `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	// recommend image
	RecommendImage string `protobuf:"bytes,2,opt,name=recommend_image,json=recommendImage,proto3" json:"recommend_image,omitempty"`
}

func (x *Workflow_PhotoGallery) Reset() {
	*x = Workflow_PhotoGallery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Workflow_PhotoGallery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Workflow_PhotoGallery) ProtoMessage() {}

func (x *Workflow_PhotoGallery) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Workflow_PhotoGallery.ProtoReflect.Descriptor instead.
func (*Workflow_PhotoGallery) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Workflow_PhotoGallery) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *Workflow_PhotoGallery) GetRecommendImage() string {
	if x != nil {
		return x.RecommendImage
	}
	return ""
}

// ConsumerData
type Workflow_ConsumerData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// effect_client_num
	EffectClientNum int32 `protobuf:"varint,1,opt,name=effect_client_num,json=effectClientNum,proto3" json:"effect_client_num,omitempty"`
	// cost_sms_token_num
	CostSmsTokenNum int32 `protobuf:"varint,2,opt,name=cost_sms_token_num,json=costSmsTokenNum,proto3" json:"cost_sms_token_num,omitempty"`
}

func (x *Workflow_ConsumerData) Reset() {
	*x = Workflow_ConsumerData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Workflow_ConsumerData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Workflow_ConsumerData) ProtoMessage() {}

func (x *Workflow_ConsumerData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Workflow_ConsumerData.ProtoReflect.Descriptor instead.
func (*Workflow_ConsumerData) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Workflow_ConsumerData) GetEffectClientNum() int32 {
	if x != nil {
		return x.EffectClientNum
	}
	return 0
}

func (x *Workflow_ConsumerData) GetCostSmsTokenNum() int32 {
	if x != nil {
		return x.CostSmsTokenNum
	}
	return 0
}

// TimeFrequency
type WorkflowSetting_TimeFrequency struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Time duration
	TimeDuration *TimeDuration `protobuf:"bytes,2,opt,name=time_duration,json=timeDuration,proto3" json:"time_duration,omitempty"`
	// Frequency limit
	Limit int64 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *WorkflowSetting_TimeFrequency) Reset() {
	*x = WorkflowSetting_TimeFrequency{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowSetting_TimeFrequency) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowSetting_TimeFrequency) ProtoMessage() {}

func (x *WorkflowSetting_TimeFrequency) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_workflow_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowSetting_TimeFrequency.ProtoReflect.Descriptor instead.
func (*WorkflowSetting_TimeFrequency) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_proto_rawDescGZIP(), []int{2, 0}
}

func (x *WorkflowSetting_TimeFrequency) GetTimeDuration() *TimeDuration {
	if x != nil {
		return x.TimeDuration
	}
	return nil
}

func (x *WorkflowSetting_TimeFrequency) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

var File_moego_models_automation_v1_workflow_proto protoreflect.FileDescriptor

var file_moego_models_automation_v1_workflow_proto_rawDesc = []byte{
	0x0a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x25, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74,
	0x65, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x85, 0x0e, 0x0a, 0x08, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x3d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74,
	0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x42, 0x79, 0x12, 0x56, 0x0a, 0x0d, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x67, 0x61, 0x6c, 0x6c,
	0x65, 0x72, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x50, 0x68, 0x6f, 0x74, 0x6f, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x52, 0x0c, 0x70, 0x68,
	0x6f, 0x74, 0x6f, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0d,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x49, 0x64, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x05, 0x73, 0x74,
	0x65, 0x70, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x52, 0x05, 0x73, 0x74, 0x65,
	0x70, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x73, 0x74,
	0x65, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x53, 0x74, 0x65, 0x70, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0c, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x50, 0x0a, 0x10, 0x74, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x52, 0x0f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x56, 0x0a, 0x0d, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x46, 0x0a, 0x11, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x28, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x59, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x29, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0d, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x72, 0x65, 0x61, 0x63, 0x68, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x32, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x72, 0x65, 0x61, 0x63, 0x68, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0c, 0x6e,
	0x65, 0x77, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x33, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x6e, 0x65, 0x77, 0x42, 0x6f, 0x6f, 0x6b, 0x4e, 0x75, 0x6d, 0x12, 0x2a, 0x0a,
	0x11, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x5f, 0x6e,
	0x75, 0x6d, 0x18, 0x34, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x4e, 0x75, 0x6d, 0x12, 0x6f, 0x0a, 0x19, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x18, 0x3c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x41, 0x70, 0x70, 0x6c,
	0x79, 0x52, 0x17, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x1a, 0x4d, 0x0a, 0x0c, 0x50, 0x68,
	0x6f, 0x74, 0x6f, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x5f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x1a, 0x67, 0x0a, 0x0c, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x66, 0x66,
	0x65, 0x63, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x2b, 0x0a, 0x12, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x73, 0x6d,
	0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0f, 0x63, 0x6f, 0x73, 0x74, 0x53, 0x6d, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x4e,
	0x75, 0x6d, 0x22, 0x52, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x44, 0x52, 0x41, 0x46, 0x54, 0x10, 0x01, 0x12,
	0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x4c,
	0x45, 0x54, 0x45, 0x44, 0x10, 0x04, 0x22, 0x38, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14,
	0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x57, 0x4f, 0x52, 0x4b, 0x46, 0x4c, 0x4f, 0x57,
	0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x10, 0x02,
	0x22, 0x3e, 0x0a, 0x0d, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x44, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0d, 0x0a, 0x09, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x10, 0x01,
	0x22, 0x5e, 0x0a, 0x10, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x12, 0x0a, 0x04,
	0x69, 0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e,
	0x22, 0x8f, 0x03, 0x0a, 0x0f, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x12, 0x44, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x60, 0x0a, 0x0e, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x0d, 0x74,
	0x69, 0x6d, 0x65, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x1a, 0x74, 0x0a, 0x0d, 0x54,
	0x69, 0x6d, 0x65, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x4d, 0x0a, 0x0d,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x74,
	0x69, 0x6d, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x22, 0x3f, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x01, 0x12, 0x12,
	0x0a, 0x0e, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59,
	0x10, 0x02, 0x22, 0xd5, 0x01, 0x0a, 0x10, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4f,
	0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x34, 0x0a, 0x16, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x75,
	0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x29, 0x0a,
	0x10, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6f, 0x6f, 0x6b,
	0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x62, 0x6f, 0x6f, 0x6b, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x3d, 0x0a, 0x1b, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x18, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x47, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x42, 0x6f, 0x6f, 0x6b, 0x65, 0x64, 0x22, 0xf2, 0x03, 0x0a, 0x0e, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x49,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x08, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x49, 0x0a, 0x0c,
	0x73, 0x74, 0x65, 0x70, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x65, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x0b, 0x73, 0x74, 0x65, 0x70,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x58, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x22, 0x5c, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x01, 0x12, 0x0b,
	0x0a, 0x07, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x50,
	0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x10, 0x04, 0x12, 0x08, 0x0a, 0x04, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x05, 0x22,
	0xa6, 0x02, 0x0a, 0x0a, 0x53, 0x74, 0x65, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x65, 0x70, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c,
	0x0a, 0x12, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x04,
	0x73, 0x74, 0x65, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x52, 0x04, 0x73, 0x74,
	0x65, 0x70, 0x22, 0x44, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x08,
	0x0a, 0x04, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x03, 0x22, 0xbc, 0x02, 0x0a, 0x0e, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3d, 0x0a, 0x07, 0x74,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x52, 0x07, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x12, 0x46, 0x0a, 0x0d, 0x70, 0x6c,
	0x61, 0x63, 0x65, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x48, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x73, 0x12, 0x4b, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12,
	0x56, 0x0a, 0x13, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x11, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0x90, 0x02, 0x0a, 0x17, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x41, 0x70,
	0x70, 0x6c, 0x79, 0x12, 0x4c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0f, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x0a, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x49, 0x64, 0x73, 0x22, 0x5a,
	0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f,
	0x41, 0x4c, 0x4c, 0x5f, 0x46, 0x52, 0x41, 0x4e, 0x43, 0x48, 0x49, 0x53, 0x45, 0x45, 0x53, 0x10,
	0x01, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x52, 0x41, 0x4e, 0x43, 0x48, 0x49, 0x53, 0x45, 0x45, 0x53,
	0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x53, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x52, 0x41,
	0x4e, 0x43, 0x48, 0x49, 0x53, 0x45, 0x45, 0x53, 0x10, 0x03, 0x22, 0x06, 0x0a, 0x04, 0x47, 0x6f,
	0x61, 0x6c, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x75, 0x74,
	0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_models_automation_v1_workflow_proto_rawDescOnce sync.Once
	file_moego_models_automation_v1_workflow_proto_rawDescData = file_moego_models_automation_v1_workflow_proto_rawDesc
)

func file_moego_models_automation_v1_workflow_proto_rawDescGZIP() []byte {
	file_moego_models_automation_v1_workflow_proto_rawDescOnce.Do(func() {
		file_moego_models_automation_v1_workflow_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_automation_v1_workflow_proto_rawDescData)
	})
	return file_moego_models_automation_v1_workflow_proto_rawDescData
}

var file_moego_models_automation_v1_workflow_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_moego_models_automation_v1_workflow_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_models_automation_v1_workflow_proto_goTypes = []interface{}{
	(Workflow_Status)(0),                  // 0: moego.models.automation.v1.Workflow.Status
	(Workflow_Type)(0),                    // 1: moego.models.automation.v1.Workflow.Type
	(Workflow_RecommendType)(0),           // 2: moego.models.automation.v1.Workflow.RecommendType
	(WorkflowSetting_Type)(0),             // 3: moego.models.automation.v1.WorkflowSetting.Type
	(WorkflowRecord_Status)(0),            // 4: moego.models.automation.v1.WorkflowRecord.Status
	(StepRecord_Status)(0),                // 5: moego.models.automation.v1.StepRecord.Status
	(WorkflowEnterpriseApply_Type)(0),     // 6: moego.models.automation.v1.WorkflowEnterpriseApply.Type
	(*Workflow)(nil),                      // 7: moego.models.automation.v1.Workflow
	(*WorkflowCategory)(nil),              // 8: moego.models.automation.v1.WorkflowCategory
	(*WorkflowSetting)(nil),               // 9: moego.models.automation.v1.WorkflowSetting
	(*WorkflowOverview)(nil),              // 10: moego.models.automation.v1.WorkflowOverview
	(*WorkflowRecord)(nil),                // 11: moego.models.automation.v1.WorkflowRecord
	(*StepRecord)(nil),                    // 12: moego.models.automation.v1.StepRecord
	(*WorkflowConfig)(nil),                // 13: moego.models.automation.v1.WorkflowConfig
	(*WorkflowEnterpriseApply)(nil),       // 14: moego.models.automation.v1.WorkflowEnterpriseApply
	(*Goal)(nil),                          // 15: moego.models.automation.v1.Goal
	(*Workflow_PhotoGallery)(nil),         // 16: moego.models.automation.v1.Workflow.PhotoGallery
	(*Workflow_ConsumerData)(nil),         // 17: moego.models.automation.v1.Workflow.ConsumerData
	(*WorkflowSetting_TimeFrequency)(nil), // 18: moego.models.automation.v1.WorkflowSetting.TimeFrequency
	(*timestamppb.Timestamp)(nil),         // 19: google.protobuf.Timestamp
	(*Step)(nil),                          // 20: moego.models.automation.v1.Step
	(Trigger_Type)(0),                     // 21: moego.models.automation.v1.Trigger.Type
	(*Scheduled)(nil),                     // 22: moego.models.automation.v1.Scheduled
	(*v1.BusinessCustomerInfoModel)(nil),  // 23: moego.models.business_customer.v1.BusinessCustomerInfoModel
	(*Trigger)(nil),                       // 24: moego.models.automation.v1.Trigger
	(*Field)(nil),                         // 25: moego.models.automation.v1.Field
	(*v2.FilterGroup)(nil),                // 26: moego.models.reporting.v2.FilterGroup
	(*TimeDuration)(nil),                  // 27: moego.models.automation.v1.TimeDuration
}
var file_moego_models_automation_v1_workflow_proto_depIdxs = []int32{
	0,  // 0: moego.models.automation.v1.Workflow.status:type_name -> moego.models.automation.v1.Workflow.Status
	1,  // 1: moego.models.automation.v1.Workflow.type:type_name -> moego.models.automation.v1.Workflow.Type
	19, // 2: moego.models.automation.v1.Workflow.created_at:type_name -> google.protobuf.Timestamp
	19, // 3: moego.models.automation.v1.Workflow.updated_at:type_name -> google.protobuf.Timestamp
	16, // 4: moego.models.automation.v1.Workflow.photo_gallery:type_name -> moego.models.automation.v1.Workflow.PhotoGallery
	20, // 5: moego.models.automation.v1.Workflow.steps:type_name -> moego.models.automation.v1.Step
	21, // 6: moego.models.automation.v1.Workflow.trigger_type:type_name -> moego.models.automation.v1.Trigger.Type
	22, // 7: moego.models.automation.v1.Workflow.trigger_schedule:type_name -> moego.models.automation.v1.Scheduled
	17, // 8: moego.models.automation.v1.Workflow.consumer_data:type_name -> moego.models.automation.v1.Workflow.ConsumerData
	19, // 9: moego.models.automation.v1.Workflow.last_trigger_time:type_name -> google.protobuf.Timestamp
	8,  // 10: moego.models.automation.v1.Workflow.category:type_name -> moego.models.automation.v1.WorkflowCategory
	2,  // 11: moego.models.automation.v1.Workflow.recommend_type:type_name -> moego.models.automation.v1.Workflow.RecommendType
	14, // 12: moego.models.automation.v1.Workflow.workflow_enterprise_apply:type_name -> moego.models.automation.v1.WorkflowEnterpriseApply
	3,  // 13: moego.models.automation.v1.WorkflowSetting.type:type_name -> moego.models.automation.v1.WorkflowSetting.Type
	18, // 14: moego.models.automation.v1.WorkflowSetting.time_frequency:type_name -> moego.models.automation.v1.WorkflowSetting.TimeFrequency
	4,  // 15: moego.models.automation.v1.WorkflowRecord.status:type_name -> moego.models.automation.v1.WorkflowRecord.Status
	7,  // 16: moego.models.automation.v1.WorkflowRecord.workflow:type_name -> moego.models.automation.v1.Workflow
	12, // 17: moego.models.automation.v1.WorkflowRecord.step_records:type_name -> moego.models.automation.v1.StepRecord
	23, // 18: moego.models.automation.v1.WorkflowRecord.customer:type_name -> moego.models.business_customer.v1.BusinessCustomerInfoModel
	5,  // 19: moego.models.automation.v1.StepRecord.status:type_name -> moego.models.automation.v1.StepRecord.Status
	20, // 20: moego.models.automation.v1.StepRecord.step:type_name -> moego.models.automation.v1.Step
	24, // 21: moego.models.automation.v1.WorkflowConfig.trigger:type_name -> moego.models.automation.v1.Trigger
	25, // 22: moego.models.automation.v1.WorkflowConfig.place_holders:type_name -> moego.models.automation.v1.Field
	26, // 23: moego.models.automation.v1.WorkflowConfig.filter_groups:type_name -> moego.models.reporting.v2.FilterGroup
	26, // 24: moego.models.automation.v1.WorkflowConfig.event_filter_groups:type_name -> moego.models.reporting.v2.FilterGroup
	6,  // 25: moego.models.automation.v1.WorkflowEnterpriseApply.type:type_name -> moego.models.automation.v1.WorkflowEnterpriseApply.Type
	27, // 26: moego.models.automation.v1.WorkflowSetting.TimeFrequency.time_duration:type_name -> moego.models.automation.v1.TimeDuration
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_moego_models_automation_v1_workflow_proto_init() }
func file_moego_models_automation_v1_workflow_proto_init() {
	if File_moego_models_automation_v1_workflow_proto != nil {
		return
	}
	file_moego_models_automation_v1_common_proto_init()
	file_moego_models_automation_v1_step_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_automation_v1_workflow_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Workflow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_workflow_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkflowCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_workflow_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkflowSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_workflow_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkflowOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_workflow_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkflowRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_workflow_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StepRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_workflow_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkflowConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_workflow_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkflowEnterpriseApply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_workflow_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Goal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_workflow_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Workflow_PhotoGallery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_workflow_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Workflow_ConsumerData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_workflow_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkflowSetting_TimeFrequency); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_automation_v1_workflow_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_automation_v1_workflow_proto_goTypes,
		DependencyIndexes: file_moego_models_automation_v1_workflow_proto_depIdxs,
		EnumInfos:         file_moego_models_automation_v1_workflow_proto_enumTypes,
		MessageInfos:      file_moego_models_automation_v1_workflow_proto_msgTypes,
	}.Build()
	File_moego_models_automation_v1_workflow_proto = out.File
	file_moego_models_automation_v1_workflow_proto_rawDesc = nil
	file_moego_models_automation_v1_workflow_proto_goTypes = nil
	file_moego_models_automation_v1_workflow_proto_depIdxs = nil
}
