// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/automation/v1/workflow_defs.proto

package automationpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CreateWorkflowDefs
type CreateWorkflowDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// meta data
	// Workflow name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Workflow description
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// Workflow image
	Image string `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	// Workflow recommend_image
	RecommendImage string `protobuf:"bytes,4,opt,name=recommend_image,json=recommendImage,proto3" json:"recommend_image,omitempty"`
	// Workflow Source Template ID
	WorkflowTemplateId int64 `protobuf:"varint,5,opt,name=workflow_template_id,json=workflowTemplateId,proto3" json:"workflow_template_id,omitempty"`
	// content data
	// Steps definitions
	Steps []*CreateStepDef `protobuf:"bytes,20,rep,name=steps,proto3" json:"steps,omitempty"`
	// Workflow Consumer Data
	ConsumerData *Workflow_ConsumerData `protobuf:"bytes,21,opt,name=consumer_data,json=consumerData,proto3" json:"consumer_data,omitempty"`
	// category
	// Workflow categories
	Category []*WorkflowCategory `protobuf:"bytes,40,rep,name=category,proto3" json:"category,omitempty"`
	// Recommend type
	RecommendType Workflow_RecommendType `protobuf:"varint,41,opt,name=recommend_type,json=recommendType,proto3,enum=moego.models.automation.v1.Workflow_RecommendType" json:"recommend_type,omitempty"`
	// enterprise apply to
	WorkflowEnterpriseApply *WorkflowEnterpriseApply `protobuf:"bytes,50,opt,name=workflow_enterprise_apply,json=workflowEnterpriseApply,proto3" json:"workflow_enterprise_apply,omitempty"`
}

func (x *CreateWorkflowDef) Reset() {
	*x = CreateWorkflowDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_workflow_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWorkflowDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkflowDef) ProtoMessage() {}

func (x *CreateWorkflowDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_workflow_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkflowDef.ProtoReflect.Descriptor instead.
func (*CreateWorkflowDef) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_defs_proto_rawDescGZIP(), []int{0}
}

func (x *CreateWorkflowDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateWorkflowDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateWorkflowDef) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *CreateWorkflowDef) GetRecommendImage() string {
	if x != nil {
		return x.RecommendImage
	}
	return ""
}

func (x *CreateWorkflowDef) GetWorkflowTemplateId() int64 {
	if x != nil {
		return x.WorkflowTemplateId
	}
	return 0
}

func (x *CreateWorkflowDef) GetSteps() []*CreateStepDef {
	if x != nil {
		return x.Steps
	}
	return nil
}

func (x *CreateWorkflowDef) GetConsumerData() *Workflow_ConsumerData {
	if x != nil {
		return x.ConsumerData
	}
	return nil
}

func (x *CreateWorkflowDef) GetCategory() []*WorkflowCategory {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *CreateWorkflowDef) GetRecommendType() Workflow_RecommendType {
	if x != nil {
		return x.RecommendType
	}
	return Workflow_RECOMMEND_TYPE_UNSPECIFIED
}

func (x *CreateWorkflowDef) GetWorkflowEnterpriseApply() *WorkflowEnterpriseApply {
	if x != nil {
		return x.WorkflowEnterpriseApply
	}
	return nil
}

// CreateStepDefs
type CreateStepDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// meta data
	// Step ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Step name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// Step description
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// adjacency data
	// Parent step ID
	ParentId string `protobuf:"bytes,5,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// content data
	// Step type
	Type Step_Type `protobuf:"varint,8,opt,name=type,proto3,enum=moego.models.automation.v1.Step_Type" json:"type,omitempty"`
	// Step data
	Data *Step_Data `protobuf:"bytes,9,opt,name=data,proto3" json:"data,omitempty"`
	// Step data preview
	PreviewData *Step_PreviewData `protobuf:"bytes,10,opt,name=preview_data,json=previewData,proto3,oneof" json:"preview_data,omitempty"`
}

func (x *CreateStepDef) Reset() {
	*x = CreateStepDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_workflow_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateStepDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateStepDef) ProtoMessage() {}

func (x *CreateStepDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_workflow_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateStepDef.ProtoReflect.Descriptor instead.
func (*CreateStepDef) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_workflow_defs_proto_rawDescGZIP(), []int{1}
}

func (x *CreateStepDef) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CreateStepDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateStepDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateStepDef) GetParentId() string {
	if x != nil {
		return x.ParentId
	}
	return ""
}

func (x *CreateStepDef) GetType() Step_Type {
	if x != nil {
		return x.Type
	}
	return Step_TYPE_UNSPECIFIED
}

func (x *CreateStepDef) GetData() *Step_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *CreateStepDef) GetPreviewData() *Step_PreviewData {
	if x != nil {
		return x.PreviewData
	}
	return nil
}

var File_moego_models_automation_v1_workflow_defs_proto protoreflect.FileDescriptor

var file_moego_models_automation_v1_workflow_defs_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x25, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x65, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe9,
	0x04, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x44, 0x65, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x5f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x05, 0x73,
	0x74, 0x65, 0x70, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x65, 0x70, 0x44, 0x65, 0x66, 0x52, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x12, 0x56, 0x0a, 0x0d,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x48, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x28, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x59,
	0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x29, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x6f, 0x0a, 0x19, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x18, 0x32, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x41, 0x70, 0x70, 0x6c,
	0x79, 0x52, 0x17, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x22, 0xcf, 0x02, 0x0a, 0x0d, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x65, 0x70, 0x44, 0x65, 0x66, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74,
	0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x54, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x2e, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0b, 0x70, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x44, 0x61, 0x74, 0x61, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f,
	0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x42, 0x84, 0x01, 0x0a,
	0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_automation_v1_workflow_defs_proto_rawDescOnce sync.Once
	file_moego_models_automation_v1_workflow_defs_proto_rawDescData = file_moego_models_automation_v1_workflow_defs_proto_rawDesc
)

func file_moego_models_automation_v1_workflow_defs_proto_rawDescGZIP() []byte {
	file_moego_models_automation_v1_workflow_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_automation_v1_workflow_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_automation_v1_workflow_defs_proto_rawDescData)
	})
	return file_moego_models_automation_v1_workflow_defs_proto_rawDescData
}

var file_moego_models_automation_v1_workflow_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_automation_v1_workflow_defs_proto_goTypes = []interface{}{
	(*CreateWorkflowDef)(nil),       // 0: moego.models.automation.v1.CreateWorkflowDef
	(*CreateStepDef)(nil),           // 1: moego.models.automation.v1.CreateStepDef
	(*Workflow_ConsumerData)(nil),   // 2: moego.models.automation.v1.Workflow.ConsumerData
	(*WorkflowCategory)(nil),        // 3: moego.models.automation.v1.WorkflowCategory
	(Workflow_RecommendType)(0),     // 4: moego.models.automation.v1.Workflow.RecommendType
	(*WorkflowEnterpriseApply)(nil), // 5: moego.models.automation.v1.WorkflowEnterpriseApply
	(Step_Type)(0),                  // 6: moego.models.automation.v1.Step.Type
	(*Step_Data)(nil),               // 7: moego.models.automation.v1.Step.Data
	(*Step_PreviewData)(nil),        // 8: moego.models.automation.v1.Step.PreviewData
}
var file_moego_models_automation_v1_workflow_defs_proto_depIdxs = []int32{
	1, // 0: moego.models.automation.v1.CreateWorkflowDef.steps:type_name -> moego.models.automation.v1.CreateStepDef
	2, // 1: moego.models.automation.v1.CreateWorkflowDef.consumer_data:type_name -> moego.models.automation.v1.Workflow.ConsumerData
	3, // 2: moego.models.automation.v1.CreateWorkflowDef.category:type_name -> moego.models.automation.v1.WorkflowCategory
	4, // 3: moego.models.automation.v1.CreateWorkflowDef.recommend_type:type_name -> moego.models.automation.v1.Workflow.RecommendType
	5, // 4: moego.models.automation.v1.CreateWorkflowDef.workflow_enterprise_apply:type_name -> moego.models.automation.v1.WorkflowEnterpriseApply
	6, // 5: moego.models.automation.v1.CreateStepDef.type:type_name -> moego.models.automation.v1.Step.Type
	7, // 6: moego.models.automation.v1.CreateStepDef.data:type_name -> moego.models.automation.v1.Step.Data
	8, // 7: moego.models.automation.v1.CreateStepDef.preview_data:type_name -> moego.models.automation.v1.Step.PreviewData
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_moego_models_automation_v1_workflow_defs_proto_init() }
func file_moego_models_automation_v1_workflow_defs_proto_init() {
	if File_moego_models_automation_v1_workflow_defs_proto != nil {
		return
	}
	file_moego_models_automation_v1_step_proto_init()
	file_moego_models_automation_v1_workflow_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_automation_v1_workflow_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWorkflowDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_automation_v1_workflow_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateStepDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_automation_v1_workflow_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_automation_v1_workflow_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_automation_v1_workflow_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_automation_v1_workflow_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_automation_v1_workflow_defs_proto_msgTypes,
	}.Build()
	File_moego_models_automation_v1_workflow_defs_proto = out.File
	file_moego_models_automation_v1_workflow_defs_proto_rawDesc = nil
	file_moego_models_automation_v1_workflow_defs_proto_goTypes = nil
	file_moego_models_automation_v1_workflow_defs_proto_depIdxs = nil
}
