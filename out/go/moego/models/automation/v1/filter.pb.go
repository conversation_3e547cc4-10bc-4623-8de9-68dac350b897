// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/automation/v1/filter.proto

package automationpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// filter groups works for event
type EventFilterGroups struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// category
	Category Event_Category `protobuf:"varint,1,opt,name=category,proto3,enum=moego.models.automation.v1.Event_Category" json:"category,omitempty"`
	// filter group
	FilterGroups []*v2.FilterGroup `protobuf:"bytes,2,rep,name=filter_groups,json=filterGroups,proto3" json:"filter_groups,omitempty"`
}

func (x *EventFilterGroups) Reset() {
	*x = EventFilterGroups{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_automation_v1_filter_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EventFilterGroups) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventFilterGroups) ProtoMessage() {}

func (x *EventFilterGroups) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_automation_v1_filter_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventFilterGroups.ProtoReflect.Descriptor instead.
func (*EventFilterGroups) Descriptor() ([]byte, []int) {
	return file_moego_models_automation_v1_filter_proto_rawDescGZIP(), []int{0}
}

func (x *EventFilterGroups) GetCategory() Event_Category {
	if x != nil {
		return x.Category
	}
	return Event_CATEGORY_UNSPECIFIED
}

func (x *EventFilterGroups) GetFilterGroups() []*v2.FilterGroup {
	if x != nil {
		return x.FilterGroups
	}
	return nil
}

var File_moego_models_automation_v1_filter_proto protoreflect.FileDescriptor

var file_moego_models_automation_v1_filter_proto_rawDesc = []byte{
	0x0a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x25, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x74, 0x65, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa8, 0x01, 0x0a, 0x11, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x12, 0x46, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x4b, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b,
	0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_automation_v1_filter_proto_rawDescOnce sync.Once
	file_moego_models_automation_v1_filter_proto_rawDescData = file_moego_models_automation_v1_filter_proto_rawDesc
)

func file_moego_models_automation_v1_filter_proto_rawDescGZIP() []byte {
	file_moego_models_automation_v1_filter_proto_rawDescOnce.Do(func() {
		file_moego_models_automation_v1_filter_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_automation_v1_filter_proto_rawDescData)
	})
	return file_moego_models_automation_v1_filter_proto_rawDescData
}

var file_moego_models_automation_v1_filter_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_automation_v1_filter_proto_goTypes = []interface{}{
	(*EventFilterGroups)(nil), // 0: moego.models.automation.v1.EventFilterGroups
	(Event_Category)(0),       // 1: moego.models.automation.v1.Event.Category
	(*v2.FilterGroup)(nil),    // 2: moego.models.reporting.v2.FilterGroup
}
var file_moego_models_automation_v1_filter_proto_depIdxs = []int32{
	1, // 0: moego.models.automation.v1.EventFilterGroups.category:type_name -> moego.models.automation.v1.Event.Category
	2, // 1: moego.models.automation.v1.EventFilterGroups.filter_groups:type_name -> moego.models.reporting.v2.FilterGroup
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_automation_v1_filter_proto_init() }
func file_moego_models_automation_v1_filter_proto_init() {
	if File_moego_models_automation_v1_filter_proto != nil {
		return
	}
	file_moego_models_automation_v1_step_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_automation_v1_filter_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EventFilterGroups); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_automation_v1_filter_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_automation_v1_filter_proto_goTypes,
		DependencyIndexes: file_moego_models_automation_v1_filter_proto_depIdxs,
		MessageInfos:      file_moego_models_automation_v1_filter_proto_msgTypes,
	}.Build()
	File_moego_models_automation_v1_filter_proto = out.File
	file_moego_models_automation_v1_filter_proto_rawDesc = nil
	file_moego_models_automation_v1_filter_proto_goTypes = nil
	file_moego_models_automation_v1_filter_proto_depIdxs = nil
}
