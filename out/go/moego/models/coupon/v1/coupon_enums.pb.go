// @since 2-23-12-6
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/coupon/v1/coupon_enums.proto

package couponpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// business category
// this enumeration is mainly used to identify the business type that the coupon belongs to,
// such as platform care or pay admin.
// it is differentiated based on the enumeration for easy querying
type CouponBusinessCategory int32

const (
	// unspecified
	CouponBusinessCategory_COUPON_BUSINESS_CATEGORY_UNSPECIFIED CouponBusinessCategory = 0
	// platform care
	CouponBusinessCategory_COUPON_BUSINESS_CATEGORY_PLATFORM_CARE CouponBusinessCategory = 1
	// pay admin
	CouponBusinessCategory_COUPON_BUSINESS_CATEGORY_PAY_ADMIN CouponBusinessCategory = 2
	// referral
	CouponBusinessCategory_COUPON_BUSINESS_CATEGORY_REFERRAL CouponBusinessCategory = 3
	// MOEGO CARE USER FACING COUPON
	CouponBusinessCategory_COUPON_BUSINESS_CATEGORY_CARE CouponBusinessCategory = 4
	// HARDWARE
	CouponBusinessCategory_COUPON_BUSINESS_CATEGORY_HARDWARE CouponBusinessCategory = 5
	// HARDWARE
	CouponBusinessCategory_COUPON_BUSINESS_CATEGORY_PLATFORM_SALES CouponBusinessCategory = 6
)

// Enum value maps for CouponBusinessCategory.
var (
	CouponBusinessCategory_name = map[int32]string{
		0: "COUPON_BUSINESS_CATEGORY_UNSPECIFIED",
		1: "COUPON_BUSINESS_CATEGORY_PLATFORM_CARE",
		2: "COUPON_BUSINESS_CATEGORY_PAY_ADMIN",
		3: "COUPON_BUSINESS_CATEGORY_REFERRAL",
		4: "COUPON_BUSINESS_CATEGORY_CARE",
		5: "COUPON_BUSINESS_CATEGORY_HARDWARE",
		6: "COUPON_BUSINESS_CATEGORY_PLATFORM_SALES",
	}
	CouponBusinessCategory_value = map[string]int32{
		"COUPON_BUSINESS_CATEGORY_UNSPECIFIED":    0,
		"COUPON_BUSINESS_CATEGORY_PLATFORM_CARE":  1,
		"COUPON_BUSINESS_CATEGORY_PAY_ADMIN":      2,
		"COUPON_BUSINESS_CATEGORY_REFERRAL":       3,
		"COUPON_BUSINESS_CATEGORY_CARE":           4,
		"COUPON_BUSINESS_CATEGORY_HARDWARE":       5,
		"COUPON_BUSINESS_CATEGORY_PLATFORM_SALES": 6,
	}
)

func (x CouponBusinessCategory) Enum() *CouponBusinessCategory {
	p := new(CouponBusinessCategory)
	*p = x
	return p
}

func (x CouponBusinessCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CouponBusinessCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_coupon_v1_coupon_enums_proto_enumTypes[0].Descriptor()
}

func (CouponBusinessCategory) Type() protoreflect.EnumType {
	return &file_moego_models_coupon_v1_coupon_enums_proto_enumTypes[0]
}

func (x CouponBusinessCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CouponBusinessCategory.Descriptor instead.
func (CouponBusinessCategory) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_coupon_v1_coupon_enums_proto_rawDescGZIP(), []int{0}
}

var File_moego_models_coupon_v1_coupon_enums_proto protoreflect.FileDescriptor

var file_moego_models_coupon_v1_coupon_enums_proto_rawDesc = []byte{
	0x0a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63,
	0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2a, 0xb4, 0x02, 0x0a, 0x16, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x28,
	0x0a, 0x24, 0x43, 0x4f, 0x55, 0x50, 0x4f, 0x4e, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53,
	0x53, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x4f, 0x55, 0x50,
	0x4f, 0x4e, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x43, 0x41,
	0x52, 0x45, 0x10, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4f, 0x55, 0x50, 0x4f, 0x4e, 0x5f, 0x42,
	0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x5f, 0x50, 0x41, 0x59, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10, 0x02, 0x12, 0x25, 0x0a, 0x21,
	0x43, 0x4f, 0x55, 0x50, 0x4f, 0x4e, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41,
	0x4c, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x43, 0x4f, 0x55, 0x50, 0x4f, 0x4e, 0x5f, 0x42, 0x55,
	0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x43, 0x41, 0x52, 0x45, 0x10, 0x04, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x4f, 0x55, 0x50, 0x4f, 0x4e,
	0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x48, 0x41, 0x52, 0x44, 0x57, 0x41, 0x52, 0x45, 0x10, 0x05, 0x12, 0x2b, 0x0a,
	0x27, 0x43, 0x4f, 0x55, 0x50, 0x4f, 0x4e, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53,
	0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f,
	0x52, 0x4d, 0x5f, 0x53, 0x41, 0x4c, 0x45, 0x53, 0x10, 0x06, 0x42, 0x78, 0x0a, 0x1e, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x54,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_coupon_v1_coupon_enums_proto_rawDescOnce sync.Once
	file_moego_models_coupon_v1_coupon_enums_proto_rawDescData = file_moego_models_coupon_v1_coupon_enums_proto_rawDesc
)

func file_moego_models_coupon_v1_coupon_enums_proto_rawDescGZIP() []byte {
	file_moego_models_coupon_v1_coupon_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_coupon_v1_coupon_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_coupon_v1_coupon_enums_proto_rawDescData)
	})
	return file_moego_models_coupon_v1_coupon_enums_proto_rawDescData
}

var file_moego_models_coupon_v1_coupon_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_coupon_v1_coupon_enums_proto_goTypes = []interface{}{
	(CouponBusinessCategory)(0), // 0: moego.models.coupon.v1.CouponBusinessCategory
}
var file_moego_models_coupon_v1_coupon_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_coupon_v1_coupon_enums_proto_init() }
func file_moego_models_coupon_v1_coupon_enums_proto_init() {
	if File_moego_models_coupon_v1_coupon_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_coupon_v1_coupon_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_coupon_v1_coupon_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_coupon_v1_coupon_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_coupon_v1_coupon_enums_proto_enumTypes,
	}.Build()
	File_moego_models_coupon_v1_coupon_enums_proto = out.File
	file_moego_models_coupon_v1_coupon_enums_proto_rawDesc = nil
	file_moego_models_coupon_v1_coupon_enums_proto_goTypes = nil
	file_moego_models_coupon_v1_coupon_enums_proto_depIdxs = nil
}
