// @since 2-23-12-6
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/coupon/v1/coupon_model.proto

package couponpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CouponDetail is the detail of a coupon
type CouponModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier for the coupon
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// code of the coupon
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	// stripe coupon id
	StripeCouponId string `protobuf:"bytes,3,opt,name=stripe_coupon_id,json=stripeCouponId,proto3" json:"stripe_coupon_id,omitempty"`
	// percentage off for the coupon
	PercentOff float64 `protobuf:"fixed64,4,opt,name=percent_off,json=percentOff,proto3" json:"percent_off,omitempty"`
	// amount off for the coupon
	AmountOff float64 `protobuf:"fixed64,5,opt,name=amount_off,json=amountOff,proto3" json:"amount_off,omitempty"`
	// maximum number of redemptions for the coupon
	MaxRedemptions int32 `protobuf:"varint,6,opt,name=max_redemptions,json=maxRedemptions,proto3" json:"max_redemptions,omitempty"`
	// number of times the coupon has been redeemed
	TimesRedeemed int32 `protobuf:"varint,7,opt,name=times_redeemed,json=timesRedeemed,proto3" json:"times_redeemed,omitempty"`
	// timestamp when the coupon expires
	RedeemBy string `protobuf:"bytes,8,opt,name=redeem_by,json=redeemBy,proto3" json:"redeem_by,omitempty"`
	// indicates if the coupon is valid
	Valid int32 `protobuf:"varint,9,opt,name=valid,proto3" json:"valid,omitempty"`
	// status of the coupon
	Status int32 `protobuf:"varint,10,opt,name=status,proto3" json:"status,omitempty"`
	// business category of the coupon
	BusinessCategory CouponBusinessCategory `protobuf:"varint,11,opt,name=business_category,json=businessCategory,proto3,enum=moego.models.coupon.v1.CouponBusinessCategory" json:"business_category,omitempty"`
	// validity period in months
	ValidMonth int32 `protobuf:"varint,12,opt,name=valid_month,json=validMonth,proto3" json:"valid_month,omitempty"`
	// created time
	CreatedTime int64 `protobuf:"varint,13,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	// update time
	UpdatedTime int64 `protobuf:"varint,14,opt,name=updated_time,json=updatedTime,proto3" json:"updated_time,omitempty"`
}

func (x *CouponModel) Reset() {
	*x = CouponModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_coupon_v1_coupon_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CouponModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CouponModel) ProtoMessage() {}

func (x *CouponModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_coupon_v1_coupon_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CouponModel.ProtoReflect.Descriptor instead.
func (*CouponModel) Descriptor() ([]byte, []int) {
	return file_moego_models_coupon_v1_coupon_model_proto_rawDescGZIP(), []int{0}
}

func (x *CouponModel) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CouponModel) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CouponModel) GetStripeCouponId() string {
	if x != nil {
		return x.StripeCouponId
	}
	return ""
}

func (x *CouponModel) GetPercentOff() float64 {
	if x != nil {
		return x.PercentOff
	}
	return 0
}

func (x *CouponModel) GetAmountOff() float64 {
	if x != nil {
		return x.AmountOff
	}
	return 0
}

func (x *CouponModel) GetMaxRedemptions() int32 {
	if x != nil {
		return x.MaxRedemptions
	}
	return 0
}

func (x *CouponModel) GetTimesRedeemed() int32 {
	if x != nil {
		return x.TimesRedeemed
	}
	return 0
}

func (x *CouponModel) GetRedeemBy() string {
	if x != nil {
		return x.RedeemBy
	}
	return ""
}

func (x *CouponModel) GetValid() int32 {
	if x != nil {
		return x.Valid
	}
	return 0
}

func (x *CouponModel) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CouponModel) GetBusinessCategory() CouponBusinessCategory {
	if x != nil {
		return x.BusinessCategory
	}
	return CouponBusinessCategory_COUPON_BUSINESS_CATEGORY_UNSPECIFIED
}

func (x *CouponModel) GetValidMonth() int32 {
	if x != nil {
		return x.ValidMonth
	}
	return 0
}

func (x *CouponModel) GetCreatedTime() int64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *CouponModel) GetUpdatedTime() int64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

// coupon simple view
type CouponSimpleView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier for the coupon
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// code of the coupon
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	// stripe coupon id
	StripeCouponId string `protobuf:"bytes,3,opt,name=stripe_coupon_id,json=stripeCouponId,proto3" json:"stripe_coupon_id,omitempty"`
	// percentage off for the coupon
	PercentOff float64 `protobuf:"fixed64,4,opt,name=percent_off,json=percentOff,proto3" json:"percent_off,omitempty"`
	// indicates if the coupon is valid
	Valid int32 `protobuf:"varint,5,opt,name=valid,proto3" json:"valid,omitempty"`
	// status of the coupon
	Status int32 `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	// business category of the coupon
	BusinessCategory CouponBusinessCategory `protobuf:"varint,7,opt,name=business_category,json=businessCategory,proto3,enum=moego.models.coupon.v1.CouponBusinessCategory" json:"business_category,omitempty"`
	// validity period in months
	ValidMonth int32 `protobuf:"varint,8,opt,name=valid_month,json=validMonth,proto3" json:"valid_month,omitempty"`
	// created time
	CreatedTime int64 `protobuf:"varint,9,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	// maximum number of redemptions for the coupon
	MaxRedemptions int32 `protobuf:"varint,10,opt,name=max_redemptions,json=maxRedemptions,proto3" json:"max_redemptions,omitempty"`
	// number of times the coupon has been redeemed
	TimesRedeemed int32 `protobuf:"varint,11,opt,name=times_redeemed,json=timesRedeemed,proto3" json:"times_redeemed,omitempty"`
	// the coupon  redeem by time ,the timestamp format("yy-mm-dd HH:MM:SS ZONE")
	RedeemBy string `protobuf:"bytes,12,opt,name=redeem_by,json=redeemBy,proto3" json:"redeem_by,omitempty"`
	// amount off for the coupon
	AmountOff float64 `protobuf:"fixed64,13,opt,name=amount_off,json=amountOff,proto3" json:"amount_off,omitempty"`
}

func (x *CouponSimpleView) Reset() {
	*x = CouponSimpleView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_coupon_v1_coupon_model_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CouponSimpleView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CouponSimpleView) ProtoMessage() {}

func (x *CouponSimpleView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_coupon_v1_coupon_model_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CouponSimpleView.ProtoReflect.Descriptor instead.
func (*CouponSimpleView) Descriptor() ([]byte, []int) {
	return file_moego_models_coupon_v1_coupon_model_proto_rawDescGZIP(), []int{1}
}

func (x *CouponSimpleView) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CouponSimpleView) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CouponSimpleView) GetStripeCouponId() string {
	if x != nil {
		return x.StripeCouponId
	}
	return ""
}

func (x *CouponSimpleView) GetPercentOff() float64 {
	if x != nil {
		return x.PercentOff
	}
	return 0
}

func (x *CouponSimpleView) GetValid() int32 {
	if x != nil {
		return x.Valid
	}
	return 0
}

func (x *CouponSimpleView) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CouponSimpleView) GetBusinessCategory() CouponBusinessCategory {
	if x != nil {
		return x.BusinessCategory
	}
	return CouponBusinessCategory_COUPON_BUSINESS_CATEGORY_UNSPECIFIED
}

func (x *CouponSimpleView) GetValidMonth() int32 {
	if x != nil {
		return x.ValidMonth
	}
	return 0
}

func (x *CouponSimpleView) GetCreatedTime() int64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *CouponSimpleView) GetMaxRedemptions() int32 {
	if x != nil {
		return x.MaxRedemptions
	}
	return 0
}

func (x *CouponSimpleView) GetTimesRedeemed() int32 {
	if x != nil {
		return x.TimesRedeemed
	}
	return 0
}

func (x *CouponSimpleView) GetRedeemBy() string {
	if x != nil {
		return x.RedeemBy
	}
	return ""
}

func (x *CouponSimpleView) GetAmountOff() float64 {
	if x != nil {
		return x.AmountOff
	}
	return 0
}

var File_moego_models_coupon_v1_coupon_model_proto protoreflect.FileDescriptor

var file_moego_models_coupon_v1_coupon_model_proto_rawDesc = []byte{
	0x0a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63,
	0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x1a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfa,
	0x03, 0x0a, 0x0b, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x75,
	0x70, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x74,
	0x72, 0x69, 0x70, 0x65, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x4f, 0x66, 0x66, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x09, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x66, 0x66, 0x12, 0x27, 0x0a, 0x0f,
	0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x5f, 0x72,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x62, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x42, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x52, 0x10, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x6d, 0x6f,
	0x6e, 0x74, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xdc, 0x03, 0x0a, 0x10,
	0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x63,
	0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x4f, 0x66, 0x66, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5b, 0x0a,
	0x11, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x10, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x64, 0x65,
	0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x5f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x09, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x66, 0x66, 0x42, 0x78, 0x0a, 0x1e, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x54,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_coupon_v1_coupon_model_proto_rawDescOnce sync.Once
	file_moego_models_coupon_v1_coupon_model_proto_rawDescData = file_moego_models_coupon_v1_coupon_model_proto_rawDesc
)

func file_moego_models_coupon_v1_coupon_model_proto_rawDescGZIP() []byte {
	file_moego_models_coupon_v1_coupon_model_proto_rawDescOnce.Do(func() {
		file_moego_models_coupon_v1_coupon_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_coupon_v1_coupon_model_proto_rawDescData)
	})
	return file_moego_models_coupon_v1_coupon_model_proto_rawDescData
}

var file_moego_models_coupon_v1_coupon_model_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_coupon_v1_coupon_model_proto_goTypes = []interface{}{
	(*CouponModel)(nil),         // 0: moego.models.coupon.v1.CouponModel
	(*CouponSimpleView)(nil),    // 1: moego.models.coupon.v1.CouponSimpleView
	(CouponBusinessCategory)(0), // 2: moego.models.coupon.v1.CouponBusinessCategory
}
var file_moego_models_coupon_v1_coupon_model_proto_depIdxs = []int32{
	2, // 0: moego.models.coupon.v1.CouponModel.business_category:type_name -> moego.models.coupon.v1.CouponBusinessCategory
	2, // 1: moego.models.coupon.v1.CouponSimpleView.business_category:type_name -> moego.models.coupon.v1.CouponBusinessCategory
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_coupon_v1_coupon_model_proto_init() }
func file_moego_models_coupon_v1_coupon_model_proto_init() {
	if File_moego_models_coupon_v1_coupon_model_proto != nil {
		return
	}
	file_moego_models_coupon_v1_coupon_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_coupon_v1_coupon_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CouponModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_coupon_v1_coupon_model_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CouponSimpleView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_coupon_v1_coupon_model_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_coupon_v1_coupon_model_proto_goTypes,
		DependencyIndexes: file_moego_models_coupon_v1_coupon_model_proto_depIdxs,
		MessageInfos:      file_moego_models_coupon_v1_coupon_model_proto_msgTypes,
	}.Build()
	File_moego_models_coupon_v1_coupon_model_proto = out.File
	file_moego_models_coupon_v1_coupon_model_proto_rawDesc = nil
	file_moego_models_coupon_v1_coupon_model_proto_goTypes = nil
	file_moego_models_coupon_v1_coupon_model_proto_depIdxs = nil
}
