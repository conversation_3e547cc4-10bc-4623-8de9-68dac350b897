// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_pet_vaccine_record_enums.proto

package businesscustomerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// source
type Source int32

const (
	// default
	Source_SOURCE_UNSPECIFIED Source = 0
	// third party VetVerifi
	Source_SOURCE_VET_VERIFI Source = 1
)

// Enum value maps for Source.
var (
	Source_name = map[int32]string{
		0: "SOURCE_UNSPECIFIED",
		1: "SOURCE_VET_VERIFI",
	}
	Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED": 0,
		"SOURCE_VET_VERIFI":  1,
	}
)

func (x Source) Enum() *Source {
	p := new(Source)
	*p = x
	return p
}

func (x Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Source) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_enumTypes[0].Descriptor()
}

func (Source) Type() protoreflect.EnumType {
	return &file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_enumTypes[0]
}

func (x Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Source.Descriptor instead.
func (Source) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_rawDescGZIP(), []int{0}
}

// verify status
type VerifyStatus int32

const (
	// unspecified
	VerifyStatus_VERIFY_STATUS_UNSPECIFIED VerifyStatus = 0
	// clear
	VerifyStatus_VERIFY_STATUS_CLEAR VerifyStatus = 1
	// not clear
	VerifyStatus_VERIFY_STATUS_NOT_CLEAR VerifyStatus = 2
)

// Enum value maps for VerifyStatus.
var (
	VerifyStatus_name = map[int32]string{
		0: "VERIFY_STATUS_UNSPECIFIED",
		1: "VERIFY_STATUS_CLEAR",
		2: "VERIFY_STATUS_NOT_CLEAR",
	}
	VerifyStatus_value = map[string]int32{
		"VERIFY_STATUS_UNSPECIFIED": 0,
		"VERIFY_STATUS_CLEAR":       1,
		"VERIFY_STATUS_NOT_CLEAR":   2,
	}
)

func (x VerifyStatus) Enum() *VerifyStatus {
	p := new(VerifyStatus)
	*p = x
	return p
}

func (x VerifyStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VerifyStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_enumTypes[1].Descriptor()
}

func (VerifyStatus) Type() protoreflect.EnumType {
	return &file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_enumTypes[1]
}

func (x VerifyStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VerifyStatus.Descriptor instead.
func (VerifyStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_rawDescGZIP(), []int{1}
}

var File_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_rawDesc = []byte{
	0x0a, 0x49, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2a, 0x37,
	0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x15, 0x0a, 0x11, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x56, 0x45, 0x54, 0x5f, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x49, 0x10, 0x01, 0x2a, 0x63, 0x0a, 0x0c, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x45, 0x41, 0x52, 0x10, 0x01, 0x12,
	0x1b, 0x0a, 0x17, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4c, 0x45, 0x41, 0x52, 0x10, 0x02, 0x42, 0x98, 0x01, 0x0a,
	0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_rawDescData = file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_goTypes = []interface{}{
	(Source)(0),       // 0: moego.models.business_customer.v1.Source
	(VerifyStatus)(0), // 1: moego.models.business_customer.v1.VerifyStatus
}
var file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_init() }
func file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_init() {
	if File_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_enumTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto = out.File
	file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_pet_vaccine_record_enums_proto_depIdxs = nil
}
