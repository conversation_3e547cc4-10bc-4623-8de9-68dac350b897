// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_customer_merge_enums.proto

package businesscustomerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// status of customer merge
type CustomerMergeStatus int32

const (
	// unspecified
	CustomerMergeStatus_CUSTOMER_MERGE_STATUS_UNSPECIFIED CustomerMergeStatus = 0
	// no merge record
	CustomerMergeStatus_NO_MERGE_RECORD CustomerMergeStatus = 1
	// merging
	CustomerMergeStatus_MERGING CustomerMergeStatus = 2
	// merged
	CustomerMergeStatus_MERGED CustomerMergeStatus = 3
)

// Enum value maps for CustomerMergeStatus.
var (
	CustomerMergeStatus_name = map[int32]string{
		0: "CUSTOMER_MERGE_STATUS_UNSPECIFIED",
		1: "NO_MERGE_RECORD",
		2: "MERGING",
		3: "MERGED",
	}
	CustomerMergeStatus_value = map[string]int32{
		"CUSTOMER_MERGE_STATUS_UNSPECIFIED": 0,
		"NO_MERGE_RECORD":                   1,
		"MERGING":                           2,
		"MERGED":                            3,
	}
)

func (x CustomerMergeStatus) Enum() *CustomerMergeStatus {
	p := new(CustomerMergeStatus)
	*p = x
	return p
}

func (x CustomerMergeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomerMergeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_business_customer_v1_business_customer_merge_enums_proto_enumTypes[0].Descriptor()
}

func (CustomerMergeStatus) Type() protoreflect.EnumType {
	return &file_moego_models_business_customer_v1_business_customer_merge_enums_proto_enumTypes[0]
}

func (x CustomerMergeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomerMergeStatus.Descriptor instead.
func (CustomerMergeStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_merge_enums_proto_rawDescGZIP(), []int{0}
}

// duplication detect rule
type DuplicationDetectRule int32

const (
	// unspecified
	DuplicationDetectRule_DUPLICATION_DETECT_RULE_UNSPECIFIED DuplicationDetectRule = 0
	// phone number
	DuplicationDetectRule_PHONE_NUMBER DuplicationDetectRule = 1
	// email
	DuplicationDetectRule_EMAIL DuplicationDetectRule = 2
	// customer name
	DuplicationDetectRule_CUSTOMER_NAME DuplicationDetectRule = 3
	// pet name and type breed
	DuplicationDetectRule_PET_NAME_AND_TYPE_BREED DuplicationDetectRule = 4
)

// Enum value maps for DuplicationDetectRule.
var (
	DuplicationDetectRule_name = map[int32]string{
		0: "DUPLICATION_DETECT_RULE_UNSPECIFIED",
		1: "PHONE_NUMBER",
		2: "EMAIL",
		3: "CUSTOMER_NAME",
		4: "PET_NAME_AND_TYPE_BREED",
	}
	DuplicationDetectRule_value = map[string]int32{
		"DUPLICATION_DETECT_RULE_UNSPECIFIED": 0,
		"PHONE_NUMBER":                        1,
		"EMAIL":                               2,
		"CUSTOMER_NAME":                       3,
		"PET_NAME_AND_TYPE_BREED":             4,
	}
)

func (x DuplicationDetectRule) Enum() *DuplicationDetectRule {
	p := new(DuplicationDetectRule)
	*p = x
	return p
}

func (x DuplicationDetectRule) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DuplicationDetectRule) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_business_customer_v1_business_customer_merge_enums_proto_enumTypes[1].Descriptor()
}

func (DuplicationDetectRule) Type() protoreflect.EnumType {
	return &file_moego_models_business_customer_v1_business_customer_merge_enums_proto_enumTypes[1]
}

func (x DuplicationDetectRule) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DuplicationDetectRule.Descriptor instead.
func (DuplicationDetectRule) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_merge_enums_proto_rawDescGZIP(), []int{1}
}

var File_moego_models_business_customer_v1_business_customer_merge_enums_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_customer_merge_enums_proto_rawDesc = []byte{
	0x0a, 0x45, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2a, 0x6a, 0x0a, 0x13, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x4d, 0x45,
	0x52, 0x47, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x4e, 0x4f, 0x5f, 0x4d,
	0x45, 0x52, 0x47, 0x45, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x10, 0x01, 0x12, 0x0b, 0x0a,
	0x07, 0x4d, 0x45, 0x52, 0x47, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x45,
	0x52, 0x47, 0x45, 0x44, 0x10, 0x03, 0x2a, 0x8d, 0x01, 0x0a, 0x15, 0x44, 0x75, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x27, 0x0a, 0x23, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x48, 0x4f,
	0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x45,
	0x4d, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x45, 0x52, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x45, 0x54,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42,
	0x52, 0x45, 0x45, 0x44, 0x10, 0x04, 0x42, 0x98, 0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_customer_v1_business_customer_merge_enums_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_customer_merge_enums_proto_rawDescData = file_moego_models_business_customer_v1_business_customer_merge_enums_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_customer_merge_enums_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_customer_merge_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_customer_merge_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_customer_merge_enums_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_customer_merge_enums_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_customer_merge_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_business_customer_v1_business_customer_merge_enums_proto_goTypes = []interface{}{
	(CustomerMergeStatus)(0),   // 0: moego.models.business_customer.v1.CustomerMergeStatus
	(DuplicationDetectRule)(0), // 1: moego.models.business_customer.v1.DuplicationDetectRule
}
var file_moego_models_business_customer_v1_business_customer_merge_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_customer_merge_enums_proto_init() }
func file_moego_models_business_customer_v1_business_customer_merge_enums_proto_init() {
	if File_moego_models_business_customer_v1_business_customer_merge_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_customer_merge_enums_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_customer_merge_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_customer_merge_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_business_customer_v1_business_customer_merge_enums_proto_enumTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_customer_merge_enums_proto = out.File
	file_moego_models_business_customer_v1_business_customer_merge_enums_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_customer_merge_enums_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_customer_merge_enums_proto_depIdxs = nil
}
