// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_customer_tag_defs.proto

package businesscustomerpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create def for customer tag
type BusinessCustomerTagCreateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tag name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *BusinessCustomerTagCreateDef) Reset() {
	*x = BusinessCustomerTagCreateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_tag_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerTagCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerTagCreateDef) ProtoMessage() {}

func (x *BusinessCustomerTagCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_tag_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerTagCreateDef.ProtoReflect.Descriptor instead.
func (*BusinessCustomerTagCreateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_tag_defs_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessCustomerTagCreateDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// update def for customer tag
type BusinessCustomerTagUpdateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tag name
	Name *string `protobuf:"bytes,4,opt,name=name,proto3,oneof" json:"name,omitempty"`
}

func (x *BusinessCustomerTagUpdateDef) Reset() {
	*x = BusinessCustomerTagUpdateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_tag_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerTagUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerTagUpdateDef) ProtoMessage() {}

func (x *BusinessCustomerTagUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_tag_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerTagUpdateDef.ProtoReflect.Descriptor instead.
func (*BusinessCustomerTagUpdateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_tag_defs_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessCustomerTagUpdateDef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

var File_moego_models_business_customer_v1_business_customer_tag_defs_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_customer_tag_defs_proto_rawDesc = []byte{
	0x0a, 0x42, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x46, 0x0a, 0x1c, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66,
	0x12, 0x26, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12,
	0xfa, 0x42, 0x0f, 0x72, 0x0d, 0x10, 0x01, 0x18, 0x32, 0x32, 0x07, 0x2e, 0x2a, 0x5c, 0x53, 0x2b,
	0x2e, 0x2a, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x54, 0x0a, 0x1c, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x61, 0x67, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x2b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0xfa, 0x42, 0x0f, 0x72, 0x0d, 0x10, 0x01, 0x18,
	0x32, 0x32, 0x07, 0x2e, 0x2a, 0x5c, 0x53, 0x2b, 0x2e, 0x2a, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x98,
	0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_models_business_customer_v1_business_customer_tag_defs_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_customer_tag_defs_proto_rawDescData = file_moego_models_business_customer_v1_business_customer_tag_defs_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_customer_tag_defs_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_customer_tag_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_customer_tag_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_customer_tag_defs_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_customer_tag_defs_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_customer_tag_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_business_customer_v1_business_customer_tag_defs_proto_goTypes = []interface{}{
	(*BusinessCustomerTagCreateDef)(nil), // 0: moego.models.business_customer.v1.BusinessCustomerTagCreateDef
	(*BusinessCustomerTagUpdateDef)(nil), // 1: moego.models.business_customer.v1.BusinessCustomerTagUpdateDef
}
var file_moego_models_business_customer_v1_business_customer_tag_defs_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_customer_tag_defs_proto_init() }
func file_moego_models_business_customer_v1_business_customer_tag_defs_proto_init() {
	if File_moego_models_business_customer_v1_business_customer_tag_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_business_customer_v1_business_customer_tag_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerTagCreateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_tag_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerTagUpdateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_business_customer_v1_business_customer_tag_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_customer_tag_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_customer_tag_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_customer_tag_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_business_customer_v1_business_customer_tag_defs_proto_msgTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_customer_tag_defs_proto = out.File
	file_moego_models_business_customer_v1_business_customer_tag_defs_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_customer_tag_defs_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_customer_tag_defs_proto_depIdxs = nil
}
