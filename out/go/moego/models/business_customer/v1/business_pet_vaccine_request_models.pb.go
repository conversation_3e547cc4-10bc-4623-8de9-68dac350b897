// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_pet_vaccine_request_models.proto

package businesscustomerpb

import (
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// status
type BusinessPetVaccineRequestModel_Status int32

const (
	// unspecified
	BusinessPetVaccineRequestModel_STATUS_UNSPECIFIED BusinessPetVaccineRequestModel_Status = 0
	// PENDING
	BusinessPetVaccineRequestModel_PENDING BusinessPetVaccineRequestModel_Status = 1
	// APPROVED
	BusinessPetVaccineRequestModel_APPROVED BusinessPetVaccineRequestModel_Status = 2
	// DECLINED
	BusinessPetVaccineRequestModel_DECLINED BusinessPetVaccineRequestModel_Status = 3
)

// Enum value maps for BusinessPetVaccineRequestModel_Status.
var (
	BusinessPetVaccineRequestModel_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "PENDING",
		2: "APPROVED",
		3: "DECLINED",
	}
	BusinessPetVaccineRequestModel_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"PENDING":            1,
		"APPROVED":           2,
		"DECLINED":           3,
	}
)

func (x BusinessPetVaccineRequestModel_Status) Enum() *BusinessPetVaccineRequestModel_Status {
	p := new(BusinessPetVaccineRequestModel_Status)
	*p = x
	return p
}

func (x BusinessPetVaccineRequestModel_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BusinessPetVaccineRequestModel_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_enumTypes[0].Descriptor()
}

func (BusinessPetVaccineRequestModel_Status) Type() protoreflect.EnumType {
	return &file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_enumTypes[0]
}

func (x BusinessPetVaccineRequestModel_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BusinessPetVaccineRequestModel_Status.Descriptor instead.
func (BusinessPetVaccineRequestModel_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_rawDescGZIP(), []int{0, 0}
}

// BusinessPetVaccineRequestModel
type BusinessPetVaccineRequestModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vaccine request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// vaccine record id
	VaccineRecordId int64 `protobuf:"varint,2,opt,name=vaccine_record_id,json=vaccineRecordId,proto3" json:"vaccine_record_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// vaccine id
	VaccineId int64 `protobuf:"varint,4,opt,name=vaccine_id,json=vaccineId,proto3" json:"vaccine_id,omitempty"`
	// expiration date, may not exist
	ExpirationDate *date.Date `protobuf:"bytes,5,opt,name=expiration_date,json=expirationDate,proto3,oneof" json:"expiration_date,omitempty"`
	// vaccine document urls
	DocumentUrls []string `protobuf:"bytes,6,rep,name=document_urls,json=documentUrls,proto3" json:"document_urls,omitempty"`
	// status
	Status BusinessPetVaccineRequestModel_Status `protobuf:"varint,7,opt,name=status,proto3,enum=moego.models.business_customer.v1.BusinessPetVaccineRequestModel_Status" json:"status,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
}

func (x *BusinessPetVaccineRequestModel) Reset() {
	*x = BusinessPetVaccineRequestModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessPetVaccineRequestModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessPetVaccineRequestModel) ProtoMessage() {}

func (x *BusinessPetVaccineRequestModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessPetVaccineRequestModel.ProtoReflect.Descriptor instead.
func (*BusinessPetVaccineRequestModel) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessPetVaccineRequestModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessPetVaccineRequestModel) GetVaccineRecordId() int64 {
	if x != nil {
		return x.VaccineRecordId
	}
	return 0
}

func (x *BusinessPetVaccineRequestModel) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *BusinessPetVaccineRequestModel) GetVaccineId() int64 {
	if x != nil {
		return x.VaccineId
	}
	return 0
}

func (x *BusinessPetVaccineRequestModel) GetExpirationDate() *date.Date {
	if x != nil {
		return x.ExpirationDate
	}
	return nil
}

func (x *BusinessPetVaccineRequestModel) GetDocumentUrls() []string {
	if x != nil {
		return x.DocumentUrls
	}
	return nil
}

func (x *BusinessPetVaccineRequestModel) GetStatus() BusinessPetVaccineRequestModel_Status {
	if x != nil {
		return x.Status
	}
	return BusinessPetVaccineRequestModel_STATUS_UNSPECIFIED
}

func (x *BusinessPetVaccineRequestModel) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

// BusinessPetVaccineRequestBindingModel
type BusinessPetVaccineRequestBindingModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vaccine record id
	// 没有 vaccine_record_id 表示这组 requests 是要新增 vaccine record
	// 有 vaccine_record_id 表示这组 requests 是要更新这个 vaccine record
	VaccineRecordId *int64 `protobuf:"varint,1,opt,name=vaccine_record_id,json=vaccineRecordId,proto3,oneof" json:"vaccine_record_id,omitempty"`
	// requests
	Requests []*BusinessPetVaccineRequestModel `protobuf:"bytes,2,rep,name=requests,proto3" json:"requests,omitempty"`
}

func (x *BusinessPetVaccineRequestBindingModel) Reset() {
	*x = BusinessPetVaccineRequestBindingModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessPetVaccineRequestBindingModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessPetVaccineRequestBindingModel) ProtoMessage() {}

func (x *BusinessPetVaccineRequestBindingModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessPetVaccineRequestBindingModel.ProtoReflect.Descriptor instead.
func (*BusinessPetVaccineRequestBindingModel) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessPetVaccineRequestBindingModel) GetVaccineRecordId() int64 {
	if x != nil && x.VaccineRecordId != nil {
		return *x.VaccineRecordId
	}
	return 0
}

func (x *BusinessPetVaccineRequestBindingModel) GetRequests() []*BusinessPetVaccineRequestModel {
	if x != nil {
		return x.Requests
	}
	return nil
}

var File_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_rawDesc = []byte{
	0x0a, 0x4b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf6, 0x03, 0x0a, 0x1e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x3f,
	0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x23, 0x0a, 0x0d, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x55, 0x72, 0x6c, 0x73, 0x12, 0x60, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0x49, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a,
	0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x02,
	0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x43, 0x4c, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x03, 0x42, 0x12,
	0x0a, 0x10, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x22, 0xcd, 0x01, 0x0a, 0x25, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50,
	0x65, 0x74, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x2f, 0x0a, 0x11,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0f, 0x76, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x5d, 0x0a,
	0x08, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x56,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x42, 0x14, 0x0a, 0x12,
	0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f,
	0x69, 0x64, 0x42, 0x98, 0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_rawDescData = file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_goTypes = []interface{}{
	(BusinessPetVaccineRequestModel_Status)(0),    // 0: moego.models.business_customer.v1.BusinessPetVaccineRequestModel.Status
	(*BusinessPetVaccineRequestModel)(nil),        // 1: moego.models.business_customer.v1.BusinessPetVaccineRequestModel
	(*BusinessPetVaccineRequestBindingModel)(nil), // 2: moego.models.business_customer.v1.BusinessPetVaccineRequestBindingModel
	(*date.Date)(nil),                             // 3: google.type.Date
	(*timestamppb.Timestamp)(nil),                 // 4: google.protobuf.Timestamp
}
var file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_depIdxs = []int32{
	3, // 0: moego.models.business_customer.v1.BusinessPetVaccineRequestModel.expiration_date:type_name -> google.type.Date
	0, // 1: moego.models.business_customer.v1.BusinessPetVaccineRequestModel.status:type_name -> moego.models.business_customer.v1.BusinessPetVaccineRequestModel.Status
	4, // 2: moego.models.business_customer.v1.BusinessPetVaccineRequestModel.create_time:type_name -> google.protobuf.Timestamp
	1, // 3: moego.models.business_customer.v1.BusinessPetVaccineRequestBindingModel.requests:type_name -> moego.models.business_customer.v1.BusinessPetVaccineRequestModel
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_init() }
func file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_init() {
	if File_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessPetVaccineRequestModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessPetVaccineRequestBindingModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_depIdxs,
		EnumInfos:         file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_enumTypes,
		MessageInfos:      file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_msgTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto = out.File
	file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_pet_vaccine_request_models_proto_depIdxs = nil
}
