// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_pet_code_defs.proto

package businesscustomerpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create def for pet code
type BusinessPetCodeCreateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code abbreviation
	Abbreviation string `protobuf:"bytes,1,opt,name=abbreviation,proto3" json:"abbreviation,omitempty"`
	// pet code description
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// pet code color
	Color string `protobuf:"bytes,3,opt,name=color,proto3" json:"color,omitempty"`
}

func (x *BusinessPetCodeCreateDef) Reset() {
	*x = BusinessPetCodeCreateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_pet_code_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessPetCodeCreateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessPetCodeCreateDef) ProtoMessage() {}

func (x *BusinessPetCodeCreateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_pet_code_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessPetCodeCreateDef.ProtoReflect.Descriptor instead.
func (*BusinessPetCodeCreateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_code_defs_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessPetCodeCreateDef) GetAbbreviation() string {
	if x != nil {
		return x.Abbreviation
	}
	return ""
}

func (x *BusinessPetCodeCreateDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BusinessPetCodeCreateDef) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

// update def for pet code
type BusinessPetCodeUpdateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code abbreviation
	Abbreviation *string `protobuf:"bytes,1,opt,name=abbreviation,proto3,oneof" json:"abbreviation,omitempty"`
	// pet code description
	Description *string `protobuf:"bytes,2,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// pet code color
	Color *string `protobuf:"bytes,3,opt,name=color,proto3,oneof" json:"color,omitempty"`
}

func (x *BusinessPetCodeUpdateDef) Reset() {
	*x = BusinessPetCodeUpdateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_pet_code_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessPetCodeUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessPetCodeUpdateDef) ProtoMessage() {}

func (x *BusinessPetCodeUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_pet_code_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessPetCodeUpdateDef.ProtoReflect.Descriptor instead.
func (*BusinessPetCodeUpdateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_code_defs_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessPetCodeUpdateDef) GetAbbreviation() string {
	if x != nil && x.Abbreviation != nil {
		return *x.Abbreviation
	}
	return ""
}

func (x *BusinessPetCodeUpdateDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *BusinessPetCodeUpdateDef) GetColor() string {
	if x != nil && x.Color != nil {
		return *x.Color
	}
	return ""
}

var File_moego_models_business_customer_v1_business_pet_code_defs_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_pet_code_defs_proto_rawDesc = []byte{
	0x0a, 0x3e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x21, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb2, 0x01, 0x0a,
	0x18, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x36, 0x0a, 0x0c, 0x61, 0x62, 0x62,
	0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x12, 0xfa, 0x42, 0x0f, 0x72, 0x0d, 0x10, 0x01, 0x18, 0x32, 0x32, 0x07, 0x2e, 0x2a, 0x5c, 0x53,
	0x2b, 0x2e, 0x2a, 0x52, 0x0c, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x34, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0xfa, 0x42, 0x0f, 0x72, 0x0d, 0x10, 0x01, 0x18,
	0x32, 0x32, 0x07, 0x2e, 0x2a, 0x5c, 0x53, 0x2b, 0x2e, 0x2a, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0xfa, 0x42, 0x0f, 0x72, 0x0d, 0x10, 0x01, 0x18,
	0x32, 0x32, 0x07, 0x2e, 0x2a, 0x5c, 0x53, 0x2b, 0x2e, 0x2a, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x22, 0xec, 0x01, 0x0a, 0x18, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x3b,
	0x0a, 0x0c, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0xfa, 0x42, 0x0f, 0x72, 0x0d, 0x10, 0x01, 0x18, 0x32, 0x32,
	0x07, 0x2e, 0x2a, 0x5c, 0x53, 0x2b, 0x2e, 0x2a, 0x48, 0x00, 0x52, 0x0c, 0x61, 0x62, 0x62, 0x72,
	0x65, 0x76, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x12, 0xfa, 0x42, 0x0f, 0x72, 0x0d, 0x10, 0x01, 0x18, 0x32, 0x32, 0x07, 0x2e, 0x2a, 0x5c,
	0x53, 0x2b, 0x2e, 0x2a, 0x48, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0xfa, 0x42, 0x0f, 0x72, 0x0d, 0x10, 0x01, 0x18, 0x32,
	0x32, 0x07, 0x2e, 0x2a, 0x5c, 0x53, 0x2b, 0x2e, 0x2a, 0x48, 0x02, 0x52, 0x05, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x61, 0x62, 0x62, 0x72, 0x65, 0x76,
	0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x42, 0x98, 0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_customer_v1_business_pet_code_defs_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_pet_code_defs_proto_rawDescData = file_moego_models_business_customer_v1_business_pet_code_defs_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_pet_code_defs_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_pet_code_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_pet_code_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_pet_code_defs_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_pet_code_defs_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_pet_code_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_business_customer_v1_business_pet_code_defs_proto_goTypes = []interface{}{
	(*BusinessPetCodeCreateDef)(nil), // 0: moego.models.business_customer.v1.BusinessPetCodeCreateDef
	(*BusinessPetCodeUpdateDef)(nil), // 1: moego.models.business_customer.v1.BusinessPetCodeUpdateDef
}
var file_moego_models_business_customer_v1_business_pet_code_defs_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_pet_code_defs_proto_init() }
func file_moego_models_business_customer_v1_business_pet_code_defs_proto_init() {
	if File_moego_models_business_customer_v1_business_pet_code_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_business_customer_v1_business_pet_code_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessPetCodeCreateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_pet_code_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessPetCodeUpdateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_business_customer_v1_business_pet_code_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_pet_code_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_pet_code_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_pet_code_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_business_customer_v1_business_pet_code_defs_proto_msgTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_pet_code_defs_proto = out.File
	file_moego_models_business_customer_v1_business_pet_code_defs_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_pet_code_defs_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_pet_code_defs_proto_depIdxs = nil
}
