// @since 2024-06-12 14:10:17
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/membership/v1/subscription_models.proto

package membershippb

import (
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/subscription/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	dayofweek "google.golang.org/genproto/googleapis/type/dayofweek"
	interval "google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Buyer status
type SubscriptionBuyerStatus int32

const (
	// default
	SubscriptionBuyerStatus_STATUS_UNSPECIFIED SubscriptionBuyerStatus = 0
	// in subscription
	SubscriptionBuyerStatus_IN_SUBSCRIPTION SubscriptionBuyerStatus = 1
	// cancelled
	SubscriptionBuyerStatus_CANCELLED SubscriptionBuyerStatus = 2
	// expired
	SubscriptionBuyerStatus_EXPIRED SubscriptionBuyerStatus = 3
	// pending
	SubscriptionBuyerStatus_PENDING SubscriptionBuyerStatus = 4
	// paused
	SubscriptionBuyerStatus_PAUSED SubscriptionBuyerStatus = 5
)

// Enum value maps for SubscriptionBuyerStatus.
var (
	SubscriptionBuyerStatus_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "IN_SUBSCRIPTION",
		2: "CANCELLED",
		3: "EXPIRED",
		4: "PENDING",
		5: "PAUSED",
	}
	SubscriptionBuyerStatus_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"IN_SUBSCRIPTION":    1,
		"CANCELLED":          2,
		"EXPIRED":            3,
		"PENDING":            4,
		"PAUSED":             5,
	}
)

func (x SubscriptionBuyerStatus) Enum() *SubscriptionBuyerStatus {
	p := new(SubscriptionBuyerStatus)
	*p = x
	return p
}

func (x SubscriptionBuyerStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubscriptionBuyerStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_membership_v1_subscription_models_proto_enumTypes[0].Descriptor()
}

func (SubscriptionBuyerStatus) Type() protoreflect.EnumType {
	return &file_moego_models_membership_v1_subscription_models_proto_enumTypes[0]
}

func (x SubscriptionBuyerStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubscriptionBuyerStatus.Descriptor instead.
func (SubscriptionBuyerStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_subscription_models_proto_rawDescGZIP(), []int{0}
}

// subscription status, the value should same to
// ref: `moego.models.subscription.v1.Subscription.Status`
type SubscriptionModel_Status int32

const (
	// status unspecified
	SubscriptionModel_STATUS_UNSPECIFIED SubscriptionModel_Status = 0
	// pending: waiting for the first charge result
	SubscriptionModel_PENDING SubscriptionModel_Status = 2
	// the subscription is charged and in validity period
	// cancel in validity period will not change the status,
	// just change the `cancel_at_period_end` value to `true`.
	SubscriptionModel_ACTIVE SubscriptionModel_Status = 3
	// manually cancelled and out of validity period
	SubscriptionModel_CANCELLED SubscriptionModel_Status = 4
	// paused
	SubscriptionModel_PAUSED SubscriptionModel_Status = 5
)

// Enum value maps for SubscriptionModel_Status.
var (
	SubscriptionModel_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		2: "PENDING",
		3: "ACTIVE",
		4: "CANCELLED",
		5: "PAUSED",
	}
	SubscriptionModel_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"PENDING":            2,
		"ACTIVE":             3,
		"CANCELLED":          4,
		"PAUSED":             5,
	}
)

func (x SubscriptionModel_Status) Enum() *SubscriptionModel_Status {
	p := new(SubscriptionModel_Status)
	*p = x
	return p
}

func (x SubscriptionModel_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubscriptionModel_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_membership_v1_subscription_models_proto_enumTypes[1].Descriptor()
}

func (SubscriptionModel_Status) Type() protoreflect.EnumType {
	return &file_moego_models_membership_v1_subscription_models_proto_enumTypes[1]
}

func (x SubscriptionModel_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubscriptionModel_Status.Descriptor instead.
func (SubscriptionModel_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_subscription_models_proto_rawDescGZIP(), []int{0, 0}
}

// Keys for sort
type PaymentHistoryItemFilter_Key int32

const (
	// default
	PaymentHistoryItemFilter_SORT_BY_UNSPECIFIED PaymentHistoryItemFilter_Key = 0
	// payment time
	PaymentHistoryItemFilter_SORT_BY_PAYMENT_TIME PaymentHistoryItemFilter_Key = 1
)

// Enum value maps for PaymentHistoryItemFilter_Key.
var (
	PaymentHistoryItemFilter_Key_name = map[int32]string{
		0: "SORT_BY_UNSPECIFIED",
		1: "SORT_BY_PAYMENT_TIME",
	}
	PaymentHistoryItemFilter_Key_value = map[string]int32{
		"SORT_BY_UNSPECIFIED":  0,
		"SORT_BY_PAYMENT_TIME": 1,
	}
)

func (x PaymentHistoryItemFilter_Key) Enum() *PaymentHistoryItemFilter_Key {
	p := new(PaymentHistoryItemFilter_Key)
	*p = x
	return p
}

func (x PaymentHistoryItemFilter_Key) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentHistoryItemFilter_Key) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_membership_v1_subscription_models_proto_enumTypes[2].Descriptor()
}

func (PaymentHistoryItemFilter_Key) Type() protoreflect.EnumType {
	return &file_moego_models_membership_v1_subscription_models_proto_enumTypes[2]
}

func (x PaymentHistoryItemFilter_Key) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentHistoryItemFilter_Key.Descriptor instead.
func (PaymentHistoryItemFilter_Key) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_subscription_models_proto_rawDescGZIP(), []int{6, 0}
}

// status_enum
type PaymentHistoryItemView_Status int32

const (
	// default
	PaymentHistoryItemView_PAYMENT_STATUS_UNSPECIFIED PaymentHistoryItemView_Status = 0
	// SUCCESS
	PaymentHistoryItemView_PAYMENT_SUCCESS PaymentHistoryItemView_Status = 1
	// FAILED
	PaymentHistoryItemView_PAYMENT_FAILED PaymentHistoryItemView_Status = 2
	// PENDING
	PaymentHistoryItemView_PAYMENT_PROCESSING PaymentHistoryItemView_Status = 3
)

// Enum value maps for PaymentHistoryItemView_Status.
var (
	PaymentHistoryItemView_Status_name = map[int32]string{
		0: "PAYMENT_STATUS_UNSPECIFIED",
		1: "PAYMENT_SUCCESS",
		2: "PAYMENT_FAILED",
		3: "PAYMENT_PROCESSING",
	}
	PaymentHistoryItemView_Status_value = map[string]int32{
		"PAYMENT_STATUS_UNSPECIFIED": 0,
		"PAYMENT_SUCCESS":            1,
		"PAYMENT_FAILED":             2,
		"PAYMENT_PROCESSING":         3,
	}
)

func (x PaymentHistoryItemView_Status) Enum() *PaymentHistoryItemView_Status {
	p := new(PaymentHistoryItemView_Status)
	*p = x
	return p
}

func (x PaymentHistoryItemView_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentHistoryItemView_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_membership_v1_subscription_models_proto_enumTypes[3].Descriptor()
}

func (PaymentHistoryItemView_Status) Type() protoreflect.EnumType {
	return &file_moego_models_membership_v1_subscription_models_proto_enumTypes[3]
}

func (x PaymentHistoryItemView_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentHistoryItemView_Status.Descriptor instead.
func (PaymentHistoryItemView_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_subscription_models_proto_rawDescGZIP(), []int{7, 0}
}

// The Subscription model
type SubscriptionModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// internal subscription id
	InternalSubscriptionId int64 `protobuf:"varint,2,opt,name=internal_subscription_id,json=internalSubscriptionId,proto3" json:"internal_subscription_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// membership id
	MembershipId int64 `protobuf:"varint,5,opt,name=membership_id,json=membershipId,proto3" json:"membership_id,omitempty"`
	// the price snapshot
	Price float64 `protobuf:"fixed64,6,opt,name=price,proto3" json:"price,omitempty"`
	// info from internal subscription
	// billing cycle
	//
	// Deprecated: Do not use.
	BillingCycle MembershipModel_BillingCycle `protobuf:"varint,10,opt,name=billing_cycle,json=billingCycle,proto3,enum=moego.models.membership.v1.MembershipModel_BillingCycle" json:"billing_cycle,omitempty"`
	// create business id
	BusinessId int64 `protobuf:"varint,11,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// billing cycle
	BillingCyclePeriod *v1.TimePeriod `protobuf:"bytes,12,opt,name=billing_cycle_period,json=billingCyclePeriod,proto3" json:"billing_cycle_period,omitempty"`
	// the create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// the update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// the delete time, non-null means is deleted
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=deleted_at,json=deletedAt,proto3,oneof" json:"deleted_at,omitempty"`
	// 最新的 card on file ID，即当前订阅的扣款卡
	LatestCardOnFileId string `protobuf:"bytes,16,opt,name=latest_card_on_file_id,json=latestCardOnFileId,proto3" json:"latest_card_on_file_id,omitempty"`
	// validity period
	ValidityPeriod *interval.Interval `protobuf:"bytes,17,opt,name=validity_period,json=validityPeriod,proto3" json:"validity_period,omitempty"`
	// next billing date
	NextBillingDate *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=next_billing_date,json=nextBillingDate,proto3" json:"next_billing_date,omitempty"`
	// expire date
	ExpiresAt *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	// cancelled but in active status
	CancelAtPeriodEnd bool `protobuf:"varint,20,opt,name=cancel_at_period_end,json=cancelAtPeriodEnd,proto3" json:"cancel_at_period_end,omitempty"`
	// sell link id
	SellLinkId *int64 `protobuf:"varint,21,opt,name=sell_link_id,json=sellLinkId,proto3,oneof" json:"sell_link_id,omitempty"`
	// membership revision
	MembershipRevision int32 `protobuf:"varint,22,opt,name=membership_revision,json=membershipRevision,proto3" json:"membership_revision,omitempty"`
	// status
	// use sub status instead
	//
	// Deprecated: Do not use.
	Status SubscriptionModel_Status `protobuf:"varint,23,opt,name=status,proto3,enum=moego.models.membership.v1.SubscriptionModel_Status" json:"status,omitempty"`
	// latest order id
	LatestOrderId int64 `protobuf:"varint,25,opt,name=latest_order_id,json=latestOrderId,proto3" json:"latest_order_id,omitempty"`
	// 暂停时间
	PausedAt *timestamppb.Timestamp `protobuf:"bytes,26,opt,name=paused_at,json=pausedAt,proto3" json:"paused_at,omitempty"`
	// 自动恢复时间
	AutoResumeAt *timestamppb.Timestamp `protobuf:"bytes,27,opt,name=auto_resume_at,json=autoResumeAt,proto3" json:"auto_resume_at,omitempty"`
	// 冗余暂停时的配置
	AutoResumeSetting *SubscriptionModel_AutoResumeSetting `protobuf:"bytes,28,opt,name=auto_resume_setting,json=autoResumeSetting,proto3" json:"auto_resume_setting,omitempty"`
	// subscription model 的status
	SubStatus v11.Subscription_Status `protobuf:"varint,29,opt,name=sub_status,json=subStatus,proto3,enum=moego.models.subscription.v1.Subscription_Status" json:"sub_status,omitempty"`
	// 取消原因，只有处于未激活状态的订阅才有效
	CancelReason string `protobuf:"bytes,30,opt,name=cancel_reason,json=cancelReason,proto3" json:"cancel_reason,omitempty"`
}

func (x *SubscriptionModel) Reset() {
	*x = SubscriptionModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionModel) ProtoMessage() {}

func (x *SubscriptionModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionModel.ProtoReflect.Descriptor instead.
func (*SubscriptionModel) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_subscription_models_proto_rawDescGZIP(), []int{0}
}

func (x *SubscriptionModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SubscriptionModel) GetInternalSubscriptionId() int64 {
	if x != nil {
		return x.InternalSubscriptionId
	}
	return 0
}

func (x *SubscriptionModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SubscriptionModel) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *SubscriptionModel) GetMembershipId() int64 {
	if x != nil {
		return x.MembershipId
	}
	return 0
}

func (x *SubscriptionModel) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

// Deprecated: Do not use.
func (x *SubscriptionModel) GetBillingCycle() MembershipModel_BillingCycle {
	if x != nil {
		return x.BillingCycle
	}
	return MembershipModel_BILLING_CYCLE_UNSPECIFIED
}

func (x *SubscriptionModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *SubscriptionModel) GetBillingCyclePeriod() *v1.TimePeriod {
	if x != nil {
		return x.BillingCyclePeriod
	}
	return nil
}

func (x *SubscriptionModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SubscriptionModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *SubscriptionModel) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *SubscriptionModel) GetLatestCardOnFileId() string {
	if x != nil {
		return x.LatestCardOnFileId
	}
	return ""
}

func (x *SubscriptionModel) GetValidityPeriod() *interval.Interval {
	if x != nil {
		return x.ValidityPeriod
	}
	return nil
}

func (x *SubscriptionModel) GetNextBillingDate() *timestamppb.Timestamp {
	if x != nil {
		return x.NextBillingDate
	}
	return nil
}

func (x *SubscriptionModel) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *SubscriptionModel) GetCancelAtPeriodEnd() bool {
	if x != nil {
		return x.CancelAtPeriodEnd
	}
	return false
}

func (x *SubscriptionModel) GetSellLinkId() int64 {
	if x != nil && x.SellLinkId != nil {
		return *x.SellLinkId
	}
	return 0
}

func (x *SubscriptionModel) GetMembershipRevision() int32 {
	if x != nil {
		return x.MembershipRevision
	}
	return 0
}

// Deprecated: Do not use.
func (x *SubscriptionModel) GetStatus() SubscriptionModel_Status {
	if x != nil {
		return x.Status
	}
	return SubscriptionModel_STATUS_UNSPECIFIED
}

func (x *SubscriptionModel) GetLatestOrderId() int64 {
	if x != nil {
		return x.LatestOrderId
	}
	return 0
}

func (x *SubscriptionModel) GetPausedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PausedAt
	}
	return nil
}

func (x *SubscriptionModel) GetAutoResumeAt() *timestamppb.Timestamp {
	if x != nil {
		return x.AutoResumeAt
	}
	return nil
}

func (x *SubscriptionModel) GetAutoResumeSetting() *SubscriptionModel_AutoResumeSetting {
	if x != nil {
		return x.AutoResumeSetting
	}
	return nil
}

func (x *SubscriptionModel) GetSubStatus() v11.Subscription_Status {
	if x != nil {
		return x.SubStatus
	}
	return v11.Subscription_Status(0)
}

func (x *SubscriptionModel) GetCancelReason() string {
	if x != nil {
		return x.CancelReason
	}
	return ""
}

// membership subscription composite
type MembershipSubscriptionModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// membership (latest)
	Membership *MembershipModel `protobuf:"bytes,1,opt,name=membership,proto3" json:"membership,omitempty"`
	// subscription (latest)
	Subscription *SubscriptionModel `protobuf:"bytes,2,opt,name=subscription,proto3" json:"subscription,omitempty"`
	// membership discount benefit
	MembershipDiscountBenefits *MembershipDiscountBenefitsDef `protobuf:"bytes,3,opt,name=membership_discount_benefits,json=membershipDiscountBenefits,proto3,oneof" json:"membership_discount_benefits,omitempty"`
	// user perk detail
	PerkDetail []*PerkDetail `protobuf:"bytes,4,rep,name=perk_detail,json=perkDetail,proto3" json:"perk_detail,omitempty"`
}

func (x *MembershipSubscriptionModel) Reset() {
	*x = MembershipSubscriptionModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MembershipSubscriptionModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MembershipSubscriptionModel) ProtoMessage() {}

func (x *MembershipSubscriptionModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MembershipSubscriptionModel.ProtoReflect.Descriptor instead.
func (*MembershipSubscriptionModel) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_subscription_models_proto_rawDescGZIP(), []int{1}
}

func (x *MembershipSubscriptionModel) GetMembership() *MembershipModel {
	if x != nil {
		return x.Membership
	}
	return nil
}

func (x *MembershipSubscriptionModel) GetSubscription() *SubscriptionModel {
	if x != nil {
		return x.Subscription
	}
	return nil
}

func (x *MembershipSubscriptionModel) GetMembershipDiscountBenefits() *MembershipDiscountBenefitsDef {
	if x != nil {
		return x.MembershipDiscountBenefits
	}
	return nil
}

func (x *MembershipSubscriptionModel) GetPerkDetail() []*PerkDetail {
	if x != nil {
		return x.PerkDetail
	}
	return nil
}

// the attachable model for expose customer membership icon
type MembershipSubscriptionListModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscriptions with membership
	MembershipSubscriptions []*MembershipSubscriptionModel `protobuf:"bytes,1,rep,name=membership_subscriptions,json=membershipSubscriptions,proto3" json:"membership_subscriptions,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *MembershipSubscriptionListModel) Reset() {
	*x = MembershipSubscriptionListModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MembershipSubscriptionListModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MembershipSubscriptionListModel) ProtoMessage() {}

func (x *MembershipSubscriptionListModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MembershipSubscriptionListModel.ProtoReflect.Descriptor instead.
func (*MembershipSubscriptionListModel) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_subscription_models_proto_rawDescGZIP(), []int{2}
}

func (x *MembershipSubscriptionListModel) GetMembershipSubscriptions() []*MembershipSubscriptionModel {
	if x != nil {
		return x.MembershipSubscriptions
	}
	return nil
}

func (x *MembershipSubscriptionListModel) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// The Subscription model
type SubscriptionModelPublicView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// membership id
	MembershipId int64 `protobuf:"varint,5,opt,name=membership_id,json=membershipId,proto3" json:"membership_id,omitempty"`
	// info from internal subscription
	// billing cycle
	//
	// Deprecated: Do not use.
	BillingCycle MembershipModel_BillingCycle `protobuf:"varint,10,opt,name=billing_cycle,json=billingCycle,proto3,enum=moego.models.membership.v1.MembershipModel_BillingCycle" json:"billing_cycle,omitempty"`
	// create business id
	BusinessId int64 `protobuf:"varint,11,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// billing cycle
	BillingCyclePeriod *v1.TimePeriod `protobuf:"bytes,12,opt,name=billing_cycle_period,json=billingCyclePeriod,proto3" json:"billing_cycle_period,omitempty"`
	// the create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// the update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// the delete time, non-null means is deleted
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=deleted_at,json=deletedAt,proto3,oneof" json:"deleted_at,omitempty"`
	// pricing
	Price float64 `protobuf:"fixed64,16,opt,name=price,proto3" json:"price,omitempty"`
	// validity period
	ValidityPeriod *interval.Interval `protobuf:"bytes,17,opt,name=validity_period,json=validityPeriod,proto3" json:"validity_period,omitempty"`
	// next billing date
	NextBillingDate *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=next_billing_date,json=nextBillingDate,proto3" json:"next_billing_date,omitempty"`
	// expire date
	ExpiresAt *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	// cancelled but in active status
	CancelAtPeriodEnd bool `protobuf:"varint,20,opt,name=cancel_at_period_end,json=cancelAtPeriodEnd,proto3" json:"cancel_at_period_end,omitempty"`
	// sell link id
	SellLinkId *int64 `protobuf:"varint,21,opt,name=sell_link_id,json=sellLinkId,proto3,oneof" json:"sell_link_id,omitempty"`
	// membership revision
	MembershipRevision int32 `protobuf:"varint,22,opt,name=membership_revision,json=membershipRevision,proto3" json:"membership_revision,omitempty"`
	// status
	Status SubscriptionModel_Status `protobuf:"varint,23,opt,name=status,proto3,enum=moego.models.membership.v1.SubscriptionModel_Status" json:"status,omitempty"`
	// paused at
	PausedAt *timestamppb.Timestamp `protobuf:"bytes,25,opt,name=paused_at,json=pausedAt,proto3" json:"paused_at,omitempty"`
	// auto resume at
	AutoResumeAt *timestamppb.Timestamp `protobuf:"bytes,26,opt,name=auto_resume_at,json=autoResumeAt,proto3" json:"auto_resume_at,omitempty"`
	// internal subscription id
	InternalSubscriptionId int64 `protobuf:"varint,27,opt,name=internal_subscription_id,json=internalSubscriptionId,proto3" json:"internal_subscription_id,omitempty"`
}

func (x *SubscriptionModelPublicView) Reset() {
	*x = SubscriptionModelPublicView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionModelPublicView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionModelPublicView) ProtoMessage() {}

func (x *SubscriptionModelPublicView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionModelPublicView.ProtoReflect.Descriptor instead.
func (*SubscriptionModelPublicView) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_subscription_models_proto_rawDescGZIP(), []int{3}
}

func (x *SubscriptionModelPublicView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SubscriptionModelPublicView) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SubscriptionModelPublicView) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *SubscriptionModelPublicView) GetMembershipId() int64 {
	if x != nil {
		return x.MembershipId
	}
	return 0
}

// Deprecated: Do not use.
func (x *SubscriptionModelPublicView) GetBillingCycle() MembershipModel_BillingCycle {
	if x != nil {
		return x.BillingCycle
	}
	return MembershipModel_BILLING_CYCLE_UNSPECIFIED
}

func (x *SubscriptionModelPublicView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *SubscriptionModelPublicView) GetBillingCyclePeriod() *v1.TimePeriod {
	if x != nil {
		return x.BillingCyclePeriod
	}
	return nil
}

func (x *SubscriptionModelPublicView) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SubscriptionModelPublicView) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *SubscriptionModelPublicView) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *SubscriptionModelPublicView) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *SubscriptionModelPublicView) GetValidityPeriod() *interval.Interval {
	if x != nil {
		return x.ValidityPeriod
	}
	return nil
}

func (x *SubscriptionModelPublicView) GetNextBillingDate() *timestamppb.Timestamp {
	if x != nil {
		return x.NextBillingDate
	}
	return nil
}

func (x *SubscriptionModelPublicView) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *SubscriptionModelPublicView) GetCancelAtPeriodEnd() bool {
	if x != nil {
		return x.CancelAtPeriodEnd
	}
	return false
}

func (x *SubscriptionModelPublicView) GetSellLinkId() int64 {
	if x != nil && x.SellLinkId != nil {
		return *x.SellLinkId
	}
	return 0
}

func (x *SubscriptionModelPublicView) GetMembershipRevision() int32 {
	if x != nil {
		return x.MembershipRevision
	}
	return 0
}

func (x *SubscriptionModelPublicView) GetStatus() SubscriptionModel_Status {
	if x != nil {
		return x.Status
	}
	return SubscriptionModel_STATUS_UNSPECIFIED
}

func (x *SubscriptionModelPublicView) GetPausedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PausedAt
	}
	return nil
}

func (x *SubscriptionModelPublicView) GetAutoResumeAt() *timestamppb.Timestamp {
	if x != nil {
		return x.AutoResumeAt
	}
	return nil
}

func (x *SubscriptionModelPublicView) GetInternalSubscriptionId() int64 {
	if x != nil {
		return x.InternalSubscriptionId
	}
	return 0
}

// the membership buyer model
type SubscriptionBuyerView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the subscription id
	SubscriptionId int64 `protobuf:"varint,1,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	// buyer (customer) info
	Buyer *v12.CustomerModelNameView `protobuf:"bytes,2,opt,name=buyer,proto3" json:"buyer,omitempty"`
	// seller (staff) info
	Seller *v13.StaffModel `protobuf:"bytes,3,opt,name=seller,proto3,oneof" json:"seller,omitempty"`
	// start date
	StartDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date (also Next billing date)
	EndDate *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// last redeem date
	LastRedeemDate *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=last_redeem_date,json=lastRedeemDate,proto3" json:"last_redeem_date,omitempty"`
	// total sales
	TotalSales *money.Money `protobuf:"bytes,7,opt,name=total_sales,json=totalSales,proto3" json:"total_sales,omitempty"`
	// status
	//
	// Deprecated: Do not use.
	Status SubscriptionBuyerStatus `protobuf:"varint,8,opt,name=status,proto3,enum=moego.models.membership.v1.SubscriptionBuyerStatus" json:"status,omitempty"`
	// invoice id
	InvoiceId int64 `protobuf:"varint,9,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
	// paused at
	PausedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=paused_at,json=pausedAt,proto3" json:"paused_at,omitempty"`
	// auto_resume_at
	AutoResumeAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=auto_resume_at,json=autoResumeAt,proto3" json:"auto_resume_at,omitempty"`
	// purchase history total price
	PurchaseHistoryTotalPrice *money.Money `protobuf:"bytes,12,opt,name=purchase_history_total_price,json=purchaseHistoryTotalPrice,proto3" json:"purchase_history_total_price,omitempty"`
	// internal subscription id
	InternalSubscriptionId int64 `protobuf:"varint,13,opt,name=internal_subscription_id,json=internalSubscriptionId,proto3" json:"internal_subscription_id,omitempty"`
	// internal subscription status
	InternalSubscriptionStatus v11.Subscription_Status `protobuf:"varint,14,opt,name=internal_subscription_status,json=internalSubscriptionStatus,proto3,enum=moego.models.subscription.v1.Subscription_Status" json:"internal_subscription_status,omitempty"`
}

func (x *SubscriptionBuyerView) Reset() {
	*x = SubscriptionBuyerView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionBuyerView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionBuyerView) ProtoMessage() {}

func (x *SubscriptionBuyerView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionBuyerView.ProtoReflect.Descriptor instead.
func (*SubscriptionBuyerView) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_subscription_models_proto_rawDescGZIP(), []int{4}
}

func (x *SubscriptionBuyerView) GetSubscriptionId() int64 {
	if x != nil {
		return x.SubscriptionId
	}
	return 0
}

func (x *SubscriptionBuyerView) GetBuyer() *v12.CustomerModelNameView {
	if x != nil {
		return x.Buyer
	}
	return nil
}

func (x *SubscriptionBuyerView) GetSeller() *v13.StaffModel {
	if x != nil {
		return x.Seller
	}
	return nil
}

func (x *SubscriptionBuyerView) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *SubscriptionBuyerView) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *SubscriptionBuyerView) GetLastRedeemDate() *timestamppb.Timestamp {
	if x != nil {
		return x.LastRedeemDate
	}
	return nil
}

func (x *SubscriptionBuyerView) GetTotalSales() *money.Money {
	if x != nil {
		return x.TotalSales
	}
	return nil
}

// Deprecated: Do not use.
func (x *SubscriptionBuyerView) GetStatus() SubscriptionBuyerStatus {
	if x != nil {
		return x.Status
	}
	return SubscriptionBuyerStatus_STATUS_UNSPECIFIED
}

func (x *SubscriptionBuyerView) GetInvoiceId() int64 {
	if x != nil {
		return x.InvoiceId
	}
	return 0
}

func (x *SubscriptionBuyerView) GetPausedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PausedAt
	}
	return nil
}

func (x *SubscriptionBuyerView) GetAutoResumeAt() *timestamppb.Timestamp {
	if x != nil {
		return x.AutoResumeAt
	}
	return nil
}

func (x *SubscriptionBuyerView) GetPurchaseHistoryTotalPrice() *money.Money {
	if x != nil {
		return x.PurchaseHistoryTotalPrice
	}
	return nil
}

func (x *SubscriptionBuyerView) GetInternalSubscriptionId() int64 {
	if x != nil {
		return x.InternalSubscriptionId
	}
	return 0
}

func (x *SubscriptionBuyerView) GetInternalSubscriptionStatus() v11.Subscription_Status {
	if x != nil {
		return x.InternalSubscriptionStatus
	}
	return v11.Subscription_Status(0)
}

// the subscription buyer list filter
type SubscriptionBuyerListFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status
	Status *SubscriptionBuyerStatus `protobuf:"varint,1,opt,name=status,proto3,enum=moego.models.membership.v1.SubscriptionBuyerStatus,oneof" json:"status,omitempty"`
	// buyer name keyword
	MembershipNameKeyword *string `protobuf:"bytes,2,opt,name=membership_name_keyword,json=membershipNameKeyword,proto3,oneof" json:"membership_name_keyword,omitempty"`
	// status list
	Statuses []SubscriptionBuyerStatus `protobuf:"varint,3,rep,packed,name=statuses,proto3,enum=moego.models.membership.v1.SubscriptionBuyerStatus" json:"statuses,omitempty"`
}

func (x *SubscriptionBuyerListFilter) Reset() {
	*x = SubscriptionBuyerListFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionBuyerListFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionBuyerListFilter) ProtoMessage() {}

func (x *SubscriptionBuyerListFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionBuyerListFilter.ProtoReflect.Descriptor instead.
func (*SubscriptionBuyerListFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_subscription_models_proto_rawDescGZIP(), []int{5}
}

func (x *SubscriptionBuyerListFilter) GetStatus() SubscriptionBuyerStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return SubscriptionBuyerStatus_STATUS_UNSPECIFIED
}

func (x *SubscriptionBuyerListFilter) GetMembershipNameKeyword() string {
	if x != nil && x.MembershipNameKeyword != nil {
		return *x.MembershipNameKeyword
	}
	return ""
}

func (x *SubscriptionBuyerListFilter) GetStatuses() []SubscriptionBuyerStatus {
	if x != nil {
		return x.Statuses
	}
	return nil
}

// the payment history item filter
type PaymentHistoryItemFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the subscription id, internal subscription id
	SubscriptionId int64 `protobuf:"varint,1,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	// sort by
	SortBy *PaymentHistoryItemFilter_Key `protobuf:"varint,2,opt,name=sort_by,json=sortBy,proto3,enum=moego.models.membership.v1.PaymentHistoryItemFilter_Key,oneof" json:"sort_by,omitempty"`
	// sort order
	IsDesc *bool `protobuf:"varint,3,opt,name=is_desc,json=isDesc,proto3,oneof" json:"is_desc,omitempty"`
	// company id 兼容 c app 接口无法通过gauth获取company id
	CompanyId *int64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,5,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
}

func (x *PaymentHistoryItemFilter) Reset() {
	*x = PaymentHistoryItemFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentHistoryItemFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentHistoryItemFilter) ProtoMessage() {}

func (x *PaymentHistoryItemFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentHistoryItemFilter.ProtoReflect.Descriptor instead.
func (*PaymentHistoryItemFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_subscription_models_proto_rawDescGZIP(), []int{6}
}

func (x *PaymentHistoryItemFilter) GetSubscriptionId() int64 {
	if x != nil {
		return x.SubscriptionId
	}
	return 0
}

func (x *PaymentHistoryItemFilter) GetSortBy() PaymentHistoryItemFilter_Key {
	if x != nil && x.SortBy != nil {
		return *x.SortBy
	}
	return PaymentHistoryItemFilter_SORT_BY_UNSPECIFIED
}

func (x *PaymentHistoryItemFilter) GetIsDesc() bool {
	if x != nil && x.IsDesc != nil {
		return *x.IsDesc
	}
	return false
}

func (x *PaymentHistoryItemFilter) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *PaymentHistoryItemFilter) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

// payment history item
type PaymentHistoryItemView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the invoice id
	InvoiceId int64 `protobuf:"varint,1,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
	// the payment time
	PaymentTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=payment_time,json=paymentTime,proto3" json:"payment_time,omitempty"`
	// the payment amount
	PaymentAmount *money.Money `protobuf:"bytes,3,opt,name=payment_amount,json=paymentAmount,proto3" json:"payment_amount,omitempty"`
	// payment method
	PaymentMethod string `protobuf:"bytes,4,opt,name=payment_method,json=paymentMethod,proto3" json:"payment_method,omitempty"`
	// status
	Status PaymentHistoryItemView_Status `protobuf:"varint,5,opt,name=status,proto3,enum=moego.models.membership.v1.PaymentHistoryItemView_Status" json:"status,omitempty"`
}

func (x *PaymentHistoryItemView) Reset() {
	*x = PaymentHistoryItemView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentHistoryItemView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentHistoryItemView) ProtoMessage() {}

func (x *PaymentHistoryItemView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentHistoryItemView.ProtoReflect.Descriptor instead.
func (*PaymentHistoryItemView) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_subscription_models_proto_rawDescGZIP(), []int{7}
}

func (x *PaymentHistoryItemView) GetInvoiceId() int64 {
	if x != nil {
		return x.InvoiceId
	}
	return 0
}

func (x *PaymentHistoryItemView) GetPaymentTime() *timestamppb.Timestamp {
	if x != nil {
		return x.PaymentTime
	}
	return nil
}

func (x *PaymentHistoryItemView) GetPaymentAmount() *money.Money {
	if x != nil {
		return x.PaymentAmount
	}
	return nil
}

func (x *PaymentHistoryItemView) GetPaymentMethod() string {
	if x != nil {
		return x.PaymentMethod
	}
	return ""
}

func (x *PaymentHistoryItemView) GetStatus() PaymentHistoryItemView_Status {
	if x != nil {
		return x.Status
	}
	return PaymentHistoryItemView_PAYMENT_STATUS_UNSPECIFIED
}

// ob request setting model
type OBRequestSettingModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription id
	SubscriptionId int64 `protobuf:"varint,1,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// day of week
	DaysOfWeek []dayofweek.DayOfWeek `protobuf:"varint,4,rep,packed,name=days_of_week,json=daysOfWeek,proto3,enum=google.type.DayOfWeek" json:"days_of_week,omitempty"`
	// pet id, 旧数据为0, 表示所有 pet
	PetId int64 `protobuf:"varint,5,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
}

func (x *OBRequestSettingModel) Reset() {
	*x = OBRequestSettingModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OBRequestSettingModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OBRequestSettingModel) ProtoMessage() {}

func (x *OBRequestSettingModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OBRequestSettingModel.ProtoReflect.Descriptor instead.
func (*OBRequestSettingModel) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_subscription_models_proto_rawDescGZIP(), []int{8}
}

func (x *OBRequestSettingModel) GetSubscriptionId() int64 {
	if x != nil {
		return x.SubscriptionId
	}
	return 0
}

func (x *OBRequestSettingModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *OBRequestSettingModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *OBRequestSettingModel) GetDaysOfWeek() []dayofweek.DayOfWeek {
	if x != nil {
		return x.DaysOfWeek
	}
	return nil
}

func (x *OBRequestSettingModel) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

// 自动恢复订阅设置，仅对暂停状态的订阅有效
type SubscriptionModel_AutoResumeSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 配置
	//
	// Types that are assignable to Setting:
	//
	//	*SubscriptionModel_AutoResumeSetting_NumOfBillingCycle
	//	*SubscriptionModel_AutoResumeSetting_Date
	Setting isSubscriptionModel_AutoResumeSetting_Setting `protobuf_oneof:"setting"`
}

func (x *SubscriptionModel_AutoResumeSetting) Reset() {
	*x = SubscriptionModel_AutoResumeSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionModel_AutoResumeSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionModel_AutoResumeSetting) ProtoMessage() {}

func (x *SubscriptionModel_AutoResumeSetting) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_subscription_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionModel_AutoResumeSetting.ProtoReflect.Descriptor instead.
func (*SubscriptionModel_AutoResumeSetting) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_subscription_models_proto_rawDescGZIP(), []int{0, 0}
}

func (m *SubscriptionModel_AutoResumeSetting) GetSetting() isSubscriptionModel_AutoResumeSetting_Setting {
	if m != nil {
		return m.Setting
	}
	return nil
}

func (x *SubscriptionModel_AutoResumeSetting) GetNumOfBillingCycle() int64 {
	if x, ok := x.GetSetting().(*SubscriptionModel_AutoResumeSetting_NumOfBillingCycle); ok {
		return x.NumOfBillingCycle
	}
	return 0
}

func (x *SubscriptionModel_AutoResumeSetting) GetDate() *timestamppb.Timestamp {
	if x, ok := x.GetSetting().(*SubscriptionModel_AutoResumeSetting_Date); ok {
		return x.Date
	}
	return nil
}

type isSubscriptionModel_AutoResumeSetting_Setting interface {
	isSubscriptionModel_AutoResumeSetting_Setting()
}

type SubscriptionModel_AutoResumeSetting_NumOfBillingCycle struct {
	// 暂停产品周期的整数倍
	NumOfBillingCycle int64 `protobuf:"varint,1,opt,name=num_of_billing_cycle,json=numOfBillingCycle,proto3,oneof"`
}

type SubscriptionModel_AutoResumeSetting_Date struct {
	// 暂停到指定日期
	Date *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=date,proto3,oneof"`
}

func (*SubscriptionModel_AutoResumeSetting_NumOfBillingCycle) isSubscriptionModel_AutoResumeSetting_Setting() {
}

func (*SubscriptionModel_AutoResumeSetting_Date) isSubscriptionModel_AutoResumeSetting_Setting() {}

var File_moego_models_membership_v1_subscription_models_proto protoreflect.FileDescriptor

var file_moego_models_membership_v1_subscription_models_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x64, 0x61, 0x79, 0x6f, 0x66, 0x77, 0x65, 0x65, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76,
	0x31, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x64, 0x65, 0x66,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xb9, 0x0d, 0x0a, 0x11, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x38, 0x0a, 0x18, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x16, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x61, 0x0a, 0x0d, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x0c, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x4c, 0x0a, 0x14, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x12, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x3e, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x32, 0x0a, 0x16, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4f,
	0x6e, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x0e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74,
	0x79, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x46, 0x0a, 0x11, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f,
	0x6e, 0x65, 0x78, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x39, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x41, 0x74, 0x12, 0x2f, 0x0a, 0x14, 0x63, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x61, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x65,
	0x6e, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x41, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x45, 0x6e, 0x64, 0x12, 0x25, 0x0a, 0x0c, 0x73,
	0x65, 0x6c, 0x6c, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x01, 0x52, 0x0a, 0x73, 0x65, 0x6c, 0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x2f, 0x0a, 0x13, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x5f, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x12, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x52, 0x65, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x37, 0x0a,
	0x09, 0x70, 0x61, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x70, 0x61,
	0x75, 0x73, 0x65, 0x64, 0x41, 0x74, 0x12, 0x40, 0x0a, 0x0e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72,
	0x65, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x6f,
	0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x74, 0x12, 0x6f, 0x0a, 0x13, 0x61, 0x75, 0x74, 0x6f,
	0x5f, 0x72, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x73, 0x75,
	0x6d, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x50, 0x0a, 0x0a, 0x73, 0x75, 0x62,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x09, 0x73, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x1a, 0x83, 0x01, 0x0a, 0x11, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x31, 0x0a, 0x14, 0x6e, 0x75, 0x6d, 0x5f, 0x6f, 0x66,
	0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x11, 0x6e, 0x75, 0x6d, 0x4f, 0x66, 0x42, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x54, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10,
	0x03, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x04,
	0x12, 0x0a, 0x0a, 0x06, 0x50, 0x41, 0x55, 0x53, 0x45, 0x44, 0x10, 0x05, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f,
	0x73, 0x65, 0x6c, 0x6c, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x22, 0xaa, 0x03, 0x0a,
	0x1b, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x4b, 0x0a, 0x0a,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x12, 0x51, 0x0a, 0x0c, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x80, 0x01, 0x0a,
	0x1c, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x44, 0x65, 0x66, 0x48, 0x00,
	0x52, 0x1a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x88, 0x01, 0x01, 0x12,
	0x47, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6b, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x65, 0x72, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0a, 0x70, 0x65,
	0x72, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x1f, 0x0a, 0x1d, 0x5f, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x22, 0xd9, 0x01, 0x0a, 0x1f, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x72, 0x0a,
	0x18, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x17, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9f, 0x09, 0x0a, 0x1b, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x63, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x49, 0x64, 0x12, 0x61, 0x0a, 0x0d, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x42,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x0c, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x4c,
	0x0a, 0x14, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f,
	0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x12, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x3e, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x48, 0x00, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x3e, 0x0a, 0x0f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x0e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69,
	0x74, 0x79, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x46, 0x0a, 0x11, 0x6e, 0x65, 0x78, 0x74,
	0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0f, 0x6e, 0x65, 0x78, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x39, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x41, 0x74, 0x12, 0x2f, 0x0a, 0x14, 0x63,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x61, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f,
	0x65, 0x6e, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x63, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x41, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x45, 0x6e, 0x64, 0x12, 0x25, 0x0a, 0x0c,
	0x73, 0x65, 0x6c, 0x6c, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x01, 0x52, 0x0a, 0x73, 0x65, 0x6c, 0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x13, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x12, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x52, 0x65, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x4c, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x37, 0x0a, 0x09, 0x70, 0x61, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x08, 0x70, 0x61, 0x75, 0x73, 0x65, 0x64, 0x41, 0x74, 0x12, 0x40, 0x0a, 0x0e, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x1a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0c, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x74, 0x12, 0x38, 0x0a,
	0x18, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x16, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x6c, 0x6c, 0x5f,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x22, 0xb5, 0x07, 0x0a, 0x15, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x75, 0x79, 0x65, 0x72, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x05, 0x62, 0x75,
	0x79, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x05, 0x62, 0x75, 0x79, 0x65,
	0x72, 0x12, 0x45, 0x0a, 0x06, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x06, 0x73,
	0x65, 0x6c, 0x6c, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x44, 0x0a, 0x10, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x33, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x53, 0x61, 0x6c, 0x65, 0x73, 0x12, 0x4f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x75, 0x79, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x09, 0x70, 0x61, 0x75, 0x73, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x70, 0x61, 0x75, 0x73, 0x65, 0x64, 0x41, 0x74, 0x12, 0x40,
	0x0a, 0x0e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x61, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x74,
	0x12, 0x53, 0x0a, 0x1c, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x68, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x19, 0x70, 0x75, 0x72, 0x63,
	0x68, 0x61, 0x73, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x38, 0x0a, 0x18, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x16, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x73, 0x0a, 0x1c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x1a, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x22,
	0xa4, 0x02, 0x0a, 0x1b, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x75, 0x79, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x50, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x75, 0x79, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01,
	0x01, 0x12, 0x3b, 0x0a, 0x17, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x01, 0x52, 0x15, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4f,
	0x0a, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x75, 0x79, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x42,
	0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x22, 0xf4, 0x02, 0x0a, 0x18, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x56, 0x0a, 0x07,
	0x73, 0x6f, 0x72, 0x74, 0x5f, 0x62, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x2e, 0x4b, 0x65, 0x79, 0x48, 0x00, 0x52, 0x06, 0x73, 0x6f, 0x72, 0x74, 0x42,
	0x79, 0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x06, 0x69, 0x73, 0x44, 0x65, 0x73, 0x63, 0x88,
	0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x48, 0x03, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x22, 0x38, 0x0a, 0x03,
	0x4b, 0x65, 0x79, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x4f, 0x52, 0x54, 0x5f, 0x42, 0x59, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14,
	0x53, 0x4f, 0x52, 0x54, 0x5f, 0x42, 0x59, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x54, 0x49, 0x4d, 0x45, 0x10, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x73, 0x6f, 0x72, 0x74, 0x5f,
	0x62, 0x79, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0x96, 0x03,
	0x0a, 0x16, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x49, 0x74, 0x65, 0x6d, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x6e,
	0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x51, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x69, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x16,
	0x0a, 0x12, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x22, 0xd1, 0x01, 0x0a, 0x15, 0x4f, 0x42, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x27, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x0c, 0x64, 0x61, 0x79,
	0x73, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x0a, 0x64, 0x61, 0x79, 0x73, 0x4f, 0x66, 0x57,
	0x65, 0x65, 0x6b, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x2a, 0x7b, 0x0a, 0x17, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x75, 0x79, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a,
	0x0f, 0x49, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10,
	0x02, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0b,
	0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x50,
	0x41, 0x55, 0x53, 0x45, 0x44, 0x10, 0x05, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76,
	0x31, 0x3b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_membership_v1_subscription_models_proto_rawDescOnce sync.Once
	file_moego_models_membership_v1_subscription_models_proto_rawDescData = file_moego_models_membership_v1_subscription_models_proto_rawDesc
)

func file_moego_models_membership_v1_subscription_models_proto_rawDescGZIP() []byte {
	file_moego_models_membership_v1_subscription_models_proto_rawDescOnce.Do(func() {
		file_moego_models_membership_v1_subscription_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_membership_v1_subscription_models_proto_rawDescData)
	})
	return file_moego_models_membership_v1_subscription_models_proto_rawDescData
}

var file_moego_models_membership_v1_subscription_models_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_moego_models_membership_v1_subscription_models_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_models_membership_v1_subscription_models_proto_goTypes = []interface{}{
	(SubscriptionBuyerStatus)(0),                // 0: moego.models.membership.v1.SubscriptionBuyerStatus
	(SubscriptionModel_Status)(0),               // 1: moego.models.membership.v1.SubscriptionModel.Status
	(PaymentHistoryItemFilter_Key)(0),           // 2: moego.models.membership.v1.PaymentHistoryItemFilter.Key
	(PaymentHistoryItemView_Status)(0),          // 3: moego.models.membership.v1.PaymentHistoryItemView.Status
	(*SubscriptionModel)(nil),                   // 4: moego.models.membership.v1.SubscriptionModel
	(*MembershipSubscriptionModel)(nil),         // 5: moego.models.membership.v1.MembershipSubscriptionModel
	(*MembershipSubscriptionListModel)(nil),     // 6: moego.models.membership.v1.MembershipSubscriptionListModel
	(*SubscriptionModelPublicView)(nil),         // 7: moego.models.membership.v1.SubscriptionModelPublicView
	(*SubscriptionBuyerView)(nil),               // 8: moego.models.membership.v1.SubscriptionBuyerView
	(*SubscriptionBuyerListFilter)(nil),         // 9: moego.models.membership.v1.SubscriptionBuyerListFilter
	(*PaymentHistoryItemFilter)(nil),            // 10: moego.models.membership.v1.PaymentHistoryItemFilter
	(*PaymentHistoryItemView)(nil),              // 11: moego.models.membership.v1.PaymentHistoryItemView
	(*OBRequestSettingModel)(nil),               // 12: moego.models.membership.v1.OBRequestSettingModel
	(*SubscriptionModel_AutoResumeSetting)(nil), // 13: moego.models.membership.v1.SubscriptionModel.AutoResumeSetting
	(MembershipModel_BillingCycle)(0),           // 14: moego.models.membership.v1.MembershipModel.BillingCycle
	(*v1.TimePeriod)(nil),                       // 15: moego.utils.v1.TimePeriod
	(*timestamppb.Timestamp)(nil),               // 16: google.protobuf.Timestamp
	(*interval.Interval)(nil),                   // 17: google.type.Interval
	(v11.Subscription_Status)(0),                // 18: moego.models.subscription.v1.Subscription.Status
	(*MembershipModel)(nil),                     // 19: moego.models.membership.v1.MembershipModel
	(*MembershipDiscountBenefitsDef)(nil),       // 20: moego.models.membership.v1.MembershipDiscountBenefitsDef
	(*PerkDetail)(nil),                          // 21: moego.models.membership.v1.PerkDetail
	(*v2.PaginationResponse)(nil),               // 22: moego.utils.v2.PaginationResponse
	(*v12.CustomerModelNameView)(nil),           // 23: moego.models.customer.v1.CustomerModelNameView
	(*v13.StaffModel)(nil),                      // 24: moego.models.organization.v1.StaffModel
	(*money.Money)(nil),                         // 25: google.type.Money
	(dayofweek.DayOfWeek)(0),                    // 26: google.type.DayOfWeek
}
var file_moego_models_membership_v1_subscription_models_proto_depIdxs = []int32{
	14, // 0: moego.models.membership.v1.SubscriptionModel.billing_cycle:type_name -> moego.models.membership.v1.MembershipModel.BillingCycle
	15, // 1: moego.models.membership.v1.SubscriptionModel.billing_cycle_period:type_name -> moego.utils.v1.TimePeriod
	16, // 2: moego.models.membership.v1.SubscriptionModel.created_at:type_name -> google.protobuf.Timestamp
	16, // 3: moego.models.membership.v1.SubscriptionModel.updated_at:type_name -> google.protobuf.Timestamp
	16, // 4: moego.models.membership.v1.SubscriptionModel.deleted_at:type_name -> google.protobuf.Timestamp
	17, // 5: moego.models.membership.v1.SubscriptionModel.validity_period:type_name -> google.type.Interval
	16, // 6: moego.models.membership.v1.SubscriptionModel.next_billing_date:type_name -> google.protobuf.Timestamp
	16, // 7: moego.models.membership.v1.SubscriptionModel.expires_at:type_name -> google.protobuf.Timestamp
	1,  // 8: moego.models.membership.v1.SubscriptionModel.status:type_name -> moego.models.membership.v1.SubscriptionModel.Status
	16, // 9: moego.models.membership.v1.SubscriptionModel.paused_at:type_name -> google.protobuf.Timestamp
	16, // 10: moego.models.membership.v1.SubscriptionModel.auto_resume_at:type_name -> google.protobuf.Timestamp
	13, // 11: moego.models.membership.v1.SubscriptionModel.auto_resume_setting:type_name -> moego.models.membership.v1.SubscriptionModel.AutoResumeSetting
	18, // 12: moego.models.membership.v1.SubscriptionModel.sub_status:type_name -> moego.models.subscription.v1.Subscription.Status
	19, // 13: moego.models.membership.v1.MembershipSubscriptionModel.membership:type_name -> moego.models.membership.v1.MembershipModel
	4,  // 14: moego.models.membership.v1.MembershipSubscriptionModel.subscription:type_name -> moego.models.membership.v1.SubscriptionModel
	20, // 15: moego.models.membership.v1.MembershipSubscriptionModel.membership_discount_benefits:type_name -> moego.models.membership.v1.MembershipDiscountBenefitsDef
	21, // 16: moego.models.membership.v1.MembershipSubscriptionModel.perk_detail:type_name -> moego.models.membership.v1.PerkDetail
	5,  // 17: moego.models.membership.v1.MembershipSubscriptionListModel.membership_subscriptions:type_name -> moego.models.membership.v1.MembershipSubscriptionModel
	22, // 18: moego.models.membership.v1.MembershipSubscriptionListModel.pagination:type_name -> moego.utils.v2.PaginationResponse
	14, // 19: moego.models.membership.v1.SubscriptionModelPublicView.billing_cycle:type_name -> moego.models.membership.v1.MembershipModel.BillingCycle
	15, // 20: moego.models.membership.v1.SubscriptionModelPublicView.billing_cycle_period:type_name -> moego.utils.v1.TimePeriod
	16, // 21: moego.models.membership.v1.SubscriptionModelPublicView.created_at:type_name -> google.protobuf.Timestamp
	16, // 22: moego.models.membership.v1.SubscriptionModelPublicView.updated_at:type_name -> google.protobuf.Timestamp
	16, // 23: moego.models.membership.v1.SubscriptionModelPublicView.deleted_at:type_name -> google.protobuf.Timestamp
	17, // 24: moego.models.membership.v1.SubscriptionModelPublicView.validity_period:type_name -> google.type.Interval
	16, // 25: moego.models.membership.v1.SubscriptionModelPublicView.next_billing_date:type_name -> google.protobuf.Timestamp
	16, // 26: moego.models.membership.v1.SubscriptionModelPublicView.expires_at:type_name -> google.protobuf.Timestamp
	1,  // 27: moego.models.membership.v1.SubscriptionModelPublicView.status:type_name -> moego.models.membership.v1.SubscriptionModel.Status
	16, // 28: moego.models.membership.v1.SubscriptionModelPublicView.paused_at:type_name -> google.protobuf.Timestamp
	16, // 29: moego.models.membership.v1.SubscriptionModelPublicView.auto_resume_at:type_name -> google.protobuf.Timestamp
	23, // 30: moego.models.membership.v1.SubscriptionBuyerView.buyer:type_name -> moego.models.customer.v1.CustomerModelNameView
	24, // 31: moego.models.membership.v1.SubscriptionBuyerView.seller:type_name -> moego.models.organization.v1.StaffModel
	16, // 32: moego.models.membership.v1.SubscriptionBuyerView.start_date:type_name -> google.protobuf.Timestamp
	16, // 33: moego.models.membership.v1.SubscriptionBuyerView.end_date:type_name -> google.protobuf.Timestamp
	16, // 34: moego.models.membership.v1.SubscriptionBuyerView.last_redeem_date:type_name -> google.protobuf.Timestamp
	25, // 35: moego.models.membership.v1.SubscriptionBuyerView.total_sales:type_name -> google.type.Money
	0,  // 36: moego.models.membership.v1.SubscriptionBuyerView.status:type_name -> moego.models.membership.v1.SubscriptionBuyerStatus
	16, // 37: moego.models.membership.v1.SubscriptionBuyerView.paused_at:type_name -> google.protobuf.Timestamp
	16, // 38: moego.models.membership.v1.SubscriptionBuyerView.auto_resume_at:type_name -> google.protobuf.Timestamp
	25, // 39: moego.models.membership.v1.SubscriptionBuyerView.purchase_history_total_price:type_name -> google.type.Money
	18, // 40: moego.models.membership.v1.SubscriptionBuyerView.internal_subscription_status:type_name -> moego.models.subscription.v1.Subscription.Status
	0,  // 41: moego.models.membership.v1.SubscriptionBuyerListFilter.status:type_name -> moego.models.membership.v1.SubscriptionBuyerStatus
	0,  // 42: moego.models.membership.v1.SubscriptionBuyerListFilter.statuses:type_name -> moego.models.membership.v1.SubscriptionBuyerStatus
	2,  // 43: moego.models.membership.v1.PaymentHistoryItemFilter.sort_by:type_name -> moego.models.membership.v1.PaymentHistoryItemFilter.Key
	16, // 44: moego.models.membership.v1.PaymentHistoryItemView.payment_time:type_name -> google.protobuf.Timestamp
	25, // 45: moego.models.membership.v1.PaymentHistoryItemView.payment_amount:type_name -> google.type.Money
	3,  // 46: moego.models.membership.v1.PaymentHistoryItemView.status:type_name -> moego.models.membership.v1.PaymentHistoryItemView.Status
	26, // 47: moego.models.membership.v1.OBRequestSettingModel.days_of_week:type_name -> google.type.DayOfWeek
	16, // 48: moego.models.membership.v1.SubscriptionModel.AutoResumeSetting.date:type_name -> google.protobuf.Timestamp
	49, // [49:49] is the sub-list for method output_type
	49, // [49:49] is the sub-list for method input_type
	49, // [49:49] is the sub-list for extension type_name
	49, // [49:49] is the sub-list for extension extendee
	0,  // [0:49] is the sub-list for field type_name
}

func init() { file_moego_models_membership_v1_subscription_models_proto_init() }
func file_moego_models_membership_v1_subscription_models_proto_init() {
	if File_moego_models_membership_v1_subscription_models_proto != nil {
		return
	}
	file_moego_models_membership_v1_membership_defs_proto_init()
	file_moego_models_membership_v1_membership_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_membership_v1_subscription_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_subscription_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MembershipSubscriptionModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_subscription_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MembershipSubscriptionListModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_subscription_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionModelPublicView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_subscription_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionBuyerView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_subscription_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionBuyerListFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_subscription_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentHistoryItemFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_subscription_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentHistoryItemView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_subscription_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OBRequestSettingModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_membership_v1_subscription_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionModel_AutoResumeSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_membership_v1_subscription_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_membership_v1_subscription_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_membership_v1_subscription_models_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_models_membership_v1_subscription_models_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_models_membership_v1_subscription_models_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_models_membership_v1_subscription_models_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_models_membership_v1_subscription_models_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*SubscriptionModel_AutoResumeSetting_NumOfBillingCycle)(nil),
		(*SubscriptionModel_AutoResumeSetting_Date)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_membership_v1_subscription_models_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_membership_v1_subscription_models_proto_goTypes,
		DependencyIndexes: file_moego_models_membership_v1_subscription_models_proto_depIdxs,
		EnumInfos:         file_moego_models_membership_v1_subscription_models_proto_enumTypes,
		MessageInfos:      file_moego_models_membership_v1_subscription_models_proto_msgTypes,
	}.Build()
	File_moego_models_membership_v1_subscription_models_proto = out.File
	file_moego_models_membership_v1_subscription_models_proto_rawDesc = nil
	file_moego_models_membership_v1_subscription_models_proto_goTypes = nil
	file_moego_models_membership_v1_subscription_models_proto_depIdxs = nil
}
