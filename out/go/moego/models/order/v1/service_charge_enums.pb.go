// @since 2025-03-07 14:58:32
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/order/v1/service_charge_enums.proto

package orderpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// surcharge type, also known as service charge type
type SurchargeType int32

const (
	// unspecified value
	SurchargeType_SURCHARGE_TYPE_UNSPECIFIED SurchargeType = 0
	// off-hours fee (Late pick-up/Early drop-off)
	SurchargeType_OFF_HOURS_FEE SurchargeType = 1
	// custom fee
	SurchargeType_CUSTOM_FEE SurchargeType = 2
	// charge 24-hour
	SurchargeType_CHARGE_24_HOUR SurchargeType = 3
	// feeding fee
	SurchargeType_FEEDING_FEE SurchargeType = 4
	// medication fee
	SurchargeType_MEDICATION_FEE SurchargeType = 5
)

// Enum value maps for SurchargeType.
var (
	SurchargeType_name = map[int32]string{
		0: "SURCHARGE_TYPE_UNSPECIFIED",
		1: "OFF_HOURS_FEE",
		2: "CUSTOM_FEE",
		3: "CHARGE_24_HOUR",
		4: "FEEDING_FEE",
		5: "MEDICATION_FEE",
	}
	SurchargeType_value = map[string]int32{
		"SURCHARGE_TYPE_UNSPECIFIED": 0,
		"OFF_HOURS_FEE":              1,
		"CUSTOM_FEE":                 2,
		"CHARGE_24_HOUR":             3,
		"FEEDING_FEE":                4,
		"MEDICATION_FEE":             5,
	}
)

func (x SurchargeType) Enum() *SurchargeType {
	p := new(SurchargeType)
	*p = x
	return p
}

func (x SurchargeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SurchargeType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_service_charge_enums_proto_enumTypes[0].Descriptor()
}

func (SurchargeType) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_service_charge_enums_proto_enumTypes[0]
}

func (x SurchargeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SurchargeType.Descriptor instead.
func (SurchargeType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_enums_proto_rawDescGZIP(), []int{0}
}

// charge method
type ChargeMethod int32

const (
	// unspecified value
	ChargeMethod_CHARGE_METHOD_UNSPECIFIED ChargeMethod = 0
	// per day
	ChargeMethod_PER_DAY ChargeMethod = 1
	// per administration
	ChargeMethod_PER_ADMINISTRATION ChargeMethod = 2
)

// Enum value maps for ChargeMethod.
var (
	ChargeMethod_name = map[int32]string{
		0: "CHARGE_METHOD_UNSPECIFIED",
		1: "PER_DAY",
		2: "PER_ADMINISTRATION",
	}
	ChargeMethod_value = map[string]int32{
		"CHARGE_METHOD_UNSPECIFIED": 0,
		"PER_DAY":                   1,
		"PER_ADMINISTRATION":        2,
	}
)

func (x ChargeMethod) Enum() *ChargeMethod {
	p := new(ChargeMethod)
	*p = x
	return p
}

func (x ChargeMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChargeMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_service_charge_enums_proto_enumTypes[1].Descriptor()
}

func (ChargeMethod) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_service_charge_enums_proto_enumTypes[1]
}

func (x ChargeMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChargeMethod.Descriptor instead.
func (ChargeMethod) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_service_charge_enums_proto_rawDescGZIP(), []int{1}
}

var File_moego_models_order_v1_service_charge_enums_proto protoreflect.FileDescriptor

var file_moego_models_order_v1_service_charge_enums_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2a, 0x8b, 0x01, 0x0a, 0x0d, 0x53, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x53,
	0x55, 0x52, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x4f,
	0x46, 0x46, 0x5f, 0x48, 0x4f, 0x55, 0x52, 0x53, 0x5f, 0x46, 0x45, 0x45, 0x10, 0x01, 0x12, 0x0e,
	0x0a, 0x0a, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x46, 0x45, 0x45, 0x10, 0x02, 0x12, 0x12,
	0x0a, 0x0e, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x32, 0x34, 0x5f, 0x48, 0x4f, 0x55, 0x52,
	0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x45, 0x45, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x45,
	0x45, 0x10, 0x04, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x45, 0x44, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x46, 0x45, 0x45, 0x10, 0x05, 0x2a, 0x52, 0x0a, 0x0c, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x48, 0x41, 0x52, 0x47,
	0x45, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x52, 0x5f, 0x44, 0x41,
	0x59, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x45, 0x52, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e,
	0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x42, 0x75, 0x0a, 0x1d, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x52,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_order_v1_service_charge_enums_proto_rawDescOnce sync.Once
	file_moego_models_order_v1_service_charge_enums_proto_rawDescData = file_moego_models_order_v1_service_charge_enums_proto_rawDesc
)

func file_moego_models_order_v1_service_charge_enums_proto_rawDescGZIP() []byte {
	file_moego_models_order_v1_service_charge_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_order_v1_service_charge_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_order_v1_service_charge_enums_proto_rawDescData)
	})
	return file_moego_models_order_v1_service_charge_enums_proto_rawDescData
}

var file_moego_models_order_v1_service_charge_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_order_v1_service_charge_enums_proto_goTypes = []interface{}{
	(SurchargeType)(0), // 0: moego.models.order.v1.SurchargeType
	(ChargeMethod)(0),  // 1: moego.models.order.v1.ChargeMethod
}
var file_moego_models_order_v1_service_charge_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_order_v1_service_charge_enums_proto_init() }
func file_moego_models_order_v1_service_charge_enums_proto_init() {
	if File_moego_models_order_v1_service_charge_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_order_v1_service_charge_enums_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_order_v1_service_charge_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_order_v1_service_charge_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_order_v1_service_charge_enums_proto_enumTypes,
	}.Build()
	File_moego_models_order_v1_service_charge_enums_proto = out.File
	file_moego_models_order_v1_service_charge_enums_proto_rawDesc = nil
	file_moego_models_order_v1_service_charge_enums_proto_goTypes = nil
	file_moego_models_order_v1_service_charge_enums_proto_depIdxs = nil
}
