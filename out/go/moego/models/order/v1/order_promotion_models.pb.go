// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/order/v1/order_promotion_models.proto

package orderpb

import (
	decimal "google.golang.org/genproto/googleapis/type/decimal"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 优惠类型
type DiscountType int32

const (
	// unspecified
	DiscountType_DISCOUNT_TYPE_UNSPECIFIED DiscountType = 0
	// percentage 按百分比
	DiscountType_PERCENTAGE DiscountType = 1
	// fixed amount 按固定金额
	DiscountType_FIXED_AMOUNT DiscountType = 2
	// item 抵扣
	DiscountType_ITEM_DEDUCTION DiscountType = 3
)

// Enum value maps for DiscountType.
var (
	DiscountType_name = map[int32]string{
		0: "DISCOUNT_TYPE_UNSPECIFIED",
		1: "PERCENTAGE",
		2: "FIXED_AMOUNT",
		3: "ITEM_DEDUCTION",
	}
	DiscountType_value = map[string]int32{
		"DISCOUNT_TYPE_UNSPECIFIED": 0,
		"PERCENTAGE":                1,
		"FIXED_AMOUNT":              2,
		"ITEM_DEDUCTION":            3,
	}
)

func (x DiscountType) Enum() *DiscountType {
	p := new(DiscountType)
	*p = x
	return p
}

func (x DiscountType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DiscountType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_order_promotion_models_proto_enumTypes[0].Descriptor()
}

func (DiscountType) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_order_promotion_models_proto_enumTypes[0]
}

func (x DiscountType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DiscountType.Descriptor instead.
func (DiscountType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_promotion_models_proto_rawDescGZIP(), []int{0}
}

// 状态
type OrderPromotionModel_Status int32

const (
	// unspecified
	OrderPromotionModel_STATUS_UNSPECIFIED OrderPromotionModel_Status = 0
	// preview 还未落库
	OrderPromotionModel_PREVIEW OrderPromotionModel_Status = 1
	// created 刚创建，还未调用 redeem
	OrderPromotionModel_CREATED OrderPromotionModel_Status = 2
	// applied 已抵扣
	OrderPromotionModel_APPLIED OrderPromotionModel_Status = 3
	// cancelled 已取消
	OrderPromotionModel_CANCELLED OrderPromotionModel_Status = 4
)

// Enum value maps for OrderPromotionModel_Status.
var (
	OrderPromotionModel_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "PREVIEW",
		2: "CREATED",
		3: "APPLIED",
		4: "CANCELLED",
	}
	OrderPromotionModel_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"PREVIEW":            1,
		"CREATED":            2,
		"APPLIED":            3,
		"CANCELLED":          4,
	}
)

func (x OrderPromotionModel_Status) Enum() *OrderPromotionModel_Status {
	p := new(OrderPromotionModel_Status)
	*p = x
	return p
}

func (x OrderPromotionModel_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderPromotionModel_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_order_v1_order_promotion_models_proto_enumTypes[1].Descriptor()
}

func (OrderPromotionModel_Status) Type() protoreflect.EnumType {
	return &file_moego_models_order_v1_order_promotion_models_proto_enumTypes[1]
}

func (x OrderPromotionModel_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderPromotionModel_Status.Descriptor instead.
func (OrderPromotionModel_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_promotion_models_proto_rawDescGZIP(), []int{0, 0}
}

// order promotion model
type OrderPromotionModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// order id
	OrderId int64 `protobuf:"varint,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// promotion id
	PromotionId int64 `protobuf:"varint,5,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id,omitempty"`
	// 使用的优惠.
	//
	// Types that are assignable to Promotion:
	//
	//	*OrderPromotionModel_Discount
	//	*OrderPromotionModel_Package
	//	*OrderPromotionModel_Membership
	//	*OrderPromotionModel_StoreCredit
	//	*OrderPromotionModel_OneTimeDiscount
	Promotion isOrderPromotionModel_Promotion `protobuf_oneof:"promotion"`
	// discount type
	DiscountType DiscountType `protobuf:"varint,11,opt,name=discount_type,json=discountType,proto3,enum=moego.models.order.v1.DiscountType" json:"discount_type,omitempty"`
	// discount value
	// - type 为 percentage 时对应 百分比
	// - type 为 fixed amount 时对应 金额
	// - type 为 item deduction 时对应 抵扣数量
	DiscountValue *decimal.Decimal `protobuf:"bytes,12,opt,name=discount_value,json=discountValue,proto3" json:"discount_value,omitempty"`
	// applied amount
	AppliedAmount *money.Money `protobuf:"bytes,13,opt,name=applied_amount,json=appliedAmount,proto3" json:"applied_amount,omitempty"`
	// status
	Status OrderPromotionModel_Status `protobuf:"varint,14,opt,name=status,proto3,enum=moego.models.order.v1.OrderPromotionModel_Status" json:"status,omitempty"`
	// item list
	Items []*OrderPromotionItem `protobuf:"bytes,15,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *OrderPromotionModel) Reset() {
	*x = OrderPromotionModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_promotion_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderPromotionModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderPromotionModel) ProtoMessage() {}

func (x *OrderPromotionModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_promotion_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderPromotionModel.ProtoReflect.Descriptor instead.
func (*OrderPromotionModel) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_promotion_models_proto_rawDescGZIP(), []int{0}
}

func (x *OrderPromotionModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderPromotionModel) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *OrderPromotionModel) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *OrderPromotionModel) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *OrderPromotionModel) GetPromotionId() int64 {
	if x != nil {
		return x.PromotionId
	}
	return 0
}

func (m *OrderPromotionModel) GetPromotion() isOrderPromotionModel_Promotion {
	if m != nil {
		return m.Promotion
	}
	return nil
}

func (x *OrderPromotionModel) GetDiscount() *OrderPromotionModel_DiscountSubject {
	if x, ok := x.GetPromotion().(*OrderPromotionModel_Discount); ok {
		return x.Discount
	}
	return nil
}

func (x *OrderPromotionModel) GetPackage() *OrderPromotionModel_PackageSubject {
	if x, ok := x.GetPromotion().(*OrderPromotionModel_Package); ok {
		return x.Package
	}
	return nil
}

func (x *OrderPromotionModel) GetMembership() *OrderPromotionModel_MembershipSubject {
	if x, ok := x.GetPromotion().(*OrderPromotionModel_Membership); ok {
		return x.Membership
	}
	return nil
}

func (x *OrderPromotionModel) GetStoreCredit() *OrderPromotionModel_StoreCreditSubject {
	if x, ok := x.GetPromotion().(*OrderPromotionModel_StoreCredit); ok {
		return x.StoreCredit
	}
	return nil
}

func (x *OrderPromotionModel) GetOneTimeDiscount() *OneTimeDiscountCode {
	if x, ok := x.GetPromotion().(*OrderPromotionModel_OneTimeDiscount); ok {
		return x.OneTimeDiscount
	}
	return nil
}

func (x *OrderPromotionModel) GetDiscountType() DiscountType {
	if x != nil {
		return x.DiscountType
	}
	return DiscountType_DISCOUNT_TYPE_UNSPECIFIED
}

func (x *OrderPromotionModel) GetDiscountValue() *decimal.Decimal {
	if x != nil {
		return x.DiscountValue
	}
	return nil
}

func (x *OrderPromotionModel) GetAppliedAmount() *money.Money {
	if x != nil {
		return x.AppliedAmount
	}
	return nil
}

func (x *OrderPromotionModel) GetStatus() OrderPromotionModel_Status {
	if x != nil {
		return x.Status
	}
	return OrderPromotionModel_STATUS_UNSPECIFIED
}

func (x *OrderPromotionModel) GetItems() []*OrderPromotionItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type isOrderPromotionModel_Promotion interface {
	isOrderPromotionModel_Promotion()
}

type OrderPromotionModel_Discount struct {
	// discount
	Discount *OrderPromotionModel_DiscountSubject `protobuf:"bytes,6,opt,name=discount,proto3,oneof"`
}

type OrderPromotionModel_Package struct {
	// package
	Package *OrderPromotionModel_PackageSubject `protobuf:"bytes,7,opt,name=package,proto3,oneof"`
}

type OrderPromotionModel_Membership struct {
	// membership
	Membership *OrderPromotionModel_MembershipSubject `protobuf:"bytes,8,opt,name=membership,proto3,oneof"`
}

type OrderPromotionModel_StoreCredit struct {
	// store credit
	StoreCredit *OrderPromotionModel_StoreCreditSubject `protobuf:"bytes,9,opt,name=store_credit,json=storeCredit,proto3,oneof"`
}

type OrderPromotionModel_OneTimeDiscount struct {
	// 一次性的 discount.
	OneTimeDiscount *OneTimeDiscountCode `protobuf:"bytes,10,opt,name=one_time_discount,json=oneTimeDiscount,proto3,oneof"`
}

func (*OrderPromotionModel_Discount) isOrderPromotionModel_Promotion() {}

func (*OrderPromotionModel_Package) isOrderPromotionModel_Promotion() {}

func (*OrderPromotionModel_Membership) isOrderPromotionModel_Promotion() {}

func (*OrderPromotionModel_StoreCredit) isOrderPromotionModel_Promotion() {}

func (*OrderPromotionModel_OneTimeDiscount) isOrderPromotionModel_Promotion() {}

// order promotion item
type OrderPromotionItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// order promotion id
	OrderPromotionId int64 `protobuf:"varint,4,opt,name=order_promotion_id,json=orderPromotionId,proto3" json:"order_promotion_id,omitempty"`
	// order item id
	OrderItemId int64 `protobuf:"varint,5,opt,name=order_item_id,json=orderItemId,proto3" json:"order_item_id,omitempty"`
	// external uuid
	ExternalUuid string `protobuf:"bytes,6,opt,name=external_uuid,json=externalUuid,proto3" json:"external_uuid,omitempty"`
	// applied amount
	AppliedAmount *money.Money `protobuf:"bytes,7,opt,name=applied_amount,json=appliedAmount,proto3" json:"applied_amount,omitempty"`
}

func (x *OrderPromotionItem) Reset() {
	*x = OrderPromotionItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_promotion_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderPromotionItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderPromotionItem) ProtoMessage() {}

func (x *OrderPromotionItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_promotion_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderPromotionItem.ProtoReflect.Descriptor instead.
func (*OrderPromotionItem) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_promotion_models_proto_rawDescGZIP(), []int{1}
}

func (x *OrderPromotionItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderPromotionItem) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *OrderPromotionItem) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *OrderPromotionItem) GetOrderPromotionId() int64 {
	if x != nil {
		return x.OrderPromotionId
	}
	return 0
}

func (x *OrderPromotionItem) GetOrderItemId() int64 {
	if x != nil {
		return x.OrderItemId
	}
	return 0
}

func (x *OrderPromotionItem) GetExternalUuid() string {
	if x != nil {
		return x.ExternalUuid
	}
	return ""
}

func (x *OrderPromotionItem) GetAppliedAmount() *money.Money {
	if x != nil {
		return x.AppliedAmount
	}
	return nil
}

// 一次性的优惠
type OneTimeDiscountCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 一次性优惠只有 2 种模式，并且互斥.
	//
	// Types that are assignable to DiscountDetail:
	//
	//	*OneTimeDiscountCode_DiscountPercentage
	//	*OneTimeDiscountCode_DiscountAmount
	DiscountDetail isOneTimeDiscountCode_DiscountDetail `protobuf_oneof:"discount_detail"`
}

func (x *OneTimeDiscountCode) Reset() {
	*x = OneTimeDiscountCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_promotion_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OneTimeDiscountCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OneTimeDiscountCode) ProtoMessage() {}

func (x *OneTimeDiscountCode) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_promotion_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OneTimeDiscountCode.ProtoReflect.Descriptor instead.
func (*OneTimeDiscountCode) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_promotion_models_proto_rawDescGZIP(), []int{2}
}

func (m *OneTimeDiscountCode) GetDiscountDetail() isOneTimeDiscountCode_DiscountDetail {
	if m != nil {
		return m.DiscountDetail
	}
	return nil
}

func (x *OneTimeDiscountCode) GetDiscountPercentage() *decimal.Decimal {
	if x, ok := x.GetDiscountDetail().(*OneTimeDiscountCode_DiscountPercentage); ok {
		return x.DiscountPercentage
	}
	return nil
}

func (x *OneTimeDiscountCode) GetDiscountAmount() *money.Money {
	if x, ok := x.GetDiscountDetail().(*OneTimeDiscountCode_DiscountAmount); ok {
		return x.DiscountAmount
	}
	return nil
}

type isOneTimeDiscountCode_DiscountDetail interface {
	isOneTimeDiscountCode_DiscountDetail()
}

type OneTimeDiscountCode_DiscountPercentage struct {
	// 按比例打折
	DiscountPercentage *decimal.Decimal `protobuf:"bytes,1,opt,name=discount_percentage,json=discountPercentage,proto3,oneof"`
}

type OneTimeDiscountCode_DiscountAmount struct {
	// 按金额打折
	DiscountAmount *money.Money `protobuf:"bytes,2,opt,name=discount_amount,json=discountAmount,proto3,oneof"`
}

func (*OneTimeDiscountCode_DiscountPercentage) isOneTimeDiscountCode_DiscountDetail() {}

func (*OneTimeDiscountCode_DiscountAmount) isOneTimeDiscountCode_DiscountDetail() {}

// DiscountSubject 折扣主体
// one time discount 也体现在这，order item id 等都是0
type OrderPromotionModel_DiscountSubject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id 折扣ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// subject id, 对应 moego/models/promotion/v1/promotion.proto:36 里面的 subject id
	SubjectId int64 `protobuf:"varint,2,opt,name=subject_id,json=subjectId,proto3" json:"subject_id,omitempty"`
	// discount code
	DiscountCode string `protobuf:"bytes,3,opt,name=discount_code,json=discountCode,proto3" json:"discount_code,omitempty"`
	// discount type
	DiscountType DiscountType `protobuf:"varint,4,opt,name=discount_type,json=discountType,proto3,enum=moego.models.order.v1.DiscountType" json:"discount_type,omitempty"`
	// discount value
	// - type 为 percentage 时对应 百分比
	// - type 为 fixed amount 时对应 金额
	DiscountValue *decimal.Decimal `protobuf:"bytes,5,opt,name=discount_value,json=discountValue,proto3" json:"discount_value,omitempty"`
}

func (x *OrderPromotionModel_DiscountSubject) Reset() {
	*x = OrderPromotionModel_DiscountSubject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_promotion_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderPromotionModel_DiscountSubject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderPromotionModel_DiscountSubject) ProtoMessage() {}

func (x *OrderPromotionModel_DiscountSubject) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_promotion_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderPromotionModel_DiscountSubject.ProtoReflect.Descriptor instead.
func (*OrderPromotionModel_DiscountSubject) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_promotion_models_proto_rawDescGZIP(), []int{0, 0}
}

func (x *OrderPromotionModel_DiscountSubject) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderPromotionModel_DiscountSubject) GetSubjectId() int64 {
	if x != nil {
		return x.SubjectId
	}
	return 0
}

func (x *OrderPromotionModel_DiscountSubject) GetDiscountCode() string {
	if x != nil {
		return x.DiscountCode
	}
	return ""
}

func (x *OrderPromotionModel_DiscountSubject) GetDiscountType() DiscountType {
	if x != nil {
		return x.DiscountType
	}
	return DiscountType_DISCOUNT_TYPE_UNSPECIFIED
}

func (x *OrderPromotionModel_DiscountSubject) GetDiscountValue() *decimal.Decimal {
	if x != nil {
		return x.DiscountValue
	}
	return nil
}

// PackageSubject 套餐服务主体
type OrderPromotionModel_PackageSubject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id 套餐服务ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// subject id, 对应 moego/models/promotion/v1/promotion.proto:36 里面的 subject id
	SubjectId int64 `protobuf:"varint,2,opt,name=subject_id,json=subjectId,proto3" json:"subject_id,omitempty"`
	// package name
	PackageName string `protobuf:"bytes,3,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
}

func (x *OrderPromotionModel_PackageSubject) Reset() {
	*x = OrderPromotionModel_PackageSubject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_promotion_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderPromotionModel_PackageSubject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderPromotionModel_PackageSubject) ProtoMessage() {}

func (x *OrderPromotionModel_PackageSubject) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_promotion_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderPromotionModel_PackageSubject.ProtoReflect.Descriptor instead.
func (*OrderPromotionModel_PackageSubject) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_promotion_models_proto_rawDescGZIP(), []int{0, 1}
}

func (x *OrderPromotionModel_PackageSubject) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderPromotionModel_PackageSubject) GetSubjectId() int64 {
	if x != nil {
		return x.SubjectId
	}
	return 0
}

func (x *OrderPromotionModel_PackageSubject) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

// MembershipSubject 会员权益主体
type OrderPromotionModel_MembershipSubject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id 会员权益ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// subject id, 对应 moego/models/promotion/v1/promotion.proto:36 里面的 subject id
	SubjectId int64 `protobuf:"varint,2,opt,name=subject_id,json=subjectId,proto3" json:"subject_id,omitempty"`
	// membership name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// discount type
	DiscountType DiscountType `protobuf:"varint,4,opt,name=discount_type,json=discountType,proto3,enum=moego.models.order.v1.DiscountType" json:"discount_type,omitempty"`
	// discount value
	// - type 为 percentage 时对应 百分比
	// - type 为 fixed amount 时对应 金额
	DiscountValue *decimal.Decimal `protobuf:"bytes,5,opt,name=discount_value,json=discountValue,proto3" json:"discount_value,omitempty"`
}

func (x *OrderPromotionModel_MembershipSubject) Reset() {
	*x = OrderPromotionModel_MembershipSubject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_promotion_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderPromotionModel_MembershipSubject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderPromotionModel_MembershipSubject) ProtoMessage() {}

func (x *OrderPromotionModel_MembershipSubject) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_promotion_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderPromotionModel_MembershipSubject.ProtoReflect.Descriptor instead.
func (*OrderPromotionModel_MembershipSubject) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_promotion_models_proto_rawDescGZIP(), []int{0, 2}
}

func (x *OrderPromotionModel_MembershipSubject) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderPromotionModel_MembershipSubject) GetSubjectId() int64 {
	if x != nil {
		return x.SubjectId
	}
	return 0
}

func (x *OrderPromotionModel_MembershipSubject) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *OrderPromotionModel_MembershipSubject) GetDiscountType() DiscountType {
	if x != nil {
		return x.DiscountType
	}
	return DiscountType_DISCOUNT_TYPE_UNSPECIFIED
}

func (x *OrderPromotionModel_MembershipSubject) GetDiscountValue() *decimal.Decimal {
	if x != nil {
		return x.DiscountValue
	}
	return nil
}

// StoreCreditSubject
type OrderPromotionModel_StoreCreditSubject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 抵扣的金额
	Amount *money.Money `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *OrderPromotionModel_StoreCreditSubject) Reset() {
	*x = OrderPromotionModel_StoreCreditSubject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v1_order_promotion_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderPromotionModel_StoreCreditSubject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderPromotionModel_StoreCreditSubject) ProtoMessage() {}

func (x *OrderPromotionModel_StoreCreditSubject) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v1_order_promotion_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderPromotionModel_StoreCreditSubject.ProtoReflect.Descriptor instead.
func (*OrderPromotionModel_StoreCreditSubject) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v1_order_promotion_models_proto_rawDescGZIP(), []int{0, 3}
}

func (x *OrderPromotionModel_StoreCreditSubject) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

var File_moego_models_order_v1_order_promotion_models_proto protoreflect.FileDescriptor

var file_moego_models_order_v1_order_promotion_models_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61,
	0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xd4, 0x0d, 0x0a, 0x13, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x58, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x48, 0x00,
	0x52, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x55, 0x0a, 0x07, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x53,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x48, 0x00, 0x52, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x12, 0x5e, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x12, 0x62, 0x0a, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x12, 0x58, 0x0a, 0x11, 0x6f, 0x6e, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x48, 0x00, 0x52, 0x0f,
	0x6f, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x48, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0e, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x49, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x1a, 0xec, 0x01,
	0x0a, 0x0f, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x48, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x3b, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x52, 0x0d, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0x62, 0x0a, 0x0e,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x1a, 0xdd, 0x01, 0x0a, 0x11, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x75, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x48, 0x0a, 0x0d, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x6d, 0x61,
	0x6c, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x1a, 0x40, 0x0a, 0x12, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x56, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0b,
	0x0a, 0x07, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x43,
	0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x42, 0x0b, 0x0a, 0x09, 0x70, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xd0, 0x02, 0x0a, 0x12, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3b,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x6d, 0x6f,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x55, 0x75, 0x69, 0x64, 0x12,
	0x39, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb0, 0x01, 0x0a, 0x13, 0x4f,
	0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x47, 0x0a, 0x13, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x65,
	0x63, 0x69, 0x6d, 0x61, 0x6c, 0x48, 0x00, 0x52, 0x12, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x3d, 0x0a, 0x0f, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x00, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x11, 0x0a, 0x0f, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2a, 0x63, 0x0a,
	0x0c, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a,
	0x19, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a,
	0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x41, 0x47, 0x45, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c,
	0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x12,
	0x0a, 0x0e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x44, 0x45, 0x44, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x03, 0x42, 0x75, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_models_order_v1_order_promotion_models_proto_rawDescOnce sync.Once
	file_moego_models_order_v1_order_promotion_models_proto_rawDescData = file_moego_models_order_v1_order_promotion_models_proto_rawDesc
)

func file_moego_models_order_v1_order_promotion_models_proto_rawDescGZIP() []byte {
	file_moego_models_order_v1_order_promotion_models_proto_rawDescOnce.Do(func() {
		file_moego_models_order_v1_order_promotion_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_order_v1_order_promotion_models_proto_rawDescData)
	})
	return file_moego_models_order_v1_order_promotion_models_proto_rawDescData
}

var file_moego_models_order_v1_order_promotion_models_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_order_v1_order_promotion_models_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_models_order_v1_order_promotion_models_proto_goTypes = []interface{}{
	(DiscountType)(0),                              // 0: moego.models.order.v1.DiscountType
	(OrderPromotionModel_Status)(0),                // 1: moego.models.order.v1.OrderPromotionModel.Status
	(*OrderPromotionModel)(nil),                    // 2: moego.models.order.v1.OrderPromotionModel
	(*OrderPromotionItem)(nil),                     // 3: moego.models.order.v1.OrderPromotionItem
	(*OneTimeDiscountCode)(nil),                    // 4: moego.models.order.v1.OneTimeDiscountCode
	(*OrderPromotionModel_DiscountSubject)(nil),    // 5: moego.models.order.v1.OrderPromotionModel.DiscountSubject
	(*OrderPromotionModel_PackageSubject)(nil),     // 6: moego.models.order.v1.OrderPromotionModel.PackageSubject
	(*OrderPromotionModel_MembershipSubject)(nil),  // 7: moego.models.order.v1.OrderPromotionModel.MembershipSubject
	(*OrderPromotionModel_StoreCreditSubject)(nil), // 8: moego.models.order.v1.OrderPromotionModel.StoreCreditSubject
	(*timestamppb.Timestamp)(nil),                  // 9: google.protobuf.Timestamp
	(*decimal.Decimal)(nil),                        // 10: google.type.Decimal
	(*money.Money)(nil),                            // 11: google.type.Money
}
var file_moego_models_order_v1_order_promotion_models_proto_depIdxs = []int32{
	9,  // 0: moego.models.order.v1.OrderPromotionModel.create_time:type_name -> google.protobuf.Timestamp
	9,  // 1: moego.models.order.v1.OrderPromotionModel.update_time:type_name -> google.protobuf.Timestamp
	5,  // 2: moego.models.order.v1.OrderPromotionModel.discount:type_name -> moego.models.order.v1.OrderPromotionModel.DiscountSubject
	6,  // 3: moego.models.order.v1.OrderPromotionModel.package:type_name -> moego.models.order.v1.OrderPromotionModel.PackageSubject
	7,  // 4: moego.models.order.v1.OrderPromotionModel.membership:type_name -> moego.models.order.v1.OrderPromotionModel.MembershipSubject
	8,  // 5: moego.models.order.v1.OrderPromotionModel.store_credit:type_name -> moego.models.order.v1.OrderPromotionModel.StoreCreditSubject
	4,  // 6: moego.models.order.v1.OrderPromotionModel.one_time_discount:type_name -> moego.models.order.v1.OneTimeDiscountCode
	0,  // 7: moego.models.order.v1.OrderPromotionModel.discount_type:type_name -> moego.models.order.v1.DiscountType
	10, // 8: moego.models.order.v1.OrderPromotionModel.discount_value:type_name -> google.type.Decimal
	11, // 9: moego.models.order.v1.OrderPromotionModel.applied_amount:type_name -> google.type.Money
	1,  // 10: moego.models.order.v1.OrderPromotionModel.status:type_name -> moego.models.order.v1.OrderPromotionModel.Status
	3,  // 11: moego.models.order.v1.OrderPromotionModel.items:type_name -> moego.models.order.v1.OrderPromotionItem
	9,  // 12: moego.models.order.v1.OrderPromotionItem.create_time:type_name -> google.protobuf.Timestamp
	9,  // 13: moego.models.order.v1.OrderPromotionItem.update_time:type_name -> google.protobuf.Timestamp
	11, // 14: moego.models.order.v1.OrderPromotionItem.applied_amount:type_name -> google.type.Money
	10, // 15: moego.models.order.v1.OneTimeDiscountCode.discount_percentage:type_name -> google.type.Decimal
	11, // 16: moego.models.order.v1.OneTimeDiscountCode.discount_amount:type_name -> google.type.Money
	0,  // 17: moego.models.order.v1.OrderPromotionModel.DiscountSubject.discount_type:type_name -> moego.models.order.v1.DiscountType
	10, // 18: moego.models.order.v1.OrderPromotionModel.DiscountSubject.discount_value:type_name -> google.type.Decimal
	0,  // 19: moego.models.order.v1.OrderPromotionModel.MembershipSubject.discount_type:type_name -> moego.models.order.v1.DiscountType
	10, // 20: moego.models.order.v1.OrderPromotionModel.MembershipSubject.discount_value:type_name -> google.type.Decimal
	11, // 21: moego.models.order.v1.OrderPromotionModel.StoreCreditSubject.amount:type_name -> google.type.Money
	22, // [22:22] is the sub-list for method output_type
	22, // [22:22] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_moego_models_order_v1_order_promotion_models_proto_init() }
func file_moego_models_order_v1_order_promotion_models_proto_init() {
	if File_moego_models_order_v1_order_promotion_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_order_v1_order_promotion_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderPromotionModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_promotion_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderPromotionItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_promotion_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OneTimeDiscountCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_promotion_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderPromotionModel_DiscountSubject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_promotion_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderPromotionModel_PackageSubject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_promotion_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderPromotionModel_MembershipSubject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_order_v1_order_promotion_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderPromotionModel_StoreCreditSubject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_order_v1_order_promotion_models_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*OrderPromotionModel_Discount)(nil),
		(*OrderPromotionModel_Package)(nil),
		(*OrderPromotionModel_Membership)(nil),
		(*OrderPromotionModel_StoreCredit)(nil),
		(*OrderPromotionModel_OneTimeDiscount)(nil),
	}
	file_moego_models_order_v1_order_promotion_models_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*OneTimeDiscountCode_DiscountPercentage)(nil),
		(*OneTimeDiscountCode_DiscountAmount)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_order_v1_order_promotion_models_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_order_v1_order_promotion_models_proto_goTypes,
		DependencyIndexes: file_moego_models_order_v1_order_promotion_models_proto_depIdxs,
		EnumInfos:         file_moego_models_order_v1_order_promotion_models_proto_enumTypes,
		MessageInfos:      file_moego_models_order_v1_order_promotion_models_proto_msgTypes,
	}.Build()
	File_moego_models_order_v1_order_promotion_models_proto = out.File
	file_moego_models_order_v1_order_promotion_models_proto_rawDesc = nil
	file_moego_models_order_v1_order_promotion_models_proto_goTypes = nil
	file_moego_models_order_v1_order_promotion_models_proto_depIdxs = nil
}
