// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/finance_tools/v1/cash_drawer_defs.proto

package financetoolspb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	interval "google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// A partial part to create a cash drawer report.
type CreateCashDrawerReportDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The date range
	Range *interval.Interval `protobuf:"bytes,1,opt,name=range,proto3" json:"range,omitempty"`
	// The starting balance
	StartBalance *money.Money `protobuf:"bytes,2,opt,name=start_balance,json=startBalance,proto3" json:"start_balance,omitempty"`
	// The total of cash payments
	PaymentsTotal *money.Money `protobuf:"bytes,3,opt,name=payments_total,json=paymentsTotal,proto3" json:"payments_total,omitempty"`
	// The total of cash in/out adjustments
	AdjustmentsTotal *money.Money `protobuf:"bytes,4,opt,name=adjustments_total,json=adjustmentsTotal,proto3" json:"adjustments_total,omitempty"`
	// The actual ending balance
	CountedBalance *money.Money `protobuf:"bytes,5,opt,name=counted_balance,json=countedBalance,proto3" json:"counted_balance,omitempty"`
	// Comment
	Comment *string `protobuf:"bytes,6,opt,name=comment,proto3,oneof" json:"comment,omitempty"`
}

func (x *CreateCashDrawerReportDef) Reset() {
	*x = CreateCashDrawerReportDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_finance_tools_v1_cash_drawer_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCashDrawerReportDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCashDrawerReportDef) ProtoMessage() {}

func (x *CreateCashDrawerReportDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_finance_tools_v1_cash_drawer_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCashDrawerReportDef.ProtoReflect.Descriptor instead.
func (*CreateCashDrawerReportDef) Descriptor() ([]byte, []int) {
	return file_moego_models_finance_tools_v1_cash_drawer_defs_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCashDrawerReportDef) GetRange() *interval.Interval {
	if x != nil {
		return x.Range
	}
	return nil
}

func (x *CreateCashDrawerReportDef) GetStartBalance() *money.Money {
	if x != nil {
		return x.StartBalance
	}
	return nil
}

func (x *CreateCashDrawerReportDef) GetPaymentsTotal() *money.Money {
	if x != nil {
		return x.PaymentsTotal
	}
	return nil
}

func (x *CreateCashDrawerReportDef) GetAdjustmentsTotal() *money.Money {
	if x != nil {
		return x.AdjustmentsTotal
	}
	return nil
}

func (x *CreateCashDrawerReportDef) GetCountedBalance() *money.Money {
	if x != nil {
		return x.CountedBalance
	}
	return nil
}

func (x *CreateCashDrawerReportDef) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

// A partial part to update a cash drawer report.
type UpdateCashDrawerReportDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The report ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Comment
	Comment *string `protobuf:"bytes,2,opt,name=comment,proto3,oneof" json:"comment,omitempty"`
}

func (x *UpdateCashDrawerReportDef) Reset() {
	*x = UpdateCashDrawerReportDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_finance_tools_v1_cash_drawer_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCashDrawerReportDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCashDrawerReportDef) ProtoMessage() {}

func (x *UpdateCashDrawerReportDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_finance_tools_v1_cash_drawer_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCashDrawerReportDef.ProtoReflect.Descriptor instead.
func (*UpdateCashDrawerReportDef) Descriptor() ([]byte, []int) {
	return file_moego_models_finance_tools_v1_cash_drawer_defs_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateCashDrawerReportDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCashDrawerReportDef) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

// A partial cash adjustment to create a cash adjustment.
type CreateCashDrawerAdjustmentDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Adjustment type (direction)
	Type CashDrawerAdjustmentType `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.finance_tools.v1.CashDrawerAdjustmentType" json:"type,omitempty"`
	// The amount of cash in/out adjustments. Always positive.
	Amount *money.Money `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// Comment
	Comment *string `protobuf:"bytes,3,opt,name=comment,proto3,oneof" json:"comment,omitempty"`
}

func (x *CreateCashDrawerAdjustmentDef) Reset() {
	*x = CreateCashDrawerAdjustmentDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_finance_tools_v1_cash_drawer_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCashDrawerAdjustmentDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCashDrawerAdjustmentDef) ProtoMessage() {}

func (x *CreateCashDrawerAdjustmentDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_finance_tools_v1_cash_drawer_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCashDrawerAdjustmentDef.ProtoReflect.Descriptor instead.
func (*CreateCashDrawerAdjustmentDef) Descriptor() ([]byte, []int) {
	return file_moego_models_finance_tools_v1_cash_drawer_defs_proto_rawDescGZIP(), []int{2}
}

func (x *CreateCashDrawerAdjustmentDef) GetType() CashDrawerAdjustmentType {
	if x != nil {
		return x.Type
	}
	return CashDrawerAdjustmentType_CASH_DRAWER_ADJUSTMENT_TYPE_UNSPECIFIED
}

func (x *CreateCashDrawerAdjustmentDef) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *CreateCashDrawerAdjustmentDef) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

var File_moego_models_finance_tools_v1_cash_drawer_defs_proto protoreflect.FileDescriptor

var file_moego_models_finance_tools_v1_cash_drawer_defs_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66,
	0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x63, 0x61, 0x73, 0x68, 0x5f, 0x64, 0x72, 0x61, 0x77, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f,
	0x6c, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x64,
	0x72, 0x61, 0x77, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa1, 0x03, 0x0a, 0x19, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x68, 0x44, 0x72, 0x61, 0x77, 0x65, 0x72, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x66, 0x12, 0x35, 0x0a, 0x05, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x12,
	0x41, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x49, 0x0a, 0x11, 0x61, 0x64, 0x6a, 0x75, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x10, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x45, 0x0a, 0x0f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x65, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x07, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72,
	0x03, 0x18, 0xc8, 0x01, 0x48, 0x00, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x88,
	0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x69,
	0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x68, 0x44, 0x72, 0x61, 0x77,
	0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x66, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xc8, 0x01, 0x48,
	0x00, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xe3, 0x01, 0x0a, 0x1d, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x73, 0x68, 0x44, 0x72, 0x61, 0x77, 0x65, 0x72, 0x41, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x66, 0x12, 0x57, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x73, 0x68, 0x44, 0x72,
	0x61, 0x77, 0x65, 0x72, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0xc8, 0x01, 0x48, 0x00, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x42,
	0x8c, 0x01, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x61, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66,
	0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x3b,
	0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_finance_tools_v1_cash_drawer_defs_proto_rawDescOnce sync.Once
	file_moego_models_finance_tools_v1_cash_drawer_defs_proto_rawDescData = file_moego_models_finance_tools_v1_cash_drawer_defs_proto_rawDesc
)

func file_moego_models_finance_tools_v1_cash_drawer_defs_proto_rawDescGZIP() []byte {
	file_moego_models_finance_tools_v1_cash_drawer_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_finance_tools_v1_cash_drawer_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_finance_tools_v1_cash_drawer_defs_proto_rawDescData)
	})
	return file_moego_models_finance_tools_v1_cash_drawer_defs_proto_rawDescData
}

var file_moego_models_finance_tools_v1_cash_drawer_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_finance_tools_v1_cash_drawer_defs_proto_goTypes = []interface{}{
	(*CreateCashDrawerReportDef)(nil),     // 0: moego.models.finance_tools.v1.CreateCashDrawerReportDef
	(*UpdateCashDrawerReportDef)(nil),     // 1: moego.models.finance_tools.v1.UpdateCashDrawerReportDef
	(*CreateCashDrawerAdjustmentDef)(nil), // 2: moego.models.finance_tools.v1.CreateCashDrawerAdjustmentDef
	(*interval.Interval)(nil),             // 3: google.type.Interval
	(*money.Money)(nil),                   // 4: google.type.Money
	(CashDrawerAdjustmentType)(0),         // 5: moego.models.finance_tools.v1.CashDrawerAdjustmentType
}
var file_moego_models_finance_tools_v1_cash_drawer_defs_proto_depIdxs = []int32{
	3, // 0: moego.models.finance_tools.v1.CreateCashDrawerReportDef.range:type_name -> google.type.Interval
	4, // 1: moego.models.finance_tools.v1.CreateCashDrawerReportDef.start_balance:type_name -> google.type.Money
	4, // 2: moego.models.finance_tools.v1.CreateCashDrawerReportDef.payments_total:type_name -> google.type.Money
	4, // 3: moego.models.finance_tools.v1.CreateCashDrawerReportDef.adjustments_total:type_name -> google.type.Money
	4, // 4: moego.models.finance_tools.v1.CreateCashDrawerReportDef.counted_balance:type_name -> google.type.Money
	5, // 5: moego.models.finance_tools.v1.CreateCashDrawerAdjustmentDef.type:type_name -> moego.models.finance_tools.v1.CashDrawerAdjustmentType
	4, // 6: moego.models.finance_tools.v1.CreateCashDrawerAdjustmentDef.amount:type_name -> google.type.Money
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_moego_models_finance_tools_v1_cash_drawer_defs_proto_init() }
func file_moego_models_finance_tools_v1_cash_drawer_defs_proto_init() {
	if File_moego_models_finance_tools_v1_cash_drawer_defs_proto != nil {
		return
	}
	file_moego_models_finance_tools_v1_cash_drawer_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_finance_tools_v1_cash_drawer_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCashDrawerReportDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_finance_tools_v1_cash_drawer_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCashDrawerReportDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_finance_tools_v1_cash_drawer_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCashDrawerAdjustmentDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_finance_tools_v1_cash_drawer_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_finance_tools_v1_cash_drawer_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_finance_tools_v1_cash_drawer_defs_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_finance_tools_v1_cash_drawer_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_finance_tools_v1_cash_drawer_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_finance_tools_v1_cash_drawer_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_finance_tools_v1_cash_drawer_defs_proto_msgTypes,
	}.Build()
	File_moego_models_finance_tools_v1_cash_drawer_defs_proto = out.File
	file_moego_models_finance_tools_v1_cash_drawer_defs_proto_rawDesc = nil
	file_moego_models_finance_tools_v1_cash_drawer_defs_proto_goTypes = nil
	file_moego_models_finance_tools_v1_cash_drawer_defs_proto_depIdxs = nil
}
