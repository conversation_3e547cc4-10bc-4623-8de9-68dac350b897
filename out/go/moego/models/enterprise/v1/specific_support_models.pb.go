// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/enterprise/v1/specific_support_models.proto

package enterprisepb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// the customer id
type MessageTask_Status int32

const (
	// Unspecified status
	MessageTask_STATUS_UNSPECIFIED MessageTask_Status = 0
	// ready for start
	MessageTask_READY MessageTask_Status = 1
	// running
	MessageTask_RUNNING MessageTask_Status = 2
	// finished
	MessageTask_FINISHED MessageTask_Status = 3
	// failed
	MessageTask_FAILED MessageTask_Status = 4
)

// Enum value maps for MessageTask_Status.
var (
	MessageTask_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "READY",
		2: "RUNNING",
		3: "FINISHED",
		4: "FAILED",
	}
	MessageTask_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"READY":              1,
		"RUNNING":            2,
		"FINISHED":           3,
		"FAILED":             4,
	}
)

func (x MessageTask_Status) Enum() *MessageTask_Status {
	p := new(MessageTask_Status)
	*p = x
	return p
}

func (x MessageTask_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageTask_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_specific_support_models_proto_enumTypes[0].Descriptor()
}

func (MessageTask_Status) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_specific_support_models_proto_enumTypes[0]
}

func (x MessageTask_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageTask_Status.Descriptor instead.
func (MessageTask_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_specific_support_models_proto_rawDescGZIP(), []int{1, 0}
}

// the company message cycle
type MessageCycle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// the message cycle period
	Period *v1.TimePeriod `protobuf:"bytes,2,opt,name=period,proto3" json:"period,omitempty"`
}

func (x *MessageCycle) Reset() {
	*x = MessageCycle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_specific_support_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageCycle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageCycle) ProtoMessage() {}

func (x *MessageCycle) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_specific_support_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageCycle.ProtoReflect.Descriptor instead.
func (*MessageCycle) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_specific_support_models_proto_rawDescGZIP(), []int{0}
}

func (x *MessageCycle) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *MessageCycle) GetPeriod() *v1.TimePeriod {
	if x != nil {
		return x.Period
	}
	return nil
}

// the message task
type MessageTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// the business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// the company id
	CompanyId int64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// the execute time
	ExecuteAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=execute_at,json=executeAt,proto3" json:"execute_at,omitempty"`
	// the message status
	Message string `protobuf:"bytes,6,opt,name=message,proto3" json:"message,omitempty"`
	// the business name
	BusinessName string `protobuf:"bytes,7,opt,name=business_name,json=businessName,proto3" json:"business_name,omitempty"`
	// the customer phone
	CustomerPhone string `protobuf:"bytes,8,opt,name=customer_phone,json=customerPhone,proto3" json:"customer_phone,omitempty"`
	// the message status
	Status MessageTask_Status `protobuf:"varint,9,opt,name=status,proto3,enum=moego.models.enterprise.v1.MessageTask_Status" json:"status,omitempty"`
	// the failure reason
	FailureReason string `protobuf:"bytes,10,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *MessageTask) Reset() {
	*x = MessageTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_specific_support_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageTask) ProtoMessage() {}

func (x *MessageTask) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_specific_support_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageTask.ProtoReflect.Descriptor instead.
func (*MessageTask) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_specific_support_models_proto_rawDescGZIP(), []int{1}
}

func (x *MessageTask) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MessageTask) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *MessageTask) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *MessageTask) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *MessageTask) GetExecuteAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExecuteAt
	}
	return nil
}

func (x *MessageTask) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *MessageTask) GetBusinessName() string {
	if x != nil {
		return x.BusinessName
	}
	return ""
}

func (x *MessageTask) GetCustomerPhone() string {
	if x != nil {
		return x.CustomerPhone
	}
	return ""
}

func (x *MessageTask) GetStatus() MessageTask_Status {
	if x != nil {
		return x.Status
	}
	return MessageTask_STATUS_UNSPECIFIED
}

func (x *MessageTask) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

var File_moego_models_enterprise_v1_specific_support_models_proto protoreflect.FileDescriptor

var file_moego_models_enterprise_v1_specific_support_models_proto_rawDesc = []byte{
	0x0a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x61, 0x0a, 0x0c, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x06, 0x70, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x52, 0x06, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0xe2, 0x03, 0x0a,
	0x0b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x39, 0x0a,
	0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x41, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x46,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72,
	0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x52, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x09, 0x0a, 0x05, 0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x55,
	0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x49, 0x4e, 0x49, 0x53,
	0x48, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x04, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_enterprise_v1_specific_support_models_proto_rawDescOnce sync.Once
	file_moego_models_enterprise_v1_specific_support_models_proto_rawDescData = file_moego_models_enterprise_v1_specific_support_models_proto_rawDesc
)

func file_moego_models_enterprise_v1_specific_support_models_proto_rawDescGZIP() []byte {
	file_moego_models_enterprise_v1_specific_support_models_proto_rawDescOnce.Do(func() {
		file_moego_models_enterprise_v1_specific_support_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_enterprise_v1_specific_support_models_proto_rawDescData)
	})
	return file_moego_models_enterprise_v1_specific_support_models_proto_rawDescData
}

var file_moego_models_enterprise_v1_specific_support_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_enterprise_v1_specific_support_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_enterprise_v1_specific_support_models_proto_goTypes = []interface{}{
	(MessageTask_Status)(0),       // 0: moego.models.enterprise.v1.MessageTask.Status
	(*MessageCycle)(nil),          // 1: moego.models.enterprise.v1.MessageCycle
	(*MessageTask)(nil),           // 2: moego.models.enterprise.v1.MessageTask
	(*v1.TimePeriod)(nil),         // 3: moego.utils.v1.TimePeriod
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
}
var file_moego_models_enterprise_v1_specific_support_models_proto_depIdxs = []int32{
	3, // 0: moego.models.enterprise.v1.MessageCycle.period:type_name -> moego.utils.v1.TimePeriod
	4, // 1: moego.models.enterprise.v1.MessageTask.execute_at:type_name -> google.protobuf.Timestamp
	0, // 2: moego.models.enterprise.v1.MessageTask.status:type_name -> moego.models.enterprise.v1.MessageTask.Status
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_enterprise_v1_specific_support_models_proto_init() }
func file_moego_models_enterprise_v1_specific_support_models_proto_init() {
	if File_moego_models_enterprise_v1_specific_support_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_enterprise_v1_specific_support_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageCycle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_specific_support_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_enterprise_v1_specific_support_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_enterprise_v1_specific_support_models_proto_goTypes,
		DependencyIndexes: file_moego_models_enterprise_v1_specific_support_models_proto_depIdxs,
		EnumInfos:         file_moego_models_enterprise_v1_specific_support_models_proto_enumTypes,
		MessageInfos:      file_moego_models_enterprise_v1_specific_support_models_proto_msgTypes,
	}.Build()
	File_moego_models_enterprise_v1_specific_support_models_proto = out.File
	file_moego_models_enterprise_v1_specific_support_models_proto_rawDesc = nil
	file_moego_models_enterprise_v1_specific_support_models_proto_goTypes = nil
	file_moego_models_enterprise_v1_specific_support_models_proto_depIdxs = nil
}
