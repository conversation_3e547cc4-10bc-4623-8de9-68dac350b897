// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/enterprise/v1/tenant_group_models.proto

package enterprisepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// tenant status
// enum for tenant status
type TenantGroupModel_Status int32

const (
	// UNSPECIFIED is the default value
	TenantGroupModel_TENANT_GROUP_STATUS_UNSPECIFIED TenantGroupModel_Status = 0
	// normal
	TenantGroupModel_NORMAL TenantGroupModel_Status = 1
	// delete
	TenantGroupModel_DELETE TenantGroupModel_Status = 2
)

// Enum value maps for TenantGroupModel_Status.
var (
	TenantGroupModel_Status_name = map[int32]string{
		0: "TENANT_GROUP_STATUS_UNSPECIFIED",
		1: "NORMAL",
		2: "DELETE",
	}
	TenantGroupModel_Status_value = map[string]int32{
		"TENANT_GROUP_STATUS_UNSPECIFIED": 0,
		"NORMAL":                          1,
		"DELETE":                          2,
	}
)

func (x TenantGroupModel_Status) Enum() *TenantGroupModel_Status {
	p := new(TenantGroupModel_Status)
	*p = x
	return p
}

func (x TenantGroupModel_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TenantGroupModel_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_enterprise_v1_tenant_group_models_proto_enumTypes[0].Descriptor()
}

func (TenantGroupModel_Status) Type() protoreflect.EnumType {
	return &file_moego_models_enterprise_v1_tenant_group_models_proto_enumTypes[0]
}

func (x TenantGroupModel_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TenantGroupModel_Status.Descriptor instead.
func (TenantGroupModel_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_tenant_group_models_proto_rawDescGZIP(), []int{0, 0}
}

// tenant group model
type TenantGroupModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tenant group id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// tenant name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// status
	Status TenantGroupModel_Status `protobuf:"varint,3,opt,name=status,proto3,enum=moego.models.enterprise.v1.TenantGroupModel_Status" json:"status,omitempty"`
}

func (x *TenantGroupModel) Reset() {
	*x = TenantGroupModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_tenant_group_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TenantGroupModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TenantGroupModel) ProtoMessage() {}

func (x *TenantGroupModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_tenant_group_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TenantGroupModel.ProtoReflect.Descriptor instead.
func (*TenantGroupModel) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_tenant_group_models_proto_rawDescGZIP(), []int{0}
}

func (x *TenantGroupModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TenantGroupModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TenantGroupModel) GetStatus() TenantGroupModel_Status {
	if x != nil {
		return x.Status
	}
	return TenantGroupModel_TENANT_GROUP_STATUS_UNSPECIFIED
}

var File_moego_models_enterprise_v1_tenant_group_models_proto protoreflect.FileDescriptor

var file_moego_models_enterprise_v1_tenant_group_models_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x22, 0xca, 0x01, 0x0a, 0x10, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x45, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x23, 0x0a, 0x1f, 0x54, 0x45, 0x4e, 0x41, 0x4e, 0x54, 0x5f, 0x47, 0x52, 0x4f,
	0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f, 0x52, 0x4d, 0x41,
	0x4c, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x02, 0x42,
	0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_enterprise_v1_tenant_group_models_proto_rawDescOnce sync.Once
	file_moego_models_enterprise_v1_tenant_group_models_proto_rawDescData = file_moego_models_enterprise_v1_tenant_group_models_proto_rawDesc
)

func file_moego_models_enterprise_v1_tenant_group_models_proto_rawDescGZIP() []byte {
	file_moego_models_enterprise_v1_tenant_group_models_proto_rawDescOnce.Do(func() {
		file_moego_models_enterprise_v1_tenant_group_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_enterprise_v1_tenant_group_models_proto_rawDescData)
	})
	return file_moego_models_enterprise_v1_tenant_group_models_proto_rawDescData
}

var file_moego_models_enterprise_v1_tenant_group_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_enterprise_v1_tenant_group_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_enterprise_v1_tenant_group_models_proto_goTypes = []interface{}{
	(TenantGroupModel_Status)(0), // 0: moego.models.enterprise.v1.TenantGroupModel.Status
	(*TenantGroupModel)(nil),     // 1: moego.models.enterprise.v1.TenantGroupModel
}
var file_moego_models_enterprise_v1_tenant_group_models_proto_depIdxs = []int32{
	0, // 0: moego.models.enterprise.v1.TenantGroupModel.status:type_name -> moego.models.enterprise.v1.TenantGroupModel.Status
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_models_enterprise_v1_tenant_group_models_proto_init() }
func file_moego_models_enterprise_v1_tenant_group_models_proto_init() {
	if File_moego_models_enterprise_v1_tenant_group_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_enterprise_v1_tenant_group_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TenantGroupModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_enterprise_v1_tenant_group_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_enterprise_v1_tenant_group_models_proto_goTypes,
		DependencyIndexes: file_moego_models_enterprise_v1_tenant_group_models_proto_depIdxs,
		EnumInfos:         file_moego_models_enterprise_v1_tenant_group_models_proto_enumTypes,
		MessageInfos:      file_moego_models_enterprise_v1_tenant_group_models_proto_msgTypes,
	}.Build()
	File_moego_models_enterprise_v1_tenant_group_models_proto = out.File
	file_moego_models_enterprise_v1_tenant_group_models_proto_rawDesc = nil
	file_moego_models_enterprise_v1_tenant_group_models_proto_goTypes = nil
	file_moego_models_enterprise_v1_tenant_group_models_proto_depIdxs = nil
}
