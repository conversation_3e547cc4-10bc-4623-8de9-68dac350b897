// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/enterprise/v1/enterprise_defs.proto

package enterprisepb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create enterprise def
type CreateEnterpriseDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// address
	Address *AddressDef `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// email
	Email string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	// staff avatar path
	LogoPath string `protobuf:"bytes,4,opt,name=logo_path,json=logoPath,proto3" json:"logo_path,omitempty"`
	// theme color
	ThemeColor string `protobuf:"bytes,5,opt,name=theme_color,json=themeColor,proto3" json:"theme_color,omitempty"`
	// currency code
	CurrencyCode string `protobuf:"bytes,6,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// currency symbol
	CurrencySymbol string `protobuf:"bytes,7,opt,name=currency_symbol,json=currencySymbol,proto3" json:"currency_symbol,omitempty"`
	// whether the notification sound is on
	NotificationSoundEnable bool `protobuf:"varint,8,opt,name=notification_sound_enable,json=notificationSoundEnable,proto3" json:"notification_sound_enable,omitempty"`
	// date format
	DateFormatType DateFormat `protobuf:"varint,9,opt,name=date_format_type,json=dateFormatType,proto3,enum=moego.models.enterprise.v1.DateFormat" json:"date_format_type,omitempty"`
	// time format
	TimeFormatType TimeFormat `protobuf:"varint,10,opt,name=time_format_type,json=timeFormatType,proto3,enum=moego.models.enterprise.v1.TimeFormat" json:"time_format_type,omitempty"`
	// unit of weight
	UnitOfWeightType WeightUnit `protobuf:"varint,11,opt,name=unit_of_weight_type,json=unitOfWeightType,proto3,enum=moego.models.enterprise.v1.WeightUnit" json:"unit_of_weight_type,omitempty"`
	// unit of distance
	UnitOfDistanceType DistanceUnit `protobuf:"varint,12,opt,name=unit_of_distance_type,json=unitOfDistanceType,proto3,enum=moego.models.enterprise.v1.DistanceUnit" json:"unit_of_distance_type,omitempty"`
	// timezone
	TimeZone *TimeZone `protobuf:"bytes,13,opt,name=time_zone,json=timeZone,proto3" json:"time_zone,omitempty"`
	// source
	Source EnterpriseModel_Source `protobuf:"varint,14,opt,name=source,proto3,enum=moego.models.enterprise.v1.EnterpriseModel_Source" json:"source,omitempty"`
}

func (x *CreateEnterpriseDef) Reset() {
	*x = CreateEnterpriseDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_enterprise_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEnterpriseDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEnterpriseDef) ProtoMessage() {}

func (x *CreateEnterpriseDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_enterprise_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEnterpriseDef.ProtoReflect.Descriptor instead.
func (*CreateEnterpriseDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_enterprise_defs_proto_rawDescGZIP(), []int{0}
}

func (x *CreateEnterpriseDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateEnterpriseDef) GetAddress() *AddressDef {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *CreateEnterpriseDef) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateEnterpriseDef) GetLogoPath() string {
	if x != nil {
		return x.LogoPath
	}
	return ""
}

func (x *CreateEnterpriseDef) GetThemeColor() string {
	if x != nil {
		return x.ThemeColor
	}
	return ""
}

func (x *CreateEnterpriseDef) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *CreateEnterpriseDef) GetCurrencySymbol() string {
	if x != nil {
		return x.CurrencySymbol
	}
	return ""
}

func (x *CreateEnterpriseDef) GetNotificationSoundEnable() bool {
	if x != nil {
		return x.NotificationSoundEnable
	}
	return false
}

func (x *CreateEnterpriseDef) GetDateFormatType() DateFormat {
	if x != nil {
		return x.DateFormatType
	}
	return DateFormat_DATE_FORMAT_UNSPECIFIED
}

func (x *CreateEnterpriseDef) GetTimeFormatType() TimeFormat {
	if x != nil {
		return x.TimeFormatType
	}
	return TimeFormat_TIME_FORMAT_UNSPECIFIED
}

func (x *CreateEnterpriseDef) GetUnitOfWeightType() WeightUnit {
	if x != nil {
		return x.UnitOfWeightType
	}
	return WeightUnit_WEIGHT_UNIT_UNSPECIFIED
}

func (x *CreateEnterpriseDef) GetUnitOfDistanceType() DistanceUnit {
	if x != nil {
		return x.UnitOfDistanceType
	}
	return DistanceUnit_DISTANCE_UNIT_UNSPECIFIED
}

func (x *CreateEnterpriseDef) GetTimeZone() *TimeZone {
	if x != nil {
		return x.TimeZone
	}
	return nil
}

func (x *CreateEnterpriseDef) GetSource() EnterpriseModel_Source {
	if x != nil {
		return x.Source
	}
	return EnterpriseModel_NORMALLY_ADD
}

// UpdateEnterpriseDef
type UpdateEnterpriseDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name
	Name *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// address
	Address *AddressDef `protobuf:"bytes,2,opt,name=address,proto3,oneof" json:"address,omitempty"`
	// email
	Email *string `protobuf:"bytes,3,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// staff avatar path
	LogoPath *string `protobuf:"bytes,4,opt,name=logo_path,json=logoPath,proto3,oneof" json:"logo_path,omitempty"`
	// theme color
	ThemeColor *string `protobuf:"bytes,5,opt,name=theme_color,json=themeColor,proto3,oneof" json:"theme_color,omitempty"`
	// currency code
	CurrencyCode *string `protobuf:"bytes,6,opt,name=currency_code,json=currencyCode,proto3,oneof" json:"currency_code,omitempty"`
	// currency symbol
	CurrencySymbol *string `protobuf:"bytes,7,opt,name=currency_symbol,json=currencySymbol,proto3,oneof" json:"currency_symbol,omitempty"`
	// whether the notification sound is on
	NotificationSoundEnable *bool `protobuf:"varint,8,opt,name=notification_sound_enable,json=notificationSoundEnable,proto3,oneof" json:"notification_sound_enable,omitempty"`
	// date format
	DateFormatType *DateFormat `protobuf:"varint,9,opt,name=date_format_type,json=dateFormatType,proto3,enum=moego.models.enterprise.v1.DateFormat,oneof" json:"date_format_type,omitempty"`
	// time format
	TimeFormatType *TimeFormat `protobuf:"varint,10,opt,name=time_format_type,json=timeFormatType,proto3,enum=moego.models.enterprise.v1.TimeFormat,oneof" json:"time_format_type,omitempty"`
	// unit of weight
	UnitOfWeightType *WeightUnit `protobuf:"varint,11,opt,name=unit_of_weight_type,json=unitOfWeightType,proto3,enum=moego.models.enterprise.v1.WeightUnit,oneof" json:"unit_of_weight_type,omitempty"`
	// unit of distance
	UnitOfDistanceType *DistanceUnit `protobuf:"varint,12,opt,name=unit_of_distance_type,json=unitOfDistanceType,proto3,enum=moego.models.enterprise.v1.DistanceUnit,oneof" json:"unit_of_distance_type,omitempty"`
	// timezone
	TimeZone *TimeZone `protobuf:"bytes,13,opt,name=time_zone,json=timeZone,proto3,oneof" json:"time_zone,omitempty"`
}

func (x *UpdateEnterpriseDef) Reset() {
	*x = UpdateEnterpriseDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_enterprise_v1_enterprise_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEnterpriseDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEnterpriseDef) ProtoMessage() {}

func (x *UpdateEnterpriseDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_enterprise_v1_enterprise_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEnterpriseDef.ProtoReflect.Descriptor instead.
func (*UpdateEnterpriseDef) Descriptor() ([]byte, []int) {
	return file_moego_models_enterprise_v1_enterprise_defs_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateEnterpriseDef) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateEnterpriseDef) GetAddress() *AddressDef {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *UpdateEnterpriseDef) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *UpdateEnterpriseDef) GetLogoPath() string {
	if x != nil && x.LogoPath != nil {
		return *x.LogoPath
	}
	return ""
}

func (x *UpdateEnterpriseDef) GetThemeColor() string {
	if x != nil && x.ThemeColor != nil {
		return *x.ThemeColor
	}
	return ""
}

func (x *UpdateEnterpriseDef) GetCurrencyCode() string {
	if x != nil && x.CurrencyCode != nil {
		return *x.CurrencyCode
	}
	return ""
}

func (x *UpdateEnterpriseDef) GetCurrencySymbol() string {
	if x != nil && x.CurrencySymbol != nil {
		return *x.CurrencySymbol
	}
	return ""
}

func (x *UpdateEnterpriseDef) GetNotificationSoundEnable() bool {
	if x != nil && x.NotificationSoundEnable != nil {
		return *x.NotificationSoundEnable
	}
	return false
}

func (x *UpdateEnterpriseDef) GetDateFormatType() DateFormat {
	if x != nil && x.DateFormatType != nil {
		return *x.DateFormatType
	}
	return DateFormat_DATE_FORMAT_UNSPECIFIED
}

func (x *UpdateEnterpriseDef) GetTimeFormatType() TimeFormat {
	if x != nil && x.TimeFormatType != nil {
		return *x.TimeFormatType
	}
	return TimeFormat_TIME_FORMAT_UNSPECIFIED
}

func (x *UpdateEnterpriseDef) GetUnitOfWeightType() WeightUnit {
	if x != nil && x.UnitOfWeightType != nil {
		return *x.UnitOfWeightType
	}
	return WeightUnit_WEIGHT_UNIT_UNSPECIFIED
}

func (x *UpdateEnterpriseDef) GetUnitOfDistanceType() DistanceUnit {
	if x != nil && x.UnitOfDistanceType != nil {
		return *x.UnitOfDistanceType
	}
	return DistanceUnit_DISTANCE_UNIT_UNSPECIFIED
}

func (x *UpdateEnterpriseDef) GetTimeZone() *TimeZone {
	if x != nil {
		return x.TimeZone
	}
	return nil
}

var File_moego_models_enterprise_v1_enterprise_defs_proto protoreflect.FileDescriptor

var file_moego_models_enterprise_v1_enterprise_defs_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x31,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76,
	0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x97, 0x07, 0x0a, 0x13, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x44,
	0x65, 0x66, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x40, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x66, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x22, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x72, 0x07, 0x10, 0x01, 0x18, 0xff, 0x01, 0x60, 0x01,
	0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x6f, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x67, 0x6f,
	0x50, 0x61, 0x74, 0x68, 0x12, 0x2a, 0x0a, 0x0b, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x14, 0x52, 0x0a, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x2d, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x98, 0x01,
	0x03, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x30, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73, 0x79, 0x6d, 0x62,
	0x6f, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x12, 0x3a, 0x0a, 0x19, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x6e, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x5c, 0x0a,
	0x10, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0e, 0x64, 0x61, 0x74,
	0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5c, 0x0a, 0x10, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x46,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x61, 0x0a, 0x13, 0x75, 0x6e, 0x69,
	0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x10, 0x75, 0x6e, 0x69, 0x74,
	0x4f, 0x66, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x67, 0x0a, 0x15,
	0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x55, 0x6e, 0x69, 0x74, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20,
	0x00, 0x52, 0x12, 0x75, 0x6e, 0x69, 0x74, 0x4f, 0x66, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x41, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f,
	0x6e, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x52, 0x08,
	0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x4a, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x22, 0xf7, 0x08, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x44, 0x65, 0x66, 0x12, 0x22, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x32, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x45, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x66, 0x48, 0x01, 0x52, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x72, 0x07, 0x10, 0x01, 0x18,
	0xff, 0x01, 0x60, 0x01, 0x48, 0x02, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01,
	0x12, 0x20, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x50, 0x61, 0x74, 0x68, 0x88,
	0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x14, 0x48, 0x04, 0x52, 0x0a, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x88, 0x01, 0x01, 0x12, 0x32, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72,
	0x03, 0x98, 0x01, 0x03, 0x48, 0x05, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x48, 0x06, 0x52, 0x0e, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x3f,
	0x0a, 0x19, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x07, 0x52, 0x17, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x6f, 0x75, 0x6e, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x61, 0x0a, 0x10, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x08, 0x52,
	0x0e, 0x64, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x61, 0x0a, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x48, 0x09, 0x52, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x66, 0x0a, 0x13, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6f, 0x66,
	0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x0a, 0x52, 0x10, 0x75, 0x6e, 0x69, 0x74, 0x4f, 0x66,
	0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x6c, 0x0a,
	0x15, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01,
	0x20, 0x00, 0x48, 0x0b, 0x52, 0x12, 0x75, 0x6e, 0x69, 0x74, 0x4f, 0x66, 0x44, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x46, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x5a, 0x6f, 0x6e, 0x65, 0x48, 0x0c, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65,
	0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0a, 0x0a, 0x08,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f,
	0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x42, 0x1c, 0x0a, 0x1a, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x16,
	0x0a, 0x14, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f,
	0x6f, 0x66, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x42, 0x84,
	0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_enterprise_v1_enterprise_defs_proto_rawDescOnce sync.Once
	file_moego_models_enterprise_v1_enterprise_defs_proto_rawDescData = file_moego_models_enterprise_v1_enterprise_defs_proto_rawDesc
)

func file_moego_models_enterprise_v1_enterprise_defs_proto_rawDescGZIP() []byte {
	file_moego_models_enterprise_v1_enterprise_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_enterprise_v1_enterprise_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_enterprise_v1_enterprise_defs_proto_rawDescData)
	})
	return file_moego_models_enterprise_v1_enterprise_defs_proto_rawDescData
}

var file_moego_models_enterprise_v1_enterprise_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_enterprise_v1_enterprise_defs_proto_goTypes = []interface{}{
	(*CreateEnterpriseDef)(nil), // 0: moego.models.enterprise.v1.CreateEnterpriseDef
	(*UpdateEnterpriseDef)(nil), // 1: moego.models.enterprise.v1.UpdateEnterpriseDef
	(*AddressDef)(nil),          // 2: moego.models.enterprise.v1.AddressDef
	(DateFormat)(0),             // 3: moego.models.enterprise.v1.DateFormat
	(TimeFormat)(0),             // 4: moego.models.enterprise.v1.TimeFormat
	(WeightUnit)(0),             // 5: moego.models.enterprise.v1.WeightUnit
	(DistanceUnit)(0),           // 6: moego.models.enterprise.v1.DistanceUnit
	(*TimeZone)(nil),            // 7: moego.models.enterprise.v1.TimeZone
	(EnterpriseModel_Source)(0), // 8: moego.models.enterprise.v1.EnterpriseModel.Source
}
var file_moego_models_enterprise_v1_enterprise_defs_proto_depIdxs = []int32{
	2,  // 0: moego.models.enterprise.v1.CreateEnterpriseDef.address:type_name -> moego.models.enterprise.v1.AddressDef
	3,  // 1: moego.models.enterprise.v1.CreateEnterpriseDef.date_format_type:type_name -> moego.models.enterprise.v1.DateFormat
	4,  // 2: moego.models.enterprise.v1.CreateEnterpriseDef.time_format_type:type_name -> moego.models.enterprise.v1.TimeFormat
	5,  // 3: moego.models.enterprise.v1.CreateEnterpriseDef.unit_of_weight_type:type_name -> moego.models.enterprise.v1.WeightUnit
	6,  // 4: moego.models.enterprise.v1.CreateEnterpriseDef.unit_of_distance_type:type_name -> moego.models.enterprise.v1.DistanceUnit
	7,  // 5: moego.models.enterprise.v1.CreateEnterpriseDef.time_zone:type_name -> moego.models.enterprise.v1.TimeZone
	8,  // 6: moego.models.enterprise.v1.CreateEnterpriseDef.source:type_name -> moego.models.enterprise.v1.EnterpriseModel.Source
	2,  // 7: moego.models.enterprise.v1.UpdateEnterpriseDef.address:type_name -> moego.models.enterprise.v1.AddressDef
	3,  // 8: moego.models.enterprise.v1.UpdateEnterpriseDef.date_format_type:type_name -> moego.models.enterprise.v1.DateFormat
	4,  // 9: moego.models.enterprise.v1.UpdateEnterpriseDef.time_format_type:type_name -> moego.models.enterprise.v1.TimeFormat
	5,  // 10: moego.models.enterprise.v1.UpdateEnterpriseDef.unit_of_weight_type:type_name -> moego.models.enterprise.v1.WeightUnit
	6,  // 11: moego.models.enterprise.v1.UpdateEnterpriseDef.unit_of_distance_type:type_name -> moego.models.enterprise.v1.DistanceUnit
	7,  // 12: moego.models.enterprise.v1.UpdateEnterpriseDef.time_zone:type_name -> moego.models.enterprise.v1.TimeZone
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_moego_models_enterprise_v1_enterprise_defs_proto_init() }
func file_moego_models_enterprise_v1_enterprise_defs_proto_init() {
	if File_moego_models_enterprise_v1_enterprise_defs_proto != nil {
		return
	}
	file_moego_models_enterprise_v1_enterprise_enums_proto_init()
	file_moego_models_enterprise_v1_enterprise_models_proto_init()
	file_moego_models_enterprise_v1_time_zone_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_enterprise_v1_enterprise_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEnterpriseDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_enterprise_v1_enterprise_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEnterpriseDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_enterprise_v1_enterprise_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_enterprise_v1_enterprise_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_enterprise_v1_enterprise_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_enterprise_v1_enterprise_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_enterprise_v1_enterprise_defs_proto_msgTypes,
	}.Build()
	File_moego_models_enterprise_v1_enterprise_defs_proto = out.File
	file_moego_models_enterprise_v1_enterprise_defs_proto_rawDesc = nil
	file_moego_models_enterprise_v1_enterprise_defs_proto_goTypes = nil
	file_moego_models_enterprise_v1_enterprise_defs_proto_depIdxs = nil
}
