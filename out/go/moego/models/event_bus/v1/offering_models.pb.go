// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/event_bus/v1/offering_models.proto

package eventbuspb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	interval "google.golang.org/genproto/googleapis/type/interval"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GroupClassSession
type GroupClassSessionEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// session
	Session *v1.GroupClassSession `protobuf:"bytes,1,opt,name=session,proto3" json:"session,omitempty"`
	// before interval
	BeforeInterval *interval.Interval `protobuf:"bytes,2,opt,name=before_interval,json=beforeInterval,proto3" json:"before_interval,omitempty"`
	// before duration
	BeforeDuration *durationpb.Duration `protobuf:"bytes,3,opt,name=before_duration,json=beforeDuration,proto3" json:"before_duration,omitempty"`
}

func (x *GroupClassSessionEvent) Reset() {
	*x = GroupClassSessionEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_event_bus_v1_offering_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupClassSessionEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupClassSessionEvent) ProtoMessage() {}

func (x *GroupClassSessionEvent) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_event_bus_v1_offering_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupClassSessionEvent.ProtoReflect.Descriptor instead.
func (*GroupClassSessionEvent) Descriptor() ([]byte, []int) {
	return file_moego_models_event_bus_v1_offering_models_proto_rawDescGZIP(), []int{0}
}

func (x *GroupClassSessionEvent) GetSession() *v1.GroupClassSession {
	if x != nil {
		return x.Session
	}
	return nil
}

func (x *GroupClassSessionEvent) GetBeforeInterval() *interval.Interval {
	if x != nil {
		return x.BeforeInterval
	}
	return nil
}

func (x *GroupClassSessionEvent) GetBeforeDuration() *durationpb.Duration {
	if x != nil {
		return x.BeforeDuration
	}
	return nil
}

var File_moego_models_event_bus_v1_offering_models_proto protoreflect.FileDescriptor

var file_moego_models_event_bus_v1_offering_models_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe3, 0x01, 0x0a, 0x16,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x45, 0x0a, 0x07, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x0a,
	0x0f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x0e, 0x62,
	0x65, 0x66, 0x6f, 0x72, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x42, 0x0a,
	0x0f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0e, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x80, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x59, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x62,
	0x75, 0x73, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_event_bus_v1_offering_models_proto_rawDescOnce sync.Once
	file_moego_models_event_bus_v1_offering_models_proto_rawDescData = file_moego_models_event_bus_v1_offering_models_proto_rawDesc
)

func file_moego_models_event_bus_v1_offering_models_proto_rawDescGZIP() []byte {
	file_moego_models_event_bus_v1_offering_models_proto_rawDescOnce.Do(func() {
		file_moego_models_event_bus_v1_offering_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_event_bus_v1_offering_models_proto_rawDescData)
	})
	return file_moego_models_event_bus_v1_offering_models_proto_rawDescData
}

var file_moego_models_event_bus_v1_offering_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_event_bus_v1_offering_models_proto_goTypes = []interface{}{
	(*GroupClassSessionEvent)(nil), // 0: moego.models.event_bus.v1.GroupClassSessionEvent
	(*v1.GroupClassSession)(nil),   // 1: moego.models.offering.v1.GroupClassSession
	(*interval.Interval)(nil),      // 2: google.type.Interval
	(*durationpb.Duration)(nil),    // 3: google.protobuf.Duration
}
var file_moego_models_event_bus_v1_offering_models_proto_depIdxs = []int32{
	1, // 0: moego.models.event_bus.v1.GroupClassSessionEvent.session:type_name -> moego.models.offering.v1.GroupClassSession
	2, // 1: moego.models.event_bus.v1.GroupClassSessionEvent.before_interval:type_name -> google.type.Interval
	3, // 2: moego.models.event_bus.v1.GroupClassSessionEvent.before_duration:type_name -> google.protobuf.Duration
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_event_bus_v1_offering_models_proto_init() }
func file_moego_models_event_bus_v1_offering_models_proto_init() {
	if File_moego_models_event_bus_v1_offering_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_event_bus_v1_offering_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupClassSessionEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_event_bus_v1_offering_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_event_bus_v1_offering_models_proto_goTypes,
		DependencyIndexes: file_moego_models_event_bus_v1_offering_models_proto_depIdxs,
		MessageInfos:      file_moego_models_event_bus_v1_offering_models_proto_msgTypes,
	}.Build()
	File_moego_models_event_bus_v1_offering_models_proto = out.File
	file_moego_models_event_bus_v1_offering_models_proto_rawDesc = nil
	file_moego_models_event_bus_v1_offering_models_proto_goTypes = nil
	file_moego_models_event_bus_v1_offering_models_proto_depIdxs = nil
}
