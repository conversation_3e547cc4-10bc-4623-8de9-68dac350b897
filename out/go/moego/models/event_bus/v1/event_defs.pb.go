// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/event_bus/v1/event_defs.proto

package eventbuspb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Event is the base message for all events
// Event type corresponds to a structure
type EventType int32

const (
	// TYPE_UNSPECIFIED
	// (-- api-linter: core::0126::unspecified=disabled
	//
	//	aip.dev/not-precedent: Will lead to breaking change. --)
	EventType_TYPE_UNSPECIFIED EventType = 0
	// online booking
	// ONLINE_BOOKING_SUBMITTED
	EventType_ONLINE_BOOKING_SUBMITTED EventType = 101
	// ONLINE_BOOKING_ABANDONED
	EventType_ONLINE_BOOKING_ABANDONED EventType = 102
	// ONLINE_BOOKING_ACCEPTED
	EventType_ONLINE_BOOKING_ACCEPTED EventType = 103
	// appointment
	// APPOINTMENT_CREATED
	EventType_APPOINTMENT_CREATED EventType = 201
	// APPOINTMENT_CANCELED
	EventType_APPOINTMENT_CANCELED EventType = 202
	// APPOINTMENT_FINISHED
	EventType_APPOINTMENT_FINISHED EventType = 203
	// APPOINTMENT_DELETED
	EventType_APPOINTMENT_DELETED EventType = 204
	// APPOINTMENT_UPDATED
	EventType_APPOINTMENT_UPDATED EventType = 205
	// intake form
	// INTAKE_FORM_SUBMITTED
	EventType_INTAKE_FORM_SUBMITTED EventType = 301
	// product
	// PRODUCT_PURCHASED
	EventType_PRODUCT_PURCHASED EventType = 401
	// package
	// PACKAGE_PURCHASED
	EventType_PACKAGE_PURCHASED EventType = 501
	// PACKAGE_REDEEMED
	EventType_PACKAGE_REDEEMED EventType = 502
	// membership
	// MEMBERSHIP_PURCHASED
	EventType_MEMBERSHIP_PURCHASED EventType = 601
	// MEMBERSHIP_CANCELED
	EventType_MEMBERSHIP_CANCELED EventType = 602
	// discount code
	// DISCOUNT_CODE_REDEEMED
	EventType_DISCOUNT_CODE_REDEEMED EventType = 701
	// order
	// ORDER_FULLY_PAID
	// Deprecated: please use ORDER_COMPLETED.
	EventType_ORDER_FULLY_PAID EventType = 801
	// ORDER_COMPLETED
	EventType_ORDER_COMPLETED EventType = 802
	// REFUND_ORDER_COMPLETED
	EventType_REFUND_ORDER_COMPLETED EventType = 803
	// ORDER_CANCELED
	EventType_ORDER_CANCELED EventType = 804
	// ORDER_CREATED
	EventType_ORDER_CREATED EventType = 805
	// message
	// MESSAGE_SENT
	EventType_MESSAGE_SENT EventType = 901
	// customer
	// CUSTOMER_CREATED
	EventType_CUSTOMER_CREATED EventType = 1001
	// payment: 1101~1200
	// PAYMENT_STATUS_CHANGED
	EventType_PAYMENT_STATUS_CHANGED EventType = 1101
	// PAYMENT_REFUND_STATUS_CHANGED
	EventType_PAYMENT_REFUND_STATUS_CHANGED EventType = 1102
	// DISPUTE_FUNDING_OPERATE
	EventType_DISPUTE_FUNDING_OPERATE EventType = 1103
	// subscription
	// SUBSCRIPTION_UPDATED
	EventType_SUBSCRIPTION_UPDATED EventType = 1201
	// SUBSCRIPTION_PAYMENT_FAILED
	EventType_SUBSCRIPTION_PAYMENT_FAILED EventType = 1202
	// capital
	// CAPITAL_LOAN_OFFER_PAYOUT
	// 注意：这个事件不是 loan offer 流转到 PAID_OUT 状态时发出，而是 payout 交易发生时发出
	EventType_CAPITAL_LOAN_OFFER_PAYOUT EventType = 1301
	// CAPITAL_LOAN_TRANSACTION_UPDATED
	EventType_CAPITAL_LOAN_TRANSACTION_UPDATED EventType = 1302
	// engagement
	// CALL_UPDATE_STATUS
	EventType_CALL_UPDATE_STATUS EventType = 1401
	// offering 2001-2099
	// update offering group class session
	EventType_OFFERING_GROUP_CLASS_SESSION_UPDATED EventType = 2001
)

// Enum value maps for EventType.
var (
	EventType_name = map[int32]string{
		0:    "TYPE_UNSPECIFIED",
		101:  "ONLINE_BOOKING_SUBMITTED",
		102:  "ONLINE_BOOKING_ABANDONED",
		103:  "ONLINE_BOOKING_ACCEPTED",
		201:  "APPOINTMENT_CREATED",
		202:  "APPOINTMENT_CANCELED",
		203:  "APPOINTMENT_FINISHED",
		204:  "APPOINTMENT_DELETED",
		205:  "APPOINTMENT_UPDATED",
		301:  "INTAKE_FORM_SUBMITTED",
		401:  "PRODUCT_PURCHASED",
		501:  "PACKAGE_PURCHASED",
		502:  "PACKAGE_REDEEMED",
		601:  "MEMBERSHIP_PURCHASED",
		602:  "MEMBERSHIP_CANCELED",
		701:  "DISCOUNT_CODE_REDEEMED",
		801:  "ORDER_FULLY_PAID",
		802:  "ORDER_COMPLETED",
		803:  "REFUND_ORDER_COMPLETED",
		804:  "ORDER_CANCELED",
		805:  "ORDER_CREATED",
		901:  "MESSAGE_SENT",
		1001: "CUSTOMER_CREATED",
		1101: "PAYMENT_STATUS_CHANGED",
		1102: "PAYMENT_REFUND_STATUS_CHANGED",
		1103: "DISPUTE_FUNDING_OPERATE",
		1201: "SUBSCRIPTION_UPDATED",
		1202: "SUBSCRIPTION_PAYMENT_FAILED",
		1301: "CAPITAL_LOAN_OFFER_PAYOUT",
		1302: "CAPITAL_LOAN_TRANSACTION_UPDATED",
		1401: "CALL_UPDATE_STATUS",
		2001: "OFFERING_GROUP_CLASS_SESSION_UPDATED",
	}
	EventType_value = map[string]int32{
		"TYPE_UNSPECIFIED":                     0,
		"ONLINE_BOOKING_SUBMITTED":             101,
		"ONLINE_BOOKING_ABANDONED":             102,
		"ONLINE_BOOKING_ACCEPTED":              103,
		"APPOINTMENT_CREATED":                  201,
		"APPOINTMENT_CANCELED":                 202,
		"APPOINTMENT_FINISHED":                 203,
		"APPOINTMENT_DELETED":                  204,
		"APPOINTMENT_UPDATED":                  205,
		"INTAKE_FORM_SUBMITTED":                301,
		"PRODUCT_PURCHASED":                    401,
		"PACKAGE_PURCHASED":                    501,
		"PACKAGE_REDEEMED":                     502,
		"MEMBERSHIP_PURCHASED":                 601,
		"MEMBERSHIP_CANCELED":                  602,
		"DISCOUNT_CODE_REDEEMED":               701,
		"ORDER_FULLY_PAID":                     801,
		"ORDER_COMPLETED":                      802,
		"REFUND_ORDER_COMPLETED":               803,
		"ORDER_CANCELED":                       804,
		"ORDER_CREATED":                        805,
		"MESSAGE_SENT":                         901,
		"CUSTOMER_CREATED":                     1001,
		"PAYMENT_STATUS_CHANGED":               1101,
		"PAYMENT_REFUND_STATUS_CHANGED":        1102,
		"DISPUTE_FUNDING_OPERATE":              1103,
		"SUBSCRIPTION_UPDATED":                 1201,
		"SUBSCRIPTION_PAYMENT_FAILED":          1202,
		"CAPITAL_LOAN_OFFER_PAYOUT":            1301,
		"CAPITAL_LOAN_TRANSACTION_UPDATED":     1302,
		"CALL_UPDATE_STATUS":                   1401,
		"OFFERING_GROUP_CLASS_SESSION_UPDATED": 2001,
	}
)

func (x EventType) Enum() *EventType {
	p := new(EventType)
	*p = x
	return p
}

func (x EventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EventType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_event_bus_v1_event_defs_proto_enumTypes[0].Descriptor()
}

func (EventType) Type() protoreflect.EnumType {
	return &file_moego_models_event_bus_v1_event_defs_proto_enumTypes[0]
}

func (x EventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EventType.Descriptor instead.
func (EventType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_event_bus_v1_event_defs_proto_rawDescGZIP(), []int{0}
}

var File_moego_models_event_bus_v1_event_defs_proto protoreflect.FileDescriptor

var file_moego_models_event_bus_v1_event_defs_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x2a, 0xf7, 0x06, 0x0a, 0x09, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x4f,
	0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x55,
	0x42, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x44, 0x10, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x4f, 0x4e, 0x4c,
	0x49, 0x4e, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x42, 0x41, 0x4e,
	0x44, 0x4f, 0x4e, 0x45, 0x44, 0x10, 0x66, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x4e, 0x4c, 0x49, 0x4e,
	0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54,
	0x45, 0x44, 0x10, 0x67, 0x12, 0x18, 0x0a, 0x13, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0xc9, 0x01, 0x12, 0x19,
	0x0a, 0x14, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41,
	0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0xca, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x41, 0x50, 0x50,
	0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x45,
	0x44, 0x10, 0xcb, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0xcc, 0x01, 0x12, 0x18,
	0x0a, 0x13, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x44, 0x10, 0xcd, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x49, 0x4e, 0x54, 0x41,
	0x4b, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x54, 0x45,
	0x44, 0x10, 0xad, 0x02, 0x12, 0x16, 0x0a, 0x11, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f,
	0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x44, 0x10, 0x91, 0x03, 0x12, 0x16, 0x0a, 0x11,
	0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x5f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45,
	0x44, 0x10, 0xf5, 0x03, 0x12, 0x15, 0x0a, 0x10, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x5f,
	0x52, 0x45, 0x44, 0x45, 0x45, 0x4d, 0x45, 0x44, 0x10, 0xf6, 0x03, 0x12, 0x19, 0x0a, 0x14, 0x4d,
	0x45, 0x4d, 0x42, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x5f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41,
	0x53, 0x45, 0x44, 0x10, 0xd9, 0x04, 0x12, 0x18, 0x0a, 0x13, 0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52,
	0x53, 0x48, 0x49, 0x50, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0xda, 0x04,
	0x12, 0x1b, 0x0a, 0x16, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x52, 0x45, 0x44, 0x45, 0x45, 0x4d, 0x45, 0x44, 0x10, 0xbd, 0x05, 0x12, 0x15, 0x0a,
	0x10, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x59, 0x5f, 0x50, 0x41, 0x49,
	0x44, 0x10, 0xa1, 0x06, 0x12, 0x14, 0x0a, 0x0f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x43, 0x4f,
	0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0xa2, 0x06, 0x12, 0x1b, 0x0a, 0x16, 0x52, 0x45,
	0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c,
	0x45, 0x54, 0x45, 0x44, 0x10, 0xa3, 0x06, 0x12, 0x13, 0x0a, 0x0e, 0x4f, 0x52, 0x44, 0x45, 0x52,
	0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0xa4, 0x06, 0x12, 0x12, 0x0a, 0x0d,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0xa5, 0x06,
	0x12, 0x11, 0x0a, 0x0c, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x45, 0x4e, 0x54,
	0x10, 0x85, 0x07, 0x12, 0x15, 0x0a, 0x10, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0xe9, 0x07, 0x12, 0x1b, 0x0a, 0x16, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x48, 0x41,
	0x4e, 0x47, 0x45, 0x44, 0x10, 0xcd, 0x08, 0x12, 0x22, 0x0a, 0x1d, 0x50, 0x41, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x44, 0x10, 0xce, 0x08, 0x12, 0x1c, 0x0a, 0x17, 0x44,
	0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4f,
	0x50, 0x45, 0x52, 0x41, 0x54, 0x45, 0x10, 0xcf, 0x08, 0x12, 0x19, 0x0a, 0x14, 0x53, 0x55, 0x42,
	0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x44, 0x10, 0xb1, 0x09, 0x12, 0x20, 0x0a, 0x1b, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0xb2, 0x09, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x41, 0x50, 0x49, 0x54, 0x41,
	0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x59,
	0x4f, 0x55, 0x54, 0x10, 0x95, 0x0a, 0x12, 0x25, 0x0a, 0x20, 0x43, 0x41, 0x50, 0x49, 0x54, 0x41,
	0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x10, 0x96, 0x0a, 0x12, 0x17, 0x0a,
	0x12, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x10, 0xf9, 0x0a, 0x12, 0x29, 0x0a, 0x24, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x49,
	0x4e, 0x47, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x5f, 0x53,
	0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x10, 0xd1,
	0x0f, 0x42, 0x80, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x59, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x62,
	0x75, 0x73, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_event_bus_v1_event_defs_proto_rawDescOnce sync.Once
	file_moego_models_event_bus_v1_event_defs_proto_rawDescData = file_moego_models_event_bus_v1_event_defs_proto_rawDesc
)

func file_moego_models_event_bus_v1_event_defs_proto_rawDescGZIP() []byte {
	file_moego_models_event_bus_v1_event_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_event_bus_v1_event_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_event_bus_v1_event_defs_proto_rawDescData)
	})
	return file_moego_models_event_bus_v1_event_defs_proto_rawDescData
}

var file_moego_models_event_bus_v1_event_defs_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_event_bus_v1_event_defs_proto_goTypes = []interface{}{
	(EventType)(0), // 0: moego.models.event_bus.v1.EventType
}
var file_moego_models_event_bus_v1_event_defs_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_event_bus_v1_event_defs_proto_init() }
func file_moego_models_event_bus_v1_event_defs_proto_init() {
	if File_moego_models_event_bus_v1_event_defs_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_event_bus_v1_event_defs_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_event_bus_v1_event_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_event_bus_v1_event_defs_proto_depIdxs,
		EnumInfos:         file_moego_models_event_bus_v1_event_defs_proto_enumTypes,
	}.Build()
	File_moego_models_event_bus_v1_event_defs_proto = out.File
	file_moego_models_event_bus_v1_event_defs_proto_rawDesc = nil
	file_moego_models_event_bus_v1_event_defs_proto_goTypes = nil
	file_moego_models_event_bus_v1_event_defs_proto_depIdxs = nil
}
