// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/event_bus/v1/order_models.proto

package eventbuspb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// OrderEvent
type OrderEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// guid
	Guid string `protobuf:"bytes,2,opt,name=guid,proto3" json:"guid,omitempty"`
	// order type
	OrderType v1.OrderModel_OrderType `protobuf:"varint,3,opt,name=order_type,json=orderType,proto3,enum=moego.models.order.v1.OrderModel_OrderType" json:"order_type,omitempty"`
	// order status
	OrderStatus v1.OrderStatus `protobuf:"varint,4,opt,name=order_status,json=orderStatus,proto3,enum=moego.models.order.v1.OrderStatus" json:"order_status,omitempty"`
	// order source type
	SourceType v1.OrderSourceType `protobuf:"varint,5,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// payment status
	PaymentStatus v1.OrderModel_PaymentStatus `protobuf:"varint,6,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.order.v1.OrderModel_PaymentStatus" json:"payment_status,omitempty"`
	// fulfillment status
	FulfillmentStatus v1.OrderModel_FulfillmentStatus `protobuf:"varint,7,opt,name=fulfillment_status,json=fulfillmentStatus,proto3,enum=moego.models.order.v1.OrderModel_FulfillmentStatus" json:"fulfillment_status,omitempty"`
	// complete time
	CompleteTime int64 `protobuf:"varint,8,opt,name=complete_time,json=completeTime,proto3" json:"complete_time,omitempty"`
	// source id
	SourceId int64 `protobuf:"varint,9,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,10,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,11,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,12,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// order version
	OrderVersion int32 `protobuf:"varint,13,opt,name=order_version,json=orderVersion,proto3" json:"order_version,omitempty"`
}

func (x *OrderEvent) Reset() {
	*x = OrderEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_event_bus_v1_order_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderEvent) ProtoMessage() {}

func (x *OrderEvent) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_event_bus_v1_order_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderEvent.ProtoReflect.Descriptor instead.
func (*OrderEvent) Descriptor() ([]byte, []int) {
	return file_moego_models_event_bus_v1_order_models_proto_rawDescGZIP(), []int{0}
}

func (x *OrderEvent) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OrderEvent) GetGuid() string {
	if x != nil {
		return x.Guid
	}
	return ""
}

func (x *OrderEvent) GetOrderType() v1.OrderModel_OrderType {
	if x != nil {
		return x.OrderType
	}
	return v1.OrderModel_OrderType(0)
}

func (x *OrderEvent) GetOrderStatus() v1.OrderStatus {
	if x != nil {
		return x.OrderStatus
	}
	return v1.OrderStatus(0)
}

func (x *OrderEvent) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

func (x *OrderEvent) GetPaymentStatus() v1.OrderModel_PaymentStatus {
	if x != nil {
		return x.PaymentStatus
	}
	return v1.OrderModel_PaymentStatus(0)
}

func (x *OrderEvent) GetFulfillmentStatus() v1.OrderModel_FulfillmentStatus {
	if x != nil {
		return x.FulfillmentStatus
	}
	return v1.OrderModel_FulfillmentStatus(0)
}

func (x *OrderEvent) GetCompleteTime() int64 {
	if x != nil {
		return x.CompleteTime
	}
	return 0
}

func (x *OrderEvent) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *OrderEvent) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *OrderEvent) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *OrderEvent) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *OrderEvent) GetOrderVersion() int32 {
	if x != nil {
		return x.OrderVersion
	}
	return 0
}

// refund order event
type RefundOrderEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// refund order id
	RefundOrderId int64 `protobuf:"varint,1,opt,name=refund_order_id,json=refundOrderId,proto3" json:"refund_order_id,omitempty"`
	// order id
	OrderId int64 `protobuf:"varint,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *RefundOrderEvent) Reset() {
	*x = RefundOrderEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_event_bus_v1_order_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundOrderEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundOrderEvent) ProtoMessage() {}

func (x *RefundOrderEvent) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_event_bus_v1_order_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundOrderEvent.ProtoReflect.Descriptor instead.
func (*RefundOrderEvent) Descriptor() ([]byte, []int) {
	return file_moego_models_event_bus_v1_order_models_proto_rawDescGZIP(), []int{1}
}

func (x *RefundOrderEvent) GetRefundOrderId() int64 {
	if x != nil {
		return x.RefundOrderId
	}
	return 0
}

func (x *RefundOrderEvent) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *RefundOrderEvent) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *RefundOrderEvent) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

var File_moego_models_event_bus_v1_order_models_proto protoreflect.FileDescriptor

var file_moego_models_event_bus_v1_order_models_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x90, 0x05, 0x0a,
	0x0a, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x67,
	0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x67, 0x75, 0x69, 0x64, 0x12,
	0x4a, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x45, 0x0a, 0x0c, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x47, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x0e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x62, 0x0a, 0x12, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x46, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c,
	0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0x95, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x42, 0x80, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x59, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x2f, 0x76, 0x31, 0x3b,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x62, 0x75, 0x73, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_models_event_bus_v1_order_models_proto_rawDescOnce sync.Once
	file_moego_models_event_bus_v1_order_models_proto_rawDescData = file_moego_models_event_bus_v1_order_models_proto_rawDesc
)

func file_moego_models_event_bus_v1_order_models_proto_rawDescGZIP() []byte {
	file_moego_models_event_bus_v1_order_models_proto_rawDescOnce.Do(func() {
		file_moego_models_event_bus_v1_order_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_event_bus_v1_order_models_proto_rawDescData)
	})
	return file_moego_models_event_bus_v1_order_models_proto_rawDescData
}

var file_moego_models_event_bus_v1_order_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_event_bus_v1_order_models_proto_goTypes = []interface{}{
	(*OrderEvent)(nil),                   // 0: moego.models.event_bus.v1.OrderEvent
	(*RefundOrderEvent)(nil),             // 1: moego.models.event_bus.v1.RefundOrderEvent
	(v1.OrderModel_OrderType)(0),         // 2: moego.models.order.v1.OrderModel.OrderType
	(v1.OrderStatus)(0),                  // 3: moego.models.order.v1.OrderStatus
	(v1.OrderSourceType)(0),              // 4: moego.models.order.v1.OrderSourceType
	(v1.OrderModel_PaymentStatus)(0),     // 5: moego.models.order.v1.OrderModel.PaymentStatus
	(v1.OrderModel_FulfillmentStatus)(0), // 6: moego.models.order.v1.OrderModel.FulfillmentStatus
}
var file_moego_models_event_bus_v1_order_models_proto_depIdxs = []int32{
	2, // 0: moego.models.event_bus.v1.OrderEvent.order_type:type_name -> moego.models.order.v1.OrderModel.OrderType
	3, // 1: moego.models.event_bus.v1.OrderEvent.order_status:type_name -> moego.models.order.v1.OrderStatus
	4, // 2: moego.models.event_bus.v1.OrderEvent.source_type:type_name -> moego.models.order.v1.OrderSourceType
	5, // 3: moego.models.event_bus.v1.OrderEvent.payment_status:type_name -> moego.models.order.v1.OrderModel.PaymentStatus
	6, // 4: moego.models.event_bus.v1.OrderEvent.fulfillment_status:type_name -> moego.models.order.v1.OrderModel.FulfillmentStatus
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_models_event_bus_v1_order_models_proto_init() }
func file_moego_models_event_bus_v1_order_models_proto_init() {
	if File_moego_models_event_bus_v1_order_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_event_bus_v1_order_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_event_bus_v1_order_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundOrderEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_event_bus_v1_order_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_event_bus_v1_order_models_proto_goTypes,
		DependencyIndexes: file_moego_models_event_bus_v1_order_models_proto_depIdxs,
		MessageInfos:      file_moego_models_event_bus_v1_order_models_proto_msgTypes,
	}.Build()
	File_moego_models_event_bus_v1_order_models_proto = out.File
	file_moego_models_event_bus_v1_order_models_proto_rawDesc = nil
	file_moego_models_event_bus_v1_order_models_proto_goTypes = nil
	file_moego_models_event_bus_v1_order_models_proto_depIdxs = nil
}
