// @since 2024-06-05 11:24:51
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/auto_message/v1/auto_message_task_models.proto

package automessagepb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// auto message task model
type AutoMessageTaskModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// auto message config id
	AutoMsgConfigId int64 `protobuf:"varint,4,opt,name=auto_msg_config_id,json=autoMsgConfigId,proto3" json:"auto_msg_config_id,omitempty"`
	// object id
	ObjectId int64 `protobuf:"varint,5,opt,name=object_id,json=objectId,proto3" json:"object_id,omitempty"`
	// object type
	ObjectType AutoMessageTaskObjectType `protobuf:"varint,6,opt,name=object_type,json=objectType,proto3,enum=moego.models.auto_message.v1.AutoMessageTaskObjectType" json:"object_type,omitempty"`
	// receiver id
	ReceiverId int64 `protobuf:"varint,7,opt,name=receiver_id,json=receiverId,proto3" json:"receiver_id,omitempty"`
	// receiver type
	ReceiverType AutoMessageTaskReceiverType `protobuf:"varint,8,opt,name=receiver_type,json=receiverType,proto3,enum=moego.models.auto_message.v1.AutoMessageTaskReceiverType" json:"receiver_type,omitempty"`
	// send time
	SendTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	// status
	Status AutoMessageTaskStatus `protobuf:"varint,10,opt,name=status,proto3,enum=moego.models.auto_message.v1.AutoMessageTaskStatus" json:"status,omitempty"`
	// use case
	UseCase v1.MessageTemplateUseCase `protobuf:"varint,11,opt,name=use_case,json=useCase,proto3,enum=moego.models.message.v1.MessageTemplateUseCase" json:"use_case,omitempty"`
	// version
	Version int32 `protobuf:"varint,12,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *AutoMessageTaskModel) Reset() {
	*x = AutoMessageTaskModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_auto_message_v1_auto_message_task_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoMessageTaskModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoMessageTaskModel) ProtoMessage() {}

func (x *AutoMessageTaskModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_auto_message_v1_auto_message_task_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoMessageTaskModel.ProtoReflect.Descriptor instead.
func (*AutoMessageTaskModel) Descriptor() ([]byte, []int) {
	return file_moego_models_auto_message_v1_auto_message_task_models_proto_rawDescGZIP(), []int{0}
}

func (x *AutoMessageTaskModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AutoMessageTaskModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *AutoMessageTaskModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AutoMessageTaskModel) GetAutoMsgConfigId() int64 {
	if x != nil {
		return x.AutoMsgConfigId
	}
	return 0
}

func (x *AutoMessageTaskModel) GetObjectId() int64 {
	if x != nil {
		return x.ObjectId
	}
	return 0
}

func (x *AutoMessageTaskModel) GetObjectType() AutoMessageTaskObjectType {
	if x != nil {
		return x.ObjectType
	}
	return AutoMessageTaskObjectType_AUTO_MESSAGE_TASK_OBJECT_TYPE_UNSPECIFIED
}

func (x *AutoMessageTaskModel) GetReceiverId() int64 {
	if x != nil {
		return x.ReceiverId
	}
	return 0
}

func (x *AutoMessageTaskModel) GetReceiverType() AutoMessageTaskReceiverType {
	if x != nil {
		return x.ReceiverType
	}
	return AutoMessageTaskReceiverType_AUTO_MESSAGE_TASK_RECEIVER_TYPE_UNSPECIFIED
}

func (x *AutoMessageTaskModel) GetSendTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SendTime
	}
	return nil
}

func (x *AutoMessageTaskModel) GetStatus() AutoMessageTaskStatus {
	if x != nil {
		return x.Status
	}
	return AutoMessageTaskStatus_AUTO_MESSAGE_TASK_STATUS_UNSPECIFIED
}

func (x *AutoMessageTaskModel) GetUseCase() v1.MessageTemplateUseCase {
	if x != nil {
		return x.UseCase
	}
	return v1.MessageTemplateUseCase(0)
}

func (x *AutoMessageTaskModel) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

var File_moego_models_auto_message_v1_auto_message_task_models_proto protoreflect.FileDescriptor

var file_moego_models_auto_message_v1_auto_message_task_models_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76,
	0x31, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf7,
	0x04, 0x0a, 0x14, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x12, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x6d, 0x73, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x4d, 0x73, 0x67, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x58, 0x0a, 0x0b, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x49, 0x64, 0x12, 0x5e, 0x0a, 0x0d,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x09,
	0x73, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x73, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x4a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x07, 0x75, 0x73, 0x65, 0x43, 0x61, 0x73, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x89, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x5f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_auto_message_v1_auto_message_task_models_proto_rawDescOnce sync.Once
	file_moego_models_auto_message_v1_auto_message_task_models_proto_rawDescData = file_moego_models_auto_message_v1_auto_message_task_models_proto_rawDesc
)

func file_moego_models_auto_message_v1_auto_message_task_models_proto_rawDescGZIP() []byte {
	file_moego_models_auto_message_v1_auto_message_task_models_proto_rawDescOnce.Do(func() {
		file_moego_models_auto_message_v1_auto_message_task_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_auto_message_v1_auto_message_task_models_proto_rawDescData)
	})
	return file_moego_models_auto_message_v1_auto_message_task_models_proto_rawDescData
}

var file_moego_models_auto_message_v1_auto_message_task_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_auto_message_v1_auto_message_task_models_proto_goTypes = []interface{}{
	(*AutoMessageTaskModel)(nil),     // 0: moego.models.auto_message.v1.AutoMessageTaskModel
	(AutoMessageTaskObjectType)(0),   // 1: moego.models.auto_message.v1.AutoMessageTaskObjectType
	(AutoMessageTaskReceiverType)(0), // 2: moego.models.auto_message.v1.AutoMessageTaskReceiverType
	(*timestamppb.Timestamp)(nil),    // 3: google.protobuf.Timestamp
	(AutoMessageTaskStatus)(0),       // 4: moego.models.auto_message.v1.AutoMessageTaskStatus
	(v1.MessageTemplateUseCase)(0),   // 5: moego.models.message.v1.MessageTemplateUseCase
}
var file_moego_models_auto_message_v1_auto_message_task_models_proto_depIdxs = []int32{
	1, // 0: moego.models.auto_message.v1.AutoMessageTaskModel.object_type:type_name -> moego.models.auto_message.v1.AutoMessageTaskObjectType
	2, // 1: moego.models.auto_message.v1.AutoMessageTaskModel.receiver_type:type_name -> moego.models.auto_message.v1.AutoMessageTaskReceiverType
	3, // 2: moego.models.auto_message.v1.AutoMessageTaskModel.send_time:type_name -> google.protobuf.Timestamp
	4, // 3: moego.models.auto_message.v1.AutoMessageTaskModel.status:type_name -> moego.models.auto_message.v1.AutoMessageTaskStatus
	5, // 4: moego.models.auto_message.v1.AutoMessageTaskModel.use_case:type_name -> moego.models.message.v1.MessageTemplateUseCase
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_models_auto_message_v1_auto_message_task_models_proto_init() }
func file_moego_models_auto_message_v1_auto_message_task_models_proto_init() {
	if File_moego_models_auto_message_v1_auto_message_task_models_proto != nil {
		return
	}
	file_moego_models_auto_message_v1_auto_message_task_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_auto_message_v1_auto_message_task_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoMessageTaskModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_auto_message_v1_auto_message_task_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_auto_message_v1_auto_message_task_models_proto_goTypes,
		DependencyIndexes: file_moego_models_auto_message_v1_auto_message_task_models_proto_depIdxs,
		MessageInfos:      file_moego_models_auto_message_v1_auto_message_task_models_proto_msgTypes,
	}.Build()
	File_moego_models_auto_message_v1_auto_message_task_models_proto = out.File
	file_moego_models_auto_message_v1_auto_message_task_models_proto_rawDesc = nil
	file_moego_models_auto_message_v1_auto_message_task_models_proto_goTypes = nil
	file_moego_models_auto_message_v1_auto_message_task_models_proto_depIdxs = nil
}
