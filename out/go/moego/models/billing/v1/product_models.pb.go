// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/billing/v1/product_models.proto

package billingpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// product object
type ProductModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// product id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// product relation id
	VendorProductId string `protobuf:"bytes,2,opt,name=vendor_product_id,json=vendorProductId,proto3" json:"vendor_product_id,omitempty"`
	// vendor
	Vendor string `protobuf:"bytes,3,opt,name=vendor,proto3" json:"vendor,omitempty"`
	// product name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// product description
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// active
	Active bool `protobuf:"varint,6,opt,name=active,proto3" json:"active,omitempty"`
}

func (x *ProductModel) Reset() {
	*x = ProductModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_billing_v1_product_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductModel) ProtoMessage() {}

func (x *ProductModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_billing_v1_product_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductModel.ProtoReflect.Descriptor instead.
func (*ProductModel) Descriptor() ([]byte, []int) {
	return file_moego_models_billing_v1_product_models_proto_rawDescGZIP(), []int{0}
}

func (x *ProductModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ProductModel) GetVendorProductId() string {
	if x != nil {
		return x.VendorProductId
	}
	return ""
}

func (x *ProductModel) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *ProductModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProductModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ProductModel) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

// price object
type PriceModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price relation id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// price object id
	VendorPriceId string `protobuf:"bytes,2,opt,name=vendor_price_id,json=vendorPriceId,proto3" json:"vendor_price_id,omitempty"`
	// product relation id
	ProductRelId int64 `protobuf:"varint,3,opt,name=product_rel_id,json=productRelId,proto3" json:"product_rel_id,omitempty"`
	// price amount with units
	UnitAmount *money.Money `protobuf:"bytes,4,opt,name=unit_amount,json=unitAmount,proto3" json:"unit_amount,omitempty"`
	// one off price if null
	Recurring *v1.TimePeriod `protobuf:"bytes,5,opt,name=recurring,proto3" json:"recurring,omitempty"`
}

func (x *PriceModel) Reset() {
	*x = PriceModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_billing_v1_product_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceModel) ProtoMessage() {}

func (x *PriceModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_billing_v1_product_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceModel.ProtoReflect.Descriptor instead.
func (*PriceModel) Descriptor() ([]byte, []int) {
	return file_moego_models_billing_v1_product_models_proto_rawDescGZIP(), []int{1}
}

func (x *PriceModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PriceModel) GetVendorPriceId() string {
	if x != nil {
		return x.VendorPriceId
	}
	return ""
}

func (x *PriceModel) GetProductRelId() int64 {
	if x != nil {
		return x.ProductRelId
	}
	return 0
}

func (x *PriceModel) GetUnitAmount() *money.Money {
	if x != nil {
		return x.UnitAmount
	}
	return nil
}

func (x *PriceModel) GetRecurring() *v1.TimePeriod {
	if x != nil {
		return x.Recurring
	}
	return nil
}

var File_moego_models_billing_v1_product_models_proto protoreflect.FileDescriptor

var file_moego_models_billing_v1_product_models_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xb0, 0x01, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x22, 0xd9, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x63, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x50, 0x72, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x72, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x65, 0x6c,
	0x49, 0x64, 0x12, 0x33, 0x0a, 0x0b, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x75, 0x6e, 0x69,
	0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x75, 0x72,
	0x72, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x09, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_billing_v1_product_models_proto_rawDescOnce sync.Once
	file_moego_models_billing_v1_product_models_proto_rawDescData = file_moego_models_billing_v1_product_models_proto_rawDesc
)

func file_moego_models_billing_v1_product_models_proto_rawDescGZIP() []byte {
	file_moego_models_billing_v1_product_models_proto_rawDescOnce.Do(func() {
		file_moego_models_billing_v1_product_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_billing_v1_product_models_proto_rawDescData)
	})
	return file_moego_models_billing_v1_product_models_proto_rawDescData
}

var file_moego_models_billing_v1_product_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_billing_v1_product_models_proto_goTypes = []interface{}{
	(*ProductModel)(nil),  // 0: moego.models.billing.v1.ProductModel
	(*PriceModel)(nil),    // 1: moego.models.billing.v1.PriceModel
	(*money.Money)(nil),   // 2: google.type.Money
	(*v1.TimePeriod)(nil), // 3: moego.utils.v1.TimePeriod
}
var file_moego_models_billing_v1_product_models_proto_depIdxs = []int32{
	2, // 0: moego.models.billing.v1.PriceModel.unit_amount:type_name -> google.type.Money
	3, // 1: moego.models.billing.v1.PriceModel.recurring:type_name -> moego.utils.v1.TimePeriod
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_billing_v1_product_models_proto_init() }
func file_moego_models_billing_v1_product_models_proto_init() {
	if File_moego_models_billing_v1_product_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_billing_v1_product_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_billing_v1_product_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_billing_v1_product_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_billing_v1_product_models_proto_goTypes,
		DependencyIndexes: file_moego_models_billing_v1_product_models_proto_depIdxs,
		MessageInfos:      file_moego_models_billing_v1_product_models_proto_msgTypes,
	}.Build()
	File_moego_models_billing_v1_product_models_proto = out.File
	file_moego_models_billing_v1_product_models_proto_rawDesc = nil
	file_moego_models_billing_v1_product_models_proto_goTypes = nil
	file_moego_models_billing_v1_product_models_proto_depIdxs = nil
}
