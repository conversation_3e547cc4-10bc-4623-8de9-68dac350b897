// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/account/v1/account_impersonate_approval_models.proto

package accountpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// account model
type AccountImpersonateApprovalInstanceModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// instance code, identify the approval instance
	InstanceCode string `protobuf:"bytes,1,opt,name=instance_code,json=instanceCode,proto3" json:"instance_code,omitempty"`
	// impersonator
	Impersonator string `protobuf:"bytes,2,opt,name=impersonator,proto3" json:"impersonator,omitempty"`
	// source
	Source string `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	// target account id
	TargetAccountId int64 `protobuf:"varint,4,opt,name=target_account_id,json=targetAccountId,proto3" json:"target_account_id,omitempty"`
	// target account id
	TargetAccountEmail string `protobuf:"bytes,5,opt,name=target_account_email,json=targetAccountEmail,proto3" json:"target_account_email,omitempty"`
	// max age
	MaxAge *durationpb.Duration `protobuf:"bytes,6,opt,name=max_age,json=maxAge,proto3" json:"max_age,omitempty"`
	// status, PENDING, APPROVED, REJECTED, CANCELED, DELETED
	Status string `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	ApprovedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=approved_at,json=approvedAt,proto3" json:"approved_at,omitempty"`
}

func (x *AccountImpersonateApprovalInstanceModel) Reset() {
	*x = AccountImpersonateApprovalInstanceModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_account_v1_account_impersonate_approval_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountImpersonateApprovalInstanceModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountImpersonateApprovalInstanceModel) ProtoMessage() {}

func (x *AccountImpersonateApprovalInstanceModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_account_v1_account_impersonate_approval_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountImpersonateApprovalInstanceModel.ProtoReflect.Descriptor instead.
func (*AccountImpersonateApprovalInstanceModel) Descriptor() ([]byte, []int) {
	return file_moego_models_account_v1_account_impersonate_approval_models_proto_rawDescGZIP(), []int{0}
}

func (x *AccountImpersonateApprovalInstanceModel) GetInstanceCode() string {
	if x != nil {
		return x.InstanceCode
	}
	return ""
}

func (x *AccountImpersonateApprovalInstanceModel) GetImpersonator() string {
	if x != nil {
		return x.Impersonator
	}
	return ""
}

func (x *AccountImpersonateApprovalInstanceModel) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *AccountImpersonateApprovalInstanceModel) GetTargetAccountId() int64 {
	if x != nil {
		return x.TargetAccountId
	}
	return 0
}

func (x *AccountImpersonateApprovalInstanceModel) GetTargetAccountEmail() string {
	if x != nil {
		return x.TargetAccountEmail
	}
	return ""
}

func (x *AccountImpersonateApprovalInstanceModel) GetMaxAge() *durationpb.Duration {
	if x != nil {
		return x.MaxAge
	}
	return nil
}

func (x *AccountImpersonateApprovalInstanceModel) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AccountImpersonateApprovalInstanceModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AccountImpersonateApprovalInstanceModel) GetApprovedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ApprovedAt
	}
	return nil
}

var File_moego_models_account_v1_account_impersonate_approval_models_proto protoreflect.FileDescriptor

var file_moego_models_account_v1_account_impersonate_approval_models_proto_rawDesc = []byte{
	0x0a, 0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x6d, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xac, 0x03,
	0x0a, 0x27, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6d, 0x70, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x69, 0x6d, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6d, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x74,
	0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x32, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f,
	0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x41, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x3b, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0a, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x41, 0x74, 0x42, 0x7b, 0x0a, 0x1f,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_models_account_v1_account_impersonate_approval_models_proto_rawDescOnce sync.Once
	file_moego_models_account_v1_account_impersonate_approval_models_proto_rawDescData = file_moego_models_account_v1_account_impersonate_approval_models_proto_rawDesc
)

func file_moego_models_account_v1_account_impersonate_approval_models_proto_rawDescGZIP() []byte {
	file_moego_models_account_v1_account_impersonate_approval_models_proto_rawDescOnce.Do(func() {
		file_moego_models_account_v1_account_impersonate_approval_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_account_v1_account_impersonate_approval_models_proto_rawDescData)
	})
	return file_moego_models_account_v1_account_impersonate_approval_models_proto_rawDescData
}

var file_moego_models_account_v1_account_impersonate_approval_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_account_v1_account_impersonate_approval_models_proto_goTypes = []interface{}{
	(*AccountImpersonateApprovalInstanceModel)(nil), // 0: moego.models.account.v1.AccountImpersonateApprovalInstanceModel
	(*durationpb.Duration)(nil),                     // 1: google.protobuf.Duration
	(*timestamppb.Timestamp)(nil),                   // 2: google.protobuf.Timestamp
}
var file_moego_models_account_v1_account_impersonate_approval_models_proto_depIdxs = []int32{
	1, // 0: moego.models.account.v1.AccountImpersonateApprovalInstanceModel.max_age:type_name -> google.protobuf.Duration
	2, // 1: moego.models.account.v1.AccountImpersonateApprovalInstanceModel.created_at:type_name -> google.protobuf.Timestamp
	2, // 2: moego.models.account.v1.AccountImpersonateApprovalInstanceModel.approved_at:type_name -> google.protobuf.Timestamp
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_account_v1_account_impersonate_approval_models_proto_init() }
func file_moego_models_account_v1_account_impersonate_approval_models_proto_init() {
	if File_moego_models_account_v1_account_impersonate_approval_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_account_v1_account_impersonate_approval_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountImpersonateApprovalInstanceModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_account_v1_account_impersonate_approval_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_account_v1_account_impersonate_approval_models_proto_goTypes,
		DependencyIndexes: file_moego_models_account_v1_account_impersonate_approval_models_proto_depIdxs,
		MessageInfos:      file_moego_models_account_v1_account_impersonate_approval_models_proto_msgTypes,
	}.Build()
	File_moego_models_account_v1_account_impersonate_approval_models_proto = out.File
	file_moego_models_account_v1_account_impersonate_approval_models_proto_rawDesc = nil
	file_moego_models_account_v1_account_impersonate_approval_models_proto_goTypes = nil
	file_moego_models_account_v1_account_impersonate_approval_models_proto_depIdxs = nil
}
