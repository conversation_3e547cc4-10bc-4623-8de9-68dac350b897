// @since 2023-04-07 09:48:36
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/metadata/v1/metadata_defs.proto

package metadatapb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// the full key def, for add a new key
type KeyFullDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the name of a key
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// the description of a key
	Description string `protobuf:"bytes,10,opt,name=description,proto3" json:"description,omitempty"`
	// the target type
	OwnerType OwnerType `protobuf:"varint,2,opt,name=owner_type,json=ownerType,proto3,enum=moego.models.metadata.v1.OwnerType" json:"owner_type,omitempty"`
	// the default value
	DefaultValue string `protobuf:"bytes,3,opt,name=default_value,json=defaultValue,proto3" json:"default_value,omitempty"`
	// the start time, 0 or null means works any time
	StartAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_at,json=startAt,proto3,oneof" json:"start_at,omitempty"`
	// the end time, 0 or null means works forever
	EndAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end_at,json=endAt,proto3,oneof" json:"end_at,omitempty"`
	// the permission level
	PermissionLevel PermissionLevel `protobuf:"varint,6,opt,name=permission_level,json=permissionLevel,proto3,enum=moego.models.metadata.v1.PermissionLevel" json:"permission_level,omitempty"`
	// the key group
	Group string `protobuf:"bytes,7,opt,name=group,proto3" json:"group,omitempty"`
}

func (x *KeyFullDef) Reset() {
	*x = KeyFullDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_metadata_v1_metadata_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeyFullDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeyFullDef) ProtoMessage() {}

func (x *KeyFullDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_metadata_v1_metadata_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeyFullDef.ProtoReflect.Descriptor instead.
func (*KeyFullDef) Descriptor() ([]byte, []int) {
	return file_moego_models_metadata_v1_metadata_defs_proto_rawDescGZIP(), []int{0}
}

func (x *KeyFullDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *KeyFullDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *KeyFullDef) GetOwnerType() OwnerType {
	if x != nil {
		return x.OwnerType
	}
	return OwnerType_OWNER_TYPE_UNSPECIFIED
}

func (x *KeyFullDef) GetDefaultValue() string {
	if x != nil {
		return x.DefaultValue
	}
	return ""
}

func (x *KeyFullDef) GetStartAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartAt
	}
	return nil
}

func (x *KeyFullDef) GetEndAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EndAt
	}
	return nil
}

func (x *KeyFullDef) GetPermissionLevel() PermissionLevel {
	if x != nil {
		return x.PermissionLevel
	}
	return PermissionLevel_PERMISSION_LEVEL_UNSPECIFIED
}

func (x *KeyFullDef) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

// the partial key def, for update key
type KeyPartialDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the description of a key
	Description *string `protobuf:"bytes,10,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// the target type
	OwnerType *OwnerType `protobuf:"varint,2,opt,name=owner_type,json=ownerType,proto3,enum=moego.models.metadata.v1.OwnerType,oneof" json:"owner_type,omitempty"`
	// the default value
	DefaultValue *string `protobuf:"bytes,3,opt,name=default_value,json=defaultValue,proto3,oneof" json:"default_value,omitempty"`
	// the start time, 0 or null means works any time
	StartAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_at,json=startAt,proto3,oneof" json:"start_at,omitempty"`
	// the end time, 0 or null means works forever
	EndAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end_at,json=endAt,proto3,oneof" json:"end_at,omitempty"`
	// the operator type
	PermissionLevel *PermissionLevel `protobuf:"varint,6,opt,name=permission_level,json=permissionLevel,proto3,enum=moego.models.metadata.v1.PermissionLevel,oneof" json:"permission_level,omitempty"`
	// the key group
	Group *string `protobuf:"bytes,9,opt,name=group,proto3,oneof" json:"group,omitempty"`
}

func (x *KeyPartialDef) Reset() {
	*x = KeyPartialDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_metadata_v1_metadata_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeyPartialDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeyPartialDef) ProtoMessage() {}

func (x *KeyPartialDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_metadata_v1_metadata_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeyPartialDef.ProtoReflect.Descriptor instead.
func (*KeyPartialDef) Descriptor() ([]byte, []int) {
	return file_moego_models_metadata_v1_metadata_defs_proto_rawDescGZIP(), []int{1}
}

func (x *KeyPartialDef) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *KeyPartialDef) GetOwnerType() OwnerType {
	if x != nil && x.OwnerType != nil {
		return *x.OwnerType
	}
	return OwnerType_OWNER_TYPE_UNSPECIFIED
}

func (x *KeyPartialDef) GetDefaultValue() string {
	if x != nil && x.DefaultValue != nil {
		return *x.DefaultValue
	}
	return ""
}

func (x *KeyPartialDef) GetStartAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartAt
	}
	return nil
}

func (x *KeyPartialDef) GetEndAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EndAt
	}
	return nil
}

func (x *KeyPartialDef) GetPermissionLevel() PermissionLevel {
	if x != nil && x.PermissionLevel != nil {
		return *x.PermissionLevel
	}
	return PermissionLevel_PERMISSION_LEVEL_UNSPECIFIED
}

func (x *KeyPartialDef) GetGroup() string {
	if x != nil && x.Group != nil {
		return *x.Group
	}
	return ""
}

// the owner identifier
type OwnerDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the owner type
	Type OwnerType `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.metadata.v1.OwnerType" json:"type,omitempty"`
	// the owner id
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *OwnerDef) Reset() {
	*x = OwnerDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_metadata_v1_metadata_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OwnerDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OwnerDef) ProtoMessage() {}

func (x *OwnerDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_metadata_v1_metadata_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OwnerDef.ProtoReflect.Descriptor instead.
func (*OwnerDef) Descriptor() ([]byte, []int) {
	return file_moego_models_metadata_v1_metadata_defs_proto_rawDescGZIP(), []int{2}
}

func (x *OwnerDef) GetType() OwnerType {
	if x != nil {
		return x.Type
	}
	return OwnerType_OWNER_TYPE_UNSPECIFIED
}

func (x *OwnerDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_moego_models_metadata_v1_metadata_defs_proto protoreflect.FileDescriptor

var file_moego_models_metadata_v1_metadata_defs_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xe7, 0x03, 0x0a, 0x0a, 0x4b, 0x65, 0x79, 0x46, 0x75, 0x6c, 0x6c, 0x44, 0x65, 0x66,
	0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x02, 0x18, 0x32, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x2c, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x02, 0x18, 0xff, 0x01,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a,
	0x0a, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x77, 0x6e,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01,
	0x20, 0x00, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a,
	0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x08, 0x52, 0x0c,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x3a, 0x0a, 0x08,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x06, 0x65, 0x6e, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x48, 0x01, 0x52, 0x05, 0x65, 0x6e, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x60, 0x0a, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20,
	0x00, 0x52, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x05, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x61, 0x74,
	0x42, 0x09, 0x0a, 0x07, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x61, 0x74, 0x22, 0xb4, 0x04, 0x0a, 0x0d,
	0x4b, 0x65, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x66, 0x12, 0x31, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x02, 0x18, 0xff, 0x01, 0x48, 0x00,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01,
	0x12, 0x53, 0x0a, 0x0a, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x77, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01,
	0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x32, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x72, 0x03, 0x18, 0x80, 0x08, 0x48, 0x02, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x08, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x06, 0x65, 0x6e, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x48, 0x04, 0x52, 0x05, 0x65, 0x6e, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x65, 0x0a,
	0x10, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x05,
	0x52, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x48, 0x06,
	0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x61, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x65, 0x6e, 0x64,
	0x5f, 0x61, 0x74, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x22, 0x68, 0x0a, 0x08, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x44, 0x65, 0x66, 0x12, 0x43,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x42, 0x7e, 0x0a, 0x20,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76,
	0x31, 0x3b, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_metadata_v1_metadata_defs_proto_rawDescOnce sync.Once
	file_moego_models_metadata_v1_metadata_defs_proto_rawDescData = file_moego_models_metadata_v1_metadata_defs_proto_rawDesc
)

func file_moego_models_metadata_v1_metadata_defs_proto_rawDescGZIP() []byte {
	file_moego_models_metadata_v1_metadata_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_metadata_v1_metadata_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_metadata_v1_metadata_defs_proto_rawDescData)
	})
	return file_moego_models_metadata_v1_metadata_defs_proto_rawDescData
}

var file_moego_models_metadata_v1_metadata_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_metadata_v1_metadata_defs_proto_goTypes = []interface{}{
	(*KeyFullDef)(nil),            // 0: moego.models.metadata.v1.KeyFullDef
	(*KeyPartialDef)(nil),         // 1: moego.models.metadata.v1.KeyPartialDef
	(*OwnerDef)(nil),              // 2: moego.models.metadata.v1.OwnerDef
	(OwnerType)(0),                // 3: moego.models.metadata.v1.OwnerType
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
	(PermissionLevel)(0),          // 5: moego.models.metadata.v1.PermissionLevel
}
var file_moego_models_metadata_v1_metadata_defs_proto_depIdxs = []int32{
	3, // 0: moego.models.metadata.v1.KeyFullDef.owner_type:type_name -> moego.models.metadata.v1.OwnerType
	4, // 1: moego.models.metadata.v1.KeyFullDef.start_at:type_name -> google.protobuf.Timestamp
	4, // 2: moego.models.metadata.v1.KeyFullDef.end_at:type_name -> google.protobuf.Timestamp
	5, // 3: moego.models.metadata.v1.KeyFullDef.permission_level:type_name -> moego.models.metadata.v1.PermissionLevel
	3, // 4: moego.models.metadata.v1.KeyPartialDef.owner_type:type_name -> moego.models.metadata.v1.OwnerType
	4, // 5: moego.models.metadata.v1.KeyPartialDef.start_at:type_name -> google.protobuf.Timestamp
	4, // 6: moego.models.metadata.v1.KeyPartialDef.end_at:type_name -> google.protobuf.Timestamp
	5, // 7: moego.models.metadata.v1.KeyPartialDef.permission_level:type_name -> moego.models.metadata.v1.PermissionLevel
	3, // 8: moego.models.metadata.v1.OwnerDef.type:type_name -> moego.models.metadata.v1.OwnerType
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_moego_models_metadata_v1_metadata_defs_proto_init() }
func file_moego_models_metadata_v1_metadata_defs_proto_init() {
	if File_moego_models_metadata_v1_metadata_defs_proto != nil {
		return
	}
	file_moego_models_metadata_v1_metadata_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_metadata_v1_metadata_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeyFullDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_metadata_v1_metadata_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeyPartialDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_metadata_v1_metadata_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OwnerDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_metadata_v1_metadata_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_metadata_v1_metadata_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_metadata_v1_metadata_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_metadata_v1_metadata_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_metadata_v1_metadata_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_metadata_v1_metadata_defs_proto_msgTypes,
	}.Build()
	File_moego_models_metadata_v1_metadata_defs_proto = out.File
	file_moego_models_metadata_v1_metadata_defs_proto_rawDesc = nil
	file_moego_models_metadata_v1_metadata_defs_proto_goTypes = nil
	file_moego_models_metadata_v1_metadata_defs_proto_depIdxs = nil
}
