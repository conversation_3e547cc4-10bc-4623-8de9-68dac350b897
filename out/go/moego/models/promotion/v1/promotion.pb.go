// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/promotion/v1/promotion.proto

package promotionpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	decimal "google.golang.org/genproto/googleapis/type/decimal"
	interval "google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// TargetType 目标类型
type TargetType int32

const (
	// unspecified
	TargetType_TARGET_TYPE_UNSPECIFIED TargetType = 0
	// service 服务
	TargetType_SERVICE TargetType = 1
	// product 产品
	TargetType_PRODUCT TargetType = 2
	// service_charge 服务费
	TargetType_SERVICE_CHARGE TargetType = 3
	// package
	TargetType_PACKAGE TargetType = 4
)

// Enum value maps for TargetType.
var (
	TargetType_name = map[int32]string{
		0: "TARGET_TYPE_UNSPECIFIED",
		1: "SERVICE",
		2: "PRODUCT",
		3: "SERVICE_CHARGE",
		4: "PACKAGE",
	}
	TargetType_value = map[string]int32{
		"TARGET_TYPE_UNSPECIFIED": 0,
		"SERVICE":                 1,
		"PRODUCT":                 2,
		"SERVICE_CHARGE":          3,
		"PACKAGE":                 4,
	}
)

func (x TargetType) Enum() *TargetType {
	p := new(TargetType)
	*p = x
	return p
}

func (x TargetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TargetType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_promotion_v1_promotion_proto_enumTypes[0].Descriptor()
}

func (TargetType) Type() protoreflect.EnumType {
	return &file_moego_models_promotion_v1_promotion_proto_enumTypes[0]
}

func (x TargetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TargetType.Descriptor instead.
func (TargetType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_promotion_proto_rawDescGZIP(), []int{0}
}

// type
type Source_Type int32

const (
	// unspecified
	Source_TYPE_UNSPECIFIED Source_Type = 0
	// discount
	Source_DISCOUNT Source_Type = 1
	// package, 此 type 对应的id 是 package service id
	Source_PACKAGE Source_Type = 2
	// membership discount, 此 type 对应的id是benefit id
	Source_MEMBERSHIP_DISCOUNT Source_Type = 3
	// membership quantity, 此 type 对应的id是benefit id
	Source_MEMBERSHIP_QUANTITY Source_Type = 4
)

// Enum value maps for Source_Type.
var (
	Source_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "DISCOUNT",
		2: "PACKAGE",
		3: "MEMBERSHIP_DISCOUNT",
		4: "MEMBERSHIP_QUANTITY",
	}
	Source_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED":    0,
		"DISCOUNT":            1,
		"PACKAGE":             2,
		"MEMBERSHIP_DISCOUNT": 3,
		"MEMBERSHIP_QUANTITY": 4,
	}
)

func (x Source_Type) Enum() *Source_Type {
	p := new(Source_Type)
	*p = x
	return p
}

func (x Source_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Source_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_promotion_v1_promotion_proto_enumTypes[1].Descriptor()
}

func (Source_Type) Type() protoreflect.EnumType {
	return &file_moego_models_promotion_v1_promotion_proto_enumTypes[1]
}

func (x Source_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Source_Type.Descriptor instead.
func (Source_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_promotion_proto_rawDescGZIP(), []int{0, 0}
}

// source
type Source struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// type
	Type Source_Type `protobuf:"varint,2,opt,name=type,proto3,enum=moego.models.promotion.v1.Source_Type" json:"type,omitempty"`
	// subject 主体，根据不同的 type 对应不同的主体
	//
	// Types that are assignable to Subject:
	//
	//	*Source_Discount
	//	*Source_Package
	//	*Source_Membership
	Subject isSource_Subject `protobuf_oneof:"subject"`
}

func (x *Source) Reset() {
	*x = Source{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Source) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Source) ProtoMessage() {}

func (x *Source) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Source.ProtoReflect.Descriptor instead.
func (*Source) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_promotion_proto_rawDescGZIP(), []int{0}
}

func (x *Source) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Source) GetType() Source_Type {
	if x != nil {
		return x.Type
	}
	return Source_TYPE_UNSPECIFIED
}

func (m *Source) GetSubject() isSource_Subject {
	if m != nil {
		return m.Subject
	}
	return nil
}

func (x *Source) GetDiscount() *DiscountSubject {
	if x, ok := x.GetSubject().(*Source_Discount); ok {
		return x.Discount
	}
	return nil
}

func (x *Source) GetPackage() *PackageSubject {
	if x, ok := x.GetSubject().(*Source_Package); ok {
		return x.Package
	}
	return nil
}

func (x *Source) GetMembership() *MembershipSubject {
	if x, ok := x.GetSubject().(*Source_Membership); ok {
		return x.Membership
	}
	return nil
}

type isSource_Subject interface {
	isSource_Subject()
}

type Source_Discount struct {
	// discount 当 type 为 DISCOUNT 时，表示这个优惠属于哪个折扣
	Discount *DiscountSubject `protobuf:"bytes,3,opt,name=discount,proto3,oneof"`
}

type Source_Package struct {
	// package 当 type 为 PACKAGE 时，表示这个优惠属于哪个套餐服务
	Package *PackageSubject `protobuf:"bytes,4,opt,name=package,proto3,oneof"`
}

type Source_Membership struct {
	// membership 当 type 为 MEMBERSHIP_DISCOUNT 或 MEMBERSHIP_QUANTITY 时，表示这个优惠属于哪个会员权益
	Membership *MembershipSubject `protobuf:"bytes,5,opt,name=membership,proto3,oneof"`
}

func (*Source_Discount) isSource_Subject() {}

func (*Source_Package) isSource_Subject() {}

func (*Source_Membership) isSource_Subject() {}

// promotion 作为优惠本身的配置
type Promotion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// description
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// source 来源
	Source *Source `protobuf:"bytes,4,opt,name=source,proto3" json:"source,omitempty"`
	// discount 折扣内容
	Discount *Discount `protobuf:"bytes,5,opt,name=discount,proto3" json:"discount,omitempty"`
	// restrictions 使用限制
	Restrictions *Restrictions `protobuf:"bytes,6,opt,name=restrictions,proto3" json:"restrictions,omitempty"`
	// validity period 是配置本身的有效期
	ValidityPeriod *Duration `protobuf:"bytes,7,opt,name=validity_period,json=validityPeriod,proto3" json:"validity_period,omitempty"`
	// available period 转化为 coupon 之后的可用期限
	AvailablePeriod *Duration `protobuf:"bytes,8,opt,name=available_period,json=availablePeriod,proto3" json:"available_period,omitempty"`
	// redemptions 使用次数限制
	Redemptions *Redemptions `protobuf:"bytes,9,opt,name=redemptions,proto3" json:"redemptions,omitempty"`
}

func (x *Promotion) Reset() {
	*x = Promotion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Promotion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Promotion) ProtoMessage() {}

func (x *Promotion) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Promotion.ProtoReflect.Descriptor instead.
func (*Promotion) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_promotion_proto_rawDescGZIP(), []int{1}
}

func (x *Promotion) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Promotion) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Promotion) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Promotion) GetSource() *Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *Promotion) GetDiscount() *Discount {
	if x != nil {
		return x.Discount
	}
	return nil
}

func (x *Promotion) GetRestrictions() *Restrictions {
	if x != nil {
		return x.Restrictions
	}
	return nil
}

func (x *Promotion) GetValidityPeriod() *Duration {
	if x != nil {
		return x.ValidityPeriod
	}
	return nil
}

func (x *Promotion) GetAvailablePeriod() *Duration {
	if x != nil {
		return x.AvailablePeriod
	}
	return nil
}

func (x *Promotion) GetRedemptions() *Redemptions {
	if x != nil {
		return x.Redemptions
	}
	return nil
}

// restrictions
type Restrictions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// targets 适用对象限制
	Targets []*Targets `protobuf:"bytes,1,rep,name=targets,proto3" json:"targets,omitempty"`
	// user filters 使用对象限制
	UserFilters []*Filter `protobuf:"bytes,2,rep,name=user_filters,json=userFilters,proto3" json:"user_filters,omitempty"`
}

func (x *Restrictions) Reset() {
	*x = Restrictions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Restrictions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Restrictions) ProtoMessage() {}

func (x *Restrictions) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Restrictions.ProtoReflect.Descriptor instead.
func (*Restrictions) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_promotion_proto_rawDescGZIP(), []int{2}
}

func (x *Restrictions) GetTargets() []*Targets {
	if x != nil {
		return x.Targets
	}
	return nil
}

func (x *Restrictions) GetUserFilters() []*Filter {
	if x != nil {
		return x.UserFilters
	}
	return nil
}

// Targets
type Targets struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// type 目标类型
	Type TargetType `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.promotion.v1.TargetType" json:"type,omitempty"`
	// ids 可用对象的 id
	Id []int64 `protobuf:"varint,2,rep,packed,name=id,proto3" json:"id,omitempty"`
	// all 是否全体对象可用
	All bool `protobuf:"varint,3,opt,name=all,proto3" json:"all,omitempty"` // TODO: 调用方无感
}

func (x *Targets) Reset() {
	*x = Targets{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Targets) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Targets) ProtoMessage() {}

func (x *Targets) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Targets.ProtoReflect.Descriptor instead.
func (*Targets) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_promotion_proto_rawDescGZIP(), []int{3}
}

func (x *Targets) GetType() TargetType {
	if x != nil {
		return x.Type
	}
	return TargetType_TARGET_TYPE_UNSPECIFIED
}

func (x *Targets) GetId() []int64 {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *Targets) GetAll() bool {
	if x != nil {
		return x.All
	}
	return false
}

// CouponApplicationTargeaet 优惠券目标用目标
type CouponApplicationTarget struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// target_type 目标类型
	TargetType TargetType `protobuf:"varint,1,opt,name=target_type,json=targetType,proto3,enum=moego.models.promotion.v1.TargetType" json:"target_type,omitempty"`
	// target_id 目标ID
	TargetId int64 `protobuf:"varint,2,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	// unit_price 单价
	UnitPrice *money.Money `protobuf:"bytes,3,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
	// quantity 数量
	TargetQuantity int32 `protobuf:"varint,4,opt,name=target_quantity,json=targetQuantity,proto3" json:"target_quantity,omitempty"`
	// order_item_id
	OrderItemId *int64 `protobuf:"varint,5,opt,name=order_item_id,json=orderItemId,proto3,oneof" json:"order_item_id,omitempty"`
}

func (x *CouponApplicationTarget) Reset() {
	*x = CouponApplicationTarget{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CouponApplicationTarget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CouponApplicationTarget) ProtoMessage() {}

func (x *CouponApplicationTarget) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CouponApplicationTarget.ProtoReflect.Descriptor instead.
func (*CouponApplicationTarget) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_promotion_proto_rawDescGZIP(), []int{4}
}

func (x *CouponApplicationTarget) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_TARGET_TYPE_UNSPECIFIED
}

func (x *CouponApplicationTarget) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *CouponApplicationTarget) GetUnitPrice() *money.Money {
	if x != nil {
		return x.UnitPrice
	}
	return nil
}

func (x *CouponApplicationTarget) GetTargetQuantity() int32 {
	if x != nil {
		return x.TargetQuantity
	}
	return 0
}

func (x *CouponApplicationTarget) GetOrderItemId() int64 {
	if x != nil && x.OrderItemId != nil {
		return *x.OrderItemId
	}
	return 0
}

// Duration
type Duration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// duration
	//
	// Types that are assignable to Duration:
	//
	//	*Duration_RelativePeriod
	//	*Duration_AbsolutePeriod
	//	*Duration_NeverExpire
	Duration isDuration_Duration `protobuf_oneof:"duration"`
}

func (x *Duration) Reset() {
	*x = Duration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Duration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Duration) ProtoMessage() {}

func (x *Duration) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Duration.ProtoReflect.Descriptor instead.
func (*Duration) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_promotion_proto_rawDescGZIP(), []int{5}
}

func (m *Duration) GetDuration() isDuration_Duration {
	if m != nil {
		return m.Duration
	}
	return nil
}

func (x *Duration) GetRelativePeriod() *v1.TimePeriod {
	if x, ok := x.GetDuration().(*Duration_RelativePeriod); ok {
		return x.RelativePeriod
	}
	return nil
}

func (x *Duration) GetAbsolutePeriod() *interval.Interval {
	if x, ok := x.GetDuration().(*Duration_AbsolutePeriod); ok {
		return x.AbsolutePeriod
	}
	return nil
}

func (x *Duration) GetNeverExpire() bool {
	if x, ok := x.GetDuration().(*Duration_NeverExpire); ok {
		return x.NeverExpire
	}
	return false
}

type isDuration_Duration interface {
	isDuration_Duration()
}

type Duration_RelativePeriod struct {
	// relative period 相对时间
	RelativePeriod *v1.TimePeriod `protobuf:"bytes,1,opt,name=relative_period,json=relativePeriod,proto3,oneof"`
}

type Duration_AbsolutePeriod struct {
	// absolute period 绝对时间
	AbsolutePeriod *interval.Interval `protobuf:"bytes,2,opt,name=absolute_period,json=absolutePeriod,proto3,oneof"`
}

type Duration_NeverExpire struct {
	// never expire 永不过期
	NeverExpire bool `protobuf:"varint,3,opt,name=never_expire,json=neverExpire,proto3,oneof"`
}

func (*Duration_RelativePeriod) isDuration_Duration() {}

func (*Duration_AbsolutePeriod) isDuration_Duration() {}

func (*Duration_NeverExpire) isDuration_Duration() {}

// Filter
type Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// value
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// operator
	Operator v2.Operator `protobuf:"varint,3,opt,name=operator,proto3,enum=moego.models.reporting.v2.Operator" json:"operator,omitempty"`
}

func (x *Filter) Reset() {
	*x = Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Filter) ProtoMessage() {}

func (x *Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Filter.ProtoReflect.Descriptor instead.
func (*Filter) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_promotion_proto_rawDescGZIP(), []int{6}
}

func (x *Filter) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Filter) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Filter) GetOperator() v2.Operator {
	if x != nil {
		return x.Operator
	}
	return v2.Operator(0)
}

// discount 折扣内容
type Discount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mode
	//
	// Types that are assignable to Mode:
	//
	//	*Discount_FixedAmount
	//	*Discount_Percentage
	//	*Discount_Deduction
	Mode isDiscount_Mode `protobuf_oneof:"mode"`
}

func (x *Discount) Reset() {
	*x = Discount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Discount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Discount) ProtoMessage() {}

func (x *Discount) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Discount.ProtoReflect.Descriptor instead.
func (*Discount) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_promotion_proto_rawDescGZIP(), []int{7}
}

func (m *Discount) GetMode() isDiscount_Mode {
	if m != nil {
		return m.Mode
	}
	return nil
}

func (x *Discount) GetFixedAmount() *money.Money {
	if x, ok := x.GetMode().(*Discount_FixedAmount); ok {
		return x.FixedAmount
	}
	return nil
}

func (x *Discount) GetPercentage() *decimal.Decimal {
	if x, ok := x.GetMode().(*Discount_Percentage); ok {
		return x.Percentage
	}
	return nil
}

func (x *Discount) GetDeduction() int64 {
	if x, ok := x.GetMode().(*Discount_Deduction); ok {
		return x.Deduction
	}
	return 0
}

type isDiscount_Mode interface {
	isDiscount_Mode()
}

type Discount_FixedAmount struct {
	// fixed amount 固定金额折扣
	FixedAmount *money.Money `protobuf:"bytes,1,opt,name=fixed_amount,json=fixedAmount,proto3,oneof"`
}

type Discount_Percentage struct {
	// percentage 百分比金额折扣
	Percentage *decimal.Decimal `protobuf:"bytes,2,opt,name=percentage,proto3,oneof"`
}

type Discount_Deduction struct {
	// deduction 固定数量抵扣
	Deduction int64 `protobuf:"varint,3,opt,name=deduction,proto3,oneof"`
}

func (*Discount_FixedAmount) isDiscount_Mode() {}

func (*Discount_Percentage) isDiscount_Mode() {}

func (*Discount_Deduction) isDiscount_Mode() {}

// redemptions
type Redemptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 已扣减次数
	RedeemedTimes int64 `protobuf:"varint,1,opt,name=redeemed_times,json=redeemedTimes,proto3" json:"redeemed_times,omitempty"`
	// 最大可扣减次数
	MaxRedeemTimes int64 `protobuf:"varint,2,opt,name=max_redeem_times,json=maxRedeemTimes,proto3" json:"max_redeem_times,omitempty"`
	// 是否可无限次使用
	Unlimited bool `protobuf:"varint,5,opt,name=unlimited,proto3" json:"unlimited,omitempty"`
	// 是否可退还，某些国内无良平台会提供此选项
	Refundable bool `protobuf:"varint,6,opt,name=refundable,proto3" json:"refundable,omitempty"`
}

func (x *Redemptions) Reset() {
	*x = Redemptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Redemptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Redemptions) ProtoMessage() {}

func (x *Redemptions) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_promotion_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Redemptions.ProtoReflect.Descriptor instead.
func (*Redemptions) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_promotion_proto_rawDescGZIP(), []int{8}
}

func (x *Redemptions) GetRedeemedTimes() int64 {
	if x != nil {
		return x.RedeemedTimes
	}
	return 0
}

func (x *Redemptions) GetMaxRedeemTimes() int64 {
	if x != nil {
		return x.MaxRedeemTimes
	}
	return 0
}

func (x *Redemptions) GetUnlimited() bool {
	if x != nil {
		return x.Unlimited
	}
	return false
}

func (x *Redemptions) GetRefundable() bool {
	if x != nil {
		return x.Refundable
	}
	return false
}

var File_moego_models_promotion_v1_promotion_proto protoreflect.FileDescriptor

var file_moego_models_promotion_v1_promotion_proto_rawDesc = []byte{
	0x0a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x6d,
	0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xab, 0x03, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3a, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x48, 0x00, 0x52, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x45, 0x0a, 0x07, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x48, 0x00, 0x52, 0x07,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x4e, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x22, 0x69, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x10, 0x02,
	0x12, 0x17, 0x0a, 0x13, 0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x5f, 0x44,
	0x49, 0x53, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x45, 0x4d,
	0x42, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x5f, 0x51, 0x55, 0x41, 0x4e, 0x54, 0x49, 0x54, 0x59,
	0x10, 0x04, 0x42, 0x09, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x22, 0x82, 0x04,
	0x0a, 0x09, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x08,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4b, 0x0a,
	0x0c, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0c, 0x72, 0x65,
	0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4c, 0x0a, 0x0f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69,
	0x74, 0x79, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x4e, 0x0a, 0x10, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x48, 0x0a, 0x0b, 0x72, 0x65, 0x64, 0x65,
	0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0b, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x22, 0x92, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x3c, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x73, 0x12, 0x44, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0x66, 0x0a, 0x07, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x73, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x61, 0x6c, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x61, 0x6c, 0x6c, 0x22,
	0x95, 0x02, 0x0a, 0x17, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x46, 0x0a, 0x0b, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x31, 0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x75, 0x6e, 0x69, 0x74, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x71, 0x75,
	0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x27, 0x0a, 0x0d,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x22, 0xc4, 0x01, 0x0a, 0x08, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x0f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x48, 0x00, 0x52, 0x0e, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x40, 0x0a, 0x0f, 0x61,
	0x62, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x48, 0x00, 0x52, 0x0e, 0x61,
	0x62, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x23, 0x0a,
	0x0c, 0x6e, 0x65, 0x76, 0x65, 0x72, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0b, 0x6e, 0x65, 0x76, 0x65, 0x72, 0x45, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x71,
	0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x3f, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x22, 0xa3, 0x01, 0x0a, 0x08, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x37,
	0x0a, 0x0c, 0x66, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x00, 0x52, 0x0b, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x36, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x6d, 0x61,
	0x6c, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12,
	0x1e, 0x0a, 0x09, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x00, 0x52, 0x09, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x06, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x22, 0x9c, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x64, 0x65,
	0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x64, 0x65, 0x65,
	0x6d, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x28,
	0x0a, 0x10, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x6e, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x75, 0x6e, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x72, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x2a, 0x64, 0x0a, 0x0a, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x01, 0x12, 0x0b,
	0x0a, 0x07, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x53,
	0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x03, 0x12,
	0x0b, 0x0a, 0x07, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x10, 0x04, 0x42, 0x81, 0x01, 0x0a,
	0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_promotion_v1_promotion_proto_rawDescOnce sync.Once
	file_moego_models_promotion_v1_promotion_proto_rawDescData = file_moego_models_promotion_v1_promotion_proto_rawDesc
)

func file_moego_models_promotion_v1_promotion_proto_rawDescGZIP() []byte {
	file_moego_models_promotion_v1_promotion_proto_rawDescOnce.Do(func() {
		file_moego_models_promotion_v1_promotion_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_promotion_v1_promotion_proto_rawDescData)
	})
	return file_moego_models_promotion_v1_promotion_proto_rawDescData
}

var file_moego_models_promotion_v1_promotion_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_promotion_v1_promotion_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_moego_models_promotion_v1_promotion_proto_goTypes = []interface{}{
	(TargetType)(0),                 // 0: moego.models.promotion.v1.TargetType
	(Source_Type)(0),                // 1: moego.models.promotion.v1.Source.Type
	(*Source)(nil),                  // 2: moego.models.promotion.v1.Source
	(*Promotion)(nil),               // 3: moego.models.promotion.v1.Promotion
	(*Restrictions)(nil),            // 4: moego.models.promotion.v1.Restrictions
	(*Targets)(nil),                 // 5: moego.models.promotion.v1.Targets
	(*CouponApplicationTarget)(nil), // 6: moego.models.promotion.v1.CouponApplicationTarget
	(*Duration)(nil),                // 7: moego.models.promotion.v1.Duration
	(*Filter)(nil),                  // 8: moego.models.promotion.v1.Filter
	(*Discount)(nil),                // 9: moego.models.promotion.v1.Discount
	(*Redemptions)(nil),             // 10: moego.models.promotion.v1.Redemptions
	(*DiscountSubject)(nil),         // 11: moego.models.promotion.v1.DiscountSubject
	(*PackageSubject)(nil),          // 12: moego.models.promotion.v1.PackageSubject
	(*MembershipSubject)(nil),       // 13: moego.models.promotion.v1.MembershipSubject
	(*money.Money)(nil),             // 14: google.type.Money
	(*v1.TimePeriod)(nil),           // 15: moego.utils.v1.TimePeriod
	(*interval.Interval)(nil),       // 16: google.type.Interval
	(v2.Operator)(0),                // 17: moego.models.reporting.v2.Operator
	(*decimal.Decimal)(nil),         // 18: google.type.Decimal
}
var file_moego_models_promotion_v1_promotion_proto_depIdxs = []int32{
	1,  // 0: moego.models.promotion.v1.Source.type:type_name -> moego.models.promotion.v1.Source.Type
	11, // 1: moego.models.promotion.v1.Source.discount:type_name -> moego.models.promotion.v1.DiscountSubject
	12, // 2: moego.models.promotion.v1.Source.package:type_name -> moego.models.promotion.v1.PackageSubject
	13, // 3: moego.models.promotion.v1.Source.membership:type_name -> moego.models.promotion.v1.MembershipSubject
	2,  // 4: moego.models.promotion.v1.Promotion.source:type_name -> moego.models.promotion.v1.Source
	9,  // 5: moego.models.promotion.v1.Promotion.discount:type_name -> moego.models.promotion.v1.Discount
	4,  // 6: moego.models.promotion.v1.Promotion.restrictions:type_name -> moego.models.promotion.v1.Restrictions
	7,  // 7: moego.models.promotion.v1.Promotion.validity_period:type_name -> moego.models.promotion.v1.Duration
	7,  // 8: moego.models.promotion.v1.Promotion.available_period:type_name -> moego.models.promotion.v1.Duration
	10, // 9: moego.models.promotion.v1.Promotion.redemptions:type_name -> moego.models.promotion.v1.Redemptions
	5,  // 10: moego.models.promotion.v1.Restrictions.targets:type_name -> moego.models.promotion.v1.Targets
	8,  // 11: moego.models.promotion.v1.Restrictions.user_filters:type_name -> moego.models.promotion.v1.Filter
	0,  // 12: moego.models.promotion.v1.Targets.type:type_name -> moego.models.promotion.v1.TargetType
	0,  // 13: moego.models.promotion.v1.CouponApplicationTarget.target_type:type_name -> moego.models.promotion.v1.TargetType
	14, // 14: moego.models.promotion.v1.CouponApplicationTarget.unit_price:type_name -> google.type.Money
	15, // 15: moego.models.promotion.v1.Duration.relative_period:type_name -> moego.utils.v1.TimePeriod
	16, // 16: moego.models.promotion.v1.Duration.absolute_period:type_name -> google.type.Interval
	17, // 17: moego.models.promotion.v1.Filter.operator:type_name -> moego.models.reporting.v2.Operator
	14, // 18: moego.models.promotion.v1.Discount.fixed_amount:type_name -> google.type.Money
	18, // 19: moego.models.promotion.v1.Discount.percentage:type_name -> google.type.Decimal
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_moego_models_promotion_v1_promotion_proto_init() }
func file_moego_models_promotion_v1_promotion_proto_init() {
	if File_moego_models_promotion_v1_promotion_proto != nil {
		return
	}
	file_moego_models_promotion_v1_subject_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_promotion_v1_promotion_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Source); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_promotion_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Promotion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_promotion_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Restrictions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_promotion_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Targets); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_promotion_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CouponApplicationTarget); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_promotion_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Duration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_promotion_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_promotion_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Discount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_promotion_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Redemptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_promotion_v1_promotion_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Source_Discount)(nil),
		(*Source_Package)(nil),
		(*Source_Membership)(nil),
	}
	file_moego_models_promotion_v1_promotion_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_models_promotion_v1_promotion_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*Duration_RelativePeriod)(nil),
		(*Duration_AbsolutePeriod)(nil),
		(*Duration_NeverExpire)(nil),
	}
	file_moego_models_promotion_v1_promotion_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*Discount_FixedAmount)(nil),
		(*Discount_Percentage)(nil),
		(*Discount_Deduction)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_promotion_v1_promotion_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_promotion_v1_promotion_proto_goTypes,
		DependencyIndexes: file_moego_models_promotion_v1_promotion_proto_depIdxs,
		EnumInfos:         file_moego_models_promotion_v1_promotion_proto_enumTypes,
		MessageInfos:      file_moego_models_promotion_v1_promotion_proto_msgTypes,
	}.Build()
	File_moego_models_promotion_v1_promotion_proto = out.File
	file_moego_models_promotion_v1_promotion_proto_rawDesc = nil
	file_moego_models_promotion_v1_promotion_proto_goTypes = nil
	file_moego_models_promotion_v1_promotion_proto_depIdxs = nil
}
