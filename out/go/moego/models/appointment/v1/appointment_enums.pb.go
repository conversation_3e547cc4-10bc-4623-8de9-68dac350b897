// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/appointment/v1/appointment_enums.proto

package appointmentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// appointment type
type AppointmentSource int32

const (
	// unspecified
	AppointmentSource_APPOINTMENT_SOURCE_UNSPECIFIED AppointmentSource = 0
	// web
	AppointmentSource_WEB AppointmentSource = 22018
	// online booking
	AppointmentSource_ONLINE_BOOKING AppointmentSource = 22168
	// android
	AppointmentSource_ANDROID AppointmentSource = 17216
	// ios
	AppointmentSource_IOS AppointmentSource = 17802
	// auto dm
	AppointmentSource_AUTO_DM AppointmentSource = 23426
	// google calendar
	AppointmentSource_GOOGLE_CALENDAR AppointmentSource = 19826
	// open api
	AppointmentSource_OPEN_API AppointmentSource = 23333
)

// Enum value maps for AppointmentSource.
var (
	AppointmentSource_name = map[int32]string{
		0:     "APPOINTMENT_SOURCE_UNSPECIFIED",
		22018: "WEB",
		22168: "ONLINE_BOOKING",
		17216: "ANDROID",
		17802: "IOS",
		23426: "AUTO_DM",
		19826: "GOOGLE_CALENDAR",
		23333: "OPEN_API",
	}
	AppointmentSource_value = map[string]int32{
		"APPOINTMENT_SOURCE_UNSPECIFIED": 0,
		"WEB":                            22018,
		"ONLINE_BOOKING":                 22168,
		"ANDROID":                        17216,
		"IOS":                            17802,
		"AUTO_DM":                        23426,
		"GOOGLE_CALENDAR":                19826,
		"OPEN_API":                       23333,
	}
)

func (x AppointmentSource) Enum() *AppointmentSource {
	p := new(AppointmentSource)
	*p = x
	return p
}

func (x AppointmentSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentSource) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[0].Descriptor()
}

func (AppointmentSource) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[0]
}

func (x AppointmentSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentSource.Descriptor instead.
func (AppointmentSource) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_enums_proto_rawDescGZIP(), []int{0}
}

// appointment status
type AppointmentStatus int32

const (
	// unspecified
	AppointmentStatus_APPOINTMENT_STATUS_UNSPECIFIED AppointmentStatus = 0
	// unconfirmed
	AppointmentStatus_UNCONFIRMED AppointmentStatus = 1
	// confirmed
	AppointmentStatus_CONFIRMED AppointmentStatus = 2
	// finished
	AppointmentStatus_FINISHED AppointmentStatus = 3
	// canceled
	AppointmentStatus_CANCELED AppointmentStatus = 4
	// ready to check in
	AppointmentStatus_READY AppointmentStatus = 5
	// checked in
	AppointmentStatus_CHECKED_IN AppointmentStatus = 6
)

// Enum value maps for AppointmentStatus.
var (
	AppointmentStatus_name = map[int32]string{
		0: "APPOINTMENT_STATUS_UNSPECIFIED",
		1: "UNCONFIRMED",
		2: "CONFIRMED",
		3: "FINISHED",
		4: "CANCELED",
		5: "READY",
		6: "CHECKED_IN",
	}
	AppointmentStatus_value = map[string]int32{
		"APPOINTMENT_STATUS_UNSPECIFIED": 0,
		"UNCONFIRMED":                    1,
		"CONFIRMED":                      2,
		"FINISHED":                       3,
		"CANCELED":                       4,
		"READY":                          5,
		"CHECKED_IN":                     6,
	}
)

func (x AppointmentStatus) Enum() *AppointmentStatus {
	p := new(AppointmentStatus)
	*p = x
	return p
}

func (x AppointmentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[1].Descriptor()
}

func (AppointmentStatus) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[1]
}

func (x AppointmentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentStatus.Descriptor instead.
func (AppointmentStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_enums_proto_rawDescGZIP(), []int{1}
}

// appointment payment status
type AppointmentPaymentStatus int32

const (
	// unspecified
	AppointmentPaymentStatus_APPOINTMENT_PAYMENT_STATUS_UNSPECIFIED AppointmentPaymentStatus = 0
	// fully paid
	AppointmentPaymentStatus_FULLY_PAID AppointmentPaymentStatus = 1
	// unpaid
	AppointmentPaymentStatus_UNPAID AppointmentPaymentStatus = 2
	// partial paid
	AppointmentPaymentStatus_PARTIAL_PAID AppointmentPaymentStatus = 3
	// prepaid
	AppointmentPaymentStatus_PREPAID AppointmentPaymentStatus = 4
)

// Enum value maps for AppointmentPaymentStatus.
var (
	AppointmentPaymentStatus_name = map[int32]string{
		0: "APPOINTMENT_PAYMENT_STATUS_UNSPECIFIED",
		1: "FULLY_PAID",
		2: "UNPAID",
		3: "PARTIAL_PAID",
		4: "PREPAID",
	}
	AppointmentPaymentStatus_value = map[string]int32{
		"APPOINTMENT_PAYMENT_STATUS_UNSPECIFIED": 0,
		"FULLY_PAID":                             1,
		"UNPAID":                                 2,
		"PARTIAL_PAID":                           3,
		"PREPAID":                                4,
	}
)

func (x AppointmentPaymentStatus) Enum() *AppointmentPaymentStatus {
	p := new(AppointmentPaymentStatus)
	*p = x
	return p
}

func (x AppointmentPaymentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentPaymentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[2].Descriptor()
}

func (AppointmentPaymentStatus) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[2]
}

func (x AppointmentPaymentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentPaymentStatus.Descriptor instead.
func (AppointmentPaymentStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_enums_proto_rawDescGZIP(), []int{2}
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// appointment book online status
type AppointmentBookOnlineStatus int32

const (
	// not book online
	AppointmentBookOnlineStatus_NOT_BOOK_ONLINE AppointmentBookOnlineStatus = 0
	// book online
	AppointmentBookOnlineStatus_BOOK_ONLINE AppointmentBookOnlineStatus = 1
)

// Enum value maps for AppointmentBookOnlineStatus.
var (
	AppointmentBookOnlineStatus_name = map[int32]string{
		0: "NOT_BOOK_ONLINE",
		1: "BOOK_ONLINE",
	}
	AppointmentBookOnlineStatus_value = map[string]int32{
		"NOT_BOOK_ONLINE": 0,
		"BOOK_ONLINE":     1,
	}
)

func (x AppointmentBookOnlineStatus) Enum() *AppointmentBookOnlineStatus {
	p := new(AppointmentBookOnlineStatus)
	*p = x
	return p
}

func (x AppointmentBookOnlineStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentBookOnlineStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[3].Descriptor()
}

func (AppointmentBookOnlineStatus) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[3]
}

func (x AppointmentBookOnlineStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentBookOnlineStatus.Descriptor instead.
func (AppointmentBookOnlineStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_enums_proto_rawDescGZIP(), []int{3}
}

// appointment type
type AppointmentType int32

const (
	// unspecified
	AppointmentType_APPOINTMENT_TYPE_UNSPECIFIED AppointmentType = 0
	// upcoming appointment
	AppointmentType_UPCOMING AppointmentType = 1
	// history appointment
	AppointmentType_PAST AppointmentType = 2
	// booking request
	AppointmentType_PENDING AppointmentType = 3
)

// Enum value maps for AppointmentType.
var (
	AppointmentType_name = map[int32]string{
		0: "APPOINTMENT_TYPE_UNSPECIFIED",
		1: "UPCOMING",
		2: "PAST",
		3: "PENDING",
	}
	AppointmentType_value = map[string]int32{
		"APPOINTMENT_TYPE_UNSPECIFIED": 0,
		"UPCOMING":                     1,
		"PAST":                         2,
		"PENDING":                      3,
	}
)

func (x AppointmentType) Enum() *AppointmentType {
	p := new(AppointmentType)
	*p = x
	return p
}

func (x AppointmentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[4].Descriptor()
}

func (AppointmentType) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[4]
}

func (x AppointmentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentType.Descriptor instead.
func (AppointmentType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_enums_proto_rawDescGZIP(), []int{4}
}

// AppointmentScheduleType
type AppointmentScheduleType int32

const (
	// unspecified
	AppointmentScheduleType_APPOINTMENT_SCHEDULE_TYPE_UNSPECIFIED AppointmentScheduleType = 0
	// normal repeat
	AppointmentScheduleType_NORMAL_REPEAT AppointmentScheduleType = 1
	// smart schedule repeat
	AppointmentScheduleType_SMART_SCHEDULE_REPEAT AppointmentScheduleType = 2
)

// Enum value maps for AppointmentScheduleType.
var (
	AppointmentScheduleType_name = map[int32]string{
		0: "APPOINTMENT_SCHEDULE_TYPE_UNSPECIFIED",
		1: "NORMAL_REPEAT",
		2: "SMART_SCHEDULE_REPEAT",
	}
	AppointmentScheduleType_value = map[string]int32{
		"APPOINTMENT_SCHEDULE_TYPE_UNSPECIFIED": 0,
		"NORMAL_REPEAT":                         1,
		"SMART_SCHEDULE_REPEAT":                 2,
	}
)

func (x AppointmentScheduleType) Enum() *AppointmentScheduleType {
	p := new(AppointmentScheduleType)
	*p = x
	return p
}

func (x AppointmentScheduleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentScheduleType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[5].Descriptor()
}

func (AppointmentScheduleType) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[5]
}

func (x AppointmentScheduleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentScheduleType.Descriptor instead.
func (AppointmentScheduleType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_enums_proto_rawDescGZIP(), []int{5}
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// Appointment canceled by type
type AppointmentUpdatedBy int32

const (
	// by business
	AppointmentUpdatedBy_BY_BUSINESS AppointmentUpdatedBy = 0
	// by customer reply msg
	AppointmentUpdatedBy_BY_CUSTOMER_REPLY_MESSAGE AppointmentUpdatedBy = 1
	// by delete pet
	AppointmentUpdatedBy_BY_DELETE_PET AppointmentUpdatedBy = 2
	// by client portal
	AppointmentUpdatedBy_BY_CLIENT_PORTAL AppointmentUpdatedBy = 3
	// by pet parent app
	AppointmentUpdatedBy_BY_PET_PARENT_APP AppointmentUpdatedBy = 4
)

// Enum value maps for AppointmentUpdatedBy.
var (
	AppointmentUpdatedBy_name = map[int32]string{
		0: "BY_BUSINESS",
		1: "BY_CUSTOMER_REPLY_MESSAGE",
		2: "BY_DELETE_PET",
		3: "BY_CLIENT_PORTAL",
		4: "BY_PET_PARENT_APP",
	}
	AppointmentUpdatedBy_value = map[string]int32{
		"BY_BUSINESS":               0,
		"BY_CUSTOMER_REPLY_MESSAGE": 1,
		"BY_DELETE_PET":             2,
		"BY_CLIENT_PORTAL":          3,
		"BY_PET_PARENT_APP":         4,
	}
)

func (x AppointmentUpdatedBy) Enum() *AppointmentUpdatedBy {
	p := new(AppointmentUpdatedBy)
	*p = x
	return p
}

func (x AppointmentUpdatedBy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentUpdatedBy) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[6].Descriptor()
}

func (AppointmentUpdatedBy) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[6]
}

func (x AppointmentUpdatedBy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentUpdatedBy.Descriptor instead.
func (AppointmentUpdatedBy) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_enums_proto_rawDescGZIP(), []int{6}
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// AppointmentNotificationStatus
type AppointmentNotificationStatus int32

const (
	// not sent
	AppointmentNotificationStatus_NOTIFICATION_NOT_SENT AppointmentNotificationStatus = 0
	// success
	AppointmentNotificationStatus_NOTIFICATION_SUCCESS AppointmentNotificationStatus = 1
	// failed
	AppointmentNotificationStatus_NOTIFICATION_FAILED AppointmentNotificationStatus = 2
)

// Enum value maps for AppointmentNotificationStatus.
var (
	AppointmentNotificationStatus_name = map[int32]string{
		0: "NOTIFICATION_NOT_SENT",
		1: "NOTIFICATION_SUCCESS",
		2: "NOTIFICATION_FAILED",
	}
	AppointmentNotificationStatus_value = map[string]int32{
		"NOTIFICATION_NOT_SENT": 0,
		"NOTIFICATION_SUCCESS":  1,
		"NOTIFICATION_FAILED":   2,
	}
)

func (x AppointmentNotificationStatus) Enum() *AppointmentNotificationStatus {
	p := new(AppointmentNotificationStatus)
	*p = x
	return p
}

func (x AppointmentNotificationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentNotificationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[7].Descriptor()
}

func (AppointmentNotificationStatus) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[7]
}

func (x AppointmentNotificationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentNotificationStatus.Descriptor instead.
func (AppointmentNotificationStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_enums_proto_rawDescGZIP(), []int{7}
}

// AppointmentReminderType
type AppointmentReminderType int32

const (
	// unspecified
	AppointmentReminderType_APPOINTMENT_REMINDER_TYPE_UNSPECIFIED AppointmentReminderType = 0
	// message
	AppointmentReminderType_MESSAGE AppointmentReminderType = 1
	// email
	AppointmentReminderType_EMAIL AppointmentReminderType = 2
	// phone call
	AppointmentReminderType_PHONE_CALL AppointmentReminderType = 3
)

// Enum value maps for AppointmentReminderType.
var (
	AppointmentReminderType_name = map[int32]string{
		0: "APPOINTMENT_REMINDER_TYPE_UNSPECIFIED",
		1: "MESSAGE",
		2: "EMAIL",
		3: "PHONE_CALL",
	}
	AppointmentReminderType_value = map[string]int32{
		"APPOINTMENT_REMINDER_TYPE_UNSPECIFIED": 0,
		"MESSAGE":                               1,
		"EMAIL":                                 2,
		"PHONE_CALL":                            3,
	}
)

func (x AppointmentReminderType) Enum() *AppointmentReminderType {
	p := new(AppointmentReminderType)
	*p = x
	return p
}

func (x AppointmentReminderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentReminderType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[8].Descriptor()
}

func (AppointmentReminderType) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[8]
}

func (x AppointmentReminderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentReminderType.Descriptor instead.
func (AppointmentReminderType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_enums_proto_rawDescGZIP(), []int{8}
}

// Calendar card type
type CalendarCardType int32

const (
	// unspecified
	CalendarCardType_CALENDAR_CARD_TYPE_UNSPECIFIED CalendarCardType = 0
	// appointment
	CalendarCardType_APPOINTMENT CalendarCardType = 1
	// service detail
	// May contain one or more service cards
	CalendarCardType_SERVICE CalendarCardType = 2
	// multi-staff operation
	// May contain one or more operation cards
	CalendarCardType_OPERATION CalendarCardType = 3
	// block time
	CalendarCardType_BLOCK CalendarCardType = 4
	// pending booking request
	CalendarCardType_BOOKING_REQUEST CalendarCardType = 5
	// service and multi-staff operation
	CalendarCardType_SERVICE_AND_OPERATION CalendarCardType = 6
)

// Enum value maps for CalendarCardType.
var (
	CalendarCardType_name = map[int32]string{
		0: "CALENDAR_CARD_TYPE_UNSPECIFIED",
		1: "APPOINTMENT",
		2: "SERVICE",
		3: "OPERATION",
		4: "BLOCK",
		5: "BOOKING_REQUEST",
		6: "SERVICE_AND_OPERATION",
	}
	CalendarCardType_value = map[string]int32{
		"CALENDAR_CARD_TYPE_UNSPECIFIED": 0,
		"APPOINTMENT":                    1,
		"SERVICE":                        2,
		"OPERATION":                      3,
		"BLOCK":                          4,
		"BOOKING_REQUEST":                5,
		"SERVICE_AND_OPERATION":          6,
	}
)

func (x CalendarCardType) Enum() *CalendarCardType {
	p := new(CalendarCardType)
	*p = x
	return p
}

func (x CalendarCardType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CalendarCardType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[9].Descriptor()
}

func (CalendarCardType) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[9]
}

func (x CalendarCardType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CalendarCardType.Descriptor instead.
func (CalendarCardType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_enums_proto_rawDescGZIP(), []int{9}
}

// AppointmentNoShowStatus
type AppointmentNoShowStatus int32

const (
	// unspecified
	AppointmentNoShowStatus_APPOINTMENT_NO_SHOW_STATUS_UNSPECIFIED AppointmentNoShowStatus = 0
	// no show
	AppointmentNoShowStatus_NO_SHOW AppointmentNoShowStatus = 1
	// not no show
	AppointmentNoShowStatus_NOT_NO_SHOW AppointmentNoShowStatus = 2
)

// Enum value maps for AppointmentNoShowStatus.
var (
	AppointmentNoShowStatus_name = map[int32]string{
		0: "APPOINTMENT_NO_SHOW_STATUS_UNSPECIFIED",
		1: "NO_SHOW",
		2: "NOT_NO_SHOW",
	}
	AppointmentNoShowStatus_value = map[string]int32{
		"APPOINTMENT_NO_SHOW_STATUS_UNSPECIFIED": 0,
		"NO_SHOW":                                1,
		"NOT_NO_SHOW":                            2,
	}
)

func (x AppointmentNoShowStatus) Enum() *AppointmentNoShowStatus {
	p := new(AppointmentNoShowStatus)
	*p = x
	return p
}

func (x AppointmentNoShowStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentNoShowStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[10].Descriptor()
}

func (AppointmentNoShowStatus) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[10]
}

func (x AppointmentNoShowStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentNoShowStatus.Descriptor instead.
func (AppointmentNoShowStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_enums_proto_rawDescGZIP(), []int{10}
}

// AppointmentSortField
type AppointmentSortField int32

const (
	// unspecified
	AppointmentSortField_APPOINTMENT_SORT_FIELD_UNSPECIFIED AppointmentSortField = 0
	// start date time
	AppointmentSortField_START_DATE_TIME AppointmentSortField = 1
	// end date time
	AppointmentSortField_END_DATE_TIME AppointmentSortField = 2
)

// Enum value maps for AppointmentSortField.
var (
	AppointmentSortField_name = map[int32]string{
		0: "APPOINTMENT_SORT_FIELD_UNSPECIFIED",
		1: "START_DATE_TIME",
		2: "END_DATE_TIME",
	}
	AppointmentSortField_value = map[string]int32{
		"APPOINTMENT_SORT_FIELD_UNSPECIFIED": 0,
		"START_DATE_TIME":                    1,
		"END_DATE_TIME":                      2,
	}
)

func (x AppointmentSortField) Enum() *AppointmentSortField {
	p := new(AppointmentSortField)
	*p = x
	return p
}

func (x AppointmentSortField) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentSortField) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[11].Descriptor()
}

func (AppointmentSortField) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_appointment_enums_proto_enumTypes[11]
}

func (x AppointmentSortField) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentSortField.Descriptor instead.
func (AppointmentSortField) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_enums_proto_rawDescGZIP(), []int{11}
}

var File_moego_models_appointment_v1_appointment_enums_proto protoreflect.FileDescriptor

var file_moego_models_appointment_v1_appointment_enums_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2a, 0xa8, 0x01, 0x0a, 0x11, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x50, 0x50, 0x4f,
	0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x03,
	0x57, 0x45, 0x42, 0x10, 0x82, 0xac, 0x01, 0x12, 0x14, 0x0a, 0x0e, 0x4f, 0x4e, 0x4c, 0x49, 0x4e,
	0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x98, 0xad, 0x01, 0x12, 0x0d, 0x0a,
	0x07, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x10, 0xc0, 0x86, 0x01, 0x12, 0x09, 0x0a, 0x03,
	0x49, 0x4f, 0x53, 0x10, 0x8a, 0x8b, 0x01, 0x12, 0x0d, 0x0a, 0x07, 0x41, 0x55, 0x54, 0x4f, 0x5f,
	0x44, 0x4d, 0x10, 0x82, 0xb7, 0x01, 0x12, 0x15, 0x0a, 0x0f, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45,
	0x5f, 0x43, 0x41, 0x4c, 0x45, 0x4e, 0x44, 0x41, 0x52, 0x10, 0xf2, 0x9a, 0x01, 0x12, 0x0e, 0x0a,
	0x08, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x41, 0x50, 0x49, 0x10, 0xa5, 0xb6, 0x01, 0x2a, 0x8e, 0x01,
	0x0a, 0x11, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x43, 0x4f, 0x4e,
	0x46, 0x49, 0x52, 0x4d, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x4f, 0x4e, 0x46,
	0x49, 0x52, 0x4d, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x49, 0x4e, 0x49, 0x53,
	0x48, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45,
	0x44, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x05, 0x12, 0x0e,
	0x0a, 0x0a, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x10, 0x06, 0x2a, 0x81,
	0x01, 0x0a, 0x18, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x26, 0x41,
	0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x55, 0x4c, 0x4c, 0x59,
	0x5f, 0x50, 0x41, 0x49, 0x44, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x4e, 0x50, 0x41, 0x49,
	0x44, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x41, 0x52, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x50,
	0x41, 0x49, 0x44, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x52, 0x45, 0x50, 0x41, 0x49, 0x44,
	0x10, 0x04, 0x2a, 0x43, 0x0a, 0x1b, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x13, 0x0a, 0x0f, 0x4e, 0x4f, 0x54, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x4f, 0x4e,
	0x4c, 0x49, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x4f,
	0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x01, 0x2a, 0x58, 0x0a, 0x0f, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x50,
	0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08,
	0x55, 0x50, 0x43, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x41,
	0x53, 0x54, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0x03, 0x2a, 0x72, 0x0a, 0x17, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x25,
	0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x48, 0x45,
	0x44, 0x55, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x4e, 0x4f, 0x52, 0x4d, 0x41,
	0x4c, 0x5f, 0x52, 0x45, 0x50, 0x45, 0x41, 0x54, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x4d,
	0x41, 0x52, 0x54, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x50,
	0x45, 0x41, 0x54, 0x10, 0x02, 0x2a, 0x86, 0x01, 0x0a, 0x14, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x0f,
	0x0a, 0x0b, 0x42, 0x59, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x00, 0x12,
	0x1d, 0x0a, 0x19, 0x42, 0x59, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x52,
	0x45, 0x50, 0x4c, 0x59, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10, 0x01, 0x12, 0x11,
	0x0a, 0x0d, 0x42, 0x59, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x50, 0x45, 0x54, 0x10,
	0x02, 0x12, 0x14, 0x0a, 0x10, 0x42, 0x59, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x50,
	0x4f, 0x52, 0x54, 0x41, 0x4c, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x42, 0x59, 0x5f, 0x50, 0x45,
	0x54, 0x5f, 0x50, 0x41, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x04, 0x2a, 0x6d,
	0x0a, 0x1d, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x19, 0x0a, 0x15, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x6c, 0x0a,
	0x17, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69,
	0x6e, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x50, 0x50, 0x4f,
	0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10, 0x01,
	0x12, 0x09, 0x0a, 0x05, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x50,
	0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x10, 0x03, 0x2a, 0x9e, 0x01, 0x0a, 0x10,
	0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x43, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x22, 0x0a, 0x1e, 0x43, 0x41, 0x4c, 0x45, 0x4e, 0x44, 0x41, 0x52, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45,
	0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x03, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f,
	0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10,
	0x05, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x41, 0x4e, 0x44,
	0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06, 0x2a, 0x63, 0x0a, 0x17,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x53, 0x68, 0x6f,
	0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x26, 0x41, 0x50, 0x50, 0x4f, 0x49,
	0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x5f, 0x53, 0x48, 0x4f, 0x57, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x4f, 0x5f, 0x53, 0x48, 0x4f, 0x57, 0x10, 0x01,
	0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x4f, 0x54, 0x5f, 0x4e, 0x4f, 0x5f, 0x53, 0x48, 0x4f, 0x57, 0x10,
	0x02, 0x2a, 0x66, 0x0a, 0x14, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x50, 0x50,
	0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x4f, 0x52, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x5f,
	0x54, 0x49, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x4e, 0x44, 0x5f, 0x44, 0x41,
	0x54, 0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x02, 0x42, 0x87, 0x01, 0x0a, 0x23, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_appointment_v1_appointment_enums_proto_rawDescOnce sync.Once
	file_moego_models_appointment_v1_appointment_enums_proto_rawDescData = file_moego_models_appointment_v1_appointment_enums_proto_rawDesc
)

func file_moego_models_appointment_v1_appointment_enums_proto_rawDescGZIP() []byte {
	file_moego_models_appointment_v1_appointment_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_appointment_v1_appointment_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_appointment_v1_appointment_enums_proto_rawDescData)
	})
	return file_moego_models_appointment_v1_appointment_enums_proto_rawDescData
}

var file_moego_models_appointment_v1_appointment_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 12)
var file_moego_models_appointment_v1_appointment_enums_proto_goTypes = []interface{}{
	(AppointmentSource)(0),             // 0: moego.models.appointment.v1.AppointmentSource
	(AppointmentStatus)(0),             // 1: moego.models.appointment.v1.AppointmentStatus
	(AppointmentPaymentStatus)(0),      // 2: moego.models.appointment.v1.AppointmentPaymentStatus
	(AppointmentBookOnlineStatus)(0),   // 3: moego.models.appointment.v1.AppointmentBookOnlineStatus
	(AppointmentType)(0),               // 4: moego.models.appointment.v1.AppointmentType
	(AppointmentScheduleType)(0),       // 5: moego.models.appointment.v1.AppointmentScheduleType
	(AppointmentUpdatedBy)(0),          // 6: moego.models.appointment.v1.AppointmentUpdatedBy
	(AppointmentNotificationStatus)(0), // 7: moego.models.appointment.v1.AppointmentNotificationStatus
	(AppointmentReminderType)(0),       // 8: moego.models.appointment.v1.AppointmentReminderType
	(CalendarCardType)(0),              // 9: moego.models.appointment.v1.CalendarCardType
	(AppointmentNoShowStatus)(0),       // 10: moego.models.appointment.v1.AppointmentNoShowStatus
	(AppointmentSortField)(0),          // 11: moego.models.appointment.v1.AppointmentSortField
}
var file_moego_models_appointment_v1_appointment_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_appointment_v1_appointment_enums_proto_init() }
func file_moego_models_appointment_v1_appointment_enums_proto_init() {
	if File_moego_models_appointment_v1_appointment_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_appointment_v1_appointment_enums_proto_rawDesc,
			NumEnums:      12,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_appointment_v1_appointment_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_appointment_v1_appointment_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_appointment_v1_appointment_enums_proto_enumTypes,
	}.Build()
	File_moego_models_appointment_v1_appointment_enums_proto = out.File
	file_moego_models_appointment_v1_appointment_enums_proto_rawDesc = nil
	file_moego_models_appointment_v1_appointment_enums_proto_goTypes = nil
	file_moego_models_appointment_v1_appointment_enums_proto_depIdxs = nil
}
