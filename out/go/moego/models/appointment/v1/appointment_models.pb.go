// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/appointment/v1/appointment_models.proto

package appointmentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// the appointment model
type AppointmentModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// order id
	OrderId string `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// appointment date, in yyyy-MM-dd format
	AppointmentDate string `protobuf:"bytes,5,opt,name=appointment_date,json=appointmentDate,proto3" json:"appointment_date,omitempty"`
	// appointment start time, the number of minutes of the day
	AppointmentStartTime int32 `protobuf:"varint,6,opt,name=appointment_start_time,json=appointmentStartTime,proto3" json:"appointment_start_time,omitempty"`
	// appointment end time, the number of minutes of the day
	AppointmentEndTime int32 `protobuf:"varint,7,opt,name=appointment_end_time,json=appointmentEndTime,proto3" json:"appointment_end_time,omitempty"`
	// is waiting list
	IsWaitingList bool `protobuf:"varint,8,opt,name=is_waiting_list,json=isWaitingList,proto3" json:"is_waiting_list,omitempty"`
	// move waiting list staff id
	MoveWaitingBy int64 `protobuf:"varint,9,opt,name=move_waiting_by,json=moveWaitingBy,proto3" json:"move_waiting_by,omitempty"`
	// confirmed time
	ConfirmedTime *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=confirmed_time,json=confirmedTime,proto3" json:"confirmed_time,omitempty"`
	// check in time
	CheckInTime *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=check_in_time,json=checkInTime,proto3" json:"check_in_time,omitempty"`
	// check out time
	CheckOutTime *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=check_out_time,json=checkOutTime,proto3" json:"check_out_time,omitempty"`
	// canceled time
	CanceledTime *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=canceled_time,json=canceledTime,proto3" json:"canceled_time,omitempty"`
	// appointment status
	Status AppointmentStatus `protobuf:"varint,14,opt,name=status,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"status,omitempty"`
	// is block
	IsBlock bool `protobuf:"varint,15,opt,name=is_block,json=isBlock,proto3" json:"is_block,omitempty"`
	// book online status
	BookOnlineStatus int32 `protobuf:"varint,16,opt,name=book_online_status,json=bookOnlineStatus,proto3" json:"book_online_status,omitempty"`
	// customer address id
	CustomerAddressId int64 `protobuf:"varint,17,opt,name=customer_address_id,json=customerAddressId,proto3" json:"customer_address_id,omitempty"`
	// repeat id
	RepeatId int64 `protobuf:"varint,18,opt,name=repeat_id,json=repeatId,proto3" json:"repeat_id,omitempty"`
	// is paid
	IsPaid AppointmentPaymentStatus `protobuf:"varint,19,opt,name=is_paid,json=isPaid,proto3,enum=moego.models.appointment.v1.AppointmentPaymentStatus" json:"is_paid,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,20,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// no show
	NoShow bool `protobuf:"varint,21,opt,name=no_show,json=noShow,proto3" json:"no_show,omitempty"`
	// no show fee
	NoShowFee float64 `protobuf:"fixed64,22,opt,name=no_show_fee,json=noShowFee,proto3" json:"no_show_fee,omitempty"`
	// is push notification
	PushNotification bool `protobuf:"varint,23,opt,name=push_notification,json=pushNotification,proto3" json:"push_notification,omitempty"`
	// cancel by type
	CancelByType int32 `protobuf:"varint,24,opt,name=cancel_by_type,json=cancelByType,proto3" json:"cancel_by_type,omitempty"`
	// cancel by
	CancelBy int64 `protobuf:"varint,25,opt,name=cancel_by,json=cancelBy,proto3" json:"cancel_by,omitempty"`
	// confirm by type
	ConfirmByType int32 `protobuf:"varint,26,opt,name=confirm_by_type,json=confirmByType,proto3" json:"confirm_by_type,omitempty"`
	// confirm by
	ConfirmBy int64 `protobuf:"varint,27,opt,name=confirm_by,json=confirmBy,proto3" json:"confirm_by,omitempty"`
	// create by id
	CreatedById int64 `protobuf:"varint,28,opt,name=created_by_id,json=createdById,proto3" json:"created_by_id,omitempty"`
	// out of area
	OutOfArea bool `protobuf:"varint,29,opt,name=out_of_area,json=outOfArea,proto3" json:"out_of_area,omitempty"`
	// is deprecate
	IsDeprecate bool `protobuf:"varint,30,opt,name=is_deprecate,json=isDeprecate,proto3" json:"is_deprecate,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,31,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,32,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// source
	Source AppointmentSource `protobuf:"varint,33,opt,name=source,proto3,enum=moego.models.appointment.v1.AppointmentSource" json:"source,omitempty"`
	// old appointment date
	OldAppointmentDate string `protobuf:"bytes,34,opt,name=old_appointment_date,json=oldAppointmentDate,proto3" json:"old_appointment_date,omitempty"`
	// old appointment start time
	OldAppointmentStartTime int32 `protobuf:"varint,35,opt,name=old_appointment_start_time,json=oldAppointmentStartTime,proto3" json:"old_appointment_start_time,omitempty"`
	// old appointment end time
	OldAppointmentEndTime int32 `protobuf:"varint,36,opt,name=old_appointment_end_time,json=oldAppointmentEndTime,proto3" json:"old_appointment_end_time,omitempty"`
	// old appointment id
	OldApptId int64 `protobuf:"varint,37,opt,name=old_appt_id,json=oldApptId,proto3" json:"old_appt_id,omitempty"`
	// schedule type
	ScheduleType int32 `protobuf:"varint,38,opt,name=schedule_type,json=scheduleType,proto3" json:"schedule_type,omitempty"`
	// source platform
	SourcePlatform string `protobuf:"bytes,39,opt,name=source_platform,json=sourcePlatform,proto3" json:"source_platform,omitempty"`
	// ready time, deprecated, use ready_timestamp instead
	//
	// Deprecated: Do not use.
	ReadyTime int64 `protobuf:"varint,40,opt,name=ready_time,json=readyTime,proto3" json:"ready_time,omitempty"`
	// pickup notification send status
	PickupNotificationSendStatus int32 `protobuf:"varint,41,opt,name=pickup_notification_send_status,json=pickupNotificationSendStatus,proto3" json:"pickup_notification_send_status,omitempty"`
	// pickup notification failed reason
	PickupNotificationFailedReason string `protobuf:"bytes,42,opt,name=pickup_notification_failed_reason,json=pickupNotificationFailedReason,proto3" json:"pickup_notification_failed_reason,omitempty"`
	// status before checkin
	StatusBeforeCheckin AppointmentStatus `protobuf:"varint,43,opt,name=status_before_checkin,json=statusBeforeCheckin,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"status_before_checkin,omitempty"`
	// status before ready
	StatusBeforeReady AppointmentStatus `protobuf:"varint,44,opt,name=status_before_ready,json=statusBeforeReady,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"status_before_ready,omitempty"`
	// status before finish
	StatusBeforeFinish AppointmentStatus `protobuf:"varint,45,opt,name=status_before_finish,json=statusBeforeFinish,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"status_before_finish,omitempty"`
	// no start time
	NoStartTime bool `protobuf:"varint,46,opt,name=no_start_time,json=noStartTime,proto3" json:"no_start_time,omitempty"`
	// updated by id
	UpdatedById int64 `protobuf:"varint,47,opt,name=updated_by_id,json=updatedById,proto3" json:"updated_by_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,48,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// is auto accept
	IsAutoAccept bool `protobuf:"varint,49,opt,name=is_auto_accept,json=isAutoAccept,proto3" json:"is_auto_accept,omitempty"`
	// wait list status
	WaitListStatus WaitListStatus `protobuf:"varint,50,opt,name=wait_list_status,json=waitListStatus,proto3,enum=moego.models.appointment.v1.WaitListStatus" json:"wait_list_status,omitempty"`
	// end date
	AppointmentEndDate string `protobuf:"bytes,51,opt,name=appointment_end_date,json=appointmentEndDate,proto3" json:"appointment_end_date,omitempty"`
	// service type include
	ServiceTypeInclude int32 `protobuf:"varint,52,opt,name=service_type_include,json=serviceTypeInclude,proto3" json:"service_type_include,omitempty"`
	// ready timestamp
	ReadyTimestamp *timestamppb.Timestamp `protobuf:"bytes,53,opt,name=ready_timestamp,json=readyTimestamp,proto3" json:"ready_timestamp,omitempty"`
	// Is new order
	IsNewOrder bool `protobuf:"varint,54,opt,name=is_new_order,json=isNewOrder,proto3" json:"is_new_order,omitempty"`
}

func (x *AppointmentModel) Reset() {
	*x = AppointmentModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_appointment_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentModel) ProtoMessage() {}

func (x *AppointmentModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_appointment_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentModel.ProtoReflect.Descriptor instead.
func (*AppointmentModel) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_models_proto_rawDescGZIP(), []int{0}
}

func (x *AppointmentModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AppointmentModel) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *AppointmentModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AppointmentModel) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *AppointmentModel) GetAppointmentDate() string {
	if x != nil {
		return x.AppointmentDate
	}
	return ""
}

func (x *AppointmentModel) GetAppointmentStartTime() int32 {
	if x != nil {
		return x.AppointmentStartTime
	}
	return 0
}

func (x *AppointmentModel) GetAppointmentEndTime() int32 {
	if x != nil {
		return x.AppointmentEndTime
	}
	return 0
}

func (x *AppointmentModel) GetIsWaitingList() bool {
	if x != nil {
		return x.IsWaitingList
	}
	return false
}

func (x *AppointmentModel) GetMoveWaitingBy() int64 {
	if x != nil {
		return x.MoveWaitingBy
	}
	return 0
}

func (x *AppointmentModel) GetConfirmedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ConfirmedTime
	}
	return nil
}

func (x *AppointmentModel) GetCheckInTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CheckInTime
	}
	return nil
}

func (x *AppointmentModel) GetCheckOutTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CheckOutTime
	}
	return nil
}

func (x *AppointmentModel) GetCanceledTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CanceledTime
	}
	return nil
}

func (x *AppointmentModel) GetStatus() AppointmentStatus {
	if x != nil {
		return x.Status
	}
	return AppointmentStatus_APPOINTMENT_STATUS_UNSPECIFIED
}

func (x *AppointmentModel) GetIsBlock() bool {
	if x != nil {
		return x.IsBlock
	}
	return false
}

func (x *AppointmentModel) GetBookOnlineStatus() int32 {
	if x != nil {
		return x.BookOnlineStatus
	}
	return 0
}

func (x *AppointmentModel) GetCustomerAddressId() int64 {
	if x != nil {
		return x.CustomerAddressId
	}
	return 0
}

func (x *AppointmentModel) GetRepeatId() int64 {
	if x != nil {
		return x.RepeatId
	}
	return 0
}

func (x *AppointmentModel) GetIsPaid() AppointmentPaymentStatus {
	if x != nil {
		return x.IsPaid
	}
	return AppointmentPaymentStatus_APPOINTMENT_PAYMENT_STATUS_UNSPECIFIED
}

func (x *AppointmentModel) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *AppointmentModel) GetNoShow() bool {
	if x != nil {
		return x.NoShow
	}
	return false
}

func (x *AppointmentModel) GetNoShowFee() float64 {
	if x != nil {
		return x.NoShowFee
	}
	return 0
}

func (x *AppointmentModel) GetPushNotification() bool {
	if x != nil {
		return x.PushNotification
	}
	return false
}

func (x *AppointmentModel) GetCancelByType() int32 {
	if x != nil {
		return x.CancelByType
	}
	return 0
}

func (x *AppointmentModel) GetCancelBy() int64 {
	if x != nil {
		return x.CancelBy
	}
	return 0
}

func (x *AppointmentModel) GetConfirmByType() int32 {
	if x != nil {
		return x.ConfirmByType
	}
	return 0
}

func (x *AppointmentModel) GetConfirmBy() int64 {
	if x != nil {
		return x.ConfirmBy
	}
	return 0
}

func (x *AppointmentModel) GetCreatedById() int64 {
	if x != nil {
		return x.CreatedById
	}
	return 0
}

func (x *AppointmentModel) GetOutOfArea() bool {
	if x != nil {
		return x.OutOfArea
	}
	return false
}

func (x *AppointmentModel) GetIsDeprecate() bool {
	if x != nil {
		return x.IsDeprecate
	}
	return false
}

func (x *AppointmentModel) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *AppointmentModel) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *AppointmentModel) GetSource() AppointmentSource {
	if x != nil {
		return x.Source
	}
	return AppointmentSource_APPOINTMENT_SOURCE_UNSPECIFIED
}

func (x *AppointmentModel) GetOldAppointmentDate() string {
	if x != nil {
		return x.OldAppointmentDate
	}
	return ""
}

func (x *AppointmentModel) GetOldAppointmentStartTime() int32 {
	if x != nil {
		return x.OldAppointmentStartTime
	}
	return 0
}

func (x *AppointmentModel) GetOldAppointmentEndTime() int32 {
	if x != nil {
		return x.OldAppointmentEndTime
	}
	return 0
}

func (x *AppointmentModel) GetOldApptId() int64 {
	if x != nil {
		return x.OldApptId
	}
	return 0
}

func (x *AppointmentModel) GetScheduleType() int32 {
	if x != nil {
		return x.ScheduleType
	}
	return 0
}

func (x *AppointmentModel) GetSourcePlatform() string {
	if x != nil {
		return x.SourcePlatform
	}
	return ""
}

// Deprecated: Do not use.
func (x *AppointmentModel) GetReadyTime() int64 {
	if x != nil {
		return x.ReadyTime
	}
	return 0
}

func (x *AppointmentModel) GetPickupNotificationSendStatus() int32 {
	if x != nil {
		return x.PickupNotificationSendStatus
	}
	return 0
}

func (x *AppointmentModel) GetPickupNotificationFailedReason() string {
	if x != nil {
		return x.PickupNotificationFailedReason
	}
	return ""
}

func (x *AppointmentModel) GetStatusBeforeCheckin() AppointmentStatus {
	if x != nil {
		return x.StatusBeforeCheckin
	}
	return AppointmentStatus_APPOINTMENT_STATUS_UNSPECIFIED
}

func (x *AppointmentModel) GetStatusBeforeReady() AppointmentStatus {
	if x != nil {
		return x.StatusBeforeReady
	}
	return AppointmentStatus_APPOINTMENT_STATUS_UNSPECIFIED
}

func (x *AppointmentModel) GetStatusBeforeFinish() AppointmentStatus {
	if x != nil {
		return x.StatusBeforeFinish
	}
	return AppointmentStatus_APPOINTMENT_STATUS_UNSPECIFIED
}

func (x *AppointmentModel) GetNoStartTime() bool {
	if x != nil {
		return x.NoStartTime
	}
	return false
}

func (x *AppointmentModel) GetUpdatedById() int64 {
	if x != nil {
		return x.UpdatedById
	}
	return 0
}

func (x *AppointmentModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *AppointmentModel) GetIsAutoAccept() bool {
	if x != nil {
		return x.IsAutoAccept
	}
	return false
}

func (x *AppointmentModel) GetWaitListStatus() WaitListStatus {
	if x != nil {
		return x.WaitListStatus
	}
	return WaitListStatus_APPTONLY
}

func (x *AppointmentModel) GetAppointmentEndDate() string {
	if x != nil {
		return x.AppointmentEndDate
	}
	return ""
}

func (x *AppointmentModel) GetServiceTypeInclude() int32 {
	if x != nil {
		return x.ServiceTypeInclude
	}
	return 0
}

func (x *AppointmentModel) GetReadyTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ReadyTimestamp
	}
	return nil
}

func (x *AppointmentModel) GetIsNewOrder() bool {
	if x != nil {
		return x.IsNewOrder
	}
	return false
}

// the appointment calendar view
type AppointmentCalendarView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// appointment date, in yyyy-MM-dd format
	AppointmentDate string `protobuf:"bytes,5,opt,name=appointment_date,json=appointmentDate,proto3" json:"appointment_date,omitempty"`
	// appointment start time, the number of minutes of the day
	AppointmentStartTime int32 `protobuf:"varint,6,opt,name=appointment_start_time,json=appointmentStartTime,proto3" json:"appointment_start_time,omitempty"`
	// appointment end time, the number of minutes of the day
	AppointmentEndTime int32 `protobuf:"varint,7,opt,name=appointment_end_time,json=appointmentEndTime,proto3" json:"appointment_end_time,omitempty"`
	// is waiting list
	IsWaitingList bool `protobuf:"varint,8,opt,name=is_waiting_list,json=isWaitingList,proto3" json:"is_waiting_list,omitempty"`
	// move waiting list staff id
	MoveWaitingBy int64 `protobuf:"varint,9,opt,name=move_waiting_by,json=moveWaitingBy,proto3" json:"move_waiting_by,omitempty"`
	// confirmed time
	ConfirmedTime *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=confirmed_time,json=confirmedTime,proto3" json:"confirmed_time,omitempty"`
	// check in time
	CheckInTime *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=check_in_time,json=checkInTime,proto3" json:"check_in_time,omitempty"`
	// check out time
	CheckOutTime *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=check_out_time,json=checkOutTime,proto3" json:"check_out_time,omitempty"`
	// canceled time
	CanceledTime *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=canceled_time,json=canceledTime,proto3" json:"canceled_time,omitempty"`
	// appointment status
	Status AppointmentStatus `protobuf:"varint,14,opt,name=status,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"status,omitempty"`
	// is block
	IsBlock bool `protobuf:"varint,15,opt,name=is_block,json=isBlock,proto3" json:"is_block,omitempty"`
	// book online status
	BookOnlineStatus int32 `protobuf:"varint,16,opt,name=book_online_status,json=bookOnlineStatus,proto3" json:"book_online_status,omitempty"`
	// customer address id
	CustomerAddressId int64 `protobuf:"varint,17,opt,name=customer_address_id,json=customerAddressId,proto3" json:"customer_address_id,omitempty"`
	// repeat id
	RepeatId int64 `protobuf:"varint,18,opt,name=repeat_id,json=repeatId,proto3" json:"repeat_id,omitempty"`
	// is paid
	IsPaid AppointmentPaymentStatus `protobuf:"varint,19,opt,name=is_paid,json=isPaid,proto3,enum=moego.models.appointment.v1.AppointmentPaymentStatus" json:"is_paid,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,20,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// no show
	NoShow bool `protobuf:"varint,21,opt,name=no_show,json=noShow,proto3" json:"no_show,omitempty"`
	// no show fee
	NoShowFee float64 `protobuf:"fixed64,22,opt,name=no_show_fee,json=noShowFee,proto3" json:"no_show_fee,omitempty"`
	// is push notification
	PushNotification bool `protobuf:"varint,23,opt,name=push_notification,json=pushNotification,proto3" json:"push_notification,omitempty"`
	// cancel by type
	CancelByType int32 `protobuf:"varint,24,opt,name=cancel_by_type,json=cancelByType,proto3" json:"cancel_by_type,omitempty"`
	// cancel by
	CancelBy int64 `protobuf:"varint,25,opt,name=cancel_by,json=cancelBy,proto3" json:"cancel_by,omitempty"`
	// confirm by type
	ConfirmByType int32 `protobuf:"varint,26,opt,name=confirm_by_type,json=confirmByType,proto3" json:"confirm_by_type,omitempty"`
	// confirm by
	ConfirmBy int64 `protobuf:"varint,27,opt,name=confirm_by,json=confirmBy,proto3" json:"confirm_by,omitempty"`
	// create by id
	CreatedById int64 `protobuf:"varint,28,opt,name=created_by_id,json=createdById,proto3" json:"created_by_id,omitempty"`
	// out of area
	OutOfArea bool `protobuf:"varint,29,opt,name=out_of_area,json=outOfArea,proto3" json:"out_of_area,omitempty"`
	// is deprecate
	IsDeprecate bool `protobuf:"varint,30,opt,name=is_deprecate,json=isDeprecate,proto3" json:"is_deprecate,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,31,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,32,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// source
	Source AppointmentSource `protobuf:"varint,33,opt,name=source,proto3,enum=moego.models.appointment.v1.AppointmentSource" json:"source,omitempty"`
	// old appointment date
	OldAppointmentDate string `protobuf:"bytes,34,opt,name=old_appointment_date,json=oldAppointmentDate,proto3" json:"old_appointment_date,omitempty"`
	// old appointment start time
	OldAppointmentStartTime int32 `protobuf:"varint,35,opt,name=old_appointment_start_time,json=oldAppointmentStartTime,proto3" json:"old_appointment_start_time,omitempty"`
	// old appointment end time
	OldAppointmentEndTime int32 `protobuf:"varint,36,opt,name=old_appointment_end_time,json=oldAppointmentEndTime,proto3" json:"old_appointment_end_time,omitempty"`
	// old appointment id
	OldApptId int64 `protobuf:"varint,37,opt,name=old_appt_id,json=oldApptId,proto3" json:"old_appt_id,omitempty"`
	// schedule type
	ScheduleType int32 `protobuf:"varint,38,opt,name=schedule_type,json=scheduleType,proto3" json:"schedule_type,omitempty"`
	// source platform
	SourcePlatform string `protobuf:"bytes,39,opt,name=source_platform,json=sourcePlatform,proto3" json:"source_platform,omitempty"`
	// ready time, deprecated, use ready_timestamp instead
	//
	// Deprecated: Do not use.
	ReadyTime int64 `protobuf:"varint,40,opt,name=ready_time,json=readyTime,proto3" json:"ready_time,omitempty"`
	// pickup notification send status
	PickupNotificationSendStatus int32 `protobuf:"varint,41,opt,name=pickup_notification_send_status,json=pickupNotificationSendStatus,proto3" json:"pickup_notification_send_status,omitempty"`
	// pickup notification failed reason
	PickupNotificationFailedReason string `protobuf:"bytes,42,opt,name=pickup_notification_failed_reason,json=pickupNotificationFailedReason,proto3" json:"pickup_notification_failed_reason,omitempty"`
	// status before checkin
	StatusBeforeCheckin AppointmentStatus `protobuf:"varint,43,opt,name=status_before_checkin,json=statusBeforeCheckin,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"status_before_checkin,omitempty"`
	// status before ready
	StatusBeforeReady AppointmentStatus `protobuf:"varint,44,opt,name=status_before_ready,json=statusBeforeReady,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"status_before_ready,omitempty"`
	// status before finish
	StatusBeforeFinish AppointmentStatus `protobuf:"varint,45,opt,name=status_before_finish,json=statusBeforeFinish,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"status_before_finish,omitempty"`
	// no start time
	NoStartTime bool `protobuf:"varint,46,opt,name=no_start_time,json=noStartTime,proto3" json:"no_start_time,omitempty"`
	// updated by id
	UpdatedById int64 `protobuf:"varint,47,opt,name=updated_by_id,json=updatedById,proto3" json:"updated_by_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,48,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// is auto accept
	IsAutoAccept bool `protobuf:"varint,49,opt,name=is_auto_accept,json=isAutoAccept,proto3" json:"is_auto_accept,omitempty"`
	// wait list status
	WaitListStatus WaitListStatus `protobuf:"varint,50,opt,name=wait_list_status,json=waitListStatus,proto3,enum=moego.models.appointment.v1.WaitListStatus" json:"wait_list_status,omitempty"`
	// end date
	AppointmentEndDate string `protobuf:"bytes,51,opt,name=appointment_end_date,json=appointmentEndDate,proto3" json:"appointment_end_date,omitempty"`
	// service type include
	ServiceTypeInclude int32 `protobuf:"varint,52,opt,name=service_type_include,json=serviceTypeInclude,proto3" json:"service_type_include,omitempty"`
	// start at same time
	StartAtSameTime bool `protobuf:"varint,53,opt,name=start_at_same_time,json=startAtSameTime,proto3" json:"start_at_same_time,omitempty"`
	// ready timestamp
	ReadyTimestamp *timestamppb.Timestamp `protobuf:"bytes,54,opt,name=ready_timestamp,json=readyTimestamp,proto3" json:"ready_timestamp,omitempty"`
	// Is new order flow
	IsNewOrder bool `protobuf:"varint,60,opt,name=is_new_order,json=isNewOrder,proto3" json:"is_new_order,omitempty"`
	// Invoice ID, Unique big invoice that identifies the appointment
	InvoiceId string `protobuf:"bytes,61,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
}

func (x *AppointmentCalendarView) Reset() {
	*x = AppointmentCalendarView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_appointment_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentCalendarView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentCalendarView) ProtoMessage() {}

func (x *AppointmentCalendarView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_appointment_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentCalendarView.ProtoReflect.Descriptor instead.
func (*AppointmentCalendarView) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_models_proto_rawDescGZIP(), []int{1}
}

func (x *AppointmentCalendarView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AppointmentCalendarView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AppointmentCalendarView) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *AppointmentCalendarView) GetAppointmentDate() string {
	if x != nil {
		return x.AppointmentDate
	}
	return ""
}

func (x *AppointmentCalendarView) GetAppointmentStartTime() int32 {
	if x != nil {
		return x.AppointmentStartTime
	}
	return 0
}

func (x *AppointmentCalendarView) GetAppointmentEndTime() int32 {
	if x != nil {
		return x.AppointmentEndTime
	}
	return 0
}

func (x *AppointmentCalendarView) GetIsWaitingList() bool {
	if x != nil {
		return x.IsWaitingList
	}
	return false
}

func (x *AppointmentCalendarView) GetMoveWaitingBy() int64 {
	if x != nil {
		return x.MoveWaitingBy
	}
	return 0
}

func (x *AppointmentCalendarView) GetConfirmedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ConfirmedTime
	}
	return nil
}

func (x *AppointmentCalendarView) GetCheckInTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CheckInTime
	}
	return nil
}

func (x *AppointmentCalendarView) GetCheckOutTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CheckOutTime
	}
	return nil
}

func (x *AppointmentCalendarView) GetCanceledTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CanceledTime
	}
	return nil
}

func (x *AppointmentCalendarView) GetStatus() AppointmentStatus {
	if x != nil {
		return x.Status
	}
	return AppointmentStatus_APPOINTMENT_STATUS_UNSPECIFIED
}

func (x *AppointmentCalendarView) GetIsBlock() bool {
	if x != nil {
		return x.IsBlock
	}
	return false
}

func (x *AppointmentCalendarView) GetBookOnlineStatus() int32 {
	if x != nil {
		return x.BookOnlineStatus
	}
	return 0
}

func (x *AppointmentCalendarView) GetCustomerAddressId() int64 {
	if x != nil {
		return x.CustomerAddressId
	}
	return 0
}

func (x *AppointmentCalendarView) GetRepeatId() int64 {
	if x != nil {
		return x.RepeatId
	}
	return 0
}

func (x *AppointmentCalendarView) GetIsPaid() AppointmentPaymentStatus {
	if x != nil {
		return x.IsPaid
	}
	return AppointmentPaymentStatus_APPOINTMENT_PAYMENT_STATUS_UNSPECIFIED
}

func (x *AppointmentCalendarView) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *AppointmentCalendarView) GetNoShow() bool {
	if x != nil {
		return x.NoShow
	}
	return false
}

func (x *AppointmentCalendarView) GetNoShowFee() float64 {
	if x != nil {
		return x.NoShowFee
	}
	return 0
}

func (x *AppointmentCalendarView) GetPushNotification() bool {
	if x != nil {
		return x.PushNotification
	}
	return false
}

func (x *AppointmentCalendarView) GetCancelByType() int32 {
	if x != nil {
		return x.CancelByType
	}
	return 0
}

func (x *AppointmentCalendarView) GetCancelBy() int64 {
	if x != nil {
		return x.CancelBy
	}
	return 0
}

func (x *AppointmentCalendarView) GetConfirmByType() int32 {
	if x != nil {
		return x.ConfirmByType
	}
	return 0
}

func (x *AppointmentCalendarView) GetConfirmBy() int64 {
	if x != nil {
		return x.ConfirmBy
	}
	return 0
}

func (x *AppointmentCalendarView) GetCreatedById() int64 {
	if x != nil {
		return x.CreatedById
	}
	return 0
}

func (x *AppointmentCalendarView) GetOutOfArea() bool {
	if x != nil {
		return x.OutOfArea
	}
	return false
}

func (x *AppointmentCalendarView) GetIsDeprecate() bool {
	if x != nil {
		return x.IsDeprecate
	}
	return false
}

func (x *AppointmentCalendarView) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *AppointmentCalendarView) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *AppointmentCalendarView) GetSource() AppointmentSource {
	if x != nil {
		return x.Source
	}
	return AppointmentSource_APPOINTMENT_SOURCE_UNSPECIFIED
}

func (x *AppointmentCalendarView) GetOldAppointmentDate() string {
	if x != nil {
		return x.OldAppointmentDate
	}
	return ""
}

func (x *AppointmentCalendarView) GetOldAppointmentStartTime() int32 {
	if x != nil {
		return x.OldAppointmentStartTime
	}
	return 0
}

func (x *AppointmentCalendarView) GetOldAppointmentEndTime() int32 {
	if x != nil {
		return x.OldAppointmentEndTime
	}
	return 0
}

func (x *AppointmentCalendarView) GetOldApptId() int64 {
	if x != nil {
		return x.OldApptId
	}
	return 0
}

func (x *AppointmentCalendarView) GetScheduleType() int32 {
	if x != nil {
		return x.ScheduleType
	}
	return 0
}

func (x *AppointmentCalendarView) GetSourcePlatform() string {
	if x != nil {
		return x.SourcePlatform
	}
	return ""
}

// Deprecated: Do not use.
func (x *AppointmentCalendarView) GetReadyTime() int64 {
	if x != nil {
		return x.ReadyTime
	}
	return 0
}

func (x *AppointmentCalendarView) GetPickupNotificationSendStatus() int32 {
	if x != nil {
		return x.PickupNotificationSendStatus
	}
	return 0
}

func (x *AppointmentCalendarView) GetPickupNotificationFailedReason() string {
	if x != nil {
		return x.PickupNotificationFailedReason
	}
	return ""
}

func (x *AppointmentCalendarView) GetStatusBeforeCheckin() AppointmentStatus {
	if x != nil {
		return x.StatusBeforeCheckin
	}
	return AppointmentStatus_APPOINTMENT_STATUS_UNSPECIFIED
}

func (x *AppointmentCalendarView) GetStatusBeforeReady() AppointmentStatus {
	if x != nil {
		return x.StatusBeforeReady
	}
	return AppointmentStatus_APPOINTMENT_STATUS_UNSPECIFIED
}

func (x *AppointmentCalendarView) GetStatusBeforeFinish() AppointmentStatus {
	if x != nil {
		return x.StatusBeforeFinish
	}
	return AppointmentStatus_APPOINTMENT_STATUS_UNSPECIFIED
}

func (x *AppointmentCalendarView) GetNoStartTime() bool {
	if x != nil {
		return x.NoStartTime
	}
	return false
}

func (x *AppointmentCalendarView) GetUpdatedById() int64 {
	if x != nil {
		return x.UpdatedById
	}
	return 0
}

func (x *AppointmentCalendarView) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *AppointmentCalendarView) GetIsAutoAccept() bool {
	if x != nil {
		return x.IsAutoAccept
	}
	return false
}

func (x *AppointmentCalendarView) GetWaitListStatus() WaitListStatus {
	if x != nil {
		return x.WaitListStatus
	}
	return WaitListStatus_APPTONLY
}

func (x *AppointmentCalendarView) GetAppointmentEndDate() string {
	if x != nil {
		return x.AppointmentEndDate
	}
	return ""
}

func (x *AppointmentCalendarView) GetServiceTypeInclude() int32 {
	if x != nil {
		return x.ServiceTypeInclude
	}
	return 0
}

func (x *AppointmentCalendarView) GetStartAtSameTime() bool {
	if x != nil {
		return x.StartAtSameTime
	}
	return false
}

func (x *AppointmentCalendarView) GetReadyTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ReadyTimestamp
	}
	return nil
}

func (x *AppointmentCalendarView) GetIsNewOrder() bool {
	if x != nil {
		return x.IsNewOrder
	}
	return false
}

func (x *AppointmentCalendarView) GetInvoiceId() string {
	if x != nil {
		return x.InvoiceId
	}
	return ""
}

// the appointment overview
type AppointmentOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// appointment date, in yyyy-MM-dd format
	AppointmentDate string `protobuf:"bytes,5,opt,name=appointment_date,json=appointmentDate,proto3" json:"appointment_date,omitempty"`
	// appointment start time, the number of minutes of the day
	AppointmentStartTime int32 `protobuf:"varint,6,opt,name=appointment_start_time,json=appointmentStartTime,proto3" json:"appointment_start_time,omitempty"`
	// appointment end time, the number of minutes of the day
	AppointmentEndTime int32 `protobuf:"varint,7,opt,name=appointment_end_time,json=appointmentEndTime,proto3" json:"appointment_end_time,omitempty"`
	// is waiting list
	IsWaitingList bool `protobuf:"varint,8,opt,name=is_waiting_list,json=isWaitingList,proto3" json:"is_waiting_list,omitempty"`
	// check in time
	CheckInTime *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=check_in_time,json=checkInTime,proto3" json:"check_in_time,omitempty"`
	// appointment status
	Status AppointmentStatus `protobuf:"varint,14,opt,name=status,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"status,omitempty"`
	// is paid
	IsPaid AppointmentPaymentStatus `protobuf:"varint,19,opt,name=is_paid,json=isPaid,proto3,enum=moego.models.appointment.v1.AppointmentPaymentStatus" json:"is_paid,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,20,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// no show
	NoShow bool `protobuf:"varint,21,opt,name=no_show,json=noShow,proto3" json:"no_show,omitempty"`
	// source
	Source AppointmentSource `protobuf:"varint,33,opt,name=source,proto3,enum=moego.models.appointment.v1.AppointmentSource" json:"source,omitempty"`
	// wait list status
	WaitListStatus WaitListStatus `protobuf:"varint,50,opt,name=wait_list_status,json=waitListStatus,proto3,enum=moego.models.appointment.v1.WaitListStatus" json:"wait_list_status,omitempty"`
	// end date
	AppointmentEndDate string `protobuf:"bytes,51,opt,name=appointment_end_date,json=appointmentEndDate,proto3" json:"appointment_end_date,omitempty"`
	// service type include
	ServiceTypeInclude int32 `protobuf:"varint,52,opt,name=service_type_include,json=serviceTypeInclude,proto3" json:"service_type_include,omitempty"`
}

func (x *AppointmentOverview) Reset() {
	*x = AppointmentOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_appointment_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentOverview) ProtoMessage() {}

func (x *AppointmentOverview) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_appointment_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentOverview.ProtoReflect.Descriptor instead.
func (*AppointmentOverview) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_models_proto_rawDescGZIP(), []int{2}
}

func (x *AppointmentOverview) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AppointmentOverview) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *AppointmentOverview) GetAppointmentDate() string {
	if x != nil {
		return x.AppointmentDate
	}
	return ""
}

func (x *AppointmentOverview) GetAppointmentStartTime() int32 {
	if x != nil {
		return x.AppointmentStartTime
	}
	return 0
}

func (x *AppointmentOverview) GetAppointmentEndTime() int32 {
	if x != nil {
		return x.AppointmentEndTime
	}
	return 0
}

func (x *AppointmentOverview) GetIsWaitingList() bool {
	if x != nil {
		return x.IsWaitingList
	}
	return false
}

func (x *AppointmentOverview) GetCheckInTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CheckInTime
	}
	return nil
}

func (x *AppointmentOverview) GetStatus() AppointmentStatus {
	if x != nil {
		return x.Status
	}
	return AppointmentStatus_APPOINTMENT_STATUS_UNSPECIFIED
}

func (x *AppointmentOverview) GetIsPaid() AppointmentPaymentStatus {
	if x != nil {
		return x.IsPaid
	}
	return AppointmentPaymentStatus_APPOINTMENT_PAYMENT_STATUS_UNSPECIFIED
}

func (x *AppointmentOverview) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *AppointmentOverview) GetNoShow() bool {
	if x != nil {
		return x.NoShow
	}
	return false
}

func (x *AppointmentOverview) GetSource() AppointmentSource {
	if x != nil {
		return x.Source
	}
	return AppointmentSource_APPOINTMENT_SOURCE_UNSPECIFIED
}

func (x *AppointmentOverview) GetWaitListStatus() WaitListStatus {
	if x != nil {
		return x.WaitListStatus
	}
	return WaitListStatus_APPTONLY
}

func (x *AppointmentOverview) GetAppointmentEndDate() string {
	if x != nil {
		return x.AppointmentEndDate
	}
	return ""
}

func (x *AppointmentOverview) GetServiceTypeInclude() int32 {
	if x != nil {
		return x.ServiceTypeInclude
	}
	return 0
}

var File_moego_models_appointment_v1_appointment_models_proto protoreflect.FileDescriptor

var file_moego_models_appointment_v1_appointment_models_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xee, 0x14, 0x0a,
	0x10, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x29,
	0x0a, 0x10, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x30, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x57, 0x61,
	0x69, 0x74, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x6f, 0x76,
	0x65, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x6d, 0x6f, 0x76, 0x65, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x42,
	0x79, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0e, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x6f, 0x75,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x4f,
	0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x63, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x69, 0x73, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x6f,
	0x6f, 0x6b, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x70, 0x61, 0x69, 0x64,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x69,
	0x73, 0x50, 0x61, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6e, 0x6f, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x1e, 0x0a,
	0x0b, 0x6e, 0x6f, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x09, 0x6e, 0x6f, 0x53, 0x68, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x12, 0x2b, 0x0a,
	0x11, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x70, 0x75, 0x73, 0x68, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x62, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x62, 0x79, 0x18, 0x19, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x42, 0x79, 0x12, 0x26, 0x0a,
	0x0f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x5f, 0x62, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x42,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x5f, 0x62, 0x79, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x42, 0x79, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x6f, 0x75, 0x74, 0x5f,
	0x6f, 0x66, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6f,
	0x75, 0x74, 0x4f, 0x66, 0x41, 0x72, 0x65, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x64,
	0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x69, 0x73, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x21, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x30, 0x0a,
	0x14, 0x6f, 0x6c, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6f, 0x6c, 0x64,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x3b, 0x0a, 0x1a, 0x6f, 0x6c, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x23, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x17, 0x6f, 0x6c, 0x64, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x18,
	0x6f, 0x6c, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x24, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15,
	0x6f, 0x6c, 0x64, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x6f, 0x6c, 0x64, 0x5f, 0x61, 0x70, 0x70,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x25, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6f, 0x6c, 0x64, 0x41,
	0x70, 0x70, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x26, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x27, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x12, 0x21, 0x0a, 0x0a, 0x72, 0x65, 0x61, 0x64, 0x79, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x28, 0x20, 0x01, 0x28, 0x03, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x72, 0x65, 0x61,
	0x64, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x45, 0x0a, 0x1f, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70,
	0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65,
	0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x29, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x1c, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a,
	0x21, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1e, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x62, 0x0a, 0x15, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x69,
	0x6e, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x13, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x65, 0x66, 0x6f, 0x72, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x12, 0x5e, 0x0a, 0x13,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x72, 0x65,
	0x61, 0x64, 0x79, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x64, 0x79, 0x12, 0x60, 0x0a, 0x14,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x66, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x12, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x12, 0x22,
	0x0a, 0x0d, 0x6e, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x2e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6e, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x42, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x30, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f,
	0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x18, 0x31, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69,
	0x73, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x12, 0x55, 0x0a, 0x10, 0x77,
	0x61, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x32, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0e, 0x77, 0x61, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x33, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x64,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x34, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49,
	0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x12, 0x43, 0x0a, 0x0f, 0x72, 0x65, 0x61, 0x64, 0x79, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x35, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x72, 0x65, 0x61,
	0x64, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x20, 0x0a, 0x0c, 0x69,
	0x73, 0x5f, 0x6e, 0x65, 0x77, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x36, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x69, 0x73, 0x4e, 0x65, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x22, 0xa6, 0x15,
	0x0a, 0x17, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26,
	0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x57, 0x61, 0x69, 0x74, 0x69,
	0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x6f, 0x76, 0x65, 0x5f, 0x77,
	0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x6d, 0x6f, 0x76, 0x65, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x12, 0x41,
	0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x40, 0x0a, 0x0e, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x73, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x69, 0x73, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x5f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x49, 0x64, 0x12, 0x4e, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x69, 0x73, 0x50, 0x61,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x6e, 0x6f, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x1e, 0x0a, 0x0b, 0x6e, 0x6f,
	0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x09, 0x6e, 0x6f, 0x53, 0x68, 0x6f, 0x77, 0x46, 0x65, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x75,
	0x73, 0x68, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x70, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x5f, 0x62, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x62, 0x79, 0x18, 0x19, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x42, 0x79, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x72, 0x6d, 0x5f, 0x62, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1a, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x42, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x5f, 0x62, 0x79,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x42,
	0x79, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x42, 0x79, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x6f, 0x75, 0x74, 0x5f, 0x6f, 0x66, 0x5f,
	0x61, 0x72, 0x65, 0x61, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6f, 0x75, 0x74, 0x4f,
	0x66, 0x41, 0x72, 0x65, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x70, 0x72,
	0x65, 0x63, 0x61, 0x74, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x44,
	0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x21, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x6f, 0x6c,
	0x64, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6f, 0x6c, 0x64, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x1a,
	0x6f, 0x6c, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x23, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x17, 0x6f, 0x6c, 0x64, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x18, 0x6f, 0x6c, 0x64,
	0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x24, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x6f, 0x6c, 0x64,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x6f, 0x6c, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x25, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6f, 0x6c, 0x64, 0x41, 0x70, 0x70, 0x74,
	0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x26, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x12, 0x21, 0x0a, 0x0a, 0x72, 0x65, 0x61, 0x64, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x28,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x72, 0x65, 0x61, 0x64, 0x79, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x45, 0x0a, 0x1f, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x29, 0x20, 0x01, 0x28, 0x05, 0x52, 0x1c, 0x70, 0x69,
	0x63, 0x6b, 0x75, 0x70, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x21, 0x70, 0x69,
	0x63, 0x6b, 0x75, 0x70, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1e, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x62, 0x0a, 0x15, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x18, 0x2b,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x13, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x65, 0x66, 0x6f,
	0x72, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x12, 0x5e, 0x0a, 0x13, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x79,
	0x18, 0x2c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x65,
	0x66, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x64, 0x79, 0x12, 0x60, 0x0a, 0x14, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x12, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x65, 0x66, 0x6f, 0x72, 0x65, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x12, 0x22, 0x0a, 0x0d, 0x6e,
	0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x2e, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x6e, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x22, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x2f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x30, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x18, 0x31, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x75,
	0x74, 0x6f, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x12, 0x55, 0x0a, 0x10, 0x77, 0x61, 0x69, 0x74,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x32, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x57, 0x61, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x0e, 0x77, 0x61, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x30, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x33, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x34, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x12, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x61, 0x74, 0x5f,
	0x73, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x35, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x53, 0x61, 0x6d, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x43, 0x0a, 0x0f, 0x72, 0x65, 0x61, 0x64, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x36, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x72, 0x65, 0x61, 0x64, 0x79, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x20, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x77, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x3c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x4e,
	0x65, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x76,
	0x6f, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x94, 0x06, 0x0a, 0x13, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x29, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x30, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x57,
	0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3e, 0x0a, 0x0d, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x4e, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x69, 0x73, 0x50, 0x61,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x6e, 0x6f, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x55, 0x0a, 0x10, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x32, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x69, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x77, 0x61, 0x69, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x33, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x18, 0x34, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x42, 0x87, 0x01,
	0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_appointment_v1_appointment_models_proto_rawDescOnce sync.Once
	file_moego_models_appointment_v1_appointment_models_proto_rawDescData = file_moego_models_appointment_v1_appointment_models_proto_rawDesc
)

func file_moego_models_appointment_v1_appointment_models_proto_rawDescGZIP() []byte {
	file_moego_models_appointment_v1_appointment_models_proto_rawDescOnce.Do(func() {
		file_moego_models_appointment_v1_appointment_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_appointment_v1_appointment_models_proto_rawDescData)
	})
	return file_moego_models_appointment_v1_appointment_models_proto_rawDescData
}

var file_moego_models_appointment_v1_appointment_models_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_appointment_v1_appointment_models_proto_goTypes = []interface{}{
	(*AppointmentModel)(nil),        // 0: moego.models.appointment.v1.AppointmentModel
	(*AppointmentCalendarView)(nil), // 1: moego.models.appointment.v1.AppointmentCalendarView
	(*AppointmentOverview)(nil),     // 2: moego.models.appointment.v1.AppointmentOverview
	(*timestamppb.Timestamp)(nil),   // 3: google.protobuf.Timestamp
	(AppointmentStatus)(0),          // 4: moego.models.appointment.v1.AppointmentStatus
	(AppointmentPaymentStatus)(0),   // 5: moego.models.appointment.v1.AppointmentPaymentStatus
	(AppointmentSource)(0),          // 6: moego.models.appointment.v1.AppointmentSource
	(WaitListStatus)(0),             // 7: moego.models.appointment.v1.WaitListStatus
}
var file_moego_models_appointment_v1_appointment_models_proto_depIdxs = []int32{
	3,  // 0: moego.models.appointment.v1.AppointmentModel.confirmed_time:type_name -> google.protobuf.Timestamp
	3,  // 1: moego.models.appointment.v1.AppointmentModel.check_in_time:type_name -> google.protobuf.Timestamp
	3,  // 2: moego.models.appointment.v1.AppointmentModel.check_out_time:type_name -> google.protobuf.Timestamp
	3,  // 3: moego.models.appointment.v1.AppointmentModel.canceled_time:type_name -> google.protobuf.Timestamp
	4,  // 4: moego.models.appointment.v1.AppointmentModel.status:type_name -> moego.models.appointment.v1.AppointmentStatus
	5,  // 5: moego.models.appointment.v1.AppointmentModel.is_paid:type_name -> moego.models.appointment.v1.AppointmentPaymentStatus
	3,  // 6: moego.models.appointment.v1.AppointmentModel.create_time:type_name -> google.protobuf.Timestamp
	3,  // 7: moego.models.appointment.v1.AppointmentModel.update_time:type_name -> google.protobuf.Timestamp
	6,  // 8: moego.models.appointment.v1.AppointmentModel.source:type_name -> moego.models.appointment.v1.AppointmentSource
	4,  // 9: moego.models.appointment.v1.AppointmentModel.status_before_checkin:type_name -> moego.models.appointment.v1.AppointmentStatus
	4,  // 10: moego.models.appointment.v1.AppointmentModel.status_before_ready:type_name -> moego.models.appointment.v1.AppointmentStatus
	4,  // 11: moego.models.appointment.v1.AppointmentModel.status_before_finish:type_name -> moego.models.appointment.v1.AppointmentStatus
	7,  // 12: moego.models.appointment.v1.AppointmentModel.wait_list_status:type_name -> moego.models.appointment.v1.WaitListStatus
	3,  // 13: moego.models.appointment.v1.AppointmentModel.ready_timestamp:type_name -> google.protobuf.Timestamp
	3,  // 14: moego.models.appointment.v1.AppointmentCalendarView.confirmed_time:type_name -> google.protobuf.Timestamp
	3,  // 15: moego.models.appointment.v1.AppointmentCalendarView.check_in_time:type_name -> google.protobuf.Timestamp
	3,  // 16: moego.models.appointment.v1.AppointmentCalendarView.check_out_time:type_name -> google.protobuf.Timestamp
	3,  // 17: moego.models.appointment.v1.AppointmentCalendarView.canceled_time:type_name -> google.protobuf.Timestamp
	4,  // 18: moego.models.appointment.v1.AppointmentCalendarView.status:type_name -> moego.models.appointment.v1.AppointmentStatus
	5,  // 19: moego.models.appointment.v1.AppointmentCalendarView.is_paid:type_name -> moego.models.appointment.v1.AppointmentPaymentStatus
	3,  // 20: moego.models.appointment.v1.AppointmentCalendarView.create_time:type_name -> google.protobuf.Timestamp
	3,  // 21: moego.models.appointment.v1.AppointmentCalendarView.update_time:type_name -> google.protobuf.Timestamp
	6,  // 22: moego.models.appointment.v1.AppointmentCalendarView.source:type_name -> moego.models.appointment.v1.AppointmentSource
	4,  // 23: moego.models.appointment.v1.AppointmentCalendarView.status_before_checkin:type_name -> moego.models.appointment.v1.AppointmentStatus
	4,  // 24: moego.models.appointment.v1.AppointmentCalendarView.status_before_ready:type_name -> moego.models.appointment.v1.AppointmentStatus
	4,  // 25: moego.models.appointment.v1.AppointmentCalendarView.status_before_finish:type_name -> moego.models.appointment.v1.AppointmentStatus
	7,  // 26: moego.models.appointment.v1.AppointmentCalendarView.wait_list_status:type_name -> moego.models.appointment.v1.WaitListStatus
	3,  // 27: moego.models.appointment.v1.AppointmentCalendarView.ready_timestamp:type_name -> google.protobuf.Timestamp
	3,  // 28: moego.models.appointment.v1.AppointmentOverview.check_in_time:type_name -> google.protobuf.Timestamp
	4,  // 29: moego.models.appointment.v1.AppointmentOverview.status:type_name -> moego.models.appointment.v1.AppointmentStatus
	5,  // 30: moego.models.appointment.v1.AppointmentOverview.is_paid:type_name -> moego.models.appointment.v1.AppointmentPaymentStatus
	6,  // 31: moego.models.appointment.v1.AppointmentOverview.source:type_name -> moego.models.appointment.v1.AppointmentSource
	7,  // 32: moego.models.appointment.v1.AppointmentOverview.wait_list_status:type_name -> moego.models.appointment.v1.WaitListStatus
	33, // [33:33] is the sub-list for method output_type
	33, // [33:33] is the sub-list for method input_type
	33, // [33:33] is the sub-list for extension type_name
	33, // [33:33] is the sub-list for extension extendee
	0,  // [0:33] is the sub-list for field type_name
}

func init() { file_moego_models_appointment_v1_appointment_models_proto_init() }
func file_moego_models_appointment_v1_appointment_models_proto_init() {
	if File_moego_models_appointment_v1_appointment_models_proto != nil {
		return
	}
	file_moego_models_appointment_v1_appointment_enums_proto_init()
	file_moego_models_appointment_v1_wait_list_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_appointment_v1_appointment_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_appointment_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentCalendarView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_appointment_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_appointment_v1_appointment_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_appointment_v1_appointment_models_proto_goTypes,
		DependencyIndexes: file_moego_models_appointment_v1_appointment_models_proto_depIdxs,
		MessageInfos:      file_moego_models_appointment_v1_appointment_models_proto_msgTypes,
	}.Build()
	File_moego_models_appointment_v1_appointment_models_proto = out.File
	file_moego_models_appointment_v1_appointment_models_proto_rawDesc = nil
	file_moego_models_appointment_v1_appointment_models_proto_goTypes = nil
	file_moego_models_appointment_v1_appointment_models_proto_depIdxs = nil
}
