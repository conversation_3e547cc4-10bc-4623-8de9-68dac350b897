// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/appointment/v1/check_in_out_alert_models.proto

package appointmentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// client icons for alert
type ClientAlertIcons int32

const (
	// unspecified value
	ClientAlertIcons_CLIENT_ALERT_ICONS_UNSPECIFIED ClientAlertIcons = 0
	// membership
	ClientAlertIcons_ICON_MEMBERSHIP ClientAlertIcons = 1
	// card on file
	ClientAlertIcons_ICON_CARD_ON_FILE ClientAlertIcons = 2
	// package
	ClientAlertIcons_ICON_PACKAGE ClientAlertIcons = 3
	// alert note
	ClientAlertIcons_ICON_ALERT_NOTE ClientAlertIcons = 4
	// unsigned agreement
	ClientAlertIcons_ICON_UNSIGNED_AGREEMENT ClientAlertIcons = 5
	// unpaid balance
	ClientAlertIcons_ICON_UNPAID_BALANCE ClientAlertIcons = 6
	// pickup person
	ClientAlertIcons_ICON_PICKUP_PERSON ClientAlertIcons = 7
)

// Enum value maps for ClientAlertIcons.
var (
	ClientAlertIcons_name = map[int32]string{
		0: "CLIENT_ALERT_ICONS_UNSPECIFIED",
		1: "ICON_MEMBERSHIP",
		2: "ICON_CARD_ON_FILE",
		3: "ICON_PACKAGE",
		4: "ICON_ALERT_NOTE",
		5: "ICON_UNSIGNED_AGREEMENT",
		6: "ICON_UNPAID_BALANCE",
		7: "ICON_PICKUP_PERSON",
	}
	ClientAlertIcons_value = map[string]int32{
		"CLIENT_ALERT_ICONS_UNSPECIFIED": 0,
		"ICON_MEMBERSHIP":                1,
		"ICON_CARD_ON_FILE":              2,
		"ICON_PACKAGE":                   3,
		"ICON_ALERT_NOTE":                4,
		"ICON_UNSIGNED_AGREEMENT":        5,
		"ICON_UNPAID_BALANCE":            6,
		"ICON_PICKUP_PERSON":             7,
	}
)

func (x ClientAlertIcons) Enum() *ClientAlertIcons {
	p := new(ClientAlertIcons)
	*p = x
	return p
}

func (x ClientAlertIcons) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientAlertIcons) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_check_in_out_alert_models_proto_enumTypes[0].Descriptor()
}

func (ClientAlertIcons) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_check_in_out_alert_models_proto_enumTypes[0]
}

func (x ClientAlertIcons) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClientAlertIcons.Descriptor instead.
func (ClientAlertIcons) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDescGZIP(), []int{0}
}

// pet icons for alert
type PetAlertIcons int32

const (
	// unspecified value
	PetAlertIcons_PET_ALERT_ICONS_UNSPECIFIED PetAlertIcons = 0
	// vaccine
	PetAlertIcons_ICON_VACCINE PetAlertIcons = 1
	// incident
	PetAlertIcons_ICON_INCIDENT PetAlertIcons = 2
)

// Enum value maps for PetAlertIcons.
var (
	PetAlertIcons_name = map[int32]string{
		0: "PET_ALERT_ICONS_UNSPECIFIED",
		1: "ICON_VACCINE",
		2: "ICON_INCIDENT",
	}
	PetAlertIcons_value = map[string]int32{
		"PET_ALERT_ICONS_UNSPECIFIED": 0,
		"ICON_VACCINE":                1,
		"ICON_INCIDENT":               2,
	}
)

func (x PetAlertIcons) Enum() *PetAlertIcons {
	p := new(PetAlertIcons)
	*p = x
	return p
}

func (x PetAlertIcons) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PetAlertIcons) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_check_in_out_alert_models_proto_enumTypes[1].Descriptor()
}

func (PetAlertIcons) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_check_in_out_alert_models_proto_enumTypes[1]
}

func (x PetAlertIcons) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PetAlertIcons.Descriptor instead.
func (PetAlertIcons) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDescGZIP(), []int{1}
}

// CheckInOutAlertSettings
type CheckInOutAlertSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// check in alert settings
	CheckInSettings *CheckInAlertSettings `protobuf:"bytes,2,opt,name=check_in_settings,json=checkInSettings,proto3" json:"check_in_settings,omitempty"`
	// check out alert settings
	CheckOutSettings *CheckOutAlertSettings `protobuf:"bytes,3,opt,name=check_out_settings,json=checkOutSettings,proto3" json:"check_out_settings,omitempty"`
	// the create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// the update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *CheckInOutAlertSettings) Reset() {
	*x = CheckInOutAlertSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckInOutAlertSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckInOutAlertSettings) ProtoMessage() {}

func (x *CheckInOutAlertSettings) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckInOutAlertSettings.ProtoReflect.Descriptor instead.
func (*CheckInOutAlertSettings) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDescGZIP(), []int{0}
}

func (x *CheckInOutAlertSettings) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CheckInOutAlertSettings) GetCheckInSettings() *CheckInAlertSettings {
	if x != nil {
		return x.CheckInSettings
	}
	return nil
}

func (x *CheckInOutAlertSettings) GetCheckOutSettings() *CheckOutAlertSettings {
	if x != nil {
		return x.CheckOutSettings
	}
	return nil
}

func (x *CheckInOutAlertSettings) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CheckInOutAlertSettings) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// CheckInAlertSettings
type CheckInAlertSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enabled
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// also trigger alert for daycare quick check in
	TriggerForQuick bool `protobuf:"varint,2,opt,name=trigger_for_quick,json=triggerForQuick,proto3" json:"trigger_for_quick,omitempty"`
	// alert settings
	Settings *AlertSettings `protobuf:"bytes,3,opt,name=settings,proto3,oneof" json:"settings,omitempty"`
}

func (x *CheckInAlertSettings) Reset() {
	*x = CheckInAlertSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckInAlertSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckInAlertSettings) ProtoMessage() {}

func (x *CheckInAlertSettings) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckInAlertSettings.ProtoReflect.Descriptor instead.
func (*CheckInAlertSettings) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDescGZIP(), []int{1}
}

func (x *CheckInAlertSettings) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *CheckInAlertSettings) GetTriggerForQuick() bool {
	if x != nil {
		return x.TriggerForQuick
	}
	return false
}

func (x *CheckInAlertSettings) GetSettings() *AlertSettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

// CheckOutAlertSettings
type CheckOutAlertSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enabled
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// alert settings
	Settings *AlertSettings `protobuf:"bytes,2,opt,name=settings,proto3,oneof" json:"settings,omitempty"`
}

func (x *CheckOutAlertSettings) Reset() {
	*x = CheckOutAlertSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckOutAlertSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOutAlertSettings) ProtoMessage() {}

func (x *CheckOutAlertSettings) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOutAlertSettings.ProtoReflect.Descriptor instead.
func (*CheckOutAlertSettings) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDescGZIP(), []int{2}
}

func (x *CheckOutAlertSettings) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *CheckOutAlertSettings) GetSettings() *AlertSettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

// AlertSettings
type AlertSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client alert settings
	ClientAlert *ClientAlertSettings `protobuf:"bytes,1,opt,name=client_alert,json=clientAlert,proto3,oneof" json:"client_alert,omitempty"`
	// pet alert settings
	PetAlert *PetAlertSettings `protobuf:"bytes,2,opt,name=pet_alert,json=petAlert,proto3,oneof" json:"pet_alert,omitempty"`
}

func (x *AlertSettings) Reset() {
	*x = AlertSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlertSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlertSettings) ProtoMessage() {}

func (x *AlertSettings) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlertSettings.ProtoReflect.Descriptor instead.
func (*AlertSettings) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDescGZIP(), []int{3}
}

func (x *AlertSettings) GetClientAlert() *ClientAlertSettings {
	if x != nil {
		return x.ClientAlert
	}
	return nil
}

func (x *AlertSettings) GetPetAlert() *PetAlertSettings {
	if x != nil {
		return x.PetAlert
	}
	return nil
}

// ClientAlertSettings
type ClientAlertSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of client icons
	ClientIcons []ClientAlertIcons `protobuf:"varint,1,rep,packed,name=client_icons,json=clientIcons,proto3,enum=moego.models.appointment.v1.ClientAlertIcons" json:"client_icons,omitempty"`
	// list of client tags
	ClientTags []int64 `protobuf:"varint,2,rep,packed,name=client_tags,json=clientTags,proto3" json:"client_tags,omitempty"`
}

func (x *ClientAlertSettings) Reset() {
	*x = ClientAlertSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientAlertSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientAlertSettings) ProtoMessage() {}

func (x *ClientAlertSettings) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientAlertSettings.ProtoReflect.Descriptor instead.
func (*ClientAlertSettings) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDescGZIP(), []int{4}
}

func (x *ClientAlertSettings) GetClientIcons() []ClientAlertIcons {
	if x != nil {
		return x.ClientIcons
	}
	return nil
}

func (x *ClientAlertSettings) GetClientTags() []int64 {
	if x != nil {
		return x.ClientTags
	}
	return nil
}

// PetAlertSettings
type PetAlertSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of pet icons
	PetIcons []PetAlertIcons `protobuf:"varint,1,rep,packed,name=pet_icons,json=petIcons,proto3,enum=moego.models.appointment.v1.PetAlertIcons" json:"pet_icons,omitempty"`
	// list of pet codes
	PetCodes []int64 `protobuf:"varint,2,rep,packed,name=pet_codes,json=petCodes,proto3" json:"pet_codes,omitempty"`
}

func (x *PetAlertSettings) Reset() {
	*x = PetAlertSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetAlertSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetAlertSettings) ProtoMessage() {}

func (x *PetAlertSettings) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetAlertSettings.ProtoReflect.Descriptor instead.
func (*PetAlertSettings) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDescGZIP(), []int{5}
}

func (x *PetAlertSettings) GetPetIcons() []PetAlertIcons {
	if x != nil {
		return x.PetIcons
	}
	return nil
}

func (x *PetAlertSettings) GetPetCodes() []int64 {
	if x != nil {
		return x.PetCodes
	}
	return nil
}

var File_moego_models_appointment_v1_check_in_out_alert_models_proto protoreflect.FileDescriptor

var file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xef, 0x02, 0x0a, 0x17,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x5d, 0x0a, 0x11, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x69, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x52, 0x0f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x60, 0x0a, 0x12, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x6f,
	0x75, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x10, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xb6, 0x01,
	0x0a, 0x14, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x2a, 0x0a, 0x11, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x66, 0x6f, 0x72, 0x5f,
	0x71, 0x75, 0x69, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x74, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x12, 0x4b, 0x0a, 0x08,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x65,
	0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x48, 0x00, 0x52, 0x08, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x8b, 0x01, 0x0a, 0x15, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x4f, 0x75, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x4b, 0x0a, 0x08, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x48, 0x00, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x22, 0xd9, 0x01, 0x0a, 0x0d, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x58, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x48, 0x00,
	0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x4f, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x48, 0x01, 0x52, 0x08, 0x70, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x88, 0x01,
	0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6c, 0x65,
	0x72, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74,
	0x22, 0x88, 0x01, 0x0a, 0x13, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x50, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x73, 0x52, 0x0b, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x73, 0x22, 0x78, 0x0a, 0x10, 0x50,
	0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12,
	0x47, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x73, 0x52, 0x08,
	0x70, 0x65, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x70, 0x65, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x73, 0x2a, 0xd7, 0x01, 0x0a, 0x10, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x73, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x4c,
	0x49, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x49, 0x43, 0x4f, 0x4e, 0x53,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13,
	0x0a, 0x0f, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x53, 0x48, 0x49,
	0x50, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x43,
	0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f,
	0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x45, 0x10,
	0x04, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x49, 0x47, 0x4e,
	0x45, 0x44, 0x5f, 0x41, 0x47, 0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x05, 0x12, 0x17,
	0x0a, 0x13, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x50, 0x41, 0x49, 0x44, 0x5f, 0x42, 0x41,
	0x4c, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x06, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x43, 0x4f, 0x4e, 0x5f,
	0x50, 0x49, 0x43, 0x4b, 0x55, 0x50, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x10, 0x07, 0x2a,
	0x55, 0x0a, 0x0d, 0x50, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x73,
	0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x45, 0x54, 0x5f, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x49, 0x43,
	0x4f, 0x4e, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x56, 0x41, 0x43, 0x43, 0x49, 0x4e,
	0x45, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x43, 0x49,
	0x44, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x42, 0x87, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDescOnce sync.Once
	file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDescData = file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDesc
)

func file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDescGZIP() []byte {
	file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDescOnce.Do(func() {
		file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDescData)
	})
	return file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDescData
}

var file_moego_models_appointment_v1_check_in_out_alert_models_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_moego_models_appointment_v1_check_in_out_alert_models_proto_goTypes = []interface{}{
	(ClientAlertIcons)(0),           // 0: moego.models.appointment.v1.ClientAlertIcons
	(PetAlertIcons)(0),              // 1: moego.models.appointment.v1.PetAlertIcons
	(*CheckInOutAlertSettings)(nil), // 2: moego.models.appointment.v1.CheckInOutAlertSettings
	(*CheckInAlertSettings)(nil),    // 3: moego.models.appointment.v1.CheckInAlertSettings
	(*CheckOutAlertSettings)(nil),   // 4: moego.models.appointment.v1.CheckOutAlertSettings
	(*AlertSettings)(nil),           // 5: moego.models.appointment.v1.AlertSettings
	(*ClientAlertSettings)(nil),     // 6: moego.models.appointment.v1.ClientAlertSettings
	(*PetAlertSettings)(nil),        // 7: moego.models.appointment.v1.PetAlertSettings
	(*timestamppb.Timestamp)(nil),   // 8: google.protobuf.Timestamp
}
var file_moego_models_appointment_v1_check_in_out_alert_models_proto_depIdxs = []int32{
	3,  // 0: moego.models.appointment.v1.CheckInOutAlertSettings.check_in_settings:type_name -> moego.models.appointment.v1.CheckInAlertSettings
	4,  // 1: moego.models.appointment.v1.CheckInOutAlertSettings.check_out_settings:type_name -> moego.models.appointment.v1.CheckOutAlertSettings
	8,  // 2: moego.models.appointment.v1.CheckInOutAlertSettings.created_at:type_name -> google.protobuf.Timestamp
	8,  // 3: moego.models.appointment.v1.CheckInOutAlertSettings.updated_at:type_name -> google.protobuf.Timestamp
	5,  // 4: moego.models.appointment.v1.CheckInAlertSettings.settings:type_name -> moego.models.appointment.v1.AlertSettings
	5,  // 5: moego.models.appointment.v1.CheckOutAlertSettings.settings:type_name -> moego.models.appointment.v1.AlertSettings
	6,  // 6: moego.models.appointment.v1.AlertSettings.client_alert:type_name -> moego.models.appointment.v1.ClientAlertSettings
	7,  // 7: moego.models.appointment.v1.AlertSettings.pet_alert:type_name -> moego.models.appointment.v1.PetAlertSettings
	0,  // 8: moego.models.appointment.v1.ClientAlertSettings.client_icons:type_name -> moego.models.appointment.v1.ClientAlertIcons
	1,  // 9: moego.models.appointment.v1.PetAlertSettings.pet_icons:type_name -> moego.models.appointment.v1.PetAlertIcons
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_moego_models_appointment_v1_check_in_out_alert_models_proto_init() }
func file_moego_models_appointment_v1_check_in_out_alert_models_proto_init() {
	if File_moego_models_appointment_v1_check_in_out_alert_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckInOutAlertSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckInAlertSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckOutAlertSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlertSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientAlertSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetAlertSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes[3].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_appointment_v1_check_in_out_alert_models_proto_goTypes,
		DependencyIndexes: file_moego_models_appointment_v1_check_in_out_alert_models_proto_depIdxs,
		EnumInfos:         file_moego_models_appointment_v1_check_in_out_alert_models_proto_enumTypes,
		MessageInfos:      file_moego_models_appointment_v1_check_in_out_alert_models_proto_msgTypes,
	}.Build()
	File_moego_models_appointment_v1_check_in_out_alert_models_proto = out.File
	file_moego_models_appointment_v1_check_in_out_alert_models_proto_rawDesc = nil
	file_moego_models_appointment_v1_check_in_out_alert_models_proto_goTypes = nil
	file_moego_models_appointment_v1_check_in_out_alert_models_proto_depIdxs = nil
}
