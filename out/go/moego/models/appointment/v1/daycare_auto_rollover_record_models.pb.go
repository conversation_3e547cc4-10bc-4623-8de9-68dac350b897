// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/appointment/v1/daycare_auto_rollover_record_models.proto

package appointmentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// DaycareAutoRolloverRecordModel status
type DaycareAutoRolloverRecordModel_Status int32

const (
	// Unspecified
	DaycareAutoRolloverRecordModel_STATUS_UNSPECIFIED DaycareAutoRolloverRecordModel_Status = 0
	// pending
	DaycareAutoRolloverRecordModel_PENDING DaycareAutoRolloverRecordModel_Status = 1
	// processing
	DaycareAutoRolloverRecordModel_PROCESSING DaycareAutoRolloverRecordModel_Status = 2
	// success
	DaycareAutoRolloverRecordModel_SUCCESS DaycareAutoRolloverRecordModel_Status = 3
	// failed
	DaycareAutoRolloverRecordModel_FAILED DaycareAutoRolloverRecordModel_Status = 4
)

// Enum value maps for DaycareAutoRolloverRecordModel_Status.
var (
	DaycareAutoRolloverRecordModel_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "PENDING",
		2: "PROCESSING",
		3: "SUCCESS",
		4: "FAILED",
	}
	DaycareAutoRolloverRecordModel_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"PENDING":            1,
		"PROCESSING":         2,
		"SUCCESS":            3,
		"FAILED":             4,
	}
)

func (x DaycareAutoRolloverRecordModel_Status) Enum() *DaycareAutoRolloverRecordModel_Status {
	p := new(DaycareAutoRolloverRecordModel_Status)
	*p = x
	return p
}

func (x DaycareAutoRolloverRecordModel_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DaycareAutoRolloverRecordModel_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_enumTypes[0].Descriptor()
}

func (DaycareAutoRolloverRecordModel_Status) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_enumTypes[0]
}

func (x DaycareAutoRolloverRecordModel_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DaycareAutoRolloverRecordModel_Status.Descriptor instead.
func (DaycareAutoRolloverRecordModel_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_rawDescGZIP(), []int{0, 0}
}

// daycare service 的 auto rollover 记录
type DaycareAutoRolloverRecordModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 对应 moe_grooming.moe_grooming_pet_detail.id
	DaycareServiceDetailId int64 `protobuf:"varint,2,opt,name=daycare_service_detail_id,json=daycareServiceDetailId,proto3" json:"daycare_service_detail_id,omitempty"`
	// daycare service id
	ServiceId int64 `protobuf:"varint,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// 1: pending, 2: processing, 3: success, 4: failed
	Status DaycareAutoRolloverRecordModel_Status `protobuf:"varint,4,opt,name=status,proto3,enum=moego.models.appointment.v1.DaycareAutoRolloverRecordModel_Status" json:"status,omitempty"`
	// rollover 触发时间
	RolloverTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=rollover_time,json=rolloverTime,proto3" json:"rollover_time,omitempty"`
}

func (x *DaycareAutoRolloverRecordModel) Reset() {
	*x = DaycareAutoRolloverRecordModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DaycareAutoRolloverRecordModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DaycareAutoRolloverRecordModel) ProtoMessage() {}

func (x *DaycareAutoRolloverRecordModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DaycareAutoRolloverRecordModel.ProtoReflect.Descriptor instead.
func (*DaycareAutoRolloverRecordModel) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_rawDescGZIP(), []int{0}
}

func (x *DaycareAutoRolloverRecordModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DaycareAutoRolloverRecordModel) GetDaycareServiceDetailId() int64 {
	if x != nil {
		return x.DaycareServiceDetailId
	}
	return 0
}

func (x *DaycareAutoRolloverRecordModel) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *DaycareAutoRolloverRecordModel) GetStatus() DaycareAutoRolloverRecordModel_Status {
	if x != nil {
		return x.Status
	}
	return DaycareAutoRolloverRecordModel_STATUS_UNSPECIFIED
}

func (x *DaycareAutoRolloverRecordModel) GetRolloverTime() *timestamppb.Timestamp {
	if x != nil {
		return x.RolloverTime
	}
	return nil
}

var File_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto protoreflect.FileDescriptor

var file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_rawDesc = []byte{
	0x0a, 0x45, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61,
	0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x6f, 0x6c, 0x6c, 0x6f,
	0x76, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xff, 0x02, 0x0a, 0x1e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72,
	0x65, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x6f, 0x6c, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x39, 0x0a, 0x19, 0x64, 0x61, 0x79, 0x63,
	0x61, 0x72, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x16, 0x64, 0x61, 0x79,
	0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x5a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x6f, 0x6c, 0x6c,
	0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f,
	0x0a, 0x0d, 0x72, 0x6f, 0x6c, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0c, 0x72, 0x6f, 0x6c, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x56, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0e,
	0x0a, 0x0a, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0b,
	0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x42, 0x87, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_rawDescOnce sync.Once
	file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_rawDescData = file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_rawDesc
)

func file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_rawDescGZIP() []byte {
	file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_rawDescOnce.Do(func() {
		file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_rawDescData)
	})
	return file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_rawDescData
}

var file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_goTypes = []interface{}{
	(DaycareAutoRolloverRecordModel_Status)(0), // 0: moego.models.appointment.v1.DaycareAutoRolloverRecordModel.Status
	(*DaycareAutoRolloverRecordModel)(nil),     // 1: moego.models.appointment.v1.DaycareAutoRolloverRecordModel
	(*timestamppb.Timestamp)(nil),              // 2: google.protobuf.Timestamp
}
var file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_depIdxs = []int32{
	0, // 0: moego.models.appointment.v1.DaycareAutoRolloverRecordModel.status:type_name -> moego.models.appointment.v1.DaycareAutoRolloverRecordModel.Status
	2, // 1: moego.models.appointment.v1.DaycareAutoRolloverRecordModel.rollover_time:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_init() }
func file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_init() {
	if File_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DaycareAutoRolloverRecordModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_goTypes,
		DependencyIndexes: file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_depIdxs,
		EnumInfos:         file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_enumTypes,
		MessageInfos:      file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_msgTypes,
	}.Build()
	File_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto = out.File
	file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_rawDesc = nil
	file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_goTypes = nil
	file_moego_models_appointment_v1_daycare_auto_rollover_record_models_proto_depIdxs = nil
}
