// @since 2025-04-02 17:04:12
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/appointment/v1/boarding_split_lodging_models.proto

package appointmentpb

import (
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The BoardingSplitLodging model
type BoardingSplitLodgingModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet detail id
	PetDetailId int64 `protobuf:"varint,3,opt,name=pet_detail_id,json=petDetailId,proto3" json:"pet_detail_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,4,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// selected lodging id
	LodgingId int64 `protobuf:"varint,5,opt,name=lodging_id,json=lodgingId,proto3" json:"lodging_id,omitempty"`
	// start date time
	StartDateTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=start_date_time,json=startDateTime,proto3" json:"start_date_time,omitempty"`
	// end date time
	EndDateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=end_date_time,json=endDateTime,proto3" json:"end_date_time,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,8,opt,name=price,proto3" json:"price,omitempty"`
	// the create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// is applicable
	IsApplicable bool `protobuf:"varint,10,opt,name=is_applicable,json=isApplicable,proto3" json:"is_applicable,omitempty"`
}

func (x *BoardingSplitLodgingModel) Reset() {
	*x = BoardingSplitLodgingModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_boarding_split_lodging_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardingSplitLodgingModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardingSplitLodgingModel) ProtoMessage() {}

func (x *BoardingSplitLodgingModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_boarding_split_lodging_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardingSplitLodgingModel.ProtoReflect.Descriptor instead.
func (*BoardingSplitLodgingModel) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_boarding_split_lodging_models_proto_rawDescGZIP(), []int{0}
}

func (x *BoardingSplitLodgingModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BoardingSplitLodgingModel) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *BoardingSplitLodgingModel) GetPetDetailId() int64 {
	if x != nil {
		return x.PetDetailId
	}
	return 0
}

func (x *BoardingSplitLodgingModel) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *BoardingSplitLodgingModel) GetLodgingId() int64 {
	if x != nil {
		return x.LodgingId
	}
	return 0
}

func (x *BoardingSplitLodgingModel) GetStartDateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDateTime
	}
	return nil
}

func (x *BoardingSplitLodgingModel) GetEndDateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDateTime
	}
	return nil
}

func (x *BoardingSplitLodgingModel) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *BoardingSplitLodgingModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *BoardingSplitLodgingModel) GetIsApplicable() bool {
	if x != nil {
		return x.IsApplicable
	}
	return false
}

var File_moego_models_appointment_v1_boarding_split_lodging_models_proto protoreflect.FileDescriptor

var file_moego_models_appointment_v1_boarding_split_lodging_models_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e,
	0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xba, 0x03, 0x0a, 0x19, 0x42, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x0d, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49,
	0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x65,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b,
	0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x23, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x87, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76,
	0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_appointment_v1_boarding_split_lodging_models_proto_rawDescOnce sync.Once
	file_moego_models_appointment_v1_boarding_split_lodging_models_proto_rawDescData = file_moego_models_appointment_v1_boarding_split_lodging_models_proto_rawDesc
)

func file_moego_models_appointment_v1_boarding_split_lodging_models_proto_rawDescGZIP() []byte {
	file_moego_models_appointment_v1_boarding_split_lodging_models_proto_rawDescOnce.Do(func() {
		file_moego_models_appointment_v1_boarding_split_lodging_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_appointment_v1_boarding_split_lodging_models_proto_rawDescData)
	})
	return file_moego_models_appointment_v1_boarding_split_lodging_models_proto_rawDescData
}

var file_moego_models_appointment_v1_boarding_split_lodging_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_appointment_v1_boarding_split_lodging_models_proto_goTypes = []interface{}{
	(*BoardingSplitLodgingModel)(nil), // 0: moego.models.appointment.v1.BoardingSplitLodgingModel
	(*timestamppb.Timestamp)(nil),     // 1: google.protobuf.Timestamp
	(*money.Money)(nil),               // 2: google.type.Money
}
var file_moego_models_appointment_v1_boarding_split_lodging_models_proto_depIdxs = []int32{
	1, // 0: moego.models.appointment.v1.BoardingSplitLodgingModel.start_date_time:type_name -> google.protobuf.Timestamp
	1, // 1: moego.models.appointment.v1.BoardingSplitLodgingModel.end_date_time:type_name -> google.protobuf.Timestamp
	2, // 2: moego.models.appointment.v1.BoardingSplitLodgingModel.price:type_name -> google.type.Money
	1, // 3: moego.models.appointment.v1.BoardingSplitLodgingModel.created_at:type_name -> google.protobuf.Timestamp
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_appointment_v1_boarding_split_lodging_models_proto_init() }
func file_moego_models_appointment_v1_boarding_split_lodging_models_proto_init() {
	if File_moego_models_appointment_v1_boarding_split_lodging_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_appointment_v1_boarding_split_lodging_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardingSplitLodgingModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_appointment_v1_boarding_split_lodging_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_appointment_v1_boarding_split_lodging_models_proto_goTypes,
		DependencyIndexes: file_moego_models_appointment_v1_boarding_split_lodging_models_proto_depIdxs,
		MessageInfos:      file_moego_models_appointment_v1_boarding_split_lodging_models_proto_msgTypes,
	}.Build()
	File_moego_models_appointment_v1_boarding_split_lodging_models_proto = out.File
	file_moego_models_appointment_v1_boarding_split_lodging_models_proto_rawDesc = nil
	file_moego_models_appointment_v1_boarding_split_lodging_models_proto_goTypes = nil
	file_moego_models_appointment_v1_boarding_split_lodging_models_proto_depIdxs = nil
}
