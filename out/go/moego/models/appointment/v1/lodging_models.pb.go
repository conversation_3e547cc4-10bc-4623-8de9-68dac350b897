// @since 2024-01-15 16:36:40
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/appointment/v1/lodging_models.proto

package appointmentpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// lodging ticket
type LodgingTicket struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	TicketId int64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	// ticket start date
	StartDate string `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// ticket end date
	EndDate string `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// ticket start time
	StartTime int32 `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// ticket end time
	EndTime int32 `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// lodging id
	LodgingId int64 `protobuf:"varint,6,opt,name=lodging_id,json=lodgingId,proto3" json:"lodging_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,7,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// lodging usage for pets
	Usages []*LodgingUsage `protobuf:"bytes,8,rep,name=usages,proto3" json:"usages,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,9,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,10,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
}

func (x *LodgingTicket) Reset() {
	*x = LodgingTicket{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_lodging_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingTicket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingTicket) ProtoMessage() {}

func (x *LodgingTicket) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_lodging_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingTicket.ProtoReflect.Descriptor instead.
func (*LodgingTicket) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_lodging_models_proto_rawDescGZIP(), []int{0}
}

func (x *LodgingTicket) GetTicketId() int64 {
	if x != nil {
		return x.TicketId
	}
	return 0
}

func (x *LodgingTicket) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *LodgingTicket) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *LodgingTicket) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *LodgingTicket) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *LodgingTicket) GetLodgingId() int64 {
	if x != nil {
		return x.LodgingId
	}
	return 0
}

func (x *LodgingTicket) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *LodgingTicket) GetUsages() []*LodgingUsage {
	if x != nil {
		return x.Usages
	}
	return nil
}

func (x *LodgingTicket) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *LodgingTicket) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

// lodging usage
type LodgingUsage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet image
	PetImage string `protobuf:"bytes,3,opt,name=pet_image,json=petImage,proto3" json:"pet_image,omitempty"`
	// pet name
	PetName string `protobuf:"bytes,4,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// pet type
	PetType string `protobuf:"bytes,5,opt,name=pet_type,json=petType,proto3" json:"pet_type,omitempty"`
}

func (x *LodgingUsage) Reset() {
	*x = LodgingUsage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_lodging_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingUsage) ProtoMessage() {}

func (x *LodgingUsage) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_lodging_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingUsage.ProtoReflect.Descriptor instead.
func (*LodgingUsage) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_lodging_models_proto_rawDescGZIP(), []int{1}
}

func (x *LodgingUsage) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingUsage) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *LodgingUsage) GetPetImage() string {
	if x != nil {
		return x.PetImage
	}
	return ""
}

func (x *LodgingUsage) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *LodgingUsage) GetPetType() string {
	if x != nil {
		return x.PetType
	}
	return ""
}

// lodging unit view
type LodgingListView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging unit type id
	LodgingTypeId int64 `protobuf:"varint,1,opt,name=lodging_type_id,json=lodgingTypeId,proto3" json:"lodging_type_id,omitempty"`
	// lodging unit type name
	LodgingTypeName string `protobuf:"bytes,2,opt,name=lodging_type_name,json=lodgingTypeName,proto3" json:"lodging_type_name,omitempty"`
	// lodging unit list
	LodgingUnits []*LodgingUnitView `protobuf:"bytes,3,rep,name=lodging_units,json=lodgingUnits,proto3" json:"lodging_units,omitempty"`
}

func (x *LodgingListView) Reset() {
	*x = LodgingListView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_lodging_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingListView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingListView) ProtoMessage() {}

func (x *LodgingListView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_lodging_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingListView.ProtoReflect.Descriptor instead.
func (*LodgingListView) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_lodging_models_proto_rawDescGZIP(), []int{2}
}

func (x *LodgingListView) GetLodgingTypeId() int64 {
	if x != nil {
		return x.LodgingTypeId
	}
	return 0
}

func (x *LodgingListView) GetLodgingTypeName() string {
	if x != nil {
		return x.LodgingTypeName
	}
	return ""
}

func (x *LodgingListView) GetLodgingUnits() []*LodgingUnitView {
	if x != nil {
		return x.LodgingUnits
	}
	return nil
}

// lodging unit view
type LodgingUnitView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging unit id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// lodging unit name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// lodging status
	OccupiedStatus LodgingOccupiedStatus `protobuf:"varint,3,opt,name=occupied_status,json=occupiedStatus,proto3,enum=moego.models.appointment.v1.LodgingOccupiedStatus" json:"occupied_status,omitempty"`
	// is applicable
	IsApplicable bool `protobuf:"varint,4,opt,name=is_applicable,json=isApplicable,proto3" json:"is_applicable,omitempty"`
}

func (x *LodgingUnitView) Reset() {
	*x = LodgingUnitView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_lodging_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingUnitView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingUnitView) ProtoMessage() {}

func (x *LodgingUnitView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_lodging_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingUnitView.ProtoReflect.Descriptor instead.
func (*LodgingUnitView) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_lodging_models_proto_rawDescGZIP(), []int{3}
}

func (x *LodgingUnitView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingUnitView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LodgingUnitView) GetOccupiedStatus() LodgingOccupiedStatus {
	if x != nil {
		return x.OccupiedStatus
	}
	return LodgingOccupiedStatus_LODGING_OCCUPIED_STATUS_UNSPECIFIED
}

func (x *LodgingUnitView) GetIsApplicable() bool {
	if x != nil {
		return x.IsApplicable
	}
	return false
}

// lodging assign info
type LodgingAssignInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging unit id
	LodgingId int64 `protobuf:"varint,1,opt,name=lodging_id,json=lodgingId,proto3" json:"lodging_id,omitempty"`
	// assign appointment
	Appointments []*LodgingAssignAppointmentInfo `protobuf:"bytes,2,rep,name=appointments,proto3" json:"appointments,omitempty"`
}

func (x *LodgingAssignInfo) Reset() {
	*x = LodgingAssignInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_lodging_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingAssignInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingAssignInfo) ProtoMessage() {}

func (x *LodgingAssignInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_lodging_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingAssignInfo.ProtoReflect.Descriptor instead.
func (*LodgingAssignInfo) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_lodging_models_proto_rawDescGZIP(), []int{4}
}

func (x *LodgingAssignInfo) GetLodgingId() int64 {
	if x != nil {
		return x.LodgingId
	}
	return 0
}

func (x *LodgingAssignInfo) GetAppointments() []*LodgingAssignAppointmentInfo {
	if x != nil {
		return x.Appointments
	}
	return nil
}

// lodging assign appointment info
type LodgingAssignAppointmentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// assign pet details
	PetDetails []*LodgingAssignPetDetailInfo `protobuf:"bytes,3,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// assign pet evaluation details
	PetEvaluations []*LodgingAssignPetEvaluationInfo `protobuf:"bytes,4,rep,name=pet_evaluations,json=petEvaluations,proto3" json:"pet_evaluations,omitempty"`
}

func (x *LodgingAssignAppointmentInfo) Reset() {
	*x = LodgingAssignAppointmentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_lodging_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingAssignAppointmentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingAssignAppointmentInfo) ProtoMessage() {}

func (x *LodgingAssignAppointmentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_lodging_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingAssignAppointmentInfo.ProtoReflect.Descriptor instead.
func (*LodgingAssignAppointmentInfo) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_lodging_models_proto_rawDescGZIP(), []int{5}
}

func (x *LodgingAssignAppointmentInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingAssignAppointmentInfo) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *LodgingAssignAppointmentInfo) GetPetDetails() []*LodgingAssignPetDetailInfo {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *LodgingAssignAppointmentInfo) GetPetEvaluations() []*LodgingAssignPetEvaluationInfo {
	if x != nil {
		return x.PetEvaluations
	}
	return nil
}

// lodging assign pet detail info
type LodgingAssignPetDetailInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet id
	PetId int32 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// boarding service start date
	StartDate string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// boarding service end date
	EndDate string `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// boarding service start time
	StartTime int32 `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// boarding service end time
	EndTime int32 `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,7,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// pet detail certain dates
	SpecificDates []string `protobuf:"bytes,8,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// pet detail date type
	DateType *PetDetailDateType `protobuf:"varint,9,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.PetDetailDateType,oneof" json:"date_type,omitempty"`
	// is split lodging
	IsSplitLodging bool `protobuf:"varint,10,opt,name=is_split_lodging,json=isSplitLodging,proto3" json:"is_split_lodging,omitempty"`
}

func (x *LodgingAssignPetDetailInfo) Reset() {
	*x = LodgingAssignPetDetailInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_lodging_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingAssignPetDetailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingAssignPetDetailInfo) ProtoMessage() {}

func (x *LodgingAssignPetDetailInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_lodging_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingAssignPetDetailInfo.ProtoReflect.Descriptor instead.
func (*LodgingAssignPetDetailInfo) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_lodging_models_proto_rawDescGZIP(), []int{6}
}

func (x *LodgingAssignPetDetailInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingAssignPetDetailInfo) GetPetId() int32 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *LodgingAssignPetDetailInfo) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *LodgingAssignPetDetailInfo) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *LodgingAssignPetDetailInfo) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *LodgingAssignPetDetailInfo) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *LodgingAssignPetDetailInfo) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *LodgingAssignPetDetailInfo) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *LodgingAssignPetDetailInfo) GetDateType() PetDetailDateType {
	if x != nil && x.DateType != nil {
		return *x.DateType
	}
	return PetDetailDateType_PET_DETAIL_DATE_TYPE_UNSPECIFIED
}

func (x *LodgingAssignPetDetailInfo) GetIsSplitLodging() bool {
	if x != nil {
		return x.IsSplitLodging
	}
	return false
}

// lodging assign pet evaluation detail info
type LodgingAssignPetEvaluationInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet evaluation detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet id
	PetId int32 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// boarding service start date
	StartDate string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// boarding service end date
	EndDate string `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// boarding service start time
	StartTime int32 `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// boarding service end time
	EndTime int32 `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *LodgingAssignPetEvaluationInfo) Reset() {
	*x = LodgingAssignPetEvaluationInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_lodging_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingAssignPetEvaluationInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingAssignPetEvaluationInfo) ProtoMessage() {}

func (x *LodgingAssignPetEvaluationInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_lodging_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingAssignPetEvaluationInfo.ProtoReflect.Descriptor instead.
func (*LodgingAssignPetEvaluationInfo) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_lodging_models_proto_rawDescGZIP(), []int{7}
}

func (x *LodgingAssignPetEvaluationInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingAssignPetEvaluationInfo) GetPetId() int32 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *LodgingAssignPetEvaluationInfo) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *LodgingAssignPetEvaluationInfo) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *LodgingAssignPetEvaluationInfo) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *LodgingAssignPetEvaluationInfo) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

var File_moego_models_appointment_v1_lodging_models_proto protoreflect.FileDescriptor

var file_moego_models_appointment_v1_lodging_models_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x99, 0x03, 0x0a, 0x0d, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x06, 0x75, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x08,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x06, 0x75, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0x88, 0x01,
	0x0a, 0x0c, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x65, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb8, 0x01, 0x0a, 0x0f, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x26, 0x0a, 0x0f,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x51, 0x0a, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69,
	0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e,
	0x69, 0x74, 0x73, 0x22, 0xb7, 0x01, 0x0a, 0x0f, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55,
	0x6e, 0x69, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x5b, 0x0a, 0x0f, 0x6f,
	0x63, 0x63, 0x75, 0x70, 0x69, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x4f, 0x63, 0x63, 0x75, 0x70, 0x69,
	0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x69,
	0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x69, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x91, 0x01,
	0x0a, 0x11, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x49, 0x64, 0x12, 0x5d, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x22, 0x8f, 0x02, 0x0a, 0x1c, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x58, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x64, 0x0a,
	0x0f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x70, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0xbf, 0x03, 0x0a, 0x1a, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x55, 0x0a,
	0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x50, 0x0a, 0x09, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00,
	0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a,
	0x10, 0x69, 0x73, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xbb, 0x01, 0x0a, 0x1e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x42, 0x87, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_appointment_v1_lodging_models_proto_rawDescOnce sync.Once
	file_moego_models_appointment_v1_lodging_models_proto_rawDescData = file_moego_models_appointment_v1_lodging_models_proto_rawDesc
)

func file_moego_models_appointment_v1_lodging_models_proto_rawDescGZIP() []byte {
	file_moego_models_appointment_v1_lodging_models_proto_rawDescOnce.Do(func() {
		file_moego_models_appointment_v1_lodging_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_appointment_v1_lodging_models_proto_rawDescData)
	})
	return file_moego_models_appointment_v1_lodging_models_proto_rawDescData
}

var file_moego_models_appointment_v1_lodging_models_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_models_appointment_v1_lodging_models_proto_goTypes = []interface{}{
	(*LodgingTicket)(nil),                  // 0: moego.models.appointment.v1.LodgingTicket
	(*LodgingUsage)(nil),                   // 1: moego.models.appointment.v1.LodgingUsage
	(*LodgingListView)(nil),                // 2: moego.models.appointment.v1.LodgingListView
	(*LodgingUnitView)(nil),                // 3: moego.models.appointment.v1.LodgingUnitView
	(*LodgingAssignInfo)(nil),              // 4: moego.models.appointment.v1.LodgingAssignInfo
	(*LodgingAssignAppointmentInfo)(nil),   // 5: moego.models.appointment.v1.LodgingAssignAppointmentInfo
	(*LodgingAssignPetDetailInfo)(nil),     // 6: moego.models.appointment.v1.LodgingAssignPetDetailInfo
	(*LodgingAssignPetEvaluationInfo)(nil), // 7: moego.models.appointment.v1.LodgingAssignPetEvaluationInfo
	(v1.ServiceItemType)(0),                // 8: moego.models.offering.v1.ServiceItemType
	(LodgingOccupiedStatus)(0),             // 9: moego.models.appointment.v1.LodgingOccupiedStatus
	(PetDetailDateType)(0),                 // 10: moego.models.appointment.v1.PetDetailDateType
}
var file_moego_models_appointment_v1_lodging_models_proto_depIdxs = []int32{
	1,  // 0: moego.models.appointment.v1.LodgingTicket.usages:type_name -> moego.models.appointment.v1.LodgingUsage
	8,  // 1: moego.models.appointment.v1.LodgingTicket.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	3,  // 2: moego.models.appointment.v1.LodgingListView.lodging_units:type_name -> moego.models.appointment.v1.LodgingUnitView
	9,  // 3: moego.models.appointment.v1.LodgingUnitView.occupied_status:type_name -> moego.models.appointment.v1.LodgingOccupiedStatus
	5,  // 4: moego.models.appointment.v1.LodgingAssignInfo.appointments:type_name -> moego.models.appointment.v1.LodgingAssignAppointmentInfo
	6,  // 5: moego.models.appointment.v1.LodgingAssignAppointmentInfo.pet_details:type_name -> moego.models.appointment.v1.LodgingAssignPetDetailInfo
	7,  // 6: moego.models.appointment.v1.LodgingAssignAppointmentInfo.pet_evaluations:type_name -> moego.models.appointment.v1.LodgingAssignPetEvaluationInfo
	8,  // 7: moego.models.appointment.v1.LodgingAssignPetDetailInfo.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	10, // 8: moego.models.appointment.v1.LodgingAssignPetDetailInfo.date_type:type_name -> moego.models.appointment.v1.PetDetailDateType
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_moego_models_appointment_v1_lodging_models_proto_init() }
func file_moego_models_appointment_v1_lodging_models_proto_init() {
	if File_moego_models_appointment_v1_lodging_models_proto != nil {
		return
	}
	file_moego_models_appointment_v1_lodging_enums_proto_init()
	file_moego_models_appointment_v1_pet_detail_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_appointment_v1_lodging_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingTicket); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_lodging_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingUsage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_lodging_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingListView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_lodging_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingUnitView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_lodging_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingAssignInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_lodging_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingAssignAppointmentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_lodging_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingAssignPetDetailInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_lodging_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingAssignPetEvaluationInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_appointment_v1_lodging_models_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_appointment_v1_lodging_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_appointment_v1_lodging_models_proto_goTypes,
		DependencyIndexes: file_moego_models_appointment_v1_lodging_models_proto_depIdxs,
		MessageInfos:      file_moego_models_appointment_v1_lodging_models_proto_msgTypes,
	}.Build()
	File_moego_models_appointment_v1_lodging_models_proto = out.File
	file_moego_models_appointment_v1_lodging_models_proto_rawDesc = nil
	file_moego_models_appointment_v1_lodging_models_proto_goTypes = nil
	file_moego_models_appointment_v1_lodging_models_proto_depIdxs = nil
}
