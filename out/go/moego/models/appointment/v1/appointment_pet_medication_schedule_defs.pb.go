// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/appointment/v1/appointment_pet_medication_schedule_defs.proto

package appointmentpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Appointment pet medication schedule definition
type AppointmentPetMedicationScheduleDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// medication amount, such as 1.2, 1/2, 1 etc.
	MedicationAmount string `protobuf:"bytes,1,opt,name=medication_amount,json=medicationAmount,proto3" json:"medication_amount,omitempty"`
	// medication unit, pet_metadata.metadata_value, metadata_name = 7
	MedicationUnit string `protobuf:"bytes,2,opt,name=medication_unit,json=medicationUnit,proto3" json:"medication_unit,omitempty"`
	// medication name, user input
	MedicationName string `protobuf:"bytes,3,opt,name=medication_name,json=medicationName,proto3" json:"medication_name,omitempty"`
	// medication source, user input
	MedicationNote *string `protobuf:"bytes,4,opt,name=medication_note,json=medicationNote,proto3,oneof" json:"medication_note,omitempty"`
	// medication time, medication time in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.
	MedicationTimes []*v1.BusinessPetScheduleTimeDef `protobuf:"bytes,6,rep,name=medication_times,json=medicationTimes,proto3" json:"medication_times,omitempty"`
	// feeding medication schedule selected date
	SelectedDate *AppointmentPetMedicationScheduleDef_SelectedDateDef `protobuf:"bytes,7,opt,name=selected_date,json=selectedDate,proto3,oneof" json:"selected_date,omitempty"`
}

func (x *AppointmentPetMedicationScheduleDef) Reset() {
	*x = AppointmentPetMedicationScheduleDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentPetMedicationScheduleDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentPetMedicationScheduleDef) ProtoMessage() {}

func (x *AppointmentPetMedicationScheduleDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentPetMedicationScheduleDef.ProtoReflect.Descriptor instead.
func (*AppointmentPetMedicationScheduleDef) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_rawDescGZIP(), []int{0}
}

func (x *AppointmentPetMedicationScheduleDef) GetMedicationAmount() string {
	if x != nil {
		return x.MedicationAmount
	}
	return ""
}

func (x *AppointmentPetMedicationScheduleDef) GetMedicationUnit() string {
	if x != nil {
		return x.MedicationUnit
	}
	return ""
}

func (x *AppointmentPetMedicationScheduleDef) GetMedicationName() string {
	if x != nil {
		return x.MedicationName
	}
	return ""
}

func (x *AppointmentPetMedicationScheduleDef) GetMedicationNote() string {
	if x != nil && x.MedicationNote != nil {
		return *x.MedicationNote
	}
	return ""
}

func (x *AppointmentPetMedicationScheduleDef) GetMedicationTimes() []*v1.BusinessPetScheduleTimeDef {
	if x != nil {
		return x.MedicationTimes
	}
	return nil
}

func (x *AppointmentPetMedicationScheduleDef) GetSelectedDate() *AppointmentPetMedicationScheduleDef_SelectedDateDef {
	if x != nil {
		return x.SelectedDate
	}
	return nil
}

// selected date
type AppointmentPetMedicationScheduleDef_SelectedDateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// feeding medication schedule date type
	// default for PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY
	DateType v1.FeedingMedicationScheduleDateType `protobuf:"varint,1,opt,name=date_type,json=dateType,proto3,enum=moego.models.business_customer.v1.FeedingMedicationScheduleDateType" json:"date_type,omitempty"`
	// specific date
	SpecificDates []string `protobuf:"bytes,2,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
}

func (x *AppointmentPetMedicationScheduleDef_SelectedDateDef) Reset() {
	*x = AppointmentPetMedicationScheduleDef_SelectedDateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentPetMedicationScheduleDef_SelectedDateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentPetMedicationScheduleDef_SelectedDateDef) ProtoMessage() {}

func (x *AppointmentPetMedicationScheduleDef_SelectedDateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentPetMedicationScheduleDef_SelectedDateDef.ProtoReflect.Descriptor instead.
func (*AppointmentPetMedicationScheduleDef_SelectedDateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_rawDescGZIP(), []int{0, 0}
}

func (x *AppointmentPetMedicationScheduleDef_SelectedDateDef) GetDateType() v1.FeedingMedicationScheduleDateType {
	if x != nil {
		return x.DateType
	}
	return v1.FeedingMedicationScheduleDateType(0)
}

func (x *AppointmentPetMedicationScheduleDef_SelectedDateDef) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

var File_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto protoreflect.FileDescriptor

var file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_rawDesc = []byte{
	0x0a, 0x4a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x4c, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x4a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xdf, 0x05, 0x0a,
	0x23, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x4d,
	0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x44, 0x65, 0x66, 0x12, 0x35, 0x0a, 0x11, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x10, 0x6d, 0x65, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x0f, 0x6d,
	0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x0e,
	0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x31,
	0x0a, 0x0f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff,
	0x01, 0x52, 0x0e, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x36, 0x0a, 0x0f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x6f, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72,
	0x03, 0x18, 0x80, 0x50, 0x48, 0x00, 0x52, 0x0e, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x6f, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x74, 0x0a, 0x10, 0x6d, 0x65, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x50, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x44,
	0x65, 0x66, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x01, 0x10, 0x63, 0x52, 0x0f,
	0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12,
	0x7a, 0x0a, 0x0d, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x50, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x50, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x44, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x48, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x1a, 0xca, 0x01, 0x0a, 0x0f,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12,
	0x6d, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x48,
	0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x21, 0xfa, 0x42, 0x1e, 0x92, 0x01, 0x1b, 0x10, 0x64,
	0x22, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b,
	0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69,
	0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x65, 0x73, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6d, 0x65, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x42, 0x10, 0x0a, 0x0e,
	0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x87,
	0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_rawDescOnce sync.Once
	file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_rawDescData = file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_rawDesc
)

func file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_rawDescGZIP() []byte {
	file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_rawDescData)
	})
	return file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_rawDescData
}

var file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_goTypes = []interface{}{
	(*AppointmentPetMedicationScheduleDef)(nil),                 // 0: moego.models.appointment.v1.AppointmentPetMedicationScheduleDef
	(*AppointmentPetMedicationScheduleDef_SelectedDateDef)(nil), // 1: moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef
	(*v1.BusinessPetScheduleTimeDef)(nil),                       // 2: moego.models.business_customer.v1.BusinessPetScheduleTimeDef
	(v1.FeedingMedicationScheduleDateType)(0),                   // 3: moego.models.business_customer.v1.FeedingMedicationScheduleDateType
}
var file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_depIdxs = []int32{
	2, // 0: moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.medication_times:type_name -> moego.models.business_customer.v1.BusinessPetScheduleTimeDef
	1, // 1: moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.selected_date:type_name -> moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef
	3, // 2: moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef.date_type:type_name -> moego.models.business_customer.v1.FeedingMedicationScheduleDateType
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_init() }
func file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_init() {
	if File_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentPetMedicationScheduleDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentPetMedicationScheduleDef_SelectedDateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_msgTypes,
	}.Build()
	File_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto = out.File
	file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_rawDesc = nil
	file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_goTypes = nil
	file_moego_models_appointment_v1_appointment_pet_medication_schedule_defs_proto_depIdxs = nil
}
