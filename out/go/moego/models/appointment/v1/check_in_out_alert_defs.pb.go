// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/appointment/v1/check_in_out_alert_defs.proto

package appointmentpb

import (
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// COFStatus
type CardOnFileAlert_COFStatus int32

const (
	// unspecified
	CardOnFileAlert_COF_STATUS_UNSPECIFIED CardOnFileAlert_COFStatus = 0
	// authorized
	CardOnFileAlert_AUTHORIZED CardOnFileAlert_COFStatus = 1
	// pending
	CardOnFileAlert_PENDING CardOnFileAlert_COFStatus = 2
	// failed
	CardOnFileAlert_FAILED CardOnFileAlert_COFStatus = 3
	// no card on file
	CardOnFileAlert_NO_CARD_ON_FILE CardOnFileAlert_COFStatus = 4
)

// Enum value maps for CardOnFileAlert_COFStatus.
var (
	CardOnFileAlert_COFStatus_name = map[int32]string{
		0: "COF_STATUS_UNSPECIFIED",
		1: "AUTHORIZED",
		2: "PENDING",
		3: "FAILED",
		4: "NO_CARD_ON_FILE",
	}
	CardOnFileAlert_COFStatus_value = map[string]int32{
		"COF_STATUS_UNSPECIFIED": 0,
		"AUTHORIZED":             1,
		"PENDING":                2,
		"FAILED":                 3,
		"NO_CARD_ON_FILE":        4,
	}
)

func (x CardOnFileAlert_COFStatus) Enum() *CardOnFileAlert_COFStatus {
	p := new(CardOnFileAlert_COFStatus)
	*p = x
	return p
}

func (x CardOnFileAlert_COFStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardOnFileAlert_COFStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_enumTypes[0].Descriptor()
}

func (CardOnFileAlert_COFStatus) Type() protoreflect.EnumType {
	return &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_enumTypes[0]
}

func (x CardOnFileAlert_COFStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardOnFileAlert_COFStatus.Descriptor instead.
func (CardOnFileAlert_COFStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{9, 0}
}

// ClientPetsMapping
type ClientPetsMapping struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// list of pet ids
	PetIds []int64 `protobuf:"varint,2,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
}

func (x *ClientPetsMapping) Reset() {
	*x = ClientPetsMapping{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientPetsMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientPetsMapping) ProtoMessage() {}

func (x *ClientPetsMapping) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientPetsMapping.ProtoReflect.Descriptor instead.
func (*ClientPetsMapping) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{0}
}

func (x *ClientPetsMapping) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ClientPetsMapping) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

// AlertDetail
type AlertDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client alert
	ClientAlert *ClientAlert `protobuf:"bytes,1,opt,name=client_alert,json=clientAlert,proto3,oneof" json:"client_alert,omitempty"`
	// list of pet alert
	PetAlerts []*PetAlert `protobuf:"bytes,2,rep,name=pet_alerts,json=petAlerts,proto3" json:"pet_alerts,omitempty"`
}

func (x *AlertDetail) Reset() {
	*x = AlertDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlertDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlertDetail) ProtoMessage() {}

func (x *AlertDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlertDetail.ProtoReflect.Descriptor instead.
func (*AlertDetail) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{1}
}

func (x *AlertDetail) GetClientAlert() *ClientAlert {
	if x != nil {
		return x.ClientAlert
	}
	return nil
}

func (x *AlertDetail) GetPetAlerts() []*PetAlert {
	if x != nil {
		return x.PetAlerts
	}
	return nil
}

// ClientAlert
type ClientAlert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// client tag alerts
	ClientTagAlert *ClientTagAlert `protobuf:"bytes,5,opt,name=client_tag_alert,json=clientTagAlert,proto3,oneof" json:"client_tag_alert,omitempty"`
	// membership alert
	MembershipAlert *MembershipAlert `protobuf:"bytes,6,opt,name=membership_alert,json=membershipAlert,proto3,oneof" json:"membership_alert,omitempty"`
	// package alert
	PackageAlert *PackageAlert `protobuf:"bytes,7,opt,name=package_alert,json=packageAlert,proto3,oneof" json:"package_alert,omitempty"`
	// note alert
	NoteAlert *NoteAlert `protobuf:"bytes,8,opt,name=note_alert,json=noteAlert,proto3,oneof" json:"note_alert,omitempty"`
	// unsigned agreement alert
	UnsignedAgreementAlert *UnsignedAgreementAlert `protobuf:"bytes,9,opt,name=unsigned_agreement_alert,json=unsignedAgreementAlert,proto3,oneof" json:"unsigned_agreement_alert,omitempty"`
	// card on file alert
	CardOnFileAlert *CardOnFileAlert `protobuf:"bytes,10,opt,name=card_on_file_alert,json=cardOnFileAlert,proto3,oneof" json:"card_on_file_alert,omitempty"`
	// unpaid balance alert
	UnpaidBalanceAlert *UnpaidBalanceAlert `protobuf:"bytes,11,opt,name=unpaid_balance_alert,json=unpaidBalanceAlert,proto3,oneof" json:"unpaid_balance_alert,omitempty"`
	// pickup person alert
	PickupPersonAlert *PickupPersonAlert `protobuf:"bytes,12,opt,name=pickup_person_alert,json=pickupPersonAlert,proto3,oneof" json:"pickup_person_alert,omitempty"`
}

func (x *ClientAlert) Reset() {
	*x = ClientAlert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientAlert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientAlert) ProtoMessage() {}

func (x *ClientAlert) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientAlert.ProtoReflect.Descriptor instead.
func (*ClientAlert) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{2}
}

func (x *ClientAlert) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ClientAlert) GetClientTagAlert() *ClientTagAlert {
	if x != nil {
		return x.ClientTagAlert
	}
	return nil
}

func (x *ClientAlert) GetMembershipAlert() *MembershipAlert {
	if x != nil {
		return x.MembershipAlert
	}
	return nil
}

func (x *ClientAlert) GetPackageAlert() *PackageAlert {
	if x != nil {
		return x.PackageAlert
	}
	return nil
}

func (x *ClientAlert) GetNoteAlert() *NoteAlert {
	if x != nil {
		return x.NoteAlert
	}
	return nil
}

func (x *ClientAlert) GetUnsignedAgreementAlert() *UnsignedAgreementAlert {
	if x != nil {
		return x.UnsignedAgreementAlert
	}
	return nil
}

func (x *ClientAlert) GetCardOnFileAlert() *CardOnFileAlert {
	if x != nil {
		return x.CardOnFileAlert
	}
	return nil
}

func (x *ClientAlert) GetUnpaidBalanceAlert() *UnpaidBalanceAlert {
	if x != nil {
		return x.UnpaidBalanceAlert
	}
	return nil
}

func (x *ClientAlert) GetPickupPersonAlert() *PickupPersonAlert {
	if x != nil {
		return x.PickupPersonAlert
	}
	return nil
}

// PetAlert
type PetAlert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet code alerts
	PetCodeAlert *PetCodeAlert `protobuf:"bytes,5,opt,name=pet_code_alert,json=petCodeAlert,proto3,oneof" json:"pet_code_alert,omitempty"`
	// vaccine alert
	VaccineAlert *VaccineAlert `protobuf:"bytes,6,opt,name=vaccine_alert,json=vaccineAlert,proto3,oneof" json:"vaccine_alert,omitempty"`
	// incident alert
	IncidentAlert *IncidentAlert `protobuf:"bytes,7,opt,name=incident_alert,json=incidentAlert,proto3,oneof" json:"incident_alert,omitempty"`
}

func (x *PetAlert) Reset() {
	*x = PetAlert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetAlert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetAlert) ProtoMessage() {}

func (x *PetAlert) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetAlert.ProtoReflect.Descriptor instead.
func (*PetAlert) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{3}
}

func (x *PetAlert) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetAlert) GetPetCodeAlert() *PetCodeAlert {
	if x != nil {
		return x.PetCodeAlert
	}
	return nil
}

func (x *PetAlert) GetVaccineAlert() *VaccineAlert {
	if x != nil {
		return x.VaccineAlert
	}
	return nil
}

func (x *PetAlert) GetIncidentAlert() *IncidentAlert {
	if x != nil {
		return x.IncidentAlert
	}
	return nil
}

// ClientTagAlert
type ClientTagAlert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client tags
	ClientTags []*v1.BusinessCustomerTagModel `protobuf:"bytes,1,rep,name=client_tags,json=clientTags,proto3" json:"client_tags,omitempty"`
}

func (x *ClientTagAlert) Reset() {
	*x = ClientTagAlert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientTagAlert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientTagAlert) ProtoMessage() {}

func (x *ClientTagAlert) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientTagAlert.ProtoReflect.Descriptor instead.
func (*ClientTagAlert) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{4}
}

func (x *ClientTagAlert) GetClientTags() []*v1.BusinessCustomerTagModel {
	if x != nil {
		return x.ClientTags
	}
	return nil
}

// PetCodeAlert
type PetCodeAlert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet codes, deprecated, use pet_code_views instead
	//
	// Deprecated: Do not use.
	PetCodes []*v1.BusinessPetCodeView `protobuf:"bytes,1,rep,name=pet_codes,json=petCodes,proto3" json:"pet_codes,omitempty"`
	// pet code binding comments
	PetCodeViews []*PetCodeAlert_PetCodeView `protobuf:"bytes,2,rep,name=pet_code_views,json=petCodeViews,proto3" json:"pet_code_views,omitempty"`
}

func (x *PetCodeAlert) Reset() {
	*x = PetCodeAlert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetCodeAlert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetCodeAlert) ProtoMessage() {}

func (x *PetCodeAlert) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetCodeAlert.ProtoReflect.Descriptor instead.
func (*PetCodeAlert) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{5}
}

// Deprecated: Do not use.
func (x *PetCodeAlert) GetPetCodes() []*v1.BusinessPetCodeView {
	if x != nil {
		return x.PetCodes
	}
	return nil
}

func (x *PetCodeAlert) GetPetCodeViews() []*PetCodeAlert_PetCodeView {
	if x != nil {
		return x.PetCodeViews
	}
	return nil
}

// VaccineAlert
type VaccineAlert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of vaccines
	Vaccines []*VaccineAlert_VaccineView `protobuf:"bytes,1,rep,name=vaccines,proto3" json:"vaccines,omitempty"`
}

func (x *VaccineAlert) Reset() {
	*x = VaccineAlert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VaccineAlert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VaccineAlert) ProtoMessage() {}

func (x *VaccineAlert) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VaccineAlert.ProtoReflect.Descriptor instead.
func (*VaccineAlert) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{6}
}

func (x *VaccineAlert) GetVaccines() []*VaccineAlert_VaccineView {
	if x != nil {
		return x.Vaccines
	}
	return nil
}

// IncidentAlert
type IncidentAlert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of incidents
	Incidents []*IncidentAlert_IncidentView `protobuf:"bytes,1,rep,name=incidents,proto3" json:"incidents,omitempty"`
}

func (x *IncidentAlert) Reset() {
	*x = IncidentAlert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncidentAlert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncidentAlert) ProtoMessage() {}

func (x *IncidentAlert) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncidentAlert.ProtoReflect.Descriptor instead.
func (*IncidentAlert) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{7}
}

func (x *IncidentAlert) GetIncidents() []*IncidentAlert_IncidentView {
	if x != nil {
		return x.Incidents
	}
	return nil
}

// MembershipAlert
type MembershipAlert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of membership subscriptions
	Subscriptions []*v11.MembershipSubscriptionModel `protobuf:"bytes,1,rep,name=subscriptions,proto3" json:"subscriptions,omitempty"`
}

func (x *MembershipAlert) Reset() {
	*x = MembershipAlert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MembershipAlert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MembershipAlert) ProtoMessage() {}

func (x *MembershipAlert) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MembershipAlert.ProtoReflect.Descriptor instead.
func (*MembershipAlert) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{8}
}

func (x *MembershipAlert) GetSubscriptions() []*v11.MembershipSubscriptionModel {
	if x != nil {
		return x.Subscriptions
	}
	return nil
}

// CardOnFileAlert
type CardOnFileAlert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// card on file status
	CofStatus *CardOnFileAlert_COFStatus `protobuf:"varint,1,opt,name=cof_status,json=cofStatus,proto3,enum=moego.models.appointment.v1.CardOnFileAlert_COFStatus,oneof" json:"cof_status,omitempty"`
}

func (x *CardOnFileAlert) Reset() {
	*x = CardOnFileAlert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardOnFileAlert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardOnFileAlert) ProtoMessage() {}

func (x *CardOnFileAlert) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardOnFileAlert.ProtoReflect.Descriptor instead.
func (*CardOnFileAlert) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{9}
}

func (x *CardOnFileAlert) GetCofStatus() CardOnFileAlert_COFStatus {
	if x != nil && x.CofStatus != nil {
		return *x.CofStatus
	}
	return CardOnFileAlert_COF_STATUS_UNSPECIFIED
}

// PackageAlert
type PackageAlert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of packages
	Packages []*PackageAlert_PackageView `protobuf:"bytes,1,rep,name=packages,proto3" json:"packages,omitempty"`
}

func (x *PackageAlert) Reset() {
	*x = PackageAlert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PackageAlert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackageAlert) ProtoMessage() {}

func (x *PackageAlert) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackageAlert.ProtoReflect.Descriptor instead.
func (*PackageAlert) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{10}
}

func (x *PackageAlert) GetPackages() []*PackageAlert_PackageView {
	if x != nil {
		return x.Packages
	}
	return nil
}

// NoteAlert
type NoteAlert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// last alert notes
	AlertNote *AppointmentNoteModel `protobuf:"bytes,1,opt,name=alert_note,json=alertNote,proto3,oneof" json:"alert_note,omitempty"`
}

func (x *NoteAlert) Reset() {
	*x = NoteAlert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoteAlert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoteAlert) ProtoMessage() {}

func (x *NoteAlert) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoteAlert.ProtoReflect.Descriptor instead.
func (*NoteAlert) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{11}
}

func (x *NoteAlert) GetAlertNote() *AppointmentNoteModel {
	if x != nil {
		return x.AlertNote
	}
	return nil
}

// UnsignedAgreementAlert
type UnsignedAgreementAlert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of unsigned agreements
	Agreements []*v12.AgreementModelSimpleView `protobuf:"bytes,1,rep,name=agreements,proto3" json:"agreements,omitempty"`
}

func (x *UnsignedAgreementAlert) Reset() {
	*x = UnsignedAgreementAlert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnsignedAgreementAlert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsignedAgreementAlert) ProtoMessage() {}

func (x *UnsignedAgreementAlert) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsignedAgreementAlert.ProtoReflect.Descriptor instead.
func (*UnsignedAgreementAlert) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{12}
}

func (x *UnsignedAgreementAlert) GetAgreements() []*v12.AgreementModelSimpleView {
	if x != nil {
		return x.Agreements
	}
	return nil
}

// UnpaidBalanceAlert
type UnpaidBalanceAlert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unpaid amount
	UnpaidAmount *money.Money `protobuf:"bytes,1,opt,name=unpaid_amount,json=unpaidAmount,proto3,oneof" json:"unpaid_amount,omitempty"`
}

func (x *UnpaidBalanceAlert) Reset() {
	*x = UnpaidBalanceAlert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnpaidBalanceAlert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnpaidBalanceAlert) ProtoMessage() {}

func (x *UnpaidBalanceAlert) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnpaidBalanceAlert.ProtoReflect.Descriptor instead.
func (*UnpaidBalanceAlert) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{13}
}

func (x *UnpaidBalanceAlert) GetUnpaidAmount() *money.Money {
	if x != nil {
		return x.UnpaidAmount
	}
	return nil
}

// PickupPersonAlert
type PickupPersonAlert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of contacts
	Contacts []*PickupPersonAlert_Contact `protobuf:"bytes,1,rep,name=contacts,proto3" json:"contacts,omitempty"`
}

func (x *PickupPersonAlert) Reset() {
	*x = PickupPersonAlert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PickupPersonAlert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PickupPersonAlert) ProtoMessage() {}

func (x *PickupPersonAlert) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PickupPersonAlert.ProtoReflect.Descriptor instead.
func (*PickupPersonAlert) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{14}
}

func (x *PickupPersonAlert) GetContacts() []*PickupPersonAlert_Contact {
	if x != nil {
		return x.Contacts
	}
	return nil
}

// pet code view
type PetCodeAlert_PetCodeView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// code id
	PetCode *v1.BusinessPetCodeView `protobuf:"bytes,1,opt,name=pet_code,json=petCode,proto3" json:"pet_code,omitempty"`
	// comment
	Comment string `protobuf:"bytes,2,opt,name=comment,proto3" json:"comment,omitempty"`
}

func (x *PetCodeAlert_PetCodeView) Reset() {
	*x = PetCodeAlert_PetCodeView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetCodeAlert_PetCodeView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetCodeAlert_PetCodeView) ProtoMessage() {}

func (x *PetCodeAlert_PetCodeView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetCodeAlert_PetCodeView.ProtoReflect.Descriptor instead.
func (*PetCodeAlert_PetCodeView) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{5, 0}
}

func (x *PetCodeAlert_PetCodeView) GetPetCode() *v1.BusinessPetCodeView {
	if x != nil {
		return x.PetCode
	}
	return nil
}

func (x *PetCodeAlert_PetCodeView) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

// VaccineView
type VaccineAlert_VaccineView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vaccine id
	VaccineId int64 `protobuf:"varint,1,opt,name=vaccine_id,json=vaccineId,proto3" json:"vaccine_id,omitempty"`
	// vaccine name
	VaccineName string `protobuf:"bytes,2,opt,name=vaccine_name,json=vaccineName,proto3" json:"vaccine_name,omitempty"`
	// vaccine binding id
	VaccineBindingId int64 `protobuf:"varint,3,opt,name=vaccine_binding_id,json=vaccineBindingId,proto3" json:"vaccine_binding_id,omitempty"`
	// vaccine document urls
	DocumentUrls []string `protobuf:"bytes,4,rep,name=document_urls,json=documentUrls,proto3" json:"document_urls,omitempty"`
	// expiration date, may not exist
	ExpirationDate *date.Date `protobuf:"bytes,5,opt,name=expiration_date,json=expirationDate,proto3,oneof" json:"expiration_date,omitempty"`
	// is missing
	IsMissing bool `protobuf:"varint,6,opt,name=is_missing,json=isMissing,proto3" json:"is_missing,omitempty"`
}

func (x *VaccineAlert_VaccineView) Reset() {
	*x = VaccineAlert_VaccineView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VaccineAlert_VaccineView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VaccineAlert_VaccineView) ProtoMessage() {}

func (x *VaccineAlert_VaccineView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VaccineAlert_VaccineView.ProtoReflect.Descriptor instead.
func (*VaccineAlert_VaccineView) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{6, 0}
}

func (x *VaccineAlert_VaccineView) GetVaccineId() int64 {
	if x != nil {
		return x.VaccineId
	}
	return 0
}

func (x *VaccineAlert_VaccineView) GetVaccineName() string {
	if x != nil {
		return x.VaccineName
	}
	return ""
}

func (x *VaccineAlert_VaccineView) GetVaccineBindingId() int64 {
	if x != nil {
		return x.VaccineBindingId
	}
	return 0
}

func (x *VaccineAlert_VaccineView) GetDocumentUrls() []string {
	if x != nil {
		return x.DocumentUrls
	}
	return nil
}

func (x *VaccineAlert_VaccineView) GetExpirationDate() *date.Date {
	if x != nil {
		return x.ExpirationDate
	}
	return nil
}

func (x *VaccineAlert_VaccineView) GetIsMissing() bool {
	if x != nil {
		return x.IsMissing
	}
	return false
}

// IncidentView
type IncidentAlert_IncidentView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// list of pet ids
	PetIds []int64 `protobuf:"varint,2,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// incident report id
	IncidentReportId int64 `protobuf:"varint,3,opt,name=incident_report_id,json=incidentReportId,proto3" json:"incident_report_id,omitempty"`
	// incident type id
	IncidentTypeId int64 `protobuf:"varint,4,opt,name=incident_type_id,json=incidentTypeId,proto3" json:"incident_type_id,omitempty"`
	// incident type name
	IncidentTypeName string `protobuf:"bytes,5,opt,name=incident_type_name,json=incidentTypeName,proto3" json:"incident_type_name,omitempty"`
	// incident description
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	// incident timestamp
	IncidentTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=incident_time,json=incidentTime,proto3" json:"incident_time,omitempty"`
}

func (x *IncidentAlert_IncidentView) Reset() {
	*x = IncidentAlert_IncidentView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncidentAlert_IncidentView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncidentAlert_IncidentView) ProtoMessage() {}

func (x *IncidentAlert_IncidentView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncidentAlert_IncidentView.ProtoReflect.Descriptor instead.
func (*IncidentAlert_IncidentView) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{7, 0}
}

func (x *IncidentAlert_IncidentView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *IncidentAlert_IncidentView) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *IncidentAlert_IncidentView) GetIncidentReportId() int64 {
	if x != nil {
		return x.IncidentReportId
	}
	return 0
}

func (x *IncidentAlert_IncidentView) GetIncidentTypeId() int64 {
	if x != nil {
		return x.IncidentTypeId
	}
	return 0
}

func (x *IncidentAlert_IncidentView) GetIncidentTypeName() string {
	if x != nil {
		return x.IncidentTypeName
	}
	return ""
}

func (x *IncidentAlert_IncidentView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *IncidentAlert_IncidentView) GetIncidentTime() *timestamppb.Timestamp {
	if x != nil {
		return x.IncidentTime
	}
	return nil
}

// PackageView
type PackageAlert_PackageView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// package id
	PackageId int64 `protobuf:"varint,2,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	// package name
	PackageName string `protobuf:"bytes,3,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	// valid start date
	StartDate *date.Date `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// expiration date
	ExpirationDate *date.Date `protobuf:"bytes,5,opt,name=expiration_date,json=expirationDate,proto3,oneof" json:"expiration_date,omitempty"`
}

func (x *PackageAlert_PackageView) Reset() {
	*x = PackageAlert_PackageView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PackageAlert_PackageView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackageAlert_PackageView) ProtoMessage() {}

func (x *PackageAlert_PackageView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackageAlert_PackageView.ProtoReflect.Descriptor instead.
func (*PackageAlert_PackageView) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{10, 0}
}

func (x *PackageAlert_PackageView) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *PackageAlert_PackageView) GetPackageId() int64 {
	if x != nil {
		return x.PackageId
	}
	return 0
}

func (x *PackageAlert_PackageView) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *PackageAlert_PackageView) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *PackageAlert_PackageView) GetExpirationDate() *date.Date {
	if x != nil {
		return x.ExpirationDate
	}
	return nil
}

// contact
type PickupPersonAlert_Contact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// first name
	FirstName string `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *PickupPersonAlert_Contact) Reset() {
	*x = PickupPersonAlert_Contact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PickupPersonAlert_Contact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PickupPersonAlert_Contact) ProtoMessage() {}

func (x *PickupPersonAlert_Contact) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PickupPersonAlert_Contact.ProtoReflect.Descriptor instead.
func (*PickupPersonAlert_Contact) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP(), []int{14, 0}
}

func (x *PickupPersonAlert_Contact) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *PickupPersonAlert_Contact) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *PickupPersonAlert_Contact) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

var File_moego_models_appointment_v1_check_in_out_alert_defs_proto protoreflect.FileDescriptor

var file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDesc = []byte{
	0x0a, 0x39, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74,
	0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x39, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5f,
	0x0a, 0x11, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x73, 0x4d, 0x61, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10, 0x64, 0x18,
	0x01, 0x22, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x06, 0x70, 0x65, 0x74, 0x49, 0x64, 0x73, 0x22,
	0xb6, 0x01, 0x0a, 0x0b, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x50, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x48,
	0x00, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x88, 0x01,
	0x01, 0x12, 0x44, 0x0a, 0x0a, 0x70, 0x65, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x09, 0x70, 0x65,
	0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x22, 0xda, 0x07, 0x0a, 0x0b, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x10, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x48, 0x00, 0x52, 0x0e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x41, 0x6c, 0x65,
	0x72, 0x74, 0x88, 0x01, 0x01, 0x12, 0x5c, 0x0a, 0x10, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x48, 0x01, 0x52,
	0x0f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x53, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x61,
	0x6c, 0x65, 0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x48, 0x02, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x88, 0x01, 0x01, 0x12, 0x4a, 0x0a, 0x0a, 0x6e, 0x6f, 0x74, 0x65,
	0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x65, 0x41,
	0x6c, 0x65, 0x72, 0x74, 0x48, 0x03, 0x52, 0x09, 0x6e, 0x6f, 0x74, 0x65, 0x41, 0x6c, 0x65, 0x72,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x72, 0x0a, 0x18, 0x75, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x48, 0x04, 0x52, 0x16, 0x75,
	0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x88, 0x01, 0x01, 0x12, 0x5e, 0x0a, 0x12, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x6c, 0x65,
	0x72, 0x74, 0x48, 0x05, 0x52, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x4f, 0x6e, 0x46, 0x69, 0x6c, 0x65,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x88, 0x01, 0x01, 0x12, 0x66, 0x0a, 0x14, 0x75, 0x6e, 0x70, 0x61,
	0x69, 0x64, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x6e, 0x70, 0x61, 0x69, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x48, 0x06, 0x52, 0x12, 0x75, 0x6e, 0x70, 0x61, 0x69,
	0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x63, 0x0a, 0x13, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x69, 0x63, 0x6b,
	0x75, 0x70, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x48, 0x07, 0x52,
	0x11, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x41, 0x6c, 0x65,
	0x72, 0x74, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x61, 0x67, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x42,
	0x10, 0x0a, 0x0e, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x6c, 0x65, 0x72,
	0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74,
	0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x75, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x42, 0x15, 0x0a,
	0x13, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x61,
	0x6c, 0x65, 0x72, 0x74, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x75, 0x6e, 0x70, 0x61, 0x69, 0x64, 0x5f,
	0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x42, 0x16, 0x0a,
	0x14, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x5f,
	0x61, 0x6c, 0x65, 0x72, 0x74, 0x22, 0xdc, 0x02, 0x0a, 0x08, 0x50, 0x65, 0x74, 0x41, 0x6c, 0x65,
	0x72, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x54, 0x0a, 0x0e, 0x70, 0x65, 0x74,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x48, 0x00, 0x52, 0x0c,
	0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x88, 0x01, 0x01, 0x12,
	0x53, 0x0a, 0x0d, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x41, 0x6c, 0x65, 0x72,
	0x74, 0x48, 0x01, 0x52, 0x0c, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x41, 0x6c, 0x65, 0x72,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x56, 0x0a, 0x0e, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x63, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x48, 0x02, 0x52, 0x0d, 0x69, 0x6e, 0x63, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f,
	0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x42,
	0x10, 0x0a, 0x0e, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x61, 0x6c, 0x65, 0x72,
	0x74, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x61,
	0x6c, 0x65, 0x72, 0x74, 0x22, 0x6e, 0x0a, 0x0e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x61,
	0x67, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x12, 0x5c, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x54, 0x61, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x54, 0x61, 0x67, 0x73, 0x22, 0xc0, 0x02, 0x0a, 0x0c, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x12, 0x57, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x69, 0x65, 0x77,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x5b,
	0x0a, 0x0e, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x41, 0x6c, 0x65, 0x72,
	0x74, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x70,
	0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x69, 0x65, 0x77, 0x73, 0x1a, 0x7a, 0x0a, 0x0b, 0x50,
	0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x51, 0x0a, 0x08, 0x70, 0x65,
	0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xfa, 0x02, 0x0a, 0x0c, 0x56, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x12, 0x51, 0x0a, 0x08, 0x76, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x2e, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x08, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x73, 0x1a, 0x96, 0x02, 0x0a, 0x0b,
	0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1d, 0x0a, 0x0a, 0x76,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a,
	0x12, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x76, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x64,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x73,
	0x12, 0x3f, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0e,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x42, 0x12, 0x0a, 0x10, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x22, 0x9a, 0x03, 0x0a, 0x0d, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x12, 0x55, 0x0a, 0x09, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x2e, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x09, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0xb1, 0x02,
	0x0a, 0x0c, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1f,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x06, 0x70, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x6e, 0x63, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0e, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64,
	0x12, 0x2c, 0x0a, 0x12, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x69, 0x6e,
	0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x3f, 0x0a, 0x0d, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0c, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x22, 0x70, 0x0a, 0x0f, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x41,
	0x6c, 0x65, 0x72, 0x74, 0x12, 0x5d, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0xe3, 0x01, 0x0a, 0x0f, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x6e, 0x46, 0x69,
	0x6c, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x12, 0x5a, 0x0a, 0x0a, 0x63, 0x6f, 0x66, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x6e,
	0x46, 0x69, 0x6c, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x2e, 0x43, 0x4f, 0x46, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x66, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x88, 0x01, 0x01, 0x22, 0x65, 0x0a, 0x09, 0x43, 0x4f, 0x46, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1a, 0x0a, 0x16, 0x43, 0x4f, 0x46, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a,
	0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07,
	0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x4e, 0x4f, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x04, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63,
	0x6f, 0x66, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xdb, 0x02, 0x0a, 0x0c, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x12, 0x51, 0x0a, 0x08, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x08, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x1a, 0xf7, 0x01,
	0x0a, 0x0b, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x30, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x3f, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00,
	0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65,
	0x88, 0x01, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0x71, 0x0a, 0x09, 0x4e, 0x6f, 0x74, 0x65, 0x41,
	0x6c, 0x65, 0x72, 0x74, 0x12, 0x55, 0x0a, 0x0a, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x6e, 0x6f,
	0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x09, 0x61,
	0x6c, 0x65, 0x72, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x22, 0x6d, 0x0a, 0x16, 0x55, 0x6e,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x41,
	0x6c, 0x65, 0x72, 0x74, 0x12, 0x53, 0x0a, 0x0a, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0a, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x64, 0x0a, 0x12, 0x55, 0x6e, 0x70,
	0x61, 0x69, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x12,
	0x3c, 0x0a, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x00, 0x52, 0x0c, 0x75, 0x6e,
	0x70, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a,
	0x0e, 0x5f, 0x75, 0x6e, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0xd1, 0x01, 0x0a, 0x11, 0x50, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x12, 0x52, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x50, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52,
	0x08, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x1a, 0x68, 0x0a, 0x07, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x42, 0x87, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescOnce sync.Once
	file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescData = file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDesc
)

func file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescGZIP() []byte {
	file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescData)
	})
	return file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDescData
}

var file_moego_models_appointment_v1_check_in_out_alert_defs_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_moego_models_appointment_v1_check_in_out_alert_defs_proto_goTypes = []interface{}{
	(CardOnFileAlert_COFStatus)(0),          // 0: moego.models.appointment.v1.CardOnFileAlert.COFStatus
	(*ClientPetsMapping)(nil),               // 1: moego.models.appointment.v1.ClientPetsMapping
	(*AlertDetail)(nil),                     // 2: moego.models.appointment.v1.AlertDetail
	(*ClientAlert)(nil),                     // 3: moego.models.appointment.v1.ClientAlert
	(*PetAlert)(nil),                        // 4: moego.models.appointment.v1.PetAlert
	(*ClientTagAlert)(nil),                  // 5: moego.models.appointment.v1.ClientTagAlert
	(*PetCodeAlert)(nil),                    // 6: moego.models.appointment.v1.PetCodeAlert
	(*VaccineAlert)(nil),                    // 7: moego.models.appointment.v1.VaccineAlert
	(*IncidentAlert)(nil),                   // 8: moego.models.appointment.v1.IncidentAlert
	(*MembershipAlert)(nil),                 // 9: moego.models.appointment.v1.MembershipAlert
	(*CardOnFileAlert)(nil),                 // 10: moego.models.appointment.v1.CardOnFileAlert
	(*PackageAlert)(nil),                    // 11: moego.models.appointment.v1.PackageAlert
	(*NoteAlert)(nil),                       // 12: moego.models.appointment.v1.NoteAlert
	(*UnsignedAgreementAlert)(nil),          // 13: moego.models.appointment.v1.UnsignedAgreementAlert
	(*UnpaidBalanceAlert)(nil),              // 14: moego.models.appointment.v1.UnpaidBalanceAlert
	(*PickupPersonAlert)(nil),               // 15: moego.models.appointment.v1.PickupPersonAlert
	(*PetCodeAlert_PetCodeView)(nil),        // 16: moego.models.appointment.v1.PetCodeAlert.PetCodeView
	(*VaccineAlert_VaccineView)(nil),        // 17: moego.models.appointment.v1.VaccineAlert.VaccineView
	(*IncidentAlert_IncidentView)(nil),      // 18: moego.models.appointment.v1.IncidentAlert.IncidentView
	(*PackageAlert_PackageView)(nil),        // 19: moego.models.appointment.v1.PackageAlert.PackageView
	(*PickupPersonAlert_Contact)(nil),       // 20: moego.models.appointment.v1.PickupPersonAlert.Contact
	(*v1.BusinessCustomerTagModel)(nil),     // 21: moego.models.business_customer.v1.BusinessCustomerTagModel
	(*v1.BusinessPetCodeView)(nil),          // 22: moego.models.business_customer.v1.BusinessPetCodeView
	(*v11.MembershipSubscriptionModel)(nil), // 23: moego.models.membership.v1.MembershipSubscriptionModel
	(*AppointmentNoteModel)(nil),            // 24: moego.models.appointment.v1.AppointmentNoteModel
	(*v12.AgreementModelSimpleView)(nil),    // 25: moego.models.agreement.v1.AgreementModelSimpleView
	(*money.Money)(nil),                     // 26: google.type.Money
	(*date.Date)(nil),                       // 27: google.type.Date
	(*timestamppb.Timestamp)(nil),           // 28: google.protobuf.Timestamp
}
var file_moego_models_appointment_v1_check_in_out_alert_defs_proto_depIdxs = []int32{
	3,  // 0: moego.models.appointment.v1.AlertDetail.client_alert:type_name -> moego.models.appointment.v1.ClientAlert
	4,  // 1: moego.models.appointment.v1.AlertDetail.pet_alerts:type_name -> moego.models.appointment.v1.PetAlert
	5,  // 2: moego.models.appointment.v1.ClientAlert.client_tag_alert:type_name -> moego.models.appointment.v1.ClientTagAlert
	9,  // 3: moego.models.appointment.v1.ClientAlert.membership_alert:type_name -> moego.models.appointment.v1.MembershipAlert
	11, // 4: moego.models.appointment.v1.ClientAlert.package_alert:type_name -> moego.models.appointment.v1.PackageAlert
	12, // 5: moego.models.appointment.v1.ClientAlert.note_alert:type_name -> moego.models.appointment.v1.NoteAlert
	13, // 6: moego.models.appointment.v1.ClientAlert.unsigned_agreement_alert:type_name -> moego.models.appointment.v1.UnsignedAgreementAlert
	10, // 7: moego.models.appointment.v1.ClientAlert.card_on_file_alert:type_name -> moego.models.appointment.v1.CardOnFileAlert
	14, // 8: moego.models.appointment.v1.ClientAlert.unpaid_balance_alert:type_name -> moego.models.appointment.v1.UnpaidBalanceAlert
	15, // 9: moego.models.appointment.v1.ClientAlert.pickup_person_alert:type_name -> moego.models.appointment.v1.PickupPersonAlert
	6,  // 10: moego.models.appointment.v1.PetAlert.pet_code_alert:type_name -> moego.models.appointment.v1.PetCodeAlert
	7,  // 11: moego.models.appointment.v1.PetAlert.vaccine_alert:type_name -> moego.models.appointment.v1.VaccineAlert
	8,  // 12: moego.models.appointment.v1.PetAlert.incident_alert:type_name -> moego.models.appointment.v1.IncidentAlert
	21, // 13: moego.models.appointment.v1.ClientTagAlert.client_tags:type_name -> moego.models.business_customer.v1.BusinessCustomerTagModel
	22, // 14: moego.models.appointment.v1.PetCodeAlert.pet_codes:type_name -> moego.models.business_customer.v1.BusinessPetCodeView
	16, // 15: moego.models.appointment.v1.PetCodeAlert.pet_code_views:type_name -> moego.models.appointment.v1.PetCodeAlert.PetCodeView
	17, // 16: moego.models.appointment.v1.VaccineAlert.vaccines:type_name -> moego.models.appointment.v1.VaccineAlert.VaccineView
	18, // 17: moego.models.appointment.v1.IncidentAlert.incidents:type_name -> moego.models.appointment.v1.IncidentAlert.IncidentView
	23, // 18: moego.models.appointment.v1.MembershipAlert.subscriptions:type_name -> moego.models.membership.v1.MembershipSubscriptionModel
	0,  // 19: moego.models.appointment.v1.CardOnFileAlert.cof_status:type_name -> moego.models.appointment.v1.CardOnFileAlert.COFStatus
	19, // 20: moego.models.appointment.v1.PackageAlert.packages:type_name -> moego.models.appointment.v1.PackageAlert.PackageView
	24, // 21: moego.models.appointment.v1.NoteAlert.alert_note:type_name -> moego.models.appointment.v1.AppointmentNoteModel
	25, // 22: moego.models.appointment.v1.UnsignedAgreementAlert.agreements:type_name -> moego.models.agreement.v1.AgreementModelSimpleView
	26, // 23: moego.models.appointment.v1.UnpaidBalanceAlert.unpaid_amount:type_name -> google.type.Money
	20, // 24: moego.models.appointment.v1.PickupPersonAlert.contacts:type_name -> moego.models.appointment.v1.PickupPersonAlert.Contact
	22, // 25: moego.models.appointment.v1.PetCodeAlert.PetCodeView.pet_code:type_name -> moego.models.business_customer.v1.BusinessPetCodeView
	27, // 26: moego.models.appointment.v1.VaccineAlert.VaccineView.expiration_date:type_name -> google.type.Date
	28, // 27: moego.models.appointment.v1.IncidentAlert.IncidentView.incident_time:type_name -> google.protobuf.Timestamp
	27, // 28: moego.models.appointment.v1.PackageAlert.PackageView.start_date:type_name -> google.type.Date
	27, // 29: moego.models.appointment.v1.PackageAlert.PackageView.expiration_date:type_name -> google.type.Date
	30, // [30:30] is the sub-list for method output_type
	30, // [30:30] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_moego_models_appointment_v1_check_in_out_alert_defs_proto_init() }
func file_moego_models_appointment_v1_check_in_out_alert_defs_proto_init() {
	if File_moego_models_appointment_v1_check_in_out_alert_defs_proto != nil {
		return
	}
	file_moego_models_appointment_v1_appointment_note_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientPetsMapping); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlertDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientAlert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetAlert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientTagAlert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetCodeAlert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VaccineAlert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncidentAlert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MembershipAlert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardOnFileAlert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PackageAlert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoteAlert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnsignedAgreementAlert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnpaidBalanceAlert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PickupPersonAlert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetCodeAlert_PetCodeView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VaccineAlert_VaccineView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncidentAlert_IncidentView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PackageAlert_PackageView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PickupPersonAlert_Contact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[11].OneofWrappers = []interface{}{}
	file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[16].OneofWrappers = []interface{}{}
	file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes[18].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_appointment_v1_check_in_out_alert_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_appointment_v1_check_in_out_alert_defs_proto_depIdxs,
		EnumInfos:         file_moego_models_appointment_v1_check_in_out_alert_defs_proto_enumTypes,
		MessageInfos:      file_moego_models_appointment_v1_check_in_out_alert_defs_proto_msgTypes,
	}.Build()
	File_moego_models_appointment_v1_check_in_out_alert_defs_proto = out.File
	file_moego_models_appointment_v1_check_in_out_alert_defs_proto_rawDesc = nil
	file_moego_models_appointment_v1_check_in_out_alert_defs_proto_goTypes = nil
	file_moego_models_appointment_v1_check_in_out_alert_defs_proto_depIdxs = nil
}
