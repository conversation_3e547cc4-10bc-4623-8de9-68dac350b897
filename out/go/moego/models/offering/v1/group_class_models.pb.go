// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/offering/v1/group_class_models.proto

package offeringpb

import (
	datetime "google.golang.org/genproto/googleapis/type/datetime"
	dayofweek "google.golang.org/genproto/googleapis/type/dayofweek"
	interval "google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// status
type GroupClassInstance_Status int32

const (
	// The status is unspecified
	GroupClassInstance_STATUS_UNSPECIFIED GroupClassInstance_Status = 0
	// upcoming
	GroupClassInstance_UPCOMING GroupClassInstance_Status = 1
	// in progress
	GroupClassInstance_IN_PROGRESS GroupClassInstance_Status = 2
	// past
	GroupClassInstance_PAST GroupClassInstance_Status = 3
)

// Enum value maps for GroupClassInstance_Status.
var (
	GroupClassInstance_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "UPCOMING",
		2: "IN_PROGRESS",
		3: "PAST",
	}
	GroupClassInstance_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"UPCOMING":           1,
		"IN_PROGRESS":        2,
		"PAST":               3,
	}
)

func (x GroupClassInstance_Status) Enum() *GroupClassInstance_Status {
	p := new(GroupClassInstance_Status)
	*p = x
	return p
}

func (x GroupClassInstance_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GroupClassInstance_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v1_group_class_models_proto_enumTypes[0].Descriptor()
}

func (GroupClassInstance_Status) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v1_group_class_models_proto_enumTypes[0]
}

func (x GroupClassInstance_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GroupClassInstance_Status.Descriptor instead.
func (GroupClassInstance_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_group_class_models_proto_rawDescGZIP(), []int{0, 0}
}

// repeat type
type GroupClassInstance_Occurrence_IntervalType int32

const (
	// the type is unspecified
	GroupClassInstance_Occurrence_REPEAT_TYPE_UNSPECIFIED GroupClassInstance_Occurrence_IntervalType = 0
	// day
	GroupClassInstance_Occurrence_DAY GroupClassInstance_Occurrence_IntervalType = 1
	// week
	GroupClassInstance_Occurrence_WEEK GroupClassInstance_Occurrence_IntervalType = 2
	// month
	GroupClassInstance_Occurrence_MONTH GroupClassInstance_Occurrence_IntervalType = 3
)

// Enum value maps for GroupClassInstance_Occurrence_IntervalType.
var (
	GroupClassInstance_Occurrence_IntervalType_name = map[int32]string{
		0: "REPEAT_TYPE_UNSPECIFIED",
		1: "DAY",
		2: "WEEK",
		3: "MONTH",
	}
	GroupClassInstance_Occurrence_IntervalType_value = map[string]int32{
		"REPEAT_TYPE_UNSPECIFIED": 0,
		"DAY":                     1,
		"WEEK":                    2,
		"MONTH":                   3,
	}
)

func (x GroupClassInstance_Occurrence_IntervalType) Enum() *GroupClassInstance_Occurrence_IntervalType {
	p := new(GroupClassInstance_Occurrence_IntervalType)
	*p = x
	return p
}

func (x GroupClassInstance_Occurrence_IntervalType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GroupClassInstance_Occurrence_IntervalType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v1_group_class_models_proto_enumTypes[1].Descriptor()
}

func (GroupClassInstance_Occurrence_IntervalType) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v1_group_class_models_proto_enumTypes[1]
}

func (x GroupClassInstance_Occurrence_IntervalType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GroupClassInstance_Occurrence_IntervalType.Descriptor instead.
func (GroupClassInstance_Occurrence_IntervalType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_group_class_models_proto_rawDescGZIP(), []int{0, 0, 0}
}

// selection type
type GroupClassInstance_Occurrence_Month_SelectionType int32

const (
	// the type is unspecified
	GroupClassInstance_Occurrence_Month_SELECTION_TYPE_UNSPECIFIED GroupClassInstance_Occurrence_Month_SelectionType = 0
	// on day 25
	GroupClassInstance_Occurrence_Month_ON_25TH GroupClassInstance_Occurrence_Month_SelectionType = 1
	// on the third Wednesday
	GroupClassInstance_Occurrence_Month_ON_THIRD_WEDNESDAY GroupClassInstance_Occurrence_Month_SelectionType = 2
)

// Enum value maps for GroupClassInstance_Occurrence_Month_SelectionType.
var (
	GroupClassInstance_Occurrence_Month_SelectionType_name = map[int32]string{
		0: "SELECTION_TYPE_UNSPECIFIED",
		1: "ON_25TH",
		2: "ON_THIRD_WEDNESDAY",
	}
	GroupClassInstance_Occurrence_Month_SelectionType_value = map[string]int32{
		"SELECTION_TYPE_UNSPECIFIED": 0,
		"ON_25TH":                    1,
		"ON_THIRD_WEDNESDAY":         2,
	}
)

func (x GroupClassInstance_Occurrence_Month_SelectionType) Enum() *GroupClassInstance_Occurrence_Month_SelectionType {
	p := new(GroupClassInstance_Occurrence_Month_SelectionType)
	*p = x
	return p
}

func (x GroupClassInstance_Occurrence_Month_SelectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GroupClassInstance_Occurrence_Month_SelectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v1_group_class_models_proto_enumTypes[2].Descriptor()
}

func (GroupClassInstance_Occurrence_Month_SelectionType) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v1_group_class_models_proto_enumTypes[2]
}

func (x GroupClassInstance_Occurrence_Month_SelectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GroupClassInstance_Occurrence_Month_SelectionType.Descriptor instead.
func (GroupClassInstance_Occurrence_Month_SelectionType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_group_class_models_proto_rawDescGZIP(), []int{0, 0, 2, 0}
}

// status
type GroupClassSession_Status int32

const (
	// The status is unspecified
	GroupClassSession_STATUS_UNSPECIFIED GroupClassSession_Status = 0
	// upcoming
	GroupClassSession_UPCOMING GroupClassSession_Status = 1
	// in progress
	GroupClassSession_IN_PROGRESS GroupClassSession_Status = 2
	// past
	GroupClassSession_PAST GroupClassSession_Status = 3
)

// Enum value maps for GroupClassSession_Status.
var (
	GroupClassSession_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "UPCOMING",
		2: "IN_PROGRESS",
		3: "PAST",
	}
	GroupClassSession_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"UPCOMING":           1,
		"IN_PROGRESS":        2,
		"PAST":               3,
	}
)

func (x GroupClassSession_Status) Enum() *GroupClassSession_Status {
	p := new(GroupClassSession_Status)
	*p = x
	return p
}

func (x GroupClassSession_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GroupClassSession_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v1_group_class_models_proto_enumTypes[3].Descriptor()
}

func (GroupClassSession_Status) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v1_group_class_models_proto_enumTypes[3]
}

func (x GroupClassSession_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GroupClassSession_Status.Descriptor instead.
func (GroupClassSession_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_group_class_models_proto_rawDescGZIP(), []int{1, 0}
}

// training group class instance
type GroupClassInstance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company ID
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business ID
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The ID of the training group class
	GroupClassId int64 `protobuf:"varint,4,opt,name=group_class_id,json=groupClassId,proto3" json:"group_class_id,omitempty"`
	// The name
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,6,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// The start time
	StartTime *datetime.DateTime `protobuf:"bytes,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// capacity
	Capacity int32 `protobuf:"varint,8,opt,name=capacity,proto3" json:"capacity,omitempty"`
	// occurrence
	Occurrence *GroupClassInstance_Occurrence `protobuf:"bytes,9,opt,name=occurrence,proto3" json:"occurrence,omitempty"`
	// status
	Status GroupClassInstance_Status `protobuf:"varint,10,opt,name=status,proto3,enum=moego.models.offering.v1.GroupClassInstance_Status" json:"status,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,11,opt,name=price,proto3" json:"price,omitempty"`
}

func (x *GroupClassInstance) Reset() {
	*x = GroupClassInstance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_group_class_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupClassInstance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupClassInstance) ProtoMessage() {}

func (x *GroupClassInstance) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_group_class_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupClassInstance.ProtoReflect.Descriptor instead.
func (*GroupClassInstance) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_group_class_models_proto_rawDescGZIP(), []int{0}
}

func (x *GroupClassInstance) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GroupClassInstance) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GroupClassInstance) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GroupClassInstance) GetGroupClassId() int64 {
	if x != nil {
		return x.GroupClassId
	}
	return 0
}

func (x *GroupClassInstance) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GroupClassInstance) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *GroupClassInstance) GetStartTime() *datetime.DateTime {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *GroupClassInstance) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

func (x *GroupClassInstance) GetOccurrence() *GroupClassInstance_Occurrence {
	if x != nil {
		return x.Occurrence
	}
	return nil
}

func (x *GroupClassInstance) GetStatus() GroupClassInstance_Status {
	if x != nil {
		return x.Status
	}
	return GroupClassInstance_STATUS_UNSPECIFIED
}

func (x *GroupClassInstance) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

// training session
type GroupClassSession struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company ID
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business ID
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// training instance id
	GroupClassInstanceId int64 `protobuf:"varint,4,opt,name=group_class_instance_id,json=groupClassInstanceId,proto3" json:"group_class_instance_id,omitempty"`
	// interval
	Interval *interval.Interval `protobuf:"bytes,5,opt,name=interval,proto3" json:"interval,omitempty"`
	// duration
	Duration *durationpb.Duration `protobuf:"bytes,6,opt,name=duration,proto3" json:"duration,omitempty"`
	// is modified
	IsModified bool `protobuf:"varint,7,opt,name=is_modified,json=isModified,proto3" json:"is_modified,omitempty"`
	// status
	Status GroupClassSession_Status `protobuf:"varint,8,opt,name=status,proto3,enum=moego.models.offering.v1.GroupClassSession_Status" json:"status,omitempty"`
}

func (x *GroupClassSession) Reset() {
	*x = GroupClassSession{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_group_class_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupClassSession) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupClassSession) ProtoMessage() {}

func (x *GroupClassSession) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_group_class_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupClassSession.ProtoReflect.Descriptor instead.
func (*GroupClassSession) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_group_class_models_proto_rawDescGZIP(), []int{1}
}

func (x *GroupClassSession) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GroupClassSession) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GroupClassSession) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GroupClassSession) GetGroupClassInstanceId() int64 {
	if x != nil {
		return x.GroupClassInstanceId
	}
	return 0
}

func (x *GroupClassSession) GetInterval() *interval.Interval {
	if x != nil {
		return x.Interval
	}
	return nil
}

func (x *GroupClassSession) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *GroupClassSession) GetIsModified() bool {
	if x != nil {
		return x.IsModified
	}
	return false
}

func (x *GroupClassSession) GetStatus() GroupClassSession_Status {
	if x != nil {
		return x.Status
	}
	return GroupClassSession_STATUS_UNSPECIFIED
}

// The group class model
type GroupClassModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The class id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The class name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// description
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,4,opt,name=price,proto3" json:"price,omitempty"`
	// number of sessions
	NumSessions int32 `protobuf:"varint,5,opt,name=num_sessions,json=numSessions,proto3" json:"num_sessions,omitempty"`
	// duration in minutes
	DurationSessionMin int32 `protobuf:"varint,6,opt,name=duration_session_min,json=durationSessionMin,proto3" json:"duration_session_min,omitempty"`
}

func (x *GroupClassModel) Reset() {
	*x = GroupClassModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_group_class_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupClassModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupClassModel) ProtoMessage() {}

func (x *GroupClassModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_group_class_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupClassModel.ProtoReflect.Descriptor instead.
func (*GroupClassModel) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_group_class_models_proto_rawDescGZIP(), []int{2}
}

func (x *GroupClassModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GroupClassModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GroupClassModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GroupClassModel) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *GroupClassModel) GetNumSessions() int32 {
	if x != nil {
		return x.NumSessions
	}
	return 0
}

func (x *GroupClassModel) GetDurationSessionMin() int32 {
	if x != nil {
		return x.DurationSessionMin
	}
	return 0
}

// occurrence
type GroupClassInstance_Occurrence struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The type of occurrence
	Type GroupClassInstance_Occurrence_IntervalType `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.offering.v1.GroupClassInstance_Occurrence_IntervalType" json:"type,omitempty"`
	// interval, repeat every *interval*
	Interval int32 `protobuf:"varint,2,opt,name=interval,proto3" json:"interval,omitempty"`
	// the detail
	//
	// Types that are assignable to IntervalUnit:
	//
	//	*GroupClassInstance_Occurrence_Day_
	//	*GroupClassInstance_Occurrence_Week_
	//	*GroupClassInstance_Occurrence_Month_
	IntervalUnit isGroupClassInstance_Occurrence_IntervalUnit `protobuf_oneof:"interval_unit"`
}

func (x *GroupClassInstance_Occurrence) Reset() {
	*x = GroupClassInstance_Occurrence{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_group_class_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupClassInstance_Occurrence) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupClassInstance_Occurrence) ProtoMessage() {}

func (x *GroupClassInstance_Occurrence) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_group_class_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupClassInstance_Occurrence.ProtoReflect.Descriptor instead.
func (*GroupClassInstance_Occurrence) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_group_class_models_proto_rawDescGZIP(), []int{0, 0}
}

func (x *GroupClassInstance_Occurrence) GetType() GroupClassInstance_Occurrence_IntervalType {
	if x != nil {
		return x.Type
	}
	return GroupClassInstance_Occurrence_REPEAT_TYPE_UNSPECIFIED
}

func (x *GroupClassInstance_Occurrence) GetInterval() int32 {
	if x != nil {
		return x.Interval
	}
	return 0
}

func (m *GroupClassInstance_Occurrence) GetIntervalUnit() isGroupClassInstance_Occurrence_IntervalUnit {
	if m != nil {
		return m.IntervalUnit
	}
	return nil
}

func (x *GroupClassInstance_Occurrence) GetDay() *GroupClassInstance_Occurrence_Day {
	if x, ok := x.GetIntervalUnit().(*GroupClassInstance_Occurrence_Day_); ok {
		return x.Day
	}
	return nil
}

func (x *GroupClassInstance_Occurrence) GetWeek() *GroupClassInstance_Occurrence_Week {
	if x, ok := x.GetIntervalUnit().(*GroupClassInstance_Occurrence_Week_); ok {
		return x.Week
	}
	return nil
}

func (x *GroupClassInstance_Occurrence) GetMonth() *GroupClassInstance_Occurrence_Month {
	if x, ok := x.GetIntervalUnit().(*GroupClassInstance_Occurrence_Month_); ok {
		return x.Month
	}
	return nil
}

type isGroupClassInstance_Occurrence_IntervalUnit interface {
	isGroupClassInstance_Occurrence_IntervalUnit()
}

type GroupClassInstance_Occurrence_Day_ struct {
	// day
	Day *GroupClassInstance_Occurrence_Day `protobuf:"bytes,3,opt,name=day,proto3,oneof"`
}

type GroupClassInstance_Occurrence_Week_ struct {
	// week
	Week *GroupClassInstance_Occurrence_Week `protobuf:"bytes,4,opt,name=week,proto3,oneof"`
}

type GroupClassInstance_Occurrence_Month_ struct {
	// month
	Month *GroupClassInstance_Occurrence_Month `protobuf:"bytes,5,opt,name=month,proto3,oneof"`
}

func (*GroupClassInstance_Occurrence_Day_) isGroupClassInstance_Occurrence_IntervalUnit() {}

func (*GroupClassInstance_Occurrence_Week_) isGroupClassInstance_Occurrence_IntervalUnit() {}

func (*GroupClassInstance_Occurrence_Month_) isGroupClassInstance_Occurrence_IntervalUnit() {}

// day
type GroupClassInstance_Occurrence_Day struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GroupClassInstance_Occurrence_Day) Reset() {
	*x = GroupClassInstance_Occurrence_Day{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_group_class_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupClassInstance_Occurrence_Day) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupClassInstance_Occurrence_Day) ProtoMessage() {}

func (x *GroupClassInstance_Occurrence_Day) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_group_class_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupClassInstance_Occurrence_Day.ProtoReflect.Descriptor instead.
func (*GroupClassInstance_Occurrence_Day) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_group_class_models_proto_rawDescGZIP(), []int{0, 0, 0}
}

// week
type GroupClassInstance_Occurrence_Week struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// day of week
	DayOfWeeks []dayofweek.DayOfWeek `protobuf:"varint,1,rep,packed,name=day_of_weeks,json=dayOfWeeks,proto3,enum=google.type.DayOfWeek" json:"day_of_weeks,omitempty"`
}

func (x *GroupClassInstance_Occurrence_Week) Reset() {
	*x = GroupClassInstance_Occurrence_Week{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_group_class_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupClassInstance_Occurrence_Week) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupClassInstance_Occurrence_Week) ProtoMessage() {}

func (x *GroupClassInstance_Occurrence_Week) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_group_class_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupClassInstance_Occurrence_Week.ProtoReflect.Descriptor instead.
func (*GroupClassInstance_Occurrence_Week) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_group_class_models_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *GroupClassInstance_Occurrence_Week) GetDayOfWeeks() []dayofweek.DayOfWeek {
	if x != nil {
		return x.DayOfWeeks
	}
	return nil
}

// month
// 为了前端快速上线，暂时抛弃通用性，
// 但同时预留后续扩展能力
type GroupClassInstance_Occurrence_Month struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selection type
	SelectionType GroupClassInstance_Occurrence_Month_SelectionType `protobuf:"varint,1,opt,name=selection_type,json=selectionType,proto3,enum=moego.models.offering.v1.GroupClassInstance_Occurrence_Month_SelectionType" json:"selection_type,omitempty"`
}

func (x *GroupClassInstance_Occurrence_Month) Reset() {
	*x = GroupClassInstance_Occurrence_Month{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_group_class_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupClassInstance_Occurrence_Month) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupClassInstance_Occurrence_Month) ProtoMessage() {}

func (x *GroupClassInstance_Occurrence_Month) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_group_class_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupClassInstance_Occurrence_Month.ProtoReflect.Descriptor instead.
func (*GroupClassInstance_Occurrence_Month) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_group_class_models_proto_rawDescGZIP(), []int{0, 0, 2}
}

func (x *GroupClassInstance_Occurrence_Month) GetSelectionType() GroupClassInstance_Occurrence_Month_SelectionType {
	if x != nil {
		return x.SelectionType
	}
	return GroupClassInstance_Occurrence_Month_SELECTION_TYPE_UNSPECIFIED
}

var File_moego_models_offering_v1_group_class_models_proto protoreflect.FileDescriptor

var file_moego_models_offering_v1_group_class_models_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x79, 0x6f, 0x66, 0x77, 0x65, 0x65, 0x6b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f,
	0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa0, 0x0a, 0x0a, 0x12,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x57, 0x0a, 0x0a, 0x6f, 0x63, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x4f, 0x63, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x12, 0x4b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x28, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x1a, 0xf7, 0x05, 0x0a, 0x0a, 0x4f, 0x63,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x58, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x4f, 0x63, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x4f,
	0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x4f, 0x63, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x2e, 0x44, 0x61, 0x79, 0x48, 0x00, 0x52, 0x03, 0x64, 0x61, 0x79, 0x12,
	0x52, 0x0a, 0x04, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x4f, 0x63, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x57, 0x65, 0x65, 0x6b, 0x48, 0x00, 0x52, 0x04, 0x77,
	0x65, 0x65, 0x6b, 0x12, 0x55, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x2e, 0x4f, 0x63, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x48, 0x00, 0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x1a, 0x05, 0x0a, 0x03, 0x44, 0x61,
	0x79, 0x1a, 0x40, 0x0a, 0x04, 0x57, 0x65, 0x65, 0x6b, 0x12, 0x38, 0x0a, 0x0c, 0x64, 0x61, 0x79,
	0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x0a, 0x64, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65,
	0x65, 0x6b, 0x73, 0x1a, 0xd1, 0x01, 0x0a, 0x05, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x72, 0x0a,
	0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x4f, 0x63, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0d, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x54, 0x0a, 0x0d, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x4e, 0x5f, 0x32, 0x35, 0x54, 0x48, 0x10, 0x01, 0x12,
	0x16, 0x0a, 0x12, 0x4f, 0x4e, 0x5f, 0x54, 0x48, 0x49, 0x52, 0x44, 0x5f, 0x57, 0x45, 0x44, 0x4e,
	0x45, 0x53, 0x44, 0x41, 0x59, 0x10, 0x02, 0x22, 0x49, 0x0a, 0x0c, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x50, 0x45, 0x41,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x44, 0x41, 0x59, 0x10, 0x01, 0x12, 0x08, 0x0a,
	0x04, 0x57, 0x45, 0x45, 0x4b, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x4f, 0x4e, 0x54, 0x48,
	0x10, 0x03, 0x42, 0x0f, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x22, 0x49, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a,
	0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x43, 0x4f, 0x4d, 0x49, 0x4e,
	0x47, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45,
	0x53, 0x53, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x41, 0x53, 0x54, 0x10, 0x03, 0x22, 0xbc,
	0x03, 0x0a, 0x11, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x17, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x08, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x35,
	0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x4a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x49, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x43, 0x4f, 0x4d, 0x49, 0x4e, 0x47,
	0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53,
	0x53, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x41, 0x53, 0x54, 0x10, 0x03, 0x22, 0xc2, 0x01,
	0x0a, 0x0f, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x6e, 0x75, 0x6d, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x30, 0x0a, 0x14, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d,
	0x69, 0x6e, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_offering_v1_group_class_models_proto_rawDescOnce sync.Once
	file_moego_models_offering_v1_group_class_models_proto_rawDescData = file_moego_models_offering_v1_group_class_models_proto_rawDesc
)

func file_moego_models_offering_v1_group_class_models_proto_rawDescGZIP() []byte {
	file_moego_models_offering_v1_group_class_models_proto_rawDescOnce.Do(func() {
		file_moego_models_offering_v1_group_class_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_offering_v1_group_class_models_proto_rawDescData)
	})
	return file_moego_models_offering_v1_group_class_models_proto_rawDescData
}

var file_moego_models_offering_v1_group_class_models_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_moego_models_offering_v1_group_class_models_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_models_offering_v1_group_class_models_proto_goTypes = []interface{}{
	(GroupClassInstance_Status)(0),                         // 0: moego.models.offering.v1.GroupClassInstance.Status
	(GroupClassInstance_Occurrence_IntervalType)(0),        // 1: moego.models.offering.v1.GroupClassInstance.Occurrence.IntervalType
	(GroupClassInstance_Occurrence_Month_SelectionType)(0), // 2: moego.models.offering.v1.GroupClassInstance.Occurrence.Month.SelectionType
	(GroupClassSession_Status)(0),                          // 3: moego.models.offering.v1.GroupClassSession.Status
	(*GroupClassInstance)(nil),                             // 4: moego.models.offering.v1.GroupClassInstance
	(*GroupClassSession)(nil),                              // 5: moego.models.offering.v1.GroupClassSession
	(*GroupClassModel)(nil),                                // 6: moego.models.offering.v1.GroupClassModel
	(*GroupClassInstance_Occurrence)(nil),                  // 7: moego.models.offering.v1.GroupClassInstance.Occurrence
	(*GroupClassInstance_Occurrence_Day)(nil),              // 8: moego.models.offering.v1.GroupClassInstance.Occurrence.Day
	(*GroupClassInstance_Occurrence_Week)(nil),             // 9: moego.models.offering.v1.GroupClassInstance.Occurrence.Week
	(*GroupClassInstance_Occurrence_Month)(nil),            // 10: moego.models.offering.v1.GroupClassInstance.Occurrence.Month
	(*datetime.DateTime)(nil),                              // 11: google.type.DateTime
	(*money.Money)(nil),                                    // 12: google.type.Money
	(*interval.Interval)(nil),                              // 13: google.type.Interval
	(*durationpb.Duration)(nil),                            // 14: google.protobuf.Duration
	(dayofweek.DayOfWeek)(0),                               // 15: google.type.DayOfWeek
}
var file_moego_models_offering_v1_group_class_models_proto_depIdxs = []int32{
	11, // 0: moego.models.offering.v1.GroupClassInstance.start_time:type_name -> google.type.DateTime
	7,  // 1: moego.models.offering.v1.GroupClassInstance.occurrence:type_name -> moego.models.offering.v1.GroupClassInstance.Occurrence
	0,  // 2: moego.models.offering.v1.GroupClassInstance.status:type_name -> moego.models.offering.v1.GroupClassInstance.Status
	12, // 3: moego.models.offering.v1.GroupClassInstance.price:type_name -> google.type.Money
	13, // 4: moego.models.offering.v1.GroupClassSession.interval:type_name -> google.type.Interval
	14, // 5: moego.models.offering.v1.GroupClassSession.duration:type_name -> google.protobuf.Duration
	3,  // 6: moego.models.offering.v1.GroupClassSession.status:type_name -> moego.models.offering.v1.GroupClassSession.Status
	1,  // 7: moego.models.offering.v1.GroupClassInstance.Occurrence.type:type_name -> moego.models.offering.v1.GroupClassInstance.Occurrence.IntervalType
	8,  // 8: moego.models.offering.v1.GroupClassInstance.Occurrence.day:type_name -> moego.models.offering.v1.GroupClassInstance.Occurrence.Day
	9,  // 9: moego.models.offering.v1.GroupClassInstance.Occurrence.week:type_name -> moego.models.offering.v1.GroupClassInstance.Occurrence.Week
	10, // 10: moego.models.offering.v1.GroupClassInstance.Occurrence.month:type_name -> moego.models.offering.v1.GroupClassInstance.Occurrence.Month
	15, // 11: moego.models.offering.v1.GroupClassInstance.Occurrence.Week.day_of_weeks:type_name -> google.type.DayOfWeek
	2,  // 12: moego.models.offering.v1.GroupClassInstance.Occurrence.Month.selection_type:type_name -> moego.models.offering.v1.GroupClassInstance.Occurrence.Month.SelectionType
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_moego_models_offering_v1_group_class_models_proto_init() }
func file_moego_models_offering_v1_group_class_models_proto_init() {
	if File_moego_models_offering_v1_group_class_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_offering_v1_group_class_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupClassInstance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_group_class_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupClassSession); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_group_class_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupClassModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_group_class_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupClassInstance_Occurrence); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_group_class_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupClassInstance_Occurrence_Day); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_group_class_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupClassInstance_Occurrence_Week); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_group_class_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupClassInstance_Occurrence_Month); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_offering_v1_group_class_models_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*GroupClassInstance_Occurrence_Day_)(nil),
		(*GroupClassInstance_Occurrence_Week_)(nil),
		(*GroupClassInstance_Occurrence_Month_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_offering_v1_group_class_models_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_offering_v1_group_class_models_proto_goTypes,
		DependencyIndexes: file_moego_models_offering_v1_group_class_models_proto_depIdxs,
		EnumInfos:         file_moego_models_offering_v1_group_class_models_proto_enumTypes,
		MessageInfos:      file_moego_models_offering_v1_group_class_models_proto_msgTypes,
	}.Build()
	File_moego_models_offering_v1_group_class_models_proto = out.File
	file_moego_models_offering_v1_group_class_models_proto_rawDesc = nil
	file_moego_models_offering_v1_group_class_models_proto_goTypes = nil
	file_moego_models_offering_v1_group_class_models_proto_depIdxs = nil
}
