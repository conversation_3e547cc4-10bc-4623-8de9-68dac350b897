// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/offering/v1/service_models.proto

package offeringpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// source
type ServiceModel_Source int32

const (
	// source is not set
	ServiceModel_SOURCE_UNSPECIFIED ServiceModel_Source = 0
	// source is from MoeGo platform (e.x. b web/app)
	ServiceModel_MOEGO_PLATFORM ServiceModel_Source = 1
	// source is from Enterprise Hub
	ServiceModel_ENTERPRISE_HUB ServiceModel_Source = 2
)

// Enum value maps for ServiceModel_Source.
var (
	ServiceModel_Source_name = map[int32]string{
		0: "SOURCE_UNSPECIFIED",
		1: "MOEGO_PLATFORM",
		2: "ENTERPRISE_HUB",
	}
	ServiceModel_Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED": 0,
		"MOEGO_PLATFORM":     1,
		"ENTERPRISE_HUB":     2,
	}
)

func (x ServiceModel_Source) Enum() *ServiceModel_Source {
	p := new(ServiceModel_Source)
	*p = x
	return p
}

func (x ServiceModel_Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceModel_Source) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v1_service_models_proto_enumTypes[0].Descriptor()
}

func (ServiceModel_Source) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v1_service_models_proto_enumTypes[0]
}

func (x ServiceModel_Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceModel_Source.Descriptor instead.
func (ServiceModel_Source) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{1, 0}
}

// service category model
type ServiceCategoryModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// category id
	CategoryId int64 `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// category name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// service list
	Services []*ServiceModel `protobuf:"bytes,3,rep,name=services,proto3" json:"services,omitempty"`
}

func (x *ServiceCategoryModel) Reset() {
	*x = ServiceCategoryModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceCategoryModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCategoryModel) ProtoMessage() {}

func (x *ServiceCategoryModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCategoryModel.ProtoReflect.Descriptor instead.
func (*ServiceCategoryModel) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceCategoryModel) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *ServiceCategoryModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceCategoryModel) GetServices() []*ServiceModel {
	if x != nil {
		return x.Services
	}
	return nil
}

// service model
type ServiceModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// description
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// inactive
	Inactive bool `protobuf:"varint,4,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// images
	Images []string `protobuf:"bytes,5,rep,name=images,proto3" json:"images,omitempty"`
	// category id
	CategoryId int64 `protobuf:"varint,6,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,7,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// location override rules
	//
	// Deprecated: Do not use.
	LocationOverrideList []*LocationOverrideRule `protobuf:"bytes,8,rep,name=location_override_list,json=locationOverrideList,proto3" json:"location_override_list,omitempty"`
	// require dedicated staff
	RequireDedicatedStaff bool `protobuf:"varint,9,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3" json:"require_dedicated_staff,omitempty"`
	// service type
	ServiceItemType ServiceItemType `protobuf:"varint,10,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,11,opt,name=price,proto3" json:"price,omitempty"`
	// price unit
	PriceUnit ServicePriceUnit `protobuf:"varint,12,opt,name=price_unit,json=priceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"price_unit,omitempty"`
	// tax id
	TaxId int64 `protobuf:"varint,13,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// duration
	Duration int32 `protobuf:"varint,14,opt,name=duration,proto3" json:"duration,omitempty"`
	// whether add to commission base
	AddToCommissionBase bool `protobuf:"varint,15,opt,name=add_to_commission_base,json=addToCommissionBase,proto3" json:"add_to_commission_base,omitempty"`
	// whether can tip
	CanTip bool `protobuf:"varint,16,opt,name=can_tip,json=canTip,proto3" json:"can_tip,omitempty"`
	// whether the service is available for all locations
	IsAllLocation bool `protobuf:"varint,17,opt,name=is_all_location,json=isAllLocation,proto3" json:"is_all_location,omitempty"`
	// available locations (only if is_available_in_all_locations is false)
	AvailableBusinessIdList []int64 `protobuf:"varint,18,rep,packed,name=available_business_id_list,json=availableBusinessIdList,proto3" json:"available_business_id_list,omitempty"`
	// whether the service is available for all pet type & breed
	BreedFilter bool `protobuf:"varint,19,opt,name=breed_filter,json=breedFilter,proto3" json:"breed_filter,omitempty"`
	// available pet type with pet breed (only if is_available_for_all_pet_type_and_breed is false)
	CustomizedBreed []*CustomizedBreed `protobuf:"bytes,20,rep,name=customized_breed,json=customizedBreed,proto3" json:"customized_breed,omitempty"`
	// available for all pet size
	PetSizeFilter bool `protobuf:"varint,21,opt,name=pet_size_filter,json=petSizeFilter,proto3" json:"pet_size_filter,omitempty"`
	// available pet size (only if is_available_for_all_pet_size is false)
	CustomizedPetSizes []int64 `protobuf:"varint,22,rep,packed,name=customized_pet_sizes,json=customizedPetSizes,proto3" json:"customized_pet_sizes,omitempty"`
	// weight filter only for compatible with old version, use pet_size in new version
	//
	// Deprecated: Do not use.
	WeightFilter bool `protobuf:"varint,23,opt,name=weight_filter,json=weightFilter,proto3" json:"weight_filter,omitempty"`
	// weight range (only if weight_filter is true)
	//
	// Deprecated: Do not use.
	WeightRange []float64 `protobuf:"fixed64,24,rep,packed,name=weight_range,json=weightRange,proto3" json:"weight_range,omitempty"`
	// available for all pet coat type
	CoatFilter bool `protobuf:"varint,25,opt,name=coat_filter,json=coatFilter,proto3" json:"coat_filter,omitempty"`
	// available pet coat type (only if is_available_for_all_pet_coat_type is false)
	CustomizedCoat []int64 `protobuf:"varint,26,rep,packed,name=customized_coat,json=customizedCoat,proto3" json:"customized_coat,omitempty"`
	// required dedicated lodging
	RequireDedicatedLodging bool `protobuf:"varint,27,opt,name=require_dedicated_lodging,json=requireDedicatedLodging,proto3" json:"require_dedicated_lodging,omitempty"`
	// whether the service is available for all lodging(only if require_dedicated_lodging is true)
	LodgingFilter bool `protobuf:"varint,28,opt,name=lodging_filter,json=lodgingFilter,proto3" json:"lodging_filter,omitempty"`
	// available lodging ids(only if require_dedicated_lodging is true and available_for_all_lodgings is false)
	CustomizedLodgings []int64 `protobuf:"varint,29,rep,packed,name=customized_lodgings,json=customizedLodgings,proto3" json:"customized_lodgings,omitempty"`
	// whether the add on is available for all services(only for add on)
	ServiceFilter bool `protobuf:"varint,30,opt,name=service_filter,json=serviceFilter,proto3" json:"service_filter,omitempty"`
	// service filters(only for add on)
	ServiceFilterList []*ServiceFilter `protobuf:"bytes,31,rep,name=service_filter_list,json=serviceFilterList,proto3" json:"service_filter_list,omitempty"`
	// service type
	Type ServiceType `protobuf:"varint,32,opt,name=type,proto3,enum=moego.models.offering.v1.ServiceType" json:"type,omitempty"`
	// max duration (only for daycare service)
	MaxDuration int32 `protobuf:"varint,33,opt,name=max_duration,json=maxDuration,proto3" json:"max_duration,omitempty"`
	// auto rollover rule
	AutoRolloverRule *AutoRolloverRule `protobuf:"bytes,34,opt,name=auto_rollover_rule,json=autoRolloverRule,proto3" json:"auto_rollover_rule,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,35,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,36,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// is deleted
	IsDeleted bool `protobuf:"varint,37,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,38,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// available staffs
	AvailableStaffIdList []int64 `protobuf:"varint,39,rep,packed,name=available_staff_id_list,json=availableStaffIdList,proto3" json:"available_staff_id_list,omitempty"`
	// location staff override rules
	LocationStaffOverrideList []*LocationStaffOverrideRule `protobuf:"bytes,40,rep,name=location_staff_override_list,json=locationStaffOverrideList,proto3" json:"location_staff_override_list,omitempty"`
	// whether the service is available for all staff
	AvailableForAllStaff bool `protobuf:"varint,42,opt,name=available_for_all_staff,json=availableForAllStaff,proto3" json:"available_for_all_staff,omitempty"`
	// pet code filter
	PetCodeFilter *ServiceModel_PetCodeFilter `protobuf:"bytes,43,opt,name=pet_code_filter,json=petCodeFilter,proto3" json:"pet_code_filter,omitempty"`
	// bundle services
	BundleServiceIds []int64 `protobuf:"varint,44,rep,packed,name=bundle_service_ids,json=bundleServiceIds,proto3" json:"bundle_service_ids,omitempty"`
	// source
	Source ServiceModel_Source `protobuf:"varint,45,opt,name=source,proto3,enum=moego.models.offering.v1.ServiceModel_Source" json:"source,omitempty"`
	// number of sessions, only for training
	NumSessions int32 `protobuf:"varint,46,opt,name=num_sessions,json=numSessions,proto3" json:"num_sessions,omitempty"`
	// duration of each session in minutes, only for training
	DurationSessionMin int32 `protobuf:"varint,47,opt,name=duration_session_min,json=durationSessionMin,proto3" json:"duration_session_min,omitempty"`
	// capacity of group class, zero means unlimited, only for training
	Capacity int32 `protobuf:"varint,48,opt,name=capacity,proto3" json:"capacity,omitempty"`
	// whether it require a prerequisite class
	IsRequirePrerequisiteClass bool `protobuf:"varint,50,opt,name=is_require_prerequisite_class,json=isRequirePrerequisiteClass,proto3" json:"is_require_prerequisite_class,omitempty"`
	// prerequisite class ids of training group class, only valid when is_require_prerequisite_class is ture
	PrerequisiteClassIds []int64 `protobuf:"varint,51,rep,packed,name=prerequisite_class_ids,json=prerequisiteClassIds,proto3" json:"prerequisite_class_ids,omitempty"`
	// whether evaluation is required
	IsEvaluationRequired bool `protobuf:"varint,52,opt,name=is_evaluation_required,json=isEvaluationRequired,proto3" json:"is_evaluation_required,omitempty"`
	// whether evaluation is required before online booking
	IsEvaluationRequiredForOb bool `protobuf:"varint,53,opt,name=is_evaluation_required_for_ob,json=isEvaluationRequiredForOb,proto3" json:"is_evaluation_required_for_ob,omitempty"`
	// evaluation id
	EvaluationId int64 `protobuf:"varint,54,opt,name=evaluation_id,json=evaluationId,proto3" json:"evaluation_id,omitempty"`
	// additional service rule
	AdditionalServiceRule *AdditionalServiceRule `protobuf:"bytes,55,opt,name=additional_service_rule,json=additionalServiceRule,proto3,oneof" json:"additional_service_rule,omitempty"`
}

func (x *ServiceModel) Reset() {
	*x = ServiceModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceModel) ProtoMessage() {}

func (x *ServiceModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceModel.ProtoReflect.Descriptor instead.
func (*ServiceModel) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceModel) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ServiceModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ServiceModel) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *ServiceModel) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *ServiceModel) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *ServiceModel) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

// Deprecated: Do not use.
func (x *ServiceModel) GetLocationOverrideList() []*LocationOverrideRule {
	if x != nil {
		return x.LocationOverrideList
	}
	return nil
}

func (x *ServiceModel) GetRequireDedicatedStaff() bool {
	if x != nil {
		return x.RequireDedicatedStaff
	}
	return false
}

func (x *ServiceModel) GetServiceItemType() ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
}

func (x *ServiceModel) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ServiceModel) GetPriceUnit() ServicePriceUnit {
	if x != nil {
		return x.PriceUnit
	}
	return ServicePriceUnit_SERVICE_PRICE_UNIT_UNSPECIFIED
}

func (x *ServiceModel) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *ServiceModel) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *ServiceModel) GetAddToCommissionBase() bool {
	if x != nil {
		return x.AddToCommissionBase
	}
	return false
}

func (x *ServiceModel) GetCanTip() bool {
	if x != nil {
		return x.CanTip
	}
	return false
}

func (x *ServiceModel) GetIsAllLocation() bool {
	if x != nil {
		return x.IsAllLocation
	}
	return false
}

func (x *ServiceModel) GetAvailableBusinessIdList() []int64 {
	if x != nil {
		return x.AvailableBusinessIdList
	}
	return nil
}

func (x *ServiceModel) GetBreedFilter() bool {
	if x != nil {
		return x.BreedFilter
	}
	return false
}

func (x *ServiceModel) GetCustomizedBreed() []*CustomizedBreed {
	if x != nil {
		return x.CustomizedBreed
	}
	return nil
}

func (x *ServiceModel) GetPetSizeFilter() bool {
	if x != nil {
		return x.PetSizeFilter
	}
	return false
}

func (x *ServiceModel) GetCustomizedPetSizes() []int64 {
	if x != nil {
		return x.CustomizedPetSizes
	}
	return nil
}

// Deprecated: Do not use.
func (x *ServiceModel) GetWeightFilter() bool {
	if x != nil {
		return x.WeightFilter
	}
	return false
}

// Deprecated: Do not use.
func (x *ServiceModel) GetWeightRange() []float64 {
	if x != nil {
		return x.WeightRange
	}
	return nil
}

func (x *ServiceModel) GetCoatFilter() bool {
	if x != nil {
		return x.CoatFilter
	}
	return false
}

func (x *ServiceModel) GetCustomizedCoat() []int64 {
	if x != nil {
		return x.CustomizedCoat
	}
	return nil
}

func (x *ServiceModel) GetRequireDedicatedLodging() bool {
	if x != nil {
		return x.RequireDedicatedLodging
	}
	return false
}

func (x *ServiceModel) GetLodgingFilter() bool {
	if x != nil {
		return x.LodgingFilter
	}
	return false
}

func (x *ServiceModel) GetCustomizedLodgings() []int64 {
	if x != nil {
		return x.CustomizedLodgings
	}
	return nil
}

func (x *ServiceModel) GetServiceFilter() bool {
	if x != nil {
		return x.ServiceFilter
	}
	return false
}

func (x *ServiceModel) GetServiceFilterList() []*ServiceFilter {
	if x != nil {
		return x.ServiceFilterList
	}
	return nil
}

func (x *ServiceModel) GetType() ServiceType {
	if x != nil {
		return x.Type
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *ServiceModel) GetMaxDuration() int32 {
	if x != nil {
		return x.MaxDuration
	}
	return 0
}

func (x *ServiceModel) GetAutoRolloverRule() *AutoRolloverRule {
	if x != nil {
		return x.AutoRolloverRule
	}
	return nil
}

func (x *ServiceModel) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ServiceModel) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ServiceModel) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *ServiceModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ServiceModel) GetAvailableStaffIdList() []int64 {
	if x != nil {
		return x.AvailableStaffIdList
	}
	return nil
}

func (x *ServiceModel) GetLocationStaffOverrideList() []*LocationStaffOverrideRule {
	if x != nil {
		return x.LocationStaffOverrideList
	}
	return nil
}

func (x *ServiceModel) GetAvailableForAllStaff() bool {
	if x != nil {
		return x.AvailableForAllStaff
	}
	return false
}

func (x *ServiceModel) GetPetCodeFilter() *ServiceModel_PetCodeFilter {
	if x != nil {
		return x.PetCodeFilter
	}
	return nil
}

func (x *ServiceModel) GetBundleServiceIds() []int64 {
	if x != nil {
		return x.BundleServiceIds
	}
	return nil
}

func (x *ServiceModel) GetSource() ServiceModel_Source {
	if x != nil {
		return x.Source
	}
	return ServiceModel_SOURCE_UNSPECIFIED
}

func (x *ServiceModel) GetNumSessions() int32 {
	if x != nil {
		return x.NumSessions
	}
	return 0
}

func (x *ServiceModel) GetDurationSessionMin() int32 {
	if x != nil {
		return x.DurationSessionMin
	}
	return 0
}

func (x *ServiceModel) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

func (x *ServiceModel) GetIsRequirePrerequisiteClass() bool {
	if x != nil {
		return x.IsRequirePrerequisiteClass
	}
	return false
}

func (x *ServiceModel) GetPrerequisiteClassIds() []int64 {
	if x != nil {
		return x.PrerequisiteClassIds
	}
	return nil
}

func (x *ServiceModel) GetIsEvaluationRequired() bool {
	if x != nil {
		return x.IsEvaluationRequired
	}
	return false
}

func (x *ServiceModel) GetIsEvaluationRequiredForOb() bool {
	if x != nil {
		return x.IsEvaluationRequiredForOb
	}
	return false
}

func (x *ServiceModel) GetEvaluationId() int64 {
	if x != nil {
		return x.EvaluationId
	}
	return 0
}

func (x *ServiceModel) GetAdditionalServiceRule() *AdditionalServiceRule {
	if x != nil {
		return x.AdditionalServiceRule
	}
	return nil
}

// location override rules model
type LocationOverrideRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// price(null for not override)
	Price *float64 `protobuf:"fixed64,2,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// tax(null for not override)
	TaxId *int64 `protobuf:"varint,3,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
	// duration(null for not override)
	Duration *int32 `protobuf:"varint,4,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// max duration(only for daycare service)
	MaxDuration *int32 `protobuf:"varint,5,opt,name=max_duration,json=maxDuration,proto3,oneof" json:"max_duration,omitempty"`
}

func (x *LocationOverrideRule) Reset() {
	*x = LocationOverrideRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationOverrideRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationOverrideRule) ProtoMessage() {}

func (x *LocationOverrideRule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationOverrideRule.ProtoReflect.Descriptor instead.
func (*LocationOverrideRule) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{2}
}

func (x *LocationOverrideRule) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *LocationOverrideRule) GetPrice() float64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *LocationOverrideRule) GetTaxId() int64 {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return 0
}

func (x *LocationOverrideRule) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *LocationOverrideRule) GetMaxDuration() int32 {
	if x != nil && x.MaxDuration != nil {
		return *x.MaxDuration
	}
	return 0
}

// override rule
type ServiceOverrideRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet override rules
	PetOverrideList []*PetOverrideRule `protobuf:"bytes,1,rep,name=pet_override_list,json=petOverrideList,proto3" json:"pet_override_list,omitempty"`
}

func (x *ServiceOverrideRule) Reset() {
	*x = ServiceOverrideRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceOverrideRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceOverrideRule) ProtoMessage() {}

func (x *ServiceOverrideRule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceOverrideRule.ProtoReflect.Descriptor instead.
func (*ServiceOverrideRule) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{3}
}

func (x *ServiceOverrideRule) GetPetOverrideList() []*PetOverrideRule {
	if x != nil {
		return x.PetOverrideList
	}
	return nil
}

// pet override rule
type PetOverrideRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// price(null for not override)
	Price *float64 `protobuf:"fixed64,2,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// duration(null for not override)
	Duration *int32 `protobuf:"varint,3,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
}

func (x *PetOverrideRule) Reset() {
	*x = PetOverrideRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetOverrideRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetOverrideRule) ProtoMessage() {}

func (x *PetOverrideRule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetOverrideRule.ProtoReflect.Descriptor instead.
func (*PetOverrideRule) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{4}
}

func (x *PetOverrideRule) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetOverrideRule) GetPrice() float64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *PetOverrideRule) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

// auto roll over rule
type AutoRolloverRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto rollover enabled
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// auto rollover after x minutes
	AfterMinute int32 `protobuf:"varint,2,opt,name=after_minute,json=afterMinute,proto3" json:"after_minute,omitempty"`
	// auto rollover target service id
	TargetServiceId int64 `protobuf:"varint,3,opt,name=target_service_id,json=targetServiceId,proto3" json:"target_service_id,omitempty"`
}

func (x *AutoRolloverRule) Reset() {
	*x = AutoRolloverRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoRolloverRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoRolloverRule) ProtoMessage() {}

func (x *AutoRolloverRule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoRolloverRule.ProtoReflect.Descriptor instead.
func (*AutoRolloverRule) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{5}
}

func (x *AutoRolloverRule) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *AutoRolloverRule) GetAfterMinute() int32 {
	if x != nil {
		return x.AfterMinute
	}
	return 0
}

func (x *AutoRolloverRule) GetTargetServiceId() int64 {
	if x != nil {
		return x.TargetServiceId
	}
	return 0
}

// service availability model
type ServiceAvailability struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// whether the service is available for all locations
	IsAllLocation bool `protobuf:"varint,2,opt,name=is_all_location,json=isAllLocation,proto3" json:"is_all_location,omitempty"`
	// available locations (only if is_available_in_all_locations is false)
	AvailableBusinessIdList []int64 `protobuf:"varint,3,rep,packed,name=available_business_id_list,json=availableBusinessIdList,proto3" json:"available_business_id_list,omitempty"`
	// whether the service is available for all pet type & breed
	BreedFilter bool `protobuf:"varint,4,opt,name=breed_filter,json=breedFilter,proto3" json:"breed_filter,omitempty"`
	// available pet type with pet breed (only if is_available_for_all_pet_type_and_breed is false)
	CustomizedBreed []*CustomizedBreed `protobuf:"bytes,5,rep,name=customized_breed,json=customizedBreed,proto3" json:"customized_breed,omitempty"`
	// available for all pet size
	PetSizeFilter bool `protobuf:"varint,6,opt,name=pet_size_filter,json=petSizeFilter,proto3" json:"pet_size_filter,omitempty"`
	// available pet size (only if is_available_for_all_pet_size is false)
	CustomizedPetSizes []int64 `protobuf:"varint,7,rep,packed,name=customized_pet_sizes,json=customizedPetSizes,proto3" json:"customized_pet_sizes,omitempty"`
	// weight filter only for compatible with old version, use pet_size in new version
	//
	// Deprecated: Do not use.
	WeightFilter bool `protobuf:"varint,8,opt,name=weight_filter,json=weightFilter,proto3" json:"weight_filter,omitempty"`
	// weight range (only if weight_filter is true)
	//
	// Deprecated: Do not use.
	WeightRange []float64 `protobuf:"fixed64,9,rep,packed,name=weight_range,json=weightRange,proto3" json:"weight_range,omitempty"`
	// available for all pet coat type
	CoatFilter bool `protobuf:"varint,10,opt,name=coat_filter,json=coatFilter,proto3" json:"coat_filter,omitempty"`
	// available pet coat type (only if is_available_for_all_pet_coat_type is false)
	CustomizedCoat []int64 `protobuf:"varint,11,rep,packed,name=customized_coat,json=customizedCoat,proto3" json:"customized_coat,omitempty"`
	// required dedicated lodging
	RequireDedicatedLodging bool `protobuf:"varint,12,opt,name=require_dedicated_lodging,json=requireDedicatedLodging,proto3" json:"require_dedicated_lodging,omitempty"`
	// whether the service is available for all lodging(only if require_dedicated_lodging is true)
	LodgingFilter bool `protobuf:"varint,13,opt,name=lodging_filter,json=lodgingFilter,proto3" json:"lodging_filter,omitempty"`
	// available lodging ids(only if require_dedicated_lodging is true and available_for_all_lodgings is false)
	CustomizedLodgings []int64 `protobuf:"varint,14,rep,packed,name=customized_lodgings,json=customizedLodgings,proto3" json:"customized_lodgings,omitempty"`
}

func (x *ServiceAvailability) Reset() {
	*x = ServiceAvailability{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceAvailability) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceAvailability) ProtoMessage() {}

func (x *ServiceAvailability) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceAvailability.ProtoReflect.Descriptor instead.
func (*ServiceAvailability) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{6}
}

func (x *ServiceAvailability) GetIsAllLocation() bool {
	if x != nil {
		return x.IsAllLocation
	}
	return false
}

func (x *ServiceAvailability) GetAvailableBusinessIdList() []int64 {
	if x != nil {
		return x.AvailableBusinessIdList
	}
	return nil
}

func (x *ServiceAvailability) GetBreedFilter() bool {
	if x != nil {
		return x.BreedFilter
	}
	return false
}

func (x *ServiceAvailability) GetCustomizedBreed() []*CustomizedBreed {
	if x != nil {
		return x.CustomizedBreed
	}
	return nil
}

func (x *ServiceAvailability) GetPetSizeFilter() bool {
	if x != nil {
		return x.PetSizeFilter
	}
	return false
}

func (x *ServiceAvailability) GetCustomizedPetSizes() []int64 {
	if x != nil {
		return x.CustomizedPetSizes
	}
	return nil
}

// Deprecated: Do not use.
func (x *ServiceAvailability) GetWeightFilter() bool {
	if x != nil {
		return x.WeightFilter
	}
	return false
}

// Deprecated: Do not use.
func (x *ServiceAvailability) GetWeightRange() []float64 {
	if x != nil {
		return x.WeightRange
	}
	return nil
}

func (x *ServiceAvailability) GetCoatFilter() bool {
	if x != nil {
		return x.CoatFilter
	}
	return false
}

func (x *ServiceAvailability) GetCustomizedCoat() []int64 {
	if x != nil {
		return x.CustomizedCoat
	}
	return nil
}

func (x *ServiceAvailability) GetRequireDedicatedLodging() bool {
	if x != nil {
		return x.RequireDedicatedLodging
	}
	return false
}

func (x *ServiceAvailability) GetLodgingFilter() bool {
	if x != nil {
		return x.LodgingFilter
	}
	return false
}

func (x *ServiceAvailability) GetCustomizedLodgings() []int64 {
	if x != nil {
		return x.CustomizedLodgings
	}
	return nil
}

// customized breed
type CustomizedBreed struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet type id
	PetTypeId int64 `protobuf:"varint,1,opt,name=pet_type_id,json=petTypeId,proto3" json:"pet_type_id,omitempty"`
	// pet breed ids
	Breeds []string `protobuf:"bytes,2,rep,name=breeds,proto3" json:"breeds,omitempty"`
	// allow all breeds
	IsAll *bool `protobuf:"varint,3,opt,name=is_all,json=isAll,proto3,oneof" json:"is_all,omitempty"`
}

func (x *CustomizedBreed) Reset() {
	*x = CustomizedBreed{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomizedBreed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizedBreed) ProtoMessage() {}

func (x *CustomizedBreed) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizedBreed.ProtoReflect.Descriptor instead.
func (*CustomizedBreed) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{7}
}

func (x *CustomizedBreed) GetPetTypeId() int64 {
	if x != nil {
		return x.PetTypeId
	}
	return 0
}

func (x *CustomizedBreed) GetBreeds() []string {
	if x != nil {
		return x.Breeds
	}
	return nil
}

func (x *CustomizedBreed) GetIsAll() bool {
	if x != nil && x.IsAll != nil {
		return *x.IsAll
	}
	return false
}

// service filter
type ServiceFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item type
	ServiceItemType ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// whether the addon is available for all services
	AvailableForAllServices *bool `protobuf:"varint,2,opt,name=available_for_all_services,json=availableForAllServices,proto3,oneof" json:"available_for_all_services,omitempty"`
	// available service ids (only if available_for_all_services is false)
	AvailableServiceIdList []int64 `protobuf:"varint,3,rep,packed,name=available_service_id_list,json=availableServiceIdList,proto3" json:"available_service_id_list,omitempty"`
}

func (x *ServiceFilter) Reset() {
	*x = ServiceFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceFilter) ProtoMessage() {}

func (x *ServiceFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceFilter.ProtoReflect.Descriptor instead.
func (*ServiceFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{8}
}

func (x *ServiceFilter) GetServiceItemType() ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
}

func (x *ServiceFilter) GetAvailableForAllServices() bool {
	if x != nil && x.AvailableForAllServices != nil {
		return *x.AvailableForAllServices
	}
	return false
}

func (x *ServiceFilter) GetAvailableServiceIdList() []int64 {
	if x != nil {
		return x.AvailableServiceIdList
	}
	return nil
}

// service category brief view
type CustomizedServiceCategoryView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// category id
	CategoryId int64 `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// category name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// service list
	Services []*CustomizedServiceView `protobuf:"bytes,3,rep,name=services,proto3" json:"services,omitempty"`
}

func (x *CustomizedServiceCategoryView) Reset() {
	*x = CustomizedServiceCategoryView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomizedServiceCategoryView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizedServiceCategoryView) ProtoMessage() {}

func (x *CustomizedServiceCategoryView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizedServiceCategoryView.ProtoReflect.Descriptor instead.
func (*CustomizedServiceCategoryView) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{9}
}

func (x *CustomizedServiceCategoryView) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *CustomizedServiceCategoryView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomizedServiceCategoryView) GetServices() []*CustomizedServiceView {
	if x != nil {
		return x.Services
	}
	return nil
}

// service brief view
type CustomizedServiceView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,3,opt,name=price,proto3" json:"price,omitempty"`
	// price unit
	PriceUnit ServicePriceUnit `protobuf:"varint,4,opt,name=price_unit,json=priceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"price_unit,omitempty"`
	// duration
	Duration *int32 `protobuf:"varint,5,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// type
	Type ServiceType `protobuf:"varint,6,opt,name=type,proto3,enum=moego.models.offering.v1.ServiceType" json:"type,omitempty"`
	// category id
	CategoryId int64 `protobuf:"varint,7,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// price override type
	PriceOverrideType ServiceOverrideType `protobuf:"varint,8,opt,name=price_override_type,json=priceOverrideType,proto3,enum=moego.models.offering.v1.ServiceOverrideType" json:"price_override_type,omitempty"`
	// duration override type
	DurationOverrideType ServiceOverrideType `protobuf:"varint,9,opt,name=duration_override_type,json=durationOverrideType,proto3,enum=moego.models.offering.v1.ServiceOverrideType" json:"duration_override_type,omitempty"`
	// tax id
	TaxId int64 `protobuf:"varint,10,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// service item type
	ServiceItemType ServiceItemType `protobuf:"varint,11,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// description
	Description string `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`
	// require dedicated staff
	RequireDedicatedStaff bool `protobuf:"varint,13,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3" json:"require_dedicated_staff,omitempty"`
	// require dedicated lodging
	RequireDedicatedLodging bool `protobuf:"varint,14,opt,name=require_dedicated_lodging,json=requireDedicatedLodging,proto3" json:"require_dedicated_lodging,omitempty"`
	// inactive
	Inactive bool `protobuf:"varint,15,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// max duration
	MaxDuration int32 `protobuf:"varint,16,opt,name=max_duration,json=maxDuration,proto3" json:"max_duration,omitempty"`
	// images
	Images []string `protobuf:"bytes,17,rep,name=images,proto3" json:"images,omitempty"`
	// staff overridden list
	StaffOverrideList []*StaffOverrideRule `protobuf:"bytes,18,rep,name=staff_override_list,json=staffOverrideList,proto3" json:"staff_override_list,omitempty"`
	// available staff list
	AvailableStaffs *CustomizedServiceView_AvailableStaffs `protobuf:"bytes,19,opt,name=available_staffs,json=availableStaffs,proto3" json:"available_staffs,omitempty"`
	// whether the service is available for all lodging
	LodgingFilter bool `protobuf:"varint,20,opt,name=lodging_filter,json=lodgingFilter,proto3" json:"lodging_filter,omitempty"`
	// available lodging ids(only if lodging_filter is true)
	CustomizedLodgings []int64 `protobuf:"varint,21,rep,packed,name=customized_lodgings,json=customizedLodgings,proto3" json:"customized_lodgings,omitempty"`
	// bundle services
	BundleServiceIds []int64 `protobuf:"varint,22,rep,packed,name=bundle_service_ids,json=bundleServiceIds,proto3" json:"bundle_service_ids,omitempty"`
	// additional service rule
	AdditionalServiceRule *AdditionalServiceRule `protobuf:"bytes,23,opt,name=additional_service_rule,json=additionalServiceRule,proto3" json:"additional_service_rule,omitempty"`
}

func (x *CustomizedServiceView) Reset() {
	*x = CustomizedServiceView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomizedServiceView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizedServiceView) ProtoMessage() {}

func (x *CustomizedServiceView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizedServiceView.ProtoReflect.Descriptor instead.
func (*CustomizedServiceView) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{10}
}

func (x *CustomizedServiceView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CustomizedServiceView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomizedServiceView) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *CustomizedServiceView) GetPriceUnit() ServicePriceUnit {
	if x != nil {
		return x.PriceUnit
	}
	return ServicePriceUnit_SERVICE_PRICE_UNIT_UNSPECIFIED
}

func (x *CustomizedServiceView) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *CustomizedServiceView) GetType() ServiceType {
	if x != nil {
		return x.Type
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *CustomizedServiceView) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *CustomizedServiceView) GetPriceOverrideType() ServiceOverrideType {
	if x != nil {
		return x.PriceOverrideType
	}
	return ServiceOverrideType_SERVICE_OVERRIDE_TYPE_UNSPECIFIED
}

func (x *CustomizedServiceView) GetDurationOverrideType() ServiceOverrideType {
	if x != nil {
		return x.DurationOverrideType
	}
	return ServiceOverrideType_SERVICE_OVERRIDE_TYPE_UNSPECIFIED
}

func (x *CustomizedServiceView) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *CustomizedServiceView) GetServiceItemType() ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
}

func (x *CustomizedServiceView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CustomizedServiceView) GetRequireDedicatedStaff() bool {
	if x != nil {
		return x.RequireDedicatedStaff
	}
	return false
}

func (x *CustomizedServiceView) GetRequireDedicatedLodging() bool {
	if x != nil {
		return x.RequireDedicatedLodging
	}
	return false
}

func (x *CustomizedServiceView) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *CustomizedServiceView) GetMaxDuration() int32 {
	if x != nil {
		return x.MaxDuration
	}
	return 0
}

func (x *CustomizedServiceView) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *CustomizedServiceView) GetStaffOverrideList() []*StaffOverrideRule {
	if x != nil {
		return x.StaffOverrideList
	}
	return nil
}

func (x *CustomizedServiceView) GetAvailableStaffs() *CustomizedServiceView_AvailableStaffs {
	if x != nil {
		return x.AvailableStaffs
	}
	return nil
}

func (x *CustomizedServiceView) GetLodgingFilter() bool {
	if x != nil {
		return x.LodgingFilter
	}
	return false
}

func (x *CustomizedServiceView) GetCustomizedLodgings() []int64 {
	if x != nil {
		return x.CustomizedLodgings
	}
	return nil
}

func (x *CustomizedServiceView) GetBundleServiceIds() []int64 {
	if x != nil {
		return x.BundleServiceIds
	}
	return nil
}

func (x *CustomizedServiceView) GetAdditionalServiceRule() *AdditionalServiceRule {
	if x != nil {
		return x.AdditionalServiceRule
	}
	return nil
}

// customized service view list
type CustomizedServiceViewList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service list
	Services []*CustomizedServiceView `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
}

func (x *CustomizedServiceViewList) Reset() {
	*x = CustomizedServiceViewList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomizedServiceViewList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizedServiceViewList) ProtoMessage() {}

func (x *CustomizedServiceViewList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizedServiceViewList.ProtoReflect.Descriptor instead.
func (*CustomizedServiceViewList) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{11}
}

func (x *CustomizedServiceViewList) GetServices() []*CustomizedServiceView {
	if x != nil {
		return x.Services
	}
	return nil
}

// service brief view
type ServiceBriefView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// category id
	CategoryId int64 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// service type
	Type ServiceType `protobuf:"varint,3,opt,name=type,proto3,enum=moego.models.offering.v1.ServiceType" json:"type,omitempty"`
	// service item type
	ServiceItemType ServiceItemType `protobuf:"varint,4,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// name
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// description
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,7,opt,name=price,proto3" json:"price,omitempty"`
	// price unit
	PriceUnit ServicePriceUnit `protobuf:"varint,8,opt,name=price_unit,json=priceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"price_unit,omitempty"`
	// duration
	Duration int32 `protobuf:"varint,9,opt,name=duration,proto3" json:"duration,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// inactive
	Inactive bool `protobuf:"varint,12,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// is deleted
	IsDeleted bool `protobuf:"varint,13,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,14,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// max duration (only for daycare service)
	MaxDuration int32 `protobuf:"varint,15,opt,name=max_duration,json=maxDuration,proto3" json:"max_duration,omitempty"`
	// require dedicated staff (only for add on)
	RequireDedicatedStaff bool `protobuf:"varint,16,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3" json:"require_dedicated_staff,omitempty"`
	// whether the service is available for all lodging
	LodgingFilter bool `protobuf:"varint,17,opt,name=lodging_filter,json=lodgingFilter,proto3" json:"lodging_filter,omitempty"`
	// available lodging ids(only if lodging_filter is true)
	CustomizedLodgings []int64 `protobuf:"varint,18,rep,packed,name=customized_lodgings,json=customizedLodgings,proto3" json:"customized_lodgings,omitempty"`
	// number of sessions, only for training
	NumSessions int32 `protobuf:"varint,19,opt,name=num_sessions,json=numSessions,proto3" json:"num_sessions,omitempty"`
	// duration of each session in minutes, only for training
	DurationSessionMin int32 `protobuf:"varint,20,opt,name=duration_session_min,json=durationSessionMin,proto3" json:"duration_session_min,omitempty"`
	// capacity of group class, zero means unlimited, only for training
	Capacity int32 `protobuf:"varint,21,opt,name=capacity,proto3" json:"capacity,omitempty"`
	// whether it require a prerequisite class
	IsRequirePrerequisiteClass bool `protobuf:"varint,22,opt,name=is_require_prerequisite_class,json=isRequirePrerequisiteClass,proto3" json:"is_require_prerequisite_class,omitempty"`
	// prerequisite class ids of training group class, only valid when is_require_prerequisite_class is ture
	PrerequisiteClassIds []int64 `protobuf:"varint,23,rep,packed,name=prerequisite_class_ids,json=prerequisiteClassIds,proto3" json:"prerequisite_class_ids,omitempty"`
	// whether evaluation is required
	IsEvaluationRequired bool `protobuf:"varint,24,opt,name=is_evaluation_required,json=isEvaluationRequired,proto3" json:"is_evaluation_required,omitempty"`
	// whether evaluation is required before online booking
	IsEvaluationRequiredForOb bool `protobuf:"varint,25,opt,name=is_evaluation_required_for_ob,json=isEvaluationRequiredForOb,proto3" json:"is_evaluation_required_for_ob,omitempty"`
	// evaluation id
	EvaluationId int64 `protobuf:"varint,26,opt,name=evaluation_id,json=evaluationId,proto3" json:"evaluation_id,omitempty"`
	// tax id
	TaxId int64 `protobuf:"varint,27,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
}

func (x *ServiceBriefView) Reset() {
	*x = ServiceBriefView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceBriefView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceBriefView) ProtoMessage() {}

func (x *ServiceBriefView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceBriefView.ProtoReflect.Descriptor instead.
func (*ServiceBriefView) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{12}
}

func (x *ServiceBriefView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceBriefView) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *ServiceBriefView) GetType() ServiceType {
	if x != nil {
		return x.Type
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *ServiceBriefView) GetServiceItemType() ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
}

func (x *ServiceBriefView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceBriefView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ServiceBriefView) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ServiceBriefView) GetPriceUnit() ServicePriceUnit {
	if x != nil {
		return x.PriceUnit
	}
	return ServicePriceUnit_SERVICE_PRICE_UNIT_UNSPECIFIED
}

func (x *ServiceBriefView) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *ServiceBriefView) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ServiceBriefView) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ServiceBriefView) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *ServiceBriefView) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *ServiceBriefView) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *ServiceBriefView) GetMaxDuration() int32 {
	if x != nil {
		return x.MaxDuration
	}
	return 0
}

func (x *ServiceBriefView) GetRequireDedicatedStaff() bool {
	if x != nil {
		return x.RequireDedicatedStaff
	}
	return false
}

func (x *ServiceBriefView) GetLodgingFilter() bool {
	if x != nil {
		return x.LodgingFilter
	}
	return false
}

func (x *ServiceBriefView) GetCustomizedLodgings() []int64 {
	if x != nil {
		return x.CustomizedLodgings
	}
	return nil
}

func (x *ServiceBriefView) GetNumSessions() int32 {
	if x != nil {
		return x.NumSessions
	}
	return 0
}

func (x *ServiceBriefView) GetDurationSessionMin() int32 {
	if x != nil {
		return x.DurationSessionMin
	}
	return 0
}

func (x *ServiceBriefView) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

func (x *ServiceBriefView) GetIsRequirePrerequisiteClass() bool {
	if x != nil {
		return x.IsRequirePrerequisiteClass
	}
	return false
}

func (x *ServiceBriefView) GetPrerequisiteClassIds() []int64 {
	if x != nil {
		return x.PrerequisiteClassIds
	}
	return nil
}

func (x *ServiceBriefView) GetIsEvaluationRequired() bool {
	if x != nil {
		return x.IsEvaluationRequired
	}
	return false
}

func (x *ServiceBriefView) GetIsEvaluationRequiredForOb() bool {
	if x != nil {
		return x.IsEvaluationRequiredForOb
	}
	return false
}

func (x *ServiceBriefView) GetEvaluationId() int64 {
	if x != nil {
		return x.EvaluationId
	}
	return 0
}

func (x *ServiceBriefView) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

// the grooming service in c app appt detail view
type ServiceClientView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// service name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// service type
	Type ServiceType `protobuf:"varint,6,opt,name=type,proto3,enum=moego.models.offering.v1.ServiceType" json:"type,omitempty"`
	// is inactive
	Inactive bool `protobuf:"varint,10,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// service item type
	ServiceItemType ServiceItemType `protobuf:"varint,11,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// is deleted
	IsDeleted bool `protobuf:"varint,13,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
}

func (x *ServiceClientView) Reset() {
	*x = ServiceClientView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceClientView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceClientView) ProtoMessage() {}

func (x *ServiceClientView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceClientView.ProtoReflect.Descriptor instead.
func (*ServiceClientView) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{13}
}

func (x *ServiceClientView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceClientView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceClientView) GetType() ServiceType {
	if x != nil {
		return x.Type
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *ServiceClientView) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *ServiceClientView) GetServiceItemType() ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
}

func (x *ServiceClientView) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

// service with available staff customized info
type StaffOverrideRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// price(null for not override)
	Price *float64 `protobuf:"fixed64,2,opt,name=price,proto3,oneof" json:"price,omitempty"`
	// tax(null for not override)
	Duration *int32 `protobuf:"varint,3,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
}

func (x *StaffOverrideRule) Reset() {
	*x = StaffOverrideRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffOverrideRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffOverrideRule) ProtoMessage() {}

func (x *StaffOverrideRule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffOverrideRule.ProtoReflect.Descriptor instead.
func (*StaffOverrideRule) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{14}
}

func (x *StaffOverrideRule) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *StaffOverrideRule) GetPrice() float64 {
	if x != nil && x.Price != nil {
		return *x.Price
	}
	return 0
}

func (x *StaffOverrideRule) GetDuration() int32 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

// service with available location/staff customized info
type LocationStaffOverrideRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business override rule
	LocationOverride *LocationOverrideRule `protobuf:"bytes,1,opt,name=location_override,json=locationOverride,proto3" json:"location_override,omitempty"`
	// staff override rules
	StaffOverrideList []*StaffOverrideRule `protobuf:"bytes,2,rep,name=staff_override_list,json=staffOverrideList,proto3" json:"staff_override_list,omitempty"`
}

func (x *LocationStaffOverrideRule) Reset() {
	*x = LocationStaffOverrideRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationStaffOverrideRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationStaffOverrideRule) ProtoMessage() {}

func (x *LocationStaffOverrideRule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationStaffOverrideRule.ProtoReflect.Descriptor instead.
func (*LocationStaffOverrideRule) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{15}
}

func (x *LocationStaffOverrideRule) GetLocationOverride() *LocationOverrideRule {
	if x != nil {
		return x.LocationOverride
	}
	return nil
}

func (x *LocationStaffOverrideRule) GetStaffOverrideList() []*StaffOverrideRule {
	if x != nil {
		return x.StaffOverrideList
	}
	return nil
}

// service bundle sale view for online booking
type ServiceBundleSaleView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// service name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,3,opt,name=price,proto3" json:"price,omitempty"`
	// price unit
	PriceUnit ServicePriceUnit `protobuf:"varint,4,opt,name=price_unit,json=priceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"price_unit,omitempty"`
}

func (x *ServiceBundleSaleView) Reset() {
	*x = ServiceBundleSaleView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceBundleSaleView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceBundleSaleView) ProtoMessage() {}

func (x *ServiceBundleSaleView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceBundleSaleView.ProtoReflect.Descriptor instead.
func (*ServiceBundleSaleView) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{16}
}

func (x *ServiceBundleSaleView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceBundleSaleView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceBundleSaleView) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ServiceBundleSaleView) GetPriceUnit() ServicePriceUnit {
	if x != nil {
		return x.PriceUnit
	}
	return ServicePriceUnit_SERVICE_PRICE_UNIT_UNSPECIFIED
}

// additional service rule
type AdditionalServiceRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enable
	Enable bool `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	// min stay length
	MinStayLength int32 `protobuf:"varint,2,opt,name=min_stay_length,json=minStayLength,proto3" json:"min_stay_length,omitempty"`
	// apply rules
	ApplyRules []*AdditionalServiceRule_ApplyRule `protobuf:"bytes,3,rep,name=apply_rules,json=applyRules,proto3" json:"apply_rules,omitempty"`
}

func (x *AdditionalServiceRule) Reset() {
	*x = AdditionalServiceRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdditionalServiceRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalServiceRule) ProtoMessage() {}

func (x *AdditionalServiceRule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalServiceRule.ProtoReflect.Descriptor instead.
func (*AdditionalServiceRule) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{17}
}

func (x *AdditionalServiceRule) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *AdditionalServiceRule) GetMinStayLength() int32 {
	if x != nil {
		return x.MinStayLength
	}
	return 0
}

func (x *AdditionalServiceRule) GetApplyRules() []*AdditionalServiceRule_ApplyRule {
	if x != nil {
		return x.ApplyRules
	}
	return nil
}

// pet code filter
type ServiceModel_PetCodeFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// whether to filter by white list or black list
	IsWhiteList bool `protobuf:"varint,1,opt,name=is_white_list,json=isWhiteList,proto3" json:"is_white_list,omitempty"`
	// whether it applies to all pet codes.
	IsAllPetCode bool `protobuf:"varint,2,opt,name=is_all_pet_code,json=isAllPetCode,proto3" json:"is_all_pet_code,omitempty"`
	// pet code list, only valid when is_all_pet_code is false
	PetCodeIds []int64 `protobuf:"varint,3,rep,packed,name=pet_code_ids,json=petCodeIds,proto3" json:"pet_code_ids,omitempty"`
}

func (x *ServiceModel_PetCodeFilter) Reset() {
	*x = ServiceModel_PetCodeFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceModel_PetCodeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceModel_PetCodeFilter) ProtoMessage() {}

func (x *ServiceModel_PetCodeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceModel_PetCodeFilter.ProtoReflect.Descriptor instead.
func (*ServiceModel_PetCodeFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ServiceModel_PetCodeFilter) GetIsWhiteList() bool {
	if x != nil {
		return x.IsWhiteList
	}
	return false
}

func (x *ServiceModel_PetCodeFilter) GetIsAllPetCode() bool {
	if x != nil {
		return x.IsAllPetCode
	}
	return false
}

func (x *ServiceModel_PetCodeFilter) GetPetCodeIds() []int64 {
	if x != nil {
		return x.PetCodeIds
	}
	return nil
}

// Available staffs
type CustomizedServiceView_AvailableStaffs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is all available for this service
	IsAllAvailable bool `protobuf:"varint,1,opt,name=is_all_available,json=isAllAvailable,proto3" json:"is_all_available,omitempty"`
	// available staff ids
	Ids []int64 `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *CustomizedServiceView_AvailableStaffs) Reset() {
	*x = CustomizedServiceView_AvailableStaffs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomizedServiceView_AvailableStaffs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizedServiceView_AvailableStaffs) ProtoMessage() {}

func (x *CustomizedServiceView_AvailableStaffs) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizedServiceView_AvailableStaffs.ProtoReflect.Descriptor instead.
func (*CustomizedServiceView_AvailableStaffs) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{10, 0}
}

func (x *CustomizedServiceView_AvailableStaffs) GetIsAllAvailable() bool {
	if x != nil {
		return x.IsAllAvailable
	}
	return false
}

func (x *CustomizedServiceView_AvailableStaffs) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// apply rule
type AdditionalServiceRule_ApplyRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// date type
	DateType DateType `protobuf:"varint,2,opt,name=date_type,json=dateType,proto3,enum=moego.models.offering.v1.DateType" json:"date_type,omitempty"`
	// quantity per day
	QuantityPerDay int32 `protobuf:"varint,3,opt,name=quantity_per_day,json=quantityPerDay,proto3" json:"quantity_per_day,omitempty"`
}

func (x *AdditionalServiceRule_ApplyRule) Reset() {
	*x = AdditionalServiceRule_ApplyRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdditionalServiceRule_ApplyRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalServiceRule_ApplyRule) ProtoMessage() {}

func (x *AdditionalServiceRule_ApplyRule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_service_models_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalServiceRule_ApplyRule.ProtoReflect.Descriptor instead.
func (*AdditionalServiceRule_ApplyRule) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_models_proto_rawDescGZIP(), []int{17, 0}
}

func (x *AdditionalServiceRule_ApplyRule) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *AdditionalServiceRule_ApplyRule) GetDateType() DateType {
	if x != nil {
		return x.DateType
	}
	return DateType_DATE_TYPE_UNSPECIFIED
}

func (x *AdditionalServiceRule_ApplyRule) GetQuantityPerDay() int32 {
	if x != nil {
		return x.QuantityPerDay
	}
	return 0
}

var File_moego_models_offering_v1_service_models_proto protoreflect.FileDescriptor

var file_moego_models_offering_v1_service_models_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8f, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0xac, 0x17, 0x0a, 0x0c, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x68, 0x0a, 0x16, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52,
	0x75, 0x6c, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x14, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x36, 0x0a,
	0x17, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x49, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e,
	0x69, 0x74, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x15, 0x0a,
	0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74,
	0x61, 0x78, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x33, 0x0a, 0x16, 0x61, 0x64, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x13, 0x61, 0x64, 0x64, 0x54, 0x6f, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x42, 0x61, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x6e, 0x5f, 0x74, 0x69, 0x70,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x63, 0x61, 0x6e, 0x54, 0x69, 0x70, 0x12, 0x26,
	0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x1a, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x12, 0x20, 0x03, 0x28, 0x03, 0x52, 0x17, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x62, 0x72, 0x65, 0x65, 0x64,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x54, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x69, 0x7a, 0x65, 0x64, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x42, 0x72, 0x65, 0x65, 0x64, 0x52, 0x0f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x42, 0x72, 0x65, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0f,
	0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a,
	0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x73, 0x18, 0x16, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x50, 0x65,
	0x74, 0x53, 0x69, 0x7a, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x0d, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x0c, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x25, 0x0a, 0x0c, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x18, 0x20, 0x03, 0x28, 0x01, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x77, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x63, 0x6f, 0x61,
	0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x61, 0x74, 0x18, 0x1a, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x6f, 0x61, 0x74,
	0x12, 0x3a, 0x0a, 0x19, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x18, 0x1b, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x17, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x1c,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65,
	0x64, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x57, 0x0a, 0x13, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x1f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x20, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x21, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x58, 0x0a, 0x12, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x6f, 0x6c, 0x6c, 0x6f,
	0x76, 0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x6f,
	0x6c, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x10, 0x61, 0x75, 0x74, 0x6f,
	0x52, 0x6f, 0x6c, 0x6c, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x3b, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x23, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x18, 0x25, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x26, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x17, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x27, 0x20, 0x03, 0x28, 0x03, 0x52, 0x14, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x74, 0x0a, 0x1c, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x28, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x19, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x35, 0x0a, 0x17, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66,
	0x6f, 0x72, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x2a, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x14, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72,
	0x41, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x5c, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x2b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x2c, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x10, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x73, 0x12, 0x45, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x2d,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6e,
	0x75, 0x6d, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x2e, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x6e, 0x75, 0x6d, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x30,
	0x0a, 0x14, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x69, 0x6e,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x30, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x41, 0x0a, 0x1d,
	0x69, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x32, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x1a, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x50, 0x72,
	0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12,
	0x34, 0x0a, 0x16, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x5f,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x33, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x14, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x69, 0x73, 0x5f, 0x65, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18,
	0x34, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x69, 0x73, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x40, 0x0a, 0x1d, 0x69,
	0x73, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6f, 0x62, 0x18, 0x35, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x19, 0x69, 0x73, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x4f, 0x62, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x36,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x6c, 0x0a, 0x17, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x37, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x75, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x88, 0x01, 0x01,
	0x1a, 0x7c, 0x0a, 0x0d, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x22, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x57, 0x68, 0x69, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f,
	0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x69, 0x73, 0x41, 0x6c, 0x6c, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0c,
	0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x73, 0x22, 0x48,
	0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x4f, 0x45, 0x47, 0x4f, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f,
	0x52, 0x4d, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49,
	0x53, 0x45, 0x5f, 0x48, 0x55, 0x42, 0x10, 0x02, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x61, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x4a, 0x04, 0x08, 0x29, 0x10, 0x2a, 0x52, 0x0c, 0x69, 0x73, 0x5f, 0x61,
	0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x22, 0xea, 0x01, 0x0a, 0x14, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x75, 0x6c,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x48, 0x00, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1a, 0x0a,
	0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52,
	0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x48, 0x02, 0x52, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x6d, 0x61,
	0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x48, 0x03, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x09, 0x0a, 0x07,
	0x5f, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x6c, 0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x55, 0x0a, 0x11,
	0x70, 0x65, 0x74, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x0f, 0x70, 0x65, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0x7b, 0x0a, 0x0f, 0x50, 0x65, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x7b, 0x0a, 0x10, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x6f, 0x6c, 0x6c, 0x6f, 0x76, 0x65, 0x72,
	0x52, 0x75, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x66, 0x74, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x75, 0x74,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0xfb, 0x04,
	0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d,
	0x69, 0x73, 0x41, 0x6c, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a,
	0x1a, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x17, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x72,
	0x65, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0b, 0x62, 0x72, 0x65, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x54, 0x0a,
	0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x62, 0x72, 0x65, 0x65,
	0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x42, 0x72, 0x65,
	0x65, 0x64, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x42, 0x72,
	0x65, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x70, 0x65,
	0x74, 0x53, 0x69, 0x7a, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x14, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x52, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x69, 0x7a, 0x65, 0x64, 0x50, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x73, 0x12, 0x27, 0x0a,
	0x0d, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0c, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x09, 0x20, 0x03, 0x28, 0x01, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x0b, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x6f, 0x61, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x63, 0x6f, 0x61, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x27,
	0x0a, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x61,
	0x74, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69,
	0x7a, 0x65, 0x64, 0x43, 0x6f, 0x61, 0x74, 0x12, 0x3a, 0x0a, 0x19, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x5f, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x13, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x03, 0x52, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69,
	0x7a, 0x65, 0x64, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x70, 0x0a, 0x0f, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x42, 0x72, 0x65, 0x65, 0x64, 0x12, 0x1e,
	0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x62, 0x72, 0x65, 0x65, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x05, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x88,
	0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x22, 0x82, 0x02,
	0x0a, 0x0d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x1a, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x17, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x19, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x16, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x1d, 0x0a, 0x1b, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x22, 0xa1, 0x01, 0x0a, 0x1d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65,
	0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x08, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65,
	0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0xcc, 0x0a, 0x0a, 0x15, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x69, 0x7a, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x49, 0x0a, 0x0a, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x1f, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x49, 0x64, 0x12, 0x5d, 0x0a, 0x13, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x63, 0x0a, 0x16, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x14, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x55, 0x0a,
	0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x17, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x5f, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x3a,
	0x0a, 0x19, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x17, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61,
	0x74, 0x65, 0x64, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x6e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61,
	0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x73, 0x12, 0x5b, 0x0a, 0x13, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x11, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x6a,
	0x0a, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x52, 0x0f, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x2f, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x03, 0x52, 0x12,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10,
	0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73,
	0x12, 0x67, 0x0a, 0x17, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x15, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x1a, 0x4d, 0x0a, 0x0f, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x28, 0x0a, 0x10,
	0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x68, 0x0a, 0x19, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69,
	0x7a, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x4b, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22,
	0xad, 0x09, 0x0a, 0x10, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x72, 0x69, 0x65, 0x66,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x49, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x6e, 0x69,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55,
	0x6e, 0x69, 0x74, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x36, 0x0a, 0x17, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x15, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x2f, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x03, 0x52, 0x12, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6e, 0x75, 0x6d, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x12, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x4d, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x12, 0x41, 0x0a, 0x1d, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x5f, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1a, 0x69, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x50, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x65,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x17, 0x20, 0x03, 0x28, 0x03, 0x52, 0x14, 0x70, 0x72, 0x65, 0x72, 0x65, 0x71, 0x75, 0x69, 0x73,
	0x69, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x69,
	0x73, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x69, 0x73, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x12, 0x40, 0x0a, 0x1d, 0x69, 0x73, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x5f,
	0x6f, 0x62, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x69, 0x73, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x46, 0x6f,
	0x72, 0x4f, 0x62, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f,
	0x69, 0x64, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x22,
	0x84, 0x02, 0x0a, 0x11, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x22, 0x81, 0x01, 0x0a, 0x11, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x0b, 0x0a,
	0x09, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xd5, 0x01, 0x0a, 0x19, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x5b, 0x0a, 0x11, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x5b, 0x0a, 0x13, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x11, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0x9c, 0x01, 0x0a, 0x15, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x53, 0x61, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x49, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x75, 0x6e, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69,
	0x74, 0x22, 0xcb, 0x02, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x69, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x79, 0x5f,
	0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6d, 0x69,
	0x6e, 0x53, 0x74, 0x61, 0x79, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x5a, 0x0a, 0x0b, 0x61,
	0x70, 0x70, 0x6c, 0x79, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x75, 0x6c,
	0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0a, 0x61, 0x70, 0x70,
	0x6c, 0x79, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x1a, 0x95, 0x01, 0x0a, 0x09, 0x41, 0x70, 0x70, 0x6c,
	0x79, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0e, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x50, 0x65, 0x72, 0x44, 0x61, 0x79, 0x42,
	0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_offering_v1_service_models_proto_rawDescOnce sync.Once
	file_moego_models_offering_v1_service_models_proto_rawDescData = file_moego_models_offering_v1_service_models_proto_rawDesc
)

func file_moego_models_offering_v1_service_models_proto_rawDescGZIP() []byte {
	file_moego_models_offering_v1_service_models_proto_rawDescOnce.Do(func() {
		file_moego_models_offering_v1_service_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_offering_v1_service_models_proto_rawDescData)
	})
	return file_moego_models_offering_v1_service_models_proto_rawDescData
}

var file_moego_models_offering_v1_service_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_offering_v1_service_models_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_moego_models_offering_v1_service_models_proto_goTypes = []interface{}{
	(ServiceModel_Source)(0),                      // 0: moego.models.offering.v1.ServiceModel.Source
	(*ServiceCategoryModel)(nil),                  // 1: moego.models.offering.v1.ServiceCategoryModel
	(*ServiceModel)(nil),                          // 2: moego.models.offering.v1.ServiceModel
	(*LocationOverrideRule)(nil),                  // 3: moego.models.offering.v1.LocationOverrideRule
	(*ServiceOverrideRule)(nil),                   // 4: moego.models.offering.v1.ServiceOverrideRule
	(*PetOverrideRule)(nil),                       // 5: moego.models.offering.v1.PetOverrideRule
	(*AutoRolloverRule)(nil),                      // 6: moego.models.offering.v1.AutoRolloverRule
	(*ServiceAvailability)(nil),                   // 7: moego.models.offering.v1.ServiceAvailability
	(*CustomizedBreed)(nil),                       // 8: moego.models.offering.v1.CustomizedBreed
	(*ServiceFilter)(nil),                         // 9: moego.models.offering.v1.ServiceFilter
	(*CustomizedServiceCategoryView)(nil),         // 10: moego.models.offering.v1.CustomizedServiceCategoryView
	(*CustomizedServiceView)(nil),                 // 11: moego.models.offering.v1.CustomizedServiceView
	(*CustomizedServiceViewList)(nil),             // 12: moego.models.offering.v1.CustomizedServiceViewList
	(*ServiceBriefView)(nil),                      // 13: moego.models.offering.v1.ServiceBriefView
	(*ServiceClientView)(nil),                     // 14: moego.models.offering.v1.ServiceClientView
	(*StaffOverrideRule)(nil),                     // 15: moego.models.offering.v1.StaffOverrideRule
	(*LocationStaffOverrideRule)(nil),             // 16: moego.models.offering.v1.LocationStaffOverrideRule
	(*ServiceBundleSaleView)(nil),                 // 17: moego.models.offering.v1.ServiceBundleSaleView
	(*AdditionalServiceRule)(nil),                 // 18: moego.models.offering.v1.AdditionalServiceRule
	(*ServiceModel_PetCodeFilter)(nil),            // 19: moego.models.offering.v1.ServiceModel.PetCodeFilter
	(*CustomizedServiceView_AvailableStaffs)(nil), // 20: moego.models.offering.v1.CustomizedServiceView.AvailableStaffs
	(*AdditionalServiceRule_ApplyRule)(nil),       // 21: moego.models.offering.v1.AdditionalServiceRule.ApplyRule
	(ServiceItemType)(0),                          // 22: moego.models.offering.v1.ServiceItemType
	(ServicePriceUnit)(0),                         // 23: moego.models.offering.v1.ServicePriceUnit
	(ServiceType)(0),                              // 24: moego.models.offering.v1.ServiceType
	(*timestamppb.Timestamp)(nil),                 // 25: google.protobuf.Timestamp
	(ServiceOverrideType)(0),                      // 26: moego.models.offering.v1.ServiceOverrideType
	(DateType)(0),                                 // 27: moego.models.offering.v1.DateType
}
var file_moego_models_offering_v1_service_models_proto_depIdxs = []int32{
	2,  // 0: moego.models.offering.v1.ServiceCategoryModel.services:type_name -> moego.models.offering.v1.ServiceModel
	3,  // 1: moego.models.offering.v1.ServiceModel.location_override_list:type_name -> moego.models.offering.v1.LocationOverrideRule
	22, // 2: moego.models.offering.v1.ServiceModel.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	23, // 3: moego.models.offering.v1.ServiceModel.price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	8,  // 4: moego.models.offering.v1.ServiceModel.customized_breed:type_name -> moego.models.offering.v1.CustomizedBreed
	9,  // 5: moego.models.offering.v1.ServiceModel.service_filter_list:type_name -> moego.models.offering.v1.ServiceFilter
	24, // 6: moego.models.offering.v1.ServiceModel.type:type_name -> moego.models.offering.v1.ServiceType
	6,  // 7: moego.models.offering.v1.ServiceModel.auto_rollover_rule:type_name -> moego.models.offering.v1.AutoRolloverRule
	25, // 8: moego.models.offering.v1.ServiceModel.create_time:type_name -> google.protobuf.Timestamp
	25, // 9: moego.models.offering.v1.ServiceModel.update_time:type_name -> google.protobuf.Timestamp
	16, // 10: moego.models.offering.v1.ServiceModel.location_staff_override_list:type_name -> moego.models.offering.v1.LocationStaffOverrideRule
	19, // 11: moego.models.offering.v1.ServiceModel.pet_code_filter:type_name -> moego.models.offering.v1.ServiceModel.PetCodeFilter
	0,  // 12: moego.models.offering.v1.ServiceModel.source:type_name -> moego.models.offering.v1.ServiceModel.Source
	18, // 13: moego.models.offering.v1.ServiceModel.additional_service_rule:type_name -> moego.models.offering.v1.AdditionalServiceRule
	5,  // 14: moego.models.offering.v1.ServiceOverrideRule.pet_override_list:type_name -> moego.models.offering.v1.PetOverrideRule
	8,  // 15: moego.models.offering.v1.ServiceAvailability.customized_breed:type_name -> moego.models.offering.v1.CustomizedBreed
	22, // 16: moego.models.offering.v1.ServiceFilter.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	11, // 17: moego.models.offering.v1.CustomizedServiceCategoryView.services:type_name -> moego.models.offering.v1.CustomizedServiceView
	23, // 18: moego.models.offering.v1.CustomizedServiceView.price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	24, // 19: moego.models.offering.v1.CustomizedServiceView.type:type_name -> moego.models.offering.v1.ServiceType
	26, // 20: moego.models.offering.v1.CustomizedServiceView.price_override_type:type_name -> moego.models.offering.v1.ServiceOverrideType
	26, // 21: moego.models.offering.v1.CustomizedServiceView.duration_override_type:type_name -> moego.models.offering.v1.ServiceOverrideType
	22, // 22: moego.models.offering.v1.CustomizedServiceView.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	15, // 23: moego.models.offering.v1.CustomizedServiceView.staff_override_list:type_name -> moego.models.offering.v1.StaffOverrideRule
	20, // 24: moego.models.offering.v1.CustomizedServiceView.available_staffs:type_name -> moego.models.offering.v1.CustomizedServiceView.AvailableStaffs
	18, // 25: moego.models.offering.v1.CustomizedServiceView.additional_service_rule:type_name -> moego.models.offering.v1.AdditionalServiceRule
	11, // 26: moego.models.offering.v1.CustomizedServiceViewList.services:type_name -> moego.models.offering.v1.CustomizedServiceView
	24, // 27: moego.models.offering.v1.ServiceBriefView.type:type_name -> moego.models.offering.v1.ServiceType
	22, // 28: moego.models.offering.v1.ServiceBriefView.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	23, // 29: moego.models.offering.v1.ServiceBriefView.price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	25, // 30: moego.models.offering.v1.ServiceBriefView.create_time:type_name -> google.protobuf.Timestamp
	25, // 31: moego.models.offering.v1.ServiceBriefView.update_time:type_name -> google.protobuf.Timestamp
	24, // 32: moego.models.offering.v1.ServiceClientView.type:type_name -> moego.models.offering.v1.ServiceType
	22, // 33: moego.models.offering.v1.ServiceClientView.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	3,  // 34: moego.models.offering.v1.LocationStaffOverrideRule.location_override:type_name -> moego.models.offering.v1.LocationOverrideRule
	15, // 35: moego.models.offering.v1.LocationStaffOverrideRule.staff_override_list:type_name -> moego.models.offering.v1.StaffOverrideRule
	23, // 36: moego.models.offering.v1.ServiceBundleSaleView.price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	21, // 37: moego.models.offering.v1.AdditionalServiceRule.apply_rules:type_name -> moego.models.offering.v1.AdditionalServiceRule.ApplyRule
	27, // 38: moego.models.offering.v1.AdditionalServiceRule.ApplyRule.date_type:type_name -> moego.models.offering.v1.DateType
	39, // [39:39] is the sub-list for method output_type
	39, // [39:39] is the sub-list for method input_type
	39, // [39:39] is the sub-list for extension type_name
	39, // [39:39] is the sub-list for extension extendee
	0,  // [0:39] is the sub-list for field type_name
}

func init() { file_moego_models_offering_v1_service_models_proto_init() }
func file_moego_models_offering_v1_service_models_proto_init() {
	if File_moego_models_offering_v1_service_models_proto != nil {
		return
	}
	file_moego_models_offering_v1_service_enum_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_offering_v1_service_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceCategoryModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationOverrideRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceOverrideRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetOverrideRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoRolloverRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceAvailability); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomizedBreed); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomizedServiceCategoryView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomizedServiceView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomizedServiceViewList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceBriefView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceClientView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffOverrideRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationStaffOverrideRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceBundleSaleView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdditionalServiceRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceModel_PetCodeFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomizedServiceView_AvailableStaffs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_service_models_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdditionalServiceRule_ApplyRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_offering_v1_service_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_offering_v1_service_models_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_models_offering_v1_service_models_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_models_offering_v1_service_models_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_models_offering_v1_service_models_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_models_offering_v1_service_models_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_models_offering_v1_service_models_proto_msgTypes[14].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_offering_v1_service_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_offering_v1_service_models_proto_goTypes,
		DependencyIndexes: file_moego_models_offering_v1_service_models_proto_depIdxs,
		EnumInfos:         file_moego_models_offering_v1_service_models_proto_enumTypes,
		MessageInfos:      file_moego_models_offering_v1_service_models_proto_msgTypes,
	}.Build()
	File_moego_models_offering_v1_service_models_proto = out.File
	file_moego_models_offering_v1_service_models_proto_rawDesc = nil
	file_moego_models_offering_v1_service_models_proto_goTypes = nil
	file_moego_models_offering_v1_service_models_proto_depIdxs = nil
}
