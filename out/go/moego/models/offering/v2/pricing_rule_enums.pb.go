// @since 2025-02-26 16:46:24
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/offering/v2/pricing_rule_enums.proto

package offeringpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// rule type
type RuleType int32

const (
	// unspecified value
	RuleType_RULE_TYPE_UNSPECIFIED RuleType = 0
	// for multiple pet
	RuleType_MULTIPLE_PET RuleType = 1
	// for multiple stay
	RuleType_MULTIPLE_STAY RuleType = 2
	// for peak date
	RuleType_PEAK_DATE RuleType = 3
)

// Enum value maps for RuleType.
var (
	RuleType_name = map[int32]string{
		0: "RULE_TYPE_UNSPECIFIED",
		1: "MULTIPLE_PET",
		2: "MULTIPLE_STAY",
		3: "PEAK_DATE",
	}
	RuleType_value = map[string]int32{
		"RULE_TYPE_UNSPECIFIED": 0,
		"MULTIPLE_PET":          1,
		"MULTIPLE_STAY":         2,
		"PEAK_DATE":             3,
	}
)

func (x RuleType) Enum() *RuleType {
	p := new(RuleType)
	*p = x
	return p
}

func (x RuleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RuleType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v2_pricing_rule_enums_proto_enumTypes[0].Descriptor()
}

func (RuleType) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v2_pricing_rule_enums_proto_enumTypes[0]
}

func (x RuleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RuleType.Descriptor instead.
func (RuleType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_enums_proto_rawDescGZIP(), []int{0}
}

// ConditionType defines what attribute a condition will evaluate
type ConditionType int32

const (
	// unspecified value
	ConditionType_CONDITION_TYPE_UNSPECIFIED ConditionType = 0
	// pet count
	ConditionType_PET_COUNT ConditionType = 1
	// stay length
	ConditionType_STAY_LENGTH ConditionType = 2
	// date range
	ConditionType_DATE_RANGE ConditionType = 3
)

// Enum value maps for ConditionType.
var (
	ConditionType_name = map[int32]string{
		0: "CONDITION_TYPE_UNSPECIFIED",
		1: "PET_COUNT",
		2: "STAY_LENGTH",
		3: "DATE_RANGE",
	}
	ConditionType_value = map[string]int32{
		"CONDITION_TYPE_UNSPECIFIED": 0,
		"PET_COUNT":                  1,
		"STAY_LENGTH":                2,
		"DATE_RANGE":                 3,
	}
)

func (x ConditionType) Enum() *ConditionType {
	p := new(ConditionType)
	*p = x
	return p
}

func (x ConditionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConditionType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v2_pricing_rule_enums_proto_enumTypes[1].Descriptor()
}

func (ConditionType) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v2_pricing_rule_enums_proto_enumTypes[1]
}

func (x ConditionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConditionType.Descriptor instead.
func (ConditionType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_enums_proto_rawDescGZIP(), []int{1}
}

// EffectType defines how a price is modified
type EffectType int32

const (
	// unspecified value
	EffectType_EFFECT_TYPE_UNSPECIFIED EffectType = 0
	// fixed discount, e.g. -$100
	EffectType_FIXED_DISCOUNT EffectType = 1
	// percentage discount, e.g. -10%
	EffectType_PERCENTAGE_DISCOUNT EffectType = 2
	// fixed increase, e.g. +$100
	EffectType_FIXED_INCREASE EffectType = 3
	// percentage increase, e.g. +10%
	EffectType_PERCENTAGE_INCREASE EffectType = 4
)

// Enum value maps for EffectType.
var (
	EffectType_name = map[int32]string{
		0: "EFFECT_TYPE_UNSPECIFIED",
		1: "FIXED_DISCOUNT",
		2: "PERCENTAGE_DISCOUNT",
		3: "FIXED_INCREASE",
		4: "PERCENTAGE_INCREASE",
	}
	EffectType_value = map[string]int32{
		"EFFECT_TYPE_UNSPECIFIED": 0,
		"FIXED_DISCOUNT":          1,
		"PERCENTAGE_DISCOUNT":     2,
		"FIXED_INCREASE":          3,
		"PERCENTAGE_INCREASE":     4,
	}
)

func (x EffectType) Enum() *EffectType {
	p := new(EffectType)
	*p = x
	return p
}

func (x EffectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EffectType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v2_pricing_rule_enums_proto_enumTypes[2].Descriptor()
}

func (EffectType) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v2_pricing_rule_enums_proto_enumTypes[2]
}

func (x EffectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EffectType.Descriptor instead.
func (EffectType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_enums_proto_rawDescGZIP(), []int{2}
}

// rule apply type
type RuleApplyType int32

const (
	// unspecified value
	RuleApplyType_RULE_APPLY_TYPE_UNSPECIFIED RuleApplyType = 0
	// apply to each one
	RuleApplyType_APPLY_TO_EACH RuleApplyType = 1
	// apply to additional
	RuleApplyType_APPLY_TO_ADDITIONAL RuleApplyType = 2
	// apply to all pets, deprecated
	//
	// Deprecated: Do not use.
	RuleApplyType_APPLY_TO_ALL_PETS RuleApplyType = 3
	// apply to first pet
	RuleApplyType_APPLY_TO_FIRST_PET RuleApplyType = 4
)

// Enum value maps for RuleApplyType.
var (
	RuleApplyType_name = map[int32]string{
		0: "RULE_APPLY_TYPE_UNSPECIFIED",
		1: "APPLY_TO_EACH",
		2: "APPLY_TO_ADDITIONAL",
		3: "APPLY_TO_ALL_PETS",
		4: "APPLY_TO_FIRST_PET",
	}
	RuleApplyType_value = map[string]int32{
		"RULE_APPLY_TYPE_UNSPECIFIED": 0,
		"APPLY_TO_EACH":               1,
		"APPLY_TO_ADDITIONAL":         2,
		"APPLY_TO_ALL_PETS":           3,
		"APPLY_TO_FIRST_PET":          4,
	}
)

func (x RuleApplyType) Enum() *RuleApplyType {
	p := new(RuleApplyType)
	*p = x
	return p
}

func (x RuleApplyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RuleApplyType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v2_pricing_rule_enums_proto_enumTypes[3].Descriptor()
}

func (RuleApplyType) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v2_pricing_rule_enums_proto_enumTypes[3]
}

func (x RuleApplyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RuleApplyType.Descriptor instead.
func (RuleApplyType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v2_pricing_rule_enums_proto_rawDescGZIP(), []int{3}
}

var File_moego_models_offering_v2_pricing_rule_enums_proto protoreflect.FileDescriptor

var file_moego_models_offering_v2_pricing_rule_enums_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2a, 0x59, 0x0a,
	0x08, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x55, 0x4c,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50, 0x4c, 0x45,
	0x5f, 0x50, 0x45, 0x54, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50,
	0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x59, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x45, 0x41,
	0x4b, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x03, 0x2a, 0x5f, 0x0a, 0x0d, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4e,
	0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x45, 0x54,
	0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x54, 0x41, 0x59,
	0x5f, 0x4c, 0x45, 0x4e, 0x47, 0x54, 0x48, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x03, 0x2a, 0x83, 0x01, 0x0a, 0x0a, 0x45, 0x66,
	0x66, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x46, 0x46, 0x45,
	0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x44,
	0x49, 0x53, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x45, 0x52,
	0x43, 0x45, 0x4e, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x43, 0x52,
	0x45, 0x41, 0x53, 0x45, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e,
	0x54, 0x41, 0x47, 0x45, 0x5f, 0x49, 0x4e, 0x43, 0x52, 0x45, 0x41, 0x53, 0x45, 0x10, 0x04, 0x2a,
	0x8f, 0x01, 0x0a, 0x0d, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x55, 0x4c, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x59, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x50, 0x50, 0x4c, 0x59, 0x5f, 0x54, 0x4f, 0x5f, 0x45,
	0x41, 0x43, 0x48, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x50, 0x50, 0x4c, 0x59, 0x5f, 0x54,
	0x4f, 0x5f, 0x41, 0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x10, 0x02, 0x12, 0x19,
	0x0a, 0x11, 0x41, 0x50, 0x50, 0x4c, 0x59, 0x5f, 0x54, 0x4f, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x50,
	0x45, 0x54, 0x53, 0x10, 0x03, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x50, 0x50,
	0x4c, 0x59, 0x5f, 0x54, 0x4f, 0x5f, 0x46, 0x49, 0x52, 0x53, 0x54, 0x5f, 0x50, 0x45, 0x54, 0x10,
	0x04, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_offering_v2_pricing_rule_enums_proto_rawDescOnce sync.Once
	file_moego_models_offering_v2_pricing_rule_enums_proto_rawDescData = file_moego_models_offering_v2_pricing_rule_enums_proto_rawDesc
)

func file_moego_models_offering_v2_pricing_rule_enums_proto_rawDescGZIP() []byte {
	file_moego_models_offering_v2_pricing_rule_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_offering_v2_pricing_rule_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_offering_v2_pricing_rule_enums_proto_rawDescData)
	})
	return file_moego_models_offering_v2_pricing_rule_enums_proto_rawDescData
}

var file_moego_models_offering_v2_pricing_rule_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_moego_models_offering_v2_pricing_rule_enums_proto_goTypes = []interface{}{
	(RuleType)(0),      // 0: moego.models.offering.v2.RuleType
	(ConditionType)(0), // 1: moego.models.offering.v2.ConditionType
	(EffectType)(0),    // 2: moego.models.offering.v2.EffectType
	(RuleApplyType)(0), // 3: moego.models.offering.v2.RuleApplyType
}
var file_moego_models_offering_v2_pricing_rule_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_offering_v2_pricing_rule_enums_proto_init() }
func file_moego_models_offering_v2_pricing_rule_enums_proto_init() {
	if File_moego_models_offering_v2_pricing_rule_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_offering_v2_pricing_rule_enums_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_offering_v2_pricing_rule_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_offering_v2_pricing_rule_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_offering_v2_pricing_rule_enums_proto_enumTypes,
	}.Build()
	File_moego_models_offering_v2_pricing_rule_enums_proto = out.File
	file_moego_models_offering_v2_pricing_rule_enums_proto_rawDesc = nil
	file_moego_models_offering_v2_pricing_rule_enums_proto_goTypes = nil
	file_moego_models_offering_v2_pricing_rule_enums_proto_depIdxs = nil
}
