// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/subscription/v1/data_migration_models.proto

package subscriptionmodpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	interval "google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// product data
type ProductData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// mid
	Mid string `protobuf:"bytes,2,opt,name=mid,proto3" json:"mid,omitempty"`
	// name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// type
	Type Product_Type `protobuf:"varint,5,opt,name=type,proto3,enum=moego.models.subscription.v1.Product_Type" json:"type,omitempty"`
	// seller
	Seller *User `protobuf:"bytes,6,opt,name=seller,proto3" json:"seller,omitempty"`
	// purchase limit
	PurchaseLimit *PurchaseLimit `protobuf:"bytes,7,opt,name=purchase_limit,json=purchaseLimit,proto3" json:"purchase_limit,omitempty"`
	// business type
	BusinessType Product_BusinessType `protobuf:"varint,8,opt,name=business_type,json=businessType,proto3,enum=moego.models.subscription.v1.Product_BusinessType" json:"business_type,omitempty"`
}

func (x *ProductData) Reset() {
	*x = ProductData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_data_migration_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductData) ProtoMessage() {}

func (x *ProductData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_data_migration_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductData.ProtoReflect.Descriptor instead.
func (*ProductData) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_data_migration_models_proto_rawDescGZIP(), []int{0}
}

func (x *ProductData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ProductData) GetMid() string {
	if x != nil {
		return x.Mid
	}
	return ""
}

func (x *ProductData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProductData) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ProductData) GetType() Product_Type {
	if x != nil {
		return x.Type
	}
	return Product_TYPE_UNSPECIFIED
}

func (x *ProductData) GetSeller() *User {
	if x != nil {
		return x.Seller
	}
	return nil
}

func (x *ProductData) GetPurchaseLimit() *PurchaseLimit {
	if x != nil {
		return x.PurchaseLimit
	}
	return nil
}

func (x *ProductData) GetBusinessType() Product_BusinessType {
	if x != nil {
		return x.BusinessType
	}
	return Product_BUSINESS_TYPE_UNSPECIFIED
}

// price data
type PriceData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// mid
	Mid string `protobuf:"bytes,2,opt,name=mid,proto3" json:"mid,omitempty"`
	// product id
	ProductId int64 `protobuf:"varint,3,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	// name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// type
	Type Price_Type `protobuf:"varint,5,opt,name=type,proto3,enum=moego.models.subscription.v1.Price_Type" json:"type,omitempty"`
	// unit_amount
	UnitAmount *money.Money `protobuf:"bytes,6,opt,name=unit_amount,json=unitAmount,proto3" json:"unit_amount,omitempty"`
	// billing cycle
	BillingCycle *v1.TimePeriod `protobuf:"bytes,7,opt,name=billing_cycle,json=billingCycle,proto3" json:"billing_cycle,omitempty"`
}

func (x *PriceData) Reset() {
	*x = PriceData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_data_migration_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceData) ProtoMessage() {}

func (x *PriceData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_data_migration_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceData.ProtoReflect.Descriptor instead.
func (*PriceData) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_data_migration_models_proto_rawDescGZIP(), []int{1}
}

func (x *PriceData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PriceData) GetMid() string {
	if x != nil {
		return x.Mid
	}
	return ""
}

func (x *PriceData) GetProductId() int64 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *PriceData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PriceData) GetType() Price_Type {
	if x != nil {
		return x.Type
	}
	return Price_TYPE_UNSPECIFIED
}

func (x *PriceData) GetUnitAmount() *money.Money {
	if x != nil {
		return x.UnitAmount
	}
	return nil
}

func (x *PriceData) GetBillingCycle() *v1.TimePeriod {
	if x != nil {
		return x.BillingCycle
	}
	return nil
}

// feature data
type FeatureData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// mid
	Mid string `protobuf:"bytes,2,opt,name=mid,proto3" json:"mid,omitempty"`
	// product id
	ProductId int64 `protobuf:"varint,3,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	// name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// description
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// 特性 key
	Key Feature_Key `protobuf:"varint,6,opt,name=key,proto3,enum=moego.models.subscription.v1.Feature_Key" json:"key,omitempty"`
	// 特性设置
	Setting *Feature_Setting `protobuf:"bytes,7,opt,name=setting,proto3" json:"setting,omitempty"`
}

func (x *FeatureData) Reset() {
	*x = FeatureData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_data_migration_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureData) ProtoMessage() {}

func (x *FeatureData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_data_migration_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureData.ProtoReflect.Descriptor instead.
func (*FeatureData) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_data_migration_models_proto_rawDescGZIP(), []int{2}
}

func (x *FeatureData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FeatureData) GetMid() string {
	if x != nil {
		return x.Mid
	}
	return ""
}

func (x *FeatureData) GetProductId() int64 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *FeatureData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FeatureData) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *FeatureData) GetKey() Feature_Key {
	if x != nil {
		return x.Key
	}
	return Feature_KEY_UNSPECIFIED
}

func (x *FeatureData) GetSetting() *Feature_Setting {
	if x != nil {
		return x.Setting
	}
	return nil
}

// subscription data
type SubscriptionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// mid
	Mid string `protobuf:"bytes,2,opt,name=mid,proto3" json:"mid,omitempty"`
	// buyer
	Buyer *User `protobuf:"bytes,3,opt,name=buyer,proto3" json:"buyer,omitempty"`
	// seller
	Seller *User `protobuf:"bytes,4,opt,name=seller,proto3" json:"seller,omitempty"`
	// price id
	PriceId int64 `protobuf:"varint,5,opt,name=price_id,json=priceId,proto3" json:"price_id,omitempty"`
	// tax id
	TaxId int64 `protobuf:"varint,6,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// validity period
	ValidityPeriod *interval.Interval `protobuf:"bytes,7,opt,name=validity_period,json=validityPeriod,proto3" json:"validity_period,omitempty"`
}

func (x *SubscriptionData) Reset() {
	*x = SubscriptionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_data_migration_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionData) ProtoMessage() {}

func (x *SubscriptionData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_data_migration_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionData.ProtoReflect.Descriptor instead.
func (*SubscriptionData) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_data_migration_models_proto_rawDescGZIP(), []int{3}
}

func (x *SubscriptionData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SubscriptionData) GetMid() string {
	if x != nil {
		return x.Mid
	}
	return ""
}

func (x *SubscriptionData) GetBuyer() *User {
	if x != nil {
		return x.Buyer
	}
	return nil
}

func (x *SubscriptionData) GetSeller() *User {
	if x != nil {
		return x.Seller
	}
	return nil
}

func (x *SubscriptionData) GetPriceId() int64 {
	if x != nil {
		return x.PriceId
	}
	return 0
}

func (x *SubscriptionData) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *SubscriptionData) GetValidityPeriod() *interval.Interval {
	if x != nil {
		return x.ValidityPeriod
	}
	return nil
}

// entitlement data
type EntitlementData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// mid
	Mid string `protobuf:"bytes,2,opt,name=mid,proto3" json:"mid,omitempty"`
	// subscription id
	SubscriptionId int64 `protobuf:"varint,3,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	// feature id
	FeatureId int64 `protobuf:"varint,4,opt,name=feature_id,json=featureId,proto3" json:"feature_id,omitempty"`
	// setting
	Setting *Feature_Setting `protobuf:"bytes,5,opt,name=setting,proto3" json:"setting,omitempty"`
}

func (x *EntitlementData) Reset() {
	*x = EntitlementData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_data_migration_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EntitlementData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntitlementData) ProtoMessage() {}

func (x *EntitlementData) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_data_migration_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntitlementData.ProtoReflect.Descriptor instead.
func (*EntitlementData) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_data_migration_models_proto_rawDescGZIP(), []int{4}
}

func (x *EntitlementData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EntitlementData) GetMid() string {
	if x != nil {
		return x.Mid
	}
	return ""
}

func (x *EntitlementData) GetSubscriptionId() int64 {
	if x != nil {
		return x.SubscriptionId
	}
	return 0
}

func (x *EntitlementData) GetFeatureId() int64 {
	if x != nil {
		return x.FeatureId
	}
	return 0
}

func (x *EntitlementData) GetSetting() *Feature_Setting {
	if x != nil {
		return x.Setting
	}
	return nil
}

var File_moego_models_subscription_v1_data_migration_models_proto protoreflect.FileDescriptor

var file_moego_models_subscription_v1_data_migration_models_proto_rawDesc = []byte{
	0x0a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8e, 0x03, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x3e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x3a, 0x0a, 0x06, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x06, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x0e, 0x70,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x52, 0x0d, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12,
	0x57, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x2e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0x94, 0x02, 0x0a, 0x09, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x2e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x33, 0x0a, 0x0b, 0x75, 0x6e, 0x69,
	0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0a, 0x75, 0x6e, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3f,
	0x0a, 0x0d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x52, 0x0c, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x22,
	0x8a, 0x02, 0x0a, 0x0b, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x4b, 0x65, 0x79, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x47, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x9c, 0x02, 0x0a,
	0x10, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x69, 0x64, 0x12, 0x38, 0x0a, 0x05, 0x62, 0x75, 0x79, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x62, 0x75, 0x79, 0x65, 0x72, 0x12, 0x3a, 0x0a,
	0x06, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x06, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x0e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x69, 0x74, 0x79, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0xc4, 0x01, 0x0a, 0x0f,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x69,
	0x64, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x07, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x42, 0x8d, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x3b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x6d, 0x6f, 0x64,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_subscription_v1_data_migration_models_proto_rawDescOnce sync.Once
	file_moego_models_subscription_v1_data_migration_models_proto_rawDescData = file_moego_models_subscription_v1_data_migration_models_proto_rawDesc
)

func file_moego_models_subscription_v1_data_migration_models_proto_rawDescGZIP() []byte {
	file_moego_models_subscription_v1_data_migration_models_proto_rawDescOnce.Do(func() {
		file_moego_models_subscription_v1_data_migration_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_subscription_v1_data_migration_models_proto_rawDescData)
	})
	return file_moego_models_subscription_v1_data_migration_models_proto_rawDescData
}

var file_moego_models_subscription_v1_data_migration_models_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_models_subscription_v1_data_migration_models_proto_goTypes = []interface{}{
	(*ProductData)(nil),       // 0: moego.models.subscription.v1.ProductData
	(*PriceData)(nil),         // 1: moego.models.subscription.v1.PriceData
	(*FeatureData)(nil),       // 2: moego.models.subscription.v1.FeatureData
	(*SubscriptionData)(nil),  // 3: moego.models.subscription.v1.SubscriptionData
	(*EntitlementData)(nil),   // 4: moego.models.subscription.v1.EntitlementData
	(Product_Type)(0),         // 5: moego.models.subscription.v1.Product.Type
	(*User)(nil),              // 6: moego.models.subscription.v1.User
	(*PurchaseLimit)(nil),     // 7: moego.models.subscription.v1.PurchaseLimit
	(Product_BusinessType)(0), // 8: moego.models.subscription.v1.Product.BusinessType
	(Price_Type)(0),           // 9: moego.models.subscription.v1.Price.Type
	(*money.Money)(nil),       // 10: google.type.Money
	(*v1.TimePeriod)(nil),     // 11: moego.utils.v1.TimePeriod
	(Feature_Key)(0),          // 12: moego.models.subscription.v1.Feature.Key
	(*Feature_Setting)(nil),   // 13: moego.models.subscription.v1.Feature.Setting
	(*interval.Interval)(nil), // 14: google.type.Interval
}
var file_moego_models_subscription_v1_data_migration_models_proto_depIdxs = []int32{
	5,  // 0: moego.models.subscription.v1.ProductData.type:type_name -> moego.models.subscription.v1.Product.Type
	6,  // 1: moego.models.subscription.v1.ProductData.seller:type_name -> moego.models.subscription.v1.User
	7,  // 2: moego.models.subscription.v1.ProductData.purchase_limit:type_name -> moego.models.subscription.v1.PurchaseLimit
	8,  // 3: moego.models.subscription.v1.ProductData.business_type:type_name -> moego.models.subscription.v1.Product.BusinessType
	9,  // 4: moego.models.subscription.v1.PriceData.type:type_name -> moego.models.subscription.v1.Price.Type
	10, // 5: moego.models.subscription.v1.PriceData.unit_amount:type_name -> google.type.Money
	11, // 6: moego.models.subscription.v1.PriceData.billing_cycle:type_name -> moego.utils.v1.TimePeriod
	12, // 7: moego.models.subscription.v1.FeatureData.key:type_name -> moego.models.subscription.v1.Feature.Key
	13, // 8: moego.models.subscription.v1.FeatureData.setting:type_name -> moego.models.subscription.v1.Feature.Setting
	6,  // 9: moego.models.subscription.v1.SubscriptionData.buyer:type_name -> moego.models.subscription.v1.User
	6,  // 10: moego.models.subscription.v1.SubscriptionData.seller:type_name -> moego.models.subscription.v1.User
	14, // 11: moego.models.subscription.v1.SubscriptionData.validity_period:type_name -> google.type.Interval
	13, // 12: moego.models.subscription.v1.EntitlementData.setting:type_name -> moego.models.subscription.v1.Feature.Setting
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_moego_models_subscription_v1_data_migration_models_proto_init() }
func file_moego_models_subscription_v1_data_migration_models_proto_init() {
	if File_moego_models_subscription_v1_data_migration_models_proto != nil {
		return
	}
	file_moego_models_subscription_v1_subscription_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_subscription_v1_data_migration_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_data_migration_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_data_migration_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_data_migration_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_data_migration_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EntitlementData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_subscription_v1_data_migration_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_subscription_v1_data_migration_models_proto_goTypes,
		DependencyIndexes: file_moego_models_subscription_v1_data_migration_models_proto_depIdxs,
		MessageInfos:      file_moego_models_subscription_v1_data_migration_models_proto_msgTypes,
	}.Build()
	File_moego_models_subscription_v1_data_migration_models_proto = out.File
	file_moego_models_subscription_v1_data_migration_models_proto_rawDesc = nil
	file_moego_models_subscription_v1_data_migration_models_proto_goTypes = nil
	file_moego_models_subscription_v1_data_migration_models_proto_depIdxs = nil
}
