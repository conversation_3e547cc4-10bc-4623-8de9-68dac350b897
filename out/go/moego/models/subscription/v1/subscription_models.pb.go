// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/subscription/v1/subscription_models.proto

package subscriptionmodpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	interval "google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 状态
type Subscription_Status int32

const (
	// 未定义
	Subscription_STATUS_UNSPECIFIED Subscription_Status = 0
	// 试用期，试用期内可以免费使用服务
	Subscription_TRIAL Subscription_Status = 1
	// 待激活，无法使用服务
	Subscription_PENDING Subscription_Status = 2
	// 激活，一定可以使用服务
	Subscription_ACTIVE Subscription_Status = 3
	// 宽限期，宽限期内可以继续使用服务，但会累计欠费
	Subscription_GRACE Subscription_Status = 4
	// 已过期，无法使用服务，但订阅仍然保留
	Subscription_EXPIRED Subscription_Status = 5
	// 已取消，无法使用服务，且会取消订阅
	Subscription_CANCELLED Subscription_Status = 6
	// 暂停，暂停期内无法使用服务，但会保留订阅
	Subscription_PAUSED Subscription_Status = 7
	// 未完成，订阅创建未完成
	Subscription_INCOMPLETE Subscription_Status = 8
)

// Enum value maps for Subscription_Status.
var (
	Subscription_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "TRIAL",
		2: "PENDING",
		3: "ACTIVE",
		4: "GRACE",
		5: "EXPIRED",
		6: "CANCELLED",
		7: "PAUSED",
		8: "INCOMPLETE",
	}
	Subscription_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"TRIAL":              1,
		"PENDING":            2,
		"ACTIVE":             3,
		"GRACE":              4,
		"EXPIRED":            5,
		"CANCELLED":          6,
		"PAUSED":             7,
		"INCOMPLETE":         8,
	}
)

func (x Subscription_Status) Enum() *Subscription_Status {
	p := new(Subscription_Status)
	*p = x
	return p
}

func (x Subscription_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Subscription_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_subscription_v1_subscription_models_proto_enumTypes[0].Descriptor()
}

func (Subscription_Status) Type() protoreflect.EnumType {
	return &file_moego_models_subscription_v1_subscription_models_proto_enumTypes[0]
}

func (x Subscription_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Subscription_Status.Descriptor instead.
func (Subscription_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{0, 0}
}

// 状态
type PurchaseDetail_Status int32

const (
	// 未定义
	PurchaseDetail_STATUS_UNSPECIFIED PurchaseDetail_Status = 0
	// 有效
	PurchaseDetail_VALID PurchaseDetail_Status = 1
	// 无效
	PurchaseDetail_INVALID PurchaseDetail_Status = 2
)

// Enum value maps for PurchaseDetail_Status.
var (
	PurchaseDetail_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "VALID",
		2: "INVALID",
	}
	PurchaseDetail_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"VALID":              1,
		"INVALID":            2,
	}
)

func (x PurchaseDetail_Status) Enum() *PurchaseDetail_Status {
	p := new(PurchaseDetail_Status)
	*p = x
	return p
}

func (x PurchaseDetail_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PurchaseDetail_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_subscription_v1_subscription_models_proto_enumTypes[1].Descriptor()
}

func (PurchaseDetail_Status) Type() protoreflect.EnumType {
	return &file_moego_models_subscription_v1_subscription_models_proto_enumTypes[1]
}

func (x PurchaseDetail_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PurchaseDetail_Status.Descriptor instead.
func (PurchaseDetail_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{4, 0}
}

// 产品类型
type Product_Type int32

const (
	// 未定义
	Product_TYPE_UNSPECIFIED Product_Type = 0
	// 套餐，所有订阅必须从套餐中创建
	Product_PLAN Product_Type = 1
	// 增值服务，可单独购买，也可作为套餐的附加服务合并计费
	Product_ADDON Product_Type = 2
)

// Enum value maps for Product_Type.
var (
	Product_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "PLAN",
		2: "ADDON",
	}
	Product_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"PLAN":             1,
		"ADDON":            2,
	}
)

func (x Product_Type) Enum() *Product_Type {
	p := new(Product_Type)
	*p = x
	return p
}

func (x Product_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Product_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_subscription_v1_subscription_models_proto_enumTypes[2].Descriptor()
}

func (Product_Type) Type() protoreflect.EnumType {
	return &file_moego_models_subscription_v1_subscription_models_proto_enumTypes[2]
}

func (x Product_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Product_Type.Descriptor instead.
func (Product_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{5, 0}
}

// 业务类型
type Product_BusinessType int32

const (
	// 未定义
	Product_BUSINESS_TYPE_UNSPECIFIED Product_BusinessType = 0
	// membership
	Product_MEMBERSHIP Product_BusinessType = 1
	// accounting
	Product_ACCOUNTING Product_BusinessType = 2
	// payroll
	Product_PAYROLL Product_BusinessType = 3
)

// Enum value maps for Product_BusinessType.
var (
	Product_BusinessType_name = map[int32]string{
		0: "BUSINESS_TYPE_UNSPECIFIED",
		1: "MEMBERSHIP",
		2: "ACCOUNTING",
		3: "PAYROLL",
	}
	Product_BusinessType_value = map[string]int32{
		"BUSINESS_TYPE_UNSPECIFIED": 0,
		"MEMBERSHIP":                1,
		"ACCOUNTING":                2,
		"PAYROLL":                   3,
	}
)

func (x Product_BusinessType) Enum() *Product_BusinessType {
	p := new(Product_BusinessType)
	*p = x
	return p
}

func (x Product_BusinessType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Product_BusinessType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_subscription_v1_subscription_models_proto_enumTypes[3].Descriptor()
}

func (Product_BusinessType) Type() protoreflect.EnumType {
	return &file_moego_models_subscription_v1_subscription_models_proto_enumTypes[3]
}

func (x Product_BusinessType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Product_BusinessType.Descriptor instead.
func (Product_BusinessType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{5, 1}
}

// 计价类型
type Price_Type int32

const (
	// 未定义
	Price_TYPE_UNSPECIFIED Price_Type = 0
	// 一次性付费
	Price_ONE_TIME Price_Type = 1
	// 循环付费
	Price_CYCLE Price_Type = 2
)

// Enum value maps for Price_Type.
var (
	Price_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "ONE_TIME",
		2: "CYCLE",
	}
	Price_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"ONE_TIME":         1,
		"CYCLE":            2,
	}
)

func (x Price_Type) Enum() *Price_Type {
	p := new(Price_Type)
	*p = x
	return p
}

func (x Price_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Price_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_subscription_v1_subscription_models_proto_enumTypes[4].Descriptor()
}

func (Price_Type) Type() protoreflect.EnumType {
	return &file_moego_models_subscription_v1_subscription_models_proto_enumTypes[4]
}

func (x Price_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Price_Type.Descriptor instead.
func (Price_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{8, 0}
}

// 用户类型
type User_Type int32

const (
	// 未定义
	User_TYPE_UNSPECIFIED User_Type = 0
	// MoeGo
	User_MOEGO User_Type = 1
	// 账号
	User_ACCOUNT User_Type = 2
	// 企业
	User_ENTERPRISE User_Type = 3
	// 公司
	User_COMPANY User_Type = 4
	// 商家
	User_BUSINESS User_Type = 5
	// 顾客
	User_CUSTOMER User_Type = 6
	// 员工
	User_STAFF User_Type = 7
)

// Enum value maps for User_Type.
var (
	User_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "MOEGO",
		2: "ACCOUNT",
		3: "ENTERPRISE",
		4: "COMPANY",
		5: "BUSINESS",
		6: "CUSTOMER",
		7: "STAFF",
	}
	User_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"MOEGO":            1,
		"ACCOUNT":          2,
		"ENTERPRISE":       3,
		"COMPANY":          4,
		"BUSINESS":         5,
		"CUSTOMER":         6,
		"STAFF":            7,
	}
)

func (x User_Type) Enum() *User_Type {
	p := new(User_Type)
	*p = x
	return p
}

func (x User_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (User_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_subscription_v1_subscription_models_proto_enumTypes[5].Descriptor()
}

func (User_Type) Type() protoreflect.EnumType {
	return &file_moego_models_subscription_v1_subscription_models_proto_enumTypes[5]
}

func (x User_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use User_Type.Descriptor instead.
func (User_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{9, 0}
}

// 购买限制类型
type PurchaseLimit_Type int32

const (
	// 未定义
	PurchaseLimit_TYPE_UNSPECIFIED PurchaseLimit_Type = 0
	// 无限制
	PurchaseLimit_UNLIMITED PurchaseLimit_Type = 1
	// 限制购买次数
	PurchaseLimit_COUNT PurchaseLimit_Type = 2
)

// Enum value maps for PurchaseLimit_Type.
var (
	PurchaseLimit_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "UNLIMITED",
		2: "COUNT",
	}
	PurchaseLimit_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"UNLIMITED":        1,
		"COUNT":            2,
	}
)

func (x PurchaseLimit_Type) Enum() *PurchaseLimit_Type {
	p := new(PurchaseLimit_Type)
	*p = x
	return p
}

func (x PurchaseLimit_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PurchaseLimit_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_subscription_v1_subscription_models_proto_enumTypes[6].Descriptor()
}

func (PurchaseLimit_Type) Type() protoreflect.EnumType {
	return &file_moego_models_subscription_v1_subscription_models_proto_enumTypes[6]
}

func (x PurchaseLimit_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PurchaseLimit_Type.Descriptor instead.
func (PurchaseLimit_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{10, 0}
}

// 功能键，用于业务系统识别功能 = feature code
type Feature_Key int32

const (
	// 未定义
	Feature_KEY_UNSPECIFIED Feature_Key = 0
	// Accounting basic
	Feature_ACCOUNTING_BASIC Feature_Key = 1
	// Accounting full-service
	Feature_ACCOUNTING_FULL_SERVICE Feature_Key = 2
	// ACCOUNTING_ONBOARDING, 只包含 onboarding 操作，不含其他权益
	Feature_ACCOUNTING_ONBOARDING Feature_Key = 3
	// Membership service discount
	Feature_MEMBERSHIP_SERVICE_DISCOUNT Feature_Key = 4
	// Membership add on discount
	Feature_MEMBERSHIP_ADDON_DISCOUNT Feature_Key = 5
	// Membership product discount
	Feature_MEMBERSHIP_PRODUCT_DISCOUNT Feature_Key = 6
	// Membership service & add on quantity
	Feature_MEMBERSHIP_SERVICE_ADDON_QUANTITY Feature_Key = 7
	// Credit
	Feature_CREDIT_CREDIT_POINT Feature_Key = 8
)

// Enum value maps for Feature_Key.
var (
	Feature_Key_name = map[int32]string{
		0: "KEY_UNSPECIFIED",
		1: "ACCOUNTING_BASIC",
		2: "ACCOUNTING_FULL_SERVICE",
		3: "ACCOUNTING_ONBOARDING",
		4: "MEMBERSHIP_SERVICE_DISCOUNT",
		5: "MEMBERSHIP_ADDON_DISCOUNT",
		6: "MEMBERSHIP_PRODUCT_DISCOUNT",
		7: "MEMBERSHIP_SERVICE_ADDON_QUANTITY",
		8: "CREDIT_CREDIT_POINT",
	}
	Feature_Key_value = map[string]int32{
		"KEY_UNSPECIFIED":                   0,
		"ACCOUNTING_BASIC":                  1,
		"ACCOUNTING_FULL_SERVICE":           2,
		"ACCOUNTING_ONBOARDING":             3,
		"MEMBERSHIP_SERVICE_DISCOUNT":       4,
		"MEMBERSHIP_ADDON_DISCOUNT":         5,
		"MEMBERSHIP_PRODUCT_DISCOUNT":       6,
		"MEMBERSHIP_SERVICE_ADDON_QUANTITY": 7,
		"CREDIT_CREDIT_POINT":               8,
	}
)

func (x Feature_Key) Enum() *Feature_Key {
	p := new(Feature_Key)
	*p = x
	return p
}

func (x Feature_Key) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Feature_Key) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_subscription_v1_subscription_models_proto_enumTypes[7].Descriptor()
}

func (Feature_Key) Type() protoreflect.EnumType {
	return &file_moego_models_subscription_v1_subscription_models_proto_enumTypes[7]
}

func (x Feature_Key) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Feature_Key.Descriptor instead.
func (Feature_Key) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{11, 0}
}

// 状态
type License_Status int32

const (
	// 未定义
	License_STATUS_UNSPECIFIED License_Status = 0
	// 有效
	License_VALID License_Status = 1
	// 无效
	License_INVALID License_Status = 2
)

// Enum value maps for License_Status.
var (
	License_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "VALID",
		2: "INVALID",
	}
	License_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"VALID":              1,
		"INVALID":            2,
	}
)

func (x License_Status) Enum() *License_Status {
	p := new(License_Status)
	*p = x
	return p
}

func (x License_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (License_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_subscription_v1_subscription_models_proto_enumTypes[8].Descriptor()
}

func (License_Status) Type() protoreflect.EnumType {
	return &file_moego_models_subscription_v1_subscription_models_proto_enumTypes[8]
}

func (x License_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use License_Status.Descriptor instead.
func (License_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{15, 0}
}

// 类型
type Revision_Type int32

const (
	// 未定义
	Revision_TYPE_UNSPECIFIED Revision_Type = 0
	// 创建订阅
	Revision_CREATE_SUBSCRIPTION Revision_Type = 1
	// 修改订阅
	Revision_CHANGE_SUBSCRIPTION Revision_Type = 2
	// 升级订阅
	Revision_UPGRADE_SUBSCRIPTION Revision_Type = 3
	// 降级订阅
	Revision_DOWNGRADE_SUBSCRIPTION Revision_Type = 4
	// 购买产品
	Revision_PURCHASE_PRODUCT Revision_Type = 5
	// 分配 License
	Revision_ASSIGN_LICENSE Revision_Type = 6
	// 修改 Entitlement
	Revision_CHANGE_ENTITLEMENT Revision_Type = 7
)

// Enum value maps for Revision_Type.
var (
	Revision_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "CREATE_SUBSCRIPTION",
		2: "CHANGE_SUBSCRIPTION",
		3: "UPGRADE_SUBSCRIPTION",
		4: "DOWNGRADE_SUBSCRIPTION",
		5: "PURCHASE_PRODUCT",
		6: "ASSIGN_LICENSE",
		7: "CHANGE_ENTITLEMENT",
	}
	Revision_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED":       0,
		"CREATE_SUBSCRIPTION":    1,
		"CHANGE_SUBSCRIPTION":    2,
		"UPGRADE_SUBSCRIPTION":   3,
		"DOWNGRADE_SUBSCRIPTION": 4,
		"PURCHASE_PRODUCT":       5,
		"ASSIGN_LICENSE":         6,
		"CHANGE_ENTITLEMENT":     7,
	}
)

func (x Revision_Type) Enum() *Revision_Type {
	p := new(Revision_Type)
	*p = x
	return p
}

func (x Revision_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Revision_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_subscription_v1_subscription_models_proto_enumTypes[9].Descriptor()
}

func (Revision_Type) Type() protoreflect.EnumType {
	return &file_moego_models_subscription_v1_subscription_models_proto_enumTypes[9]
}

func (x Revision_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Revision_Type.Descriptor instead.
func (Revision_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{17, 0}
}

// update credit type
type UpdateCredit_Type int32

const (
	// meaningless
	UpdateCredit_TYPE_UNSPECIFIED UpdateCredit_Type = 0
	// payment
	UpdateCredit_TYPE_PAYMENT UpdateCredit_Type = 1
	// refund
	UpdateCredit_TYPE_REFUND UpdateCredit_Type = 2
	// keep the change as credit
	UpdateCredit_TYPE_KEEP_CHANGE UpdateCredit_Type = 3
	// manual adjustment
	UpdateCredit_TYPE_MANUAL UpdateCredit_Type = 4
)

// Enum value maps for UpdateCredit_Type.
var (
	UpdateCredit_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "TYPE_PAYMENT",
		2: "TYPE_REFUND",
		3: "TYPE_KEEP_CHANGE",
		4: "TYPE_MANUAL",
	}
	UpdateCredit_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"TYPE_PAYMENT":     1,
		"TYPE_REFUND":      2,
		"TYPE_KEEP_CHANGE": 3,
		"TYPE_MANUAL":      4,
	}
)

func (x UpdateCredit_Type) Enum() *UpdateCredit_Type {
	p := new(UpdateCredit_Type)
	*p = x
	return p
}

func (x UpdateCredit_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateCredit_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_subscription_v1_subscription_models_proto_enumTypes[10].Descriptor()
}

func (UpdateCredit_Type) Type() protoreflect.EnumType {
	return &file_moego_models_subscription_v1_subscription_models_proto_enumTypes[10]
}

func (x UpdateCredit_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateCredit_Type.Descriptor instead.
func (UpdateCredit_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{21, 0}
}

// update credit reason
// the front-end copy may change
// and strings cannot be stored directly
type UpdateCredit_Reason int32

const (
	// meaningless
	UpdateCredit_REASON_UNSPECIFIED UpdateCredit_Reason = 0
	// refund
	UpdateCredit_REFUND UpdateCredit_Reason = 1
	// bonus
	UpdateCredit_BONUS UpdateCredit_Reason = 2
	// transfer from perks
	UpdateCredit_TRANSFER_FROM_PERKS UpdateCredit_Reason = 3
	// other
	UpdateCredit_OTHER UpdateCredit_Reason = 99
)

// Enum value maps for UpdateCredit_Reason.
var (
	UpdateCredit_Reason_name = map[int32]string{
		0:  "REASON_UNSPECIFIED",
		1:  "REFUND",
		2:  "BONUS",
		3:  "TRANSFER_FROM_PERKS",
		99: "OTHER",
	}
	UpdateCredit_Reason_value = map[string]int32{
		"REASON_UNSPECIFIED":  0,
		"REFUND":              1,
		"BONUS":               2,
		"TRANSFER_FROM_PERKS": 3,
		"OTHER":               99,
	}
)

func (x UpdateCredit_Reason) Enum() *UpdateCredit_Reason {
	p := new(UpdateCredit_Reason)
	*p = x
	return p
}

func (x UpdateCredit_Reason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateCredit_Reason) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_subscription_v1_subscription_models_proto_enumTypes[11].Descriptor()
}

func (UpdateCredit_Reason) Type() protoreflect.EnumType {
	return &file_moego_models_subscription_v1_subscription_models_proto_enumTypes[11]
}

func (x UpdateCredit_Reason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateCredit_Reason.Descriptor instead.
func (UpdateCredit_Reason) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{21, 1}
}

// 订阅
type Subscription struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称，继承自 plan product
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 描述，继承自 plan product
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// 状态
	Status Subscription_Status `protobuf:"varint,4,opt,name=status,proto3,enum=moego.models.subscription.v1.Subscription_Status" json:"status,omitempty"`
	// 订阅的有效期，起点为当前扣款时间，终点为下一次扣款时间
	ValidityPeriod *interval.Interval `protobuf:"bytes,5,opt,name=validity_period,json=validityPeriod,proto3" json:"validity_period,omitempty"`
	// 总商品价格，冗余字段，将所有的 PurchaseDetail 的价格加和得到
	TotalPrice *money.Money `protobuf:"bytes,6,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	// 总税，冗余字段，将所有的 PurchaseDetail 的税加和得到
	TotalTax *money.Money `protobuf:"bytes,7,opt,name=total_tax,json=totalTax,proto3" json:"total_tax,omitempty"`
	// 计费周期
	BillingCycle *v1.TimePeriod `protobuf:"bytes,8,opt,name=billing_cycle,json=billingCycle,proto3" json:"billing_cycle,omitempty"`
	// 宽限期
	GracePeriod *v1.TimePeriod `protobuf:"bytes,9,opt,name=grace_period,json=gracePeriod,proto3" json:"grace_period,omitempty"`
	// 是否在周期结束时取消订阅
	CancelAtPeriodEnd bool `protobuf:"varint,10,opt,name=cancel_at_period_end,json=cancelAtPeriodEnd,proto3" json:"cancel_at_period_end,omitempty"`
	// 买家
	Buyer *User `protobuf:"bytes,11,opt,name=buyer,proto3" json:"buyer,omitempty"`
	// 卖家
	Seller *User `protobuf:"bytes,12,opt,name=seller,proto3" json:"seller,omitempty"`
	// 每个订阅仅有唯一一个套餐产品，冗余字段，可在 PurchaseDetail 查到
	PlanProductId int64 `protobuf:"varint,13,opt,name=plan_product_id,json=planProductId,proto3" json:"plan_product_id,omitempty"`
	// 最新的 invoice ID
	LatestInvoiceId int64 `protobuf:"varint,14,opt,name=latest_invoice_id,json=latestInvoiceId,proto3" json:"latest_invoice_id,omitempty"`
	// payment 的映射 ID
	PaymentSubscriptionId int64 `protobuf:"varint,15,opt,name=payment_subscription_id,json=paymentSubscriptionId,proto3" json:"payment_subscription_id,omitempty"`
	// 最新的 card on file ID，即当前订阅的扣款卡
	LatestCardOnFileId string `protobuf:"bytes,16,opt,name=latest_card_on_file_id,json=latestCardOnFileId,proto3" json:"latest_card_on_file_id,omitempty"`
	// 执行者（staff）的 ID
	OperatorId int64 `protobuf:"varint,17,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// 暂停时间
	PausedAt *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=paused_at,json=pausedAt,proto3" json:"paused_at,omitempty"`
	// 自动恢复时间
	AutoResumeAt *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=auto_resume_at,json=autoResumeAt,proto3" json:"auto_resume_at,omitempty"`
	// 取消原因，只有处于未激活状态的订阅此字段才有效
	CancelReason string `protobuf:"bytes,20,opt,name=cancel_reason,json=cancelReason,proto3" json:"cancel_reason,omitempty"`
}

func (x *Subscription) Reset() {
	*x = Subscription{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Subscription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Subscription) ProtoMessage() {}

func (x *Subscription) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Subscription.ProtoReflect.Descriptor instead.
func (*Subscription) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{0}
}

func (x *Subscription) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Subscription) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Subscription) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Subscription) GetStatus() Subscription_Status {
	if x != nil {
		return x.Status
	}
	return Subscription_STATUS_UNSPECIFIED
}

func (x *Subscription) GetValidityPeriod() *interval.Interval {
	if x != nil {
		return x.ValidityPeriod
	}
	return nil
}

func (x *Subscription) GetTotalPrice() *money.Money {
	if x != nil {
		return x.TotalPrice
	}
	return nil
}

func (x *Subscription) GetTotalTax() *money.Money {
	if x != nil {
		return x.TotalTax
	}
	return nil
}

func (x *Subscription) GetBillingCycle() *v1.TimePeriod {
	if x != nil {
		return x.BillingCycle
	}
	return nil
}

func (x *Subscription) GetGracePeriod() *v1.TimePeriod {
	if x != nil {
		return x.GracePeriod
	}
	return nil
}

func (x *Subscription) GetCancelAtPeriodEnd() bool {
	if x != nil {
		return x.CancelAtPeriodEnd
	}
	return false
}

func (x *Subscription) GetBuyer() *User {
	if x != nil {
		return x.Buyer
	}
	return nil
}

func (x *Subscription) GetSeller() *User {
	if x != nil {
		return x.Seller
	}
	return nil
}

func (x *Subscription) GetPlanProductId() int64 {
	if x != nil {
		return x.PlanProductId
	}
	return 0
}

func (x *Subscription) GetLatestInvoiceId() int64 {
	if x != nil {
		return x.LatestInvoiceId
	}
	return 0
}

func (x *Subscription) GetPaymentSubscriptionId() int64 {
	if x != nil {
		return x.PaymentSubscriptionId
	}
	return 0
}

func (x *Subscription) GetLatestCardOnFileId() string {
	if x != nil {
		return x.LatestCardOnFileId
	}
	return ""
}

func (x *Subscription) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *Subscription) GetPausedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PausedAt
	}
	return nil
}

func (x *Subscription) GetAutoResumeAt() *timestamppb.Timestamp {
	if x != nil {
		return x.AutoResumeAt
	}
	return nil
}

func (x *Subscription) GetCancelReason() string {
	if x != nil {
		return x.CancelReason
	}
	return ""
}

// 订阅统计
type SubscriptionStat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 产品 ID
	ProductId int64 `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	// 订阅状态
	Status Subscription_Status `protobuf:"varint,2,opt,name=status,proto3,enum=moego.models.subscription.v1.Subscription_Status" json:"status,omitempty"`
	// 是否在周期结束时取消订阅
	CancelAtPeriodEnd bool `protobuf:"varint,3,opt,name=cancel_at_period_end,json=cancelAtPeriodEnd,proto3" json:"cancel_at_period_end,omitempty"`
	// 数量
	Count int32 `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *SubscriptionStat) Reset() {
	*x = SubscriptionStat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubscriptionStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionStat) ProtoMessage() {}

func (x *SubscriptionStat) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionStat.ProtoReflect.Descriptor instead.
func (*SubscriptionStat) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{1}
}

func (x *SubscriptionStat) GetProductId() int64 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *SubscriptionStat) GetStatus() Subscription_Status {
	if x != nil {
		return x.Status
	}
	return Subscription_STATUS_UNSPECIFIED
}

func (x *SubscriptionStat) GetCancelAtPeriodEnd() bool {
	if x != nil {
		return x.CancelAtPeriodEnd
	}
	return false
}

func (x *SubscriptionStat) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 购买者订阅信息
type BuyerSubscription struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 购买者
	Buyer *User `protobuf:"bytes,1,opt,name=buyer,proto3" json:"buyer,omitempty"`
	// 订阅信息
	Subscriptions []*Subscription `protobuf:"bytes,2,rep,name=subscriptions,proto3" json:"subscriptions,omitempty"`
	// 订阅总数
	Total int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *BuyerSubscription) Reset() {
	*x = BuyerSubscription{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuyerSubscription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuyerSubscription) ProtoMessage() {}

func (x *BuyerSubscription) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuyerSubscription.ProtoReflect.Descriptor instead.
func (*BuyerSubscription) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{2}
}

func (x *BuyerSubscription) GetBuyer() *User {
	if x != nil {
		return x.Buyer
	}
	return nil
}

func (x *BuyerSubscription) GetSubscriptions() []*Subscription {
	if x != nil {
		return x.Subscriptions
	}
	return nil
}

func (x *BuyerSubscription) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 购买行为
type Purchase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 产品 ID
	ProductId int64 `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	// 价格 ID
	PriceId int64 `protobuf:"varint,2,opt,name=price_id,json=priceId,proto3" json:"price_id,omitempty"`
	// 购买数量
	Quantity int32 `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// 需要应用的税 IDs
	TaxIds []int64 `protobuf:"varint,4,rep,packed,name=tax_ids,json=taxIds,proto3" json:"tax_ids,omitempty"`
}

func (x *Purchase) Reset() {
	*x = Purchase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Purchase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Purchase) ProtoMessage() {}

func (x *Purchase) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Purchase.ProtoReflect.Descriptor instead.
func (*Purchase) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{3}
}

func (x *Purchase) GetProductId() int64 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *Purchase) GetPriceId() int64 {
	if x != nil {
		return x.PriceId
	}
	return 0
}

func (x *Purchase) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *Purchase) GetTaxIds() []int64 {
	if x != nil {
		return x.TaxIds
	}
	return nil
}

// 购买详情，作为创建订阅时购买产品的快照
// 记录了购买时的价格和税，也用于计算订阅当前包含多少产品
type PurchaseDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 订阅 ID
	SubscriptionId int64 `protobuf:"varint,2,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	// 产品
	Product *Product `protobuf:"bytes,3,opt,name=product,proto3" json:"product,omitempty"`
	// 价格
	Price *Price `protobuf:"bytes,4,opt,name=price,proto3" json:"price,omitempty"`
	// 购买数量
	Quantity int32 `protobuf:"varint,5,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// 应用的税
	Taxes []*PurchaseDetail_Tax `protobuf:"bytes,6,rep,name=taxes,proto3" json:"taxes,omitempty"`
	// 状态
	Status PurchaseDetail_Status `protobuf:"varint,7,opt,name=status,proto3,enum=moego.models.subscription.v1.PurchaseDetail_Status" json:"status,omitempty"`
	// 买家
	Buyer *User `protobuf:"bytes,8,opt,name=buyer,proto3" json:"buyer,omitempty"`
	// 冗余总价和总税，用来给前端展示
	// 总价
	TotalPrice *money.Money `protobuf:"bytes,9,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	// 总税
	TotalTax *money.Money `protobuf:"bytes,10,opt,name=total_tax,json=totalTax,proto3" json:"total_tax,omitempty"`
	// 所应用的discount ID
	DiscountIds []int64 `protobuf:"varint,11,rep,packed,name=discount_ids,json=discountIds,proto3" json:"discount_ids,omitempty"`
}

func (x *PurchaseDetail) Reset() {
	*x = PurchaseDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PurchaseDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurchaseDetail) ProtoMessage() {}

func (x *PurchaseDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurchaseDetail.ProtoReflect.Descriptor instead.
func (*PurchaseDetail) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{4}
}

func (x *PurchaseDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PurchaseDetail) GetSubscriptionId() int64 {
	if x != nil {
		return x.SubscriptionId
	}
	return 0
}

func (x *PurchaseDetail) GetProduct() *Product {
	if x != nil {
		return x.Product
	}
	return nil
}

func (x *PurchaseDetail) GetPrice() *Price {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *PurchaseDetail) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *PurchaseDetail) GetTaxes() []*PurchaseDetail_Tax {
	if x != nil {
		return x.Taxes
	}
	return nil
}

func (x *PurchaseDetail) GetStatus() PurchaseDetail_Status {
	if x != nil {
		return x.Status
	}
	return PurchaseDetail_STATUS_UNSPECIFIED
}

func (x *PurchaseDetail) GetBuyer() *User {
	if x != nil {
		return x.Buyer
	}
	return nil
}

func (x *PurchaseDetail) GetTotalPrice() *money.Money {
	if x != nil {
		return x.TotalPrice
	}
	return nil
}

func (x *PurchaseDetail) GetTotalTax() *money.Money {
	if x != nil {
		return x.TotalTax
	}
	return nil
}

func (x *PurchaseDetail) GetDiscountIds() []int64 {
	if x != nil {
		return x.DiscountIds
	}
	return nil
}

// 产品
type Product struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 描述
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// 类型
	Type Product_Type `protobuf:"varint,4,opt,name=type,proto3,enum=moego.models.subscription.v1.Product_Type" json:"type,omitempty"`
	// seller 需要设置到 product metadata 中
	Seller *User `protobuf:"bytes,5,opt,name=seller,proto3" json:"seller,omitempty"`
	// 购买限制
	PurchaseLimit *PurchaseLimit `protobuf:"bytes,6,opt,name=purchase_limit,json=purchaseLimit,proto3" json:"purchase_limit,omitempty"`
	// 产品包含的功能 ID
	FeatureIds []int64 `protobuf:"varint,7,rep,packed,name=feature_ids,json=featureIds,proto3" json:"feature_ids,omitempty"`
	// 下游 payment 的映射 ID
	PaymentProductId int64 `protobuf:"varint,8,opt,name=payment_product_id,json=paymentProductId,proto3" json:"payment_product_id,omitempty"`
	// 业务类型
	BusinessType Product_BusinessType `protobuf:"varint,9,opt,name=business_type,json=businessType,proto3,enum=moego.models.subscription.v1.Product_BusinessType" json:"business_type,omitempty"`
	// extra
	Extra *ProductExtra `protobuf:"bytes,10,opt,name=extra,proto3" json:"extra,omitempty"`
}

func (x *Product) Reset() {
	*x = Product{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Product) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Product) ProtoMessage() {}

func (x *Product) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Product.ProtoReflect.Descriptor instead.
func (*Product) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{5}
}

func (x *Product) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Product) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Product) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Product) GetType() Product_Type {
	if x != nil {
		return x.Type
	}
	return Product_TYPE_UNSPECIFIED
}

func (x *Product) GetSeller() *User {
	if x != nil {
		return x.Seller
	}
	return nil
}

func (x *Product) GetPurchaseLimit() *PurchaseLimit {
	if x != nil {
		return x.PurchaseLimit
	}
	return nil
}

func (x *Product) GetFeatureIds() []int64 {
	if x != nil {
		return x.FeatureIds
	}
	return nil
}

func (x *Product) GetPaymentProductId() int64 {
	if x != nil {
		return x.PaymentProductId
	}
	return 0
}

func (x *Product) GetBusinessType() Product_BusinessType {
	if x != nil {
		return x.BusinessType
	}
	return Product_BUSINESS_TYPE_UNSPECIFIED
}

func (x *Product) GetExtra() *ProductExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

// 产品额外信息
type ProductExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 额外信息
	//
	// Types that are assignable to Extra:
	//
	//	*ProductExtra_AccountingExtra
	Extra isProductExtra_Extra `protobuf_oneof:"extra"`
}

func (x *ProductExtra) Reset() {
	*x = ProductExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductExtra) ProtoMessage() {}

func (x *ProductExtra) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductExtra.ProtoReflect.Descriptor instead.
func (*ProductExtra) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{6}
}

func (m *ProductExtra) GetExtra() isProductExtra_Extra {
	if m != nil {
		return m.Extra
	}
	return nil
}

func (x *ProductExtra) GetAccountingExtra() *AccountingExtra {
	if x, ok := x.GetExtra().(*ProductExtra_AccountingExtra); ok {
		return x.AccountingExtra
	}
	return nil
}

type isProductExtra_Extra interface {
	isProductExtra_Extra()
}

type ProductExtra_AccountingExtra struct {
	// accounting
	AccountingExtra *AccountingExtra `protobuf:"bytes,1,opt,name=accounting_extra,json=accountingExtra,proto3,oneof"`
}

func (*ProductExtra_AccountingExtra) isProductExtra_Extra() {}

// accounting 产品的额外信息
type AccountingExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用于显示在前端的文案之一，如："Basic including:", "Everything in basic, plus:"
	FeaturesTitle string `protobuf:"bytes,1,opt,name=features_title,json=featuresTitle,proto3" json:"features_title,omitempty"`
	// 用于显示在前端的feature文案，注意仅用于营销文案，无任何业务逻辑，
	// 如："Effortless integration with MoeGo for invoices, payroll, and capital tracking"
	Features []string `protobuf:"bytes,2,rep,name=features,proto3" json:"features,omitempty"`
	// 用于显示在前端的文案，展示浮动价格
	FloatingPrice *AccountingExtra_FloatingPrice `protobuf:"bytes,3,opt,name=floating_price,json=floatingPrice,proto3,oneof" json:"floating_price,omitempty"`
	// 用于显示在前端的文案，展示功能
	FeatureTexts []*AccountingExtra_Feature `protobuf:"bytes,4,rep,name=feature_texts,json=featureTexts,proto3" json:"feature_texts,omitempty"`
}

func (x *AccountingExtra) Reset() {
	*x = AccountingExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountingExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountingExtra) ProtoMessage() {}

func (x *AccountingExtra) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountingExtra.ProtoReflect.Descriptor instead.
func (*AccountingExtra) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{7}
}

func (x *AccountingExtra) GetFeaturesTitle() string {
	if x != nil {
		return x.FeaturesTitle
	}
	return ""
}

func (x *AccountingExtra) GetFeatures() []string {
	if x != nil {
		return x.Features
	}
	return nil
}

func (x *AccountingExtra) GetFloatingPrice() *AccountingExtra_FloatingPrice {
	if x != nil {
		return x.FloatingPrice
	}
	return nil
}

func (x *AccountingExtra) GetFeatureTexts() []*AccountingExtra_Feature {
	if x != nil {
		return x.FeatureTexts
	}
	return nil
}

// 价格
type Price struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 产品 ID
	ProductId int64 `protobuf:"varint,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 类型
	Type Price_Type `protobuf:"varint,4,opt,name=type,proto3,enum=moego.models.subscription.v1.Price_Type" json:"type,omitempty"`
	// 单价
	UnitAmount *money.Money `protobuf:"bytes,5,opt,name=unit_amount,json=unitAmount,proto3" json:"unit_amount,omitempty"`
	// 计费周期
	BillingCycle *v1.TimePeriod `protobuf:"bytes,6,opt,name=billing_cycle,json=billingCycle,proto3" json:"billing_cycle,omitempty"`
	// JSON格式的价格模型规则 TODO: 定义具体的规则，如固定计价、按量计价、阶梯计价等
	// 需要设置 application_fee_percent
	Rules string `protobuf:"bytes,7,opt,name=rules,proto3" json:"rules,omitempty"`
	// 下游 payment 的映射 ID，由于我们的 tax 设计比较蠢，这里暂时无法映射
	PaymentPriceId int64 `protobuf:"varint,8,opt,name=payment_price_id,json=paymentPriceId,proto3" json:"payment_price_id,omitempty"`
	// 预审
	Prequalification *Price_Prequalification `protobuf:"bytes,9,opt,name=prequalification,proto3" json:"prequalification,omitempty"`
}

func (x *Price) Reset() {
	*x = Price{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Price) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Price) ProtoMessage() {}

func (x *Price) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Price.ProtoReflect.Descriptor instead.
func (*Price) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{8}
}

func (x *Price) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Price) GetProductId() int64 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *Price) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Price) GetType() Price_Type {
	if x != nil {
		return x.Type
	}
	return Price_TYPE_UNSPECIFIED
}

func (x *Price) GetUnitAmount() *money.Money {
	if x != nil {
		return x.UnitAmount
	}
	return nil
}

func (x *Price) GetBillingCycle() *v1.TimePeriod {
	if x != nil {
		return x.BillingCycle
	}
	return nil
}

func (x *Price) GetRules() string {
	if x != nil {
		return x.Rules
	}
	return ""
}

func (x *Price) GetPaymentPriceId() int64 {
	if x != nil {
		return x.PaymentPriceId
	}
	return 0
}

func (x *Price) GetPrequalification() *Price_Prequalification {
	if x != nil {
		return x.Prequalification
	}
	return nil
}

// 用户
type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 类型
	Type User_Type `protobuf:"varint,2,opt,name=type,proto3,enum=moego.models.subscription.v1.User_Type" json:"type,omitempty"`
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{9}
}

func (x *User) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *User) GetType() User_Type {
	if x != nil {
		return x.Type
	}
	return User_TYPE_UNSPECIFIED
}

// 购买限制
type PurchaseLimit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 类型
	Type PurchaseLimit_Type `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.subscription.v1.PurchaseLimit_Type" json:"type,omitempty"`
	// 限制次数
	Count int32 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *PurchaseLimit) Reset() {
	*x = PurchaseLimit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PurchaseLimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurchaseLimit) ProtoMessage() {}

func (x *PurchaseLimit) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurchaseLimit.ProtoReflect.Descriptor instead.
func (*PurchaseLimit) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{10}
}

func (x *PurchaseLimit) GetType() PurchaseLimit_Type {
	if x != nil {
		return x.Type
	}
	return PurchaseLimit_TYPE_UNSPECIFIED
}

func (x *PurchaseLimit) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 功能
type Feature struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 功能键，作为业务方识别功能的唯一标识
	Key Feature_Key `protobuf:"varint,2,opt,name=key,proto3,enum=moego.models.subscription.v1.Feature_Key" json:"key,omitempty"`
	// 名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 描述
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// 功能配置，包含使用时需要的属性和余量控制
	Setting *Feature_Setting `protobuf:"bytes,5,opt,name=setting,proto3" json:"setting,omitempty"`
}

func (x *Feature) Reset() {
	*x = Feature{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Feature) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Feature) ProtoMessage() {}

func (x *Feature) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Feature.ProtoReflect.Descriptor instead.
func (*Feature) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{11}
}

func (x *Feature) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Feature) GetKey() Feature_Key {
	if x != nil {
		return x.Key
	}
	return Feature_KEY_UNSPECIFIED
}

func (x *Feature) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Feature) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Feature) GetSetting() *Feature_Setting {
	if x != nil {
		return x.Setting
	}
	return nil
}

// 开关
type OnOff struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否开启
	On bool `protobuf:"varint,1,opt,name=on,proto3" json:"on,omitempty"`
}

func (x *OnOff) Reset() {
	*x = OnOff{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnOff) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnOff) ProtoMessage() {}

func (x *OnOff) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnOff.ProtoReflect.Descriptor instead.
func (*OnOff) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{12}
}

func (x *OnOff) GetOn() bool {
	if x != nil {
		return x.On
	}
	return false
}

// 计数
type Count struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 已使用数量
	UsedAmount int64 `protobuf:"varint,1,opt,name=used_amount,json=usedAmount,proto3" json:"used_amount,omitempty"`
	// 已分配数量
	AllocatedAmount int64 `protobuf:"varint,2,opt,name=allocated_amount,json=allocatedAmount,proto3" json:"allocated_amount,omitempty"`
	// 总数量
	TotalAmount int64 `protobuf:"varint,3,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	// 单位
	Unit string `protobuf:"bytes,4,opt,name=unit,proto3" json:"unit,omitempty"`
	// 是否无限制
	Unlimited bool `protobuf:"varint,5,opt,name=unlimited,proto3" json:"unlimited,omitempty"`
	// 是否可刷新
	Renewable bool `protobuf:"varint,6,opt,name=renewable,proto3" json:"renewable,omitempty"`
}

func (x *Count) Reset() {
	*x = Count{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Count) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Count) ProtoMessage() {}

func (x *Count) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Count.ProtoReflect.Descriptor instead.
func (*Count) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{13}
}

func (x *Count) GetUsedAmount() int64 {
	if x != nil {
		return x.UsedAmount
	}
	return 0
}

func (x *Count) GetAllocatedAmount() int64 {
	if x != nil {
		return x.AllocatedAmount
	}
	return 0
}

func (x *Count) GetTotalAmount() int64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *Count) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *Count) GetUnlimited() bool {
	if x != nil {
		return x.Unlimited
	}
	return false
}

func (x *Count) GetRenewable() bool {
	if x != nil {
		return x.Renewable
	}
	return false
}

// 访问列表
type AccessList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 允许列表
	AllowList []string `protobuf:"bytes,1,rep,name=allow_list,json=allowList,proto3" json:"allow_list,omitempty"`
	// 拒绝列表
	DenyList []string `protobuf:"bytes,2,rep,name=deny_list,json=denyList,proto3" json:"deny_list,omitempty"`
}

func (x *AccessList) Reset() {
	*x = AccessList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessList) ProtoMessage() {}

func (x *AccessList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessList.ProtoReflect.Descriptor instead.
func (*AccessList) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{14}
}

func (x *AccessList) GetAllowList() []string {
	if x != nil {
		return x.AllowList
	}
	return nil
}

func (x *AccessList) GetDenyList() []string {
	if x != nil {
		return x.DenyList
	}
	return nil
}

// 许可证
type License struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 订阅 ID
	SubscriptionId int64 `protobuf:"varint,2,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	// 继承关系，记录当前节点到根节点的路径，形如 license_grand_parent_id/license_parent_id/license_id
	// for change plan and upgrade/downgrade
	InheritPath string `protobuf:"bytes,3,opt,name=inherit_path,json=inheritPath,proto3" json:"inherit_path,omitempty"`
	// 所有者
	Owner *User `protobuf:"bytes,4,opt,name=owner,proto3" json:"owner,omitempty"`
	// 状态
	Status License_Status `protobuf:"varint,5,opt,name=status,proto3,enum=moego.models.subscription.v1.License_Status" json:"status,omitempty"`
}

func (x *License) Reset() {
	*x = License{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *License) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*License) ProtoMessage() {}

func (x *License) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use License.ProtoReflect.Descriptor instead.
func (*License) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{15}
}

func (x *License) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *License) GetSubscriptionId() int64 {
	if x != nil {
		return x.SubscriptionId
	}
	return 0
}

func (x *License) GetInheritPath() string {
	if x != nil {
		return x.InheritPath
	}
	return ""
}

func (x *License) GetOwner() *User {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *License) GetStatus() License_Status {
	if x != nil {
		return x.Status
	}
	return License_STATUS_UNSPECIFIED
}

// 权益
type Entitlement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 许可证 ID
	LicenseId int64 `protobuf:"varint,2,opt,name=license_id,json=licenseId,proto3" json:"license_id,omitempty"`
	// 产品 ID
	ProductId int64 `protobuf:"varint,3,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	// 功能
	Feature *Feature `protobuf:"bytes,4,opt,name=feature,proto3" json:"feature,omitempty"`
}

func (x *Entitlement) Reset() {
	*x = Entitlement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Entitlement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Entitlement) ProtoMessage() {}

func (x *Entitlement) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Entitlement.ProtoReflect.Descriptor instead.
func (*Entitlement) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{16}
}

func (x *Entitlement) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Entitlement) GetLicenseId() int64 {
	if x != nil {
		return x.LicenseId
	}
	return 0
}

func (x *Entitlement) GetProductId() int64 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *Entitlement) GetFeature() *Feature {
	if x != nil {
		return x.Feature
	}
	return nil
}

// 修订记录
type Revision struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 类型
	Type Revision_Type `protobuf:"varint,2,opt,name=type,proto3,enum=moego.models.subscription.v1.Revision_Type" json:"type,omitempty"`
	// 详情
	Detail *RevisionDetail `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail,omitempty"`
	// 发生时间
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *Revision) Reset() {
	*x = Revision{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Revision) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Revision) ProtoMessage() {}

func (x *Revision) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Revision.ProtoReflect.Descriptor instead.
func (*Revision) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{17}
}

func (x *Revision) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Revision) GetType() Revision_Type {
	if x != nil {
		return x.Type
	}
	return Revision_TYPE_UNSPECIFIED
}

func (x *Revision) GetDetail() *RevisionDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *Revision) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// 修订记录详情
type RevisionDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 详情
	//
	// Types that are assignable to Detail:
	//
	//	*RevisionDetail_CreateSubscriptionDetail
	//	*RevisionDetail_UpdateEntitlementDetail
	Detail isRevisionDetail_Detail `protobuf_oneof:"detail"`
}

func (x *RevisionDetail) Reset() {
	*x = RevisionDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RevisionDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevisionDetail) ProtoMessage() {}

func (x *RevisionDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevisionDetail.ProtoReflect.Descriptor instead.
func (*RevisionDetail) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{18}
}

func (m *RevisionDetail) GetDetail() isRevisionDetail_Detail {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (x *RevisionDetail) GetCreateSubscriptionDetail() *CreateSubscriptionDetail {
	if x, ok := x.GetDetail().(*RevisionDetail_CreateSubscriptionDetail); ok {
		return x.CreateSubscriptionDetail
	}
	return nil
}

func (x *RevisionDetail) GetUpdateEntitlementDetail() *UpdateEntitlementDetail {
	if x, ok := x.GetDetail().(*RevisionDetail_UpdateEntitlementDetail); ok {
		return x.UpdateEntitlementDetail
	}
	return nil
}

type isRevisionDetail_Detail interface {
	isRevisionDetail_Detail()
}

type RevisionDetail_CreateSubscriptionDetail struct {
	// TODO(arkxiong): 定义所有的事件结构
	// 创建订阅
	CreateSubscriptionDetail *CreateSubscriptionDetail `protobuf:"bytes,6,opt,name=create_subscription_detail,json=createSubscriptionDetail,proto3,oneof"`
}

type RevisionDetail_UpdateEntitlementDetail struct {
	// 修改权益
	UpdateEntitlementDetail *UpdateEntitlementDetail `protobuf:"bytes,7,opt,name=update_entitlement_detail,json=updateEntitlementDetail,proto3,oneof"`
}

func (*RevisionDetail_CreateSubscriptionDetail) isRevisionDetail_Detail() {}

func (*RevisionDetail_UpdateEntitlementDetail) isRevisionDetail_Detail() {}

// 创建订阅详情
type CreateSubscriptionDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 已创建的订阅
	CreatedSubscription *Subscription `protobuf:"bytes,1,opt,name=created_subscription,json=createdSubscription,proto3" json:"created_subscription,omitempty"`
}

func (x *CreateSubscriptionDetail) Reset() {
	*x = CreateSubscriptionDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSubscriptionDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSubscriptionDetail) ProtoMessage() {}

func (x *CreateSubscriptionDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSubscriptionDetail.ProtoReflect.Descriptor instead.
func (*CreateSubscriptionDetail) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{19}
}

func (x *CreateSubscriptionDetail) GetCreatedSubscription() *Subscription {
	if x != nil {
		return x.CreatedSubscription
	}
	return nil
}

// 修改entitlement 详情
type UpdateEntitlementDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// updated entitlement
	UpdateEntitlement *Entitlement `protobuf:"bytes,1,opt,name=update_entitlement,json=updateEntitlement,proto3" json:"update_entitlement,omitempty"`
	// feature
	Feature *Feature `protobuf:"bytes,2,opt,name=feature,proto3" json:"feature,omitempty"`
	// operator staff id
	OperatorId int64 `protobuf:"varint,3,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// owner
	Owner *User `protobuf:"bytes,4,opt,name=owner,proto3" json:"owner,omitempty"`
	// the information attached when modifying entitlement may be related to the business
	// or it may be a backend modification
	// anyway, it means changing the information attached to this entitlement,
	// which will be parsed into json and stored in the database.
	Details string `protobuf:"bytes,5,opt,name=details,proto3" json:"details,omitempty"`
}

func (x *UpdateEntitlementDetail) Reset() {
	*x = UpdateEntitlementDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEntitlementDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEntitlementDetail) ProtoMessage() {}

func (x *UpdateEntitlementDetail) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEntitlementDetail.ProtoReflect.Descriptor instead.
func (*UpdateEntitlementDetail) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{20}
}

func (x *UpdateEntitlementDetail) GetUpdateEntitlement() *Entitlement {
	if x != nil {
		return x.UpdateEntitlement
	}
	return nil
}

func (x *UpdateEntitlementDetail) GetFeature() *Feature {
	if x != nil {
		return x.Feature
	}
	return nil
}

func (x *UpdateEntitlementDetail) GetOperatorId() int64 {
	if x != nil {
		return x.OperatorId
	}
	return 0
}

func (x *UpdateEntitlementDetail) GetOwner() *User {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *UpdateEntitlementDetail) GetDetails() string {
	if x != nil {
		return x.Details
	}
	return ""
}

// credit
type UpdateCredit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateCredit) Reset() {
	*x = UpdateCredit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCredit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCredit) ProtoMessage() {}

func (x *UpdateCredit) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCredit.ProtoReflect.Descriptor instead.
func (*UpdateCredit) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{21}
}

// discount
type Discount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Price id
	PriceId int64 `protobuf:"varint,2,opt,name=price_id,json=priceId,proto3" json:"price_id,omitempty"`
	// 优惠金额
	AmountOff *money.Money `protobuf:"bytes,3,opt,name=amount_off,json=amountOff,proto3" json:"amount_off,omitempty"`
	// 有效期
	ValidityPeriod *interval.Interval `protobuf:"bytes,4,opt,name=validity_period,json=validityPeriod,proto3" json:"validity_period,omitempty"`
}

func (x *Discount) Reset() {
	*x = Discount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Discount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Discount) ProtoMessage() {}

func (x *Discount) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Discount.ProtoReflect.Descriptor instead.
func (*Discount) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{22}
}

func (x *Discount) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Discount) GetPriceId() int64 {
	if x != nil {
		return x.PriceId
	}
	return 0
}

func (x *Discount) GetAmountOff() *money.Money {
	if x != nil {
		return x.AmountOff
	}
	return nil
}

func (x *Discount) GetValidityPeriod() *interval.Interval {
	if x != nil {
		return x.ValidityPeriod
	}
	return nil
}

// Report
type Report struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// in subscription count
	InSubscriptionCount uint32 `protobuf:"varint,1,opt,name=in_subscription_count,json=inSubscriptionCount,proto3" json:"in_subscription_count,omitempty"`
	// in subscription percentage
	InSubscriptionPercentage float64 `protobuf:"fixed64,2,opt,name=in_subscription_percentage,json=inSubscriptionPercentage,proto3" json:"in_subscription_percentage,omitempty"`
	// cancelled count
	CancelledCount uint32 `protobuf:"varint,3,opt,name=cancelled_count,json=cancelledCount,proto3" json:"cancelled_count,omitempty"`
	// total sales
	TotalSalesAmount *money.Money `protobuf:"bytes,4,opt,name=total_sales_amount,json=totalSalesAmount,proto3" json:"total_sales_amount,omitempty"`
	// bold/billing cycle
	CycleAmount *money.Money `protobuf:"bytes,5,opt,name=cycle_amount,json=cycleAmount,proto3" json:"cycle_amount,omitempty"`
	// membership total sales
	MembershipTotalSalesAmount *money.Money `protobuf:"bytes,6,opt,name=membership_total_sales_amount,json=membershipTotalSalesAmount,proto3" json:"membership_total_sales_amount,omitempty"`
	// pause count
	PausedCount uint32 `protobuf:"varint,7,opt,name=paused_count,json=pausedCount,proto3" json:"paused_count,omitempty"`
}

func (x *Report) Reset() {
	*x = Report{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Report) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Report) ProtoMessage() {}

func (x *Report) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Report.ProtoReflect.Descriptor instead.
func (*Report) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{23}
}

func (x *Report) GetInSubscriptionCount() uint32 {
	if x != nil {
		return x.InSubscriptionCount
	}
	return 0
}

func (x *Report) GetInSubscriptionPercentage() float64 {
	if x != nil {
		return x.InSubscriptionPercentage
	}
	return 0
}

func (x *Report) GetCancelledCount() uint32 {
	if x != nil {
		return x.CancelledCount
	}
	return 0
}

func (x *Report) GetTotalSalesAmount() *money.Money {
	if x != nil {
		return x.TotalSalesAmount
	}
	return nil
}

func (x *Report) GetCycleAmount() *money.Money {
	if x != nil {
		return x.CycleAmount
	}
	return nil
}

func (x *Report) GetMembershipTotalSalesAmount() *money.Money {
	if x != nil {
		return x.MembershipTotalSalesAmount
	}
	return nil
}

func (x *Report) GetPausedCount() uint32 {
	if x != nil {
		return x.PausedCount
	}
	return 0
}

// 税，作为购买时记录的税快照
type PurchaseDetail_Tax struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 购买详情 ID
	PurchaseDetailId int64 `protobuf:"varint,2,opt,name=purchase_detail_id,json=purchaseDetailId,proto3" json:"purchase_detail_id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 金额
	Amount *money.Money `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"`
	// 税率
	Rate float64 `protobuf:"fixed64,5,opt,name=rate,proto3" json:"rate,omitempty"`
	// 税种代码
	Code string `protobuf:"bytes,6,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *PurchaseDetail_Tax) Reset() {
	*x = PurchaseDetail_Tax{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PurchaseDetail_Tax) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurchaseDetail_Tax) ProtoMessage() {}

func (x *PurchaseDetail_Tax) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurchaseDetail_Tax.ProtoReflect.Descriptor instead.
func (*PurchaseDetail_Tax) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{4, 0}
}

func (x *PurchaseDetail_Tax) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PurchaseDetail_Tax) GetPurchaseDetailId() int64 {
	if x != nil {
		return x.PurchaseDetailId
	}
	return 0
}

func (x *PurchaseDetail_Tax) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PurchaseDetail_Tax) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *PurchaseDetail_Tax) GetRate() float64 {
	if x != nil {
		return x.Rate
	}
	return 0
}

func (x *PurchaseDetail_Tax) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

// 浮动定价
type AccountingExtra_FloatingPrice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 描述
	Description string `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	// 价格表
	PriceTable []*AccountingExtra_FloatingPrice_PriceTableRow `protobuf:"bytes,2,rep,name=price_table,json=priceTable,proto3" json:"price_table,omitempty"`
}

func (x *AccountingExtra_FloatingPrice) Reset() {
	*x = AccountingExtra_FloatingPrice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountingExtra_FloatingPrice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountingExtra_FloatingPrice) ProtoMessage() {}

func (x *AccountingExtra_FloatingPrice) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountingExtra_FloatingPrice.ProtoReflect.Descriptor instead.
func (*AccountingExtra_FloatingPrice) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{7, 0}
}

func (x *AccountingExtra_FloatingPrice) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AccountingExtra_FloatingPrice) GetPriceTable() []*AccountingExtra_FloatingPrice_PriceTableRow {
	if x != nil {
		return x.PriceTable
	}
	return nil
}

// 仅用于显示在前端的文案，不可用作业务计算逻辑
type AccountingExtra_Feature struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用于显示在前端的文案，如："Basic including:", "Everything in basic, plus:"
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// 用于显示在前端的feature文案，注意仅用于营销文案，无任何业务逻辑，
	Features []string `protobuf:"bytes,2,rep,name=features,proto3" json:"features,omitempty"`
}

func (x *AccountingExtra_Feature) Reset() {
	*x = AccountingExtra_Feature{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountingExtra_Feature) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountingExtra_Feature) ProtoMessage() {}

func (x *AccountingExtra_Feature) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountingExtra_Feature.ProtoReflect.Descriptor instead.
func (*AccountingExtra_Feature) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{7, 1}
}

func (x *AccountingExtra_Feature) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AccountingExtra_Feature) GetFeatures() []string {
	if x != nil {
		return x.Features
	}
	return nil
}

// 价格表行
type AccountingExtra_FloatingPrice_PriceTableRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 营业额
	SmbRevenue string `protobuf:"bytes,1,opt,name=smb_revenue,json=smbRevenue,proto3" json:"smb_revenue,omitempty"`
	// 价格
	SmbPrice string `protobuf:"bytes,2,opt,name=smb_price,json=smbPrice,proto3" json:"smb_price,omitempty"`
	// 价格的Money
	SmbPriceMoney *money.Money `protobuf:"bytes,3,opt,name=smb_price_money,json=smbPriceMoney,proto3" json:"smb_price_money,omitempty"`
}

func (x *AccountingExtra_FloatingPrice_PriceTableRow) Reset() {
	*x = AccountingExtra_FloatingPrice_PriceTableRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountingExtra_FloatingPrice_PriceTableRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountingExtra_FloatingPrice_PriceTableRow) ProtoMessage() {}

func (x *AccountingExtra_FloatingPrice_PriceTableRow) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountingExtra_FloatingPrice_PriceTableRow.ProtoReflect.Descriptor instead.
func (*AccountingExtra_FloatingPrice_PriceTableRow) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{7, 0, 0}
}

func (x *AccountingExtra_FloatingPrice_PriceTableRow) GetSmbRevenue() string {
	if x != nil {
		return x.SmbRevenue
	}
	return ""
}

func (x *AccountingExtra_FloatingPrice_PriceTableRow) GetSmbPrice() string {
	if x != nil {
		return x.SmbPrice
	}
	return ""
}

func (x *AccountingExtra_FloatingPrice_PriceTableRow) GetSmbPriceMoney() *money.Money {
	if x != nil {
		return x.SmbPriceMoney
	}
	return nil
}

// 预审
type Price_Prequalification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否需要预审
	Required bool `protobuf:"varint,1,opt,name=required,proto3" json:"required,omitempty"`
	// 预审规则
	//
	// Types that are assignable to Rule:
	//
	//	*Price_Prequalification_RevenueRule
	Rule isPrice_Prequalification_Rule `protobuf_oneof:"rule"`
	// 是否通过预审，注意只有 "请求携带 buyer" 时才有值
	Passed *bool `protobuf:"varint,3,opt,name=passed,proto3,oneof" json:"passed,omitempty"`
}

func (x *Price_Prequalification) Reset() {
	*x = Price_Prequalification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Price_Prequalification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Price_Prequalification) ProtoMessage() {}

func (x *Price_Prequalification) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Price_Prequalification.ProtoReflect.Descriptor instead.
func (*Price_Prequalification) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{8, 0}
}

func (x *Price_Prequalification) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

func (m *Price_Prequalification) GetRule() isPrice_Prequalification_Rule {
	if m != nil {
		return m.Rule
	}
	return nil
}

func (x *Price_Prequalification) GetRevenueRule() *Price_RevenueRule {
	if x, ok := x.GetRule().(*Price_Prequalification_RevenueRule); ok {
		return x.RevenueRule
	}
	return nil
}

func (x *Price_Prequalification) GetPassed() bool {
	if x != nil && x.Passed != nil {
		return *x.Passed
	}
	return false
}

type isPrice_Prequalification_Rule interface {
	isPrice_Prequalification_Rule()
}

type Price_Prequalification_RevenueRule struct {
	// 营业额预审规则
	RevenueRule *Price_RevenueRule `protobuf:"bytes,2,opt,name=revenue_rule,json=revenueRule,proto3,oneof"`
}

func (*Price_Prequalification_RevenueRule) isPrice_Prequalification_Rule() {}

// 营业额预审规则: 在过去的一段时间 period 内，营业额满足 revenue_condition
type Price_RevenueRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// peroid
	Period *v1.TimePeriod `protobuf:"bytes,1,opt,name=period,proto3" json:"period,omitempty"`
	// revenue condition
	RevenueCondition *v2.Predicate `protobuf:"bytes,2,opt,name=revenue_condition,json=revenueCondition,proto3" json:"revenue_condition,omitempty"`
}

func (x *Price_RevenueRule) Reset() {
	*x = Price_RevenueRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Price_RevenueRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Price_RevenueRule) ProtoMessage() {}

func (x *Price_RevenueRule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Price_RevenueRule.ProtoReflect.Descriptor instead.
func (*Price_RevenueRule) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{8, 1}
}

func (x *Price_RevenueRule) GetPeriod() *v1.TimePeriod {
	if x != nil {
		return x.Period
	}
	return nil
}

func (x *Price_RevenueRule) GetRevenueCondition() *v2.Predicate {
	if x != nil {
		return x.RevenueCondition
	}
	return nil
}

// 功能设置
type Feature_Setting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 设置类型
	//
	// Types that are assignable to Setting:
	//
	//	*Feature_Setting_OnOff
	//	*Feature_Setting_Count
	//	*Feature_Setting_AccessList
	Setting isFeature_Setting_Setting `protobuf_oneof:"setting"`
}

func (x *Feature_Setting) Reset() {
	*x = Feature_Setting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Feature_Setting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Feature_Setting) ProtoMessage() {}

func (x *Feature_Setting) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_subscription_v1_subscription_models_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Feature_Setting.ProtoReflect.Descriptor instead.
func (*Feature_Setting) Descriptor() ([]byte, []int) {
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP(), []int{11, 0}
}

func (m *Feature_Setting) GetSetting() isFeature_Setting_Setting {
	if m != nil {
		return m.Setting
	}
	return nil
}

func (x *Feature_Setting) GetOnOff() *OnOff {
	if x, ok := x.GetSetting().(*Feature_Setting_OnOff); ok {
		return x.OnOff
	}
	return nil
}

func (x *Feature_Setting) GetCount() *Count {
	if x, ok := x.GetSetting().(*Feature_Setting_Count); ok {
		return x.Count
	}
	return nil
}

func (x *Feature_Setting) GetAccessList() *AccessList {
	if x, ok := x.GetSetting().(*Feature_Setting_AccessList); ok {
		return x.AccessList
	}
	return nil
}

type isFeature_Setting_Setting interface {
	isFeature_Setting_Setting()
}

type Feature_Setting_OnOff struct {
	// 开关
	OnOff *OnOff `protobuf:"bytes,1,opt,name=on_off,json=onOff,proto3,oneof"`
}

type Feature_Setting_Count struct {
	// 计数
	Count *Count `protobuf:"bytes,2,opt,name=count,proto3,oneof"`
}

type Feature_Setting_AccessList struct {
	// 访问列表
	AccessList *AccessList `protobuf:"bytes,3,opt,name=access_list,json=accessList,proto3,oneof"`
}

func (*Feature_Setting_OnOff) isFeature_Setting_Setting() {}

func (*Feature_Setting_Count) isFeature_Setting_Setting() {}

func (*Feature_Setting_AccessList) isFeature_Setting_Setting() {}

var File_moego_models_subscription_v1_subscription_models_proto protoreflect.FileDescriptor

var file_moego_models_subscription_v1_subscription_models_proto_rawDesc = []byte{
	0x0a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf7, 0x08, 0x0a, 0x0c, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x49,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x0e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x69, 0x74, 0x79, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x2f,
	0x0a, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x78, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x08, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x61, 0x78, 0x12,
	0x3f, 0x0a, 0x0d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x52, 0x0c, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65,
	0x12, 0x3d, 0x0a, 0x0c, 0x67, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x52, 0x0b, 0x67, 0x72, 0x61, 0x63, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12,
	0x2f, 0x0a, 0x14, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x61, 0x74, 0x5f, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x63,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x45, 0x6e, 0x64,
	0x12, 0x38, 0x0a, 0x05, 0x62, 0x75, 0x79, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x05, 0x62, 0x75, 0x79, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x06, 0x73, 0x65,
	0x6c, 0x6c, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x06,
	0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x11, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6c, 0x61, 0x74, 0x65, 0x73,
	0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x17, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x32, 0x0a, 0x16, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x6e,
	0x46, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x09, 0x70, 0x61, 0x75, 0x73, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x70, 0x61, 0x75, 0x73, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x40, 0x0a, 0x0e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x5f,
	0x61, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65,
	0x41, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x87, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x54, 0x52,
	0x49, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x03, 0x12, 0x09,
	0x0a, 0x05, 0x47, 0x52, 0x41, 0x43, 0x45, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x58, 0x50,
	0x49, 0x52, 0x45, 0x44, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c,
	0x4c, 0x45, 0x44, 0x10, 0x06, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x41, 0x55, 0x53, 0x45, 0x44, 0x10,
	0x07, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x10,
	0x08, 0x22, 0xc3, 0x01, 0x0a, 0x10, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x2f, 0x0a, 0x14, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x61, 0x74, 0x5f, 0x70, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11,
	0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x45, 0x6e,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb5, 0x01, 0x0a, 0x11, 0x42, 0x75, 0x79, 0x65,
	0x72, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a,
	0x05, 0x62, 0x75, 0x79, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x05, 0x62, 0x75, 0x79, 0x65, 0x72, 0x12, 0x50, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22,
	0x79, 0x0a, 0x08, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x78, 0x49, 0x64, 0x73, 0x22, 0xa1, 0x06, 0x0a, 0x0e, 0x50,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x27, 0x0a,
	0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x07,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x39, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x46,
	0x0a, 0x05, 0x74, 0x61, 0x78, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x72,
	0x63, 0x68, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x54, 0x61, 0x78, 0x52,
	0x05, 0x74, 0x61, 0x78, 0x65, 0x73, 0x12, 0x4b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x38, 0x0a, 0x05, 0x62, 0x75, 0x79, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x62, 0x75, 0x79, 0x65, 0x72, 0x12, 0x33, 0x0a,
	0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x78, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x08, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x54, 0x61, 0x78, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x1a, 0xab, 0x01, 0x0a, 0x03, 0x54, 0x61, 0x78, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c,
	0x0a, 0x12, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x70, 0x75, 0x72, 0x63,
	0x68, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x72, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x22, 0x38, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16,
	0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x02, 0x22, 0x98,
	0x05, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x3e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x3a, 0x0a, 0x06, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x06, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x0e,
	0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x52, 0x0d, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x64,
	0x73, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x57, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x2e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x45, 0x78,
	0x74, 0x72, 0x61, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x31, 0x0a, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x4c, 0x41, 0x4e,
	0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x44, 0x44, 0x4f, 0x4e, 0x10, 0x02, 0x22, 0x5a, 0x0a,
	0x0c, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a,
	0x19, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a,
	0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07,
	0x50, 0x41, 0x59, 0x52, 0x4f, 0x4c, 0x4c, 0x10, 0x03, 0x22, 0x73, 0x0a, 0x0c, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x5a, 0x0a, 0x10, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x48, 0x00, 0x52, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x42, 0x07, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x95,
	0x05, 0x0a, 0x0f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x5f, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x73, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x67, 0x0a, 0x0e, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x69, 0x6e,
	0x67, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x78, 0x74, 0x72, 0x61, 0x2e, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x69, 0x63, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x66, 0x6c,
	0x6f, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x69, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x5a,
	0x0a, 0x0d, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x0c, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x54, 0x65, 0x78, 0x74, 0x73, 0x1a, 0xa9, 0x02, 0x0a, 0x0d, 0x46,
	0x6c, 0x6f, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6a,
	0x0a, 0x0b, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x2e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x6f, 0x77, 0x52, 0x0a,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x1a, 0x89, 0x01, 0x0a, 0x0d, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x6f, 0x77, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x6d, 0x62, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x73, 0x6d, 0x62, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x73, 0x6d, 0x62, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x6d, 0x62, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x0f, 0x73, 0x6d,
	0x62, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x73, 0x6d, 0x62, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x1a, 0x3b, 0x0a, 0x07, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x73, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x69, 0x6e, 0x67,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x22, 0x9a, 0x06, 0x0a, 0x05, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x33, 0x0a, 0x0b, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x75, 0x6e, 0x69, 0x74,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x0d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x0c, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x28, 0x0a,
	0x10, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x10, 0x70, 0x72, 0x65, 0x71, 0x75,
	0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x2e, 0x50, 0x72, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x70, 0x72, 0x65, 0x71, 0x75, 0x61, 0x6c,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xb4, 0x01, 0x0a, 0x10, 0x50, 0x72,
	0x65, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x54, 0x0a, 0x0c, 0x72, 0x65,
	0x76, 0x65, 0x6e, 0x75, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x2e, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x52, 0x75, 0x6c,
	0x65, 0x48, 0x00, 0x52, 0x0b, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x1b, 0x0a, 0x06, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x01, 0x52, 0x06, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x88, 0x01, 0x01, 0x42, 0x06, 0x0a,
	0x04, 0x72, 0x75, 0x6c, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64,
	0x1a, 0x89, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x32, 0x0a, 0x06, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x06, 0x70, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x12, 0x46, 0x0a, 0x11, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x52, 0x10, 0x72, 0x65, 0x76, 0x65,
	0x6e, 0x75, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x35, 0x0a, 0x04,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x4f, 0x4e,
	0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x43, 0x59, 0x43, 0x4c,
	0x45, 0x10, 0x02, 0x22, 0xcd, 0x01, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x2e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x78, 0x0a, 0x04, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x4f, 0x45, 0x47, 0x4f,
	0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x02, 0x12,
	0x0e, 0x0a, 0x0a, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x10, 0x03, 0x12,
	0x0b, 0x0a, 0x07, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08,
	0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x06, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x54, 0x41, 0x46,
	0x46, 0x10, 0x07, 0x22, 0xa3, 0x01, 0x0a, 0x0d, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x44, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x36, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x55, 0x4e, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x09,
	0x0a, 0x05, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x02, 0x22, 0xc0, 0x05, 0x0a, 0x07, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x4b, 0x65, 0x79, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x1a, 0xdc, 0x01, 0x0a, 0x07, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x3c, 0x0a,
	0x06, 0x6f, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x6e, 0x4f,
	0x66, 0x66, 0x48, 0x00, 0x52, 0x05, 0x6f, 0x6e, 0x4f, 0x66, 0x66, 0x12, 0x3b, 0x0a, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x48,
	0x00, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x22, 0x89, 0x02, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x12, 0x13, 0x0a, 0x0f, 0x4b, 0x45, 0x59, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a,
	0x10, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x42, 0x41, 0x53, 0x49,
	0x43, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x49, 0x4e,
	0x47, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x02,
	0x12, 0x19, 0x0a, 0x15, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x4f,
	0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x4d,
	0x45, 0x4d, 0x42, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43,
	0x45, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x04, 0x12, 0x1d, 0x0a, 0x19,
	0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x5f, 0x41, 0x44, 0x44, 0x4f, 0x4e,
	0x5f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x05, 0x12, 0x1f, 0x0a, 0x1b, 0x4d,
	0x45, 0x4d, 0x42, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43,
	0x54, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x06, 0x12, 0x25, 0x0a, 0x21,
	0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49,
	0x43, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x4f, 0x4e, 0x5f, 0x51, 0x55, 0x41, 0x4e, 0x54, 0x49, 0x54,
	0x59, 0x10, 0x07, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x10, 0x08, 0x22, 0x17, 0x0a, 0x05,
	0x4f, 0x6e, 0x4f, 0x66, 0x66, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x02, 0x6f, 0x6e, 0x22, 0xc6, 0x01, 0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x29, 0x0a, 0x10, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x61, 0x6c, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x6e,
	0x69, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x6e, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x75, 0x6e, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x48,
	0x0a, 0x0a, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x64,
	0x65, 0x6e, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x64, 0x65, 0x6e, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x9f, 0x02, 0x0a, 0x07, 0x4c, 0x69, 0x63,
	0x65, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x69, 0x6e, 0x68, 0x65, 0x72, 0x69, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x68, 0x65, 0x72, 0x69, 0x74, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x38, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x44, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73,
	0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x38, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x01, 0x12, 0x0b, 0x0a,
	0x07, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x02, 0x22, 0x9c, 0x01, 0x0a, 0x0b, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x69,
	0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x07, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x52, 0x07, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22, 0xa5, 0x03, 0x0a, 0x08, 0x52, 0x65,
	0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xc6, 0x01, 0x0a, 0x04, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01,
	0x12, 0x17, 0x0a, 0x13, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43,
	0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x50, 0x47,
	0x52, 0x41, 0x44, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x4f, 0x57, 0x4e, 0x47, 0x52, 0x41, 0x44, 0x45,
	0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12,
	0x14, 0x0a, 0x10, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x44,
	0x55, 0x43, 0x54, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x53, 0x53, 0x49, 0x47, 0x4e, 0x5f,
	0x4c, 0x49, 0x43, 0x45, 0x4e, 0x53, 0x45, 0x10, 0x06, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x48, 0x41,
	0x4e, 0x47, 0x45, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x07, 0x22, 0x87, 0x02, 0x0a, 0x0e, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x76, 0x0a, 0x1a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x48, 0x00, 0x52, 0x18, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x73, 0x0a, 0x19,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x48, 0x00, 0x52, 0x17, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x42, 0x08, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x79, 0x0a, 0x18, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x5d, 0x0a, 0x14, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x13, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa9, 0x02, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x58, 0x0a, 0x12, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x11, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x07,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x52, 0x07, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x38,
	0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x22, 0xd3, 0x01, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x22, 0x66, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x55,
	0x4e, 0x44, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4b, 0x45, 0x45,
	0x50, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x10, 0x04, 0x22, 0x5b, 0x0a, 0x06, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a,
	0x06, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x4f, 0x4e,
	0x55, 0x53, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52,
	0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x50, 0x45, 0x52, 0x4b, 0x53, 0x10, 0x03, 0x12, 0x09, 0x0a,
	0x05, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x63, 0x22, 0xa8, 0x01, 0x0a, 0x08, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x72, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x31, 0x0a, 0x0a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x4f, 0x66, 0x66, 0x12, 0x3e, 0x0a, 0x0f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x5f,
	0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x52, 0x0e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x50, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x22, 0x96, 0x03, 0x0a, 0x06, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x32,
	0x0a, 0x15, 0x69, 0x6e, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x13, 0x69,
	0x6e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x1a, 0x69, 0x6e, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x18, 0x69, 0x6e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65,
	0x12, 0x27, 0x0a, 0x0f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x63, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x6c, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x40, 0x0a, 0x12, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x53, 0x61, 0x6c, 0x65, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x0c, 0x63,
	0x79, 0x63, 0x6c, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x55, 0x0a, 0x1d, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x1a, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x61,
	0x6c, 0x65, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x75,
	0x73, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0b, 0x70, 0x61, 0x75, 0x73, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x8d, 0x01, 0x0a,
	0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x6d, 0x6f, 0x64, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_subscription_v1_subscription_models_proto_rawDescOnce sync.Once
	file_moego_models_subscription_v1_subscription_models_proto_rawDescData = file_moego_models_subscription_v1_subscription_models_proto_rawDesc
)

func file_moego_models_subscription_v1_subscription_models_proto_rawDescGZIP() []byte {
	file_moego_models_subscription_v1_subscription_models_proto_rawDescOnce.Do(func() {
		file_moego_models_subscription_v1_subscription_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_subscription_v1_subscription_models_proto_rawDescData)
	})
	return file_moego_models_subscription_v1_subscription_models_proto_rawDescData
}

var file_moego_models_subscription_v1_subscription_models_proto_enumTypes = make([]protoimpl.EnumInfo, 12)
var file_moego_models_subscription_v1_subscription_models_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_moego_models_subscription_v1_subscription_models_proto_goTypes = []interface{}{
	(Subscription_Status)(0),              // 0: moego.models.subscription.v1.Subscription.Status
	(PurchaseDetail_Status)(0),            // 1: moego.models.subscription.v1.PurchaseDetail.Status
	(Product_Type)(0),                     // 2: moego.models.subscription.v1.Product.Type
	(Product_BusinessType)(0),             // 3: moego.models.subscription.v1.Product.BusinessType
	(Price_Type)(0),                       // 4: moego.models.subscription.v1.Price.Type
	(User_Type)(0),                        // 5: moego.models.subscription.v1.User.Type
	(PurchaseLimit_Type)(0),               // 6: moego.models.subscription.v1.PurchaseLimit.Type
	(Feature_Key)(0),                      // 7: moego.models.subscription.v1.Feature.Key
	(License_Status)(0),                   // 8: moego.models.subscription.v1.License.Status
	(Revision_Type)(0),                    // 9: moego.models.subscription.v1.Revision.Type
	(UpdateCredit_Type)(0),                // 10: moego.models.subscription.v1.UpdateCredit.Type
	(UpdateCredit_Reason)(0),              // 11: moego.models.subscription.v1.UpdateCredit.Reason
	(*Subscription)(nil),                  // 12: moego.models.subscription.v1.Subscription
	(*SubscriptionStat)(nil),              // 13: moego.models.subscription.v1.SubscriptionStat
	(*BuyerSubscription)(nil),             // 14: moego.models.subscription.v1.BuyerSubscription
	(*Purchase)(nil),                      // 15: moego.models.subscription.v1.Purchase
	(*PurchaseDetail)(nil),                // 16: moego.models.subscription.v1.PurchaseDetail
	(*Product)(nil),                       // 17: moego.models.subscription.v1.Product
	(*ProductExtra)(nil),                  // 18: moego.models.subscription.v1.ProductExtra
	(*AccountingExtra)(nil),               // 19: moego.models.subscription.v1.AccountingExtra
	(*Price)(nil),                         // 20: moego.models.subscription.v1.Price
	(*User)(nil),                          // 21: moego.models.subscription.v1.User
	(*PurchaseLimit)(nil),                 // 22: moego.models.subscription.v1.PurchaseLimit
	(*Feature)(nil),                       // 23: moego.models.subscription.v1.Feature
	(*OnOff)(nil),                         // 24: moego.models.subscription.v1.OnOff
	(*Count)(nil),                         // 25: moego.models.subscription.v1.Count
	(*AccessList)(nil),                    // 26: moego.models.subscription.v1.AccessList
	(*License)(nil),                       // 27: moego.models.subscription.v1.License
	(*Entitlement)(nil),                   // 28: moego.models.subscription.v1.Entitlement
	(*Revision)(nil),                      // 29: moego.models.subscription.v1.Revision
	(*RevisionDetail)(nil),                // 30: moego.models.subscription.v1.RevisionDetail
	(*CreateSubscriptionDetail)(nil),      // 31: moego.models.subscription.v1.CreateSubscriptionDetail
	(*UpdateEntitlementDetail)(nil),       // 32: moego.models.subscription.v1.UpdateEntitlementDetail
	(*UpdateCredit)(nil),                  // 33: moego.models.subscription.v1.UpdateCredit
	(*Discount)(nil),                      // 34: moego.models.subscription.v1.Discount
	(*Report)(nil),                        // 35: moego.models.subscription.v1.Report
	(*PurchaseDetail_Tax)(nil),            // 36: moego.models.subscription.v1.PurchaseDetail.Tax
	(*AccountingExtra_FloatingPrice)(nil), // 37: moego.models.subscription.v1.AccountingExtra.FloatingPrice
	(*AccountingExtra_Feature)(nil),       // 38: moego.models.subscription.v1.AccountingExtra.Feature
	(*AccountingExtra_FloatingPrice_PriceTableRow)(nil), // 39: moego.models.subscription.v1.AccountingExtra.FloatingPrice.PriceTableRow
	(*Price_Prequalification)(nil),                      // 40: moego.models.subscription.v1.Price.Prequalification
	(*Price_RevenueRule)(nil),                           // 41: moego.models.subscription.v1.Price.RevenueRule
	(*Feature_Setting)(nil),                             // 42: moego.models.subscription.v1.Feature.Setting
	(*interval.Interval)(nil),                           // 43: google.type.Interval
	(*money.Money)(nil),                                 // 44: google.type.Money
	(*v1.TimePeriod)(nil),                               // 45: moego.utils.v1.TimePeriod
	(*timestamppb.Timestamp)(nil),                       // 46: google.protobuf.Timestamp
	(*v2.Predicate)(nil),                                // 47: moego.utils.v2.Predicate
}
var file_moego_models_subscription_v1_subscription_models_proto_depIdxs = []int32{
	0,  // 0: moego.models.subscription.v1.Subscription.status:type_name -> moego.models.subscription.v1.Subscription.Status
	43, // 1: moego.models.subscription.v1.Subscription.validity_period:type_name -> google.type.Interval
	44, // 2: moego.models.subscription.v1.Subscription.total_price:type_name -> google.type.Money
	44, // 3: moego.models.subscription.v1.Subscription.total_tax:type_name -> google.type.Money
	45, // 4: moego.models.subscription.v1.Subscription.billing_cycle:type_name -> moego.utils.v1.TimePeriod
	45, // 5: moego.models.subscription.v1.Subscription.grace_period:type_name -> moego.utils.v1.TimePeriod
	21, // 6: moego.models.subscription.v1.Subscription.buyer:type_name -> moego.models.subscription.v1.User
	21, // 7: moego.models.subscription.v1.Subscription.seller:type_name -> moego.models.subscription.v1.User
	46, // 8: moego.models.subscription.v1.Subscription.paused_at:type_name -> google.protobuf.Timestamp
	46, // 9: moego.models.subscription.v1.Subscription.auto_resume_at:type_name -> google.protobuf.Timestamp
	0,  // 10: moego.models.subscription.v1.SubscriptionStat.status:type_name -> moego.models.subscription.v1.Subscription.Status
	21, // 11: moego.models.subscription.v1.BuyerSubscription.buyer:type_name -> moego.models.subscription.v1.User
	12, // 12: moego.models.subscription.v1.BuyerSubscription.subscriptions:type_name -> moego.models.subscription.v1.Subscription
	17, // 13: moego.models.subscription.v1.PurchaseDetail.product:type_name -> moego.models.subscription.v1.Product
	20, // 14: moego.models.subscription.v1.PurchaseDetail.price:type_name -> moego.models.subscription.v1.Price
	36, // 15: moego.models.subscription.v1.PurchaseDetail.taxes:type_name -> moego.models.subscription.v1.PurchaseDetail.Tax
	1,  // 16: moego.models.subscription.v1.PurchaseDetail.status:type_name -> moego.models.subscription.v1.PurchaseDetail.Status
	21, // 17: moego.models.subscription.v1.PurchaseDetail.buyer:type_name -> moego.models.subscription.v1.User
	44, // 18: moego.models.subscription.v1.PurchaseDetail.total_price:type_name -> google.type.Money
	44, // 19: moego.models.subscription.v1.PurchaseDetail.total_tax:type_name -> google.type.Money
	2,  // 20: moego.models.subscription.v1.Product.type:type_name -> moego.models.subscription.v1.Product.Type
	21, // 21: moego.models.subscription.v1.Product.seller:type_name -> moego.models.subscription.v1.User
	22, // 22: moego.models.subscription.v1.Product.purchase_limit:type_name -> moego.models.subscription.v1.PurchaseLimit
	3,  // 23: moego.models.subscription.v1.Product.business_type:type_name -> moego.models.subscription.v1.Product.BusinessType
	18, // 24: moego.models.subscription.v1.Product.extra:type_name -> moego.models.subscription.v1.ProductExtra
	19, // 25: moego.models.subscription.v1.ProductExtra.accounting_extra:type_name -> moego.models.subscription.v1.AccountingExtra
	37, // 26: moego.models.subscription.v1.AccountingExtra.floating_price:type_name -> moego.models.subscription.v1.AccountingExtra.FloatingPrice
	38, // 27: moego.models.subscription.v1.AccountingExtra.feature_texts:type_name -> moego.models.subscription.v1.AccountingExtra.Feature
	4,  // 28: moego.models.subscription.v1.Price.type:type_name -> moego.models.subscription.v1.Price.Type
	44, // 29: moego.models.subscription.v1.Price.unit_amount:type_name -> google.type.Money
	45, // 30: moego.models.subscription.v1.Price.billing_cycle:type_name -> moego.utils.v1.TimePeriod
	40, // 31: moego.models.subscription.v1.Price.prequalification:type_name -> moego.models.subscription.v1.Price.Prequalification
	5,  // 32: moego.models.subscription.v1.User.type:type_name -> moego.models.subscription.v1.User.Type
	6,  // 33: moego.models.subscription.v1.PurchaseLimit.type:type_name -> moego.models.subscription.v1.PurchaseLimit.Type
	7,  // 34: moego.models.subscription.v1.Feature.key:type_name -> moego.models.subscription.v1.Feature.Key
	42, // 35: moego.models.subscription.v1.Feature.setting:type_name -> moego.models.subscription.v1.Feature.Setting
	21, // 36: moego.models.subscription.v1.License.owner:type_name -> moego.models.subscription.v1.User
	8,  // 37: moego.models.subscription.v1.License.status:type_name -> moego.models.subscription.v1.License.Status
	23, // 38: moego.models.subscription.v1.Entitlement.feature:type_name -> moego.models.subscription.v1.Feature
	9,  // 39: moego.models.subscription.v1.Revision.type:type_name -> moego.models.subscription.v1.Revision.Type
	30, // 40: moego.models.subscription.v1.Revision.detail:type_name -> moego.models.subscription.v1.RevisionDetail
	46, // 41: moego.models.subscription.v1.Revision.created_at:type_name -> google.protobuf.Timestamp
	31, // 42: moego.models.subscription.v1.RevisionDetail.create_subscription_detail:type_name -> moego.models.subscription.v1.CreateSubscriptionDetail
	32, // 43: moego.models.subscription.v1.RevisionDetail.update_entitlement_detail:type_name -> moego.models.subscription.v1.UpdateEntitlementDetail
	12, // 44: moego.models.subscription.v1.CreateSubscriptionDetail.created_subscription:type_name -> moego.models.subscription.v1.Subscription
	28, // 45: moego.models.subscription.v1.UpdateEntitlementDetail.update_entitlement:type_name -> moego.models.subscription.v1.Entitlement
	23, // 46: moego.models.subscription.v1.UpdateEntitlementDetail.feature:type_name -> moego.models.subscription.v1.Feature
	21, // 47: moego.models.subscription.v1.UpdateEntitlementDetail.owner:type_name -> moego.models.subscription.v1.User
	44, // 48: moego.models.subscription.v1.Discount.amount_off:type_name -> google.type.Money
	43, // 49: moego.models.subscription.v1.Discount.validity_period:type_name -> google.type.Interval
	44, // 50: moego.models.subscription.v1.Report.total_sales_amount:type_name -> google.type.Money
	44, // 51: moego.models.subscription.v1.Report.cycle_amount:type_name -> google.type.Money
	44, // 52: moego.models.subscription.v1.Report.membership_total_sales_amount:type_name -> google.type.Money
	44, // 53: moego.models.subscription.v1.PurchaseDetail.Tax.amount:type_name -> google.type.Money
	39, // 54: moego.models.subscription.v1.AccountingExtra.FloatingPrice.price_table:type_name -> moego.models.subscription.v1.AccountingExtra.FloatingPrice.PriceTableRow
	44, // 55: moego.models.subscription.v1.AccountingExtra.FloatingPrice.PriceTableRow.smb_price_money:type_name -> google.type.Money
	41, // 56: moego.models.subscription.v1.Price.Prequalification.revenue_rule:type_name -> moego.models.subscription.v1.Price.RevenueRule
	45, // 57: moego.models.subscription.v1.Price.RevenueRule.period:type_name -> moego.utils.v1.TimePeriod
	47, // 58: moego.models.subscription.v1.Price.RevenueRule.revenue_condition:type_name -> moego.utils.v2.Predicate
	24, // 59: moego.models.subscription.v1.Feature.Setting.on_off:type_name -> moego.models.subscription.v1.OnOff
	25, // 60: moego.models.subscription.v1.Feature.Setting.count:type_name -> moego.models.subscription.v1.Count
	26, // 61: moego.models.subscription.v1.Feature.Setting.access_list:type_name -> moego.models.subscription.v1.AccessList
	62, // [62:62] is the sub-list for method output_type
	62, // [62:62] is the sub-list for method input_type
	62, // [62:62] is the sub-list for extension type_name
	62, // [62:62] is the sub-list for extension extendee
	0,  // [0:62] is the sub-list for field type_name
}

func init() { file_moego_models_subscription_v1_subscription_models_proto_init() }
func file_moego_models_subscription_v1_subscription_models_proto_init() {
	if File_moego_models_subscription_v1_subscription_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Subscription); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubscriptionStat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BuyerSubscription); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Purchase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PurchaseDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Product); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountingExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Price); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PurchaseLimit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Feature); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnOff); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Count); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccessList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*License); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Entitlement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Revision); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RevisionDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSubscriptionDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEntitlementDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCredit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Discount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Report); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PurchaseDetail_Tax); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountingExtra_FloatingPrice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountingExtra_Feature); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountingExtra_FloatingPrice_PriceTableRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Price_Prequalification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Price_RevenueRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_subscription_v1_subscription_models_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Feature_Setting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_subscription_v1_subscription_models_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*ProductExtra_AccountingExtra)(nil),
	}
	file_moego_models_subscription_v1_subscription_models_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_models_subscription_v1_subscription_models_proto_msgTypes[18].OneofWrappers = []interface{}{
		(*RevisionDetail_CreateSubscriptionDetail)(nil),
		(*RevisionDetail_UpdateEntitlementDetail)(nil),
	}
	file_moego_models_subscription_v1_subscription_models_proto_msgTypes[28].OneofWrappers = []interface{}{
		(*Price_Prequalification_RevenueRule)(nil),
	}
	file_moego_models_subscription_v1_subscription_models_proto_msgTypes[30].OneofWrappers = []interface{}{
		(*Feature_Setting_OnOff)(nil),
		(*Feature_Setting_Count)(nil),
		(*Feature_Setting_AccessList)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_subscription_v1_subscription_models_proto_rawDesc,
			NumEnums:      12,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_subscription_v1_subscription_models_proto_goTypes,
		DependencyIndexes: file_moego_models_subscription_v1_subscription_models_proto_depIdxs,
		EnumInfos:         file_moego_models_subscription_v1_subscription_models_proto_enumTypes,
		MessageInfos:      file_moego_models_subscription_v1_subscription_models_proto_msgTypes,
	}.Build()
	File_moego_models_subscription_v1_subscription_models_proto = out.File
	file_moego_models_subscription_v1_subscription_models_proto_rawDesc = nil
	file_moego_models_subscription_v1_subscription_models_proto_goTypes = nil
	file_moego_models_subscription_v1_subscription_models_proto_depIdxs = nil
}
