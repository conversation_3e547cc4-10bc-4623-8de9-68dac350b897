// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/smart_scheduler/v1/smart_schedule_setting_models.proto

package smartschedulerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// service model
type SmartScheduleSettingModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// certain area for certain days, mobile business setting(company level)
	ServiceAreaEnable bool `protobuf:"varint,2,opt,name=service_area_enable,json=serviceAreaEnable,proto3" json:"service_area_enable,omitempty"`
	// appointment buffer time(company level, if param set business_id, use business level)
	BufferTime int32 `protobuf:"varint,3,opt,name=buffer_time,json=bufferTime,proto3" json:"buffer_time,omitempty"`
}

func (x *SmartScheduleSettingModel) Reset() {
	*x = SmartScheduleSettingModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmartScheduleSettingModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmartScheduleSettingModel) ProtoMessage() {}

func (x *SmartScheduleSettingModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmartScheduleSettingModel.ProtoReflect.Descriptor instead.
func (*SmartScheduleSettingModel) Descriptor() ([]byte, []int) {
	return file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_rawDescGZIP(), []int{0}
}

func (x *SmartScheduleSettingModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SmartScheduleSettingModel) GetServiceAreaEnable() bool {
	if x != nil {
		return x.ServiceAreaEnable
	}
	return false
}

func (x *SmartScheduleSettingModel) GetBufferTime() int32 {
	if x != nil {
		return x.BufferTime
	}
	return 0
}

// business setting override model
type BusinessSettingOverrideModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// buffer time
	BufferTime *int32 `protobuf:"varint,2,opt,name=buffer_time,json=bufferTime,proto3,oneof" json:"buffer_time,omitempty"`
}

func (x *BusinessSettingOverrideModel) Reset() {
	*x = BusinessSettingOverrideModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessSettingOverrideModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessSettingOverrideModel) ProtoMessage() {}

func (x *BusinessSettingOverrideModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessSettingOverrideModel.ProtoReflect.Descriptor instead.
func (*BusinessSettingOverrideModel) Descriptor() ([]byte, []int) {
	return file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessSettingOverrideModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessSettingOverrideModel) GetBufferTime() int32 {
	if x != nil && x.BufferTime != nil {
		return *x.BufferTime
	}
	return 0
}

var File_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto protoreflect.FileDescriptor

var file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_rawDesc = []byte{
	0x0a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73,
	0x6d, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x22, 0x8b, 0x01, 0x0a, 0x19, 0x53, 0x6d, 0x61, 0x72, 0x74,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61,
	0x72, 0x65, 0x61, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x72, 0x65, 0x61, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x62, 0x75, 0x66, 0x66, 0x65, 0x72,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0x75, 0x0a, 0x1c, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0b, 0x62, 0x75, 0x66, 0x66, 0x65, 0x72, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75,
	0x66, 0x66, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x62, 0x75, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x92, 0x01, 0x0a, 0x27,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x65, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73, 0x6d, 0x61,
	0x72, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b,
	0x73, 0x6d, 0x61, 0x72, 0x74, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_rawDescOnce sync.Once
	file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_rawDescData = file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_rawDesc
)

func file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_rawDescGZIP() []byte {
	file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_rawDescOnce.Do(func() {
		file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_rawDescData)
	})
	return file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_rawDescData
}

var file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_goTypes = []interface{}{
	(*SmartScheduleSettingModel)(nil),    // 0: moego.models.smart_scheduler.v1.SmartScheduleSettingModel
	(*BusinessSettingOverrideModel)(nil), // 1: moego.models.smart_scheduler.v1.BusinessSettingOverrideModel
}
var file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_init() }
func file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_init() {
	if File_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmartScheduleSettingModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessSettingOverrideModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_goTypes,
		DependencyIndexes: file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_depIdxs,
		MessageInfos:      file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_msgTypes,
	}.Build()
	File_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto = out.File
	file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_rawDesc = nil
	file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_goTypes = nil
	file_moego_models_smart_scheduler_v1_smart_schedule_setting_models_proto_depIdxs = nil
}
