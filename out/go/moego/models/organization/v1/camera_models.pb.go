// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/organization/v1/camera_models.proto

package organizationpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Camera Model
type CameraModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Company ID
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Business ID
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Config ID
	ConfigId int64 `protobuf:"varint,4,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`
	// Camera Type
	Type CameraConfigType `protobuf:"varint,5,opt,name=type,proto3,enum=moego.models.organization.v1.CameraConfigType" json:"type,omitempty"`
	// Origin Camera ID
	OriginCameraId string `protobuf:"bytes,6,opt,name=origin_camera_id,json=originCameraId,proto3" json:"origin_camera_id,omitempty"`
	// Origin Camera Title
	OriginCameraTitle string `protobuf:"bytes,7,opt,name=origin_camera_title,json=originCameraTitle,proto3" json:"origin_camera_title,omitempty"`
	// video url
	VideoUrl string `protobuf:"bytes,8,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	// Origin Status
	OriginStatus OriginStatus `protobuf:"varint,9,opt,name=origin_status,json=originStatus,proto3,enum=moego.models.organization.v1.OriginStatus" json:"origin_status,omitempty"`
	// Is Active
	IsActive bool `protobuf:"varint,10,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// Visibility Type
	VisibilityType VisibilityType `protobuf:"varint,11,opt,name=visibility_type,json=visibilityType,proto3,enum=moego.models.organization.v1.VisibilityType" json:"visibility_type,omitempty"`
	// Created Time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// Updated Time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *CameraModel) Reset() {
	*x = CameraModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_camera_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CameraModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CameraModel) ProtoMessage() {}

func (x *CameraModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_camera_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CameraModel.ProtoReflect.Descriptor instead.
func (*CameraModel) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_camera_models_proto_rawDescGZIP(), []int{0}
}

func (x *CameraModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CameraModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CameraModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CameraModel) GetConfigId() int64 {
	if x != nil {
		return x.ConfigId
	}
	return 0
}

func (x *CameraModel) GetType() CameraConfigType {
	if x != nil {
		return x.Type
	}
	return CameraConfigType_CAMERA_TYPE_UNSPECIFIED
}

func (x *CameraModel) GetOriginCameraId() string {
	if x != nil {
		return x.OriginCameraId
	}
	return ""
}

func (x *CameraModel) GetOriginCameraTitle() string {
	if x != nil {
		return x.OriginCameraTitle
	}
	return ""
}

func (x *CameraModel) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *CameraModel) GetOriginStatus() OriginStatus {
	if x != nil {
		return x.OriginStatus
	}
	return OriginStatus_ORIGIN_STATUS_UNSPECIFIED
}

func (x *CameraModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *CameraModel) GetVisibilityType() VisibilityType {
	if x != nil {
		return x.VisibilityType
	}
	return VisibilityType_VISIBILITY_TYPE_UNSPECIFIED
}

func (x *CameraModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CameraModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_moego_models_organization_v1_camera_models_proto protoreflect.FileDescriptor

var file_moego_models_organization_v1_camera_models_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xf0, 0x04, 0x0a, 0x0b, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12,
	0x42, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d,
	0x65, 0x72, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x61,
	0x6d, 0x65, 0x72, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x49, 0x64, 0x12, 0x2e, 0x0a,
	0x13, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x5f, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x43, 0x61, 0x6d, 0x65, 0x72, 0x61, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x4f, 0x0a, 0x0d, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x69,
	0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x55, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0e, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x8a, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_organization_v1_camera_models_proto_rawDescOnce sync.Once
	file_moego_models_organization_v1_camera_models_proto_rawDescData = file_moego_models_organization_v1_camera_models_proto_rawDesc
)

func file_moego_models_organization_v1_camera_models_proto_rawDescGZIP() []byte {
	file_moego_models_organization_v1_camera_models_proto_rawDescOnce.Do(func() {
		file_moego_models_organization_v1_camera_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_organization_v1_camera_models_proto_rawDescData)
	})
	return file_moego_models_organization_v1_camera_models_proto_rawDescData
}

var file_moego_models_organization_v1_camera_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_organization_v1_camera_models_proto_goTypes = []interface{}{
	(*CameraModel)(nil),           // 0: moego.models.organization.v1.CameraModel
	(CameraConfigType)(0),         // 1: moego.models.organization.v1.CameraConfigType
	(OriginStatus)(0),             // 2: moego.models.organization.v1.OriginStatus
	(VisibilityType)(0),           // 3: moego.models.organization.v1.VisibilityType
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
}
var file_moego_models_organization_v1_camera_models_proto_depIdxs = []int32{
	1, // 0: moego.models.organization.v1.CameraModel.type:type_name -> moego.models.organization.v1.CameraConfigType
	2, // 1: moego.models.organization.v1.CameraModel.origin_status:type_name -> moego.models.organization.v1.OriginStatus
	3, // 2: moego.models.organization.v1.CameraModel.visibility_type:type_name -> moego.models.organization.v1.VisibilityType
	4, // 3: moego.models.organization.v1.CameraModel.created_at:type_name -> google.protobuf.Timestamp
	4, // 4: moego.models.organization.v1.CameraModel.updated_at:type_name -> google.protobuf.Timestamp
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_models_organization_v1_camera_models_proto_init() }
func file_moego_models_organization_v1_camera_models_proto_init() {
	if File_moego_models_organization_v1_camera_models_proto != nil {
		return
	}
	file_moego_models_organization_v1_camera_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_organization_v1_camera_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CameraModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_organization_v1_camera_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_organization_v1_camera_models_proto_goTypes,
		DependencyIndexes: file_moego_models_organization_v1_camera_models_proto_depIdxs,
		MessageInfos:      file_moego_models_organization_v1_camera_models_proto_msgTypes,
	}.Build()
	File_moego_models_organization_v1_camera_models_proto = out.File
	file_moego_models_organization_v1_camera_models_proto_rawDesc = nil
	file_moego_models_organization_v1_camera_models_proto_goTypes = nil
	file_moego_models_organization_v1_camera_models_proto_depIdxs = nil
}
