// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/organization/v1/location_enums.proto

package organizationpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// Business type
type BusinessType int32

const (
	// Mobile Grooming
	BusinessType_MOBILE BusinessType = 0
	// Salon
	BusinessType_SALON BusinessType = 1
	// Mobile & Salon
	BusinessType_HYBRID BusinessType = 2
)

// Enum value maps for BusinessType.
var (
	BusinessType_name = map[int32]string{
		0: "MOBILE",
		1: "SALON",
		2: "HYBRID",
	}
	BusinessType_value = map[string]int32{
		"MOBILE": 0,
		"SALON":  1,
		"HYBRID": 2,
	}
)

func (x BusinessType) Enum() *BusinessType {
	p := new(BusinessType)
	*p = x
	return p
}

func (x BusinessType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BusinessType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_location_enums_proto_enumTypes[0].Descriptor()
}

func (BusinessType) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_location_enums_proto_enumTypes[0]
}

func (x BusinessType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BusinessType.Descriptor instead.
func (BusinessType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_location_enums_proto_rawDescGZIP(), []int{0}
}

// Business source from type
type BusinessSourceFromType int32

const (
	// UNSPECIFIED
	BusinessSourceFromType_BUSINESS_SOURCE_FROM_UNSPECIFIED BusinessSourceFromType = 0
	// APP_ANDROID
	BusinessSourceFromType_APP_ANDROID BusinessSourceFromType = 1
	// APP_IOS
	BusinessSourceFromType_APP_IOS BusinessSourceFromType = 2
	// WEB_ANDROID
	BusinessSourceFromType_WEB_ANDROID BusinessSourceFromType = 3
	// WEB_IOS
	BusinessSourceFromType_WEB_IOS BusinessSourceFromType = 4
	// WEB_DESKTOP
	BusinessSourceFromType_WEB_DESKTOP BusinessSourceFromType = 5
	// enterprise_tenant
	BusinessSourceFromType_ENTERPRISE_TENANT BusinessSourceFromType = 6
)

// Enum value maps for BusinessSourceFromType.
var (
	BusinessSourceFromType_name = map[int32]string{
		0: "BUSINESS_SOURCE_FROM_UNSPECIFIED",
		1: "APP_ANDROID",
		2: "APP_IOS",
		3: "WEB_ANDROID",
		4: "WEB_IOS",
		5: "WEB_DESKTOP",
		6: "ENTERPRISE_TENANT",
	}
	BusinessSourceFromType_value = map[string]int32{
		"BUSINESS_SOURCE_FROM_UNSPECIFIED": 0,
		"APP_ANDROID":                      1,
		"APP_IOS":                          2,
		"WEB_ANDROID":                      3,
		"WEB_IOS":                          4,
		"WEB_DESKTOP":                      5,
		"ENTERPRISE_TENANT":                6,
	}
)

func (x BusinessSourceFromType) Enum() *BusinessSourceFromType {
	p := new(BusinessSourceFromType)
	*p = x
	return p
}

func (x BusinessSourceFromType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BusinessSourceFromType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_location_enums_proto_enumTypes[1].Descriptor()
}

func (BusinessSourceFromType) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_location_enums_proto_enumTypes[1]
}

func (x BusinessSourceFromType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BusinessSourceFromType.Descriptor instead.
func (BusinessSourceFromType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_location_enums_proto_rawDescGZIP(), []int{1}
}

// Source Type (How to know us)
type SourceType int32

const (
	// UNSPECIFIED
	SourceType_SOURCE_TYPE_UNSPECIFIED SourceType = 0
	// Other
	SourceType_OTHER SourceType = 1
	// FACEBOOK
	SourceType_FACEBOOK SourceType = 2
	// INTERNET_SEARCH
	SourceType_INTERNET_SEARCH SourceType = 3
	// CAPTERRA
	SourceType_CAPTERRA SourceType = 4
	// SQUARE
	SourceType_SQUARE SourceType = 5
	// Expo
	SourceType_EXPO SourceType = 6
)

// Enum value maps for SourceType.
var (
	SourceType_name = map[int32]string{
		0: "SOURCE_TYPE_UNSPECIFIED",
		1: "OTHER",
		2: "FACEBOOK",
		3: "INTERNET_SEARCH",
		4: "CAPTERRA",
		5: "SQUARE",
		6: "EXPO",
	}
	SourceType_value = map[string]int32{
		"SOURCE_TYPE_UNSPECIFIED": 0,
		"OTHER":                   1,
		"FACEBOOK":                2,
		"INTERNET_SEARCH":         3,
		"CAPTERRA":                4,
		"SQUARE":                  5,
		"EXPO":                    6,
	}
)

func (x SourceType) Enum() *SourceType {
	p := new(SourceType)
	*p = x
	return p
}

func (x SourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_location_enums_proto_enumTypes[2].Descriptor()
}

func (SourceType) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_location_enums_proto_enumTypes[2]
}

func (x SourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SourceType.Descriptor instead.
func (SourceType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_location_enums_proto_rawDescGZIP(), []int{2}
}

var File_moego_models_organization_v1_location_enums_proto protoreflect.FileDescriptor

var file_moego_models_organization_v1_location_enums_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2a, 0x31, 0x0a, 0x0c, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x10, 0x00, 0x12, 0x09, 0x0a,
	0x05, 0x53, 0x41, 0x4c, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x48, 0x59, 0x42, 0x52,
	0x49, 0x44, 0x10, 0x02, 0x2a, 0xa2, 0x01, 0x0a, 0x16, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x24, 0x0a, 0x20, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x50, 0x50, 0x5f, 0x41, 0x4e, 0x44,
	0x52, 0x4f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x50, 0x50, 0x5f, 0x49, 0x4f,
	0x53, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x57, 0x45, 0x42, 0x5f, 0x41, 0x4e, 0x44, 0x52, 0x4f,
	0x49, 0x44, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x57, 0x45, 0x42, 0x5f, 0x49, 0x4f, 0x53, 0x10,
	0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x57, 0x45, 0x42, 0x5f, 0x44, 0x45, 0x53, 0x4b, 0x54, 0x4f, 0x50,
	0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45,
	0x5f, 0x54, 0x45, 0x4e, 0x41, 0x4e, 0x54, 0x10, 0x06, 0x2a, 0x7b, 0x0a, 0x0a, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x4f, 0x55, 0x52, 0x43,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x01, 0x12,
	0x0c, 0x0a, 0x08, 0x46, 0x41, 0x43, 0x45, 0x42, 0x4f, 0x4f, 0x4b, 0x10, 0x02, 0x12, 0x13, 0x0a,
	0x0f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x45, 0x54, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48,
	0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x50, 0x54, 0x45, 0x52, 0x52, 0x41, 0x10, 0x04,
	0x12, 0x0a, 0x0a, 0x06, 0x53, 0x51, 0x55, 0x41, 0x52, 0x45, 0x10, 0x05, 0x12, 0x08, 0x0a, 0x04,
	0x45, 0x58, 0x50, 0x4f, 0x10, 0x06, 0x42, 0x8a, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_organization_v1_location_enums_proto_rawDescOnce sync.Once
	file_moego_models_organization_v1_location_enums_proto_rawDescData = file_moego_models_organization_v1_location_enums_proto_rawDesc
)

func file_moego_models_organization_v1_location_enums_proto_rawDescGZIP() []byte {
	file_moego_models_organization_v1_location_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_organization_v1_location_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_organization_v1_location_enums_proto_rawDescData)
	})
	return file_moego_models_organization_v1_location_enums_proto_rawDescData
}

var file_moego_models_organization_v1_location_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_organization_v1_location_enums_proto_goTypes = []interface{}{
	(BusinessType)(0),           // 0: moego.models.organization.v1.BusinessType
	(BusinessSourceFromType)(0), // 1: moego.models.organization.v1.BusinessSourceFromType
	(SourceType)(0),             // 2: moego.models.organization.v1.SourceType
}
var file_moego_models_organization_v1_location_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_organization_v1_location_enums_proto_init() }
func file_moego_models_organization_v1_location_enums_proto_init() {
	if File_moego_models_organization_v1_location_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_organization_v1_location_enums_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_organization_v1_location_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_organization_v1_location_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_organization_v1_location_enums_proto_enumTypes,
	}.Build()
	File_moego_models_organization_v1_location_enums_proto = out.File
	file_moego_models_organization_v1_location_enums_proto_rawDesc = nil
	file_moego_models_organization_v1_location_enums_proto_goTypes = nil
	file_moego_models_organization_v1_location_enums_proto_depIdxs = nil
}
