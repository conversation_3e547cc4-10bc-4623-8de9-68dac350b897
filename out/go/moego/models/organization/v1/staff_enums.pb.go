// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/organization/v1/staff_enums.proto

package organizationpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// staff employee category enum
type StaffEmployeeCategory int32

const (
	// company staff
	StaffEmployeeCategory_COMPANY_STAFF StaffEmployeeCategory = 0
	// company owner
	StaffEmployeeCategory_COMPANY_OWNER StaffEmployeeCategory = 1
	// enterprise owner
	StaffEmployeeCategory_ENTERPRISE_OWNER StaffEmployeeCategory = 2
	// enterprise staff
	StaffEmployeeCategory_ENTERPRISE_STAFF StaffEmployeeCategory = 3
)

// Enum value maps for StaffEmployeeCategory.
var (
	StaffEmployeeCategory_name = map[int32]string{
		0: "COMPANY_STAFF",
		1: "COMPANY_OWNER",
		2: "ENTERPRISE_OWNER",
		3: "ENTERPRISE_STAFF",
	}
	StaffEmployeeCategory_value = map[string]int32{
		"COMPANY_STAFF":    0,
		"COMPANY_OWNER":    1,
		"ENTERPRISE_OWNER": 2,
		"ENTERPRISE_STAFF": 3,
	}
)

func (x StaffEmployeeCategory) Enum() *StaffEmployeeCategory {
	p := new(StaffEmployeeCategory)
	*p = x
	return p
}

func (x StaffEmployeeCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StaffEmployeeCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_staff_enums_proto_enumTypes[0].Descriptor()
}

func (StaffEmployeeCategory) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_staff_enums_proto_enumTypes[0]
}

func (x StaffEmployeeCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StaffEmployeeCategory.Descriptor instead.
func (StaffEmployeeCategory) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_enums_proto_rawDescGZIP(), []int{0}
}

// staff invite link send method type enum
type StaffInviteLinkSendMethodType int32

const (
	// send invite link unspecified
	StaffInviteLinkSendMethodType_STAFF_INVITE_LINK_SEND_METHOD_TYPE_UNSPECIFIED StaffInviteLinkSendMethodType = 0
	// send invite link via sms
	StaffInviteLinkSendMethodType_SMS StaffInviteLinkSendMethodType = 1
	// send invite link via email
	StaffInviteLinkSendMethodType_EMAIL StaffInviteLinkSendMethodType = 2
)

// Enum value maps for StaffInviteLinkSendMethodType.
var (
	StaffInviteLinkSendMethodType_name = map[int32]string{
		0: "STAFF_INVITE_LINK_SEND_METHOD_TYPE_UNSPECIFIED",
		1: "SMS",
		2: "EMAIL",
	}
	StaffInviteLinkSendMethodType_value = map[string]int32{
		"STAFF_INVITE_LINK_SEND_METHOD_TYPE_UNSPECIFIED": 0,
		"SMS":   1,
		"EMAIL": 2,
	}
)

func (x StaffInviteLinkSendMethodType) Enum() *StaffInviteLinkSendMethodType {
	p := new(StaffInviteLinkSendMethodType)
	*p = x
	return p
}

func (x StaffInviteLinkSendMethodType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StaffInviteLinkSendMethodType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_staff_enums_proto_enumTypes[1].Descriptor()
}

func (StaffInviteLinkSendMethodType) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_staff_enums_proto_enumTypes[1]
}

func (x StaffInviteLinkSendMethodType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StaffInviteLinkSendMethodType.Descriptor instead.
func (StaffInviteLinkSendMethodType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_enums_proto_rawDescGZIP(), []int{1}
}

// staff link status enum
type StaffLinkStatus int32

const (
	// staff link status unspecified
	StaffLinkStatus_STAFF_LINK_STATUS_UNSPECIFIED StaffLinkStatus = 0
	// staff link status unlinked
	StaffLinkStatus_UNLINKED StaffLinkStatus = 1
	// staff link status pending
	StaffLinkStatus_PENDING StaffLinkStatus = 2
	// staff link status linked
	StaffLinkStatus_LINKED StaffLinkStatus = 3
)

// Enum value maps for StaffLinkStatus.
var (
	StaffLinkStatus_name = map[int32]string{
		0: "STAFF_LINK_STATUS_UNSPECIFIED",
		1: "UNLINKED",
		2: "PENDING",
		3: "LINKED",
	}
	StaffLinkStatus_value = map[string]int32{
		"STAFF_LINK_STATUS_UNSPECIFIED": 0,
		"UNLINKED":                      1,
		"PENDING":                       2,
		"LINKED":                        3,
	}
)

func (x StaffLinkStatus) Enum() *StaffLinkStatus {
	p := new(StaffLinkStatus)
	*p = x
	return p
}

func (x StaffLinkStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StaffLinkStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_staff_enums_proto_enumTypes[2].Descriptor()
}

func (StaffLinkStatus) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_staff_enums_proto_enumTypes[2]
}

func (x StaffLinkStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StaffLinkStatus.Descriptor instead.
func (StaffLinkStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_enums_proto_rawDescGZIP(), []int{2}
}

// staff type
type StaffSource int32

const (
	// staff source unspecified, default value
	StaffSource_STAFF_SOURCE_UNSPECIFIED StaffSource = 0
	// created by system
	StaffSource_STAFF_SOURCE_SYSTEM StaffSource = 1
)

// Enum value maps for StaffSource.
var (
	StaffSource_name = map[int32]string{
		0: "STAFF_SOURCE_UNSPECIFIED",
		1: "STAFF_SOURCE_SYSTEM",
	}
	StaffSource_value = map[string]int32{
		"STAFF_SOURCE_UNSPECIFIED": 0,
		"STAFF_SOURCE_SYSTEM":      1,
	}
)

func (x StaffSource) Enum() *StaffSource {
	p := new(StaffSource)
	*p = x
	return p
}

func (x StaffSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StaffSource) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_staff_enums_proto_enumTypes[3].Descriptor()
}

func (StaffSource) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_staff_enums_proto_enumTypes[3]
}

func (x StaffSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StaffSource.Descriptor instead.
func (StaffSource) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_enums_proto_rawDescGZIP(), []int{3}
}

// staff login limit type
type StaffLoginLimitType int32

const (
	// unspecified
	StaffLoginLimitType_LOGIN_LIMIT_TYPE_UNSPECIFIED StaffLoginLimitType = 0
	// no limit
	StaffLoginLimitType_NO_LIMIT StaffLoginLimitType = 1
	// time range in a day
	StaffLoginLimitType_TIME_RANGE_IN_ONE_DAY StaffLoginLimitType = 2
)

// Enum value maps for StaffLoginLimitType.
var (
	StaffLoginLimitType_name = map[int32]string{
		0: "LOGIN_LIMIT_TYPE_UNSPECIFIED",
		1: "NO_LIMIT",
		2: "TIME_RANGE_IN_ONE_DAY",
	}
	StaffLoginLimitType_value = map[string]int32{
		"LOGIN_LIMIT_TYPE_UNSPECIFIED": 0,
		"NO_LIMIT":                     1,
		"TIME_RANGE_IN_ONE_DAY":        2,
	}
)

func (x StaffLoginLimitType) Enum() *StaffLoginLimitType {
	p := new(StaffLoginLimitType)
	*p = x
	return p
}

func (x StaffLoginLimitType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StaffLoginLimitType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_staff_enums_proto_enumTypes[4].Descriptor()
}

func (StaffLoginLimitType) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_staff_enums_proto_enumTypes[4]
}

func (x StaffLoginLimitType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StaffLoginLimitType.Descriptor instead.
func (StaffLoginLimitType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_staff_enums_proto_rawDescGZIP(), []int{4}
}

var File_moego_models_organization_v1_staff_enums_proto protoreflect.FileDescriptor

var file_moego_models_organization_v1_staff_enums_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2a, 0x69,
	0x0a, 0x15, 0x53, 0x74, 0x61, 0x66, 0x66, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x4f, 0x4d, 0x50, 0x41,
	0x4e, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x4f,
	0x4d, 0x50, 0x41, 0x4e, 0x59, 0x5f, 0x4f, 0x57, 0x4e, 0x45, 0x52, 0x10, 0x01, 0x12, 0x14, 0x0a,
	0x10, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x5f, 0x4f, 0x57, 0x4e, 0x45,
	0x52, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x10, 0x03, 0x2a, 0x67, 0x0a, 0x1d, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x65, 0x6e, 0x64,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x2e, 0x53, 0x54,
	0x41, 0x46, 0x46, 0x5f, 0x49, 0x4e, 0x56, 0x49, 0x54, 0x45, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x5f,
	0x53, 0x45, 0x4e, 0x44, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07,
	0x0a, 0x03, 0x53, 0x4d, 0x53, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x4d, 0x41, 0x49, 0x4c,
	0x10, 0x02, 0x2a, 0x5b, 0x0a, 0x0f, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x6e, 0x6b, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x1d, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x4c,
	0x49, 0x4e, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x4e, 0x4c, 0x49,
	0x4e, 0x4b, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x10, 0x03, 0x2a,
	0x44, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x66, 0x66, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1c,
	0x0a, 0x18, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13,
	0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x53, 0x59, 0x53,
	0x54, 0x45, 0x4d, 0x10, 0x01, 0x2a, 0x60, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c,
	0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x4e, 0x4f, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15,
	0x54, 0x49, 0x4d, 0x45, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x4f, 0x4e,
	0x45, 0x5f, 0x44, 0x41, 0x59, 0x10, 0x02, 0x42, 0x8a, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_organization_v1_staff_enums_proto_rawDescOnce sync.Once
	file_moego_models_organization_v1_staff_enums_proto_rawDescData = file_moego_models_organization_v1_staff_enums_proto_rawDesc
)

func file_moego_models_organization_v1_staff_enums_proto_rawDescGZIP() []byte {
	file_moego_models_organization_v1_staff_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_organization_v1_staff_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_organization_v1_staff_enums_proto_rawDescData)
	})
	return file_moego_models_organization_v1_staff_enums_proto_rawDescData
}

var file_moego_models_organization_v1_staff_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_moego_models_organization_v1_staff_enums_proto_goTypes = []interface{}{
	(StaffEmployeeCategory)(0),         // 0: moego.models.organization.v1.StaffEmployeeCategory
	(StaffInviteLinkSendMethodType)(0), // 1: moego.models.organization.v1.StaffInviteLinkSendMethodType
	(StaffLinkStatus)(0),               // 2: moego.models.organization.v1.StaffLinkStatus
	(StaffSource)(0),                   // 3: moego.models.organization.v1.StaffSource
	(StaffLoginLimitType)(0),           // 4: moego.models.organization.v1.StaffLoginLimitType
}
var file_moego_models_organization_v1_staff_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_organization_v1_staff_enums_proto_init() }
func file_moego_models_organization_v1_staff_enums_proto_init() {
	if File_moego_models_organization_v1_staff_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_organization_v1_staff_enums_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_organization_v1_staff_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_organization_v1_staff_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_organization_v1_staff_enums_proto_enumTypes,
	}.Build()
	File_moego_models_organization_v1_staff_enums_proto = out.File
	file_moego_models_organization_v1_staff_enums_proto_rawDesc = nil
	file_moego_models_organization_v1_staff_enums_proto_goTypes = nil
	file_moego_models_organization_v1_staff_enums_proto_depIdxs = nil
}
