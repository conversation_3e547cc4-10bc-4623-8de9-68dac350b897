// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/organization/v1/working_hour_defs.proto

package organizationpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// working hours
type WorkingHoursDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// working hours for monday
	Monday []*TimeRangeDef `protobuf:"bytes,1,rep,name=monday,proto3" json:"monday,omitempty"`
	// working hours for tuesday
	Tuesday []*TimeRangeDef `protobuf:"bytes,2,rep,name=tuesday,proto3" json:"tuesday,omitempty"`
	// working hours for wednesday
	Wednesday []*TimeRangeDef `protobuf:"bytes,3,rep,name=wednesday,proto3" json:"wednesday,omitempty"`
	// working hours for thursday
	Thursday []*TimeRangeDef `protobuf:"bytes,4,rep,name=thursday,proto3" json:"thursday,omitempty"`
	// working hours for friday
	Friday []*TimeRangeDef `protobuf:"bytes,5,rep,name=friday,proto3" json:"friday,omitempty"`
	// working hours for saturday
	Saturday []*TimeRangeDef `protobuf:"bytes,6,rep,name=saturday,proto3" json:"saturday,omitempty"`
	// working hours for sunday
	Sunday []*TimeRangeDef `protobuf:"bytes,7,rep,name=sunday,proto3" json:"sunday,omitempty"`
}

func (x *WorkingHoursDef) Reset() {
	*x = WorkingHoursDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_working_hour_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkingHoursDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkingHoursDef) ProtoMessage() {}

func (x *WorkingHoursDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_working_hour_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkingHoursDef.ProtoReflect.Descriptor instead.
func (*WorkingHoursDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_working_hour_defs_proto_rawDescGZIP(), []int{0}
}

func (x *WorkingHoursDef) GetMonday() []*TimeRangeDef {
	if x != nil {
		return x.Monday
	}
	return nil
}

func (x *WorkingHoursDef) GetTuesday() []*TimeRangeDef {
	if x != nil {
		return x.Tuesday
	}
	return nil
}

func (x *WorkingHoursDef) GetWednesday() []*TimeRangeDef {
	if x != nil {
		return x.Wednesday
	}
	return nil
}

func (x *WorkingHoursDef) GetThursday() []*TimeRangeDef {
	if x != nil {
		return x.Thursday
	}
	return nil
}

func (x *WorkingHoursDef) GetFriday() []*TimeRangeDef {
	if x != nil {
		return x.Friday
	}
	return nil
}

func (x *WorkingHoursDef) GetSaturday() []*TimeRangeDef {
	if x != nil {
		return x.Saturday
	}
	return nil
}

func (x *WorkingHoursDef) GetSunday() []*TimeRangeDef {
	if x != nil {
		return x.Sunday
	}
	return nil
}

// time range of working hours
type TimeRangeDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start minute of working hours
	StartTime int32 `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// end minute of working hours
	EndTime int32 `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *TimeRangeDef) Reset() {
	*x = TimeRangeDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_working_hour_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeRangeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeRangeDef) ProtoMessage() {}

func (x *TimeRangeDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_working_hour_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeRangeDef.ProtoReflect.Descriptor instead.
func (*TimeRangeDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_working_hour_defs_proto_rawDescGZIP(), []int{1}
}

func (x *TimeRangeDef) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *TimeRangeDef) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

var File_moego_models_organization_v1_working_hour_defs_proto protoreflect.FileDescriptor

var file_moego_models_organization_v1_working_hour_defs_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfd, 0x03,
	0x0a, 0x0f, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x44, 0x65,
	0x66, 0x12, 0x42, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x64, 0x61, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x06, 0x6d,
	0x6f, 0x6e, 0x64, 0x61, 0x79, 0x12, 0x44, 0x0a, 0x07, 0x74, 0x75, 0x65, 0x73, 0x64, 0x61, 0x79,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44,
	0x65, 0x66, 0x52, 0x07, 0x74, 0x75, 0x65, 0x73, 0x64, 0x61, 0x79, 0x12, 0x48, 0x0a, 0x09, 0x77,
	0x65, 0x64, 0x6e, 0x65, 0x73, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x09, 0x77, 0x65, 0x64, 0x6e,
	0x65, 0x73, 0x64, 0x61, 0x79, 0x12, 0x46, 0x0a, 0x08, 0x74, 0x68, 0x75, 0x72, 0x73, 0x64, 0x61,
	0x79, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x44, 0x65, 0x66, 0x52, 0x08, 0x74, 0x68, 0x75, 0x72, 0x73, 0x64, 0x61, 0x79, 0x12, 0x42, 0x0a,
	0x06, 0x66, 0x72, 0x69, 0x64, 0x61, 0x79, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x06, 0x66, 0x72, 0x69, 0x64, 0x61,
	0x79, 0x12, 0x46, 0x0a, 0x08, 0x73, 0x61, 0x74, 0x75, 0x72, 0x64, 0x61, 0x79, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52,
	0x08, 0x73, 0x61, 0x74, 0x75, 0x72, 0x64, 0x61, 0x79, 0x12, 0x42, 0x0a, 0x06, 0x73, 0x75, 0x6e,
	0x64, 0x61, 0x79, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x06, 0x73, 0x75, 0x6e, 0x64, 0x61, 0x79, 0x22, 0x60, 0x0a,
	0x0c, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x12, 0x29, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x10, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a,
	0x05, 0x10, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x42,
	0x8a, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_organization_v1_working_hour_defs_proto_rawDescOnce sync.Once
	file_moego_models_organization_v1_working_hour_defs_proto_rawDescData = file_moego_models_organization_v1_working_hour_defs_proto_rawDesc
)

func file_moego_models_organization_v1_working_hour_defs_proto_rawDescGZIP() []byte {
	file_moego_models_organization_v1_working_hour_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_organization_v1_working_hour_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_organization_v1_working_hour_defs_proto_rawDescData)
	})
	return file_moego_models_organization_v1_working_hour_defs_proto_rawDescData
}

var file_moego_models_organization_v1_working_hour_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_organization_v1_working_hour_defs_proto_goTypes = []interface{}{
	(*WorkingHoursDef)(nil), // 0: moego.models.organization.v1.WorkingHoursDef
	(*TimeRangeDef)(nil),    // 1: moego.models.organization.v1.TimeRangeDef
}
var file_moego_models_organization_v1_working_hour_defs_proto_depIdxs = []int32{
	1, // 0: moego.models.organization.v1.WorkingHoursDef.monday:type_name -> moego.models.organization.v1.TimeRangeDef
	1, // 1: moego.models.organization.v1.WorkingHoursDef.tuesday:type_name -> moego.models.organization.v1.TimeRangeDef
	1, // 2: moego.models.organization.v1.WorkingHoursDef.wednesday:type_name -> moego.models.organization.v1.TimeRangeDef
	1, // 3: moego.models.organization.v1.WorkingHoursDef.thursday:type_name -> moego.models.organization.v1.TimeRangeDef
	1, // 4: moego.models.organization.v1.WorkingHoursDef.friday:type_name -> moego.models.organization.v1.TimeRangeDef
	1, // 5: moego.models.organization.v1.WorkingHoursDef.saturday:type_name -> moego.models.organization.v1.TimeRangeDef
	1, // 6: moego.models.organization.v1.WorkingHoursDef.sunday:type_name -> moego.models.organization.v1.TimeRangeDef
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_moego_models_organization_v1_working_hour_defs_proto_init() }
func file_moego_models_organization_v1_working_hour_defs_proto_init() {
	if File_moego_models_organization_v1_working_hour_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_organization_v1_working_hour_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkingHoursDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_working_hour_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeRangeDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_organization_v1_working_hour_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_organization_v1_working_hour_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_organization_v1_working_hour_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_organization_v1_working_hour_defs_proto_msgTypes,
	}.Build()
	File_moego_models_organization_v1_working_hour_defs_proto = out.File
	file_moego_models_organization_v1_working_hour_defs_proto_rawDesc = nil
	file_moego_models_organization_v1_working_hour_defs_proto_goTypes = nil
	file_moego_models_organization_v1_working_hour_defs_proto_depIdxs = nil
}
