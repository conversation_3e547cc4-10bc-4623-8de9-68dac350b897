// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/organization/v1/enterprise_models.proto

package organizationpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// source
type EnterpriseModel_Source int32

const (
	// normally add
	EnterpriseModel_NORMALLY_ADD EnterpriseModel_Source = 0
	// manually add
	EnterpriseModel_MANUALLY_ADD EnterpriseModel_Source = 1
	// split company
	EnterpriseModel_SPLIT_COMPANY EnterpriseModel_Source = 2
)

// Enum value maps for EnterpriseModel_Source.
var (
	EnterpriseModel_Source_name = map[int32]string{
		0: "NORMALLY_ADD",
		1: "MANUALLY_ADD",
		2: "SPLIT_COMPANY",
	}
	EnterpriseModel_Source_value = map[string]int32{
		"NORMALLY_ADD":  0,
		"MANUALLY_ADD":  1,
		"SPLIT_COMPANY": 2,
	}
)

func (x EnterpriseModel_Source) Enum() *EnterpriseModel_Source {
	p := new(EnterpriseModel_Source)
	*p = x
	return p
}

func (x EnterpriseModel_Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnterpriseModel_Source) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_enterprise_models_proto_enumTypes[0].Descriptor()
}

func (EnterpriseModel_Source) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_enterprise_models_proto_enumTypes[0]
}

func (x EnterpriseModel_Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnterpriseModel_Source.Descriptor instead.
func (EnterpriseModel_Source) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_enterprise_models_proto_rawDescGZIP(), []int{0, 0}
}

// EnterpriseModel
//
// Deprecated: Do not use.
type EnterpriseModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// account_id
	AccountId int64 `protobuf:"varint,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// email
	Email string `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	// source
	Source EnterpriseModel_Source `protobuf:"varint,5,opt,name=source,proto3,enum=moego.models.organization.v1.EnterpriseModel_Source" json:"source,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3,oneof" json:"updated_at,omitempty"`
}

func (x *EnterpriseModel) Reset() {
	*x = EnterpriseModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_enterprise_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnterpriseModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnterpriseModel) ProtoMessage() {}

func (x *EnterpriseModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_enterprise_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnterpriseModel.ProtoReflect.Descriptor instead.
func (*EnterpriseModel) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_enterprise_models_proto_rawDescGZIP(), []int{0}
}

func (x *EnterpriseModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EnterpriseModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EnterpriseModel) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *EnterpriseModel) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *EnterpriseModel) GetSource() EnterpriseModel_Source {
	if x != nil {
		return x.Source
	}
	return EnterpriseModel_NORMALLY_ADD
}

func (x *EnterpriseModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *EnterpriseModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_moego_models_organization_v1_enterprise_models_proto protoreflect.FileDescriptor

var file_moego_models_organization_v1_enterprise_models_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x87, 0x03, 0x0a, 0x0f, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x4c, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3e, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x22, 0x3f, 0x0a, 0x06, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x4c,
	0x59, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x41, 0x4e, 0x55, 0x41,
	0x4c, 0x4c, 0x59, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x50, 0x4c,
	0x49, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59, 0x10, 0x02, 0x3a, 0x02, 0x18, 0x01,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42,
	0x8a, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_organization_v1_enterprise_models_proto_rawDescOnce sync.Once
	file_moego_models_organization_v1_enterprise_models_proto_rawDescData = file_moego_models_organization_v1_enterprise_models_proto_rawDesc
)

func file_moego_models_organization_v1_enterprise_models_proto_rawDescGZIP() []byte {
	file_moego_models_organization_v1_enterprise_models_proto_rawDescOnce.Do(func() {
		file_moego_models_organization_v1_enterprise_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_organization_v1_enterprise_models_proto_rawDescData)
	})
	return file_moego_models_organization_v1_enterprise_models_proto_rawDescData
}

var file_moego_models_organization_v1_enterprise_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_organization_v1_enterprise_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_organization_v1_enterprise_models_proto_goTypes = []interface{}{
	(EnterpriseModel_Source)(0),   // 0: moego.models.organization.v1.EnterpriseModel.Source
	(*EnterpriseModel)(nil),       // 1: moego.models.organization.v1.EnterpriseModel
	(*timestamppb.Timestamp)(nil), // 2: google.protobuf.Timestamp
}
var file_moego_models_organization_v1_enterprise_models_proto_depIdxs = []int32{
	0, // 0: moego.models.organization.v1.EnterpriseModel.source:type_name -> moego.models.organization.v1.EnterpriseModel.Source
	2, // 1: moego.models.organization.v1.EnterpriseModel.created_at:type_name -> google.protobuf.Timestamp
	2, // 2: moego.models.organization.v1.EnterpriseModel.updated_at:type_name -> google.protobuf.Timestamp
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_organization_v1_enterprise_models_proto_init() }
func file_moego_models_organization_v1_enterprise_models_proto_init() {
	if File_moego_models_organization_v1_enterprise_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_organization_v1_enterprise_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnterpriseModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_organization_v1_enterprise_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_organization_v1_enterprise_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_organization_v1_enterprise_models_proto_goTypes,
		DependencyIndexes: file_moego_models_organization_v1_enterprise_models_proto_depIdxs,
		EnumInfos:         file_moego_models_organization_v1_enterprise_models_proto_enumTypes,
		MessageInfos:      file_moego_models_organization_v1_enterprise_models_proto_msgTypes,
	}.Build()
	File_moego_models_organization_v1_enterprise_models_proto = out.File
	file_moego_models_organization_v1_enterprise_models_proto_rawDesc = nil
	file_moego_models_organization_v1_enterprise_models_proto_goTypes = nil
	file_moego_models_organization_v1_enterprise_models_proto_depIdxs = nil
}
