// @since 2-23-12-05
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/pay_ops/v1/billing_enums.proto

package payopspb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// in the sdk provided by Stripe,
// the add, delete, and update api have been combined into one
// therefore, at the api layer,
// actions are differentiated through enumeration
// and data integration is performed in the admin api based on the type passed from the frontend
type EditSubscriptionType int32

const (
	// unspecified
	EditSubscriptionType_EDIT_SUBSCRIPTION_UNSPECIFIED EditSubscriptionType = 0
	// add subscription
	EditSubscriptionType_EDIT_SUBSCRIPTION_ADD EditSubscriptionType = 1
	// delete subscription
	EditSubscriptionType_EDIT_SUBSCRIPTION_DELETE EditSubscriptionType = 2
	// update subscription
	EditSubscriptionType_EDIT_SUBSCRIPTION_UPDATE EditSubscriptionType = 3
)

// Enum value maps for EditSubscriptionType.
var (
	EditSubscriptionType_name = map[int32]string{
		0: "EDIT_SUBSCRIPTION_UNSPECIFIED",
		1: "EDIT_SUBSCRIPTION_ADD",
		2: "EDIT_SUBSCRIPTION_DELETE",
		3: "EDIT_SUBSCRIPTION_UPDATE",
	}
	EditSubscriptionType_value = map[string]int32{
		"EDIT_SUBSCRIPTION_UNSPECIFIED": 0,
		"EDIT_SUBSCRIPTION_ADD":         1,
		"EDIT_SUBSCRIPTION_DELETE":      2,
		"EDIT_SUBSCRIPTION_UPDATE":      3,
	}
)

func (x EditSubscriptionType) Enum() *EditSubscriptionType {
	p := new(EditSubscriptionType)
	*p = x
	return p
}

func (x EditSubscriptionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EditSubscriptionType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_pay_ops_v1_billing_enums_proto_enumTypes[0].Descriptor()
}

func (EditSubscriptionType) Type() protoreflect.EnumType {
	return &file_moego_models_pay_ops_v1_billing_enums_proto_enumTypes[0]
}

func (x EditSubscriptionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EditSubscriptionType.Descriptor instead.
func (EditSubscriptionType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_billing_enums_proto_rawDescGZIP(), []int{0}
}

var File_moego_models_pay_ops_v1_billing_enums_proto protoreflect.FileDescriptor

var file_moego_models_pay_ops_v1_billing_enums_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f,
	0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2a, 0x90, 0x01, 0x0a, 0x14, 0x45, 0x64, 0x69, 0x74, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x21, 0x0a, 0x1d, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43,
	0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x01, 0x12, 0x1c, 0x0a,
	0x18, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x45,
	0x44, 0x49, 0x54, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0x03, 0x42, 0x7a, 0x0a, 0x1f, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x55,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79,
	0x6f, 0x70, 0x73, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_pay_ops_v1_billing_enums_proto_rawDescOnce sync.Once
	file_moego_models_pay_ops_v1_billing_enums_proto_rawDescData = file_moego_models_pay_ops_v1_billing_enums_proto_rawDesc
)

func file_moego_models_pay_ops_v1_billing_enums_proto_rawDescGZIP() []byte {
	file_moego_models_pay_ops_v1_billing_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_pay_ops_v1_billing_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_pay_ops_v1_billing_enums_proto_rawDescData)
	})
	return file_moego_models_pay_ops_v1_billing_enums_proto_rawDescData
}

var file_moego_models_pay_ops_v1_billing_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_pay_ops_v1_billing_enums_proto_goTypes = []interface{}{
	(EditSubscriptionType)(0), // 0: moego.models.pay_ops.v1.EditSubscriptionType
}
var file_moego_models_pay_ops_v1_billing_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_pay_ops_v1_billing_enums_proto_init() }
func file_moego_models_pay_ops_v1_billing_enums_proto_init() {
	if File_moego_models_pay_ops_v1_billing_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_pay_ops_v1_billing_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_pay_ops_v1_billing_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_pay_ops_v1_billing_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_pay_ops_v1_billing_enums_proto_enumTypes,
	}.Build()
	File_moego_models_pay_ops_v1_billing_enums_proto = out.File
	file_moego_models_pay_ops_v1_billing_enums_proto_rawDesc = nil
	file_moego_models_pay_ops_v1_billing_enums_proto_goTypes = nil
	file_moego_models_pay_ops_v1_billing_enums_proto_depIdxs = nil
}
