// @since 2023-07-03 12:43:02
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/ai_assistant/v1/business_conversation_enums.proto

package aiassistantpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The BusinessConversation status
type BusinessConversationStatus int32

const (
	// unspecified
	BusinessConversationStatus_BUSINESS_CONVERSATION_STATUS_UNSPECIFIED BusinessConversationStatus = 0
	// communicating
	BusinessConversationStatus_BUSINESS_CONVERSATION_STATUS_COMMUNICATING BusinessConversationStatus = 1
	// closed
	BusinessConversationStatus_BUSINESS_CONVERSATION_STATUS_CLOSED BusinessConversationStatus = 2
)

// Enum value maps for BusinessConversationStatus.
var (
	BusinessConversationStatus_name = map[int32]string{
		0: "BUSINESS_CONVERSATION_STATUS_UNSPECIFIED",
		1: "BUSINESS_CONVERSATION_STATUS_COMMUNICATING",
		2: "BUSINESS_CONVERSATION_STATUS_CLOSED",
	}
	BusinessConversationStatus_value = map[string]int32{
		"BUSINESS_CONVERSATION_STATUS_UNSPECIFIED":   0,
		"BUSINESS_CONVERSATION_STATUS_COMMUNICATING": 1,
		"BUSINESS_CONVERSATION_STATUS_CLOSED":        2,
	}
)

func (x BusinessConversationStatus) Enum() *BusinessConversationStatus {
	p := new(BusinessConversationStatus)
	*p = x
	return p
}

func (x BusinessConversationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BusinessConversationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_ai_assistant_v1_business_conversation_enums_proto_enumTypes[0].Descriptor()
}

func (BusinessConversationStatus) Type() protoreflect.EnumType {
	return &file_moego_models_ai_assistant_v1_business_conversation_enums_proto_enumTypes[0]
}

func (x BusinessConversationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BusinessConversationStatus.Descriptor instead.
func (BusinessConversationStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_ai_assistant_v1_business_conversation_enums_proto_rawDescGZIP(), []int{0}
}

// The BusinessConversationQuestion status
type BusinessConversationQuestionStatus int32

const (
	// unspecified
	BusinessConversationQuestionStatus_BUSINESS_CONVERSATION_QUESTION_STATUS_UNSPECIFIED BusinessConversationQuestionStatus = 0
	// created
	BusinessConversationQuestionStatus_BUSINESS_CONVERSATION_QUESTION_STATUS_CREATED BusinessConversationQuestionStatus = 1
	// failed
	BusinessConversationQuestionStatus_BUSINESS_CONVERSATION_QUESTION_STATUS_FAILED BusinessConversationQuestionStatus = 2
	// replied
	BusinessConversationQuestionStatus_BUSINESS_CONVERSATION_QUESTION_STATUS_REPLIED BusinessConversationQuestionStatus = 3
	// abandoned
	BusinessConversationQuestionStatus_BUSINESS_CONVERSATION_QUESTION_STATUS_ABANDONED BusinessConversationQuestionStatus = 4
)

// Enum value maps for BusinessConversationQuestionStatus.
var (
	BusinessConversationQuestionStatus_name = map[int32]string{
		0: "BUSINESS_CONVERSATION_QUESTION_STATUS_UNSPECIFIED",
		1: "BUSINESS_CONVERSATION_QUESTION_STATUS_CREATED",
		2: "BUSINESS_CONVERSATION_QUESTION_STATUS_FAILED",
		3: "BUSINESS_CONVERSATION_QUESTION_STATUS_REPLIED",
		4: "BUSINESS_CONVERSATION_QUESTION_STATUS_ABANDONED",
	}
	BusinessConversationQuestionStatus_value = map[string]int32{
		"BUSINESS_CONVERSATION_QUESTION_STATUS_UNSPECIFIED": 0,
		"BUSINESS_CONVERSATION_QUESTION_STATUS_CREATED":     1,
		"BUSINESS_CONVERSATION_QUESTION_STATUS_FAILED":      2,
		"BUSINESS_CONVERSATION_QUESTION_STATUS_REPLIED":     3,
		"BUSINESS_CONVERSATION_QUESTION_STATUS_ABANDONED":   4,
	}
)

func (x BusinessConversationQuestionStatus) Enum() *BusinessConversationQuestionStatus {
	p := new(BusinessConversationQuestionStatus)
	*p = x
	return p
}

func (x BusinessConversationQuestionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BusinessConversationQuestionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_ai_assistant_v1_business_conversation_enums_proto_enumTypes[1].Descriptor()
}

func (BusinessConversationQuestionStatus) Type() protoreflect.EnumType {
	return &file_moego_models_ai_assistant_v1_business_conversation_enums_proto_enumTypes[1]
}

func (x BusinessConversationQuestionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BusinessConversationQuestionStatus.Descriptor instead.
func (BusinessConversationQuestionStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_ai_assistant_v1_business_conversation_enums_proto_rawDescGZIP(), []int{1}
}

var File_moego_models_ai_assistant_v1_business_conversation_enums_proto protoreflect.FileDescriptor

var file_moego_models_ai_assistant_v1_business_conversation_enums_proto_rawDesc = []byte{
	0x0a, 0x3e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2a, 0xa3,
	0x01, 0x0a, 0x1a, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x0a,
	0x28, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52,
	0x53, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2e, 0x0a, 0x2a, 0x42,
	0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x4d,
	0x55, 0x4e, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x42,
	0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x4f, 0x53,
	0x45, 0x44, 0x10, 0x02, 0x2a, 0xa8, 0x02, 0x0a, 0x22, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x31, 0x42,
	0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x31, 0x0a, 0x2d, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43,
	0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x30, 0x0a, 0x2c, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53,
	0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x31, 0x0a, 0x2d, 0x42, 0x55, 0x53, 0x49, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x52, 0x45, 0x50, 0x4c, 0x49, 0x45, 0x44, 0x10, 0x03, 0x12, 0x33, 0x0a, 0x2f, 0x42, 0x55,
	0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x41, 0x42, 0x41, 0x4e, 0x44, 0x4f, 0x4e, 0x45, 0x44, 0x10, 0x04, 0x42,
	0x89, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5f, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x69,
	0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x69,
	0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_models_ai_assistant_v1_business_conversation_enums_proto_rawDescOnce sync.Once
	file_moego_models_ai_assistant_v1_business_conversation_enums_proto_rawDescData = file_moego_models_ai_assistant_v1_business_conversation_enums_proto_rawDesc
)

func file_moego_models_ai_assistant_v1_business_conversation_enums_proto_rawDescGZIP() []byte {
	file_moego_models_ai_assistant_v1_business_conversation_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_ai_assistant_v1_business_conversation_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_ai_assistant_v1_business_conversation_enums_proto_rawDescData)
	})
	return file_moego_models_ai_assistant_v1_business_conversation_enums_proto_rawDescData
}

var file_moego_models_ai_assistant_v1_business_conversation_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_ai_assistant_v1_business_conversation_enums_proto_goTypes = []interface{}{
	(BusinessConversationStatus)(0),         // 0: moego.models.ai_assistant.v1.BusinessConversationStatus
	(BusinessConversationQuestionStatus)(0), // 1: moego.models.ai_assistant.v1.BusinessConversationQuestionStatus
}
var file_moego_models_ai_assistant_v1_business_conversation_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_ai_assistant_v1_business_conversation_enums_proto_init() }
func file_moego_models_ai_assistant_v1_business_conversation_enums_proto_init() {
	if File_moego_models_ai_assistant_v1_business_conversation_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_ai_assistant_v1_business_conversation_enums_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_ai_assistant_v1_business_conversation_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_ai_assistant_v1_business_conversation_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_ai_assistant_v1_business_conversation_enums_proto_enumTypes,
	}.Build()
	File_moego_models_ai_assistant_v1_business_conversation_enums_proto = out.File
	file_moego_models_ai_assistant_v1_business_conversation_enums_proto_rawDesc = nil
	file_moego_models_ai_assistant_v1_business_conversation_enums_proto_goTypes = nil
	file_moego_models_ai_assistant_v1_business_conversation_enums_proto_depIdxs = nil
}
