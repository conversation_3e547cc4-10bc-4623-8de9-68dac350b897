// @since 2022-06-30 19:14:21
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/business_ob_gallery_models.proto

package onlinebookingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// BusinessOBGalleryModel
type BusinessOBGalleryModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// gallery id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// image path
	ImagePath string `protobuf:"bytes,4,opt,name=image_path,json=imagePath,proto3" json:"image_path,omitempty"`
	// sort number
	Sort int64 `protobuf:"varint,5,opt,name=sort,proto3" json:"sort,omitempty"`
	// false / true
	IsDelete bool `protobuf:"varint,6,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	// false / true
	IsStar bool `protobuf:"varint,7,opt,name=is_star,json=isStar,proto3" json:"is_star,omitempty"`
	// create time, milliseconds
	CreateTime int64 `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time, milliseconds
	UpdateTime int64 `protobuf:"varint,9,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *BusinessOBGalleryModel) Reset() {
	*x = BusinessOBGalleryModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_business_ob_gallery_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessOBGalleryModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessOBGalleryModel) ProtoMessage() {}

func (x *BusinessOBGalleryModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_business_ob_gallery_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessOBGalleryModel.ProtoReflect.Descriptor instead.
func (*BusinessOBGalleryModel) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_business_ob_gallery_models_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessOBGalleryModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessOBGalleryModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessOBGalleryModel) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *BusinessOBGalleryModel) GetImagePath() string {
	if x != nil {
		return x.ImagePath
	}
	return ""
}

func (x *BusinessOBGalleryModel) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *BusinessOBGalleryModel) GetIsDelete() bool {
	if x != nil {
		return x.IsDelete
	}
	return false
}

func (x *BusinessOBGalleryModel) GetIsStar() bool {
	if x != nil {
		return x.IsStar
	}
	return false
}

func (x *BusinessOBGalleryModel) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *BusinessOBGalleryModel) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

// BusinessOBGalleryClientPortalView
type BusinessOBGalleryClientPortalView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// gallery id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// image path
	ImagePath string `protobuf:"bytes,4,opt,name=image_path,json=imagePath,proto3" json:"image_path,omitempty"`
	// sort number
	Sort int64 `protobuf:"varint,5,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *BusinessOBGalleryClientPortalView) Reset() {
	*x = BusinessOBGalleryClientPortalView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_business_ob_gallery_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessOBGalleryClientPortalView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessOBGalleryClientPortalView) ProtoMessage() {}

func (x *BusinessOBGalleryClientPortalView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_business_ob_gallery_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessOBGalleryClientPortalView.ProtoReflect.Descriptor instead.
func (*BusinessOBGalleryClientPortalView) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_business_ob_gallery_models_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessOBGalleryClientPortalView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessOBGalleryClientPortalView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessOBGalleryClientPortalView) GetImagePath() string {
	if x != nil {
		return x.ImagePath
	}
	return ""
}

func (x *BusinessOBGalleryClientPortalView) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

var File_moego_models_online_booking_v1_business_ob_gallery_models_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_business_ob_gallery_models_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x62, 0x5f, 0x67, 0x61, 0x6c,
	0x6c, 0x65, 0x72, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x22, 0x8f, 0x02, 0x0a, 0x16, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x42,
	0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x69,
	0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x73,
	0x74, 0x61, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x53, 0x74, 0x61,
	0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x21, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x4f, 0x42, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50,
	0x6f, 0x72, 0x74, 0x61, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x42, 0x8f, 0x01,
	0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_online_booking_v1_business_ob_gallery_models_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_business_ob_gallery_models_proto_rawDescData = file_moego_models_online_booking_v1_business_ob_gallery_models_proto_rawDesc
)

func file_moego_models_online_booking_v1_business_ob_gallery_models_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_business_ob_gallery_models_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_business_ob_gallery_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_business_ob_gallery_models_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_business_ob_gallery_models_proto_rawDescData
}

var file_moego_models_online_booking_v1_business_ob_gallery_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_online_booking_v1_business_ob_gallery_models_proto_goTypes = []interface{}{
	(*BusinessOBGalleryModel)(nil),            // 0: moego.models.online_booking.v1.BusinessOBGalleryModel
	(*BusinessOBGalleryClientPortalView)(nil), // 1: moego.models.online_booking.v1.BusinessOBGalleryClientPortalView
}
var file_moego_models_online_booking_v1_business_ob_gallery_models_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_business_ob_gallery_models_proto_init() }
func file_moego_models_online_booking_v1_business_ob_gallery_models_proto_init() {
	if File_moego_models_online_booking_v1_business_ob_gallery_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_online_booking_v1_business_ob_gallery_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessOBGalleryModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_business_ob_gallery_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessOBGalleryClientPortalView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_business_ob_gallery_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_business_ob_gallery_models_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_business_ob_gallery_models_proto_depIdxs,
		MessageInfos:      file_moego_models_online_booking_v1_business_ob_gallery_models_proto_msgTypes,
	}.Build()
	File_moego_models_online_booking_v1_business_ob_gallery_models_proto = out.File
	file_moego_models_online_booking_v1_business_ob_gallery_models_proto_rawDesc = nil
	file_moego_models_online_booking_v1_business_ob_gallery_models_proto_goTypes = nil
	file_moego_models_online_booking_v1_business_ob_gallery_models_proto_depIdxs = nil
}
