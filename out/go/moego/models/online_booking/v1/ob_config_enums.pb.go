// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/ob_config_enums.proto

package onlinebookingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ob access client type
type AcceptClientType int32

const (
	// unspecified
	AcceptClientType_ACCEPT_CLIENT_TYPE_UNSPECIFIED AcceptClientType = 0
	// only accept new client
	AcceptClientType_ACCEPT_CLIENT_TYPE_NEW AcceptClientType = 1
	// only accept existing client
	AcceptClientType_ACCEPT_CLIENT_TYPE_EXISTING AcceptClientType = 2
	// accept both new and existing client
	AcceptClientType_ACCEPT_CLIENT_TYPE_BOTH AcceptClientType = 3
)

// Enum value maps for AcceptClientType.
var (
	AcceptClientType_name = map[int32]string{
		0: "ACCEPT_CLIENT_TYPE_UNSPECIFIED",
		1: "ACCEPT_CLIENT_TYPE_NEW",
		2: "ACCEPT_CLIENT_TYPE_EXISTING",
		3: "ACCEPT_CLIENT_TYPE_BOTH",
	}
	AcceptClientType_value = map[string]int32{
		"ACCEPT_CLIENT_TYPE_UNSPECIFIED": 0,
		"ACCEPT_CLIENT_TYPE_NEW":         1,
		"ACCEPT_CLIENT_TYPE_EXISTING":    2,
		"ACCEPT_CLIENT_TYPE_BOTH":        3,
	}
)

func (x AcceptClientType) Enum() *AcceptClientType {
	p := new(AcceptClientType)
	*p = x
	return p
}

func (x AcceptClientType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AcceptClientType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_online_booking_v1_ob_config_enums_proto_enumTypes[0].Descriptor()
}

func (AcceptClientType) Type() protoreflect.EnumType {
	return &file_moego_models_online_booking_v1_ob_config_enums_proto_enumTypes[0]
}

func (x AcceptClientType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AcceptClientType.Descriptor instead.
func (AcceptClientType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_config_enums_proto_rawDescGZIP(), []int{0}
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// ob availability type
type AvailabilityType int32

const (
	// by working hours
	AvailabilityType_AVAILABILITY_TYPE_BY_WORKING_HOURS AvailabilityType = 0
	// by slots
	AvailabilityType_AVAILABILITY_TYPE_BY_SLOTS AvailabilityType = 1
	// disable select time
	AvailabilityType_AVAILABILITY_TYPE_DISABLE_SELECT_TIME AvailabilityType = 2
)

// Enum value maps for AvailabilityType.
var (
	AvailabilityType_name = map[int32]string{
		0: "AVAILABILITY_TYPE_BY_WORKING_HOURS",
		1: "AVAILABILITY_TYPE_BY_SLOTS",
		2: "AVAILABILITY_TYPE_DISABLE_SELECT_TIME",
	}
	AvailabilityType_value = map[string]int32{
		"AVAILABILITY_TYPE_BY_WORKING_HOURS":    0,
		"AVAILABILITY_TYPE_BY_SLOTS":            1,
		"AVAILABILITY_TYPE_DISABLE_SELECT_TIME": 2,
	}
)

func (x AvailabilityType) Enum() *AvailabilityType {
	p := new(AvailabilityType)
	*p = x
	return p
}

func (x AvailabilityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AvailabilityType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_online_booking_v1_ob_config_enums_proto_enumTypes[1].Descriptor()
}

func (AvailabilityType) Type() protoreflect.EnumType {
	return &file_moego_models_online_booking_v1_ob_config_enums_proto_enumTypes[1]
}

func (x AvailabilityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AvailabilityType.Descriptor instead.
func (AvailabilityType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_config_enums_proto_rawDescGZIP(), []int{1}
}

// time slot format
type TimeSlotFormat int32

const (
	// unspecified
	TimeSlotFormat_TIME_SLOT_FORMAT_UNSPECIFIED TimeSlotFormat = 0
	// exact times
	TimeSlotFormat_TIME_SLOT_FORMAT_EXACT_TIMES TimeSlotFormat = 1
	// arrival windows
	TimeSlotFormat_TIME_SLOT_FORMAT_ARRIVAL_WINDOWS TimeSlotFormat = 2
	// date only
	TimeSlotFormat_TIME_SLOT_FORMAT_DATE_ONLY TimeSlotFormat = 3
)

// Enum value maps for TimeSlotFormat.
var (
	TimeSlotFormat_name = map[int32]string{
		0: "TIME_SLOT_FORMAT_UNSPECIFIED",
		1: "TIME_SLOT_FORMAT_EXACT_TIMES",
		2: "TIME_SLOT_FORMAT_ARRIVAL_WINDOWS",
		3: "TIME_SLOT_FORMAT_DATE_ONLY",
	}
	TimeSlotFormat_value = map[string]int32{
		"TIME_SLOT_FORMAT_UNSPECIFIED":     0,
		"TIME_SLOT_FORMAT_EXACT_TIMES":     1,
		"TIME_SLOT_FORMAT_ARRIVAL_WINDOWS": 2,
		"TIME_SLOT_FORMAT_DATE_ONLY":       3,
	}
)

func (x TimeSlotFormat) Enum() *TimeSlotFormat {
	p := new(TimeSlotFormat)
	*p = x
	return p
}

func (x TimeSlotFormat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TimeSlotFormat) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_online_booking_v1_ob_config_enums_proto_enumTypes[2].Descriptor()
}

func (TimeSlotFormat) Type() protoreflect.EnumType {
	return &file_moego_models_online_booking_v1_ob_config_enums_proto_enumTypes[2]
}

func (x TimeSlotFormat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TimeSlotFormat.Descriptor instead.
func (TimeSlotFormat) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_config_enums_proto_rawDescGZIP(), []int{2}
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// ob prepay type
type PrepayType int32

const (
	// full amount
	PrepayType_PREPAY_TYPE_FULL_AMOUNT PrepayType = 0
	// deposit
	PrepayType_PREPAY_TYPE_DEPOSIT PrepayType = 1
)

// Enum value maps for PrepayType.
var (
	PrepayType_name = map[int32]string{
		0: "PREPAY_TYPE_FULL_AMOUNT",
		1: "PREPAY_TYPE_DEPOSIT",
	}
	PrepayType_value = map[string]int32{
		"PREPAY_TYPE_FULL_AMOUNT": 0,
		"PREPAY_TYPE_DEPOSIT":     1,
	}
)

func (x PrepayType) Enum() *PrepayType {
	p := new(PrepayType)
	*p = x
	return p
}

func (x PrepayType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PrepayType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_online_booking_v1_ob_config_enums_proto_enumTypes[3].Descriptor()
}

func (PrepayType) Type() protoreflect.EnumType {
	return &file_moego_models_online_booking_v1_ob_config_enums_proto_enumTypes[3]
}

func (x PrepayType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PrepayType.Descriptor instead.
func (PrepayType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_config_enums_proto_rawDescGZIP(), []int{3}
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// ob prepay deposit type
type PrepayDepositType int32

const (
	// fixed amount
	PrepayDepositType_PREPAY_DEPOSIT_TYPE_FIXED_AMOUNT PrepayDepositType = 0
	// percentage
	PrepayDepositType_PREPAY_DEPOSIT_TYPE_PERCENTAGE PrepayDepositType = 1
)

// Enum value maps for PrepayDepositType.
var (
	PrepayDepositType_name = map[int32]string{
		0: "PREPAY_DEPOSIT_TYPE_FIXED_AMOUNT",
		1: "PREPAY_DEPOSIT_TYPE_PERCENTAGE",
	}
	PrepayDepositType_value = map[string]int32{
		"PREPAY_DEPOSIT_TYPE_FIXED_AMOUNT": 0,
		"PREPAY_DEPOSIT_TYPE_PERCENTAGE":   1,
	}
)

func (x PrepayDepositType) Enum() *PrepayDepositType {
	p := new(PrepayDepositType)
	*p = x
	return p
}

func (x PrepayDepositType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PrepayDepositType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_online_booking_v1_ob_config_enums_proto_enumTypes[4].Descriptor()
}

func (PrepayDepositType) Type() protoreflect.EnumType {
	return &file_moego_models_online_booking_v1_ob_config_enums_proto_enumTypes[4]
}

func (x PrepayDepositType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PrepayDepositType.Descriptor instead.
func (PrepayDepositType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_config_enums_proto_rawDescGZIP(), []int{4}
}

// ob booking range end date type
type BookingRangeEndType int32

const (
	// unspecified
	BookingRangeEndType_BOOKING_RANGE_END_TYPE_UNSPECIFIED BookingRangeEndType = 0
	// relative date
	BookingRangeEndType_BOOKING_RANGE_END_TYPE_RELATIVE BookingRangeEndType = 1
	// absolute date
	BookingRangeEndType_BOOKING_RANGE_END_TYPE_ABSOLUTE BookingRangeEndType = 2
)

// Enum value maps for BookingRangeEndType.
var (
	BookingRangeEndType_name = map[int32]string{
		0: "BOOKING_RANGE_END_TYPE_UNSPECIFIED",
		1: "BOOKING_RANGE_END_TYPE_RELATIVE",
		2: "BOOKING_RANGE_END_TYPE_ABSOLUTE",
	}
	BookingRangeEndType_value = map[string]int32{
		"BOOKING_RANGE_END_TYPE_UNSPECIFIED": 0,
		"BOOKING_RANGE_END_TYPE_RELATIVE":    1,
		"BOOKING_RANGE_END_TYPE_ABSOLUTE":    2,
	}
)

func (x BookingRangeEndType) Enum() *BookingRangeEndType {
	p := new(BookingRangeEndType)
	*p = x
	return p
}

func (x BookingRangeEndType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BookingRangeEndType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_online_booking_v1_ob_config_enums_proto_enumTypes[5].Descriptor()
}

func (BookingRangeEndType) Type() protoreflect.EnumType {
	return &file_moego_models_online_booking_v1_ob_config_enums_proto_enumTypes[5]
}

func (x BookingRangeEndType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BookingRangeEndType.Descriptor instead.
func (BookingRangeEndType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_config_enums_proto_rawDescGZIP(), []int{5}
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// ob payments type
type PaymentType int32

const (
	// disable payments
	PaymentType_PAYMENT_TYPE_DISABLE PaymentType = 0
	// card of file
	PaymentType_PAYMENT_TYPE_CARD_ON_FILE PaymentType = 1
	// prepayment / deposit
	PaymentType_PAYMENT_TYPE_PREPAY PaymentType = 2
	// pre-auth
	PaymentType_PAYMENT_TYPE_PRE_AUTH PaymentType = 3
)

// Enum value maps for PaymentType.
var (
	PaymentType_name = map[int32]string{
		0: "PAYMENT_TYPE_DISABLE",
		1: "PAYMENT_TYPE_CARD_ON_FILE",
		2: "PAYMENT_TYPE_PREPAY",
		3: "PAYMENT_TYPE_PRE_AUTH",
	}
	PaymentType_value = map[string]int32{
		"PAYMENT_TYPE_DISABLE":      0,
		"PAYMENT_TYPE_CARD_ON_FILE": 1,
		"PAYMENT_TYPE_PREPAY":       2,
		"PAYMENT_TYPE_PRE_AUTH":     3,
	}
)

func (x PaymentType) Enum() *PaymentType {
	p := new(PaymentType)
	*p = x
	return p
}

func (x PaymentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_online_booking_v1_ob_config_enums_proto_enumTypes[6].Descriptor()
}

func (PaymentType) Type() protoreflect.EnumType {
	return &file_moego_models_online_booking_v1_ob_config_enums_proto_enumTypes[6]
}

func (x PaymentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentType.Descriptor instead.
func (PaymentType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_config_enums_proto_rawDescGZIP(), []int{6}
}

var File_moego_models_online_booking_v1_ob_config_enums_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_ob_config_enums_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x6f, 0x62, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2a, 0x90, 0x01, 0x0a, 0x10, 0x41, 0x63, 0x63, 0x65, 0x70,
	0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x41,
	0x43, 0x43, 0x45, 0x50, 0x54, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x1a, 0x0a, 0x16, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x45, 0x57, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x41,
	0x43, 0x43, 0x45, 0x50, 0x54, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17,
	0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x42, 0x4f, 0x54, 0x48, 0x10, 0x03, 0x2a, 0x85, 0x01, 0x0a, 0x10, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26,
	0x0a, 0x22, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x42, 0x59, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x48,
	0x4f, 0x55, 0x52, 0x53, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41,
	0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x59, 0x5f, 0x53,
	0x4c, 0x4f, 0x54, 0x53, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41,
	0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x41,
	0x42, 0x4c, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10,
	0x02, 0x2a, 0x9a, 0x01, 0x0a, 0x0e, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x12, 0x20, 0x0a, 0x1c, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x53, 0x4c, 0x4f,
	0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x53,
	0x4c, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x45, 0x58, 0x41, 0x43, 0x54,
	0x5f, 0x54, 0x49, 0x4d, 0x45, 0x53, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20, 0x54, 0x49, 0x4d, 0x45,
	0x5f, 0x53, 0x4c, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x41, 0x52, 0x52,
	0x49, 0x56, 0x41, 0x4c, 0x5f, 0x57, 0x49, 0x4e, 0x44, 0x4f, 0x57, 0x53, 0x10, 0x02, 0x12, 0x1e,
	0x0a, 0x1a, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x53, 0x4c, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d,
	0x41, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x10, 0x03, 0x2a, 0x42,
	0x0a, 0x0a, 0x50, 0x72, 0x65, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17,
	0x50, 0x52, 0x45, 0x50, 0x41, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x55, 0x4c, 0x4c,
	0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x52, 0x45,
	0x50, 0x41, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54,
	0x10, 0x01, 0x2a, 0x5d, 0x0a, 0x11, 0x50, 0x72, 0x65, 0x70, 0x61, 0x79, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x20, 0x50, 0x52, 0x45, 0x50, 0x41,
	0x59, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46,
	0x49, 0x58, 0x45, 0x44, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x00, 0x12, 0x22, 0x0a,
	0x1e, 0x50, 0x52, 0x45, 0x50, 0x41, 0x59, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x41, 0x47, 0x45, 0x10,
	0x01, 0x2a, 0x87, 0x01, 0x0a, 0x13, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x45, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x42, 0x4f, 0x4f,
	0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x45, 0x4e, 0x44, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x23, 0x0a, 0x1f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x41, 0x4e,
	0x47, 0x45, 0x5f, 0x45, 0x4e, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x4c, 0x41,
	0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e,
	0x47, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x45, 0x4e, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x41, 0x42, 0x53, 0x4f, 0x4c, 0x55, 0x54, 0x45, 0x10, 0x02, 0x2a, 0x7a, 0x0a, 0x0b, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42,
	0x4c, 0x45, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x4c,
	0x45, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x50, 0x41, 0x59, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x45,
	0x5f, 0x41, 0x55, 0x54, 0x48, 0x10, 0x03, 0x42, 0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_models_online_booking_v1_ob_config_enums_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_ob_config_enums_proto_rawDescData = file_moego_models_online_booking_v1_ob_config_enums_proto_rawDesc
)

func file_moego_models_online_booking_v1_ob_config_enums_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_ob_config_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_ob_config_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_ob_config_enums_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_ob_config_enums_proto_rawDescData
}

var file_moego_models_online_booking_v1_ob_config_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_moego_models_online_booking_v1_ob_config_enums_proto_goTypes = []interface{}{
	(AcceptClientType)(0),    // 0: moego.models.online_booking.v1.AcceptClientType
	(AvailabilityType)(0),    // 1: moego.models.online_booking.v1.AvailabilityType
	(TimeSlotFormat)(0),      // 2: moego.models.online_booking.v1.TimeSlotFormat
	(PrepayType)(0),          // 3: moego.models.online_booking.v1.PrepayType
	(PrepayDepositType)(0),   // 4: moego.models.online_booking.v1.PrepayDepositType
	(BookingRangeEndType)(0), // 5: moego.models.online_booking.v1.BookingRangeEndType
	(PaymentType)(0),         // 6: moego.models.online_booking.v1.PaymentType
}
var file_moego_models_online_booking_v1_ob_config_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_ob_config_enums_proto_init() }
func file_moego_models_online_booking_v1_ob_config_enums_proto_init() {
	if File_moego_models_online_booking_v1_ob_config_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_ob_config_enums_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_ob_config_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_ob_config_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_online_booking_v1_ob_config_enums_proto_enumTypes,
	}.Build()
	File_moego_models_online_booking_v1_ob_config_enums_proto = out.File
	file_moego_models_online_booking_v1_ob_config_enums_proto_rawDesc = nil
	file_moego_models_online_booking_v1_ob_config_enums_proto_goTypes = nil
	file_moego_models_online_booking_v1_ob_config_enums_proto_depIdxs = nil
}
