// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/boarding_service_waitlist_defs.proto

package onlinebookingpb

import (
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// boarding waitlist
type BoardingWaitlist struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start date
	StartDate *date.Date `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
}

func (x *BoardingWaitlist) Reset() {
	*x = BoardingWaitlist{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardingWaitlist) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardingWaitlist) ProtoMessage() {}

func (x *BoardingWaitlist) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardingWaitlist.ProtoReflect.Descriptor instead.
func (*BoardingWaitlist) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_rawDescGZIP(), []int{0}
}

func (x *BoardingWaitlist) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *BoardingWaitlist) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

var File_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_rawDesc = []byte{
	0x0a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x72, 0x0a,
	0x10, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73,
	0x74, 0x12, 0x30, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x42, 0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_rawDescData = file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_rawDesc
)

func file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_rawDescData
}

var file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_goTypes = []interface{}{
	(*BoardingWaitlist)(nil), // 0: moego.models.online_booking.v1.BoardingWaitlist
	(*date.Date)(nil),        // 1: google.type.Date
}
var file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_depIdxs = []int32{
	1, // 0: moego.models.online_booking.v1.BoardingWaitlist.start_date:type_name -> google.type.Date
	1, // 1: moego.models.online_booking.v1.BoardingWaitlist.end_date:type_name -> google.type.Date
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_init() }
func file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_init() {
	if File_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardingWaitlist); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_msgTypes,
	}.Build()
	File_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto = out.File
	file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_rawDesc = nil
	file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_goTypes = nil
	file_moego_models_online_booking_v1_boarding_service_waitlist_defs_proto_depIdxs = nil
}
