// @since 2024-03-21 14:16:42
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/booking_request_enums.proto

package onlinebookingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Booking request status
type BookingRequestStatus int32

const (
	// unspecified
	BookingRequestStatus_BOOKING_REQUEST_STATUS_UNSPECIFIED BookingRequestStatus = 0
	// The user submitted it from online booking, but the business did not process it.
	BookingRequestStatus_SUBMITTED BookingRequestStatus = 1
	// moved to wait list
	BookingRequestStatus_WAIT_LIST BookingRequestStatus = 2
	// business accepts and scheduled to an appointment
	BookingRequestStatus_SCHEDULED BookingRequestStatus = 3
	// business rejects the booking request, it will be displayed in the canceled list.
	BookingRequestStatus_DECLINED BookingRequestStatus = 4
	// business deletes the booking request, it won't show up on any lists
	BookingRequestStatus_DELETED BookingRequestStatus = 5
	// payment failed, BookingRequest 通过 deposit 状态来判断是否支付成功
	//
	// Deprecated: Do not use.
	BookingRequestStatus_PAYMENT_FAILED BookingRequestStatus = 6
)

// Enum value maps for BookingRequestStatus.
var (
	BookingRequestStatus_name = map[int32]string{
		0: "BOOKING_REQUEST_STATUS_UNSPECIFIED",
		1: "SUBMITTED",
		2: "WAIT_LIST",
		3: "SCHEDULED",
		4: "DECLINED",
		5: "DELETED",
		6: "PAYMENT_FAILED",
	}
	BookingRequestStatus_value = map[string]int32{
		"BOOKING_REQUEST_STATUS_UNSPECIFIED": 0,
		"SUBMITTED":                          1,
		"WAIT_LIST":                          2,
		"SCHEDULED":                          3,
		"DECLINED":                           4,
		"DELETED":                            5,
		"PAYMENT_FAILED":                     6,
	}
)

func (x BookingRequestStatus) Enum() *BookingRequestStatus {
	p := new(BookingRequestStatus)
	*p = x
	return p
}

func (x BookingRequestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BookingRequestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_online_booking_v1_booking_request_enums_proto_enumTypes[0].Descriptor()
}

func (BookingRequestStatus) Type() protoreflect.EnumType {
	return &file_moego_models_online_booking_v1_booking_request_enums_proto_enumTypes[0]
}

func (x BookingRequestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BookingRequestStatus.Descriptor instead.
func (BookingRequestStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_enums_proto_rawDescGZIP(), []int{0}
}

// Booking request source platform
type BookingRequestSourcePlatform int32

const (
	// unspecified
	BookingRequestSourcePlatform_BOOKING_REQUEST_SOURCE_PLATFORM_UNSPECIFIED BookingRequestSourcePlatform = 0
	// reserve with google
	BookingRequestSourcePlatform_RESERVE_WITH_GOOGLE BookingRequestSourcePlatform = 1
	// pet parent app
	BookingRequestSourcePlatform_PET_PARENT_APP BookingRequestSourcePlatform = 2
)

// Enum value maps for BookingRequestSourcePlatform.
var (
	BookingRequestSourcePlatform_name = map[int32]string{
		0: "BOOKING_REQUEST_SOURCE_PLATFORM_UNSPECIFIED",
		1: "RESERVE_WITH_GOOGLE",
		2: "PET_PARENT_APP",
	}
	BookingRequestSourcePlatform_value = map[string]int32{
		"BOOKING_REQUEST_SOURCE_PLATFORM_UNSPECIFIED": 0,
		"RESERVE_WITH_GOOGLE":                         1,
		"PET_PARENT_APP":                              2,
	}
)

func (x BookingRequestSourcePlatform) Enum() *BookingRequestSourcePlatform {
	p := new(BookingRequestSourcePlatform)
	*p = x
	return p
}

func (x BookingRequestSourcePlatform) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BookingRequestSourcePlatform) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_online_booking_v1_booking_request_enums_proto_enumTypes[1].Descriptor()
}

func (BookingRequestSourcePlatform) Type() protoreflect.EnumType {
	return &file_moego_models_online_booking_v1_booking_request_enums_proto_enumTypes[1]
}

func (x BookingRequestSourcePlatform) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BookingRequestSourcePlatform.Descriptor instead.
func (BookingRequestSourcePlatform) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_enums_proto_rawDescGZIP(), []int{1}
}

// Booking request associated model
type BookingRequestAssociatedModel int32

const (
	// unspecified
	BookingRequestAssociatedModel_BOOKING_REQUEST_ASSOCIATED_MODEL_UNSPECIFIED BookingRequestAssociatedModel = 0
	// service
	BookingRequestAssociatedModel_SERVICE BookingRequestAssociatedModel = 1
	// add-on
	BookingRequestAssociatedModel_ADD_ON BookingRequestAssociatedModel = 2
	// feeding
	BookingRequestAssociatedModel_FEEDING BookingRequestAssociatedModel = 3
	// medication
	BookingRequestAssociatedModel_MEDICATION BookingRequestAssociatedModel = 4
	// auto assign
	BookingRequestAssociatedModel_AUTO_ASSIGN BookingRequestAssociatedModel = 5
)

// Enum value maps for BookingRequestAssociatedModel.
var (
	BookingRequestAssociatedModel_name = map[int32]string{
		0: "BOOKING_REQUEST_ASSOCIATED_MODEL_UNSPECIFIED",
		1: "SERVICE",
		2: "ADD_ON",
		3: "FEEDING",
		4: "MEDICATION",
		5: "AUTO_ASSIGN",
	}
	BookingRequestAssociatedModel_value = map[string]int32{
		"BOOKING_REQUEST_ASSOCIATED_MODEL_UNSPECIFIED": 0,
		"SERVICE":     1,
		"ADD_ON":      2,
		"FEEDING":     3,
		"MEDICATION":  4,
		"AUTO_ASSIGN": 5,
	}
)

func (x BookingRequestAssociatedModel) Enum() *BookingRequestAssociatedModel {
	p := new(BookingRequestAssociatedModel)
	*p = x
	return p
}

func (x BookingRequestAssociatedModel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BookingRequestAssociatedModel) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_online_booking_v1_booking_request_enums_proto_enumTypes[2].Descriptor()
}

func (BookingRequestAssociatedModel) Type() protoreflect.EnumType {
	return &file_moego_models_online_booking_v1_booking_request_enums_proto_enumTypes[2]
}

func (x BookingRequestAssociatedModel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BookingRequestAssociatedModel.Descriptor instead.
func (BookingRequestAssociatedModel) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_enums_proto_rawDescGZIP(), []int{2}
}

// Booking request sort field
type BookingRequestSortField int32

const (
	// unspecified
	BookingRequestSortField_BOOKING_REQUEST_SORT_FIELD_UNSPECIFIED BookingRequestSortField = 0
	// start date
	BookingRequestSortField_START_DATE BookingRequestSortField = 1
	// created at
	BookingRequestSortField_CREATED_AT BookingRequestSortField = 2
)

// Enum value maps for BookingRequestSortField.
var (
	BookingRequestSortField_name = map[int32]string{
		0: "BOOKING_REQUEST_SORT_FIELD_UNSPECIFIED",
		1: "START_DATE",
		2: "CREATED_AT",
	}
	BookingRequestSortField_value = map[string]int32{
		"BOOKING_REQUEST_SORT_FIELD_UNSPECIFIED": 0,
		"START_DATE":                             1,
		"CREATED_AT":                             2,
	}
)

func (x BookingRequestSortField) Enum() *BookingRequestSortField {
	p := new(BookingRequestSortField)
	*p = x
	return p
}

func (x BookingRequestSortField) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BookingRequestSortField) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_online_booking_v1_booking_request_enums_proto_enumTypes[3].Descriptor()
}

func (BookingRequestSortField) Type() protoreflect.EnumType {
	return &file_moego_models_online_booking_v1_booking_request_enums_proto_enumTypes[3]
}

func (x BookingRequestSortField) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BookingRequestSortField.Descriptor instead.
func (BookingRequestSortField) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_booking_request_enums_proto_rawDescGZIP(), []int{3}
}

var File_moego_models_online_booking_v1_booking_request_enums_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_booking_request_enums_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2a, 0x9e, 0x01, 0x0a,
	0x14, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x22, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a,
	0x09, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09,
	0x57, 0x41, 0x49, 0x54, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x53,
	0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45,
	0x43, 0x4c, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45,
	0x54, 0x45, 0x44, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x0e, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x06, 0x1a, 0x02, 0x08, 0x01, 0x2a, 0x7c, 0x0a,
	0x1c, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x2f, 0x0a,
	0x2b, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17,
	0x0a, 0x13, 0x52, 0x45, 0x53, 0x45, 0x52, 0x56, 0x45, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x47,
	0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x45, 0x54, 0x5f, 0x50,
	0x41, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x02, 0x2a, 0x98, 0x01, 0x0a, 0x1d,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x73,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x30, 0x0a,
	0x2c, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x41, 0x53, 0x53, 0x4f, 0x43, 0x49, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x4d, 0x4f, 0x44, 0x45,
	0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0b, 0x0a, 0x07, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06,
	0x41, 0x44, 0x44, 0x5f, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x45, 0x45, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x45, 0x44, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x41, 0x53,
	0x53, 0x49, 0x47, 0x4e, 0x10, 0x05, 0x2a, 0x65, 0x0a, 0x17, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x12, 0x2a, 0x0a, 0x26, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x4f, 0x52, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a,
	0x0a, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x0e, 0x0a,
	0x0a, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x02, 0x42, 0x8f, 0x01,
	0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_online_booking_v1_booking_request_enums_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_booking_request_enums_proto_rawDescData = file_moego_models_online_booking_v1_booking_request_enums_proto_rawDesc
)

func file_moego_models_online_booking_v1_booking_request_enums_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_booking_request_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_booking_request_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_booking_request_enums_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_booking_request_enums_proto_rawDescData
}

var file_moego_models_online_booking_v1_booking_request_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_moego_models_online_booking_v1_booking_request_enums_proto_goTypes = []interface{}{
	(BookingRequestStatus)(0),          // 0: moego.models.online_booking.v1.BookingRequestStatus
	(BookingRequestSourcePlatform)(0),  // 1: moego.models.online_booking.v1.BookingRequestSourcePlatform
	(BookingRequestAssociatedModel)(0), // 2: moego.models.online_booking.v1.BookingRequestAssociatedModel
	(BookingRequestSortField)(0),       // 3: moego.models.online_booking.v1.BookingRequestSortField
}
var file_moego_models_online_booking_v1_booking_request_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_booking_request_enums_proto_init() }
func file_moego_models_online_booking_v1_booking_request_enums_proto_init() {
	if File_moego_models_online_booking_v1_booking_request_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_booking_request_enums_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_booking_request_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_booking_request_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_online_booking_v1_booking_request_enums_proto_enumTypes,
	}.Build()
	File_moego_models_online_booking_v1_booking_request_enums_proto = out.File
	file_moego_models_online_booking_v1_booking_request_enums_proto_rawDesc = nil
	file_moego_models_online_booking_v1_booking_request_enums_proto_goTypes = nil
	file_moego_models_online_booking_v1_booking_request_enums_proto_depIdxs = nil
}
