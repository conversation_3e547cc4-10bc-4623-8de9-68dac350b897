// @since 2022-06-30 19:14:21
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/business_ob_config_models.proto

package onlinebookingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// BusinessOBConfigModel
type BusinessOBConfigModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// enable ob
	IsEnable bool `protobuf:"varint,2,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	// ob url name
	BookOnlineName string `protobuf:"bytes,3,opt,name=book_online_name,json=bookOnlineName,proto3" json:"book_online_name,omitempty"`
	// ob version
	UseVersion int32 `protobuf:"varint,4,opt,name=use_version,json=useVersion,proto3" json:"use_version,omitempty"`
	// create time
	CreateTime int64 `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime int64 `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// available time type
	AvailableTimeType AvailabilityType `protobuf:"varint,7,opt,name=available_time_type,json=availableTimeType,proto3,enum=moego.models.online_booking.v1.AvailabilityType" json:"available_time_type,omitempty"`
	// by slots timeslot format, 1-exact times, 2-arrival window
	BySlotTimeslotFormat TimeSlotFormat `protobuf:"varint,8,opt,name=by_slot_timeslot_format,json=bySlotTimeslotFormat,proto3,enum=moego.models.online_booking.v1.TimeSlotFormat" json:"by_slot_timeslot_format,omitempty"`
	// by slots timeslot interval mins
	BySlotTimeslotMins int32 `protobuf:"varint,9,opt,name=by_slot_timeslot_mins,json=bySlotTimeslotMins,proto3" json:"by_slot_timeslot_mins,omitempty"`
	// by working hours timeslot format type, 1-exact times, 2-arrival windows, 3-date only
	TimeslotFormat TimeSlotFormat `protobuf:"varint,10,opt,name=timeslot_format,json=timeslotFormat,proto3,enum=moego.models.online_booking.v1.TimeSlotFormat" json:"timeslot_format,omitempty"`
	// by working hours timeslot interval mins
	TimeslotMins int32 `protobuf:"varint,11,opt,name=timeslot_mins,json=timeslotMins,proto3" json:"timeslot_mins,omitempty"`
	// arrival window before mins
	ArrivalWindowBeforeMin int32 `protobuf:"varint,12,opt,name=arrival_window_before_min,json=arrivalWindowBeforeMin,proto3" json:"arrival_window_before_min,omitempty"`
	// arrival window after mins
	ArrivalWindowAfterMin int32 `protobuf:"varint,13,opt,name=arrival_window_after_min,json=arrivalWindowAfterMin,proto3" json:"arrival_window_after_min,omitempty"`
	// soonest available
	SoonestAvailable int32 `protobuf:"varint,14,opt,name=soonest_available,json=soonestAvailable,proto3" json:"soonest_available,omitempty"`
	// farthest available
	FarthestAvailable int32 `protobuf:"varint,15,opt,name=farthest_available,json=farthestAvailable,proto3" json:"farthest_available,omitempty"`
	// by slot soonest available
	BySlotSoonestAvailable int32 `protobuf:"varint,16,opt,name=by_slot_soonest_available,json=bySlotSoonestAvailable,proto3" json:"by_slot_soonest_available,omitempty"`
	// by slot farthest available
	BySlotFarthestAvailable int32 `protobuf:"varint,17,opt,name=by_slot_farthest_available,json=bySlotFarthestAvailable,proto3" json:"by_slot_farthest_available,omitempty"`
	// is need address
	IsNeedAddress bool `protobuf:"varint,18,opt,name=is_need_address,json=isNeedAddress,proto3" json:"is_need_address,omitempty"`
	// accept client type
	AcceptClientType AcceptClientType `protobuf:"varint,19,opt,name=accept_client_type,json=acceptClientType,proto3,enum=moego.models.online_booking.v1.AcceptClientType" json:"accept_client_type,omitempty"`
	// allowed simplify submit
	IsAllowedSimplifySubmit bool `protobuf:"varint,20,opt,name=is_allowed_simplify_submit,json=isAllowedSimplifySubmit,proto3" json:"is_allowed_simplify_submit,omitempty"`
	// payment type
	PaymentType PaymentType `protobuf:"varint,21,opt,name=payment_type,json=paymentType,proto3,enum=moego.models.online_booking.v1.PaymentType" json:"payment_type,omitempty"`
	// required cof policy
	CofPolicy string `protobuf:"bytes,22,opt,name=cof_policy,json=cofPolicy,proto3" json:"cof_policy,omitempty"`
	// prepay type
	PrepayType PrepayType `protobuf:"varint,23,opt,name=prepay_type,json=prepayType,proto3,enum=moego.models.online_booking.v1.PrepayType" json:"prepay_type,omitempty"`
	// prepay tip enable
	IsPrepayTipEnable bool `protobuf:"varint,24,opt,name=is_prepay_tip_enable,json=isPrepayTipEnable,proto3" json:"is_prepay_tip_enable,omitempty"`
	// prepay deposit type
	PrepayDepositType PrepayDepositType `protobuf:"varint,25,opt,name=prepay_deposit_type,json=prepayDepositType,proto3,enum=moego.models.online_booking.v1.PrepayDepositType" json:"prepay_deposit_type,omitempty"`
	// deposit amount
	DepositAmount float64 `protobuf:"fixed64,26,opt,name=deposit_amount,json=depositAmount,proto3" json:"deposit_amount,omitempty"`
	// deposit percentage
	DepositPercentage float64 `protobuf:"fixed64,27,opt,name=deposit_percentage,json=depositPercentage,proto3" json:"deposit_percentage,omitempty"`
	// prepay policy
	PrepayPolicy string `protobuf:"bytes,28,opt,name=prepay_policy,json=prepayPolicy,proto3" json:"prepay_policy,omitempty"`
	// pre-auth policy
	PreAuthPolicy string `protobuf:"bytes,29,opt,name=pre_auth_policy,json=preAuthPolicy,proto3" json:"pre_auth_policy,omitempty"`
	// pre-auth tip enable
	IsPreAuthTipEnable bool `protobuf:"varint,30,opt,name=is_pre_auth_tip_enable,json=isPreAuthTipEnable,proto3" json:"is_pre_auth_tip_enable,omitempty"`
	// display staff selection page
	IsDisplayStaffSelectionPage bool `protobuf:"varint,40,opt,name=is_display_staff_selection_page,json=isDisplayStaffSelectionPage,proto3" json:"is_display_staff_selection_page,omitempty"`
	// booking range start offset
	BookingRangeStartOffset int32 `protobuf:"varint,41,opt,name=booking_range_start_offset,json=bookingRangeStartOffset,proto3" json:"booking_range_start_offset,omitempty"`
	// booking range end type, 1-offset, 2-date
	BookingRangeEndType BookingRangeEndType `protobuf:"varint,42,opt,name=booking_range_end_type,json=bookingRangeEndType,proto3,enum=moego.models.online_booking.v1.BookingRangeEndType" json:"booking_range_end_type,omitempty"`
	// booking range end offset
	BookingRangeEndOffset int32 `protobuf:"varint,43,opt,name=booking_range_end_offset,json=bookingRangeEndOffset,proto3" json:"booking_range_end_offset,omitempty"`
	// booking range end date
	BookingRangeEndDate string `protobuf:"bytes,44,opt,name=booking_range_end_date,json=bookingRangeEndDate,proto3" json:"booking_range_end_date,omitempty"`
	// is check existing client address
	IsCheckExistingClient bool `protobuf:"varint,45,opt,name=is_check_existing_client,json=isCheckExistingClient,proto3" json:"is_check_existing_client,omitempty"`
}

func (x *BusinessOBConfigModel) Reset() {
	*x = BusinessOBConfigModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessOBConfigModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessOBConfigModel) ProtoMessage() {}

func (x *BusinessOBConfigModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessOBConfigModel.ProtoReflect.Descriptor instead.
func (*BusinessOBConfigModel) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessOBConfigModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessOBConfigModel) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

func (x *BusinessOBConfigModel) GetBookOnlineName() string {
	if x != nil {
		return x.BookOnlineName
	}
	return ""
}

func (x *BusinessOBConfigModel) GetUseVersion() int32 {
	if x != nil {
		return x.UseVersion
	}
	return 0
}

func (x *BusinessOBConfigModel) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *BusinessOBConfigModel) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *BusinessOBConfigModel) GetAvailableTimeType() AvailabilityType {
	if x != nil {
		return x.AvailableTimeType
	}
	return AvailabilityType_AVAILABILITY_TYPE_BY_WORKING_HOURS
}

func (x *BusinessOBConfigModel) GetBySlotTimeslotFormat() TimeSlotFormat {
	if x != nil {
		return x.BySlotTimeslotFormat
	}
	return TimeSlotFormat_TIME_SLOT_FORMAT_UNSPECIFIED
}

func (x *BusinessOBConfigModel) GetBySlotTimeslotMins() int32 {
	if x != nil {
		return x.BySlotTimeslotMins
	}
	return 0
}

func (x *BusinessOBConfigModel) GetTimeslotFormat() TimeSlotFormat {
	if x != nil {
		return x.TimeslotFormat
	}
	return TimeSlotFormat_TIME_SLOT_FORMAT_UNSPECIFIED
}

func (x *BusinessOBConfigModel) GetTimeslotMins() int32 {
	if x != nil {
		return x.TimeslotMins
	}
	return 0
}

func (x *BusinessOBConfigModel) GetArrivalWindowBeforeMin() int32 {
	if x != nil {
		return x.ArrivalWindowBeforeMin
	}
	return 0
}

func (x *BusinessOBConfigModel) GetArrivalWindowAfterMin() int32 {
	if x != nil {
		return x.ArrivalWindowAfterMin
	}
	return 0
}

func (x *BusinessOBConfigModel) GetSoonestAvailable() int32 {
	if x != nil {
		return x.SoonestAvailable
	}
	return 0
}

func (x *BusinessOBConfigModel) GetFarthestAvailable() int32 {
	if x != nil {
		return x.FarthestAvailable
	}
	return 0
}

func (x *BusinessOBConfigModel) GetBySlotSoonestAvailable() int32 {
	if x != nil {
		return x.BySlotSoonestAvailable
	}
	return 0
}

func (x *BusinessOBConfigModel) GetBySlotFarthestAvailable() int32 {
	if x != nil {
		return x.BySlotFarthestAvailable
	}
	return 0
}

func (x *BusinessOBConfigModel) GetIsNeedAddress() bool {
	if x != nil {
		return x.IsNeedAddress
	}
	return false
}

func (x *BusinessOBConfigModel) GetAcceptClientType() AcceptClientType {
	if x != nil {
		return x.AcceptClientType
	}
	return AcceptClientType_ACCEPT_CLIENT_TYPE_UNSPECIFIED
}

func (x *BusinessOBConfigModel) GetIsAllowedSimplifySubmit() bool {
	if x != nil {
		return x.IsAllowedSimplifySubmit
	}
	return false
}

func (x *BusinessOBConfigModel) GetPaymentType() PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return PaymentType_PAYMENT_TYPE_DISABLE
}

func (x *BusinessOBConfigModel) GetCofPolicy() string {
	if x != nil {
		return x.CofPolicy
	}
	return ""
}

func (x *BusinessOBConfigModel) GetPrepayType() PrepayType {
	if x != nil {
		return x.PrepayType
	}
	return PrepayType_PREPAY_TYPE_FULL_AMOUNT
}

func (x *BusinessOBConfigModel) GetIsPrepayTipEnable() bool {
	if x != nil {
		return x.IsPrepayTipEnable
	}
	return false
}

func (x *BusinessOBConfigModel) GetPrepayDepositType() PrepayDepositType {
	if x != nil {
		return x.PrepayDepositType
	}
	return PrepayDepositType_PREPAY_DEPOSIT_TYPE_FIXED_AMOUNT
}

func (x *BusinessOBConfigModel) GetDepositAmount() float64 {
	if x != nil {
		return x.DepositAmount
	}
	return 0
}

func (x *BusinessOBConfigModel) GetDepositPercentage() float64 {
	if x != nil {
		return x.DepositPercentage
	}
	return 0
}

func (x *BusinessOBConfigModel) GetPrepayPolicy() string {
	if x != nil {
		return x.PrepayPolicy
	}
	return ""
}

func (x *BusinessOBConfigModel) GetPreAuthPolicy() string {
	if x != nil {
		return x.PreAuthPolicy
	}
	return ""
}

func (x *BusinessOBConfigModel) GetIsPreAuthTipEnable() bool {
	if x != nil {
		return x.IsPreAuthTipEnable
	}
	return false
}

func (x *BusinessOBConfigModel) GetIsDisplayStaffSelectionPage() bool {
	if x != nil {
		return x.IsDisplayStaffSelectionPage
	}
	return false
}

func (x *BusinessOBConfigModel) GetBookingRangeStartOffset() int32 {
	if x != nil {
		return x.BookingRangeStartOffset
	}
	return 0
}

func (x *BusinessOBConfigModel) GetBookingRangeEndType() BookingRangeEndType {
	if x != nil {
		return x.BookingRangeEndType
	}
	return BookingRangeEndType_BOOKING_RANGE_END_TYPE_UNSPECIFIED
}

func (x *BusinessOBConfigModel) GetBookingRangeEndOffset() int32 {
	if x != nil {
		return x.BookingRangeEndOffset
	}
	return 0
}

func (x *BusinessOBConfigModel) GetBookingRangeEndDate() string {
	if x != nil {
		return x.BookingRangeEndDate
	}
	return ""
}

func (x *BusinessOBConfigModel) GetIsCheckExistingClient() bool {
	if x != nil {
		return x.IsCheckExistingClient
	}
	return false
}

// BusinessOBConfigSimpleView
type BusinessOBConfigSimpleView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// enable ob
	IsEnable bool `protobuf:"varint,2,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	// ob url name
	BookOnlineName string `protobuf:"bytes,3,opt,name=book_online_name,json=bookOnlineName,proto3" json:"book_online_name,omitempty"`
}

func (x *BusinessOBConfigSimpleView) Reset() {
	*x = BusinessOBConfigSimpleView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessOBConfigSimpleView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessOBConfigSimpleView) ProtoMessage() {}

func (x *BusinessOBConfigSimpleView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessOBConfigSimpleView.ProtoReflect.Descriptor instead.
func (*BusinessOBConfigSimpleView) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessOBConfigSimpleView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessOBConfigSimpleView) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

func (x *BusinessOBConfigSimpleView) GetBookOnlineName() string {
	if x != nil {
		return x.BookOnlineName
	}
	return ""
}

// BusinessOBConfigClientPortalView
type BusinessOBConfigClientPortalView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// enable ob
	IsEnable bool `protobuf:"varint,2,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	// ob url name
	BookOnlineName string `protobuf:"bytes,3,opt,name=book_online_name,json=bookOnlineName,proto3" json:"book_online_name,omitempty"`
	// ob version
	UseVersion int32 `protobuf:"varint,4,opt,name=use_version,json=useVersion,proto3" json:"use_version,omitempty"`
}

func (x *BusinessOBConfigClientPortalView) Reset() {
	*x = BusinessOBConfigClientPortalView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessOBConfigClientPortalView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessOBConfigClientPortalView) ProtoMessage() {}

func (x *BusinessOBConfigClientPortalView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessOBConfigClientPortalView.ProtoReflect.Descriptor instead.
func (*BusinessOBConfigClientPortalView) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDescGZIP(), []int{2}
}

func (x *BusinessOBConfigClientPortalView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessOBConfigClientPortalView) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

func (x *BusinessOBConfigClientPortalView) GetBookOnlineName() string {
	if x != nil {
		return x.BookOnlineName
	}
	return ""
}

func (x *BusinessOBConfigClientPortalView) GetUseVersion() int32 {
	if x != nil {
		return x.UseVersion
	}
	return 0
}

// business ob config in c app appt list view
type BusinessOBConfigModelClientListView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// available time type
	AvailableTimeType AvailabilityType `protobuf:"varint,2,opt,name=available_time_type,json=availableTimeType,proto3,enum=moego.models.online_booking.v1.AvailabilityType" json:"available_time_type,omitempty"`
	// by working hours timeslot format type
	TimeslotFormat TimeSlotFormat `protobuf:"varint,3,opt,name=timeslot_format,json=timeslotFormat,proto3,enum=moego.models.online_booking.v1.TimeSlotFormat" json:"timeslot_format,omitempty"`
	// by slots timeslot format
	BySlotTimeslotFormat TimeSlotFormat `protobuf:"varint,4,opt,name=by_slot_timeslot_format,json=bySlotTimeslotFormat,proto3,enum=moego.models.online_booking.v1.TimeSlotFormat" json:"by_slot_timeslot_format,omitempty"`
	// arrival window before mins
	ArrivalWindowBeforeMin int32 `protobuf:"varint,5,opt,name=arrival_window_before_min,json=arrivalWindowBeforeMin,proto3" json:"arrival_window_before_min,omitempty"`
	// arrival window after mins
	ArrivalWindowAfterMin int32 `protobuf:"varint,6,opt,name=arrival_window_after_min,json=arrivalWindowAfterMin,proto3" json:"arrival_window_after_min,omitempty"`
}

func (x *BusinessOBConfigModelClientListView) Reset() {
	*x = BusinessOBConfigModelClientListView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessOBConfigModelClientListView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessOBConfigModelClientListView) ProtoMessage() {}

func (x *BusinessOBConfigModelClientListView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessOBConfigModelClientListView.ProtoReflect.Descriptor instead.
func (*BusinessOBConfigModelClientListView) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDescGZIP(), []int{3}
}

func (x *BusinessOBConfigModelClientListView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessOBConfigModelClientListView) GetAvailableTimeType() AvailabilityType {
	if x != nil {
		return x.AvailableTimeType
	}
	return AvailabilityType_AVAILABILITY_TYPE_BY_WORKING_HOURS
}

func (x *BusinessOBConfigModelClientListView) GetTimeslotFormat() TimeSlotFormat {
	if x != nil {
		return x.TimeslotFormat
	}
	return TimeSlotFormat_TIME_SLOT_FORMAT_UNSPECIFIED
}

func (x *BusinessOBConfigModelClientListView) GetBySlotTimeslotFormat() TimeSlotFormat {
	if x != nil {
		return x.BySlotTimeslotFormat
	}
	return TimeSlotFormat_TIME_SLOT_FORMAT_UNSPECIFIED
}

func (x *BusinessOBConfigModelClientListView) GetArrivalWindowBeforeMin() int32 {
	if x != nil {
		return x.ArrivalWindowBeforeMin
	}
	return 0
}

func (x *BusinessOBConfigModelClientListView) GetArrivalWindowAfterMin() int32 {
	if x != nil {
		return x.ArrivalWindowAfterMin
	}
	return 0
}

// business ob config in c app appt detail view
type BusinessOBConfigModelClientView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// available time type
	AvailableTimeType AvailabilityType `protobuf:"varint,2,opt,name=available_time_type,json=availableTimeType,proto3,enum=moego.models.online_booking.v1.AvailabilityType" json:"available_time_type,omitempty"`
	// by working hours timeslot format type
	TimeslotFormat TimeSlotFormat `protobuf:"varint,3,opt,name=timeslot_format,json=timeslotFormat,proto3,enum=moego.models.online_booking.v1.TimeSlotFormat" json:"timeslot_format,omitempty"`
	// by slots timeslot format
	BySlotTimeslotFormat TimeSlotFormat `protobuf:"varint,4,opt,name=by_slot_timeslot_format,json=bySlotTimeslotFormat,proto3,enum=moego.models.online_booking.v1.TimeSlotFormat" json:"by_slot_timeslot_format,omitempty"`
	// arrival window before mins
	ArrivalWindowBeforeMin int32 `protobuf:"varint,5,opt,name=arrival_window_before_min,json=arrivalWindowBeforeMin,proto3" json:"arrival_window_before_min,omitempty"`
	// arrival window after mins
	ArrivalWindowAfterMin int32 `protobuf:"varint,6,opt,name=arrival_window_after_min,json=arrivalWindowAfterMin,proto3" json:"arrival_window_after_min,omitempty"`
	// enable ob
	IsEnable bool `protobuf:"varint,7,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	// ob url name
	BookOnlineName string `protobuf:"bytes,8,opt,name=book_online_name,json=bookOnlineName,proto3" json:"book_online_name,omitempty"`
	// ob version
	UseVersion int32 `protobuf:"varint,9,opt,name=use_version,json=useVersion,proto3" json:"use_version,omitempty"`
	// accept client type
	AcceptClientType AcceptClientType `protobuf:"varint,10,opt,name=accept_client_type,json=acceptClientType,proto3,enum=moego.models.online_booking.v1.AcceptClientType" json:"accept_client_type,omitempty"`
}

func (x *BusinessOBConfigModelClientView) Reset() {
	*x = BusinessOBConfigModelClientView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessOBConfigModelClientView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessOBConfigModelClientView) ProtoMessage() {}

func (x *BusinessOBConfigModelClientView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessOBConfigModelClientView.ProtoReflect.Descriptor instead.
func (*BusinessOBConfigModelClientView) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDescGZIP(), []int{4}
}

func (x *BusinessOBConfigModelClientView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessOBConfigModelClientView) GetAvailableTimeType() AvailabilityType {
	if x != nil {
		return x.AvailableTimeType
	}
	return AvailabilityType_AVAILABILITY_TYPE_BY_WORKING_HOURS
}

func (x *BusinessOBConfigModelClientView) GetTimeslotFormat() TimeSlotFormat {
	if x != nil {
		return x.TimeslotFormat
	}
	return TimeSlotFormat_TIME_SLOT_FORMAT_UNSPECIFIED
}

func (x *BusinessOBConfigModelClientView) GetBySlotTimeslotFormat() TimeSlotFormat {
	if x != nil {
		return x.BySlotTimeslotFormat
	}
	return TimeSlotFormat_TIME_SLOT_FORMAT_UNSPECIFIED
}

func (x *BusinessOBConfigModelClientView) GetArrivalWindowBeforeMin() int32 {
	if x != nil {
		return x.ArrivalWindowBeforeMin
	}
	return 0
}

func (x *BusinessOBConfigModelClientView) GetArrivalWindowAfterMin() int32 {
	if x != nil {
		return x.ArrivalWindowAfterMin
	}
	return 0
}

func (x *BusinessOBConfigModelClientView) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

func (x *BusinessOBConfigModelClientView) GetBookOnlineName() string {
	if x != nil {
		return x.BookOnlineName
	}
	return ""
}

func (x *BusinessOBConfigModelClientView) GetUseVersion() int32 {
	if x != nil {
		return x.UseVersion
	}
	return 0
}

func (x *BusinessOBConfigModelClientView) GetAcceptClientType() AcceptClientType {
	if x != nil {
		return x.AcceptClientType
	}
	return AcceptClientType_ACCEPT_CLIENT_TYPE_UNSPECIFIED
}

// business ob config in c app booking view
type BusinessOBConfigModelBookingView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// enable ob
	IsEnable bool `protobuf:"varint,2,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	// ob url name
	BookOnlineName string `protobuf:"bytes,3,opt,name=book_online_name,json=bookOnlineName,proto3" json:"book_online_name,omitempty"`
	// ob version
	UseVersion int32 `protobuf:"varint,4,opt,name=use_version,json=useVersion,proto3" json:"use_version,omitempty"`
	// available time type
	AvailableTimeType AvailabilityType `protobuf:"varint,5,opt,name=available_time_type,json=availableTimeType,proto3,enum=moego.models.online_booking.v1.AvailabilityType" json:"available_time_type,omitempty"`
	// by working hours timeslot format type
	TimeslotFormat TimeSlotFormat `protobuf:"varint,6,opt,name=timeslot_format,json=timeslotFormat,proto3,enum=moego.models.online_booking.v1.TimeSlotFormat" json:"timeslot_format,omitempty"`
	// by slots timeslot format
	BySlotTimeslotFormat TimeSlotFormat `protobuf:"varint,7,opt,name=by_slot_timeslot_format,json=bySlotTimeslotFormat,proto3,enum=moego.models.online_booking.v1.TimeSlotFormat" json:"by_slot_timeslot_format,omitempty"`
	// arrival window before mins
	ArrivalWindowBeforeMin int32 `protobuf:"varint,8,opt,name=arrival_window_before_min,json=arrivalWindowBeforeMin,proto3" json:"arrival_window_before_min,omitempty"`
	// arrival window after mins
	ArrivalWindowAfterMin int32 `protobuf:"varint,9,opt,name=arrival_window_after_min,json=arrivalWindowAfterMin,proto3" json:"arrival_window_after_min,omitempty"`
	// soonest available
	SoonestAvailable int32 `protobuf:"varint,10,opt,name=soonest_available,json=soonestAvailable,proto3" json:"soonest_available,omitempty"`
	// farthest available
	FarthestAvailable int32 `protobuf:"varint,11,opt,name=farthest_available,json=farthestAvailable,proto3" json:"farthest_available,omitempty"`
	// by slot soonest available
	BySlotSoonestAvailable int32 `protobuf:"varint,12,opt,name=by_slot_soonest_available,json=bySlotSoonestAvailable,proto3" json:"by_slot_soonest_available,omitempty"`
	// by slot farthest available
	BySlotFarthestAvailable int32 `protobuf:"varint,13,opt,name=by_slot_farthest_available,json=bySlotFarthestAvailable,proto3" json:"by_slot_farthest_available,omitempty"`
	// display staff selection page
	IsDisplayStaffSelectionPage bool `protobuf:"varint,14,opt,name=is_display_staff_selection_page,json=isDisplayStaffSelectionPage,proto3" json:"is_display_staff_selection_page,omitempty"`
	// booking range start offset
	BookingRangeStartOffset int32 `protobuf:"varint,15,opt,name=booking_range_start_offset,json=bookingRangeStartOffset,proto3" json:"booking_range_start_offset,omitempty"`
	// booking range end type, 1-offset, 2-date
	BookingRangeEndType BookingRangeEndType `protobuf:"varint,16,opt,name=booking_range_end_type,json=bookingRangeEndType,proto3,enum=moego.models.online_booking.v1.BookingRangeEndType" json:"booking_range_end_type,omitempty"`
	// booking range end offset
	BookingRangeEndOffset int32 `protobuf:"varint,17,opt,name=booking_range_end_offset,json=bookingRangeEndOffset,proto3" json:"booking_range_end_offset,omitempty"`
	// booking range end date
	BookingRangeEndDate string `protobuf:"bytes,18,opt,name=booking_range_end_date,json=bookingRangeEndDate,proto3" json:"booking_range_end_date,omitempty"`
	// payment type
	PaymentType PaymentType `protobuf:"varint,19,opt,name=payment_type,json=paymentType,proto3,enum=moego.models.online_booking.v1.PaymentType" json:"payment_type,omitempty"`
	// required cof policy
	CofPolicy string `protobuf:"bytes,20,opt,name=cof_policy,json=cofPolicy,proto3" json:"cof_policy,omitempty"`
	// prepay type
	PrepayType PrepayType `protobuf:"varint,21,opt,name=prepay_type,json=prepayType,proto3,enum=moego.models.online_booking.v1.PrepayType" json:"prepay_type,omitempty"`
	// prepay tip enable
	IsPrepayTipEnable bool `protobuf:"varint,22,opt,name=is_prepay_tip_enable,json=isPrepayTipEnable,proto3" json:"is_prepay_tip_enable,omitempty"`
	// prepay deposit type
	PrepayDepositType PrepayDepositType `protobuf:"varint,23,opt,name=prepay_deposit_type,json=prepayDepositType,proto3,enum=moego.models.online_booking.v1.PrepayDepositType" json:"prepay_deposit_type,omitempty"`
	// deposit amount
	DepositAmount float64 `protobuf:"fixed64,24,opt,name=deposit_amount,json=depositAmount,proto3" json:"deposit_amount,omitempty"`
	// deposit percentage
	DepositPercentage float64 `protobuf:"fixed64,25,opt,name=deposit_percentage,json=depositPercentage,proto3" json:"deposit_percentage,omitempty"`
	// prepay policy
	PrepayPolicy string `protobuf:"bytes,26,opt,name=prepay_policy,json=prepayPolicy,proto3" json:"prepay_policy,omitempty"`
	// pre-auth policy
	PreAuthPolicy string `protobuf:"bytes,27,opt,name=pre_auth_policy,json=preAuthPolicy,proto3" json:"pre_auth_policy,omitempty"`
	// pre-auth tip enable
	IsPreAuthTipEnable bool `protobuf:"varint,28,opt,name=is_pre_auth_tip_enable,json=isPreAuthTipEnable,proto3" json:"is_pre_auth_tip_enable,omitempty"`
	// allowed simplify submit
	IsAllowedSimplifySubmit bool `protobuf:"varint,29,opt,name=is_allowed_simplify_submit,json=isAllowedSimplifySubmit,proto3" json:"is_allowed_simplify_submit,omitempty"`
	// is need address
	IsNeedAddress bool `protobuf:"varint,30,opt,name=is_need_address,json=isNeedAddress,proto3" json:"is_need_address,omitempty"`
	// is check existing client address
	IsCheckExistingClient bool `protobuf:"varint,45,opt,name=is_check_existing_client,json=isCheckExistingClient,proto3" json:"is_check_existing_client,omitempty"`
}

func (x *BusinessOBConfigModelBookingView) Reset() {
	*x = BusinessOBConfigModelBookingView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessOBConfigModelBookingView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessOBConfigModelBookingView) ProtoMessage() {}

func (x *BusinessOBConfigModelBookingView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessOBConfigModelBookingView.ProtoReflect.Descriptor instead.
func (*BusinessOBConfigModelBookingView) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDescGZIP(), []int{5}
}

func (x *BusinessOBConfigModelBookingView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessOBConfigModelBookingView) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

func (x *BusinessOBConfigModelBookingView) GetBookOnlineName() string {
	if x != nil {
		return x.BookOnlineName
	}
	return ""
}

func (x *BusinessOBConfigModelBookingView) GetUseVersion() int32 {
	if x != nil {
		return x.UseVersion
	}
	return 0
}

func (x *BusinessOBConfigModelBookingView) GetAvailableTimeType() AvailabilityType {
	if x != nil {
		return x.AvailableTimeType
	}
	return AvailabilityType_AVAILABILITY_TYPE_BY_WORKING_HOURS
}

func (x *BusinessOBConfigModelBookingView) GetTimeslotFormat() TimeSlotFormat {
	if x != nil {
		return x.TimeslotFormat
	}
	return TimeSlotFormat_TIME_SLOT_FORMAT_UNSPECIFIED
}

func (x *BusinessOBConfigModelBookingView) GetBySlotTimeslotFormat() TimeSlotFormat {
	if x != nil {
		return x.BySlotTimeslotFormat
	}
	return TimeSlotFormat_TIME_SLOT_FORMAT_UNSPECIFIED
}

func (x *BusinessOBConfigModelBookingView) GetArrivalWindowBeforeMin() int32 {
	if x != nil {
		return x.ArrivalWindowBeforeMin
	}
	return 0
}

func (x *BusinessOBConfigModelBookingView) GetArrivalWindowAfterMin() int32 {
	if x != nil {
		return x.ArrivalWindowAfterMin
	}
	return 0
}

func (x *BusinessOBConfigModelBookingView) GetSoonestAvailable() int32 {
	if x != nil {
		return x.SoonestAvailable
	}
	return 0
}

func (x *BusinessOBConfigModelBookingView) GetFarthestAvailable() int32 {
	if x != nil {
		return x.FarthestAvailable
	}
	return 0
}

func (x *BusinessOBConfigModelBookingView) GetBySlotSoonestAvailable() int32 {
	if x != nil {
		return x.BySlotSoonestAvailable
	}
	return 0
}

func (x *BusinessOBConfigModelBookingView) GetBySlotFarthestAvailable() int32 {
	if x != nil {
		return x.BySlotFarthestAvailable
	}
	return 0
}

func (x *BusinessOBConfigModelBookingView) GetIsDisplayStaffSelectionPage() bool {
	if x != nil {
		return x.IsDisplayStaffSelectionPage
	}
	return false
}

func (x *BusinessOBConfigModelBookingView) GetBookingRangeStartOffset() int32 {
	if x != nil {
		return x.BookingRangeStartOffset
	}
	return 0
}

func (x *BusinessOBConfigModelBookingView) GetBookingRangeEndType() BookingRangeEndType {
	if x != nil {
		return x.BookingRangeEndType
	}
	return BookingRangeEndType_BOOKING_RANGE_END_TYPE_UNSPECIFIED
}

func (x *BusinessOBConfigModelBookingView) GetBookingRangeEndOffset() int32 {
	if x != nil {
		return x.BookingRangeEndOffset
	}
	return 0
}

func (x *BusinessOBConfigModelBookingView) GetBookingRangeEndDate() string {
	if x != nil {
		return x.BookingRangeEndDate
	}
	return ""
}

func (x *BusinessOBConfigModelBookingView) GetPaymentType() PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return PaymentType_PAYMENT_TYPE_DISABLE
}

func (x *BusinessOBConfigModelBookingView) GetCofPolicy() string {
	if x != nil {
		return x.CofPolicy
	}
	return ""
}

func (x *BusinessOBConfigModelBookingView) GetPrepayType() PrepayType {
	if x != nil {
		return x.PrepayType
	}
	return PrepayType_PREPAY_TYPE_FULL_AMOUNT
}

func (x *BusinessOBConfigModelBookingView) GetIsPrepayTipEnable() bool {
	if x != nil {
		return x.IsPrepayTipEnable
	}
	return false
}

func (x *BusinessOBConfigModelBookingView) GetPrepayDepositType() PrepayDepositType {
	if x != nil {
		return x.PrepayDepositType
	}
	return PrepayDepositType_PREPAY_DEPOSIT_TYPE_FIXED_AMOUNT
}

func (x *BusinessOBConfigModelBookingView) GetDepositAmount() float64 {
	if x != nil {
		return x.DepositAmount
	}
	return 0
}

func (x *BusinessOBConfigModelBookingView) GetDepositPercentage() float64 {
	if x != nil {
		return x.DepositPercentage
	}
	return 0
}

func (x *BusinessOBConfigModelBookingView) GetPrepayPolicy() string {
	if x != nil {
		return x.PrepayPolicy
	}
	return ""
}

func (x *BusinessOBConfigModelBookingView) GetPreAuthPolicy() string {
	if x != nil {
		return x.PreAuthPolicy
	}
	return ""
}

func (x *BusinessOBConfigModelBookingView) GetIsPreAuthTipEnable() bool {
	if x != nil {
		return x.IsPreAuthTipEnable
	}
	return false
}

func (x *BusinessOBConfigModelBookingView) GetIsAllowedSimplifySubmit() bool {
	if x != nil {
		return x.IsAllowedSimplifySubmit
	}
	return false
}

func (x *BusinessOBConfigModelBookingView) GetIsNeedAddress() bool {
	if x != nil {
		return x.IsNeedAddress
	}
	return false
}

func (x *BusinessOBConfigModelBookingView) GetIsCheckExistingClient() bool {
	if x != nil {
		return x.IsCheckExistingClient
	}
	return false
}

var File_moego_models_online_booking_v1_business_ob_config_models_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDesc = []byte{
	0x0a, 0x3e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x62, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x6f, 0x62, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa4, 0x10, 0x0a, 0x15, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x4f, 0x42, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x28,
	0x0a, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x75,
	0x73, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x60, 0x0a, 0x13, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x65, 0x0a,
	0x17, 0x62, 0x79, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f,
	0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x14,
	0x62, 0x79, 0x53, 0x6c, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x12, 0x31, 0x0a, 0x15, 0x62, 0x79, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x6d, 0x69, 0x6e, 0x73, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x12, 0x62, 0x79, 0x53, 0x6c, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x6c, 0x6f, 0x74, 0x4d, 0x69, 0x6e, 0x73, 0x12, 0x57, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x6c, 0x6f, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x52, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x12, 0x23, 0x0a, 0x0d, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x6d, 0x69, 0x6e,
	0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f,
	0x74, 0x4d, 0x69, 0x6e, 0x73, 0x12, 0x39, 0x0a, 0x19, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c,
	0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x6d,
	0x69, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x4d, 0x69, 0x6e,
	0x12, 0x37, 0x0a, 0x18, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x77, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x15, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f,
	0x77, 0x41, 0x66, 0x74, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x6f, 0x6f,
	0x6e, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x73, 0x6f, 0x6f, 0x6e, 0x65, 0x73, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x66, 0x61, 0x72, 0x74, 0x68, 0x65,
	0x73, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x11, 0x66, 0x61, 0x72, 0x74, 0x68, 0x65, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x19, 0x62, 0x79, 0x5f, 0x73, 0x6c, 0x6f, 0x74,
	0x5f, 0x73, 0x6f, 0x6f, 0x6e, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x62, 0x79, 0x53, 0x6c, 0x6f, 0x74,
	0x53, 0x6f, 0x6f, 0x6e, 0x65, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x3b, 0x0a, 0x1a, 0x62, 0x79, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x66, 0x61, 0x72, 0x74,
	0x68, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x17, 0x62, 0x79, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x61, 0x72, 0x74,
	0x68, 0x65, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x26, 0x0a,
	0x0f, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x4e, 0x65, 0x65, 0x64, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x5e, 0x0a, 0x12, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x10, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x1a, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x75, 0x62,
	0x6d, 0x69, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x69, 0x73, 0x41, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x64, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x69, 0x66, 0x79, 0x53, 0x75, 0x62, 0x6d,
	0x69, 0x74, 0x12, 0x4e, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x66, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x66, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x12, 0x4b, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x70, 0x61, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f,
	0x0a, 0x14, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x5f, 0x74, 0x69, 0x70, 0x5f,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x73,
	0x50, 0x72, 0x65, 0x70, 0x61, 0x79, 0x54, 0x69, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x61, 0x0a, 0x13, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x65, 0x70, 0x61, 0x79, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x11, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x64, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x12, 0x64, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x50, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x65, 0x70,
	0x61, 0x79, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x26, 0x0a,
	0x0f, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x32, 0x0a, 0x16, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x5f,
	0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x69, 0x70, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x50, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68,
	0x54, 0x69, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x44, 0x0a, 0x1f, 0x69, 0x73, 0x5f,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x28, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x1b, 0x69, 0x73, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x67, 0x65, 0x12,
	0x3b, 0x0a, 0x1a, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x29, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x17, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x68, 0x0a, 0x16,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x6e, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x13, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x45,
	0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x18, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x6e, 0x64, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12,
	0x33, 0x0a, 0x16, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x6e, 0x64,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x18, 0x69, 0x73, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x18, 0x2d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x69, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45,
	0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x22, 0x84, 0x01,
	0x0a, 0x1a, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x42, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x6f,
	0x6f, 0x6b, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0xab, 0x01, 0x0a, 0x20, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x4f, 0x42, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50,
	0x6f, 0x72, 0x74, 0x61, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69,
	0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x5f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x62, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0xdc, 0x03, 0x0a, 0x23, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f,
	0x42, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x13, 0x61,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x57, 0x0a,
	0x0f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x6c, 0x6f, 0x74,
	0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74,
	0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x65, 0x0a, 0x17, 0x62, 0x79, 0x5f, 0x73, 0x6c, 0x6f,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x6c, 0x6f,
	0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x14, 0x62, 0x79, 0x53, 0x6c, 0x6f, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x39, 0x0a,
	0x19, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x5f,
	0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x16, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x42,
	0x65, 0x66, 0x6f, 0x72, 0x65, 0x4d, 0x69, 0x6e, 0x12, 0x37, 0x0a, 0x18, 0x61, 0x72, 0x72, 0x69,
	0x76, 0x61, 0x6c, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72,
	0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x61, 0x72, 0x72, 0x69,
	0x76, 0x61, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x41, 0x66, 0x74, 0x65, 0x72, 0x4d, 0x69,
	0x6e, 0x22, 0xa0, 0x05, 0x0a, 0x1f, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x42,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x13, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x57, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x52, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x12, 0x65, 0x0a, 0x17, 0x62, 0x79, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x52, 0x14, 0x62, 0x79, 0x53, 0x6c, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x6c,
	0x6f, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x39, 0x0a, 0x19, 0x61, 0x72, 0x72, 0x69,
	0x76, 0x61, 0x6c, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72,
	0x65, 0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x61, 0x72, 0x72,
	0x69, 0x76, 0x61, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65,
	0x4d, 0x69, 0x6e, 0x12, 0x37, 0x0a, 0x18, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x77,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x6d, 0x69, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x57, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x41, 0x66, 0x74, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x6f, 0x6f,
	0x6b, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x5e, 0x0a, 0x12, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x10, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x22, 0xb5, 0x0e, 0x0a, 0x20, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x4f, 0x42, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69,
	0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x5f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x62, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x60, 0x0a, 0x13, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x57, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74,
	0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x0e, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x65, 0x0a,
	0x17, 0x62, 0x79, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f,
	0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x14,
	0x62, 0x79, 0x53, 0x6c, 0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x6c, 0x6f, 0x74, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x12, 0x39, 0x0a, 0x19, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f,
	0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x6d, 0x69,
	0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c,
	0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x4d, 0x69, 0x6e, 0x12,
	0x37, 0x0a, 0x18, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f,
	0x77, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x15, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77,
	0x41, 0x66, 0x74, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x6f, 0x6f, 0x6e,
	0x65, 0x73, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x10, 0x73, 0x6f, 0x6f, 0x6e, 0x65, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x66, 0x61, 0x72, 0x74, 0x68, 0x65, 0x73,
	0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x11, 0x66, 0x61, 0x72, 0x74, 0x68, 0x65, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x19, 0x62, 0x79, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f,
	0x73, 0x6f, 0x6f, 0x6e, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x62, 0x79, 0x53, 0x6c, 0x6f, 0x74, 0x53,
	0x6f, 0x6f, 0x6e, 0x65, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x3b, 0x0a, 0x1a, 0x62, 0x79, 0x5f, 0x73, 0x6c, 0x6f, 0x74, 0x5f, 0x66, 0x61, 0x72, 0x74, 0x68,
	0x65, 0x73, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x17, 0x62, 0x79, 0x53, 0x6c, 0x6f, 0x74, 0x46, 0x61, 0x72, 0x74, 0x68,
	0x65, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x44, 0x0a, 0x1f,
	0x69, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x69, 0x73, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61,
	0x67, 0x65, 0x12, 0x3b, 0x0a, 0x1a, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x17, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12,
	0x68, 0x0a, 0x16, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x6e, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x13, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x45, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x18, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x6e, 0x64, 0x4f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x12, 0x33, 0x0a, 0x16, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x4e, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x66, 0x5f, 0x70,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x66,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x4b, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65,
	0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79,
	0x5f, 0x74, 0x69, 0x70, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x11, 0x69, 0x73, 0x50, 0x72, 0x65, 0x70, 0x61, 0x79, 0x54, 0x69, 0x70, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x61, 0x0a, 0x13, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x5f, 0x64,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x70, 0x61, 0x79, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0d, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d,
	0x0a, 0x12, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x61, 0x67, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x64, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x1a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x70,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x65,
	0x41, 0x75, 0x74, 0x68, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x32, 0x0a, 0x16, 0x69, 0x73,
	0x5f, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x69, 0x70, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x50, 0x72,
	0x65, 0x41, 0x75, 0x74, 0x68, 0x54, 0x69, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x3b,
	0x0a, 0x1a, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6d,
	0x70, 0x6c, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x18, 0x1d, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x17, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x53, 0x69, 0x6d,
	0x70, 0x6c, 0x69, 0x66, 0x79, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x69,
	0x73, 0x5f, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x1e,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x4e, 0x65, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x69, 0x73, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x65, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18,
	0x2d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x69, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x8f, 0x01, 0x0a,
	0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDescData = file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDesc
)

func file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDescData
}

var file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_moego_models_online_booking_v1_business_ob_config_models_proto_goTypes = []interface{}{
	(*BusinessOBConfigModel)(nil),               // 0: moego.models.online_booking.v1.BusinessOBConfigModel
	(*BusinessOBConfigSimpleView)(nil),          // 1: moego.models.online_booking.v1.BusinessOBConfigSimpleView
	(*BusinessOBConfigClientPortalView)(nil),    // 2: moego.models.online_booking.v1.BusinessOBConfigClientPortalView
	(*BusinessOBConfigModelClientListView)(nil), // 3: moego.models.online_booking.v1.BusinessOBConfigModelClientListView
	(*BusinessOBConfigModelClientView)(nil),     // 4: moego.models.online_booking.v1.BusinessOBConfigModelClientView
	(*BusinessOBConfigModelBookingView)(nil),    // 5: moego.models.online_booking.v1.BusinessOBConfigModelBookingView
	(AvailabilityType)(0),                       // 6: moego.models.online_booking.v1.AvailabilityType
	(TimeSlotFormat)(0),                         // 7: moego.models.online_booking.v1.TimeSlotFormat
	(AcceptClientType)(0),                       // 8: moego.models.online_booking.v1.AcceptClientType
	(PaymentType)(0),                            // 9: moego.models.online_booking.v1.PaymentType
	(PrepayType)(0),                             // 10: moego.models.online_booking.v1.PrepayType
	(PrepayDepositType)(0),                      // 11: moego.models.online_booking.v1.PrepayDepositType
	(BookingRangeEndType)(0),                    // 12: moego.models.online_booking.v1.BookingRangeEndType
}
var file_moego_models_online_booking_v1_business_ob_config_models_proto_depIdxs = []int32{
	6,  // 0: moego.models.online_booking.v1.BusinessOBConfigModel.available_time_type:type_name -> moego.models.online_booking.v1.AvailabilityType
	7,  // 1: moego.models.online_booking.v1.BusinessOBConfigModel.by_slot_timeslot_format:type_name -> moego.models.online_booking.v1.TimeSlotFormat
	7,  // 2: moego.models.online_booking.v1.BusinessOBConfigModel.timeslot_format:type_name -> moego.models.online_booking.v1.TimeSlotFormat
	8,  // 3: moego.models.online_booking.v1.BusinessOBConfigModel.accept_client_type:type_name -> moego.models.online_booking.v1.AcceptClientType
	9,  // 4: moego.models.online_booking.v1.BusinessOBConfigModel.payment_type:type_name -> moego.models.online_booking.v1.PaymentType
	10, // 5: moego.models.online_booking.v1.BusinessOBConfigModel.prepay_type:type_name -> moego.models.online_booking.v1.PrepayType
	11, // 6: moego.models.online_booking.v1.BusinessOBConfigModel.prepay_deposit_type:type_name -> moego.models.online_booking.v1.PrepayDepositType
	12, // 7: moego.models.online_booking.v1.BusinessOBConfigModel.booking_range_end_type:type_name -> moego.models.online_booking.v1.BookingRangeEndType
	6,  // 8: moego.models.online_booking.v1.BusinessOBConfigModelClientListView.available_time_type:type_name -> moego.models.online_booking.v1.AvailabilityType
	7,  // 9: moego.models.online_booking.v1.BusinessOBConfigModelClientListView.timeslot_format:type_name -> moego.models.online_booking.v1.TimeSlotFormat
	7,  // 10: moego.models.online_booking.v1.BusinessOBConfigModelClientListView.by_slot_timeslot_format:type_name -> moego.models.online_booking.v1.TimeSlotFormat
	6,  // 11: moego.models.online_booking.v1.BusinessOBConfigModelClientView.available_time_type:type_name -> moego.models.online_booking.v1.AvailabilityType
	7,  // 12: moego.models.online_booking.v1.BusinessOBConfigModelClientView.timeslot_format:type_name -> moego.models.online_booking.v1.TimeSlotFormat
	7,  // 13: moego.models.online_booking.v1.BusinessOBConfigModelClientView.by_slot_timeslot_format:type_name -> moego.models.online_booking.v1.TimeSlotFormat
	8,  // 14: moego.models.online_booking.v1.BusinessOBConfigModelClientView.accept_client_type:type_name -> moego.models.online_booking.v1.AcceptClientType
	6,  // 15: moego.models.online_booking.v1.BusinessOBConfigModelBookingView.available_time_type:type_name -> moego.models.online_booking.v1.AvailabilityType
	7,  // 16: moego.models.online_booking.v1.BusinessOBConfigModelBookingView.timeslot_format:type_name -> moego.models.online_booking.v1.TimeSlotFormat
	7,  // 17: moego.models.online_booking.v1.BusinessOBConfigModelBookingView.by_slot_timeslot_format:type_name -> moego.models.online_booking.v1.TimeSlotFormat
	12, // 18: moego.models.online_booking.v1.BusinessOBConfigModelBookingView.booking_range_end_type:type_name -> moego.models.online_booking.v1.BookingRangeEndType
	9,  // 19: moego.models.online_booking.v1.BusinessOBConfigModelBookingView.payment_type:type_name -> moego.models.online_booking.v1.PaymentType
	10, // 20: moego.models.online_booking.v1.BusinessOBConfigModelBookingView.prepay_type:type_name -> moego.models.online_booking.v1.PrepayType
	11, // 21: moego.models.online_booking.v1.BusinessOBConfigModelBookingView.prepay_deposit_type:type_name -> moego.models.online_booking.v1.PrepayDepositType
	22, // [22:22] is the sub-list for method output_type
	22, // [22:22] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_business_ob_config_models_proto_init() }
func file_moego_models_online_booking_v1_business_ob_config_models_proto_init() {
	if File_moego_models_online_booking_v1_business_ob_config_models_proto != nil {
		return
	}
	file_moego_models_online_booking_v1_ob_config_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessOBConfigModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessOBConfigSimpleView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessOBConfigClientPortalView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessOBConfigModelClientListView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessOBConfigModelClientView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessOBConfigModelBookingView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_business_ob_config_models_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_business_ob_config_models_proto_depIdxs,
		MessageInfos:      file_moego_models_online_booking_v1_business_ob_config_models_proto_msgTypes,
	}.Build()
	File_moego_models_online_booking_v1_business_ob_config_models_proto = out.File
	file_moego_models_online_booking_v1_business_ob_config_models_proto_rawDesc = nil
	file_moego_models_online_booking_v1_business_ob_config_models_proto_goTypes = nil
	file_moego_models_online_booking_v1_business_ob_config_models_proto_depIdxs = nil
}
