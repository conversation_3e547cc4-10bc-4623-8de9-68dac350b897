// @since 2023-06-20 17:13:10
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/sms/v1/business_setting_models.proto

package smspb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The company atp status model
type BusinessPhoneNumberConfigModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// query business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// business phone number
	PhoneNumber string `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *BusinessPhoneNumberConfigModel) Reset() {
	*x = BusinessPhoneNumberConfigModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_sms_v1_business_setting_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessPhoneNumberConfigModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessPhoneNumberConfigModel) ProtoMessage() {}

func (x *BusinessPhoneNumberConfigModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_sms_v1_business_setting_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessPhoneNumberConfigModel.ProtoReflect.Descriptor instead.
func (*BusinessPhoneNumberConfigModel) Descriptor() ([]byte, []int) {
	return file_moego_models_sms_v1_business_setting_models_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessPhoneNumberConfigModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessPhoneNumberConfigModel) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

// business pn setting model
type BusinessSmsSettingModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// twilio account sid
	TwilioSid string `protobuf:"bytes,3,opt,name=twilio_sid,json=twilioSid,proto3" json:"twilio_sid,omitempty"`
	// twilio account token
	TwilioToken string `protobuf:"bytes,4,opt,name=twilio_token,json=twilioToken,proto3" json:"twilio_token,omitempty"`
	// twilio accuont messsging service sid
	TwilioMsSid string `protobuf:"bytes,5,opt,name=twilio_ms_sid,json=twilioMsSid,proto3" json:"twilio_ms_sid,omitempty"`
	// twilio phone number
	TwilioNumber string `protobuf:"bytes,6,opt,name=twilio_number,json=twilioNumber,proto3" json:"twilio_number,omitempty"`
	// business call handle type
	CallHandleType int32 `protobuf:"varint,7,opt,name=call_handle_type,json=callHandleType,proto3" json:"call_handle_type,omitempty"`
	// business call phone number
	CallPhoneNumber string `protobuf:"bytes,8,opt,name=call_phone_number,json=callPhoneNumber,proto3" json:"call_phone_number,omitempty"`
	// business call reply type
	CallReplyType int32 `protobuf:"varint,9,opt,name=call_reply_type,json=callReplyType,proto3" json:"call_reply_type,omitempty"`
	// business reply message body
	ReplyMessage string `protobuf:"bytes,10,opt,name=reply_message,json=replyMessage,proto3" json:"reply_message,omitempty"`
}

func (x *BusinessSmsSettingModel) Reset() {
	*x = BusinessSmsSettingModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_sms_v1_business_setting_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessSmsSettingModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessSmsSettingModel) ProtoMessage() {}

func (x *BusinessSmsSettingModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_sms_v1_business_setting_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessSmsSettingModel.ProtoReflect.Descriptor instead.
func (*BusinessSmsSettingModel) Descriptor() ([]byte, []int) {
	return file_moego_models_sms_v1_business_setting_models_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessSmsSettingModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BusinessSmsSettingModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessSmsSettingModel) GetTwilioSid() string {
	if x != nil {
		return x.TwilioSid
	}
	return ""
}

func (x *BusinessSmsSettingModel) GetTwilioToken() string {
	if x != nil {
		return x.TwilioToken
	}
	return ""
}

func (x *BusinessSmsSettingModel) GetTwilioMsSid() string {
	if x != nil {
		return x.TwilioMsSid
	}
	return ""
}

func (x *BusinessSmsSettingModel) GetTwilioNumber() string {
	if x != nil {
		return x.TwilioNumber
	}
	return ""
}

func (x *BusinessSmsSettingModel) GetCallHandleType() int32 {
	if x != nil {
		return x.CallHandleType
	}
	return 0
}

func (x *BusinessSmsSettingModel) GetCallPhoneNumber() string {
	if x != nil {
		return x.CallPhoneNumber
	}
	return ""
}

func (x *BusinessSmsSettingModel) GetCallReplyType() int32 {
	if x != nil {
		return x.CallReplyType
	}
	return 0
}

func (x *BusinessSmsSettingModel) GetReplyMessage() string {
	if x != nil {
		return x.ReplyMessage
	}
	return ""
}

// address param
type BusinessNumberAddressModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// address 1
	Address1 string `protobuf:"bytes,1,opt,name=address1,proto3" json:"address1,omitempty"`
	// address 2
	Address2 string `protobuf:"bytes,2,opt,name=address2,proto3" json:"address2,omitempty"`
	// country
	Country string `protobuf:"bytes,3,opt,name=country,proto3" json:"country,omitempty"`
	// city
	City string `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`
	// state
	State string `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	// zipcode
	Zipcode string `protobuf:"bytes,6,opt,name=zipcode,proto3" json:"zipcode,omitempty"`
	// lat
	Lat string `protobuf:"bytes,7,opt,name=lat,proto3" json:"lat,omitempty"`
	// lng
	Lng string `protobuf:"bytes,8,opt,name=lng,proto3" json:"lng,omitempty"`
}

func (x *BusinessNumberAddressModel) Reset() {
	*x = BusinessNumberAddressModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_sms_v1_business_setting_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessNumberAddressModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessNumberAddressModel) ProtoMessage() {}

func (x *BusinessNumberAddressModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_sms_v1_business_setting_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessNumberAddressModel.ProtoReflect.Descriptor instead.
func (*BusinessNumberAddressModel) Descriptor() ([]byte, []int) {
	return file_moego_models_sms_v1_business_setting_models_proto_rawDescGZIP(), []int{2}
}

func (x *BusinessNumberAddressModel) GetAddress1() string {
	if x != nil {
		return x.Address1
	}
	return ""
}

func (x *BusinessNumberAddressModel) GetAddress2() string {
	if x != nil {
		return x.Address2
	}
	return ""
}

func (x *BusinessNumberAddressModel) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *BusinessNumberAddressModel) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *BusinessNumberAddressModel) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *BusinessNumberAddressModel) GetZipcode() string {
	if x != nil {
		return x.Zipcode
	}
	return ""
}

func (x *BusinessNumberAddressModel) GetLat() string {
	if x != nil {
		return x.Lat
	}
	return ""
}

func (x *BusinessNumberAddressModel) GetLng() string {
	if x != nil {
		return x.Lng
	}
	return ""
}

var File_moego_models_sms_v1_business_setting_models_proto protoreflect.FileDescriptor

var file_moego_models_sms_v1_business_setting_models_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73,
	0x6d, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x13, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x22, 0x64, 0x0a, 0x1e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x87,
	0x03, 0x0a, 0x17, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x6d, 0x73, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x77,
	0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x53, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x77, 0x69,
	0x6c, 0x69, 0x6f, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x22, 0x0a, 0x0d,
	0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x6d, 0x73, 0x5f, 0x73, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x4d, 0x73, 0x53, 0x69, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x68, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x2a, 0x0a, 0x11, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x61, 0x6c, 0x6c,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x63,
	0x61, 0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6c,
	0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xd6, 0x01, 0x0a, 0x1a, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x31, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6c, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x6c, 0x6e, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6c, 0x6e,
	0x67, 0x42, 0x6f, 0x0a, 0x1b, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x4e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73, 0x6d, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x73, 0x6d, 0x73,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_sms_v1_business_setting_models_proto_rawDescOnce sync.Once
	file_moego_models_sms_v1_business_setting_models_proto_rawDescData = file_moego_models_sms_v1_business_setting_models_proto_rawDesc
)

func file_moego_models_sms_v1_business_setting_models_proto_rawDescGZIP() []byte {
	file_moego_models_sms_v1_business_setting_models_proto_rawDescOnce.Do(func() {
		file_moego_models_sms_v1_business_setting_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_sms_v1_business_setting_models_proto_rawDescData)
	})
	return file_moego_models_sms_v1_business_setting_models_proto_rawDescData
}

var file_moego_models_sms_v1_business_setting_models_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_sms_v1_business_setting_models_proto_goTypes = []interface{}{
	(*BusinessPhoneNumberConfigModel)(nil), // 0: moego.models.sms.v1.BusinessPhoneNumberConfigModel
	(*BusinessSmsSettingModel)(nil),        // 1: moego.models.sms.v1.BusinessSmsSettingModel
	(*BusinessNumberAddressModel)(nil),     // 2: moego.models.sms.v1.BusinessNumberAddressModel
}
var file_moego_models_sms_v1_business_setting_models_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_sms_v1_business_setting_models_proto_init() }
func file_moego_models_sms_v1_business_setting_models_proto_init() {
	if File_moego_models_sms_v1_business_setting_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_sms_v1_business_setting_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessPhoneNumberConfigModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_sms_v1_business_setting_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessSmsSettingModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_sms_v1_business_setting_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessNumberAddressModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_sms_v1_business_setting_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_sms_v1_business_setting_models_proto_goTypes,
		DependencyIndexes: file_moego_models_sms_v1_business_setting_models_proto_depIdxs,
		MessageInfos:      file_moego_models_sms_v1_business_setting_models_proto_msgTypes,
	}.Build()
	File_moego_models_sms_v1_business_setting_models_proto = out.File
	file_moego_models_sms_v1_business_setting_models_proto_rawDesc = nil
	file_moego_models_sms_v1_business_setting_models_proto_goTypes = nil
	file_moego_models_sms_v1_business_setting_models_proto_depIdxs = nil
}
