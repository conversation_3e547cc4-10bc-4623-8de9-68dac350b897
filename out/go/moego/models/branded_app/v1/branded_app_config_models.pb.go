// @since 2024-06-03 14:43:06
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/branded_app/v1/branded_app_config_models.proto

package brandedapppb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The BrandedAppConfig model
type BrandedAppConfigModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the branded app id
	BrandedAppId string `protobuf:"bytes,2,opt,name=branded_app_id,json=brandedAppId,proto3" json:"branded_app_id,omitempty"`
	// the theme color
	ThemeColor string `protobuf:"bytes,3,opt,name=theme_color,json=themeColor,proto3" json:"theme_color,omitempty"`
	// the logo url
	LogoUrl string `protobuf:"bytes,4,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// the app name
	AppName string `protobuf:"bytes,5,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	// introduction
	Introduction string `protobuf:"bytes,6,opt,name=introduction,proto3" json:"introduction,omitempty"`
	// description
	Description string `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	// country code
	CountryCode []string `protobuf:"bytes,8,rep,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	// branded type, enterprise or company
	BrandedType v1.AccountNamespaceType `protobuf:"varint,9,opt,name=branded_type,json=brandedType,proto3,enum=moego.models.account.v1.AccountNamespaceType" json:"branded_type,omitempty"`
	// branded id, enterprise id or company id
	BrandedId int64 `protobuf:"varint,10,opt,name=branded_id,json=brandedId,proto3" json:"branded_id,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,11,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// require payment method
	RequirePaymentMethod bool `protobuf:"varint,12,opt,name=require_payment_method,json=requirePaymentMethod,proto3" json:"require_payment_method,omitempty"`
	// the created time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// the updated time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// iOS download link
	IosDownloadLink string `protobuf:"bytes,16,opt,name=ios_download_link,json=iosDownloadLink,proto3" json:"ios_download_link,omitempty"`
	// Android download link
	AndroidDownloadLink string `protobuf:"bytes,17,opt,name=android_download_link,json=androidDownloadLink,proto3" json:"android_download_link,omitempty"`
	// App icon url
	AppIconUrl string `protobuf:"bytes,18,opt,name=app_icon_url,json=appIconUrl,proto3" json:"app_icon_url,omitempty"`
	// Boot download link
	BootDownloadLink string `protobuf:"bytes,19,opt,name=boot_download_link,json=bootDownloadLink,proto3" json:"boot_download_link,omitempty"`
	// The flag of allowed to free cancel appointment
	IsFreeCancellation bool `protobuf:"varint,20,opt,name=is_free_cancellation,json=isFreeCancellation,proto3" json:"is_free_cancellation,omitempty"`
	// The X hours before can cancel for free, cancel within X hours for a fee
	CancellationThresholdHours int32 `protobuf:"varint,21,opt,name=cancellation_threshold_hours,json=cancellationThresholdHours,proto3" json:"cancellation_threshold_hours,omitempty"`
	// Fee to be paid if canceled within cancellation threshold hours
	CancellationFee float64 `protobuf:"fixed64,22,opt,name=cancellation_fee,json=cancellationFee,proto3" json:"cancellation_fee,omitempty"`
	// The flag of allowed to reschedule appointment
	IsAllowReschedule bool `protobuf:"varint,23,opt,name=is_allow_reschedule,json=isAllowReschedule,proto3" json:"is_allow_reschedule,omitempty"`
	// X hours before can reschedule for free, reschedule unavailable within reschedule threshold hours
	RescheduleThresholdHours int32 `protobuf:"varint,24,opt,name=reschedule_threshold_hours,json=rescheduleThresholdHours,proto3" json:"reschedule_threshold_hours,omitempty"`
	// The flag of allowed to cancel appointment
	IsAllowCancel bool `protobuf:"varint,25,opt,name=is_allow_cancel,json=isAllowCancel,proto3" json:"is_allow_cancel,omitempty"`
}

func (x *BrandedAppConfigModel) Reset() {
	*x = BrandedAppConfigModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_branded_app_v1_branded_app_config_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BrandedAppConfigModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrandedAppConfigModel) ProtoMessage() {}

func (x *BrandedAppConfigModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_branded_app_v1_branded_app_config_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrandedAppConfigModel.ProtoReflect.Descriptor instead.
func (*BrandedAppConfigModel) Descriptor() ([]byte, []int) {
	return file_moego_models_branded_app_v1_branded_app_config_models_proto_rawDescGZIP(), []int{0}
}

func (x *BrandedAppConfigModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BrandedAppConfigModel) GetBrandedAppId() string {
	if x != nil {
		return x.BrandedAppId
	}
	return ""
}

func (x *BrandedAppConfigModel) GetThemeColor() string {
	if x != nil {
		return x.ThemeColor
	}
	return ""
}

func (x *BrandedAppConfigModel) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *BrandedAppConfigModel) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *BrandedAppConfigModel) GetIntroduction() string {
	if x != nil {
		return x.Introduction
	}
	return ""
}

func (x *BrandedAppConfigModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BrandedAppConfigModel) GetCountryCode() []string {
	if x != nil {
		return x.CountryCode
	}
	return nil
}

func (x *BrandedAppConfigModel) GetBrandedType() v1.AccountNamespaceType {
	if x != nil {
		return x.BrandedType
	}
	return v1.AccountNamespaceType(0)
}

func (x *BrandedAppConfigModel) GetBrandedId() int64 {
	if x != nil {
		return x.BrandedId
	}
	return 0
}

func (x *BrandedAppConfigModel) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *BrandedAppConfigModel) GetRequirePaymentMethod() bool {
	if x != nil {
		return x.RequirePaymentMethod
	}
	return false
}

func (x *BrandedAppConfigModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *BrandedAppConfigModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *BrandedAppConfigModel) GetIosDownloadLink() string {
	if x != nil {
		return x.IosDownloadLink
	}
	return ""
}

func (x *BrandedAppConfigModel) GetAndroidDownloadLink() string {
	if x != nil {
		return x.AndroidDownloadLink
	}
	return ""
}

func (x *BrandedAppConfigModel) GetAppIconUrl() string {
	if x != nil {
		return x.AppIconUrl
	}
	return ""
}

func (x *BrandedAppConfigModel) GetBootDownloadLink() string {
	if x != nil {
		return x.BootDownloadLink
	}
	return ""
}

func (x *BrandedAppConfigModel) GetIsFreeCancellation() bool {
	if x != nil {
		return x.IsFreeCancellation
	}
	return false
}

func (x *BrandedAppConfigModel) GetCancellationThresholdHours() int32 {
	if x != nil {
		return x.CancellationThresholdHours
	}
	return 0
}

func (x *BrandedAppConfigModel) GetCancellationFee() float64 {
	if x != nil {
		return x.CancellationFee
	}
	return 0
}

func (x *BrandedAppConfigModel) GetIsAllowReschedule() bool {
	if x != nil {
		return x.IsAllowReschedule
	}
	return false
}

func (x *BrandedAppConfigModel) GetRescheduleThresholdHours() int32 {
	if x != nil {
		return x.RescheduleThresholdHours
	}
	return 0
}

func (x *BrandedAppConfigModel) GetIsAllowCancel() bool {
	if x != nil {
		return x.IsAllowCancel
	}
	return false
}

// The BrandedAppConfig view
type BrandedAppConfigView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the branded app id
	BrandedAppId string `protobuf:"bytes,1,opt,name=branded_app_id,json=brandedAppId,proto3" json:"branded_app_id,omitempty"`
	// the theme color
	ThemeColor string `protobuf:"bytes,2,opt,name=theme_color,json=themeColor,proto3" json:"theme_color,omitempty"`
	// the logo url
	LogoUrl string `protobuf:"bytes,3,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// the app name
	AppName string `protobuf:"bytes,4,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	// introduction
	Introduction string `protobuf:"bytes,5,opt,name=introduction,proto3" json:"introduction,omitempty"`
	// description
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	// country code
	CountryCode []string `protobuf:"bytes,7,rep,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,8,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// branded type, enterprise or company
	BrandedType v1.AccountNamespaceType `protobuf:"varint,9,opt,name=branded_type,json=brandedType,proto3,enum=moego.models.account.v1.AccountNamespaceType" json:"branded_type,omitempty"`
	// branded id, enterprise id or company id
	BrandedId int64 `protobuf:"varint,10,opt,name=branded_id,json=brandedId,proto3" json:"branded_id,omitempty"`
	// require payment method
	RequirePaymentMethod bool `protobuf:"varint,12,opt,name=require_payment_method,json=requirePaymentMethod,proto3" json:"require_payment_method,omitempty"`
	// The flag of allowed to free cancel appointment
	IsFreeCancellation bool `protobuf:"varint,20,opt,name=is_free_cancellation,json=isFreeCancellation,proto3" json:"is_free_cancellation,omitempty"`
	// The X hours before can cancel for free, cancel within X hours for a fee
	CancellationThresholdHours int32 `protobuf:"varint,21,opt,name=cancellation_threshold_hours,json=cancellationThresholdHours,proto3" json:"cancellation_threshold_hours,omitempty"`
	// Fee to be paid if canceled within cancellation threshold hours
	CancellationFee float64 `protobuf:"fixed64,22,opt,name=cancellation_fee,json=cancellationFee,proto3" json:"cancellation_fee,omitempty"`
	// The flag of allowed to reschedule appointment
	IsAllowReschedule bool `protobuf:"varint,23,opt,name=is_allow_reschedule,json=isAllowReschedule,proto3" json:"is_allow_reschedule,omitempty"`
	// X hours before can reschedule for free, reschedule unavailable within reschedule threshold hours
	RescheduleThresholdHours int32 `protobuf:"varint,24,opt,name=reschedule_threshold_hours,json=rescheduleThresholdHours,proto3" json:"reschedule_threshold_hours,omitempty"`
	// The flag of allowed to cancel appointment
	IsAllowCancel bool `protobuf:"varint,25,opt,name=is_allow_cancel,json=isAllowCancel,proto3" json:"is_allow_cancel,omitempty"`
}

func (x *BrandedAppConfigView) Reset() {
	*x = BrandedAppConfigView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_branded_app_v1_branded_app_config_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BrandedAppConfigView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrandedAppConfigView) ProtoMessage() {}

func (x *BrandedAppConfigView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_branded_app_v1_branded_app_config_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrandedAppConfigView.ProtoReflect.Descriptor instead.
func (*BrandedAppConfigView) Descriptor() ([]byte, []int) {
	return file_moego_models_branded_app_v1_branded_app_config_models_proto_rawDescGZIP(), []int{1}
}

func (x *BrandedAppConfigView) GetBrandedAppId() string {
	if x != nil {
		return x.BrandedAppId
	}
	return ""
}

func (x *BrandedAppConfigView) GetThemeColor() string {
	if x != nil {
		return x.ThemeColor
	}
	return ""
}

func (x *BrandedAppConfigView) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *BrandedAppConfigView) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *BrandedAppConfigView) GetIntroduction() string {
	if x != nil {
		return x.Introduction
	}
	return ""
}

func (x *BrandedAppConfigView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BrandedAppConfigView) GetCountryCode() []string {
	if x != nil {
		return x.CountryCode
	}
	return nil
}

func (x *BrandedAppConfigView) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *BrandedAppConfigView) GetBrandedType() v1.AccountNamespaceType {
	if x != nil {
		return x.BrandedType
	}
	return v1.AccountNamespaceType(0)
}

func (x *BrandedAppConfigView) GetBrandedId() int64 {
	if x != nil {
		return x.BrandedId
	}
	return 0
}

func (x *BrandedAppConfigView) GetRequirePaymentMethod() bool {
	if x != nil {
		return x.RequirePaymentMethod
	}
	return false
}

func (x *BrandedAppConfigView) GetIsFreeCancellation() bool {
	if x != nil {
		return x.IsFreeCancellation
	}
	return false
}

func (x *BrandedAppConfigView) GetCancellationThresholdHours() int32 {
	if x != nil {
		return x.CancellationThresholdHours
	}
	return 0
}

func (x *BrandedAppConfigView) GetCancellationFee() float64 {
	if x != nil {
		return x.CancellationFee
	}
	return 0
}

func (x *BrandedAppConfigView) GetIsAllowReschedule() bool {
	if x != nil {
		return x.IsAllowReschedule
	}
	return false
}

func (x *BrandedAppConfigView) GetRescheduleThresholdHours() int32 {
	if x != nil {
		return x.RescheduleThresholdHours
	}
	return 0
}

func (x *BrandedAppConfigView) GetIsAllowCancel() bool {
	if x != nil {
		return x.IsAllowCancel
	}
	return false
}

var File_moego_models_branded_app_v1_branded_app_config_models_proto protoreflect.FileDescriptor

var file_moego_models_branded_app_v1_branded_app_config_models_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb2, 0x08, 0x0a, 0x15, 0x42, 0x72, 0x61,
	0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x68, 0x65, 0x6d,
	0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74,
	0x68, 0x65, 0x6d, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x67,
	0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x67,
	0x6f, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x50, 0x0a, 0x0c, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x16,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x6f, 0x73, 0x5f,
	0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6f, 0x73, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x32, 0x0a, 0x15, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f,
	0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x13, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x44, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x5f,
	0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x61, 0x70, 0x70, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x6f,
	0x6f, 0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x74, 0x44, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x66,
	0x72, 0x65, 0x65, 0x5f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x46, 0x72, 0x65, 0x65, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x1c, 0x63, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x1a, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x12, 0x29, 0x0a, 0x10,
	0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x65, 0x65,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x5f, 0x72, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x3c, 0x0a, 0x1a, 0x72, 0x65, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x5f,
	0x68, 0x6f, 0x75, 0x72, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x52, 0x18, 0x72, 0x65, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64,
	0x48, 0x6f, 0x75, 0x72, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x5f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d,
	0x69, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x22, 0xfb, 0x05,
	0x0a, 0x14, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x56, 0x69, 0x65, 0x77, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65,
	0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x19, 0x0a,
	0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x50, 0x0a, 0x0c, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x49, 0x64,
	0x12, 0x34, 0x0a, 0x16, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x14, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x66, 0x72, 0x65,
	0x65, 0x5f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x46, 0x72, 0x65, 0x65, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x1c, 0x63, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x5f, 0x68, 0x6f, 0x75, 0x72, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x1a,
	0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x48, 0x6f, 0x75, 0x72, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x46, 0x65, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x5f, 0x72, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x17, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x11, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x3c, 0x0a, 0x1a, 0x72, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x5f, 0x68, 0x6f,
	0x75, 0x72, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x05, 0x52, 0x18, 0x72, 0x65, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x48, 0x6f,
	0x75, 0x72, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f,
	0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x18, 0x19, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73,
	0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x42, 0x86, 0x01, 0x0a, 0x23,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64,
	0x5f, 0x61, 0x70, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x61,
	0x70, 0x70, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_branded_app_v1_branded_app_config_models_proto_rawDescOnce sync.Once
	file_moego_models_branded_app_v1_branded_app_config_models_proto_rawDescData = file_moego_models_branded_app_v1_branded_app_config_models_proto_rawDesc
)

func file_moego_models_branded_app_v1_branded_app_config_models_proto_rawDescGZIP() []byte {
	file_moego_models_branded_app_v1_branded_app_config_models_proto_rawDescOnce.Do(func() {
		file_moego_models_branded_app_v1_branded_app_config_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_branded_app_v1_branded_app_config_models_proto_rawDescData)
	})
	return file_moego_models_branded_app_v1_branded_app_config_models_proto_rawDescData
}

var file_moego_models_branded_app_v1_branded_app_config_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_branded_app_v1_branded_app_config_models_proto_goTypes = []interface{}{
	(*BrandedAppConfigModel)(nil), // 0: moego.models.branded_app.v1.BrandedAppConfigModel
	(*BrandedAppConfigView)(nil),  // 1: moego.models.branded_app.v1.BrandedAppConfigView
	(v1.AccountNamespaceType)(0),  // 2: moego.models.account.v1.AccountNamespaceType
	(*timestamppb.Timestamp)(nil), // 3: google.protobuf.Timestamp
}
var file_moego_models_branded_app_v1_branded_app_config_models_proto_depIdxs = []int32{
	2, // 0: moego.models.branded_app.v1.BrandedAppConfigModel.branded_type:type_name -> moego.models.account.v1.AccountNamespaceType
	3, // 1: moego.models.branded_app.v1.BrandedAppConfigModel.created_at:type_name -> google.protobuf.Timestamp
	3, // 2: moego.models.branded_app.v1.BrandedAppConfigModel.updated_at:type_name -> google.protobuf.Timestamp
	2, // 3: moego.models.branded_app.v1.BrandedAppConfigView.branded_type:type_name -> moego.models.account.v1.AccountNamespaceType
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_branded_app_v1_branded_app_config_models_proto_init() }
func file_moego_models_branded_app_v1_branded_app_config_models_proto_init() {
	if File_moego_models_branded_app_v1_branded_app_config_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_branded_app_v1_branded_app_config_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BrandedAppConfigModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_branded_app_v1_branded_app_config_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BrandedAppConfigView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_branded_app_v1_branded_app_config_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_branded_app_v1_branded_app_config_models_proto_goTypes,
		DependencyIndexes: file_moego_models_branded_app_v1_branded_app_config_models_proto_depIdxs,
		MessageInfos:      file_moego_models_branded_app_v1_branded_app_config_models_proto_msgTypes,
	}.Build()
	File_moego_models_branded_app_v1_branded_app_config_models_proto = out.File
	file_moego_models_branded_app_v1_branded_app_config_models_proto_rawDesc = nil
	file_moego_models_branded_app_v1_branded_app_config_models_proto_goTypes = nil
	file_moego_models_branded_app_v1_branded_app_config_models_proto_depIdxs = nil
}
