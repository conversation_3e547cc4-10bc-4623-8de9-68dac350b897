// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business/v1/arrival_window_models.proto

package businesspb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// business arrival window setting model, only for mobile
type ArrivalWindowModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// is enable
	IsEnable bool `protobuf:"varint,3,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	// before minutes
	BeforeMinutes int32 `protobuf:"varint,4,opt,name=before_minutes,json=beforeMinutes,proto3" json:"before_minutes,omitempty"`
	// after minutes
	AfterMinutes int32 `protobuf:"varint,5,opt,name=after_minutes,json=afterMinutes,proto3" json:"after_minutes,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *ArrivalWindowModel) Reset() {
	*x = ArrivalWindowModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_v1_arrival_window_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArrivalWindowModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArrivalWindowModel) ProtoMessage() {}

func (x *ArrivalWindowModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_v1_arrival_window_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArrivalWindowModel.ProtoReflect.Descriptor instead.
func (*ArrivalWindowModel) Descriptor() ([]byte, []int) {
	return file_moego_models_business_v1_arrival_window_models_proto_rawDescGZIP(), []int{0}
}

func (x *ArrivalWindowModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ArrivalWindowModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ArrivalWindowModel) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

func (x *ArrivalWindowModel) GetBeforeMinutes() int32 {
	if x != nil {
		return x.BeforeMinutes
	}
	return 0
}

func (x *ArrivalWindowModel) GetAfterMinutes() int32 {
	if x != nil {
		return x.AfterMinutes
	}
	return 0
}

func (x *ArrivalWindowModel) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ArrivalWindowModel) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// business arrival window setting model public view
type ArrivalWindowModelPublicView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// is enable
	IsEnable bool `protobuf:"varint,2,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	// before minutes
	BeforeMinutes int32 `protobuf:"varint,3,opt,name=before_minutes,json=beforeMinutes,proto3" json:"before_minutes,omitempty"`
	// after minutes
	AfterMinutes int32 `protobuf:"varint,4,opt,name=after_minutes,json=afterMinutes,proto3" json:"after_minutes,omitempty"`
}

func (x *ArrivalWindowModelPublicView) Reset() {
	*x = ArrivalWindowModelPublicView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_v1_arrival_window_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArrivalWindowModelPublicView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArrivalWindowModelPublicView) ProtoMessage() {}

func (x *ArrivalWindowModelPublicView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_v1_arrival_window_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArrivalWindowModelPublicView.ProtoReflect.Descriptor instead.
func (*ArrivalWindowModelPublicView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_v1_arrival_window_models_proto_rawDescGZIP(), []int{1}
}

func (x *ArrivalWindowModelPublicView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ArrivalWindowModelPublicView) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

func (x *ArrivalWindowModelPublicView) GetBeforeMinutes() int32 {
	if x != nil {
		return x.BeforeMinutes
	}
	return 0
}

func (x *ArrivalWindowModelPublicView) GetAfterMinutes() int32 {
	if x != nil {
		return x.AfterMinutes
	}
	return 0
}

var File_moego_models_business_v1_arrival_window_models_proto protoreflect.FileDescriptor

var file_moego_models_business_v1_arrival_window_models_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x72, 0x72, 0x69, 0x76,
	0x61, 0x6c, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xa8, 0x02, 0x0a, 0x12, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x57, 0x69, 0x6e,
	0x64, 0x6f, 0x77, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65,
	0x5f, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d,
	0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x66, 0x74, 0x65, 0x72, 0x4d, 0x69, 0x6e, 0x75, 0x74,
	0x65, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xa8, 0x01, 0x0a,
	0x1c, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x62,
	0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x4d, 0x69, 0x6e, 0x75, 0x74,
	0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x6d, 0x69, 0x6e, 0x75,
	0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x66, 0x74, 0x65, 0x72,
	0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_v1_arrival_window_models_proto_rawDescOnce sync.Once
	file_moego_models_business_v1_arrival_window_models_proto_rawDescData = file_moego_models_business_v1_arrival_window_models_proto_rawDesc
)

func file_moego_models_business_v1_arrival_window_models_proto_rawDescGZIP() []byte {
	file_moego_models_business_v1_arrival_window_models_proto_rawDescOnce.Do(func() {
		file_moego_models_business_v1_arrival_window_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_v1_arrival_window_models_proto_rawDescData)
	})
	return file_moego_models_business_v1_arrival_window_models_proto_rawDescData
}

var file_moego_models_business_v1_arrival_window_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_business_v1_arrival_window_models_proto_goTypes = []interface{}{
	(*ArrivalWindowModel)(nil),           // 0: moego.models.business.v1.ArrivalWindowModel
	(*ArrivalWindowModelPublicView)(nil), // 1: moego.models.business.v1.ArrivalWindowModelPublicView
	(*timestamppb.Timestamp)(nil),        // 2: google.protobuf.Timestamp
}
var file_moego_models_business_v1_arrival_window_models_proto_depIdxs = []int32{
	2, // 0: moego.models.business.v1.ArrivalWindowModel.create_time:type_name -> google.protobuf.Timestamp
	2, // 1: moego.models.business.v1.ArrivalWindowModel.update_time:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_business_v1_arrival_window_models_proto_init() }
func file_moego_models_business_v1_arrival_window_models_proto_init() {
	if File_moego_models_business_v1_arrival_window_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_business_v1_arrival_window_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArrivalWindowModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_v1_arrival_window_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArrivalWindowModelPublicView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_v1_arrival_window_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_v1_arrival_window_models_proto_goTypes,
		DependencyIndexes: file_moego_models_business_v1_arrival_window_models_proto_depIdxs,
		MessageInfos:      file_moego_models_business_v1_arrival_window_models_proto_msgTypes,
	}.Build()
	File_moego_models_business_v1_arrival_window_models_proto = out.File
	file_moego_models_business_v1_arrival_window_models_proto_rawDesc = nil
	file_moego_models_business_v1_arrival_window_models_proto_goTypes = nil
	file_moego_models_business_v1_arrival_window_models_proto_depIdxs = nil
}
