// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/message/v1/twilio_enums.proto

package messagepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// twilio number assign status
type TwilioNumberAssignStatus int32

const (
	// unspecified
	TwilioNumberAssignStatus_TWILIO_NUMBER_ASSIGN_STATUS_UNSPECIFIED TwilioNumberAssignStatus = 0
	// assign finished
	TwilioNumberAssignStatus_TWILIO_NUMBER_ASSIGN_STATUS_FINISHED TwilioNumberAssignStatus = 1
	// wait assign
	TwilioNumberAssignStatus_TWILIO_NUMBER_ASSIGN_STATUS_WAIT_ASSIGN TwilioNumberAssignStatus = 2
	// assign phone
	TwilioNumberAssignStatus_TWILIO_NUMBER_ASSIGN_STATUS_ASSIGN_MOBILE TwilioNumberAssignStatus = 3
	// assign sub account
	TwilioNumberAssignStatus_TWILIO_NUMBER_ASSIGN_STATUS_ASSIGN_SUB_ACCOUNT TwilioNumberAssignStatus = 4
	// failure
	TwilioNumberAssignStatus_TWILIO_NUMBER_ASSIGN_STATUS_BINDING_FAILURE TwilioNumberAssignStatus = 9
)

// Enum value maps for TwilioNumberAssignStatus.
var (
	TwilioNumberAssignStatus_name = map[int32]string{
		0: "TWILIO_NUMBER_ASSIGN_STATUS_UNSPECIFIED",
		1: "TWILIO_NUMBER_ASSIGN_STATUS_FINISHED",
		2: "TWILIO_NUMBER_ASSIGN_STATUS_WAIT_ASSIGN",
		3: "TWILIO_NUMBER_ASSIGN_STATUS_ASSIGN_MOBILE",
		4: "TWILIO_NUMBER_ASSIGN_STATUS_ASSIGN_SUB_ACCOUNT",
		9: "TWILIO_NUMBER_ASSIGN_STATUS_BINDING_FAILURE",
	}
	TwilioNumberAssignStatus_value = map[string]int32{
		"TWILIO_NUMBER_ASSIGN_STATUS_UNSPECIFIED":        0,
		"TWILIO_NUMBER_ASSIGN_STATUS_FINISHED":           1,
		"TWILIO_NUMBER_ASSIGN_STATUS_WAIT_ASSIGN":        2,
		"TWILIO_NUMBER_ASSIGN_STATUS_ASSIGN_MOBILE":      3,
		"TWILIO_NUMBER_ASSIGN_STATUS_ASSIGN_SUB_ACCOUNT": 4,
		"TWILIO_NUMBER_ASSIGN_STATUS_BINDING_FAILURE":    9,
	}
)

func (x TwilioNumberAssignStatus) Enum() *TwilioNumberAssignStatus {
	p := new(TwilioNumberAssignStatus)
	*p = x
	return p
}

func (x TwilioNumberAssignStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TwilioNumberAssignStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v1_twilio_enums_proto_enumTypes[0].Descriptor()
}

func (TwilioNumberAssignStatus) Type() protoreflect.EnumType {
	return &file_moego_models_message_v1_twilio_enums_proto_enumTypes[0]
}

func (x TwilioNumberAssignStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TwilioNumberAssignStatus.Descriptor instead.
func (TwilioNumberAssignStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v1_twilio_enums_proto_rawDescGZIP(), []int{0}
}

// twilio number use status
type TwilioNumberUseStatus int32

const (
	// unspecified
	TwilioNumberUseStatus_TWILIO_NUMBER_USE_STATUS_UNSPECIFIED TwilioNumberUseStatus = 0
	// using
	TwilioNumberUseStatus_TWILIO_NUMBER_USE_STATUS_USING TwilioNumberUseStatus = 1
	// unused
	TwilioNumberUseStatus_TWILIO_NUMBER_USE_STATUS_UNUSED TwilioNumberUseStatus = 2
	// closed
	TwilioNumberUseStatus_TWILIO_NUMBER_USE_STATUS_CLOSED TwilioNumberUseStatus = 3
)

// Enum value maps for TwilioNumberUseStatus.
var (
	TwilioNumberUseStatus_name = map[int32]string{
		0: "TWILIO_NUMBER_USE_STATUS_UNSPECIFIED",
		1: "TWILIO_NUMBER_USE_STATUS_USING",
		2: "TWILIO_NUMBER_USE_STATUS_UNUSED",
		3: "TWILIO_NUMBER_USE_STATUS_CLOSED",
	}
	TwilioNumberUseStatus_value = map[string]int32{
		"TWILIO_NUMBER_USE_STATUS_UNSPECIFIED": 0,
		"TWILIO_NUMBER_USE_STATUS_USING":       1,
		"TWILIO_NUMBER_USE_STATUS_UNUSED":      2,
		"TWILIO_NUMBER_USE_STATUS_CLOSED":      3,
	}
)

func (x TwilioNumberUseStatus) Enum() *TwilioNumberUseStatus {
	p := new(TwilioNumberUseStatus)
	*p = x
	return p
}

func (x TwilioNumberUseStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TwilioNumberUseStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v1_twilio_enums_proto_enumTypes[1].Descriptor()
}

func (TwilioNumberUseStatus) Type() protoreflect.EnumType {
	return &file_moego_models_message_v1_twilio_enums_proto_enumTypes[1]
}

func (x TwilioNumberUseStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TwilioNumberUseStatus.Descriptor instead.
func (TwilioNumberUseStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v1_twilio_enums_proto_rawDescGZIP(), []int{1}
}

var File_moego_models_message_v1_twilio_enums_proto protoreflect.FileDescriptor

var file_moego_models_message_v1_twilio_enums_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2a, 0xb2, 0x02, 0x0a, 0x18, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x2b, 0x0a, 0x27, 0x54, 0x57, 0x49, 0x4c, 0x49, 0x4f, 0x5f, 0x4e, 0x55, 0x4d,
	0x42, 0x45, 0x52, 0x5f, 0x41, 0x53, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x28, 0x0a, 0x24, 0x54, 0x57, 0x49, 0x4c, 0x49, 0x4f, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52,
	0x5f, 0x41, 0x53, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46,
	0x49, 0x4e, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0x01, 0x12, 0x2b, 0x0a, 0x27, 0x54, 0x57, 0x49,
	0x4c, 0x49, 0x4f, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x41, 0x53, 0x53, 0x49, 0x47,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x5f, 0x41, 0x53,
	0x53, 0x49, 0x47, 0x4e, 0x10, 0x02, 0x12, 0x2d, 0x0a, 0x29, 0x54, 0x57, 0x49, 0x4c, 0x49, 0x4f,
	0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x41, 0x53, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x53, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x4d, 0x4f, 0x42,
	0x49, 0x4c, 0x45, 0x10, 0x03, 0x12, 0x32, 0x0a, 0x2e, 0x54, 0x57, 0x49, 0x4c, 0x49, 0x4f, 0x5f,
	0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x41, 0x53, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x53, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x04, 0x12, 0x2f, 0x0a, 0x2b, 0x54, 0x57, 0x49,
	0x4c, 0x49, 0x4f, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x41, 0x53, 0x53, 0x49, 0x47,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x09, 0x2a, 0xaf, 0x01, 0x0a, 0x15, 0x54,
	0x77, 0x69, 0x6c, 0x69, 0x6f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x55, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x24, 0x54, 0x57, 0x49, 0x4c, 0x49, 0x4f, 0x5f, 0x4e,
	0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x22,
	0x0a, 0x1e, 0x54, 0x57, 0x49, 0x4c, 0x49, 0x4f, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f,
	0x55, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x53, 0x49, 0x4e, 0x47,
	0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x54, 0x57, 0x49, 0x4c, 0x49, 0x4f, 0x5f, 0x4e, 0x55, 0x4d,
	0x42, 0x45, 0x52, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x55, 0x53, 0x45, 0x44, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x54, 0x57, 0x49, 0x4c, 0x49,
	0x4f, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x03, 0x42, 0x7b, 0x0a, 0x1f,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x3b,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_models_message_v1_twilio_enums_proto_rawDescOnce sync.Once
	file_moego_models_message_v1_twilio_enums_proto_rawDescData = file_moego_models_message_v1_twilio_enums_proto_rawDesc
)

func file_moego_models_message_v1_twilio_enums_proto_rawDescGZIP() []byte {
	file_moego_models_message_v1_twilio_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_message_v1_twilio_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_message_v1_twilio_enums_proto_rawDescData)
	})
	return file_moego_models_message_v1_twilio_enums_proto_rawDescData
}

var file_moego_models_message_v1_twilio_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_message_v1_twilio_enums_proto_goTypes = []interface{}{
	(TwilioNumberAssignStatus)(0), // 0: moego.models.message.v1.TwilioNumberAssignStatus
	(TwilioNumberUseStatus)(0),    // 1: moego.models.message.v1.TwilioNumberUseStatus
}
var file_moego_models_message_v1_twilio_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_message_v1_twilio_enums_proto_init() }
func file_moego_models_message_v1_twilio_enums_proto_init() {
	if File_moego_models_message_v1_twilio_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_message_v1_twilio_enums_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_message_v1_twilio_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_message_v1_twilio_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_message_v1_twilio_enums_proto_enumTypes,
	}.Build()
	File_moego_models_message_v1_twilio_enums_proto = out.File
	file_moego_models_message_v1_twilio_enums_proto_rawDesc = nil
	file_moego_models_message_v1_twilio_enums_proto_goTypes = nil
	file_moego_models_message_v1_twilio_enums_proto_depIdxs = nil
}
