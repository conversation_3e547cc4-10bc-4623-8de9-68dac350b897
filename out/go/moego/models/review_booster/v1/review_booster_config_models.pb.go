// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/review_booster/v1/review_booster_config_models.proto

package reviewboosterpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// the review booster config model
type ReviewBoosterConfigModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// review booster config id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// enable automatic review booster
	IsEnableAutomatic bool `protobuf:"varint,4,opt,name=is_enable_automatic,json=isEnableAutomatic,proto3" json:"is_enable_automatic,omitempty"`
	// auto waiting minutes, Request a review X mins after checking out
	AutoWaitingMinutes int32 `protobuf:"varint,5,opt,name=auto_waiting_minutes,json=autoWaitingMinutes,proto3" json:"auto_waiting_minutes,omitempty"`
	// review body, Automatically sent review booster content
	ReviewBody string `protobuf:"bytes,6,opt,name=review_body,json=reviewBody,proto3" json:"review_body,omitempty"`
	// positive score, Positive reviews must be greater than or equal to this score
	PositiveScore int32 `protobuf:"varint,7,opt,name=positive_score,json=positiveScore,proto3" json:"positive_score,omitempty"`
	// positive review content
	PositiveBody string `protobuf:"bytes,8,opt,name=positive_body,json=positiveBody,proto3" json:"positive_body,omitempty"`
	// yelp link
	PositiveYelp string `protobuf:"bytes,9,opt,name=positive_yelp,json=positiveYelp,proto3" json:"positive_yelp,omitempty"`
	// facebook link
	PositiveFacebook string `protobuf:"bytes,10,opt,name=positive_facebook,json=positiveFacebook,proto3" json:"positive_facebook,omitempty"`
	// google link
	PositiveGoogle string `protobuf:"bytes,11,opt,name=positive_google,json=positiveGoogle,proto3" json:"positive_google,omitempty"`
	// negative review content
	NegativeBody string `protobuf:"bytes,12,opt,name=negative_body,json=negativeBody,proto3" json:"negative_body,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *ReviewBoosterConfigModel) Reset() {
	*x = ReviewBoosterConfigModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_review_booster_v1_review_booster_config_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewBoosterConfigModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewBoosterConfigModel) ProtoMessage() {}

func (x *ReviewBoosterConfigModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_review_booster_v1_review_booster_config_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewBoosterConfigModel.ProtoReflect.Descriptor instead.
func (*ReviewBoosterConfigModel) Descriptor() ([]byte, []int) {
	return file_moego_models_review_booster_v1_review_booster_config_models_proto_rawDescGZIP(), []int{0}
}

func (x *ReviewBoosterConfigModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReviewBoosterConfigModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ReviewBoosterConfigModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ReviewBoosterConfigModel) GetIsEnableAutomatic() bool {
	if x != nil {
		return x.IsEnableAutomatic
	}
	return false
}

func (x *ReviewBoosterConfigModel) GetAutoWaitingMinutes() int32 {
	if x != nil {
		return x.AutoWaitingMinutes
	}
	return 0
}

func (x *ReviewBoosterConfigModel) GetReviewBody() string {
	if x != nil {
		return x.ReviewBody
	}
	return ""
}

func (x *ReviewBoosterConfigModel) GetPositiveScore() int32 {
	if x != nil {
		return x.PositiveScore
	}
	return 0
}

func (x *ReviewBoosterConfigModel) GetPositiveBody() string {
	if x != nil {
		return x.PositiveBody
	}
	return ""
}

func (x *ReviewBoosterConfigModel) GetPositiveYelp() string {
	if x != nil {
		return x.PositiveYelp
	}
	return ""
}

func (x *ReviewBoosterConfigModel) GetPositiveFacebook() string {
	if x != nil {
		return x.PositiveFacebook
	}
	return ""
}

func (x *ReviewBoosterConfigModel) GetPositiveGoogle() string {
	if x != nil {
		return x.PositiveGoogle
	}
	return ""
}

func (x *ReviewBoosterConfigModel) GetNegativeBody() string {
	if x != nil {
		return x.NegativeBody
	}
	return ""
}

func (x *ReviewBoosterConfigModel) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ReviewBoosterConfigModel) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// review booster config view for client app view
type ReviewBoosterConfigClientView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// positive score, Positive reviews must be greater than or equal to this score
	PositiveScore int32 `protobuf:"varint,2,opt,name=positive_score,json=positiveScore,proto3" json:"positive_score,omitempty"`
}

func (x *ReviewBoosterConfigClientView) Reset() {
	*x = ReviewBoosterConfigClientView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_review_booster_v1_review_booster_config_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewBoosterConfigClientView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewBoosterConfigClientView) ProtoMessage() {}

func (x *ReviewBoosterConfigClientView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_review_booster_v1_review_booster_config_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewBoosterConfigClientView.ProtoReflect.Descriptor instead.
func (*ReviewBoosterConfigClientView) Descriptor() ([]byte, []int) {
	return file_moego_models_review_booster_v1_review_booster_config_models_proto_rawDescGZIP(), []int{1}
}

func (x *ReviewBoosterConfigClientView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ReviewBoosterConfigClientView) GetPositiveScore() int32 {
	if x != nil {
		return x.PositiveScore
	}
	return 0
}

var File_moego_models_review_booster_v1_review_booster_config_models_proto protoreflect.FileDescriptor

var file_moego_models_review_booster_v1_review_booster_config_models_proto_rawDesc = []byte{
	0x0a, 0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x62, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x62, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x62, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd3, 0x04, 0x0a, 0x18, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42,
	0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61,
	0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11,
	0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x63, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e,
	0x67, 0x5f, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x12, 0x61, 0x75, 0x74, 0x6f, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x69, 0x6e, 0x75,
	0x74, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x62, 0x6f,
	0x64, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x42, 0x6f, 0x64, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x42, 0x6f, 0x64, 0x79,
	0x12, 0x23, 0x0a, 0x0d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x79, 0x65, 0x6c,
	0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76,
	0x65, 0x59, 0x65, 0x6c, 0x70, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x46, 0x61, 0x63, 0x65, 0x62, 0x6f,
	0x6f, 0x6b, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x76, 0x65, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6e,
	0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6e, 0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x42, 0x6f, 0x64, 0x79,
	0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x67, 0x0a, 0x1d, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x42, 0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x5f, 0x62, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x62, 0x6f, 0x6f, 0x73, 0x74,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x62, 0x6f, 0x6f, 0x73,
	0x74, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_review_booster_v1_review_booster_config_models_proto_rawDescOnce sync.Once
	file_moego_models_review_booster_v1_review_booster_config_models_proto_rawDescData = file_moego_models_review_booster_v1_review_booster_config_models_proto_rawDesc
)

func file_moego_models_review_booster_v1_review_booster_config_models_proto_rawDescGZIP() []byte {
	file_moego_models_review_booster_v1_review_booster_config_models_proto_rawDescOnce.Do(func() {
		file_moego_models_review_booster_v1_review_booster_config_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_review_booster_v1_review_booster_config_models_proto_rawDescData)
	})
	return file_moego_models_review_booster_v1_review_booster_config_models_proto_rawDescData
}

var file_moego_models_review_booster_v1_review_booster_config_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_review_booster_v1_review_booster_config_models_proto_goTypes = []interface{}{
	(*ReviewBoosterConfigModel)(nil),      // 0: moego.models.review_booster.v1.ReviewBoosterConfigModel
	(*ReviewBoosterConfigClientView)(nil), // 1: moego.models.review_booster.v1.ReviewBoosterConfigClientView
	(*timestamppb.Timestamp)(nil),         // 2: google.protobuf.Timestamp
}
var file_moego_models_review_booster_v1_review_booster_config_models_proto_depIdxs = []int32{
	2, // 0: moego.models.review_booster.v1.ReviewBoosterConfigModel.create_time:type_name -> google.protobuf.Timestamp
	2, // 1: moego.models.review_booster.v1.ReviewBoosterConfigModel.update_time:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_review_booster_v1_review_booster_config_models_proto_init() }
func file_moego_models_review_booster_v1_review_booster_config_models_proto_init() {
	if File_moego_models_review_booster_v1_review_booster_config_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_review_booster_v1_review_booster_config_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewBoosterConfigModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_review_booster_v1_review_booster_config_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewBoosterConfigClientView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_review_booster_v1_review_booster_config_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_review_booster_v1_review_booster_config_models_proto_goTypes,
		DependencyIndexes: file_moego_models_review_booster_v1_review_booster_config_models_proto_depIdxs,
		MessageInfos:      file_moego_models_review_booster_v1_review_booster_config_models_proto_msgTypes,
	}.Build()
	File_moego_models_review_booster_v1_review_booster_config_models_proto = out.File
	file_moego_models_review_booster_v1_review_booster_config_models_proto_rawDesc = nil
	file_moego_models_review_booster_v1_review_booster_config_models_proto_goTypes = nil
	file_moego_models_review_booster_v1_review_booster_config_models_proto_depIdxs = nil
}
