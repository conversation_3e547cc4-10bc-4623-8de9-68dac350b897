// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/grooming/v1/appointment_models.proto

package groomingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// the appointment model
type AppointmentModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// appointment date, in yyyy-MM-dd format
	AppointmentDate string `protobuf:"bytes,4,opt,name=appointment_date,json=appointmentDate,proto3" json:"appointment_date,omitempty"`
	// appointment start time, in minutes
	AppointmentStartTime int32 `protobuf:"varint,5,opt,name=appointment_start_time,json=appointmentStartTime,proto3" json:"appointment_start_time,omitempty"`
	// appointment end time, in minutes
	AppointmentEndTime int32 `protobuf:"varint,6,opt,name=appointment_end_time,json=appointmentEndTime,proto3" json:"appointment_end_time,omitempty"`
	// appointment source
	Source AppointmentSource `protobuf:"varint,7,opt,name=source,proto3,enum=moego.models.grooming.v1.AppointmentSource" json:"source,omitempty"`
	// appointment status
	Status AppointmentStatus `protobuf:"varint,8,opt,name=status,proto3,enum=moego.models.grooming.v1.AppointmentStatus" json:"status,omitempty"`
	// payment status
	IsPaid AppointmentPaymentStatus `protobuf:"varint,9,opt,name=is_paid,json=isPaid,proto3,enum=moego.models.grooming.v1.AppointmentPaymentStatus" json:"is_paid,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// no start time
	NoStartTime bool `protobuf:"varint,12,opt,name=no_start_time,json=noStartTime,proto3" json:"no_start_time,omitempty"`
	// is waiting list
	IsWaitingList bool `protobuf:"varint,13,opt,name=is_waiting_list,json=isWaitingList,proto3" json:"is_waiting_list,omitempty"`
	// move waiting list staff id
	MoveWaitingBy int64 `protobuf:"varint,14,opt,name=move_waiting_by,json=moveWaitingBy,proto3" json:"move_waiting_by,omitempty"`
	// confirmed time
	ConfirmedTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=confirmed_time,json=confirmedTime,proto3" json:"confirmed_time,omitempty"`
	// check in time
	CheckInTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=check_in_time,json=checkInTime,proto3" json:"check_in_time,omitempty"`
	// check out time
	CheckOutTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=check_out_time,json=checkOutTime,proto3" json:"check_out_time,omitempty"`
	// canceled time
	CanceledTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=canceled_time,json=canceledTime,proto3" json:"canceled_time,omitempty"`
	// is blocked time
	IsBlock bool `protobuf:"varint,19,opt,name=is_block,json=isBlock,proto3" json:"is_block,omitempty"`
	// booking request status, true is booking request, false is booking
	BookOnlineStatus bool `protobuf:"varint,20,opt,name=book_online_status,json=bookOnlineStatus,proto3" json:"book_online_status,omitempty"`
	// customer address id
	CustomerAddressId int64 `protobuf:"varint,21,opt,name=customer_address_id,json=customerAddressId,proto3" json:"customer_address_id,omitempty"`
	// repeat appointment id
	RepeatId int64 `protobuf:"varint,22,opt,name=repeat_id,json=repeatId,proto3" json:"repeat_id,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,23,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// appointment pet details
	PetDetails []*PetDetailModel `protobuf:"bytes,40,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
}

func (x *AppointmentModel) Reset() {
	*x = AppointmentModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_grooming_v1_appointment_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentModel) ProtoMessage() {}

func (x *AppointmentModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_grooming_v1_appointment_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentModel.ProtoReflect.Descriptor instead.
func (*AppointmentModel) Descriptor() ([]byte, []int) {
	return file_moego_models_grooming_v1_appointment_models_proto_rawDescGZIP(), []int{0}
}

func (x *AppointmentModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AppointmentModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AppointmentModel) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *AppointmentModel) GetAppointmentDate() string {
	if x != nil {
		return x.AppointmentDate
	}
	return ""
}

func (x *AppointmentModel) GetAppointmentStartTime() int32 {
	if x != nil {
		return x.AppointmentStartTime
	}
	return 0
}

func (x *AppointmentModel) GetAppointmentEndTime() int32 {
	if x != nil {
		return x.AppointmentEndTime
	}
	return 0
}

func (x *AppointmentModel) GetSource() AppointmentSource {
	if x != nil {
		return x.Source
	}
	return AppointmentSource_APPOINTMENT_SOURCE_UNSPECIFIED
}

func (x *AppointmentModel) GetStatus() AppointmentStatus {
	if x != nil {
		return x.Status
	}
	return AppointmentStatus_APPOINTMENT_STATUS_UNSPECIFIED
}

func (x *AppointmentModel) GetIsPaid() AppointmentPaymentStatus {
	if x != nil {
		return x.IsPaid
	}
	return AppointmentPaymentStatus_APPOINTMENT_PAYMENT_STATUS_UNSPECIFIED
}

func (x *AppointmentModel) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *AppointmentModel) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *AppointmentModel) GetNoStartTime() bool {
	if x != nil {
		return x.NoStartTime
	}
	return false
}

func (x *AppointmentModel) GetIsWaitingList() bool {
	if x != nil {
		return x.IsWaitingList
	}
	return false
}

func (x *AppointmentModel) GetMoveWaitingBy() int64 {
	if x != nil {
		return x.MoveWaitingBy
	}
	return 0
}

func (x *AppointmentModel) GetConfirmedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ConfirmedTime
	}
	return nil
}

func (x *AppointmentModel) GetCheckInTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CheckInTime
	}
	return nil
}

func (x *AppointmentModel) GetCheckOutTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CheckOutTime
	}
	return nil
}

func (x *AppointmentModel) GetCanceledTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CanceledTime
	}
	return nil
}

func (x *AppointmentModel) GetIsBlock() bool {
	if x != nil {
		return x.IsBlock
	}
	return false
}

func (x *AppointmentModel) GetBookOnlineStatus() bool {
	if x != nil {
		return x.BookOnlineStatus
	}
	return false
}

func (x *AppointmentModel) GetCustomerAddressId() int64 {
	if x != nil {
		return x.CustomerAddressId
	}
	return 0
}

func (x *AppointmentModel) GetRepeatId() int64 {
	if x != nil {
		return x.RepeatId
	}
	return 0
}

func (x *AppointmentModel) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *AppointmentModel) GetPetDetails() []*PetDetailModel {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

// appointment view for c app list
type AppointmentModelClientListView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// appointment date, in yyyy-MM-dd format
	AppointmentDate string `protobuf:"bytes,4,opt,name=appointment_date,json=appointmentDate,proto3" json:"appointment_date,omitempty"`
	// appointment start time, in minutes
	AppointmentStartTime int32 `protobuf:"varint,5,opt,name=appointment_start_time,json=appointmentStartTime,proto3" json:"appointment_start_time,omitempty"`
	// appointment end time, in minutes
	AppointmentEndTime int32 `protobuf:"varint,6,opt,name=appointment_end_time,json=appointmentEndTime,proto3" json:"appointment_end_time,omitempty"`
	// no start time
	NoStartTime bool `protobuf:"varint,7,opt,name=no_start_time,json=noStartTime,proto3" json:"no_start_time,omitempty"`
}

func (x *AppointmentModelClientListView) Reset() {
	*x = AppointmentModelClientListView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_grooming_v1_appointment_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentModelClientListView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentModelClientListView) ProtoMessage() {}

func (x *AppointmentModelClientListView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_grooming_v1_appointment_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentModelClientListView.ProtoReflect.Descriptor instead.
func (*AppointmentModelClientListView) Descriptor() ([]byte, []int) {
	return file_moego_models_grooming_v1_appointment_models_proto_rawDescGZIP(), []int{1}
}

func (x *AppointmentModelClientListView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AppointmentModelClientListView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AppointmentModelClientListView) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *AppointmentModelClientListView) GetAppointmentDate() string {
	if x != nil {
		return x.AppointmentDate
	}
	return ""
}

func (x *AppointmentModelClientListView) GetAppointmentStartTime() int32 {
	if x != nil {
		return x.AppointmentStartTime
	}
	return 0
}

func (x *AppointmentModelClientListView) GetAppointmentEndTime() int32 {
	if x != nil {
		return x.AppointmentEndTime
	}
	return 0
}

func (x *AppointmentModelClientListView) GetNoStartTime() bool {
	if x != nil {
		return x.NoStartTime
	}
	return false
}

// appointment detail view for client app detail view
type AppointmentModelClientView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// appointment date, in yyyy-MM-dd format
	AppointmentDate string `protobuf:"bytes,4,opt,name=appointment_date,json=appointmentDate,proto3" json:"appointment_date,omitempty"`
	// appointment start time, in minutes
	AppointmentStartTime int32 `protobuf:"varint,5,opt,name=appointment_start_time,json=appointmentStartTime,proto3" json:"appointment_start_time,omitempty"`
	// appointment end time, in minutes
	AppointmentEndTime int32 `protobuf:"varint,6,opt,name=appointment_end_time,json=appointmentEndTime,proto3" json:"appointment_end_time,omitempty"`
	// appointment status
	Status AppointmentStatus `protobuf:"varint,7,opt,name=status,proto3,enum=moego.models.grooming.v1.AppointmentStatus" json:"status,omitempty"`
	// payment status
	PaymentStatus AppointmentPaymentStatus `protobuf:"varint,8,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.grooming.v1.AppointmentPaymentStatus" json:"payment_status,omitempty"`
	// no start time
	NoStartTime bool `protobuf:"varint,9,opt,name=no_start_time,json=noStartTime,proto3" json:"no_start_time,omitempty"`
	// booking request status, true is booking request, false is booking
	BookOnlineStatus bool `protobuf:"varint,10,opt,name=book_online_status,json=bookOnlineStatus,proto3" json:"book_online_status,omitempty"`
}

func (x *AppointmentModelClientView) Reset() {
	*x = AppointmentModelClientView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_grooming_v1_appointment_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentModelClientView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentModelClientView) ProtoMessage() {}

func (x *AppointmentModelClientView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_grooming_v1_appointment_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentModelClientView.ProtoReflect.Descriptor instead.
func (*AppointmentModelClientView) Descriptor() ([]byte, []int) {
	return file_moego_models_grooming_v1_appointment_models_proto_rawDescGZIP(), []int{2}
}

func (x *AppointmentModelClientView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AppointmentModelClientView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AppointmentModelClientView) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *AppointmentModelClientView) GetAppointmentDate() string {
	if x != nil {
		return x.AppointmentDate
	}
	return ""
}

func (x *AppointmentModelClientView) GetAppointmentStartTime() int32 {
	if x != nil {
		return x.AppointmentStartTime
	}
	return 0
}

func (x *AppointmentModelClientView) GetAppointmentEndTime() int32 {
	if x != nil {
		return x.AppointmentEndTime
	}
	return 0
}

func (x *AppointmentModelClientView) GetStatus() AppointmentStatus {
	if x != nil {
		return x.Status
	}
	return AppointmentStatus_APPOINTMENT_STATUS_UNSPECIFIED
}

func (x *AppointmentModelClientView) GetPaymentStatus() AppointmentPaymentStatus {
	if x != nil {
		return x.PaymentStatus
	}
	return AppointmentPaymentStatus_APPOINTMENT_PAYMENT_STATUS_UNSPECIFIED
}

func (x *AppointmentModelClientView) GetNoStartTime() bool {
	if x != nil {
		return x.NoStartTime
	}
	return false
}

func (x *AppointmentModelClientView) GetBookOnlineStatus() bool {
	if x != nil {
		return x.BookOnlineStatus
	}
	return false
}

var File_moego_models_grooming_v1_appointment_models_proto protoreflect.FileDescriptor

var file_moego_models_grooming_v1_appointment_models_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xc2, 0x09, 0x0a, 0x10, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x70, 0x61, 0x69,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x69, 0x73, 0x50,
	0x61, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a,
	0x0d, 0x6e, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6e, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x57, 0x61,
	0x69, 0x74, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x6f, 0x76,
	0x65, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x79, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x6d, 0x6f, 0x76, 0x65, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x42,
	0x79, 0x12, 0x41, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0e, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x6f, 0x75,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x4f,
	0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x63, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10,
	0x62, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x2e, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x49, 0x0a, 0x0b,
	0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x28, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x70, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xa9, 0x02, 0x0a, 0x1e, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a,
	0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x22, 0x0a, 0x0d, 0x6e, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6e, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0xf3, 0x03, 0x0a, 0x1a, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x34, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x12, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x59, 0x0a, 0x0e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x6f, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x6e, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x62,
	0x6f, 0x6f, 0x6b, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_models_grooming_v1_appointment_models_proto_rawDescOnce sync.Once
	file_moego_models_grooming_v1_appointment_models_proto_rawDescData = file_moego_models_grooming_v1_appointment_models_proto_rawDesc
)

func file_moego_models_grooming_v1_appointment_models_proto_rawDescGZIP() []byte {
	file_moego_models_grooming_v1_appointment_models_proto_rawDescOnce.Do(func() {
		file_moego_models_grooming_v1_appointment_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_grooming_v1_appointment_models_proto_rawDescData)
	})
	return file_moego_models_grooming_v1_appointment_models_proto_rawDescData
}

var file_moego_models_grooming_v1_appointment_models_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_grooming_v1_appointment_models_proto_goTypes = []interface{}{
	(*AppointmentModel)(nil),               // 0: moego.models.grooming.v1.AppointmentModel
	(*AppointmentModelClientListView)(nil), // 1: moego.models.grooming.v1.AppointmentModelClientListView
	(*AppointmentModelClientView)(nil),     // 2: moego.models.grooming.v1.AppointmentModelClientView
	(AppointmentSource)(0),                 // 3: moego.models.grooming.v1.AppointmentSource
	(AppointmentStatus)(0),                 // 4: moego.models.grooming.v1.AppointmentStatus
	(AppointmentPaymentStatus)(0),          // 5: moego.models.grooming.v1.AppointmentPaymentStatus
	(*timestamppb.Timestamp)(nil),          // 6: google.protobuf.Timestamp
	(*PetDetailModel)(nil),                 // 7: moego.models.grooming.v1.PetDetailModel
}
var file_moego_models_grooming_v1_appointment_models_proto_depIdxs = []int32{
	3,  // 0: moego.models.grooming.v1.AppointmentModel.source:type_name -> moego.models.grooming.v1.AppointmentSource
	4,  // 1: moego.models.grooming.v1.AppointmentModel.status:type_name -> moego.models.grooming.v1.AppointmentStatus
	5,  // 2: moego.models.grooming.v1.AppointmentModel.is_paid:type_name -> moego.models.grooming.v1.AppointmentPaymentStatus
	6,  // 3: moego.models.grooming.v1.AppointmentModel.create_time:type_name -> google.protobuf.Timestamp
	6,  // 4: moego.models.grooming.v1.AppointmentModel.update_time:type_name -> google.protobuf.Timestamp
	6,  // 5: moego.models.grooming.v1.AppointmentModel.confirmed_time:type_name -> google.protobuf.Timestamp
	6,  // 6: moego.models.grooming.v1.AppointmentModel.check_in_time:type_name -> google.protobuf.Timestamp
	6,  // 7: moego.models.grooming.v1.AppointmentModel.check_out_time:type_name -> google.protobuf.Timestamp
	6,  // 8: moego.models.grooming.v1.AppointmentModel.canceled_time:type_name -> google.protobuf.Timestamp
	7,  // 9: moego.models.grooming.v1.AppointmentModel.pet_details:type_name -> moego.models.grooming.v1.PetDetailModel
	4,  // 10: moego.models.grooming.v1.AppointmentModelClientView.status:type_name -> moego.models.grooming.v1.AppointmentStatus
	5,  // 11: moego.models.grooming.v1.AppointmentModelClientView.payment_status:type_name -> moego.models.grooming.v1.AppointmentPaymentStatus
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_moego_models_grooming_v1_appointment_models_proto_init() }
func file_moego_models_grooming_v1_appointment_models_proto_init() {
	if File_moego_models_grooming_v1_appointment_models_proto != nil {
		return
	}
	file_moego_models_grooming_v1_appointment_enums_proto_init()
	file_moego_models_grooming_v1_pet_detail_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_grooming_v1_appointment_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_grooming_v1_appointment_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentModelClientListView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_grooming_v1_appointment_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentModelClientView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_grooming_v1_appointment_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_grooming_v1_appointment_models_proto_goTypes,
		DependencyIndexes: file_moego_models_grooming_v1_appointment_models_proto_depIdxs,
		MessageInfos:      file_moego_models_grooming_v1_appointment_models_proto_msgTypes,
	}.Build()
	File_moego_models_grooming_v1_appointment_models_proto = out.File
	file_moego_models_grooming_v1_appointment_models_proto_rawDesc = nil
	file_moego_models_grooming_v1_appointment_models_proto_goTypes = nil
	file_moego_models_grooming_v1_appointment_models_proto_depIdxs = nil
}
