// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/grooming/v1/grooming_report_models.proto

package groomingpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// the grooming report model
type GroomingReportModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// grooming id
	GroomingId int64 `protobuf:"varint,4,opt,name=grooming_id,json=groomingId,proto3" json:"grooming_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,5,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet type
	PetType v1.PetType `protobuf:"varint,6,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// uuid
	Uuid string `protobuf:"bytes,7,opt,name=uuid,proto3" json:"uuid,omitempty"`
	// template publish time
	TemplatePublishTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=template_publish_time,json=templatePublishTime,proto3" json:"template_publish_time,omitempty"`
	// template json
	TemplateJson string `protobuf:"bytes,9,opt,name=template_json,json=templateJson,proto3" json:"template_json,omitempty"`
	// content json
	ContentJson string `protobuf:"bytes,10,opt,name=content_json,json=contentJson,proto3" json:"content_json,omitempty"`
	// status
	Status GroomingReportStatus `protobuf:"varint,11,opt,name=status,proto3,enum=moego.models.grooming.v1.GroomingReportStatus" json:"status,omitempty"`
	// submitted time
	SubmittedTime *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=submitted_time,json=submittedTime,proto3" json:"submitted_time,omitempty"`
	// link opened count
	LinkOpenedCount int64 `protobuf:"varint,13,opt,name=link_opened_count,json=linkOpenedCount,proto3" json:"link_opened_count,omitempty"`
	// last update staff id
	UpdatedBy int64 `protobuf:"varint,14,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	// create time
	CreatedTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	// update time
	UpdatedTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=updated_time,json=updatedTime,proto3" json:"updated_time,omitempty"`
	// theme code
	ThemeCode string `protobuf:"bytes,17,opt,name=theme_code,json=themeCode,proto3" json:"theme_code,omitempty"`
}

func (x *GroomingReportModel) Reset() {
	*x = GroomingReportModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_grooming_v1_grooming_report_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroomingReportModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingReportModel) ProtoMessage() {}

func (x *GroomingReportModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_grooming_v1_grooming_report_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingReportModel.ProtoReflect.Descriptor instead.
func (*GroomingReportModel) Descriptor() ([]byte, []int) {
	return file_moego_models_grooming_v1_grooming_report_models_proto_rawDescGZIP(), []int{0}
}

func (x *GroomingReportModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GroomingReportModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GroomingReportModel) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *GroomingReportModel) GetGroomingId() int64 {
	if x != nil {
		return x.GroomingId
	}
	return 0
}

func (x *GroomingReportModel) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GroomingReportModel) GetPetType() v1.PetType {
	if x != nil {
		return x.PetType
	}
	return v1.PetType(0)
}

func (x *GroomingReportModel) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *GroomingReportModel) GetTemplatePublishTime() *timestamppb.Timestamp {
	if x != nil {
		return x.TemplatePublishTime
	}
	return nil
}

func (x *GroomingReportModel) GetTemplateJson() string {
	if x != nil {
		return x.TemplateJson
	}
	return ""
}

func (x *GroomingReportModel) GetContentJson() string {
	if x != nil {
		return x.ContentJson
	}
	return ""
}

func (x *GroomingReportModel) GetStatus() GroomingReportStatus {
	if x != nil {
		return x.Status
	}
	return GroomingReportStatus_GROOMING_REPORT_STATUS_UNSPECIFIED
}

func (x *GroomingReportModel) GetSubmittedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SubmittedTime
	}
	return nil
}

func (x *GroomingReportModel) GetLinkOpenedCount() int64 {
	if x != nil {
		return x.LinkOpenedCount
	}
	return 0
}

func (x *GroomingReportModel) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *GroomingReportModel) GetCreatedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedTime
	}
	return nil
}

func (x *GroomingReportModel) GetUpdatedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedTime
	}
	return nil
}

func (x *GroomingReportModel) GetThemeCode() string {
	if x != nil {
		return x.ThemeCode
	}
	return ""
}

// grooming report model in c app client view
type GroomingReportModelClientView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming id
	GroomingId int64 `protobuf:"varint,4,opt,name=grooming_id,json=groomingId,proto3" json:"grooming_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,5,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// uuid
	Uuid string `protobuf:"bytes,7,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (x *GroomingReportModelClientView) Reset() {
	*x = GroomingReportModelClientView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_grooming_v1_grooming_report_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroomingReportModelClientView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingReportModelClientView) ProtoMessage() {}

func (x *GroomingReportModelClientView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_grooming_v1_grooming_report_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingReportModelClientView.ProtoReflect.Descriptor instead.
func (*GroomingReportModelClientView) Descriptor() ([]byte, []int) {
	return file_moego_models_grooming_v1_grooming_report_models_proto_rawDescGZIP(), []int{1}
}

func (x *GroomingReportModelClientView) GetGroomingId() int64 {
	if x != nil {
		return x.GroomingId
	}
	return 0
}

func (x *GroomingReportModelClientView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GroomingReportModelClientView) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

var File_moego_models_grooming_v1_grooming_report_models_proto protoreflect.FileDescriptor

var file_moego_models_grooming_v1_grooming_report_models_proto_rawDesc = []byte{
	0x0a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f,
	0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfc, 0x05, 0x0a, 0x13,
	0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x3c, 0x0a,
	0x08, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x07, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x75, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12,
	0x4e, 0x0a, 0x15, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x13, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6a, 0x73, 0x6f, 0x6e,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f,
	0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x41, 0x0a, 0x0e, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x65,
	0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6c,
	0x69, 0x6e, 0x6b, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x3d, 0x0a,
	0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x0c,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74,
	0x68, 0x65, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x6b, 0x0a, 0x1d, 0x47, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06,
	0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_grooming_v1_grooming_report_models_proto_rawDescOnce sync.Once
	file_moego_models_grooming_v1_grooming_report_models_proto_rawDescData = file_moego_models_grooming_v1_grooming_report_models_proto_rawDesc
)

func file_moego_models_grooming_v1_grooming_report_models_proto_rawDescGZIP() []byte {
	file_moego_models_grooming_v1_grooming_report_models_proto_rawDescOnce.Do(func() {
		file_moego_models_grooming_v1_grooming_report_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_grooming_v1_grooming_report_models_proto_rawDescData)
	})
	return file_moego_models_grooming_v1_grooming_report_models_proto_rawDescData
}

var file_moego_models_grooming_v1_grooming_report_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_grooming_v1_grooming_report_models_proto_goTypes = []interface{}{
	(*GroomingReportModel)(nil),           // 0: moego.models.grooming.v1.GroomingReportModel
	(*GroomingReportModelClientView)(nil), // 1: moego.models.grooming.v1.GroomingReportModelClientView
	(v1.PetType)(0),                       // 2: moego.models.customer.v1.PetType
	(*timestamppb.Timestamp)(nil),         // 3: google.protobuf.Timestamp
	(GroomingReportStatus)(0),             // 4: moego.models.grooming.v1.GroomingReportStatus
}
var file_moego_models_grooming_v1_grooming_report_models_proto_depIdxs = []int32{
	2, // 0: moego.models.grooming.v1.GroomingReportModel.pet_type:type_name -> moego.models.customer.v1.PetType
	3, // 1: moego.models.grooming.v1.GroomingReportModel.template_publish_time:type_name -> google.protobuf.Timestamp
	4, // 2: moego.models.grooming.v1.GroomingReportModel.status:type_name -> moego.models.grooming.v1.GroomingReportStatus
	3, // 3: moego.models.grooming.v1.GroomingReportModel.submitted_time:type_name -> google.protobuf.Timestamp
	3, // 4: moego.models.grooming.v1.GroomingReportModel.created_time:type_name -> google.protobuf.Timestamp
	3, // 5: moego.models.grooming.v1.GroomingReportModel.updated_time:type_name -> google.protobuf.Timestamp
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_moego_models_grooming_v1_grooming_report_models_proto_init() }
func file_moego_models_grooming_v1_grooming_report_models_proto_init() {
	if File_moego_models_grooming_v1_grooming_report_models_proto != nil {
		return
	}
	file_moego_models_grooming_v1_grooming_report_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_grooming_v1_grooming_report_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroomingReportModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_grooming_v1_grooming_report_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroomingReportModelClientView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_grooming_v1_grooming_report_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_grooming_v1_grooming_report_models_proto_goTypes,
		DependencyIndexes: file_moego_models_grooming_v1_grooming_report_models_proto_depIdxs,
		MessageInfos:      file_moego_models_grooming_v1_grooming_report_models_proto_msgTypes,
	}.Build()
	File_moego_models_grooming_v1_grooming_report_models_proto = out.File
	file_moego_models_grooming_v1_grooming_report_models_proto_rawDesc = nil
	file_moego_models_grooming_v1_grooming_report_models_proto_goTypes = nil
	file_moego_models_grooming_v1_grooming_report_models_proto_depIdxs = nil
}
