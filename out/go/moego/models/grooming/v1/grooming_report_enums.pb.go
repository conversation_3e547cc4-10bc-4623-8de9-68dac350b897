// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/grooming/v1/grooming_report_enums.proto

package groomingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// grooming report status
type GroomingReportStatus int32

const (
	// unspecified
	GroomingReportStatus_GROOMING_REPORT_STATUS_UNSPECIFIED GroomingReportStatus = 0
	// created
	GroomingReportStatus_GROOMING_REPORT_STATUS_CREATED GroomingReportStatus = 1
	// draft
	GroomingReportStatus_GROOMING_REPORT_STATUS_DRAFT GroomingReportStatus = 2
	// ready
	GroomingReportStatus_GROOMING_REPORT_STATUS_READY GroomingReportStatus = 3
	// sent
	GroomingReportStatus_GROOMING_REPORT_STATUS_SENT GroomingReportStatus = 4
)

// Enum value maps for GroomingReportStatus.
var (
	GroomingReportStatus_name = map[int32]string{
		0: "GROOMING_REPORT_STATUS_UNSPECIFIED",
		1: "GROOMING_REPORT_STATUS_CREATED",
		2: "GROOMING_REPORT_STATUS_DRAFT",
		3: "GROOMING_REPORT_STATUS_READY",
		4: "GROOMING_REPORT_STATUS_SENT",
	}
	GroomingReportStatus_value = map[string]int32{
		"GROOMING_REPORT_STATUS_UNSPECIFIED": 0,
		"GROOMING_REPORT_STATUS_CREATED":     1,
		"GROOMING_REPORT_STATUS_DRAFT":       2,
		"GROOMING_REPORT_STATUS_READY":       3,
		"GROOMING_REPORT_STATUS_SENT":        4,
	}
)

func (x GroomingReportStatus) Enum() *GroomingReportStatus {
	p := new(GroomingReportStatus)
	*p = x
	return p
}

func (x GroomingReportStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GroomingReportStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_grooming_v1_grooming_report_enums_proto_enumTypes[0].Descriptor()
}

func (GroomingReportStatus) Type() protoreflect.EnumType {
	return &file_moego_models_grooming_v1_grooming_report_enums_proto_enumTypes[0]
}

func (x GroomingReportStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GroomingReportStatus.Descriptor instead.
func (GroomingReportStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_grooming_v1_grooming_report_enums_proto_rawDescGZIP(), []int{0}
}

// grooming report send method
type GroomingReportSendMethod int32

const (
	// unspecified
	GroomingReportSendMethod_GROOMING_REPORT_SEND_METHOD_UNSPECIFIED GroomingReportSendMethod = 0
	// send by sms
	GroomingReportSendMethod_GROOMING_REPORT_SEND_BY_SMS GroomingReportSendMethod = 1
	// send by email
	GroomingReportSendMethod_GROOMING_REPORT_SEND_BY_EMAIL GroomingReportSendMethod = 2
)

// Enum value maps for GroomingReportSendMethod.
var (
	GroomingReportSendMethod_name = map[int32]string{
		0: "GROOMING_REPORT_SEND_METHOD_UNSPECIFIED",
		1: "GROOMING_REPORT_SEND_BY_SMS",
		2: "GROOMING_REPORT_SEND_BY_EMAIL",
	}
	GroomingReportSendMethod_value = map[string]int32{
		"GROOMING_REPORT_SEND_METHOD_UNSPECIFIED": 0,
		"GROOMING_REPORT_SEND_BY_SMS":             1,
		"GROOMING_REPORT_SEND_BY_EMAIL":           2,
	}
)

func (x GroomingReportSendMethod) Enum() *GroomingReportSendMethod {
	p := new(GroomingReportSendMethod)
	*p = x
	return p
}

func (x GroomingReportSendMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GroomingReportSendMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_grooming_v1_grooming_report_enums_proto_enumTypes[1].Descriptor()
}

func (GroomingReportSendMethod) Type() protoreflect.EnumType {
	return &file_moego_models_grooming_v1_grooming_report_enums_proto_enumTypes[1]
}

func (x GroomingReportSendMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GroomingReportSendMethod.Descriptor instead.
func (GroomingReportSendMethod) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_grooming_v1_grooming_report_enums_proto_rawDescGZIP(), []int{1}
}

var File_moego_models_grooming_v1_grooming_report_enums_proto protoreflect.FileDescriptor

var file_moego_models_grooming_v1_grooming_report_enums_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2a, 0xc7, 0x01, 0x0a, 0x14, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x22, 0x47, 0x52, 0x4f,
	0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x22, 0x0a, 0x1e, 0x47, 0x52, 0x4f, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45,
	0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x47, 0x52, 0x4f, 0x4f, 0x4d, 0x49, 0x4e,
	0x47, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x44, 0x52, 0x41, 0x46, 0x54, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x47, 0x52, 0x4f, 0x4f, 0x4d,
	0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x47, 0x52, 0x4f,
	0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x04, 0x2a, 0x8b, 0x01, 0x0a, 0x18, 0x47,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x6e,
	0x64, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x2b, 0x0a, 0x27, 0x47, 0x52, 0x4f, 0x4f, 0x4d,
	0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f,
	0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x47, 0x52, 0x4f, 0x4f, 0x4d, 0x49, 0x4e, 0x47,
	0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x42, 0x59, 0x5f,
	0x53, 0x4d, 0x53, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x47, 0x52, 0x4f, 0x4f, 0x4d, 0x49, 0x4e,
	0x47, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x42, 0x59,
	0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_grooming_v1_grooming_report_enums_proto_rawDescOnce sync.Once
	file_moego_models_grooming_v1_grooming_report_enums_proto_rawDescData = file_moego_models_grooming_v1_grooming_report_enums_proto_rawDesc
)

func file_moego_models_grooming_v1_grooming_report_enums_proto_rawDescGZIP() []byte {
	file_moego_models_grooming_v1_grooming_report_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_grooming_v1_grooming_report_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_grooming_v1_grooming_report_enums_proto_rawDescData)
	})
	return file_moego_models_grooming_v1_grooming_report_enums_proto_rawDescData
}

var file_moego_models_grooming_v1_grooming_report_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_grooming_v1_grooming_report_enums_proto_goTypes = []interface{}{
	(GroomingReportStatus)(0),     // 0: moego.models.grooming.v1.GroomingReportStatus
	(GroomingReportSendMethod)(0), // 1: moego.models.grooming.v1.GroomingReportSendMethod
}
var file_moego_models_grooming_v1_grooming_report_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_grooming_v1_grooming_report_enums_proto_init() }
func file_moego_models_grooming_v1_grooming_report_enums_proto_init() {
	if File_moego_models_grooming_v1_grooming_report_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_grooming_v1_grooming_report_enums_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_grooming_v1_grooming_report_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_grooming_v1_grooming_report_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_grooming_v1_grooming_report_enums_proto_enumTypes,
	}.Build()
	File_moego_models_grooming_v1_grooming_report_enums_proto = out.File
	file_moego_models_grooming_v1_grooming_report_enums_proto_rawDesc = nil
	file_moego_models_grooming_v1_grooming_report_enums_proto_goTypes = nil
	file_moego_models_grooming_v1_grooming_report_enums_proto_depIdxs = nil
}
