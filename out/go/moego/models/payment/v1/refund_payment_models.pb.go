// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v1/refund_payment_models.proto

package paymentpb

import (
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Refund Payment. 直接由 Refund 表现状定义，未整理和加工。
// 与 Payment 对应.
type RefundPaymentModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Module.
	Module string `protobuf:"bytes,2,opt,name=module,proto3" json:"module,omitempty"`
	// Invoice ID.
	InvoiceId int64 `protobuf:"varint,3,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
	// Refund ID. Useless.
	RefundId int64 `protobuf:"varint,4,opt,name=refund_id,json=refundId,proto3" json:"refund_id,omitempty"`
	// Customer ID.
	CustomerId int64 `protobuf:"varint,5,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// Staff ID.
	StaffId int64 `protobuf:"varint,6,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// Method Name.
	Method string `protobuf:"bytes,7,opt,name=method,proto3" json:"method,omitempty"`
	// Amount.
	Amount *money.Money `protobuf:"bytes,8,opt,name=amount,proto3" json:"amount,omitempty"`
	// Status.
	Status RefundPaymentStatus `protobuf:"varint,9,opt,name=status,proto3,enum=moego.models.payment.v1.RefundPaymentStatus" json:"status,omitempty"`
	// Create time.
	CreateTime int64 `protobuf:"varint,10,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// Update Time.
	UpdateTime int64 `protobuf:"varint,11,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// Stripe refund ID.
	StripeRefundId string `protobuf:"bytes,12,opt,name=stripe_refund_id,json=stripeRefundId,proto3" json:"stripe_refund_id,omitempty"`
	// Origin payment ID.
	OriginPaymentId int64 `protobuf:"varint,13,opt,name=origin_payment_id,json=originPaymentId,proto3" json:"origin_payment_id,omitempty"`
	// Method ID.
	MethodId int64 `protobuf:"varint,14,opt,name=method_id,json=methodId,proto3" json:"method_id,omitempty"`
	// Business ID.
	BusinessId int64 `protobuf:"varint,15,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// Reason.
	Reason string `protobuf:"bytes,16,opt,name=reason,proto3" json:"reason,omitempty"`
	// Error.
	Error string `protobuf:"bytes,17,opt,name=error,proto3" json:"error,omitempty"`
	// Source payment ID.
	SourcePaymentId string `protobuf:"bytes,18,opt,name=source_payment_id,json=sourcePaymentId,proto3" json:"source_payment_id,omitempty"`
	// Grooming ID.
	GroomingId uint64 `protobuf:"varint,19,opt,name=grooming_id,json=groomingId,proto3" json:"grooming_id,omitempty"`
	// Company ID.
	CompanyId int64 `protobuf:"varint,20,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// Booking fee.
	BookingFee *money.Money `protobuf:"bytes,21,opt,name=booking_fee,json=bookingFee,proto3" json:"booking_fee,omitempty"`
	// Currency code.
	CurrencyCode string `protobuf:"bytes,22,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// Refund order payment ID.
	RefundOrderPaymentId int64 `protobuf:"varint,23,opt,name=refund_order_payment_id,json=refundOrderPaymentId,proto3" json:"refund_order_payment_id,omitempty"`
}

func (x *RefundPaymentModel) Reset() {
	*x = RefundPaymentModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_refund_payment_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundPaymentModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundPaymentModel) ProtoMessage() {}

func (x *RefundPaymentModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_refund_payment_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundPaymentModel.ProtoReflect.Descriptor instead.
func (*RefundPaymentModel) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_refund_payment_models_proto_rawDescGZIP(), []int{0}
}

func (x *RefundPaymentModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RefundPaymentModel) GetModule() string {
	if x != nil {
		return x.Module
	}
	return ""
}

func (x *RefundPaymentModel) GetInvoiceId() int64 {
	if x != nil {
		return x.InvoiceId
	}
	return 0
}

func (x *RefundPaymentModel) GetRefundId() int64 {
	if x != nil {
		return x.RefundId
	}
	return 0
}

func (x *RefundPaymentModel) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *RefundPaymentModel) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *RefundPaymentModel) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *RefundPaymentModel) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *RefundPaymentModel) GetStatus() RefundPaymentStatus {
	if x != nil {
		return x.Status
	}
	return RefundPaymentStatus_REFUND_PAYMENT_STATUS_CREATED
}

func (x *RefundPaymentModel) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *RefundPaymentModel) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *RefundPaymentModel) GetStripeRefundId() string {
	if x != nil {
		return x.StripeRefundId
	}
	return ""
}

func (x *RefundPaymentModel) GetOriginPaymentId() int64 {
	if x != nil {
		return x.OriginPaymentId
	}
	return 0
}

func (x *RefundPaymentModel) GetMethodId() int64 {
	if x != nil {
		return x.MethodId
	}
	return 0
}

func (x *RefundPaymentModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *RefundPaymentModel) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *RefundPaymentModel) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *RefundPaymentModel) GetSourcePaymentId() string {
	if x != nil {
		return x.SourcePaymentId
	}
	return ""
}

func (x *RefundPaymentModel) GetGroomingId() uint64 {
	if x != nil {
		return x.GroomingId
	}
	return 0
}

func (x *RefundPaymentModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *RefundPaymentModel) GetBookingFee() *money.Money {
	if x != nil {
		return x.BookingFee
	}
	return nil
}

func (x *RefundPaymentModel) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *RefundPaymentModel) GetRefundOrderPaymentId() int64 {
	if x != nil {
		return x.RefundOrderPaymentId
	}
	return 0
}

var File_moego_models_payment_v1_refund_payment_models_proto protoreflect.FileDescriptor

var file_moego_models_payment_v1_refund_payment_models_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x17,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbf, 0x06, 0x0a, 0x12, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x44, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x69, 0x70,
	0x65, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x49,
	0x64, 0x12, 0x2a, 0x0a, 0x11, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0b, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x66, 0x65, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x35, 0x0a, 0x17, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x14, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v1_refund_payment_models_proto_rawDescOnce sync.Once
	file_moego_models_payment_v1_refund_payment_models_proto_rawDescData = file_moego_models_payment_v1_refund_payment_models_proto_rawDesc
)

func file_moego_models_payment_v1_refund_payment_models_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v1_refund_payment_models_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v1_refund_payment_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v1_refund_payment_models_proto_rawDescData)
	})
	return file_moego_models_payment_v1_refund_payment_models_proto_rawDescData
}

var file_moego_models_payment_v1_refund_payment_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_payment_v1_refund_payment_models_proto_goTypes = []interface{}{
	(*RefundPaymentModel)(nil), // 0: moego.models.payment.v1.RefundPaymentModel
	(*money.Money)(nil),        // 1: google.type.Money
	(RefundPaymentStatus)(0),   // 2: moego.models.payment.v1.RefundPaymentStatus
}
var file_moego_models_payment_v1_refund_payment_models_proto_depIdxs = []int32{
	1, // 0: moego.models.payment.v1.RefundPaymentModel.amount:type_name -> google.type.Money
	2, // 1: moego.models.payment.v1.RefundPaymentModel.status:type_name -> moego.models.payment.v1.RefundPaymentStatus
	1, // 2: moego.models.payment.v1.RefundPaymentModel.booking_fee:type_name -> google.type.Money
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v1_refund_payment_models_proto_init() }
func file_moego_models_payment_v1_refund_payment_models_proto_init() {
	if File_moego_models_payment_v1_refund_payment_models_proto != nil {
		return
	}
	file_moego_models_payment_v1_payment_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_payment_v1_refund_payment_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundPaymentModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v1_refund_payment_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v1_refund_payment_models_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v1_refund_payment_models_proto_depIdxs,
		MessageInfos:      file_moego_models_payment_v1_refund_payment_models_proto_msgTypes,
	}.Build()
	File_moego_models_payment_v1_refund_payment_models_proto = out.File
	file_moego_models_payment_v1_refund_payment_models_proto_rawDesc = nil
	file_moego_models_payment_v1_refund_payment_models_proto_goTypes = nil
	file_moego_models_payment_v1_refund_payment_models_proto_depIdxs = nil
}
