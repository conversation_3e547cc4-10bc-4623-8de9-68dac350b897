// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v1/payment_method_models.proto

package paymentpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// payment method model, reference: https://stripe.com/docs/api/payment_methods/object
type PaymentMethodModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payment method id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// payment method type, card, paypal etc
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	// type
	//
	// Types that are assignable to PaymentType:
	//
	//	*PaymentMethodModel_Card
	PaymentType isPaymentMethodModel_PaymentType `protobuf_oneof:"payment_type"`
}

func (x *PaymentMethodModel) Reset() {
	*x = PaymentMethodModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_payment_method_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentMethodModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethodModel) ProtoMessage() {}

func (x *PaymentMethodModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_payment_method_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethodModel.ProtoReflect.Descriptor instead.
func (*PaymentMethodModel) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_payment_method_models_proto_rawDescGZIP(), []int{0}
}

func (x *PaymentMethodModel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PaymentMethodModel) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (m *PaymentMethodModel) GetPaymentType() isPaymentMethodModel_PaymentType {
	if m != nil {
		return m.PaymentType
	}
	return nil
}

func (x *PaymentMethodModel) GetCard() *Card {
	if x, ok := x.GetPaymentType().(*PaymentMethodModel_Card); ok {
		return x.Card
	}
	return nil
}

type isPaymentMethodModel_PaymentType interface {
	isPaymentMethodModel_PaymentType()
}

type PaymentMethodModel_Card struct {
	// credit card
	Card *Card `protobuf:"bytes,11,opt,name=card,proto3,oneof"`
}

func (*PaymentMethodModel_Card) isPaymentMethodModel_PaymentType() {}

// 支付方式的扩展字段，不同的支付方式扩展的字段需求不同.
// 暂时抠出来不放在 PaymentMethodModel 里面，后面重构可以考虑嵌入.
type PaymentMethodExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// By payment method 定义的结构，不共用，不混用.
	//
	// Types that are assignable to Extra:
	//
	//	*PaymentMethodExtra_Legacy_
	Extra isPaymentMethodExtra_Extra `protobuf_oneof:"extra"`
}

func (x *PaymentMethodExtra) Reset() {
	*x = PaymentMethodExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_payment_method_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentMethodExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethodExtra) ProtoMessage() {}

func (x *PaymentMethodExtra) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_payment_method_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethodExtra.ProtoReflect.Descriptor instead.
func (*PaymentMethodExtra) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_payment_method_models_proto_rawDescGZIP(), []int{1}
}

func (m *PaymentMethodExtra) GetExtra() isPaymentMethodExtra_Extra {
	if m != nil {
		return m.Extra
	}
	return nil
}

func (x *PaymentMethodExtra) GetLegacy() *PaymentMethodExtra_Legacy {
	if x, ok := x.GetExtra().(*PaymentMethodExtra_Legacy_); ok {
		return x.Legacy
	}
	return nil
}

type isPaymentMethodExtra_Extra interface {
	isPaymentMethodExtra_Extra()
}

type PaymentMethodExtra_Legacy_ struct {
	// 重构前所有的支付方式的额外字段.
	Legacy *PaymentMethodExtra_Legacy `protobuf:"bytes,1,opt,name=legacy,proto3,oneof"`
}

func (*PaymentMethodExtra_Legacy_) isPaymentMethodExtra_Extra() {}

// 退款方式的扩展字段，不同的退款方式的字段需求不同.
// 暂时抠出来不放在 PaymentMethodModel 里面，后面重构可以考虑嵌入.
type RefundPaymentMethodExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// By refund payment method 定义的结构，不共用，不混用.
	//
	// Types that are assignable to Extra:
	//
	//	*RefundPaymentMethodExtra_Legacy_
	Extra isRefundPaymentMethodExtra_Extra `protobuf_oneof:"extra"`
}

func (x *RefundPaymentMethodExtra) Reset() {
	*x = RefundPaymentMethodExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_payment_method_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundPaymentMethodExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundPaymentMethodExtra) ProtoMessage() {}

func (x *RefundPaymentMethodExtra) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_payment_method_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundPaymentMethodExtra.ProtoReflect.Descriptor instead.
func (*RefundPaymentMethodExtra) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_payment_method_models_proto_rawDescGZIP(), []int{2}
}

func (m *RefundPaymentMethodExtra) GetExtra() isRefundPaymentMethodExtra_Extra {
	if m != nil {
		return m.Extra
	}
	return nil
}

func (x *RefundPaymentMethodExtra) GetLegacy() *RefundPaymentMethodExtra_Legacy {
	if x, ok := x.GetExtra().(*RefundPaymentMethodExtra_Legacy_); ok {
		return x.Legacy
	}
	return nil
}

type isRefundPaymentMethodExtra_Extra interface {
	isRefundPaymentMethodExtra_Extra()
}

type RefundPaymentMethodExtra_Legacy_ struct {
	// 重构前所有的支付方式的额外字段.
	Legacy *RefundPaymentMethodExtra_Legacy `protobuf:"bytes,1,opt,name=legacy,proto3,oneof"`
}

func (*RefundPaymentMethodExtra_Legacy_) isRefundPaymentMethodExtra_Extra() {}

// card model
type Card struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// brand of the credit card, visa
	Brand string `protobuf:"bytes,1,opt,name=brand,proto3" json:"brand,omitempty"`
	// expiration month of the credit card
	ExpMonth int32 `protobuf:"varint,2,opt,name=exp_month,json=expMonth,proto3" json:"exp_month,omitempty"`
	// expiration year of the credit card
	ExpYear int32 `protobuf:"varint,3,opt,name=exp_year,json=expYear,proto3" json:"exp_year,omitempty"`
	// last 4 digits of the credit card
	Last4 string `protobuf:"bytes,4,opt,name=last4,proto3" json:"last4,omitempty"`
}

func (x *Card) Reset() {
	*x = Card{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_payment_method_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Card) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Card) ProtoMessage() {}

func (x *Card) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_payment_method_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Card.ProtoReflect.Descriptor instead.
func (*Card) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_payment_method_models_proto_rawDescGZIP(), []int{3}
}

func (x *Card) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *Card) GetExpMonth() int32 {
	if x != nil {
		return x.ExpMonth
	}
	return 0
}

func (x *Card) GetExpYear() int32 {
	if x != nil {
		return x.ExpYear
	}
	return 0
}

func (x *Card) GetLast4() string {
	if x != nil {
		return x.Last4
	}
	return ""
}

// payment method model, reference: https://stripe.com/docs/api/payment_methods/object
type PaymentMethodModelPublicView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payment method type, card, paypal etc
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	// type
	//
	// Types that are assignable to PaymentType:
	//
	//	*PaymentMethodModelPublicView_Card
	PaymentType isPaymentMethodModelPublicView_PaymentType `protobuf_oneof:"payment_type"`
}

func (x *PaymentMethodModelPublicView) Reset() {
	*x = PaymentMethodModelPublicView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_payment_method_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentMethodModelPublicView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethodModelPublicView) ProtoMessage() {}

func (x *PaymentMethodModelPublicView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_payment_method_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethodModelPublicView.ProtoReflect.Descriptor instead.
func (*PaymentMethodModelPublicView) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_payment_method_models_proto_rawDescGZIP(), []int{4}
}

func (x *PaymentMethodModelPublicView) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (m *PaymentMethodModelPublicView) GetPaymentType() isPaymentMethodModelPublicView_PaymentType {
	if m != nil {
		return m.PaymentType
	}
	return nil
}

func (x *PaymentMethodModelPublicView) GetCard() *Card {
	if x, ok := x.GetPaymentType().(*PaymentMethodModelPublicView_Card); ok {
		return x.Card
	}
	return nil
}

type isPaymentMethodModelPublicView_PaymentType interface {
	isPaymentMethodModelPublicView_PaymentType()
}

type PaymentMethodModelPublicView_Card struct {
	// credit card
	Card *Card `protobuf:"bytes,11,opt,name=card,proto3,oneof"`
}

func (*PaymentMethodModelPublicView_Card) isPaymentMethodModelPublicView_PaymentType() {}

// 重构前所有的支付方式的字段都混合放在 Legacy 中，便于与重构前的 Payment 结构对齐.
type PaymentMethodExtra_Legacy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Card type, 实际是 card.brand.
	CardType string `protobuf:"bytes,1,opt,name=card_type,json=cardType,proto3" json:"card_type,omitempty"`
	// Expiration month.
	ExpMonth int32 `protobuf:"varint,2,opt,name=exp_month,json=expMonth,proto3" json:"exp_month,omitempty"`
	// Expiration year.
	ExpYear int32 `protobuf:"varint,3,opt,name=exp_year,json=expYear,proto3" json:"exp_year,omitempty"`
	// Card number, 实际是 card.last4.
	CardNumber string `protobuf:"bytes,4,opt,name=card_number,json=cardNumber,proto3" json:"card_number,omitempty"`
	// Signature.
	Signature string `protobuf:"bytes,5,opt,name=signature,proto3" json:"signature,omitempty"`
	// Stripe payment method.
	StripePaymentMethod int32 `protobuf:"varint,6,opt,name=stripe_payment_method,json=stripePaymentMethod,proto3" json:"stripe_payment_method,omitempty"`
	// Card funding.
	CardFunding string `protobuf:"bytes,7,opt,name=card_funding,json=cardFunding,proto3" json:"card_funding,omitempty"`
	// Stripe client secret.
	StripeClientSecret string `protobuf:"bytes,8,opt,name=stripe_client_secret,json=stripeClientSecret,proto3" json:"stripe_client_secret,omitempty"`
	// Stripe charge ID.
	StripeChargeId string `protobuf:"bytes,9,opt,name=stripe_charge_id,json=stripeChargeId,proto3" json:"stripe_charge_id,omitempty"`
	// Stripe intent ID.
	StripeIntentId string `protobuf:"bytes,10,opt,name=stripe_intent_id,json=stripeIntentId,proto3" json:"stripe_intent_id,omitempty"`
	// Merchant.
	Merchant string `protobuf:"bytes,11,opt,name=merchant,proto3" json:"merchant,omitempty"`
	// Check number.
	CheckNumber string `protobuf:"bytes,12,opt,name=check_number,json=checkNumber,proto3" json:"check_number,omitempty"`
	// Device ID.
	DeviceId string `protobuf:"bytes,13,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// Square payment method.
	SquarePaymentMethod int32 `protobuf:"varint,14,opt,name=square_payment_method,json=squarePaymentMethod,proto3" json:"square_payment_method,omitempty"`
	// Square checkout ID.
	SquareCheckoutId string `protobuf:"bytes,15,opt,name=square_checkout_id,json=squareCheckoutId,proto3" json:"square_checkout_id,omitempty"`
	// Square customer ID.
	SquareCustomerId string `protobuf:"bytes,16,opt,name=square_customer_id,json=squareCustomerId,proto3" json:"square_customer_id,omitempty"`
}

func (x *PaymentMethodExtra_Legacy) Reset() {
	*x = PaymentMethodExtra_Legacy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_payment_method_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentMethodExtra_Legacy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethodExtra_Legacy) ProtoMessage() {}

func (x *PaymentMethodExtra_Legacy) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_payment_method_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethodExtra_Legacy.ProtoReflect.Descriptor instead.
func (*PaymentMethodExtra_Legacy) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_payment_method_models_proto_rawDescGZIP(), []int{1, 0}
}

func (x *PaymentMethodExtra_Legacy) GetCardType() string {
	if x != nil {
		return x.CardType
	}
	return ""
}

func (x *PaymentMethodExtra_Legacy) GetExpMonth() int32 {
	if x != nil {
		return x.ExpMonth
	}
	return 0
}

func (x *PaymentMethodExtra_Legacy) GetExpYear() int32 {
	if x != nil {
		return x.ExpYear
	}
	return 0
}

func (x *PaymentMethodExtra_Legacy) GetCardNumber() string {
	if x != nil {
		return x.CardNumber
	}
	return ""
}

func (x *PaymentMethodExtra_Legacy) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *PaymentMethodExtra_Legacy) GetStripePaymentMethod() int32 {
	if x != nil {
		return x.StripePaymentMethod
	}
	return 0
}

func (x *PaymentMethodExtra_Legacy) GetCardFunding() string {
	if x != nil {
		return x.CardFunding
	}
	return ""
}

func (x *PaymentMethodExtra_Legacy) GetStripeClientSecret() string {
	if x != nil {
		return x.StripeClientSecret
	}
	return ""
}

func (x *PaymentMethodExtra_Legacy) GetStripeChargeId() string {
	if x != nil {
		return x.StripeChargeId
	}
	return ""
}

func (x *PaymentMethodExtra_Legacy) GetStripeIntentId() string {
	if x != nil {
		return x.StripeIntentId
	}
	return ""
}

func (x *PaymentMethodExtra_Legacy) GetMerchant() string {
	if x != nil {
		return x.Merchant
	}
	return ""
}

func (x *PaymentMethodExtra_Legacy) GetCheckNumber() string {
	if x != nil {
		return x.CheckNumber
	}
	return ""
}

func (x *PaymentMethodExtra_Legacy) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *PaymentMethodExtra_Legacy) GetSquarePaymentMethod() int32 {
	if x != nil {
		return x.SquarePaymentMethod
	}
	return 0
}

func (x *PaymentMethodExtra_Legacy) GetSquareCheckoutId() string {
	if x != nil {
		return x.SquareCheckoutId
	}
	return ""
}

func (x *PaymentMethodExtra_Legacy) GetSquareCustomerId() string {
	if x != nil {
		return x.SquareCustomerId
	}
	return ""
}

// 重构前所有的支付方式的字段都混合放在 Legacy 中，便于与重构前的 RefundPayment 结构对齐.
type RefundPaymentMethodExtra_Legacy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Stripe refund ID.
	StripeRefundId string `protobuf:"bytes,1,opt,name=stripe_refund_id,json=stripeRefundId,proto3" json:"stripe_refund_id,omitempty"`
	// Source payment id.
	// 目前是 Payment 中 stripe_intent_id.
	SourcePaymentId string `protobuf:"bytes,2,opt,name=source_payment_id,json=sourcePaymentId,proto3" json:"source_payment_id,omitempty"`
}

func (x *RefundPaymentMethodExtra_Legacy) Reset() {
	*x = RefundPaymentMethodExtra_Legacy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_payment_method_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundPaymentMethodExtra_Legacy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundPaymentMethodExtra_Legacy) ProtoMessage() {}

func (x *RefundPaymentMethodExtra_Legacy) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_payment_method_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundPaymentMethodExtra_Legacy.ProtoReflect.Descriptor instead.
func (*RefundPaymentMethodExtra_Legacy) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_payment_method_models_proto_rawDescGZIP(), []int{2, 0}
}

func (x *RefundPaymentMethodExtra_Legacy) GetStripeRefundId() string {
	if x != nil {
		return x.StripeRefundId
	}
	return ""
}

func (x *RefundPaymentMethodExtra_Legacy) GetSourcePaymentId() string {
	if x != nil {
		return x.SourcePaymentId
	}
	return ""
}

var File_moego_models_payment_v1_payment_method_models_proto protoreflect.FileDescriptor

var file_moego_models_payment_v1_payment_method_models_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x82, 0x01, 0x0a, 0x12, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x33, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x48,
	0x00, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x42, 0x13, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xd3, 0x05, 0x0a,
	0x12, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x45, 0x78,
	0x74, 0x72, 0x61, 0x12, 0x4c, 0x0a, 0x06, 0x6c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x2e, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x48, 0x00, 0x52, 0x06, 0x6c, 0x65, 0x67, 0x61, 0x63,
	0x79, 0x1a, 0xe5, 0x04, 0x0a, 0x06, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70,
	0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65, 0x78,
	0x70, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x5f, 0x79, 0x65,
	0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x78, 0x70, 0x59, 0x65, 0x61,
	0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x12, 0x32, 0x0a, 0x15, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x13, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x66, 0x75, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x64,
	0x46, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x74, 0x72, 0x69, 0x70,
	0x65, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x72,
	0x69, 0x70, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x69, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73,
	0x74, 0x72, 0x69, 0x70, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x71, 0x75,
	0x61, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x2c, 0x0a,
	0x12, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x71, 0x75, 0x61, 0x72,
	0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x6f, 0x75, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x73,
	0x71, 0x75, 0x61, 0x72, 0x65, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x42, 0x07, 0x0a, 0x05, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x22, 0xd7, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12,
	0x52, 0x0a, 0x06, 0x6c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x2e, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x48, 0x00, 0x52, 0x06, 0x6c, 0x65, 0x67,
	0x61, 0x63, 0x79, 0x1a, 0x5e, 0x0a, 0x06, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x12, 0x28, 0x0a,
	0x10, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x42, 0x07, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x6a, 0x0a, 0x04,
	0x43, 0x61, 0x72, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78,
	0x70, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65,
	0x78, 0x70, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x5f, 0x79,
	0x65, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x78, 0x70, 0x59, 0x65,
	0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x73, 0x74, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6c, 0x61, 0x73, 0x74, 0x34, 0x22, 0x7c, 0x0a, 0x1c, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x75,
	0x62, 0x6c, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x33, 0x0a, 0x04,
	0x63, 0x61, 0x72, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x48, 0x00, 0x52, 0x04, 0x63, 0x61, 0x72,
	0x64, 0x42, 0x13, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v1_payment_method_models_proto_rawDescOnce sync.Once
	file_moego_models_payment_v1_payment_method_models_proto_rawDescData = file_moego_models_payment_v1_payment_method_models_proto_rawDesc
)

func file_moego_models_payment_v1_payment_method_models_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v1_payment_method_models_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v1_payment_method_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v1_payment_method_models_proto_rawDescData)
	})
	return file_moego_models_payment_v1_payment_method_models_proto_rawDescData
}

var file_moego_models_payment_v1_payment_method_models_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_models_payment_v1_payment_method_models_proto_goTypes = []interface{}{
	(*PaymentMethodModel)(nil),              // 0: moego.models.payment.v1.PaymentMethodModel
	(*PaymentMethodExtra)(nil),              // 1: moego.models.payment.v1.PaymentMethodExtra
	(*RefundPaymentMethodExtra)(nil),        // 2: moego.models.payment.v1.RefundPaymentMethodExtra
	(*Card)(nil),                            // 3: moego.models.payment.v1.Card
	(*PaymentMethodModelPublicView)(nil),    // 4: moego.models.payment.v1.PaymentMethodModelPublicView
	(*PaymentMethodExtra_Legacy)(nil),       // 5: moego.models.payment.v1.PaymentMethodExtra.Legacy
	(*RefundPaymentMethodExtra_Legacy)(nil), // 6: moego.models.payment.v1.RefundPaymentMethodExtra.Legacy
}
var file_moego_models_payment_v1_payment_method_models_proto_depIdxs = []int32{
	3, // 0: moego.models.payment.v1.PaymentMethodModel.card:type_name -> moego.models.payment.v1.Card
	5, // 1: moego.models.payment.v1.PaymentMethodExtra.legacy:type_name -> moego.models.payment.v1.PaymentMethodExtra.Legacy
	6, // 2: moego.models.payment.v1.RefundPaymentMethodExtra.legacy:type_name -> moego.models.payment.v1.RefundPaymentMethodExtra.Legacy
	3, // 3: moego.models.payment.v1.PaymentMethodModelPublicView.card:type_name -> moego.models.payment.v1.Card
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v1_payment_method_models_proto_init() }
func file_moego_models_payment_v1_payment_method_models_proto_init() {
	if File_moego_models_payment_v1_payment_method_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_payment_v1_payment_method_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentMethodModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v1_payment_method_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentMethodExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v1_payment_method_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundPaymentMethodExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v1_payment_method_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Card); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v1_payment_method_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentMethodModelPublicView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v1_payment_method_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentMethodExtra_Legacy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v1_payment_method_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundPaymentMethodExtra_Legacy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_payment_v1_payment_method_models_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*PaymentMethodModel_Card)(nil),
	}
	file_moego_models_payment_v1_payment_method_models_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*PaymentMethodExtra_Legacy_)(nil),
	}
	file_moego_models_payment_v1_payment_method_models_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*RefundPaymentMethodExtra_Legacy_)(nil),
	}
	file_moego_models_payment_v1_payment_method_models_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*PaymentMethodModelPublicView_Card)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v1_payment_method_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v1_payment_method_models_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v1_payment_method_models_proto_depIdxs,
		MessageInfos:      file_moego_models_payment_v1_payment_method_models_proto_msgTypes,
	}.Build()
	File_moego_models_payment_v1_payment_method_models_proto = out.File
	file_moego_models_payment_v1_payment_method_models_proto_rawDesc = nil
	file_moego_models_payment_v1_payment_method_models_proto_goTypes = nil
	file_moego_models_payment_v1_payment_method_models_proto_depIdxs = nil
}
