// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v1/deposit_enums.proto

package paymentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// deposit status
type DepositStatus int32

const (
	// unspecified
	DepositStatus_DEPOSIT_STATUS_UNSPECIFIED DepositStatus = 0
	// processing
	DepositStatus_DEPOSIT_STATUS_PROCESSING DepositStatus = 1
	// require payment method
	DepositStatus_DEPOSIT_STATUS_REQUIRE_PAYMENT_METHOD DepositStatus = 2
	// require confirm
	DepositStatus_DEPOSIT_STATUS_REQUIRE_CONFIRM DepositStatus = 3
	// require capture
	DepositStatus_DEPOSIT_STATUS_REQUIRE_CAPTURE DepositStatus = 4
	// processing capture
	DepositStatus_DEPOSIT_STATUS_PROCESSING_CAPTURE DepositStatus = 5
	// paid
	DepositStatus_DEPOSIT_STATUS_PAID DepositStatus = 6
	// refunded
	DepositStatus_DEPOSIT_STATUS_REFUNDED DepositStatus = 7
	// cancel
	DepositStatus_DEPOSIT_STATUS_CANCEL DepositStatus = 8
	// failed, database mapping is -1
	DepositStatus_DEPOSIT_STATUS_FAILED DepositStatus = 9
)

// Enum value maps for DepositStatus.
var (
	DepositStatus_name = map[int32]string{
		0: "DEPOSIT_STATUS_UNSPECIFIED",
		1: "DEPOSIT_STATUS_PROCESSING",
		2: "DEPOSIT_STATUS_REQUIRE_PAYMENT_METHOD",
		3: "DEPOSIT_STATUS_REQUIRE_CONFIRM",
		4: "DEPOSIT_STATUS_REQUIRE_CAPTURE",
		5: "DEPOSIT_STATUS_PROCESSING_CAPTURE",
		6: "DEPOSIT_STATUS_PAID",
		7: "DEPOSIT_STATUS_REFUNDED",
		8: "DEPOSIT_STATUS_CANCEL",
		9: "DEPOSIT_STATUS_FAILED",
	}
	DepositStatus_value = map[string]int32{
		"DEPOSIT_STATUS_UNSPECIFIED":            0,
		"DEPOSIT_STATUS_PROCESSING":             1,
		"DEPOSIT_STATUS_REQUIRE_PAYMENT_METHOD": 2,
		"DEPOSIT_STATUS_REQUIRE_CONFIRM":        3,
		"DEPOSIT_STATUS_REQUIRE_CAPTURE":        4,
		"DEPOSIT_STATUS_PROCESSING_CAPTURE":     5,
		"DEPOSIT_STATUS_PAID":                   6,
		"DEPOSIT_STATUS_REFUNDED":               7,
		"DEPOSIT_STATUS_CANCEL":                 8,
		"DEPOSIT_STATUS_FAILED":                 9,
	}
)

func (x DepositStatus) Enum() *DepositStatus {
	p := new(DepositStatus)
	*p = x
	return p
}

func (x DepositStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DepositStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v1_deposit_enums_proto_enumTypes[0].Descriptor()
}

func (DepositStatus) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v1_deposit_enums_proto_enumTypes[0]
}

func (x DepositStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DepositStatus.Descriptor instead.
func (DepositStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_deposit_enums_proto_rawDescGZIP(), []int{0}
}

var File_moego_models_payment_v1_deposit_enums_proto protoreflect.FileDescriptor

var file_moego_models_payment_v1_deposit_enums_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2a, 0xd4, 0x02, 0x0a, 0x0d, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x45, 0x50, 0x4f,
	0x53, 0x49, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x45, 0x50, 0x4f,
	0x53, 0x49, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45,
	0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x44, 0x45, 0x50, 0x4f, 0x53,
	0x49, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52,
	0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44,
	0x10, 0x02, 0x12, 0x22, 0x0a, 0x1e, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x5f, 0x43, 0x4f, 0x4e,
	0x46, 0x49, 0x52, 0x4d, 0x10, 0x03, 0x12, 0x22, 0x0a, 0x1e, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45,
	0x5f, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x10, 0x04, 0x12, 0x25, 0x0a, 0x21, 0x44, 0x45,
	0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f,
	0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x10,
	0x05, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x49, 0x44, 0x10, 0x06, 0x12, 0x1b, 0x0a, 0x17, 0x44, 0x45,
	0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x46,
	0x55, 0x4e, 0x44, 0x45, 0x44, 0x10, 0x07, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x45, 0x50, 0x4f, 0x53,
	0x49, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c,
	0x10, 0x08, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x09, 0x42, 0x7b, 0x0a,
	0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_models_payment_v1_deposit_enums_proto_rawDescOnce sync.Once
	file_moego_models_payment_v1_deposit_enums_proto_rawDescData = file_moego_models_payment_v1_deposit_enums_proto_rawDesc
)

func file_moego_models_payment_v1_deposit_enums_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v1_deposit_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v1_deposit_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v1_deposit_enums_proto_rawDescData)
	})
	return file_moego_models_payment_v1_deposit_enums_proto_rawDescData
}

var file_moego_models_payment_v1_deposit_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_payment_v1_deposit_enums_proto_goTypes = []interface{}{
	(DepositStatus)(0), // 0: moego.models.payment.v1.DepositStatus
}
var file_moego_models_payment_v1_deposit_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v1_deposit_enums_proto_init() }
func file_moego_models_payment_v1_deposit_enums_proto_init() {
	if File_moego_models_payment_v1_deposit_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v1_deposit_enums_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v1_deposit_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v1_deposit_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_payment_v1_deposit_enums_proto_enumTypes,
	}.Build()
	File_moego_models_payment_v1_deposit_enums_proto = out.File
	file_moego_models_payment_v1_deposit_enums_proto_rawDesc = nil
	file_moego_models_payment_v1_deposit_enums_proto_goTypes = nil
	file_moego_models_payment_v1_deposit_enums_proto_depIdxs = nil
}
