// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v1/ach_models.proto

package paymentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ACH view
type ACHView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the ach on file
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// bank name
	BankName string `protobuf:"bytes,2,opt,name=bank_name,json=bankName,proto3" json:"bank_name,omitempty"`
	// last4
	Last4 string `protobuf:"bytes,3,opt,name=last4,proto3" json:"last4,omitempty"`
}

func (x *ACHView) Reset() {
	*x = ACHView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_ach_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ACHView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ACHView) ProtoMessage() {}

func (x *ACHView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_ach_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ACHView.ProtoReflect.Descriptor instead.
func (*ACHView) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_ach_models_proto_rawDescGZIP(), []int{0}
}

func (x *ACHView) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ACHView) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

func (x *ACHView) GetLast4() string {
	if x != nil {
		return x.Last4
	}
	return ""
}

var File_moego_models_payment_v1_ach_models_proto protoreflect.FileDescriptor

var file_moego_models_payment_v1_ach_models_proto_rawDesc = []byte{
	0x0a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x68, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x22, 0x4c, 0x0a, 0x07, 0x41, 0x43, 0x48, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x61, 0x73, 0x74, 0x34, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x73, 0x74,
	0x34, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v1_ach_models_proto_rawDescOnce sync.Once
	file_moego_models_payment_v1_ach_models_proto_rawDescData = file_moego_models_payment_v1_ach_models_proto_rawDesc
)

func file_moego_models_payment_v1_ach_models_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v1_ach_models_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v1_ach_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v1_ach_models_proto_rawDescData)
	})
	return file_moego_models_payment_v1_ach_models_proto_rawDescData
}

var file_moego_models_payment_v1_ach_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_payment_v1_ach_models_proto_goTypes = []interface{}{
	(*ACHView)(nil), // 0: moego.models.payment.v1.ACHView
}
var file_moego_models_payment_v1_ach_models_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v1_ach_models_proto_init() }
func file_moego_models_payment_v1_ach_models_proto_init() {
	if File_moego_models_payment_v1_ach_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_payment_v1_ach_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ACHView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v1_ach_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v1_ach_models_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v1_ach_models_proto_depIdxs,
		MessageInfos:      file_moego_models_payment_v1_ach_models_proto_msgTypes,
	}.Build()
	File_moego_models_payment_v1_ach_models_proto = out.File
	file_moego_models_payment_v1_ach_models_proto_rawDesc = nil
	file_moego_models_payment_v1_ach_models_proto_goTypes = nil
	file_moego_models_payment_v1_ach_models_proto_depIdxs = nil
}
