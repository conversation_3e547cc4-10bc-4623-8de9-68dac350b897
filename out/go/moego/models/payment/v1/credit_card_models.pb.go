// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v1/credit_card_models.proto

package paymentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// credit card model
type CreditCardModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the credit card
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// brand of the credit card, visa
	CardBrand string `protobuf:"bytes,2,opt,name=card_brand,json=cardBrand,proto3" json:"card_brand,omitempty"`
	// expiration month of the credit card
	ExpMonth int32 `protobuf:"varint,3,opt,name=exp_month,json=expMonth,proto3" json:"exp_month,omitempty"`
	// expiration year of the credit card
	ExpYear int32 `protobuf:"varint,4,opt,name=exp_year,json=expYear,proto3" json:"exp_year,omitempty"`
	// last 4 digits of the credit card
	Last4 string `protobuf:"bytes,5,opt,name=last4,proto3" json:"last4,omitempty"`
	// name of the credit card holder
	CardHolderName string `protobuf:"bytes,6,opt,name=card_holder_name,json=cardHolderName,proto3" json:"card_holder_name,omitempty"`
	// type of the credit card, credit or debit
	CardType string `protobuf:"bytes,7,opt,name=card_type,json=cardType,proto3" json:"card_type,omitempty"`
	// expired flag of the credit card
	IsExpired bool `protobuf:"varint,8,opt,name=is_expired,json=isExpired,proto3" json:"is_expired,omitempty"`
}

func (x *CreditCardModel) Reset() {
	*x = CreditCardModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_credit_card_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditCardModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditCardModel) ProtoMessage() {}

func (x *CreditCardModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_credit_card_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditCardModel.ProtoReflect.Descriptor instead.
func (*CreditCardModel) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_credit_card_models_proto_rawDescGZIP(), []int{0}
}

func (x *CreditCardModel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CreditCardModel) GetCardBrand() string {
	if x != nil {
		return x.CardBrand
	}
	return ""
}

func (x *CreditCardModel) GetExpMonth() int32 {
	if x != nil {
		return x.ExpMonth
	}
	return 0
}

func (x *CreditCardModel) GetExpYear() int32 {
	if x != nil {
		return x.ExpYear
	}
	return 0
}

func (x *CreditCardModel) GetLast4() string {
	if x != nil {
		return x.Last4
	}
	return ""
}

func (x *CreditCardModel) GetCardHolderName() string {
	if x != nil {
		return x.CardHolderName
	}
	return ""
}

func (x *CreditCardModel) GetCardType() string {
	if x != nil {
		return x.CardType
	}
	return ""
}

func (x *CreditCardModel) GetIsExpired() bool {
	if x != nil {
		return x.IsExpired
	}
	return false
}

// credit card model public view
type CreditCardModelPublicView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the credit card
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// brand of the credit card, visa
	CardBrand string `protobuf:"bytes,2,opt,name=card_brand,json=cardBrand,proto3" json:"card_brand,omitempty"`
	// expiration month of the credit card
	ExpMonth int32 `protobuf:"varint,3,opt,name=exp_month,json=expMonth,proto3" json:"exp_month,omitempty"`
	// expiration year of the credit card
	ExpYear int32 `protobuf:"varint,4,opt,name=exp_year,json=expYear,proto3" json:"exp_year,omitempty"`
	// last 4 digits of the credit card
	Last4 string `protobuf:"bytes,5,opt,name=last4,proto3" json:"last4,omitempty"`
	// name of the credit card holder
	CardHolderName string `protobuf:"bytes,6,opt,name=card_holder_name,json=cardHolderName,proto3" json:"card_holder_name,omitempty"`
	// type of the credit card, credit or debit
	CardType string `protobuf:"bytes,7,opt,name=card_type,json=cardType,proto3" json:"card_type,omitempty"`
}

func (x *CreditCardModelPublicView) Reset() {
	*x = CreditCardModelPublicView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_credit_card_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditCardModelPublicView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditCardModelPublicView) ProtoMessage() {}

func (x *CreditCardModelPublicView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_credit_card_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditCardModelPublicView.ProtoReflect.Descriptor instead.
func (*CreditCardModelPublicView) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_credit_card_models_proto_rawDescGZIP(), []int{1}
}

func (x *CreditCardModelPublicView) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CreditCardModelPublicView) GetCardBrand() string {
	if x != nil {
		return x.CardBrand
	}
	return ""
}

func (x *CreditCardModelPublicView) GetExpMonth() int32 {
	if x != nil {
		return x.ExpMonth
	}
	return 0
}

func (x *CreditCardModelPublicView) GetExpYear() int32 {
	if x != nil {
		return x.ExpYear
	}
	return 0
}

func (x *CreditCardModelPublicView) GetLast4() string {
	if x != nil {
		return x.Last4
	}
	return ""
}

func (x *CreditCardModelPublicView) GetCardHolderName() string {
	if x != nil {
		return x.CardHolderName
	}
	return ""
}

func (x *CreditCardModelPublicView) GetCardType() string {
	if x != nil {
		return x.CardType
	}
	return ""
}

var File_moego_models_payment_v1_credit_card_models_proto protoreflect.FileDescriptor

var file_moego_models_payment_v1_credit_card_models_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x22, 0xf4, 0x01, 0x0a, 0x0f,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x61, 0x72, 0x64, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x65, 0x78, 0x70, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x65, 0x78, 0x70, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x65,
	0x78, 0x70, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65,
	0x78, 0x70, 0x59, 0x65, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x73, 0x74, 0x34, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x73, 0x74, 0x34, 0x12, 0x28, 0x0a, 0x10,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x61, 0x72, 0x64, 0x48, 0x6f, 0x6c, 0x64,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x64, 0x22, 0xdf, 0x01, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x61, 0x72, 0x64, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x65, 0x78, 0x70, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x78, 0x70, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x65, 0x78, 0x70, 0x59, 0x65, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x73, 0x74, 0x34,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x73, 0x74, 0x34, 0x12, 0x28, 0x0a,
	0x10, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x61, 0x72, 0x64, 0x48, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v1_credit_card_models_proto_rawDescOnce sync.Once
	file_moego_models_payment_v1_credit_card_models_proto_rawDescData = file_moego_models_payment_v1_credit_card_models_proto_rawDesc
)

func file_moego_models_payment_v1_credit_card_models_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v1_credit_card_models_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v1_credit_card_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v1_credit_card_models_proto_rawDescData)
	})
	return file_moego_models_payment_v1_credit_card_models_proto_rawDescData
}

var file_moego_models_payment_v1_credit_card_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_payment_v1_credit_card_models_proto_goTypes = []interface{}{
	(*CreditCardModel)(nil),           // 0: moego.models.payment.v1.CreditCardModel
	(*CreditCardModelPublicView)(nil), // 1: moego.models.payment.v1.CreditCardModelPublicView
}
var file_moego_models_payment_v1_credit_card_models_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v1_credit_card_models_proto_init() }
func file_moego_models_payment_v1_credit_card_models_proto_init() {
	if File_moego_models_payment_v1_credit_card_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_payment_v1_credit_card_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditCardModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v1_credit_card_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditCardModelPublicView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v1_credit_card_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v1_credit_card_models_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v1_credit_card_models_proto_depIdxs,
		MessageInfos:      file_moego_models_payment_v1_credit_card_models_proto_msgTypes,
	}.Build()
	File_moego_models_payment_v1_credit_card_models_proto = out.File
	file_moego_models_payment_v1_credit_card_models_proto_rawDesc = nil
	file_moego_models_payment_v1_credit_card_models_proto_goTypes = nil
	file_moego_models_payment_v1_credit_card_models_proto_depIdxs = nil
}
