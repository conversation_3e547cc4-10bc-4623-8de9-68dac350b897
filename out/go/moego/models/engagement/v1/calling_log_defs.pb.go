// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/engagement/v1/calling_log_defs.proto

package engagementpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Trend type
type Trend int32

const (
	// Unspecified trend
	Trend_TREND_UNSPECIFIED Trend = 0
	// benefit matrix type
	Trend_BENEFIT Trend = 1
	// harmful matrix type
	Trend_HARMFUL Trend = 2
	// neutral matrix type
	Trend_NEUTRAL Trend = 3
)

// Enum value maps for Trend.
var (
	Trend_name = map[int32]string{
		0: "TREND_UNSPECIFIED",
		1: "BENEFIT",
		2: "HARMFUL",
		3: "NEUTRAL",
	}
	Trend_value = map[string]int32{
		"TREND_UNSPECIFIED": 0,
		"BENEFIT":           1,
		"HARMFUL":           2,
		"NEUTRAL":           3,
	}
)

func (x Trend) Enum() *Trend {
	p := new(Trend)
	*p = x
	return p
}

func (x Trend) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Trend) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_engagement_v1_calling_log_defs_proto_enumTypes[0].Descriptor()
}

func (Trend) Type() protoreflect.EnumType {
	return &file_moego_models_engagement_v1_calling_log_defs_proto_enumTypes[0]
}

func (x Trend) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Trend.Descriptor instead.
func (Trend) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_engagement_v1_calling_log_defs_proto_rawDescGZIP(), []int{0}
}

// create calling log definition
type CreateCallingLogDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// client id
	ClientId int64 `protobuf:"varint,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// summary
	Summary *string `protobuf:"bytes,5,opt,name=summary,proto3,oneof" json:"summary,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,6,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// record transcript
	RecordTranscript *string `protobuf:"bytes,7,opt,name=record_transcript,json=recordTranscript,proto3,oneof" json:"record_transcript,omitempty"`
	// record ai summary
	RecordAiSummary *string `protobuf:"bytes,9,opt,name=record_ai_summary,json=recordAiSummary,proto3,oneof" json:"record_ai_summary,omitempty"`
	// record url
	RecordUrl *string `protobuf:"bytes,10,opt,name=record_url,json=recordUrl,proto3,oneof" json:"record_url,omitempty"`
	// record type
	RecordType RecordType `protobuf:"varint,11,opt,name=record_type,json=recordType,proto3,enum=moego.models.engagement.v1.RecordType" json:"record_type,omitempty"`
	// direction
	Direction CallingDirection `protobuf:"varint,12,opt,name=direction,proto3,enum=moego.models.engagement.v1.CallingDirection" json:"direction,omitempty"`
	// status
	Status Status `protobuf:"varint,13,opt,name=status,proto3,enum=moego.models.engagement.v1.Status" json:"status,omitempty"`
	// categories
	Categories []Category `protobuf:"varint,14,rep,packed,name=categories,proto3,enum=moego.models.engagement.v1.Category" json:"categories,omitempty"`
	// init time
	InitTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=init_time,json=initTime,proto3" json:"init_time,omitempty"`
	// record duration
	RecordDuration *durationpb.Duration `protobuf:"bytes,16,opt,name=record_duration,json=recordDuration,proto3,oneof" json:"record_duration,omitempty"`
	// twilio call sid
	TwilioCallSid string `protobuf:"bytes,17,opt,name=twilio_call_sid,json=twilioCallSid,proto3" json:"twilio_call_sid,omitempty"`
}

func (x *CreateCallingLogDef) Reset() {
	*x = CreateCallingLogDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_engagement_v1_calling_log_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCallingLogDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCallingLogDef) ProtoMessage() {}

func (x *CreateCallingLogDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_engagement_v1_calling_log_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCallingLogDef.ProtoReflect.Descriptor instead.
func (*CreateCallingLogDef) Descriptor() ([]byte, []int) {
	return file_moego_models_engagement_v1_calling_log_defs_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCallingLogDef) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateCallingLogDef) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateCallingLogDef) GetClientId() int64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *CreateCallingLogDef) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *CreateCallingLogDef) GetSummary() string {
	if x != nil && x.Summary != nil {
		return *x.Summary
	}
	return ""
}

func (x *CreateCallingLogDef) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *CreateCallingLogDef) GetRecordTranscript() string {
	if x != nil && x.RecordTranscript != nil {
		return *x.RecordTranscript
	}
	return ""
}

func (x *CreateCallingLogDef) GetRecordAiSummary() string {
	if x != nil && x.RecordAiSummary != nil {
		return *x.RecordAiSummary
	}
	return ""
}

func (x *CreateCallingLogDef) GetRecordUrl() string {
	if x != nil && x.RecordUrl != nil {
		return *x.RecordUrl
	}
	return ""
}

func (x *CreateCallingLogDef) GetRecordType() RecordType {
	if x != nil {
		return x.RecordType
	}
	return RecordType_RECORD_TYPE_UNSPECIFIED
}

func (x *CreateCallingLogDef) GetDirection() CallingDirection {
	if x != nil {
		return x.Direction
	}
	return CallingDirection_CALLING_DIRECTION_UNSPECIFIED
}

func (x *CreateCallingLogDef) GetStatus() Status {
	if x != nil {
		return x.Status
	}
	return Status_CALLING_LOG_STATUS_UNSPECIFIED
}

func (x *CreateCallingLogDef) GetCategories() []Category {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *CreateCallingLogDef) GetInitTime() *timestamppb.Timestamp {
	if x != nil {
		return x.InitTime
	}
	return nil
}

func (x *CreateCallingLogDef) GetRecordDuration() *durationpb.Duration {
	if x != nil {
		return x.RecordDuration
	}
	return nil
}

func (x *CreateCallingLogDef) GetTwilioCallSid() string {
	if x != nil {
		return x.TwilioCallSid
	}
	return ""
}

// update calling log definition
type UpdateCallingLogDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client id
	ClientId *int64 `protobuf:"varint,1,opt,name=client_id,json=clientId,proto3,oneof" json:"client_id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// summary
	Summary *string `protobuf:"bytes,3,opt,name=summary,proto3,oneof" json:"summary,omitempty"`
	// phone number
	PhoneNumber *string `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3,oneof" json:"phone_number,omitempty"`
	// record transcript
	RecordTranscript *string `protobuf:"bytes,5,opt,name=record_transcript,json=recordTranscript,proto3,oneof" json:"record_transcript,omitempty"`
	// record ai summary
	RecordAiSummary *string `protobuf:"bytes,6,opt,name=record_ai_summary,json=recordAiSummary,proto3,oneof" json:"record_ai_summary,omitempty"`
	// record url
	RecordUrl *string `protobuf:"bytes,7,opt,name=record_url,json=recordUrl,proto3,oneof" json:"record_url,omitempty"`
	// record type
	RecordType *RecordType `protobuf:"varint,8,opt,name=record_type,json=recordType,proto3,enum=moego.models.engagement.v1.RecordType,oneof" json:"record_type,omitempty"`
	// id of the engagement
	Direction *CallingDirection `protobuf:"varint,9,opt,name=direction,proto3,enum=moego.models.engagement.v1.CallingDirection,oneof" json:"direction,omitempty"`
	// status
	Status *Status `protobuf:"varint,10,opt,name=status,proto3,enum=moego.models.engagement.v1.Status,oneof" json:"status,omitempty"`
	// categories
	Categories []Category `protobuf:"varint,11,rep,packed,name=categories,proto3,enum=moego.models.engagement.v1.Category" json:"categories,omitempty"`
	// init time
	InitTime *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=init_time,json=initTime,proto3,oneof" json:"init_time,omitempty"`
	// duration
	RecordDuration *durationpb.Duration `protobuf:"bytes,13,opt,name=record_duration,json=recordDuration,proto3,oneof" json:"record_duration,omitempty"`
}

func (x *UpdateCallingLogDef) Reset() {
	*x = UpdateCallingLogDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_engagement_v1_calling_log_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCallingLogDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCallingLogDef) ProtoMessage() {}

func (x *UpdateCallingLogDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_engagement_v1_calling_log_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCallingLogDef.ProtoReflect.Descriptor instead.
func (*UpdateCallingLogDef) Descriptor() ([]byte, []int) {
	return file_moego_models_engagement_v1_calling_log_defs_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateCallingLogDef) GetClientId() int64 {
	if x != nil && x.ClientId != nil {
		return *x.ClientId
	}
	return 0
}

func (x *UpdateCallingLogDef) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *UpdateCallingLogDef) GetSummary() string {
	if x != nil && x.Summary != nil {
		return *x.Summary
	}
	return ""
}

func (x *UpdateCallingLogDef) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

func (x *UpdateCallingLogDef) GetRecordTranscript() string {
	if x != nil && x.RecordTranscript != nil {
		return *x.RecordTranscript
	}
	return ""
}

func (x *UpdateCallingLogDef) GetRecordAiSummary() string {
	if x != nil && x.RecordAiSummary != nil {
		return *x.RecordAiSummary
	}
	return ""
}

func (x *UpdateCallingLogDef) GetRecordUrl() string {
	if x != nil && x.RecordUrl != nil {
		return *x.RecordUrl
	}
	return ""
}

func (x *UpdateCallingLogDef) GetRecordType() RecordType {
	if x != nil && x.RecordType != nil {
		return *x.RecordType
	}
	return RecordType_RECORD_TYPE_UNSPECIFIED
}

func (x *UpdateCallingLogDef) GetDirection() CallingDirection {
	if x != nil && x.Direction != nil {
		return *x.Direction
	}
	return CallingDirection_CALLING_DIRECTION_UNSPECIFIED
}

func (x *UpdateCallingLogDef) GetStatus() Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return Status_CALLING_LOG_STATUS_UNSPECIFIED
}

func (x *UpdateCallingLogDef) GetCategories() []Category {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *UpdateCallingLogDef) GetInitTime() *timestamppb.Timestamp {
	if x != nil {
		return x.InitTime
	}
	return nil
}

func (x *UpdateCallingLogDef) GetRecordDuration() *durationpb.Duration {
	if x != nil {
		return x.RecordDuration
	}
	return nil
}

// indicator
type IndicatorDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// value
	Value float64 `protobuf:"fixed64,1,opt,name=value,proto3" json:"value,omitempty"`
	// percent
	Percent float64 `protobuf:"fixed64,2,opt,name=percent,proto3" json:"percent,omitempty"`
	// direction
	Trend Trend `protobuf:"varint,3,opt,name=trend,proto3,enum=moego.models.engagement.v1.Trend" json:"trend,omitempty"`
}

func (x *IndicatorDef) Reset() {
	*x = IndicatorDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_engagement_v1_calling_log_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IndicatorDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IndicatorDef) ProtoMessage() {}

func (x *IndicatorDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_engagement_v1_calling_log_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IndicatorDef.ProtoReflect.Descriptor instead.
func (*IndicatorDef) Descriptor() ([]byte, []int) {
	return file_moego_models_engagement_v1_calling_log_defs_proto_rawDescGZIP(), []int{2}
}

func (x *IndicatorDef) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *IndicatorDef) GetPercent() float64 {
	if x != nil {
		return x.Percent
	}
	return 0
}

func (x *IndicatorDef) GetTrend() Trend {
	if x != nil {
		return x.Trend
	}
	return Trend_TREND_UNSPECIFIED
}

var File_moego_models_engagement_v1_calling_log_defs_proto protoreflect.FileDescriptor

var file_moego_models_engagement_v1_calling_log_defs_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a,
	0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76,
	0x31, 0x2f, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcc, 0x07,
	0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c,
	0x6f, 0x67, 0x44, 0x65, 0x66, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x27, 0x0a,
	0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x10, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x11, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x5f, 0x61, 0x69, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x0f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x41,
	0x69, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x04, 0x52, 0x09, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12,
	0x53, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20,
	0x00, 0x52, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x44, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0a,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x37, 0x0a, 0x09, 0x69, 0x6e,
	0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x69, 0x6e, 0x69, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x47, 0x0a, 0x0f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x05, 0x52, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0f,
	0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x69, 0x64, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x43, 0x61, 0x6c,
	0x6c, 0x53, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x42, 0x14, 0x0a,
	0x12, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x61,
	0x69, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xc6, 0x07, 0x0a,
	0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4c, 0x6f,
	0x67, 0x44, 0x65, 0x66, 0x12, 0x29, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x48, 0x00, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x27, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x07, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x07, 0x73, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52,
	0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12,
	0x30, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x10, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x88, 0x01,
	0x01, 0x12, 0x2f, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x61, 0x69, 0x5f, 0x73,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x0f,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x41, 0x69, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x88,
	0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x09, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x58, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48,
	0x07, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x5b, 0x0a, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x08, 0x52,
	0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x44, 0x0a, 0x0a, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x12, 0x3c, 0x0a, 0x09, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48,
	0x0a, 0x52, 0x08, 0x69, 0x6e, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x47,
	0x0a, 0x0f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x0b, 0x52, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x42, 0x0f,
	0x0a, 0x0d, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42,
	0x14, 0x0a, 0x12, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x5f, 0x61, 0x69, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x77, 0x0a, 0x0c, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x6f, 0x72, 0x44, 0x65, 0x66, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x70, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x52, 0x05, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x2a, 0x45,
	0x0a, 0x05, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52, 0x45, 0x4e, 0x44,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x48,
	0x41, 0x52, 0x4d, 0x46, 0x55, 0x4c, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x45, 0x55, 0x54,
	0x52, 0x41, 0x4c, 0x10, 0x03, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b,
	0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_engagement_v1_calling_log_defs_proto_rawDescOnce sync.Once
	file_moego_models_engagement_v1_calling_log_defs_proto_rawDescData = file_moego_models_engagement_v1_calling_log_defs_proto_rawDesc
)

func file_moego_models_engagement_v1_calling_log_defs_proto_rawDescGZIP() []byte {
	file_moego_models_engagement_v1_calling_log_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_engagement_v1_calling_log_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_engagement_v1_calling_log_defs_proto_rawDescData)
	})
	return file_moego_models_engagement_v1_calling_log_defs_proto_rawDescData
}

var file_moego_models_engagement_v1_calling_log_defs_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_engagement_v1_calling_log_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_engagement_v1_calling_log_defs_proto_goTypes = []interface{}{
	(Trend)(0),                    // 0: moego.models.engagement.v1.Trend
	(*CreateCallingLogDef)(nil),   // 1: moego.models.engagement.v1.CreateCallingLogDef
	(*UpdateCallingLogDef)(nil),   // 2: moego.models.engagement.v1.UpdateCallingLogDef
	(*IndicatorDef)(nil),          // 3: moego.models.engagement.v1.IndicatorDef
	(RecordType)(0),               // 4: moego.models.engagement.v1.RecordType
	(CallingDirection)(0),         // 5: moego.models.engagement.v1.CallingDirection
	(Status)(0),                   // 6: moego.models.engagement.v1.Status
	(Category)(0),                 // 7: moego.models.engagement.v1.Category
	(*timestamppb.Timestamp)(nil), // 8: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),   // 9: google.protobuf.Duration
}
var file_moego_models_engagement_v1_calling_log_defs_proto_depIdxs = []int32{
	4,  // 0: moego.models.engagement.v1.CreateCallingLogDef.record_type:type_name -> moego.models.engagement.v1.RecordType
	5,  // 1: moego.models.engagement.v1.CreateCallingLogDef.direction:type_name -> moego.models.engagement.v1.CallingDirection
	6,  // 2: moego.models.engagement.v1.CreateCallingLogDef.status:type_name -> moego.models.engagement.v1.Status
	7,  // 3: moego.models.engagement.v1.CreateCallingLogDef.categories:type_name -> moego.models.engagement.v1.Category
	8,  // 4: moego.models.engagement.v1.CreateCallingLogDef.init_time:type_name -> google.protobuf.Timestamp
	9,  // 5: moego.models.engagement.v1.CreateCallingLogDef.record_duration:type_name -> google.protobuf.Duration
	4,  // 6: moego.models.engagement.v1.UpdateCallingLogDef.record_type:type_name -> moego.models.engagement.v1.RecordType
	5,  // 7: moego.models.engagement.v1.UpdateCallingLogDef.direction:type_name -> moego.models.engagement.v1.CallingDirection
	6,  // 8: moego.models.engagement.v1.UpdateCallingLogDef.status:type_name -> moego.models.engagement.v1.Status
	7,  // 9: moego.models.engagement.v1.UpdateCallingLogDef.categories:type_name -> moego.models.engagement.v1.Category
	8,  // 10: moego.models.engagement.v1.UpdateCallingLogDef.init_time:type_name -> google.protobuf.Timestamp
	9,  // 11: moego.models.engagement.v1.UpdateCallingLogDef.record_duration:type_name -> google.protobuf.Duration
	0,  // 12: moego.models.engagement.v1.IndicatorDef.trend:type_name -> moego.models.engagement.v1.Trend
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_moego_models_engagement_v1_calling_log_defs_proto_init() }
func file_moego_models_engagement_v1_calling_log_defs_proto_init() {
	if File_moego_models_engagement_v1_calling_log_defs_proto != nil {
		return
	}
	file_moego_models_engagement_v1_calling_log_models_proto_init()
	file_moego_models_engagement_v1_voice_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_engagement_v1_calling_log_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCallingLogDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_engagement_v1_calling_log_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCallingLogDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_engagement_v1_calling_log_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IndicatorDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_engagement_v1_calling_log_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_engagement_v1_calling_log_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_engagement_v1_calling_log_defs_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_engagement_v1_calling_log_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_engagement_v1_calling_log_defs_proto_depIdxs,
		EnumInfos:         file_moego_models_engagement_v1_calling_log_defs_proto_enumTypes,
		MessageInfos:      file_moego_models_engagement_v1_calling_log_defs_proto_msgTypes,
	}.Build()
	File_moego_models_engagement_v1_calling_log_defs_proto = out.File
	file_moego_models_engagement_v1_calling_log_defs_proto_rawDesc = nil
	file_moego_models_engagement_v1_calling_log_defs_proto_goTypes = nil
	file_moego_models_engagement_v1_calling_log_defs_proto_depIdxs = nil
}
