// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/reporting/v2/filter_model.proto

package reportingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// group type
type FilterGroup_FilterGroupType int32

const (
	// Unspecified filter group type
	FilterGroup_FILTER_GROUP_TYPE_UNSPECIFIED FilterGroup_FilterGroupType = 0
	// SINGLE FILTER
	FilterGroup_SINGLE_FILTER FilterGroup_FilterGroupType = 1
	// normal filter group
	FilterGroup_LEVEL_TREE FilterGroup_FilterGroupType = 2
	// date input
	FilterGroup_DATE_INPUT FilterGroup_FilterGroupType = 3
	// checkbox + relative date filter
	FilterGroup_CHECKBOX_RELATIVE_DATE FilterGroup_FilterGroupType = 4
	// checkbox + checkbox filter
	FilterGroup_CHECKBOX_CHECKBOX FilterGroup_FilterGroupType = 5
)

// Enum value maps for FilterGroup_FilterGroupType.
var (
	FilterGroup_FilterGroupType_name = map[int32]string{
		0: "FILTER_GROUP_TYPE_UNSPECIFIED",
		1: "SINGLE_FILTER",
		2: "LEVEL_TREE",
		3: "DATE_INPUT",
		4: "CHECKBOX_RELATIVE_DATE",
		5: "CHECKBOX_CHECKBOX",
	}
	FilterGroup_FilterGroupType_value = map[string]int32{
		"FILTER_GROUP_TYPE_UNSPECIFIED": 0,
		"SINGLE_FILTER":                 1,
		"LEVEL_TREE":                    2,
		"DATE_INPUT":                    3,
		"CHECKBOX_RELATIVE_DATE":        4,
		"CHECKBOX_CHECKBOX":             5,
	}
)

func (x FilterGroup_FilterGroupType) Enum() *FilterGroup_FilterGroupType {
	p := new(FilterGroup_FilterGroupType)
	*p = x
	return p
}

func (x FilterGroup_FilterGroupType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FilterGroup_FilterGroupType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_filter_model_proto_enumTypes[0].Descriptor()
}

func (FilterGroup_FilterGroupType) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_filter_model_proto_enumTypes[0]
}

func (x FilterGroup_FilterGroupType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FilterGroup_FilterGroupType.Descriptor instead.
func (FilterGroup_FilterGroupType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_filter_model_proto_rawDescGZIP(), []int{0, 0}
}

// A filter component
type Filter_ComponentType int32

const (
	// Unspecified filter component
	Filter_COMPONENT_TYPE_UNSPECIFIED Filter_ComponentType = 0
	// A text filter component
	Filter_CHECKBOX Filter_ComponentType = 1
	// A radio filter component
	Filter_RADIO Filter_ComponentType = 2
	// A select
	Filter_SELECT Filter_ComponentType = 3
	// A date filter component
	Filter_DATE Filter_ComponentType = 4
	// A radio filter component with customized options
	Filter_RADIO_WITH_CUSTOMIZED Filter_ComponentType = 5
	// A radio filter component with input option
	Filter_RADIO_INPUT Filter_ComponentType = 6
	// A input filter component
	Filter_INPUT Filter_ComponentType = 7 // preserved 8-20 for normal filter component
	// RELATIVE DATE FOR PASS DATE
	Filter_RELATIVE_DATE Filter_ComponentType = 8
	// LEVEL TREE SUB NODE
	Filter_LEVEL_TREE_NODE Filter_ComponentType = 9
	// RELATIVE DATE FOR FUTURE DATE
	Filter_RELATIVE_DATE_FUTURE Filter_ComponentType = 10
	// customized filter component: client tag
	Filter_CUSTOMIZED_CLIENT_TAG Filter_ComponentType = 21
	// customized filter component: pet type and breed
	Filter_CUSTOMIZED_PET_TYPE_AND_BREED Filter_ComponentType = 22
	// customized filter component: pet code
	Filter_CUSTOMIZED_PET_CODE Filter_ComponentType = 23
	// customized filter component: zipcode
	Filter_CUSTOMIZED_ZIPCODE Filter_ComponentType = 24
	// customized filter component: payment method
	Filter_CUSTOMIZED_PAYMENT_METHOD Filter_ComponentType = 25
	// customized filter component: service area
	Filter_CUSTOMIZED_SERVICE_AREA Filter_ComponentType = 26
	// customized filter component: workflow
	Filter_CUSTOMIZED_WORKFLOW Filter_ComponentType = 27
)

// Enum value maps for Filter_ComponentType.
var (
	Filter_ComponentType_name = map[int32]string{
		0:  "COMPONENT_TYPE_UNSPECIFIED",
		1:  "CHECKBOX",
		2:  "RADIO",
		3:  "SELECT",
		4:  "DATE",
		5:  "RADIO_WITH_CUSTOMIZED",
		6:  "RADIO_INPUT",
		7:  "INPUT",
		8:  "RELATIVE_DATE",
		9:  "LEVEL_TREE_NODE",
		10: "RELATIVE_DATE_FUTURE",
		21: "CUSTOMIZED_CLIENT_TAG",
		22: "CUSTOMIZED_PET_TYPE_AND_BREED",
		23: "CUSTOMIZED_PET_CODE",
		24: "CUSTOMIZED_ZIPCODE",
		25: "CUSTOMIZED_PAYMENT_METHOD",
		26: "CUSTOMIZED_SERVICE_AREA",
		27: "CUSTOMIZED_WORKFLOW",
	}
	Filter_ComponentType_value = map[string]int32{
		"COMPONENT_TYPE_UNSPECIFIED":    0,
		"CHECKBOX":                      1,
		"RADIO":                         2,
		"SELECT":                        3,
		"DATE":                          4,
		"RADIO_WITH_CUSTOMIZED":         5,
		"RADIO_INPUT":                   6,
		"INPUT":                         7,
		"RELATIVE_DATE":                 8,
		"LEVEL_TREE_NODE":               9,
		"RELATIVE_DATE_FUTURE":          10,
		"CUSTOMIZED_CLIENT_TAG":         21,
		"CUSTOMIZED_PET_TYPE_AND_BREED": 22,
		"CUSTOMIZED_PET_CODE":           23,
		"CUSTOMIZED_ZIPCODE":            24,
		"CUSTOMIZED_PAYMENT_METHOD":     25,
		"CUSTOMIZED_SERVICE_AREA":       26,
		"CUSTOMIZED_WORKFLOW":           27,
	}
)

func (x Filter_ComponentType) Enum() *Filter_ComponentType {
	p := new(Filter_ComponentType)
	*p = x
	return p
}

func (x Filter_ComponentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Filter_ComponentType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_filter_model_proto_enumTypes[1].Descriptor()
}

func (Filter_ComponentType) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_filter_model_proto_enumTypes[1]
}

func (x Filter_ComponentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Filter_ComponentType.Descriptor instead.
func (Filter_ComponentType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_filter_model_proto_rawDescGZIP(), []int{1, 0}
}

// The operator of the filter parameter
type FilterGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The group name
	GroupName string `protobuf:"bytes,1,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	// The filters
	Filters []*Filter `protobuf:"bytes,2,rep,name=filters,proto3" json:"filters,omitempty"`
	// group type
	GroupType FilterGroup_FilterGroupType `protobuf:"varint,3,opt,name=group_type,json=groupType,proto3,enum=moego.models.reporting.v2.FilterGroup_FilterGroupType" json:"group_type,omitempty"`
	// The description of the filter group
	Description *string `protobuf:"bytes,4,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// category name
	CategoryName *string `protobuf:"bytes,5,opt,name=category_name,json=categoryName,proto3,oneof" json:"category_name,omitempty"`
	// category sort
	CategorySort *int32 `protobuf:"varint,6,opt,name=category_sort,json=categorySort,proto3,oneof" json:"category_sort,omitempty"`
	// filter group sort
	Sort *int32 `protobuf:"varint,7,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
}

func (x *FilterGroup) Reset() {
	*x = FilterGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_filter_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilterGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterGroup) ProtoMessage() {}

func (x *FilterGroup) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_filter_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterGroup.ProtoReflect.Descriptor instead.
func (*FilterGroup) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_filter_model_proto_rawDescGZIP(), []int{0}
}

func (x *FilterGroup) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *FilterGroup) GetFilters() []*Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *FilterGroup) GetGroupType() FilterGroup_FilterGroupType {
	if x != nil {
		return x.GroupType
	}
	return FilterGroup_FILTER_GROUP_TYPE_UNSPECIFIED
}

func (x *FilterGroup) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *FilterGroup) GetCategoryName() string {
	if x != nil && x.CategoryName != nil {
		return *x.CategoryName
	}
	return ""
}

func (x *FilterGroup) GetCategorySort() int32 {
	if x != nil && x.CategorySort != nil {
		return *x.CategorySort
	}
	return 0
}

func (x *FilterGroup) GetSort() int32 {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return 0
}

// A filter
type Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The field key of the filter parameter
	FieldKey string `protobuf:"bytes,1,opt,name=field_key,json=fieldKey,proto3" json:"field_key,omitempty"`
	// The name of the filter parameter
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// The options of the filter
	Options []*FilterOption `protobuf:"bytes,3,rep,name=options,proto3" json:"options,omitempty"`
	// The component type of the filter parameter
	ComponentType Filter_ComponentType `protobuf:"varint,4,opt,name=component_type,json=componentType,proto3,enum=moego.models.reporting.v2.Filter_ComponentType" json:"component_type,omitempty"`
	// Current filter's required permission code
	PermissionCode string `protobuf:"bytes,5,opt,name=permission_code,json=permissionCode,proto3" json:"permission_code,omitempty"`
	// The operator of the filter parameter, key is the [label] of parent filter options.
	LevelOptions map[string]*LevelFilterOptions `protobuf:"bytes,6,rep,name=level_options,json=levelOptions,proto3" json:"level_options,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Field type of the filter
	FieldType Field_Type `protobuf:"varint,7,opt,name=field_type,json=fieldType,proto3,enum=moego.models.reporting.v2.Field_Type" json:"field_type,omitempty"`
	// The default operator of this filter
	Operator Operator `protobuf:"varint,8,opt,name=operator,proto3,enum=moego.models.reporting.v2.Operator" json:"operator,omitempty"`
}

func (x *Filter) Reset() {
	*x = Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_filter_model_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Filter) ProtoMessage() {}

func (x *Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_filter_model_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Filter.ProtoReflect.Descriptor instead.
func (*Filter) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_filter_model_proto_rawDescGZIP(), []int{1}
}

func (x *Filter) GetFieldKey() string {
	if x != nil {
		return x.FieldKey
	}
	return ""
}

func (x *Filter) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Filter) GetOptions() []*FilterOption {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *Filter) GetComponentType() Filter_ComponentType {
	if x != nil {
		return x.ComponentType
	}
	return Filter_COMPONENT_TYPE_UNSPECIFIED
}

func (x *Filter) GetPermissionCode() string {
	if x != nil {
		return x.PermissionCode
	}
	return ""
}

func (x *Filter) GetLevelOptions() map[string]*LevelFilterOptions {
	if x != nil {
		return x.LevelOptions
	}
	return nil
}

func (x *Filter) GetFieldType() Field_Type {
	if x != nil {
		return x.FieldType
	}
	return Field_TYPE_UNSPECIFIED
}

func (x *Filter) GetOperator() Operator {
	if x != nil {
		return x.Operator
	}
	return Operator_OPERATOR_UNSPECIFIED
}

// Level filter options
type LevelFilterOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The operator of the filter parameter
	Options []*FilterOption `protobuf:"bytes,1,rep,name=options,proto3" json:"options,omitempty"`
}

func (x *LevelFilterOptions) Reset() {
	*x = LevelFilterOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_filter_model_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LevelFilterOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LevelFilterOptions) ProtoMessage() {}

func (x *LevelFilterOptions) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_filter_model_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LevelFilterOptions.ProtoReflect.Descriptor instead.
func (*LevelFilterOptions) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_filter_model_proto_rawDescGZIP(), []int{2}
}

func (x *LevelFilterOptions) GetOptions() []*FilterOption {
	if x != nil {
		return x.Options
	}
	return nil
}

// Filter option definition
type FilterOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The operator of the filter parameter
	Operator Operator `protobuf:"varint,1,opt,name=operator,proto3,enum=moego.models.reporting.v2.Operator" json:"operator,omitempty"`
	// The label of the filter option
	Label *string `protobuf:"bytes,2,opt,name=label,proto3,oneof" json:"label,omitempty"`
	// The label of the filter option
	Description *string `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// option values
	Values []*Value `protobuf:"bytes,4,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *FilterOption) Reset() {
	*x = FilterOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_filter_model_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilterOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterOption) ProtoMessage() {}

func (x *FilterOption) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_filter_model_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterOption.ProtoReflect.Descriptor instead.
func (*FilterOption) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_filter_model_proto_rawDescGZIP(), []int{3}
}

func (x *FilterOption) GetOperator() Operator {
	if x != nil {
		return x.Operator
	}
	return Operator_OPERATOR_UNSPECIFIED
}

func (x *FilterOption) GetLabel() string {
	if x != nil && x.Label != nil {
		return *x.Label
	}
	return ""
}

func (x *FilterOption) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *FilterOption) GetValues() []*Value {
	if x != nil {
		return x.Values
	}
	return nil
}

var File_moego_models_reporting_v2_filter_model_proto protoreflect.FileDescriptor

var file_moego_models_reporting_v2_filter_model_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x32, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xae, 0x04, 0x0a, 0x0b, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x12, 0x55, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x28,
	0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x48,
	0x02, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x53, 0x6f, 0x72, 0x74, 0x88,
	0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x48, 0x03, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x88, 0x01, 0x01, 0x22, 0x9a, 0x01, 0x0a, 0x0f,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x21, 0x0a, 0x1d, 0x46, 0x49, 0x4c, 0x54, 0x45, 0x52, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x46, 0x49, 0x4c,
	0x54, 0x45, 0x52, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x54,
	0x52, 0x45, 0x45, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e,
	0x50, 0x55, 0x54, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x42, 0x4f,
	0x58, 0x5f, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10,
	0x04, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x42, 0x4f, 0x58, 0x5f, 0x43, 0x48,
	0x45, 0x43, 0x4b, 0x42, 0x4f, 0x58, 0x10, 0x05, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x6f, 0x72, 0x74, 0x42, 0x07, 0x0a, 0x05,
	0x5f, 0x73, 0x6f, 0x72, 0x74, 0x22, 0xfa, 0x07, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79, 0x12, 0x17, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x56, 0x0a, 0x0e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x58, 0x0a, 0x0d, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x2e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x44, 0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x08, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x1a, 0x6e, 0x0a, 0x11, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x43, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x9b, 0x03, 0x0a, 0x0d,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a,
	0x1a, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x42, 0x4f, 0x58, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x52,
	0x41, 0x44, 0x49, 0x4f, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54,
	0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x41, 0x54, 0x45, 0x10, 0x04, 0x12, 0x19, 0x0a, 0x15,
	0x52, 0x41, 0x44, 0x49, 0x4f, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f,
	0x4d, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x41, 0x44, 0x49, 0x4f,
	0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x10, 0x06, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x4e, 0x50, 0x55,
	0x54, 0x10, 0x07, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x56, 0x45, 0x5f,
	0x44, 0x41, 0x54, 0x45, 0x10, 0x08, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f,
	0x54, 0x52, 0x45, 0x45, 0x5f, 0x4e, 0x4f, 0x44, 0x45, 0x10, 0x09, 0x12, 0x18, 0x0a, 0x14, 0x52,
	0x45, 0x4c, 0x41, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x55, 0x54,
	0x55, 0x52, 0x45, 0x10, 0x0a, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x49,
	0x5a, 0x45, 0x44, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x41, 0x47, 0x10, 0x15,
	0x12, 0x21, 0x0a, 0x1d, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x49, 0x5a, 0x45, 0x44, 0x5f, 0x50,
	0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x42, 0x52, 0x45, 0x45,
	0x44, 0x10, 0x16, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x49, 0x5a, 0x45,
	0x44, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x17, 0x12, 0x16, 0x0a, 0x12,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x49, 0x5a, 0x45, 0x44, 0x5f, 0x5a, 0x49, 0x50, 0x43, 0x4f,
	0x44, 0x45, 0x10, 0x18, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x49, 0x5a,
	0x45, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f,
	0x44, 0x10, 0x19, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x49, 0x5a, 0x45,
	0x44, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x41, 0x52, 0x45, 0x41, 0x10, 0x1a,
	0x12, 0x17, 0x0a, 0x13, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x49, 0x5a, 0x45, 0x44, 0x5f, 0x57,
	0x4f, 0x52, 0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x1b, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0x57, 0x0a, 0x12, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x41, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xe5, 0x01, 0x0a, 0x0c,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x08,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x19, 0x0a,
	0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x05,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12,
	0x38, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x81, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_reporting_v2_filter_model_proto_rawDescOnce sync.Once
	file_moego_models_reporting_v2_filter_model_proto_rawDescData = file_moego_models_reporting_v2_filter_model_proto_rawDesc
)

func file_moego_models_reporting_v2_filter_model_proto_rawDescGZIP() []byte {
	file_moego_models_reporting_v2_filter_model_proto_rawDescOnce.Do(func() {
		file_moego_models_reporting_v2_filter_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_reporting_v2_filter_model_proto_rawDescData)
	})
	return file_moego_models_reporting_v2_filter_model_proto_rawDescData
}

var file_moego_models_reporting_v2_filter_model_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_reporting_v2_filter_model_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_models_reporting_v2_filter_model_proto_goTypes = []interface{}{
	(FilterGroup_FilterGroupType)(0), // 0: moego.models.reporting.v2.FilterGroup.FilterGroupType
	(Filter_ComponentType)(0),        // 1: moego.models.reporting.v2.Filter.ComponentType
	(*FilterGroup)(nil),              // 2: moego.models.reporting.v2.FilterGroup
	(*Filter)(nil),                   // 3: moego.models.reporting.v2.Filter
	(*LevelFilterOptions)(nil),       // 4: moego.models.reporting.v2.LevelFilterOptions
	(*FilterOption)(nil),             // 5: moego.models.reporting.v2.FilterOption
	nil,                              // 6: moego.models.reporting.v2.Filter.LevelOptionsEntry
	(Field_Type)(0),                  // 7: moego.models.reporting.v2.Field.Type
	(Operator)(0),                    // 8: moego.models.reporting.v2.Operator
	(*Value)(nil),                    // 9: moego.models.reporting.v2.Value
}
var file_moego_models_reporting_v2_filter_model_proto_depIdxs = []int32{
	3,  // 0: moego.models.reporting.v2.FilterGroup.filters:type_name -> moego.models.reporting.v2.Filter
	0,  // 1: moego.models.reporting.v2.FilterGroup.group_type:type_name -> moego.models.reporting.v2.FilterGroup.FilterGroupType
	5,  // 2: moego.models.reporting.v2.Filter.options:type_name -> moego.models.reporting.v2.FilterOption
	1,  // 3: moego.models.reporting.v2.Filter.component_type:type_name -> moego.models.reporting.v2.Filter.ComponentType
	6,  // 4: moego.models.reporting.v2.Filter.level_options:type_name -> moego.models.reporting.v2.Filter.LevelOptionsEntry
	7,  // 5: moego.models.reporting.v2.Filter.field_type:type_name -> moego.models.reporting.v2.Field.Type
	8,  // 6: moego.models.reporting.v2.Filter.operator:type_name -> moego.models.reporting.v2.Operator
	5,  // 7: moego.models.reporting.v2.LevelFilterOptions.options:type_name -> moego.models.reporting.v2.FilterOption
	8,  // 8: moego.models.reporting.v2.FilterOption.operator:type_name -> moego.models.reporting.v2.Operator
	9,  // 9: moego.models.reporting.v2.FilterOption.values:type_name -> moego.models.reporting.v2.Value
	4,  // 10: moego.models.reporting.v2.Filter.LevelOptionsEntry.value:type_name -> moego.models.reporting.v2.LevelFilterOptions
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_moego_models_reporting_v2_filter_model_proto_init() }
func file_moego_models_reporting_v2_filter_model_proto_init() {
	if File_moego_models_reporting_v2_filter_model_proto != nil {
		return
	}
	file_moego_models_reporting_v2_common_model_proto_init()
	file_moego_models_reporting_v2_field_model_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_reporting_v2_filter_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilterGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_filter_model_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_filter_model_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LevelFilterOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_filter_model_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilterOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_reporting_v2_filter_model_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_filter_model_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_reporting_v2_filter_model_proto_msgTypes[3].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_reporting_v2_filter_model_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_reporting_v2_filter_model_proto_goTypes,
		DependencyIndexes: file_moego_models_reporting_v2_filter_model_proto_depIdxs,
		EnumInfos:         file_moego_models_reporting_v2_filter_model_proto_enumTypes,
		MessageInfos:      file_moego_models_reporting_v2_filter_model_proto_msgTypes,
	}.Build()
	File_moego_models_reporting_v2_filter_model_proto = out.File
	file_moego_models_reporting_v2_filter_model_proto_rawDesc = nil
	file_moego_models_reporting_v2_filter_model_proto_goTypes = nil
	file_moego_models_reporting_v2_filter_model_proto_depIdxs = nil
}
