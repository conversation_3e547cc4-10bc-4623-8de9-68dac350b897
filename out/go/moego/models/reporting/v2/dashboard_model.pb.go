// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/reporting/v2/dashboard_model.proto

package reportingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Dashboard page types
type DashboardPage_Tab int32

const (
	// Unspecified dashboard page tab
	DashboardPage_TAB_UNSPECIFIED DashboardPage_Tab = 0
	// The dashboard page tab is overview
	DashboardPage_OVERVIEW DashboardPage_Tab = 1
	// The dashboard page tab is sales
	DashboardPage_SALES DashboardPage_Tab = 2
	// The dashboard page tab is clients and pets
	DashboardPage_PETS DashboardPage_Tab = 3
	// The dashboard page tab is staff
	DashboardPage_STAFF DashboardPage_Tab = 4
	// The dashboard page tab is operation
	DashboardPage_OPERATION DashboardPage_Tab = 5
	// App Dashboard > Performance
	DashboardPage_APP_PERSONAL_PERFORMANCE DashboardPage_Tab = 6
	// App Dashboard > Overview
	DashboardPage_APP_OVERVIEW DashboardPage_Tab = 7
	// APP Dashboard > Staff performance
	DashboardPage_APP_STAFF_PERFORMANCE DashboardPage_Tab = 8
	// APP Dashboard > History(Dashboard 的 tab，用于控制 permission，Tab 下无关联的 diagram meta)
	DashboardPage_APP_HISTORY DashboardPage_Tab = 9
	// APP Dashboard > Report(Dashboard 的 tab，用于控制 permission，Tab 下无关联的 diagram meta)
	DashboardPage_APP_REPORT DashboardPage_Tab = 10
	// Payroll
	DashboardPage_PAYROLL DashboardPage_Tab = 11
	// Daily revenue
	DashboardPage_DAILY_REVENUE DashboardPage_Tab = 12
)

// Enum value maps for DashboardPage_Tab.
var (
	DashboardPage_Tab_name = map[int32]string{
		0:  "TAB_UNSPECIFIED",
		1:  "OVERVIEW",
		2:  "SALES",
		3:  "PETS",
		4:  "STAFF",
		5:  "OPERATION",
		6:  "APP_PERSONAL_PERFORMANCE",
		7:  "APP_OVERVIEW",
		8:  "APP_STAFF_PERFORMANCE",
		9:  "APP_HISTORY",
		10: "APP_REPORT",
		11: "PAYROLL",
		12: "DAILY_REVENUE",
	}
	DashboardPage_Tab_value = map[string]int32{
		"TAB_UNSPECIFIED":          0,
		"OVERVIEW":                 1,
		"SALES":                    2,
		"PETS":                     3,
		"STAFF":                    4,
		"OPERATION":                5,
		"APP_PERSONAL_PERFORMANCE": 6,
		"APP_OVERVIEW":             7,
		"APP_STAFF_PERFORMANCE":    8,
		"APP_HISTORY":              9,
		"APP_REPORT":               10,
		"PAYROLL":                  11,
		"DAILY_REVENUE":            12,
	}
)

func (x DashboardPage_Tab) Enum() *DashboardPage_Tab {
	p := new(DashboardPage_Tab)
	*p = x
	return p
}

func (x DashboardPage_Tab) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DashboardPage_Tab) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_dashboard_model_proto_enumTypes[0].Descriptor()
}

func (DashboardPage_Tab) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_dashboard_model_proto_enumTypes[0]
}

func (x DashboardPage_Tab) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DashboardPage_Tab.Descriptor instead.
func (DashboardPage_Tab) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_dashboard_model_proto_rawDescGZIP(), []int{0, 0}
}

// A dashboard page
type DashboardPage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The tab of the dashboard page
	Tab DashboardPage_Tab `protobuf:"varint,1,opt,name=tab,proto3,enum=moego.models.reporting.v2.DashboardPage_Tab" json:"tab,omitempty"`
	// The title of the dashboard page
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// The dashboard groups
	Groups []*DashBoardGroup `protobuf:"bytes,3,rep,name=groups,proto3" json:"groups,omitempty"`
	// Current page's required permission code
	PermissionCode string `protobuf:"bytes,4,opt,name=permission_code,json=permissionCode,proto3" json:"permission_code,omitempty"`
}

func (x *DashboardPage) Reset() {
	*x = DashboardPage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_dashboard_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardPage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardPage) ProtoMessage() {}

func (x *DashboardPage) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_dashboard_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardPage.ProtoReflect.Descriptor instead.
func (*DashboardPage) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_dashboard_model_proto_rawDescGZIP(), []int{0}
}

func (x *DashboardPage) GetTab() DashboardPage_Tab {
	if x != nil {
		return x.Tab
	}
	return DashboardPage_TAB_UNSPECIFIED
}

func (x *DashboardPage) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *DashboardPage) GetGroups() []*DashBoardGroup {
	if x != nil {
		return x.Groups
	}
	return nil
}

func (x *DashboardPage) GetPermissionCode() string {
	if x != nil {
		return x.PermissionCode
	}
	return ""
}

// A group of dashboards
type DashBoardGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram id
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// The title of the dashboard group
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// description
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// The drill configuration
	DrillConfig *DrillConfig `protobuf:"bytes,4,opt,name=drill_config,json=drillConfig,proto3" json:"drill_config,omitempty"`
	// describe the dashboard group contents
	Diagrams []*DashBoardDiagram `protobuf:"bytes,5,rep,name=diagrams,proto3" json:"diagrams,omitempty"`
	// Current diagram's required permission code
	PermissionCode string `protobuf:"bytes,6,opt,name=permission_code,json=permissionCode,proto3" json:"permission_code,omitempty"`
}

func (x *DashBoardGroup) Reset() {
	*x = DashBoardGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_dashboard_model_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashBoardGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashBoardGroup) ProtoMessage() {}

func (x *DashBoardGroup) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_dashboard_model_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashBoardGroup.ProtoReflect.Descriptor instead.
func (*DashBoardGroup) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_dashboard_model_proto_rawDescGZIP(), []int{1}
}

func (x *DashBoardGroup) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *DashBoardGroup) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *DashBoardGroup) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DashBoardGroup) GetDrillConfig() *DrillConfig {
	if x != nil {
		return x.DrillConfig
	}
	return nil
}

func (x *DashBoardGroup) GetDiagrams() []*DashBoardDiagram {
	if x != nil {
		return x.Diagrams
	}
	return nil
}

func (x *DashBoardGroup) GetPermissionCode() string {
	if x != nil {
		return x.PermissionCode
	}
	return ""
}

// A dashboard diagram
type DashBoardDiagram struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The diagram id
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// The diagram type
	DiagramType DiagramType `protobuf:"varint,2,opt,name=diagram_type,json=diagramType,proto3,enum=moego.models.reporting.v2.DiagramType" json:"diagram_type,omitempty"`
	// table metas
	TableMeta *TableMeta `protobuf:"bytes,3,opt,name=table_meta,json=tableMeta,proto3,oneof" json:"table_meta,omitempty"`
	// The drill configuration
	DrillConfig *DrillConfig `protobuf:"bytes,4,opt,name=drill_config,json=drillConfig,proto3,oneof" json:"drill_config,omitempty"`
	// Current diagram's permission code
	PermissionCode string `protobuf:"bytes,5,opt,name=permission_code,json=permissionCode,proto3" json:"permission_code,omitempty"`
	// associated diagram id
	AssociatedDiagramId *string `protobuf:"bytes,6,opt,name=associated_diagram_id,json=associatedDiagramId,proto3,oneof" json:"associated_diagram_id,omitempty"`
	// default group by field key
	DefaultGroupByFieldKeys []string `protobuf:"bytes,7,rep,name=default_group_by_field_keys,json=defaultGroupByFieldKeys,proto3" json:"default_group_by_field_keys,omitempty"`
	// fields of the diagram
	Fields []*Field `protobuf:"bytes,8,rep,name=fields,proto3" json:"fields,omitempty"`
	// filters of the diagram
	Filters []*Filter `protobuf:"bytes,9,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *DashBoardDiagram) Reset() {
	*x = DashBoardDiagram{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_dashboard_model_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashBoardDiagram) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashBoardDiagram) ProtoMessage() {}

func (x *DashBoardDiagram) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_dashboard_model_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashBoardDiagram.ProtoReflect.Descriptor instead.
func (*DashBoardDiagram) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_dashboard_model_proto_rawDescGZIP(), []int{2}
}

func (x *DashBoardDiagram) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *DashBoardDiagram) GetDiagramType() DiagramType {
	if x != nil {
		return x.DiagramType
	}
	return DiagramType_DIAGRAM_TYPE_UNSPECIFIED
}

func (x *DashBoardDiagram) GetTableMeta() *TableMeta {
	if x != nil {
		return x.TableMeta
	}
	return nil
}

func (x *DashBoardDiagram) GetDrillConfig() *DrillConfig {
	if x != nil {
		return x.DrillConfig
	}
	return nil
}

func (x *DashBoardDiagram) GetPermissionCode() string {
	if x != nil {
		return x.PermissionCode
	}
	return ""
}

func (x *DashBoardDiagram) GetAssociatedDiagramId() string {
	if x != nil && x.AssociatedDiagramId != nil {
		return *x.AssociatedDiagramId
	}
	return ""
}

func (x *DashBoardDiagram) GetDefaultGroupByFieldKeys() []string {
	if x != nil {
		return x.DefaultGroupByFieldKeys
	}
	return nil
}

func (x *DashBoardDiagram) GetFields() []*Field {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *DashBoardDiagram) GetFilters() []*Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

var File_moego_models_reporting_v2_dashboard_model_proto protoreflect.FileDescriptor

var file_moego_models_reporting_v2_dashboard_model_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x64, 0x61, 0x73, 0x68,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x1a, 0x2c, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x32, 0x2f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb7, 0x03, 0x0a, 0x0d, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x12, 0x3e, 0x0a, 0x03, 0x74, 0x61, 0x62, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x61,
	0x62, 0x52, 0x03, 0x74, 0x61, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x41, 0x0a, 0x06,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61,
	0x72, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12,
	0x27, 0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xe3, 0x01, 0x0a, 0x03, 0x54, 0x61, 0x62,
	0x12, 0x13, 0x0a, 0x0f, 0x54, 0x41, 0x42, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x4f, 0x56, 0x45, 0x52, 0x56, 0x49, 0x45,
	0x57, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x41, 0x4c, 0x45, 0x53, 0x10, 0x02, 0x12, 0x08,
	0x0a, 0x04, 0x50, 0x45, 0x54, 0x53, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x54, 0x41, 0x46,
	0x46, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x50, 0x50, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e,
	0x41, 0x4c, 0x5f, 0x50, 0x45, 0x52, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x06,
	0x12, 0x10, 0x0a, 0x0c, 0x41, 0x50, 0x50, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x56, 0x49, 0x45, 0x57,
	0x10, 0x07, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x50, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f,
	0x50, 0x45, 0x52, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x08, 0x12, 0x0f, 0x0a,
	0x0b, 0x41, 0x50, 0x50, 0x5f, 0x48, 0x49, 0x53, 0x54, 0x4f, 0x52, 0x59, 0x10, 0x09, 0x12, 0x0e,
	0x0a, 0x0a, 0x41, 0x50, 0x50, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x0a, 0x12, 0x0b,
	0x0a, 0x07, 0x50, 0x41, 0x59, 0x52, 0x4f, 0x4c, 0x4c, 0x10, 0x0b, 0x12, 0x11, 0x0a, 0x0d, 0x44,
	0x41, 0x49, 0x4c, 0x59, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x4e, 0x55, 0x45, 0x10, 0x0c, 0x22, 0xa4,
	0x02, 0x0a, 0x0e, 0x44, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x49, 0x0a, 0x0c, 0x64, 0x72, 0x69, 0x6c,
	0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x72, 0x69, 0x6c, 0x6c,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0b, 0x64, 0x72, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x47, 0x0a, 0x08, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x44, 0x69, 0x61, 0x67, 0x72,
	0x61, 0x6d, 0x52, 0x08, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x27, 0x0a, 0x0f,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xe7, 0x04, 0x0a, 0x10, 0x44, 0x61, 0x73, 0x68, 0x42, 0x6f,
	0x61, 0x72, 0x64, 0x44, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x69,
	0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x0c, 0x64, 0x69, 0x61,
	0x67, 0x72, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x69, 0x61, 0x67,
	0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x48, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x48, 0x00,
	0x52, 0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x88, 0x01, 0x01, 0x12, 0x4e,
	0x0a, 0x0c, 0x64, 0x72, 0x69, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x44, 0x72, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x01, 0x52, 0x0b,
	0x64, 0x72, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x88, 0x01, 0x01, 0x12, 0x27,
	0x0a, 0x0f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x37, 0x0a, 0x15, 0x61, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x13, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x74, 0x65, 0x64, 0x44, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x3c, 0x0a, 0x1b, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x62, 0x79, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x17, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x42, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x38,
	0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x3b, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x72, 0x69, 0x6c, 0x6c, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x42,
	0x81, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_reporting_v2_dashboard_model_proto_rawDescOnce sync.Once
	file_moego_models_reporting_v2_dashboard_model_proto_rawDescData = file_moego_models_reporting_v2_dashboard_model_proto_rawDesc
)

func file_moego_models_reporting_v2_dashboard_model_proto_rawDescGZIP() []byte {
	file_moego_models_reporting_v2_dashboard_model_proto_rawDescOnce.Do(func() {
		file_moego_models_reporting_v2_dashboard_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_reporting_v2_dashboard_model_proto_rawDescData)
	})
	return file_moego_models_reporting_v2_dashboard_model_proto_rawDescData
}

var file_moego_models_reporting_v2_dashboard_model_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_reporting_v2_dashboard_model_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_reporting_v2_dashboard_model_proto_goTypes = []interface{}{
	(DashboardPage_Tab)(0),   // 0: moego.models.reporting.v2.DashboardPage.Tab
	(*DashboardPage)(nil),    // 1: moego.models.reporting.v2.DashboardPage
	(*DashBoardGroup)(nil),   // 2: moego.models.reporting.v2.DashBoardGroup
	(*DashBoardDiagram)(nil), // 3: moego.models.reporting.v2.DashBoardDiagram
	(*DrillConfig)(nil),      // 4: moego.models.reporting.v2.DrillConfig
	(DiagramType)(0),         // 5: moego.models.reporting.v2.DiagramType
	(*TableMeta)(nil),        // 6: moego.models.reporting.v2.TableMeta
	(*Field)(nil),            // 7: moego.models.reporting.v2.Field
	(*Filter)(nil),           // 8: moego.models.reporting.v2.Filter
}
var file_moego_models_reporting_v2_dashboard_model_proto_depIdxs = []int32{
	0, // 0: moego.models.reporting.v2.DashboardPage.tab:type_name -> moego.models.reporting.v2.DashboardPage.Tab
	2, // 1: moego.models.reporting.v2.DashboardPage.groups:type_name -> moego.models.reporting.v2.DashBoardGroup
	4, // 2: moego.models.reporting.v2.DashBoardGroup.drill_config:type_name -> moego.models.reporting.v2.DrillConfig
	3, // 3: moego.models.reporting.v2.DashBoardGroup.diagrams:type_name -> moego.models.reporting.v2.DashBoardDiagram
	5, // 4: moego.models.reporting.v2.DashBoardDiagram.diagram_type:type_name -> moego.models.reporting.v2.DiagramType
	6, // 5: moego.models.reporting.v2.DashBoardDiagram.table_meta:type_name -> moego.models.reporting.v2.TableMeta
	4, // 6: moego.models.reporting.v2.DashBoardDiagram.drill_config:type_name -> moego.models.reporting.v2.DrillConfig
	7, // 7: moego.models.reporting.v2.DashBoardDiagram.fields:type_name -> moego.models.reporting.v2.Field
	8, // 8: moego.models.reporting.v2.DashBoardDiagram.filters:type_name -> moego.models.reporting.v2.Filter
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_moego_models_reporting_v2_dashboard_model_proto_init() }
func file_moego_models_reporting_v2_dashboard_model_proto_init() {
	if File_moego_models_reporting_v2_dashboard_model_proto != nil {
		return
	}
	file_moego_models_reporting_v2_common_model_proto_init()
	file_moego_models_reporting_v2_diagram_model_proto_init()
	file_moego_models_reporting_v2_field_model_proto_init()
	file_moego_models_reporting_v2_filter_model_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_reporting_v2_dashboard_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardPage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_dashboard_model_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashBoardGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_reporting_v2_dashboard_model_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashBoardDiagram); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_reporting_v2_dashboard_model_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_reporting_v2_dashboard_model_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_reporting_v2_dashboard_model_proto_goTypes,
		DependencyIndexes: file_moego_models_reporting_v2_dashboard_model_proto_depIdxs,
		EnumInfos:         file_moego_models_reporting_v2_dashboard_model_proto_enumTypes,
		MessageInfos:      file_moego_models_reporting_v2_dashboard_model_proto_msgTypes,
	}.Build()
	File_moego_models_reporting_v2_dashboard_model_proto = out.File
	file_moego_models_reporting_v2_dashboard_model_proto_rawDesc = nil
	file_moego_models_reporting_v2_dashboard_model_proto_goTypes = nil
	file_moego_models_reporting_v2_dashboard_model_proto_depIdxs = nil
}
