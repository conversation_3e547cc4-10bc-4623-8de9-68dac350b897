// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/account/v1/account_info_api.proto

package accountapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AccountInfoServiceClient is the client API for AccountInfoService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccountInfoServiceClient interface {
	// Error codes:
	// - CODE_SESSION_NOT_EXIST: the session cannot be found or has been deleted.
	GetAccountInfo(ctx context.Context, in *GetAccountInfoRequest, opts ...grpc.CallOption) (*GetAccountInfoResponse, error)
	// Update profile of the account related to the current session.
	// The request should be in a valid session.
	UpdateProfile(ctx context.Context, in *UpdateProfileRequest, opts ...grpc.CallOption) (*UpdateProfileResponse, error)
	// Update password of the account related to the current session.
	// The request should be in a valid session.
	// After success, all sessions of this account will be invalidated, and a new valid session will be created.
	//
	// Error codes:
	// - OLD_PASSWORD_ERROR: old password error.
	UpdatePassword(ctx context.Context, in *UpdatePasswordRequest, opts ...grpc.CallOption) (*UpdatePasswordResponse, error)
}

type accountInfoServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountInfoServiceClient(cc grpc.ClientConnInterface) AccountInfoServiceClient {
	return &accountInfoServiceClient{cc}
}

func (c *accountInfoServiceClient) GetAccountInfo(ctx context.Context, in *GetAccountInfoRequest, opts ...grpc.CallOption) (*GetAccountInfoResponse, error) {
	out := new(GetAccountInfoResponse)
	err := c.cc.Invoke(ctx, "/moego.enterprise.account.v1.AccountInfoService/GetAccountInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountInfoServiceClient) UpdateProfile(ctx context.Context, in *UpdateProfileRequest, opts ...grpc.CallOption) (*UpdateProfileResponse, error) {
	out := new(UpdateProfileResponse)
	err := c.cc.Invoke(ctx, "/moego.enterprise.account.v1.AccountInfoService/UpdateProfile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountInfoServiceClient) UpdatePassword(ctx context.Context, in *UpdatePasswordRequest, opts ...grpc.CallOption) (*UpdatePasswordResponse, error) {
	out := new(UpdatePasswordResponse)
	err := c.cc.Invoke(ctx, "/moego.enterprise.account.v1.AccountInfoService/UpdatePassword", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountInfoServiceServer is the server API for AccountInfoService service.
// All implementations must embed UnimplementedAccountInfoServiceServer
// for forward compatibility
type AccountInfoServiceServer interface {
	// Error codes:
	// - CODE_SESSION_NOT_EXIST: the session cannot be found or has been deleted.
	GetAccountInfo(context.Context, *GetAccountInfoRequest) (*GetAccountInfoResponse, error)
	// Update profile of the account related to the current session.
	// The request should be in a valid session.
	UpdateProfile(context.Context, *UpdateProfileRequest) (*UpdateProfileResponse, error)
	// Update password of the account related to the current session.
	// The request should be in a valid session.
	// After success, all sessions of this account will be invalidated, and a new valid session will be created.
	//
	// Error codes:
	// - OLD_PASSWORD_ERROR: old password error.
	UpdatePassword(context.Context, *UpdatePasswordRequest) (*UpdatePasswordResponse, error)
	mustEmbedUnimplementedAccountInfoServiceServer()
}

// UnimplementedAccountInfoServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAccountInfoServiceServer struct {
}

func (UnimplementedAccountInfoServiceServer) GetAccountInfo(context.Context, *GetAccountInfoRequest) (*GetAccountInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountInfo not implemented")
}
func (UnimplementedAccountInfoServiceServer) UpdateProfile(context.Context, *UpdateProfileRequest) (*UpdateProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateProfile not implemented")
}
func (UnimplementedAccountInfoServiceServer) UpdatePassword(context.Context, *UpdatePasswordRequest) (*UpdatePasswordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePassword not implemented")
}
func (UnimplementedAccountInfoServiceServer) mustEmbedUnimplementedAccountInfoServiceServer() {}

// UnsafeAccountInfoServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountInfoServiceServer will
// result in compilation errors.
type UnsafeAccountInfoServiceServer interface {
	mustEmbedUnimplementedAccountInfoServiceServer()
}

func RegisterAccountInfoServiceServer(s grpc.ServiceRegistrar, srv AccountInfoServiceServer) {
	s.RegisterService(&AccountInfoService_ServiceDesc, srv)
}

func _AccountInfoService_GetAccountInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountInfoServiceServer).GetAccountInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.account.v1.AccountInfoService/GetAccountInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountInfoServiceServer).GetAccountInfo(ctx, req.(*GetAccountInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountInfoService_UpdateProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountInfoServiceServer).UpdateProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.account.v1.AccountInfoService/UpdateProfile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountInfoServiceServer).UpdateProfile(ctx, req.(*UpdateProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountInfoService_UpdatePassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePasswordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountInfoServiceServer).UpdatePassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.account.v1.AccountInfoService/UpdatePassword",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountInfoServiceServer).UpdatePassword(ctx, req.(*UpdatePasswordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AccountInfoService_ServiceDesc is the grpc.ServiceDesc for AccountInfoService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AccountInfoService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.account.v1.AccountInfoService",
	HandlerType: (*AccountInfoServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAccountInfo",
			Handler:    _AccountInfoService_GetAccountInfo_Handler,
		},
		{
			MethodName: "UpdateProfile",
			Handler:    _AccountInfoService_UpdateProfile_Handler,
		},
		{
			MethodName: "UpdatePassword",
			Handler:    _AccountInfoService_UpdatePassword_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/account/v1/account_info_api.proto",
}
