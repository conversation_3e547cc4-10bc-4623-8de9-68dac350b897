// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/enterprise/map/v1/map_api.proto

package mapapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/map/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list zip code params
type ListZipCodeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// zip code
	ZipCodes []string `protobuf:"bytes,1,rep,name=zip_codes,json=zipCodes,proto3" json:"zip_codes,omitempty"`
}

func (x *ListZipCodeParams) Reset() {
	*x = ListZipCodeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_map_v1_map_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListZipCodeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListZipCodeParams) ProtoMessage() {}

func (x *ListZipCodeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_map_v1_map_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListZipCodeParams.ProtoReflect.Descriptor instead.
func (*ListZipCodeParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_map_v1_map_api_proto_rawDescGZIP(), []int{0}
}

func (x *ListZipCodeParams) GetZipCodes() []string {
	if x != nil {
		return x.ZipCodes
	}
	return nil
}

// list zip code result
type ListZipCodeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// zip codes
	ZipCodes []*v1.ZipCodeModel `protobuf:"bytes,1,rep,name=zip_codes,json=zipCodes,proto3" json:"zip_codes,omitempty"`
}

func (x *ListZipCodeResult) Reset() {
	*x = ListZipCodeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_map_v1_map_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListZipCodeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListZipCodeResult) ProtoMessage() {}

func (x *ListZipCodeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_map_v1_map_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListZipCodeResult.ProtoReflect.Descriptor instead.
func (*ListZipCodeResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_map_v1_map_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListZipCodeResult) GetZipCodes() []*v1.ZipCodeModel {
	if x != nil {
		return x.ZipCodes
	}
	return nil
}

// search zip code params
type SearchZipCodeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// prefix
	Prefix *string `protobuf:"bytes,1,opt,name=prefix,proto3,oneof" json:"prefix,omitempty"`
}

func (x *SearchZipCodeParams) Reset() {
	*x = SearchZipCodeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_map_v1_map_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchZipCodeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchZipCodeParams) ProtoMessage() {}

func (x *SearchZipCodeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_map_v1_map_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchZipCodeParams.ProtoReflect.Descriptor instead.
func (*SearchZipCodeParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_map_v1_map_api_proto_rawDescGZIP(), []int{2}
}

func (x *SearchZipCodeParams) GetPrefix() string {
	if x != nil && x.Prefix != nil {
		return *x.Prefix
	}
	return ""
}

// search zip code result
type SearchZipCodeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// zip codes
	ZipCodes []*v1.ZipCodeModel `protobuf:"bytes,1,rep,name=zip_codes,json=zipCodes,proto3" json:"zip_codes,omitempty"`
}

func (x *SearchZipCodeResult) Reset() {
	*x = SearchZipCodeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_map_v1_map_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchZipCodeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchZipCodeResult) ProtoMessage() {}

func (x *SearchZipCodeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_map_v1_map_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchZipCodeResult.ProtoReflect.Descriptor instead.
func (*SearchZipCodeResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_map_v1_map_api_proto_rawDescGZIP(), []int{3}
}

func (x *SearchZipCodeResult) GetZipCodes() []*v1.ZipCodeModel {
	if x != nil {
		return x.ZipCodes
	}
	return nil
}

// ListGooglePlaceParams
type ListGooglePlaceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// place id list
	PlaceIds []string `protobuf:"bytes,1,rep,name=place_ids,json=placeIds,proto3" json:"place_ids,omitempty"`
}

func (x *ListGooglePlaceParams) Reset() {
	*x = ListGooglePlaceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_map_v1_map_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGooglePlaceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGooglePlaceParams) ProtoMessage() {}

func (x *ListGooglePlaceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_map_v1_map_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGooglePlaceParams.ProtoReflect.Descriptor instead.
func (*ListGooglePlaceParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_map_v1_map_api_proto_rawDescGZIP(), []int{4}
}

func (x *ListGooglePlaceParams) GetPlaceIds() []string {
	if x != nil {
		return x.PlaceIds
	}
	return nil
}

// ListGooglePlaceResult
type ListGooglePlaceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// google place list
	Places []*v11.GooglePlace `protobuf:"bytes,1,rep,name=places,proto3" json:"places,omitempty"`
}

func (x *ListGooglePlaceResult) Reset() {
	*x = ListGooglePlaceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_map_v1_map_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGooglePlaceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGooglePlaceResult) ProtoMessage() {}

func (x *ListGooglePlaceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_map_v1_map_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGooglePlaceResult.ProtoReflect.Descriptor instead.
func (*ListGooglePlaceResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_map_v1_map_api_proto_rawDescGZIP(), []int{5}
}

func (x *ListGooglePlaceResult) GetPlaces() []*v11.GooglePlace {
	if x != nil {
		return x.Places
	}
	return nil
}

var File_moego_enterprise_map_v1_map_api_proto protoreflect.FileDescriptor

var file_moego_enterprise_map_v1_map_api_proto_rawDesc = []byte{
	0x0a, 0x25, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x70, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31,
	0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x7a, 0x69, 0x70, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x61, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x6d, 0x61, 0x70,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3d, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x5a, 0x69,
	0x70, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x09, 0x7a,
	0x69, 0x70, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0b,
	0xfa, 0x42, 0x08, 0x92, 0x01, 0x05, 0x08, 0x01, 0x10, 0xe8, 0x07, 0x52, 0x08, 0x7a, 0x69, 0x70,
	0x43, 0x6f, 0x64, 0x65, 0x73, 0x22, 0x58, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x5a, 0x69, 0x70,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x43, 0x0a, 0x09, 0x7a, 0x69,
	0x70, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x5a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x7a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x22,
	0x46, 0x0a, 0x13, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x24, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x64, 0x48,
	0x00, 0x52, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07,
	0x5f, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x22, 0x5a, 0x0a, 0x13, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x5a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x43,
	0x0a, 0x09, 0x7a, 0x69, 0x70, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x5a, 0x69, 0x70,
	0x43, 0x6f, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x7a, 0x69, 0x70, 0x43, 0x6f,
	0x64, 0x65, 0x73, 0x22, 0x4b, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x32, 0x0a, 0x09,
	0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42,
	0x15, 0xfa, 0x42, 0x12, 0x92, 0x01, 0x0f, 0x08, 0x01, 0x10, 0x64, 0x18, 0x01, 0x22, 0x07, 0x72,
	0x05, 0x10, 0x01, 0x18, 0x80, 0x08, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x64, 0x73,
	0x22, 0x51, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x6c,
	0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x38, 0x0a, 0x06, 0x70, 0x6c, 0x61,
	0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x52, 0x06, 0x70, 0x6c, 0x61,
	0x63, 0x65, 0x73, 0x32, 0xd3, 0x02, 0x0a, 0x0a, 0x4d, 0x61, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x65, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x5a, 0x69, 0x70, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x5a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x5a, 0x69, 0x70, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6b, 0x0a, 0x0d, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x5a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6d, 0x61,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5a, 0x69, 0x70, 0x43, 0x6f,
	0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5a, 0x69, 0x70, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x71, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6d, 0x61, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x6c,
	0x61, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6d, 0x61, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x6c,
	0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x7a, 0x0a, 0x1f, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x55,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x61, 0x70,
	0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_enterprise_map_v1_map_api_proto_rawDescOnce sync.Once
	file_moego_enterprise_map_v1_map_api_proto_rawDescData = file_moego_enterprise_map_v1_map_api_proto_rawDesc
)

func file_moego_enterprise_map_v1_map_api_proto_rawDescGZIP() []byte {
	file_moego_enterprise_map_v1_map_api_proto_rawDescOnce.Do(func() {
		file_moego_enterprise_map_v1_map_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_enterprise_map_v1_map_api_proto_rawDescData)
	})
	return file_moego_enterprise_map_v1_map_api_proto_rawDescData
}

var file_moego_enterprise_map_v1_map_api_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_moego_enterprise_map_v1_map_api_proto_goTypes = []interface{}{
	(*ListZipCodeParams)(nil),     // 0: moego.enterprise.map.v1.ListZipCodeParams
	(*ListZipCodeResult)(nil),     // 1: moego.enterprise.map.v1.ListZipCodeResult
	(*SearchZipCodeParams)(nil),   // 2: moego.enterprise.map.v1.SearchZipCodeParams
	(*SearchZipCodeResult)(nil),   // 3: moego.enterprise.map.v1.SearchZipCodeResult
	(*ListGooglePlaceParams)(nil), // 4: moego.enterprise.map.v1.ListGooglePlaceParams
	(*ListGooglePlaceResult)(nil), // 5: moego.enterprise.map.v1.ListGooglePlaceResult
	(*v1.ZipCodeModel)(nil),       // 6: moego.models.grooming.v1.ZipCodeModel
	(*v11.GooglePlace)(nil),       // 7: moego.models.map.v1.GooglePlace
}
var file_moego_enterprise_map_v1_map_api_proto_depIdxs = []int32{
	6, // 0: moego.enterprise.map.v1.ListZipCodeResult.zip_codes:type_name -> moego.models.grooming.v1.ZipCodeModel
	6, // 1: moego.enterprise.map.v1.SearchZipCodeResult.zip_codes:type_name -> moego.models.grooming.v1.ZipCodeModel
	7, // 2: moego.enterprise.map.v1.ListGooglePlaceResult.places:type_name -> moego.models.map.v1.GooglePlace
	0, // 3: moego.enterprise.map.v1.MapService.ListZipCode:input_type -> moego.enterprise.map.v1.ListZipCodeParams
	2, // 4: moego.enterprise.map.v1.MapService.SearchZipCode:input_type -> moego.enterprise.map.v1.SearchZipCodeParams
	4, // 5: moego.enterprise.map.v1.MapService.ListGooglePlace:input_type -> moego.enterprise.map.v1.ListGooglePlaceParams
	1, // 6: moego.enterprise.map.v1.MapService.ListZipCode:output_type -> moego.enterprise.map.v1.ListZipCodeResult
	3, // 7: moego.enterprise.map.v1.MapService.SearchZipCode:output_type -> moego.enterprise.map.v1.SearchZipCodeResult
	5, // 8: moego.enterprise.map.v1.MapService.ListGooglePlace:output_type -> moego.enterprise.map.v1.ListGooglePlaceResult
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_enterprise_map_v1_map_api_proto_init() }
func file_moego_enterprise_map_v1_map_api_proto_init() {
	if File_moego_enterprise_map_v1_map_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_enterprise_map_v1_map_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListZipCodeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_map_v1_map_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListZipCodeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_map_v1_map_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchZipCodeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_map_v1_map_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchZipCodeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_map_v1_map_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGooglePlaceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_map_v1_map_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGooglePlaceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_enterprise_map_v1_map_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_enterprise_map_v1_map_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_enterprise_map_v1_map_api_proto_goTypes,
		DependencyIndexes: file_moego_enterprise_map_v1_map_api_proto_depIdxs,
		MessageInfos:      file_moego_enterprise_map_v1_map_api_proto_msgTypes,
	}.Build()
	File_moego_enterprise_map_v1_map_api_proto = out.File
	file_moego_enterprise_map_v1_map_api_proto_rawDesc = nil
	file_moego_enterprise_map_v1_map_api_proto_goTypes = nil
	file_moego_enterprise_map_v1_map_api_proto_depIdxs = nil
}
