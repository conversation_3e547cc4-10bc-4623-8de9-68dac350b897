// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/map/v1/map_api.proto

package mapapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// MapServiceClient is the client API for MapService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MapServiceClient interface {
	// list zip code
	ListZipCode(ctx context.Context, in *ListZipCodeParams, opts ...grpc.CallOption) (*ListZipCodeResult, error)
	// search zip code
	SearchZipCode(ctx context.Context, in *SearchZipCodeParams, opts ...grpc.CallOption) (*SearchZipCodeResult, error)
	// get google place list
	ListGooglePlace(ctx context.Context, in *ListGooglePlaceParams, opts ...grpc.CallOption) (*ListGooglePlaceResult, error)
}

type mapServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMapServiceClient(cc grpc.ClientConnInterface) MapServiceClient {
	return &mapServiceClient{cc}
}

func (c *mapServiceClient) ListZipCode(ctx context.Context, in *ListZipCodeParams, opts ...grpc.CallOption) (*ListZipCodeResult, error) {
	out := new(ListZipCodeResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.map.v1.MapService/ListZipCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mapServiceClient) SearchZipCode(ctx context.Context, in *SearchZipCodeParams, opts ...grpc.CallOption) (*SearchZipCodeResult, error) {
	out := new(SearchZipCodeResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.map.v1.MapService/SearchZipCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mapServiceClient) ListGooglePlace(ctx context.Context, in *ListGooglePlaceParams, opts ...grpc.CallOption) (*ListGooglePlaceResult, error) {
	out := new(ListGooglePlaceResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.map.v1.MapService/ListGooglePlace", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MapServiceServer is the server API for MapService service.
// All implementations must embed UnimplementedMapServiceServer
// for forward compatibility
type MapServiceServer interface {
	// list zip code
	ListZipCode(context.Context, *ListZipCodeParams) (*ListZipCodeResult, error)
	// search zip code
	SearchZipCode(context.Context, *SearchZipCodeParams) (*SearchZipCodeResult, error)
	// get google place list
	ListGooglePlace(context.Context, *ListGooglePlaceParams) (*ListGooglePlaceResult, error)
	mustEmbedUnimplementedMapServiceServer()
}

// UnimplementedMapServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMapServiceServer struct {
}

func (UnimplementedMapServiceServer) ListZipCode(context.Context, *ListZipCodeParams) (*ListZipCodeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListZipCode not implemented")
}
func (UnimplementedMapServiceServer) SearchZipCode(context.Context, *SearchZipCodeParams) (*SearchZipCodeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchZipCode not implemented")
}
func (UnimplementedMapServiceServer) ListGooglePlace(context.Context, *ListGooglePlaceParams) (*ListGooglePlaceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGooglePlace not implemented")
}
func (UnimplementedMapServiceServer) mustEmbedUnimplementedMapServiceServer() {}

// UnsafeMapServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MapServiceServer will
// result in compilation errors.
type UnsafeMapServiceServer interface {
	mustEmbedUnimplementedMapServiceServer()
}

func RegisterMapServiceServer(s grpc.ServiceRegistrar, srv MapServiceServer) {
	s.RegisterService(&MapService_ServiceDesc, srv)
}

func _MapService_ListZipCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListZipCodeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MapServiceServer).ListZipCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.map.v1.MapService/ListZipCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MapServiceServer).ListZipCode(ctx, req.(*ListZipCodeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MapService_SearchZipCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchZipCodeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MapServiceServer).SearchZipCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.map.v1.MapService/SearchZipCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MapServiceServer).SearchZipCode(ctx, req.(*SearchZipCodeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MapService_ListGooglePlace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGooglePlaceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MapServiceServer).ListGooglePlace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.map.v1.MapService/ListGooglePlace",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MapServiceServer).ListGooglePlace(ctx, req.(*ListGooglePlaceParams))
	}
	return interceptor(ctx, in, info, handler)
}

// MapService_ServiceDesc is the grpc.ServiceDesc for MapService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MapService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.map.v1.MapService",
	HandlerType: (*MapServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListZipCode",
			Handler:    _MapService_ListZipCode_Handler,
		},
		{
			MethodName: "SearchZipCode",
			Handler:    _MapService_SearchZipCode_Handler,
		},
		{
			MethodName: "ListGooglePlace",
			Handler:    _MapService_ListGooglePlace_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/map/v1/map_api.proto",
}
