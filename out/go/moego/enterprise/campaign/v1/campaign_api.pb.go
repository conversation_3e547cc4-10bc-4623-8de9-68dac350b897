// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/enterprise/campaign/v1/campaign_api.proto

package campaignapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SendTestEmailParams
type SendTestEmailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// email subject
	Subject string `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject,omitempty"`
	// email content
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	// recipient email
	RecipientEmail string `protobuf:"bytes,3,opt,name=recipient_email,json=recipientEmail,proto3" json:"recipient_email,omitempty"`
}

func (x *SendTestEmailParams) Reset() {
	*x = SendTestEmailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendTestEmailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTestEmailParams) ProtoMessage() {}

func (x *SendTestEmailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTestEmailParams.ProtoReflect.Descriptor instead.
func (*SendTestEmailParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescGZIP(), []int{0}
}

func (x *SendTestEmailParams) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *SendTestEmailParams) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SendTestEmailParams) GetRecipientEmail() string {
	if x != nil {
		return x.RecipientEmail
	}
	return ""
}

// SendTestEmailResult
type SendTestEmailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendTestEmailResult) Reset() {
	*x = SendTestEmailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendTestEmailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTestEmailResult) ProtoMessage() {}

func (x *SendTestEmailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTestEmailResult.ProtoReflect.Descriptor instead.
func (*SendTestEmailResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescGZIP(), []int{1}
}

// CreateTemplateParams
type CreateTemplateParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// campaign template
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// description
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// type
	Type v1.Campaign_Type `protobuf:"varint,3,opt,name=type,proto3,enum=moego.models.enterprise.v1.Campaign_Type" json:"type,omitempty"`
	// cover
	Cover string `protobuf:"bytes,4,opt,name=cover,proto3" json:"cover,omitempty"`
	// subject
	Subject string `protobuf:"bytes,5,opt,name=subject,proto3" json:"subject,omitempty"`
	// content
	Content string `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *CreateTemplateParams) Reset() {
	*x = CreateTemplateParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTemplateParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTemplateParams) ProtoMessage() {}

func (x *CreateTemplateParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTemplateParams.ProtoReflect.Descriptor instead.
func (*CreateTemplateParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescGZIP(), []int{2}
}

func (x *CreateTemplateParams) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateTemplateParams) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateTemplateParams) GetType() v1.Campaign_Type {
	if x != nil {
		return x.Type
	}
	return v1.Campaign_Type(0)
}

func (x *CreateTemplateParams) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *CreateTemplateParams) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *CreateTemplateParams) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

// CreateTemplateResult
type CreateTemplateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// campaign template
	Template *v1.CampaignTemplate `protobuf:"bytes,1,opt,name=template,proto3" json:"template,omitempty"`
}

func (x *CreateTemplateResult) Reset() {
	*x = CreateTemplateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTemplateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTemplateResult) ProtoMessage() {}

func (x *CreateTemplateResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTemplateResult.ProtoReflect.Descriptor instead.
func (*CreateTemplateResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescGZIP(), []int{3}
}

func (x *CreateTemplateResult) GetTemplate() *v1.CampaignTemplate {
	if x != nil {
		return x.Template
	}
	return nil
}

// GetTemplateParams
type GetTemplateParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// campaign template id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetTemplateParams) Reset() {
	*x = GetTemplateParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTemplateParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemplateParams) ProtoMessage() {}

func (x *GetTemplateParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemplateParams.ProtoReflect.Descriptor instead.
func (*GetTemplateParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetTemplateParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// GetTemplateResult
type GetTemplateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// campaign template
	Template *v1.CampaignTemplate `protobuf:"bytes,1,opt,name=template,proto3" json:"template,omitempty"`
}

func (x *GetTemplateResult) Reset() {
	*x = GetTemplateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTemplateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemplateResult) ProtoMessage() {}

func (x *GetTemplateResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemplateResult.ProtoReflect.Descriptor instead.
func (*GetTemplateResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescGZIP(), []int{5}
}

func (x *GetTemplateResult) GetTemplate() *v1.CampaignTemplate {
	if x != nil {
		return x.Template
	}
	return nil
}

// UpdateTemplateParams
type UpdateTemplateParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// campaign template id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// campaign template
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// description
	Description *string `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// cover
	Cover *string `protobuf:"bytes,4,opt,name=cover,proto3,oneof" json:"cover,omitempty"`
	// subject
	Subject *string `protobuf:"bytes,5,opt,name=subject,proto3,oneof" json:"subject,omitempty"`
	// content
	Content *string `protobuf:"bytes,6,opt,name=content,proto3,oneof" json:"content,omitempty"`
}

func (x *UpdateTemplateParams) Reset() {
	*x = UpdateTemplateParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTemplateParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTemplateParams) ProtoMessage() {}

func (x *UpdateTemplateParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTemplateParams.ProtoReflect.Descriptor instead.
func (*UpdateTemplateParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateTemplateParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateTemplateParams) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateTemplateParams) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateTemplateParams) GetCover() string {
	if x != nil && x.Cover != nil {
		return *x.Cover
	}
	return ""
}

func (x *UpdateTemplateParams) GetSubject() string {
	if x != nil && x.Subject != nil {
		return *x.Subject
	}
	return ""
}

func (x *UpdateTemplateParams) GetContent() string {
	if x != nil && x.Content != nil {
		return *x.Content
	}
	return ""
}

// UpdateTemplateResult
type UpdateTemplateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// campaign template
	Template *v1.CampaignTemplate `protobuf:"bytes,1,opt,name=template,proto3" json:"template,omitempty"`
}

func (x *UpdateTemplateResult) Reset() {
	*x = UpdateTemplateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTemplateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTemplateResult) ProtoMessage() {}

func (x *UpdateTemplateResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTemplateResult.ProtoReflect.Descriptor instead.
func (*UpdateTemplateResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateTemplateResult) GetTemplate() *v1.CampaignTemplate {
	if x != nil {
		return x.Template
	}
	return nil
}

// ListTemplatesParams
type ListTemplatesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// filter
	Filter *v11.ListTemplatesRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListTemplatesParams) Reset() {
	*x = ListTemplatesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatesParams) ProtoMessage() {}

func (x *ListTemplatesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatesParams.ProtoReflect.Descriptor instead.
func (*ListTemplatesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescGZIP(), []int{8}
}

func (x *ListTemplatesParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListTemplatesParams) GetFilter() *v11.ListTemplatesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListTemplatesResult
type ListTemplatesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// campaign templates
	Templates []*v1.CampaignTemplate `protobuf:"bytes,2,rep,name=templates,proto3" json:"templates,omitempty"`
}

func (x *ListTemplatesResult) Reset() {
	*x = ListTemplatesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatesResult) ProtoMessage() {}

func (x *ListTemplatesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatesResult.ProtoReflect.Descriptor instead.
func (*ListTemplatesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescGZIP(), []int{9}
}

func (x *ListTemplatesResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListTemplatesResult) GetTemplates() []*v1.CampaignTemplate {
	if x != nil {
		return x.Templates
	}
	return nil
}

// PushTemplatesParams
type PushTemplatesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// campaign template ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// tenant ids
	Targets []*v1.TenantObject `protobuf:"bytes,2,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *PushTemplatesParams) Reset() {
	*x = PushTemplatesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushTemplatesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushTemplatesParams) ProtoMessage() {}

func (x *PushTemplatesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushTemplatesParams.ProtoReflect.Descriptor instead.
func (*PushTemplatesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescGZIP(), []int{10}
}

func (x *PushTemplatesParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *PushTemplatesParams) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

// PushTemplatesResult
type PushTemplatesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *PushTemplatesResult) Reset() {
	*x = PushTemplatesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushTemplatesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushTemplatesResult) ProtoMessage() {}

func (x *PushTemplatesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushTemplatesResult.ProtoReflect.Descriptor instead.
func (*PushTemplatesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescGZIP(), []int{11}
}

func (x *PushTemplatesResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// ListTemplatePlaceholdersParams
type ListTemplatePlaceholdersParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// campaign type
	Type v1.Campaign_Type `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.enterprise.v1.Campaign_Type" json:"type,omitempty"`
}

func (x *ListTemplatePlaceholdersParams) Reset() {
	*x = ListTemplatePlaceholdersParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePlaceholdersParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePlaceholdersParams) ProtoMessage() {}

func (x *ListTemplatePlaceholdersParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePlaceholdersParams.ProtoReflect.Descriptor instead.
func (*ListTemplatePlaceholdersParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescGZIP(), []int{12}
}

func (x *ListTemplatePlaceholdersParams) GetType() v1.Campaign_Type {
	if x != nil {
		return x.Type
	}
	return v1.Campaign_Type(0)
}

// ListTemplatePlaceholdersResult
type ListTemplatePlaceholdersResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template placeholders
	Placeholders []*v12.MessageTemplatePlaceholderSimpleView `protobuf:"bytes,1,rep,name=placeholders,proto3" json:"placeholders,omitempty"`
}

func (x *ListTemplatePlaceholdersResult) Reset() {
	*x = ListTemplatePlaceholdersResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePlaceholdersResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePlaceholdersResult) ProtoMessage() {}

func (x *ListTemplatePlaceholdersResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePlaceholdersResult.ProtoReflect.Descriptor instead.
func (*ListTemplatePlaceholdersResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescGZIP(), []int{13}
}

func (x *ListTemplatePlaceholdersResult) GetPlaceholders() []*v12.MessageTemplatePlaceholderSimpleView {
	if x != nil {
		return x.Placeholders
	}
	return nil
}

var File_moego_enterprise_campaign_v1_campaign_api_proto protoreflect.FileDescriptor

var file_moego_enterprise_campaign_v1_campaign_api_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x76, 0x31, 0x1a,
	0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x90, 0x01, 0x0a, 0x13, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x22, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18,
	0xc8, 0x01, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x23, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x18, 0xa0, 0x8d, 0x06, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x30, 0x0a, 0x0f, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x60, 0x01, 0x52, 0x0e, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x22, 0x15, 0x0a, 0x13, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xd5, 0x01, 0x0a, 0x14, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x22, 0x60, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x48, 0x0a, 0x08, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x22, 0x2c, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x5d, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x48, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x22, 0xfa, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x19, 0x0a, 0x05, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x05, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x88,
	0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0x0a, 0x06, 0x5f,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x60, 0x0a,
	0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x48, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22,
	0xaa, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xa5, 0x01, 0x0a,
	0x13, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x09, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x73, 0x22, 0x6b, 0x0a, 0x13, 0x50, 0x75, 0x73, 0x68, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x42, 0x0a,
	0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x73, 0x22, 0x2f, 0x0a, 0x13, 0x50, 0x75, 0x73, 0x68, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x22, 0x5f, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x3d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x83, 0x01, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x61, 0x0a, 0x0c, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x70, 0x6c, 0x61,
	0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x32, 0x80, 0x07, 0x0a, 0x0f, 0x43, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x77, 0x0a,
	0x0d, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x7a, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x00, 0x12, 0x71, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x7a, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x00, 0x12, 0x77, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x73, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x77, 0x0a, 0x0d, 0x50, 0x75,
	0x73, 0x68, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x12, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75,
	0x73, 0x68, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x00, 0x12, 0x96, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73,
	0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x63,
	0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x89, 0x01, 0x0a,
	0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescOnce sync.Once
	file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescData = file_moego_enterprise_campaign_v1_campaign_api_proto_rawDesc
)

func file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescGZIP() []byte {
	file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescOnce.Do(func() {
		file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescData)
	})
	return file_moego_enterprise_campaign_v1_campaign_api_proto_rawDescData
}

var file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_moego_enterprise_campaign_v1_campaign_api_proto_goTypes = []interface{}{
	(*SendTestEmailParams)(nil),                      // 0: moego.enterprise.campaign.v1.SendTestEmailParams
	(*SendTestEmailResult)(nil),                      // 1: moego.enterprise.campaign.v1.SendTestEmailResult
	(*CreateTemplateParams)(nil),                     // 2: moego.enterprise.campaign.v1.CreateTemplateParams
	(*CreateTemplateResult)(nil),                     // 3: moego.enterprise.campaign.v1.CreateTemplateResult
	(*GetTemplateParams)(nil),                        // 4: moego.enterprise.campaign.v1.GetTemplateParams
	(*GetTemplateResult)(nil),                        // 5: moego.enterprise.campaign.v1.GetTemplateResult
	(*UpdateTemplateParams)(nil),                     // 6: moego.enterprise.campaign.v1.UpdateTemplateParams
	(*UpdateTemplateResult)(nil),                     // 7: moego.enterprise.campaign.v1.UpdateTemplateResult
	(*ListTemplatesParams)(nil),                      // 8: moego.enterprise.campaign.v1.ListTemplatesParams
	(*ListTemplatesResult)(nil),                      // 9: moego.enterprise.campaign.v1.ListTemplatesResult
	(*PushTemplatesParams)(nil),                      // 10: moego.enterprise.campaign.v1.PushTemplatesParams
	(*PushTemplatesResult)(nil),                      // 11: moego.enterprise.campaign.v1.PushTemplatesResult
	(*ListTemplatePlaceholdersParams)(nil),           // 12: moego.enterprise.campaign.v1.ListTemplatePlaceholdersParams
	(*ListTemplatePlaceholdersResult)(nil),           // 13: moego.enterprise.campaign.v1.ListTemplatePlaceholdersResult
	(v1.Campaign_Type)(0),                            // 14: moego.models.enterprise.v1.Campaign.Type
	(*v1.CampaignTemplate)(nil),                      // 15: moego.models.enterprise.v1.CampaignTemplate
	(*v2.PaginationRequest)(nil),                     // 16: moego.utils.v2.PaginationRequest
	(*v11.ListTemplatesRequest_Filter)(nil),          // 17: moego.service.enterprise.v1.ListTemplatesRequest.Filter
	(*v2.PaginationResponse)(nil),                    // 18: moego.utils.v2.PaginationResponse
	(*v1.TenantObject)(nil),                          // 19: moego.models.enterprise.v1.TenantObject
	(*v12.MessageTemplatePlaceholderSimpleView)(nil), // 20: moego.models.message.v1.MessageTemplatePlaceholderSimpleView
}
var file_moego_enterprise_campaign_v1_campaign_api_proto_depIdxs = []int32{
	14, // 0: moego.enterprise.campaign.v1.CreateTemplateParams.type:type_name -> moego.models.enterprise.v1.Campaign.Type
	15, // 1: moego.enterprise.campaign.v1.CreateTemplateResult.template:type_name -> moego.models.enterprise.v1.CampaignTemplate
	15, // 2: moego.enterprise.campaign.v1.GetTemplateResult.template:type_name -> moego.models.enterprise.v1.CampaignTemplate
	15, // 3: moego.enterprise.campaign.v1.UpdateTemplateResult.template:type_name -> moego.models.enterprise.v1.CampaignTemplate
	16, // 4: moego.enterprise.campaign.v1.ListTemplatesParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	17, // 5: moego.enterprise.campaign.v1.ListTemplatesParams.filter:type_name -> moego.service.enterprise.v1.ListTemplatesRequest.Filter
	18, // 6: moego.enterprise.campaign.v1.ListTemplatesResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	15, // 7: moego.enterprise.campaign.v1.ListTemplatesResult.templates:type_name -> moego.models.enterprise.v1.CampaignTemplate
	19, // 8: moego.enterprise.campaign.v1.PushTemplatesParams.targets:type_name -> moego.models.enterprise.v1.TenantObject
	14, // 9: moego.enterprise.campaign.v1.ListTemplatePlaceholdersParams.type:type_name -> moego.models.enterprise.v1.Campaign.Type
	20, // 10: moego.enterprise.campaign.v1.ListTemplatePlaceholdersResult.placeholders:type_name -> moego.models.message.v1.MessageTemplatePlaceholderSimpleView
	0,  // 11: moego.enterprise.campaign.v1.CampaignService.SendTestEmail:input_type -> moego.enterprise.campaign.v1.SendTestEmailParams
	2,  // 12: moego.enterprise.campaign.v1.CampaignService.CreateTemplate:input_type -> moego.enterprise.campaign.v1.CreateTemplateParams
	4,  // 13: moego.enterprise.campaign.v1.CampaignService.GetTemplate:input_type -> moego.enterprise.campaign.v1.GetTemplateParams
	6,  // 14: moego.enterprise.campaign.v1.CampaignService.UpdateTemplate:input_type -> moego.enterprise.campaign.v1.UpdateTemplateParams
	8,  // 15: moego.enterprise.campaign.v1.CampaignService.ListTemplates:input_type -> moego.enterprise.campaign.v1.ListTemplatesParams
	10, // 16: moego.enterprise.campaign.v1.CampaignService.PushTemplates:input_type -> moego.enterprise.campaign.v1.PushTemplatesParams
	12, // 17: moego.enterprise.campaign.v1.CampaignService.ListTemplatePlaceholders:input_type -> moego.enterprise.campaign.v1.ListTemplatePlaceholdersParams
	1,  // 18: moego.enterprise.campaign.v1.CampaignService.SendTestEmail:output_type -> moego.enterprise.campaign.v1.SendTestEmailResult
	3,  // 19: moego.enterprise.campaign.v1.CampaignService.CreateTemplate:output_type -> moego.enterprise.campaign.v1.CreateTemplateResult
	5,  // 20: moego.enterprise.campaign.v1.CampaignService.GetTemplate:output_type -> moego.enterprise.campaign.v1.GetTemplateResult
	7,  // 21: moego.enterprise.campaign.v1.CampaignService.UpdateTemplate:output_type -> moego.enterprise.campaign.v1.UpdateTemplateResult
	9,  // 22: moego.enterprise.campaign.v1.CampaignService.ListTemplates:output_type -> moego.enterprise.campaign.v1.ListTemplatesResult
	11, // 23: moego.enterprise.campaign.v1.CampaignService.PushTemplates:output_type -> moego.enterprise.campaign.v1.PushTemplatesResult
	13, // 24: moego.enterprise.campaign.v1.CampaignService.ListTemplatePlaceholders:output_type -> moego.enterprise.campaign.v1.ListTemplatePlaceholdersResult
	18, // [18:25] is the sub-list for method output_type
	11, // [11:18] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_moego_enterprise_campaign_v1_campaign_api_proto_init() }
func file_moego_enterprise_campaign_v1_campaign_api_proto_init() {
	if File_moego_enterprise_campaign_v1_campaign_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendTestEmailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendTestEmailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTemplateParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTemplateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTemplateParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTemplateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTemplateParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTemplateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushTemplatesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushTemplatesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePlaceholdersParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePlaceholdersResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_enterprise_campaign_v1_campaign_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_enterprise_campaign_v1_campaign_api_proto_goTypes,
		DependencyIndexes: file_moego_enterprise_campaign_v1_campaign_api_proto_depIdxs,
		MessageInfos:      file_moego_enterprise_campaign_v1_campaign_api_proto_msgTypes,
	}.Build()
	File_moego_enterprise_campaign_v1_campaign_api_proto = out.File
	file_moego_enterprise_campaign_v1_campaign_api_proto_rawDesc = nil
	file_moego_enterprise_campaign_v1_campaign_api_proto_goTypes = nil
	file_moego_enterprise_campaign_v1_campaign_api_proto_depIdxs = nil
}
