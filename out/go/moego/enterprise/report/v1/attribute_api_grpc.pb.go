// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/report/v1/attribute_api.proto

package reportapipb

import (
	context "context"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// EnterpriseAttributeServiceClient is the client API for EnterpriseAttributeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EnterpriseAttributeServiceClient interface {
	// Get dimensions
	GetDimensions(ctx context.Context, in *v2.GetDimensionsParams, opts ...grpc.CallOption) (*v2.GetDimensionsResult, error)
	// Get metrics categories
	GetMetricsCategories(ctx context.Context, in *v2.GetMetricsCategoriesParams, opts ...grpc.CallOption) (*v2.GetMetricsCategoriesResult, error)
}

type enterpriseAttributeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEnterpriseAttributeServiceClient(cc grpc.ClientConnInterface) EnterpriseAttributeServiceClient {
	return &enterpriseAttributeServiceClient{cc}
}

func (c *enterpriseAttributeServiceClient) GetDimensions(ctx context.Context, in *v2.GetDimensionsParams, opts ...grpc.CallOption) (*v2.GetDimensionsResult, error) {
	out := new(v2.GetDimensionsResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.report.v1.EnterpriseAttributeService/GetDimensions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseAttributeServiceClient) GetMetricsCategories(ctx context.Context, in *v2.GetMetricsCategoriesParams, opts ...grpc.CallOption) (*v2.GetMetricsCategoriesResult, error) {
	out := new(v2.GetMetricsCategoriesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.report.v1.EnterpriseAttributeService/GetMetricsCategories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EnterpriseAttributeServiceServer is the server API for EnterpriseAttributeService service.
// All implementations must embed UnimplementedEnterpriseAttributeServiceServer
// for forward compatibility
type EnterpriseAttributeServiceServer interface {
	// Get dimensions
	GetDimensions(context.Context, *v2.GetDimensionsParams) (*v2.GetDimensionsResult, error)
	// Get metrics categories
	GetMetricsCategories(context.Context, *v2.GetMetricsCategoriesParams) (*v2.GetMetricsCategoriesResult, error)
	mustEmbedUnimplementedEnterpriseAttributeServiceServer()
}

// UnimplementedEnterpriseAttributeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedEnterpriseAttributeServiceServer struct {
}

func (UnimplementedEnterpriseAttributeServiceServer) GetDimensions(context.Context, *v2.GetDimensionsParams) (*v2.GetDimensionsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDimensions not implemented")
}
func (UnimplementedEnterpriseAttributeServiceServer) GetMetricsCategories(context.Context, *v2.GetMetricsCategoriesParams) (*v2.GetMetricsCategoriesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMetricsCategories not implemented")
}
func (UnimplementedEnterpriseAttributeServiceServer) mustEmbedUnimplementedEnterpriseAttributeServiceServer() {
}

// UnsafeEnterpriseAttributeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EnterpriseAttributeServiceServer will
// result in compilation errors.
type UnsafeEnterpriseAttributeServiceServer interface {
	mustEmbedUnimplementedEnterpriseAttributeServiceServer()
}

func RegisterEnterpriseAttributeServiceServer(s grpc.ServiceRegistrar, srv EnterpriseAttributeServiceServer) {
	s.RegisterService(&EnterpriseAttributeService_ServiceDesc, srv)
}

func _EnterpriseAttributeService_GetDimensions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.GetDimensionsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseAttributeServiceServer).GetDimensions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.report.v1.EnterpriseAttributeService/GetDimensions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseAttributeServiceServer).GetDimensions(ctx, req.(*v2.GetDimensionsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseAttributeService_GetMetricsCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.GetMetricsCategoriesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseAttributeServiceServer).GetMetricsCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.report.v1.EnterpriseAttributeService/GetMetricsCategories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseAttributeServiceServer).GetMetricsCategories(ctx, req.(*v2.GetMetricsCategoriesParams))
	}
	return interceptor(ctx, in, info, handler)
}

// EnterpriseAttributeService_ServiceDesc is the grpc.ServiceDesc for EnterpriseAttributeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EnterpriseAttributeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.report.v1.EnterpriseAttributeService",
	HandlerType: (*EnterpriseAttributeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetDimensions",
			Handler:    _EnterpriseAttributeService_GetDimensions_Handler,
		},
		{
			MethodName: "GetMetricsCategories",
			Handler:    _EnterpriseAttributeService_GetMetricsCategories_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/report/v1/attribute_api.proto",
}
