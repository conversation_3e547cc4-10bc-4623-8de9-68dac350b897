// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/agreement/v1/agreement_record_admin.proto

package agreementapipb

import (
	context "context"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AgreementRecordServiceClient is the client API for AgreementRecordService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AgreementRecordServiceClient interface {
	// get agreement with recent signed record list
	GetRecentSignedAgreementList(ctx context.Context, in *GetRecentSignedAgreementListParams, opts ...grpc.CallOption) (*GetRecentSignedAgreementListResult, error)
	// query agreement record list
	GetRecordList(ctx context.Context, in *GetRecordListParams, opts ...grpc.CallOption) (*GetRecordListResult, error)
	// get an agreement record detail by id
	GetRecord(ctx context.Context, in *GetRecordParams, opts ...grpc.CallOption) (*v1.AgreementRecordModel, error)
	// get an agreement record by id
	GetRecordSimpleView(ctx context.Context, in *GetRecordParams, opts ...grpc.CallOption) (*v1.AgreementRecordSimpleView, error)
}

type agreementRecordServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAgreementRecordServiceClient(cc grpc.ClientConnInterface) AgreementRecordServiceClient {
	return &agreementRecordServiceClient{cc}
}

func (c *agreementRecordServiceClient) GetRecentSignedAgreementList(ctx context.Context, in *GetRecentSignedAgreementListParams, opts ...grpc.CallOption) (*GetRecentSignedAgreementListResult, error) {
	out := new(GetRecentSignedAgreementListResult)
	err := c.cc.Invoke(ctx, "/moego.admin.agreement.v1.AgreementRecordService/GetRecentSignedAgreementList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) GetRecordList(ctx context.Context, in *GetRecordListParams, opts ...grpc.CallOption) (*GetRecordListResult, error) {
	out := new(GetRecordListResult)
	err := c.cc.Invoke(ctx, "/moego.admin.agreement.v1.AgreementRecordService/GetRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) GetRecord(ctx context.Context, in *GetRecordParams, opts ...grpc.CallOption) (*v1.AgreementRecordModel, error) {
	out := new(v1.AgreementRecordModel)
	err := c.cc.Invoke(ctx, "/moego.admin.agreement.v1.AgreementRecordService/GetRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agreementRecordServiceClient) GetRecordSimpleView(ctx context.Context, in *GetRecordParams, opts ...grpc.CallOption) (*v1.AgreementRecordSimpleView, error) {
	out := new(v1.AgreementRecordSimpleView)
	err := c.cc.Invoke(ctx, "/moego.admin.agreement.v1.AgreementRecordService/GetRecordSimpleView", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AgreementRecordServiceServer is the server API for AgreementRecordService service.
// All implementations must embed UnimplementedAgreementRecordServiceServer
// for forward compatibility
type AgreementRecordServiceServer interface {
	// get agreement with recent signed record list
	GetRecentSignedAgreementList(context.Context, *GetRecentSignedAgreementListParams) (*GetRecentSignedAgreementListResult, error)
	// query agreement record list
	GetRecordList(context.Context, *GetRecordListParams) (*GetRecordListResult, error)
	// get an agreement record detail by id
	GetRecord(context.Context, *GetRecordParams) (*v1.AgreementRecordModel, error)
	// get an agreement record by id
	GetRecordSimpleView(context.Context, *GetRecordParams) (*v1.AgreementRecordSimpleView, error)
	mustEmbedUnimplementedAgreementRecordServiceServer()
}

// UnimplementedAgreementRecordServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAgreementRecordServiceServer struct {
}

func (UnimplementedAgreementRecordServiceServer) GetRecentSignedAgreementList(context.Context, *GetRecentSignedAgreementListParams) (*GetRecentSignedAgreementListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecentSignedAgreementList not implemented")
}
func (UnimplementedAgreementRecordServiceServer) GetRecordList(context.Context, *GetRecordListParams) (*GetRecordListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecordList not implemented")
}
func (UnimplementedAgreementRecordServiceServer) GetRecord(context.Context, *GetRecordParams) (*v1.AgreementRecordModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecord not implemented")
}
func (UnimplementedAgreementRecordServiceServer) GetRecordSimpleView(context.Context, *GetRecordParams) (*v1.AgreementRecordSimpleView, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecordSimpleView not implemented")
}
func (UnimplementedAgreementRecordServiceServer) mustEmbedUnimplementedAgreementRecordServiceServer() {
}

// UnsafeAgreementRecordServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AgreementRecordServiceServer will
// result in compilation errors.
type UnsafeAgreementRecordServiceServer interface {
	mustEmbedUnimplementedAgreementRecordServiceServer()
}

func RegisterAgreementRecordServiceServer(s grpc.ServiceRegistrar, srv AgreementRecordServiceServer) {
	s.RegisterService(&AgreementRecordService_ServiceDesc, srv)
}

func _AgreementRecordService_GetRecentSignedAgreementList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecentSignedAgreementListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecentSignedAgreementList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.agreement.v1.AgreementRecordService/GetRecentSignedAgreementList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecentSignedAgreementList(ctx, req.(*GetRecentSignedAgreementListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_GetRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecordListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.agreement.v1.AgreementRecordService/GetRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecordList(ctx, req.(*GetRecordListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_GetRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecordParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.agreement.v1.AgreementRecordService/GetRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecord(ctx, req.(*GetRecordParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgreementRecordService_GetRecordSimpleView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecordParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecordSimpleView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.agreement.v1.AgreementRecordService/GetRecordSimpleView",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecordSimpleView(ctx, req.(*GetRecordParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AgreementRecordService_ServiceDesc is the grpc.ServiceDesc for AgreementRecordService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AgreementRecordService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.agreement.v1.AgreementRecordService",
	HandlerType: (*AgreementRecordServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRecentSignedAgreementList",
			Handler:    _AgreementRecordService_GetRecentSignedAgreementList_Handler,
		},
		{
			MethodName: "GetRecordList",
			Handler:    _AgreementRecordService_GetRecordList_Handler,
		},
		{
			MethodName: "GetRecord",
			Handler:    _AgreementRecordService_GetRecord_Handler,
		},
		{
			MethodName: "GetRecordSimpleView",
			Handler:    _AgreementRecordService_GetRecordSimpleView_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/agreement/v1/agreement_record_admin.proto",
}
