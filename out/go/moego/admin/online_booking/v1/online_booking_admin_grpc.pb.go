// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/online_booking/v1/online_booking_admin.proto

package onlinebookingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// OnlineBookingServiceClient is the client API for OnlineBookingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OnlineBookingServiceClient interface {
	// check ob session
	CheckSession(ctx context.Context, in *CheckSessionParams, opts ...grpc.CallOption) (*CheckSessionResult, error)
	// create ob impersonate session
	Impersonate(ctx context.Context, in *ImpersonateParams, opts ...grpc.CallOption) (*ImpersonateResult, error)
	// remove ob impersonate session
	RemoveImpersonate(ctx context.Context, in *RemoveImpersonateParams, opts ...grpc.CallOption) (*RemoveImpersonateResult, error)
}

type onlineBookingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOnlineBookingServiceClient(cc grpc.ClientConnInterface) OnlineBookingServiceClient {
	return &onlineBookingServiceClient{cc}
}

func (c *onlineBookingServiceClient) CheckSession(ctx context.Context, in *CheckSessionParams, opts ...grpc.CallOption) (*CheckSessionResult, error) {
	out := new(CheckSessionResult)
	err := c.cc.Invoke(ctx, "/moego.admin.online_booking.v1.OnlineBookingService/CheckSession", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onlineBookingServiceClient) Impersonate(ctx context.Context, in *ImpersonateParams, opts ...grpc.CallOption) (*ImpersonateResult, error) {
	out := new(ImpersonateResult)
	err := c.cc.Invoke(ctx, "/moego.admin.online_booking.v1.OnlineBookingService/Impersonate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onlineBookingServiceClient) RemoveImpersonate(ctx context.Context, in *RemoveImpersonateParams, opts ...grpc.CallOption) (*RemoveImpersonateResult, error) {
	out := new(RemoveImpersonateResult)
	err := c.cc.Invoke(ctx, "/moego.admin.online_booking.v1.OnlineBookingService/RemoveImpersonate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OnlineBookingServiceServer is the server API for OnlineBookingService service.
// All implementations must embed UnimplementedOnlineBookingServiceServer
// for forward compatibility
type OnlineBookingServiceServer interface {
	// check ob session
	CheckSession(context.Context, *CheckSessionParams) (*CheckSessionResult, error)
	// create ob impersonate session
	Impersonate(context.Context, *ImpersonateParams) (*ImpersonateResult, error)
	// remove ob impersonate session
	RemoveImpersonate(context.Context, *RemoveImpersonateParams) (*RemoveImpersonateResult, error)
	mustEmbedUnimplementedOnlineBookingServiceServer()
}

// UnimplementedOnlineBookingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedOnlineBookingServiceServer struct {
}

func (UnimplementedOnlineBookingServiceServer) CheckSession(context.Context, *CheckSessionParams) (*CheckSessionResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckSession not implemented")
}
func (UnimplementedOnlineBookingServiceServer) Impersonate(context.Context, *ImpersonateParams) (*ImpersonateResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Impersonate not implemented")
}
func (UnimplementedOnlineBookingServiceServer) RemoveImpersonate(context.Context, *RemoveImpersonateParams) (*RemoveImpersonateResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveImpersonate not implemented")
}
func (UnimplementedOnlineBookingServiceServer) mustEmbedUnimplementedOnlineBookingServiceServer() {}

// UnsafeOnlineBookingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OnlineBookingServiceServer will
// result in compilation errors.
type UnsafeOnlineBookingServiceServer interface {
	mustEmbedUnimplementedOnlineBookingServiceServer()
}

func RegisterOnlineBookingServiceServer(s grpc.ServiceRegistrar, srv OnlineBookingServiceServer) {
	s.RegisterService(&OnlineBookingService_ServiceDesc, srv)
}

func _OnlineBookingService_CheckSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckSessionParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnlineBookingServiceServer).CheckSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.online_booking.v1.OnlineBookingService/CheckSession",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnlineBookingServiceServer).CheckSession(ctx, req.(*CheckSessionParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnlineBookingService_Impersonate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImpersonateParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnlineBookingServiceServer).Impersonate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.online_booking.v1.OnlineBookingService/Impersonate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnlineBookingServiceServer).Impersonate(ctx, req.(*ImpersonateParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _OnlineBookingService_RemoveImpersonate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveImpersonateParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnlineBookingServiceServer).RemoveImpersonate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.online_booking.v1.OnlineBookingService/RemoveImpersonate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnlineBookingServiceServer).RemoveImpersonate(ctx, req.(*RemoveImpersonateParams))
	}
	return interceptor(ctx, in, info, handler)
}

// OnlineBookingService_ServiceDesc is the grpc.ServiceDesc for OnlineBookingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OnlineBookingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.online_booking.v1.OnlineBookingService",
	HandlerType: (*OnlineBookingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckSession",
			Handler:    _OnlineBookingService_CheckSession_Handler,
		},
		{
			MethodName: "Impersonate",
			Handler:    _OnlineBookingService_Impersonate_Handler,
		},
		{
			MethodName: "RemoveImpersonate",
			Handler:    _OnlineBookingService_RemoveImpersonate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/online_booking/v1/online_booking_admin.proto",
}
