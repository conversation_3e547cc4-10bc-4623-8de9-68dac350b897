// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/account/v1/session_admin.proto

package accountapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SessionServiceClient is the client API for SessionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SessionServiceClient interface {
	// describe sessions
	DescribeSessions(ctx context.Context, in *DescribeSessionsParams, opts ...grpc.CallOption) (*DescribeSessionsResult, error)
	// batch delete sessions
	BatchDeleteSessions(ctx context.Context, in *BatchDeleteSessionsParams, opts ...grpc.CallOption) (*BatchDeleteSessionsResult, error)
	// batch update sessions
	BatchUpdateSessions(ctx context.Context, in *BatchUpdateSessionsParams, opts ...grpc.CallOption) (*BatchUpdateSessionsResult, error)
	// create session archive task
	CreateSessionArchiveTask(ctx context.Context, in *CreateSessionArchiveTaskParams, opts ...grpc.CallOption) (*CreateSessionArchiveTaskResult, error)
	// update session archive task status
	UpdateSessionArchiveTaskStatus(ctx context.Context, in *UpdateSessionArchiveTaskStatusParams, opts ...grpc.CallOption) (*UpdateSessionArchiveTaskStatusResult, error)
}

type sessionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSessionServiceClient(cc grpc.ClientConnInterface) SessionServiceClient {
	return &sessionServiceClient{cc}
}

func (c *sessionServiceClient) DescribeSessions(ctx context.Context, in *DescribeSessionsParams, opts ...grpc.CallOption) (*DescribeSessionsResult, error) {
	out := new(DescribeSessionsResult)
	err := c.cc.Invoke(ctx, "/moego.admin.account.v1.SessionService/DescribeSessions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) BatchDeleteSessions(ctx context.Context, in *BatchDeleteSessionsParams, opts ...grpc.CallOption) (*BatchDeleteSessionsResult, error) {
	out := new(BatchDeleteSessionsResult)
	err := c.cc.Invoke(ctx, "/moego.admin.account.v1.SessionService/BatchDeleteSessions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) BatchUpdateSessions(ctx context.Context, in *BatchUpdateSessionsParams, opts ...grpc.CallOption) (*BatchUpdateSessionsResult, error) {
	out := new(BatchUpdateSessionsResult)
	err := c.cc.Invoke(ctx, "/moego.admin.account.v1.SessionService/BatchUpdateSessions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) CreateSessionArchiveTask(ctx context.Context, in *CreateSessionArchiveTaskParams, opts ...grpc.CallOption) (*CreateSessionArchiveTaskResult, error) {
	out := new(CreateSessionArchiveTaskResult)
	err := c.cc.Invoke(ctx, "/moego.admin.account.v1.SessionService/CreateSessionArchiveTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sessionServiceClient) UpdateSessionArchiveTaskStatus(ctx context.Context, in *UpdateSessionArchiveTaskStatusParams, opts ...grpc.CallOption) (*UpdateSessionArchiveTaskStatusResult, error) {
	out := new(UpdateSessionArchiveTaskStatusResult)
	err := c.cc.Invoke(ctx, "/moego.admin.account.v1.SessionService/UpdateSessionArchiveTaskStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SessionServiceServer is the server API for SessionService service.
// All implementations must embed UnimplementedSessionServiceServer
// for forward compatibility
type SessionServiceServer interface {
	// describe sessions
	DescribeSessions(context.Context, *DescribeSessionsParams) (*DescribeSessionsResult, error)
	// batch delete sessions
	BatchDeleteSessions(context.Context, *BatchDeleteSessionsParams) (*BatchDeleteSessionsResult, error)
	// batch update sessions
	BatchUpdateSessions(context.Context, *BatchUpdateSessionsParams) (*BatchUpdateSessionsResult, error)
	// create session archive task
	CreateSessionArchiveTask(context.Context, *CreateSessionArchiveTaskParams) (*CreateSessionArchiveTaskResult, error)
	// update session archive task status
	UpdateSessionArchiveTaskStatus(context.Context, *UpdateSessionArchiveTaskStatusParams) (*UpdateSessionArchiveTaskStatusResult, error)
	mustEmbedUnimplementedSessionServiceServer()
}

// UnimplementedSessionServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSessionServiceServer struct {
}

func (UnimplementedSessionServiceServer) DescribeSessions(context.Context, *DescribeSessionsParams) (*DescribeSessionsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeSessions not implemented")
}
func (UnimplementedSessionServiceServer) BatchDeleteSessions(context.Context, *BatchDeleteSessionsParams) (*BatchDeleteSessionsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchDeleteSessions not implemented")
}
func (UnimplementedSessionServiceServer) BatchUpdateSessions(context.Context, *BatchUpdateSessionsParams) (*BatchUpdateSessionsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpdateSessions not implemented")
}
func (UnimplementedSessionServiceServer) CreateSessionArchiveTask(context.Context, *CreateSessionArchiveTaskParams) (*CreateSessionArchiveTaskResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSessionArchiveTask not implemented")
}
func (UnimplementedSessionServiceServer) UpdateSessionArchiveTaskStatus(context.Context, *UpdateSessionArchiveTaskStatusParams) (*UpdateSessionArchiveTaskStatusResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSessionArchiveTaskStatus not implemented")
}
func (UnimplementedSessionServiceServer) mustEmbedUnimplementedSessionServiceServer() {}

// UnsafeSessionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SessionServiceServer will
// result in compilation errors.
type UnsafeSessionServiceServer interface {
	mustEmbedUnimplementedSessionServiceServer()
}

func RegisterSessionServiceServer(s grpc.ServiceRegistrar, srv SessionServiceServer) {
	s.RegisterService(&SessionService_ServiceDesc, srv)
}

func _SessionService_DescribeSessions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeSessionsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).DescribeSessions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.account.v1.SessionService/DescribeSessions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).DescribeSessions(ctx, req.(*DescribeSessionsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_BatchDeleteSessions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteSessionsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).BatchDeleteSessions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.account.v1.SessionService/BatchDeleteSessions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).BatchDeleteSessions(ctx, req.(*BatchDeleteSessionsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_BatchUpdateSessions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateSessionsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).BatchUpdateSessions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.account.v1.SessionService/BatchUpdateSessions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).BatchUpdateSessions(ctx, req.(*BatchUpdateSessionsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_CreateSessionArchiveTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSessionArchiveTaskParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).CreateSessionArchiveTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.account.v1.SessionService/CreateSessionArchiveTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).CreateSessionArchiveTask(ctx, req.(*CreateSessionArchiveTaskParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _SessionService_UpdateSessionArchiveTaskStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSessionArchiveTaskStatusParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SessionServiceServer).UpdateSessionArchiveTaskStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.account.v1.SessionService/UpdateSessionArchiveTaskStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SessionServiceServer).UpdateSessionArchiveTaskStatus(ctx, req.(*UpdateSessionArchiveTaskStatusParams))
	}
	return interceptor(ctx, in, info, handler)
}

// SessionService_ServiceDesc is the grpc.ServiceDesc for SessionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SessionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.account.v1.SessionService",
	HandlerType: (*SessionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DescribeSessions",
			Handler:    _SessionService_DescribeSessions_Handler,
		},
		{
			MethodName: "BatchDeleteSessions",
			Handler:    _SessionService_BatchDeleteSessions_Handler,
		},
		{
			MethodName: "BatchUpdateSessions",
			Handler:    _SessionService_BatchUpdateSessions_Handler,
		},
		{
			MethodName: "CreateSessionArchiveTask",
			Handler:    _SessionService_CreateSessionArchiveTask_Handler,
		},
		{
			MethodName: "UpdateSessionArchiveTaskStatus",
			Handler:    _SessionService_UpdateSessionArchiveTaskStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/account/v1/session_admin.proto",
}
