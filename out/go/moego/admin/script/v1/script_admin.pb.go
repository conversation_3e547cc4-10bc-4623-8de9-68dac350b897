// @since 2023-05-23 11:43:54
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/script/v1/script_admin.proto

package scriptapipb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// wash business session params
type WashBusinessSessionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start account id
	StartAccountId int64 `protobuf:"varint,1,opt,name=start_account_id,json=startAccountId,proto3" json:"start_account_id,omitempty"`
	// end account id
	EndAccountId int64 `protobuf:"varint,2,opt,name=end_account_id,json=endAccountId,proto3" json:"end_account_id,omitempty"`
}

func (x *WashBusinessSessionParams) Reset() {
	*x = WashBusinessSessionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_script_v1_script_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WashBusinessSessionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WashBusinessSessionParams) ProtoMessage() {}

func (x *WashBusinessSessionParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_script_v1_script_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WashBusinessSessionParams.ProtoReflect.Descriptor instead.
func (*WashBusinessSessionParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_script_v1_script_admin_proto_rawDescGZIP(), []int{0}
}

func (x *WashBusinessSessionParams) GetStartAccountId() int64 {
	if x != nil {
		return x.StartAccountId
	}
	return 0
}

func (x *WashBusinessSessionParams) GetEndAccountId() int64 {
	if x != nil {
		return x.EndAccountId
	}
	return 0
}

// wash business session result
type WashBusinessSessionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WashBusinessSessionResult) Reset() {
	*x = WashBusinessSessionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_script_v1_script_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WashBusinessSessionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WashBusinessSessionResult) ProtoMessage() {}

func (x *WashBusinessSessionResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_script_v1_script_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WashBusinessSessionResult.ProtoReflect.Descriptor instead.
func (*WashBusinessSessionResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_script_v1_script_admin_proto_rawDescGZIP(), []int{1}
}

var File_moego_admin_script_v1_script_admin_proto protoreflect.FileDescriptor

var file_moego_admin_script_v1_script_admin_proto_rawDesc = []byte{
	0x0a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x76,
	0x31, 0x22, 0x6b, 0x0a, 0x19, 0x57, 0x61, 0x73, 0x68, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28,
	0x0a, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x6e, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x65, 0x6e, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x1b,
	0x0a, 0x19, 0x57, 0x61, 0x73, 0x68, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0x8a, 0x01, 0x0a, 0x0d,
	0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x79, 0x0a,
	0x13, 0x57, 0x61, 0x73, 0x68, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x73,
	0x68, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x61, 0x73, 0x68, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x79, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x61, 0x70,
	0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_script_v1_script_admin_proto_rawDescOnce sync.Once
	file_moego_admin_script_v1_script_admin_proto_rawDescData = file_moego_admin_script_v1_script_admin_proto_rawDesc
)

func file_moego_admin_script_v1_script_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_script_v1_script_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_script_v1_script_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_script_v1_script_admin_proto_rawDescData)
	})
	return file_moego_admin_script_v1_script_admin_proto_rawDescData
}

var file_moego_admin_script_v1_script_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_admin_script_v1_script_admin_proto_goTypes = []interface{}{
	(*WashBusinessSessionParams)(nil), // 0: moego.admin.script.v1.WashBusinessSessionParams
	(*WashBusinessSessionResult)(nil), // 1: moego.admin.script.v1.WashBusinessSessionResult
}
var file_moego_admin_script_v1_script_admin_proto_depIdxs = []int32{
	0, // 0: moego.admin.script.v1.ScriptService.WashBusinessSession:input_type -> moego.admin.script.v1.WashBusinessSessionParams
	1, // 1: moego.admin.script.v1.ScriptService.WashBusinessSession:output_type -> moego.admin.script.v1.WashBusinessSessionResult
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_admin_script_v1_script_admin_proto_init() }
func file_moego_admin_script_v1_script_admin_proto_init() {
	if File_moego_admin_script_v1_script_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_script_v1_script_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WashBusinessSessionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_script_v1_script_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WashBusinessSessionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_script_v1_script_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_script_v1_script_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_script_v1_script_admin_proto_depIdxs,
		MessageInfos:      file_moego_admin_script_v1_script_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_script_v1_script_admin_proto = out.File
	file_moego_admin_script_v1_script_admin_proto_rawDesc = nil
	file_moego_admin_script_v1_script_admin_proto_goTypes = nil
	file_moego_admin_script_v1_script_admin_proto_depIdxs = nil
}
