// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/pay_ops/v1/quick_book.proto

package payopsapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// QuickBookServiceClient is the client API for QuickBookService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type QuickBookServiceClient interface {
	// list quick book setting record
	ListQuickBooksSetting(ctx context.Context, in *ListQuickBooksSettingParams, opts ...grpc.CallOption) (*ListQuickBooksSettingResult, error)
	// modify receipt status
	UpdateQuickBooksSetting(ctx context.Context, in *UpdateQuickBooksSettingParams, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Compensate
	Compensate(ctx context.Context, in *CompensateParams, opts ...grpc.CallOption) (*CompensateResult, error)
	// list quick book sync records invoices
	ListSyncInvoices(ctx context.Context, in *ListSyncInvoicesParams, opts ...grpc.CallOption) (*ListSyncInvoicesResult, error)
}

type quickBookServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewQuickBookServiceClient(cc grpc.ClientConnInterface) QuickBookServiceClient {
	return &quickBookServiceClient{cc}
}

func (c *quickBookServiceClient) ListQuickBooksSetting(ctx context.Context, in *ListQuickBooksSettingParams, opts ...grpc.CallOption) (*ListQuickBooksSettingResult, error) {
	out := new(ListQuickBooksSettingResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.QuickBookService/ListQuickBooksSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *quickBookServiceClient) UpdateQuickBooksSetting(ctx context.Context, in *UpdateQuickBooksSettingParams, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.QuickBookService/UpdateQuickBooksSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *quickBookServiceClient) Compensate(ctx context.Context, in *CompensateParams, opts ...grpc.CallOption) (*CompensateResult, error) {
	out := new(CompensateResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.QuickBookService/Compensate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *quickBookServiceClient) ListSyncInvoices(ctx context.Context, in *ListSyncInvoicesParams, opts ...grpc.CallOption) (*ListSyncInvoicesResult, error) {
	out := new(ListSyncInvoicesResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.QuickBookService/ListSyncInvoices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// QuickBookServiceServer is the server API for QuickBookService service.
// All implementations must embed UnimplementedQuickBookServiceServer
// for forward compatibility
type QuickBookServiceServer interface {
	// list quick book setting record
	ListQuickBooksSetting(context.Context, *ListQuickBooksSettingParams) (*ListQuickBooksSettingResult, error)
	// modify receipt status
	UpdateQuickBooksSetting(context.Context, *UpdateQuickBooksSettingParams) (*emptypb.Empty, error)
	// Compensate
	Compensate(context.Context, *CompensateParams) (*CompensateResult, error)
	// list quick book sync records invoices
	ListSyncInvoices(context.Context, *ListSyncInvoicesParams) (*ListSyncInvoicesResult, error)
	mustEmbedUnimplementedQuickBookServiceServer()
}

// UnimplementedQuickBookServiceServer must be embedded to have forward compatible implementations.
type UnimplementedQuickBookServiceServer struct {
}

func (UnimplementedQuickBookServiceServer) ListQuickBooksSetting(context.Context, *ListQuickBooksSettingParams) (*ListQuickBooksSettingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListQuickBooksSetting not implemented")
}
func (UnimplementedQuickBookServiceServer) UpdateQuickBooksSetting(context.Context, *UpdateQuickBooksSettingParams) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateQuickBooksSetting not implemented")
}
func (UnimplementedQuickBookServiceServer) Compensate(context.Context, *CompensateParams) (*CompensateResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Compensate not implemented")
}
func (UnimplementedQuickBookServiceServer) ListSyncInvoices(context.Context, *ListSyncInvoicesParams) (*ListSyncInvoicesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSyncInvoices not implemented")
}
func (UnimplementedQuickBookServiceServer) mustEmbedUnimplementedQuickBookServiceServer() {}

// UnsafeQuickBookServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to QuickBookServiceServer will
// result in compilation errors.
type UnsafeQuickBookServiceServer interface {
	mustEmbedUnimplementedQuickBookServiceServer()
}

func RegisterQuickBookServiceServer(s grpc.ServiceRegistrar, srv QuickBookServiceServer) {
	s.RegisterService(&QuickBookService_ServiceDesc, srv)
}

func _QuickBookService_ListQuickBooksSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListQuickBooksSettingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuickBookServiceServer).ListQuickBooksSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.QuickBookService/ListQuickBooksSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuickBookServiceServer).ListQuickBooksSetting(ctx, req.(*ListQuickBooksSettingParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _QuickBookService_UpdateQuickBooksSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateQuickBooksSettingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuickBookServiceServer).UpdateQuickBooksSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.QuickBookService/UpdateQuickBooksSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuickBookServiceServer).UpdateQuickBooksSetting(ctx, req.(*UpdateQuickBooksSettingParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _QuickBookService_Compensate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompensateParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuickBookServiceServer).Compensate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.QuickBookService/Compensate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuickBookServiceServer).Compensate(ctx, req.(*CompensateParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _QuickBookService_ListSyncInvoices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSyncInvoicesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuickBookServiceServer).ListSyncInvoices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.QuickBookService/ListSyncInvoices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuickBookServiceServer).ListSyncInvoices(ctx, req.(*ListSyncInvoicesParams))
	}
	return interceptor(ctx, in, info, handler)
}

// QuickBookService_ServiceDesc is the grpc.ServiceDesc for QuickBookService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var QuickBookService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.pay_ops.v1.QuickBookService",
	HandlerType: (*QuickBookServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListQuickBooksSetting",
			Handler:    _QuickBookService_ListQuickBooksSetting_Handler,
		},
		{
			MethodName: "UpdateQuickBooksSetting",
			Handler:    _QuickBookService_UpdateQuickBooksSetting_Handler,
		},
		{
			MethodName: "Compensate",
			Handler:    _QuickBookService_Compensate_Handler,
		},
		{
			MethodName: "ListSyncInvoices",
			Handler:    _QuickBookService_ListSyncInvoices_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/pay_ops/v1/quick_book.proto",
}
