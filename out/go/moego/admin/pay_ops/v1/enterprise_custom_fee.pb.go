// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/pay_ops/v1/enterprise_custom_fee.proto

package payopsapipb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// enterprise custom fee
type EnterpriseCustomFee struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// account id
	AccountId int64 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// email
	Email string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,4,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// enterprise name
	EnterpriseName string `protobuf:"bytes,5,opt,name=enterprise_name,json=enterpriseName,proto3" json:"enterprise_name,omitempty"`
	// customized min vol
	CustomizedMinVol int64 `protobuf:"varint,6,opt,name=customized_min_vol,json=customizedMinVol,proto3" json:"customized_min_vol,omitempty"`
	// online fee rate
	OnlineFeeRate float64 `protobuf:"fixed64,7,opt,name=online_fee_rate,json=onlineFeeRate,proto3" json:"online_fee_rate,omitempty"`
	// online fee cents
	OnlineFeeCents uint32 `protobuf:"varint,8,opt,name=online_fee_cents,json=onlineFeeCents,proto3" json:"online_fee_cents,omitempty"`
	// reader fee rate
	ReaderFeeRate float64 `protobuf:"fixed64,9,opt,name=reader_fee_rate,json=readerFeeRate,proto3" json:"reader_fee_rate,omitempty"`
	// reader fee cents
	ReaderFeeCents uint32 `protobuf:"varint,10,opt,name=reader_fee_cents,json=readerFeeCents,proto3" json:"reader_fee_cents,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *EnterpriseCustomFee) Reset() {
	*x = EnterpriseCustomFee{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnterpriseCustomFee) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnterpriseCustomFee) ProtoMessage() {}

func (x *EnterpriseCustomFee) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnterpriseCustomFee.ProtoReflect.Descriptor instead.
func (*EnterpriseCustomFee) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDescGZIP(), []int{0}
}

func (x *EnterpriseCustomFee) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EnterpriseCustomFee) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *EnterpriseCustomFee) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *EnterpriseCustomFee) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *EnterpriseCustomFee) GetEnterpriseName() string {
	if x != nil {
		return x.EnterpriseName
	}
	return ""
}

func (x *EnterpriseCustomFee) GetCustomizedMinVol() int64 {
	if x != nil {
		return x.CustomizedMinVol
	}
	return 0
}

func (x *EnterpriseCustomFee) GetOnlineFeeRate() float64 {
	if x != nil {
		return x.OnlineFeeRate
	}
	return 0
}

func (x *EnterpriseCustomFee) GetOnlineFeeCents() uint32 {
	if x != nil {
		return x.OnlineFeeCents
	}
	return 0
}

func (x *EnterpriseCustomFee) GetReaderFeeRate() float64 {
	if x != nil {
		return x.ReaderFeeRate
	}
	return 0
}

func (x *EnterpriseCustomFee) GetReaderFeeCents() uint32 {
	if x != nil {
		return x.ReaderFeeCents
	}
	return 0
}

func (x *EnterpriseCustomFee) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *EnterpriseCustomFee) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// list company custom fee params
type ListEnterpriseCustomFeesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise ids
	EnterpriseId *int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3,oneof" json:"enterprise_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListEnterpriseCustomFeesParams) Reset() {
	*x = ListEnterpriseCustomFeesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEnterpriseCustomFeesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEnterpriseCustomFeesParams) ProtoMessage() {}

func (x *ListEnterpriseCustomFeesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEnterpriseCustomFeesParams.ProtoReflect.Descriptor instead.
func (*ListEnterpriseCustomFeesParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDescGZIP(), []int{1}
}

func (x *ListEnterpriseCustomFeesParams) GetEnterpriseId() int64 {
	if x != nil && x.EnterpriseId != nil {
		return *x.EnterpriseId
	}
	return 0
}

func (x *ListEnterpriseCustomFeesParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list enterprise custom fee params
type ListEnterpriseCustomFeesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise custom fees
	EnterpriseCustomFees []*EnterpriseCustomFee `protobuf:"bytes,1,rep,name=enterprise_custom_fees,json=enterpriseCustomFees,proto3" json:"enterprise_custom_fees,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListEnterpriseCustomFeesResult) Reset() {
	*x = ListEnterpriseCustomFeesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEnterpriseCustomFeesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEnterpriseCustomFeesResult) ProtoMessage() {}

func (x *ListEnterpriseCustomFeesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEnterpriseCustomFeesResult.ProtoReflect.Descriptor instead.
func (*ListEnterpriseCustomFeesResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDescGZIP(), []int{2}
}

func (x *ListEnterpriseCustomFeesResult) GetEnterpriseCustomFees() []*EnterpriseCustomFee {
	if x != nil {
		return x.EnterpriseCustomFees
	}
	return nil
}

func (x *ListEnterpriseCustomFeesResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// add enterprise custom fee params
type CreateEnterpriseCustomFeeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// online fee rate
	OnlineFeeRate float64 `protobuf:"fixed64,3,opt,name=online_fee_rate,json=onlineFeeRate,proto3" json:"online_fee_rate,omitempty"`
	// online fee cents
	OnlineFeeCents uint32 `protobuf:"varint,4,opt,name=online_fee_cents,json=onlineFeeCents,proto3" json:"online_fee_cents,omitempty"`
	// reader fee rate
	ReaderFeeRate float64 `protobuf:"fixed64,5,opt,name=reader_fee_rate,json=readerFeeRate,proto3" json:"reader_fee_rate,omitempty"`
	// reader fee cents
	ReaderFeeCents uint32 `protobuf:"varint,6,opt,name=reader_fee_cents,json=readerFeeCents,proto3" json:"reader_fee_cents,omitempty"`
	// customized min vol
	CustomizedMinVol *int64 `protobuf:"varint,7,opt,name=customized_min_vol,json=customizedMinVol,proto3,oneof" json:"customized_min_vol,omitempty"`
}

func (x *CreateEnterpriseCustomFeeParams) Reset() {
	*x = CreateEnterpriseCustomFeeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEnterpriseCustomFeeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEnterpriseCustomFeeParams) ProtoMessage() {}

func (x *CreateEnterpriseCustomFeeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEnterpriseCustomFeeParams.ProtoReflect.Descriptor instead.
func (*CreateEnterpriseCustomFeeParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDescGZIP(), []int{3}
}

func (x *CreateEnterpriseCustomFeeParams) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *CreateEnterpriseCustomFeeParams) GetOnlineFeeRate() float64 {
	if x != nil {
		return x.OnlineFeeRate
	}
	return 0
}

func (x *CreateEnterpriseCustomFeeParams) GetOnlineFeeCents() uint32 {
	if x != nil {
		return x.OnlineFeeCents
	}
	return 0
}

func (x *CreateEnterpriseCustomFeeParams) GetReaderFeeRate() float64 {
	if x != nil {
		return x.ReaderFeeRate
	}
	return 0
}

func (x *CreateEnterpriseCustomFeeParams) GetReaderFeeCents() uint32 {
	if x != nil {
		return x.ReaderFeeCents
	}
	return 0
}

func (x *CreateEnterpriseCustomFeeParams) GetCustomizedMinVol() int64 {
	if x != nil && x.CustomizedMinVol != nil {
		return *x.CustomizedMinVol
	}
	return 0
}

// update enterprise custom fee params
type UpdateEnterpriseCustomFeeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// enterprise id
	EnterpriseId int64 `protobuf:"varint,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// customized min vol
	CustomizedMinVol *int64 `protobuf:"varint,3,opt,name=customized_min_vol,json=customizedMinVol,proto3,oneof" json:"customized_min_vol,omitempty"`
	// online fee rate
	OnlineFeeRate float64 `protobuf:"fixed64,4,opt,name=online_fee_rate,json=onlineFeeRate,proto3" json:"online_fee_rate,omitempty"`
	// online fee cents
	OnlineFeeCents uint32 `protobuf:"varint,5,opt,name=online_fee_cents,json=onlineFeeCents,proto3" json:"online_fee_cents,omitempty"`
	// reader fee rate
	ReaderFeeRate float64 `protobuf:"fixed64,6,opt,name=reader_fee_rate,json=readerFeeRate,proto3" json:"reader_fee_rate,omitempty"`
	// reader fee cents
	ReaderFeeCents uint32 `protobuf:"varint,7,opt,name=reader_fee_cents,json=readerFeeCents,proto3" json:"reader_fee_cents,omitempty"`
}

func (x *UpdateEnterpriseCustomFeeParams) Reset() {
	*x = UpdateEnterpriseCustomFeeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEnterpriseCustomFeeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEnterpriseCustomFeeParams) ProtoMessage() {}

func (x *UpdateEnterpriseCustomFeeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEnterpriseCustomFeeParams.ProtoReflect.Descriptor instead.
func (*UpdateEnterpriseCustomFeeParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateEnterpriseCustomFeeParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateEnterpriseCustomFeeParams) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *UpdateEnterpriseCustomFeeParams) GetCustomizedMinVol() int64 {
	if x != nil && x.CustomizedMinVol != nil {
		return *x.CustomizedMinVol
	}
	return 0
}

func (x *UpdateEnterpriseCustomFeeParams) GetOnlineFeeRate() float64 {
	if x != nil {
		return x.OnlineFeeRate
	}
	return 0
}

func (x *UpdateEnterpriseCustomFeeParams) GetOnlineFeeCents() uint32 {
	if x != nil {
		return x.OnlineFeeCents
	}
	return 0
}

func (x *UpdateEnterpriseCustomFeeParams) GetReaderFeeRate() float64 {
	if x != nil {
		return x.ReaderFeeRate
	}
	return 0
}

func (x *UpdateEnterpriseCustomFeeParams) GetReaderFeeCents() uint32 {
	if x != nil {
		return x.ReaderFeeCents
	}
	return 0
}

// delete enterprise custom fee params
type DeleteEnterpriseCustomFeeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteEnterpriseCustomFeeParams) Reset() {
	*x = DeleteEnterpriseCustomFeeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEnterpriseCustomFeeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEnterpriseCustomFeeParams) ProtoMessage() {}

func (x *DeleteEnterpriseCustomFeeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEnterpriseCustomFeeParams.ProtoReflect.Descriptor instead.
func (*DeleteEnterpriseCustomFeeParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteEnterpriseCustomFeeParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete enterprise customFee result
type DeleteEnterpriseCustomFeeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *DeleteEnterpriseCustomFeeResult) Reset() {
	*x = DeleteEnterpriseCustomFeeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEnterpriseCustomFeeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEnterpriseCustomFeeResult) ProtoMessage() {}

func (x *DeleteEnterpriseCustomFeeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEnterpriseCustomFeeResult.ProtoReflect.Descriptor instead.
func (*DeleteEnterpriseCustomFeeResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteEnterpriseCustomFeeResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_moego_admin_pay_ops_v1_enterprise_custom_fee_proto protoreflect.FileDescriptor

var file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x70, 0x61,
	0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x66, 0x65, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xf4, 0x03, 0x0a, 0x13, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x76, 0x6f,
	0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69,
	0x7a, 0x65, 0x64, 0x4d, 0x69, 0x6e, 0x56, 0x6f, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0d, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x46, 0x65, 0x65, 0x52, 0x61, 0x74,
	0x65, 0x12, 0x28, 0x0a, 0x10, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x5f,
	0x63, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x46, 0x65, 0x65, 0x43, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x72,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x72, 0x65, 0x61, 0x64, 0x65, 0x72, 0x46, 0x65, 0x65, 0x52,
	0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x72, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x66, 0x65,
	0x65, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x72,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x46, 0x65, 0x65, 0x43, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x3b, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x9f, 0x01, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74,
	0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x46, 0x65, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0d, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x22, 0xc7, 0x01, 0x0a, 0x1e, 0x4c, 0x69,
	0x73, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x46, 0x65, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x61, 0x0a, 0x16,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x5f, 0x66, 0x65, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f,
	0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x52, 0x14, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x73, 0x12,
	0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x8f, 0x03, 0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x0f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x66, 0x65, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x42, 0x17,
	0xfa, 0x42, 0x14, 0x12, 0x12, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x40, 0x21, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0d, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x46,
	0x65, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x10, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x66, 0x65, 0x65, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52, 0x0e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x46, 0x65, 0x65, 0x43, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x3f, 0x0a, 0x0f, 0x72,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x01, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x12, 0x12, 0x11, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x10, 0x40, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0d, 0x72,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x46, 0x65, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x10,
	0x72, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x18, 0x64, 0x28,
	0x00, 0x52, 0x0e, 0x72, 0x65, 0x61, 0x64, 0x65, 0x72, 0x46, 0x65, 0x65, 0x43, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x3b, 0x0a, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f,
	0x6d, 0x69, 0x6e, 0x5f, 0x76, 0x6f, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x22, 0x03, 0x20, 0xe8, 0x07, 0x48, 0x00, 0x52, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x69, 0x7a, 0x65, 0x64, 0x4d, 0x69, 0x6e, 0x56, 0x6f, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x15,
	0x0a, 0x13, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x6d, 0x69,
	0x6e, 0x5f, 0x76, 0x6f, 0x6c, 0x22, 0xa8, 0x03, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x46, 0x65, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x3b, 0x0a, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x6d,
	0x69, 0x6e, 0x5f, 0x76, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x22, 0x03, 0x20, 0xe8, 0x07, 0x48, 0x00, 0x52, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x69, 0x7a, 0x65, 0x64, 0x4d, 0x69, 0x6e, 0x56, 0x6f, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x3f, 0x0a,
	0x0f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x12, 0x12, 0x11, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x10, 0x40, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52,
	0x0d, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x46, 0x65, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x33,
	0x0a, 0x10, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x2a, 0x04, 0x18,
	0x64, 0x28, 0x00, 0x52, 0x0e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x46, 0x65, 0x65, 0x43, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x3f, 0x0a, 0x0f, 0x72, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x66, 0x65,
	0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x42, 0x17, 0xfa, 0x42,
	0x14, 0x12, 0x12, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x40, 0x21, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0d, 0x72, 0x65, 0x61, 0x64, 0x65, 0x72, 0x46, 0x65, 0x65,
	0x52, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x10, 0x72, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x66,
	0x65, 0x65, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x2a, 0x04, 0x18, 0x64, 0x28, 0x00, 0x52, 0x0e, 0x72, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x46, 0x65, 0x65, 0x43, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x76, 0x6f, 0x6c,
	0x22, 0x3a, 0x0a, 0x1f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x3b, 0x0a, 0x1f,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x32, 0xc1, 0x04, 0x0a, 0x1a, 0x45, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73,
	0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x46, 0x65, 0x65, 0x73, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f,
	0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x81, 0x01, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x46, 0x65, 0x65, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f,
	0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x19, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70,
	0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x12, 0x8d, 0x01,
	0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x12, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x46, 0x65, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x7b, 0x0a,
	0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x57, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2f, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x70,
	0x61, 0x79, 0x6f, 0x70, 0x73, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDescOnce sync.Once
	file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDescData = file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDesc
)

func file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDescGZIP() []byte {
	file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDescOnce.Do(func() {
		file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDescData)
	})
	return file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDescData
}

var file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_goTypes = []interface{}{
	(*EnterpriseCustomFee)(nil),             // 0: moego.admin.pay_ops.v1.EnterpriseCustomFee
	(*ListEnterpriseCustomFeesParams)(nil),  // 1: moego.admin.pay_ops.v1.ListEnterpriseCustomFeesParams
	(*ListEnterpriseCustomFeesResult)(nil),  // 2: moego.admin.pay_ops.v1.ListEnterpriseCustomFeesResult
	(*CreateEnterpriseCustomFeeParams)(nil), // 3: moego.admin.pay_ops.v1.CreateEnterpriseCustomFeeParams
	(*UpdateEnterpriseCustomFeeParams)(nil), // 4: moego.admin.pay_ops.v1.UpdateEnterpriseCustomFeeParams
	(*DeleteEnterpriseCustomFeeParams)(nil), // 5: moego.admin.pay_ops.v1.DeleteEnterpriseCustomFeeParams
	(*DeleteEnterpriseCustomFeeResult)(nil), // 6: moego.admin.pay_ops.v1.DeleteEnterpriseCustomFeeResult
	(*timestamppb.Timestamp)(nil),           // 7: google.protobuf.Timestamp
	(*v2.PaginationRequest)(nil),            // 8: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),           // 9: moego.utils.v2.PaginationResponse
}
var file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_depIdxs = []int32{
	7, // 0: moego.admin.pay_ops.v1.EnterpriseCustomFee.create_time:type_name -> google.protobuf.Timestamp
	7, // 1: moego.admin.pay_ops.v1.EnterpriseCustomFee.update_time:type_name -> google.protobuf.Timestamp
	8, // 2: moego.admin.pay_ops.v1.ListEnterpriseCustomFeesParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	0, // 3: moego.admin.pay_ops.v1.ListEnterpriseCustomFeesResult.enterprise_custom_fees:type_name -> moego.admin.pay_ops.v1.EnterpriseCustomFee
	9, // 4: moego.admin.pay_ops.v1.ListEnterpriseCustomFeesResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	1, // 5: moego.admin.pay_ops.v1.EnterpriseCustomFeeService.ListEnterpriseCustomFees:input_type -> moego.admin.pay_ops.v1.ListEnterpriseCustomFeesParams
	3, // 6: moego.admin.pay_ops.v1.EnterpriseCustomFeeService.CreateEnterpriseCustomFee:input_type -> moego.admin.pay_ops.v1.CreateEnterpriseCustomFeeParams
	4, // 7: moego.admin.pay_ops.v1.EnterpriseCustomFeeService.UpdateEnterpriseCustomFee:input_type -> moego.admin.pay_ops.v1.UpdateEnterpriseCustomFeeParams
	5, // 8: moego.admin.pay_ops.v1.EnterpriseCustomFeeService.DeleteEnterpriseCustomFee:input_type -> moego.admin.pay_ops.v1.DeleteEnterpriseCustomFeeParams
	2, // 9: moego.admin.pay_ops.v1.EnterpriseCustomFeeService.ListEnterpriseCustomFees:output_type -> moego.admin.pay_ops.v1.ListEnterpriseCustomFeesResult
	0, // 10: moego.admin.pay_ops.v1.EnterpriseCustomFeeService.CreateEnterpriseCustomFee:output_type -> moego.admin.pay_ops.v1.EnterpriseCustomFee
	0, // 11: moego.admin.pay_ops.v1.EnterpriseCustomFeeService.UpdateEnterpriseCustomFee:output_type -> moego.admin.pay_ops.v1.EnterpriseCustomFee
	6, // 12: moego.admin.pay_ops.v1.EnterpriseCustomFeeService.DeleteEnterpriseCustomFee:output_type -> moego.admin.pay_ops.v1.DeleteEnterpriseCustomFeeResult
	9, // [9:13] is the sub-list for method output_type
	5, // [5:9] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_init() }
func file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_init() {
	if File_moego_admin_pay_ops_v1_enterprise_custom_fee_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnterpriseCustomFee); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEnterpriseCustomFeesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEnterpriseCustomFeesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEnterpriseCustomFeeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEnterpriseCustomFeeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEnterpriseCustomFeeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEnterpriseCustomFeeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes[4].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_goTypes,
		DependencyIndexes: file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_depIdxs,
		MessageInfos:      file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_msgTypes,
	}.Build()
	File_moego_admin_pay_ops_v1_enterprise_custom_fee_proto = out.File
	file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_rawDesc = nil
	file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_goTypes = nil
	file_moego_admin_pay_ops_v1_enterprise_custom_fee_proto_depIdxs = nil
}
