// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/pay_ops/v1/transaction_history_admin.proto

package payopsapipb

import (
	context "context"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/pay_ops/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// TransactionHistoryServiceClient is the client API for TransactionHistoryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TransactionHistoryServiceClient interface {
	// get transaction history list
	GetTransactionHistoryList(ctx context.Context, in *GetTransactionHistoryListParams, opts ...grpc.CallOption) (*GetTransactionHistoryListResult, error)
	// get transaction history view
	GetTransactionHistoryView(ctx context.Context, in *GetTransactionHistoryViewParams, opts ...grpc.CallOption) (*v1.TransactionHistoryModel, error)
	// get static value
	GetStaticValue(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetStaticValueModel, error)
	// transaction history refund
	RefundTransactionHistory(ctx context.Context, in *RefundTransactionHistoryParams, opts ...grpc.CallOption) (*RefundTransactionHistoryResult, error)
	// get business transaction history list
	GetBusinessTransactionHistoryList(ctx context.Context, in *BusinessTransactionHistoryListParams, opts ...grpc.CallOption) (*BusinessTransactionHistoryListResult, error)
	// fetch stripe payment intent
	GetStripePaymentIntentView(ctx context.Context, in *GetPaymentIntentParams, opts ...grpc.CallOption) (*GetPaymentIntentResult, error)
}

type transactionHistoryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTransactionHistoryServiceClient(cc grpc.ClientConnInterface) TransactionHistoryServiceClient {
	return &transactionHistoryServiceClient{cc}
}

func (c *transactionHistoryServiceClient) GetTransactionHistoryList(ctx context.Context, in *GetTransactionHistoryListParams, opts ...grpc.CallOption) (*GetTransactionHistoryListResult, error) {
	out := new(GetTransactionHistoryListResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.TransactionHistoryService/GetTransactionHistoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionHistoryServiceClient) GetTransactionHistoryView(ctx context.Context, in *GetTransactionHistoryViewParams, opts ...grpc.CallOption) (*v1.TransactionHistoryModel, error) {
	out := new(v1.TransactionHistoryModel)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.TransactionHistoryService/GetTransactionHistoryView", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionHistoryServiceClient) GetStaticValue(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetStaticValueModel, error) {
	out := new(GetStaticValueModel)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.TransactionHistoryService/GetStaticValue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionHistoryServiceClient) RefundTransactionHistory(ctx context.Context, in *RefundTransactionHistoryParams, opts ...grpc.CallOption) (*RefundTransactionHistoryResult, error) {
	out := new(RefundTransactionHistoryResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.TransactionHistoryService/RefundTransactionHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionHistoryServiceClient) GetBusinessTransactionHistoryList(ctx context.Context, in *BusinessTransactionHistoryListParams, opts ...grpc.CallOption) (*BusinessTransactionHistoryListResult, error) {
	out := new(BusinessTransactionHistoryListResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.TransactionHistoryService/GetBusinessTransactionHistoryList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionHistoryServiceClient) GetStripePaymentIntentView(ctx context.Context, in *GetPaymentIntentParams, opts ...grpc.CallOption) (*GetPaymentIntentResult, error) {
	out := new(GetPaymentIntentResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.TransactionHistoryService/GetStripePaymentIntentView", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TransactionHistoryServiceServer is the server API for TransactionHistoryService service.
// All implementations must embed UnimplementedTransactionHistoryServiceServer
// for forward compatibility
type TransactionHistoryServiceServer interface {
	// get transaction history list
	GetTransactionHistoryList(context.Context, *GetTransactionHistoryListParams) (*GetTransactionHistoryListResult, error)
	// get transaction history view
	GetTransactionHistoryView(context.Context, *GetTransactionHistoryViewParams) (*v1.TransactionHistoryModel, error)
	// get static value
	GetStaticValue(context.Context, *emptypb.Empty) (*GetStaticValueModel, error)
	// transaction history refund
	RefundTransactionHistory(context.Context, *RefundTransactionHistoryParams) (*RefundTransactionHistoryResult, error)
	// get business transaction history list
	GetBusinessTransactionHistoryList(context.Context, *BusinessTransactionHistoryListParams) (*BusinessTransactionHistoryListResult, error)
	// fetch stripe payment intent
	GetStripePaymentIntentView(context.Context, *GetPaymentIntentParams) (*GetPaymentIntentResult, error)
	mustEmbedUnimplementedTransactionHistoryServiceServer()
}

// UnimplementedTransactionHistoryServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTransactionHistoryServiceServer struct {
}

func (UnimplementedTransactionHistoryServiceServer) GetTransactionHistoryList(context.Context, *GetTransactionHistoryListParams) (*GetTransactionHistoryListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionHistoryList not implemented")
}
func (UnimplementedTransactionHistoryServiceServer) GetTransactionHistoryView(context.Context, *GetTransactionHistoryViewParams) (*v1.TransactionHistoryModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionHistoryView not implemented")
}
func (UnimplementedTransactionHistoryServiceServer) GetStaticValue(context.Context, *emptypb.Empty) (*GetStaticValueModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaticValue not implemented")
}
func (UnimplementedTransactionHistoryServiceServer) RefundTransactionHistory(context.Context, *RefundTransactionHistoryParams) (*RefundTransactionHistoryResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefundTransactionHistory not implemented")
}
func (UnimplementedTransactionHistoryServiceServer) GetBusinessTransactionHistoryList(context.Context, *BusinessTransactionHistoryListParams) (*BusinessTransactionHistoryListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBusinessTransactionHistoryList not implemented")
}
func (UnimplementedTransactionHistoryServiceServer) GetStripePaymentIntentView(context.Context, *GetPaymentIntentParams) (*GetPaymentIntentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStripePaymentIntentView not implemented")
}
func (UnimplementedTransactionHistoryServiceServer) mustEmbedUnimplementedTransactionHistoryServiceServer() {
}

// UnsafeTransactionHistoryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TransactionHistoryServiceServer will
// result in compilation errors.
type UnsafeTransactionHistoryServiceServer interface {
	mustEmbedUnimplementedTransactionHistoryServiceServer()
}

func RegisterTransactionHistoryServiceServer(s grpc.ServiceRegistrar, srv TransactionHistoryServiceServer) {
	s.RegisterService(&TransactionHistoryService_ServiceDesc, srv)
}

func _TransactionHistoryService_GetTransactionHistoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransactionHistoryListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionHistoryServiceServer).GetTransactionHistoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.TransactionHistoryService/GetTransactionHistoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionHistoryServiceServer).GetTransactionHistoryList(ctx, req.(*GetTransactionHistoryListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionHistoryService_GetTransactionHistoryView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransactionHistoryViewParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionHistoryServiceServer).GetTransactionHistoryView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.TransactionHistoryService/GetTransactionHistoryView",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionHistoryServiceServer).GetTransactionHistoryView(ctx, req.(*GetTransactionHistoryViewParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionHistoryService_GetStaticValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionHistoryServiceServer).GetStaticValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.TransactionHistoryService/GetStaticValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionHistoryServiceServer).GetStaticValue(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionHistoryService_RefundTransactionHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundTransactionHistoryParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionHistoryServiceServer).RefundTransactionHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.TransactionHistoryService/RefundTransactionHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionHistoryServiceServer).RefundTransactionHistory(ctx, req.(*RefundTransactionHistoryParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionHistoryService_GetBusinessTransactionHistoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BusinessTransactionHistoryListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionHistoryServiceServer).GetBusinessTransactionHistoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.TransactionHistoryService/GetBusinessTransactionHistoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionHistoryServiceServer).GetBusinessTransactionHistoryList(ctx, req.(*BusinessTransactionHistoryListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionHistoryService_GetStripePaymentIntentView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaymentIntentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionHistoryServiceServer).GetStripePaymentIntentView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.TransactionHistoryService/GetStripePaymentIntentView",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionHistoryServiceServer).GetStripePaymentIntentView(ctx, req.(*GetPaymentIntentParams))
	}
	return interceptor(ctx, in, info, handler)
}

// TransactionHistoryService_ServiceDesc is the grpc.ServiceDesc for TransactionHistoryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TransactionHistoryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.pay_ops.v1.TransactionHistoryService",
	HandlerType: (*TransactionHistoryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetTransactionHistoryList",
			Handler:    _TransactionHistoryService_GetTransactionHistoryList_Handler,
		},
		{
			MethodName: "GetTransactionHistoryView",
			Handler:    _TransactionHistoryService_GetTransactionHistoryView_Handler,
		},
		{
			MethodName: "GetStaticValue",
			Handler:    _TransactionHistoryService_GetStaticValue_Handler,
		},
		{
			MethodName: "RefundTransactionHistory",
			Handler:    _TransactionHistoryService_RefundTransactionHistory_Handler,
		},
		{
			MethodName: "GetBusinessTransactionHistoryList",
			Handler:    _TransactionHistoryService_GetBusinessTransactionHistoryList_Handler,
		},
		{
			MethodName: "GetStripePaymentIntentView",
			Handler:    _TransactionHistoryService_GetStripePaymentIntentView_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/pay_ops/v1/transaction_history_admin.proto",
}
