// @since 2-23-10-07
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/pay_ops/v1/payout_whitlist_control.proto

package payopsapipb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// company custom feature relation
type CompanyFeatureRelation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// code
	Code string `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	// allow type
	AllowType int32 `protobuf:"varint,4,opt,name=allow_type,json=allowType,proto3" json:"allow_type,omitempty"`
	// enable
	Enable int32 `protobuf:"varint,5,opt,name=enable,proto3" json:"enable,omitempty"`
	// quota
	Quota int32 `protobuf:"varint,6,opt,name=quota,proto3" json:"quota,omitempty"`
	// expiration time
	ExpirationTime int64 `protobuf:"varint,7,opt,name=expiration_time,json=expirationTime,proto3" json:"expiration_time,omitempty"`
	// create time
	CreateTime int64 `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime int64 `protobuf:"varint,9,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// note
	Note *string `protobuf:"bytes,10,opt,name=note,proto3,oneof" json:"note,omitempty"`
	// is deleted
	IsDeleted bool `protobuf:"varint,11,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	// company name
	CompanyName string `protobuf:"bytes,12,opt,name=company_name,json=companyName,proto3" json:"company_name,omitempty"`
}

func (x *CompanyFeatureRelation) Reset() {
	*x = CompanyFeatureRelation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyFeatureRelation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyFeatureRelation) ProtoMessage() {}

func (x *CompanyFeatureRelation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyFeatureRelation.ProtoReflect.Descriptor instead.
func (*CompanyFeatureRelation) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDescGZIP(), []int{0}
}

func (x *CompanyFeatureRelation) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CompanyFeatureRelation) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CompanyFeatureRelation) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CompanyFeatureRelation) GetAllowType() int32 {
	if x != nil {
		return x.AllowType
	}
	return 0
}

func (x *CompanyFeatureRelation) GetEnable() int32 {
	if x != nil {
		return x.Enable
	}
	return 0
}

func (x *CompanyFeatureRelation) GetQuota() int32 {
	if x != nil {
		return x.Quota
	}
	return 0
}

func (x *CompanyFeatureRelation) GetExpirationTime() int64 {
	if x != nil {
		return x.ExpirationTime
	}
	return 0
}

func (x *CompanyFeatureRelation) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *CompanyFeatureRelation) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *CompanyFeatureRelation) GetNote() string {
	if x != nil && x.Note != nil {
		return *x.Note
	}
	return ""
}

func (x *CompanyFeatureRelation) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *CompanyFeatureRelation) GetCompanyName() string {
	if x != nil {
		return x.CompanyName
	}
	return ""
}

// company custom feature relation list response
type CompanyFeatureRelationListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company custom feature relation
	CompanyFeatureRelation []*CompanyFeatureRelation `protobuf:"bytes,1,rep,name=company_feature_relation,json=companyFeatureRelation,proto3" json:"company_feature_relation,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *CompanyFeatureRelationListResult) Reset() {
	*x = CompanyFeatureRelationListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyFeatureRelationListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyFeatureRelationListResult) ProtoMessage() {}

func (x *CompanyFeatureRelationListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyFeatureRelationListResult.ProtoReflect.Descriptor instead.
func (*CompanyFeatureRelationListResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDescGZIP(), []int{1}
}

func (x *CompanyFeatureRelationListResult) GetCompanyFeatureRelation() []*CompanyFeatureRelation {
	if x != nil {
		return x.CompanyFeatureRelation
	}
	return nil
}

func (x *CompanyFeatureRelationListResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// whitelist control get request
type CompanyFeatureRelationGetParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *CompanyFeatureRelationGetParams) Reset() {
	*x = CompanyFeatureRelationGetParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyFeatureRelationGetParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyFeatureRelationGetParams) ProtoMessage() {}

func (x *CompanyFeatureRelationGetParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyFeatureRelationGetParams.ProtoReflect.Descriptor instead.
func (*CompanyFeatureRelationGetParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDescGZIP(), []int{2}
}

func (x *CompanyFeatureRelationGetParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// create company custom feature relation params
type CreateCompanyFeatureRelationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// note
	Note string `protobuf:"bytes,3,opt,name=note,proto3" json:"note,omitempty"`
}

func (x *CreateCompanyFeatureRelationParams) Reset() {
	*x = CreateCompanyFeatureRelationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCompanyFeatureRelationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCompanyFeatureRelationParams) ProtoMessage() {}

func (x *CreateCompanyFeatureRelationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCompanyFeatureRelationParams.ProtoReflect.Descriptor instead.
func (*CreateCompanyFeatureRelationParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDescGZIP(), []int{3}
}

func (x *CreateCompanyFeatureRelationParams) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateCompanyFeatureRelationParams) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

// create company custom feature relation result
type CreateCompanyFeatureRelationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateCompanyFeatureRelationResult) Reset() {
	*x = CreateCompanyFeatureRelationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCompanyFeatureRelationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCompanyFeatureRelationResult) ProtoMessage() {}

func (x *CreateCompanyFeatureRelationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCompanyFeatureRelationResult.ProtoReflect.Descriptor instead.
func (*CreateCompanyFeatureRelationResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDescGZIP(), []int{4}
}

func (x *CreateCompanyFeatureRelationResult) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// deleted company custom feature relation params
type DeletedCompanyFeatureRelationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletedCompanyFeatureRelationParams) Reset() {
	*x = DeletedCompanyFeatureRelationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletedCompanyFeatureRelationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletedCompanyFeatureRelationParams) ProtoMessage() {}

func (x *DeletedCompanyFeatureRelationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletedCompanyFeatureRelationParams.ProtoReflect.Descriptor instead.
func (*DeletedCompanyFeatureRelationParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDescGZIP(), []int{5}
}

func (x *DeletedCompanyFeatureRelationParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// deleted company custom feature relation result
type DeletedCompanyFeatureRelationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletedCompanyFeatureRelationResult) Reset() {
	*x = DeletedCompanyFeatureRelationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletedCompanyFeatureRelationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletedCompanyFeatureRelationResult) ProtoMessage() {}

func (x *DeletedCompanyFeatureRelationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletedCompanyFeatureRelationResult.ProtoReflect.Descriptor instead.
func (*DeletedCompanyFeatureRelationResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDescGZIP(), []int{6}
}

func (x *DeletedCompanyFeatureRelationResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_moego_admin_pay_ops_v1_payout_whitlist_control_proto protoreflect.FileDescriptor

var file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x70, 0x61,
	0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f,
	0x77, 0x68, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x28,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xf7, 0x02, 0x0a, 0x16, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x12, 0x27, 0x0a, 0x0f,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x22, 0xd0, 0x01, 0x0a, 0x20,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x68, 0x0a, 0x18, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x16, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x64,
	0x0a, 0x1f, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x6c, 0x0a, 0x22, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0x80, 0x10, 0x52, 0x04, 0x6e, 0x6f,
	0x74, 0x65, 0x22, 0x3d, 0x0a, 0x22, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x3e, 0x0a, 0x23, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x3e, 0x0a, 0x23, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x32, 0xef, 0x03, 0x0a, 0x1d, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x57, 0x68, 0x69, 0x74,
	0x65, 0x6c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x94, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79,
	0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x98, 0x01, 0x0a, 0x1c, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x9b, 0x01, 0x0a, 0x1d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x00, 0x42, 0x7b, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f,
	0x70, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x57, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70,
	0x73, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6f, 0x70, 0x73, 0x61, 0x70, 0x69, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDescOnce sync.Once
	file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDescData = file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDesc
)

func file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDescGZIP() []byte {
	file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDescOnce.Do(func() {
		file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDescData)
	})
	return file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDescData
}

var file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_goTypes = []interface{}{
	(*CompanyFeatureRelation)(nil),              // 0: moego.admin.pay_ops.v1.CompanyFeatureRelation
	(*CompanyFeatureRelationListResult)(nil),    // 1: moego.admin.pay_ops.v1.CompanyFeatureRelationListResult
	(*CompanyFeatureRelationGetParams)(nil),     // 2: moego.admin.pay_ops.v1.CompanyFeatureRelationGetParams
	(*CreateCompanyFeatureRelationParams)(nil),  // 3: moego.admin.pay_ops.v1.CreateCompanyFeatureRelationParams
	(*CreateCompanyFeatureRelationResult)(nil),  // 4: moego.admin.pay_ops.v1.CreateCompanyFeatureRelationResult
	(*DeletedCompanyFeatureRelationParams)(nil), // 5: moego.admin.pay_ops.v1.DeletedCompanyFeatureRelationParams
	(*DeletedCompanyFeatureRelationResult)(nil), // 6: moego.admin.pay_ops.v1.DeletedCompanyFeatureRelationResult
	(*v2.PaginationResponse)(nil),               // 7: moego.utils.v2.PaginationResponse
	(*v2.PaginationRequest)(nil),                // 8: moego.utils.v2.PaginationRequest
}
var file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_depIdxs = []int32{
	0, // 0: moego.admin.pay_ops.v1.CompanyFeatureRelationListResult.company_feature_relation:type_name -> moego.admin.pay_ops.v1.CompanyFeatureRelation
	7, // 1: moego.admin.pay_ops.v1.CompanyFeatureRelationListResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	8, // 2: moego.admin.pay_ops.v1.CompanyFeatureRelationGetParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	2, // 3: moego.admin.pay_ops.v1.PayoutWhitelistControlService.GetCompanyFeatureRelationList:input_type -> moego.admin.pay_ops.v1.CompanyFeatureRelationGetParams
	3, // 4: moego.admin.pay_ops.v1.PayoutWhitelistControlService.CreateCompanyFeatureRelation:input_type -> moego.admin.pay_ops.v1.CreateCompanyFeatureRelationParams
	5, // 5: moego.admin.pay_ops.v1.PayoutWhitelistControlService.DeletedCompanyFeatureRelation:input_type -> moego.admin.pay_ops.v1.DeletedCompanyFeatureRelationParams
	1, // 6: moego.admin.pay_ops.v1.PayoutWhitelistControlService.GetCompanyFeatureRelationList:output_type -> moego.admin.pay_ops.v1.CompanyFeatureRelationListResult
	4, // 7: moego.admin.pay_ops.v1.PayoutWhitelistControlService.CreateCompanyFeatureRelation:output_type -> moego.admin.pay_ops.v1.CreateCompanyFeatureRelationResult
	6, // 8: moego.admin.pay_ops.v1.PayoutWhitelistControlService.DeletedCompanyFeatureRelation:output_type -> moego.admin.pay_ops.v1.DeletedCompanyFeatureRelationResult
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_init() }
func file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_init() {
	if File_moego_admin_pay_ops_v1_payout_whitlist_control_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyFeatureRelation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyFeatureRelationListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyFeatureRelationGetParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCompanyFeatureRelationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCompanyFeatureRelationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletedCompanyFeatureRelationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletedCompanyFeatureRelationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_goTypes,
		DependencyIndexes: file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_depIdxs,
		MessageInfos:      file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_msgTypes,
	}.Build()
	File_moego_admin_pay_ops_v1_payout_whitlist_control_proto = out.File
	file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_rawDesc = nil
	file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_goTypes = nil
	file_moego_admin_pay_ops_v1_payout_whitlist_control_proto_depIdxs = nil
}
