// @since 2023-04-07 09:48:36
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/metadata/v1/metadata_admin.proto

package metadataapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/metadata/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// describe groups response
type DescribeGroupsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the group list
	Groups []string `protobuf:"bytes,1,rep,name=groups,proto3" json:"groups,omitempty"`
}

func (x *DescribeGroupsResult) Reset() {
	*x = DescribeGroupsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeGroupsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeGroupsResult) ProtoMessage() {}

func (x *DescribeGroupsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeGroupsResult.ProtoReflect.Descriptor instead.
func (*DescribeGroupsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_metadata_v1_metadata_admin_proto_rawDescGZIP(), []int{0}
}

func (x *DescribeGroupsResult) GetGroups() []string {
	if x != nil {
		return x.Groups
	}
	return nil
}

// describe keys request
type DescribeKeysParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter by group, empty will not filter
	Group *string `protobuf:"bytes,1,opt,name=group,proto3,oneof" json:"group,omitempty"`
	// filter by owner type, 0 will not filter
	OwnerType *v1.OwnerType `protobuf:"varint,2,opt,name=owner_type,json=ownerType,proto3,enum=moego.models.metadata.v1.OwnerType,oneof" json:"owner_type,omitempty"`
	// filter by name like
	NameLike *string `protobuf:"bytes,3,opt,name=name_like,json=nameLike,proto3,oneof" json:"name_like,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *DescribeKeysParams) Reset() {
	*x = DescribeKeysParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeKeysParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeKeysParams) ProtoMessage() {}

func (x *DescribeKeysParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeKeysParams.ProtoReflect.Descriptor instead.
func (*DescribeKeysParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_metadata_v1_metadata_admin_proto_rawDescGZIP(), []int{1}
}

func (x *DescribeKeysParams) GetGroup() string {
	if x != nil && x.Group != nil {
		return *x.Group
	}
	return ""
}

func (x *DescribeKeysParams) GetOwnerType() v1.OwnerType {
	if x != nil && x.OwnerType != nil {
		return *x.OwnerType
	}
	return v1.OwnerType(0)
}

func (x *DescribeKeysParams) GetNameLike() string {
	if x != nil && x.NameLike != nil {
		return *x.NameLike
	}
	return ""
}

func (x *DescribeKeysParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// describe keys response
type DescribeKeysResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the keys
	Keys []*v1.KeyModel `protobuf:"bytes,1,rep,name=keys,proto3" json:"keys,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *DescribeKeysResult) Reset() {
	*x = DescribeKeysResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeKeysResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeKeysResult) ProtoMessage() {}

func (x *DescribeKeysResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeKeysResult.ProtoReflect.Descriptor instead.
func (*DescribeKeysResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_metadata_v1_metadata_admin_proto_rawDescGZIP(), []int{2}
}

func (x *DescribeKeysResult) GetKeys() []*v1.KeyModel {
	if x != nil {
		return x.Keys
	}
	return nil
}

func (x *DescribeKeysResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// get key request
type GetKeyParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetKeyParams) Reset() {
	*x = GetKeyParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetKeyParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKeyParams) ProtoMessage() {}

func (x *GetKeyParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKeyParams.ProtoReflect.Descriptor instead.
func (*GetKeyParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_metadata_v1_metadata_admin_proto_rawDescGZIP(), []int{3}
}

func (x *GetKeyParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get key response
type GetKeyResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the key
	Key *v1.KeyModel `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *GetKeyResult) Reset() {
	*x = GetKeyResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetKeyResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKeyResult) ProtoMessage() {}

func (x *GetKeyResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKeyResult.ProtoReflect.Descriptor instead.
func (*GetKeyResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_metadata_v1_metadata_admin_proto_rawDescGZIP(), []int{4}
}

func (x *GetKeyResult) GetKey() *v1.KeyModel {
	if x != nil {
		return x.Key
	}
	return nil
}

// create key request
type CreateKeyParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the key def
	KeyDef *v1.KeyFullDef `protobuf:"bytes,1,opt,name=key_def,json=keyDef,proto3" json:"key_def,omitempty"`
}

func (x *CreateKeyParams) Reset() {
	*x = CreateKeyParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateKeyParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateKeyParams) ProtoMessage() {}

func (x *CreateKeyParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateKeyParams.ProtoReflect.Descriptor instead.
func (*CreateKeyParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_metadata_v1_metadata_admin_proto_rawDescGZIP(), []int{5}
}

func (x *CreateKeyParams) GetKeyDef() *v1.KeyFullDef {
	if x != nil {
		return x.KeyDef
	}
	return nil
}

// create key response
type CreateKeyResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the key
	Key *v1.KeyModel `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *CreateKeyResult) Reset() {
	*x = CreateKeyResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateKeyResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateKeyResult) ProtoMessage() {}

func (x *CreateKeyResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateKeyResult.ProtoReflect.Descriptor instead.
func (*CreateKeyResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_metadata_v1_metadata_admin_proto_rawDescGZIP(), []int{6}
}

func (x *CreateKeyResult) GetKey() *v1.KeyModel {
	if x != nil {
		return x.Key
	}
	return nil
}

// update key request
type UpdateKeyParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the key def
	KeyDef *v1.KeyPartialDef `protobuf:"bytes,2,opt,name=key_def,json=keyDef,proto3" json:"key_def,omitempty"`
}

func (x *UpdateKeyParams) Reset() {
	*x = UpdateKeyParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateKeyParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKeyParams) ProtoMessage() {}

func (x *UpdateKeyParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKeyParams.ProtoReflect.Descriptor instead.
func (*UpdateKeyParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_metadata_v1_metadata_admin_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateKeyParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateKeyParams) GetKeyDef() *v1.KeyPartialDef {
	if x != nil {
		return x.KeyDef
	}
	return nil
}

// delete key params
type DeleteKeyParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteKeyParams) Reset() {
	*x = DeleteKeyParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteKeyParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteKeyParams) ProtoMessage() {}

func (x *DeleteKeyParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteKeyParams.ProtoReflect.Descriptor instead.
func (*DeleteKeyParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_metadata_v1_metadata_admin_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteKeyParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// describe values request
type DescribeValuesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key id
	KeyId int64 `protobuf:"varint,1,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	// owner id
	OwnerIds []int64 `protobuf:"varint,2,rep,packed,name=owner_ids,json=ownerIds,proto3" json:"owner_ids,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,15,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *DescribeValuesParams) Reset() {
	*x = DescribeValuesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeValuesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeValuesParams) ProtoMessage() {}

func (x *DescribeValuesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeValuesParams.ProtoReflect.Descriptor instead.
func (*DescribeValuesParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_metadata_v1_metadata_admin_proto_rawDescGZIP(), []int{9}
}

func (x *DescribeValuesParams) GetKeyId() int64 {
	if x != nil {
		return x.KeyId
	}
	return 0
}

func (x *DescribeValuesParams) GetOwnerIds() []int64 {
	if x != nil {
		return x.OwnerIds
	}
	return nil
}

func (x *DescribeValuesParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// describe values responses
type DescribeValuesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// owners
	Owners map[int64]*DescribeValuesResult_Owner `protobuf:"bytes,1,rep,name=owners,proto3" json:"owners,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// values
	Values []*v1.ValueModel `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *DescribeValuesResult) Reset() {
	*x = DescribeValuesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeValuesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeValuesResult) ProtoMessage() {}

func (x *DescribeValuesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeValuesResult.ProtoReflect.Descriptor instead.
func (*DescribeValuesResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_metadata_v1_metadata_admin_proto_rawDescGZIP(), []int{10}
}

func (x *DescribeValuesResult) GetOwners() map[int64]*DescribeValuesResult_Owner {
	if x != nil {
		return x.Owners
	}
	return nil
}

func (x *DescribeValuesResult) GetValues() []*v1.ValueModel {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *DescribeValuesResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// get value request
type GetValueParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key
	KeyId int64 `protobuf:"varint,1,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	// value
	OwnerId int64 `protobuf:"varint,2,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
}

func (x *GetValueParams) Reset() {
	*x = GetValueParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetValueParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetValueParams) ProtoMessage() {}

func (x *GetValueParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetValueParams.ProtoReflect.Descriptor instead.
func (*GetValueParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_metadata_v1_metadata_admin_proto_rawDescGZIP(), []int{11}
}

func (x *GetValueParams) GetKeyId() int64 {
	if x != nil {
		return x.KeyId
	}
	return 0
}

func (x *GetValueParams) GetOwnerId() int64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

// get value response
type GetValueResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the key
	Key *v1.KeyModel `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// the value
	Value *v1.ValueModel `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *GetValueResult) Reset() {
	*x = GetValueResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetValueResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetValueResult) ProtoMessage() {}

func (x *GetValueResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetValueResult.ProtoReflect.Descriptor instead.
func (*GetValueResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_metadata_v1_metadata_admin_proto_rawDescGZIP(), []int{12}
}

func (x *GetValueResult) GetKey() *v1.KeyModel {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *GetValueResult) GetValue() *v1.ValueModel {
	if x != nil {
		return x.Value
	}
	return nil
}

// update value request
type BatchUpdateValueParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key
	KeyId int64 `protobuf:"varint,1,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	// owner
	OwnerIds []int64 `protobuf:"varint,2,rep,packed,name=owner_ids,json=ownerIds,proto3" json:"owner_ids,omitempty"`
	// value
	Value *string `protobuf:"bytes,3,opt,name=value,proto3,oneof" json:"value,omitempty"`
}

func (x *BatchUpdateValueParams) Reset() {
	*x = BatchUpdateValueParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateValueParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateValueParams) ProtoMessage() {}

func (x *BatchUpdateValueParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateValueParams.ProtoReflect.Descriptor instead.
func (*BatchUpdateValueParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_metadata_v1_metadata_admin_proto_rawDescGZIP(), []int{13}
}

func (x *BatchUpdateValueParams) GetKeyId() int64 {
	if x != nil {
		return x.KeyId
	}
	return 0
}

func (x *BatchUpdateValueParams) GetOwnerIds() []int64 {
	if x != nil {
		return x.OwnerIds
	}
	return nil
}

func (x *BatchUpdateValueParams) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

// the owner
type DescribeValuesResult_Owner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// email
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
}

func (x *DescribeValuesResult_Owner) Reset() {
	*x = DescribeValuesResult_Owner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeValuesResult_Owner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeValuesResult_Owner) ProtoMessage() {}

func (x *DescribeValuesResult_Owner) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeValuesResult_Owner.ProtoReflect.Descriptor instead.
func (*DescribeValuesResult_Owner) Descriptor() ([]byte, []int) {
	return file_moego_admin_metadata_v1_metadata_admin_proto_rawDescGZIP(), []int{10, 1}
}

func (x *DescribeValuesResult_Owner) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DescribeValuesResult_Owner) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

var File_moego_admin_metadata_v1_metadata_admin_proto protoreflect.FileDescriptor

var file_moego_admin_metadata_v1_metadata_admin_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76,
	0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2e, 0x0a, 0x14, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x22, 0xb8, 0x02, 0x0a, 0x12, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x4b, 0x65, 0x79, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x24, 0x0a, 0x05, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x32, 0x48, 0x00, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x88, 0x01,
	0x01, 0x12, 0x53, 0x0a, 0x0a, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6c,
	0x69, 0x6b, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x18, 0x32, 0x48, 0x02, 0x52, 0x08, 0x6e, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x6b, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x03, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x69, 0x6b, 0x65,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x90, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4b, 0x65, 0x79, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x36, 0x0a, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e,
	0x4b, 0x65, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x12, 0x42,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x27, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x44, 0x0a, 0x0c, 0x47,
	0x65, 0x74, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x34, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x65, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x22, 0x5a, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x47, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31,
	0x2e, 0x4b, 0x65, 0x79, 0x46, 0x75, 0x6c, 0x6c, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6b, 0x65, 0x79, 0x44, 0x65, 0x66, 0x22, 0x47, 0x0a,
	0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x34, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x65, 0x79, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0x76, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4b, 0x65, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x4a, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4b,
	0x65, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6b, 0x65, 0x79, 0x44, 0x65, 0x66, 0x22, 0x2a,
	0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0xbd, 0x01, 0x0a, 0x14, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x1e, 0x0a, 0x06, 0x6b, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x6b, 0x65,
	0x79, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x10, 0xf4,
	0x03, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x28, 0x01, 0x52, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x49, 0x64, 0x73, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x8e, 0x03, 0x0a, 0x14, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x51, 0x0a, 0x06, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x3c, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76,
	0x31, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x6e, 0x0a, 0x0b, 0x4f, 0x77, 0x6e, 0x65,
	0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x49, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x31, 0x0a, 0x05, 0x4f, 0x77, 0x6e, 0x65,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x54, 0x0a, 0x0e, 0x47,
	0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1e, 0x0a,
	0x06, 0x6b, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x6b, 0x65, 0x79, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x82, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x34, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x65, 0x79,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3a, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x99, 0x01, 0x0a, 0x16, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x1e, 0x0a, 0x06, 0x6b, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x6b, 0x65, 0x79, 0x49,
	0x64, 0x12, 0x30, 0x0a, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x08, 0x01, 0x10, 0xf4,
	0x03, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x49, 0x64, 0x73, 0x12, 0x23, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x08, 0x48, 0x00, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x32, 0xe0, 0x06, 0x0a, 0x14, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x41,
	0x64, 0x6d, 0x69, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x57, 0x0a, 0x0e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x68, 0x0a, 0x0c, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x4b, 0x65, 0x79, 0x73, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4b, 0x65, 0x79, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x4b, 0x65, 0x79, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x56,
	0x0a, 0x06, 0x47, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4b, 0x65, 0x79,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5f, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4b, 0x65, 0x79, 0x12, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x28, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4b, 0x65,
	0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4d, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4b, 0x65, 0x79, 0x12, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4d, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x4b, 0x65, 0x79, 0x12, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x6e, 0x0a, 0x0e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5c, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x27, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x5b, 0x0a, 0x10, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x03, 0x88, 0x02, 0x01, 0x42, 0x7f, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_metadata_v1_metadata_admin_proto_rawDescOnce sync.Once
	file_moego_admin_metadata_v1_metadata_admin_proto_rawDescData = file_moego_admin_metadata_v1_metadata_admin_proto_rawDesc
)

func file_moego_admin_metadata_v1_metadata_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_metadata_v1_metadata_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_metadata_v1_metadata_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_metadata_v1_metadata_admin_proto_rawDescData)
	})
	return file_moego_admin_metadata_v1_metadata_admin_proto_rawDescData
}

var file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_moego_admin_metadata_v1_metadata_admin_proto_goTypes = []interface{}{
	(*DescribeGroupsResult)(nil),       // 0: moego.admin.metadata.v1.DescribeGroupsResult
	(*DescribeKeysParams)(nil),         // 1: moego.admin.metadata.v1.DescribeKeysParams
	(*DescribeKeysResult)(nil),         // 2: moego.admin.metadata.v1.DescribeKeysResult
	(*GetKeyParams)(nil),               // 3: moego.admin.metadata.v1.GetKeyParams
	(*GetKeyResult)(nil),               // 4: moego.admin.metadata.v1.GetKeyResult
	(*CreateKeyParams)(nil),            // 5: moego.admin.metadata.v1.CreateKeyParams
	(*CreateKeyResult)(nil),            // 6: moego.admin.metadata.v1.CreateKeyResult
	(*UpdateKeyParams)(nil),            // 7: moego.admin.metadata.v1.UpdateKeyParams
	(*DeleteKeyParams)(nil),            // 8: moego.admin.metadata.v1.DeleteKeyParams
	(*DescribeValuesParams)(nil),       // 9: moego.admin.metadata.v1.DescribeValuesParams
	(*DescribeValuesResult)(nil),       // 10: moego.admin.metadata.v1.DescribeValuesResult
	(*GetValueParams)(nil),             // 11: moego.admin.metadata.v1.GetValueParams
	(*GetValueResult)(nil),             // 12: moego.admin.metadata.v1.GetValueResult
	(*BatchUpdateValueParams)(nil),     // 13: moego.admin.metadata.v1.BatchUpdateValueParams
	nil,                                // 14: moego.admin.metadata.v1.DescribeValuesResult.OwnersEntry
	(*DescribeValuesResult_Owner)(nil), // 15: moego.admin.metadata.v1.DescribeValuesResult.Owner
	(v1.OwnerType)(0),                  // 16: moego.models.metadata.v1.OwnerType
	(*v2.PaginationRequest)(nil),       // 17: moego.utils.v2.PaginationRequest
	(*v1.KeyModel)(nil),                // 18: moego.models.metadata.v1.KeyModel
	(*v2.PaginationResponse)(nil),      // 19: moego.utils.v2.PaginationResponse
	(*v1.KeyFullDef)(nil),              // 20: moego.models.metadata.v1.KeyFullDef
	(*v1.KeyPartialDef)(nil),           // 21: moego.models.metadata.v1.KeyPartialDef
	(*v1.ValueModel)(nil),              // 22: moego.models.metadata.v1.ValueModel
	(*emptypb.Empty)(nil),              // 23: google.protobuf.Empty
}
var file_moego_admin_metadata_v1_metadata_admin_proto_depIdxs = []int32{
	16, // 0: moego.admin.metadata.v1.DescribeKeysParams.owner_type:type_name -> moego.models.metadata.v1.OwnerType
	17, // 1: moego.admin.metadata.v1.DescribeKeysParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	18, // 2: moego.admin.metadata.v1.DescribeKeysResult.keys:type_name -> moego.models.metadata.v1.KeyModel
	19, // 3: moego.admin.metadata.v1.DescribeKeysResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	18, // 4: moego.admin.metadata.v1.GetKeyResult.key:type_name -> moego.models.metadata.v1.KeyModel
	20, // 5: moego.admin.metadata.v1.CreateKeyParams.key_def:type_name -> moego.models.metadata.v1.KeyFullDef
	18, // 6: moego.admin.metadata.v1.CreateKeyResult.key:type_name -> moego.models.metadata.v1.KeyModel
	21, // 7: moego.admin.metadata.v1.UpdateKeyParams.key_def:type_name -> moego.models.metadata.v1.KeyPartialDef
	17, // 8: moego.admin.metadata.v1.DescribeValuesParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	14, // 9: moego.admin.metadata.v1.DescribeValuesResult.owners:type_name -> moego.admin.metadata.v1.DescribeValuesResult.OwnersEntry
	22, // 10: moego.admin.metadata.v1.DescribeValuesResult.values:type_name -> moego.models.metadata.v1.ValueModel
	19, // 11: moego.admin.metadata.v1.DescribeValuesResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	18, // 12: moego.admin.metadata.v1.GetValueResult.key:type_name -> moego.models.metadata.v1.KeyModel
	22, // 13: moego.admin.metadata.v1.GetValueResult.value:type_name -> moego.models.metadata.v1.ValueModel
	15, // 14: moego.admin.metadata.v1.DescribeValuesResult.OwnersEntry.value:type_name -> moego.admin.metadata.v1.DescribeValuesResult.Owner
	23, // 15: moego.admin.metadata.v1.MetadataAdminService.DescribeGroups:input_type -> google.protobuf.Empty
	1,  // 16: moego.admin.metadata.v1.MetadataAdminService.DescribeKeys:input_type -> moego.admin.metadata.v1.DescribeKeysParams
	3,  // 17: moego.admin.metadata.v1.MetadataAdminService.GetKey:input_type -> moego.admin.metadata.v1.GetKeyParams
	5,  // 18: moego.admin.metadata.v1.MetadataAdminService.CreateKey:input_type -> moego.admin.metadata.v1.CreateKeyParams
	7,  // 19: moego.admin.metadata.v1.MetadataAdminService.UpdateKey:input_type -> moego.admin.metadata.v1.UpdateKeyParams
	8,  // 20: moego.admin.metadata.v1.MetadataAdminService.DeleteKey:input_type -> moego.admin.metadata.v1.DeleteKeyParams
	9,  // 21: moego.admin.metadata.v1.MetadataAdminService.DescribeValues:input_type -> moego.admin.metadata.v1.DescribeValuesParams
	11, // 22: moego.admin.metadata.v1.MetadataAdminService.GetValue:input_type -> moego.admin.metadata.v1.GetValueParams
	13, // 23: moego.admin.metadata.v1.MetadataAdminService.BatchUpdateValue:input_type -> moego.admin.metadata.v1.BatchUpdateValueParams
	0,  // 24: moego.admin.metadata.v1.MetadataAdminService.DescribeGroups:output_type -> moego.admin.metadata.v1.DescribeGroupsResult
	2,  // 25: moego.admin.metadata.v1.MetadataAdminService.DescribeKeys:output_type -> moego.admin.metadata.v1.DescribeKeysResult
	4,  // 26: moego.admin.metadata.v1.MetadataAdminService.GetKey:output_type -> moego.admin.metadata.v1.GetKeyResult
	6,  // 27: moego.admin.metadata.v1.MetadataAdminService.CreateKey:output_type -> moego.admin.metadata.v1.CreateKeyResult
	23, // 28: moego.admin.metadata.v1.MetadataAdminService.UpdateKey:output_type -> google.protobuf.Empty
	23, // 29: moego.admin.metadata.v1.MetadataAdminService.DeleteKey:output_type -> google.protobuf.Empty
	10, // 30: moego.admin.metadata.v1.MetadataAdminService.DescribeValues:output_type -> moego.admin.metadata.v1.DescribeValuesResult
	12, // 31: moego.admin.metadata.v1.MetadataAdminService.GetValue:output_type -> moego.admin.metadata.v1.GetValueResult
	23, // 32: moego.admin.metadata.v1.MetadataAdminService.BatchUpdateValue:output_type -> google.protobuf.Empty
	24, // [24:33] is the sub-list for method output_type
	15, // [15:24] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_moego_admin_metadata_v1_metadata_admin_proto_init() }
func file_moego_admin_metadata_v1_metadata_admin_proto_init() {
	if File_moego_admin_metadata_v1_metadata_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeGroupsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeKeysParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeKeysResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetKeyParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetKeyResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateKeyParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateKeyResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateKeyParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteKeyParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeValuesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeValuesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetValueParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetValueResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateValueParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeValuesResult_Owner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes[13].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_metadata_v1_metadata_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_metadata_v1_metadata_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_metadata_v1_metadata_admin_proto_depIdxs,
		MessageInfos:      file_moego_admin_metadata_v1_metadata_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_metadata_v1_metadata_admin_proto = out.File
	file_moego_admin_metadata_v1_metadata_admin_proto_rawDesc = nil
	file_moego_admin_metadata_v1_metadata_admin_proto_goTypes = nil
	file_moego_admin_metadata_v1_metadata_admin_proto_depIdxs = nil
}
