// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/metadata/v1/metadata_admin.proto

package metadataapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// MetadataAdminServiceClient is the client API for MetadataAdminService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Deprecated: Do not use.
type MetadataAdminServiceClient interface {
	// describe groups
	DescribeGroups(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*DescribeGroupsResult, error)
	// describe keys
	DescribeKeys(ctx context.Context, in *DescribeKeysParams, opts ...grpc.CallOption) (*DescribeKeysResult, error)
	// get key
	GetKey(ctx context.Context, in *GetKeyParams, opts ...grpc.CallOption) (*GetKeyResult, error)
	// create key
	CreateKey(ctx context.Context, in *CreateKeyParams, opts ...grpc.CallOption) (*CreateKeyResult, error)
	// update key
	UpdateKey(ctx context.Context, in *UpdateKeyParams, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// delete key
	DeleteKey(ctx context.Context, in *DeleteKeyParams, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// describe values
	DescribeValues(ctx context.Context, in *DescribeValuesParams, opts ...grpc.CallOption) (*DescribeValuesResult, error)
	// get value
	GetValue(ctx context.Context, in *GetValueParams, opts ...grpc.CallOption) (*GetValueResult, error)
	// batch update value
	BatchUpdateValue(ctx context.Context, in *BatchUpdateValueParams, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type metadataAdminServiceClient struct {
	cc grpc.ClientConnInterface
}

// Deprecated: Do not use.
func NewMetadataAdminServiceClient(cc grpc.ClientConnInterface) MetadataAdminServiceClient {
	return &metadataAdminServiceClient{cc}
}

func (c *metadataAdminServiceClient) DescribeGroups(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*DescribeGroupsResult, error) {
	out := new(DescribeGroupsResult)
	err := c.cc.Invoke(ctx, "/moego.admin.metadata.v1.MetadataAdminService/DescribeGroups", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataAdminServiceClient) DescribeKeys(ctx context.Context, in *DescribeKeysParams, opts ...grpc.CallOption) (*DescribeKeysResult, error) {
	out := new(DescribeKeysResult)
	err := c.cc.Invoke(ctx, "/moego.admin.metadata.v1.MetadataAdminService/DescribeKeys", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataAdminServiceClient) GetKey(ctx context.Context, in *GetKeyParams, opts ...grpc.CallOption) (*GetKeyResult, error) {
	out := new(GetKeyResult)
	err := c.cc.Invoke(ctx, "/moego.admin.metadata.v1.MetadataAdminService/GetKey", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataAdminServiceClient) CreateKey(ctx context.Context, in *CreateKeyParams, opts ...grpc.CallOption) (*CreateKeyResult, error) {
	out := new(CreateKeyResult)
	err := c.cc.Invoke(ctx, "/moego.admin.metadata.v1.MetadataAdminService/CreateKey", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataAdminServiceClient) UpdateKey(ctx context.Context, in *UpdateKeyParams, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.admin.metadata.v1.MetadataAdminService/UpdateKey", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataAdminServiceClient) DeleteKey(ctx context.Context, in *DeleteKeyParams, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.admin.metadata.v1.MetadataAdminService/DeleteKey", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataAdminServiceClient) DescribeValues(ctx context.Context, in *DescribeValuesParams, opts ...grpc.CallOption) (*DescribeValuesResult, error) {
	out := new(DescribeValuesResult)
	err := c.cc.Invoke(ctx, "/moego.admin.metadata.v1.MetadataAdminService/DescribeValues", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataAdminServiceClient) GetValue(ctx context.Context, in *GetValueParams, opts ...grpc.CallOption) (*GetValueResult, error) {
	out := new(GetValueResult)
	err := c.cc.Invoke(ctx, "/moego.admin.metadata.v1.MetadataAdminService/GetValue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *metadataAdminServiceClient) BatchUpdateValue(ctx context.Context, in *BatchUpdateValueParams, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.admin.metadata.v1.MetadataAdminService/BatchUpdateValue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MetadataAdminServiceServer is the server API for MetadataAdminService service.
// All implementations must embed UnimplementedMetadataAdminServiceServer
// for forward compatibility
//
// Deprecated: Do not use.
type MetadataAdminServiceServer interface {
	// describe groups
	DescribeGroups(context.Context, *emptypb.Empty) (*DescribeGroupsResult, error)
	// describe keys
	DescribeKeys(context.Context, *DescribeKeysParams) (*DescribeKeysResult, error)
	// get key
	GetKey(context.Context, *GetKeyParams) (*GetKeyResult, error)
	// create key
	CreateKey(context.Context, *CreateKeyParams) (*CreateKeyResult, error)
	// update key
	UpdateKey(context.Context, *UpdateKeyParams) (*emptypb.Empty, error)
	// delete key
	DeleteKey(context.Context, *DeleteKeyParams) (*emptypb.Empty, error)
	// describe values
	DescribeValues(context.Context, *DescribeValuesParams) (*DescribeValuesResult, error)
	// get value
	GetValue(context.Context, *GetValueParams) (*GetValueResult, error)
	// batch update value
	BatchUpdateValue(context.Context, *BatchUpdateValueParams) (*emptypb.Empty, error)
	mustEmbedUnimplementedMetadataAdminServiceServer()
}

// UnimplementedMetadataAdminServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMetadataAdminServiceServer struct {
}

func (UnimplementedMetadataAdminServiceServer) DescribeGroups(context.Context, *emptypb.Empty) (*DescribeGroupsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeGroups not implemented")
}
func (UnimplementedMetadataAdminServiceServer) DescribeKeys(context.Context, *DescribeKeysParams) (*DescribeKeysResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeKeys not implemented")
}
func (UnimplementedMetadataAdminServiceServer) GetKey(context.Context, *GetKeyParams) (*GetKeyResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKey not implemented")
}
func (UnimplementedMetadataAdminServiceServer) CreateKey(context.Context, *CreateKeyParams) (*CreateKeyResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateKey not implemented")
}
func (UnimplementedMetadataAdminServiceServer) UpdateKey(context.Context, *UpdateKeyParams) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateKey not implemented")
}
func (UnimplementedMetadataAdminServiceServer) DeleteKey(context.Context, *DeleteKeyParams) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteKey not implemented")
}
func (UnimplementedMetadataAdminServiceServer) DescribeValues(context.Context, *DescribeValuesParams) (*DescribeValuesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeValues not implemented")
}
func (UnimplementedMetadataAdminServiceServer) GetValue(context.Context, *GetValueParams) (*GetValueResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetValue not implemented")
}
func (UnimplementedMetadataAdminServiceServer) BatchUpdateValue(context.Context, *BatchUpdateValueParams) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpdateValue not implemented")
}
func (UnimplementedMetadataAdminServiceServer) mustEmbedUnimplementedMetadataAdminServiceServer() {}

// UnsafeMetadataAdminServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MetadataAdminServiceServer will
// result in compilation errors.
type UnsafeMetadataAdminServiceServer interface {
	mustEmbedUnimplementedMetadataAdminServiceServer()
}

// Deprecated: Do not use.
func RegisterMetadataAdminServiceServer(s grpc.ServiceRegistrar, srv MetadataAdminServiceServer) {
	s.RegisterService(&MetadataAdminService_ServiceDesc, srv)
}

func _MetadataAdminService_DescribeGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataAdminServiceServer).DescribeGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.metadata.v1.MetadataAdminService/DescribeGroups",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataAdminServiceServer).DescribeGroups(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataAdminService_DescribeKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeKeysParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataAdminServiceServer).DescribeKeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.metadata.v1.MetadataAdminService/DescribeKeys",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataAdminServiceServer).DescribeKeys(ctx, req.(*DescribeKeysParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataAdminService_GetKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKeyParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataAdminServiceServer).GetKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.metadata.v1.MetadataAdminService/GetKey",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataAdminServiceServer).GetKey(ctx, req.(*GetKeyParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataAdminService_CreateKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateKeyParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataAdminServiceServer).CreateKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.metadata.v1.MetadataAdminService/CreateKey",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataAdminServiceServer).CreateKey(ctx, req.(*CreateKeyParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataAdminService_UpdateKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateKeyParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataAdminServiceServer).UpdateKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.metadata.v1.MetadataAdminService/UpdateKey",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataAdminServiceServer).UpdateKey(ctx, req.(*UpdateKeyParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataAdminService_DeleteKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteKeyParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataAdminServiceServer).DeleteKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.metadata.v1.MetadataAdminService/DeleteKey",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataAdminServiceServer).DeleteKey(ctx, req.(*DeleteKeyParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataAdminService_DescribeValues_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeValuesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataAdminServiceServer).DescribeValues(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.metadata.v1.MetadataAdminService/DescribeValues",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataAdminServiceServer).DescribeValues(ctx, req.(*DescribeValuesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataAdminService_GetValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetValueParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataAdminServiceServer).GetValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.metadata.v1.MetadataAdminService/GetValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataAdminServiceServer).GetValue(ctx, req.(*GetValueParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _MetadataAdminService_BatchUpdateValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateValueParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetadataAdminServiceServer).BatchUpdateValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.metadata.v1.MetadataAdminService/BatchUpdateValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetadataAdminServiceServer).BatchUpdateValue(ctx, req.(*BatchUpdateValueParams))
	}
	return interceptor(ctx, in, info, handler)
}

// MetadataAdminService_ServiceDesc is the grpc.ServiceDesc for MetadataAdminService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MetadataAdminService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.metadata.v1.MetadataAdminService",
	HandlerType: (*MetadataAdminServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DescribeGroups",
			Handler:    _MetadataAdminService_DescribeGroups_Handler,
		},
		{
			MethodName: "DescribeKeys",
			Handler:    _MetadataAdminService_DescribeKeys_Handler,
		},
		{
			MethodName: "GetKey",
			Handler:    _MetadataAdminService_GetKey_Handler,
		},
		{
			MethodName: "CreateKey",
			Handler:    _MetadataAdminService_CreateKey_Handler,
		},
		{
			MethodName: "UpdateKey",
			Handler:    _MetadataAdminService_UpdateKey_Handler,
		},
		{
			MethodName: "DeleteKey",
			Handler:    _MetadataAdminService_DeleteKey_Handler,
		},
		{
			MethodName: "DescribeValues",
			Handler:    _MetadataAdminService_DescribeValues_Handler,
		},
		{
			MethodName: "GetValue",
			Handler:    _MetadataAdminService_GetValue_Handler,
		},
		{
			MethodName: "BatchUpdateValue",
			Handler:    _MetadataAdminService_BatchUpdateValue_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/metadata/v1/metadata_admin.proto",
}
