// @since 2-24-04-10

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/platform_sales/v1/platform_sales_admin.proto

package platformsalesapipb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Subscription plan
type SubscriptionPlan int32

const (
	// Unspecified
	SubscriptionPlan_SUBSCRIPTION_PLAN_UNSPECIFIED SubscriptionPlan = 0
	// Growth - Grooming
	SubscriptionPlan_GROWTH_GROOMING SubscriptionPlan = 1
	// Ultimate - Grooming
	SubscriptionPlan_ULTIMATE_GROOMING SubscriptionPlan = 2
	// Growth - Boarding & Daycare
	SubscriptionPlan_GROWTH_BOARDING_DAYCARE SubscriptionPlan = 3
	// Ultimate - Boarding & Daycare
	SubscriptionPlan_ULTIMATE_BOARDING_DAYCARE SubscriptionPlan = 4
	// Enterprise - Boarding & Daycare
	SubscriptionPlan_ENTERPRISE_BOARDING_DAYCARE SubscriptionPlan = 5
)

// Enum value maps for SubscriptionPlan.
var (
	SubscriptionPlan_name = map[int32]string{
		0: "SUBSCRIPTION_PLAN_UNSPECIFIED",
		1: "GROWTH_GROOMING",
		2: "ULTIMATE_GROOMING",
		3: "GROWTH_BOARDING_DAYCARE",
		4: "ULTIMATE_BOARDING_DAYCARE",
		5: "ENTERPRISE_BOARDING_DAYCARE",
	}
	SubscriptionPlan_value = map[string]int32{
		"SUBSCRIPTION_PLAN_UNSPECIFIED": 0,
		"GROWTH_GROOMING":               1,
		"ULTIMATE_GROOMING":             2,
		"GROWTH_BOARDING_DAYCARE":       3,
		"ULTIMATE_BOARDING_DAYCARE":     4,
		"ENTERPRISE_BOARDING_DAYCARE":   5,
	}
)

func (x SubscriptionPlan) Enum() *SubscriptionPlan {
	p := new(SubscriptionPlan)
	*p = x
	return p
}

func (x SubscriptionPlan) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubscriptionPlan) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_admin_platform_sales_v1_platform_sales_admin_proto_enumTypes[0].Descriptor()
}

func (SubscriptionPlan) Type() protoreflect.EnumType {
	return &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_enumTypes[0]
}

func (x SubscriptionPlan) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubscriptionPlan.Descriptor instead.
func (SubscriptionPlan) EnumDescriptor() ([]byte, []int) {
	return file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescGZIP(), []int{0}
}

// Custom rate approval status
type CustomRateApprovalStatus int32

const (
	// unspecified
	CustomRateApprovalStatus_CUSTOM_RATE_APPROVAL_STATUS_UNSPECIFIED CustomRateApprovalStatus = 0
	// ignored
	CustomRateApprovalStatus_CUSTOM_RATE_APPROVAL_STATUS_IGNORED CustomRateApprovalStatus = 1
	// pending
	CustomRateApprovalStatus_CUSTOM_RATE_APPROVAL_STATUS_PENDING CustomRateApprovalStatus = 2
	// approved
	CustomRateApprovalStatus_CUSTOM_RATE_APPROVAL_STATUS_APPROVED CustomRateApprovalStatus = 3
	// rejected
	CustomRateApprovalStatus_CUSTOM_RATE_APPROVAL_STATUS_REJECTED CustomRateApprovalStatus = 4
)

// Enum value maps for CustomRateApprovalStatus.
var (
	CustomRateApprovalStatus_name = map[int32]string{
		0: "CUSTOM_RATE_APPROVAL_STATUS_UNSPECIFIED",
		1: "CUSTOM_RATE_APPROVAL_STATUS_IGNORED",
		2: "CUSTOM_RATE_APPROVAL_STATUS_PENDING",
		3: "CUSTOM_RATE_APPROVAL_STATUS_APPROVED",
		4: "CUSTOM_RATE_APPROVAL_STATUS_REJECTED",
	}
	CustomRateApprovalStatus_value = map[string]int32{
		"CUSTOM_RATE_APPROVAL_STATUS_UNSPECIFIED": 0,
		"CUSTOM_RATE_APPROVAL_STATUS_IGNORED":     1,
		"CUSTOM_RATE_APPROVAL_STATUS_PENDING":     2,
		"CUSTOM_RATE_APPROVAL_STATUS_APPROVED":    3,
		"CUSTOM_RATE_APPROVAL_STATUS_REJECTED":    4,
	}
)

func (x CustomRateApprovalStatus) Enum() *CustomRateApprovalStatus {
	p := new(CustomRateApprovalStatus)
	*p = x
	return p
}

func (x CustomRateApprovalStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomRateApprovalStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_admin_platform_sales_v1_platform_sales_admin_proto_enumTypes[1].Descriptor()
}

func (CustomRateApprovalStatus) Type() protoreflect.EnumType {
	return &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_enumTypes[1]
}

func (x CustomRateApprovalStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomRateApprovalStatus.Descriptor instead.
func (CustomRateApprovalStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescGZIP(), []int{1}
}

// platform sales record
type PlatformSalesRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// code
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	// accountId
	AccountId int64 `protobuf:"varint,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// agreementId
	AgreementId int64 `protobuf:"varint,4,opt,name=agreement_id,json=agreementId,proto3" json:"agreement_id,omitempty"`
	// companyId
	CompanyId int64 `protobuf:"varint,5,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// email
	Email string `protobuf:"bytes,6,opt,name=email,proto3" json:"email,omitempty"`
	// companyType
	CompanyType int32 `protobuf:"varint,7,opt,name=company_type,json=companyType,proto3" json:"company_type,omitempty"`
	// subDiscount
	SubDiscount int32 `protobuf:"varint,8,opt,name=sub_discount,json=subDiscount,proto3" json:"sub_discount,omitempty"`
	// subCouponValidMonth
	SubCouponValidMonth int32 `protobuf:"varint,9,opt,name=sub_coupon_valid_month,json=subCouponValidMonth,proto3" json:"sub_coupon_valid_month,omitempty"`
	// subCouponCode
	SubCouponCode string `protobuf:"bytes,10,opt,name=sub_coupon_code,json=subCouponCode,proto3" json:"sub_coupon_code,omitempty"`
	// needHardware
	NeedHardware int32 `protobuf:"varint,11,opt,name=need_hardware,json=needHardware,proto3" json:"need_hardware,omitempty"`
	// hardwareDiscount
	HardwareDiscount int32 `protobuf:"varint,12,opt,name=hardware_discount,json=hardwareDiscount,proto3" json:"hardware_discount,omitempty"`
	// hardwareCode
	HardwareCode string `protobuf:"bytes,13,opt,name=hardware_code,json=hardwareCode,proto3" json:"hardware_code,omitempty"`
	// vansNum
	VansNum int32 `protobuf:"varint,14,opt,name=vans_num,json=vansNum,proto3" json:"vans_num,omitempty"`
	// locationNum
	LocationNum int32 `protobuf:"varint,15,opt,name=location_num,json=locationNum,proto3" json:"location_num,omitempty"`
	// agreementRecordUuid
	AgreementRecordUuid string `protobuf:"bytes,16,opt,name=agreement_record_uuid,json=agreementRecordUuid,proto3" json:"agreement_record_uuid,omitempty"`
	// signedTime
	SignedTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=signed_time,json=signedTime,proto3" json:"signed_time,omitempty"`
	// orderShippingStatus
	OrderShippingStatus string `protobuf:"bytes,18,opt,name=order_shipping_status,json=orderShippingStatus,proto3" json:"order_shipping_status,omitempty"`
	// premiumType
	PremiumType int32 `protobuf:"varint,19,opt,name=premium_type,json=premiumType,proto3" json:"premium_type,omitempty"`
	// status
	Status int32 `protobuf:"varint,20,opt,name=status,proto3" json:"status,omitempty"`
	// sales status
	SalesStatus int32 `protobuf:"varint,21,opt,name=sales_status,json=salesStatus,proto3" json:"sales_status,omitempty"`
	// createdAt
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updatedAt
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,23,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// link
	Link *string `protobuf:"bytes,24,opt,name=link,proto3,oneof" json:"link,omitempty"`
	// show monthly term
	ShowMonthlyTerm int32 `protobuf:"varint,25,opt,name=show_monthly_term,json=showMonthlyTerm,proto3" json:"show_monthly_term,omitempty"`
	// show annually term
	ShowAnnuallyTerm int32 `protobuf:"varint,26,opt,name=show_annually_term,json=showAnnuallyTerm,proto3" json:"show_annually_term,omitempty"`
	// creator
	Creator string `protobuf:"bytes,27,opt,name=creator,proto3" json:"creator,omitempty"`
	// show hardware
	ShowHardware int32 `protobuf:"varint,28,opt,name=show_hardware,json=showHardware,proto3" json:"show_hardware,omitempty"`
	// is bd plan
	IsBdPlan int32 `protobuf:"varint,29,opt,name=is_bd_plan,json=isBdPlan,proto3" json:"is_bd_plan,omitempty"`
	// terminal card processing rate
	TerminalCardRate string `protobuf:"bytes,30,opt,name=terminal_card_rate,json=terminalCardRate,proto3" json:"terminal_card_rate,omitempty"`
	// non - terminal card processing rate
	NonTerminalCardRate string `protobuf:"bytes,31,opt,name=non_terminal_card_rate,json=nonTerminalCardRate,proto3" json:"non_terminal_card_rate,omitempty"`
	// minimum monthly transaction volume
	MinMonthlyTransaction string `protobuf:"bytes,32,opt,name=min_monthly_transaction,json=minMonthlyTransaction,proto3" json:"min_monthly_transaction,omitempty"`
	// is customer rate
	IsCustomRate int32 `protobuf:"varint,33,opt,name=is_custom_rate,json=isCustomRate,proto3" json:"is_custom_rate,omitempty"`
	// show accounting
	ShowAccounting int32 `protobuf:"varint,34,opt,name=show_accounting,json=showAccounting,proto3" json:"show_accounting,omitempty"`
	// tier
	Tier string `protobuf:"bytes,35,opt,name=tier,proto3" json:"tier,omitempty"`
	// subscription plan
	SubscriptionPlan SubscriptionPlan `protobuf:"varint,36,opt,name=subscription_plan,json=subscriptionPlan,proto3,enum=moego.admin.platform_sales.v1.SubscriptionPlan" json:"subscription_plan,omitempty"`
	// custom rate approval status
	CustomRateApprovalStatus CustomRateApprovalStatus `protobuf:"varint,37,opt,name=custom_rate_approval_status,json=customRateApprovalStatus,proto3,enum=moego.admin.platform_sales.v1.CustomRateApprovalStatus" json:"custom_rate_approval_status,omitempty"`
	// opportunity id
	OpportunityId string `protobuf:"bytes,38,opt,name=opportunity_id,json=opportunityId,proto3" json:"opportunity_id,omitempty"`
}

func (x *PlatformSalesRecord) Reset() {
	*x = PlatformSalesRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlatformSalesRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlatformSalesRecord) ProtoMessage() {}

func (x *PlatformSalesRecord) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlatformSalesRecord.ProtoReflect.Descriptor instead.
func (*PlatformSalesRecord) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescGZIP(), []int{0}
}

func (x *PlatformSalesRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PlatformSalesRecord) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *PlatformSalesRecord) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *PlatformSalesRecord) GetAgreementId() int64 {
	if x != nil {
		return x.AgreementId
	}
	return 0
}

func (x *PlatformSalesRecord) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *PlatformSalesRecord) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *PlatformSalesRecord) GetCompanyType() int32 {
	if x != nil {
		return x.CompanyType
	}
	return 0
}

func (x *PlatformSalesRecord) GetSubDiscount() int32 {
	if x != nil {
		return x.SubDiscount
	}
	return 0
}

func (x *PlatformSalesRecord) GetSubCouponValidMonth() int32 {
	if x != nil {
		return x.SubCouponValidMonth
	}
	return 0
}

func (x *PlatformSalesRecord) GetSubCouponCode() string {
	if x != nil {
		return x.SubCouponCode
	}
	return ""
}

func (x *PlatformSalesRecord) GetNeedHardware() int32 {
	if x != nil {
		return x.NeedHardware
	}
	return 0
}

func (x *PlatformSalesRecord) GetHardwareDiscount() int32 {
	if x != nil {
		return x.HardwareDiscount
	}
	return 0
}

func (x *PlatformSalesRecord) GetHardwareCode() string {
	if x != nil {
		return x.HardwareCode
	}
	return ""
}

func (x *PlatformSalesRecord) GetVansNum() int32 {
	if x != nil {
		return x.VansNum
	}
	return 0
}

func (x *PlatformSalesRecord) GetLocationNum() int32 {
	if x != nil {
		return x.LocationNum
	}
	return 0
}

func (x *PlatformSalesRecord) GetAgreementRecordUuid() string {
	if x != nil {
		return x.AgreementRecordUuid
	}
	return ""
}

func (x *PlatformSalesRecord) GetSignedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SignedTime
	}
	return nil
}

func (x *PlatformSalesRecord) GetOrderShippingStatus() string {
	if x != nil {
		return x.OrderShippingStatus
	}
	return ""
}

func (x *PlatformSalesRecord) GetPremiumType() int32 {
	if x != nil {
		return x.PremiumType
	}
	return 0
}

func (x *PlatformSalesRecord) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PlatformSalesRecord) GetSalesStatus() int32 {
	if x != nil {
		return x.SalesStatus
	}
	return 0
}

func (x *PlatformSalesRecord) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PlatformSalesRecord) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *PlatformSalesRecord) GetLink() string {
	if x != nil && x.Link != nil {
		return *x.Link
	}
	return ""
}

func (x *PlatformSalesRecord) GetShowMonthlyTerm() int32 {
	if x != nil {
		return x.ShowMonthlyTerm
	}
	return 0
}

func (x *PlatformSalesRecord) GetShowAnnuallyTerm() int32 {
	if x != nil {
		return x.ShowAnnuallyTerm
	}
	return 0
}

func (x *PlatformSalesRecord) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *PlatformSalesRecord) GetShowHardware() int32 {
	if x != nil {
		return x.ShowHardware
	}
	return 0
}

func (x *PlatformSalesRecord) GetIsBdPlan() int32 {
	if x != nil {
		return x.IsBdPlan
	}
	return 0
}

func (x *PlatformSalesRecord) GetTerminalCardRate() string {
	if x != nil {
		return x.TerminalCardRate
	}
	return ""
}

func (x *PlatformSalesRecord) GetNonTerminalCardRate() string {
	if x != nil {
		return x.NonTerminalCardRate
	}
	return ""
}

func (x *PlatformSalesRecord) GetMinMonthlyTransaction() string {
	if x != nil {
		return x.MinMonthlyTransaction
	}
	return ""
}

func (x *PlatformSalesRecord) GetIsCustomRate() int32 {
	if x != nil {
		return x.IsCustomRate
	}
	return 0
}

func (x *PlatformSalesRecord) GetShowAccounting() int32 {
	if x != nil {
		return x.ShowAccounting
	}
	return 0
}

func (x *PlatformSalesRecord) GetTier() string {
	if x != nil {
		return x.Tier
	}
	return ""
}

func (x *PlatformSalesRecord) GetSubscriptionPlan() SubscriptionPlan {
	if x != nil {
		return x.SubscriptionPlan
	}
	return SubscriptionPlan_SUBSCRIPTION_PLAN_UNSPECIFIED
}

func (x *PlatformSalesRecord) GetCustomRateApprovalStatus() CustomRateApprovalStatus {
	if x != nil {
		return x.CustomRateApprovalStatus
	}
	return CustomRateApprovalStatus_CUSTOM_RATE_APPROVAL_STATUS_UNSPECIFIED
}

func (x *PlatformSalesRecord) GetOpportunityId() string {
	if x != nil {
		return x.OpportunityId
	}
	return ""
}

// create platform sales link params
type CreatePlatformSalesLinkParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// email
	Email string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	// subscription term
	SubTerm *int32 `protobuf:"varint,2,opt,name=sub_term,json=subTerm,proto3,oneof" json:"sub_term,omitempty"`
	// 5=5% 10=10% number=number%
	SubPriceDiscount *int32 `protobuf:"varint,3,opt,name=sub_price_discount,json=subPriceDiscount,proto3,oneof" json:"sub_price_discount,omitempty"`
	// need hardware
	NeedHardware bool `protobuf:"varint,4,opt,name=need_hardware,json=needHardware,proto3" json:"need_hardware,omitempty"`
	// hardware discount
	HardwareDiscount *int32 `protobuf:"varint,5,opt,name=hardware_discount,json=hardwareDiscount,proto3,oneof" json:"hardware_discount,omitempty"`
	// van number
	VansNum *int32 `protobuf:"varint,6,opt,name=vans_num,json=vansNum,proto3,oneof" json:"vans_num,omitempty"`
	// location number
	LocationNum *int32 `protobuf:"varint,7,opt,name=location_num,json=locationNum,proto3,oneof" json:"location_num,omitempty"`
	// 0 mobile 1salon  3 mobile and salon
	CompanyType *int32 `protobuf:"varint,8,opt,name=company_type,json=companyType,proto3,oneof" json:"company_type,omitempty"`
	// 2 or 3, use subscription_plan instead
	//
	// Deprecated: Do not use.
	PremiumType int32 `protobuf:"varint,9,opt,name=premium_type,json=premiumType,proto3" json:"premium_type,omitempty"`
	// show monthly term
	ShowMonthlyTerm bool `protobuf:"varint,10,opt,name=show_monthly_term,json=showMonthlyTerm,proto3" json:"show_monthly_term,omitempty"`
	// show annually term
	ShowAnnuallyTerm bool `protobuf:"varint,11,opt,name=show_annually_term,json=showAnnuallyTerm,proto3" json:"show_annually_term,omitempty"`
	// creator
	Creator string `protobuf:"bytes,12,opt,name=creator,proto3" json:"creator,omitempty"`
	// show hardware
	ShowHardware bool `protobuf:"varint,13,opt,name=show_hardware,json=showHardware,proto3" json:"show_hardware,omitempty"`
	// is bd plan, use subscription_plan instead
	//
	// Deprecated: Do not use.
	IsBdPlan bool `protobuf:"varint,14,opt,name=is_bd_plan,json=isBdPlan,proto3" json:"is_bd_plan,omitempty"`
	// terminal card processing rate
	TerminalCardRate *string `protobuf:"bytes,15,opt,name=terminal_card_rate,json=terminalCardRate,proto3,oneof" json:"terminal_card_rate,omitempty"`
	// non - terminal card processing rate
	NonTerminalCardRate *string `protobuf:"bytes,16,opt,name=non_terminal_card_rate,json=nonTerminalCardRate,proto3,oneof" json:"non_terminal_card_rate,omitempty"`
	// minimum monthly transaction volume
	MinMonthlyTransaction *string `protobuf:"bytes,17,opt,name=min_monthly_transaction,json=minMonthlyTransaction,proto3,oneof" json:"min_monthly_transaction,omitempty"`
	// is customer rate
	IsCustomRate bool `protobuf:"varint,18,opt,name=is_custom_rate,json=isCustomRate,proto3" json:"is_custom_rate,omitempty"`
	// show accounting
	ShowAccounting bool `protobuf:"varint,19,opt,name=show_accounting,json=showAccounting,proto3" json:"show_accounting,omitempty"`
	// subscription plan
	SubscriptionPlan SubscriptionPlan `protobuf:"varint,20,opt,name=subscription_plan,json=subscriptionPlan,proto3,enum=moego.admin.platform_sales.v1.SubscriptionPlan" json:"subscription_plan,omitempty"`
	// tier
	Tier string `protobuf:"bytes,21,opt,name=tier,proto3" json:"tier,omitempty"`
	// preset custom rate
	PresetCustomRate *CreatePlatformSalesLinkParams_PresetCustomRate `protobuf:"bytes,22,opt,name=preset_custom_rate,json=presetCustomRate,proto3,oneof" json:"preset_custom_rate,omitempty"`
	// preset minimum monthly transaction volume, different from min_monthly_transaction
	PresetMinMonthlyTransaction *string `protobuf:"bytes,23,opt,name=preset_min_monthly_transaction,json=presetMinMonthlyTransaction,proto3,oneof" json:"preset_min_monthly_transaction,omitempty"`
	// opportunity id
	OpportunityId string `protobuf:"bytes,24,opt,name=opportunity_id,json=opportunityId,proto3" json:"opportunity_id,omitempty"`
}

func (x *CreatePlatformSalesLinkParams) Reset() {
	*x = CreatePlatformSalesLinkParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePlatformSalesLinkParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePlatformSalesLinkParams) ProtoMessage() {}

func (x *CreatePlatformSalesLinkParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePlatformSalesLinkParams.ProtoReflect.Descriptor instead.
func (*CreatePlatformSalesLinkParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescGZIP(), []int{1}
}

func (x *CreatePlatformSalesLinkParams) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreatePlatformSalesLinkParams) GetSubTerm() int32 {
	if x != nil && x.SubTerm != nil {
		return *x.SubTerm
	}
	return 0
}

func (x *CreatePlatformSalesLinkParams) GetSubPriceDiscount() int32 {
	if x != nil && x.SubPriceDiscount != nil {
		return *x.SubPriceDiscount
	}
	return 0
}

func (x *CreatePlatformSalesLinkParams) GetNeedHardware() bool {
	if x != nil {
		return x.NeedHardware
	}
	return false
}

func (x *CreatePlatformSalesLinkParams) GetHardwareDiscount() int32 {
	if x != nil && x.HardwareDiscount != nil {
		return *x.HardwareDiscount
	}
	return 0
}

func (x *CreatePlatformSalesLinkParams) GetVansNum() int32 {
	if x != nil && x.VansNum != nil {
		return *x.VansNum
	}
	return 0
}

func (x *CreatePlatformSalesLinkParams) GetLocationNum() int32 {
	if x != nil && x.LocationNum != nil {
		return *x.LocationNum
	}
	return 0
}

func (x *CreatePlatformSalesLinkParams) GetCompanyType() int32 {
	if x != nil && x.CompanyType != nil {
		return *x.CompanyType
	}
	return 0
}

// Deprecated: Do not use.
func (x *CreatePlatformSalesLinkParams) GetPremiumType() int32 {
	if x != nil {
		return x.PremiumType
	}
	return 0
}

func (x *CreatePlatformSalesLinkParams) GetShowMonthlyTerm() bool {
	if x != nil {
		return x.ShowMonthlyTerm
	}
	return false
}

func (x *CreatePlatformSalesLinkParams) GetShowAnnuallyTerm() bool {
	if x != nil {
		return x.ShowAnnuallyTerm
	}
	return false
}

func (x *CreatePlatformSalesLinkParams) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *CreatePlatformSalesLinkParams) GetShowHardware() bool {
	if x != nil {
		return x.ShowHardware
	}
	return false
}

// Deprecated: Do not use.
func (x *CreatePlatformSalesLinkParams) GetIsBdPlan() bool {
	if x != nil {
		return x.IsBdPlan
	}
	return false
}

func (x *CreatePlatformSalesLinkParams) GetTerminalCardRate() string {
	if x != nil && x.TerminalCardRate != nil {
		return *x.TerminalCardRate
	}
	return ""
}

func (x *CreatePlatformSalesLinkParams) GetNonTerminalCardRate() string {
	if x != nil && x.NonTerminalCardRate != nil {
		return *x.NonTerminalCardRate
	}
	return ""
}

func (x *CreatePlatformSalesLinkParams) GetMinMonthlyTransaction() string {
	if x != nil && x.MinMonthlyTransaction != nil {
		return *x.MinMonthlyTransaction
	}
	return ""
}

func (x *CreatePlatformSalesLinkParams) GetIsCustomRate() bool {
	if x != nil {
		return x.IsCustomRate
	}
	return false
}

func (x *CreatePlatformSalesLinkParams) GetShowAccounting() bool {
	if x != nil {
		return x.ShowAccounting
	}
	return false
}

func (x *CreatePlatformSalesLinkParams) GetSubscriptionPlan() SubscriptionPlan {
	if x != nil {
		return x.SubscriptionPlan
	}
	return SubscriptionPlan_SUBSCRIPTION_PLAN_UNSPECIFIED
}

func (x *CreatePlatformSalesLinkParams) GetTier() string {
	if x != nil {
		return x.Tier
	}
	return ""
}

func (x *CreatePlatformSalesLinkParams) GetPresetCustomRate() *CreatePlatformSalesLinkParams_PresetCustomRate {
	if x != nil {
		return x.PresetCustomRate
	}
	return nil
}

func (x *CreatePlatformSalesLinkParams) GetPresetMinMonthlyTransaction() string {
	if x != nil && x.PresetMinMonthlyTransaction != nil {
		return *x.PresetMinMonthlyTransaction
	}
	return ""
}

func (x *CreatePlatformSalesLinkParams) GetOpportunityId() string {
	if x != nil {
		return x.OpportunityId
	}
	return ""
}

// create platform sales link result
type CreatePlatformSalesLinkResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// link
	Link string `protobuf:"bytes,1,opt,name=link,proto3" json:"link,omitempty"`
}

func (x *CreatePlatformSalesLinkResult) Reset() {
	*x = CreatePlatformSalesLinkResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePlatformSalesLinkResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePlatformSalesLinkResult) ProtoMessage() {}

func (x *CreatePlatformSalesLinkResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePlatformSalesLinkResult.ProtoReflect.Descriptor instead.
func (*CreatePlatformSalesLinkResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescGZIP(), []int{2}
}

func (x *CreatePlatformSalesLinkResult) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

// get platform sales record list params
type GetPlatformSalesRecordListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// code
	Code *string `protobuf:"bytes,1,opt,name=code,proto3,oneof" json:"code,omitempty"`
	// email
	Email *string `protobuf:"bytes,2,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// agreement_uuid
	AgreementRecordUuid *string `protobuf:"bytes,3,opt,name=agreement_record_uuid,json=agreementRecordUuid,proto3,oneof" json:"agreement_record_uuid,omitempty"`
	// account
	AccountId *int64 `protobuf:"varint,4,opt,name=account_id,json=accountId,proto3,oneof" json:"account_id,omitempty"`
	// status
	Status *int32 `protobuf:"varint,5,opt,name=status,proto3,oneof" json:"status,omitempty"`
	// status
	Creator *string `protobuf:"bytes,6,opt,name=creator,proto3,oneof" json:"creator,omitempty"`
	// custom rate approval status
	CustomRateApprovalStatuses []CustomRateApprovalStatus `protobuf:"varint,7,rep,packed,name=custom_rate_approval_statuses,json=customRateApprovalStatuses,proto3,enum=moego.admin.platform_sales.v1.CustomRateApprovalStatus" json:"custom_rate_approval_statuses,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetPlatformSalesRecordListParams) Reset() {
	*x = GetPlatformSalesRecordListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlatformSalesRecordListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlatformSalesRecordListParams) ProtoMessage() {}

func (x *GetPlatformSalesRecordListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlatformSalesRecordListParams.ProtoReflect.Descriptor instead.
func (*GetPlatformSalesRecordListParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescGZIP(), []int{3}
}

func (x *GetPlatformSalesRecordListParams) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *GetPlatformSalesRecordListParams) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *GetPlatformSalesRecordListParams) GetAgreementRecordUuid() string {
	if x != nil && x.AgreementRecordUuid != nil {
		return *x.AgreementRecordUuid
	}
	return ""
}

func (x *GetPlatformSalesRecordListParams) GetAccountId() int64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *GetPlatformSalesRecordListParams) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *GetPlatformSalesRecordListParams) GetCreator() string {
	if x != nil && x.Creator != nil {
		return *x.Creator
	}
	return ""
}

func (x *GetPlatformSalesRecordListParams) GetCustomRateApprovalStatuses() []CustomRateApprovalStatus {
	if x != nil {
		return x.CustomRateApprovalStatuses
	}
	return nil
}

func (x *GetPlatformSalesRecordListParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// get platform sales record list result
type GetPlatformSalesRecordListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// platform sales record list
	PlatformSalesRecords []*PlatformSalesRecord `protobuf:"bytes,1,rep,name=platform_sales_records,json=platformSalesRecords,proto3" json:"platform_sales_records,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *GetPlatformSalesRecordListResult) Reset() {
	*x = GetPlatformSalesRecordListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlatformSalesRecordListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlatformSalesRecordListResult) ProtoMessage() {}

func (x *GetPlatformSalesRecordListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlatformSalesRecordListResult.ProtoReflect.Descriptor instead.
func (*GetPlatformSalesRecordListResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescGZIP(), []int{4}
}

func (x *GetPlatformSalesRecordListResult) GetPlatformSalesRecords() []*PlatformSalesRecord {
	if x != nil {
		return x.PlatformSalesRecords
	}
	return nil
}

func (x *GetPlatformSalesRecordListResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// delete platform sales record params
type DeletePlatformSalesRecordParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletePlatformSalesRecordParams) Reset() {
	*x = DeletePlatformSalesRecordParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePlatformSalesRecordParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePlatformSalesRecordParams) ProtoMessage() {}

func (x *DeletePlatformSalesRecordParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePlatformSalesRecordParams.ProtoReflect.Descriptor instead.
func (*DeletePlatformSalesRecordParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescGZIP(), []int{5}
}

func (x *DeletePlatformSalesRecordParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete platform sales record result
type DeletePlatformSalesRecordResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePlatformSalesRecordResult) Reset() {
	*x = DeletePlatformSalesRecordResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePlatformSalesRecordResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePlatformSalesRecordResult) ProtoMessage() {}

func (x *DeletePlatformSalesRecordResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePlatformSalesRecordResult.ProtoReflect.Descriptor instead.
func (*DeletePlatformSalesRecordResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescGZIP(), []int{6}
}

// update platform sales record params
type UpdatePlatformSalesRecordParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// custom rate approval status
	CustomRateApprovalStatus *CustomRateApprovalStatus `protobuf:"varint,2,opt,name=custom_rate_approval_status,json=customRateApprovalStatus,proto3,enum=moego.admin.platform_sales.v1.CustomRateApprovalStatus,oneof" json:"custom_rate_approval_status,omitempty"`
}

func (x *UpdatePlatformSalesRecordParams) Reset() {
	*x = UpdatePlatformSalesRecordParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePlatformSalesRecordParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePlatformSalesRecordParams) ProtoMessage() {}

func (x *UpdatePlatformSalesRecordParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePlatformSalesRecordParams.ProtoReflect.Descriptor instead.
func (*UpdatePlatformSalesRecordParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescGZIP(), []int{7}
}

func (x *UpdatePlatformSalesRecordParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePlatformSalesRecordParams) GetCustomRateApprovalStatus() CustomRateApprovalStatus {
	if x != nil && x.CustomRateApprovalStatus != nil {
		return *x.CustomRateApprovalStatus
	}
	return CustomRateApprovalStatus_CUSTOM_RATE_APPROVAL_STATUS_UNSPECIFIED
}

// update platform sales record result
type UpdatePlatformSalesRecordResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdatePlatformSalesRecordResult) Reset() {
	*x = UpdatePlatformSalesRecordResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePlatformSalesRecordResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePlatformSalesRecordResult) ProtoMessage() {}

func (x *UpdatePlatformSalesRecordResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePlatformSalesRecordResult.ProtoReflect.Descriptor instead.
func (*UpdatePlatformSalesRecordResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescGZIP(), []int{8}
}

// preset custom rate
type CreatePlatformSalesLinkParams_PresetCustomRate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// option
	Option string `protobuf:"bytes,1,opt,name=option,proto3" json:"option,omitempty"`
	// terminal card rate
	TerminalCardRate string `protobuf:"bytes,2,opt,name=terminal_card_rate,json=terminalCardRate,proto3" json:"terminal_card_rate,omitempty"`
	// non-terminal card rate
	NonTerminalCardRate string `protobuf:"bytes,3,opt,name=non_terminal_card_rate,json=nonTerminalCardRate,proto3" json:"non_terminal_card_rate,omitempty"`
	// spif
	Spif string `protobuf:"bytes,4,opt,name=spif,proto3" json:"spif,omitempty"`
}

func (x *CreatePlatformSalesLinkParams_PresetCustomRate) Reset() {
	*x = CreatePlatformSalesLinkParams_PresetCustomRate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePlatformSalesLinkParams_PresetCustomRate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePlatformSalesLinkParams_PresetCustomRate) ProtoMessage() {}

func (x *CreatePlatformSalesLinkParams_PresetCustomRate) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePlatformSalesLinkParams_PresetCustomRate.ProtoReflect.Descriptor instead.
func (*CreatePlatformSalesLinkParams_PresetCustomRate) Descriptor() ([]byte, []int) {
	return file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescGZIP(), []int{1, 0}
}

func (x *CreatePlatformSalesLinkParams_PresetCustomRate) GetOption() string {
	if x != nil {
		return x.Option
	}
	return ""
}

func (x *CreatePlatformSalesLinkParams_PresetCustomRate) GetTerminalCardRate() string {
	if x != nil {
		return x.TerminalCardRate
	}
	return ""
}

func (x *CreatePlatformSalesLinkParams_PresetCustomRate) GetNonTerminalCardRate() string {
	if x != nil {
		return x.NonTerminalCardRate
	}
	return ""
}

func (x *CreatePlatformSalesLinkParams_PresetCustomRate) GetSpif() string {
	if x != nil {
		return x.Spif
	}
	return ""
}

var File_moego_admin_platform_sales_v1_platform_sales_admin_proto protoreflect.FileDescriptor

var file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDesc = []byte{
	0x0a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x5f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd5, 0x0c,
	0x0a, 0x13, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x16, 0x73, 0x75, 0x62, 0x5f, 0x63, 0x6f,
	0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x75, 0x70, 0x6f,
	0x6e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x26, 0x0a, 0x0f, 0x73,
	0x75, 0x62, 0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6e, 0x65, 0x65, 0x64,
	0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x68, 0x61, 0x72, 0x64,
	0x77, 0x61, 0x72, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x10, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x68, 0x61,
	0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x61,
	0x6e, 0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x61,
	0x6e, 0x73, 0x4e, 0x75, 0x6d, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x32, 0x0a, 0x15, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x75, 0x75, 0x69,
	0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x55, 0x75, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x0b,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x73,
	0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x61, 0x6c, 0x65,
	0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x73, 0x61, 0x6c, 0x65, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x17, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x68,
	0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x73, 0x68, 0x6f, 0x77, 0x4d, 0x6f, 0x6e, 0x74, 0x68,
	0x6c, 0x79, 0x54, 0x65, 0x72, 0x6d, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x61,
	0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x6c, 0x79, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x10, 0x73, 0x68, 0x6f, 0x77, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x6c, 0x79,
	0x54, 0x65, 0x72, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x23,
	0x0a, 0x0d, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x68, 0x6f, 0x77, 0x48, 0x61, 0x72, 0x64, 0x77,
	0x61, 0x72, 0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x62, 0x64, 0x5f, 0x70, 0x6c, 0x61,
	0x6e, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x73, 0x42, 0x64, 0x50, 0x6c, 0x61,
	0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x52, 0x61, 0x74, 0x65, 0x12,
	0x33, 0x0a, 0x16, 0x6e, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x6e, 0x6f, 0x6e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x61, 0x74, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x6d, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74,
	0x68, 0x6c, 0x79, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6d, 0x69, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c,
	0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e,
	0x69, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x21,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x69, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x61,
	0x74, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x22, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x68, 0x6f,
	0x77, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x69, 0x65, 0x72, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x69, 0x65, 0x72, 0x12,
	0x5c, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x70, 0x6c, 0x61, 0x6e, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6c, 0x61, 0x6e, 0x52, 0x10, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x76, 0x0a,
	0x1b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x25, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x18, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x52, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x75,
	0x6e, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x49, 0x64, 0x42, 0x07, 0x0a, 0x05,
	0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0xa0, 0x0d, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x4c, 0x69, 0x6e,
	0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x18, 0x40, 0x60,
	0x01, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x27, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f,
	0x74, 0x65, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x28, 0x00, 0x48, 0x00, 0x52, 0x07, 0x73, 0x75, 0x62, 0x54, 0x65, 0x72, 0x6d, 0x88, 0x01,
	0x01, 0x12, 0x31, 0x0a, 0x12, 0x73, 0x75, 0x62, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52,
	0x10, 0x73, 0x75, 0x62, 0x50, 0x72, 0x69, 0x63, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0d, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x68, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6e, 0x65, 0x65,
	0x64, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x12, 0x30, 0x0a, 0x11, 0x68, 0x61, 0x72,
	0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x48, 0x02, 0x52, 0x10, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x08, 0x76,
	0x61, 0x6e, 0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x03, 0x52, 0x07, 0x76, 0x61, 0x6e, 0x73, 0x4e, 0x75,
	0x6d, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x28, 0x00, 0x48, 0x04, 0x52, 0x0b, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e,
	0x75, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x1a, 0x02, 0x28, 0x00, 0x48, 0x05, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x6d, 0x69, 0x75,
	0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0x18, 0x01,
	0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x6e,
	0x74, 0x68, 0x6c, 0x79, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0f, 0x73, 0x68, 0x6f, 0x77, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x54, 0x65, 0x72, 0x6d,
	0x12, 0x2c, 0x0a, 0x12, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x6c,
	0x79, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x73, 0x68,
	0x6f, 0x77, 0x41, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x6c, 0x79, 0x54, 0x65, 0x72, 0x6d, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x68, 0x6f, 0x77,
	0x5f, 0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x73, 0x68, 0x6f, 0x77, 0x48, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x12, 0x20, 0x0a,
	0x0a, 0x69, 0x73, 0x5f, 0x62, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x69, 0x73, 0x42, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x12,
	0x3b, 0x0a, 0x12, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x06, 0x52, 0x10, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x43, 0x61, 0x72, 0x64, 0x52, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x42, 0x0a, 0x16,
	0x6e, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x07, 0x52, 0x13, 0x6e, 0x6f, 0x6e, 0x54, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x52, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x45, 0x0a, 0x17, 0x6d, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x08, 0x52, 0x15, 0x6d,
	0x69, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x69, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a,
	0x0f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x73, 0x68, 0x6f, 0x77, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x5c, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6c,
	0x61, 0x6e, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x6c, 0x61, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x65, 0x72, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x69, 0x65, 0x72, 0x12, 0x80, 0x01, 0x0a, 0x12, 0x70, 0x72, 0x65,
	0x73, 0x65, 0x74, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x73, 0x61, 0x6c,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x4c, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x2e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x52, 0x61, 0x74, 0x65, 0x48, 0x09, 0x52, 0x10, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x52, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x52, 0x0a, 0x1e, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c,
	0x79, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x0a, 0x52,
	0x1b, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x4d, 0x69, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c,
	0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12,
	0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x75,
	0x6e, 0x69, 0x74, 0x79, 0x49, 0x64, 0x1a, 0xbe, 0x01, 0x0a, 0x10, 0x50, 0x72, 0x65, 0x73, 0x65,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x12, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x10, 0x74, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x52, 0x61, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x16, 0x6e,
	0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x13, 0x6e, 0x6f, 0x6e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x52, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x73, 0x70,
	0x69, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18,
	0x20, 0x52, 0x04, 0x73, 0x70, 0x69, 0x66, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x75, 0x62, 0x5f,
	0x74, 0x65, 0x72, 0x6d, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x14, 0x0a, 0x12, 0x5f,
	0x68, 0x61, 0x72, 0x64, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x76, 0x61, 0x6e, 0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x42, 0x0f,
	0x0a, 0x0d, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x42,
	0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x42, 0x15, 0x0a, 0x13, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x6e, 0x6f, 0x6e, 0x5f,
	0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x61,
	0x74, 0x65, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68,
	0x6c, 0x79, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x15,
	0x0a, 0x13, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x5f, 0x72, 0x61, 0x74, 0x65, 0x42, 0x21, 0x0a, 0x1f, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74,
	0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x33, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x4c,
	0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x6e,
	0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0xb7, 0x04,
	0x0a, 0x20, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c,
	0x65, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x20, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x40, 0x48, 0x00, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x40, 0x48, 0x01, 0x52, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x15, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x75, 0x75, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x20,
	0x48, 0x02, 0x52, 0x13, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x55, 0x75, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x03, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00,
	0x48, 0x04, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x05, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x88, 0x01, 0x01, 0x12, 0x7a, 0x0a, 0x1d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f,
	0x72, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x52, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x1a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x61, 0x74,
	0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65,
	0x73, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x08, 0x0a,
	0x06, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x75, 0x75, 0x69,
	0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0x0a, 0x08, 0x5f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x22, 0xd0, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x50,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x68, 0x0a, 0x16,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x14, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x31, 0x0a, 0x1f, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65,
	0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x21, 0x0a,
	0x1f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53,
	0x61, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0xe2, 0x01, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x85, 0x01,
	0x0a, 0x1b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x61, 0x74, 0x65, 0x41, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x18, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x52, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x88, 0x01, 0x01, 0x42, 0x1e, 0x0a, 0x1c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x21, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2a, 0xbe, 0x01, 0x0a, 0x10, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x21, 0x0a,
	0x1d, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x4c,
	0x41, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x13, 0x0a, 0x0f, 0x47, 0x52, 0x4f, 0x57, 0x54, 0x48, 0x5f, 0x47, 0x52, 0x4f, 0x4f, 0x4d,
	0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x55, 0x4c, 0x54, 0x49, 0x4d, 0x41, 0x54,
	0x45, 0x5f, 0x47, 0x52, 0x4f, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17,
	0x47, 0x52, 0x4f, 0x57, 0x54, 0x48, 0x5f, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x44, 0x41, 0x59, 0x43, 0x41, 0x52, 0x45, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x55, 0x4c, 0x54,
	0x49, 0x4d, 0x41, 0x54, 0x45, 0x5f, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x44,
	0x41, 0x59, 0x43, 0x41, 0x52, 0x45, 0x10, 0x04, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x4e, 0x54, 0x45,
	0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x5f, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x44, 0x41, 0x59, 0x43, 0x41, 0x52, 0x45, 0x10, 0x05, 0x2a, 0xed, 0x01, 0x0a, 0x18, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x52, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x5f, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x41, 0x4c, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x52, 0x41,
	0x54, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x44, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52,
	0x4f, 0x56, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f,
	0x52, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x41, 0x4c, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x03, 0x12,
	0x28, 0x0a, 0x24, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x41,
	0x50, 0x50, 0x52, 0x4f, 0x56, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52,
	0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x04, 0x32, 0x8b, 0x05, 0x0a, 0x14, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x95, 0x01, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x3c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c,
	0x65, 0x73, 0x4c, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73,
	0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9e, 0x01, 0x0a, 0x1a, 0x47,
	0x65, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9b, 0x01, 0x0a, 0x19,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61,
	0x6c, 0x65, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9b, 0x01, 0x0a, 0x19, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65,
	0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x73,
	0x61, 0x6c, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x73,
	0x61, 0x6c, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x90, 0x01, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x65, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x73,
	0x61, 0x6c, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x73, 0x61, 0x6c, 0x65, 0x73, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescOnce sync.Once
	file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescData = file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDesc
)

func file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescData)
	})
	return file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDescData
}

var file_moego_admin_platform_sales_v1_platform_sales_admin_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_admin_platform_sales_v1_platform_sales_admin_proto_goTypes = []interface{}{
	(SubscriptionPlan)(0),                                  // 0: moego.admin.platform_sales.v1.SubscriptionPlan
	(CustomRateApprovalStatus)(0),                          // 1: moego.admin.platform_sales.v1.CustomRateApprovalStatus
	(*PlatformSalesRecord)(nil),                            // 2: moego.admin.platform_sales.v1.PlatformSalesRecord
	(*CreatePlatformSalesLinkParams)(nil),                  // 3: moego.admin.platform_sales.v1.CreatePlatformSalesLinkParams
	(*CreatePlatformSalesLinkResult)(nil),                  // 4: moego.admin.platform_sales.v1.CreatePlatformSalesLinkResult
	(*GetPlatformSalesRecordListParams)(nil),               // 5: moego.admin.platform_sales.v1.GetPlatformSalesRecordListParams
	(*GetPlatformSalesRecordListResult)(nil),               // 6: moego.admin.platform_sales.v1.GetPlatformSalesRecordListResult
	(*DeletePlatformSalesRecordParams)(nil),                // 7: moego.admin.platform_sales.v1.DeletePlatformSalesRecordParams
	(*DeletePlatformSalesRecordResult)(nil),                // 8: moego.admin.platform_sales.v1.DeletePlatformSalesRecordResult
	(*UpdatePlatformSalesRecordParams)(nil),                // 9: moego.admin.platform_sales.v1.UpdatePlatformSalesRecordParams
	(*UpdatePlatformSalesRecordResult)(nil),                // 10: moego.admin.platform_sales.v1.UpdatePlatformSalesRecordResult
	(*CreatePlatformSalesLinkParams_PresetCustomRate)(nil), // 11: moego.admin.platform_sales.v1.CreatePlatformSalesLinkParams.PresetCustomRate
	(*timestamppb.Timestamp)(nil),                          // 12: google.protobuf.Timestamp
	(*v2.PaginationRequest)(nil),                           // 13: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),                          // 14: moego.utils.v2.PaginationResponse
}
var file_moego_admin_platform_sales_v1_platform_sales_admin_proto_depIdxs = []int32{
	12, // 0: moego.admin.platform_sales.v1.PlatformSalesRecord.signed_time:type_name -> google.protobuf.Timestamp
	12, // 1: moego.admin.platform_sales.v1.PlatformSalesRecord.created_at:type_name -> google.protobuf.Timestamp
	12, // 2: moego.admin.platform_sales.v1.PlatformSalesRecord.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 3: moego.admin.platform_sales.v1.PlatformSalesRecord.subscription_plan:type_name -> moego.admin.platform_sales.v1.SubscriptionPlan
	1,  // 4: moego.admin.platform_sales.v1.PlatformSalesRecord.custom_rate_approval_status:type_name -> moego.admin.platform_sales.v1.CustomRateApprovalStatus
	0,  // 5: moego.admin.platform_sales.v1.CreatePlatformSalesLinkParams.subscription_plan:type_name -> moego.admin.platform_sales.v1.SubscriptionPlan
	11, // 6: moego.admin.platform_sales.v1.CreatePlatformSalesLinkParams.preset_custom_rate:type_name -> moego.admin.platform_sales.v1.CreatePlatformSalesLinkParams.PresetCustomRate
	1,  // 7: moego.admin.platform_sales.v1.GetPlatformSalesRecordListParams.custom_rate_approval_statuses:type_name -> moego.admin.platform_sales.v1.CustomRateApprovalStatus
	13, // 8: moego.admin.platform_sales.v1.GetPlatformSalesRecordListParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	2,  // 9: moego.admin.platform_sales.v1.GetPlatformSalesRecordListResult.platform_sales_records:type_name -> moego.admin.platform_sales.v1.PlatformSalesRecord
	14, // 10: moego.admin.platform_sales.v1.GetPlatformSalesRecordListResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	1,  // 11: moego.admin.platform_sales.v1.UpdatePlatformSalesRecordParams.custom_rate_approval_status:type_name -> moego.admin.platform_sales.v1.CustomRateApprovalStatus
	3,  // 12: moego.admin.platform_sales.v1.PlatformSalesService.CreatePlatformSalesLink:input_type -> moego.admin.platform_sales.v1.CreatePlatformSalesLinkParams
	5,  // 13: moego.admin.platform_sales.v1.PlatformSalesService.GetPlatformSalesRecordList:input_type -> moego.admin.platform_sales.v1.GetPlatformSalesRecordListParams
	7,  // 14: moego.admin.platform_sales.v1.PlatformSalesService.DeletePlatformSalesRecord:input_type -> moego.admin.platform_sales.v1.DeletePlatformSalesRecordParams
	9,  // 15: moego.admin.platform_sales.v1.PlatformSalesService.UpdatePlatformSalesRecord:input_type -> moego.admin.platform_sales.v1.UpdatePlatformSalesRecordParams
	4,  // 16: moego.admin.platform_sales.v1.PlatformSalesService.CreatePlatformSalesLink:output_type -> moego.admin.platform_sales.v1.CreatePlatformSalesLinkResult
	6,  // 17: moego.admin.platform_sales.v1.PlatformSalesService.GetPlatformSalesRecordList:output_type -> moego.admin.platform_sales.v1.GetPlatformSalesRecordListResult
	8,  // 18: moego.admin.platform_sales.v1.PlatformSalesService.DeletePlatformSalesRecord:output_type -> moego.admin.platform_sales.v1.DeletePlatformSalesRecordResult
	10, // 19: moego.admin.platform_sales.v1.PlatformSalesService.UpdatePlatformSalesRecord:output_type -> moego.admin.platform_sales.v1.UpdatePlatformSalesRecordResult
	16, // [16:20] is the sub-list for method output_type
	12, // [12:16] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_moego_admin_platform_sales_v1_platform_sales_admin_proto_init() }
func file_moego_admin_platform_sales_v1_platform_sales_admin_proto_init() {
	if File_moego_admin_platform_sales_v1_platform_sales_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlatformSalesRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePlatformSalesLinkParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePlatformSalesLinkResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlatformSalesRecordListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlatformSalesRecordListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePlatformSalesRecordParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePlatformSalesRecordResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePlatformSalesRecordParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePlatformSalesRecordResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePlatformSalesLinkParams_PresetCustomRate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes[7].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_platform_sales_v1_platform_sales_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_platform_sales_v1_platform_sales_admin_proto_depIdxs,
		EnumInfos:         file_moego_admin_platform_sales_v1_platform_sales_admin_proto_enumTypes,
		MessageInfos:      file_moego_admin_platform_sales_v1_platform_sales_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_platform_sales_v1_platform_sales_admin_proto = out.File
	file_moego_admin_platform_sales_v1_platform_sales_admin_proto_rawDesc = nil
	file_moego_admin_platform_sales_v1_platform_sales_admin_proto_goTypes = nil
	file_moego_admin_platform_sales_v1_platform_sales_admin_proto_depIdxs = nil
}
