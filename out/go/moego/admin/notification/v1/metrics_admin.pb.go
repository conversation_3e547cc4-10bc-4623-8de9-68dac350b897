// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/notification/v1/metrics_admin.proto

package notificationapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/reporting/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	interval "google.golang.org/genproto/googleapis/type/interval"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Search Campaign Metrics Params
type SearchCampaignMetricsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// group by
	GroupBy v1.GroupBy `protobuf:"varint,1,opt,name=group_by,json=groupBy,proto3,enum=moego.service.reporting.v1.GroupBy" json:"group_by,omitempty"`
	// order by
	OrderBys []*v2.OrderBy `protobuf:"bytes,2,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// filter
	//
	// Types that are assignable to Filter:
	//
	//	*SearchCampaignMetricsParams_CampaignFilter
	//	*SearchCampaignMetricsParams_StaffCampaignFilter
	Filter isSearchCampaignMetricsParams_Filter `protobuf_oneof:"filter"`
}

func (x *SearchCampaignMetricsParams) Reset() {
	*x = SearchCampaignMetricsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchCampaignMetricsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCampaignMetricsParams) ProtoMessage() {}

func (x *SearchCampaignMetricsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCampaignMetricsParams.ProtoReflect.Descriptor instead.
func (*SearchCampaignMetricsParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_metrics_admin_proto_rawDescGZIP(), []int{0}
}

func (x *SearchCampaignMetricsParams) GetGroupBy() v1.GroupBy {
	if x != nil {
		return x.GroupBy
	}
	return v1.GroupBy(0)
}

func (x *SearchCampaignMetricsParams) GetOrderBys() []*v2.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *SearchCampaignMetricsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (m *SearchCampaignMetricsParams) GetFilter() isSearchCampaignMetricsParams_Filter {
	if m != nil {
		return m.Filter
	}
	return nil
}

func (x *SearchCampaignMetricsParams) GetCampaignFilter() *CampaignFilter {
	if x, ok := x.GetFilter().(*SearchCampaignMetricsParams_CampaignFilter); ok {
		return x.CampaignFilter
	}
	return nil
}

func (x *SearchCampaignMetricsParams) GetStaffCampaignFilter() *StaffCampaignFilter {
	if x, ok := x.GetFilter().(*SearchCampaignMetricsParams_StaffCampaignFilter); ok {
		return x.StaffCampaignFilter
	}
	return nil
}

type isSearchCampaignMetricsParams_Filter interface {
	isSearchCampaignMetricsParams_Filter()
}

type SearchCampaignMetricsParams_CampaignFilter struct {
	// campaign filter
	CampaignFilter *CampaignFilter `protobuf:"bytes,4,opt,name=campaign_filter,json=campaignFilter,proto3,oneof"`
}

type SearchCampaignMetricsParams_StaffCampaignFilter struct {
	// staff campaign filter
	StaffCampaignFilter *StaffCampaignFilter `protobuf:"bytes,5,opt,name=staff_campaign_filter,json=staffCampaignFilter,proto3,oneof"`
}

func (*SearchCampaignMetricsParams_CampaignFilter) isSearchCampaignMetricsParams_Filter() {}

func (*SearchCampaignMetricsParams_StaffCampaignFilter) isSearchCampaignMetricsParams_Filter() {}

// CampaignFilter
type CampaignFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// campaign starter
	Starter *string `protobuf:"bytes,1,opt,name=starter,proto3,oneof" json:"starter,omitempty"`
	// campaign started at
	StartedAt *interval.Interval `protobuf:"bytes,2,opt,name=started_at,json=startedAt,proto3,oneof" json:"started_at,omitempty"`
	// campaign notification title
	NotificationTitle *string `protobuf:"bytes,3,opt,name=notification_title,json=notificationTitle,proto3,oneof" json:"notification_title,omitempty"`
	// campaign notification body
	NotificationBody *string `protobuf:"bytes,4,opt,name=notification_body,json=notificationBody,proto3,oneof" json:"notification_body,omitempty"`
}

func (x *CampaignFilter) Reset() {
	*x = CampaignFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CampaignFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignFilter) ProtoMessage() {}

func (x *CampaignFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignFilter.ProtoReflect.Descriptor instead.
func (*CampaignFilter) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_metrics_admin_proto_rawDescGZIP(), []int{1}
}

func (x *CampaignFilter) GetStarter() string {
	if x != nil && x.Starter != nil {
		return *x.Starter
	}
	return ""
}

func (x *CampaignFilter) GetStartedAt() *interval.Interval {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *CampaignFilter) GetNotificationTitle() string {
	if x != nil && x.NotificationTitle != nil {
		return *x.NotificationTitle
	}
	return ""
}

func (x *CampaignFilter) GetNotificationBody() string {
	if x != nil && x.NotificationBody != nil {
		return *x.NotificationBody
	}
	return ""
}

// StaffCampaignFilter
type StaffCampaignFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// receiver name
	ReceiverName *string `protobuf:"bytes,1,opt,name=receiver_name,json=receiverName,proto3,oneof" json:"receiver_name,omitempty"`
}

func (x *StaffCampaignFilter) Reset() {
	*x = StaffCampaignFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StaffCampaignFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaffCampaignFilter) ProtoMessage() {}

func (x *StaffCampaignFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaffCampaignFilter.ProtoReflect.Descriptor instead.
func (*StaffCampaignFilter) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_metrics_admin_proto_rawDescGZIP(), []int{2}
}

func (x *StaffCampaignFilter) GetReceiverName() string {
	if x != nil && x.ReceiverName != nil {
		return *x.ReceiverName
	}
	return ""
}

// Search Campaign Metrics Result
type SearchCampaignMetricsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// metrics
	Metrics []*v1.CampaignMetrics `protobuf:"bytes,2,rep,name=metrics,proto3" json:"metrics,omitempty"`
}

func (x *SearchCampaignMetricsResult) Reset() {
	*x = SearchCampaignMetricsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchCampaignMetricsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCampaignMetricsResult) ProtoMessage() {}

func (x *SearchCampaignMetricsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCampaignMetricsResult.ProtoReflect.Descriptor instead.
func (*SearchCampaignMetricsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_metrics_admin_proto_rawDescGZIP(), []int{3}
}

func (x *SearchCampaignMetricsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *SearchCampaignMetricsResult) GetMetrics() []*v1.CampaignMetrics {
	if x != nil {
		return x.Metrics
	}
	return nil
}

// SearchCampaignReportsParams
type SearchCampaignReportsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// filter
	Filter *CampaignReportsFilter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *SearchCampaignReportsParams) Reset() {
	*x = SearchCampaignReportsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchCampaignReportsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCampaignReportsParams) ProtoMessage() {}

func (x *SearchCampaignReportsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCampaignReportsParams.ProtoReflect.Descriptor instead.
func (*SearchCampaignReportsParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_metrics_admin_proto_rawDescGZIP(), []int{4}
}

func (x *SearchCampaignReportsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *SearchCampaignReportsParams) GetFilter() *CampaignReportsFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// CampaignReportsFilter
type CampaignReportsFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// campaign id
	CampaignId *int64 `protobuf:"varint,1,opt,name=campaign_id,json=campaignId,proto3,oneof" json:"campaign_id,omitempty"`
	// receiver staff id
	ReceiverStaffId *int64 `protobuf:"varint,2,opt,name=receiver_staff_id,json=receiverStaffId,proto3,oneof" json:"receiver_staff_id,omitempty"`
}

func (x *CampaignReportsFilter) Reset() {
	*x = CampaignReportsFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CampaignReportsFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignReportsFilter) ProtoMessage() {}

func (x *CampaignReportsFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignReportsFilter.ProtoReflect.Descriptor instead.
func (*CampaignReportsFilter) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_metrics_admin_proto_rawDescGZIP(), []int{5}
}

func (x *CampaignReportsFilter) GetCampaignId() int64 {
	if x != nil && x.CampaignId != nil {
		return *x.CampaignId
	}
	return 0
}

func (x *CampaignReportsFilter) GetReceiverStaffId() int64 {
	if x != nil && x.ReceiverStaffId != nil {
		return *x.ReceiverStaffId
	}
	return 0
}

// SearchCampaignReportsResult
type SearchCampaignReportsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// reports
	Reports []*CampaignReport `protobuf:"bytes,2,rep,name=reports,proto3" json:"reports,omitempty"`
}

func (x *SearchCampaignReportsResult) Reset() {
	*x = SearchCampaignReportsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchCampaignReportsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCampaignReportsResult) ProtoMessage() {}

func (x *SearchCampaignReportsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCampaignReportsResult.ProtoReflect.Descriptor instead.
func (*SearchCampaignReportsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_metrics_admin_proto_rawDescGZIP(), []int{6}
}

func (x *SearchCampaignReportsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *SearchCampaignReportsResult) GetReports() []*CampaignReport {
	if x != nil {
		return x.Reports
	}
	return nil
}

// CampaignReport
type CampaignReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business info
	Business *v11.BusinessModel `protobuf:"bytes,1,opt,name=business,proto3" json:"business,omitempty"`
	// report
	Report *v12.CampaignReportModel `protobuf:"bytes,2,opt,name=report,proto3" json:"report,omitempty"`
}

func (x *CampaignReport) Reset() {
	*x = CampaignReport{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CampaignReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignReport) ProtoMessage() {}

func (x *CampaignReport) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignReport.ProtoReflect.Descriptor instead.
func (*CampaignReport) Descriptor() ([]byte, []int) {
	return file_moego_admin_notification_v1_metrics_admin_proto_rawDescGZIP(), []int{7}
}

func (x *CampaignReport) GetBusiness() *v11.BusinessModel {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *CampaignReport) GetReport() *v12.CampaignReportModel {
	if x != nil {
		return x.Report
	}
	return nil
}

var File_moego_admin_notification_v1_metrics_admin_proto protoreflect.FileDescriptor

var file_moego_admin_notification_v1_metrics_admin_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1a,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xaa, 0x03, 0x0a, 0x1b, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x3e, 0x0a, 0x08, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x62, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42,
	0x79, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x12, 0x34, 0x0a, 0x09, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x73,
	0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x56, 0x0a,
	0x0f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x66, 0x0a, 0x15, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x13, 0x73, 0x74, 0x61, 0x66, 0x66, 0x43,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x08, 0x0a,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x98, 0x02, 0x0a, 0x0e, 0x43, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x07, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x88, 0x01, 0x01, 0x12, 0x32, 0x0a, 0x12, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x02, 0x52, 0x11, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x11, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x10, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x64, 0x79, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x65, 0x72, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x42, 0x14, 0x0a, 0x12,
	0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x6f,
	0x64, 0x79, 0x22, 0x51, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x66, 0x66, 0x43, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x0d, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xa8, 0x01, 0x0a, 0x1b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x07, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x22, 0xb6, 0x01, 0x0a, 0x1b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4a, 0x0a,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xa6, 0x01, 0x0a, 0x15, 0x43, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x38, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x42, 0x14, 0x0a, 0x12,
	0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x22, 0xa8, 0x01, 0x0a, 0x1b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x52, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x22, 0x9d, 0x01,
	0x0a, 0x0e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x43, 0x0a, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x46, 0x0a, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x32, 0xac, 0x02,
	0x0a, 0x0e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x8b, 0x01, 0x0a, 0x15, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8b,
	0x01, 0x0a, 0x15, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x8b, 0x01, 0x0a,
	0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x62, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_admin_notification_v1_metrics_admin_proto_rawDescOnce sync.Once
	file_moego_admin_notification_v1_metrics_admin_proto_rawDescData = file_moego_admin_notification_v1_metrics_admin_proto_rawDesc
)

func file_moego_admin_notification_v1_metrics_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_notification_v1_metrics_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_notification_v1_metrics_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_notification_v1_metrics_admin_proto_rawDescData)
	})
	return file_moego_admin_notification_v1_metrics_admin_proto_rawDescData
}

var file_moego_admin_notification_v1_metrics_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_admin_notification_v1_metrics_admin_proto_goTypes = []interface{}{
	(*SearchCampaignMetricsParams)(nil), // 0: moego.admin.notification.v1.SearchCampaignMetricsParams
	(*CampaignFilter)(nil),              // 1: moego.admin.notification.v1.CampaignFilter
	(*StaffCampaignFilter)(nil),         // 2: moego.admin.notification.v1.StaffCampaignFilter
	(*SearchCampaignMetricsResult)(nil), // 3: moego.admin.notification.v1.SearchCampaignMetricsResult
	(*SearchCampaignReportsParams)(nil), // 4: moego.admin.notification.v1.SearchCampaignReportsParams
	(*CampaignReportsFilter)(nil),       // 5: moego.admin.notification.v1.CampaignReportsFilter
	(*SearchCampaignReportsResult)(nil), // 6: moego.admin.notification.v1.SearchCampaignReportsResult
	(*CampaignReport)(nil),              // 7: moego.admin.notification.v1.CampaignReport
	(v1.GroupBy)(0),                     // 8: moego.service.reporting.v1.GroupBy
	(*v2.OrderBy)(nil),                  // 9: moego.utils.v2.OrderBy
	(*v2.PaginationRequest)(nil),        // 10: moego.utils.v2.PaginationRequest
	(*interval.Interval)(nil),           // 11: google.type.Interval
	(*v2.PaginationResponse)(nil),       // 12: moego.utils.v2.PaginationResponse
	(*v1.CampaignMetrics)(nil),          // 13: moego.service.reporting.v1.CampaignMetrics
	(*v11.BusinessModel)(nil),           // 14: moego.models.business.v1.BusinessModel
	(*v12.CampaignReportModel)(nil),     // 15: moego.models.reporting.v1.CampaignReportModel
}
var file_moego_admin_notification_v1_metrics_admin_proto_depIdxs = []int32{
	8,  // 0: moego.admin.notification.v1.SearchCampaignMetricsParams.group_by:type_name -> moego.service.reporting.v1.GroupBy
	9,  // 1: moego.admin.notification.v1.SearchCampaignMetricsParams.order_bys:type_name -> moego.utils.v2.OrderBy
	10, // 2: moego.admin.notification.v1.SearchCampaignMetricsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	1,  // 3: moego.admin.notification.v1.SearchCampaignMetricsParams.campaign_filter:type_name -> moego.admin.notification.v1.CampaignFilter
	2,  // 4: moego.admin.notification.v1.SearchCampaignMetricsParams.staff_campaign_filter:type_name -> moego.admin.notification.v1.StaffCampaignFilter
	11, // 5: moego.admin.notification.v1.CampaignFilter.started_at:type_name -> google.type.Interval
	12, // 6: moego.admin.notification.v1.SearchCampaignMetricsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	13, // 7: moego.admin.notification.v1.SearchCampaignMetricsResult.metrics:type_name -> moego.service.reporting.v1.CampaignMetrics
	10, // 8: moego.admin.notification.v1.SearchCampaignReportsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	5,  // 9: moego.admin.notification.v1.SearchCampaignReportsParams.filter:type_name -> moego.admin.notification.v1.CampaignReportsFilter
	12, // 10: moego.admin.notification.v1.SearchCampaignReportsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	7,  // 11: moego.admin.notification.v1.SearchCampaignReportsResult.reports:type_name -> moego.admin.notification.v1.CampaignReport
	14, // 12: moego.admin.notification.v1.CampaignReport.business:type_name -> moego.models.business.v1.BusinessModel
	15, // 13: moego.admin.notification.v1.CampaignReport.report:type_name -> moego.models.reporting.v1.CampaignReportModel
	0,  // 14: moego.admin.notification.v1.MetricsService.SearchCampaignMetrics:input_type -> moego.admin.notification.v1.SearchCampaignMetricsParams
	4,  // 15: moego.admin.notification.v1.MetricsService.SearchCampaignReports:input_type -> moego.admin.notification.v1.SearchCampaignReportsParams
	3,  // 16: moego.admin.notification.v1.MetricsService.SearchCampaignMetrics:output_type -> moego.admin.notification.v1.SearchCampaignMetricsResult
	6,  // 17: moego.admin.notification.v1.MetricsService.SearchCampaignReports:output_type -> moego.admin.notification.v1.SearchCampaignReportsResult
	16, // [16:18] is the sub-list for method output_type
	14, // [14:16] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_moego_admin_notification_v1_metrics_admin_proto_init() }
func file_moego_admin_notification_v1_metrics_admin_proto_init() {
	if File_moego_admin_notification_v1_metrics_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchCampaignMetricsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CampaignFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StaffCampaignFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchCampaignMetricsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchCampaignReportsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CampaignReportsFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchCampaignReportsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CampaignReport); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*SearchCampaignMetricsParams_CampaignFilter)(nil),
		(*SearchCampaignMetricsParams_StaffCampaignFilter)(nil),
	}
	file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_admin_notification_v1_metrics_admin_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_notification_v1_metrics_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_notification_v1_metrics_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_notification_v1_metrics_admin_proto_depIdxs,
		MessageInfos:      file_moego_admin_notification_v1_metrics_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_notification_v1_metrics_admin_proto = out.File
	file_moego_admin_notification_v1_metrics_admin_proto_rawDesc = nil
	file_moego_admin_notification_v1_metrics_admin_proto_goTypes = nil
	file_moego_admin_notification_v1_metrics_admin_proto_depIdxs = nil
}
