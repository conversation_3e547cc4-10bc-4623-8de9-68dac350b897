package com.moego.lib.uid;

import me.ahoo.cosid.IdGenerator;
import me.ahoo.cosid.provider.IdGeneratorProvider;

public class UidGenerator {

    private final IdGeneratorProvider provider;

    public UidGenerator(IdGeneratorProvider provider) {
        this.provider = provider;
    }

    public IdGenerator getShareGenerator() {
        return provider.getShare();
    }

    public IdGenerator getGenerator(String name) {
        return provider.getRequired(name);
    }

    /**
     * generate a new id globally
     * @return id
     */
    public long nextId() {
        return provider.getShare().generate();
    }

    /**
     * generate a new id using a specific provider
     * @param name name of provider
     * @return id
     */
    public long nextId(String name) {
        return provider.getRequired(name).generate();
    }

    /**
     * generate a new friendly id globally
     * @return id
     */
    public String nextFriendlyId() {
        return provider.getShare().generateAsString();
    }

    /**
     * generate a new friendly id using a specific provider
     * @param name name of provider
     * @return id
     */
    public String nextFriendlyId(String name) {
        return provider.getRequired(name).generateAsString();
    }

    /**
     * parse a numeric id to a friendly id globally
     * @param id id
     * @return friendly id
     */
    public String toFriendlyId(long id) {
        return provider.getShare().idConverter().asString(id);
    }

    /**
     * parse a numeric id to a friendly id using a specific provider
     * @param id id
     * @param name name of provider
     * @return friendly id
     */
    public String toFriendlyId(long id, String name) {
        return provider.getRequired(name).idConverter().asString(id);
    }

    /**
     * parse a friendly id to a numeric id globally
     * @param friendlyId friendly id
     * @return id
     */
    public long toId(String friendlyId) {
        return provider.getShare().idConverter().asLong(friendlyId);
    }

    /**
     * parse a friendly id to a numeric id using a specific provider
     * @param friendlyId friendly id
     * @param name name of provider
     * @return id
     */
    public long toId(String friendlyId, String name) {
        return provider.getRequired(name).idConverter().asLong(friendlyId);
    }
}
