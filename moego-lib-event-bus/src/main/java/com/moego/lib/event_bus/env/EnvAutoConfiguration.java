package com.moego.lib.event_bus.env;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
public class EnvAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public EnvHelper envHelper() {
        return new EnvHelper();
    }
}
