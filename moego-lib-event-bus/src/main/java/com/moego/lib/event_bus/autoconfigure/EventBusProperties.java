package com.moego.lib.event_bus.autoconfigure;

import static com.moego.lib.event_bus.autoconfigure.EventBusProperties.PREFIX;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(PREFIX)
public class EventBusProperties {
    public static final String PREFIX = "moego.event-bus";

    private List<Broker> brokers = new ArrayList<>();
    private Consumer consumer = new Consumer();
    private Producer producer = new Producer();

    @Data
    public static class Broker {
        private Security security = new Security();
        private String name;
        private List<String> addresses;

        @Data
        public static class Security {
            private boolean enabled = false;
            private Map<String, String> properties = new HashMap<>();
        }
    }

    @Data
    public static class Consumer {
        private boolean enabled = false;
        private boolean logReceive = false;
        private String groupId = "";
    }

    @Data
    public static class Producer {
        private boolean enabled = false;
        private boolean logSuccess = false;
        private boolean logFailure = true;
    }
}
