package com.moego.lib.event_bus.autoconfigure;

import java.util.HashMap;
import java.util.Map;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.util.CollectionUtils;

public class EventBusBeanDefinitionRegistryPostProcessor
        implements BeanDefinitionRegistryPostProcessor, EnvironmentAware {

    private EventBusProperties eventBusProperties;

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        // no-op
    }

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        var brokers = eventBusProperties.getBrokers();
        if (CollectionUtils.isEmpty(brokers)) {
            throw new IllegalArgumentException("No brokers found in configuration");
        }

        // 将默认的 kafkaTemplate 移除
        if (registry.containsBeanDefinition("kafkaTemplate")) {
            registry.removeBeanDefinition("kafkaTemplate");
        }

        // 根据定义的 broker 地址列表手动注册 kafkaTemplate
        for (var broker : brokers) {
            var kafkaTemplate = BeanDefinitionBuilder.genericBeanDefinition(
                            KafkaTemplate.class, () -> new KafkaTemplate<>(producerFactory(broker)))
                    .getBeanDefinition();
            registry.registerBeanDefinition(broker.getName(), kafkaTemplate);
        }
    }

    private ProducerFactory<String, String> producerFactory(EventBusProperties.Broker broker) {
        Map<String, Object> props = new HashMap<>();
        // broker address
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, broker.getAddresses());
        // 投递失败重试次数
        props.put(ProducerConfig.RETRIES_CONFIG, 3);
        // 同步到副本, 默认为1
        // acks=0 把消息发送到kafka就认为发送成功
        // acks=1 把消息发送到kafka leader分区，并且写入磁盘就认为发送成功
        // acks=all 把消息发送到kafka leader分区，并且leader分区的副本follower对消息进行了同步就任务发送成功
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        // key 的序列化方式
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        // value 的序列化方式
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

        // security
        if (broker.getSecurity().isEnabled()) {
            props.putAll(broker.getSecurity().getProperties());
        }

        return new DefaultKafkaProducerFactory<>(props);
    }

    @Override
    public void setEnvironment(Environment environment) {
        eventBusProperties = Binder.get(environment)
                .bind(EventBusProperties.PREFIX, EventBusProperties.class)
                .orElseGet(EventBusProperties::new);
    }
}
