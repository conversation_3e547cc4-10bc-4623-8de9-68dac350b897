# moego-lib-event-bus

该模块提供了一个基于 kafka 的 event bus 实现

## 使用指南

### 上线须知
生产环境连接 kafka 集群时, 需要添加凭据才能连上 broker, 请给每个 broker 集群添加如下配置:
```yaml
moego:
  event-bus:
    brokers:
      - name: default
        addresses:
          - ...
        # 添加 security 配置 (测试环境不需要加), enabled 必须为 true. 剩下的找 Frank 进行配置
        security:
          enabled: true
          properties:
            security.protocol: SASL_SSL
            sasl.mechanism: AWS_MSK_IAM
            sasl.jaas.config: software.amazon.msk.auth.iam.IAMLoginModule required awsProfileName="moego_msk";
            sasl.client.callback.handler.class: software.amazon.msk.auth.iam.IAMClientCallbackHandler
```

### 依赖添加
使用前, 请在你的代码仓库的 deps.json 文件里添加依赖:
```json
{
  "deps": {
    ... // 其他依赖
    "moego-java-lib": {
      ... // 其他 lib 模块
      "com.moego.lib:moego-lib-event-bus": "moego-lib-event-bus"
    }
  }
}
```

### Producer

#### 快速接入
如果需要生产并投递 event, 首先在你的 Spring Boot 应用的配置文件里添加配置:
```yaml
moego:
  event-bus:
    brokers:
      # 可以添加多个 broker 集群的配置, 但是至少要有一个
      # 每个 broker 集群需要指定一个唯一的 name, 以及 broker 的地址列表
      - name: default
        # 一个 broker 集群可以有多个地址, 用逗号分隔或者使用数组, 推荐使用数组, 方便更好的阅读
        addresses:
          - kafka-controller-0.kafka-controller-headless.kafka.svc.cluster.local:9092
          - kafka-controller-1.kafka-controller-headless.kafka.svc.cluster.local:9092
          - kafka-controller-2.kafka-controller-headless.kafka.svc.cluster.local:9092
    producer:
      # 如果 enabled 为 false (默认值), 则不会初始化 producer, 此时如果代码里依赖了 producer, 则会抛出异常
      enabled: true
      # 发送消息成功时是否打印日志, 默认为 false
      log-success: false
      # 发送消息失败时是否打印日志, 默认为 true
      log-failure: true
```

然后, 在你的业务代码中定义一个这样的类:
```java
// 必须加上 @Component 注解, 以便被 Spring 扫描到
@Component
// 使用 @RequiredArgsConstructor 注解, 或者自定义构造函数, 以便注入 Producer
@RequiredArgsConstructor
public class AfterCreateCustomer {

    // 通过构造函数注入 Producer
    private final Producer producer;

    // 声明需要投递的 topic 名称, 这里不需要加上环境后缀, 底层已经封装好 topic 的名称解析逻辑
    // 通常建议使用 "moego.{域}.{模型}" 的格式来命名 topic
    private static final String TOPIC_NAME = "moego.business_customer.customer";

    // 定义一个发送事件的方法, 在需要发送事件的时候调用该方法即可
    // 入参可以是服务内自定义的 POJO 等, 但是投递之前必须转成 protobuf 的 message 类型
    // 同一个 topic 必须使用同一个 protobuf 的 message 类型进行投递, 否则消费者反序列化时会出现异常
    public void sendEvent(CustomerRecord record) {
        // 构造 event
        var event = EventRecord.builder()
            // 可以指定 event id, 如果不指定, builder 默认会使用 uuid v7 生成一个 id
            // .id("xxx")
            // 可以指定 event time, 如果不指定, builder 默认会使用当前时间
            // .time(Instant.now())
            // 可以指定 event key, 如果不指定, builder 默认会使用 id 作为 key
            // .key(String.valueOf(record.getId())
            // event detail 必须传入, 并且必须是 protobuf 的 message 类型
            .detail(toModel(record))
            .build();

        // 投递 event
        producer.send(TOPIC_NAME, event);
    }

    // 将自定义的 POJO 转成 protobuf 的 message 类型
    // 可以用 mapstruct 等转换工具, 或者手动转换, 这里只是举个例子
    private CustomerModel toModel(CustomerRecord record) {
        return CustomerModel.newBuilder()
                .setId(record.getId())
                .setCompanyId(record.getCompanyId())
                .setFirstName(record.getFirstName())
                .setLastName(record.getLastName())
                .build();
    }
}
```

#### 多 broker 集群投递
假如需要使用多个 broker 集群, 可以在配置文件里添加多个 broker 集群的配置:
```yaml
moego:
  event-bus:
    brokers:
      # 可以添加多个 broker 集群的配置, 每个 broker 集群的 name 必须唯一
      - name: default
        addresses:
          - kafka-controller-0.kafka-controller-headless.kafka.svc.cluster.local:9092
          - kafka-controller-1.kafka-controller-headless.kafka.svc.cluster.local:9092
          - kafka-controller-2.kafka-controller-headless.kafka.svc.cluster.local:9092
      - name: secondary
        addresses:
          - secondary-kafka-controller-0.kafka-controller-headless.kafka.svc.cluster.local:9092
          - secondary-kafka-controller-1.kafka-controller-headless.kafka.svc.cluster.local:9092
          - secondary-kafka-controller-2.kafka-controller-headless.kafka.svc.cluster.local:9092
    producer:
      enabled: true
```

然后可以在业务代码中指定使用哪个 broker 集群. 如果不指定, 则使用默认使用配置文件里的第一个 broker 集群. 例如:
```java
@Component
@RequiredArgsConstructor
public class AfterCreateCustomer {

    private final Producer producer;
    // 指定使用哪个 broker
    private static final String BROKER_NAME = "secondary";
    private static final String TOPIC_NAME = "moego.business_customer.customer";

    public void sendEvent(CustomerRecord record) {
        var event = EventRecord.builder()
            .detail(toModel(record))
            .build();

        // 使用指定的 broker 向指定的 topic 投递 event
        producer.send(BROKER_NAME, TOPIC_NAME, event);
        // 如果不指定 broker, 则使用默认的 broker (默认是配置文件里的第一个 broker)
        // producer.send(TOPIC_NAME, event);
    }

    private CustomerModel toModel(CustomerRecord record) {
        return CustomerModel.newBuilder()
            .setId(record.getId())
            .setCompanyId(record.getCompanyId())
            .setFirstName(record.getFirstName())
            .setLastName(record.getLastName())
            .build();
    }
}
```

### Consumer

#### 快速接入
如果需要消费 event, 首先在你的 Spring Boot 应用的配置文件里添加配置:
```yaml
moego:
  event-bus:
    brokers:
      - name: default
        addresses:
          - kafka-controller-0.kafka-controller-headless.kafka.svc.cluster.local:9092
          - kafka-controller-1.kafka-controller-headless.kafka.svc.cluster.local:9092
          - kafka-controller-2.kafka-controller-headless.kafka.svc.cluster.local:9092
    consumer:
      # 如果 enabled 为 false (默认值), 则不会启动 consumer 监听消息, 但是 consumer 可以被初始化.
      enabled: true
      # 接收到消息时是否打印日志, 默认为 false
      log-receive: false
      # 消费者组，通常可以不配，默认为 spring.application.name
      # 如果需要某个 feature 分支独立消费消息，可以通过手动设置 groupId 来隔离
      groupId: xxxx
```

然后, 在你的业务代码中通过继承 AbstractConsumer 类来实现消费逻辑. 每个 topic 都需要一个对应的 Consumer 类, 例如:
```java
// 必须加上 @Component 注解, 以便被 Spring 扫描到
@Component
// 继承 AbstractConsumer, 并指定泛型为生产者生产消息时使用的 message 类型
public class CustomerEventConsumer extends AbstractConsumer<CustomerModel> {

    // 声明需要消费的 topic 名称, 这里不需要加上环境后缀, 底层已经封装好 topic 的名称解析逻辑
    private static final String TOPIC_NAME = "moego.business_customer.customer";

    // 实现 topicName 方法, 返回指定的 topic 名称
    @Override
    public String topicName() {
        return TOPIC_NAME;
    }

    // 实现 consume 方法, 处理接收到的事件
    // 如果消费失败, 应该抛出异常, 以便消息重试
    // 如果没有异常抛出, 则认为消费成功
    // 消费逻辑一定要保证幂等性, 因为消息可能会重复投递或者重复消费
    @Override
    public void consume(EventRecord<CustomerModel> event) {
        // TODO: 处理接收到的事件
        // ...
    }
}
```

#### 多 broker 集群消费
如果你需要消费不同的 topic, 并且这些 topic 在不同的 broker 集群里, 可以在配置文件里添加多个 broker 集群的配置:
```yaml
moego:
  event-bus:
    brokers:
      - name: default
        addresses:
          - kafka-controller-0.kafka-controller-headless.kafka.svc.cluster.local:9092
          - kafka-controller-1.kafka-controller-headless.kafka.svc.cluster.local:9092
          - kafka-controller-2.kafka-controller-headless.kafka.svc.cluster.local:9092
      - name: secondary
        addresses:
          - secondary-kafka-controller-0.kafka-controller-headless.kafka.svc.cluster.local:9092
          - secondary-kafka-controller-1.kafka-controller-headless.kafka.svc.cluster.local:9092
          - secondary-kafka-controller-2.kafka-controller-headless.kafka.svc.cluster.local:9092
    consumer:
      enabled: true
```

然后在业务代码中, 通过**重写** `brokerName` 方法,指定使用哪个 broker. 如果不指定 (不重写 `brokerName` 方法),
则使用默认使用配置文件里的第一个 broker. 例如:
```java
@Component
public class InvoiceEventConsumer extends AbstractConsumer<InvoiceModel> {

    private static final String BROKER_NAME = "secondary";
    private static final String TOPIC_NAME = "moego.payment.invoice";

    // 重写 brokerName 方法, 返回指定的 broker 集群名称
    // 如果返回 null 或者不重写该方法, 则使用默认的 broker 集群 (默认是配置文件里的第一个 broker)
    @Override
    protected String brokerName() {
        return BROKER_NAME;
    }

    @Override
    public String topicName() {
        return TOPIC_NAME;
    }

    @Override
    public void consume(EventRecord<InvoiceModel> event) {
        // TODO: 处理接收到的事件
        // ...
    }
}
```

#### 自定义 consumer group
如果需要在同一个应用中使用不同的 consumer group 消费同一个 topic (通常情况下不建议这么做),
可以通过**重写** `groupId` 方法来指定 consumer group. 不同的 consumer group 会各自维护 offset 独立消费, 互不影响.
注意, 重写 groupId 方法优先级低于 moego.event-bus.consumer.groupId 配置. 不建议二者同时使用.
例如:
```java
@Component
public class CustomerEventConsumerV2 extends AbstractConsumer<CustomerModel> {

    private static final String TOPIC_NAME = "moego.business_customer.customer";
    private static final String GROUP_ID = "customer-event-consumer-v2";

    @Override
    public String topicName() {
        return TOPIC_NAME;
    }

    // 重写 groupId 方法, 返回指定的 consumer group 名称
    @Override
    protected String groupId() {
        return GROUP_ID;
    }

    @Override
    public void consume(EventRecord<CustomerModel> event) {
        // TODO: 处理接收到的事件
        // ...
    }
}
```
