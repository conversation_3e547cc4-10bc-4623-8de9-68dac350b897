#!/usr/bin/env bash
# @since 2023-06-30 13:45:36
# <AUTHOR> <EMAIL>
#
# Note: no jq here

set -xeuo pipefail

TAG=latest
OLD_VERSION=$(node -p "require('./package.json').version")
MAJOR="$(echo "$OLD_VERSION" | cut -d. -f1)"

case "$BRANCH_NAME" in
production | main)
  export VERSION="$MAJOR.$BUILD_ID.$RUN_ATTEMPT"
  ;;
feature-* | bugfix-*)
  TAG=$(echo "$BRANCH_NAME" | sed -E 's/^(feature|bugfix)-(.*)$/\2/')
  CURRENT_DATE=$(date +%Y%m%d%H%M)
  FORMAT_BUILD_ID=$(printf '%03d' $BUILD_ID)
  export VERSION="$OLD_VERSION-$TAG.$CURRENT_DATE$FORMAT_BUILD_ID"
  ;;
*)
  echo "Skip publish npm on branch $BRANCH_NAME"
  exit 0
  ;;
esac

yarn npm-cli-login \
  -u "$NPM_PUBLISHER_USR" \
  -p "$NPM_PUBLISHER_PSW" \
  -e <EMAIL> \
  -r "https://nexus.devops.moego.pet/repository/npm-local"

yarn sync-applications

for dir in web; do
  echo "Compiling $dir..."
  cp -r "templates/$dir/". "out/$dir"
  sed -i "s/VERSION/$VERSION/g" "out/$dir/package.json"
  yarn tsc -p "out/$dir/tsconfig.json"
  echo "Publishing $dir..."
  pushd "out/$dir"
  npm publish --tag "$TAG"
  popd
done

for dir in node; do
  echo "Compiling $dir..."
  cp -r "templates/$dir/". "out/$dir"
  sed -i "s/VERSION/$VERSION/g" "out/$dir/package.json"
  yarn tsc -p "out/$dir/tsconfig.json"
  # @connectrpc/connect-node fix node版本不兼容，tsc后，publish前remove
  sed -i '/@ts-expect-error/d' "out/$dir/serviceClient.ts"
  sed -i '/@ts-expect-error/d' "out/$dir/serviceClient.js"
  echo "Publishing $dir..."
  pushd "out/$dir"
  npm publish --tag "$TAG"
  popd
done
