syntax = "proto3";

package moego.api.customer.v1;

import "google/protobuf/empty.proto";
import "moego/models/customer/v1/customer_pet_metadata_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/customer/v1;customerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.customer.v1";

// get pet metadata list response
message GetPetMetadataListResponse {
  // pet metadata list
  repeated moego.models.customer.v1.PetMetadataModel pet_metadata_list = 1;
}

// pet metadata service
service PetMetadataService {
  // get pet metadata list
  rpc GetPetMetadataList(google.protobuf.Empty) returns (GetPetMetadataListResponse);
}
