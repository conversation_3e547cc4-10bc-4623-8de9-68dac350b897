syntax = "proto3";

package moego.api.customer.v1;

import "google/protobuf/empty.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/customer/v1/customer_pet_models.proto";
import "moego/models/customer/v1/customer_pet_vaccine_models.proto";
import "moego/utils/v1/id_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/customer/v1;customerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.customer.v1";

// pet vaccine request
message PetVaccineRequest {
  // vaccine metadata id
  int32 vaccine_metadata_id = 1 [(validate.rules).int32 = {gt: 0}];
  // expiration date
  string expiration_date = 2 [(validate.rules).string = {
    ignore_empty: true
    pattern: "^(\\d{4}-\\d{2}-\\d{2})?$"
  }];
  // document url list
  repeated string document_url_list = 3 [(validate.rules).repeated = {
    ignore_empty: true
    min_items: 0
    items: {
      string: {
        uri: true
        max_len: 1000
      }
    }
  }];
}

// pet request
message PetRequest {
  // pet name
  string pet_name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // pet type
  moego.models.customer.v1.PetType pet_type = 2 [(validate.rules).enum = {defined_only: true}];
  // avatar path
  string avatar_path = 3 [(validate.rules).string = {
    ignore_empty: true
    max_len: 255
    uri: true
  }];
  // breed id
  int32 breed_id = 4 [(validate.rules).int32 = {gt: 0}];
  // breed mix
  bool breed_mix = 5;
  // birthday
  string birthday = 6 [(validate.rules).string = {
    ignore_empty: true
    pattern: "^(\\d{4}-\\d{2}-\\d{2})?$"
  }];
  // gender
  moego.models.customer.v1.PetGender gender = 7 [(validate.rules).enum = {defined_only: true}];
  // hair length metadata id
  int32 hair_length_metadata_id = 8 [(validate.rules).int32 = {
    ignore_empty: true
    gte: 0
  }];
  // behavior metadata id
  int32 behavior_metadata_id = 9 [(validate.rules).int32 = {
    ignore_empty: true
    gte: 0
  }];
  // weight
  string weight = 10 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 50
  }];
  // weight unit metadata id
  int32 weight_unit_metadata_id = 12 [(validate.rules).int32 = {
    ignore_empty: true
    gte: 0
  }];
  // fixed metadata id
  int32 fixed_metadata_id = 13 [(validate.rules).int32 = {
    ignore_empty: true
    gte: 0
  }];
}

// add pet request
message AddPetRequest {
  // pet request
  PetRequest pet = 1 [(validate.rules).message = {required: true}];
  // vaccine list
  repeated PetVaccineRequest vaccine_list = 2;
}

// update pet request
message UpdatePetRequest {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet request
  PetRequest pet = 2 [(validate.rules).message = {required: true}];
  // vaccine list
  repeated PetVaccineRequest vaccine_list = 3;
}

// get pet list response
message GetPetListResponse {
  // pet list
  repeated GetPetResponse pet_list = 1;
}

// get pet response
message GetPetResponse {
  // pet model
  moego.models.customer.v1.CustomerPetModel pet_model = 1;
  // pet vaccine list
  repeated moego.models.customer.v1.PetVaccineSimpleView vaccine_list = 2;
}

// pet service
service PetService {
  // get pet list
  rpc GetPetList(google.protobuf.Empty) returns (GetPetListResponse);
  // add pet
  rpc AddPet(AddPetRequest) returns (moego.utils.v1.Id);
  // update pet
  rpc UpdatePet(UpdatePetRequest) returns (google.protobuf.Empty);
  // delete pet
  rpc DeletePet(moego.utils.v1.Id) returns (google.protobuf.Empty);
  // get pet
  rpc GetPet(moego.utils.v1.Id) returns (GetPetResponse);
}
