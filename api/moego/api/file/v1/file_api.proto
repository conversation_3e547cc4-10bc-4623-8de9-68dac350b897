syntax = "proto3";

package moego.api.file.v1;

import "google/api/httpbody.proto";
import "moego/models/file/v1/file_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/file/v1;fileapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.file.v1";

// UploadFileRequest
message UploadFileRequest {
  // target file name
  string target_name = 2;
  // original file name
  optional string original_name = 1;
  // file type, if not specified, get from file extension of original_name
  optional string file_type = 3;
  // file size with bytes
  uint64 file_size = 4;
  // raw file content
  google.api.HttpBody file_content = 5;
}

// FileService
service FileService {
  // upload file
  rpc UploadFile(UploadFileRequest) returns (moego.models.file.v1.FileModel);
}
