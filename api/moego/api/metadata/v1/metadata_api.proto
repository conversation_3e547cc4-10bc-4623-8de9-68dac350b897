// @since 2023-04-07 09:48:36
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.metadata.v1;

import "google/protobuf/empty.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/metadata/v1;metadataapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.metadata.v1";

// get metadata request
message DescribeMetadataParams {
  // the specifier to filter keys
  oneof specifier {
    option (validate.required) = true;
    // filter by group
    string group = 1 [(validate.rules).string = {
      min_len: 1
      max_len: 50
    }];
    // filter by key
    string key = 2 [(validate.rules).string = {
      min_len: 2
      max_len: 50
    }];
  }
}

// get metadata response
message DescribeMetadataResult {
  // the metadata map, key is the metadata key
  map<string, string> values = 1;
}

// update metadata request
message UpdateMetadataParams {
  // the key
  string key = 1 [(validate.rules).string = {
    min_len: 2
    max_len: 50
  }];

  // the value, omit (null is different) this field will reset to default value
  optional string value = 2 [(validate.rules).string = {max_len: 1048576}];
}

// the metadata service
// Deprecated: should be MetadataService
service MetadataApiService {
  option deprecated = true;
  // get all metadata related to current user
  // filter by group or key
  rpc DescribeMetadata(DescribeMetadataParams) returns (DescribeMetadataResult);
  // update a metadata key
  // Note: if you make a ultra vires operation, will will return a 401 rather than 403 error code
  // TODO(junbao): allow update staff's value if is owner according to the permission
  rpc UpdateMetadata(UpdateMetadataParams) returns (google.protobuf.Empty);
}
