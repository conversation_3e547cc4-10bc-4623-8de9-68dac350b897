syntax = "proto3";

package moego.api.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_note_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// pin pet note params
message PinPetNoteParams {
  // pet note id
  int64 id = 1 [(validate.rules).int64.gt = 0];

  // pin status
  bool is_pinned = 3;
}

// pin pet note result
message PinPetNoteResult {
  // pet note
  moego.models.business_customer.v1.BusinessPetNoteModel note = 1;
}

// API for pet note
service BusinessPetNoteService {
  // Pin or unpin a pet note
  rpc PinPetNote(PinPetNoteParams) returns (PinPetNoteResult);
}
