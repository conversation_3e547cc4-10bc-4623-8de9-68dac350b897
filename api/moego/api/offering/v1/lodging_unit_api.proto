syntax = "proto3";

package moego.api.offering.v1;

import "moego/models/offering/v1/lodging_unit_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1;offeringapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.offering.v1";

/**
 * Request body for create LodgingUnit service
 */
message CreateLodgingUnitParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // name of the lodging unit
  repeated string name = 2 [(validate.rules).repeated = {min_items: 1}];
  // lodging type of this lodging unit
  int64 lodging_type_id = 3 [(validate.rules).int64.gt = 0];
  // camera id
  optional int64 camera_id = 4 [(validate.rules).int64 = {gte: 0}];
}

/**
 * Response body for create LodgingUnit
 */
message CreateLodgingUnitResult {
  // lodging unit
  repeated models.offering.v1.LodgingUnitView lodging_unit_list = 1;
}

/**
 * Request body for update LodgingUnit
 */
message UpdateLodgingUnitParams {
  // id of the lodging unit
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // name of the lodging unit
  optional string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // camera id
  optional int64 camera_id = 3 [(validate.rules).int64 = {gte: 0}];
}

/**
 * Response body for update LodgingUnit
 */
message UpdateLodgingUnitResult {
  // lodging unit
  models.offering.v1.LodgingUnitView lodging_unit = 1;
}

/**
 * Request body for delete LodgingUnit
 */
message DeleteLodgingUnitParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // id of the lodging unit
  int64 id = 2 [(validate.rules).int64 = {gt: 0}];
}

/**
 * Response body for delete LodgingUnit
 */
message DeleteLodgingUnitResult {}

/**
 * Request body for get LodgingUnit list
 */
message GetLodgingUnitListParams {
  // business id
  optional int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // lodging unit id list
  repeated int64 lodging_type_id_list = 2 [(validate.rules).repeated = {min_items: 0}];
}

/**
 * get LodgingUnit list response
 */
message GetLodgingUnitListResult {
  // lodging unit list
  repeated models.offering.v1.LodgingUnitView lodging_unit_list = 1;
}

// The params for sort lodging unit by ids
message SortLodgingUnitByIdsParams {
  // ids of lodging units to sort
  repeated int64 ids = 1 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// The result for sort lodging unit by ids
message SortLodgingUnitByIdsResult {}

// lodging Unit service
service LodgingUnitService {
  // create lodging Unit
  rpc CreateLodgingUnit(CreateLodgingUnitParams) returns (CreateLodgingUnitResult);
  // update lodging Unit
  rpc UpdateLodgingUnit(UpdateLodgingUnitParams) returns (UpdateLodgingUnitResult);
  // delete lodging Unit
  rpc DeleteLodgingUnit(DeleteLodgingUnitParams) returns (DeleteLodgingUnitResult);
  // get lodging Unit list
  rpc GetLodgingUnitList(GetLodgingUnitListParams) returns (GetLodgingUnitListResult);
  // sort lodging unit list
  rpc SortLodgingUnitByIds(SortLodgingUnitByIdsParams) returns (SortLodgingUnitByIdsResult);
}
