syntax = "proto3";

package moego.api.appointment.v1;

import "google/type/date.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// customer package view
message CustomerPackageView {
  // customer id
  int64 customer_id = 1;
  // package id
  int64 package_id = 2;
  // package name
  string package_name = 3;
  // package detail
  repeated PackageDetail package_details = 4;
  // valid start date
  google.type.Date start_date = 5;
  // expiration date
  optional google.type.Date expiration_date = 6;

  // package detail
  message PackageDetail {
    // service id
    // Deprecated by <PERSON>, use Service instead
    int64 service_id = 1 [deprecated = true];
    // service name
    // Deprecated by <PERSON>, use Service instead
    string service_name = 2 [deprecated = true];
    // package remaining quantity
    int32 remaining_quantity = 3;
    // package total quantity
    int32 total_quantity = 4;
    // services in package
    repeated Service services = 5;

    // service
    message Service {
      // service id
      int64 id = 1;
      // service name
      string name = 2;
      // unit price
      double unit_price = 3;
    }
  }
}
