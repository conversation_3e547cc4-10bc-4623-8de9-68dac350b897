// @since 2025-05-01 15:22:53
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.appointment.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// create service_charge_detail params
message AddServiceChargeDetailParams {
  // Appointment ID
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Service charge ID, foreign key to service_charge table
  repeated int64 service_charge_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// create service_charge_detail result
message AddServiceChargeDetailResult {}

// delete service_charge_detail params
message DeleteServiceChargeDetailParams {
  // the service_charge_detail id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete service_charge_detail result
message DeleteServiceChargeDetailResult {}

// the service_charge_detail service
service ServiceChargeDetailService {
  // add service_charge_detail
  rpc AddServiceChargeDetail(AddServiceChargeDetailParams) returns (AddServiceChargeDetailResult);

  // delete service_charge_detail
  rpc DeleteServiceChargeDetail(DeleteServiceChargeDetailParams) returns (DeleteServiceChargeDetailResult);
}
