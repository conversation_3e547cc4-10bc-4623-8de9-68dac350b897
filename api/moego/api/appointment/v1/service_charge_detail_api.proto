// @since 2025-05-01 15:22:53
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.appointment.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// create service_charge_detail params
message AddServiceChargeDetailParams {
  // Appointment ID
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Service charge ID, foreign key to service_charge table
  repeated int64 service_charge_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// create service_charge_detail result
message AddServiceChargeDetailResult {}

// delete service_charge_detail params
message DeleteServiceChargeDetailParams {
  // the service_charge_detail id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete service_charge_detail result
message DeleteServiceChargeDetailResult {}

// check late pick-up rules params
message CheckLatePickUpRuleParams {
  // Appointment ID
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// check late pick-up rules result
message CheckLatePickUpRuleResult {
  // hit rule
  bool hit_rule = 1;
}

// add late pick-up fee params
message AddLatePickUpServiceChargeDetailParams {
  // Appointment ID
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// add late pick-up fee result
message AddLatePickUpServiceChargeDetailResult {}

// the service_charge_detail service
service ServiceChargeDetailService {
  // add service_charge_detail
  rpc AddServiceChargeDetail(AddServiceChargeDetailParams) returns (AddServiceChargeDetailResult);

  // delete service_charge_detail
  rpc DeleteServiceChargeDetail(DeleteServiceChargeDetailParams) returns (DeleteServiceChargeDetailResult);

  // Check late pick-up rule
  // Use the current business time as the check out time to determine if there is a service charge hit.
  rpc CheckLatePickUpRule(CheckLatePickUpRuleParams) returns (CheckLatePickUpRuleResult);

  // Add late pick-up fee
  // Add service charge fee to the appointment if there is a late pick up rule hit.
  rpc AddLatePickUpServiceChargeDetail(AddLatePickUpServiceChargeDetailParams) returns (AddLatePickUpServiceChargeDetailResult);
}
