// @since 2024-01-15 15:02:36
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.appointment.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/api/appointment/v1/appointment_view.proto";
import "moego/models/appointment/v1/appointment_defs.proto";
import "moego/models/appointment/v1/appointment_enums.proto";
import "moego/models/appointment/v1/appointment_models.proto";
import "moego/models/appointment/v1/appointment_note_defs.proto";
import "moego/models/appointment/v1/appointment_note_models.proto";
import "moego/models/appointment/v1/auto_assign_models.proto";
import "moego/models/appointment/v1/boarding_split_lodging_defs.proto";
import "moego/models/appointment/v1/pet_detail_defs.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/models/appointment/v1/pricing_rule_apply_log_models.proto";
import "moego/models/appointment/v1/service_operation_models.proto";
import "moego/models/appointment/v1/wait_list_models.proto";
import "moego/models/appointment/v2/pricing_rule_apply_models.proto";
import "moego/models/business_customer/v1/business_customer_address_models.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/business_customer/v1/business_customer_pet_models.proto";
import "moego/models/business_customer/v1/business_customer_tag_models.proto";
import "moego/models/business_customer/v1/business_pet_code_models.proto";
import "moego/models/business_customer/v1/business_pet_evaluation_models.proto";
import "moego/models/membership/v1/subscription_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/order/v1/invoice_models.proto";
import "moego/models/order/v1/order_detail_defs.proto";
import "moego/models/order/v1/order_models.proto";
import "moego/models/organization/v1/staff_models.proto";
import "moego/models/payment/v1/pre_auth_defs.proto";
import "moego/models/payment/v1/pre_auth_models.proto";
import "moego/models/payment/v1/pre_pay_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// Create a appointment params
message CreateAppointmentParams {
  // Selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Appointment params
  models.appointment.v1.AppointmentCreateDef appointment = 2 [(validate.rules).message = {required: true}];

  // Selected pet and services
  repeated models.appointment.v1.PetDetailDef pet_details = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Pre-auth
  // Simply request a card on file, and we'll pre-authorize the ticket amount 24 hours before the appointment.
  // Final payment will be automatically charged at the end of the service day.
  optional models.payment.v1.PreAuthEnableDef pre_auth = 4 [(validate.rules).message = {skip: true}];

  // Appointment notes, contains ticket comments and alert notes
  // Ticket comments: For this appointment. Private to business only
  // Alert notes: It will be shown as a red exclamation mark on the appointment card. Private for business.
  repeated models.appointment.v1.AppointmentNoteCreateDef notes = 5 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Pet belongings
  repeated PetBelonging pet_belongings = 6 [(validate.rules).repeated = {max_items: 100}];

  // Pet belonging
  message PetBelonging {
    // pet id
    int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
    // pet name
    string name = 2 [(validate.rules).string = {
      min_len: 1
      max_len: 1024
    }];
    // pet area
    optional string area = 3 [(validate.rules).string = {max_len: 100}];
    // pet photo url
    optional string photo_url = 4 [(validate.rules).string = {
      max_len: 1024
      uri: true
    }];
  }

  // created by, if not set, use current staff id, used for data migration from other systems
  optional int64 created_by = 7;

  // created at, if not set, use current time, used for data migration from other systems
  optional google.protobuf.Timestamp created_at = 8;
}

// Create a appointment result
message CreateAppointmentResult {
  // Appointment id
  int64 appointment_id = 1;
}

// Update a appointment params, contains appointment date and pet details
message UpdateAppointmentParams {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // update params
  models.appointment.v1.AppointmentUpdateDef appointment = 2 [(validate.rules).message = {required: true}];
}

// Update a appointment result
message UpdateAppointmentResult {}

// Get appointment params
message GetAppointmentParams {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// Get appointment result
message GetAppointmentResult {
  // Appointment detail
  models.appointment.v1.AppointmentCalendarView appointment = 1;
  // pet and services
  repeated ServiceDetail service_detail = 2;
  // notes
  repeated models.appointment.v1.AppointmentNoteModel notes = 3;
  // customer
  CustomerComposite customer = 4;
  // invoice, Please use orders instead.
  models.order.v1.InvoiceCalendarView invoice = 5 [deprecated = true];
  // no show invoice, Please use orders instead.
  models.order.v1.NoShowInvoiceCalendarView no_show_invoice = 6 [deprecated = true];
  // pre pay
  models.payment.v1.PrePayCalendarView pre_pay = 7;
  // pre auth
  models.payment.v1.PreAuthCalendarView pre_auth = 8;
  // auto assign
  models.appointment.v1.AutoAssignCalendarView auto_assign = 9;
  // wait list
  models.appointment.v1.WaitListCalendarView wait_list = 10;
  // staff
  repeated models.organization.v1.StaffModel staffs = 11;
  // service type list
  repeated models.offering.v1.ServiceItemType service_item_types = 12;
  // memberships
  moego.models.membership.v1.MembershipSubscriptionListModel membership_subscriptions = 13;
  // pricing rule apply log list, deprecated, use pricing_rule_apply_logs_v2 instead
  repeated models.appointment.v1.PricingRuleApplyLogDrawerView pricing_rule_apply_logs = 14 [deprecated = true];
  // pricing rule apply log list
  repeated models.appointment.v2.PricingRuleApplyLogDrawerView pricing_rule_apply_logs_v2 = 15;
  // Order list, might be empty if the appointment not charged yet.
  repeated models.order.v1.OrderModelAppointmentView orders = 16;
  // Payment summary
  PaymentSummary payment_summary = 17;
}

// Payment summary
message PaymentSummary {
  // Service subtotal = sum(service total_price) + sum(service_charge price)
  google.type.Money subtotal_amount = 1;
  // Payment collected = sum(order paid_amount)
  google.type.Money collected_amount = 2;
  // deposit amount
  google.type.Money deposit_amount = 3;
  // Collected deposit amount = sum(deposit paid_amount)
  google.type.Money collected_deposit_amount = 4;
  // Use store credit
  bool use_store_credit = 5;
  // Collected tips amount = sum(order tips_amount)
  google.type.Money collected_tips_amount = 6;
}

// pet detail
message ServiceDetail {
  // pet
  models.business_customer.v1.BusinessCustomerPetModel pet = 1;
  // services
  repeated ServiceComposite services = 2;
  // add-ons
  repeated AddOnComposite add_ons = 3;
  // pet code
  repeated models.business_customer.v1.BusinessPetCodeModel pet_codes = 4;
  // vaccine
  repeated PetVaccineComposite vaccines = 5;
  // evaluation
  repeated EvaluationServiceComposite evaluations = 6;
  // pet evaluation
  repeated models.business_customer.v1.PetEvaluationModel pet_evaluations = 7;
}

// pet detail service composite
message ServiceComposite {
  // pet detail
  ServiceDetailComposite service_detail = 1;
  // operation
  repeated models.appointment.v1.ServiceOperationModel operations = 2;
  // split lodgings
  repeated models.appointment.v1.BoardingSplitLodgingDetailDef split_lodgings = 3;
}

// pet detail add on composite
message AddOnComposite {
  // pet detail
  ServiceDetailComposite service_detail = 1;
  // operation
  repeated models.appointment.v1.ServiceOperationModel operations = 2;
}

// service detail composite
message ServiceDetailComposite {
  // pet detail id
  int64 id = 1;
  // appointment id
  int64 appointment_id = 2;
  // pet id
  int64 pet_id = 3;
  // staff id
  int64 staff_id = 4;
  // service id
  int64 service_id = 5;
  // service type
  models.offering.v1.ServiceType service_type = 6;
  // service time, in minutes
  int32 service_time = 7;
  // service price
  double service_price = 8;
  // start time, in minutes
  int32 start_time = 9;
  // end time, in minutes
  int32 end_time = 10;
  // scope type price
  models.offering.v1.ServiceScopeType scope_type_price = 13;
  // scope type time
  models.offering.v1.ServiceScopeType scope_type_time = 14;
  // package service id
  int64 package_service_id = 16;
  // enable operation
  bool enable_operation = 21;
  // work mode, 0-parallel, 1-sequence
  models.appointment.v1.WorkMode work_mode = 22;
  // service color code
  string service_color_code = 23;
  // service start date, in yyyy-MM-dd format, for boarding or daycare service
  string start_date = 24;
  // service end date, in yyyy-MM-dd format, for boarding or daycare service
  string end_date = 25;
  // service item type, different from service type, it includes grooming, boarding, daycare or other services.
  models.offering.v1.ServiceItemType service_item_type = 26;
  // lodging id, only for boarding service item type
  int64 lodging_id = 27;
  // price unit, 1 - per session, 2 - per night, 3 - per hour, 4 - per day
  int32 price_unit = 28;
  // specific dates, yyyy-MM-dd
  repeated string specific_dates = 29;
  // add-on associated service id
  int64 associated_service_id = 30;
  // service name
  string service_name = 31;
  // lodging unit name
  string lodging_unit_name = 32;
  // lodging type name
  string lodging_type_name = 33;
  // staff name
  string staff_name = 34;
  // service delete
  bool is_deleted = 35;
  // service inactive
  bool inactive = 36;
  // max duration (only for daycare service)
  int32 max_duration = 37;
  // pet detail date type
  optional models.appointment.v1.PetDetailDateType date_type = 38;
  // price override type
  optional moego.models.offering.v1.ServiceOverrideType price_override_type = 39;
  // duration override type
  optional moego.models.offering.v1.ServiceOverrideType duration_override_type = 40;
  // quantity per day
  int32 quantity_per_day = 41;
  // order line item id
  int64 order_line_item_id = 42;
  // require dedicated staff
  bool require_dedicated_staff = 43;
}

// customer composite
message CustomerComposite {
  // customer
  models.business_customer.v1.BusinessCustomerModel customer_profile = 1;
  // customer tag
  repeated models.business_customer.v1.BusinessCustomerTagModel customer_tags = 2;
  // customer address
  models.business_customer.v1.BusinessCustomerAddressModel customer_address = 3;
  // is new customer
  bool is_new_customer = 4;
  // required sign
  bool required_sign = 5;
  // review booster sent
  bool review_booster_sent = 6;
  // last alert note
  optional models.appointment.v1.AppointmentNoteModel last_alert_note = 7;
  // customer package info
  repeated CustomerPackageView customer_packages = 8;
}

// pet vaccine
message PetVaccineComposite {
  // vaccine record id
  int64 id = 1;
  // vaccine id
  int64 vaccine_id = 3;
  // expiration date, may not exist
  optional google.type.Date expiration_date = 4;
  // vaccine document urls
  repeated string document_urls = 5;
  // vaccine name
  string vaccine_name = 6;
}

// pet evaluation
message EvaluationServiceComposite {
  // pet evaluation detail id
  int64 id = 1;
  // appointment id
  int64 appointment_id = 2;
  // pet id
  int64 pet_id = 3;
  // service id
  int64 service_id = 4;
  // service time, in minutes
  int32 service_time = 5;
  // service price
  double service_price = 6;
  // start time, in minutes
  int32 start_time = 7;
  // end time, in minutes
  int32 end_time = 8;
  // service start date, in yyyy-MM-dd format
  string start_date = 9;
  // service end date, in yyyy-MM-dd format
  string end_date = 10;
  // service item type, always be evaluation services.
  models.offering.v1.ServiceItemType service_item_type = 11;
  // color code
  string color_code = 12;
  // staff id
  optional int64 staff_id = 13;
  // service name
  string service_name = 14;
  // lodging id
  optional int64 lodging_id = 15;
  // lodging unit name
  optional string lodging_unit_name = 16;
  // lodging type name
  optional string lodging_type_name = 17;
  // staff name
  optional string staff_name = 34;
  // order line item id
  int64 order_line_item_id = 42;
}

// get customer last appointment params
message GetCustomerLastAppointmentParams {
  // customer id
  int64 customer_id = 1 [(validate.rules).int64 = {gt: 0}];
  // selected business id
  optional int64 selected_business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get customer last finished appointment result
message GetCustomerLastAppointmentResult {
  // has last appointment
  bool has_last_appointment = 1;
  // customer
  CustomerComposite customer = 2;
  // Appointment detail
  models.appointment.v1.AppointmentCalendarView appointment = 3;
  // pet and services
  repeated ServiceDetail service_detail = 4;
  // notes
  repeated models.appointment.v1.AppointmentNoteModel notes = 5;
  // staff
  repeated models.organization.v1.StaffModel staffs = 6;
  // service type list
  repeated models.offering.v1.ServiceItemType service_item_types = 7;
  // pricing rule apply log list, deprecated, use pricing_rule_apply_logs_v2 instead
  repeated models.appointment.v1.PricingRuleApplyLogDrawerView pricing_rule_apply_logs = 8 [deprecated = true];
  // pricing rule apply log list
  repeated models.appointment.v2.PricingRuleApplyLogDrawerView pricing_rule_apply_logs_v2 = 15;
}

// calculate appointment invoice params
message CalculateAppointmentInvoiceParams {
  // Selected pet and services
  repeated models.appointment.v1.PetDetailDef pet_details = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// calculate appointment invoice result
message CalculateAppointmentInvoiceResult {
  // order detail
  models.order.v1.OrderDef order = 1;
}

// get in progress evaluation appointment params
message GetInProgressEvaluationAppointmentParams {
  // business id
  optional int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // customer id
  optional int64 customer_id = 2 [(validate.rules).int64 = {gt: 0}];
  // pet id
  optional int64 pet_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// get in progress evaluation appointment result
message GetInProgressEvaluationAppointmentResult {
  // appointment id
  optional int64 appointment_id = 1;
}

// Get service summary params
message GetServiceSummaryParams {
  // Selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // start date, in yyyy-MM-dd format
  string start_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // end date, in yyyy-MM-dd format
  string end_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
}

// Get service summary result
message GetServiceSummaryResult {
  // Count by appointment
  CountedByAppointment count_by_appointment = 1;
  // Count by pet
  CountedByPet count_by_pet = 2;

  // Count by appointment
  message CountedByAppointment {
    // rows
    repeated ServiceSummaryRow rows = 1;
  }

  // Count by pet
  message CountedByPet {
    // rows
    repeated ServiceSummaryRow rows = 1;
  }

  // Row
  message ServiceSummaryRow {
    // date, in yyyy-MM-dd format
    string date = 1;
    // total in count
    int32 in = 2;
    // total out count
    int32 out = 3;
    // total overnight count
    int32 overnight = 4;
    // total count
    int32 total = 5;
    // details
    ServiceSummaryChildren children = 6;
  }

  // Service summary children
  message ServiceSummaryChildren {
    // grooming
    ServiceRow grooming = 1;
    // boarding
    ServiceRow boarding = 2;
    // daycare
    ServiceRow daycare = 3;
    // evaluation
    ServiceRow evaluation = 4;
    // dog walking
    ServiceRow dog_walking = 5;
  }

  // Row detail
  message ServiceRow {
    // in count
    int32 in = 1;
    // out count
    int32 out = 2;
    // overnight count
    int32 overnight = 3;
    // total count
    int32 total = 4;
  }
}

// get day summary params
message GetDaySummaryParams {
  // Selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // need query date, in yyyy-MM-dd format
  string date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
}

// get day summary result
message GetDaySummaryResult {
  // need query date, in yyyy-MM-dd format
  string date = 1; // 日期
  // appointment_day_summary
  AppointmentDaySummary appointment_day_summary = 2;
  // pet_day_summary
  PetDaySummary pet_day_summary = 3;
}

// appointment day summary
message AppointmentDaySummary {
  // daily status
  DailyStatus daily_status = 1;
  // care_list
  repeated CareCategory care_list = 2;
}

// pet day summary
message PetDaySummary {
  // daily_status
  DailyStatus daily_status = 1;
  // care_list
  repeated CareCategory care_list = 2;
}

// daily status
message DailyStatus {
  // total in count
  uint32 in = 1;
  // total out count
  uint32 out = 2;
  // total overnight count
  uint32 overnight = 3;
  // total count
  uint32 total = 4;
}

// care category
message CareCategory {
  // category type: grooming, boarding
  string type = 1;
  // total
  uint32 total = 2;
  // service_list. eg: grooming: full grooming、bath.
  repeated ServiceItem service_list = 3;
}

// service item
message ServiceItem {
  // name
  string name = 1;
  // total
  uint32 total = 2;
}

// get appointment lodging info params
message GetAppointmentLodgingParams {
  // appointment id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get appointment lodging info result
message GetAppointmentLodgingResult {
  // pet lodging view list
  repeated PetLodgingView pet_lodging_views = 1;

  // pet lodging view
  message PetLodgingView {
    // pet id
    int64 pet_id = 1;
    // service lodging view list
    repeated ServiceLodgingView service_lodgings = 2;
    // evaluation lodging view list
    repeated EvaluationLodgingView evaluation_lodgings = 3;
  }

  // lodgings info for pet service detail
  message ServiceLodgingView {
    // service item type
    models.offering.v1.ServiceItemType service_item_type = 1;
    // service id
    int64 service_id = 2;
    // service name
    string service_name = 3;
    // pet service detail id
    int64 pet_service_detail_id = 4;
    // service start date, in yyyy-MM-dd format
    string start_date = 5;
    // service end date, in yyyy-MM-dd format
    string end_date = 6;
    // specific dates, yyyy-MM-dd
    repeated string specific_dates = 7;
    // lodging unit id
    int64 lodging_unit_id = 8;
    // lodging unit name
    string lodging_unit_name = 9;
  }

  // lodgings info for pet evaluation detail
  message EvaluationLodgingView {
    // service item type
    models.offering.v1.ServiceItemType service_item_type = 1;
    // evaluation service id
    int64 evaluation_id = 2;
    // evaluation service name
    string evaluation_name = 3;
    // pet evaluation detail id
    int64 pet_service_evaluation_id = 4;
    // service start date, in yyyy-MM-dd format
    string start_date = 5;
    // service end date, in yyyy-MM-dd format
    string end_date = 6;
    // lodging unit id
    int64 lodging_unit_id = 7;
    // lodging unit name
    string lodging_unit_name = 8;
  }
}

// Batch quick check in for appointment params
message BatchQuickCheckInParams {
  // Selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Selected service
  int64 service_id = 2 [(validate.rules).int64 = {gt: 0}];

  // Selected multi pet ids
  repeated int64 pet_ids = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // Selected date
  google.type.Date date = 4 [(validate.rules).message = {required: true}];

  // source
  models.appointment.v1.AppointmentSource source = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Quick check in for appointment result
message BatchQuickCheckInResult {
  // created appointment id
  repeated int64 created_appointment_ids = 1;

  // updated appointment id
  repeated int64 updated_appointment_ids = 2;
}

// ListStaffAppointmentsParams
message ListStaffAppointmentsParams {
  // query period
  google.type.Interval period = 1 [(validate.rules).message = {required: true}];
  // business ids
  repeated uint64 business_ids = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      uint64: {gt: 0}
    }
  }];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3;
  // staff id, override header staff id
  optional uint64 staff_id = 4 [(validate.rules).uint64 = {gt: 0}];
  // get staff appt by operation detail(multi-staff)
  optional bool include_service_operation = 5;
  // query appointment date
  optional google.type.Date appointment_date = 6;
}

// ListStaffAppointmentsResult
message ListStaffAppointmentsResult {
  // Invoice detail view
  message InvoiceView {
    // net sale
    google.type.Money net_sale = 1;
    // tips
    google.type.Money tips = 2;
  }
  // Customer detail view
  message CustomerView {
    // customer profile
    models.business_customer.v1.BusinessCustomerInfoModel customer_profile = 1;
    // is new customer
    bool is_new_customer = 2;
    // customer address
    optional models.business_customer.v1.BusinessCustomerAddressModel customer_address = 3;
  }

  // Appointment detail view
  message AppointmentDetailView {
    // appointment
    models.appointment.v1.AppointmentModel appointment = 1;
    // service detail
    repeated appointment.v1.ServiceDetail service_detail = 2;
    // invoice amount detail
    InvoiceView invoice = 3;
    // customer detail
    CustomerView customer = 4;
    // include service item types
    repeated models.offering.v1.ServiceItemType service_item_types = 5;
  }

  // appointment list
  repeated AppointmentDetailView appointments = 1;
  // pagination response
  utils.v2.PaginationResponse pagination = 2;
}

// Batch book again appointment by staff and date params
message BatchBookAgainAppointmentParams {
  // Selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Selected staff id
  int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];

  // Selected date
  google.type.Date source_date = 3;

  // Target date
  google.type.Date target_date = 4;
}

// Batch book again appointment by staff and date result
message BatchBookAgainAppointmentResult {
  // book again appointments detail
  repeated models.appointment.v1.AppointmentModel appointments = 1;
}

// Batch cancel appointment by staff and date params
message BatchCancelAppointmentParams {
  // Selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Selected staff id
  int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];

  // Selected date
  google.type.Date date = 3;

  // cancel reason, optional
  optional string cancel_reason = 4 [(validate.rules).string = {max_len: 1024}];
}

// Batch cancel appointment by staff and date result
message BatchCancelAppointmentResult {
  // canceled appointments detail
  repeated models.appointment.v1.AppointmentModel appointments = 1;
}

// RescheduleBoardingAppointment params
message RescheduleBoardingAppointmentParams {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // start date, optional
  optional string start_date = 2 [(validate.rules).string = {pattern: "\\d{4}-\\d{2}-\\d{2}"}];

  // end date, optional
  optional string end_date = 3 [(validate.rules).string = {pattern: "\\d{4}-\\d{2}-\\d{2}"}];
}

// RescheduleBoardingAppointment result
message RescheduleBoardingAppointmentResult {}

// the appointment service
service AppointmentService {
  // Create a appointment
  // Applicable to quick add and advanced add appointment scenarios
  rpc CreateAppointment(CreateAppointmentParams) returns (CreateAppointmentResult);

  // Incremental update appointment
  // Applicable to add/modify pet&service, switch on multi-pets start at the same time.
  rpc UpdateAppointment(UpdateAppointmentParams) returns (UpdateAppointmentResult);

  // Get appointment detail
  rpc GetAppointment(GetAppointmentParams) returns (GetAppointmentResult);

  // get customer last appointment
  rpc GetCustomerLastAppointment(GetCustomerLastAppointmentParams) returns (GetCustomerLastAppointmentResult);

  // Calculate appointment invoice
  // Estimated total, including service, add-on, tax, discount etc.
  rpc CalculateAppointmentInvoice(CalculateAppointmentInvoiceParams) returns (CalculateAppointmentInvoiceResult);
  // Get in progress evaluation appointment
  rpc GetInProgressEvaluationAppointment(GetInProgressEvaluationAppointmentParams) returns (GetInProgressEvaluationAppointmentResult);

  // Get day summary
  rpc GetDaySummary(GetDaySummaryParams) returns (GetDaySummaryResult);

  // Get service summary
  rpc GetServiceSummary(GetServiceSummaryParams) returns (GetServiceSummaryResult);

  // get appointment lodging info
  rpc GetAppointmentLodging(GetAppointmentLodgingParams) returns (GetAppointmentLodgingResult);

  // Batch quick check in multi-pets for appointments <br>
  // Pets belonging to the same client are in one appointment <br>
  // Pets of different clients create different appointments <br>
  // Update the appointment status to check in
  rpc BatchQuickCheckIn(BatchQuickCheckInParams) returns (BatchQuickCheckInResult);

  // ListStaffAppointments, used for getting staff appointment list in Mobile reports
  rpc ListStaffAppointments(ListStaffAppointmentsParams) returns (ListStaffAppointmentsResult);

  // book again appointment by staff and date
  rpc BatchBookAgainAppointment(BatchBookAgainAppointmentParams) returns (BatchBookAgainAppointmentResult);

  // Batch cancel appointment by staff and date
  rpc BatchCancelAppointment(BatchCancelAppointmentParams) returns (BatchCancelAppointmentResult);

  // Reschedule boarding appointment
  // 处理与 boarding 相关的附加数据，如 daycare、grooming、addon 等服务。
  // 如果传入的 end_date 早于当前 end_date，则删除 boarding 关联的所有 daycare、grooming、addon 数据。
  // 如果传入的 end_date 晚于当前 end_date，则在 boarding 上添加相应的新增 daycare、grooming、addon 数据。
  rpc RescheduleBoardingAppointment(RescheduleBoardingAppointmentParams) returns (RescheduleBoardingAppointmentResult);
}
