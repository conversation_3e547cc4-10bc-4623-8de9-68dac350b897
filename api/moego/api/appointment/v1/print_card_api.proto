// @since 2024-11-13 15:32:15
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.appointment.v1;

import "google/type/date.proto";
import "moego/models/appointment/v1/appointment_task_enums.proto";
import "moego/models/appointment/v1/boarding_split_lodging_defs.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/offering/v1/lodging_unit_models.proto";
import "moego/models/offering/v1/playgroup_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// get print appointment card list params
message ListAppointmentCardParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // target date
  google.type.Date date = 2;
  // service item type
  repeated models.offering.v1.ServiceItemType types = 3 [
    (validate.rules).repeated = {
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    },
    deprecated = true
  ];
  // is pet check in
  optional bool is_pets_checked_in_only = 4;
  // service filter
  repeated ServiceFilter service_filters = 5;
}

// service filter
message ServiceFilter {
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: 0
  }];
  // is all service
  bool is_all_service = 2;
  // service ids
  repeated int64 service_ids = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// get appointment card list result
message ListAppointmentCardResult {
  // boarding view list
  repeated BoardingView boardings = 1;
  // daycare view list
  repeated DaycareView daycares = 2;
  // grooming view list
  repeated GroomingView groomings = 3;
  // evaluation view list
  repeated EvaluationView evaluations = 4;
  // dog walking view list
  repeated DogWalkingView dog_walkings = 5;
  // pet view list
  repeated PetView pets = 11;
  // appointment view list
  repeated AppointmentView appointments = 12;
  //  lodging type list view
  repeated LodgingTypeDayView lodging_types = 13;
  // lodging unit list
  repeated moego.models.offering.v1.LodgingUnitModel lodging_units = 14;

  // boarding view
  message BoardingView {
    // appointment id
    int64 appointment_id = 1;
    // pet id
    int64 pet_id = 2;
    // pet service list
    repeated PetServiceView pet_services = 3;

    // pet service view
    message PetServiceView {
      // pet service id
      int64 pet_service_id = 1;
      // service name
      string service_name = 2;
      // appointment start date
      string start_date = 3;
      // appointment start time
      int32 start_time = 4;
      // appointment end date
      string end_date = 5;
      // appointment end time
      int32 end_time = 6;
      // lodging unit name (if has split lodgings, this filed means the last lodging unit name)
      string lodging_unit_name = 7;
      // lodging type name (if has split lodgings, this filed means the last lodging type name)
      string lodging_type_name = 8;
      // lodging type id (if has split lodgings, this filed means the last lodging type id)
      int64 lodging_type_id = 9;
      // split lodging list
      repeated models.appointment.v1.BoardingSplitLodgingDetailDef split_lodgings = 10;
      // lodging unit id (if has split lodgings, this filed means the last lodging unit id)
      int64 lodging_unit_id = 11;
    }
  }

  // daycare view
  message DaycareView {
    // appointment id
    int64 appointment_id = 1;
    // pet id
    int64 pet_id = 2;
    // pet service
    repeated PetServiceView pet_services = 3;

    // pet service view
    message PetServiceView {
      // pet service id
      int64 pet_service_id = 1;
      // service name
      string service_name = 2;
      // appointment start time
      int32 start_time = 3;
      // appointment end time
      int32 end_time = 4;
      // lodging unit name
      string lodging_unit_name = 5;
      // lodging type name
      string lodging_type_name = 6;
      // lodging type id
      int64 lodging_type_id = 7;
      // lodging unit id
      int64 lodging_unit_id = 8;
    }
  }

  // grooming view
  message GroomingView {
    // appointment id
    int64 appointment_id = 1;
    // pet id
    int64 pet_id = 2;
    // pet service
    repeated PetServiceView pet_services = 3;

    // pet service view
    message PetServiceView {
      // pet service id
      int64 pet_service_id = 1;
      // service name
      string service_name = 2;
      // appointment start time
      int32 start_time = 3;
      // appointment end time
      int32 end_time = 4;
      // staff first name
      string staff_first_name = 5;
      // staff last name
      string staff_last_name = 6;
      // service type
      models.offering.v1.ServiceType service_type = 7;
    }
  }

  // evaluation view
  message EvaluationView {
    // appointment id
    int64 appointment_id = 1;
    // pet id
    int64 pet_id = 2;
    // pet service
    repeated PetServiceView pet_services = 3;

    // pet service view
    message PetServiceView {
      // pet service id
      int64 pet_service_id = 1;
      // service name
      string service_name = 2;
      // appointment start time
      int32 start_time = 3;
      // appointment end time
      int32 end_time = 4;
      // lodging unit name
      string lodging_unit_name = 5;
      // lodging type name
      string lodging_type_name = 6;
      // lodging type id
      int64 lodging_type_id = 7;
      // lodging unit id
      int64 lodging_unit_id = 8;
    }
  }

  // dog walking view
  message DogWalkingView {
    // appointment id
    int64 appointment_id = 1;
    // pet id
    int64 pet_id = 2;
    // pet service
    repeated PetServiceView pet_services = 3;

    // pet service view
    message PetServiceView {
      // pet service id
      int64 pet_service_id = 1;
      // service name
      string service_name = 2;
      // appointment start time
      int32 start_time = 3;
      // appointment end time
      int32 end_time = 4;
      // staff first name
      string staff_first_name = 5;
      // staff last name
      string staff_last_name = 6;
    }
  }

  // pet view
  message PetView {
    // pet id
    int64 id = 1;
    // pet name
    string name = 2;
    // avatar path
    string avatar_path = 3;
    // gender
    moego.models.customer.v1.PetGender gender = 4;
    // breed
    string breed = 5;
    // pet type id
    moego.models.customer.v1.PetType pet_type = 6;
    // weight
    string weight = 7;
    // pet code id list
    repeated int64 pet_code_ids = 8;
    // pet notes
    repeated string pet_notes = 9;
    // pet appearance color
    string pet_appearance_color = 10;
    // pet appearance notes
    string pet_appearance_notes = 11;
    // pet fixed
    string pet_fixed = 12;
    // pet owner name
    string owner_name = 13;
    // pet owner first name
    string owner_first_name = 14;
    // pet owner last name
    string owner_last_name = 15;
    // pet code bindings
    repeated PetCodeBindingsView pet_code_bindings = 16;
  }

  // appointment view
  message AppointmentView {
    // appointment id
    int64 id = 1;
    // appointment comment
    string comment = 2;
    // appointment alert
    string alert = 3;
  }

  // lodging type day view
  message LodgingTypeDayView {
    // id of the lodging type
    int64 id = 1;
    // name of the lodging type
    string name = 2;
    // max pet number of this lodging type
    int32 max_pet_num = 3;
    // max pet num for all lodging types
    int32 max_pet_total_num = 4;
    // occupied pet num
    int32 occupied_pet_num = 5;
    // sort
    int32 sort = 6;
  }

  // pet code bindings view
  message PetCodeBindingsView {
    // pet code id
    int64 pet_code_id = 1;
    // pet code abbreviation
    string abbreviation = 2;
    // pet code color
    string color = 3;
    // pet code unique comment
    string unique_comment = 4;
  }
}

// The params message for ListDailyTasks
message ListDailyTasksParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // target date
  google.type.Date date = 2;
  // The service item types
  repeated models.offering.v1.ServiceItemType service_item_types = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// The result message for ListDailyTasks
message ListDailyTasksResult {
  // The list of feeding tasks
  repeated TaskRow feedings = 1;
  // The list of medication tasks
  repeated TaskRow medications = 2;
  // The list of add-on groups
  repeated AddOnGroup add_ons = 3;

  // The add-on group
  message AddOnGroup {
    // The add-on id
    int64 id = 1;
    // The add-on name
    string name = 2;
    // The list of add-on tasks
    repeated TaskRow tasks = 3;
  }

  // The task row
  message TaskRow {
    // The task type
    models.appointment.v1.AppointmentTaskCategory category = 1;
    // The pet column
    PetColumn pet = 2;
    // The service column
    ServiceColumn service = 3;
    // The feeding instruction
    string instruction = 4;
    // The feeding time
    optional int32 time = 5;
    // The assign staff
    StaffColumn staff = 6;
  }

  // The pet column
  message PetColumn {
    // The pet id
    int64 id = 1;
    // The pet name
    string name = 2;
    // The pet type id
    models.customer.v1.PetType pet_type = 3;
    // The breed
    string breed = 4;
    // The gender
    models.customer.v1.PetGender gender = 5;
    // The avatar path
    string avatar_path = 6;
    // The weight
    string weight = 7;
  }

  // The service column
  message ServiceColumn {
    // The service id
    int64 id = 1;
    // The service name
    string name = 2;
    // The service item type
    models.offering.v1.ServiceItemType service_item_type = 3;
  }

  // The staff column
  message StaffColumn {
    // The staff id
    int64 id = 1;
    // The first name
    string first_name = 2;
    // The last name
    string last_name = 3;
  }
}

// The params message for ListBoardingDepartureCard
message ListBoardingDepartureCardParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // target date
  google.type.Date date = 2 [(validate.rules).message = {required: true}];
  // is pet check in
  optional bool is_pets_checked_in_only = 3;
}

// The result message for ListBoardingDepartureCard
message ListBoardingDepartureCardResult {
  // boarding view list
  repeated BoardingView boardings = 1;
  // pet view list
  repeated ListAppointmentCardResult.PetView pets = 11;
  // appointment view list
  repeated ListAppointmentCardResult.AppointmentView appointments = 12;
  //  lodging type list view
  repeated ListAppointmentCardResult.LodgingTypeDayView lodging_types = 13;
  // lodging unit list
  repeated moego.models.offering.v1.LodgingUnitModel lodging_units = 14;

  // boarding view
  message BoardingView {
    // appointment id
    int64 appointment_id = 1;
    // pet id
    int64 pet_id = 2;
    // pet service list
    repeated ListAppointmentCardResult.BoardingView.PetServiceView pet_services = 3;
    // Pet belongings
    repeated PetBelonging pet_belongings = 4;

    // Pet belonging
    message PetBelonging {
      // pet name
      string name = 1;
      // pet area
      optional string area = 2;
    }
  }
}

// The params message for list daily playgroup params
message ListDailyPlaygroupCardParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // target date
  google.type.Date date = 2;
  // is pet check in
  optional bool is_pets_checked_in_only = 3;
}

// The result message for list daily playgroup result
message ListDailyPlaygroupCardResult {
  // playgroup model list
  repeated moego.models.offering.v1.PlaygroupModel playgroups = 1;
  // playgroup view
  repeated PlaygroupView playgroup_views = 2;
  // pet view list
  repeated PetView pets = 3;

  // playgroup view
  message PlaygroupView {
    // playgroup id
    int64 playgroup_id = 1;
    // pet number
    int32 pet_number = 2;
    // pet list
    repeated PetPlaygroupView pet_playgroups = 3;
  }

  // pet playgroup view
  message PetPlaygroupView {
    // pet playgroup id
    int64 pet_playgroup_id = 1;
    // pet id
    int64 pet_id = 2;
    // pet playgroup list sort. start with 1 and put the smallest first
    int32 sort = 3;
  }

  // pet view
  message PetView {
    // pet id
    int64 pet_id = 1;
    // pet name
    string pet_name = 2;
    // pet type
    moego.models.customer.v1.PetType pet_type = 4;
    // pet playgroup color
    string pet_playgroup_color = 5;
    // customer
    CustomerView customer = 6;
    // pet codes
    repeated PetCodeBindingsView pet_code_bindings = 7;
    // pet breed name
    string pet_breed = 8;
  }

  // customer view
  message CustomerView {
    // customer id
    int64 customer_id = 1;
    // first name
    string first_name = 2;
    // last name
    string last_name = 3;
  }

  // pet code bindings view
  message PetCodeBindingsView {
    // pet code abbreviation
    string abbreviation = 1;
    // pet code color
    string color = 2;
    // pet code unique comment
    string unique_comment = 3;
  }
}

// The params message for ListBoardingArrivalCard
message ListBoardingArrivalCardParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // target date
  google.type.Date date = 2 [(validate.rules).message = {required: true}];
  // is pet check in
  optional bool is_pets_checked_in_only = 3;
}

// The result message for ListBoardingArrivalCard
message ListBoardingArrivalCardResult {
  // boarding view list
  repeated BoardingView boardings = 1;
  // pet view list
  repeated ListAppointmentCardResult.PetView pets = 11;
  // appointment view list
  repeated ListAppointmentCardResult.AppointmentView appointments = 12;
  //  lodging type list view
  repeated ListAppointmentCardResult.LodgingTypeDayView lodging_types = 13;
  // lodging unit list
  repeated moego.models.offering.v1.LodgingUnitModel lodging_units = 14;

  // boarding view
  message BoardingView {
    // appointment id
    int64 appointment_id = 1;
    // pet id
    int64 pet_id = 2;
    // pet service list
    repeated ListAppointmentCardResult.BoardingView.PetServiceView pet_services = 3;
    // Pet belongings
    repeated PetBelonging pet_belongings = 4;

    // Pet belonging
    message PetBelonging {
      // pet name
      string name = 1;
      // pet area
      optional string area = 2;
    }
  }
}

// the print card service
service PrintCardService {
  // get appointment card list
  rpc ListAppointmentCard(ListAppointmentCardParams) returns (ListAppointmentCardResult);

  // List Daily Tasks
  rpc ListDailyTasks(ListDailyTasksParams) returns (ListDailyTasksResult);

  // list boarding departure card
  rpc ListBoardingDepartureCard(ListBoardingDepartureCardParams) returns (ListBoardingDepartureCardResult);

  // list playgroup card
  rpc ListDailyPlaygroupCard(ListDailyPlaygroupCardParams) returns (ListDailyPlaygroupCardResult);

  // list boarding arrival card
  rpc ListBoardingArrivalCard(ListBoardingArrivalCardParams) returns (ListBoardingArrivalCardResult);
}
