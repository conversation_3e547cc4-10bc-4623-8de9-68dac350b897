syntax = "proto3";

package moego.api.appointment.v1;

import "google/type/date.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/online_booking/v1/ob_availability_setting_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// check save appointment params
message CheckSaveAppointmentParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // start date
  google.type.Date start_date = 2 [(validate.rules).message = {required: true}];
  // end date
  google.type.Date end_date = 3 [(validate.rules).message = {required: true}];
  // lodging unit ids
  repeated int64 lodging_unit_ids = 4;
  // pet ids
  repeated int64 pet_ids = 5;
  // customer id
  optional int64 customer_id = 6;
  // appointment id
  optional int64 appointment_id = 7;
}

// check save appointment result
message CheckSaveAppointmentResult {
  // lodging over capacity check result
  LodgingOverCapacityCheckResult lodging_over_capacity_check_result = 1;
  // appointment date conflict check result
  AppointmentDateConflictCheckResult appointment_date_conflict_check_result = 2;
  // business closed date check result
  BusinessClosedDateCheckResult business_closed_date_check_result = 3;
}

// lodging over capacity check result
message LodgingOverCapacityCheckResult {
  // lodging over capacity list
  repeated LodgingUnitOverview lodging_units = 1;
  // lodging type list
  repeated LodgingTypeOverview lodging_types = 2;
}

//  lodging unit over view
message LodgingUnitOverview {
  // lodging unit id
  int64 id = 1;
  // lodging unit name
  string name = 2;
  // lodging unit type id
  int64 lodging_type_id = 3;
}

// lodging type over view
message LodgingTypeOverview {
  // lodging type id
  int64 id = 1;
  // lodging type name
  string name = 2;
}

// appointment date conflict check result
message AppointmentDateConflictCheckResult {
  // conflict appointments overview for pet
  repeated PetAppointmentsOverview conflict_appointments = 1;
}

// appointments overview for pet
message PetAppointmentsOverview {
  // pet
  PetOverview pet = 1;
  // appointments
  repeated AppointmentOverview appointments = 2;
}

// business closed date check result
message BusinessClosedDateCheckResult {
  // closed date list, in yyyy-MM-dd format
  repeated string closed_date = 1;
}

// pet overview
message PetOverview {
  // id
  int64 id = 1;
  // pet name
  string pet_name = 2;
  // avatar path
  string avatar_path = 3;
  // weight
  string weight = 4;
  // coat type
  string coat_type = 5;
  // breed
  string breed = 6;
  // pet type
  moego.models.customer.v1.PetType pet_type = 7;
}

// appointment overview
message AppointmentOverview {
  // appointment id
  int64 appointment_id = 1;
  // appointment start date
  google.type.Date start_date = 2;
  // appointment end date
  google.type.Date end_date = 3;
  // services
  repeated ServiceOverview services = 4;
  // appointment start time, the number of minutes of the day
  int32 appointment_start_time = 5;
  // appointment end time, the number of minutes of the day
  int32 appointment_end_time = 6;
}

// service overview
message ServiceOverview {
  // service id
  int64 service_id = 1;
  // service name
  string service_name = 2;
  // service start date
  google.type.Date start_date = 3;
  // service end date
  google.type.Date end_date = 4;
  // service item type, different from service type, it includes grooming, boarding, daycare or other services.
  models.offering.v1.ServiceItemType service_item_type = 5;
}

// get available dates params
message GetAvailableDatesParams {
  // business_id
  int64 business_id = 1;
  // pet 和选择的 service 信息
  repeated PetServices pet_services = 2;
  // start date
  google.type.Date start_date = 3;
  // end date
  google.type.Date end_date = 4;

  // pet 和选择的 service/evaluation 信息
  message PetServices {
    // pet id
    int64 pet_id = 1;
    // pet selected evaluations
    repeated int64 evaluation_ids = 2;
  }
}

// get available dates result
message GetAvailableDatesResult {
  // available dates
  repeated google.type.Date available_dates = 1;
  // unavailable dates
  repeated google.type.Date unavailable_dates = 2;
}

// get available time ranges params
message GetEvaluationAvailableTimeParams {
  // business_id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet 和选择的 service 信息
  repeated PetServices pet_services = 2;
  // start date
  google.type.Date date = 3;

  // pet 和选择的 service/evaluation 信息
  message PetServices {
    // pet id
    int64 pet_id = 1;
    // pet selected evaluations
    repeated int64 evaluation_ids = 2;
  }
}

// get available time ranges result
message GetEvaluationAvailableTimeResult {
  // available time ranges
  repeated DayAvailableTimeRanges day_time_ranges = 1;
  // available time ranges for a certain day
  message DayAvailableTimeRanges {
    // date
    google.type.Date date = 1;
    // available time range list
    repeated moego.models.online_booking.v1.DayTimeRangeDef time_range = 2;
  }
}

// appointment checker service
service AppointmentCheckerService {
  // check save appointment
  rpc CheckSaveAppointment(CheckSaveAppointmentParams) returns (CheckSaveAppointmentResult);
  // get available dates
  rpc GetAvailableDates(GetAvailableDatesParams) returns (GetAvailableDatesResult);
  // get available time ranges
  rpc GetEvaluationAvailableTime(GetEvaluationAvailableTimeParams) returns (GetEvaluationAvailableTimeResult);
}
