syntax = "proto3";

package moego.api.organization.v1;

import "moego/models/organization/v1/close_date_defs.proto";
import "moego/models/organization/v1/location_defs.proto";
import "moego/models/organization/v1/location_models.proto";
import "moego/models/organization/v1/working_hour_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/organization/v1;organizationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.organization.v1";

// params to create a location
message CreateLocationParams {
  // location to create
  models.organization.v1.CreateLocationDef location = 1 [(validate.rules).message = {required: true}];
}

// result to create a location
message CreateLocationResult {
  // location id
  int64 id = 1;
}

// params to update a location
message UpdateLocationParams {
  // location to update
  models.organization.v1.UpdateLocationDef location = 1 [(validate.rules).message = {required: true}];
}

// result to update a location
message UpdateLocationResult {
  // update result
  bool success = 1;
}

// params to get location list, currently only support get all
message GetLocationListParams {}

// result to get location list
message GetLocationListResult {
  // location list
  repeated models.organization.v1.LocationBriefView location = 1;
}

// params to get location detail
message GetLocationDetailParams {
  // location id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// result to get location detail
message GetLocationDetailResult {
  // location detail
  models.organization.v1.LocationModel location = 1;
}

// update online preference request
message UpdateOnlinePreferenceParams {
  // online preference
  models.organization.v1.UpdateOnlinePreferenceDef online_preference = 1 [(validate.rules).message = {required: true}];
}

// update online preference response
message UpdateOnlinePreferenceResult {
  // update result
  bool success = 1;
}

// get working hours list
message GetWorkingHoursListParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get working hours list response
message GetWorkingHoursListResult {
  // working hours list
  models.organization.v1.WorkingHoursDef working_hours = 1;
}

// update working hours
message UpdateWorkingHoursParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // working hours to update
  models.organization.v1.WorkingHoursDef working_hours = 2 [(validate.rules).message = {required: true}];
}

// update working hours response
message UpdateWorkingHoursResult {
  // update result
  bool success = 1;
}

// add closed date
message AddClosedDateParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // closed date to add
  models.organization.v1.CloseDateDef closed_date = 2 [(validate.rules).message = {required: true}];
}

// add closed date response
message AddClosedDateResult {
  // closed date id
  int64 id = 1;
}

// update closed date
message UpdateClosedDateParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // closed date to update
  models.organization.v1.CloseDateDef closed_date = 2 [(validate.rules).message = {required: true}];
}

// update closed date response
message UpdateClosedDateResult {
  // update result
  bool success = 1;
}

// delete closed date
message DeleteClosedDateParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // closed date id
  int64 id = 2 [(validate.rules).int64 = {gt: 0}];
}

// delete closed date response
message DeleteClosedDateResult {
  // delete result
  bool success = 1;
}

// get closed date list
message GetClosedDateListParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get closed date list response
message GetClosedDateListResult {
  // closed date list
  repeated models.organization.v1.CloseDateDef closed_date = 1;
}

// get closed holiday list
message GetClosedHolidayListParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // year
  optional int32 year = 2 [(validate.rules).int32 = {gt: 0}];
}

// get closed holiday list response
message GetClosedHolidayListResult {
  // closed holiday list
  repeated models.organization.v1.CloseDateDef closed_date = 1;
}

// edit closed holiday
message EditClosedHolidayParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // start date
  string start_date = 2 [(validate.rules).string.len = 10];
  // end date
  string end_date = 3 [(validate.rules).string.len = 10];
  // close or open
  bool is_closed = 4;
}

// edit closed holiday response
message EditClosedHolidayResult {
  // edit result
  bool success = 1;
}

// mass edit closed holiday
message MassEditClosedHolidayParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // closed holiday list
  repeated models.organization.v1.ClosedHolidayMassEditDef closed_holiday = 2;
}

// mass edit closed holiday response
message MassEditClosedHolidayResult {
  // edit result
  bool success = 1;
}

// switch business params
message SwitchBusinessParams {
  // to business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// switch business result
message SwitchBusinessResult {}

// business service
service BusinessService {
  // create a location
  rpc CreateLocation(CreateLocationParams) returns (CreateLocationResult) {}
  // update a location
  rpc UpdateLocation(UpdateLocationParams) returns (UpdateLocationResult) {}
  // get location list
  rpc GetLocationList(GetLocationListParams) returns (GetLocationListResult) {}
  // get location detail
  rpc GetLocationDetail(GetLocationDetailParams) returns (GetLocationDetailResult) {}
  // update online preference
  rpc UpdateOnlinePreference(UpdateOnlinePreferenceParams) returns (UpdateOnlinePreferenceResult) {}
  // get working hours list
  rpc GetWorkingHoursList(GetWorkingHoursListParams) returns (GetWorkingHoursListResult) {}
  // update working hours
  rpc UpdateWorkingHours(UpdateWorkingHoursParams) returns (UpdateWorkingHoursResult) {}
  // add closed date
  rpc AddClosedDate(AddClosedDateParams) returns (AddClosedDateResult) {}
  // update closed date
  rpc UpdateClosedDate(UpdateClosedDateParams) returns (UpdateClosedDateResult) {}
  // delete closed date
  rpc DeleteClosedDate(DeleteClosedDateParams) returns (DeleteClosedDateResult) {}
  // get closed date list
  rpc GetClosedDateList(GetClosedDateListParams) returns (GetClosedDateListResult) {}
  // get closed holiday list
  rpc GetClosedHolidayList(GetClosedHolidayListParams) returns (GetClosedHolidayListResult) {}
  // edit closed holiday
  rpc EditClosedHoliday(EditClosedHolidayParams) returns (EditClosedHolidayResult) {}
  // mass edit closed holiday
  rpc MassEditClosedHoliday(MassEditClosedHolidayParams) returns (MassEditClosedHolidayResult) {}
  // switch business
  rpc SwitchBusiness(SwitchBusinessParams) returns (SwitchBusinessResult) {}
}
