syntax = "proto3";

package moego.api.online_booking.v1;

import "moego/models/online_booking/v1/ob_customize_care_type_defs.proto";
import "moego/models/online_booking/v1/ob_customize_care_type_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.online_booking.v1";

// Create booking care type params
message CreateBookingCareTypeParams {
  // business id
  int64 business_id = 1;
  // booking care type def
  moego.models.online_booking.v1.CreateBookingCareTypeDef booking_care_type = 2;
}

// Create booking care type result
message CreateBookingCareTypeResult {
  // the created booking care type
  moego.models.online_booking.v1.BookingCareTypeView booking_care_type = 1;
}

// Update booking care type params
message UpdateBookingCareTypeParams {
  // business id
  int64 business_id = 1;
  // booking care type def
  moego.models.online_booking.v1.UpdateBookingCareTypeDef booking_care_type = 2;
}

// Update booking care type result
message UpdateBookingCareTypeResult {
  // the updated booking care type
  moego.models.online_booking.v1.BookingCareTypeView booking_care_type = 1;
}

// List booking care types params
message ListBookingCareTypesParams {
  // business id
  int64 business_id = 1;
}

// List booking care types result
message ListBookingCareTypesResult {
  // the booking care type list
  repeated moego.models.online_booking.v1.BookingCareTypeView booking_care_types = 1;
}

// Get booking care type params
message GetBookingCareTypeParams {
  // the unique id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2;
}

// Get booking care type result
message GetBookingCareTypeResult {
  // the booking care type
  moego.models.online_booking.v1.BookingCareType booking_care_type = 1;
}

// Delete booking care type params
message DeleteBookingCareTypeParams {
  // the unique id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2;
}

// Delete booking care type result
message DeleteBookingCareTypeResult {}

// sort booking care type params
message SortBookingCareTypeParams {
  // business id
  int64 business_id = 1;
  // booking care type ids
  repeated int64 booking_care_type_ids = 2;
}

// sort booking care type result
message SortBookingCareTypeResult {
  // the sorted booking care type
  repeated moego.models.online_booking.v1.BookingCareTypeView booking_care_types = 1;
}

// Booking care type service
service BookingCareTypeService {
  // Create booking care type
  rpc CreateBookingCareType(CreateBookingCareTypeParams) returns (CreateBookingCareTypeResult);
  // Update booking care type
  rpc UpdateBookingCareType(UpdateBookingCareTypeParams) returns (UpdateBookingCareTypeResult);
  // List booking care types
  rpc ListBookingCareTypes(ListBookingCareTypesParams) returns (ListBookingCareTypesResult);
  // Get booking care type
  rpc GetBookingCareType(GetBookingCareTypeParams) returns (GetBookingCareTypeResult);
  // Delete booking care type
  rpc DeleteBookingCareType(DeleteBookingCareTypeParams) returns (DeleteBookingCareTypeResult);
  // Sort booking care type
  rpc SortBookingCareType(SortBookingCareTypeParams) returns (SortBookingCareTypeResult);
}
