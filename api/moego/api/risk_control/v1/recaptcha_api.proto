// @since 2023-09-05 17:03:16
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.risk_control.v1;

import "moego/models/risk_control/v1/recaptcha_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/risk_control/v1;riskcontrolapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.risk_control.v1";

// challenge request
message RecaptchaChallengeRequest {
  // the recaptcha challenge def
  moego.models.risk_control.v1.RecaptchaDef recaptcha = 1 [(validate.rules).message = {required: true}];
}

// challenge response
message RecaptchaChallengeResponse {}

// the recaptcha service
service RecaptchaService {
  // challenge
  rpc Challenge(RecaptchaChallengeRequest) returns (RecaptchaChallengeResponse);
}
