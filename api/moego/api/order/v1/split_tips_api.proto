syntax = "proto3";

package moego.api.order.v1;

import "google/type/decimal.proto";
import "google/type/money.proto";
import "moego/models/order/v1/order_enums.proto";
import "moego/models/order/v1/order_models.proto";
import "moego/models/order/v1/refund_order_models.proto";
import "moego/models/order/v1/split_tips_defs.proto";
import "moego/models/order/v1/split_tips_enum.proto";
import "moego/models/order/v1/split_tips_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/order/v1;orderapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.order.v1";

// customized tip config
message CustomizedTipConfigRequest {
  // staff id
  int64 staff_id = 1;
  // tips amount
  optional double amount = 2 [(validate.rules).double = {gte: 0}];
  // tips percentage
  optional int32 percentage = 3 [(validate.rules).int32 = {
    gte: 0
    lte: 100
  }];
}

// save tip split request
message SaveSplitTipsRequest {
  // order id
  int64 order_id = 1;
  // tip split method
  moego.models.order.v1.SplitTipsMethod split_method = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // customized type: amount/percentage
  optional moego.models.order.v1.CustomizedTipType customized_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // customized tip config list
  repeated CustomizedTipConfigRequest customized_config = 4;
  // tips to business.
  optional google.type.Decimal business_tip_amount = 5;
}

// save tip split response
message SaveSplitTipsResponse {
  // save result
  bool success = 1;
}

// GetTipsSplitParams
message GetTipsSplitParams {
  // source id
  int64 source_id = 1 [(validate.rules).int64 = {gt: 0}];
  // source type
  models.order.v1.OrderSourceType source_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [
      0,
      1,
      3
    ]
  }];
  // business id
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// GetTipsSplitResult
message GetTipsSplitResult {
  // total collected tips from completed orders
  models.order.v1.TipsSplitModel tips_split = 1;
  // tips split records for each staff
  repeated models.order.v1.TipsSplitDetailView tips_split_detail = 2;
  // completed order list
  repeated models.order.v1.OrderModelV1 orders = 3;
  // Refunded orders.
  repeated models.order.v1.RefundOrderModel refund_orders = 4;
}

// EditStaffAndTipsSplitParams
message EditStaffAndTipsSplitParams {
  // source id
  int64 source_id = 1 [(validate.rules).int64 = {gt: 0}];
  // source type
  models.order.v1.OrderSourceType source_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [
      0,
      1,
      3
    ]
  }];

  // edit staff
  repeated models.order.v1.EditStaffDef edit_staffs = 3;

  // tips split config
  optional models.order.v1.TipsSplitModel.TipsSplitConfig tips_split_config = 4;

  // business id
  int64 business_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// PreviewEditApptLevelTipsSplitResult
message PreviewEditTipsSplitResult {
  // split config
  models.order.v1.TipsSplitModel.TipsSplitConfig split_config = 1;
}

// EditTipsSplitResult
message EditTipsSplitResult {
  // tips split records for each staff
  repeated models.order.v1.TipsSplitDetailModel tips_split_details = 1;
  // tips split for business.
  google.type.Money business_tip_amount = 2;
}

// GetTipsSplitChangedStatusParams
message GetTipsSplitChangedStatusParams {
  // source id
  int64 source_id = 1 [(validate.rules).int64 = {gt: 0}];
  // source type
  models.order.v1.OrderSourceType source_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// GetTipsSplitChangedStatusResult
message GetTipsSplitChangedStatusResult {
  // indicate tips split changed status
  bool is_changed = 1;
}

// ClearTipsSplitChangedStatusParams
message ClearTipsSplitChangedStatusParams {
  // source id
  int64 source_id = 1 [(validate.rules).int64 = {gt: 0}];
  // source type
  models.order.v1.OrderSourceType source_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// ClearTipsSplitChangedStatusResult
message ClearTipsSplitChangedStatusResult {}

// split tip api
service SplitTipsService {
  // save tip split api
  rpc SaveTipSplit(SaveSplitTipsRequest) returns (SaveSplitTipsResponse);
  // get tips details for source type
  rpc GetTipsSplitDetails(GetTipsSplitParams) returns (GetTipsSplitResult);
  // preview edit staff and split tips
  rpc PreviewEditTipsSplit(EditStaffAndTipsSplitParams) returns (PreviewEditTipsSplitResult);
  // update edit staff and split tips
  rpc EditTipsSplit(EditStaffAndTipsSplitParams) returns (EditTipsSplitResult);
  // get tips split changed status
  rpc GetTipsSplitChangedStatus(GetTipsSplitChangedStatusParams) returns (GetTipsSplitChangedStatusResult);
  // clear tips split changed status
  rpc ClearTipsSplitChangedStatus(ClearTipsSplitChangedStatusParams) returns (ClearTipsSplitChangedStatusResult);
}
