// @since 2024-07-29 15:20:58
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.ai_assistant.v1;

import "moego/models/ai_assistant/v1/natural_language_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/ai_assistant/v1;aiassistantapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.ai_assistant.v1";

// DetectLanguageParams
message DetectLanguageParams {
  // content to detect
  string content = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 1000
  }];
}

// DetectLanguageResult
message DetectLanguageResult {
  // detected result
  optional moego.models.ai_assistant.v1.Language language = 1;
}

// the natural_language service
service NaturalLanguageService {
  // detect language
  rpc DetectLanguage(DetectLanguageParams) returns (DetectLanguageResult);
}
