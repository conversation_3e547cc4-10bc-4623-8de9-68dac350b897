syntax = "proto3";

package moego.api.engagement.v1;

import "moego/models/engagement/v1/email_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/engagement/v1;engagementapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.engagement.v1";

// EmailService is a service for sending emails.
service EmailService {
  // GetSenderEmail gets the sender email.
  rpc GetSenderEmail(GetSenderEmailParams) returns (GetSenderEmailResult) {}
  // SendConfirmEmail sends a confirmation email.
  rpc SendConfirmEmail(SendConfirmEmailParams) returns (SendConfirmEmailResult) {}
  // ConfirmSenderEmail confirms the sender email.
  rpc ConfirmSenderEmail(ConfirmSenderEmailParams) returns (ConfirmSenderEmailResult) {}
  // FetchDNSConfigs fetches the DNS configurations.
  rpc FetchDNSConfigs(FetchDNSConfigsParams) returns (FetchDNSConfigsResult) {}
  // VerifySenderEmail
  rpc VerifySenderEmail(VerifySenderEmailParams) returns (VerifySenderEmailResult) {}
  // SaveSenderEmail
  rpc SaveSenderEmail(SaveSenderEmailParams) returns (SaveSenderEmailResult) {}
}

// GetSenderEmailParams is a request to get the sender email.
message GetSenderEmailParams {}

// GetSenderEmailResult is a response to get the sender email.
message GetSenderEmailResult {
  // sender_email is the sender email.
  optional moego.models.engagement.v1.SenderEmailModel sender_email = 1;
  // SenderEmailUsageType
  moego.models.engagement.v1.SenderEmailUsageType sender_email_usage_type = 2;
}

// SendConfirmEmailParams is a request to send a confirmation email.
message SendConfirmEmailParams {
  // email address
  string email = 1;
}

// SendConfirmEmailResult is a response to send a confirmation email.
message SendConfirmEmailResult {
  // success is true if the email was sent successfully.
  bool success = 1;
  // error is the error message if the email was not sent successfully.
  optional string error = 2;
}

// ConfirmSenderEmailParams is a request to confirm the sender email.
message ConfirmSenderEmailParams {
  // email address
  string email = 1;
  // confirmation code
  string confirmation_code = 2;
}

// ConfirmSenderEmailResult is a response to confirm the sender email.
message ConfirmSenderEmailResult {
  // success is true if the email was confirmed successfully.
  bool success = 1;
  // error is the error message if the email was not confirmed successfully.
  optional string error = 2;
}

// FetchDNSConfigsParams is a request to fetch the DNS configurations.
message FetchDNSConfigsParams {
  // email address
  optional string email = 1;
}

// FetchDNSConfigsResult is a response to fetch the DNS configurations.
message FetchDNSConfigsResult {
  // dns_records is the DNS records.
  repeated moego.models.engagement.v1.DNSRecordModel dns_records = 1;
}

// VerifySenderEmailParams is a request to verify the sender email.
message VerifySenderEmailParams {
  // email address, 传了的话就校验指定 email, 没传的话校验 business 当前 email
  optional string email = 1;
}

// VerifySenderEmailResult is a response to verify the sender email.
message VerifySenderEmailResult {
  // success is true if the email was verified successfully.
  bool success = 1;
  // error is the error message if the email was not verified successfully.
  optional string error = 2;
}

// SaveSenderEmailRequest is a request to save the sender email.
message SaveSenderEmailParams {
  // email address
  optional string email = 1;
  // name
  optional string name = 2;
  // type: default 时上面两个字段可以不传，否则需要传 email 和 name
  moego.models.engagement.v1.SenderEmailUsageType type = 3;
}

// SaveSenderEmailResponse is a response to save the sender email.
message SaveSenderEmailResult {
  // success is true if the email was saved successfully.
  bool success = 1;
  // error is the error message if the email was not saved successfully.
  optional string error = 2;
  // sender_email is the sender email.
  optional moego.models.engagement.v1.SenderEmailModel sender_email = 3;
}
