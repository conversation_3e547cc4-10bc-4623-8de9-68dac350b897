syntax = "proto3";

package moego.api.agreement.v1;

import "google/protobuf/empty.proto";
import "moego/models/agreement/v1/agreement_enums.proto";
import "moego/models/agreement/v1/agreement_models.proto";
import "moego/utils/v1/id_messages.proto";
import "moego/utils/v1/status_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/agreement/v1;agreementapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.agreement.v1";

// CreateAgreementRequest
message AddAgreementRequest {
  // signed policy, see definition in SignedPolicy
  moego.models.agreement.v1.SignedPolicy signed_policy = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // apply for services type, see the enum definition in ServiceType
  // notes: service_types is a combined value, For example:
  //   if you want to apply the agreement to grooming and boarding services,
  //   the service_types value is 3, logical OR result of enumeration values for grooming(1) and boarding(2)
  int32 service_types = 2 [(validate.rules).int32 = {gt: 0}];
  // agreement title
  string agreement_title = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // agreement content
  string agreement_content = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 1048576
  }];
}

// UpdateAgreementRequest
message UpdateAgreementRequest {
  // agreement id
  int64 id = 1;
  // 0: sign for first, 1: sign for each, 2: allow not to sign.
  optional moego.models.agreement.v1.SignedPolicy signed_policy = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // agreement title
  optional string agreement_title = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // agreement content
  optional string agreement_content = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 1048576
  }];
  // template for send sms
  optional string sms_template = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 65536
  }];
  // email template title
  optional string email_template_title = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // email template body
  optional string email_template_body = 7 [(validate.rules).string = {
    min_len: 1
    max_len: 131072
  }];
  // whether to update last_required_time
  optional bool update_last_required_time = 8;
}

// UpdateServiceTypeRequest
message UpdateServiceTypeRequest {
  // agreement id
  int64 id = 1;
  // services type, see the enum definition in ServiceType
  moego.models.agreement.v1.ServiceType service_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // true is set the service_type, false is cancel the service_type
  bool set_or_cancel = 3;
}

// agreement model list
message GetAgreementListResponse {
  // agreement list
  repeated moego.models.agreement.v1.AgreementModelSimpleView agreement_simple_view = 1;
}

// GetAgreementSignStatusListRequest
message GetAgreementSignStatusListRequest {
  // customer id
  int64 customer_id = 1;
  // target id
  optional int64 target_id = 2;
  // services type, see the enum definition in ServiceType
  optional moego.models.agreement.v1.ServiceType service_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// GetAgreementSignStatusListResponse
message GetAgreementSignStatusListResponse {
  // agreement sign status view list
  repeated moego.models.agreement.v1.AgreementSignStatusView agreement_status_view = 1;
}

// DeleteAgreementResponse
message DeleteAgreementResponse {
  // number of delete
  int32 number = 1;
}

// Get agreement content list by company params
message GetAgreementContentListByCompanyParams {
  // not support yet, ignore this field
  repeated int64 business_ids = 1 [(validate.rules).repeated = {ignore_empty: true}];
  // agreement id list
  repeated int64 ids = 2;
  // status: normal, deleted
  optional moego.utils.v1.Status status = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service type, see definition in ServiceType
  optional int32 service_types = 4 [(validate.rules).int32 = {gt: 0}];
}

// Get agreement content list by company result
message GetAgreementContentListByCompanyResult {
  // agreement list
  repeated moego.models.agreement.v1.AgreementModelContentView agreement_content_view = 1;
}

// QueryCompanyAgreementInfoListParams
message QueryCompanyAgreementInfoListParams {
  // service_type
  optional string service_type = 1 [(validate.rules).string = {
    max_len: 256
    ignore_empty: true
  }];
}

// QueryCompanyAgreementInfoListResult
message QueryCompanyAgreementInfoListResult {
  //see the result of GET /business/agreement/list, BusinessAgreementDTO
  message AgreementInfo {
    // agreement id
    int64 id = 1;
    // agreement title
    string agreement_header = 2 [(validate.rules).string = {
      min_len: 1
      max_len: 256
    }];
    // agreement content
    string agreement_content = 3 [(validate.rules).string = {
      min_len: 1
      max_len: 1048576
    }];
    // create time
    int32 create_time = 4;
    // update time
    int32 update_time = 5;
    // book online available, 1 for true, 0 for false
    int32 book_online_available = 6;
    // agreement required type,1 first time booking, 2 every booking, 3 not required
    int32 agreement_required_type = 7;
    // business id
    int64 business_id = 8;
  }
  // agreement_info_list
  repeated AgreementInfo agreement_info_list = 1;
}

// Agreement API
service AgreementService {
  // get one agreement
  rpc GetAgreement(moego.utils.v1.Id) returns (moego.models.agreement.v1.AgreementModel);

  // get agreement list
  rpc GetAgreementList(google.protobuf.Empty) returns (GetAgreementListResponse);

  // get agreement list for whether need to sign
  rpc GetAgreementSignStatusList(GetAgreementSignStatusListRequest) returns (GetAgreementSignStatusListResponse);

  // create an agreement
  rpc AddAgreement(AddAgreementRequest) returns (moego.models.agreement.v1.AgreementModel);

  // update an agreement
  rpc UpdateAgreement(UpdateAgreementRequest) returns (moego.models.agreement.v1.AgreementModel);

  // update agreement service type
  rpc UpdateAgreementServiceType(UpdateServiceTypeRequest) returns (moego.models.agreement.v1.AgreementModelSimpleView);

  // delete an agreement
  rpc DeleteAgreement(moego.utils.v1.Id) returns (DeleteAgreementResponse);

  // get agreement content list by company
  rpc GetAgreementContentListByCompany(GetAgreementContentListByCompanyParams) returns (GetAgreementContentListByCompanyResult);

  // query company agreement info list
  rpc QueryCompanyAgreementInfoList(QueryCompanyAgreementInfoListParams) returns (QueryCompanyAgreementInfoListResult);
}
