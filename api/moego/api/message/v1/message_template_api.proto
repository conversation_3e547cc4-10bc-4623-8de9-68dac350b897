// @since 2023-12-07 11:36:34
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.message.v1;

import "moego/models/message/v1/message_template_defs.proto";
import "moego/models/message/v1/message_template_enums.proto";
import "moego/models/message/v1/message_template_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/message/v1;messageapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.message.v1";

// check message template name exist params
message CheckMessageTemplateNameExistParams {
  // template name
  string template_name = 1 [(validate.rules).string = {max_len: 30}];
}

// check message template name exist result
message CheckMessageTemplateNameExistResult {
  // exist
  bool exist = 1;
}

// create message_template params
message CreateMessageTemplateParams {
  // the message_template def
  moego.models.message.v1.MessageTemplateDef message_template_def = 1 [(validate.rules).message = {required: true}];
}

// create message_template result
message CreateMessageTemplateResult {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get message template params
message GetMessageTemplateParams {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get message template result
message GetMessageTemplateResult {
  // the message_template detail view
  moego.models.message.v1.MessageTemplateDetailView message_template_detail_view = 1;
}

// update message template params
message UpdateMessageTemplateParams {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // the message_template def
  moego.models.message.v1.MessageTemplateDef message_template_def = 2 [(validate.rules).message = {required: true}];
}

// update message_template result
message UpdateMessageTemplateResult {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete message template params
message DeleteMessageTemplateParams {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete message_template result
message DeleteMessageTemplateResult {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get message template params
// Return customize only when types is empty and with_system is null.
message GetMessageTemplatesParams {
  // keyword template name
  optional string keyword = 1 [(validate.rules).string = {max_len: 30}];
  // if results contains system templates. Use types instead.
  optional bool with_system = 2 [deprecated = true];
  // list of message template type enum.
  // In order to be compatible with older versions, customize templates returned when empty.
  repeated moego.models.message.v1.MessageTemplateType types = 3 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// get message template holders result
message GetMessageTemplatesResult {
  // message template simple views
  repeated moego.models.message.v1.MessageTemplateSimpleView message_template_simple_views = 1;
}

// get message template placeholders params
message GetMessageTemplatePlaceholdersParams {
  // keyword template placeholder name
  optional string keyword = 1 [(validate.rules).string = {max_len: 30}];
  // use case enum, default for USE_CASE_SAVED_REPLY
  optional moego.models.message.v1.MessageTemplateUseCase use_case = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// get message template holders result
message GetMessageTemplatePlaceholdersResult {
  // message template holders
  repeated moego.models.message.v1.MessageTemplatePlaceholderSimpleView message_template_placeholder_simple_views = 1;
}

// get rendered message params
message GetRenderedMessageParams {
  // message template id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // customer id
  int64 customer_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get rendered message result
message GetRenderedMessageResult {
  // message after rendering
  string rendered_message = 1;
}

// the message template service
service MessageTemplateService {
  // check message template name exist
  rpc CheckMessageTemplateNameExist(CheckMessageTemplateNameExistParams) returns (CheckMessageTemplateNameExistResult);

  // create message template
  rpc CreateMessageTemplate(CreateMessageTemplateParams) returns (CreateMessageTemplateResult);

  // get message template
  rpc GetMessageTemplate(GetMessageTemplateParams) returns (GetMessageTemplateResult);

  // update message template
  rpc UpdateMessageTemplate(UpdateMessageTemplateParams) returns (UpdateMessageTemplateResult);

  // delete message template
  rpc DeleteMessageTemplate(DeleteMessageTemplateParams) returns (DeleteMessageTemplateResult);

  // get message templates
  rpc GetMessageTemplates(GetMessageTemplatesParams) returns (GetMessageTemplatesResult);

  // get message template holders
  rpc GetMessageTemplatePlaceholders(GetMessageTemplatePlaceholdersParams) returns (GetMessageTemplatePlaceholdersResult);

  // get rendered message
  rpc GetRenderedMessage(GetRenderedMessageParams) returns (GetRenderedMessageResult);
}
