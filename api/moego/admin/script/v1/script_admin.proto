// @since 2023-05-23 11:43:54
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.script.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/script/v1;scriptapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.script.v1";

// wash business session params
message WashBusinessSessionParams {
  // start account id
  int64 start_account_id = 1;
  // end account id
  int64 end_account_id = 2;
}

// wash business session result
message WashBusinessSessionResult {}

// script service
service ScriptService {
  // wash business session
  rpc WashBusinessSession(WashBusinessSessionParams) returns (WashBusinessSessionResult);
}
