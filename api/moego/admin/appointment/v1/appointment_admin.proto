syntax = "proto3";

package moego.admin.appointment.v1;

import "google/protobuf/struct.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.appointment.v1";

// query appointments params
message QueryAppointmentListParams {
  //查询方式一
  // the id id
  optional int64 id = 1;

  //查询方式二
  //the business id
  optional int64 business_id = 2;
  //the appointment date
  optional string appointment_date = 3;

  // the pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// query appointments result
message QueryAppointmentListResult {
  // the appointments
  repeated google.protobuf.Struct appointments = 1;
  // the appointments
  map<int64, moego.models.business_customer.v1.BusinessCustomerNameStatusView> customers = 2;
  // the pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// delete appointments params
message DeleteAppointmentParams {
  // the appointment id
  int64 appointment_id = 1 [(validate.rules).int64.gt = 0];
}

// delete appointments result
message DeleteAppointmentResult {}

// the appointment service
service AppointmentAdminService {
  // query appointments list
  rpc QueryAppointmentList(QueryAppointmentListParams) returns (QueryAppointmentListResult);
  // delete appointment
  rpc DeleteAppointment(DeleteAppointmentParams) returns (DeleteAppointmentResult);
}
