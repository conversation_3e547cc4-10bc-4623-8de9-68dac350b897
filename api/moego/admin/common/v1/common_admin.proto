// @since 2023-07-06 14:28:04
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.common.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/common/v1;commonapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.common.v1";

// describe enums params
message DescribeEnumsParams {}

// describe enums result value
message EnumOption {
  // enum value
  int32 value = 1;
  // enum label
  string label = 2;
}

// status option
message StatusOption {
  // status icon
  string icon = 1;
  // status label
  string label = 2;
  // status color
  string color = 3;
  // status value
  int32 value = 4;
}

// enum model
message EnumModel {
  // enum name
  string name = 1;
  // enum options
  repeated EnumOption options = 2;
  // label map
  map<int32, string> label_map = 3;
  // status map
  map<int32, StatusOption> status_map = 4;
}

// describe enums result
message DescribeEnumsResult {
  // message module
  message MessageModule {
    // target type
    EnumModel target_type = 1;
    // auto message type
    EnumModel auto_message_type = 2;
    // source
    EnumModel source = 3;
    // reminder type
    EnumModel reminder_type = 4;
    // send status
    EnumModel send_status = 5;
    // message type
    EnumModel message_type = 6;
    // sender type
    EnumModel sender_type = 7;
    // method
    EnumModel method = 8;
  }

  // grooming module
  message GroomingModule {
    // appointment status
    EnumModel appointment_status = 1;
    // source
    EnumModel source = 2;
  }

  // common module
  message CommonModule {
    // common status
    EnumModel status = 1;
    // bool status
    EnumModel bool = 2;
  }

  // message module
  MessageModule message = 1;
  // grooming module
  GroomingModule grooming = 2;
  // common module
  CommonModule common = 3;
}

// the common service
service CommonService {
  // describe enums
  rpc DescribeEnums(DescribeEnumsParams) returns (DescribeEnumsResult);
}
