// @since 2024-07-11 14:21:59
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.ai_assistant.v2;

import "moego/models/ai_assistant/v2/conversation_models.proto";
import "moego/utils/v2/pagination_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/ai_assistant/v2;aiassistantapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.ai_assistant.v2";

// ListConversationsRequest
message ListConversationsParams {
  // filter by company id
  optional int64 company_id = 1;
  // filter by business id
  optional int64 business_id = 2;
  // filter by customer id
  optional int64 customer_id = 3;

  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// ListConversationsResult
message ListConversationsResult {
  // conversations
  repeated moego.models.ai_assistant.v2.ConversationModel conversations = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// the chat_assistant service
service ChatAssistantService {
  // ListConversations
  rpc ListConversations(ListConversationsParams) returns (ListConversationsResult);
}
