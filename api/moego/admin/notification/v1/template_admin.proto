syntax = "proto3";

package moego.admin.notification.v1;

import "google/protobuf/empty.proto";
import "moego/admin/notification/v1/campaign_admin.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/notification/v1;notificationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.notification.v1";

// the notification template service
service TemplateService {
  // create notification template
  rpc CreateNotificationTemplate(CreateNotificationTemplateParams) returns (google.protobuf.Empty);
  // update notification template
  rpc UpdateNotificationTemplate(UpdateNotificationTemplateParams) returns (google.protobuf.Empty);
  // delete notification template
  rpc DeleteNotificationTemplate(DeleteNotificationTemplateParams) returns (google.protobuf.Empty);
  // search notification templates
  rpc SearchNotificationTemplates(SearchNotificationTemplatesParams) returns (SearchNotificationTemplatesResult);
}

// CreateNotificationTemplatesParams
message CreateNotificationTemplateParams {
  // marketing campaign notification
  MarketingCampaignNotification notification = 1 [(validate.rules).message = {required: true}];
}

// UpdateNotificationTemplateParams
message UpdateNotificationTemplateParams {
  // marketing campaign notification
  MarketingCampaignNotification notification = 1 [(validate.rules).message = {required: true}];
}

// DeleteNotificationTemplateParams
message DeleteNotificationTemplateParams {
  // id
  int64 id = 1;
}

// SearchNotificationTemplatesParams
message SearchNotificationTemplatesParams {
  // author_name
  optional string author = 1 [(validate.rules).string = {min_len: 1}];
  // title
  optional string title = 2 [(validate.rules).string = {min_len: 1}];
  // body
  optional string body = 3 [(validate.rules).string = {min_len: 1}];
  // mobile title
  optional string mobile_title = 4 [(validate.rules).string = {min_len: 1}];
  // mobile body
  optional string mobile_body = 5 [(validate.rules).string = {min_len: 1}];
  // extra
  optional string extra = 6 [(validate.rules).string = {min_len: 1}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 7 [(validate.rules).message = {required: true}];
}

// SearchNotificationTemplatesResult
message SearchNotificationTemplatesResult {
  // templates
  repeated MarketingCampaignNotification templates = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}
