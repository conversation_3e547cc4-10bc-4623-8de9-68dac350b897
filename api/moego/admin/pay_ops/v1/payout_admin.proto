// @since 2-23-08-28
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.pay_ops.v1;

import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/pay_ops/v1;payopsapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.pay_ops.v1";

// payout get request
message PayoutGetRequest {}

// stripe account model payout rerviewing view
message StripeAccountReviewing {
  // business id
  int64 business_id = 1;
  // stripe account id
  string stripe_account_id = 2;
  // payout status
  string payout_schedule_status = 3;
  // email
  string email = 4;
}

// payout get response
message PayoutGetResponse {
  // stripe account list
  repeated StripeAccountReviewing stripe_account_list = 1;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// review payout schedule applying request
message ReviewPayoutScheduleApplyingRequest {
  // stripe account id
  string stripe_account_id = 1 [(validate.rules).string = {
    min_len: 10
    max_len: 50
  }];
  // review ops
  string review_ops = 2 [(validate.rules).string = {
    in: [
      "REJECT",
      "APPROVE"
    ]
  }];
}

// payout service
service PayoutService {
  // list reviewing account
  rpc ListReviewingAccount(PayoutGetRequest) returns (PayoutGetResponse);

  // review payout schedule applying
  rpc ReviewPayoutScheduleApplying(ReviewPayoutScheduleApplyingRequest) returns (StripeAccountReviewing);
}
