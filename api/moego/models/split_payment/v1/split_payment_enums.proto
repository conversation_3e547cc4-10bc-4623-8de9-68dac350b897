syntax = "proto3";

package moego.models.split_payment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/split_payment/v1;splitpaymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.split_payment.v1";

// vendor
enum Vendor {
  // Unspecified
  VENDOR_UNSPECIFIED = 0;
  // Stripe
  STRIPE = 1;
  // Adyen
  ADYEN = 2;
}

// account type for split payment
// This account type is used to manage different split payment entities
enum AccountType {
  // Unspecified
  ACCOUNT_TYPE_UNSPECIFIED = 0;
  // platform account,moego
  PLATFORM = 1;
  // business account
  BUSINESS = 2;
  // staff account
  STAFF = 3;
  // Loan account
  LOAN = 4;
}

// split type
enum SplitType {
  // Unspecified
  SPLIT_TYPE_UNSPECIFIED = 0;
  // booking fee
  BOOKING_FEE = 1;
  // processing fee
  PROCESSING_FEE = 2;
  // tips
  TIPS = 3;
  // business revenue
  BUSINESS_REVENUE = 4;
  // loan repayment
  LOAN_REPAYMENT = 5;
}

// split entity type
enum SplitEntityType {
  // Unspecified
  SPLIT_ENTITY_TYPE_UNSPECIFIED = 0;
  // payment
  PAYMENT = 1;
  // refund
  REFUND = 2;
  // dispute
  DISPUTE = 3;
  // dispute reverse
  DISPUTE_REVERSE = 4;
  // PAYMENT_V2 对应svc-payment
  PAYMENT_V2 = 5;
  // REFUND_V2 对应svc-payment
  REFUND_V2 = 6;
}

// external type
enum ExternalType {
  // Unspecified
  EXTERNAL_TYPE_UNSPECIFIED = 0;
  // LOAN
  EXTERNAL_TYPE_LOAN = 1;
}
