syntax = "proto3";

package moego.models.business_customer.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/business_customer/v1/business_pet_metadata_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Business pet metadata model
message BusinessPetMetadataModel {
  // id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // metadata name
  models.business_customer.v1.BusinessPetMetadataName metadata_name = 3;
  // metadata value
  string metadata_value = 4;
  // extra json data, can customize additional metadata information.
  map<string, string> extra_json = 5;
  // sort number
  int32 sort = 6;
  // created at
  google.protobuf.Timestamp created_at = 7;
  // updated at
  google.protobuf.Timestamp updated_at = 8;
  // deleted at
  google.protobuf.Timestamp deleted_at = 9;
}

// Pet metadata view
message BusinessPetMetadataView {
  // id
  int64 id = 1;
  // metadata name
  models.business_customer.v1.BusinessPetMetadataName metadata_name = 2;
  // metadata value
  string metadata_value = 3;
  // extra json data, can customize additional metadata information.
  map<string, string> extra_json = 4;
  // sort number
  int32 sort = 5;
}
