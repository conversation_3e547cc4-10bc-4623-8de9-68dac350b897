syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Contact model for business customer.
// A business customer can have multiple contacts and at least one contact (which is a main contact). The main contact
// is used to identify the business customer, and is not deletable.
//
// Besides, a business customer can only set one phone number as primary among its all contacts to receive messages from
// business. By default the phone number in main contact is primary. Noted that the phone number of the main contact is
// always the unique identifier regardless of whether it is primary.
//
// Currently we don't support to set primary email. But maybe we will support it in the future?
message BusinessCustomerContactModel {
  // contact id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // business id, please do not use this field
  int64 business_id = 3 [deprecated = true];
  // customer id
  int64 customer_id = 4;

  // first name, may not be empty
  string first_name = 5;
  // last name, may not be empty
  string last_name = 6;
  // phone number, may not be empty
  string phone_number = 7;
  // email, may be empty
  string email = 9;
  // title
  string title = 10;

  // if the phone number is primary
  bool is_primary = 8;
  // if the email is primary, support in the future?
  //  bool is_primary_email = 11;

  // if this is main contact
  bool is_main_contact = 12;
  // if this contact is deleted
  bool deleted = 13;

  // CRM-3555  emergency contact
  // contact type, 1=main, 2=additional, 3=emergency
  int32 type = 14;
}

// the business customer contact public view
message BusinessCustomerContactPublicView {
  // contact id
  int64 id = 1;
  // business customer id
  int64 customer_id = 2;
  // phone number, which is required
  string phone_number = 3;
  // primary contact
  bool is_primary = 4;
}
