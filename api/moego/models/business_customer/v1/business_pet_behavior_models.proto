syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// pet behavior model
message BusinessPetBehaviorModel {
  // behavior id
  int64 id = 1;

  // behavior name
  string name = 2;

  // behavior sort. The larger the sort number, the higher the priority.
  int32 sort = 3;

  // if the pet behavior is deleted
  bool deleted = 4;
}
