syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// status of customer merge
enum CustomerMergeStatus {
  // unspecified
  CUSTOMER_MERGE_STATUS_UNSPECIFIED = 0;
  // no merge record
  NO_MERGE_RECORD = 1;
  // merging
  MERGING = 2;
  // merged
  MERGED = 3;
}

// duplication detect rule
enum DuplicationDetectRule {
  // unspecified
  DUPLICATION_DETECT_RULE_UNSPECIFIED = 0;
  // phone number
  PHONE_NUMBER = 1;
  // email
  EMAIL = 2;
  // customer name
  CUSTOMER_NAME = 3;
  // pet name and type breed
  PET_NAME_AND_TYPE_BREED = 4;
}
