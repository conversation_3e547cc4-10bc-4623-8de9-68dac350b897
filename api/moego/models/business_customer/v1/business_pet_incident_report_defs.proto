syntax = "proto3";

package moego.models.business_customer.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// create def for pet incident report
message BusinessPetIncidentReportCreateDef {
  // pet id list
  repeated int64 pet_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // incident timestamp
  google.protobuf.Timestamp incident_time = 2 [(validate.rules).timestamp.required = true];
  // incident type id
  int64 incident_type_id = 3 [(validate.rules).int64.gt = 0];
  // incident description
  string description = 4 [(validate.rules).string = {max_len: 50000}];
  // attachment url list
  repeated PetIncidentAttachmentDef attachment_files = 5 [(validate.rules).repeated = {max_items: 10}];
  // business id
  int64 business_id = 6 [(validate.rules).int64.gt = 0];
  // is staff injured
  bool is_staff_injured = 7;
  // is pet injured
  bool is_pet_injured = 8;
  // is vet visited
  bool is_vet_visited = 9;
}

// update def for pet incident report
message BusinessPetIncidentReportUpdateDef {
  // pet id list
  repeated int64 pet_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // incident timestamp
  google.protobuf.Timestamp incident_time = 2 [(validate.rules).timestamp.required = true];
  // incident type id
  int64 incident_type_id = 3 [(validate.rules).int64.gt = 0];
  // incident description
  string description = 4 [(validate.rules).string = {max_len: 50000}];
  // attachment url list
  repeated PetIncidentAttachmentDef attachment_files = 5 [(validate.rules).repeated = {max_items: 10}];
  // business id
  int64 business_id = 6 [(validate.rules).int64.gt = 0];
  // is staff injured
  bool is_staff_injured = 7;
  // is pet injured
  bool is_pet_injured = 8;
  // is vet visited
  bool is_vet_visited = 9;
}

// pet incident attachment def
message PetIncidentAttachmentDef {
  // attachment url
  string url = 1 [(validate.rules).string = {
    max_len: 255
    uri: true
  }];
  // attachment name
  string name = 2 [(validate.rules).string = {max_len: 255}];
}
