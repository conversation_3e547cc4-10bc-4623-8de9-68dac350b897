syntax = "proto3";

package moego.models.business_customer.v1;

import "google/type/date.proto";
import "moego/models/business_customer/v1/business_pet_vaccine_request_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// BusinessPetVaccineRequestCreateDef
message BusinessPetVaccineRequestCreateDef {
  // vaccine record id (vaccine binding id).
  // if not set, a new vaccine record will be requested to add.
  // if set, an existing vaccine record will be requested to update.
  optional int64 vaccine_record_id = 1 [(validate.rules).int64 = {gt: 0}];
  // vaccine id
  int64 vaccine_id = 2 [(validate.rules).int64 = {gt: 0}];
  // expiration date, optional
  optional google.type.Date expiration_date = 3;
  // document url
  repeated string document_urls = 4 [(validate.rules).repeated = {
    unique: true
    max_items: 20
    items: {
      string: {uri: true}
    }
  }];
}

// BusinessPetVaccineRequestUpdateDef
message BusinessPetVaccineRequestUpdateDef {
  // vaccine record id (vaccine binding id).
  // if set to 0, will not be related to a vaccine record
  optional int64 vaccine_record_id = 1 [(validate.rules).int64 = {gte: 0}];
  // vaccine id
  optional int64 vaccine_id = 2 [(validate.rules).int64 = {gt: 0}];
  // expiration date
  optional google.type.Date expiration_date = 3;
  // document url
  optional DocumentUrlList document_urls = 4;
  // status
  optional BusinessPetVaccineRequestModel.Status status = 5 [(validate.rules).enum = {
    not_in: [0]
  }];

  // document url list
  message DocumentUrlList {
    // document url
    repeated string urls = 4 [(validate.rules).repeated = {
      unique: true
      max_items: 20
      items: {
        string: {uri: true}
      }
    }];
  }
}
