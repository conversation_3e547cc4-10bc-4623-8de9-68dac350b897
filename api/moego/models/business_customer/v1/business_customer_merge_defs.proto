syntax = "proto3";

package moego.models.business_customer.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// merge relation def
message MergeRelationDef {
  // the id to be remained
  int64 target_id = 3 [(validate.rules).int64 = {gt: 0}];

  // ids to be merged into the target id
  repeated int64 source_ids = 4 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// merge relation def for business customer
message BusinessCustomerMergeRelationDef {
  // merge relation of customer
  MergeRelationDef customer_merge_relation = 1;
  // merge relation of pet, may be empty
  repeated MergeRelationDef pet_merge_relations = 2;
}
