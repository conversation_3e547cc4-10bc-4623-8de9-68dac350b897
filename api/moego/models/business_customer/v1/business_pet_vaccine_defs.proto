syntax = "proto3";

package moego.models.business_customer.v1;

import "moego/models/customer/v1/customer_pet_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// create def for pet vaccine
message BusinessPetVaccineCreateDef {
  // vaccine name
  string name = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 50
    // not blank
    pattern: ".*\\S+.*"
  }];

  // vaccine availability setting
  optional BusinessPetVaccineAvailabilityDef availability = 4;
}

// update def for pet vaccine
message BusinessPetVaccineUpdateDef {
  // vaccine name
  optional string name = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 50
    // not blank
    pattern: ".*\\S+.*"
  }];

  // vaccine availability setting
  optional BusinessPetVaccineAvailabilityDef availability = 5;
}

// vaccine availability setting
message BusinessPetVaccineAvailabilityDef {
  // availability of pet types ----------------
  // only for specific pet types
  optional bool only_for_specific_pet_type = 1;
  // available pet types, if only_for_specific_pet_types is true
  repeated models.customer.v1.PetType available_pet_types = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // availability of pet types ----------------
}
