syntax = "proto3";

package moego.models.grooming.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/grooming/v1/service_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1;groomingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.grooming.v1";

// the grooming customer's custom service model
message CustomerServiceModel {
  // custom id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // customer id
  int64 customer_id = 3;
  // created staff id
  int64 create_by = 4;
  // pet id
  int64 pet_id = 5;
  // service id
  int64 service_id = 6;
  // service name
  string service_name = 7;
  // service detail
  string service_detail = 8;
  // service type
  ServiceType type = 9;
  // category id
  int64 category_id = 10;
  // service duration, in minutes
  int32 service_time = 11;
  // service price
  double service_fee = 12;
  // save type
  ServiceCustomType save_type = 13;
  // is deleted
  bool is_deleted = 14;
  // create time
  google.protobuf.Timestamp create_time = 15;
  // update time
  google.protobuf.Timestamp update_time = 16;
  // tax id
  int64 tax_id = 17;
  // tax rate
  double tax_rate = 18;
}

// the grooming service in c app select service view
message CustomerServiceModelSelectServiceView {
  // pet id
  int64 pet_id = 1;
  // service id
  int64 service_id = 2;
  // service type
  ServiceType type = 3;
  // service duration, in minutes
  int32 service_time = 4;
  // service price, null means not set
  string service_fee = 5;
  // category id
  int64 category_id = 6;
}
