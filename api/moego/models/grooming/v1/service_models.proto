syntax = "proto3";

package moego.models.grooming.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/grooming/v1/service_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1;groomingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.grooming.v1";

// the grooming service model
message ServiceModel {
  // service id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // category id
  int64 category_id = 3;
  // service name
  string name = 4;
  // service description
  string description = 5;
  // service type
  ServiceType type = 6;
  // tax id
  int64 tax_id = 7;
  // service price
  double price = 8;
  // service duration, in minutes
  int32 duration = 9;
  // is inactive
  bool is_inactive = 10;
  // sort number
  int32 sort = 11;
  // color code
  string color_code = 12;
  // is deleted
  bool is_deleted = 13;
  // create time
  google.protobuf.Timestamp create_time = 14;
  // update time
  google.protobuf.Timestamp update_time = 15;
  // show base price
  ShowBasePrice show_base_price = 16;
  // book online enable
  bool is_book_online_available = 17;
  // available for all staff
  bool is_all_staff = 18;
  // pet breed filter switch
  bool is_breed_filter = 19;
  // pet weight filter switch
  bool is_weight_filter = 20;
  // pet weight down limit
  double weight_down_limit = 21;
  // pet weight up limit
  double weight_up_limit = 22;
  // pet coat type filter
  bool is_coat_type_filter = 23;
}

// the grooming service in c app appt detail view
message ServiceModelClientView {
  // service id
  int64 id = 1;
  // service name
  string name = 4;
  // service type
  ServiceType type = 6;
  // is inactive
  bool is_inactive = 10;
  // is deleted
  bool is_deleted = 13;
  // book online enable
  bool is_book_online_available = 17;
}

// the grooming service in c app select service view
message ServiceModelSelectServiceView {
  // service id
  int64 id = 1;
  // service name
  string name = 2;
  // service type
  ServiceType type = 3;
  // service price
  double price = 4;
  // service duration, in minutes
  int32 duration = 5;
  // sort number
  int32 sort = 6;
  // show base price
  ShowBasePrice show_base_price = 7;
  // category id
  int64 category_id = 8;
  // service description
  string description = 9;
}
