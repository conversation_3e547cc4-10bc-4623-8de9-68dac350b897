syntax = "proto3";

package moego.models.grooming.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1;groomingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.grooming.v1";

// zip code model
message ZipCodeModel {
  // Unique identifier for the place
  int64 id = 1;
  // Zip code of the place
  string zip_code = 2;
  // Name of the place
  string place_name = 3;
  // State where the place is located
  string state = 4;
  // State abbreviation
  string state_abbreviation = 5;
  // County where the place is located
  string county = 6;
  // Country name (if applicable)
  string country_name = 7;
  // Latitude of the place
  string latitude = 8;
  // Longitude of the place
  string longitude = 9;
  // Unique identifier for the place (e.g. Google Places ID)
  string place_id = 10;
  // Status of the place (true/false)
  bool status = 11;
  // Timestamp when the place information was last updated
  google.protobuf.Timestamp updated_at = 12;
  // Timestamp when the place information was created
  google.protobuf.Timestamp created_at = 13;
}
