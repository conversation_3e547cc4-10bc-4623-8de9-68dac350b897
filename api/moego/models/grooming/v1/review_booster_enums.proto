syntax = "proto3";

package moego.models.grooming.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1;groomingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.grooming.v1";

// review booster record source
enum ReviewBoosterRecordSource {
  // unspecified
  REVIEW_BOOSTER_RECORD_SOURCE_UNSPECIFIED = 0;
  // sms
  REVIEW_BOOSTER_RECORD_SOURCE_SMS = 1;
  // grooming report link
  REVIEW_BOOSTER_RECORD_SOURCE_GROOMING_REPORT_LINK = 2;
  // client app
  REVIEW_BOOSTER_RECORD_SOURCE_CLIENT_APP = 3;
}
