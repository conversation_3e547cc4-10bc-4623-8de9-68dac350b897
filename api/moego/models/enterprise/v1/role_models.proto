syntax = "proto3";

package moego.models.enterprise.v1;

import "moego/models/permission/v1/permission_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// model for role
message RoleModel {
  // role id
  int64 id = 1;
  // role name
  string name = 2;
  // role description
  string description = 3;
  // enterprise id
  int64 enterprise_id = 4;
  // role type
  permission.v1.RoleType type = 5;
}
