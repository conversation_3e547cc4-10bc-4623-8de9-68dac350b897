syntax = "proto3";

package moego.models.enterprise.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/organization/v1/staff_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// StaffModel
message StaffModel {
  // id
  int64 id = 1;
  // first name
  string first_name = 2;
  // last name
  string last_name = 3;
  // role_id
  int64 role_id = 4;
  // color code
  string color_code = 5;
  // avatar path
  string avatar_path = 6;
  // profile email
  string profile_email = 7;
  // employee category
  models.organization.v1.StaffEmployeeCategory employee_category = 8;
  // hired at
  google.protobuf.Timestamp hire_time = 9;
  // update at
  google.protobuf.Timestamp update_time = 10;
  // invite code
  string invite_code = 11;
  // note
  string note = 12;
  // source
  models.organization.v1.StaffSource source = 13;
}
