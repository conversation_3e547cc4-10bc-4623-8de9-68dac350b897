// @since 2023-07-02 15:06:52
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.llm.v1;

import "moego/models/llm/v1/usage_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/llm/v1;llmpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.llm.v1";

// The Conversation model
message ConversationCompletionModel {
  // id, no value currently
  int64 id = 1 [(validate.rules).int64 = {const: 0}];
  // provider platform
  string provider = 2;
  // model name
  string model = 3;
  // external id
  string external_id = 4;
  // usage info
  UsageDef usage = 5;
  // choices, must be 1 element
  repeated string choices = 6 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1
  }];
}
