syntax = "proto3";

package moego.models.payment.v1;

import "google/type/money.proto";
import "moego/models/payment/v1/payment_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// PaymentModel. 直接由 Payment 表现状定义，未整理和加工。
message PaymentModel {
  // ID.
  int64 id = 1;
  // Module.
  string module = 2;
  // Invoice ID.
  int64 invoice_id = 3;
  // Customer ID.
  int64 customer_id = 4;
  // Staff ID.
  int64 staff_id = 5;
  // Method.
  string method = 6;
  // Amount.
  google.type.Money amount = 7;
  // Status.
  PaymentStatus status = 8;
  // Create time.
  int64 create_time = 9;
  // Update time.
  int64 update_time = 10;
  // Check number.
  string check_number = 11;
  // Card type.
  string card_type = 12;
  // Card number.
  string card_number = 13;
  // Exp month.
  string exp_month = 14;
  // Exp year.
  string exp_year = 15;
  // Signature.
  string signature = 16;
  // Paid by.
  string paid_by = 17;
  // Description.
  string description = 18;
  // Method ID.
  int64 method_id = 19;
  // Stripe intent ID.
  string stripe_intent_id = 20;
  // Stripe client secret.
  string stripe_client_secret = 21;
  // Business ID.
  int64 business_id = 22;
  // Stripe charge ID.
  string stripe_charge_id = 23;
  // Is online.
  bool is_online = 24;
  // Currency.
  string currency = 25;
  // Grooming ID.
  uint64 grooming_id = 26;
  // Square customer ID.
  string square_customer_id = 27;
  // Location ID.
  string location_id = 28;
  // Square payment method.
  int32 square_payment_method = 29;
  // Device ID.
  string device_id = 30;
  // Square checkout ID.
  string square_checkout_id = 31;
  // Vendor.
  string vendor = 32;
  // Merchant.
  string merchant = 33;
  // Cancel reason.
  string cancel_reason = 34;
  // Tips.
  google.type.Money tips = 35;
  // Processing fee.
  google.type.Money processing_fee = 36;
  // Is deposit.
  bool is_deposit = 37;
  // Stripe payment method.
  int32 stripe_payment_method = 38;
  // Card funding.
  string card_funding = 39;
  // Company ID.
  int64 company_id = 40;
}
