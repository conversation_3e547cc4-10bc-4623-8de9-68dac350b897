// @since 2024-06-03 14:43:06
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.branded_app.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/branded_app/v1;brandedapppb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.branded_app.v1";

// The BrandedPackConfigModel message represents the configuration details for a branded app pack.
message BrandedPackConfigModel {
  // The unique identifier for the branded pack configuration.
  int64 id = 1;

  // The identifier for the branded app.
  string branded_app_id = 2;

  // The schema associated with the branded app.
  string schema = 3;

  // The version of the branded app pack, e.g., 1.0.0.
  string version = 4;

  // The development version of the branded app pack, e.g., 1, 2, 3, 4.
  string dev_version = 5;

  // The iOS app bundle identifier, e.g., com.moego-branded.happy-spa-dogs.
  string apple_bundle_identifier = 6;

  // The iOS Apple Pay merchant identifier, e.g., merchant.moego-branded.happy-spa-dogs.
  string merchant_identifier = 7;

  // The Android app package name, e.g., com.moego_branded.happy_spa_dogs.
  string android_package = 8;

  // The MoeGo runtime version.
  string runtime_version = 9;

  // The Google services JSON configuration, encoded as a base64 string.
  string google_services_json = 10;

  // The keystore file, encoded as a base64 string.
  string firebase_channel_id = 11;

  // base64 string
  string keystore = 12;

  // The password for the keystore, stored as an encrypted string.
  string keystore_password = 13;

  // The key password for the keystore, stored as an encrypted string.
  string keystore_key_password = 14;

  // The iOS distribution certificate P12 file, encoded as a base64 string.
  string dist_p12 = 15;

  // The password for the iOS distribution certificate, stored as an encrypted string.
  string dist_password = 16;

  // The iOS distribution mobile provision file, encoded as a base64 string.
  string dist_mobile_provision = 17;

  // The APNs topic for the iOS app, e.g., com.moego-branded.happy-spa-dogs.
  string apns_topic = 18;
}

// pack config view
message BrandedPackConfigView {
  // The unique identifier for the branded pack configuration.
  int64 id = 1;

  // The identifier for the branded app.
  string branded_app_id = 2;

  // The schema associated with the branded app.
  string schema = 3;
}
