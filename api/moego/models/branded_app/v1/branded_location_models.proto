// @since 2024-06-03 14:43:06
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.branded_app.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/branded_app/v1;brandedapppb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.branded_app.v1";

// The Branded Location model
message BrandedLocationModel {
  // the unique id
  int64 id = 1;

  // the branded app id
  string branded_app_id = 2;

  // the company id
  int64 company_id = 3;

  // the name of the personalized configuration
  string location_name = 4;

  // the location is available
  bool is_available = 5;

  // the created time
  google.protobuf.Timestamp created_at = 14;

  // the updated time
  google.protobuf.Timestamp updated_at = 15;
}
