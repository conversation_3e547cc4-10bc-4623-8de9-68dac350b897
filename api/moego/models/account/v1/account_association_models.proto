syntax = "proto3";

package moego.models.account.v1;

import "google/protobuf/struct.proto";
import "moego/models/account/v1/account_association_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1;accountpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.account.v1";

// account association model
message AccountAssociationModel {
  // account association id
  int64 id = 1;

  // account id
  int64 account_id = 2;

  // platform
  AccountAssociationPlatform platform = 3;

  // platform account id
  string platform_account_id = 4;

  // visible
  bool visible = 5;

  // platform data
  google.protobuf.Struct platform_data = 6;
}
