syntax = "proto3";

package moego.models.user_profile.v1;

import "google/protobuf/struct.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/user_profile/v1;userprofilepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.user_profile.v1";

// 用户，是画像服务的抽象概念，可以是系统内任意用户类型的实体
message User {
  // 用户类型
  enum Type {
    // 未定义
    TYPE_UNSPECIFIED = 0;
    // 商家
    BUSINESS = 1;
    // 公司
    COMPANY = 2;
    // 企业
    ENTERPRISE = 3;
    // 顾客
    CUSTOMER = 4;
  }
  // 用户 ID
  int64 id = 1;
  // 用户类型
  Type type = 2;
}

// 用户画像
message UserProfile {
  // 用户
  User user = 1;
  // 标签，JSON 对象，形如：
  // {
  //    "tier" : "T1",
  //    "rank" : "top"
  // }
  google.protobuf.Struct tags = 2;
}
