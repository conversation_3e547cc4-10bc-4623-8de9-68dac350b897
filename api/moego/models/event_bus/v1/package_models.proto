syntax = "proto3";

package moego.models.event_bus.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.event_bus.v1";

// model for package purchased event
message PackagePurchasedEvent {
  // customer id
  int64 customer_id = 1;
  // grooming package id
  repeated GroomingPackageRecord grooming_packages = 2;
  // purchased time
  google.protobuf.Timestamp purchased_time = 3;

  // grooming package record used data
  message GroomingPackageRecord {
    // package id
    int64 package_id = 1;
    // grooming package id
    int64 grooming_package_id = 2;
    // expiration time
    optional google.protobuf.Timestamp expiration_time = 3;
  }
}

// model for package redeemed event
message PackageRedeemedEvent {
  // customer id
  int64 customer_id = 1;
  // grooming package history list
  repeated GroomingPackageHistory grooming_packages = 2;
  // used time
  google.protobuf.Timestamp used_time = 3;

  // grooming package used data
  message GroomingPackageHistory {
    // grooming package id
    int64 grooming_package_id = 1;
    // package history id
    repeated int64 package_history_id = 2;
  }
}
