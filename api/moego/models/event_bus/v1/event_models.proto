// @since 2024-06-06 00:11:57
// <AUTHOR> <z<PERSON><PERSON>@moego.pet>

syntax = "proto3";

package moego.models.event_bus.v1;

import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/event_bus/v1/appointment_models.proto";
import "moego/models/event_bus/v1/call_models.proto";
import "moego/models/event_bus/v1/capital_models.proto";
import "moego/models/event_bus/v1/customer_models.proto";
import "moego/models/event_bus/v1/event_defs.proto";
import "moego/models/event_bus/v1/message_models.proto";
import "moego/models/event_bus/v1/offering_models.proto";
import "moego/models/event_bus/v1/online_booking_models.proto";
import "moego/models/event_bus/v1/order_models.proto";
import "moego/models/event_bus/v1/package_models.proto";
import "moego/models/event_bus/v1/payment_models.proto";
import "moego/models/event_bus/v1/subscription_models.proto";
import "moego/models/event_bus/v1/webhook_models.proto";
import "moego/models/organization/v1/tenant.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.event_bus.v1";

// The Event model
message Event {
  // unique id of the event, generated by event publisher to ensure that one event is only processed once
  string id = 1;
  // created time of the event
  google.protobuf.Timestamp time = 2;
  // event detail
  google.protobuf.Any detail = 3;
  // source of the event
  string source = 4;
  // type
  models.event_bus.v1.EventType event_type = 5;
}

// The Event Data Model
message EventData {
  // tenant
  moego.models.organization.v1.Tenant tenant = 1;
  // event
  oneof event {
    // appointment created event
    moego.models.event_bus.v1.AppointmentCreatedEvent appointment_created_event = 2;
    // appointment finished event
    moego.models.event_bus.v1.AppointmentFinishedEvent appointment_finished_event = 3;
    // appointment canceled event
    moego.models.event_bus.v1.AppointmentCanceledEvent appointment_canceled_event = 4;
    // message send event
    moego.models.event_bus.v1.MessageSendEvent message_send_event = 5;
    // online booking submitted event
    moego.models.event_bus.v1.OnlineBookingSubmittedEvent online_booking_submitted_event = 6;
    // online booking accepted event
    moego.models.event_bus.v1.OnlineBookingAcceptedEvent online_booking_accepted_event = 7;
    // online booking abandoned event
    moego.models.event_bus.v1.OnlineBookingAbandonedEvent online_booking_abandoned_event = 8;
    // order event
    moego.models.event_bus.v1.OrderEvent order_event = 9;
    // refund order event
    moego.models.event_bus.v1.RefundOrderEvent refund_order_event = 10;
    // customer created event
    moego.models.event_bus.v1.CustomerCreatedEvent customer_created_event = 11;
    // package purchased event
    moego.models.event_bus.v1.PackagePurchasedEvent package_purchased_event = 12;
    // package redeemed event
    moego.models.event_bus.v1.PackageRedeemedEvent package_redeemed_event = 13;
    // payment event
    moego.models.event_bus.v1.PaymentEvent payment_event = 14;
    // payment refund event
    moego.models.event_bus.v1.PaymentRefundEvent payment_refund_event = 15;
    // subscription update event
    moego.models.event_bus.v1.SubscriptionUpdated subscription_updated_event = 16;
    // capital loan offer payout event
    moego.models.event_bus.v1.LoanOfferPayoutEvent capital_loan_offer_payout_event = 17;
    // capital loan transaction updated event
    moego.models.event_bus.v1.LoanTransactionUpdatedEvent capital_loan_transaction_updated_event = 18;
    // dispute event
    moego.models.event_bus.v1.DisputeFundingOperateEvent dispute_event = 19;
    // appointment deleted event
    moego.models.event_bus.v1.AppointmentDeletedEvent appointment_deleted_event = 20;
    // appointment updated event
    moego.models.event_bus.v1.AppointmentUpdatedEvent appointment_updated_event = 21;
    // call update log status
    moego.models.event_bus.v1.CallUpdatedStatusEvent call_updated_status_event = 22;
    // group class session event
    moego.models.event_bus.v1.GroupClassSessionEvent group_class_session_event = 23;
    // subscription payment
    moego.models.event_bus.v1.SubscriptionPaymentEvent subscription_payment_event = 24;
    // appointment rescheduled event
    moego.models.event_bus.v1.AppointmentRescheduledEvent appointment_rescheduled_event = 25;
    // appointment pet & service info event
    moego.models.event_bus.v1.AppointmentPetServiceInfoEvent appointment_pet_service_event = 26;
    // webhook delivery sent
    moego.models.event_bus.v1.WebhookDeliverySentEvent webhook_delivery_sent_event = 27;
    // delete pet from appointment event
    moego.models.event_bus.v1.AppointmentDeletePetEvent appointment_delete_pet_event = 28;
  }
}
