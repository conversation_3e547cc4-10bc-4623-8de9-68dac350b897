syntax = "proto3";

package moego.models.event_bus.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.event_bus.v1";

// Event is the base message for all events
// Event type corresponds to a structure
enum EventType {
  // TYPE_UNSPECIFIED
  // (-- api-linter: core::0126::unspecified=disabled
  //     aip.dev/not-precedent: Will lead to breaking change. --)
  TYPE_UNSPECIFIED = 0;

  // online booking
  // ONLINE_BOOKING_SUBMITTED
  ONLINE_BOOKING_SUBMITTED = 101;
  // ONLINE_BOOKING_ABANDONED
  ONLINE_BOOKING_ABANDONED = 102;
  // ONLINE_BOOKING_ACCEPTED
  ONLINE_BOOKING_ACCEPTED = 103;

  // appointment
  // APPOINTMENT_CREATED
  APPOINTMENT_CREATED = 201;
  // APPOINTMENT_CANCELED
  APPOINTMENT_CANCELED = 202;
  // APPOINTMENT_FINISHED
  APPOINTMENT_FINISHED = 203;
  // APPOINTMENT_DELETED
  APPOINTMENT_DELETED = 204;
  // APPOINTMENT_UPDATED
  APPOINTMENT_UPDATED = 205;
  // APPOINTMENT_RESCHEDULED
  APPOINTMENT_RESCHEDULED = 206;
  // APPOINTMENT_DELETE_PET
  APPOINTMENT_DELETE_PET = 207;
  // APPOINTMENT_PET_SERVICE_INFO
  APPOINTMENT_PET_SERVICE_INFO = 210;

  // intake form
  // INTAKE_FORM_SUBMITTED
  INTAKE_FORM_SUBMITTED = 301;

  // product
  // PRODUCT_PURCHASED
  PRODUCT_PURCHASED = 401;

  // package
  // PACKAGE_PURCHASED
  PACKAGE_PURCHASED = 501;
  // PACKAGE_REDEEMED
  PACKAGE_REDEEMED = 502;

  // membership
  // MEMBERSHIP_PURCHASED
  MEMBERSHIP_PURCHASED = 601;
  // MEMBERSHIP_CANCELED
  MEMBERSHIP_CANCELED = 602;

  // discount code
  // DISCOUNT_CODE_REDEEMED
  DISCOUNT_CODE_REDEEMED = 701;

  // order
  // ORDER_FULLY_PAID
  // Deprecated: please use ORDER_COMPLETED.
  ORDER_FULLY_PAID = 801;
  // ORDER_COMPLETED
  ORDER_COMPLETED = 802;
  // REFUND_ORDER_COMPLETED
  REFUND_ORDER_COMPLETED = 803;
  // ORDER_CANCELED
  ORDER_CANCELED = 804;
  // ORDER_CREATED
  ORDER_CREATED = 805;

  // message
  // MESSAGE_SENT
  MESSAGE_SENT = 901;
  // customer
  // CUSTOMER_CREATED
  CUSTOMER_CREATED = 1001;
  // payment: 1101~1200
  // PAYMENT_STATUS_CHANGED
  PAYMENT_STATUS_CHANGED = 1101;
  // PAYMENT_REFUND_STATUS_CHANGED
  PAYMENT_REFUND_STATUS_CHANGED = 1102;
  // DISPUTE_FUNDING_OPERATE
  DISPUTE_FUNDING_OPERATE = 1103;

  // subscription
  // SUBSCRIPTION_UPDATED
  SUBSCRIPTION_UPDATED = 1201;
  // SUBSCRIPTION_PAYMENT_FAILED
  SUBSCRIPTION_PAYMENT_FAILED = 1202;

  // capital
  // CAPITAL_LOAN_OFFER_PAYOUT
  // 注意：这个事件不是 loan offer 流转到 PAID_OUT 状态时发出，而是 payout 交易发生时发出
  CAPITAL_LOAN_OFFER_PAYOUT = 1301;
  // CAPITAL_LOAN_TRANSACTION_UPDATED
  CAPITAL_LOAN_TRANSACTION_UPDATED = 1302;

  // engagement
  // CALL_UPDATE_STATUS
  CALL_UPDATE_STATUS = 1401;

  // webhook delivery
  // WEBHOOK_DELIVERY_SENT
  WEBHOOK_DELIVERY_SENT = 1501;

  // offering 2001-2099
  // update offering group class session
  OFFERING_GROUP_CLASS_SESSION_UPDATED = 2001;
}
