// @since 2024-11-07 14:39:17
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.models.event_bus.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/appointment/v1/pet_detail_defs.proto";
import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.event_bus.v1";

// model for appointment creation event
message AppointmentCreatedEvent {
  // appointment id
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  // start time
  google.protobuf.Timestamp start_time = 3;
  // service item types included in the appointment
  repeated models.offering.v1.ServiceItemType service_item_types = 4;
  // end time
  google.protobuf.Timestamp end_time = 5;
}

// model for appointment finish event
message AppointmentFinishedEvent {
  // appointment id
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  // service item types included in the appointment
  repeated models.offering.v1.ServiceItemType service_item_types = 3;
  // start time
  google.protobuf.Timestamp start_time = 4;
  // end time
  google.protobuf.Timestamp end_time = 5;
}

// model for appointment cancel event
message AppointmentCanceledEvent {
  // appointment id
  int64 id = 1;
  // cancel reason
  enum CancelledType {
    // unspecified
    CANCELLED_BY_UNSPECIFIED = 0;
    // canceled by customer
    CANCELLED_BY_CUSTOMER = 1;
    // canceled by business
    CANCELLED_BY_BUSINESS = 2;
    // canceled by system
    CANCELLED_BY_SYSTEM = 3;
  }
  // cancel type
  CancelledType cancelled_type = 2;
  // cancel reason
  optional string cancelled_reason = 3;
  /*
     if the appointment is canceled by customer, the operator_id is the customer id;
     if the appointment is canceled by business, the operator_id is the business id;
     if the appointment is canceled by system, the operator_id will be empty;
  */
  optional int64 operator_id = 4;
  // service item types included in the appointment
  repeated models.offering.v1.ServiceItemType service_item_types = 5;
  // customer id
  int64 customer_id = 6;
  // start time
  google.protobuf.Timestamp start_time = 7;
  // end time
  google.protobuf.Timestamp end_time = 8;
  // Controls whether to automatically trigger refunds for associated orders. true required, false not required
  bool auto_refund_order = 9;
  //is repeat batch cancel
  optional bool is_repeat_batch_cancel = 10;
  // refund deposit
  bool refund_deposit = 11;
}

// model for appointment deleted event
message AppointmentDeletedEvent {
  // appointment id
  int64 id = 1;
}

// model for appointment updated event
message AppointmentUpdatedEvent {
  // appointment id
  int64 id = 1;
}

// model for appointment rescheduled event
message AppointmentRescheduledEvent {
  // appointment id
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  // start time
  google.protobuf.Timestamp start_time = 3;
  // end time
  google.protobuf.Timestamp end_time = 4;
  // reason
  string reason = 5;
}

// model for appointment pet & service info event
message AppointmentPetServiceInfoEvent {
  // appointment id
  int64 id = 1;
  // pet & service info
  repeated models.appointment.v1.PetDetailDef pet_details = 2;
}

// model for delete pet from appointment event
message AppointmentDeletePetEvent {
  // appointment id
  int64 id = 1;
  // pet id
  int64 pet_id = 2;
}
