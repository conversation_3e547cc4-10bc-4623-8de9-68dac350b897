syntax = "proto3";

package moego.models.finance_tools.v1;

import "google/protobuf/timestamp.proto";
import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/models/finance_tools/v1/cash_drawer_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/finance_tools/v1;financetoolspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.finance_tools.v1";

// Cash drawer report.
message CashDrawerReport {
  // The report ID
  int64 id = 1 [(validate.rules).int64.gte = 0];
  // ID of the staff that make this reconciliation report.
  int64 staff_id = 2 [(validate.rules).int64.gt = 0];
  // The date range
  google.type.Interval range = 3;
  // The starting balance
  google.type.Money start_balance = 4;
  // The total of cash payments
  google.type.Money payments_total = 5;
  // The total of cash in/out adjustments
  google.type.Money adjustments_total = 6;
  // The expected ending balance
  google.type.Money expected_balance = 7;
  // The actual ending balance
  google.type.Money counted_balance = 8;
  // The difference between the expected and the actual ending balance. Will be positive if the actual balance is over
  // the expected, and negative if the actual balance is short.
  google.type.Money difference = 9;
  // Comment
  optional string comment = 10 [(validate.rules).string = {max_len: 200}];
}

// A cash adjustment.
message CashDrawerAdjustment {
  // The adjustment ID
  int64 id = 1 [(validate.rules).int64.gte = 0];
  // The ID of the report this transaction is attached. Optional because an adjustment may not be attached to any report
  // yet.
  optional int64 report_id = 2 [(validate.rules).int64.gte = 0];
  // Adjustment type (direction)
  CashDrawerAdjustmentType type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Time
  google.protobuf.Timestamp time = 4 [(validate.rules).timestamp.gt.seconds = 0];
  // ID of the staff that make this cash in/out.
  int64 staff_id = 5 [(validate.rules).int64.gt = 0];
  // The amount of cash in/out adjustments. Always positive.
  google.type.Money amount = 6;
  // Comment
  optional string comment = 7 [(validate.rules).string = {max_len: 200}];
}
