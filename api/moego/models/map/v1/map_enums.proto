syntax = "proto3";

package moego.models.map.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/map/v1;mappb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.map.v1";

// RegionLevel
// civil entity region level in country
enum RegionLevel {
  // unspecified
  REGION_LEVEL_UNSPECIFIED = 0;
  // country
  LV0 = 1;
  // a first-order civil entity below the country level.
  // within the United States, these administrative levels are states.
  // within the China, these administrative levels are provinces.
  LV1 = 2;
  // a second-order civil entity below the country level.
  // within the United States, these administrative levels are counties/districts.
  // within the China, these administrative levels are cities.
  LV2 = 3;
  // a third-order civil entity below the country level.
  // within the United States, these administrative levels are township/cities.
  // within the China, these administrative levels are counties/districts.
  LV3 = 4;
  // A fourth-order civil entity below the country level.
  // This type indicates a minor civil division. e.g. village
  LV4 = 5;
  // A fifth-order civil entity below the country level.
  // This type indicates a minor civil division. e.g. ward
  LV5 = 6;
  // A sixth-order civil entity below the country level.
  // This type indicates a minor civil division. e.g. street
  LV6 = 7;
}

// RegionStatus
enum RegionStatus {
  // unspecified
  REGION_STATUS_UNSPECIFIED = 0;
  // region status normal
  ACTIVE = 1;
  // region is expired
  EXPIRED = 2;
  // region is deleted
  DELETED = 3;
}

// AreaRelation
enum AreaRelation {
  // unspecified
  AREA_RELATION_UNSPECIFIED = 0;
  // no relation
  NOTHING = 1;
  // adjacent
  ADJACENT = 2;
  // intersect
  INTERSECT = 3;
  // coincide
  COINCIDE = 4;
  // contain
  CONTAIN = 5;
}
