syntax = "proto3";

package moego.models.order.v1;

import "google/type/money.proto";
import "moego/models/grooming/v1/appointment_enums.proto";
import "moego/models/order/v1/order_enums.proto";
import "moego/models/order/v1/order_line_discount_models.proto";
import "moego/models/order/v1/order_line_extra_fee_models.proto";
import "moego/models/order/v1/order_line_tax_models.proto";
import "moego/models/order/v1/refund_order_enums.proto";
import "moego/models/payment/v1/payment_method_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

/**
 * order type view
 */
message OrderModelHistoryView {
  // order id
  int64 id = 1;
  // order type
  OrderModel.OrderType order_type = 2;
  // order status
  string status = 3;
  // order source type
  OrderSourceType source_type = 4;
  // payment status
  OrderModel.PaymentStatus payment_status = 5;
  // fulfillment status
  OrderModel.FulfillmentStatus fulfillment_status = 6;
  // complete time
  int64 complete_time = 7;
  // source id
  int64 source_id = 8;
  // paid amount
  double paid_amount = 9;
  // total amount
  double total_amount = 10;
  // company id
  int64 company_id = 11;
  // business id
  int64 business_id = 12;
  // order version
  int32 order_version = 13;
}

/**
 * order info
 */
message OrderModel {
  // order type
  // 自 invoice 4 期开始， order_type 字段含义调整为订单本身的类型，不再用于区分是否为 “主单”
  enum OrderType {
    // order type.
    ORDER_TYPE_UNSPECIFIED = 0;
    // origin order
    // 复用定义，含义从主单调整为普通订单 / sales order.
    ORIGIN = 1;
    // extra order.
    // deprecated: please use other types.
    EXTRA = 2;
    // Deposit order.
    DEPOSIT = 3;
    // Tip.
    TIP = 4;
  }
  // payment status
  enum PaymentStatus {
    // payment status: UNPAID, PARTIAL_PAID, PAID, PARTIAL_REFUNDED, REFUNDED
    PAYMENT_STATUS_UNSPECIFIED = 0;
    // unpaid
    UNPAID = 1;
    // partial paid
    PARTIAL_PAID = 2;
    // paid
    PAID = 3;
  }

  // order fulfillment status: indicate the appointment is finished or not
  enum FulfillmentStatus {
    // fulfillment status: UNFULFILLED, PARTIAL_FULFILLED, FULFILLED
    FULFILLMENT_STATUS_UNSPECIFIED = 0;
    // init
    INIT = 1;
    // completed
    COMPLETED = 2;
    // canceled
    CANCELED = 3;
  }

  // id
  optional int64 id = 1;
  // business id
  int64 business_id = 2;
  // customer id
  int64 customer_id = 3;
  // order status
  int32 status = 4;
  // order creating source: GROOMING, RETAIL
  string source_type = 5;
  // payment status: UNPAID, PARTIAL_PAID, PAID, PARTIAL_REFUNDED, REFUNDED
  optional string payment_status = 6;
  // order guid
  optional string guid = 7;
  // source id
  optional int64 source_id = 8;
  // line item types: a record for querying
  optional int32 line_item_types = 9;
  //order version
  optional int32 version = 10;
  // tips amount
  optional double tips_amount = 11;
  // tax amount
  optional double tax_amount = 12;
  // discount amount
  optional double discount_amount = 13;
  // deducted deposit amount
  optional double deposit_amount = 42;
  // extra fee amount
  optional double extra_fee_amount = 14;
  // subTotal amount
  optional double sub_total_amount = 15;
  // amount of tips based on
  optional double tips_based_amount = 16;
  // total amount
  optional double total_amount = 17;
  // paid amount
  optional double paid_amount = 18;
  // remain amount to pay
  optional double remain_amount = 19;
  // refunded amount
  optional double refunded_amount = 20;
  // order title
  optional string title = 21;
  // staff id of creating this order
  optional int64 create_by = 22;
  // staff id of last updating this order
  optional int64 update_by = 23;
  // create time
  optional int64 create_time = 24;
  // update time
  optional int64 update_time = 25;
  // fulfillment status: reserved field
  optional string fulfillment_status = 26;
  // description
  optional string description = 28;

  // tax applied on this order
  repeated OrderLineTaxModel line_taxes = 29;
  // discount applied on this order
  repeated OrderLineDiscountModel line_discounts = 30;
  // extra fee applied on this order
  repeated OrderLineExtraFeeModel line_extra_fees = 31;

  // appointment source
  optional moego.models.grooming.v1.AppointmentSource source = 32;

  // add companyId
  optional int64 company_id = 33;

  // order type
  optional OrderType order_type = 34;
  // complete time
  optional int64 complete_time = 35;
  // order ref id
  optional int64 order_ref_id = 36;
  // extra charge reason
  optional string extra_charge_reason = 37;
  // order version
  optional int32 order_version = 38;
  // order has extra order
  bool has_extra_order = 39;
  // currency code.
  string currency_code = 40;
  // 当前订单可支持的退款模式.
  repeated RefundMode refundable_modes = 41;
}

// OrderModelV1 与 OrderModel 一致，但是调整部分字段的类型:
// - 枚举字段的类型从 string 调整为真正的 enum 类型.
// - 钱有关的字段从 float64 调整 Money.
message OrderModelV1 {
  // id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // customer id
  int64 customer_id = 3;
  // order status
  OrderStatus status = 4;
  // order creating source: GROOMING, RETAIL
  string source_type = 5;
  // payment status: UNPAID, PARTIAL_PAID, PAID, PARTIAL_REFUNDED, REFUNDED
  OrderModel.PaymentStatus payment_status = 6;
  // order guid
  string guid = 7;
  // source id
  int64 source_id = 8;
  // line item types: a record for querying
  int32 line_item_types = 9;
  // order version
  int32 version = 10;
  // tips amount
  google.type.Money tips_amount = 11;
  // tax amount
  google.type.Money tax_amount = 12;
  // discount amount
  google.type.Money discount_amount = 13;
  // deposit amount
  google.type.Money deposit_amount = 42;
  // convenience_fee amount
  google.type.Money convenience_fee = 14;
  // subTotal amount
  google.type.Money sub_total_amount = 15;
  // amount of tips based on
  google.type.Money tips_based_amount = 16;
  // total amount
  google.type.Money total_amount = 17;
  // paid amount
  google.type.Money paid_amount = 18;
  // remain amount to pay
  google.type.Money remain_amount = 19;
  // refunded amount
  google.type.Money refunded_amount = 20;
  // order title
  string title = 21;
  // staff id of creating this order
  int64 create_by = 22;
  // staff id of last updating this order
  int64 update_by = 23;
  // create time
  int64 create_time = 24;
  // update time
  int64 update_time = 25;
  // fulfillment status: reserved field
  string fulfillment_status = 26;
  // description
  string description = 28;

  // tax applied on this order
  repeated OrderLineTaxModel line_taxes = 29;
  // discount applied on this order
  repeated OrderLineDiscountModel line_discounts = 30;
  // extra fee applied on this order
  repeated OrderLineExtraFeeModel line_extra_fees = 31;

  // appointment source
  moego.models.grooming.v1.AppointmentSource source = 32;

  // add companyId
  int64 company_id = 33;

  // order type
  OrderModel.OrderType order_type = 34;
  // complete time
  int64 complete_time = 35;
  // order ref id
  int64 order_ref_id = 36;
  // extra charge reason
  string extra_charge_reason = 37;
  // order version
  int32 order_version = 38;
  // order has extra order
  bool has_extra_order = 39;
  // currency code.
  string currency_code = 40;
  // 当前订单可支持的退款模式.
  repeated RefundMode refundable_modes = 41;
}

// order model v1 history view
message OrderModelV1HistoryView {
  // order id
  int64 id = 1;
  // order type
  OrderModel.OrderType order_type = 2;
  // order status
  string status = 3;
  // order source type
  OrderSourceType source_type = 4;
  // payment status
  OrderModel.PaymentStatus payment_status = 5;
  // fulfillment status
  OrderModel.FulfillmentStatus fulfillment_status = 6;
  // complete time
  int64 complete_time = 7;
  // source id
  int64 source_id = 8;
  // tips amount
  google.type.Money tips_amount = 9;
  // paid amount
  google.type.Money paid_amount = 10;
  // total amount
  google.type.Money total_amount = 11;
  // company id
  int64 company_id = 12;
  // business id
  int64 business_id = 13;
  // order version
  int32 order_version = 14;
  // order ref id
  int64 order_ref_id = 15;
}

//trigger refund
message RefundChannelResponse {
  // comb refund
  bool is_combination = 1;
  // refund amount
  double refund_amount = 2;
  //channel
  repeated RefundChannel channel_list = 3;
  //invoice id
  int64 invoice_id = 4;
}

//each refund channel
message RefundChannel {
  // like : visa(1111)
  string payment_method = 1;

  // payment id
  int64 payment_id = 2;

  //remain amount
  double can_refund_amount = 3;
}

// grooming detail relation model
message GroomingDetailRelationModel {
  // id
  int64 id = 1;
  // order id
  int64 order_id = 2;
  // grooming id
  int64 grooming_id = 3;
  // pet detail id
  int64 pet_detail_id = 4;
}

// edit staff commission operation, multi staff item
message EditStaffCommissionOperationItem {
  // staff id
  int64 staff_id = 1;
  // staff work ratio
  double ratio = 2;
  // staff work duration, min
  int32 duration = 3;
  // operation name
  string operation_name = 4;
}

// edit staff commission item
message EditStaffCommissionItem {
  // pet detail id
  int64 pet_detail_id = 1;
  // service id
  int64 service_id = 2;
  // pet id
  int64 pet_id = 3;
  // staff id
  int64 staff_id = 4;
  // business id
  optional int64 business_id = 5;
  // company id
  optional int64 company_id = 6;
  // operation list
  repeated EditStaffCommissionOperationItem operation = 7;
  // order item type: product/service
  string order_item_type = 8;
  // order item id
  int64 order_item_id = 9;
}

// order event model
message OrderEventModel {
  // 事件类型
  EventType event_type = 1;
  // order详情
  OrderModelHistoryView order = 2;
}

// Order Payment.
message OrderPaymentModel {
  // Order Payment ID.
  int64 id = 1;
  // 关联的 Order 的 ID.
  int64 order_id = 2;
  // 关联的 Order 的 Company ID.
  int64 company_id = 3;
  // 关联的 Order 的 Business ID.
  int64 business_id = 4;
  // 关联的 Order 的 Customer ID.
  int64 customer_id = 5;
  // 发起支付的 staff ID.
  int64 staff_id = 6;

  // 支付方式的 ID.
  int64 payment_method_id = 11;
  // 支付方式的名字.
  string payment_method = 12;
  // 支付方式用于展示的名字.
  string payment_method_display_name = 13;
  // 支付方式的扩展字段，不同的支付方式内容不同.
  payment.v1.PaymentMethodExtra payment_method_extra = 14;
  // 支付方式的供应商.
  string payment_method_vendor = 15;
  // 是否为 Pay by link 的支付单.
  bool is_online = 16;
  // 是否为定金.
  bool is_deposit = 17;
  // Paid by.
  string paid_by = 18;

  // 支付货币，支付的货币，符合 ISO-4217 规范的三个大写字母.
  // https://www.iso.org/iso-4217-currency-codes.html
  string currency_code = 21;
  // 总的支付金额，与重构前 Payment 的 Amount 对应.
  //     total_amount = amount + payment_tips + convenience_fee.
  // 内部的 currency_code 需要与上面的 currency_code 相同.
  google.type.Money total_amount = 22;
  // 应付金额，创建 OrderPayment 时写入，不可更改.
  // 不包含 payment_tips_after_create.
  // 内部的 currency_code 需要与上面的 currency_code 相同.
  google.type.Money amount = 23;
  // 已经退款的金额，内部的 currency_code 需要与上面的 currency_code 相同.
  google.type.Money refunded_amount = 24;
  // 本次支付的 processing fee, 内部的 currency_code 需要与上面的 currency_code 相同.
  google.type.Money processing_fee = 25;
  // 该笔支付单总的 Tips，包含 payment_tips_before_create 和 payment_tips_after_create.
  // 内部的 currency_code 需要与上面的 currency_code 相同.
  //
  // 支付状态变成 Paid 之后，会同步回订单.
  google.type.Money payment_tips = 26;
  // 支付发起前，在 MoeGo 侧， Customer 附加的 tips，在发起时可明确知道金额.
  // 内部的 currency_code 需要与上面的 currency_code 相同.
  // 包含在 amount 中，也包含在 total_amount 中.
  //
  // 支付状态变成 Paid 之后，会同步回订单.
  google.type.Money payment_tips_before_create = 27;
  // 支付发起后，在供应商侧， Customer 附加的 tips，需要在回调时才可知道具体的金额.
  // 内部的 currency_code 需要与上面的 currency_code 相同.
  // 不包含在 amount 中，包含在 total_amount 中.
  // 特别的：
  //   Square 侧该值与支付结果的回调是分开的，当支付结果先回调成功时，订单可能已经满足关单条件从而关单。
  //   然后再收到 Tips 回调，此时会陷入关单后改单的情况.
  //
  // 支付状态变成 Paid 之后，会同步回订单.
  google.type.Money payment_tips_after_create = 28;
  // 由于本次支付增加的 Convenience Fee.
  //
  // 支付状态变成 Paid 之后，会同步回订单.
  google.type.Money payment_convenience_fee = 29;
  // 已退还的 Convenience Fee.
  google.type.Money refunded_payment_convenience_fee = 30;

  // Payment 状态.
  OrderPaymentStatus payment_status = 31;
  // 搭配 Payment status 展示，进入该状态的原因:
  //     Failed -> 失败的原因
  //     Canceled -> 取消的原因
  string payment_status_reason = 32;
  // 创建时间，单位秒.
  int64 create_time = 33;
  // 支付时间，单位秒.
  int64 pay_time = 34;
  // 支付失败的时间，单位秒.
  int64 fail_time = 35;
  // 支付取消的时间，单位秒.
  int64 cancel_time = 36;
  // 更新时间，单位秒.
  int64 update_time = 37;
  // payment id
  int64 payment_id = 38;
}

// The order model in appointment view
message OrderModelAppointmentView {
  // order id
  int64 id = 1;
  // order type
  OrderModel.OrderType order_type = 2;
  // order status
  OrderStatus status = 3;
  // order source type
  OrderSourceType source_type = 4;
  // payment status
  OrderModel.PaymentStatus payment_status = 5;
  // source id
  int64 source_id = 8;
  // subTotal amount
  google.type.Money sub_total_amount = 15;
  // total amount
  google.type.Money total_amount = 17;
  // paid amount
  google.type.Money paid_amount = 18;
  // remain amount to pay
  google.type.Money remain_amount = 19;
  // refunded amount
  google.type.Money refunded_amount = 20;
  // tips amount
  google.type.Money tips_amount = 21;
}
