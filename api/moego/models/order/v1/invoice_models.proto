syntax = "proto3";

package moego.models.order.v1;

import "moego/models/order/v1/order_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

/**
 * order info
 */
message InvoiceModel {
  // id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // grooming id
  int64 grooming_id = 3;
  // type: appointment, noshow
  string type = 4;
  // customer id
  int64 customer_id = 5;
  // subTotal amount
  double sub_total_amount = 6;
  // discount amount
  double discount_amount = 7;
  // discount rate
  double discount_rate = 8;
  // discount type
  string discount_type = 9;
  // tips amount
  double tips_amount = 10;
  // tips rate
  double tips_rate = 11;
  // tips type
  string tips_type = 12;
  // tax amount
  double tax_amount = 13;
  // convenience_fee
  double convenience_fee = 14;
  // payment amount
  double payment_amount = 15;
  // paid amount
  double paid_amount = 16;
  // remain amount to pay
  double remain_amount = 17;
  // refunded amount
  double refunded_amount = 18;
  // status
  int32 status = 19;
  // order guid
  string guid = 20;
  // staff id of creating this order
  int64 create_by = 21;
  // staff id of last updating this order
  int64 update_by = 22;
  // create time
  int64 create_time = 23;
  // update time
  int64 update_time = 24;
  // amount of tips based on
  double tips_based_amount = 25;

  // staff id
  int64 staff_id = 26;
  // retail title
  string title = 27;
  // retail item quantity
  int32 quantity = 28;
}

// invoice for appointment
message InvoiceCalendarView {
  // invoice id
  int64 invoice_id = 1;
  // status
  int32 status = 2;
  // paid amount
  double paid_amount = 3;
  // refunded amount
  double refunded_amount = 4;
  // total amount
  double total_amount = 5;
  // sub total amount
  double sub_total_amount = 6;
  // payment status
  OrderModel.PaymentStatus payment_status = 7;
  // outstanding_balance: computed by formula (total_amount - paid_amount + refunded_amount)
  double outstanding_balance = 8;
}

// no show invoice for appointment
message NoShowInvoiceCalendarView {
  // invoice id
  int64 invoice_id = 1;
  // status
  int32 status = 2;
}
