syntax = "proto3";

package moego.models.order.v1;

//import "moego/models/order/v1/order_enums.proto";
option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

/**
 * line tax info
 */
message OrderLineTaxModel {
  // id
  optional int64 id = 1;
  // businessId
  int64 business_id = 2;
  // orderId
  int64 order_id = 3;
  // orderItemId
  optional int64 order_item_id = 4;
  // applyType
  string apply_type = 5;
  // isDeleted
  optional bool is_deleted = 6;
  // taxId
  int64 tax_id = 7;
  // taxRate
  double tax_rate = 8;
  // taxAmount
  optional double tax_amount = 9;
  // applyBy
  int64 apply_by = 10;
  // applySequence
  optional int32 apply_sequence = 11;
  // create time
  optional int64 create_time = 12;
  // update time
  optional int64 update_time = 13;
  // Tax name.
  string tax_name = 14;
}
