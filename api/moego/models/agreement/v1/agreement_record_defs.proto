syntax = "proto3";

package moego.models.agreement.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1;agreementpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.agreement.v1";

// agreement record definition
message AgreementRecordDef {
  // agreement id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // signature
  string signature = 4 [(validate.rules).string = {max_len: 16777215}];
}
