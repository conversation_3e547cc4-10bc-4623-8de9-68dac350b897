// @since 2022-06-24 15:44:32
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.errors.v1;

import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";
import "moego/models/errors/v1/error_code_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1;errorspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.errors.v1";

// the common error structure
message CommonError {
  // error code
  Code code = 1;
  // error message
  string message = 2;
  // the unique request id
  string x_request_id = 3;
  // the timestamp
  google.protobuf.Timestamp timestamp = 4;
  // any ambiguous data to present
  // it could be a protobuf message
  // or a custom primitive/object value
  // which will be convert to a Value
  google.protobuf.Any data = 5;
  // the reason of the error
  string caused_by = 6;
}
