// @since 2024-06-12 11:04:31
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.membership.v1;

import "google/protobuf/timestamp.proto";
import "google/type/dayofweek.proto";
import "google/type/money.proto";
import "moego/models/membership/v1/redeem_models.proto";
import "moego/models/offering/v1/service_models.proto";
import "moego/utils/v1/time_period.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1;membershippb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.membership.v1";

// The Membership model
message MembershipModel {
  // membership status
  enum Status {
    // default
    STATUS_UNSPECIFIED = 0;
    // active
    ACTIVE = 1;
    // inactive
    INACTIVE = 2;
  }

  // membership billing cycle
  enum BillingCycle {
    // default
    BILLING_CYCLE_UNSPECIFIED = 0;
    // monthly
    MONTHLY = 1;
    // annually
    ANNUALLY = 2;
    // weekly
    WEEKLY = 3;
  }

  // the unique id
  int64 id = 1;
  // the product id in subscription service
  int64 internal_product_id = 2;
  // the membership name
  string name = 3;
  // the description
  string description = 4;
  // the status
  Status status = 5;
  // the price
  double price = 6;
  // the tax id
  int64 tax_id = 7;
  // billing cycle
  BillingCycle billing_cycle = 8 [deprecated = true];
  // policy
  string policy = 9;
  // the company id
  int64 company_id = 10;
  // the price id
  int64 price_id = 11;
  // billing cycle
  moego.utils.v1.TimePeriod billing_cycle_period = 12;
  // the create time
  google.protobuf.Timestamp created_at = 13;
  // the update time
  google.protobuf.Timestamp updated_at = 14;
  // the delete time, non-null means is deleted
  optional google.protobuf.Timestamp deleted_at = 15;
  // revision, any update action will +1, default is 1
  int32 revision = 16;
  // the total price
  google.type.Money total_price = 17;
  // the total tax
  google.type.Money total_tax = 18;
  // enable purchase for online booking
  bool enable_online_booking = 19;
  // enable membership benefits discount
  bool enable_discount_benefits = 20;
  // enable membership benefits quantity
  bool enable_quantity_benefits = 21;
  // billing cycyle day of week
  google.type.DayOfWeek billing_cycle_day_of_week = 22;
  // breed filter
  bool breed_filter = 23;
  // customized breed
  repeated offering.v1.CustomizedBreed customized_breed = 24;
  // available for all pet size
  bool pet_size_filter = 25;
  // available pet size (only if is_available_for_all_pet_size is false)
  repeated int64 customized_pet_sizes = 26;
  // available for all pet coat type
  bool coat_filter = 27;
  // available pet coat type (only if is_available_for_all_pet_coat_type is false)
  repeated int64 customized_coat = 28;
}

// The Membership model
message MembershipModelPublicView {
  // the unique id
  int64 id = 1;
  // the membership name
  string name = 3;
  // the description
  string description = 4;
  // the status
  MembershipModel.Status status = 5;
  // the price
  double price = 6;
  // the tax id
  int64 tax_id = 7;
  // billing cycle
  MembershipModel.BillingCycle billing_cycle = 8 [deprecated = true];
  // policy
  string policy = 9;
  // the company id
  int64 company_id = 10;
  // the price id
  int64 price_id = 11;
  // billing cycle
  moego.utils.v1.TimePeriod billing_cycle_period = 12;
  // the create time
  google.protobuf.Timestamp created_at = 13;
  // the update time
  google.protobuf.Timestamp updated_at = 14;
  // the delete time, non-null means is deleted
  optional google.protobuf.Timestamp deleted_at = 15;
  // revision, any update action will +1
  int32 revision = 16;
  // the total price
  google.type.Money total_price = 17;
  // the total tax
  google.type.Money total_tax = 18;
  // billing cycle day of week
  google.type.DayOfWeek billing_cycle_day_of_week = 19;
  // enable purchase for online booking
  bool enable_online_booking = 20;
  // enable membership benefits discount
  bool enable_discount_benefits = 21;
  // enable membership benefits quantity
  bool enable_quantity_benefits = 22;
}

// the membership usage summary model
message MembershipSummaryModel {
  // the membership id
  int64 id = 1;
  // in subscription count
  int32 in_subscription_count = 2;
  // cancelled count
  int32 cancelled_count = 3;
  //  paused count
  int32 paused_count = 4;
}

// The benefit item
message BenefitRecommendView {
  // benefit id
  int64 id = 1;
  // redeem scenario item
  moego.models.membership.v1.RedeemScenarioItem scenario_item = 2;
  // benefit detail
  oneof benefit_detail {
    // discount
    DiscountBenefitModel discount = 4;
    // quality, a typo, should be quantity
    QualityBenefitModel quality = 5;
  }
  // membership id
  optional int64 membership_id = 6;
}

// membership usage view
message MembershipUsageView {
  // order item id
  int64 order_item_id = 1;
  // price reduction, aka discount amount
  double price_reduction = 7;
  // membership id
  int64 membership_id = 2;
  // benefit detail
  oneof benefit_detail {
    // discount
    DiscountBenefitModel discount = 4;
    // quantity
    QualityBenefitModel quantity = 5;
  }
  // redeem quantity, only for quantity benefit
  int64 redeem_quantity = 6;
}

// discount benefit model
message DiscountBenefitModel {
  // id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;
  // membership id
  int64 membership_id = 4;
  // target type
  models.membership.v1.TargetType target_type = 5;
  // target ids
  repeated int64 target_ids = 6;
  // is all
  bool is_all = 7;
  // discount unit
  models.membership.v1.DiscountUnit discount_unit = 8;
  // discount value
  double discount_value = 9;
  // feature id
  int64 feature_id = 10;
  // created at
  google.protobuf.Timestamp created_at = 11;
  // updated at
  google.protobuf.Timestamp updated_at = 12;
  // deleted at
  optional google.protobuf.Timestamp deleted_at = 13;
}

// quality benefit model, a typo, should be quantity
message QualityBenefitModel {
  // id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;
  // membership id
  int64 membership_id = 4;
  // target type
  models.membership.v1.TargetType target_type = 5;
  // target sub type
  models.membership.v1.TargetSubType target_sub_type = 6;
  // target id
  int64 target_id = 7;
  // is limited
  bool is_limited = 8;
  // limited value
  int64 limited_value = 9;
  // created at
  google.protobuf.Timestamp created_at = 10;
  // updated at
  google.protobuf.Timestamp updated_at = 11;
  // deleted at
  optional google.protobuf.Timestamp deleted_at = 12;
  // feature id
  int64 feature_id = 13;
}

// The benefit item
message BenefitRedeemView {
  // benefit id
  int64 id = 1;
  // target type
  models.membership.v1.TargetType target_type = 2;
  // recommend target id， 只有在需要推荐的时候才存在该字段
  optional int64 recommend_target_id = 3;
  // 实际的 target id，只有在 redeem 之后才存在该字段
  optional int64 actual_target_id = 4;
  // benefit detail
  oneof benefit_detail {
    // discount
    DiscountBenefitModel discount = 5;
    // quality, a typo, should be quantity
    QualityBenefitModel quality = 6;
  }
  // redeem amount
  optional int64 redeem_amount = 7;
}

// The benefit summary view
message BenefitSummaryView {
  // the benefit id
  int64 id = 1;
  // the target type
  models.membership.v1.TargetType target_type = 2;
  // the target id
  int64 target_id = 3;
  // the benefit detail
  oneof benefit_detail {
    // discount
    DiscountBenefitModel discount = 4;
    // quality, a typo, should be quantity
    QualityBenefitModel quality = 5;
  }
  // quantity remaining, only for quality benefit
  optional int64 quantity_remaining = 6;
  // target name
  optional string target_name = 7;
}
