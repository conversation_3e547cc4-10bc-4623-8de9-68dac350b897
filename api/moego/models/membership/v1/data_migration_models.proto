syntax = "proto3";

package moego.models.membership.v1;

import "google/protobuf/timestamp.proto";
import "google/type/dayofweek.proto";
import "google/type/money.proto";
import "moego/models/membership/v1/membership_defs.proto";
import "moego/models/membership/v1/redeem_models.proto";
import "moego/utils/v1/time_period.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1;membershippb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.membership.v1";

// membership data
message MembershipData {
  // id
  int64 id = 1;
  // mid
  string mid = 2;
  // the company id
  int64 company_id = 3;
  // the membership name
  string name = 4;
  // description
  string description = 5;
  // the price
  google.type.Money price = 6;
  // price id, output only
  int64 price_id = 7;
  // product id, output only
  int64 product_id = 8;
  // the tax id
  int64 tax_id = 9;
  // billing cycle period
  moego.utils.v1.TimePeriod billing_cycle_period = 10;
  // billing cycle day of week, only for weekly billing cycle
  google.type.DayOfWeek billing_cycle_day_of_week = 11;
}

// discount benefit data
message DiscountBenefitData {
  // id
  int64 id = 1;
  // mid
  string mid = 2;
  // membership id
  int64 membership_id = 3;
  // target type
  models.membership.v1.TargetType target_type = 4;
  // target ids
  repeated int64 target_ids = 5;
  // is all
  bool is_all = 6;
  // discount unit
  models.membership.v1.DiscountUnit discount_unit = 7;
  // discount value
  double discount_value = 8;
  // feature id, output only
  int64 feature_id = 9;
}

// quantity benefit data
message QuantityBenefitData {
  // id
  int64 id = 1;
  // mid
  string mid = 2;
  // membership id
  int64 membership_id = 3;
  // target type
  models.membership.v1.TargetType target_type = 4;
  // target id
  int64 target_id = 5;
  // is limited
  bool is_limited = 6;
  // limited value
  int64 limited_value = 7;
  // period type
  models.membership.v1.PeriodType period_type = 8;
  // specified period
  moego.utils.v1.TimePeriod specified_period = 9;
  // feature id, output only
  int64 feature_id = 10;
}

// subscription data
message SubscriptionData {
  // id
  int64 id = 1;
  // mid
  string mid = 2;
  // membership id
  int64 membership_id = 3;
  // company id
  int64 company_id = 4;
  // business id
  int64 business_id = 5;
  // customer id
  int64 customer_id = 6;
  // the start time of the subscription period
  google.protobuf.Timestamp start_time = 7;
  // the end time of the subscription period
  google.protobuf.Timestamp end_time = 8;
}

// quantity entitlement data
message QuantityEntitlementData {
  // id
  int64 id = 1;
  // mid
  string mid = 2;
  // subscription id
  int64 subscription_id = 3;
  // benefit id
  int64 benefit_id = 4;
  // the remaining quantity of this entitlement
  int64 remaining = 5;
  // whether this entitlement has usage limits
  bool is_limited = 6;
}
