syntax = "proto3";

package moego.models.review_booster.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/review_booster/v1;reviewboosterpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.review_booster.v1";

// the review booster config model
message ReviewBoosterConfigModel {
  // review booster config id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;
  // enable automatic review booster
  bool is_enable_automatic = 4;
  // auto waiting minutes, Request a review X mins after checking out
  int32 auto_waiting_minutes = 5;
  // review body, Automatically sent review booster content
  string review_body = 6;
  // positive score, Positive reviews must be greater than or equal to this score
  int32 positive_score = 7;
  // positive review content
  string positive_body = 8;
  // yelp link
  string positive_yelp = 9;
  // facebook link
  string positive_facebook = 10;
  // google link
  string positive_google = 11;
  // negative review content
  string negative_body = 12;
  // create time
  google.protobuf.Timestamp create_time = 13;
  // update time
  google.protobuf.Timestamp update_time = 14;
}

// review booster config view for client app view
message ReviewBoosterConfigClientView {
  // business id
  int64 business_id = 1;
  // positive score, Positive reviews must be greater than or equal to this score
  int32 positive_score = 2;
}
