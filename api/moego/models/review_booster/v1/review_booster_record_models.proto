syntax = "proto3";

package moego.models.review_booster.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/review_booster/v1;reviewboosterpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.review_booster.v1";

// the review booster record view
message ReviewBoosterRecordView {
  // id
  int64 id = 1;
  // review booster id
  int64 review_booster_id = 3;
  // customer id
  int64 customer_id = 5;
  // positive score
  int32 positive_score = 6;
  // appointment id
  int64 appointment_id = 7;
  // appointment date
  string appointment_date = 8;
  // review content
  string review_content = 9;
  // review time
  int32 review_time = 10;
}
