syntax = "proto3";

package moego.models.customer.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.customer.v1";

// online booking pet definition
message PetDef {
  // pet type id
  optional int32 pet_type_id = 1 [(validate.rules).int32 = {
    in: [
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11
    ]
  }];
  // pet name
  optional string pet_name = 2 [(validate.rules).string = {max_len: 50}];
  // breed, from business settings
  optional string breed = 3 [(validate.rules).string = {max_len: 50}];
  // avatar path
  optional string avatar_path = 4 [(validate.rules).string = {max_len: 255}];
  // breed mix
  optional int32 breed_mix = 6 [(validate.rules).int32 = {
    in: [
      0,
      1
    ]
  }];
  // fixed name, from business settings
  optional string fixed = 7 [(validate.rules).string = {max_len: 50}];
  // birthday
  optional string birthday = 8 [(validate.rules).string = {
    max_len: 50
    ignore_empty: true
    pattern: "^(\\d{4}-\\d{2}-\\d{2})?$"
  }];
  // gender
  optional int32 gender = 9 [(validate.rules).int32 = {
    in: [
      0,
      1,
      2
    ]
  }];
  // hair length, from business settings
  optional string hair_length = 10 [(validate.rules).string = {max_len: 50}];
  // behavior, from business settings
  optional string behavior = 11 [(validate.rules).string = {max_len: 50}];
  // weight, units from business settings
  optional string weight = 12 [(validate.rules).string = {max_len: 50}];
  // vet address
  optional string vet_address = 13 [(validate.rules).string = {max_len: 100}];
  // vet name
  optional string vet_name = 14 [(validate.rules).string = {max_len: 100}];
  // vet phone
  optional string vet_phone = 15 [(validate.rules).string = {max_len: 30}];
  // emergency contact name
  optional string emergency_contact_name = 16 [(validate.rules).string = {max_len: 100}];
  // emergency contact phone
  optional string emergency_contact_phone = 17 [(validate.rules).string = {max_len: 30}];
  // health issues
  optional string health_issues = 18 [(validate.rules).string = {max_len: 3000}];
}
