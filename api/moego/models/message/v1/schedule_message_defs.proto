syntax = "proto3";

package moego.models.message.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// Custom content in schedule message model
message ScheduleMessageCustomDef {
  // custom content, the message manually entered by the user in the input box
  string content = 1 [(validate.rules).string = {max_len: 2048}];
  // receipt customer's contact id. If not specified, it will be sent to the primary phone by default.
  optional int64 receipt_contact_id = 2 [(validate.rules).int64 = {gt: 0}];
}
