syntax = "proto3";

package moego.models.message.v1;

import "google/protobuf/struct.proto";
import "moego/models/message/v1/notification_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// notification record model
message NotificationRecordModel {
  // record id
  int64 id = 1;
  // notification id
  int64 notification_id = 2;
  // business id
  int64 business_id = 3;
  // staff_id
  int64 staff_id = 4;
  // is success
  models.message.v1.NotificationRecordSendType is_success = 5;
  // status
  models.message.v1.NotificationRecordStatus status = 6;
  // send time
  int32 send_time = 7;
  // read time
  int32 read_time = 8;
  // company id
  int64 company_id = 9;
  // create time
  int32 create_time = 10;
  // update time
  int32 update_time = 11;
}

// notification model
message NotificationModel {
  // id
  int64 id = 1;
  // type
  string type = 2;
  // business id
  int64 business_id = 3;
  // company id
  int64 company_id = 4;
  // title
  string title = 5;
  // body
  string body = 6;
  //extra
  google.protobuf.Struct extra = 7;
  // create_time
  int32 create_time = 8;
}
