syntax = "proto3";

package moego.models.captcha.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/captcha/v1;captchapb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.captcha.v1";

// verification scenario
enum VerificationScenario {
  // unspecified
  VERIFICATION_SCENARIO_UNSPECIFIED = 0;

  // register
  VERIFICATION_SCENARIO_REGISTER = 1;

  // login
  VERIFICATION_SCENARIO_LOGIN = 2;

  // change password (login)
  VERIFICATION_SCENARIO_CHANGE_PASSWORD = 3;

  // forget password (no login)
  VERIFICATION_SCENARIO_FORGET_PASSWORD = 4;

  // todo: OB_LOGIN ?
}
