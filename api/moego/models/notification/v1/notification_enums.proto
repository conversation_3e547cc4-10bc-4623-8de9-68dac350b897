syntax = "proto3";

package moego.models.notification.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/notification/v1;notificationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.notification.v1";

// notification type
enum NotificationType {
  // unspecified
  NOTIFICATION_TYPE_UNSPECIFIED = 0;
  // submit booking request
  NOTIFICATION_TYPE_OB_REQUEST = 1;
  // appointment assigned
  NOTIFICATION_TYPE_APPT_ASSIGNED = 2;
  // invoice paid
  NOTIFICATION_TYPE_INVOICE_PAID = 3;
  // appointment cancelled
  NOTIFICATION_TYPE_APPT_CANCELLED = 4;
  // appointment rescheduled
  NOTIFICATION_TYPE_APPT_RESCHEDULED = 5;
  // appointment created
  NOTIFICATION_TYPE_APPT_CREATED = 6;
  // agreement signed
  NOTIFICATION_TYPE_AGREEMENT_SIGNED = 7;
  // review booster submitted
  NOTIFICATION_TYPE_REVIEW_SUBMITTED = 8;
  // new intake form submitted
  NOTIFICATION_TYPE_NEW_INTAKE_FORM = 9;
  // appointment confirmed by client
  NOTIFICATION_TYPE_APPT_CONFIRMED_BY_CLIENT = 10;
  // appointment cancelled by client
  NOTIFICATION_TYPE_APPT_CANCELLED_BY_CLIENT = 11;
  // sub payment fail
  NOTIFICATION_TYPE_SUB_PAYMENT_FAIL = 12;
  // sub payment success
  NOTIFICATION_TYPE_SUB_PAYMENT_SUCCESS = 13;
  // a2p fail
  NOTIFICATION_TYPE_A2P_FAIL = 14;
  // a2p required
  NOTIFICATION_TYPE_A2P_REQUIRED = 15;
  // ob request rescheduled
  NOTIFICATION_TYPE_OB_REQUEST_RESCHEDULE = 16;
  // ob request cancelled
  NOTIFICATION_TYPE_OB_REQUEST_CANCEL = 17;
  // renew online booking end date
  NOTIFICATION_TYPE_RENEW_ONLINE_BOOKING_END_DATE = 18;
  // ob abandoned record
  NOTIFICATION_TYPE_OB_ABANDONED = 19;
  // google reserve geo unmatched
  NOTIFICATION_TYPE_GOOGLE_RESERVE_GEO_UNMATCHED = 20;
  // google reserve geo matched
  NOTIFICATION_TYPE_GOOGLE_RESERVE_GEO_MATCHED = 21;

  // OB notification: Online booking settings -> notification
  // booking request sent
  NOTIFICATION_TYPE_BOOKING_REQUEST_SENT = 1000;
  // booking request accepted
  NOTIFICATION_TYPE_BOOKING_REQUEST_ACCEPTED = 1001;
  // booking request moved to wait list
  NOTIFICATION_TYPE_BOOKING_REQUEST_MOVED_TO_WAIT_LIST = 1002;
  // booking request declined
  NOTIFICATION_TYPE_BOOKING_REQUEST_DECLINED = 1003;
  // Schedule online booking waitlist
  NOTIFICATION_TYPE_SCHEDULE_ONLINE_BOOKING_WAITLIST = 1004;
  // Delete online booking waitlist
  NOTIFICATION_TYPE_DELETE_ONLINE_BOOKING_WAITLIST = 1005;
  // Appt auto message: Settings -> auto message -> auto message template
  // appointment booked
  NOTIFICATION_TYPE_APPOINTMENT_BOOKED = 1100;
  // appointment rescheduled
  NOTIFICATION_TYPE_APPOINTMENT_RESCHEDULED = 1101;
  // appointment cancelled
  NOTIFICATION_TYPE_APPOINTMENT_CANCELLED = 1102;
  // appointment first reminder
  NOTIFICATION_TYPE_APPOINTMENT_FIRST_REMINDER = 1103;
  // appointment second reminder
  NOTIFICATION_TYPE_APPOINTMENT_SECOND_REMINDER = 1104;
  // appointment general reminder
  NOTIFICATION_TYPE_APPOINTMENT_GENERAL_REMINDER = 1105;
  // appointment rebook reminder
  NOTIFICATION_TYPE_APPOINTMENT_REBOOK_REMINDER = 1106;
  // C App appointment tracking
  // when appointment is today
  NOTIFICATION_TYPE_APPOINTMENT_DAY = 1201;
  // when appointment is ready for pick up
  NOTIFICATION_TYPE_READY_TO_PICK_UP = 1202;
  // when appointment is finished, ask for review
  NOTIFICATION_TYPE_ASK_FOR_REVIEW = 1203;

  // when app message received
  NOTIFICATION_TYPE_NEW_APP_MESSAGE = 1301;

  // appointment tracking notification
  // start
  NOTIFICATION_TYPE_TRACKING_START = 1401;
  // delay slightly from last in transit
  NOTIFICATION_TYPE_APPOINTMENT_DELAY_SLIGHTLY = 1402;
  // delay seriously from last in transit
  NOTIFICATION_TYPE_APPOINTMENT_DELAY_SERIOUSLY = 1403;
  // delay slightly when last in transit finish on time
  NOTIFICATION_TYPE_APPOINTMENT_DELAY_SLIGHTLY_WHEN_LAST_TRACKING_FINISH_ON_TIME = 1404;
  // delay seriously when last in transit finish on time
  NOTIFICATION_TYPE_APPOINTMENT_DELAY_SERIOUSLY_WHEN_LAST_TRACKING_FINISH_ON_TIME = 1405;
}

// notification method
enum NotificationMethod {
  // unspecified
  NOTIFICATION_METHOD_UNSPECIFIED = 0;
  // business web
  NOTIFICATION_METHOD_BUSINESS_WEB = 1;
  // business app
  NOTIFICATION_METHOD_BUSINESS_APP = 2;
  // sms
  NOTIFICATION_METHOD_SMS = 3;
  // email
  NOTIFICATION_METHOD_EMAIL = 4;
  // pet parent app
  NOTIFICATION_METHOD_PET_PARENT_APP = 5;
}

// notification source
enum NotificationSource {
  // unspecified
  NOTIFICATION_SOURCE_UNSPECIFIED = 0;
  // MoeGo platform
  NOTIFICATION_SOURCE_PLATFORM = 1;
  // client
  NOTIFICATION_SOURCE_CLIENT = 2;
  // business
  NOTIFICATION_SOURCE_BUSINESS = 3;
}

// notification list sort field
enum NotificationSortField {
  // unspecified
  NOTIFICATION_SORT_FIELD_UNSPECIFIED = 0;
  // sent at
  NOTIFICATION_SORT_FIELD_SENT_AT = 1;
  // read at
  NOTIFICATION_SORT_FIELD_READ_AT = 2;
}

// app push device type
enum DeviceType {
  // unspecified
  DEVICE_TYPE_UNSPECIFIED = 0;
  // ios
  DEVICE_TYPE_IOS = 1;
  // android
  DEVICE_TYPE_ANDROID = 2;
}

// app push token source
enum PushTokenSource {
  // unspecified
  PUSH_TOKEN_SOURCE_UNSPECIFIED = 0;
  // business
  PUSH_TOKEN_SOURCE_BUSINESS = 1;
  // client
  PUSH_TOKEN_SOURCE_CLIENT = 2;
}
