// @since 2023-06-20 17:13:10
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.sms.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/sms/v1;smspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.sms.v1";

// The Sms model
message SmsModel {
  // the unique id
  int64 id = 1;
  // the create time
  google.protobuf.Timestamp created_at = 14;
  // the update time
  google.protobuf.Timestamp updated_at = 15;
}
