// @since 2023-06-20 17:13:10
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.sms.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/sms/v1;smspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.sms.v1";

// A demo enum, please delete me
enum InitTypeEnum {
  // subscription created
  INIT_TYPE_ENUM_SUBSCRIPTION_CREATED_UNSPECIFIED = 0;
  //business created
  INIT_TYPE_ENUM_BUSINESS_CREATED = 1;
}
