// @since 2025-03-05 10:27:11
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v2;

import "google/protobuf/timestamp.proto";
import "moego/models/appointment/v2/pricing_rule_apply_enums.proto";
import "moego/models/offering/v2/pricing_rule_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v2;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v2";

// pricing rule apply log model
message PricingRuleApplyLogModel {
  // the unique id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // company id
  int64 company_id = 3;
  // pet id
  int64 pet_id = 4;
  // service id
  int64 service_id = 5;
  // service date
  optional string service_date = 6;

  // original price, the price before apply the pricing rule
  double original_price = 7;
  // service price, the price after apply the pricing rule
  double adjusted_price = 8;

  // whether the pricing rule is used
  bool is_using_rule = 9;
  // the pricing rule
  moego.models.offering.v2.PricingRule pricing_rule = 10;
  // the create time
  google.protobuf.Timestamp created_at = 11;
  // the delete time
  optional google.protobuf.Timestamp deleted_at = 12;

  // the source id
  int64 source_id = 13;
  // source type
  PricingRuleApplySourceType source_type = 14;
}

// pricing rule apply log drawer view
message PricingRuleApplyLogDrawerView {
  // pet id
  int64 pet_id = 5;
  // service id
  int64 service_id = 6;
  // original price, the price before apply the pricing rule
  double original_price = 7;
  // service price, the price after apply the pricing rule
  double adjusted_price = 8;
  // the pricing rule
  moego.models.offering.v2.PricingRule pricing_rule = 9;
}
