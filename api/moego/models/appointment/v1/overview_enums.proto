// @since 2024-01-30 16:32:41
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// overview status
enum OverviewStatus {
  // unspecified
  OVERVIEW_STATUS_UNSPECIFIED = 0;
  // Expected
  EXPECTED = 1;
  // In-store
  IN_STORE = 2;
  // Ready to go
  READY_TO_GO = 3;
  // Finished
  APPOINTMENT_FINISHED = 4;
}

// overview date type
enum OverviewDateType {
  // unspecified
  OVERVIEW_DATE_TYPE_UNSPECIFIED = 0;
  // past
  PAST_DATE = 1;
  // now
  NOW_DATE = 2;
  // future
  FUTURE_DATE = 3;
}

// overview report type
enum OverviewReportStatus {
  // unspecified
  OVERVIEW_REPORT_STATUS_UNSPECIFIED = 0;
  // unsent
  UNSENT = 1;
  // sent
  SENT_TODAY = 2;
  // draft
  DRAFT = 3;
}
