// @since 2024-08-17 10:03:02
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/offering/v1/pricing_rule_enums.proto";
import "moego/models/offering/v1/pricing_rule_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// pricing rule apply log model
message PricingRuleApplyLogModel {
  // the unique id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // company id
  int64 company_id = 3;
  // appointment id
  int64 appointment_id = 4;
  // pet id
  int64 pet_id = 5;
  // service id
  int64 service_id = 6;
  // original price, the price before apply the pricing rule
  double original_price = 7;
  // service price, the price after apply the pricing rule
  double service_price = 8;
  // the pricing rule
  moego.models.offering.v1.PricingRuleModel pricing_rule = 9;
  // the create time
  google.protobuf.Timestamp created_at = 10;
  // the delete time
  optional google.protobuf.Timestamp deleted_at = 11;
  // service date
  optional string service_date = 12;
  // rule group type
  optional moego.models.offering.v1.RuleGroupType rule_group_type = 13;
}

// pricing rule apply log drawer view
message PricingRuleApplyLogDrawerView {
  // pet id
  int64 pet_id = 5;
  // service id
  int64 service_id = 6;
  // original price, the price before apply the pricing rule
  double original_price = 7;
  // service price, the price after apply the pricing rule
  double service_price = 8;
  // the pricing rule
  moego.models.offering.v1.PricingRuleModel pricing_rule = 9;
}
