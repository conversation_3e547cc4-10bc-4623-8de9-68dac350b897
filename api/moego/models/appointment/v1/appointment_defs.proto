syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/appointment/v1/appointment_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// Appointment create definition
message AppointmentCreateDef {
  // Selected customer id
  int64 customer_id = 1 [(validate.rules).int64.gt = 0];

  // Source of the appointment
  models.appointment.v1.AppointmentSource source = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // Color code of the appointment, in hex format
  string color_code = 3 [(validate.rules).string = {max_len: 16}];

  // Multi-pets start at the same time, default to false
  // Not persisted to the database, to avoid front-end calculations.
  bool all_pets_start_at_same_time = 4;
}

// Appointment definition
message AppointmentUpdateDef {
  // Color code of the appointment, in hex format
  optional string color_code = 1 [(validate.rules).string = {max_len: 16}];

  // Multi-pets start at the same time
  // Not persisted to the database, to avoid front-end calculations.
  optional bool all_pets_start_at_same_time = 2;
}

// Appointment schedule definition
message AppointmentScheduleDef {
  // Start date, in yyyy-MM-dd format
  string start_date = 1 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // End date, in yyyy-MM-dd format
  string end_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // start time, in minutes
  int32 start_time = 3 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // end time, in minutes
  int32 end_time = 4 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// Appointment calendar schedule definition
message AppointmentCalendarScheduleDef {
  // Start date, in yyyy-MM-dd format
  string start_date = 1 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // start time, in minutes
  int32 start_time = 2 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// Block appointment create definition
message BlockAppointmentCreateDef {
  // Color code of the appointment, in hex format
  string color_code = 1 [(validate.rules).string = {max_len: 16}];

  // Source of the appointment
  models.appointment.v1.AppointmentSource source = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Block appointment update definition
message BlockAppointmentUpdateDef {
  // Color code of the appointment, in hex format
  string color_code = 1 [(validate.rules).string = {max_len: 16}];
}

// Appointment create definition
message AppointmentCreateForOnlineBookingDef {
  // Selected customer id
  int64 customer_id = 1 [(validate.rules).int64.gt = 0];

  // status
  optional models.appointment.v1.AppointmentStatus status = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // cancel by staff id, 0 means operator is system
  optional int64 cancel_by = 6 [(validate.rules).int64.gte = 0];

  // booking request create time
  google.protobuf.Timestamp created_at = 7;
}

// The params message for reschedule grooming service
message GroomingRescheduleDef {
  // The grooming staff id
  optional int64 staff_id = 5 [(validate.rules).int64 = {gt: 0}];
  // The grooming start date
  optional string start_date = 6 [(validate.rules).string = {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}];
  // The grooming start time, in minutes since midnight
  optional int32 start_time = 7 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}
