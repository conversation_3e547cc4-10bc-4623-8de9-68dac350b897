syntax = "proto3";

package moego.models.appointment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// outbox message send status
enum OutboxSendStatus {
  // unspecified
  OUTBOX_SEND_STATUS_UNSPECIFIED = 0;
  // message to be sent
  SEND_PENDING = 1;
  // message sent succeed
  SEND_SUCCEED = 2;
  // message sent failed
  SEND_FAILED = 3;
}
