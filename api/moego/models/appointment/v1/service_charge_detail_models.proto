// @since 2025-05-01 15:22:53
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// ServiceChargeDetailModel corresponds to service_charge_detail table
message ServiceChargeDetailModel {
  // Primary key ID
  int32 id = 1;

  // Appointment ID
  int64 appointment_id = 2;

  // Service charge ID, foreign key to service_charge table
  int64 service_charge_id = 3;

  // Price of the service charge
  google.type.Money price = 4;

  // Price override type
  moego.models.offering.v1.ServiceOverrideType price_override_type = 5;

  // Tax ID, foreign key to moe_business_tax table
  int32 tax_id = 6;

  // Creation timestamp
  google.protobuf.Timestamp created_at = 7;

  // Update timestamp
  google.protobuf.Timestamp updated_at = 8;

  // Delete timestamp
  google.protobuf.Timestamp deleted_at = 9;
}
