syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// daycare service 的 auto rollover 记录
message DaycareAutoRolloverRecordModel {
  // id
  int64 id = 1;
  // 对应 moe_grooming.moe_grooming_pet_detail.id
  int64 daycare_service_detail_id = 2;
  // daycare service id
  int64 service_id = 3;
  // 1: pending, 2: processing, 3: success, 4: failed
  Status status = 4;
  // rollover 触发时间
  google.protobuf.Timestamp rollover_time = 5;

  // DaycareAutoRolloverRecordModel status
  enum Status {
    // Unspecified
    STATUS_UNSPECIFIED = 0;
    // pending
    PENDING = 1;
    // processing
    PROCESSING = 2;
    // success
    SUCCESS = 3;
    // failed
    FAILED = 4;
  }
}
