// @since 2025-04-02 17:04:12
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v1;

import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// Split lodging definition
message BoardingSplitLodgingScheduleDef {
  // selected lodging id
  int64 lodging_id = 1 [(validate.rules).int64.gt = 0];

  // start date, in yyyy-MM-dd format
  string start_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // end date, in yyyy-MM-dd format
  string end_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // start time, in minutes
  int32 start_time = 4 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];

  // end time, in minutes
  int32 end_time = 5 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];

  // price
  google.type.Money price = 6 [(validate.rules).message.required = true];

  // lodging is applicable
  bool is_applicable = 7;
}

// Split lodging definition
message BoardingSplitLodgingDetailDef {
  // selected lodging id
  int64 lodging_id = 1 [(validate.rules).int64.gt = 0];
  // start date, in yyyy-MM-dd format
  string start_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // end date, in yyyy-MM-dd format
  string end_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // start time, in minutes
  int32 start_time = 4 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // end time, in minutes
  int32 end_time = 5 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // price
  google.type.Money price = 6 [(validate.rules).message.required = true];
  // lodging unit name
  string lodging_unit_name = 7 [(validate.rules).string = {min_len: 1}];
  // lodging type id
  int64 lodging_type_id = 8 [(validate.rules).int64.gt = 0];
  // lodging type name
  string lodging_type_name = 9 [(validate.rules).string = {min_len: 1}];

  // lodging is applicable
  bool is_applicable = 10;
}
