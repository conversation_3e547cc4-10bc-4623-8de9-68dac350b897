syntax = "proto3";

package moego.models.automation.v1;

import "google/type/date.proto";
import "google/type/dayofweek.proto";
import "google/type/timeofday.proto";
import "moego/models/automation/v1/common.proto";
import "moego/models/reporting/v2/common_model.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/automation/v1;automationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.automation.v1";

// Step
message Step {
  // Type
  enum Type {
    // TYPE_UNSPECIFIED
    TYPE_UNSPECIFIED = 0;
    // TRIGGER
    TRIGGER = 1;
    // WAIT
    WAIT = 2;
    // MESSAGE
    MESSAGE = 3;
    // CONDITION
    CONDITION = 4;
    // ADVANCED ACTION
    ADVANCED_ACTION = 5;
  }
  // Data
  message Data {
    // Step data
    oneof data {
      // Trigger
      Trigger trigger = 1;
      // Wait
      Wait wait = 2;
      // Message
      Message message = 3;
      // Condition
      Condition condition = 4;
      // Advanced action
      AdvancedAction advanced_action = 5;
    }
  }
  // Preview Data
  message PreviewData {
    // preview description
    string description = 1;
  }

  // meta data
  // Step ID
  string id = 1;
  // Workflow ID
  int64 workflow_id = 2;
  // Step name
  string name = 3;
  // Step description
  string description = 4;

  // adjacency data
  // Parent step ID
  string parent_id = 5;
  // Children step IDs
  repeated string children_ids = 6;
  // Hierarchical path
  string hierarchical_path = 7;

  // content data
  // Step type
  Type type = 8;
  // Step data
  Data data = 9;
  // Step data preview
  optional PreviewData preview_data = 10;
}

// Trigger
message Trigger {
  // Type
  enum Type {
    // TYPE_UNSPECIFIED
    TYPE_UNSPECIFIED = 0;
    // SCHEDULED
    SCHEDULED = 1;
    // EVENT
    EVENT = 2;
    // BEFORE
    BEFORE = 3;
  }
  // Data
  message Data {
    // Trigger data
    oneof data {
      // Scheduled
      Scheduled scheduled = 1;
      // Event
      Event event = 2;
    }
  }
  // Trigger type
  Type type = 1;
  // Trigger data
  Data data = 2;
  // Filters
  repeated models.reporting.v2.FilterRequest filters = 3;
  // Trigger name
  string name = 4;
}

// Scheduled
message Scheduled {
  // Frequency
  enum Frequency {
    // FREQUENCY_UNSPECIFIED
    FREQUENCY_UNSPECIFIED = 0;
    // DAILY
    DAILY = 1; // 每天
    // WEEKLY
    WEEKLY = 2; // 每周
    // MONTHLY
    MONTHLY = 3; // 每月
  }
  // Day of week
  google.type.DayOfWeek day_of_week = 1; // 适用于每周调度，表示星期几（0=周日, 1=周一, ..., 6=周六）
  // Time of day
  google.type.TimeOfDay time_of_day = 2; // 适用于每天调度, 一天中的具体时间
  // Date
  google.type.Date date = 3; // 适用于每月调度，表示几号（1-31）
  // Frequency
  Frequency frequency = 4; // 调度频率
}

// Event
message Event {
  // Category
  enum Category {
    // CATEGORY_UNSPECIFIED
    CATEGORY_UNSPECIFIED = 0;
    // ONLINE_BOOKINGS
    ONLINE_BOOKINGS = 1;
    // APPOINTMENT
    APPOINTMENT = 2;
    // INTAKE_FROM
    INTAKE_FROM = 3;
    // PACKAGE
    PACKAGE = 4;
    // MEMBERSHIP
    MEMBERSHIP = 5;
    // DISCOUNT
    DISCOUNT = 6;
    // CLIENT
    CLIENT = 7;
  }
  // EntityTrigger
  enum EntityTrigger {
    // ENTITY_UNSPECIFIED
    ENTITY_UNSPECIFIED = 0;

    // ONLINE_BOOKINGS_SUBMITTED
    ONLINE_BOOKINGS_SUBMITTED = 100;
    // ONLINE_BOOKINGS_ABANDONED
    ONLINE_BOOKINGS_ABANDONED = 101;
    // ONLINE_BOOKINGS_ACCEPTED
    ONLINE_BOOKINGS_ACCEPTED = 102;
    // EVALUATION_BOOKING_SUBMITTED
    EVALUATION_BOOKING_SUBMITTED = 103;

    // APPOINTMENT_CREATED
    APPOINTMENT_CREATED = 200;
    // APPOINTMENT_CANCELED
    APPOINTMENT_CANCELED = 201;
    // APPOINTMENT_FINISHED
    APPOINTMENT_FINISHED = 202;
    // APPOINTMENT_FULLY_PAID
    APPOINTMENT_FULLY_PAID = 203;
    // EVALUATION_FINISHED
    EVALUATION_FINISHED = 204;

    // PACKAGE_PURCHASED
    PACKAGE_PURCHASED = 400;
    // PACKAGE_REDEEMED
    PACKAGE_REDEEMED = 401;

    // MEMBERSHIP_PURCHASED
    MEMBERSHIP_PURCHASED = 500;
    // MEMBERSHIP_CANCELED
    MEMBERSHIP_CANCELED = 501;

    // DISCOUNT_CODE_REDEEMED
    DISCOUNT_CODE_REDEEMED = 600;

    // CLIENT_CREATED
    CLIENT_CREATED = 700;
  }
  // Event category
  Category category = 1;
  // Event trigger
  EntityTrigger trigger = 2;
  // Event Filters
  repeated models.reporting.v2.FilterRequest filters = 3;
}

// Before
message Before {}

// Wait
message Wait {
  // Type
  enum Type {
    // TYPE_UNSPECIFIED
    TYPE_UNSPECIFIED = 0;
    // DURATION
    DURATION = 1;
  }

  // Wait type
  Type type = 1;
  // Time duration
  TimeDuration time_duration = 2;
}

// Condition
message Condition {
  // Type
  enum Type {
    // TYPE_UNSPECIFIED
    TYPE_UNSPECIFIED = 0;

    // Filter
    FILTER = 1;
    // Action Result Filter
    ACTION_FILTER = 2;
  }

  // Filters
  repeated models.reporting.v2.FilterRequest filters = 1;
  // Next step ID if true
  string next_step_id_true = 2;
  // Next step ID if false
  string next_step_id_false = 3;
  // Type
  Type type = 4;
  // Action Filters
  ActionResultFilter action_result_filter = 5;
}

// ActionResultFilter
enum ActionResultFilter {
  // TYPE_UNSPECIFIED
  ACTION_FILTER_UNSPECIFIED = 0;

  // INTAKE_FORM_SUBMIT_SUCCESS
  INTAKE_FORM_SUBMIT_SUCCESS = 1;
}

// Message
message Message {
  // Type
  enum Type {
    // TYPE_UNSPECIFIED
    TYPE_UNSPECIFIED = 0;
    // SMS
    SMS = 1;
    // EMAIL
    EMAIL = 2;
  }
  // Category
  enum Category {
    // CATEGORY_UNSPECIFIED
    CATEGORY_UNSPECIFIED = 0;
    // NOTIFICATION
    NOTIFICATION = 1;
    // CAMPAIGN
    CAMPAIGN = 2;
  }
  // Data
  message Data {
    // Action data
    oneof data {
      // SMS data
      SMSData sms_data = 1;
      // Email data
      EmailData email_data = 2;
    }
  }

  // Action type
  Type type = 1;
  // Action category
  Category category = 2;

  // Action data
  Data data = 10;
}

// SMSData
message SMSData {
  // Content
  string content = 1;
}

// EmailData
message EmailData {
  // Content
  string content = 1;
  // Title
  string title = 2;
}

// AdvancedAction
message AdvancedAction {
  // Type
  enum Type {
    // TYPE_UNSPECIFIED
    TYPE_UNSPECIFIED = 0;
    // ADD_CLIENT_TAG
    ADD_CLIENT_TAG = 1;
    // DELETE_CLIENT_TAG
    DELETE_CLIENT_TAG = 2;
    // CHANGE_CLIENT_TAG
    CHANGE_CLIENT_TAG = 3;
    // ADD_PET_CODE
    ADD_PET_CODE = 4;
    // CHANGE_CUSTOMER_LIFE_CYCLE
    CHANGE_CUSTOMER_LIFE_CYCLE = 5;
    // CHANGE_CUSTOMER_ACTION_STATUS
    CHANGE_CUSTOMER_ACTION_STATUS = 6;
    // ADD_CUSTOMER_TASK
    ADD_CUSTOMER_TASK = 7;
  }
  // Data
  message Data {
    // AdvancedAction data
    oneof data {
      // Add Client Tag
      AddClientTagData add_client_tag_data = 1;
      // Delete Client Tag
      DeleteClientTagData delete_client_tag_data = 2;
      // Change Client Tag
      ChangeClientTagData change_client_tag_data = 3;
      // Add Pet Code
      AddPetCodeData add_pet_code_data = 4;
      // Change Customer Life Cycle
      ChangeCustomerLifeCycleData change_customer_life_cycle_data = 5;
      // Change Customer Action Status
      ChangeCustomerActionStatusData change_customer_action_status_data = 6;
      // Add Customer Task
      AddCustomerTaskData add_customer_task_data = 7;
    }
  }

  // type
  Type type = 1;

  // data
  Data data = 10;
}

// AddClientData
message AddClientTagData {
  // Customer Tag ids
  repeated int64 customer_tag_ids = 1;
}

// DeleteClientData
message DeleteClientTagData {
  // Customer Tag ids
  repeated int64 customer_tag_ids = 1;
}

// ChangeClientData
message ChangeClientTagData {
  // Source Customer Tag ids
  repeated int64 source_customer_tag_ids = 1;
  // Target Customer Tag ids
  repeated int64 target_customer_tag_ids = 2;
}

// AddPetCodeData
message AddPetCodeData {
  // Pet Code ids
  repeated int64 pet_code_ids = 1;
}

// ChangeCustomerLifeCycle
message ChangeCustomerLifeCycleData {
  // Life Cycle, definition on https://github.com/MoeGolibrary/moego/blob/main/backend/proto/customer/v1/customer_models.proto
  int32 life_cycle = 1;
}

// ChangeCustomerActionStatus
message ChangeCustomerActionStatusData {
  // Action Status, definition on https://github.com/MoeGolibrary/moego/blob/main/backend/proto/customer/v1/customer_models.proto
  int32 action_status = 1;
}

// AddCustomerTaskData
message AddCustomerTaskData {
  // 任务名称
  string name = 1;
  // 分配员工
  optional int64 allocate_staff_id = 2;
}

// Branch
message Branch {}
