syntax = "proto3";

package moego.models.automation.v1;

import "moego/models/automation/v1/step.proto";
import "moego/models/reporting/v2/filter_model.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/automation/v1;automationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.automation.v1";

// filter groups works for event
message EventFilterGroups {
  // category
  Event.Category category = 1;
  // filter group
  repeated models.reporting.v2.FilterGroup filter_groups = 2;
}
