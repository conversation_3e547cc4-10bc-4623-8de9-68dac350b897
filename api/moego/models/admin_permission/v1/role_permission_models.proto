// @since 2023-05-27 21:48:13
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.admin_permission.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/admin_permission/v1/role_permission_defs.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/admin_permission/v1;adminpermissionpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.admin_permission.v1";

// the permission points, not a model
message RolePermissionModel {
  // id
  int64 id = 1;
  // role id
  int64 role_id = 2;
  // permission point
  string permission = 3;
  // the filters
  repeated FilterDef filters = 4;

  // the operator id
  string operator_id = 12;
  // the create time
  google.protobuf.Timestamp created_at = 13;
  // the update time
  optional google.protobuf.Timestamp updated_at = 14;
  // the delete time
  optional google.protobuf.Timestamp deleted_at = 15;
}
