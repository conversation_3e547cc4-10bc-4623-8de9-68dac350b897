// @since 2023-06-06 18:51:01
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.admin_permission.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/admin_permission/v1;adminpermissionpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.admin_permission.v1";

// permission type
// move here to avoid import permission enums
enum PermissionType {
  // unspecified
  PERMISSION_TYPE_UNSPECIFIED = 0;
  // group
  PERMISSION_TYPE_GROUP = 1;
  // sub group
  PERMISSION_TYPE_SUB_GROUP = 2;
  // point
  PERMISSION_TYPE_POINT = 3;
}

// permission model
message PermissionModel {
  // the key without PERMISSION_ prefix
  string value = 1;
  // the permission type
  PermissionType type = 2;
  // the full label: Group > Sub group > Full name
  string label = 3;
  // the label, drop the group/sub group prefix: Full name
  string short_label = 4;
  // the parent id
  optional string parent = 5;
  // the children, for group/choice
  repeated PermissionModel children = 6;
  // is deprecated or not
  bool disabled = 7;
}
