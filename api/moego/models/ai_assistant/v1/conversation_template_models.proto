// @since 2023-07-03 12:43:02
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.ai_assistant.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/ai_assistant/v1;aiassistantpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.ai_assistant.v1";

// The ConversationTemplate model
message ConversationTemplateModel {
  // the unique id
  int64 id = 1;
  // the scenario
  string scenario = 2;
  // the template
  string template = 3;
  // the trailer_template
  string trailer_template = 4;
  // the temperature, default is 1
  double temperature = 5;

  // the operator id
  string operator_id = 12;
  // the create time
  google.protobuf.Timestamp created_at = 13;
  // the update time
  google.protobuf.Timestamp updated_at = 14;
  // the delete time
  optional google.protobuf.Timestamp deleted_at = 15;
}
