// @since 2024-07-11 14:21:59
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.ai_assistant.v2;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/ai_assistant/v2;aiassistantpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.ai_assistant.v2";

// The ChatAssistant model
message ConversationModel {
  // the unique id
  int64 id = 1;
  // the create time
  google.protobuf.Timestamp created_at = 14;
  // the update time
  google.protobuf.Timestamp updated_at = 15;
}
