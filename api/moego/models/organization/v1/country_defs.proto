syntax = "proto3";

package moego.models.organization.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// Country
message CountryDef {
  // country
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // country code, ISO 3166-1 alpha-2
  string code = 2 [(validate.rules).string.len = 2];
}
