syntax = "proto3";

package moego.models.organization.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// tax model
message TaxRuleModel {
  // tax id
  int64 id = 1;
  // tax name
  string name = 2;
  // tax rate
  double rate = 3;
  // company id
  int64 company_id = 4;
  // is deleted
  bool is_deleted = 5;
}

// tax model public view
message TaxRuleModelPublicView {
  // tax id
  int64 id = 1;
  // tax name
  string name = 2;
  // tax rate
  double rate = 3;
}
