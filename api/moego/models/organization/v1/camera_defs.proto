syntax = "proto3";

package moego.models.organization.v1;

import "moego/models/organization/v1/camera_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

//idogcam filter
message IdogcamFilter {
  //kennel id
  optional string kennel_id = 1 [(validate.rules).string = {max_len: 255}];
  //erp code
  optional string erp_code = 2 [(validate.rules).string = {max_len: 255}];
}

//abckam filter
message AbckamFilter {
  //abckam_id
  optional string abckam_id = 1 [(validate.rules).string = {max_len: 255}];
}

//camera filter
message CameraFilter {
  // is active
  optional bool is_active = 1;
  // visibility type
  optional models.organization.v1.VisibilityType visibility_type = 2;
  // relation business id
  optional int64 relation_business_id = 3;
}
