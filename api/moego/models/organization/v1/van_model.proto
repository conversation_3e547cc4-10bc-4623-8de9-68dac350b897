syntax = "proto3";

package moego.models.organization.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// VanModel
message VanModel {
  // van id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // nick name
  string nick_name = 3;
  // avatar path
  string avatar_path = 4;
  // date in service
  string date_in_service = 5;
  // license plate
  string license_plate = 6;
  // note
  string note = 7;
  // model
  string model = 8;
  // make
  string make = 9;
  // sort
  int32 sort = 10;
  // is deleted
  bool is_deleted = 11;
  // created time
  google.protobuf.Timestamp created_time = 12;
  // updated time
  google.protobuf.Timestamp updated_time = 13;
  // company id
  int64 company_id = 14;
}

// Van Model list
message VanListModel {
  //all vans
  repeated VanModel vans = 1;
}
