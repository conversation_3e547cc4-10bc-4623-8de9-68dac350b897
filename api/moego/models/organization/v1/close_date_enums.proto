syntax = "proto3";

package moego.models.organization.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// enum for close date type
enum CloseDateType {
  // UNSPECIFIED is the default value
  CLOSE_DATE_TYPE_UNSPECIFIED = 0;
  // Normal close date
  NORMAL = 1;
  // Holiday
  HOLIDAY = 2;
}
