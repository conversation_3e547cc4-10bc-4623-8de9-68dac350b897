syntax = "proto3";

package moego.models.organization.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// staff employee category enum
enum ServiceCommissionType {
  // company staff
  SERVICE_COMMISSION_TYPE_UNSPECIFIED = 0;
  // fixed rate
  SERVICE_COMMISSION_TYPE_FIXED_RATE = 1;
  // tier rate
  SERVICE_COMMISSION_TYPE_TIER_RATE = 2;
}

// tier type
enum TierType {
  // tier type unspecified
  TIER_TYPE_UNSPECIFIED = 0;
  // tier type - sliding scale
  TIER_TYPE_SLIDING_SCALE = 1;
  // tier type - progressive
  TIER_TYPE_PROGRESSIVE = 2;
}
