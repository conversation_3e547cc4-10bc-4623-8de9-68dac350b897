syntax = "proto3";

package moego.models.organization.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.organization.v1";

// tax rule definition
message TaxRuleDef {
  // tax name
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // tax rate
  double rate = 2 [(validate.rules).double = {gte: 0}];
}
