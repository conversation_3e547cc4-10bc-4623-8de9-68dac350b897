syntax = "proto3";

package moego.models.reporting.v2;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2;reportingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.reporting.v2";

// ReportingType
enum ReportingScene {
  // Unspecified reporting type
  REPORTING_TYPE_UNSPECIFIED = 0;
  // The reporting type is common
  COMMON = 1;
  // The reporting type is enterprise hub
  ENTERPRISE_HUB = 2;
  // Mobile app reporting
  APP = 3;
}

// A value of any type
message Value {
  // The value
  oneof value {
    // The string value
    string string = 2;
    // The double value
    double double = 3;
    // The int64 value
    int64 int64 = 4;
    // The bool value
    bool bool = 6;
    // The money value
    google.type.Money money = 7;
    // The timestamp value
    google.protobuf.Timestamp timestamp = 8;
  }
}

// Enumeration of different filter parameter operator types
enum Operator {
  // Unspecified operator
  OPERATOR_UNSPECIFIED = 0;
  // Equal to
  EQUAL = 1;
  // Not equal to
  NOT_EQUAL = 2;
  // In a collection
  IN = 3;
  // Not in a collection
  NOT_IN = 4;
  // Like multiple values
  LIKE_MULTI = 5;
  // Like a value
  LIKE = 6;
  // Not like a value
  NOT_LIKE = 7;
  // Prefix like a value
  PREFIX_LIKE = 8;
  // Suffix like a value
  SUFFIX_LIKE = 9;
  // Greater than a value
  GREATER_THAN = 10;
  // Less than a value
  LESS_THAN = 11;
  // Greater than or equal to a value
  GREATER_THAN_OR_EQUAL = 12;
  // Less than or equal to a value
  LESS_THAN_OR_EQUAL = 13;
  // After a certain time
  AFTER = 14;
  // Before a certain time
  BEFORE = 15;
  // On a certain date
  ON = 16;
  // Within a range
  RANGE = 17;
  // Array contains
  ARRAY_CONTAINS = 18;
  // Array not contains
  ARRAY_NOT_CONTAINS = 19;
}

// TokenInfo for request
message TokenInfo {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // staff id
  optional int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// Insight report tab type
enum TabType {
  // Unspecified tab type
  TAB_TYPE_UNSPECIFIED = 0;
  // Dashboard tab
  DASHBOARD_TAB = 1;
  // Report tab
  REPORT_TAB = 2;
  // Enterprise Dashboard tab
  ENTERPRISE_DASHBOARD_TAB = 3;
  // Enterprise Report tab
  ENTERPRISE_REPORT_TAB = 4;
  // App Dashboard tab
  APP_DASHBOARD_TAB = 5;
  // App Report tab
  APP_REPORT_TAB = 6;
}

// A report configuration
message DrillConfig {
  // target type
  enum TargetType {
    // unspecified
    TARGET_TYPE_UNSPECIFIED = 0;
    // report table
    TARGET_TYPE_REPORT_TABLE = 1;
  }
  // drill type
  TargetType target_type = 1;
  // The target report ID
  string target_id = 2;
  // The filter of drill config
  repeated FilterRequest filters = 3;
  // The title of the drill
  optional string title = 4;
}

// Style config for diagram
message StyleConfig {
  // Line type enumeration
  enum LineType {
    // Unspecified line type
    LINE_TYPE_UNSPECIFIED = 0;
    // Solid line type
    SOLID = 1;
    // Dashed line type
    DASHED = 2;
  }
  // Format enumeration
  enum Format {
    // Unspecified format
    FORMAT_UNSPECIFIED = 0;
    // Duration field format by min
    DURATION_MIN = 1;
    // Duration field format by hour
    DURATION_HOUR = 2;
    // Money field format by abbreviation
    MONEY_ABBREVIATION = 3;
    // Rating star field format
    RATING_STAR = 4;
    // Percentage field displayed as bar
    PERCENTAGE_BAR = 5;
  }
  // Icon enumeration
  enum Icon {
    // Unspecified icon
    ICON_UNSPECIFIED = 0;
    // minor refresh
    MINOR_REFRESH_OUTLINED = 1;
    // minor dollar
    MINOR_DOLLAR_OUTLINED = 2;
    // minor give dollar
    MINOR_GIVE_DOLLAR_OUTLINED = 3;
    // minor paws
    MINOR_PAWS_OUTLINED = 4;
    // contact
    MINOR_CONTRACT_OUTLINED = 5;
    // heart
    MINOR_HEART_OUTLINED = 6;
    // clock
    MINOR_CLOCK_OUTLINED = 7;
    // calendar
    MINOR_CALENDAR_OUTLINED = 8;
    // user
    MINOR_USER_OUTLINED = 9;
    // ticket
    MINOR_TICKET_OUTLINED = 10;
  }

  // Color value
  string color = 1;
  // Line type only for line chart
  optional LineType line_type = 2;
  // Stack name for bar chart, same stack will overlap
  optional string stack = 3;
  // Format of the data
  optional Format format = 4;
  // Field icon
  optional Icon icon = 5;
  // Style map for dynamic rows
  map<string, StyleConfig> sub_style_map = 6;
}

// Filter params definition
message FilterRequest {
  // The field key of the filter parameter
  string field_key = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // The operator of the filter parameter
  Operator operator = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // The option value or input values
  repeated Value values = 3;
  // The label of the filter option
  optional string label = 4;
  // The preview value of the filter option
  optional string value_preview = 5;
  // Invert select values
  optional bool invert_select = 6;
}

// Filter group params definition
message FilterRequestGroup {
  // The group name, = FilterGroup.group_name
  string group_name = 1;
  // The filters
  repeated FilterRequest filters = 2;
}

// Enumeration of option type, determined which resource to fetch when fill the filter's options
enum OptionType {
  // Unspecified option type
  OPTION_TYPE_UNSPECIFIED = 0;
  // Fixed options, saved in report meta, no need to fetch
  FIXED_OPTIONS = 1;
  // Location
  LOCATION = 2;
  // Payment method
  PAYMENT_METHOD = 3;
  // Staff
  STAFF = 4;
  // Service
  SERVICE = 5;
  // Add-on
  ADD_ON = 6;
  // Service charge
  SERVICE_CHARGE = 7;
  // Package
  PACKAGE = 8;
  // Product
  PRODUCT = 9;
}

// Trend type
enum Trend {
  // Unspecified trend
  TREND_UNSPECIFIED = 0;
  // benefit matrix type
  BENEFIT = 1;
  // harmful matrix type
  HARMFUL = 2;
  // neutral matrix type
  NEUTRAL = 3;
}

// Insights tab enum
enum InsightsTab {
  // Unspecified page tab
  INSIGHTS_TAB_UNSPECIFIED = 0;
  // all
  ALL = 1;
  // overview tab
  OVERVIEW = 2;
  // sales tab
  SALES = 3;
  // clients & pets tab
  CLIENT_INSIGHTS = 4;
  // staff tab
  EMPLOYEE = 5;
  // operation tab
  APPOINTMENT = 6;
  // finance tab
  FINANCE = 7;
  // tenant tab
  TENANT = 8;
  // payroll tab
  PAYROLL = 9;
  // Customized report tab
  CUSTOMIZED = 10;
  // Legacy reports tab for mobile app, will be deprecated after mobile report redesign
  LEGACY_APPOINTMENT = 101;
}
