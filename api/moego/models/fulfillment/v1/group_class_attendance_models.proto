// @since 2025-04-07 15:32:56
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.fulfillment.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1;fulfillmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.fulfillment.v1";

// The GroupClassAttendance model
message GroupClassAttendanceModel {
  // The unique identifier of the model
  int64 id = 1;

  // The fulfillment id
  int64 fulfillment_id = 2;

  // The pet id
  int64 pet_id = 3;

  // The group class detail id
  int64 group_class_detail_id = 4;

  // The group class session id
  int64 group_class_session_id = 5;

  // The check in time
  google.protobuf.Timestamp check_in_time = 6;

  // the create time
  google.protobuf.Timestamp created_at = 13;
  // the update time
  google.protobuf.Timestamp updated_at = 14;
  // the delete time, non-null means is deleted
  optional google.protobuf.Timestamp deleted_at = 15;
}
