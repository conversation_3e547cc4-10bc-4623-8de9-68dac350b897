// @since 2024-09-19 21:27:56
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.models.offering.v1;

import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// The ServiceVaccineRequirement model
message ServiceVaccineRequirementModel {
  // the unique id of this record
  int64 id = 1;
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 2;
  // vaccine id
  int64 vaccine_id = 3;
}
