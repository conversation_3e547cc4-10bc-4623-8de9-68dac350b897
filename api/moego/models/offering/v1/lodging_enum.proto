syntax = "proto3";

package moego.models.offering.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// type enum for lodging type
// property of all lodging unit under one lodging type
enum LodgingUnitType {
  // Unspecified lodging unit type
  LODGING_UNIT_TYPE_UNSPECIFIED = 0;
  // Room/kennel type
  ROOM = 1;
  // Area type
  AREA = 2;
}
