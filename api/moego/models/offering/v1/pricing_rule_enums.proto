// @since 2024-07-30 11:14:10
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.offering.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// rule item type
enum RuleItemType {
  // unspecified value
  RULE_ITEM_TYPE_UNSPECIFIED = 0;
  // for multiple pet
  MULTIPLE_PET = 1;
  // for multiple night
  MULTIPLE_NIGHT = 2;
}

// rule apply type
enum RuleApplyType {
  // unspecified value
  RULE_APPLY_TYPE_UNSPECIFIED = 0;
  // apply to each one
  APPLY_TO_EACH = 1;
  // apply to additional
  APPLY_TO_ADDITIONAL = 2;
}

// rule price type
enum RulePriceType {
  // unspecified value
  RULE_PRICE_TYPE_UNSPECIFIED = 0;
  // fixed discount, e.g. $100
  FIXED_DISCOUNT = 1;
  // percentage discount, e.g. 10%
  PERCENTAGE_DISCOUNT = 2;
  // fixed increase, e.g. $100
  FIXED_INCREASE = 3;
  // percentage increase, e.g. 10%
  PERCENTAGE_INCREASE = 4;
}

// rule group type
enum RuleGroupType {
  // unspecified value
  RULE_GROUP_TYPE_UNSPECIFIED = 0;
  // common
  COMMON = 1;
  // peak date
  PEAK_DATE = 2;
}

// rule apply choice type
enum RuleApplyChoiceType {
  // unspecified value
  RULE_GROUP_APPLY_CHOICE_UNSPECIFIED = 0;
  // sequence
  SEQUENCE = 1;
  // choose highest
  CHOOSE_HIGHEST = 2;
}
