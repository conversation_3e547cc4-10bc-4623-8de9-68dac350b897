syntax = "proto3";

package moego.models.offering.v1;

import "moego/models/offering/v1/lodging_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

/*
 * lodging type list model
 */
message LodgingTypeView {
  // id of the lodging type
  int64 id = 1;
  // name of the lodging type
  string name = 2;
  // description of the lodging type
  string description = 3;
  // images of this lodging type
  repeated string photo_list = 4;
  // max pet number of this lodging type
  int32 max_pet_num = 5;
  // max pet total weight of this lodging type
  int32 max_pet_total_weight = 6 [deprecated = true];
  // available for all pet size
  bool all_pet_sizes = 7 [deprecated = true];
  // available pet size (only if is_available_for_all_pet_size is false)
  // moe_pet_size.id list
  repeated int64 pet_size_ids = 8;
  // lodging unit type in this lodging type
  moego.models.offering.v1.LodgingUnitType lodging_unit_type = 9;
  // whether the lodging type is available for all pet size
  bool pet_size_filter = 10;
  // Sort for the lodging type
  int32 sort = 11;
}

/*
 * lodging type list model
 */
message LodgingTypeModel {
  // id of the lodging type
  int64 id = 1;
  // name of the lodging type
  string name = 2;
  // description of the lodging type
  string description = 3;
  // images of this lodging type
  repeated string photo_list = 4;
  // max pet number of this lodging type
  int32 max_pet_num = 5;
  // max pet total weight of this lodging type
  int32 max_pet_total_weight = 6 [deprecated = true];
  // available for all pet size
  bool all_pet_sizes = 7 [deprecated = true];
  // available pet size (only if is_available_for_all_pet_size is false)
  // moe_pet_size.id list
  repeated int64 pet_size_ids = 8;
  // lodging unit type in this lodging type
  moego.models.offering.v1.LodgingUnitType lodging_unit_type = 9;
  // whether the lodging type is available for all pet size
  bool pet_size_filter = 10;
  // Sort for the lodging type
  int32 sort = 11;
}
