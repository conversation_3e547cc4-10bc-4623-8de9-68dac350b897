syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/grooming/v1/service_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// Service book online config model
message ServiceConfigModel {
  // unique id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;
  // service id
  int64 service_id = 4;
  // service price display type in online booking
  models.grooming.v1.ShowBasePrice show_base_price = 5;
  // service available in online booking
  bool book_online_available = 6;
  // is all staff available
  bool is_all_staff = 7;
  // createdAt
  google.protobuf.Timestamp created_at = 8;
  // updatedAt
  google.protobuf.Timestamp updated_at = 9;
}

// Service book online config view
message ServiceConfigView {
  // service id
  int64 service_id = 1;
  // service price display type in online booking
  models.grooming.v1.ShowBasePrice show_base_price = 2;
}
