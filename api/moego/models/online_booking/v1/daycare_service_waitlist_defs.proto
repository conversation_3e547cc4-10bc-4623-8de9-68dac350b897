syntax = "proto3";

package moego.models.online_booking.v1;

import "google/type/date.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// boarding waitlist
message DaycareWaitlist {
  // specific dates
  repeated google.type.Date specific_dates = 1;
}
