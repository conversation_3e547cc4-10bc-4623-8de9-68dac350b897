// @since 2024-03-21 14:16:42
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.online_booking.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// Pet unavailable reason
enum PetUnavailableReason {
  // unspecified
  PET_UNAVAILABLE_REASON_UNSPECIFIED = 0;
  // Over weight limit
  OVER_WEIGHT_LIMIT = 1;
  // Please update pet info
  PET_INFO_NEEDS_UPDATE = 2;
  // Pet type not accepted boarding
  PET_TYPE_NOT_ACCEPTED_BOARDING = 3;
  // Pet type not accepted daycare
  PET_TYPE_NOT_ACCEPTED_DAYCARE = 4;
  // Pet type not accepted grooming
  PET_TYPE_NOT_ACCEPTED_GROOMING = 5;
}
