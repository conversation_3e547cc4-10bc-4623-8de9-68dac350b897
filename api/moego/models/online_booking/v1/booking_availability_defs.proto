// @since 2024-03-21 14:16:42
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.online_booking.v1;

import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/offering/v1/evaluation_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// Booking pet definition<br>
// If pet id is not specified, it is new pet<br>
// If pet id is specified, it is existing pet
message BookingPetDef {
  // id, pet's unique identifier<br>
  // new pet may be index number or uuid or timestamp
  // existing pet may be auto-increment id or uuid
  string virtual_id = 1 [(validate.rules).string = {max_len: 255}];

  // existing pet id
  optional int64 pet_id = 2 [(validate.rules).int64 = {gte: 0}];

  // pet name
  optional string pet_name = 3 [(validate.rules).string = {max_len: 50}];

  // pet type id
  optional models.customer.v1.PetType pet_type = 4 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];

  // pet breed
  optional string breed = 5 [(validate.rules).string = {max_len: 50}];

  // pet weight
  optional double weight = 6 [(validate.rules).double = {gte: 0}];

  // coat type
  optional string coat_type = 7 [(validate.rules).string = {max_len: 50}];
}

// Booking pet's available service definition
message BookingPetServiceDef {
  // Booking pet id
  BookingPetDef pet = 1;

  // Available service id list
  repeated int64 available_service_ids = 2;

  // Pet's customized service
  repeated CustomizedServiceDef customized_services = 3;

  // Missing evaluations for this pet
  repeated MissingEvaluations missing_evaluations = 4;

  // Service unpassed evaluation
  message MissingEvaluations {
    // service id
    int64 service_id = 1;
    // unpassed evaluation
    moego.models.offering.v1.EvaluationBriefView missing_evaluation = 2;
  }
}

// Pet's customized service information, contains service price and duration
message CustomizedServiceDef {
  // service id
  int64 service_id = 1;

  // price(null for not override)
  optional double price = 2;

  // duration(null for not override)
  optional int32 duration = 3;
}
