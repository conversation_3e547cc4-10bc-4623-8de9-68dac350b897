// @since 2023-09-05 17:03:16
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.online_booking.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// ob access type
enum AccessType {
  // unspecified
  ACCESS_TYPE_UNSPECIFIED = 0;
  // access by phone
  ACCESS_TYPE_BY_PHONE = 1;
  // access by email
  ACCESS_TYPE_BY_EMAIL = 2;
}
