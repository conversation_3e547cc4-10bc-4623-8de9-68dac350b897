syntax = "proto3";
package moego.models.engagement.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/engagement/v1;engagementpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.engagement.v1";

// client
message Client {
  // id
  int64 id = 1;

  // company id
  int64 company_id = 2;

  // customer id
  int64 customer_id = 3;

  // phone number
  string phone_number = 4;

  // name
  string name = 8;
}
