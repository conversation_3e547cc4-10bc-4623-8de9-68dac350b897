// @since 2023-07-27 10:03:21
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.utils.v2;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2;utilsV2";
option java_multiple_files = true;
option java_package = "com.moego.idl.utils.v2";

// option for frontend select component
message Option {
  // label
  string label = 1;
  // value
  string value = 2;
  // children
  repeated Option children = 3;
}
