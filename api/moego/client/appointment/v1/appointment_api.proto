syntax = "proto3";

package moego.client.appointment.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/agreement/v1/agreement_record_models.proto";
import "moego/models/appointment/v1/appointment_defs.proto";
import "moego/models/appointment/v1/appointment_enums.proto";
import "moego/models/appointment/v1/appointment_pet_schedule_defs.proto";
import "moego/models/appointment/v1/appointment_tracking.proto";
import "moego/models/appointment/v1/evaluation_service_models.proto";
import "moego/models/appointment/v1/pet_detail_models.proto";
import "moego/models/business/v1/arrival_window_models.proto";
import "moego/models/business/v1/business_models.proto";
import "moego/models/business/v1/staff_models.proto";
import "moego/models/business_customer/v1/business_customer_address_models.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/business_customer/v1/business_customer_pet_models.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/marketing/v1/discount_code_enums.proto";
import "moego/models/message/v1/business_twilio_model.proto";
import "moego/models/offering/v1/evaluation_models.proto";
import "moego/models/offering/v1/service_models.proto";
import "moego/models/online_booking/v1/business_ob_config_models.proto";
import "moego/models/online_booking/v1/ob_config_enums.proto";
import "moego/models/payment/v1/payment_method_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.appointment.v1";

// The appointment service
service AppointmentService {
  // List appointments by filter, required c-side account session
  rpc ListAppointments(ListAppointmentsParams) returns (ListAppointmentsResult);

  // Get last finished appointment
  // Only the most recent appointment prior to the current time is given
  rpc GetLastFinishedAppointment(GetLastFinishedAppointmentParams) returns (GetLastFinishedAppointmentResult);

  // List upcoming day appointments by day for the day closest to the current time.
  rpc ListUpcomingDayAppointments(ListUpcomingDayAppointmentsParams) returns (ListUpcomingDayAppointmentsResult);

  // List all appointments for today
  // Contains status: pending, ongoing, finished
  rpc ListTodayAppointments(ListTodayAppointmentsParams) returns (ListTodayAppointmentsResult);

  // List pending appointments
  // Includes only appointments that have been pending for the next day
  rpc ListPendingDayAppointments(ListPendingDayAppointmentsParams) returns (ListPendingDayAppointmentsResult);

  // Get appointment detail
  rpc GetAppointment(GetAppointmentParams) returns (GetAppointmentResult);

  // Reschedule appointment
  rpc RescheduleAppointment(RescheduleAppointmentParams) returns (RescheduleAppointmentResult);

  // Cancel appointment
  rpc CancelAppointment(CancelAppointmentParams) returns (CancelAppointmentResult);

  // Confirm appointment
  rpc ConfirmAppointment(ConfirmAppointmentParams) returns (ConfirmAppointmentResult);
}

// The params message for ListAppointments
message ListAppointmentsParams {
  // Selected company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // filter
  Filter filter = 2 [(validate.rules).message = {required: true}];

  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 3;

  // sort by
  repeated AppointmentSortDef sorts = 4;

  // filter
  message Filter {
    // appointment list type
    AppointmentListType list_type = 1 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
  }

  // appointment list sort definition
  message AppointmentSortDef {
    // sort field
    AppointmentSortField field = 1;
    // sort asc or desc
    bool asc = 2;
  }

  // appointment list sort field
  enum AppointmentSortField {
    // unspecified
    APPOINTMENT_SORT_FIELD_UNSPECIFIED = 0;
    // appointment date
    APPOINTMENT_DATE = 1;
    // appointment start time
    APPOINTMENT_START_TIME = 2;
  }

  // Appointment list type
  enum AppointmentListType {
    // unspecified
    APPOINTMENT_LIST_TYPE_UNSPECIFIED = 0;
    // Pending
    // Client appointment status: pending
    PENDING = 1;
    // Ongoing
    // Client appointment status: (upcoming, in progress, ready to pick up) & appointment end date >= today
    ONGOING = 2;
    // Finished
    // Client appointment status: finished
    FINISHED = 3;
    // Canceled
    // Client appointment status: canceled
    CANCELED = 4;
    // Past
    // Finished || (Ongoing && appointment end date < today)
    PAST = 5;
  }
}

// The result message for ListAppointments
message ListAppointmentsResult {
  // appointment list
  repeated AppointmentItem appointments = 1;

  // pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;

  // business mobile arrival window view
  repeated moego.models.business.v1.ArrivalWindowModelPublicView arrival_windows = 3;

  // total appointment count
  int32 total_count = 4;

  // appointment item
  message AppointmentItem {
    // appointment id
    int64 appointment_id = 1;
    // appointment start date
    string start_date = 2;
    // appointment start time, in minutes
    int32 start_time = 3;
    // client appointment status
    ClientAppointmentStatus status = 4;
    // pet and name
    repeated ServicePetItem service_pets = 5;
    // total amount
    double total_amount = 6;
    // business id
    int64 business_id = 7;
    // appointment tracking, available only when the appointment is in progress
    optional moego.models.appointment.v1.AppointmentTrackingView appointment_tracking = 8;
    // service view
    repeated moego.models.offering.v1.ServiceClientView services = 9;
    // booking request id for pending appointment
    optional int64 booking_request_id = 10;
    // Pet's schedules
    repeated models.appointment.v1.PetScheduleDef schedules = 11;
    //  service type include
    int32 service_type_include = 12;
    // evaluation service view
    repeated moego.models.offering.v1.EvaluationServiceClientView evaluation_services = 13;
    // appointment end date
    string end_date = 14;
    // appointment end time, in minutes
    int32 end_time = 15;
  }

  // service pet item
  message ServicePetItem {
    // pet id
    int64 pet_id = 1;
    // pet name
    string pet_name = 2;
    // avatar path
    string avatar_path = 3;
    // pet type
    moego.models.customer.v1.PetType pet_type = 4;
  }
}

// Client appointment status
enum ClientAppointmentStatus {
  // unspecified
  CLIENT_APPOINTMENT_STATUS_UNSPECIFIED = 0;
  // Pending
  // Booking request status: submitted
  PENDING = 1;
  // Upcoming
  // Appointment status: unconfirmed, confirmed
  // Different from B-side definition, There will be no judgment on appointment dates
  // If the time expires and the appointment status has not changed, the delay is displayed
  // New update:
  // Upcoming was split to unconfirmed and confirmed
  // Upcoming means confirmed now
  UPCOMING = 2;
  // In progress
  // Appointment status: checked in
  IN_PROGRESS = 3;
  // Ready to pick up
  // Appointment status: ready to pick up
  READY_TO_PICK_UP = 4;
  // Finished
  // Appointment status: finished
  FINISHED = 5;
  // Cancelled
  // Appointment status: canceled
  CANCELLED = 6;
  // Unconfirmed
  UNCONFIRMED = 7;
}

// The params message for GetLastFinishedAppointment
message GetLastFinishedAppointmentParams {
  // Selected company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The result message for GetLastFinishedAppointment
message GetLastFinishedAppointmentResult {
  // appointment item
  AppointmentDetailItem appointment = 1;
}

// The params message for ListUpcomingDayAppointments
message ListUpcomingDayAppointmentsParams {
  // Selected company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The result message for ListUpcomingDayAppointments
message ListUpcomingDayAppointmentsResult {
  // appointment list
  repeated AppointmentDetailItem appointments = 1;
}

// The params message for ListTodayAppointments
message ListTodayAppointmentsParams {
  // Selected company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The result message for ListTodayAppointments
message ListTodayAppointmentsResult {
  // appointment list
  repeated AppointmentDetailItem appointments = 1;
}

// appointment detail view
message AppointmentDetailView {
  // appointment id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // customer id
  int64 customer_id = 3;
  // appointment date, in yyyy-MM-dd format
  string appointment_date = 4;
  // appointment start time, in minutes
  optional int32 appointment_start_time = 5;
  // appointment end time, in minutes
  optional int32 appointment_end_time = 6;
  // client appointment status, different from appointment status
  ClientAppointmentStatus status = 7;
  // payment status
  models.appointment.v1.AppointmentPaymentStatus payment_status = 8;
  // pending booking request id
  optional int64 booking_request_id = 9;
  // appointment end date, in yyyy-MM-dd format
  string appointment_end_date = 10;
  //  service type include
  int32 service_type_include = 11;
}

// Payment detail view
message PaymentDetailView {
  // book online payment
  moego.models.online_booking.v1.PaymentType payment_type = 1;
  // prepay type
  moego.models.online_booking.v1.PrepayType prepay_type = 2;
  // subtotal, service total + service charge
  double subtotal = 3;
  // tax and fees, tax + (convenience fee + booking fee)
  double tax_and_fees = 4;
  // tip amount
  double tip = 5;
  // discount amount, all payment type support
  double discount = 6;
  // total amount, subtotal + tax + fees - discount, exclude tip
  double total = 7;
  // order discounts
  // Includes discount code and one time discount
  repeated OrderDiscount order_discounts = 8;
  // order items
  repeated OrderItem order_items = 9;
  // apply packages
  repeated OrderApplyPackage order_apply_packages = 10;
  // payments
  repeated OrderPayment order_payments = 11;
}

// order discount
message OrderDiscount {
  // order discount id
  int64 id = 1;
  // discount code, one-time discount does not have a discount code
  optional string discount_code = 2;
  // discount type
  models.marketing.v1.DiscountCodeType type = 3;
  // discount amount, fixed amount or percentage
  double amount = 4;
}

// order item
message OrderItem {
  // order item id
  int64 id = 1;
  // item type
  // service/product/package/noshow/service_charge/evaluation_service
  string type = 2;
  // item name
  string name = 3;
  // item unit price
  double unit_price = 4;
  // total quantity
  int32 quantity = 5;
}

// order apply package
message OrderApplyPackage {
  // apply package id
  int64 id = 1;
  // package id
  int64 package_id = 2;
  // package name
  string package_name = 3;
  // package service id
  int64 service_id = 4;
  // service name
  string service_name = 5;
  // quantity
  int32 quantity = 6;
  // service unit price
  double service_price = 7;
}

// order payment
message OrderPayment {
  // payment id
  int64 id = 1;
  // transaction type
  TransactionType transaction_type = 2;
  // payment amount
  double amount = 3;
  // payment method, contains card, cash, check, etc.
  models.payment.v1.PaymentMethodModelPublicView payment_method = 4;
  // payment time
  google.protobuf.Timestamp payment_time = 5;

  // transaction type
  enum TransactionType {
    // unspecified
    TRANSACTION_TYPE_UNSPECIFIED = 0;
    // Completed payment status
    COMPLETED_PAYMENT = 1;
    // Refund
    REFUND = 2;
    // Deposit
    DEPOSIT = 3;
    // Processing payment status, only for prepay
    PROCESSING_PAYMENT = 4;
    // Canceled payment status, only for prepay
    CANCELED_PAYMENT = 5;
    // Pre-authorized
    PRE_AUTHORIZED = 6;
  }
}

// The appointment detail item
message AppointmentDetailItem {
  // appointment detail view
  AppointmentDetailView appointment = 1;
  // pet detail view
  repeated moego.models.appointment.v1.PetDetailModelClientView appointment_items = 2;
  // business view
  moego.models.business.v1.BusinessModelClientView business = 3;
  // business twilio number
  moego.models.message.v1.BusinessTwilioNumberView twilio_number = 4;
  // service view
  repeated moego.models.offering.v1.ServiceClientView services = 5;
  // business pet view
  repeated moego.models.business_customer.v1.BusinessCustomerPetModelClientView business_pets = 6;
  // staff view
  repeated moego.models.business.v1.StaffModelClientView staffs = 7;
  // ob deposit view
  PaymentDetailView payment_detail = 8;
  // ob config view
  moego.models.online_booking.v1.BusinessOBConfigModelClientView ob_config = 10;
  // business customer view
  moego.models.business_customer.v1.BusinessCustomerModelClientView customer = 11;
  // business customer primary address view
  moego.models.business_customer.v1.BusinessCustomerAddressModelClientView primary_address = 12;
  // business mobile arrival window view
  moego.models.business.v1.ArrivalWindowModelPublicView arrival_window = 13;
  // signed agreements
  repeated moego.models.agreement.v1.AgreementRecordSimpleView signed_agreements = 14;
  // allowed operation
  AllowedOperation allowed_operation = 15;
  // appointment tracking, available only when the appointment is in progress
  optional moego.models.appointment.v1.AppointmentTrackingView appointment_tracking = 16;
  // Pet's schedules
  repeated models.appointment.v1.PetScheduleDef schedules = 17;
  // evaluation services
  repeated moego.models.offering.v1.EvaluationServiceClientView evaluation_services = 18;
  // evaluation pet detail
  repeated moego.models.appointment.v1.PetEvaluationDetailClientView appointment_evaluation_items = 19;
}

// The appointment operation
message AllowedOperation {
  // allowed to cancel appointment
  bool is_allowed_cancellation = 1;
  // direct cancellation, false means direct message page
  bool is_direct_cancellation = 2;
  // allowed to reschedule appointment
  bool is_allowed_reschedule = 3;
  // direct reschedule, false means direct message page
  bool is_direct_reschedule = 4;
}

// The params message for GetAppointment
message GetAppointmentParams {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gte: 0}];
  // booking request id, 兼容 bd 的 booking request
  // 传这个表示查 booking request
  optional int64 booking_request_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// The result message for GetAppointment
message GetAppointmentResult {
  // appointment detail item
  AppointmentDetailItem appointment = 1;
}

// The params message for ListPendingAppointments
message ListPendingAppointmentsParams {
  // Selected company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The result message for ListPendingAppointments
message ListPendingAppointmentsResult {
  // appointment list
  repeated AppointmentDetailItem appointments = 1;
}

// The params message for ListPendingDayAppointments
message ListPendingDayAppointmentsParams {
  // Selected company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The result message for ListPendingDayAppointments
message ListPendingDayAppointmentsResult {
  // appointment list
  repeated AppointmentDetailItem appointments = 1;
}

// The params message for RescheduleAppointment
message RescheduleAppointmentParams {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // reschedule grooming params
  models.appointment.v1.GroomingRescheduleDef grooming_reschedule = 2 [(validate.rules).message = {required: true}];
}

// The result message for RescheduleAppointment
message RescheduleAppointmentResult {}

// The params message for CancelAppointment
message CancelAppointmentParams {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // stripe payment method id, card id
  optional string payment_method_id = 2 [(validate.rules).string = {max_len: 255}];
}

// The result message for CancelAppointment
message CancelAppointmentResult {}

// The params message for ConfirmAppointment
message ConfirmAppointmentParams {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The result message for ConfirmAppointment
message ConfirmAppointmentResult {}
