syntax = "proto3";

package moego.client.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_breed_models.proto";
import "moego/models/business_customer/v1/business_pet_coat_type_models.proto";
import "moego/models/business_customer/v1/business_pet_metadata_enums.proto";
import "moego/models/business_customer/v1/business_pet_metadata_models.proto";
import "moego/models/business_customer/v1/business_pet_type_models.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.business_customer.v1";

// List pet metadata params
message ListPetMetadataParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }

  // metadata name
  repeated models.business_customer.v1.BusinessPetMetadataName metadata_names = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// List pet metadata result
message ListPetMetadataResult {
  // Pet metadata
  repeated models.business_customer.v1.BusinessPetMetadataView metadata = 1;
}

// List pet types params
message ListPetTypesParams {
  // Selected company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// List pet types result
message ListPetTypesResult {
  // pet type list
  repeated moego.models.business_customer.v1.BusinessPetTypeModel types = 1;
}

// List pet breeds params
message ListPetBreedsParams {
  // Selected company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Selected pet type id
  moego.models.customer.v1.PetType pet_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// List pet breeds result
message ListPetBreedsResult {
  // pet breed list
  repeated moego.models.business_customer.v1.BusinessPetBreedModel breeds = 1;
}

// List pet coat types params
message ListPetCoatTypesParams {
  // Selected company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// List pet coat types result
message ListPetCoatTypesResult {
  // pet coat type list
  repeated moego.models.business_customer.v1.BusinessPetCoatTypeModel coat_types = 1;
}

// Business pet metadata service, which provides pet metadata related operations
service BusinessPetMetadataService {
  // List pet metadata
  rpc ListPetMetadata(ListPetMetadataParams) returns (ListPetMetadataResult);

  // List pet types
  rpc ListPetTypes(ListPetTypesParams) returns (ListPetTypesResult);

  // List pet breeds
  rpc ListPetBreeds(ListPetBreedsParams) returns (ListPetBreedsResult);

  // List pet coat type
  rpc ListPetCoatTypes(ListPetCoatTypesParams) returns (ListPetCoatTypesResult);
}
