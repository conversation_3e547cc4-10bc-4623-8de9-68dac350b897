syntax = "proto3";

package moego.client.account.v1;

import "moego/models/business_customer/v1/business_customer_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/account/v1;accountapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.account.v1";

// The params message for GetAccountProfile
message GetAccountProfileParams {
  // The company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The result message for GetAccountProfile
message GetAccountProfileResult {
  // The business customer profile
  optional moego.models.business_customer.v1.BusinessCustomerBrandedAppView business_customer = 1;
}

// The params message for GetAccountLinkStatus
message GetAccountLinkStatusParams {
  // The company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The result message for GetAccountLinkStatus
message GetAccountLinkStatusResult {
  // The link status
  // Identifies whether the current account has a customer associated with it
  bool is_linked = 1;

  // Has business customer profile
  // Retrieve if there is a customer under company by phone number
  bool has_business_customer = 2;
}

// The params message for LinkAccountToBrandedClient
message LinkAccountToBrandedClientParams {
  // The company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // The account profile
  optional AccountProfileCreateDef profile = 2;

  // create def for account profile
  message AccountProfileCreateDef {
    // first name
    string first_name = 1 [(validate.rules).string = {
      min_len: 1
      max_len: 50
    }];

    // last name
    string last_name = 2 [(validate.rules).string = {
      min_len: 1
      max_len: 50
    }];

    // avatar path
    optional string avatar_path = 3 [(validate.rules).string = {
      uri: true
      max_len: 255
    }];

    // email
    optional string email = 5 [(validate.rules).string = {
      email: true
      max_len: 50
    }];
  }
}

// The result message for LinkAccountToBrandedClient
message LinkAccountToBrandedClientResult {
  // The link result
  LinkResult link_result = 1;

  // The result of linked client enum
  enum LinkResult {
    // unspecified
    LINK_RESULT_UNSPECIFIED = 0;
    // linked existing client
    LINKED_EXISTING_CLIENT = 1;
    // created new client and linked
    CREATED_NEW_CLIENT = 2;
    // already linked
    ALREADY_LINKED = 3;
    // unknown error
    UNKNOWN_ERROR = 4;
  }
}

// AccountProfileService is the service for manage account profile. Requires C-side login
service AccountProfileService {
  // Get account profile.
  rpc GetAccountProfile(GetAccountProfileParams) returns (GetAccountProfileResult);

  // Get account link status.
  rpc GetAccountLinkStatus(GetAccountLinkStatusParams) returns (GetAccountLinkStatusResult);

  // Link account to the branded client.
  // If the client is not existed, create and link it, and return CREATED_NEW_CLIENT.
  // If the client is existed, link it, and return LINKED_EXISTING_CLIENT.
  // If the client is linked, return ALREADY_LINKED.
  rpc LinkAccountToBrandedClient(LinkAccountToBrandedClientParams) returns (LinkAccountToBrandedClientResult);
}
