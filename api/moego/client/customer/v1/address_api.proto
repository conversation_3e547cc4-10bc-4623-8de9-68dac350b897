syntax = "proto3";

package moego.client.customer.v1;

import "moego/models/business_customer/v1/business_customer_address_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/customer/v1;customerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.customer.v1";

// get primary address request
message GetPrimaryAddressRequest {}

// get primary address response
message GetPrimaryAddressResponse {
  // customer address
  moego.models.business_customer.v1.BusinessCustomerAddressModelClientView primary_address = 12;
}

// address service
service AddressService {
  // get primary address
  rpc GetPrimaryAddress(GetPrimaryAddressRequest) returns (GetPrimaryAddressResponse);
}
