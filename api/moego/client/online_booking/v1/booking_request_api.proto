syntax = "proto3";

package moego.client.online_booking.v1;

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "moego/models/appointment/v1/appointment_defs.proto";
import "moego/models/appointment/v1/appointment_pet_medication_schedule_defs.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/models/online_booking/v1/feeding_models.proto";
import "moego/models/online_booking/v1/medication_models.proto";
import "moego/service/online_booking/v1/booking_request_service.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.online_booking.v1";

// OB request address params
message Address {
  // Existing address id
  optional int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // address 1
  optional string address1 = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // address 2
  optional string address2 = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // city
  optional string city = 4 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // state
  optional string state = 5 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // zip code
  optional string zipcode = 6 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];
  // country
  optional string country = 7 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // lat
  optional string lat = 8 [(validate.rules).string = {max_len: 50}];
  // lng
  optional string lng = 9 [(validate.rules).string = {max_len: 50}];
  // Whether the address is profile request address,
  // if true, id is profile_request_address id,
  // if false, id is customer_address id
  optional bool is_profile_request_address = 10;
}

// OB request customer params
message Customer {
  // Customer first name
  optional string first_name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // Customer last name
  optional string last_name = 2 [(validate.rules).string = {max_len: 255}];
  // Phone number
  optional string phone_number = 3 [(validate.rules).string = {max_len: 255}];
  // Email
  optional string email = 4 [(validate.rules).string = {
    ignore_empty: true
    email: true
  }];
  // Answers map
  map<string, google.protobuf.Value> answers_map = 5;
  // Charge token
  optional string charge_token = 6;
  // Has stripe card
  optional bool has_stripe_card = 7;
  // Stripe customer id
  optional string stripe_customer_id = 8;
  // Additional info
  optional AdditionalInfo additional_info = 9;
  // Address
  optional Address address = 10;
  // birthday
  optional google.protobuf.Timestamp birthday = 11;
  // OB additional info
  message AdditionalInfo {
    // Referral source
    optional int32 referral_source_id = 1 [(validate.rules).int32 = {gt: 0}];
    // Referral source desc
    optional string referral_source_desc = 2 [(validate.rules).string = {max_len: 255}];
    // Preferred groomer
    optional int32 preferred_groomer_id = 3 [(validate.rules).int32 = {gte: 0}];
    // Preferred frequency
    optional int32 preferred_frequency_day = 4 [(validate.rules).int32 = {gte: 0}];
    // Preferred frequency type (0-by days, 1-by weeks)
    optional int32 preferred_frequency_type = 5 [(validate.rules).int32 = {gte: 0}];
    // Preferred days of the week
    repeated int32 preferred_day = 6;
    // Preferred times of the day
    repeated int32 preferred_time = 7;
  }
  // Emergency contact
  optional Contact emergency_contact = 12;
  // pickup contact
  optional Contact pickup_contact = 13;
  // contact
  message Contact {
    // First name
    optional string first_name = 1 [(validate.rules).string = {max_len: 50}];
    // Last name
    optional string last_name = 2 [(validate.rules).string = {max_len: 50}];
    // Phone number
    optional string phone_number = 3 [(validate.rules).string = {max_len: 30}];
  }
}

// OB request pet params
message Pet {
  // Id
  optional int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Name
  optional string pet_name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // Avatar path
  optional string avatar_path = 3 [(validate.rules).string = {max_len: 255}];
  // Breed
  optional string breed = 4 [(validate.rules).string = {max_len: 255}];
  // Breed mix
  optional int32 breed_mix = 5 [(validate.rules).int32 = {gte: 0}];
  // Pet type id
  optional int32 pet_type_id = 6 [(validate.rules).int32 = {gt: 0}];
  // Gender
  optional int32 gender = 7 [(validate.rules).int32 = {gte: 0}];
  // Birthday
  optional string birthday = 8 [(validate.rules).string = {
    ignore_empty: true
    pattern: "^\\d{4}-\\d{2}-\\d{2}$"
  }];
  // Weight
  optional string weight = 9 [(validate.rules).string = {max_len: 255}];
  // Fixed
  optional string fixed = 10 [(validate.rules).string = {max_len: 255}];
  // Behavior
  optional string behavior = 11 [(validate.rules).string = {max_len: 255}];
  // Hair length
  optional string hair_length = 12 [(validate.rules).string = {max_len: 255}];
  // Expiry notification
  optional int32 expiry_notification = 13 [(validate.rules).int32 = {gte: 0}];
  // Vet name
  optional string vet_name = 14 [(validate.rules).string = {max_len: 255}];
  // Vet phone
  optional string vet_phone = 15 [(validate.rules).string = {max_len: 255}];
  // Vet address
  optional string vet_address = 16;
  // Emergency contact name
  optional string emergency_contact_name = 17 [(validate.rules).string = {max_len: 255}];
  // Emergency contact phone
  optional string emergency_contact_phone = 18 [(validate.rules).string = {max_len: 255}];
  // Health issues
  optional string health_issues = 19 [(validate.rules).string = {max_len: 2048}];
  // Vaccines
  repeated Vaccine vaccine_list = 20;
  // Pet image
  optional string pet_image = 21 [(validate.rules).string = {max_len: 2048}];
  // Is selected
  //  bool is_selected = 22;
  // Pet question answers
  map<string, google.protobuf.Value> pet_question_answers = 25;

  // OB request vaccine params
  message Vaccine {
    // Vaccine binding id
    optional int64 vaccine_binding_id = 1 [(validate.rules).int64 = {gt: 0}];
    // Vaccine name
    optional int32 type = 2 [(validate.rules).int32 = {gte: 0}];
    // Vaccine id
    optional int32 vaccine_id = 3 [(validate.rules).int32 = {gte: 0}];
    // Vaccine name
    optional string expiration_date = 4 [(validate.rules).string = {
      ignore_empty: true
      pattern: "^\\d{4}-\\d{2}-\\d{2}$"
    }];
    // Vaccine document
    optional string vaccine_document = 5;
    // Document urls
    repeated string document_urls = 6;
  }
}

// OB agreement params
message Agreement {
  // Agreement id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // Signature
  string signature = 3;
}

// Feeding
message Feeding {
  // Feeding time
  repeated moego.models.online_booking.v1.FeedingModel.FeedingSchedule time = 1;
  // Feeding amount
  optional double amount = 2 [
    (validate.rules).double = {gt: 0},
    deprecated = true
  ];
  // Feeding unit
  optional string unit = 3 [(validate.rules).string = {max_len: 255}];
  // Food type
  optional string food_type = 4 [(validate.rules).string = {max_len: 255}];
  // Food source
  optional string food_source = 5 [(validate.rules).string = {max_len: 255}];
  // Feeding instructions
  optional string instruction = 6 [(validate.rules).string = {max_len: 2048}];
  // Feeding note
  optional string note = 7 [(validate.rules).string = {max_len: 255}];
  // Feeding amount, such as 1.2, 1/2, 1 etc.
  optional string amount_str = 8 [(validate.rules).string = {max_len: 255}];
}

// Medication
message Medication {
  // Medication time
  repeated moego.models.online_booking.v1.MedicationModel.MedicationSchedule time = 1;
  // Medication amount
  optional double amount = 2 [
    (validate.rules).double = {gt: 0},
    deprecated = true
  ];
  // Medication unit
  optional string unit = 3 [(validate.rules).string = {max_len: 255}];
  // Medication name
  optional string medication_name = 4 [(validate.rules).string = {max_len: 255}];
  // Medication notes
  optional string notes = 5 [(validate.rules).string = {max_len: 2048}];
  // Medication amount, such as 1.2, 1/2, 1 etc.
  optional string amount_str = 8 [(validate.rules).string = {max_len: 255}];
  // Medication select date
  optional moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef selected_date = 9;
}

// Boarding addon
message BoardingAddon {
  // Boarding addon id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // Is every day, not include checkout day
  optional bool is_every_day = 3 [deprecated = true];
  // Specific dates
  repeated string dates = 4 [(validate.rules).repeated = {
    items: {
      string: {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}
    }
  }];
  // Addon price
  double service_price = 5;
  // Tax
  int64 tax_id = 6;
  // Duration
  int32 duration = 7;
  // Quantity per day
  optional int32 quantity_per_day = 8;
  // date type
  optional moego.models.appointment.v1.PetDetailDateType date_type = 9;
  // start date
  optional google.type.Date start_date = 10;
}

// Daycare addon
message DaycareAddon {
  // Daycare addon id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // Is every day
  optional bool is_every_day = 3;
  // Specific dates
  repeated string dates = 4 [(validate.rules).repeated = {
    items: {
      string: {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}
    }
  }];
  // Addon price
  double service_price = 5;
  // Tax
  int64 tax_id = 6;
  // Duration
  int32 duration = 7;
  // Quantity per day
  optional int32 quantity_per_day = 8;
}

// Daycare addon
message GroomingAddon {
  // grooming addon id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// OB request service
message Service {
  // Service type
  oneof service {
    option (validate.required) = true;

    // Grooming service
    Grooming grooming = 1;
    // Boarding service
    Boarding boarding = 2;
    // Daycare service
    Daycare daycare = 3;
    // Evaluation service
    Evaluation evaluation = 4;
    // Dog walking service
    DogWalking dog_walking = 5;
    // Group class service
    GroupClass group_class = 6;
  }

  // Grooming service
  message Grooming {
    // service id
    int64 service_id = 1 [(validate.rules).int64 = {gt: 0}];
    // start date
    optional string start_date = 3 [(validate.rules).string = {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}];
    // addons
    repeated GroomingAddon addons = 4;
    // date type
    optional moego.models.appointment.v1.PetDetailDateType date_type = 5;
  }

  // Boarding service
  message Boarding {
    // Boarding service id
    int64 service_id = 1 [(validate.rules).int64 = {gt: 0}];
    // Arrival date
    // 当 boarding service detail 加入 waitlist 时，这个参数可以为空
    optional string start_date = 3 [(validate.rules).string = {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}];
    // Arrival time, in minutes since midnight
    optional int32 arrival_time = 4 [(validate.rules).int32 = {
      gte: 0
      lte: 1440
    }];
    // Pick up date
    // 当 boarding service detail 加入 waitlist 时，这个参数可以为空
    optional string end_date = 5 [(validate.rules).string = {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}];
    // Pick up time, in minutes since midnight
    optional int32 pickup_time = 6 [(validate.rules).int32 = {
      gte: 0
      lte: 1440
    }];
    // Service price
    double service_price = 7;
    // Tax
    int64 tax_id = 8;
    // Feeding. Deprecated, use feedings instead
    optional Feeding feeding = 9 [deprecated = true];
    // Medication. Deprecated, use medications instead
    optional Medication medication = 10 [deprecated = true];
    // Addons
    repeated BoardingAddon addons = 11;
    // Feedings
    repeated Feeding feedings = 12;
    // Medications
    repeated Medication medications = 13;
    // boarding service waitlist
    optional moego.service.online_booking.v1.CreateBoardingServiceWaitlistRequest waitlist = 15;
  }

  // Daycare service
  message Daycare {
    // Daycare service id
    int64 service_id = 1 [(validate.rules).int64 = {gt: 0}];
    // Dates of the daycare, in the format of "yyyy-MM-dd"
    // 当 daycare service detail 加入 waitlist 时，这个参数可以为空
    repeated string dates = 2 [(validate.rules).repeated = {
      items: {
        string: {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}
      }
    }];
    // Arrival time, in minutes since midnight
    optional int32 arrival_time = 4 [(validate.rules).int32 = {
      gte: 0
      lte: 1440
    }];
    // Pick up time, in minutes since midnight
    optional int32 pickup_time = 6 [(validate.rules).int32 = {
      gte: 0
      lt: 1440
    }];
    // Service price
    double service_price = 7;
    // Tax
    int64 tax_id = 8;
    // Max stay duration
    int32 max_duration = 9;
    // Feeding. Deprecated, use feedings instead
    optional Feeding feeding = 10 [deprecated = true];
    // Medication. Deprecated, use medications instead
    optional Medication medication = 11 [deprecated = true];
    // Addons
    repeated DaycareAddon addons = 12;
    // Feedings
    repeated Feeding feedings = 13;
    // Medications
    repeated Medication medications = 14;
    // daycare service waitlist
    optional moego.service.online_booking.v1.CreateDaycareServiceWaitlistRequest waitlist = 15;
  }

  // Evaluation service
  message Evaluation {
    // Evaluation service id
    // 这个是 evaluation id
    int64 service_id = 1 [(validate.rules).int64 = {gt: 0}];
    // The date of the evaluation, in the format of "yyyy-MM-dd"
    string date = 2 [(validate.rules).string = {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}];
    // Minutes since midnight
    int32 time = 3 [(validate.rules).int32 = {
      gte: 0
      lte: 1440
    }];
    // Service price
    double service_price = 4;
    // Duration
    int32 duration = 5;
    // 这个 evaluation 绑定的 service id
    // 暂时只有 boarding & daycare service
    optional int64 target_service_id = 6;
  }

  // Dog walking service
  message DogWalking {
    // Dog walking service id
    int64 service_id = 1 [(validate.rules).int64 = {gt: 0}];
    // The staff id
    int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];
    // The date of the evaluation, in the format of "yyyy-MM-dd"
    string date = 3 [(validate.rules).string = {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}];
    // Minutes since midnight
    int32 time = 5 [(validate.rules).int32 = {
      gte: 0
      lte: 1440
    }];
    // Service price
    double service_price = 6;
    // Duration
    int32 duration = 7;
    // Tax
    int64 tax_id = 8;
  }

  // Group class service
  message GroupClass {
    // Group class instance id
    int64 group_class_instance_id = 1 [(validate.rules).int64 = {gt: 0}];
    // The trainer id, same to the staff id
    int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];
    // The dates of each group session
    repeated string dates = 3 [(validate.rules).repeated = {
      min_items: 1
      max_items: 100
      items: {
        string: {pattern: "^[0-9]{4}-[0-9]{2}-[0-9]{2}$"}
      }
    }];
    // The start time of per group class session
    int32 start_time = 4 [(validate.rules).int32 = {
      gte: 0
      lte: 1440
    }];
    // The end time of per group class session
    int32 end_time = 5 [(validate.rules).int32 = {
      gte: 0
      lte: 1440
    }];
    // The group class price
    double service_price = 6;
  }
}

// PrePay
message PrePay {
  // service total
  double service_total = 1 [(validate.rules).double = {gte: 0}];
  // tax amount
  double tax_amount = 2 [(validate.rules).double = {gte: 0}];
  // service charge amount
  double service_charge_amount = 3 [(validate.rules).double = {gte: 0}];
}

// Source type
enum SourceType {
  // Unspecified
  SOURCE_TYPE_UNSPECIFIED = 0;
  // Marketing campaign email
  MARKETING_CAMPAIGN_EMAIL = 1;
}

// PreAuth
message PreAuth {
  // Tips amount
  double tips_amount = 1 [(validate.rules).double = {gte: 0}];
  // Service total
  double service_total = 2 [(validate.rules).double = {gte: 0}];
  // Tax amount
  double tax_amount = 3 [(validate.rules).double = {gte: 0}];
  // Service charge amount
  double service_charge_amount = 4 [(validate.rules).double = {gte: 0}];
  // Payment method id
  string payment_method_id = 5 [(validate.rules).string = {max_len: 255}];
  // Card number
  string card_number = 6 [(validate.rules).string = {max_len: 255}];
  // Charge token
  string charge_token = 7 [(validate.rules).string = {max_len: 255}];
}

// Discount code
message DiscountCode {
  // Discount code id
  int64 discount_code_id = 1 [(validate.rules).int64 = {gte: 0}];
  // Discount amount
  double discount_amount = 2 [(validate.rules).double = {gte: 0}];
  // Discount code
  string discount_code = 3 [(validate.rules).string = {max_len: 255}];
}

// Membership apply Info
message Membership {
  // membership id
  repeated int64 membership_ids = 1 [(validate.rules).repeated = {
    unique: true
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
}

// Create booking request request
message SubmitBookingRequestParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // Customer
  optional Customer customer = 3;
  // Pet to services mapping
  repeated PetServices pet_services = 4;
  // Agreements
  repeated Agreement agreements = 5;
  // Prepay guid
  optional string prepay_guid = 6;
  // PrePay
  // Order decoupling 之后，payment 流程会在 submit 之后进行，所以 submit 接口已经不再需要 pre_pay 参数
  optional PrePay pre_pay = 7 [deprecated = true];
  // Source type
  optional SourceType source_type = 8;
  // PreAuth
  optional PreAuth pre_auth = 9;
  // Discount code
  optional DiscountCode discount_code = 10;
  // Additional notes
  optional string additional_notes = 11;
  // membership apply info
  optional Membership membership = 12;
}

// Pet services
message PetServices {
  // Pet id
  Pet pet = 1 [(validate.rules).message = {required: true}];
  // Service id
  repeated Service services = 2;
}

// Create booking request response
message SubmitBookingRequestResult {
  // The booking request id
  // 当提交的 service 里同时包含 waitlist 和正常 service 时，返回的 id 是正常 service 创建的 booking request id
  // 当提交的 service 里只有 waitlist 时，返回的 id 是 waitlist 创建的 booking request id
  int64 id = 1;
  // order id
  int64 order_id = 2;
  // If the customer is new_visitor, return created customer id
  // If the customer is existing, return existing customer id
  int64 customer_id = 3;
  // 这个 BookingRequest 是否被自动 accept
  // 特别注意：
  // 当 prepay 开启时，只会返回 false，因为支付流程时异步的，没法在这个接口里判断支付状态
  // 当 prepay 关闭时，可以正常返回 true/false
  bool auto_accept_request = 4;
}

// The params message for reschedule booking request
message RescheduleBookingRequestParams {
  // The booking request id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // reschedule grooming params
  models.appointment.v1.GroomingRescheduleDef grooming_reschedule = 2 [(validate.rules).message = {required: true}];
}

// The result message for reschedule booking request
message RescheduleBookingRequestResult {
  // The flag of auto accepted
  bool is_auto_accept = 1;
}

// The params message for cancel booking request
message CancelBookingRequestParams {
  // The booking request id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The result message for cancel booking request
message CancelBookingRequestResult {}

// Create booking request request
message CalculateBookingRequestParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // Pet to services mapping
  repeated PetServices pet_services = 4;
}

// Calculate booking request response
message CalculateBookingRequestResult {
  // estimated total price
  double estimated_total_price = 1;
}

// Update booking request params
message UpdateBookingRequestParams {
  // The booking request id to update
  int64 booking_request_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Services to update
  repeated Service services = 2;

  // Service
  message Service {
    // Action
    oneof action {
      // Add service
      PetServices add = 1;
    }
  }
}

// Update booking request result
message UpdateBookingRequestResult {}

// the booking_request service
service BookingRequestService {
  // Submit booking request
  // 当提交的 service 里同时包含 waitlist 和正常 service 时，接口会创建两个 BookingRequest，一个 status 是 SUBMITTED，另一个 status 是 WAIT_LIST
  rpc SubmitBookingRequest(SubmitBookingRequestParams) returns (SubmitBookingRequestResult);

  // Reschedule booking request
  rpc RescheduleBookingRequest(RescheduleBookingRequestParams) returns (RescheduleBookingRequestResult);

  // Cancel booking request
  rpc CancelBookingRequest(CancelBookingRequestParams) returns (CancelBookingRequestResult);

  // calculate booking request
  rpc CalculateBookingRequest(CalculateBookingRequestParams) returns (CalculateBookingRequestResult);

  // Update booking request - allows C-end users to modify selected service items
  rpc UpdateBookingRequest(UpdateBookingRequestParams) returns (UpdateBookingRequestResult);
}
