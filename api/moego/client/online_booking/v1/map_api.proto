syntax = "proto3";

package moego.client.online_booking.v1;

import "google/type/latlng.proto";
import "moego/models/map/v1/map_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.online_booking.v1";

// GetCountriesRequest
message GetCountriesParams {
  // the name of the country, which can be a short name, official name or local name.
  optional string name = 1;
  // the code of the country, which can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code.
  optional string code = 2;
  // whether the country is independent
  optional bool independent = 3;
}

// GetCountriesResponse
message GetCountriesResult {
  // country list
  repeated moego.models.map.v1.CountryModel countries = 1;
}

// GetAddressRequest
message GetAddressParams {
  // condition_type
  oneof condition_type {
    // address source
    moego.models.map.v1.AddressSource address_source = 1;
    // specify the coordinate of address
    google.type.LatLng coordinate = 2;
    // combined conditions for querying address
    // Note: that the API itself does not check the accuracy of the conditions.
    // It will only return an address that meets all conditions,
    // which means that the accuracy of the address is guaranteed by the caller.
    CombinedConditions condition = 3;
  }

  // language code
  optional string language_code = 6;

  // CombinedConditions
  message CombinedConditions {
    // the country of the address, which can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code or country name.
    // Note: country is not case sensitive, but an exact match is required.
    string country = 1;
    // the part of the address
    optional string address = 2;
    // the zip/postal code of the address
    optional string postal_code = 3;
    // the label of the address
    optional string label = 4;
    // the bounds of the address
    optional moego.models.map.v1.Bounds bounds = 5;
  }
}

// GetAddressResponse
message GetAddressResult {
  // the address
  optional moego.models.map.v1.AddressModel address = 1;
}

// AutocompleteAddressParams
message AutocompleteAddressParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // The text string on which to search.
  string term = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // The origin point from which to calculate geodesic distance to the destination (returned as distance).
  // If this value is omitted, geodesic distance will not be returned.
  optional google.type.LatLng origin = 4;
  // A list of countries to limit the search. Each country element can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code or country name.
  // If no element is specified, there is no restriction on countries, which is equivalent to searching worldwide.
  // Note: country element is not case sensitive, but an exact match is required.
  repeated string countries = 5 [(validate.rules).repeated = {
    max_items: 16
    unique: true
    items: {
      string: {
        min_len: 2
        max_len: 128
      }
    }
  }];
  // the state of the address.
  // you must specify `countries` and must contain exactly one element when specifying this field.
  optional string state = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 128
  }];
  // the county of the address.
  // you must specify `countries` and must contain exactly one element when specifying this field.
  optional string county = 7 [(validate.rules).string = {
    min_len: 1
    max_len: 128
  }];
  // the district of the address.
  // you must specify `countries` and must contain exactly one element when specifying this field.
  optional string district = 8 [(validate.rules).string = {
    min_len: 1
    max_len: 128
  }];
  // the city of the address.
  // you must specify `countries` and must contain exactly one element when specifying this field.
  optional string city = 9 [(validate.rules).string = {
    min_len: 1
    max_len: 128
  }];
  // the zip/postal code of the address.
  // you must specify `countries` and must contain exactly one element when specifying this field.
  optional string postal_code = 10 [(validate.rules).string = {
    min_len: 1
    max_len: 32
  }];
  // Included primary Place type (for example, "restaurant" or "gas_station"), or only (regions), or only (cities).
  // Supported types please see: https://developers.google.com/maps/documentation/places/web-service/place-types
  // A Place is only returned if its primary type is included in this list.
  // Up to 5 values can be specified.
  // If no types are specified, all Place types are returned.
  repeated string address_types = 11 [(validate.rules).repeated = {
    max_items: 5
    unique: true
    items: {
      string: {
        min_len: 2
        max_len: 128
      }
    }
  }];
  // Bias results to a specified location.
  // At most one of `location_bias` or `location_restriction` should be set.
  optional moego.models.map.v1.LocationBias location_bias = 12;
  // Restrict results to a specified location.
  // At most one of `location_bias` or `location_restriction` should be set.
  optional moego.models.map.v1.LocationRestriction location_restriction = 13;

  // reserved ...

  // language code
  optional string language_code = 15 [(validate.rules).string = {
    min_len: 2
    max_len: 8
  }];
  // Specify the maximum number of results to return, default 5
  optional int32 max_results = 16 [(validate.rules).int32 = {
    gte: 1
    lte: 10
  }];
}

// AutocompleteAddressResult
message AutocompleteAddressResult {
  // Contains a list of suggestions, ordered in descending order of relevance.
  repeated moego.models.map.v1.SuggestedAddress suggestions = 1;
}

// MapService
service MapService {
  // get countries info, NOTE: if no parameters are specified, all countries will be returned.
  rpc GetCountries(GetCountriesParams) returns (GetCountriesResult);

  // get an address
  rpc GetAddress(GetAddressParams) returns (GetAddressResult);

  // predict addresses by term
  rpc AutocompleteAddress(AutocompleteAddressParams) returns (AutocompleteAddressResult);
}
