syntax = "proto3";

package moego.client.online_booking.v1;

import "moego/models/online_booking/v1/ob_customize_care_type_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.online_booking.v1";

// Get booking care type params
message ListBookingCareTypesParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// List booking care types result
message ListBookingCareTypesResult {
  // the booking care type list
  repeated moego.models.online_booking.v1.BookingCareTypeView booking_care_types = 1;
}

// Booking care type service
service BookingCareTypeService {
  // List booking care types
  rpc ListBookingCareTypes(ListBookingCareTypesParams) returns (ListBookingCareTypesResult);
}
