syntax = "proto3";

package moego.client.grooming.v1;

import "moego/models/online_booking/v1/business_ob_config_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/grooming/v1;groomingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.grooming.v1";

// get ob config request
message GetOnlineBookingConfigRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get ob config response
message GetOnlineBookingConfigResponse {
  // online booking config view
  moego.models.online_booking.v1.BusinessOBConfigModelBookingView config = 1;
}

// online booking config service
service OnlineBookingConfigService {
  // get business ob config
  rpc GetOnlineBookingConfig(GetOnlineBookingConfigRequest) returns (GetOnlineBookingConfigResponse);
}
