syntax = "proto3";

package moego.client.grooming.v1;

import "google/protobuf/empty.proto";
import "moego/models/agreement/v1/agreement_record_defs.proto";
import "moego/models/agreement/v1/agreement_record_models.proto";
import "moego/models/business/v1/arrival_window_models.proto";
import "moego/models/business/v1/business_models.proto";
import "moego/models/business/v1/staff_models.proto";
import "moego/models/business_customer/v1/business_customer_address_models.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/business_customer/v1/business_customer_pet_models.proto";
import "moego/models/grooming/v1/appointment_defs.proto";
import "moego/models/grooming/v1/appointment_enums.proto";
import "moego/models/grooming/v1/appointment_models.proto";
import "moego/models/grooming/v1/pet_detail_models.proto";
import "moego/models/grooming/v1/service_models.proto";
import "moego/models/message/v1/business_twilio_model.proto";
import "moego/models/online_booking/v1/business_ob_config_models.proto";
import "moego/models/online_booking/v1/payments_defs.proto";
import "moego/models/online_booking/v1/selected_defs.proto";
import "moego/models/payment/v1/deposit_models.proto";
import "moego/models/payment/v1/payment_method_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/grooming/v1;groomingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.grooming.v1";

// appointment list request
message AppointmentListRequest {
  // appointment type
  moego.models.grooming.v1.AppointmentType type = 1 [(validate.rules).enum = {defined_only: true}];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 2;
  // sort by
  repeated moego.models.grooming.v1.AppointmentSortDef sorts = 3;
}

// appointment list response
message AppointmentListResponse {
  // appointment list view
  repeated moego.models.grooming.v1.AppointmentModelClientListView appointments = 1;
  // business view
  repeated moego.models.business.v1.BusinessModelClientListView businesses = 2;
  // online booking config
  repeated moego.models.online_booking.v1.BusinessOBConfigModelClientListView ob_configs = 3;
  // pagination
  optional moego.utils.v2.PaginationResponse pagination = 4;
  // business mobile arrival window view
  repeated moego.models.business.v1.ArrivalWindowModelPublicView arrival_windows = 5;
}

// appointment detail request
message AppointmentDetailRequest {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// appointment detail response
message AppointmentDetailResponse {
  // appointment detail view
  moego.models.grooming.v1.AppointmentModelClientView appointment = 1;
  // pet detail view
  repeated moego.models.grooming.v1.PetDetailModelClientView appointment_items = 2;
  // business view
  moego.models.business.v1.BusinessModelClientView business = 3;
  // business twilio number
  moego.models.message.v1.BusinessTwilioNumberView twilio_number = 4;
  // service view
  repeated moego.models.grooming.v1.ServiceModelClientView services = 5;
  // business pet view
  repeated moego.models.business_customer.v1.BusinessCustomerPetModelClientView business_pets = 6;
  // staff view
  repeated moego.models.business.v1.StaffModelClientView staffs = 7;
  // payment method view
  moego.models.payment.v1.PaymentMethodModelPublicView payment_method = 8;
  // ob deposit view
  moego.models.payment.v1.BookOnlineDepositModelClientView ob_deposit = 9;
  // ob config view
  moego.models.online_booking.v1.BusinessOBConfigModelClientView ob_config = 10;
  // business customer view
  moego.models.business_customer.v1.BusinessCustomerModelClientView customer = 11;
  // business customer primary address view
  moego.models.business_customer.v1.BusinessCustomerAddressModelClientView primary_address = 12;
  // business mobile arrival window view
  moego.models.business.v1.ArrivalWindowModelPublicView arrival_window = 13;
  // signed agreements
  repeated moego.models.agreement.v1.AgreementRecordSimpleView signed_agreements = 14;
}

// create booking request
message CreateBookingRequestRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet selected service list
  repeated moego.models.online_booking.v1.SelectedPetServiceDef selected_pet_services = 2;
  // selected staff id
  int64 staff_id = 3;
  // selected appointment date
  string appointment_date = 4 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // selected appointment start time, in minutes
  int32 appointment_start_time = 5 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // additional note
  string additional_note = 7 [(validate.rules).string = {max_len: 65535}];
  // sign agreements
  repeated moego.models.agreement.v1.AgreementRecordDef sign_agreements = 6;
  // payment
  oneof payment {
    // card on file
    moego.models.online_booking.v1.CardOnFileDef card_on_file = 11;
    // prepay
    moego.models.online_booking.v1.PrepayDef prepay = 12;
    // pre-auth
    moego.models.online_booking.v1.PreAuthDef pre_auth = 13;
  }
}

// booking response
message CreateBookingRequestResponse {
  // auto accept flag
  bool is_auto_accept = 1;
}

// pre submit online booking request response
message PreSubmitBookingRequestResponse {
  // result
  bool result = 1;
}

// update booking request request
message UpdateBookingRequestRequest {
  // booking request
  oneof booking_request {
    option (validate.required) = true;
    // reschedule booking request
    RescheduleBookingRequest reschedule = 1;
    // cancel booking request
    CancelBookingRequest cancel = 2;
  }
}

// reschedule booking request
message RescheduleBookingRequest {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // selected staff id
  int64 staff_id = 2;
  // selected appointment date
  string appointment_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // selected appointment start time, in minutes
  int32 appointment_start_time = 4 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// cancel booking request
message CancelBookingRequest {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// update booking request response
message UpdateBookingRequestResponse {
  // result
  bool result = 1;
  // auto accept flag
  bool is_auto_accept = 2;
}

// update appointment request
message UpdateAppointmentRequest {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // update status
  moego.models.grooming.v1.AppointmentStatus status = 2;
}

// update appointment response
message UpdateAppointmentResponse {
  // result
  bool result = 1;
}

// get today appointment list response
message GetTodayAppointmentListResponse {
  // appointment list
  repeated AppointmentDetailResponse appointments = 1;
}

// appointment service
service AppointmentService {
  // get appointment list
  rpc GetAppointmentList(AppointmentListRequest) returns (AppointmentListResponse);
  // get appointment detail
  rpc GetAppointmentDetail(AppointmentDetailRequest) returns (AppointmentDetailResponse);
  // pre submit online booking request
  rpc PreSubmitBookingRequest(CreateBookingRequestRequest) returns (PreSubmitBookingRequestResponse) {
    option deprecated = true;
  }
  // create booking request
  rpc CreateBookingRequest(CreateBookingRequestRequest) returns (CreateBookingRequestResponse) {
    option deprecated = true;
  }
  // update booking request
  rpc UpdateBookingRequest(UpdateBookingRequestRequest) returns (UpdateBookingRequestResponse);
  // update appointment
  rpc UpdateAppointment(UpdateAppointmentRequest) returns (UpdateAppointmentResponse);
  // get last finished appointment
  rpc GetLastFinishedAppointment(google.protobuf.Empty) returns (AppointmentDetailResponse);
  // get next upcoming appointment
  rpc GetNextUpcomingAppointment(google.protobuf.Empty) returns (AppointmentDetailResponse);
  // get today appointment list
  rpc GetTodayAppointmentList(google.protobuf.Empty) returns (GetTodayAppointmentListResponse);
}
