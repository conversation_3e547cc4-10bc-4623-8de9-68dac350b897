syntax = "proto3";

package moego.enterprise.automation.v1;

import "moego/models/automation/v1/workflow.proto";
import "moego/models/automation/v1/workflow_defs.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/filter_model.proto";
import "moego/utils/v2/pagination_messages.proto";
import "moego/models/enterprise/v1/tenant_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/automation/v1;automationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.automation.v1";

// Workflow Service
service WorkflowService {
  // GetWorkflowConfig
  rpc GetWorkflowConfig(GetWorkflowConfigParams) returns (GetWorkflowConfigResult);

  // CreateWorkflow
  rpc CreateWorkflow(CreateWorkflowParams) returns (CreateWorkflowResult);

  // UpdateWorkflowContent
  rpc UpdateWorkflowContent(UpdateWorkflowContentParams) returns (UpdateWorkflowContentResult);
  // UpdateWorkflowInfo
  rpc UpdateWorkflowInfo(UpdateWorkflowInfoParams) returns (UpdateWorkflowInfoResult);

  // ListEnterpriseWorkflows
  rpc ListEnterpriseWorkflows(ListEnterpriseWorkflowsParams) returns (ListEnterpriseWorkflowsResult);
  // GetWorkflowInfo
  rpc GetWorkflowInfo(GetWorkflowInfoParams) returns (GetWorkflowInfoResult);

  // opt:FilterCustomer
  rpc FilterCustomer(FilterCustomerParams) returns (FilterCustomerResult);

  // PushWorkflows
  rpc PushWorkflows(PushWorkflowsParams) returns (PushWorkflowsResult);
}

// GetWorkflowConfigParams
message GetWorkflowConfigParams {}

// GetWorkflowConfigResult
message GetWorkflowConfigResult {
  // WorkflowConfig List
  repeated models.automation.v1.WorkflowConfig workflow_configs = 1;
  // Common Filters
  repeated models.reporting.v2.FilterGroup filter_groups = 2;
}

// CreateWorkflowParams
message CreateWorkflowParams {
  // Workflow to be created
  models.automation.v1.CreateWorkflowDef workflow = 1;
}

// CreateWorkflowResult
message CreateWorkflowResult {
  // Workflow
  models.automation.v1.Workflow workflow = 1;
}

// UpdateWorkflowContentParams
message UpdateWorkflowContentParams {
  // Workflow ID
  int64 workflow_id = 1;
  // Steps to update
  repeated models.automation.v1.CreateStepDef steps = 2;
}

// UpdateWorkflowContentResult
message UpdateWorkflowContentResult {
  // Workflow
  models.automation.v1.Workflow workflow = 1;
}

// UpdateWorkflowInfoParams
message UpdateWorkflowInfoParams {
  // Workflow ID
  int64 workflow_id = 1;
  // Workflow name
  optional string name = 2;
  // Workflow description
  optional string desc = 3;
  // Workflow status
  optional models.automation.v1.Workflow.Status status = 4;
  // Workflow setting
  optional models.automation.v1.WorkflowSetting setting = 5;
  // enterprise apply to
  optional models.automation.v1.WorkflowEnterpriseApply workflow_enterprise_apply = 6;
}

// UpdateWorkflowInfoResult
message UpdateWorkflowInfoResult {
  // Workflow
  models.automation.v1.Workflow workflow = 1;
}

// ListEnterpriseWorkflowsParams
message ListEnterpriseWorkflowsParams {
  // Filter
  message Filter {
    // Workflow name filter
    optional string name = 1;
    // Workflow status filter
    repeated models.automation.v1.Workflow.Status status = 2;
    // Tenant Group IDs filter
    repeated int64 tenants_group_ids = 3;
    // Tenant IDs filter
    repeated int64 tenants_ids = 4;
  }
  // Pagination
  optional moego.utils.v2.PaginationRequest pagination = 1;
  // Filter
  optional Filter filter = 2;
}

// ListEnterpriseWorkflowsResult
message ListEnterpriseWorkflowsResult {
  // Workflows
  repeated models.automation.v1.Workflow workflows = 1;
  // Pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

// GetWorkflowInfoParams
message GetWorkflowInfoParams {
  // Workflow ID
  int64 workflow_id = 1;
}

// GetWorkflowInfoResult
message GetWorkflowInfoResult {
  // Workflow
  models.automation.v1.Workflow workflow = 1;
}

// FilterCustomerParams
message FilterCustomerParams {
  // Filter requests
  repeated models.reporting.v2.FilterRequest filters = 1;
  // Pagination
  optional moego.utils.v2.PaginationRequest pagination = 2;
  // Tenants IDs, empty is all tenants
  repeated int64 tenants_ids = 3;

  // Customer ID filter
  optional int64 customer_id = 10;
}

// FilterCustomerResult
message FilterCustomerResult {
  // Customers
  repeated moego.models.business_customer.v1.BusinessCustomerInfoModel customer = 1;
  // Pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;
}


// PushWorkflowsParams
message PushWorkflowsParams {
  // workflow ids
  repeated int64 ids = 1;
  // tenant objects
  repeated models.enterprise.v1.TenantObject targets = 2;
}

// PushWorkflowsResult
message PushWorkflowsResult {
  // success
  bool success = 1;
}
