syntax = "proto3";

package moego.enterprise.tenant.v1;

import "moego/models/enterprise/v1/tenant_defs.proto";
import "moego/models/enterprise/v1/tenant_group_models.proto";
import "moego/models/enterprise/v1/tenant_models.proto";
import "moego/models/enterprise/v1/tenant_template_models.proto";
import "moego/models/enterprise/v1/territory_models.proto";
import "moego/models/organization/v1/staff_defs.proto";
import "moego/models/organization/v1/staff_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/tenant/v1;tenantapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.tenant.v1";

// create tenant request
message CreateTenantParams {
  // account id
  optional int64 account_id = 1 [(validate.rules).int64 = {gt: 0}];
  // enterprise id
  optional int64 enterprise_id = 2 [(validate.rules).int64 = {gt: 0}];
  // tenant
  models.enterprise.v1.CreateTenantDef tenant = 3;
  // group ids
  optional models.enterprise.v1.GroupDef group_def = 4;
}

// create tenant response
message CreateTenantResult {
  // tenant
  models.enterprise.v1.TenantModel tenant = 1;
}

// update tenant request
message UpdateTenantParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // account id
  optional int64 account_id = 2 [(validate.rules).int64 = {gt: 0}];
  // tenant
  optional models.enterprise.v1.UpdateTenantDef tenant = 3;
  // group ids
  optional models.enterprise.v1.GroupDef group_def = 4;
}

// update tenant request
message UpdateTenantResult {
  // tenant
  models.enterprise.v1.TenantModel tenant = 1;
}

// delete tenant request
message DeleteTenantParams {
  // tenant id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete tenant result
message DeleteTenantResult {}

// get tenant request
message GetTenantParams {
  // tenant id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // need territory
  optional bool need_territory = 2;
  // need tenant group
  optional bool need_tenant_group = 3;
}

// get tenant response
message GetTenantResult {
  // tenant
  models.enterprise.v1.TenantModel tenant = 1;
  // tenant template model
  models.enterprise.v1.TenantTemplateModel tenant_template = 2;
  // territories
  optional moego.models.enterprise.v1.TerritoryModel territories = 3;
  // tenant group
  repeated moego.models.enterprise.v1.TenantGroupModel tenant_group = 4;
  // tenant group def
  models.organization.v1.StaffModel staff = 5;
  // staff email
  models.organization.v1.StaffEmailDef staff_email = 6;
}

// list tenant request
message ListTenantParams {
  // pagination
  moego.utils.v2.PaginationRequest pagination = 1 [(validate.rules).message = {required: true}];
  // need tenant group
  optional bool need_tenant_group = 2;
  // order by
  optional moego.utils.v2.OrderBy order_by = 3;
  // filter
  message Filter {
    // group names
    repeated int64 group_ids = 1;
    // statuses
    repeated models.enterprise.v1.TenantModel.Status statuses = 3;
    // keyword
    optional string keyword = 4;
    // types
    repeated models.enterprise.v1.TenantModel.Type types = 5;
  }
  // filter
  optional Filter filter = 4;
}

// list tenant params
message ListTenantResult {
  // tenant list
  repeated moego.models.enterprise.v1.TenantModel tenants = 1;
  // account info
  repeated models.organization.v1.StaffModel staff = 2;
  // territory model
  repeated models.enterprise.v1.TenantTemplateModel tenant_template = 3;
  // territories
  repeated moego.models.enterprise.v1.TerritoryModel territories = 4;
  // tenant group
  repeated moego.models.enterprise.v1.TenantGroupModel tenant_group = 5;
  // tenant group def
  repeated moego.models.enterprise.v1.TenantGroupDef tenant_group_relation = 6;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 7;
}

// list tenant group params
message ListTenantGroupParams {
  // filter
  message Filter {
    // group ids
    repeated int64 group_ids = 1;
  }
  // filter
  optional Filter filter = 1;
}

// list tenant group result
message ListTenantGroupResult {
  // tenant groups
  repeated models.enterprise.v1.TenantGroupModel tenant_groups = 1;
}

// delete tenant group params
message DeleteTenantGroupParams {
  // group id
  int64 group_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete tenant group result
message DeleteTenantGroupResult {}

// list all tenant and group params
message ListAllTenantAndGroupParams {}

// list all tenant and group result
message ListAllTenantAndGroupResult {
  // tenant list
  repeated moego.models.enterprise.v1.TenantView tenant_views = 1;
  // tenant group list
  repeated moego.models.enterprise.v1.GroupTenantDef group_tenant_relation = 2;
  // assign tenant list
  repeated int64 assign_tenant_ids = 3;
  // assign group list
  repeated int64 assign_group_ids = 4;
}

// tenant service
service TenantService {
  // create tenant
  rpc CreateTenant(CreateTenantParams) returns (CreateTenantResult);
  // update tenant
  rpc UpdateTenant(UpdateTenantParams) returns (UpdateTenantResult);
  // get tenant
  rpc GetTenant(GetTenantParams) returns (GetTenantResult);
  // get tenant list
  rpc ListTenant(ListTenantParams) returns (ListTenantResult);
  // delete tenant
  rpc DeleteTenant(DeleteTenantParams) returns (DeleteTenantResult);
  // list tenant groups
  rpc ListTenantGroups(ListTenantGroupParams) returns (ListTenantGroupResult);
  // delete tenant group
  rpc DeleteTenantGroup(DeleteTenantGroupParams) returns (DeleteTenantGroupResult);
  // list all tenant and group
  rpc ListAllTenantAndGroup(ListAllTenantAndGroupParams) returns (ListAllTenantAndGroupResult);
}
