syntax = "proto3";

package moego.enterprise.tenant.v1;

import "moego/models/enterprise/v1/territory_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/tenant/v1;tenantapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.enterprise.tenant.v1";

// create territory params
message CreateTerritoryParams {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64.gt = 0];
  // zip code
  repeated string zip_codes = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 100
      }
    }
  }];
  // territory type
  moego.models.enterprise.v1.TerritoryModel.Type type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// create territory result
message CreateTerritoryResult {
  // territory
  models.enterprise.v1.TerritoryModel territory = 1;
}

// update territory params
message UpdateTerritoryParams {
  // id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // zip code
  repeated string zip_codes = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 100
      }
    }
  }];
  // territory type
  optional moego.models.enterprise.v1.TerritoryModel.Type type = 3;
}

// update territory result
message UpdateTerritoryResult {
  // territory
  models.enterprise.v1.TerritoryModel territory = 1;
}

// delete territory params
message DeleteTerritoryParams {
  // territory id
  int64 id = 1;
}

// delete territory result
message DeleteTerritoryResult {}

// get territory params
message GetTerritoryParams {
  // territory id
  int64 id = 1;
}

// get territory result
message GetTerritoryResult {
  // territory
  models.enterprise.v1.TerritoryModel territory = 1;
}

// get territory list params
message ListTerritoriesParams {
  // filter
  message Filter {
    // territory ids
    repeated int64 territory_ids = 1;
    // type
    repeated moego.models.enterprise.v1.TerritoryModel.Type types = 2;
    // statuses
    repeated moego.models.enterprise.v1.TerritoryModel.Status statuses = 3;
  }
  // filter
  optional Filter filter = 1;
}

// get territory list result
message ListTerritoriesResult {
  // territories
  repeated models.enterprise.v1.TerritoryModel territories = 1;
}

// territory service
service TerritoryService {
  // create territory
  rpc CreateTerritory(CreateTerritoryParams) returns (CreateTerritoryResult);
  // update territory
  rpc UpdateTerritory(UpdateTerritoryParams) returns (UpdateTerritoryResult);
  // delete territory
  rpc DeleteTerritory(DeleteTerritoryParams) returns (DeleteTerritoryResult);
  // get territory
  rpc GetTerritory(GetTerritoryParams) returns (GetTerritoryResult);
  // list territories
  rpc ListTerritories(ListTerritoriesParams) returns (ListTerritoriesResult);
}
