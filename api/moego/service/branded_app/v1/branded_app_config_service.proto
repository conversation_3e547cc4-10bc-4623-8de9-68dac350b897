// @since 2024-06-03 14:43:06
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.branded_app.v1;

import "moego/models/account/v1/account_enums.proto";
import "moego/models/branded_app/v1/branded_app_config_models.proto";
import "moego/models/branded_app/v1/branded_pack_config_models.proto";
import "moego/models/branded_app/v1/branded_theme_config_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/branded_app/v1;brandedappsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.branded_app.v1";

// request for the GetBrandedAppConfig method
message GetBrandedAppConfigRequest {
  // filter
  oneof filter {
    option (validate.required) = true;
    // branded app id, branded app global unique identity
    string branded_app_id = 1 [(validate.rules).string = {max_len: 255}];

    // branded entity
    BrandedEntity branded_entity = 2;
  }

  // branded entity information
  message BrandedEntity {
    // branded type
    models.account.v1.AccountNamespaceType branded_type = 1 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
    // branded id
    int64 branded_id = 2 [(validate.rules).int64 = {gte: 0}];
  }
}

// response for the GetBrandedAppConfig method
message GetBrandedAppConfigResponse {
  // the branded app config
  moego.models.branded_app.v1.BrandedAppConfigModel config = 1;
}

// request for the ListBrandedAppConfig method
message ListBrandedAppConfigRequest {}

// response for the ListBrandedAppConfig method
message ListBrandedAppConfigResponse {
  // the branded app config list
  repeated moego.models.branded_app.v1.BrandedAppConfigModel configs = 1;
}

// request for the GetDefaultThemeConfig method
message GetDefaultThemeConfigRequest {
  // branded app id, branded app global unique identity
  string branded_app_id = 1 [(validate.rules).string = {max_len: 255}];
}

// response for the GetDefaultThemeConfig method
message GetDefaultThemeConfigResponse {
  // the default theme config
  moego.models.branded_app.v1.BrandedThemeConfigModel default_theme = 1;
}

// request for the GetBrandedAppPackConfig method
message GetBrandedAppPackConfigRequest {
  // branded app id, branded app global unique identity
  string branded_app_id = 1 [(validate.rules).string = {max_len: 255}];
}

// response for the GetBrandedAppPackConfig method
message GetBrandedAppPackConfigResponse {
  // the branded app pack config
  moego.models.branded_app.v1.BrandedPackConfigModel pack_config = 1;
}

// request for the ListBrandedAppPackConfig method
message ListBrandedAppPackConfigRequest {}

// response for the ListBrandedAppPackConfig method
message ListBrandedAppPackConfigResponse {
  // the branded app pack config list
  repeated moego.models.branded_app.v1.BrandedPackConfigModel pack_configs = 1;
}

// the branded_app_config service
service BrandedAppConfigService {
  // get branded app config
  rpc GetBrandedAppConfig(GetBrandedAppConfigRequest) returns (GetBrandedAppConfigResponse);

  // must get branded app config
  // Error codes:
  // - CODE_BRANDED_APP_NOT_FOUND: Branded app config not found
  rpc MustGetBrandedAppConfig(GetBrandedAppConfigRequest) returns (GetBrandedAppConfigResponse);

  // list branded app config
  rpc ListBrandedAppConfig(ListBrandedAppConfigRequest) returns (ListBrandedAppConfigResponse);

  // get branded app default theme config
  rpc GetDefaultThemeConfig(GetDefaultThemeConfigRequest) returns (GetDefaultThemeConfigResponse);

  // Get branded app pack config. contains public and private config
  rpc GetBrandedAppPackConfig(GetBrandedAppPackConfigRequest) returns (GetBrandedAppPackConfigResponse);

  // List branded app pack config. contains public and private config
  rpc ListBrandedAppPackConfig(ListBrandedAppPackConfigRequest) returns (ListBrandedAppPackConfigResponse);
}
