// @since 2025-04-07 15:32:56
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.fulfillment.v1;

import "moego/models/fulfillment/v1/group_class_attendance_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/fulfillment/v1;fulfillmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.fulfillment.v1";

// The request message for ListAttendances
message ListAttendancesRequest {
  // The group class session id
  repeated int64 group_class_session_id = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// The response message for ListAttendances
message ListAttendancesResponse {
  // The group class attendance details
  repeated moego.models.fulfillment.v1.GroupClassAttendanceModel attendances = 1;
}

// the group_class_attendance service
service GroupClassAttendanceService {
  // List group class attendance
  rpc ListAttendances(ListAttendancesRequest) returns (ListAttendancesResponse);
}
