syntax = "proto3";

package moego.service.account.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
import "moego/models/account/v1/oauth_account_enums.proto";
import "moego/models/account/v1/oauth_account_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1;accountsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.account.v1";

// get oauth account list by account id request
message GetOauthAccountListByAccountIdRequest {
  // account id
  int64 account_id = 1;

  // oauth provider
  optional moego.models.account.v1.OauthAccountProvider provider = 2;
}

// get oauth account list by account id response
message GetOauthAccountListByAccountIdResponse {
  // oauth account list
  repeated moego.models.account.v1.OauthAccountModel oauth_accounts = 1;
}

// update or insert oauth account request
message UpsertOauthAccountRequest {
  // account id
  optional int64 account_id = 1;

  // oauth provider
  moego.models.account.v1.OauthAccountProvider provider = 2;

  // oauth open id
  string open_id = 3 [(validate.rules).string = {max_len: 100}];

  // oauth user's email
  string email = 4 [(validate.rules).string = {
    ignore_empty: true
    max_len: 100
    email: true
  }];

  // oauth user's first name
  string first_name = 5 [(validate.rules).string = {max_len: 50}];

  // oauth user's last name
  string last_name = 6 [(validate.rules).string = {max_len: 50}];

  // oauth user's avatar path
  string avatar_path = 7 [(validate.rules).string = {
    ignore_empty: true
    max_len: 256
    uri: true
  }];

  // extra data, existing values will be overwritten and null values will be deleted
  google.protobuf.Struct extra_data = 10;
}

// delete oauth account request
message DeleteOauthAccountRequest {
  // oauth account id
  int64 id = 1;
}

// oauth account service
service OauthAccountService {
  // get oauth account list by account id
  rpc GetOauthAccountListByAccountId(GetOauthAccountListByAccountIdRequest) returns (GetOauthAccountListByAccountIdResponse);

  // update or insert oauth account request
  rpc UpsertOauthAccount(UpsertOauthAccountRequest) returns (moego.models.account.v1.OauthAccountModel);

  // delete oauth account
  rpc DeleteOauthAccount(DeleteOauthAccountRequest) returns (google.protobuf.Empty);
}
