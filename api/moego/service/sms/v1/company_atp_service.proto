// @since 2023-06-20 17:13:10
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.sms.v1;

import "moego/models/sms/v1/company_atp_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/sms/v1;smssvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.sms.v1";

// query company atp status
message CompanyOneAtpStatusQueryRequest {
  // the company id list
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// response for company atp status
message CompanyOneAtpStatusQueryResponse {
  //company atp status
  moego.models.sms.v1.CompanyAtpStatusModel company_atp_status = 1;
}

// query company atp status
message CompanyMultiAtpStatusQueryRequest {
  // the company id list
  repeated int64 company_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 500
    items: {
      int64: {gt: 0}
    }
  }];
}

// response for company atp status
message CompanyMultiAtpStatusQueryResponse {
  //company atp status by company_id
  map<int64, moego.models.sms.v1.CompanyAtpStatusModel> company_atp_status_map = 1;
}

//query company atp config
message CompanyAtpConfigQueryRequest {
  //the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

//company atp config response
message CompanyAtpConfigQueryResponse {
  //company atp data model
  moego.models.sms.v1.CompanyAtpDataModel company_atp_data = 1;
}

//submit atp config data request
message SubmitAtpDataRequest {
  //the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  //business legal name
  optional string business_name = 2 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];

  //business legal type
  optional string business_type = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];

  //business address1
  optional string address1 = 4 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];

  //business address2
  optional string address2 = 5 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];

  //business address city
  optional string city = 6 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];

  //business address region
  optional string region = 7 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];

  //business address zipcode
  optional string zipcode = 8 [(validate.rules).string = {
    min_len: 0
    max_len: 20
  }];

  //business address lat
  optional string lat = 9 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];

  //business address lng
  optional string lng = 10 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];

  //business website
  optional string website = 255 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];

  //business ein
  optional string ein = 12 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];

  //business einfiles
  repeated string ein_files = 13 [(validate.rules).repeated = {
    min_items: 0
    max_items: 50
    items: {
      string: {
        min_len: 0
        max_len: 255
      }
    }
  }];

  //business owner first_name
  string first_name = 14 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];

  //business owner last_name
  string last_name = 15 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];

  //business owner mobile phone number
  string phone_number = 16 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];

  //business owner email
  string email = 17 [(validate.rules).string = {
    min_len: 0
    max_len: 50
  }];
  //submit data with ein
  bool with_ein = 18;
}

//submit atp data response
message SubmitAtpDataResponse {}

//retry otp message request
message NoEinRetryOTPRequest {
  // the company id list
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

//retry otp message response
message NoEinRetryOTPResponse {}

//atp task
message AtpStatusCheckTaskRequest {}

//retry otp message response
message AtpStatusCheckTaskResponse {}

//task for twilio full resource request
message TwilioFullResourceRefreshRequest {}

//task for twilio full resource response
message TwilioFullResourceRefreshResponse {}

// the company atp service
service CompanyAtpService {
  // query one companys atp status
  rpc QueryOneCompanysAtpStatus(CompanyOneAtpStatusQueryRequest) returns (CompanyOneAtpStatusQueryResponse);
  // query multi companys atp status
  rpc QueryMultiCompanysAtpStatus(CompanyMultiAtpStatusQueryRequest) returns (CompanyMultiAtpStatusQueryResponse);
  //query company atp config and data
  rpc QueryCompanyAtpConfig(CompanyAtpConfigQueryRequest) returns (CompanyAtpConfigQueryResponse);
  //submit business owner company data
  rpc SubmitCompanyData(SubmitAtpDataRequest) returns (SubmitAtpDataResponse);
  //submit business owner company data
  rpc NoEinRetryOTP(NoEinRetryOTPRequest) returns (NoEinRetryOTPResponse);
  //task for atp status check
  rpc AtpStatusCheckTask(AtpStatusCheckTaskRequest) returns (AtpStatusCheckTaskResponse);
  //task for twilio full resource
  rpc TwilioFullResourceRefresh(TwilioFullResourceRefreshRequest) returns (TwilioFullResourceRefreshResponse);
}
