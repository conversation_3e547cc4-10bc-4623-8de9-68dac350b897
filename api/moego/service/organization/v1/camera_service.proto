syntax = "proto3";

package moego.service.organization.v1;

import "moego/models/organization/v1/camera_defs.proto";
import "moego/models/organization/v1/camera_enums.proto";
import "moego/models/organization/v1/camera_models.proto";
import "moego/models/organization/v1/tenant.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1;organizationsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.organization.v1";

// get camera list requests
message GetCameraListRequest {
  // tenant id
  models.organization.v1.Tenant tenant = 4 [(validate.rules).message = {required: true}];
  // filter
  oneof filter {
    option (validate.required) = true;
    // idogcam filter
    models.organization.v1.IdogcamFilter idogcam_filter = 1;
    // abckam filter
    models.organization.v1.AbckamFilter abckam_filter = 2;
    // camera filter
    models.organization.v1.CameraFilter camera_filter = 3;
  }
}

// get camera list response
message GetCameraListResponse {
  // camera list
  repeated models.organization.v1.CameraModel cameras = 1;
}

//update camera request
message UpdateCameraRequest {
  // tenant id
  models.organization.v1.Tenant tenant = 4 [(validate.rules).message = {required: true}];
  // camera id
  int64 camera_id = 1;
  // Is Active
  optional bool is_active = 10;
  // Visibility Type
  optional models.organization.v1.VisibilityType visibility_type = 11;
}

//update camera response
message UpdateCameraResponse {}

// camera service
service CameraService {
  // get camera list
  rpc GetCameraList(GetCameraListRequest) returns (GetCameraListResponse);
  // update camera
  rpc UpdateCamera(UpdateCameraRequest) returns (UpdateCameraResponse);
}
