syntax = "proto3";

package moego.service.organization.v1;

import "google/type/dayofweek.proto";
import "moego/models/enterprise/v1/staff_defs.proto";
import "moego/models/organization/v1/staff_availability_def.proto";
import "moego/models/organization/v1/staff_availability_models.proto";
import "moego/models/organization/v1/staff_defs.proto";
import "moego/models/organization/v1/staff_enums.proto";
import "moego/models/organization/v1/staff_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1;organizationsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.organization.v1";

// staff service
service StaffService {
  // api to create a staff
  rpc CreateStaff(CreateStaffRequest) returns (CreateStaffResponse) {}
  // api to get a staff detail
  rpc GetStaffDetail(GetStaffDetailRequest) returns (GetStaffDetailResponse) {}
  // api to get staff full detail
  rpc GetStaffFullDetail(GetStaffFullDetailRequest) returns (GetStaffFullDetailResponse) {}
  /* Api to get staffs by account id,including enterprise staff,migrated company and legacy company staff.
   * migrated company staff has no business_id and legacy company staff has a certain business_id.
   * Enterprise staffs place in the front and company staffs will sort desc by account_sort. */
  rpc GetStaffsByAccountId(GetStaffsByAccountIdRequest) returns (GetStaffsByAccountIdResponse) {}
  // get staff by company
  rpc GetStaffByCompany(GetStaffByCompanyRequest) returns (GetStaffByCompanyResponse);
  // update account last visit business
  rpc UpdateAccountLastVisitBusiness(UpdateAccountLastVisitBusinessRequest) returns (UpdateAccountLastVisitBusinessResponse);
  // update staff
  rpc UpdateStaff(UpdateStaffRequest) returns (UpdateStaffResponse) {}
  // delete staff
  rpc DeleteStaff(DeleteStaffRequest) returns (DeleteStaffResponse) {}
  // get staff info list by pagination
  rpc QueryStaffListByPagination(QueryStaffListByPaginationRequest) returns (QueryStaffListByPaginationResponse);
  // get working staffs by location ids
  rpc GetStaffsByWorkingLocationIds(GetStaffsByWorkingLocationIdsRequest) returns (GetStaffsByWorkingLocationIdsResponse);
  // query staff by ids
  rpc QueryStaffByIds(QueryStaffByIdsRequest) returns (QueryStaffByIdsResponse);
  // get show on calendar staffs
  rpc GetShowOnCalendarStaffs(GetShowOnCalendarStaffsRequest) returns (GetShowOnCalendarStaffsResponse);
  // get show on calendar staff ids
  rpc GetShowOnCalendarStaffIds(GetShowOnCalendarStaffsRequest) returns (GetShowOnCalendarStaffIdsResponse);
  // staff data migrate
  rpc MigrateStaffData(MigrateStaffDataRequest) returns (MigrateStaffDataResponse) {}
  // count staff with role
  rpc CountStaffWithRole(CountStaffWithRoleRequest) returns (CountStaffWithRoleResponse) {}
  // get staffs by working location
  rpc GetStaffsByWorkingLocation(GetStaffsByWorkingLocationRequest) returns (GetStaffsByWorkingLocationResponse) {}
  // get clock in out staffs of current staff
  rpc GetClockInOutStaffs(GetClockInOutStaffsRequest) returns (GetClockInOutStaffsResponse);
  // get enterprise staffs by working location ids
  rpc GetEnterpriseStaffsByWorkingLocationIds(GetEnterpriseStaffsByWorkingLocationIdsRequest) returns (GetEnterpriseStaffsByWorkingLocationIdsResponse);
  // create enterprise owner
  rpc CreateEnterpriseOwner(CreateEnterpriseOwnerRequest) returns (CreateEnterpriseOwnerResponse);
  // query staffs by company id
  rpc QueryStaffByCompanyId(QueryStaffByCompanyIdRequest) returns (QueryStaffByCompanyIdResponse);
  // get staffs by account id
  rpc SendInviteStaffLink(SendInviteStaffLinkRequest) returns (SendInviteStaffLinkResponse);
  // get staff invited link status
  rpc ListOwnerStaffInfo(ListOwnerStaffInfoRequest) returns (ListOwnerStaffInfoResponse);
  // create staff record
  rpc CreateStaffRecord(CreateStaffRecordRequest) returns (CreateStaffRecordResponse);
  // update staff record
  rpc UpdateStaffRecord(UpdateStaffRecordRequest) returns (UpdateStaffRecordResponse);
  // get enterprise staffs by account id
  rpc GetEnterpriseStaffsByAccountId(GetEnterpriseStaffsByAccountIdRequest) returns (GetEnterpriseStaffsByAccountIdResponse);
  // get enterprise staff
  rpc CreateEnterpriseStaff(CreateEnterpriseStaffRequest) returns (CreateEnterpriseStaffResponse);
  // update enterprise staff
  rpc UpdateEnterpriseStaff(UpdateEnterpriseStaffRequest) returns (UpdateEnterpriseStaffResponse);
  // get enterprise staff
  rpc GetEnterpriseStaff(GetEnterpriseStaffRequest) returns (GetEnterpriseStaffResponse);
  // delete enterprise staff
  rpc DeleteEnterpriseStaff(DeleteEnterpriseStaffRequest) returns (DeleteEnterpriseStaffResponse);
  // list staff email def
  rpc ListStaffEmailDefs(ListStaffEmailDefsRequest) returns (ListStaffEmailDefsResponse);
  // send invite link
  rpc SendInviteLink(SendInviteLinkRequest) returns (SendInviteLinkResponse);
  // unlink staff
  rpc UnlinkStaff(UnlinkStaffRequest) returns (UnlinkStaffResponse);
  // get staff login time
  rpc GetStaffLoginTime(GetStaffLoginTimeRequest) returns (GetStaffLoginTimeResponse);
  // check staff login time
  rpc CheckStaffLoginTime(CheckStaffLoginTimeRequest) returns (CheckStaffLoginTimeResponse);
  // get staffs with phone number
  rpc GetStaffByPhoneNumber(GetStaffByPhoneNumberRequest) returns (GetStaffByPhoneNumberResponse);
  // get staffs with role
  rpc GetStaffsByRole(GetStaffsByRoleRequest) returns (GetStaffsByRoleResponse);

  // get business级别的 staff slot availability type 返回by time or by slot
  rpc GetBusinessStaffAvailabilityType(GetBusinessStaffAvailabilityTypeRequest) returns (GetBusinessStaffAvailabilityTypeResponse) {}
  // update business级别的 staff slot availability type
  rpc UpdateBusinessStaffAvailabilityType(UpdateBusinessStaffAvailabilityTypeRequest) returns (UpdateStaffAvailabilityResponse) {}
  // get staff slot availability
  rpc GetStaffAvailability(GetStaffAvailabilityRequest) returns (GetStaffAvailabilityResponse) {}
  // update staff slot availability
  rpc UpdateStaffAvailability(UpdateStaffAvailabilityRequest) returns (UpdateStaffAvailabilityResponse) {}
  // override相关
  // update staff slot availability override
  rpc UpdateStaffAvailabilityOverride(UpdateStaffAvailabilityOverrideRequest) returns (UpdateStaffAvailabilityResponse) {}
  // delete staff slot availability override
  rpc DeleteStaffAvailabilityOverride(DeleteStaffAvailabilityOverrideRequest) returns (DeleteStaffAvailabilityOverrideResponse) {}
  // get staff override config
  rpc GetStaffAvailabilityOverride(GetStaffAvailabilityOverrideRequest) returns (GetStaffAvailabilityOverrideResponse) {}
  // get staff calender view
  rpc GetStaffCalenderView(GetStaffCalenderViewRequest) returns (GetStaffCalenderViewResponse);

  // init staff availability
  rpc InitStaffAvailability(InitStaffAvailabilityRequest) returns (InitStaffAvailabilityResponse);
}

// init staff availability request
message InitStaffAvailabilityRequest {
  // company id
  optional int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // staff id list
  repeated int64 staff_ids = 3 [(validate.rules).repeated = {max_items: 100}];
  // start business id
  optional int64 start_business_id = 4 [(validate.rules).int64.gt = 0];
  // end business id
  optional int64 end_business_id = 5 [(validate.rules).int64.gt = 0];
}

// init staff availability response
message InitStaffAvailabilityResponse {
  // results
  repeated Result results = 1;

  // result
  message Result {
    // business id
    int32 business_id = 1;
    // staff id
    int32 staff_id = 2;
    // schedule type
    int32 schedule_type = 3;
    //  day of week
    google.type.DayOfWeek day_of_week = 4;
    // staff name
    string staff_name = 5;
    // start time
    int32 ob_start_time = 6;
    // end time
    int32 ob_end_time = 7;
    // start time
    int32 sm_start_time = 8;
    // end time
    int32 sm_end_time = 9;
  }
}

// request to create a staff
message CreateStaffRequest {
  // staff id from token
  int64 token_staff_id = 1;
  // company id from token
  int64 token_company_id = 2;
  // business id from token
  int64 token_business_id = 3;

  // staff profile def
  models.organization.v1.CreateStaffDef staff_profile = 6;
  // working business def
  optional models.organization.v1.StaffWorkingLocationDef working_location = 7;
  // access control def
  optional models.organization.v1.StaffAccessControlDef access_control = 8;
  // notification def
  optional models.organization.v1.StaffNotificationDef notification_setting = 9;
  // payroll setting def
  optional models.organization.v1.StaffPayrollSettingDef payroll_setting = 10;
  // send invite link params
  optional models.organization.v1.SendInviteLinkParamsDef invite_link = 11;
  // staff login time
  optional models.organization.v1.StaffLoginTimeDef login_time = 12;
}

// response to create a staff
message CreateStaffResponse {
  // id for the created staff
  int64 id = 1;
}

// request to update a staff
message UpdateStaffRequest {
  // staff id from token
  int64 token_staff_id = 1;
  // company id from token
  int64 token_company_id = 2;
  // business id from token
  int64 token_business_id = 3;

  // staff id to update
  int64 id = 6 [(validate.rules).int64.gt = 0];
  // staff profile def
  optional models.organization.v1.UpdateStaffDef staff_profile = 7;
  // working business def
  optional models.organization.v1.StaffWorkingLocationDef working_location = 8;
  // access control def
  optional models.organization.v1.StaffAccessControlDef access_control = 9;
  // notification def
  optional models.organization.v1.StaffNotificationDef notification_setting = 10;
  // payroll setting def
  optional models.organization.v1.StaffPayrollSettingDef payroll_setting = 11;
  // send invite link params
  optional models.organization.v1.SendInviteLinkParamsDef invite_link = 12;
  // staff login time
  optional models.organization.v1.StaffLoginTimeDef login_time = 13;
}

// response to update a staff
message UpdateStaffResponse {
  // update result
  bool success = 1;
}

// request to delete a staff
message DeleteStaffRequest {
  // the staff to delete
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // staff id from token
  int64 token_staff_id = 2;
  // company id from token
  int64 token_company_id = 3;
  // enterprise hub need to change company owner,
  // so it need to delete the owner and create a new owner staff
  optional bool is_need_to_delete_owner = 4;
}

// response to delete a staff
message DeleteStaffResponse {
  // delete result
  bool success = 1;
}

// request to get staff detail
message GetStaffDetailRequest {
  // staff id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gte = 0];
  // enterprise id
  int64 enterprise_id = 3 [(validate.rules).int64.gte = 0];
}

// response to get staff detail
message GetStaffDetailResponse {
  // staff detail
  models.organization.v1.StaffModel staff = 1;
}

// request to get staff full detail
message GetStaffFullDetailRequest {
  // staff id
  int64 id = 1 [(validate.rules).int64.gt = 0];

  // staff id from token
  optional int64 token_staff_id = 6;
  // company id from token
  optional int64 token_company_id = 7;
}

// response to get staff full detail
message GetStaffFullDetailResponse {
  // staff id
  int64 id = 1;
  // staff info
  moego.models.organization.v1.StaffBasicView staff_profile = 2;
  // working location
  moego.models.organization.v1.StaffWorkingLocationDef working_location = 3;
  // access control
  moego.models.organization.v1.StaffAccessControlDef access_control = 4;
  // notification setting
  moego.models.organization.v1.StaffNotificationDef notification_setting = 5;
  // payroll setting
  moego.models.organization.v1.StaffPayrollSettingDef payroll_setting = 6;
  // staff email
  moego.models.organization.v1.StaffEmailDef staff_email = 7;
  // staff van
  moego.models.organization.v1.StaffVanDef staff_van = 8;
  // staff login time
  models.organization.v1.StaffLoginTimeDef login_time = 9;
}

// request to get staff list
message QueryStaffListByPaginationRequest {
  // staff id from token
  optional int64 token_staff_id = 1;
  // company id from token
  optional int64 token_company_id = 2;
  // 3-5: preserved for future use

  // business id list
  repeated int64 business_ids = 6 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
  // 7-13: preserved for future use, for example filter by staff type, status, etc.

  // order by params
  repeated moego.utils.v2.OrderBy order_bys = 14;
  // pagination
  utils.v2.PaginationRequest pagination = 15;
}

// response to get staff list
message QueryStaffListByPaginationResponse {
  // staff list
  repeated models.organization.v1.StaffInfoDef staffs = 1;
  // pagination
  utils.v2.PaginationResponse pagination = 2;
}

// get staffs by account id request
message GetStaffsByAccountIdRequest {
  // account id
  int64 account_id = 1 [(validate.rules).int64.gt = 0];
}

// get staffs by account id response
message GetStaffsByAccountIdResponse {
  // staffs
  repeated models.organization.v1.StaffModel staffs = 1;
}

// get staff by company request
message GetStaffByCompanyRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // account id
  int64 account_id = 3 [(validate.rules).int64 = {gt: 0}];
  // use it to choose the staff in the company when the company did not migrate
  // otherwise, it should be empty
  optional int64 business_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// get staff by company response
message GetStaffByCompanyResponse {
  // staff
  models.organization.v1.StaffModel staff = 1;
}

// update account last visit business request
message UpdateAccountLastVisitBusinessRequest {
  // account id
  int64 account_id = 1 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];
  // last visited_at
  int64 visited_at = 3 [(validate.rules).int64 = {gt: 0}];
  // last visit business id
  int64 last_visit_business_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// update account last visit business response
message UpdateAccountLastVisitBusinessResponse {}

// request for get staff list by business ids
message GetStaffsByWorkingLocationIdsRequest {
  // token staff id
  int64 token_staff_id = 1;
  // token company id
  int64 token_company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // business ids, if empty, will get all working location staffs
  repeated int64 business_ids = 3 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
  // include deleted staffs
  optional bool include_deleted = 4;
}

// response for get working staff list by business ids
message GetStaffsByWorkingLocationIdsResponse {
  // staff list
  repeated models.organization.v1.LocationStaffsDef location_staffs = 1;
  // total location count
  int64 total_location_count = 2;
  // total staff count
  int64 total_staff_count = 3;
}

// request for get enterprise staff list by location ids
message GetEnterpriseStaffsByWorkingLocationIdsRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business ids, if empty, will get all working location staffs
  repeated int64 business_ids = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
}

// response for get enterprise working staff list by business ids
message GetEnterpriseStaffsByWorkingLocationIdsResponse {
  // staff list
  repeated models.organization.v1.LocationStaffsDef location_staffs = 1;
}

// query staff by ids request
message QueryStaffByIdsRequest {
  // staff ids
  repeated int64 staff_ids = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
    max_items: 200
  }];
}

// query staff by ids response
message QueryStaffByIdsResponse {
  // staff list
  repeated models.organization.v1.StaffModel staffs = 1;
}

// query staff by company id  request
message QueryStaffByCompanyIdRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2 [(validate.rules).message = {required: true}];
}

// query staff by company id  response
message QueryStaffByCompanyIdResponse {
  // staff list
  repeated models.organization.v1.StaffModel staffs = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2 [(validate.rules).message = {required: true}];
}

// request for get show on calendar staffs
message GetShowOnCalendarStaffsRequest {
  // token staff id
  int64 token_staff_id = 1;
  // token company id
  int64 token_company_id = 2;
  // business ids, if empty, will get all working location staffs
  repeated int64 business_ids = 3 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
}

// response for get show on calendar staffs
message GetShowOnCalendarStaffsResponse {
  // staff list
  repeated models.organization.v1.LocationStaffsDef location_staffs = 1;
}

// response for get show on calendar staff ids
message GetShowOnCalendarStaffIdsResponse {
  // staff ids
  repeated models.organization.v1.LocationStaffIdsDef location_staff_ids = 1;
}

// staff data transfer request
message MigrateStaffDataRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
}

// migrate data response staff mapping def
message StaffMappingDef {
  // origin staff id
  int64 from_staff_id = 1;
  // merged to the new staff id
  int64 to_staff_id = 2;
}

// staff data migrate response
message MigrateStaffDataResponse {
  // staff mapping list
  repeated StaffMappingDef staff_mapping = 1;
}

// count staff with role request
message CountStaffWithRoleRequest {
  // company id, deprecated, now only filter by role_id
  int64 company_id = 1 [
    (validate.rules).int64.gte = 0,
    deprecated = true
  ];
  // role id
  int64 role_id = 2 [(validate.rules).int64.gt = 0];
}

// count staff with role response
message CountStaffWithRoleResponse {
  // staff count
  int64 staff_count = 1;
}

// get staffs in working location request
message GetStaffsByWorkingLocationRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // working location id
  int64 working_location_id = 2 [(validate.rules).int64.gt = 0];
}

// get staffs by working location response
message GetStaffsByWorkingLocationResponse {
  // staff list
  repeated models.organization.v1.StaffBasicView staffs = 1;
}

// request for get clock in out staffs of current staff
message GetClockInOutStaffsRequest {
  // token staff id
  int64 token_staff_id = 1 [(validate.rules).int64.gt = 0];
  // token company id
  int64 token_company_id = 2 [(validate.rules).int64.gt = 0];

  // clock in/out date
  string date = 6 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // staff ids
  repeated int64 staff_ids = 7 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
  // is for themselves, permission control from api layer
  optional bool is_for_themselves = 8;
}

// response for get clock in out staffs of current staff
message GetClockInOutStaffsResponse {
  // staff list
  repeated models.organization.v1.ClockInOutStaffDef clock_in_out_staffs = 1;
}

// request to create a enterprise owner
message CreateEnterpriseOwnerRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // account_id
  int64 account_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff profile def
  string first_name = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // last name of the staff
  string last_name = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // role id for enterprise owner
  int64 role_id = 5 [(validate.rules).int64 = {gte: 0}];
  // source
  optional models.organization.v1.StaffSource source = 6;
}

// response to create a enterprise staff
message CreateEnterpriseOwnerResponse {
  // id for the created staff
  int64 id = 1;
}

// request to send invite staff link
message SendInviteStaffLinkRequest {
  // staff id from token
  int64 staff_id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // email
  string email = 3 [(validate.rules).string = {
    email: true
    max_len: 100
  }];
}

// response to send invite staff link
message SendInviteStaffLinkResponse {}

// get staff invited link status request
message ListOwnerStaffInfoRequest {
  // company id
  repeated int64 company_id = 1;
}

// get staff invited link status response
message ListOwnerStaffInfoResponse {
  // own staffs
  map<int64, models.organization.v1.OwnerStaffDef> own_staffs = 1;
}

// request to create a owner staff
message CreateStaffRecordRequest {
  // company id
  int64 company_id = 1;
  // account id to bind on company,
  optional int64 account_id = 2;
  // staff profile def
  optional string first_name = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // last name of the staff
  optional string last_name = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // email
  optional string email = 5;
  // employee category
  models.organization.v1.StaffEmployeeCategory employee_category = 6;
  // status
  optional models.organization.v1.StaffModel.Status status = 7;
  // role id
  optional int64 role_id = 8 [(validate.rules).int64 = {gte: 0}];
}

// response to create a owner staff
message CreateStaffRecordResponse {
  // staff
  models.organization.v1.StaffModel staff = 1;
}

// update owner staff, can delete staff or change account id
message UpdateStaffRecordRequest {
  // staff id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // account id to bind on own staff
  optional int64 account_id = 3;
  // first_name
  optional string first_name = 4;
  // last_name
  optional string last_name = 5;
  // email
  optional string email = 6;
  // status
  optional models.organization.v1.StaffModel.Status status = 7;
  // employee category
  optional models.organization.v1.StaffEmployeeCategory employee_category = 8;
  // working in all locations when set owner ,working_in_all_locations should be set true
  optional bool working_in_all_locations = 9;
  // role id,can be set zero,if set zero,will upgrade staff to owner
  optional int64 role_id = 10 [(validate.rules).int64 = {gte: 0}];
}

// response to update a owner staff
message UpdateStaffRecordResponse {
  // staff
  models.organization.v1.StaffModel staff = 1;
}

// get enterprise staff by account
message GetEnterpriseStaffsByAccountIdRequest {
  // account id
  int64 account_id = 1 [(validate.rules).int64.gt = 0];
  // filter
  message Filter {
    // include deleted staffs, default is false
    bool include_deleted = 1;
    // include not allow login in, default is false
    bool include_not_allow_login_in = 2;
  }
  // filter
  Filter filter = 2;
}

// get enterprise staff by account response
message GetEnterpriseStaffsByAccountIdResponse {
  // staff list
  repeated models.organization.v1.StaffModel staffs = 1;
}

// create enterprise staff request
message CreateEnterpriseStaffRequest {
  // enterprise id
  int64 enterprise_id = 1;
  // staff id from token
  models.enterprise.v1.CreateStaffProfile profile = 2;
  // invite link
  optional models.organization.v1.SendInviteLinkParamsDef invite_link = 3;
}

// create enterprise staff response
message CreateEnterpriseStaffResponse {
  // enterprise id
  int64 enterprise_id = 1;
  // staff model
  models.organization.v1.StaffModel staff = 2;
  // staff email
  moego.models.organization.v1.StaffEmailDef staff_email = 3;
}

// update enterprise staff request
message UpdateEnterpriseStaffRequest {
  // staff id
  int64 id = 1;
  // enterprise id
  int64 enterprise_id = 2;
  // staff profile
  optional models.enterprise.v1.UpdateStaffProfile profile = 3;
  // invite link
  optional models.organization.v1.SendInviteLinkParamsDef invite_link = 4;
}

// update enterprise staff response
message UpdateEnterpriseStaffResponse {
  // staff model
  models.organization.v1.StaffModel staff = 1;
  // staff email
  moego.models.organization.v1.StaffEmailDef staff_email = 2;
}

// get enterprise staff request
message GetEnterpriseStaffRequest {
  // staff id
  int64 id = 1;
  // enterprise id
  int64 enterprise_id = 2;
}

// get enterprise staff response
message GetEnterpriseStaffResponse {
  // staff model
  models.organization.v1.StaffModel staff = 1;
  // staff email
  moego.models.organization.v1.StaffEmailDef staff_email = 2;
}

// delete enterprise staff request
message DeleteEnterpriseStaffRequest {
  // staff id
  int64 id = 1;
  // enterprise id
  int64 enterprise_id = 2;
}

// delete enterprise staff response
message DeleteEnterpriseStaffResponse {}

// list staff email def response
message ListStaffEmailDefsRequest {
  // staff ids
  repeated int64 staff_ids = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
}

// list staff email def response
message ListStaffEmailDefsResponse {
  // staff email list
  map<int64, moego.models.organization.v1.StaffEmailDef> staff_emails = 1;
}

// send invite link request
message SendInviteLinkRequest {
  // send invite link def
  models.organization.v1.SendInviteLinkDef invite_link = 1;
  // enterprise id
  optional int64 enterprise_id = 2;
}

// send invite link response
message SendInviteLinkResponse {}

// unlink staff request
message UnlinkStaffRequest {
  // staff id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id
  optional int64 enterprise_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// unlink staff response
message UnlinkStaffResponse {}

// get staff login time request
message GetStaffLoginTimeRequest {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Check if the current time is within the allowed login time
  bool need_check_allowed = 2;
  // company id
  optional int64 company_id = 3;
}

// get staff login time response
message GetStaffLoginTimeResponse {
  // staff login time
  models.organization.v1.StaffLoginTimeModel login_time = 1;
  // if current time is within the allowed login time
  optional bool is_allowed = 2;
  // if is_allowed is false, next time to login, unix timestamp in seconds
  optional int64 next_login_time = 3;
}

// check staff login time request
message CheckStaffLoginTimeRequest {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// check staff login time response
message CheckStaffLoginTimeResponse {
  // if current time is within the allowed login time
  bool is_allowed = 1;
  // if is_allowed is false, here's a pop up message
  optional string pop_up_message = 2;
}

// get staffs with role request
message GetStaffByPhoneNumberRequest {
  // company id, deprecated, now only filter by role_id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // phone number
  string phone_number = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
}

// get staffs with role response
message GetStaffByPhoneNumberResponse {
  // staff list
  models.organization.v1.StaffModel staff = 1;
}

// get staffs with role request
message GetStaffsByRoleRequest {
  // role id
  int64 role_id = 1 [(validate.rules).int64.gte = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
}

// get staffs with role response
message GetStaffsByRoleResponse {
  // staff list
  repeated models.organization.v1.StaffModel staffs = 1;
}

// GetBusinessStaffAvailabilityTypeRequest staff availability 配置
message GetBusinessStaffAvailabilityTypeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// UpdateBusinessStaffAvailabilityTypeRequest staff availability 配置
message UpdateBusinessStaffAvailabilityTypeRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // availability type
  moego.models.organization.v1.AvailabilityType availability_type = 2;
}

// GetBusinessStaffAvailabilityTypeResponse 返回staff 类型的结果
message GetBusinessStaffAvailabilityTypeResponse {
  // availability type
  moego.models.organization.v1.AvailabilityType availability_type = 1;
}

// GetStaffAvailabilityRequest 获取staff 的 availability
message GetStaffAvailabilityRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id list, staff will init when staff_id no exist
  repeated int64 staff_id_list = 3 [(validate.rules).repeated = {unique: true}];
  // availability type
  optional moego.models.organization.v1.AvailabilityType availability_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// StaffAvailabilityResponse
message GetStaffAvailabilityResponse {
  // staff available list
  repeated moego.models.organization.v1.StaffAvailability staff_availability_list = 1;
}

// GetStaffCalenderViewRequest
message GetStaffCalenderViewRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // start date
  string start_date = 4;
}

// CalenderStaff
message CalenderStaff {
  // staff id
  int64 staff_id = 1;
  // is available
  bool is_available = 2;
  // schedule type
  moego.models.organization.v1.ScheduleType schedule_type = 3;
  // slot daily setting
  map<string, moego.models.organization.v1.SlotAvailabilityDay> slot_availability_day_map = 4;
  // slot start sunday
  string slot_start_sunday = 6;
}

// GetStaffCalenderViewResponse
message GetStaffCalenderViewResponse {
  // staff available list
  repeated CalenderStaff staff_list = 1;
}

// UpdateStaffAvailabilityRequest
message UpdateStaffAvailabilityRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff available list
  repeated moego.models.organization.v1.StaffAvailabilityDef staff_availability_list = 3 [(validate.rules).repeated = {min_items: 1}];
}

// UpdateStaffAvailabilityRequest
message UpdateStaffAvailabilityOverrideRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
  // staff availability override day list
  repeated moego.models.organization.v1.SlotAvailabilityDayDef override_days = 4 [(validate.rules).repeated = {max_items: 100}];
  // time override days
  repeated moego.models.organization.v1.TimeAvailabilityDayDef time_override_days = 5 [(validate.rules).repeated = {max_items: 100}];
}

// DeleteStaffAvailabilityOverrideRequest
message DeleteStaffAvailabilityOverrideRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
  // staff availability override day list
  repeated string override_days = 4;
  // availability type
  optional moego.models.organization.v1.AvailabilityType availability_type = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// DeleteStaffAvailabilityOverrideResponse
message DeleteStaffAvailabilityOverrideResponse {}

// GetStaffAvailabilityOverrideRequest
message GetStaffAvailabilityOverrideRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id, if empty, will return all staffs
  repeated int64 staff_ids = 3 [(validate.rules).repeated = {unique: true}];
  // availability type
  optional moego.models.organization.v1.AvailabilityType availability_type = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

//SlotAvailabilityDayList
message SlotAvailabilityDayList {
  // slot availability day
  repeated moego.models.organization.v1.SlotAvailabilityDay slots = 1;
}

// TimeAvailabilityDayList
message TimeAvailabilityDayList {
  // time availability day
  repeated moego.models.organization.v1.TimeAvailabilityDay slots = 1;
}

// GetStaffAvailabilityOverrideResponse
message GetStaffAvailabilityOverrideResponse {
  // key is staff id
  map<int64, SlotAvailabilityDayList> override_days = 1;
  // time override days
  map<int64, TimeAvailabilityDayList> time_override_days = 3;
}

// get staff availability status
message GetStaffAvailabilityStatusRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // staff id list
  repeated int64 staff_id_list = 2 [(validate.rules).repeated = {unique: true}];
}

// GetStaffAvailabilityStatusResponse
message GetStaffAvailabilityStatusResponse {
  // map staff id to is available
  map<int64, bool> staff_availability = 1;
}

// UpdateStaffAvailabilityResponse
message UpdateStaffAvailabilityResponse {}
