syntax = "proto3";

package moego.service.organization.v1;

import "moego/models/organization/v1/van_model.proto";
import "moego/utils/v1/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1;organizationsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.organization.v1";

// get van list request
message GetVanListRequest {
  // company id
  string company_id = 1;
  // business id(optional)
  optional string business_id = 2;
  // pagination request(optional)
  optional moego.utils.v1.PaginationRequest pagination = 3;
}

// get van list response
message GetVanListResponse {
  // pagination
  moego.utils.v1.PaginationResponse pagination = 1;
  // van list
  repeated models.organization.v1.VanModel van_list = 2;
}

// get van list by staff ids request
message GetVanListByStaffIdsRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // staff ids
  repeated int64 staff_ids = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
    min_items: 1
  }];
}

// get van list by staff ids response
message GetVanListByStaffIdsResponse {
  // staff id to van
  map<int64, models.organization.v1.VanModel> staff_van_map = 1;
}

// get van list by multi company ids request
message GetVanListByMultiCompanyIdRequest {
  // company id
  repeated int64 company_ids = 1;
}

// get van list by multi company ids response
message GetVanListByMultiCompanyIdResponse {
  // staff id to van
  map<int64, models.organization.v1.VanListModel> company_van_list_map = 1;
}

// get assigned van by staff ids request
message GetAssignedVanForStaffIdRequest {
  // staff ids
  repeated int64 staff_ids = 1;
}

// get assigned van by staff ids response
message GetAssignedVanForStaffIdResponse {
  // staff id to van
  map<int64, int64> staff_van_map = 1;
}

// force assign staff to van request
message ForceAssignStaffToVanRequest {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64.gt = 0];
  // van id
  optional int64 van_id = 2;
}

// force assign staff to van response
message ForceAssignStaffToVanResponse {}

// company service
service VanService {
  // get van list
  rpc GetVanList(GetVanListRequest) returns (GetVanListResponse);
  // get van list by staff ids
  rpc GetVanListByStaffIds(GetVanListByStaffIdsRequest) returns (GetVanListByStaffIdsResponse);
  // get van list by multi company id
  rpc GetVanListByMultiCompanyId(GetVanListByMultiCompanyIdRequest) returns (GetVanListByMultiCompanyIdResponse);
  // get assigned van for staff id
  rpc GetAssignedVanForStaffId(GetAssignedVanForStaffIdRequest) returns (GetAssignedVanForStaffIdResponse);
  // force assign staff to van
  rpc ForceAssignStaffToVan(ForceAssignStaffToVanRequest) returns (ForceAssignStaffToVanResponse);
}
