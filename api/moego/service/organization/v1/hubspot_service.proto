syntax = "proto3";

package moego.service.organization.v1;

import "moego/models/organization/v1/hubspot_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1;organizationsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.organization.v1";

// init hubspot request
message InitContactRequest {
  // moego account id
  int64 account_id = 1;
}

// init contact response
message InitContactResponse {
  // moego account id
  int64 account_id = 1;
  // hubspot contact id
  string hubspot_contact_id = 2;
}

// query contact request
message QueryContactRequest {
  // moego company id
  int64 company_id = 1;
  //is notify slack tier2
  bool notify_slack = 2;
}

// query contact response
message QueryContactResponse {
  //tier class
  models.organization.v1.TierEnums tier = 1;
  //timezone name
  string timezone_name = 2;
  //phone
  string phone_number = 3;
}

// hubspot connect service
service HubspotService {
  // init contact
  rpc InitContact(InitContactRequest) returns (InitContactResponse);
  // query contact some properties
  rpc QueryAndSyncContactTier(QueryContactRequest) returns (QueryContactResponse);
}
