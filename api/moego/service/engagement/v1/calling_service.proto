syntax = "proto3";

package moego.service.engagement.v1;

import "google/protobuf/empty.proto";
import "google/type/interval.proto";
import "moego/models/engagement/v1/calling_client_models.proto";
import "moego/models/engagement/v1/calling_log_defs.proto";
import "moego/models/engagement/v1/calling_log_models.proto";
import "moego/models/engagement/v1/voice_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/engagement/v1;engagementsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.engagement.v1";

// calling log service
service CallingService {
  // create calling log
  rpc CreateCallingLog(CreateCallingLogRequest) returns (CreateCallingLogResponse);
  // get calling log
  rpc GetCallingLog(GetCallingLogRequest) returns (GetCallingLogResponse);
  //  update calling log
  rpc UpdateCallingLog(UpdateCallingLogRequest) returns (UpdateCallingLogResponse);
  //  delete calling log
  rpc DeleteCallingLog(DeleteCallingLogRequest) returns (DeleteCallingLogResponse);
  //  list calling logs
  rpc ListCallingLogs(ListCallingLogsRequest) returns (ListCallingLogsResponse);
  // get token
  rpc GetToken(google.protobuf.Empty) returns (GetTokenResponse) {
    option deprecated = true;
  }
  // get calling detail
  rpc GetCallingDetail(GetCallingDetailRequest) returns (GetCallingDetailResponse);
  // get customer dial mask
  rpc GetCustomerDialMask(GetCustomerDialMaskRequest) returns (GetCustomerDialMaskResponse);
  // search client
  rpc SearchClient(SearchClientRequest) returns (SearchClientResponse);
  // get calling log overview
  rpc GetCallingLogOverview(GetCallingLogOverviewRequest) returns (GetCallingLogOverviewResponse);
  // get default local phone number for B-APP
  rpc GetDefaultLocalPhoneNumber(google.protobuf.Empty) returns (GetDefaultLocalPhoneNumberResponse);
  // make call on B-APP
  rpc CallFromBApp(CallFromBAppRequest) returns (google.protobuf.Empty);
  // get token
  rpc GetVoipToken(GetTokenRequest) returns (GetTokenResponse);
  // 是否存在多个相同号码处于 unresolved 状态的 activity log
  rpc ConfirmUnresolvedRange(ConfirmUnresolvedRangeRequest) returns (ConfirmUnresolvedRangeResponse);
  // 将一个或多个 activity log 标记为 resolved
  rpc MarkLogResolveStatus(MarkLogResolveStatusRequest) returns (MarkLogResolveStatusResponse);
  // add company to calling feature whitelist
  rpc AddCompanyToWhitelist(AddCompanyToWhitelistRequest) returns (AddCompanyToWhitelistResponse);
}

// create calling log request
message CreateCallingLogRequest {
  // calling log
  models.engagement.v1.CreateCallingLogDef calling_log = 1;
}

// create calling response
message CreateCallingLogResponse {
  // calling log
  models.engagement.v1.CallingLogView calling_log_view = 1;
}

// get calling log request
message GetCallingLogRequest {
  // ID
  int64 id = 1;
}

// get calling log response
message GetCallingLogResponse {
  // calling log.
  models.engagement.v1.CallingLogView calling_log_view = 1;
}

// list calling logs request
message ListCallingLogsRequest {
  // filter
  message Filter {
    // filter client ids
    repeated int64 client_ids = 1 [(validate.rules).repeated = {
      max_items: 500
      items: {
        int64: {not_in: 0}
      }
    }];
    // filter customer ids
    repeated int64 customer_ids = 2 [(validate.rules).repeated = {
      max_items: 500
      items: {
        int64: {not_in: 0}
      }
    }];
    // statuses
    repeated models.engagement.v1.CallingDirection direction = 3;
    // statuses
    repeated models.engagement.v1.Status statuses = 4;
    // categories
    repeated models.engagement.v1.Category categories = 5;
    // record types
    repeated models.engagement.v1.RecordType record_types = 6;
    // business ids
    repeated int64 business_ids = 7;
    // init time period
    optional google.type.Interval init_time_period = 8;
    // is resolved
    optional bool is_resolved = 9;
  }

  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 1 [(validate.rules).message = {required: true}];
  // order by
  optional models.engagement.v1.CallingLogOrderBy order_by = 2;
  // deprecated, replace with CallingLogFilter
  optional Filter filter = 3 [deprecated = true];
  // company id
  int64 company_id = 4 [(validate.rules).int64 = {gt: 0}];
  // filter
  optional models.engagement.v1.CallingLogFilter log_filter = 5;
}

// list calling logs response
message ListCallingLogsResponse {
  // calling logs
  repeated models.engagement.v1.CallingLogView calling_log_views = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// update calling log request
message UpdateCallingLogRequest {
  // ID of the calling to update.
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // Company ID of the calling to update.
  models.engagement.v1.UpdateCallingLogDef update_calling_log_def = 2;
}

// update calling log response
message UpdateCallingLogResponse {
  // calling log.
  models.engagement.v1.CallingLogView calling_log_view = 1;
}

// delete calling log request
message DeleteCallingLogRequest {
  // ID of the calling to delete.
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // Company ID
  optional int64 company_id = 2;
}

// delete calling log response
message DeleteCallingLogResponse {
  // success
  bool success = 1;
}

// get token request
message GetTokenRequest {
  // calling source
  optional models.engagement.v1.CallingSource calling_source = 1;
}

// get token response
message GetTokenResponse {
  // the token def
  string token = 1;
  // staff permissions
  repeated models.engagement.v1.StaffPermission staff_permissions = 2;
}

// get calling detail request
message GetCallingDetailRequest {
  // the identifier
  oneof identifier {
    // the customer id
    int64 customer_id = 1;
    // client id
    int64 client_id = 2;
  }
}

// get calling detail response
message GetCallingDetailResponse {
  // the customer
  moego.models.engagement.v1.Customer customer = 1;
  // the pets
  repeated moego.models.engagement.v1.Pet pets = 2;
  // direction
  moego.models.engagement.v1.CallingDirection direction = 3;
  // company id
  int64 company_id = 4;
  // business id
  int64 business_id = 5;
  // company name
  string company_name = 6;
  // business name
  string business_name = 7;
  // client id
  int64 client_id = 8;
  // client name
  string client_name = 9;
  // is recording
  bool is_recording = 10;
}

// get customer dial mask request
message GetCustomerDialMaskRequest {
  // the identifier
  oneof identifier {
    // the customer id
    int64 customer_id = 1;
    // client id
    int64 client_id = 2;
  }
}

// get customer dial mask response
message GetCustomerDialMaskResponse {
  // the mask
  string mask = 1;
}

// search client
message SearchClientRequest {
  // search keyword search name
  string keyword = 1;
  // company id
  int64 company_id = 2;
}

// search client
message SearchClientResponse {
  // calling logs
  repeated models.engagement.v1.Client clients = 1;
}

// get calling log overview request
message GetCallingLogOverviewRequest {
  // filter
  message Filter {
    // init time period
    optional google.type.Interval init_time_period = 2;
  }
  // filter
  Filter filter = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;
  // staff id
  int64 staff_id = 4;
}

// get calling log overview response
message GetCallingLogOverviewResponse {
  // calling log overview
  message CallReceived {
    // call received
    models.engagement.v1.IndicatorDef call_received = 1;
    // calling answer overview
    models.engagement.v1.IndicatorDef call_answered = 2;
    // calling unanswered overview
    models.engagement.v1.IndicatorDef call_unanswered = 3;
  }
  // calling received overview
  CallReceived call_received = 1;
  // calling log overview
  models.engagement.v1.IndicatorDef call_in_after_hour = 2;
  // calling log overview
  models.engagement.v1.IndicatorDef voicemail_received = 3;
  // calling log overview
  models.engagement.v1.IndicatorDef average_response_time = 4;
  // resolved
  models.engagement.v1.IndicatorDef resolved = 5;
  // unresolved
  models.engagement.v1.IndicatorDef unresolved = 6;
}

// get default local phone number result
message GetDefaultLocalPhoneNumberResponse {
  // the phone number
  string phone_number = 1;
}

// call from B-APP params
message CallFromBAppRequest {
  // local phone number, 可能为空，为空服务端会读取店家的坐席配置
  optional string local_phone_number = 1;
  // call to identifier
  oneof target_identifier {
    // the customer id
    int64 customer_id = 2;
    // the client id
    int64 client_id = 3;
    // the phone number
    string phone_number = 4;
  }
}

// ExistMultipleUnresolvedLogRequest
message ConfirmUnresolvedRangeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // 待确认的 unresolved log 过滤范围
  models.engagement.v1.CallingLogFilter filter = 2;
}

// ExistMultipleUnresolvedLogResponse
message ConfirmUnresolvedRangeResponse {
  // 范围中 unresolved 状态的 log 数目
  int32 unresolved_count = 1;
}

// MarkLogAsResolvedRequest
message MarkLogResolveStatusRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // 待标记为 resolved 的 log 过滤范围
  models.engagement.v1.CallingLogFilter filter = 2;
  // is resolved
  bool is_resolved = 3;
}

// MarkLogAsResolvedResponse
message MarkLogResolveStatusResponse {}

// add company to calling feature whitelist request
message AddCompanyToWhitelistRequest {
  // company identifier
  oneof company_identifier {
    // company id
    int64 company_id = 1 [(validate.rules).int64.gt = 0];
    // company owner email
    string company_owner_email = 2 [(validate.rules).string.email = true];
  }
  // api key
  string api_key = 3 [(validate.rules).string.min_len = 1];
}

// add company to calling feature whitelist response
message AddCompanyToWhitelistResponse {}
