// @since 2024-07-11 14:21:59
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.ai_assistant.v2;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/ai_assistant/v2;aiassistantsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.ai_assistant.v2";

// process new message request
message ProcessMessageRequest {
  // the company id
  int64 company_id = 1;
  // the business id
  int64 business_id = 2;
  // the customer id
  int64 customer_id = 3;
  // the last message id
  int64 last_message_id = 4;
}

// process new message response
message ProcessMessageAsyncResponse {}

// the chat_assistant service
service ChatAssistantService {
  // handle new message, anything insert into message detail table should notify me.
  rpc ProcessMessageAsync(ProcessMessageRequest) returns (ProcessMessageAsyncResponse);
}
