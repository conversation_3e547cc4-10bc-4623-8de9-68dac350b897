// @since 2023-06-29 16:13:41
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.ai_assistant.v1;

import "moego/models/ai_assistant/v1/business_conversation_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/ai_assistant/v1;aiassistantsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.ai_assistant.v1";

// create business conversation request
message CreateBusinessConversationRequest {
  // business id
  int64 business_id = 1;

  // staff id
  int64 staff_id = 2;

  // prompt. If provided, the conversation will start based on this prompt
  string prompt = 3 [(validate.rules).string = {max_len: 8192}];

  // scenario
  string scenario = 5;
  // variables
  map<string, string> variables = 4;
}

// create business conversation response
message CreateBusinessConversationResponse {
  // conversation id
  int64 conversation_id = 1;
}

// close business conversation request
message CloseBusinessConversationRequest {
  // conversation id
  int64 conversation_id = 1;
  // business id
  int64 business_id = 2;
}

// close business conversation response
message CloseBusinessConversationResponse {}

// ask request
message AskRequest {
  // conversation id
  int64 conversation_id = 1;

  // business id
  int64 business_id = 4;

  // sender id (staff id)
  int64 sender_id = 2;

  // question
  string question = 3 [(validate.rules).string = {max_len: 8192}];
}

// ask response
message AskResponse {
  // question id
  int64 question_id = 1;
  // answer
  string answer = 2;
}

// rephrase answer request
message RephraseAnswerRequest {
  // business id
  int64 business_id = 4;

  // conversation id
  int64 conversation_id = 1;

  // question id
  int64 question_id = 2;
}

// rephrase answer response
message RephraseAnswerResponse {
  // new question id, different from the one in RephraseAnswerRequest
  int64 question_id = 1;
  // answer
  string answer = 2;
}

// adopt answer request
message AdoptAnswerRequest {
  // business id
  int64 business_id = 4;

  // conversation id
  int64 conversation_id = 1;

  // question id
  int64 question_id = 2;
}

// adopt answer request
message SendAnswerRequest {
  // business id
  int64 business_id = 4;

  // conversation id
  int64 conversation_id = 1;

  // question id
  int64 question_id = 2;

  // the message id
  int64 message_id = 3;
}

// adopt answer response
message AdoptAnswerResponse {}

// adopt answer response
message SendAnswerResponse {}

// get conversation questions request
message GetConversationQuestionsRequest {
  // conversation id
  int64 conversation_id = 1;
}

// get conversation questions response
message GetConversationQuestionsResponse {
  // questions
  repeated moego.models.ai_assistant.v1.BusinessConversationQuestionModel questions = 1;
}

// describe business conversations request
message DescribeBusinessConversationsRequest {
  // business id
  optional int64 business_id = 1;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2;
}

// describe business conversations response
message DescribeBusinessConversationsResponse {
  // conversations
  repeated moego.models.ai_assistant.v1.BusinessConversationModel conversations = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// describe business conversation summaries request
message DescribeBusinessConversationSummariesRequest {
  // business id
  optional int64 business_id = 1;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2;
  // order by, field name should be in BusinessConversationSummaryModel
  optional moego.utils.v2.OrderBy order_by = 3;
}

// describe business conversation summaries response
message DescribeBusinessConversationSummariesResponse {
  // summaries
  repeated moego.models.ai_assistant.v1.BusinessConversationSummaryModel summaries = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// describe system summaries request
message DescribeSystemSummariesRequest {}

// describe system summaries response
message DescribeSystemSummariesResponse {
  // business count
  int32 business_count = 1;
  // conversation count
  int32 conversation_count = 2;
  // nonempty conversation count
  int32 nonempty_conversation_count = 9;
  // adopted conversation count
  int32 adopted_conversation_count = 3;
  // question count
  int32 question_count = 4;
  // total cost
  double total_cost = 5;
  // total input token
  int32 total_input_token = 6;
  // total output token
  int32 total_output_token = 7;
  // total token
  int32 total_token = 8;
}

// business conversation service
service BusinessConversationService {
  // create a business conversation
  rpc CreateConversation(CreateBusinessConversationRequest) returns (CreateBusinessConversationResponse);

  // close the business conversation
  rpc CloseConversation(CloseBusinessConversationRequest) returns (CloseBusinessConversationResponse);

  // pose a question and ask for an answer
  rpc Ask(AskRequest) returns (AskResponse);

  // get a rephrase answer for the question
  rpc RephraseAnswer(RephraseAnswerRequest) returns (RephraseAnswerResponse);

  // adopt an answer
  rpc AdoptAnswer(AdoptAnswerRequest) returns (AdoptAnswerResponse);

  // adopt an answer
  rpc SendAnswer(SendAnswerRequest) returns (SendAnswerResponse);

  // get conversation questions
  rpc GetConversationQuestions(GetConversationQuestionsRequest) returns (GetConversationQuestionsResponse);

  // describe business conversation
  rpc DescribeConversations(DescribeBusinessConversationsRequest) returns (DescribeBusinessConversationsResponse);

  // describe business conversation summaries
  rpc DescribeConversationSummaries(DescribeBusinessConversationSummariesRequest) returns (DescribeBusinessConversationSummariesResponse);

  // describe system summaries
  rpc DescribeSystemSummaries(DescribeSystemSummariesRequest) returns (DescribeSystemSummariesResponse);
}
