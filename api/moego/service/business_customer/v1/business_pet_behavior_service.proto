syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_behavior_defs.proto";
import "moego/models/business_customer/v1/business_pet_behavior_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// get pet behavior request
message GetPetBehaviorRequest {
  // pet behavior id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// get pet behavior response
message GetPetBehaviorResponse {
  // pet behavior
  moego.models.business_customer.v1.BusinessPetBehaviorModel behavior = 1;
}

// list pet behavior request
message ListPetBehaviorRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
}

// list pet behavior response
message ListPetBehaviorResponse {
  // pet behavior list
  repeated moego.models.business_customer.v1.BusinessPetBehaviorModel behaviors = 1;
}

// create pet behavior request
message CreatePetBehaviorRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];

  // pet behavior
  moego.models.business_customer.v1.BusinessPetBehaviorCreateDef behavior = 3 [(validate.rules).message.required = true];
}

// create pet behavior response
message CreatePetBehaviorResponse {
  // pet behavior
  moego.models.business_customer.v1.BusinessPetBehaviorModel behavior = 1;
}

// update pet behavior request
message UpdatePetBehaviorRequest {
  // pet behavior id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];

  // pet behavior
  moego.models.business_customer.v1.BusinessPetBehaviorUpdateDef behavior = 4 [(validate.rules).message.required = true];
}

// update pet behavior response
message UpdatePetBehaviorResponse {}

// sort pet behavior request
message SortPetBehaviorRequest {
  // pet behavior id list, should contain all pet behavior ids for the company / business
  repeated int64 ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];

  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// sort pet behavior response
message SortPetBehaviorResponse {}

// delete pet behavior request
message DeletePetBehaviorRequest {
  // pet behavior id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// delete pet behavior response
message DeletePetBehaviorResponse {}

// Service for pet behavior settings
service BusinessPetBehaviorService {
  // Get a pet behavior.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The pet behavior does not exist, or does not belong to the given company or business.
  rpc GetPetBehavior(GetPetBehaviorRequest) returns (GetPetBehaviorResponse);

  // List pet behaviors.
  // If the company does not exists, or does not define any pet behaviors, an empty list will be returned rather than an error.
  rpc ListPetBehavior(ListPetBehaviorRequest) returns (ListPetBehaviorResponse);

  // Create a pet behavior.
  // The name of the new pet behavior must be unique among all pet behaviors of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The name is already used by another pet behavior of the company or business.
  rpc CreatePetBehavior(CreatePetBehaviorRequest) returns (CreatePetBehaviorResponse);

  // Update a pet behavior.
  // If the name of the pet behavior is changed, it must be unique among all pet behavior of the company or business.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The name is already used by another pet behavior of the company or business, or the pet
  //                      behavior does not exist, or has been deleted, or does not belong to the company or business.
  rpc UpdatePetBehavior(UpdatePetBehaviorRequest) returns (UpdatePetBehaviorResponse);

  // Sort pet behaviors of the company or business.
  // Pet behaviors will be sorted according to the order of `ids`. If there are pet behaviors of the company or business
  // whose ids are not included in `ids`, they will be sorted to the end. If an id in `ids` does not exist or does not
  // belong to the company or business, it will be ignored.
  rpc SortPetBehavior(SortPetBehaviorRequest) returns (SortPetBehaviorResponse);

  // Delete a pet behavior.
  // If the pet behavior is already deleted, it will return success without throwing any error.
  //
  // Error codes:
  // - CODE_PARAMS_ERROR: The pet behavior does not exist, or does not belong to the given company.
  rpc DeletePetBehavior(DeletePetBehaviorRequest) returns (DeletePetBehaviorResponse);
}
