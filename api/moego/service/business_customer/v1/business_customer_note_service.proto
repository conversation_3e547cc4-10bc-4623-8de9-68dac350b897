syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_customer_note_defs.proto";
import "moego/models/business_customer/v1/business_customer_note_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// create customer note request
message CreateCustomerNoteRequest {
  // created by staff id
  optional int64 created_by = 1 [(validate.rules).int64.gt = 0];

  // customer id
  int64 customer_id = 2 [(validate.rules).int64.gt = 0];

  // note
  moego.models.business_customer.v1.BusinessCustomerNoteCreateDef note = 3 [(validate.rules).message.required = true];
}

// create customer note response
message CreateCustomerNoteResponse {
  // customer note
  moego.models.business_customer.v1.BusinessCustomerNoteModel note = 1;
}

// update customer note request
message UpdateCustomerNoteRequest {
  // updated by staff id
  optional int64 updated_by = 1 [(validate.rules).int64.gt = 0];

  // customer note id
  int64 id = 2 [(validate.rules).int64.gt = 0];

  // note
  moego.models.business_customer.v1.BusinessCustomerNoteUpdateDef note = 3 [(validate.rules).message.required = true];
}

// update customer note response
message UpdateCustomerNoteResponse {
  // customer note
  moego.models.business_customer.v1.BusinessCustomerNoteModel note = 1;
}

// delete customer note request
message DeleteCustomerNoteRequest {
  // deleted by staff id
  optional int64 deleted_by = 1 [(validate.rules).int64.gt = 0];

  // customer note id
  int64 id = 2 [(validate.rules).int64.gt = 0];
}

// delete customer note response
message DeleteCustomerNoteResponse {}

// get customer note request
message GetCustomerNoteRequest {
  // customer note id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// get customer note response
message GetCustomerNoteResponse {
  // customer note
  moego.models.business_customer.v1.BusinessCustomerNoteModel note = 1;
}

// list customer note request
message ListCustomerNoteRequest {
  // company id, optional
  int64 company_id = 1 [(validate.rules).int64.gte = 0];

  // customer id
  int64 customer_id = 3 [(validate.rules).int64.gt = 0];

  // pagination, required
  utils.v2.PaginationRequest pagination = 2;
}

// list customer note response
message ListCustomerNoteResponse {
  // customer note list
  repeated moego.models.business_customer.v1.BusinessCustomerNoteModel notes = 1;

  // pagination
  utils.v2.PaginationResponse pagination = 2;
}

// batch list customer note request
message BatchListCustomerNoteRequest {
  // customer ids
  repeated int64 customer_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 200
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch list customer notes response
message BatchListCustomerNoteResponse {
  // notes
  message CustomerNotes {
    // notes
    repeated moego.models.business_customer.v1.BusinessCustomerNoteModel notes = 1;
  }
  // customer id to notes
  map<int64, CustomerNotes> customer_notes_map = 1;
}

// Service for customer note
service BusinessCustomerNoteService {
  // Create a customer note
  // A customer can at most have 200 notes
  rpc CreateCustomerNote(CreateCustomerNoteRequest) returns (CreateCustomerNoteResponse);

  // Update a customer note
  rpc UpdateCustomerNote(UpdateCustomerNoteRequest) returns (UpdateCustomerNoteResponse);

  // Delete a customer note
  rpc DeleteCustomerNote(DeleteCustomerNoteRequest) returns (DeleteCustomerNoteResponse);

  // Get a customer note by id
  rpc GetCustomerNote(GetCustomerNoteRequest) returns (GetCustomerNoteResponse);

  // List customer notes of a customer
  rpc ListCustomerNote(ListCustomerNoteRequest) returns (ListCustomerNoteResponse);

  // Batch list customer note of multiple customers
  // Each customer returns a maximum of 50 latest notes
  rpc BatchListCustomerNote(BatchListCustomerNoteRequest) returns (BatchListCustomerNoteResponse);
}
