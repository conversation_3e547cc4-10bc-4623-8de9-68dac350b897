syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_vaccine_request_defs.proto";
import "moego/models/business_customer/v1/business_pet_vaccine_request_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// Service for pet vaccine request
service BusinessPetVaccineRequestService {
  // get a pet vaccine request
  rpc GetPetVaccineRequest(GetPetVaccineRequestRequest) returns (GetPetVaccineRequestResponse);
  // create a new pet vaccine request
  rpc CreatePetVaccineRequest(CreatePetVaccineRequestRequest) returns (CreatePetVaccineRequestResponse);
  // update a pet vaccine request
  rpc UpdatePetVaccineRequest(UpdatePetVaccineRequestRequest) returns (UpdatePetVaccineRequestResponse);
  // list pet vaccine requests
  rpc ListPetVaccineRequests(ListPetVaccineRequestsRequest) returns (ListPetVaccineRequestsResponse);
}

// GetPetVaccineRequestRequest
message GetPetVaccineRequestRequest {
  // pet vaccine request id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// GetPetVaccineRequestResponse
message GetPetVaccineRequestResponse {
  // pet vaccine request
  models.business_customer.v1.BusinessPetVaccineRequestModel pet_vaccine_request = 1;
}

// CreatePetVaccineRequestRequest
message CreatePetVaccineRequestRequest {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet vaccine request
  models.business_customer.v1.BusinessPetVaccineRequestCreateDef pet_vaccine_request = 2 [(validate.rules).message.required = true];
}

// CreatePetVaccineRequestResponse
message CreatePetVaccineRequestResponse {
  // pet vaccine request
  models.business_customer.v1.BusinessPetVaccineRequestModel pet_vaccine_request = 1;
}

// UpdatePetVaccineRequestRequest
message UpdatePetVaccineRequestRequest {
  // pet vaccine request id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet vaccine request
  models.business_customer.v1.BusinessPetVaccineRequestUpdateDef pet_vaccine_request = 2 [(validate.rules).message.required = true];
}

// UpdatePetVaccineRequestResponse
message UpdatePetVaccineRequestResponse {
  // pet vaccine request
  models.business_customer.v1.BusinessPetVaccineRequestModel pet_vaccine_request = 1;
}

// ListPetVaccineRequestsRequest
message ListPetVaccineRequestsRequest {
  // pet ids
  repeated int64 pet_ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // statuses
  repeated models.business_customer.v1.BusinessPetVaccineRequestModel.Status statuses = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        not_in: [0]
      }
    }
  }];

  // pagination
  utils.v2.PaginationRequest pagination = 3 [(validate.rules).message.required = true];
}

// ListPetVaccineRequestsResponse
message ListPetVaccineRequestsResponse {
  // pet vaccine requests
  repeated models.business_customer.v1.BusinessPetVaccineRequestModel pet_vaccine_requests = 1;
  // pagination
  utils.v2.PaginationResponse pagination = 2;
}
