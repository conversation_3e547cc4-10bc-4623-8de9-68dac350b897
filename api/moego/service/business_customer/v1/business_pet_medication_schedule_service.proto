syntax = "proto3";

package moego.service.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_medication_models.proto";
import "moego/models/business_customer/v1/business_pet_medication_schedule_defs.proto";
import "moego/models/business_customer/v1/business_pet_schedule_setting_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.business_customer.v1";

// Create medication schedule request
message CreateMedicationScheduleRequest {
  // pet medication schedule
  models.business_customer.v1.BusinessPetMedicationScheduleDef medication_schedule = 1 [(validate.rules).message = {required: true}];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
}

// Create medication schedule response
message CreateMedicationScheduleResponse {
  // medication id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// batch create medication schedule request
message BatchCreateMedicationScheduleRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // medication schedule
  repeated models.business_customer.v1.BusinessPetMedicationScheduleDef medication_schedules = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// batch create medication schedule response
message BatchCreateMedicationScheduleResponse {}

// Update medication schedule request
message UpdateMedicationScheduleRequest {
  // medication id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // pet medication schedule
  models.business_customer.v1.BusinessPetMedicationScheduleDef medication_schedule = 2 [(validate.rules).message = {required: true}];
  // company id
  int64 company_id = 3 [(validate.rules).int64.gt = 0];
}

// Update medication schedule response
message UpdateMedicationScheduleResponse {}

// Delete medication schedule request
message DeleteMedicationScheduleRequest {
  // medication id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
}

// Delete medication schedule response
message DeleteMedicationScheduleResponse {}

// List pet's medication schedule request
message ListPetMedicationScheduleRequest {
  // pet id. Will be added to pet_ids if larger than zero, otherwise, it will be ignored
  int64 pet_id = 1 [(validate.rules).int64.gte = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // pet id list
  repeated int64 pet_ids = 3 [(validate.rules).repeated = {
    max_items: 100
    items: {
      int64: {gt: 0}
    }
  }];
}

// List pet's medication schedule response
message ListPetMedicationScheduleResponse {
  // pet's medication contents
  repeated models.business_customer.v1.BusinessPetMedicationModel medications = 1;
  // pet's medication schedules
  repeated models.business_customer.v1.BusinessPetScheduleSettingModel schedules = 2;
}

// Business pet medication schedule service
service BusinessPetMedicationScheduleService {
  // Create medication schedule
  // Medication display rules: {Medication schedule} {Amount} {Medication unit} {Medication name} {Medication notes}
  rpc CreateMedicationSchedule(CreateMedicationScheduleRequest) returns (CreateMedicationScheduleResponse);

  // Batch create medication schedule
  rpc BatchCreateMedicationSchedule(BatchCreateMedicationScheduleRequest) returns (BatchCreateMedicationScheduleResponse);

  // Update medication schedule
  rpc UpdateMedicationSchedule(UpdateMedicationScheduleRequest) returns (UpdateMedicationScheduleResponse);

  // Delete medication schedule
  rpc DeleteMedicationSchedule(DeleteMedicationScheduleRequest) returns (DeleteMedicationScheduleResponse);

  // List pet's medication schedule
  rpc ListPetMedicationSchedule(ListPetMedicationScheduleRequest) returns (ListPetMedicationScheduleResponse);
}
