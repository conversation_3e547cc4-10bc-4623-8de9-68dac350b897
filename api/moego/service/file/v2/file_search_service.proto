syntax = "proto3";

package moego.service.file.v2;

import "moego/models/file/v2/file_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/file/v2;filesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.file.v2";

// search file request
message DescribeFilesRequest {
  // creator id
  repeated int64 creator_ids = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
    max_items: 100
  }];
  // business id
  repeated int64 business_ids = 2 [(validate.rules).repeated = {
    items: {
      int64: {gte: 0}
    }
    unique: true
    max_items: 100
  }];
  // owner type
  optional string owner_type_like = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // usage
  optional string usage_like = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // id
  repeated int64 ids = 5 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
    max_items: 100
  }];
  // default not include
  optional bool include_deleted = 6;
  // owner ids
  repeated int64 owner_ids = 7 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
    max_items: 100
  }];
  // company ids
  repeated int64 company_ids = 8 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
    max_items: 100
  }];
  // order by
  optional moego.utils.v2.OrderBy order_by = 14;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 15;
}

// search file response
message DescribeFilesResponse {
  // pagination
  moego.utils.v2.PaginationResponse pagination = 1;
  // file list
  repeated models.file.v2.FileModel files = 2;
}

// FileService
service FileSearchService {
  // DescribeFiles
  rpc DescribeFiles(DescribeFilesRequest) returns (DescribeFilesResponse);
}
