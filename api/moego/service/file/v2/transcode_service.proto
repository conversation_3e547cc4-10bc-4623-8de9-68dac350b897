syntax = "proto3";

package moego.service.file.v2;

import "moego/models/file/v2/file_defs.proto";
import "moego/models/file/v2/transcode_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/file/v2;filesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.file.v2";

// GetJobRequest
message GetJobRequest {
  // job id
  int64 job_id = 1;
}

// GetJobResponse
message GetJobResponse {
  // job record
  moego.models.file.v2.JobModel job = 1;
}

// CancelJobRequest
message CancelJobRequest {
  // job id
  int64 job_id = 1;
}

// CancelJobResponse
message CancelJobResponse {
  // job record
  moego.models.file.v2.JobModel job = 1;
}

// CreateJobRequest
message CreateJobRequest {
  // account id for the creator
  optional int64 creator_id = 1 [(validate.rules).int64 = {gt: 0}];
  // owner type,eg:staff,pet...
  optional string owner_type = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // owner id
  optional int64 owner_id = 3 [(validate.rules).int64 = {gt: 0}];
  // source
  oneof source {
    option (validate.required) = true;
    // platform
    moego.models.file.v2.PlatformSourceDef platform = 4;
    // tenant
    moego.models.file.v2.TenantSourceDef tenant = 5;
  }
  // input source
  moego.models.file.v2.InputSource input_source = 6;
  // input settings
  optional moego.models.file.v2.InputSettings input_settings = 7;
  // Note: Both parameters `template_id` and `output_groups` are optional, and you can specify neither.
  // Usually, you only need to specify one of them. We recommend specifying parameter `template_id`.
  // If both are specified, parameter `template_id` will be used first, and parameter `output_groups` will be ignored.
  // If neither is specified, the service will use the default settings for transcoding.
  optional string template_id = 8;
  // custom output settings
  repeated moego.models.file.v2.OutputGroup output_groups = 9;
}

// CreateJobResponse
message CreateJobResponse {
  // job record
  moego.models.file.v2.JobModel job = 1;
}

// UpdateJobRequest
message UpdateJobRequest {
  // job identity
  oneof job_identity {
    // job id in database
    int64 job_id = 1;
    // aws MediaConvert job id
    string aws_media_convert_job_id = 2;
  }

  // job status
  optional moego.models.file.v2.JobStatus status = 7;

  // output manifest
  repeated moego.models.file.v2.OutputManifests manifests = 8;
}

// UpdateJobResponse
message UpdateJobResponse {
  // job record
  moego.models.file.v2.JobModel job = 1;
}

// TranscodeService
service TranscodeService {
  // get job
  rpc GetJob(GetJobRequest) returns (GetJobResponse);

  // create job
  rpc CreateJob(CreateJobRequest) returns (CreateJobResponse);

  // update job
  rpc UpdateJob(UpdateJobRequest) returns (UpdateJobResponse);

  // cancel job
  rpc CancelJob(CancelJobRequest) returns (CancelJobResponse);
}
