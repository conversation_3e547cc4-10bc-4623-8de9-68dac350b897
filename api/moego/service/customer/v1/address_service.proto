syntax = "proto3";

package moego.service.customer.v1;

import "google/protobuf/empty.proto";
import "moego/models/customer/v1/customer_address_models.proto";
import "moego/utils/v1/id_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/customer/v1;customersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.customer.v1";

// address input
message AddressInput {
  // address 1
  string address1 = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // address 2
  string address2 = 2 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 255
  }];
  // country
  string country = 3 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 255
  }];
  // city
  string city = 4 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 255
  }];
  // state
  string state = 5 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 255
  }];
  // zipcode
  string zipcode = 6 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 10
  }];
  // lat
  string lat = 7 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 50
  }];
  // lng
  string lng = 8 [(validate.rules).string = {
    ignore_empty: true
    min_len: 1
    max_len: 50
  }];
  // is primary
  bool is_primary = 9;
}

// address list
message AddressListOutput {
  // address list
  repeated moego.models.customer.v1.CustomerAddressModel address_list = 1;
}

// add address Input
message AddAddressInput {
  // address Input
  AddressInput address = 1 [(validate.rules).message = {required: true}];
  // account id
  int64 account_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// update address Input
message UpdateAddressInput {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // address input
  AddressInput address = 2 [(validate.rules).message = {required: true}];
  // account id
  int64 account_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// get address list input
message GetAddressListInput {
  // account id
  int64 account_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete address input
message DeleteAddressInput {
  // address id
  int64 address_id = 1 [(validate.rules).int64 = {gt: 0}];
  // account id
  int64 account_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// address service
service AddressService {
  // get address list
  rpc GetAddressList(GetAddressListInput) returns (AddressListOutput);
  // add address
  rpc AddAddress(AddAddressInput) returns (moego.utils.v1.Id);
  // update address
  rpc UpdateAddress(UpdateAddressInput) returns (google.protobuf.Empty);
  // delete address
  rpc DeleteAddress(DeleteAddressInput) returns (google.protobuf.Empty);
}
