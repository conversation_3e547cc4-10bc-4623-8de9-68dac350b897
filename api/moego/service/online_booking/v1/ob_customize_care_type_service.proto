syntax = "proto3";

package moego.service.online_booking.v1;

import "moego/models/offering/v1/service_enum.proto";
import "moego/models/online_booking/v1/ob_customize_care_type_defs.proto";
import "moego/models/online_booking/v1/ob_customize_care_type_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// Create booking care type request
message CreateBookingCareTypeRequest {
  // create booking care type def
  moego.models.online_booking.v1.BookingCareType booking_care_type = 1 [(validate.rules).message.required = true];
}

// Create booking care type response
message CreateBookingCareTypeResponse {
  // create booking care type result
  moego.models.online_booking.v1.BookingCareType booking_care_type = 1;
}

// Update booking care type request
message UpdateBookingCareTypeRequest {
  // update booking care type def
  moego.models.online_booking.v1.BookingCareType booking_care_type = 1 [(validate.rules).message.required = true];
}

// Update booking care type response
message UpdateBookingCareTypeResponse {
  // update booking care type result
  moego.models.online_booking.v1.BookingCareType booking_care_type = 1;
}

// List booking care types request
message ListBookingCareTypesRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
}

// List booking care types response
message ListBookingCareTypesResponse {
  // booking care type list
  repeated moego.models.online_booking.v1.BookingCareTypeView booking_care_types = 1;
}

// Get booking care type request
message GetBookingCareTypeRequest {
  // booking care type id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 3 [(validate.rules).int64.gt = 0];
}

// Get booking care type response
message GetBookingCareTypeResponse {
  // booking care type
  moego.models.online_booking.v1.BookingCareType booking_care_type = 1;
}

// Delete booking care type request
message DeleteBookingCareTypeRequest {
  // booking care type id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // company id
  int64 company_id = 2 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 3 [(validate.rules).int64.gt = 0];
  // staff id
  int64 staff_id = 4 [(validate.rules).int64.gt = 0];
}

// Delete booking care type response
message DeleteBookingCareTypeResponse {}

// Sort booking care type request
message SortBookingCareTypeRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // staff id
  int64 staff_id = 3 [(validate.rules).int64.gt = 0];
  // booking care type ids
  repeated int64 booking_care_type_ids = 4 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// Sort booking care type response
message SortBookingCareTypeResponse {
  // the sorted booking care type
  repeated moego.models.online_booking.v1.BookingCareTypeView booking_care_types = 1;
}

// Update selected services request
message UpdateSelectedServicesRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // service item  type
  moego.models.offering.v1.ServiceItemType service_item_type = 2;
  // location ids removed
  repeated int64 removed_location_ids = 3;
  // service id
  int64 service_id = 4 [(validate.rules).int64.gt = 0];
}

// Update selected services response
message UpdateSelectedServicesResponse {}

// Booking care type service
service BookingCareTypeService {
  // Create booking care type
  rpc CreateBookingCareType(CreateBookingCareTypeRequest) returns (CreateBookingCareTypeResponse);
  // Update booking care type
  rpc UpdateBookingCareType(UpdateBookingCareTypeRequest) returns (UpdateBookingCareTypeResponse);
  // List booking care types
  rpc ListBookingCareTypes(ListBookingCareTypesRequest) returns (ListBookingCareTypesResponse);
  // Get booking care type
  rpc GetBookingCareType(GetBookingCareTypeRequest) returns (GetBookingCareTypeResponse);
  // Delete booking care type
  rpc DeleteBookingCareType(DeleteBookingCareTypeRequest) returns (DeleteBookingCareTypeResponse);
  // Sort booking care type
  rpc SortBookingCareType(SortBookingCareTypeRequest) returns (SortBookingCareTypeResponse);
  // Update selected services
  rpc UpdateSelectedServices(UpdateSelectedServicesRequest) returns (UpdateSelectedServicesResponse);
}
