syntax = "proto3";

package moego.service.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/appointment/v1/appointment_pet_medication_schedule_defs.proto";
import "moego/models/online_booking/v1/medication_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// Create Medication request
message CreateMedicationRequest {
  // service detail type, 1: boarding, 2: daycare
  //  optional int32 service_detail_type = 3 [(validate.rules).int32 = {gt: 0}];
  // Medication time.
  repeated moego.models.online_booking.v1.MedicationModel.MedicationSchedule time = 4;
  // Medication amount, must be greater than 0.
  optional double amount = 5 [deprecated = true];
  // Medication unit.
  optional string unit = 6 [(validate.rules).string = {max_len: 2048}];
  // Medication name.
  optional string medication_name = 7 [(validate.rules).string = {max_len: 2048}];
  // Additional notes about the medication.
  optional string notes = 8 [(validate.rules).string = {max_len: 2048}];
  // createdAt
  optional google.protobuf.Timestamp created_at = 9;
  // updatedAt
  optional google.protobuf.Timestamp updated_at = 10;
  // Medication amount, such as 1.2, 1/2, 1 etc.
  optional string amount_str = 11 [(validate.rules).string = {max_len: 255}];
  // Medication select date
  optional moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef selected_date = 12;
}

// Medication service
service MedicationService {}

// Create Medication request list
message CreateMedicationRequestList {
  // values
  repeated CreateMedicationRequest values = 1;
}
