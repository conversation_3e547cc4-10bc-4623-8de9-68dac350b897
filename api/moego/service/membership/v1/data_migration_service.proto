syntax = "proto3";

package moego.service.membership.v1;

import "moego/models/membership/v1/data_migration_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/membership/v1;membershipsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.membership.v1";

// data migration service
service DataMigrationService {
  // import memberships
  rpc ImportMemberships(ImportMembershipsRequest) returns (ImportMembershipsResponse);
  // import discount benefits
  rpc ImportDiscountBenefits(ImportDiscountBenefitsRequest) returns (ImportDiscountBenefitsResponse);
  // import quantity benefits
  rpc ImportQuantityBenefits(ImportQuantityBenefitsRequest) returns (ImportQuantityBenefitsResponse);
  // import subscriptions
  rpc ImportSubscriptions(ImportSubscriptionsRequest) returns (ImportSubscriptionsResponse);
  // import quantity entitlements
  rpc ImportQuantityEntitlements(ImportQuantityEntitlementsRequest) returns (ImportQuantityEntitlementsResponse);
}

// import memberships request
message ImportMembershipsRequest {
  // membership data
  repeated models.membership.v1.MembershipData data = 1;
}

// import memberships response
message ImportMembershipsResponse {
  // successfully imported membership data
  repeated models.membership.v1.MembershipData imported = 1;
  // failed to import membership data
  repeated models.membership.v1.MembershipData failed = 2;
}

// import discount benefits request
message ImportDiscountBenefitsRequest {
  // discount benefit data
  repeated models.membership.v1.DiscountBenefitData data = 1;
}

// import discount benefits response
message ImportDiscountBenefitsResponse {
  // successfully imported discount benefit data
  repeated models.membership.v1.DiscountBenefitData imported = 1;
  // failed to import discount benefit data
  repeated models.membership.v1.DiscountBenefitData failed = 2;
}

// import quantity benefits request
message ImportQuantityBenefitsRequest {
  // quantity benefit data
  repeated models.membership.v1.QuantityBenefitData data = 1;
}

// import quantity benefits response
message ImportQuantityBenefitsResponse {
  // successfully imported quantity benefit data
  repeated models.membership.v1.QuantityBenefitData imported = 1;
  // failed to import quantity benefit data
  repeated models.membership.v1.QuantityBenefitData failed = 2;
}

// import subscriptions request
message ImportSubscriptionsRequest {
  // subscription data
  repeated models.membership.v1.SubscriptionData data = 1;
}

// import subscriptions response
message ImportSubscriptionsResponse {
  // successfully imported subscription data
  repeated models.membership.v1.SubscriptionData imported = 1;
  // failed to import subscription data
  repeated models.membership.v1.SubscriptionData failed = 2;
}

// import quantity entitlements request
message ImportQuantityEntitlementsRequest {
  // quantity entitlement data
  repeated models.membership.v1.QuantityEntitlementData data = 1;
}

// import quantity entitlements response
message ImportQuantityEntitlementsResponse {
  // successfully imported quantity entitlement data
  repeated models.membership.v1.QuantityEntitlementData imported = 1;
  // failed to import quantity entitlement data
  repeated models.membership.v1.QuantityEntitlementData failed = 2;
}
