// @since 2024-02-22 11:28:28
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.appointment.v1;

import "moego/models/appointment/v1/invoice_deposit_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// get invoice_deposit request
message GetInvoiceDepositListRequest {
  // the company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // the invoice id
  repeated int64 invoice_ids = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
}

// get invoice_deposit response
message GetInvoiceDepositListResponse {
  // deposit
  map<int64, models.appointment.v1.InvoiceDepositModel> invoice_deposit = 1;
}

// the invoice_deposit service
service InvoiceDepositService {
  // get invoice_deposit
  rpc GetInvoiceDepositList(GetInvoiceDepositListRequest) returns (GetInvoiceDepositListResponse);
}
