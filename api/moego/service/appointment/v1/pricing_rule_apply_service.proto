// @since 2024-08-17 10:03:02
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.appointment.v1;

import "moego/models/appointment/v1/pricing_rule_apply_log_enums.proto";
import "moego/models/appointment/v1/pricing_rule_apply_log_models.proto";
import "moego/models/offering/v1/pricing_rule_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// list pricing_rule_apply request
message ListPricingRuleApplyLogRequest {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// list pricing_rule_apply response
message ListPricingRuleApplyLogResponse {
  // the pricing_rule_apply
  repeated moego.models.appointment.v1.PricingRuleApplyLogModel pricing_rule_apply_logs = 1;
}

// Update upcoming appointment pet details request
message UpdateUpcomingAppointmentUsingPricingRuleRequest {
  // associated service ids
  // Appointments with associated service IDs will be updated
  repeated int64 service_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];

  // staff id
  // The staff id of the staff who updated the service
  int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// Update upcoming appt pet details response
message UpdateUpcomingAppointmentUsingPricingRuleResponse {
  // affected appointment count
  int64 affected_appointment_count = 1;
}

// apply pricing rule request
message ApplyPricingRuleRequest {
  // appointment id, deprecated, use source_id and source_type instead
  optional int64 appointment_id = 1 [deprecated = true];
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];

  // source id
  optional int64 source_id = 4 [(validate.rules).int64 = {gt: 0}];
  // source type
  optional moego.models.appointment.v1.PricingRuleApplySourceType source_type = 5 [(validate.rules).enum = {defined_only: true}];
  // pet detail list
  repeated moego.models.offering.v1.PetDetailCalculateDef pet_details = 6;
}

// apply pricing rule response
message ApplyPricingRuleResponse {
  // pet detail list
  repeated moego.models.offering.v1.PetDetailCalculateResultDef pet_details = 1;
}

// migrate pricing rule request
message MigratePricingRuleRequest {
  // company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// migrate pricing rule response
message MigratePricingRuleResponse {}

// the pricing_rule_apply service
service PricingRuleApplyService {
  // list pricing_rule_apply
  rpc ListPricingRuleApplyLog(ListPricingRuleApplyLogRequest) returns (ListPricingRuleApplyLogResponse) {
    option deprecated = true;
  }
  // Update upcoming appointment pet details
  rpc UpdateUpcomingAppointmentUsingPricingRule(UpdateUpcomingAppointmentUsingPricingRuleRequest) returns (UpdateUpcomingAppointmentUsingPricingRuleResponse) {
    option deprecated = true;
  }
  // apply pricing rule
  rpc ApplyPricingRule(ApplyPricingRuleRequest) returns (ApplyPricingRuleResponse) {
    option deprecated = true;
  }

  // migrate pricing rule
  rpc MigratePricingRule(MigratePricingRuleRequest) returns (MigratePricingRuleResponse);
}
