syntax = "proto3";

package moego.service.appointment.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// ListServiceColorCodeByAppointmentIds request
message ListServiceColorCodeByAppointmentIdsRequest {
  // Appointment ids
  repeated int64 appointment_ids = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
}

// ListServiceColorCodeByAppointmentIds response
message ListServiceColorCodeByAppointmentIdsResponse {
  // List of AppointmentIdAndServices
  repeated AppointmentIdAndColorCode color_codes = 1;

  // Appointment id and services
  message AppointmentIdAndColorCode {
    // Appointment id
    int64 appointment_id = 1;
    // Color code
    string color_code = 2;
  }
}

// ServiceService
service ServiceService {
  // service 现在属于 offering 域，不要使用这个 ServiceService
  option deprecated = true;

  // List service by appointment ids
  rpc ListServiceColorCodeByAppointmentIds(ListServiceColorCodeByAppointmentIdsRequest) returns (ListServiceColorCodeByAppointmentIdsResponse) {}
}
