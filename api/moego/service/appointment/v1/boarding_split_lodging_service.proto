// @since 2025-04-02 17:04:12
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.appointment.v1;

import "moego/models/appointment/v1/boarding_split_lodging_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// list boarding_split_lodging request
message ListBoardingSplitLodgingsRequest {
  // the appointment id
  repeated int64 appointment_ids = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
}

// list boarding_split_lodging response
message ListBoardingSplitLodgingsResponse {
  // the boarding_split_lodging
  repeated moego.models.appointment.v1.BoardingSplitLodgingModel boarding_split_lodgings = 1;
}

// the boarding_split_lodging service
service BoardingSplitLodgingService {
  // list boarding_split_lodging
  rpc ListBoardingSplitLodgings(ListBoardingSplitLodgingsRequest) returns (ListBoardingSplitLodgingsResponse);
}
