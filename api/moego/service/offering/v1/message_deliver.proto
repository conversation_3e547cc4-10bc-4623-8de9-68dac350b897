syntax = "proto3";

package moego.service.offering.v1;

import "google/protobuf/empty.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1;offeringsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.offering.v1";

// message deliver
service MessageDeliverService {
  // create sessions
  rpc TaskSendAllEvent(google.protobuf.Empty) returns (google.protobuf.Empty);
}
