// @since 2024-09-19 18:00:00
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.service.offering.v1;

import "moego/models/offering/v1/service_enum.proto";
import "moego/models/offering/v1/service_vaccine_requirement_models.proto";
import "moego/models/organization/v1/tenant.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1;offeringsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.offering.v1";

// update service vaccine requirement for service request
message UpdateVaccineRequirementForServiceRequest {
  // tenant
  moego.models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // vaccine id
  repeated int64 vaccine_ids = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// update service vaccine requirement for service item type response
message UpdateVaccineRequirementForServiceResponse {}

// update service vaccine requirement for vaccine request
message UpdateServiceRequirementForVaccineRequest {
  // tenant
  moego.models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];
  // vaccine id
  int64 vaccine_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service item types
  repeated moego.models.offering.v1.ServiceItemType service_item_types = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// update service vaccine requirement for vaccine response
message UpdateServiceRequirementForVaccineResponse {}

// list service_vaccine_requirement request
message ListServiceVaccineRequirementsRequest {
  // tenant
  moego.models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2 [(validate.rules).message.required = true];
  // filter, will return all if not set, otherwise will return the intersection of all non-empty filters
  message Filters {
    // service item type
    repeated moego.models.offering.v1.ServiceItemType service_item_types = 1;
    // vaccine ids
    repeated int64 vaccine_ids = 2;
  }
  // filters
  optional Filters filter = 3;
}

// list service_vaccine_requirement response
message ListServiceVaccineRequirementsResponse {
  // service_vaccine_requirements
  repeated moego.models.offering.v1.ServiceVaccineRequirementModel service_vaccine_requirements = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// the service_vaccine_requirement service
service ServiceVaccineRequirementService {
  // update service vaccine requirement for service item type
  rpc UpdateVaccineRequirementForService(UpdateVaccineRequirementForServiceRequest) returns (UpdateVaccineRequirementForServiceResponse);
  // update service vaccine requirement for vaccine
  rpc UpdateServiceRequirementForVaccine(UpdateServiceRequirementForVaccineRequest) returns (UpdateServiceRequirementForVaccineResponse);
  // list service_vaccine_requirement
  rpc ListServiceVaccineRequirements(ListServiceVaccineRequirementsRequest) returns (ListServiceVaccineRequirementsResponse);
}
