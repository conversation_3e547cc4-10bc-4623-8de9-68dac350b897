// @since 2023-12-07 11:36:34
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.message.v1;

import "moego/models/message/v1/template_defs.proto";
import "moego/models/message/v1/template_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/message/v1;messagesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.message.v1";

// batch create templates request
message BatchCreateTemplatesRequest {
  // templates.
  repeated moego.models.message.v1.BatchCreateTemplateItemDef templates = 1 [(validate.rules).repeated = {max_items: 500}];
}

// batch create templates response
message BatchCreateTemplatesResponse {
  // templates. key is seq num
  map<int64, moego.models.message.v1.TemplateModel> templates = 1;
}

// batch get templates by id request
message MGetTemplatesRequest {
  // templates ids.
  repeated int64 ids = 1 [(validate.rules).repeated = {max_items: 500}];
}

// batch get templates by id response
message MGetTemplatesResponse {
  // templates
  repeated moego.models.message.v1.TemplateModel templates = 1;
}

// the template service
service TemplateService {
  // batch create templates
  rpc BatchCreateTemplates(BatchCreateTemplatesRequest) returns (BatchCreateTemplatesResponse);

  // batch get templates by id
  rpc MGetTemplates(MGetTemplatesRequest) returns (MGetTemplatesResponse);
}
