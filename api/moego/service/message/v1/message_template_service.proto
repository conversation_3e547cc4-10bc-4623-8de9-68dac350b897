// @since 2023-12-07 11:36:34
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.message.v1;

import "moego/models/message/v1/message_template_defs.proto";
import "moego/models/message/v1/message_template_enums.proto";
import "moego/models/message/v1/message_template_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/message/v1;messagesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.message.v1";

// check message template name exist request
message CheckMessageTemplateNameExistRequest {
  // template name
  string template_name = 1 [(validate.rules).string = {max_len: 30}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// check message template name exist response
message CheckMessageTemplateNameExistResponse {
  // exist
  bool exist = 1;
}

// create message_template request
message CreateMessageTemplateRequest {
  // the message_template def
  moego.models.message.v1.MessageTemplateDef message_template_def = 1 [(validate.rules).message = {required: true}];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
  // company id
  optional int64 company_id = 4 [(validate.rules).int64 = {gt: 0}];
  // enterprise id
  optional int64 enterprise_id = 8 [(validate.rules).int64.gt = 0];
}

// create message_template response
message CreateMessageTemplateResponse {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get message template request
message GetMessageTemplateRequest {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get message template response
message GetMessageTemplateResponse {
  // the message_template detail view
  moego.models.message.v1.MessageTemplateDetailView message_template_detail_view = 1;
}

// delete message template request
message UpdateMessageTemplateRequest {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // the message_template def
  moego.models.message.v1.MessageTemplateDef message_template_def = 2 [(validate.rules).message = {required: true}];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 4 [(validate.rules).int64 = {gt: 0}];
  // enterprise id
  optional int64 enterprise_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// update message_template response
message UpdateMessageTemplateResponse {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete message template request
message DeleteMessageTemplateRequest {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// delete message_template response
message DeleteMessageTemplateResponse {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get message template holders request
message GetMessageTemplatesRequest {
  // keyword template name
  optional string keyword = 1 [(validate.rules).string = {max_len: 30}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // if results contains system templates. Use types instead.
  optional bool with_system = 3 [deprecated = true];
  // template types
  // In order to be compatible with older versions, customize templates returned when empty.
  repeated moego.models.message.v1.MessageTemplateType types = 4 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// get message template response
message GetMessageTemplatesResponse {
  // message template simple views
  repeated moego.models.message.v1.MessageTemplateSimpleView message_template_simple_views = 1;
}

// get message template holders request
message GetMessageTemplatePlaceholdersRequest {
  // keyword template placeholder name
  optional string keyword = 1 [(validate.rules).string = {max_len: 30}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // use case enum, default for USE_CASE_SAVED_REPLY
  optional moego.models.message.v1.MessageTemplateUseCase use_case = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// get message template holders response
message GetMessageTemplatePlaceholdersResponse {
  // message template holder simple vies
  repeated moego.models.message.v1.MessageTemplatePlaceholderSimpleView message_template_placeholder_simple_views = 1;
}

// get rendered message request
message GetRenderedMessageRequest {
  // message template id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // customer id
  int64 customer_id = 2 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// get rendered message response
message GetRenderedMessageResponse {
  // message after rendering
  string rendered_message = 1;
}

// sync from auto message as system templates request
message SyncSystemMessageTemplatesRequest {
  // business id
  repeated int64 business_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
  }];
}

// sync system templates response
message SyncSystemMessageTemplatesResponse {}

// init auto message config request
message InitSystemMessageRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// init auto message config response
message InitSystemMessageResponse {}

// the message template service
service MessageTemplateService {
  // check message template name exist
  rpc CheckMessageTemplateNameExist(CheckMessageTemplateNameExistRequest) returns (CheckMessageTemplateNameExistResponse);

  // create message template
  rpc CreateMessageTemplate(CreateMessageTemplateRequest) returns (CreateMessageTemplateResponse);

  // get message template
  rpc GetMessageTemplate(GetMessageTemplateRequest) returns (GetMessageTemplateResponse);

  // update message template
  rpc UpdateMessageTemplate(UpdateMessageTemplateRequest) returns (UpdateMessageTemplateResponse);

  // delete message template
  rpc DeleteMessageTemplate(DeleteMessageTemplateRequest) returns (DeleteMessageTemplateResponse);

  // get message templates
  rpc GetMessageTemplates(GetMessageTemplatesRequest) returns (GetMessageTemplatesResponse);

  // get message template holders
  rpc GetMessageTemplatePlaceholders(GetMessageTemplatePlaceholdersRequest) returns (GetMessageTemplatePlaceholdersResponse);

  // get rendered message
  rpc GetRenderedMessage(GetRenderedMessageRequest) returns (GetRenderedMessageResponse);

  // sync from auto message as system templates. for auto message service initialization
  rpc SyncSystemMessageTemplates(SyncSystemMessageTemplatesRequest) returns (SyncSystemMessageTemplatesResponse);

  // init auto message config. for business initialization
  rpc InitSystemMessage(InitSystemMessageRequest) returns (InitSystemMessageResponse);
}
