syntax = "proto3";

package moego.service.payment.v2;

import "google/type/money.proto";
import "moego/models/payment/v2/payment_enums.proto";
import "moego/models/payment/v2/payment_models.proto";
import "moego/utils/v1/time_period.proto";
import "moego/utils/v2/pagination_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v2;paymentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.payment.v2";

// RefundService 退款服务
service RefundService {
  // Refund 退款
  rpc RefundPayment(RefundPaymentRequest) returns (RefundPaymentResponse);
  // GetRefund 获取退款单据
  rpc GetRefund(GetRefundRequest) returns (GetRefundResponse);
  // ListRefund 获取退款列表
  rpc ListRefunds(ListRefundsRequest) returns (ListRefundsResponse);
}

// refund request
message RefundPaymentRequest {
  // payment id
  int64 payment_id = 1;
  // 退款金额
  google.type.Money amount = 2;
  // 外部调用方类型，如ORDER
  moego.models.payment.v2.ExternalType external_type = 3;
  // 外部调用方关联id，幂等key
  string external_id = 4;
  // 退款原因
  string reason = 5;
}

// refund response
message RefundPaymentResponse {
  // refund model
  models.payment.v2.RefundModel refund = 1;
}

// get refund request
message GetRefundRequest {
  // refund id
  int64 id = 1;
}

// get refund response
message GetRefundResponse {
  // refund model
  models.payment.v2.RefundModel refund = 1;
}

// list refund request
message ListRefundsRequest {
  // filter
  message Filter {
    // 买家
    repeated models.payment.v2.User payers = 1;
    // 卖家
    repeated models.payment.v2.User payees = 2;
    // order id
    repeated int64 order_ids = 3;
    // order payment id
    repeated int64 order_payment_ids = 4;
    // payment
    repeated int64 payment_ids = 5;
    // refund id list
    repeated int64 refund_ids = 6;
    // 查询时间范围
    moego.utils.v1.TimePeriod time_period = 7;
  }

  // filter
  Filter filter = 1;
  // pagination
  utils.v2.PaginationRequest pagination = 2;
}

// list refund response
message ListRefundsResponse {
  // refund models
  repeated models.payment.v2.RefundModel refunds = 1;
  // 分页
  utils.v2.PaginationResponse pagination_request = 2;
}
