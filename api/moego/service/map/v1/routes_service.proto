syntax = "proto3";

package moego.service.map.v1;

import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "google/type/latlng.proto";
import "moego/models/map/v1/route_enums.proto";
import "moego/models/map/v1/route_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/map/v1;mapsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.map.v1";

// QueryRoutesRequest
message QueryRoutesRequest {
  // specifies the field list for response
  // you only need to specify whether the following fields need to be returned,
  // other basic fields will be returned by default or selectively returned based on request.
  //   - *
  //   - routes.*
  //   - routes.viewport
  //   - routes.route_token
  //   - routes.polyline
  //   - routes.travel_advisory
  //   - routes.localized_values
  //   - routes.legs.*
  //   - routes.legs.polyline
  //   - routes.legs.travel_advisory
  //   - routes.legs.localized_values
  //   - routes.legs.steps_overview
  //   - routes.legs.steps.*
  //   - routes.legs.steps.travel_mode
  //   - routes.legs.steps.distance_meters
  //   - routes.legs.steps.static_duration
  //   - routes.legs.steps.start_location
  //   - routes.legs.steps.end_location
  //   - routes.legs.steps.polyline
  //   - routes.legs.steps.travel_advisory
  //   - routes.legs.steps.localized_values
  //   - routes.legs.steps.transit_details
  //   - routes.legs.steps.navigation_instruction
  repeated string field_mask = 1;
  // the country code, which can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code or country name.
  optional string country = 2;
  // Optional. The BCP-47 language code, such as "en-US" or "sr-Latn".
  // For more information, see: http://www.unicode.org/reports/tr35/#Unicode_locale_identifier
  // Language Support, see: https://developers.google.com/maps/faq#languagesupport
  // for the list of supported languages. When you don't provide this value,
  // the display language is inferred from the location of the route request.
  optional string language_code = 3;
  // specify the waypoint that the route needs to pass through, including at least two points(starting point and ending point).
  repeated moego.models.map.v1.Waypoint waypoints = 4;
  // the travel mode, default is DRIVING
  optional moego.models.map.v1.TravelMode travel_mode = 5;
  // specifies the assumptions to use when calculating time in traffic.
  // This setting affects the value returned in the duration field in the `Route` and `RouteLeg`
  // which contains the predicted time in traffic based on historical averages.
  // `TrafficModel` is only available for requests that have set `RoutePreference` to `ROUTE_PREFERENCE_OPTIMAL`
  // and `TravelMode` to `TRAVEL_MODE_DRIVING`.
  // Defaults to `TRAFFIC_MODEL_BEST_GUESS` if traffic is requested and `TrafficModel` is not specified.
  optional moego.models.map.v1.TrafficModel traffic_model = 6;
  // specify factors to take into consideration when calculating the route.
  // when specified, the travel_mode must be DRIVING or TWO_WHEELER, otherwise the request will be failed.
  optional moego.models.map.v1.RoutePreference route_preference = 7;
  // specify transit preferences, when specified, the travel_mode must be TRANSIT, otherwise the request will be failed.
  optional moego.models.map.v1.TransitPreferences transit_preferences = 8;
  // the departure time, default is now.
  optional google.protobuf.Timestamp departure_time = 9;
  // the arrival time, default is now.
  // Can only be set when `TravelMode` is set to `TRAVEL_MODE_TRANSIT`.
  // You can specify either departure_time or arrival_time, but not both.
  optional google.protobuf.Timestamp arrival_time = 10;
  // specifies the units of measure for the display fields.
  // These fields include the `instruction` field in `NavigationInstruction`
  // The units of measure used for the route, leg, step distance, and duration are not affected by this value.
  // If you don't provide this value, then the display units are inferred from the location of the first origin.
  optional moego.models.map.v1.Units units = 11;
  // specifies whether to calculate alternate routes in addition to the route.
  // No alternative routes are returned for requests that have intermediate waypoints.
  optional bool alternative_route = 12;
  // specifies whether to calculate Fuel efficient route.
  // Routes labeled with this value are determined to be optimized for parameters such as fuel consumption.
  optional bool fuel_efficient = 13;
  // if true, the service attempts to minimize the overall cost of the route by re-ordering the specified intermediate waypoints.
  optional bool optimize_waypoint_order = 14;
  // Optional. Specifies your preference for the quality of the polyline.
  optional moego.models.map.v1.PolylineQuality polyline_quality = 15;
  // specifies the preferred type of polyline to be returned.
  optional moego.models.map.v1.PolylineEncoding polyline_encoding = 16;
  // specify a list of extra computations which may be used to complete the request.
  repeated moego.models.map.v1.ExtraComputation extra_computations = 17;
  // specify a set of conditions to satisfy that affect the way routes are calculated.
  optional moego.models.map.v1.RouteModifiers route_modifiers = 18;
}

// QueryRouteByPointsRequest
message QueryRouteByPointRequest {
  // the coordinate of origin
  google.type.LatLng origin = 1;
  // the coordinate of destination
  google.type.LatLng destination = 2;
}

// QueryRoutesResponse
message QueryRoutesResponse {
  // if the request failed, the error message will be returned.
  google.rpc.Status status = 1;

  // Contains an array of computed routes (up to three) when you specify compute_alternatives_routes,
  // and contains just one route when you don't.
  // When this array contains multiple entries, the first one is the most recommended route.
  // If the array is empty, then it means no route could be found.
  repeated moego.models.map.v1.Route routes = 2;

  // In some cases when the server is not able to compute the route results with
  // all of the input preferences, it may fallback to using a different way of
  // computation. When fallback mode is used, this field contains detailed info
  // about the fallback response. Otherwise this field is unset.
  optional moego.models.map.v1.FallbackInfo fallback_info = 3;
}

// RouteMatrixOrigin
message RouteMatrixOrigin {
  // the coordinates of origin
  moego.models.map.v1.Waypoint waypoint = 1;
  // specify a set of conditions to satisfy that affect the way routes are calculated.
  optional moego.models.map.v1.RouteModifiers route_modifiers = 2;
}

// QueryRouteMatrixRequest
message QueryRouteMatrixRequest {
  // specifies the field list for response
  // you only need to specify whether the following fields need to be returned,
  // other basic fields will be returned by default or selectively returned based on request.
  //   - travel_advisory
  //   - localized_values
  repeated string field_mask = 1;
  // the country code, which can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code or country name.
  optional string country = 2;
  // Optional. The BCP-47 language code, such as "en-US" or "sr-Latn".
  // For more information, see: http://www.unicode.org/reports/tr35/#Unicode_locale_identifier
  // Language Support, see: https://developers.google.com/maps/faq#languagesupport
  // for the list of supported languages. When you don't provide this value,
  // the display language is inferred from the location of the first origin.
  optional string language_code = 3;
  // Array of origins, which determines the rows of the response matrix.
  repeated RouteMatrixOrigin origins = 4;
  // Array of destinations, which determines the columns of the response matrix.
  repeated moego.models.map.v1.Waypoint destinations = 5;
  // the travel mode, default is driving
  optional moego.models.map.v1.TravelMode travel_mode = 6;
  // Specifies the assumptions to use when calculating time in traffic.
  // This setting affects the value returned in the duration field in the `RouteMatrixItem`
  // which contains the predicted time in traffic based on historical averages.
  // `TrafficModel` is only available for requests that have set `RoutePreference` to `ROUTE_PREFERENCE_OPTIMAL`
  // and `TravelMode` to `TRAVEL_MODE_DRIVING`.
  // Defaults to `TRAFFIC_MODEL_BEST_GUESS` if traffic is requested and `TrafficModel` is not specified.
  optional moego.models.map.v1.TrafficModel traffic_model = 7;
  // specify factors to take into consideration when calculating the route.
  // when specified, the travel_mode must be DRIVING or TWO_WHEELER, otherwise the request will be failed.
  optional moego.models.map.v1.RoutePreference route_preference = 8;
  // specify transit preferences, when specified, the travel_mode must be TRANSIT, otherwise the request will be failed.
  optional moego.models.map.v1.TransitPreferences transit_preferences = 9;
  // the departure time, default is now
  optional google.protobuf.Timestamp departure_time = 10;
  // the arrival time, default is now.
  // Can only be set when `TravelMode` is set to `TRAVEL_MODE_TRANSIT`.
  // You can specify either departure_time or arrival_time, but not both.
  optional google.protobuf.Timestamp arrival_time = 11;
  // specify a list of extra computations which may be used to complete the request.
  repeated moego.models.map.v1.ExtraComputation extra_computations = 12;
}

// QueryRouteMatrixByPointsRequest
message QueryRouteMatrixByPointsRequest {
  // the coordinates of origin
  repeated google.type.LatLng origins = 1;
  // the coordinates of destination
  repeated google.type.LatLng destinations = 2;
}

// QueryRouteMatrixResponse
message QueryRouteMatrixResponse {
  // DistanceMatrixItems list
  repeated moego.models.map.v1.RouteMatrixElement rows = 1;
}

// RoutesService
service RoutesService {
  // query routes
  rpc QueryRoutes(QueryRoutesRequest) returns (QueryRoutesResponse);

  // query routes by point
  // This interface uses the following parameters by default:
  //   - TravelMode: DRIVING
  //   - PolylineEncoding: ENCODED_POLYLINE
  // Note: Call this API will give priority to getting the value from the cache.
  // If the value is called from third party(e.g. google map), it will also be synchronized to the cache.
  rpc QueryRouteByPoint(QueryRouteByPointRequest) returns (QueryRoutesResponse);

  // query route matrix
  rpc QueryRouteMatrix(QueryRouteMatrixRequest) returns (QueryRouteMatrixResponse);

  // query route matrix by points
  // This interface uses the following parameters by default:
  //   - TravelMode: DRIVING
  // Note: Call this API will give priority to getting the value from the cache.
  // If the value is called from third party(e.g. google map), it will also be synchronized to the cache.
  rpc QueryRouteMatrixByPoints(QueryRouteMatrixByPointsRequest) returns (QueryRouteMatrixResponse);
}
