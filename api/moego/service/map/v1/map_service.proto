syntax = "proto3";

package moego.service.map.v1;

import "google/type/latlng.proto";
import "moego/models/map/v1/google_map_models.proto";
import "moego/models/map/v1/map_enums.proto";
import "moego/models/map/v1/map_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/map/v1;mapsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.map.v1";

// GetAddressRequest
message GetAddressRequest {
  // condition_type
  oneof condition_type {
    // the address source
    moego.models.map.v1.AddressSource address_source = 1;
    // specify the coordinate of address
    google.type.LatLng coordinate = 2;
    // combined conditions for querying address
    // Note: that the API itself does not check the accuracy of the conditions.
    // It will only return an address that meets all conditions,
    // which means that the accuracy of the address is guaranteed by the caller.
    CombinedConditions condition = 3;
  }

  // language code
  optional string language_code = 6;

  // CombinedConditions
  message CombinedConditions {
    // the country of the address, which can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code or country name.
    // Note: country is not case sensitive, but an exact match is required.
    string country = 1;
    // the part of the address
    optional string address = 2;
    // the zip/postal code of the address
    optional string postal_code = 3;
    // the label of the address
    optional string label = 4;
    // the bounds of the address
    optional moego.models.map.v1.Bounds bounds = 5;
  }
}

// GetAddressResponse
message GetAddressResponse {
  // the address
  optional moego.models.map.v1.AddressModel address = 1;
}

// SearchAddressRequest
message SearchAddressRequest {
  // the country of the address, which can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code or country name.
  // Note: country is not case sensitive, but an exact match is required.
  optional string country = 1;
  // the part of the address
  optional string address = 2;
  // the zip/postal code of the address
  optional string postal_code = 3;
  // the label of the address
  optional string label = 4;
  // specify the coordinate of address
  optional google.type.LatLng coordinate = 5;
  // the bounds of the address
  optional moego.models.map.v1.Bounds bounds = 6;

  // language code
  optional string language_code = 9;
  // specify the maximum number of addresses returned, the default is 100
  optional int32 limit = 10;
}

// SearchAddressResponse
message SearchAddressResponse {
  // address list
  repeated moego.models.map.v1.AddressModel addresses = 1;
}

// AutocompleteAddressRequest
message AutocompleteAddressRequest {
  // The text string on which to search.
  string term = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // The origin point from which to calculate geodesic distance to the destination (returned as distance).
  // If this value is omitted, geodesic distance will not be returned.
  optional google.type.LatLng origin = 2;
  // A list of countries to limit the search. Each country element can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code or country name.
  // If no element is specified, there is no restriction on countries, which is equivalent to searching worldwide.
  // Note: country element is not case sensitive, but an exact match is required.
  repeated string countries = 3 [(validate.rules).repeated = {
    max_items: 16
    unique: true
    items: {
      string: {
        min_len: 2
        max_len: 128
      }
    }
  }];
  // the state of the address.
  // you must specify `countries` and must contain exactly one element when specifying this field.
  optional string state = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 128
  }];
  // the county of the address.
  // you must specify `countries` and must contain exactly one element when specifying this field.
  optional string county = 5 [(validate.rules).string = {
    min_len: 1
    max_len: 128
  }];
  // the district of the address.
  // you must specify `countries` and must contain exactly one element when specifying this field.
  optional string district = 6 [(validate.rules).string = {
    min_len: 1
    max_len: 128
  }];
  // the city of the address.
  // you must specify `countries` and must contain exactly one element when specifying this field.
  optional string city = 7 [(validate.rules).string = {
    min_len: 1
    max_len: 128
  }];
  // the zip/postal code of the address.
  // you must specify `countries` and must contain exactly one element when specifying this field.
  optional string postal_code = 8 [(validate.rules).string = {
    min_len: 1
    max_len: 32
  }];
  // Included primary Place type (for example, "restaurant" or "gas_station"), or only (regions), or only (cities).
  // Supported types please see: https://developers.google.com/maps/documentation/places/web-service/place-types
  // A Place is only returned if its primary type is included in this list.
  // Up to 5 values can be specified.
  // If no types are specified, all Place types are returned.
  repeated string address_types = 9 [(validate.rules).repeated = {
    max_items: 5
    unique: true
    items: {
      string: {
        min_len: 2
        max_len: 128
      }
    }
  }];
  // Bias results to a specified location.
  // At most one of `location_bias` or `location_restriction` should be set.
  optional moego.models.map.v1.LocationBias location_bias = 10;
  // Restrict results to a specified location.
  // At most one of `location_bias` or `location_restriction` should be set.
  optional moego.models.map.v1.LocationRestriction location_restriction = 11;

  // reserved ...

  // language code
  optional string language_code = 14 [(validate.rules).string = {
    min_len: 2
    max_len: 8
  }];
  // Specify the maximum number of results to return, default 5
  optional int32 max_results = 15 [(validate.rules).int32 = {
    gte: 1
    lte: 10
  }];
}

// AutocompleteAddressResponse
message AutocompleteAddressResponse {
  // Contains a list of suggestions, ordered in descending order of relevance.
  repeated moego.models.map.v1.SuggestedAddress suggestions = 1;
}

// GetRegionRequest
message GetRegionRequest {
  // condition_type
  oneof condition_type {
    // the id of the region
    int64 id = 1;
    // combined conditions for querying region
    // Note: that the API itself does not check the accuracy of the conditions.
    // It will only return an region that meets all conditions,
    // which means that the accuracy of the regions is guaranteed by the caller.
    CombinedConditions condition = 2;
  }

  // specify whether polygons of region needs to be returned, not returned by default.
  optional bool polygons = 3;
  // language code
  optional string language_code = 6;

  // CombinedConditions
  message CombinedConditions {
    // the country of the region, which can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code or country name.
    // Note: country is not case sensitive, but an exact match is required.
    string country = 1;
    // the short name of the region
    optional string short_name = 2;
    // the long name of the region
    optional string long_name = 3;
    // the level of the region
    optional moego.models.map.v1.RegionLevel level = 4;
    // the zip/postal code of the region
    optional string postal_code = 5;
    // the parent code of the region
    optional string parent_code = 6;
    // the parent name of the region
    optional string parent_name = 7;
    // the parent level of the region
    optional moego.models.map.v1.RegionLevel parent_level = 8;
    // the label of the region
    optional string label = 9;
    // the coordinate of the region
    optional google.type.LatLng coordinate = 10;
  }
}

// GetRegionResponse
message GetRegionResponse {
  // the region
  optional moego.models.map.v1.RegionModel region = 1;
}

// GetRegionsRequest
// Note: for the batch get regions, the field `polygons` is not populated.
// To obtain `polygons`, please call `GetRegion` and specify `polygons` as `true`.
message GetRegionsRequest {
  // id list
  repeated int64 ids = 1;
  // postal code list
  repeated string postal_codes = 2;
  // the parent id of the region
  optional int64 parent_id = 3;
  // the country of the region, which can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code or country name.
  // Note: country is not case sensitive, but an exact match is required.
  // If the ids field is not specified, the country field must be specified.
  optional string country = 4;
  // the level of the region
  optional moego.models.map.v1.RegionLevel level = 5;
  // the status of the region
  optional moego.models.map.v1.RegionStatus status = 6;
  // the parent code of the region
  optional string parent_code = 7;
  // the parent name of the region
  optional string parent_name = 8;
  // the parent level of the region
  optional moego.models.map.v1.RegionLevel parent_level = 9;
  // the label of the region
  optional string label = 10;
  // the coordinate of the region
  optional google.type.LatLng coordinate = 11;
  // the bound of the region, if bounds is specified, level must also be specified, otherwise bounds is ignored.
  optional moego.models.map.v1.Bounds bounds = 12;
  // language code
  optional string language_code = 14;
}

// SearchRegionsRequest
// Note: for the batch get regions, the field `polygons` is not populated.
// To obtain `polygons`, please call `GetRegion` and specify `polygons` as `true`.
message SearchRegionsRequest {
  // the country of the region, which can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code or country name.
  // Note: country is not case sensitive, but an exact match is required.
  string country = 1;
  // the name of the region, short_name or long_name, fuzzy matching
  optional string name = 2;
  // the postal/zip code of the region, fuzzy matching
  optional string postal_code = 3;
  // language code
  optional string language_code = 8;
  // specify the maximum number of regions returned, the default is 100
  optional int32 limit = 10;
}

// GetRegionsResponse
message GetRegionsResponse {
  // region list
  repeated moego.models.map.v1.RegionModel regions = 1;
}

// GetRelationRequest
message GetRelationRequest {
  // params for request address
  GetAddressRequest address = 1;
  // params for request region
  GetRegionRequest region = 2;
}

// GetRelationResponse
message GetRelationResponse {
  // the relation between address and region
  optional moego.models.map.v1.AreaRelation relation = 1;
}

// GetCountriesRequest
message GetCountriesRequest {
  // the name of the country, which can be a short name, official name or local name.
  optional string name = 1;
  // the code of the country, which can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code.
  optional string code = 2;
  // whether the country is independent
  optional bool independent = 3;
}

// GetCountriesResponse
message GetCountriesResponse {
  // country list
  repeated moego.models.map.v1.CountryModel countries = 1;
}

// GetPostalCodeRequest
message GetPostalCodeRequest {
  // the country of the region, which can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code or country name.
  // Note: country is not case sensitive, but an exact match is required.
  string country = 1 [(validate.rules).string = {
    min_len: 2
    max_len: 128
  }];
  // the postal code
  string postal_code = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 32
  }];
}

// GetPostalCodeResponse
message GetPostalCodeResponse {
  // the region represented by the postal code
  optional moego.models.map.v1.PostalCodeRegion region = 1;
}

// SearchPostalCodesRequest
message SearchPostalCodesRequest {
  // the country of the region, which can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code or country name.
  // Note: country is not case sensitive, but an exact match is required.
  string country = 1 [(validate.rules).string = {
    min_len: 2
    max_len: 128
  }];
  // The postal code of the region, prefix matching
  string postal_code = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 32
  }];
  // specify the maximum number of regions returned, the default is 20
  optional int32 limit = 8 [(validate.rules).int32 = {gte: 1}];
}

// SearchPostalCodesResponse
message SearchPostalCodesResponse {
  // the regions represented by the postal code
  repeated moego.models.map.v1.PostalCodeRegion regions = 1;
}

// GuessCountriesFromPostalCodeRequest
message GuessCountriesFromPostalCodeRequest {
  // the postal code
  string postal_code = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 32
  }];
}

// GuessCountriesFromPostalCodeResponse
message GuessCountriesFromPostalCodeResponse {
  // the ISO 3166-1 alpha-2 codes for country
  repeated string country_codes = 1;
}

// GetGooglePlaceRequest
message GetGooglePlaceRequest {
  // place id
  string place_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 1024
  }];
}

// GetGooglePlaceResponse
message GetGooglePlaceResponse {
  // google place
  optional moego.models.map.v1.GooglePlace place = 1;
}

// ListGooglePlaceRequest
message ListGooglePlaceRequest {
  // place id list
  repeated string place_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 1024
      }
    }
  }];

  // reserved ...
}

// ListGooglePlaceResponse
message ListGooglePlaceResponse {
  // google place list
  repeated moego.models.map.v1.GooglePlace places = 1;
}

// MapService
service MapService {
  // get countries info, NOTE: if no parameters are specified, all countries will be returned.
  rpc GetCountries(GetCountriesRequest) returns (GetCountriesResponse);

  // get an address
  rpc GetAddress(GetAddressRequest) returns (GetAddressResponse);

  // search addresses
  rpc SearchAddresses(SearchAddressRequest) returns (SearchAddressResponse);

  // predict addresses by term
  rpc AutocompleteAddress(AutocompleteAddressRequest) returns (AutocompleteAddressResponse);

  // get a region
  rpc GetRegion(GetRegionRequest) returns (GetRegionResponse);

  // get region list
  rpc GetRegions(GetRegionsRequest) returns (GetRegionsResponse);

  // get a region and all parent regions
  rpc GetRegionWithParents(GetRegionRequest) returns (GetRegionsResponse);

  // search regions by name
  rpc SearchRegions(SearchRegionsRequest) returns (GetRegionsResponse);

  // get relation between address and region
  rpc GetRelation(GetRelationRequest) returns (GetRelationResponse);

  // query region represented by postal code
  rpc GetPostalCode(GetPostalCodeRequest) returns (GetPostalCodeResponse);

  // search region represented by postal code
  rpc SearchPostalCodes(SearchPostalCodesRequest) returns (SearchPostalCodesResponse);

  // guess which countries the given postal code might be from
  rpc GuessCountriesFromPostalCode(GuessCountriesFromPostalCodeRequest) returns (GuessCountriesFromPostalCodeResponse);

  // get google place by placeId
  rpc GetGooglePlace(GetGooglePlaceRequest) returns (GetGooglePlaceResponse);

  // get google place list
  rpc ListGooglePlace(ListGooglePlaceRequest) returns (ListGooglePlaceResponse);
}
