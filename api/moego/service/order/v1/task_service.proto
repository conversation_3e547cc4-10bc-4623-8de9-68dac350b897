syntax = "proto3";

package moego.service.order.v1;

import "google/protobuf/empty.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1;ordersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.order.v1";

// task service
service TaskService {
  // retry message delivery
  rpc RetryMessageDelivery(google.protobuf.Empty) returns (google.protobuf.Empty);
}
