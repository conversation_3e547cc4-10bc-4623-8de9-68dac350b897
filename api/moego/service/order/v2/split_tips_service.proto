syntax = "proto3";

package moego.service.order.v2;

import "google/type/money.proto";
import "moego/models/order/v1/order_enums.proto";
import "moego/models/order/v1/order_models.proto";
import "moego/models/order/v1/refund_order_models.proto";
import "moego/models/order/v1/split_tips_defs.proto";
import "moego/models/order/v1/split_tips_enum.proto";
import "moego/models/order/v1/split_tips_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v2;ordersvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.order.v2";

// GetTipsSplitRequest
message GetTipsSplitRequest {
  // source id
  int64 source_id = 1 [(validate.rules).int64 = {gt: 0}];
  // source type
  models.order.v1.OrderSourceType source_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// GetTipsSplitResponse
message GetTipsSplitResponse {
  // total collected tips from completed orders
  models.order.v1.TipsSplitModel tips_split = 1;
  // tips split records for each staff
  repeated models.order.v1.TipsSplitDetailModel tips_split_details = 2;
  // completed order list
  repeated models.order.v1.OrderModelV1 orders = 3;
  // Refunded orders.
  repeated models.order.v1.RefundOrderModel refund_orders = 4;
  // is appt level split record exist
  models.order.v1.TipsSplitMode tips_split_mode = 5;
}

// EditStaffAndTipsSplitRequest
message EditStaffAndTipsSplitRequest {
  // source id
  int64 source_id = 1 [(validate.rules).int64 = {gt: 0}];
  // source type
  models.order.v1.OrderSourceType source_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // edit staff
  repeated models.order.v1.EditStaffDef edit_staffs = 3;

  // tips split config
  optional models.order.v1.TipsSplitModel.TipsSplitConfig tips_split_config = 4;

  // company id
  int64 company_id = 5 [(validate.rules).int64 = {gt: 0}];

  // business id
  int64 business_id = 6 [(validate.rules).int64 = {gt: 0}];

  // apply by
  int64 apply_by = 7 [(validate.rules).int64 = {gt: 0}];
}

// EditTipsSplitResponse
message EditTipsSplitResponse {
  // tips split records for each staff
  repeated models.order.v1.TipsSplitDetailModel tips_split_details = 1;
  // tips split for business.
  google.type.Money business_tip_amount = 2;
}

// PreviewEditTipsSplitResponse.
// 与 EditTipsSplit 共享同一个 Request 定义.
message PreviewEditTipsSplitResponse {
  // tips split records for each staff
  models.order.v1.TipsSplitModel.TipsSplitConfig split_config = 1;
}

// GetTipsSplitChangedStatusRequest
message GetTipsSplitChangedStatusRequest {
  // source id
  int64 source_id = 1 [(validate.rules).int64 = {gt: 0}];
  // source type
  models.order.v1.OrderSourceType source_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// GetTipsSplitChangedStatusResponse
message GetTipsSplitChangedStatusResponse {
  // indicate tips split changed status
  bool is_changed = 1;
}

// ClearTipsRedDotRequest
message ClearTipsSplitChangedStatusRequest {
  // source id
  int64 source_id = 1 [(validate.rules).int64 = {gt: 0}];
  // source type
  models.order.v1.OrderSourceType source_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// ClearTipsRedDotResponse
message ClearTipsSplitChangedStatusResponse {}

// ListTipsSplitDetailsBySourceRequest
message ListTipsSplitDetailsBySourceRequest {
  // sources.
  repeated Source sources = 1 [(validate.rules).repeated = {
    max_items: 100
    min_items: 1
  }];

  // Source.
  message Source {
    // source id.
    int64 source_id = 1 [(validate.rules).int64 = {gt: 0}];
    // source type.
    models.order.v1.OrderSourceType source_type = 2 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
  }
}

// ListTipsSplitDetailsBySourceResponse.
message ListTipsSplitDetailsBySourceResponse {
  // split details.
  // Request 中匹配不到的 Source 不会出现在列表里.
  repeated TipsSplitDetail tips_split_details = 1;

  // split detail.
  message TipsSplitDetail {
    // 分配配置.
    models.order.v1.TipsSplitModel tips_split = 1;
    // 分配详情.
    repeated models.order.v1.TipsSplitDetailModel tips_split_details = 2;
  }
}

// split tips internal api
service SplitTipsService {
  // get tips details for source type
  rpc GetTipsSplitDetails(GetTipsSplitRequest) returns (GetTipsSplitResponse);
  // preview edit staff and split tips
  rpc PreviewEditTipsSplit(EditStaffAndTipsSplitRequest) returns (PreviewEditTipsSplitResponse);
  // update edit staff and split tips
  rpc EditTipsSplit(EditStaffAndTipsSplitRequest) returns (EditTipsSplitResponse);
  // get tips split changed status
  rpc GetTipsSplitChangedStatus(GetTipsSplitChangedStatusRequest) returns (GetTipsSplitChangedStatusResponse);
  // clear tips split changed status
  rpc ClearTipsSplitChangedStatus(ClearTipsSplitChangedStatusRequest) returns (ClearTipsSplitChangedStatusResponse);

  // ListTipsSplitDetailsBySource 通过 SourceID+SourceType 批量查询 Tips 分配详情.
  // 主要提供给 老 Report 进行实时查询.
  rpc ListTipsSplitDetailsBySource(ListTipsSplitDetailsBySourceRequest) returns (ListTipsSplitDetailsBySourceResponse);
}
