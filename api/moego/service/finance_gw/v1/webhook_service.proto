syntax = "proto3";

package moego.service.finance_gw.v1;

import "moego/models/finance_gw/v1/webhook_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/finance_gw/v1;financegwsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.finance_gw.v1";

// The common gateway service for all webhooks.
service WebhookService {
  // Handle webhook event. The raw event body is received.
  rpc HandleRawEvent(HandleRawEventRequest) returns (HandleRawEventResponse);
}

// Request for HandleRawEvent. Ideally, most HTTP data should be passed in this request.
message HandleRawEventRequest {
  // Webhook event from HTTP outbound.
  moego.models.finance_gw.v1.HttpWebhookEvent http_webhook_event = 1 [(validate.rules).message.required = true];
}

// Response for HandleRawEvent
message HandleRawEventResponse {}
