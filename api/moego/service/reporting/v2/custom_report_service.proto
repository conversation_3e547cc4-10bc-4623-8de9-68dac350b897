syntax = "proto3";

package moego.service.reporting.v2;

import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/report_meta_def.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/reporting/v2;reportingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.reporting.v2";

// Custom report internal API
service CustomReportService {
  // SaveCustomReport
  rpc SaveCustomReport(SaveCustomReportRequest) returns (SaveCustomReportResponse);
  // ModifyCustomDiagram
  rpc ModifyCustomDiagram(ModifyCustomDiagramRequest) returns (ModifyCustomDiagramResponse);
  // DuplicateCustomReport
  rpc DuplicateCustomReport(DuplicateCustomReportRequest) returns (DuplicateCustomReportResponse);
  // DeleteCustomReport
  rpc DeleteCustomReport(DeleteCustomReportRequest) returns (DeleteCustomReportResponse);
}

// SaveCustomReportRequest
message SaveCustomReportRequest {
  // diagram id, exists when update
  optional string diagram_id = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // name of custom report, should be trimmed
  optional string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
    pattern: "^[^\\s].*[^\\s]$"
  }];
  // description of custom report
  optional string description = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // metric field keys
  repeated string metric_keys = 4 [(validate.rules).repeated = {
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 255
      }
    }
  }];
  // dimension fields
  repeated moego.models.reporting.v2.DimensionField dimensions = 5;
  // saved filters
  repeated moego.models.reporting.v2.FilterRequest saved_filters = 6;
  // dynamic column mode, use final dimension to generate columns
  optional bool dynamic_column_mode = 7;
  // reporting scene
  models.reporting.v2.ReportingScene scene = 8;
}

// SaveCustomReportResponse
message SaveCustomReportResponse {
  // return created or updated diagram_id
  string diagram_id = 1;
}

// ModifyCustomDiagramRequest
message ModifyCustomDiagramRequest {
  // reporting scene
  models.reporting.v2.ReportingScene scene = 1;
  // diagram id
  string diagram_id = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // new name
  optional string name = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 255
    pattern: "^[^\\s].*[^\\s]$"
  }];
  // new description
  optional string description = 4 [(validate.rules).string = {
    min_len: 0
    max_len: 255
  }];
  // dynamic column mode, use final dimension to generate columns
  optional bool dynamic_column_mode = 5;
}

// ModifyCustomDiagramResponse
message ModifyCustomDiagramResponse {
  // modify success or not
  bool success = 1;
}

// DuplicateCustomReportRequest
message DuplicateCustomReportRequest {
  // diagram ids
  repeated string diagram_ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 255
      }
    }
  }];
  // reporting scene
  models.reporting.v2.ReportingScene scene = 2;
}

// DuplicateCustomReportResponse
message DuplicateCustomReportResponse {
  // duplicate success or not
  bool success = 1;
}

// DeleteCustomReportRequest
message DeleteCustomReportRequest {
  // diagram ids
  repeated string diagram_ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      string: {
        min_len: 1
        max_len: 255
      }
    }
  }];
  // reporting scene
  models.reporting.v2.ReportingScene scene = 2;
}

// DeleteCustomReportResponse
message DeleteCustomReportResponse {
  // delete success or not
  bool success = 1;
}
