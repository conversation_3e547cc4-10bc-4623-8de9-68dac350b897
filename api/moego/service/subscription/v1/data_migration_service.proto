syntax = "proto3";

package moego.service.subscription.v1;

import "moego/models/subscription/v1/data_migration_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/subscription/v1;subscriptionpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.subscription.v1";

// data migration service
service DataMigrationService {
  // import products
  rpc ImportProducts(ImportProductsRequest) returns (ImportProductsResponse);
  // import prices
  rpc ImportPrices(ImportPricesRequest) returns (ImportPricesResponse);
  // import features
  rpc ImportFeatures(ImportFeaturesRequest) returns (ImportFeaturesResponse);
  // import subscriptions
  rpc ImportSubscriptions(ImportSubscriptionsRequest) returns (ImportSubscriptionsResponse);
  // import entitlements
  rpc ImportEntitlements(ImportEntitlementsRequest) returns (ImportEntitlementsResponse);
}

// import products request
message ImportProductsRequest {
  // product data
  repeated models.subscription.v1.ProductData data = 1;
}

// import products response
message ImportProductsResponse {
  // successfully imported product data
  repeated models.subscription.v1.ProductData imported = 1;
  // failed to import product data
  repeated models.subscription.v1.ProductData failed = 2;
}

// import prices request
message ImportPricesRequest {
  // price data
  repeated models.subscription.v1.PriceData data = 1;
}

// import prices response
message ImportPricesResponse {
  // successfully imported price data
  repeated models.subscription.v1.PriceData imported = 1;
  // failed to import price data
  repeated models.subscription.v1.PriceData failed = 2;
}

// import features request
message ImportFeaturesRequest {
  // feature data
  repeated models.subscription.v1.FeatureData data = 1;
}

// import features response
message ImportFeaturesResponse {
  // successfully imported feature data
  repeated models.subscription.v1.FeatureData imported = 1;
  // failed to import feature data
  repeated models.subscription.v1.FeatureData failed = 2;
}

// import subscriptions request
message ImportSubscriptionsRequest {
  // subscription data
  repeated models.subscription.v1.SubscriptionData data = 1;
}

// import subscriptions response
message ImportSubscriptionsResponse {
  // successfully imported subscription data
  repeated models.subscription.v1.SubscriptionData imported = 1;
  // failed to import subscription data
  repeated models.subscription.v1.SubscriptionData failed = 2;
}

// import entitlements request
message ImportEntitlementsRequest {
  // entitlement data
  repeated models.subscription.v1.EntitlementData data = 1;
}

// import entitlements response
message ImportEntitlementsResponse {
  // successfully imported entitlement data
  repeated models.subscription.v1.EntitlementData imported = 1;
  // failed to import entitlement data
  repeated models.subscription.v1.EntitlementData failed = 2;
}
