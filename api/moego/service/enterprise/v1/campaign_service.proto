syntax = "proto3";

package moego.service.enterprise.v1;

import "moego/models/enterprise/v1/campaign_models.proto";
import "moego/models/enterprise/v1/tenant_models.proto";
import "moego/models/message/v1/message_template_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1;enterprisesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.enterprise.v1";

// Blast campaign service in Enterprise Hub
service CampaignService {
  // Send a test email
  rpc SendTestEmail(SendTestEmailRequest) returns (SendTestEmailResponse);
  // Create a campaign template
  rpc CreateTemplate(CreateTemplateRequest) returns (CreateTemplateResponse);
  // Get a campaign template
  rpc GetTemplate(GetTemplateRequest) returns (GetTemplateResponse);
  // Update a campaign template
  rpc UpdateTemplate(UpdateTemplateRequest) returns (UpdateTemplateResponse);
  // List campaign templates
  rpc ListTemplates(ListTemplatesRequest) returns (ListTemplatesResponse);
  // Push campaign templates to tenants
  rpc PushTemplates(PushTemplatesRequest) returns (PushTemplatesResponse);
  // List template apply record
  rpc ListTemplatePushRecords(ListTemplatePushRecordsRequest) returns (ListTemplatePushRecordsResponse);
  // List template variables
  rpc ListTemplatePlaceholders(ListTemplatePlaceholdersRequest) returns (ListTemplatePlaceholdersResponse);
}

// SendTestEmailRequest
message SendTestEmailRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // email subject
  string subject = 2 [(validate.rules).string.max_len = 200];
  // email content
  string content = 3 [(validate.rules).string.max_len = 100000];
  // recipient email
  string recipient_email = 4 [(validate.rules).string.email = true];
  // staff id
  int64 staff_id = 5 [(validate.rules).int64.gt = 0];
}

// SendTestEmailResponse
message SendTestEmailResponse {}

// CreateTemplateRequest
message CreateTemplateRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];
  // campaign template
  string name = 3 [(validate.rules).string = {min_len: 1}];
  // description
  string description = 4;
  // type
  moego.models.enterprise.v1.Campaign.Type type = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // cover
  string cover = 6;
  // subject
  string subject = 7;
  // content
  string content = 8;
}

// CreateTemplateResponse
message CreateTemplateResponse {
  // campaign template
  moego.models.enterprise.v1.CampaignTemplate template = 1;
}

// GetTemplateRequest
message GetTemplateRequest {
  // campaign template id
  int64 id = 1;
}

// GetTemplateResponse
message GetTemplateResponse {
  // campaign template
  moego.models.enterprise.v1.CampaignTemplate template = 1;
}

// UpdateTemplateRequest
message UpdateTemplateRequest {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // name
  optional string name = 2;
  // description
  optional string description = 3;
  // status
  moego.models.enterprise.v1.CampaignTemplate.Status status = 4;
  // cover
  optional string cover = 5;
  // subject
  optional string subject = 6;
  // content
  optional string content = 7;
}

// UpdateTemplateResponse
message UpdateTemplateResponse {
  // campaign template
  moego.models.enterprise.v1.CampaignTemplate template = 1;
}

// ListTemplatesRequest
message ListTemplatesRequest {
  // filter
  message Filter {
    // enterprise ids
    repeated int64 enterprise_ids = 1;
    // template name
    string name = 2;
    // template types
    repeated moego.models.enterprise.v1.Campaign.Type types = 3;
    // template statuses
    repeated moego.models.enterprise.v1.CampaignTemplate.Status statuses = 4;
  }
  // 分页信息
  utils.v2.PaginationRequest pagination = 1;
  // filter
  Filter filter = 2;
}

// ListTemplatesResponse
message ListTemplatesResponse {
  // 分页信息
  utils.v2.PaginationResponse pagination = 1;
  // campaign templates
  repeated moego.models.enterprise.v1.CampaignTemplate templates = 2;
}

// PushTemplatesRequest
message PushTemplatesRequest {
  // campaign template ids
  repeated int64 ids = 1 [(validate.rules).repeated = {min_items: 1}];
  // tenant ids
  repeated moego.models.enterprise.v1.TenantObject targets = 2 [(validate.rules).repeated = {min_items: 1}];
}

// PushTemplatesResponse
message PushTemplatesResponse {
  // success
  bool success = 1;
}

// ListTemplatePushRecordsRequest
message ListTemplatePushRecordsRequest {
  // filter
  message Filter {
    // company ids
    repeated int64 company_ids = 1;
    // types
    repeated models.enterprise.v1.Campaign.Type types = 2;
  }
  // filter
  Filter filter = 1;
}

// ListTemplatePushRecordsResponse
message ListTemplatePushRecordsResponse {
  // campaign template apply records
  repeated moego.models.enterprise.v1.CampaignTemplatePushRecord records = 1;
}

// ListTemplatePlaceholdersRequest
message ListTemplatePlaceholdersRequest {
  // type
  models.enterprise.v1.Campaign.Type type = 1;
}

// ListTemplatePlaceholdersResponse
message ListTemplatePlaceholdersResponse {
  // template variables
  repeated moego.models.message.v1.MessageTemplatePlaceholderSimpleView placeholders = 1;
}
