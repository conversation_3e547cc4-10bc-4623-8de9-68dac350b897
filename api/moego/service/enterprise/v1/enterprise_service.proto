syntax = "proto3";

package moego.service.enterprise.v1;

import "moego/models/enterprise/v1/enterprise_defs.proto";
import "moego/models/enterprise/v1/enterprise_models.proto";
import "moego/models/enterprise/v1/tenant_template_defs.proto";
import "moego/models/enterprise/v1/tenant_template_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1;enterprisesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.enterprise.v1";

// enterprise service
service EnterpriseService {
  // get enterprise by id
  rpc GetEnterprise(GetEnterpriseRequest) returns (GetEnterpriseResponse) {}
  // list enterprise
  rpc ListEnterprise(ListEnterpriseRequest) returns (ListEnterpriseResponse) {}
  // create enterprise
  rpc CreateEnterprise(CreateEnterpriseRequest) returns (CreateEnterpriseResponse) {}
  // update enterprise
  rpc UpdateEnterprise(UpdateEnterpriseRequest) returns (UpdateEnterpriseResponse) {}
  // update tenant template
  rpc UpdateTenantTemplate(UpdateTenantTemplateRequest) returns (UpdateTenantTemplateResponse) {}
  // create tenant template
  rpc CreateTenantTemplate(CreateTenantTemplateRequest) returns (CreateTenantTemplateResponse) {}
  // list tenant templates
  rpc ListTenantTemplates(ListTenantTemplatesRequest) returns (ListTenantTemplatesResponse) {}
  // sync franchisee
  rpc SyncFranchisee(SyncFranchiseeRequest) returns (SyncFranchiseeResponse) {}
  // sync franchisee owner permission
  rpc SyncFranchiseeOwnerPermission(SyncFranchiseeOwnerPermissionRequest) returns (SyncFranchiseeOwnerPermissionResponse) {}
}

// get enterprise by id request
message GetEnterpriseRequest {
  // identifier
  oneof identifier {
    option (validate.required) = true;
    // id
    int64 id = 1 [(validate.rules).int64 = {not_in: 0}];
  }
}

// get enterprise by id response
message GetEnterpriseResponse {
  // enterprise
  models.enterprise.v1.EnterpriseModel enterprise = 1;
}

// list enterprise request
message ListEnterpriseRequest {
  // filter
  message Filter {
    // id
    repeated int64 ids = 1;
    // account id
    repeated int64 account_ids = 2;
  }
  // filter
  optional Filter filter = 1;
  // pagination, null for all
  optional utils.v2.PaginationRequest pagination = 2;
}

// list enterprise response
message ListEnterpriseResponse {
  // pagination
  optional utils.v2.PaginationResponse pagination = 1;
  // enterprises
  repeated models.enterprise.v1.EnterpriseModel enterprises = 2;
}

// create enterprise request
message CreateEnterpriseRequest {
  // enterprise
  models.enterprise.v1.CreateEnterpriseDef enterprise = 1;
}

// create enterprise response
message CreateEnterpriseResponse {
  // enterprise
  models.enterprise.v1.EnterpriseModel enterprise = 1;
}

// update enterprise request
message UpdateEnterpriseRequest {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // enterprise setting def
  optional models.enterprise.v1.UpdateEnterpriseDef enterprise = 2;
}

// update enterprise response
message UpdateEnterpriseResponse {
  // enterprise
  models.enterprise.v1.EnterpriseModel enterprise = 1;
}

// create tenant template request
message CreateTenantTemplateRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // tenant template
  models.enterprise.v1.CreateTenantTemplateDef tenant_template = 2;
}

// create tenant template response
message CreateTenantTemplateResponse {
  // template
  models.enterprise.v1.TenantTemplateModel tenant_template = 1;
}

// update tenant template request
message UpdateTenantTemplateRequest {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // optional
  optional models.enterprise.v1.UpdateTenantTemplateDef tenant_template = 3;
}

// update tenant template response
message UpdateTenantTemplateResponse {
  // template
  models.enterprise.v1.TenantTemplateModel tenant_template = 1;
}

// list tenant templates request
message ListTenantTemplatesRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // filter
  message Filter {
    // tenant id
    repeated int64 tenant_ids = 1;
    // status
    repeated models.enterprise.v1.TenantTemplateModel.Status statuses = 2;
    // type
    repeated models.enterprise.v1.TenantTemplateModel.Type types = 3;
  }
  // filter
  optional Filter filter = 2;
}

// list tenant templates response
message ListTenantTemplatesResponse {
  // tenant templates
  repeated models.enterprise.v1.TenantTemplateModel tenant_templates = 1;
}

// sync franchisee request
message SyncFranchiseeRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // company ids, if empty, sync all
  repeated int64 company_ids = 2 [(validate.rules).repeated.items.int64.gt = 0];
}

// sync franchisee response
message SyncFranchiseeResponse {}

// sync franchisee owner permission
message SyncFranchiseeOwnerPermissionRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64 = {gt: 0}];
  // company ids, if empty, sync all
  repeated int64 company_ids = 2 [(validate.rules).repeated.items.int64.gt = 0];
}

// sync franchisee owner permission response
message SyncFranchiseeOwnerPermissionResponse {}
