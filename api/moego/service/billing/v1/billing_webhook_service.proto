syntax = "proto3";

package moego.service.billing.v1;

import "moego/service/finance_gw/v1/webhook_downstream.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/billing/v1;billingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.billing.v1";

// billing webhook service
service WebhookService {
  // receive webhook from financial gateway
  rpc HandleWebhookEvent(moego.service.finance_gw.v1.HandleWebhookEventRequest) returns (moego.service.finance_gw.v1.HandleWebhookEventResponse);
}
