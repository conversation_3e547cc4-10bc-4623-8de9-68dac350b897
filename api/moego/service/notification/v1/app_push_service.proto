syntax = "proto3";

package moego.service.notification.v1;

import "moego/models/notification/v1/notification_defs.proto";
import "moego/models/notification/v1/notification_enums.proto";
import "moego/models/notification/v1/notification_extra_defs.proto";
import "moego/models/notification/v1/push_token_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/notification/v1;notificationsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.notification.v1";

// the app push service
service AppPushService {
  // Refresh device
  rpc RefreshDevice(RefreshDeviceRequest) returns (RefreshDeviceResponse);

  // Create app push
  rpc CreateAppPush(CreateAppPushRequest) returns (CreateAppPushResponse);

  // Batch create app push
  rpc BatchCreateAppPush(BatchCreateAppPushRequest) returns (BatchCreateAppPushResponse);

  // refresh branded app notification config
  rpc RefreshBrandedAppNotificationConfig(RefreshBrandedAppNotificationConfigRequest) returns (RefreshBrandedAppNotificationConfigResponse);

  // List push token
  rpc ListPushToken(ListPushTokenRequest) returns (ListPushTokenResponse);
}

// register device request
message RefreshDeviceRequest {
  // push token
  string push_token = 1;
  // device type
  models.notification.v1.DeviceType device_type = 2;
  // account id
  optional int64 account_id = 3 [(validate.rules).int64 = {gt: 0}];
  // source
  models.notification.v1.PushTokenSource source = 4 [(validate.rules).enum = {defined_only: true}];
  // company id
  optional int64 company_id = 5 [(validate.rules).int64 = {gt: 0}];
  // business id
  optional int64 business_id = 6 [(validate.rules).int64 = {gt: 0}];
}

// register device response
message RefreshDeviceResponse {
  // is online
  bool is_online = 1;
}

// create app push request
message CreateAppPushRequest {
  // account id
  int64 account_id = 1 [(validate.rules).int64 = {gt: 0}];
  // type
  models.notification.v1.NotificationType type = 2 [(validate.rules).enum = {defined_only: true}];
  // extra info
  moego.models.notification.v1.NotificationExtraDef extra = 5;
  // app push
  models.notification.v1.AppPushDef app_push = 6;
}

// create app push response
message CreateAppPushResponse {
  // push successes
  int64 push_successes = 1 [(validate.rules).int64 = {gte: 0}];
}

// batch crate app push request
message BatchCreateAppPushRequest {
  // identifier
  oneof identifier {
    option (validate.required) = true;
    // by account
    MultiAccountDef by_account = 1;
    // by company
    MultiCompanyDef by_company = 2;
    // by business
    MultiBusinessDef by_business = 3;
  }
}

// multi account def
message MultiAccountDef {
  // multi account ids
  repeated int64 account_ids = 1 [(validate.rules).repeated = {
    min_items: 0
    items: {
      int64: {gt: 0}
    }
  }];
}

// multi company def
message MultiCompanyDef {
  // multi company ids
  repeated int64 company_ids = 1 [(validate.rules).repeated = {
    min_items: 0
    items: {
      int64: {gt: 0}
    }
  }];
}

// multi business def
message MultiBusinessDef {
  // multi business ids
  repeated int64 business_ids = 1 [(validate.rules).repeated = {
    min_items: 0
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch create app push response
message BatchCreateAppPushResponse {}

// refresh branded app notification config request
message RefreshBrandedAppNotificationConfigRequest {}

// refresh branded app notification config response
message RefreshBrandedAppNotificationConfigResponse {}

// list push token request
message ListPushTokenRequest {
  // filter
  message Filter {
    // account id
    repeated int64 account_ids = 1 [(validate.rules).repeated = {
      items: {
        int64: {gt: 0}
      }
    }];
    // source
    repeated models.notification.v1.PushTokenSource sources = 2 [(validate.rules).repeated = {
      items: {
        enum: {defined_only: true}
      }
    }];
  }
  // filter
  Filter filter = 1;
}

// list push token response
message ListPushTokenResponse {
  // push tokens
  repeated moego.models.notification.v1.PushTokenModel push_tokens = 1;
}
