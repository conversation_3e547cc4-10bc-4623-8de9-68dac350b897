version: v1
breaking:
  use:
    - FILE
lint:
  allow_comment_ignores: true
  use:
    - DEFAULT
    - COMMENT_ENUM
    - COMMENT_ENUM_VALUE
    - COMMENT_FIELD
    - COMMENT_MESSAGE
    - COMMENT_ONEOF
    - COMMENT_RPC
    - COMMENT_SERVICE
    - PACKAGE_NO_IMPORT_CYCLE
  except:
    - RPC_REQUEST_RESPONSE_UNIQUE
    - RPC_REQUEST_STANDARD_NAME
    - RPC_RESPONSE_STANDARD_NAME
    - ENUM_VALUE_PREFIX
    - SERVICE_SUFFIX
  ignore:
    - grpc
deps:
  - buf.build/envoyproxy/protoc-gen-validate
  - buf.build/googleapis/googleapis
  - buf.build/bufbuild/protovalidate
