package com.moego.common.utils;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.moego.common.enums.BusinessConst;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;
import org.junit.jupiter.api.Test;

public class DateUtilTest {

    @Test
    public void minuteToTimeHour12_success() {
        assertEquals("12:00 am", DateUtil.minuteToTime12Hour(0));
        assertEquals("00:10 am", DateUtil.minuteToTime12Hour(10));
        assertEquals("10:00 am", DateUtil.minuteToTime12Hour(600));
        assertEquals("10:30 am", DateUtil.minuteToTime12Hour(630));
        assertEquals("12:00 pm", DateUtil.minuteToTime12Hour(720));
        assertEquals("12:30 pm", DateUtil.minuteToTime12Hour(750));
        assertEquals("06:20 pm", DateUtil.minuteToTime12Hour(1100));
        assertEquals("10:20 pm", DateUtil.minuteToTime12Hour(1340));
        assertEquals("12:00 am", DateUtil.minuteToTime12Hour(1440));
    }

    @Test
    public void TimeZone() {
        System.out.println(TimeZone.getTimeZone("Asia/Taipei"));
    }

    @Test
    public void testReg() {
        assertTrue(EmailFormatUtil.isEmailValid("<EMAIL>"));
        assertTrue(EmailFormatUtil.isEmailValid("<EMAIL>"));
        assertTrue(EmailFormatUtil.isEmailValid("<EMAIL>"));
        assertTrue(EmailFormatUtil.isEmailValid("<EMAIL>"));
        assertFalse(EmailFormatUtil.isEmailValid("<EMAIL>  <EMAIL>"));
        assertFalse(EmailFormatUtil.isEmailValid("<EMAIL>"));
    }

    @Test
    public void parseDateWithTimezone() throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("dd-M-yyyy hh:mm:ss a", Locale.ENGLISH);
        formatter.setTimeZone(TimeZone.getTimeZone("America/New_York"));

        String dateInString = "22-01-2015 10:15:55 AM";
        Date date = formatter.parse(dateInString);
        String formattedDateString = formatter.format(date);
        System.out.println(formattedDateString);
    }

    @Test
    public void localDatePeriodTest() {
        String date1 = "2021-04-25";
        String date2 = "2021-07-25";
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate d1 = LocalDate.parse(date1, fmt);
        LocalDate d2 = LocalDate.parse(date2, fmt);
        //        Period  dur = Period.between(d1, d2);
        long daysDiff = ChronoUnit.DAYS.between(d1, d2);

        LocalDate addResult1 = d1.plusDays(10);
        LocalDate addResult2 = d1.plusDays(-10);

        String date3 = "2021-05-25";
        LocalDate d3 = LocalDate.parse(date3, fmt);
        LocalDate addResult3 = d3.plusDays(daysDiff);
        System.out.println(addResult3.toString());
        //        System.out.println(daysDiff);
        //        System.out.println(d1.toString());
        //        System.out.println(addResult1.toString());
        //        System.out.println(addResult2.toString());
    }

    /**
     * {@link DateUtil#timestamp(String, String)}
     */
    @Test
    void testTimestamp() {
        long timestampOfAsiaShanghai20220101 = 1640966400000L;

        assertThat(DateUtil.timestamp("2022-01-01", "Asia/Shanghai")).isEqualTo(timestampOfAsiaShanghai20220101);
        assertThat(DateUtil.timestamp("2022-01-01 00:00:00", "Asia/Shanghai"))
                .isEqualTo(timestampOfAsiaShanghai20220101);
        assertThat(DateUtil.timestamp("2022-01-01", "America/Los_Angeles"))
                .isNotEqualTo(timestampOfAsiaShanghai20220101);
    }

    @Test
    void testConvertTimeBySeconds() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeZone(TimeZone.getTimeZone("America/Los_Angeles"));

        calendar.set(2023, Calendar.JUNE, 16, 0, 0, 0);
        assertThat(DateUtil.convertTimeBySeconds(
                        calendar.getTimeInMillis() / 1000, "America/Los_Angeles", BusinessConst.TIME_12_HOURS_TYPE))
                .isEqualTo("12:00 AM");

        calendar.set(2023, Calendar.JUNE, 16, 0, 30, 0);
        assertThat(DateUtil.convertTimeBySeconds(
                        calendar.getTimeInMillis() / 1000, "America/Los_Angeles", BusinessConst.TIME_12_HOURS_TYPE))
                .isEqualTo("12:30 AM");

        calendar.set(2023, Calendar.JUNE, 16, 1, 0, 0);
        assertThat(DateUtil.convertTimeBySeconds(
                        calendar.getTimeInMillis() / 1000, "America/Los_Angeles", BusinessConst.TIME_12_HOURS_TYPE))
                .isEqualTo("01:00 AM");

        calendar.set(2023, Calendar.JUNE, 16, 12, 0, 0);
        assertThat(DateUtil.convertTimeBySeconds(
                        calendar.getTimeInMillis() / 1000, "America/Los_Angeles", BusinessConst.TIME_12_HOURS_TYPE))
                .isEqualTo("12:00 PM");

        calendar.set(2023, Calendar.JUNE, 16, 12, 30, 0);
        assertThat(DateUtil.convertTimeBySeconds(
                        calendar.getTimeInMillis() / 1000, "America/Los_Angeles", BusinessConst.TIME_12_HOURS_TYPE))
                .isEqualTo("12:30 PM");

        calendar.set(2023, Calendar.JUNE, 16, 13, 0, 0);
        assertThat(DateUtil.convertTimeBySeconds(
                        calendar.getTimeInMillis() / 1000, "America/Los_Angeles", BusinessConst.TIME_12_HOURS_TYPE))
                .isEqualTo("01:00 PM");

        calendar.set(2023, Calendar.JUNE, 16, 23, 30, 0);
        assertThat(DateUtil.convertTimeBySeconds(
                        calendar.getTimeInMillis() / 1000, "America/Los_Angeles", BusinessConst.TIME_12_HOURS_TYPE))
                .isEqualTo("11:30 PM");
    }

    @Test
    void testGetStrDateByDaysDiff() {
        String before = "2023-06-16";
        String after = "2023-06-15";
        assertThat(DateUtil.getStrDateByDaysDiff(before, 1L)).isEqualTo(after);
    }

    @Test
    void testGetDaysDiffByTwoDate() {
        String before = "2023-06-16";
        String after = "2023-06-17";
        assertThat(DateUtil.getDaysDiffByTwoDate(before, after)).isEqualTo(-1L);
    }

    @Test
    void testGetArrivalWindowTimeStringWithinWorkTime() {
        String appointmentDateStr = "2024-09-10";
        Integer apptStartTime = 600; // 10:00 AM
        Integer arrivalBefore = 30; // 30 minutes before
        Integer arrivalAfter = 30; // 30 minutes after
        Byte timeFormatType = 1; // 24-hour format
        Integer calendarViewStartAt = 540; // 9:00 AM
        Integer calendarViewEndAt = 1020; // 5:00 PM

        String expected = "09:30 - 10:30"; // Expected time window in 24-hour format

        String actual = DateUtil.getArrivalWindowTimeString(
                appointmentDateStr,
                apptStartTime,
                arrivalBefore,
                arrivalAfter,
                timeFormatType,
                calendarViewStartAt,
                calendarViewEndAt);

        assertEquals(expected, actual);
    }

    @Test
    void testGetArrivalWindowTimeStringOutOfWorkTime() {
        String appointmentDateStr = "2024-09-10";
        Integer apptStartTime = 1080; // 6:00 PM
        Integer arrivalBefore = 30; // 30 minutes before
        Integer arrivalAfter = 30; // 30 minutes after
        Byte timeFormatType = 1; // 24-hour format
        Integer calendarViewStartAt = 540; // 9:00 AM
        Integer calendarViewEndAt = 1020; // 5:00 PM

        String expected = "17:30 - 18:00"; // Expected time window should start and end at the appointment time

        String actual = DateUtil.getArrivalWindowTimeString(
                appointmentDateStr,
                apptStartTime,
                arrivalBefore,
                arrivalAfter,
                timeFormatType,
                calendarViewStartAt,
                calendarViewEndAt);

        assertEquals(expected, actual);
    }
}
