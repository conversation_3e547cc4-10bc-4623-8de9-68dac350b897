package com.moego.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.moego.common.security.Des3Util;
import org.junit.jupiter.api.Test;

public class Des3UtilTest {

    String key = "AAAA33*2020_123456789AAA"; // 必须24位

    @Test
    public void testEncode() {
        String source = "moego.Lily";
        String encrypt = Des3Util.encode(key, source);
        assertEquals(source, Des3Util.decode(key, encrypt));
    }
}
