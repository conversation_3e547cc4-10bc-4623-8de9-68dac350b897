package com.moego.common.utils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.moego.common.dto.StaffPermissions;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.StaffEnum;
import com.moego.common.exception.CommonException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

@Slf4j
public class PermissionUtil {

    public static final String ROLE_ADMIN = "Admin";
    public static final String ROLE_GROOMER_PRO = "Groomer Pro";
    public static final String ROLE_GROOMER = "Groomer";

    public static final int CREATE_NEW_BOOKING = 1;
    public static final int CANCEL_TICKET = 2;
    public static final int CAN_EDIT_TICKET = 28;
    public static final int VIEW_CLIENT_LIST = 3;
    public static final int VIEW_CLIENT_EMAIL = 4;
    public static final int VIEW_CLIENT_PHONE = 5;
    public static final int CREATE_NEW_CLIENT = 6;
    public static final int DELETE_CLIENT = 7;
    public static final int DELETE_PET = 8;
    public static final int SET_PET_PASSAWAY = 9;
    public static final int CAN_ACCESS_REPORT_CENTER = 10;
    public static final int VIEW_FULL_MESSAGE = 11;
    public static final int VIEW_OWN_MESSAGE = 12;
    public static final int VIEW_SETTING = 13;
    public static final int VIEW_ROLE = 14;
    public static final int VIEW_MESSAGE_CENTER = 15;
    public static final int VIEW_PACKAGE_SETTING = 16;
    public static final int VIEW_PACKAGE_SALE = 17;
    public static final int VIEW_PRODUCT_SETTING = 18;
    public static final int VIEW_PRODUCT_SALE = 19;
    public static final int VIEW_INTAKE_FORM = 20;
    /**
     * 产品定义上已经没有这个 permission，使用 {@link #CAN_ACCESS_ONLINE_BOOKING_REQUEST_AND_WAIT_LIST} 替换
     *
     * @deprecated by Freeman, use {@link #CAN_ACCESS_ONLINE_BOOKING_REQUEST_AND_WAIT_LIST} instead.
     * @see <a href="https://moego.atlassian.net/browse/ERP-6447">ERP-6447</a>
     */
    @Deprecated(since = "2023/9/8")
    public static final int VIEW_ONLINE_BOOKING = 21;

    public static final int VIEW_AGREEMENT = 22;
    public static final int VIEW_BUSINESS_REPORT = 23;
    public static final int EDIT_REVIEW_BOOSTER = 24;
    public static final int VIEW_CARD_PROCESSING = 25;
    public static final int VIEW_OWN_ALL_MESSAGE = 26;

    public static final int CAN_ACCESS_GOOGLE_CALENDAR_SYNC = 27;
    public static final int CAN_ACCESS_CLOCK_IN_AND_OUT_OF_ALL_STAFF = 28;
    public static final int CAN_EDIT_CLOCK_IN_AND_OUT_RECORDS_OF_ALL_STAFF = 29;

    public static final int CAN_ACCESS_STAFF_SETTINGS = 31;
    public static final int CAN_ACCESS_ROLE_SETTINGS = 32;
    public static final int CAN_ACCESS_PAY_RATE_SETTINGS = 33;
    public static final int CAN_ACCESS_RECEIVE_NOTIFICATION_SETTINGS = 34;
    public static final int CAN_SYNC_ALL_STAFF_CALENDAR = 35;
    public static final int CAN_SYNC_STAFF_OWN_CALENDAR = 36;
    public static final int CAN_IMPORT_CLIENTS = 37;
    public static final int CAN_EXPORT_CLIENTS = 38;
    public static final int CAN_ACCESS_QUICKBOOKS = 39;

    public static final int CAN_CONTROL_PROCESSING_FEE_BY_CLIENTS_IN_INVOICE = 40;

    // Can access marketing campaigns
    public static final int CAN_ACCESS_MARKETING_CAMPAIGNS = 41;

    // Can access grooming report setting
    public static final int CAN_ACCESS_GROOMING_REPORT_SETTING = 42;

    public static final int CAN_ACCESS_ONLINE_BOOKING_REQUEST_AND_WAIT_LIST = 21;
    /**
     * 需要 {@link #CAN_ACCESS_ONLINE_BOOKING_REQUEST_AND_WAIT_LIST} 开启
     */
    public static final int CAN_ACCESS_ONLINE_BOOKING_METRICS = 43;
    /**
     * 需要 {@link #CAN_ACCESS_ONLINE_BOOKING_REQUEST_AND_WAIT_LIST} 开启
     */
    public static final int CAN_ACCESS_ONLINE_BOOKING_AUTOMATION_AND_CONFIGURATION = 44;

    public static final int CAN_ACCESS_ABANDONED_BOOKINGS = 45;
    /**
     * 需要 {@link #CAN_ACCESS_ABANDONED_BOOKINGS}
     */
    public static final int CAN_ACCESS_ABANDONED_BOOKINGS_METRICS = 46;

    public static final int CAN_CREATE_BLOCK = 48;

    public static final int CAN_ACCESS_DISCOUNT_MODULE = 49;

    public static final int CAN_CREATE_EDIT_ARCHIVE_DISCOUNT = 50;

    public static final int CAN_ADD_REMOVE_DISCOUNT_AT_CHECK_OUT = 51;
    public static final int CAN_PROCESS_PAYMENT = 52;

    // is company owner and not enterprise staff
    public static boolean isOwner(StaffPermissions staffPermissions) {
        return staffPermissions != null
                && StaffEnum.EMPLOYEE_CATEGORY_OWNER.equals(staffPermissions.getEmployeeCategory());
    }

    public static boolean hasOwnerPermission(StaffPermissions staffPermissions) {
        return staffPermissions != null && hasOwnerPermission(staffPermissions.getEmployeeCategory());
    }

    public static boolean hasOwnerPermission(Byte employeeCategory) {
        return StaffEnum.EMPLOYEE_CATEGORY_OWNER.equals(employeeCategory)
                || StaffEnum.EMPLOYEE_CATEGORY_MASTER_OWNER.equals(employeeCategory);
    }

    public static boolean hasEnterprisePermission(Byte employeeCategory) {
        return StaffEnum.EMPLOYEE_CATEGORY_ENTERPRISE_STAFF.equals(employeeCategory)
                || StaffEnum.EMPLOYEE_CATEGORY_MASTER_OWNER.equals(employeeCategory);
    }
    /**
     * 检查权限判断
     * true 有此权限值 false 无此权限
     * 权限12，是负面权限，owner 为false
     * @param staffPermissions
     * @param pIds
     * @return
     */
    public static Boolean checkStaffPermissionsInfo(StaffPermissions staffPermissions, int[] pIds) {
        if (staffPermissions == null) {
            return false;
        }
        // owner不校验权限
        if (hasOwnerPermission(staffPermissions)) {
            return true;
        }
        if (staffPermissions.getPermissionIdList() == null) {
            staffPermissions.setPermissionIdList(
                    new Gson().fromJson(staffPermissions.getIdStr(), new TypeToken<List<Integer>>() {}.getType()));
        }
        if (staffPermissions.getPermissionIdList() == null) {
            return false;
        }
        if (staffPermissions.getPermissionIdList().contains(-1)) {
            return true;
        }
        for (int pId : pIds) {
            boolean isFind = staffPermissions.getPermissionIdList().contains(pId);
            if (!isFind) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查权限判断
     *
     * @param staffPermissions
     * @param pId
     * @return
     */
    public static Boolean checkStaffPermissionsInfo(StaffPermissions staffPermissions, int pId) {
        return checkStaffPermissionsInfo(staffPermissions, new int[] {pId});
    }

    /**
     * 检查在permission信息内，是否包含pIds的权限值
     *
     * @param staffPermissions
     * @param pIds
     * @return
     */
    public static void verifyStaffPermissions(StaffPermissions staffPermissions, int[] pIds) {
        boolean checkPass = checkStaffPermissionsInfo(staffPermissions, pIds);
        if (!checkPass) {
            throw new CommonException(ResponseCodeEnum.PERMISSION_NOT_ENOUGH);
        }
    }

    /**
     * 检查在permission信息内，是否包含pIds的权限值
     *
     * @param staffPermissions
     * @param pId
     * @return
     */
    public static void verifyStaffPermissions(StaffPermissions staffPermissions, int pId) {
        verifyStaffPermissions(staffPermissions, new int[] {pId});
    }

    public static String phoneNumberConfusion(String phoneNumber) {
        if (StringUtils.isEmpty(phoneNumber)) {
            return "";
        }
        int len = phoneNumber.length();
        if (len > 4) {
            phoneNumber = phoneNumber.substring(0, len - 4) + "****";
        }
        return phoneNumber;
    }

    public static String emailConfusion(String email) {
        if (StringUtils.isEmpty(email)) {
            return "";
        }
        String[] emailInfo = email.split("@");
        if (emailInfo.length != 2) {
            // https://sentry.moego.pet/organizations/moego/issues/658/?environment=prod&project=5&query=is%3Aunresolved
            log.warn("email format is invalid, email = {}", email);
            return email.toLowerCase();
        }
        int len = emailInfo[0].length();
        if (len > 4) {
            emailInfo[0] = emailInfo[0].substring(0, len - 4) + "****";
        } else {
            if (len == 1) {
                emailInfo[0] = "*";
            }
            if (len == 2) {
                emailInfo[0] = "**";
            }
            if (len == 3) {
                emailInfo[0] = "***";
            }
            if (len == 4) {
                emailInfo[0] = "****";
            }
        }
        return emailInfo[0] + "@" + emailInfo[1];
    }
}
