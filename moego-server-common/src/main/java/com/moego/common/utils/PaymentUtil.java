package com.moego.common.utils;

import com.moego.common.dto.NewPricingLevelDto;
import com.moego.common.enums.PaymentMethodEnum;
import com.moego.common.enums.PaymentStatusEnum;
import com.moego.common.enums.PaymentStripeStatus;
import com.moego.common.utils.payment.ProcessingFee;
import com.moego.common.utils.payment.ProcessingFeePair;
import com.moego.common.utils.payment.ProcessingFeeUtil;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2021/3/3 4:48 PM
 */
@Slf4j
public class PaymentUtil {

    public static boolean isCreditCard(String method) {
        if (method == null) {
            return false;
        }
        method = method.toLowerCase();
        if (PaymentMethodEnum.METHOD_NAME_CREDIT_CARD.toLowerCase().equals(method) || "creditcard".equals(method)) {
            return true;
        }
        return false;
    }

    /**
     * 将credit card拆分为stripe card、square card等4中支付方式
     * @param method
     * @param squarePaymentMethod
     * @return
     */
    public static String getCreditCardReportMethod(String method, Byte squarePaymentMethod) {
        if (PaymentMethodEnum.METHOD_NAME_CREDIT_CARD.equals(method)) {
            if (squarePaymentMethod == null) {
                return PaymentMethodEnum.METHOD_NAME_STRIPE_CREDIT_CARD;
            } else {
                // square_payment_method	1 - card, 2 - card on file, 3- terminal, 4 - reader
                if (PaymentMethodEnum.SQUARE_PAYMENT_METHOD_TERMINAL.equals(squarePaymentMethod)) {
                    return PaymentMethodEnum.METHOD_NAME_SQUARE_CREDIT_CARD_TERMINAL;
                }
                if (PaymentMethodEnum.SQUARE_PAYMENT_METHOD_READER.equals(squarePaymentMethod)) {
                    return PaymentMethodEnum.METHOD_NAME_SQUARE_CREDIT_CARD_READER;
                }
                return PaymentMethodEnum.METHOD_NAME_SQUARE_CREDIT_CARD;
            }
        }
        return method;
    }

    public static String formatDescription(String description) {
        if (StringUtils.isEmpty(description)) {
            return "MoeGo Customer";
        }
        description = description.replace("<", "");
        description = description.replace(">", "");
        description = description.replace("'", "");
        description = description.replace("\\", "");
        description = description.replace("\"", "");
        return description.length() > 20 ? description.substring(0, 20) : description;
    }

    // 默认usd,
    public static String getAllowCurrency(String businessCurrency) {
        String[] tempList = PaymentStatusEnum.STRIPE_ALLOW_CURRENCY;
        for (String allowCurrency : tempList) {
            if (allowCurrency.equals(businessCurrency)) {
                return allowCurrency;
            }
        }
        return "usd";
    }

    /**
     * php 版本检查stripe payment description代码
     * @param desc
     * @return
     */
    public static String phpCheckPaymentDesc(String desc) {
        desc = desc.replace("<", "");
        desc = desc.replace(">", "");
        desc = desc.replace("'", "");
        desc = desc.replace("’", "");
        desc = desc.replace("\\", "");
        desc = desc.replace("\"", "");
        return desc.length() > 20 ? desc.substring(0, 20) : desc;
    }

    public static BigDecimal getAmountFromUnit(Long amount) {
        return new BigDecimal(amount).divide(new BigDecimal(100), MathContext.DECIMAL64);
    }

    /**
     * application fees： 3.4% * amount + 30 cent
     * @param chargeAmount  unit: cent
     * @return
     */
    @Deprecated
    public static Integer getStripeApplicationFee(Integer chargeAmount) {
        if (chargeAmount <= 0) {
            return 0;
        }
        return ((Double) (Math.ceil(chargeAmount * 0.034))).intValue() + 30;
    }

    /**
     * 根据不同的支付方式设置不同的费率
     * https://moego.atlassian.net/browse/ERP-1309 : 2022-06-16 修改为2.9% + 50cents
     * @param chargeAmount
     * @param paymentMethod
     * @return
     */
    @Deprecated
    public static Integer getStripeFeeByPaymentMethod(Integer chargeAmount, Byte paymentMethod) {
        if (chargeAmount <= 0) {
            return 0;
        }
        if (PaymentStripeStatus.BLUETOOTH_PAY.equals(paymentMethod)) {
            return ((Double) (Math.ceil(chargeAmount * 0.029))).intValue() + 50;
        } else if (PaymentStripeStatus.SMART_READER_PAY.equals(paymentMethod)) {
            // 当前reader 采取相同费率
            return ((Double) (Math.ceil(chargeAmount * 0.029))).intValue() + 50;
        } else { // keep same for other payment method
            return getStripeApplicationFee(chargeAmount);
        }
    }

    /**
     * 因为"一户一费"的存在，该方法已经废弃
     *
     * @deprecated since 2022/11/2 By Freeman, use {@link #getStripeFee(Integer, Byte, ProcessingFee)} instead
     * @see <a href="https://moego.atlassian.net/wiki/spaces/Lyft/pages/61669377/Customized+processing+fee">Customized processing fee</a>
     */
    @Deprecated
    public static Integer getStripeFee(Integer chargeAmount, Byte paymentMethod, NewPricingLevelDto planTier) {
        ProcessingFee currentFeeConfig = ProcessingFeeUtil.getProcessFee(planTier);
        boolean isTerminal = PaymentStripeStatus.isStripeTerminal(paymentMethod);
        ProcessingFeePair pmFeeConfig = isTerminal ? currentFeeConfig.getTerminal() : currentFeeConfig.getOnline();
        return ((Double) (Math.ceil(chargeAmount * pmFeeConfig.getPercent()))).intValue() + pmFeeConfig.getCent();
    }

    /**
     * Get stripe fee by payment method and processing fee.
     *
     * @param chargeAmount amount in cent
     * @param paymentMethod payment method
     * @param currentFee current processing fee
     * @return stripe fee in cent
     */
    @Deprecated
    public static Integer getStripeFee(Integer chargeAmount, Byte paymentMethod, ProcessingFee currentFee) {
        boolean isTerminal = PaymentStripeStatus.isStripeTerminal(paymentMethod);
        ProcessingFeePair pmFeeConfig = isTerminal ? currentFee.getTerminal() : currentFee.getOnline();
        // update  bank round to integer
        int fee = BigDecimal.valueOf(pmFeeConfig.getPercent())
                .multiply(BigDecimal.valueOf(chargeAmount))
                .setScale(0, RoundingMode.HALF_EVEN)
                .intValue();
        log.info("getStripeFee,origin:{},after:{},addition:{}", chargeAmount, fee, pmFeeConfig.getCent());
        return fee + pmFeeConfig.getCent();
    }

    /**
     * 根据自定义费率计算convenienceFee
     * convenience fee是由顾客支付的processing fee，需满足：
     *  (chargeAmount + convenienceFee) * rate + cents = convenienceFee
     * 简化后可得计算公式为：
     *  convenienceFee = (chargeAmount + cents) / (1 - rate) - chargeAmount
     *
     * @param chargeAmount       支付金额
     * @param processingFeeRate  自定义rate
     * @param processingFeeCents 自定义cents
     * @return
     */
    @Deprecated
    public static Integer getCustomizedConvenienceFee(
            Integer chargeAmount, BigDecimal processingFeeRate, Integer processingFeeCents) {
        if (chargeAmount <= 0 || processingFeeRate == null || processingFeeCents == null) {
            return 0;
        }
        // 百分比转小数
        double feeRateValue = processingFeeRate
                .divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP)
                .doubleValue();
        return (BigDecimal.valueOf(chargeAmount + processingFeeCents)
                        .divide(BigDecimal.valueOf(1 - feeRateValue), 0, RoundingMode.HALF_EVEN)
                        .intValue()
                - chargeAmount);
    }
}
