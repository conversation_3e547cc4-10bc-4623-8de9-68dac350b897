package com.moego.common.utils;

import com.github.pagehelper.util.StringUtil;
import com.moego.common.enums.BusinessConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2020-06-07 13:59
 */
public class DateUtil {

    /**
     * square expiresAt time format: 2021-07-23T08:04:49Z （Z表示 zero hour offset 即UTC时间）
     */
    private static final DateTimeFormatter SQUARE_DATE_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");

    public static final String STANDARD_DATE_TIME = "yyyy-MM-dd HH:mm:ss";

    public static final String LA_DATE_FORMAT = "MM/dd/yyyy";
    public static final String LA_ZONE_NAME = "America/Los_Angeles";
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(STANDARD_DATE_TIME);

    /**
     * 检查指定日期时间是否在当前日期之前
     *
     * @param targetDateStr 要检查的日期字符串：2021-07-23T08:04:49Z
     * @return true：已过期  false：未过期
     */
    public static boolean isSquareExpiresBeforeNow(String targetDateStr) {
        if (!StringUtils.hasLength(targetDateStr)) {
            return true;
        }
        LocalDateTime targetDate = LocalDateTime.parse(targetDateStr, SQUARE_DATE_FORMATTER);
        // 提前一个小时过期
        LocalDateTime now = LocalDateTime.now(ZoneOffset.UTC).plusHours(8);
        return targetDate.isBefore(now);
    }

    public static final String[] WEEK_ARR = new String[] {
        "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday",
    };
    public static final String[] MONTHS = new String[] {
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
    };
    public static final String STANDARD_DATE = "yyyy-MM-dd";
    public static final String BIRTHDAY_FORMAT_STR =
            "^((\\d{2}(([02468][048])|([13579][26]))[\\-]?((((0?[13578])|(1[02]))[\\-]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-]?((((0?[13578])|(1[02]))[\\-]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-]?((0?[1-9])|(1[0-9])|(2[0-8]))))))";
    public static final Pattern BIRTHDAY_PATTERN = Pattern.compile(BIRTHDAY_FORMAT_STR);

    /**
     * @param dateStr 必须是 2020-02-02这种格式，以-相连
     */
    public static Boolean checkDateFormat(String dateStr) {
        if (!StringUtils.hasText(dateStr)) {
            return true;
        }
        Matcher mat = BIRTHDAY_PATTERN.matcher(dateStr);
        return mat.matches();
    }

    public static String getApptDateAndTimeStr(
            String appointmentDateStr, Integer startTime, String dateFormat, Byte timeFormatType) {
        Date appointmentDate = DateUtil.parseDate(appointmentDateStr, STANDARD_DATE);
        if (appointmentDate == null) {
            return "";
        }
        long times = appointmentDate.getTime() + startTime * 60 * 1000L;
        Date date = new Date(times);

        StringBuilder resultDateString = new StringBuilder();
        resultDateString.append(
                DateUtil.getDateByBusiness(date, StringUtils.hasText(dateFormat) ? dateFormat : STANDARD_DATE));
        resultDateString.append(" ");

        if (timeFormatType.intValue() == 1) {
            resultDateString.append(DateUtil.dateToStrWithLocale(date, "HH:mm", Locale.US));
        } else {
            resultDateString.append(DateUtil.dateToStrWithLocale(date, "hh:mm a", Locale.US));
        }
        return resultDateString.toString();
    }

    /**
     * @param appointmentDateStr date, e.g. 2020-11-11
     * @param dateFormat date format
     * @return formatted date
     */
    public static String getApptDateStr(String appointmentDateStr, String dateFormat) {
        Date appointmentDate = DateUtil.parseDate(appointmentDateStr, STANDARD_DATE);
        if (appointmentDate == null) {
            return "";
        }
        return DateUtil.getDateByBusiness(
                appointmentDate, StringUtils.hasText(dateFormat) ? dateFormat : STANDARD_DATE);
    }

    /**
     * @param dateFormat     businessPreferenceDto内的 String dateFormat
     * @param timeFormatType businessPreferenceDto内的 Integer timeFormatType
     * @return
     */
    public static String getDateByBusiness(Date date, String dateFormat, Integer timeFormatType) {
        String format = getDateFormatForBusiness(dateFormat);
        //        Dictionary.TIME_FORMAT_1
        //        24 hour
        //        Dictionary.TIME_FORMAT_2
        //        12 hour
        if (timeFormatType == 2) {
            format += " HH:mm";
        } else {
            format += " kk:mm a";
        }
        SimpleDateFormat outPutFormat = new SimpleDateFormat(format, Locale.US);
        return outPutFormat.format(date);
    }

    /**
     * Convert min of the day to business set format time
     * 1200, type = 2 -> 08:00 pm
     * 1200, type = 1 -> 20:00
     *
     * @param timeFormatType 1 - 24 hour time, 2 - 12 hour time
     */
    public static String minuteToBusinessTime(Integer min, Integer timeFormatType) {
        if (Integer.valueOf(1).equals(timeFormatType)) {
            return minuteToTime24Hour(min);
        } else {
            return minuteToTime12Hour(min);
        }
    }

    /**
     * Standard 2020-12-22 date to business format date
     */
    public static String dateToBusinessFormat(String standardDate, String format) {
        Date date = parseDate(standardDate, STANDARD_DATE);
        return getDateByBusiness(date, format);
    }

    /**
     * 判断两个日期的大小
     *
     * @param strDate1 date1
     * @param strDate2 date2
     * @param format   date format
     * @return 1 date1大于date2    0 date1等于date2   -1 date1小于date2
     */
    public static Integer twoStrDateCompare(String strDate1, String strDate2, String format) {
        if (StringUtils.isEmpty(format)) {
            format = STANDARD_DATE;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        try {
            Date date1 = simpleDateFormat.parse(strDate1);
            Date date2 = simpleDateFormat.parse(strDate2);
            if (date1.getTime() > date2.getTime()) {
                return 1;
            }
            if (date1.getTime() == date2.getTime()) {
                return 0;
            }
            return -1;
        } catch (ParseException p) {
            throw new CommonException(ResponseCodeEnum.SERVER_ERROR, "getTimeByAppointmentDate ParseException");
        }
    }

    /**
     * Epoch Second to business format date
     */
    public static String dateToBusinessFormat(long epochSeconds, String format) {
        Date date = new Date(epochSeconds * 1000);
        return getDateByBusiness(date, format);
    }

    /**
     * @param dateFormat businessPreferenceDto内的 String dateFormat
     * @return
     */
    public static String getDateByBusiness(Date date, String dateFormat) {
        SimpleDateFormat outPutFormat = new SimpleDateFormat(getDateFormatForBusiness(dateFormat));
        return outPutFormat.format(date);
    }

    public static String getDateFormatForBusiness(String dateFormat) {
        dateFormat = dateFormat.replace("YYYY", "yyyy");
        dateFormat = dateFormat.replace("DD", "dd");
        return dateFormat;
    }

    public static String getArrivalWindowTimeString(
            String appointmentDateStr,
            Integer apptStartTime,
            Integer arrivalBefore,
            Integer arrivalAfter,
            Byte timeFormatType,
            Integer calendarViewStartAt,
            Integer calendarViewEndAt) {
        if (StringUtils.isEmpty(appointmentDateStr)) {
            return "";
        }
        Date appointmentDate = DateUtil.parseDate(appointmentDateStr, STANDARD_DATE);
        // 开启arrivalWindow  返回的是时间段，需要做特殊处理

        long apptBeforeTime = apptStartTime - arrivalBefore;
        long apptAfterTime = apptStartTime + arrivalAfter;
        // 1、当预约时间大于start time 且 (预约-arrival_before)时间小于start time时，才会将( 预约-arrival_before )时间设置为start_time
        // 2、当预约时间小于end time 且 (预约+arrival_after)时间大于end_time时，才会将(预约+arrival_after)时间设置为end_time
        // 3、当预约时间小于start time，( 预约-arrival_before )时间为预约时间
        // 4、当预约时间大于end time,(预约+arrival_after) 时间为预约时间
        // 5、当预约时间大于start time 且(预约-arrival_before)时间大于start time时，不做任何特殊处理
        // 6、当预约时间小于end time 且 (预约+arrival_after)时间小于end_time时，不做任何特殊处理
        if (apptStartTime >= calendarViewStartAt && apptBeforeTime < calendarViewStartAt) {
            apptBeforeTime = calendarViewStartAt;
        }
        if (apptStartTime < calendarViewStartAt) {
            apptBeforeTime = apptStartTime;
        }
        if (apptStartTime <= calendarViewEndAt && apptAfterTime > calendarViewEndAt) {
            apptAfterTime = calendarViewEndAt;
        }
        if (apptStartTime > calendarViewEndAt) {
            apptAfterTime = apptStartTime;
        }

        long arrivalBeforeTimes = appointmentDate.getTime() + apptBeforeTime * 60 * 1000;
        Date arrivalBeforeTimesDate = new Date(arrivalBeforeTimes);
        long arrivalAfterTimes = appointmentDate.getTime() + apptAfterTime * 60 * 1000;
        Date arrivalAfterTimesDate = new Date(arrivalAfterTimes);
        String time = "";
        if (timeFormatType.intValue() == 1) {
            String arrivalBeforeTime = DateUtil.dateToStrWithLocale(arrivalBeforeTimesDate, "HH:mm", Locale.US);
            String arrivalAfterTime = DateUtil.dateToStrWithLocale(arrivalAfterTimesDate, "HH:mm", Locale.US);
            time = arrivalBeforeTime + " - " + arrivalAfterTime;
        } else {
            String arrivalBeforeTime = DateUtil.dateToStrWithLocale(arrivalBeforeTimesDate, "hh:mm a", Locale.US);
            String arrivalAfterTime = DateUtil.dateToStrWithLocale(arrivalAfterTimesDate, "hh:mm a", Locale.US);
            time = arrivalBeforeTime + " - " + arrivalAfterTime;
        }
        return time;
    }

    public static String formatArrivalWindowDateTime(
            String appointmentDate,
            Integer arrivalWindowStartTime,
            Integer arrivalWindowEndTime,
            String dateFormat,
            Byte timeFormatType) {
        String date = dateToBusinessFormat(appointmentDate, dateFormat);
        String arrivalWindowTime =
                formatArrivalWindowTime(arrivalWindowStartTime, arrivalWindowEndTime, timeFormatType);
        return date + " " + arrivalWindowTime;
    }

    public static String formatArrivalWindowTime(
            Integer arrivalWindowStartTime, Integer arrivalWindowEndTime, Byte timeFormatType) {
        LocalTime arrivalWindowStartLocalTime = LocalTime.ofSecondOfDay(arrivalWindowStartTime * 60);
        String arrivalWindowStartTimeString;
        if (Objects.equals(timeFormatType, BusinessConst.TIME_24_HOURS_TYPE)) {
            arrivalWindowStartTimeString = arrivalWindowStartLocalTime.format(DateTimeFormatter.ofPattern("HH:mm"));
        } else {
            arrivalWindowStartTimeString = arrivalWindowStartLocalTime.format(DateTimeFormatter.ofPattern("hh:mm a"));
        }

        LocalTime arrivalWindowEndLocalTime = LocalTime.ofSecondOfDay(arrivalWindowEndTime * 60);
        String arrivalWindowEndTimeString;
        if (Objects.equals(timeFormatType, BusinessConst.TIME_24_HOURS_TYPE)) {
            arrivalWindowEndTimeString = arrivalWindowEndLocalTime.format(DateTimeFormatter.ofPattern("HH:mm"));
        } else {
            arrivalWindowEndTimeString = arrivalWindowEndLocalTime.format(DateTimeFormatter.ofPattern("hh:mm a"));
        }
        // 注意：这里要用 en dash，而不是减号: https://en.wikipedia.org/wiki/Dash#En_dash
        return arrivalWindowStartTimeString + " – " + arrivalWindowEndTimeString;
    }

    /**
     * 获取10位unixTime(now)
     *
     * @return
     */
    public static Long get10Timestamp() {
        Date time = new Date();
        return get10Timestamp(time).longValue();
    }

    /**
     * 获取10位unixTime(now)
     *
     * @return 整数
     */
    public static Integer get10TimestampInteger() {
        Date time = new Date();
        return get10Timestamp(time);
    }

    /**
     * 获取10位unixTime
     *
     * @param time Date
     * @return
     */
    public static Integer get10Timestamp(Date time) {
        Timestamp ts = new Timestamp(time.getTime());
        return (int) ((ts.getTime()) / 1000);
    }

    public static String getFormatStrForDate(Date date, String format) {
        if (date == null) {
            date = new Date();
        }
        if (StringUtils.isEmpty(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.format(date);
    }

    public static String getFormatStrForDateAndLocale(Date date, String format, Locale locale) {
        if (date == null) {
            date = new Date();
        }
        if (StringUtils.isEmpty(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        if (locale != null) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format, locale);
            return simpleDateFormat.format(date);
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.format(date);
    }

    public static Date parseDateWithTimeZone(String stringDate, String zoneName) {
        if (StringUtils.isEmpty(stringDate)) {
            return null;
        }
        LocalDate ld = LocalDate.parse(stringDate);
        return Date.from(ld.atStartOfDay(ZoneId.of(zoneName)).toInstant());
    }

    public static Date parseDateWithTimeZone(String stringDate, String zoneName, String format) {
        if (StringUtils.isEmpty(stringDate)) {
            return null;
        }
        LocalDate ld = LocalDate.parse(stringDate);
        return Date.from(ld.atStartOfDay(ZoneId.of(zoneName)).toInstant());
    }

    public static Date parseDate(String stringDate, String... format) {
        if (StringUtils.isEmpty(stringDate)) {
            return null;
        }

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat();

        simpleDateFormat.setLenient(false);
        for (String f : format) {
            simpleDateFormat.applyPattern(f);
            ParsePosition pos = new ParsePosition(0);
            Date parse = simpleDateFormat.parse(stringDate, pos);
            if (parse != null) {
                return parse;
            }
        }
        return null;
    }

    /**
     * 计算两个日期之间相差的天数
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int daysBetween(Date date1, Date date2) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date1);
        long time1 = cal.getTimeInMillis();
        cal.setTime(date2);
        long time2 = cal.getTimeInMillis();
        long between_days = (time2 - time1) / (1000 * 3600 * 24);

        return Integer.parseInt(String.valueOf(between_days));
    }

    /**
     * 获取某个日期前后几天的日期
     *
     * @param date
     * @param days
     * @return
     */
    public static Date daysBeforeAfter(Date date, int days) {
        Calendar cal = Calendar.getInstance();

        cal.setTime(date);

        cal.add(Calendar.DATE, days);

        return cal.getTime();
    }

    /**
     * 获取某个日期前后几天的日期
     * 传入字符串 返回字符串
     *
     * @param strDate
     * @param days
     * @return
     * @throws ParseException
     */
    public static String daysBeforeAfter(String strDate, int days) throws ParseException {
        if (StringUtils.isEmpty(strDate)) {
            return "";
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(STANDARD_DATE);
        Date date = simpleDateFormat.parse(strDate);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, days);
        return simpleDateFormat.format(cal.getTime());
    }

    /**
     * 返回日期范围内的所有日期
     * example:
     * input: 2020-11-11, 2020-11-15
     * output: 2020-11-11, 2020-11-12, 2020-11-13, 2020-11-14, 2020-11-15
     */
    public static List<String> generateAllDatesBetween(String startDate, String endDate) {
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);

        long days = ChronoUnit.DAYS.between(start, end);
        List<String> result = new ArrayList<>();
        for (int i = 0; i <= days; i++) {
            result.add(start.plusDays(i).toString());
        }
        return result;
    }

    /**
     * Get first day of the week 00:00 am
     * e.g. Fri Jan 15 15:28:58 CST 2021 -> Sun Jan 10 00:00:00 CST 2021
     */
    public static Date getCurrentWeekStart(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        clearCalendarTime(cal);

        cal.set(Calendar.DAY_OF_WEEK, cal.getFirstDayOfWeek());
        return cal.getTime();
    }

    /**
     * Get next day of the week 00:00 am
     * e.g. Fri Jan 15 15:28:58 CST 2021 -> Sun Jan 17 00:00:00 CST 2021
     */
    public static Date getNextWeekStart(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        clearCalendarTime(cal);

        cal.set(Calendar.DAY_OF_WEEK, cal.getFirstDayOfWeek());
        cal.add(Calendar.WEEK_OF_YEAR, 1);
        return cal.getTime();
    }

    /**
     * Get first day of the week 00:00 am
     * e.g. Fri Jan 15 15:28:58 CST 2021 -> Sun Jan 10 00:00:00 CST 2021
     */
    public static Date getCurrentMonthStart(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        clearCalendarTime(cal);

        cal.set(Calendar.DAY_OF_MONTH, 1);
        return cal.getTime();
    }

    /**
     * Get next day of the week 00:00 am
     * e.g. Fri Jan 15 15:28:58 CST 2021 -> Sun Jan 17 00:00:00 CST 2021
     */
    public static Date getNextMonthStart(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        clearCalendarTime(cal);

        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.add(Calendar.MONTH, 1);
        return cal.getTime();
    }

    private static void clearCalendarTime(Calendar cal) {
        // ! clear would not reset the hour of day !
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.clear(Calendar.MINUTE);
        cal.clear(Calendar.SECOND);
        cal.clear(Calendar.MILLISECOND);
    }

    public static int getWeekOfYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.WEEK_OF_YEAR);
    }

    public static int getWeekOfYear(String date) {
        return getWeekOfYear(parseDate(date, STANDARD_DATE));
    }

    public static int getWeekOfYear(long epochSeconds, TimeZone timeZone) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(epochSeconds * 1000);
        if (timeZone != null) {
            calendar.setTimeZone(timeZone);
        }
        return calendar.get(Calendar.WEEK_OF_YEAR);
    }

    /**
     * Have to be format: 2020-11-22
     */
    public static int getMonth(String date) {
        return Integer.parseInt(date.split("-")[1]);
    }

    public static int getMonth(long epochSeconds, TimeZone timeZone) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(epochSeconds * 1000);
        if (timeZone != null) {
            calendar.setTimeZone(timeZone);
        }
        return calendar.get(Calendar.MONTH);
    }

    /**
     * Return year string of given date string
     * Have to be in format 2021-01-22
     */
    public static int getYear(String date) {
        return Integer.parseInt(date.split("-", 2)[0]);
    }

    public static int getYear(long epochSeconds, TimeZone timeZone) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(epochSeconds * 1000);
        if (timeZone != null) {
            calendar.setTimeZone(timeZone);
        }
        return calendar.get(Calendar.YEAR);
    }

    /**
     * @param datetime
     * @doc 日期转换星期几
     */
    public static int dateToWeek(Date datetime) {
        int[] weekDays = {0, 1, 2, 3, 4, 5, 6};
        Calendar cal = Calendar.getInstance(); // 获得一个日历
        cal.setTime(datetime);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1; // 指示一个星期中的某天。
        if (w < 0) {
            w = 0;
        }

        return weekDays[w];
    }

    /**
     * @param localDate
     * @doc 日期转换星期几
     */
    public static int localDateToWeek(LocalDate localDate) {
        return localDate.getDayOfWeek().getValue() % 7;
    }

    public static int dateToWeek(long epochSeconds, TimeZone timeZone) {
        int[] weekDays = {0, 1, 2, 3, 4, 5, 6};
        Calendar cal = Calendar.getInstance(); // 获得一个日历
        cal.setTimeInMillis(epochSeconds * 1000);
        if (timeZone != null) {
            cal.setTimeZone(timeZone);
        }
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1; // 指示一个星期中的某天。
        if (w < 0) {
            w = 0;
        }

        return weekDays[w];
    }

    /**
     * 分钟数转时分
     * 输入：480
     * 输出：08:00
     *
     * @param minuteNumber
     * @return
     */
    public static String minuteToTime24Hour(Integer minuteNumber) {
        int hours = minuteNumber / 60;
        int minutes = minuteNumber % 60;
        String resultHours = Integer.toString(hours);
        String resultMinutes = Integer.toString(minutes);
        if (hours < 10) {
            resultHours = "0" + resultHours;
        }
        if (minutes < 10) {
            resultMinutes = "0" + resultMinutes;
        }
        return resultHours + ":" + resultMinutes;
    }

    public static String minuteToTime12Hour(Integer minuteNumber) {
        if (minuteNumber == 0 || minuteNumber >= 1440) {
            return "12:00 am";
        }

        int hours = minuteNumber / 60;
        int minutes = minuteNumber % 60;

        String suffix = " am";

        if (hours >= 12) {
            suffix = " pm";
            if (hours >= 13) {
                hours -= 12;
            }
        }

        String hourStr = Integer.toString(hours);
        String minuteStr = Integer.toString(minutes);
        if (hours < 10) {
            hourStr = "0" + hours;
        }
        if (minutes < 10) {
            minuteStr = "0" + minutes;
        }
        return hourStr + ":" + minuteStr + suffix;
    }

    /**
     * 获取当天分钟数
     *
     * @return
     */
    public static Integer getNowMinutes(String zoneName) {
        Calendar instance = Calendar.getInstance(TimeZone.getTimeZone(zoneName));

        int i = instance.get(Calendar.HOUR_OF_DAY);
        int i1 = instance.get(Calendar.MINUTE);

        return i * 60 + i1;
    }

    /**
     * 获取当前date年月日
     *
     * @return
     */
    public static Date getNowDate() {
        LocalDate localDate = LocalDate.now();
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        Date date = Date.from(instant);
        return date;
    }

    public static String getNowDateString() {
        LocalDate localDate = LocalDate.now();
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(STANDARD_DATE);
        return localDate.format(fmt);
    }

    public static String getNowDateString(ZoneId zoneId, String dateFormat) {
        LocalDate localDate = LocalDate.now(zoneId);
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(dateFormat);
        return localDate.format(fmt);
    }

    public static Date getCurrentDateTime() {
        return new Date();
    }

    /**
     * 获取当月最后一天
     *
     * @return
     */
    public static String getLastDateThisMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE));
        DateFormat format = new SimpleDateFormat(STANDARD_DATE);
        return format.format(calendar.getTime());
    }

    public static String getDateMinusDays(String date, Integer days) {
        DateTimeFormatter sdf = DateTimeFormatter.ofPattern(STANDARD_DATE);
        LocalDate dateTime = LocalDate.parse(date, sdf);
        return dateTime.minusDays(days).format(sdf);
    }

    public static String getDateMinusMonths(String date, Integer months) {
        DateTimeFormatter sdf = DateTimeFormatter.ofPattern(STANDARD_DATE);
        LocalDate dateTime = LocalDate.parse(date, sdf);
        return dateTime.minusMonths(months).format(sdf);
    }

    public static String getStringDate(Long timeStamp) {
        DateTimeFormatter sdf = DateTimeFormatter.ofPattern(STANDARD_DATE);
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(timeStamp, 0, ZoneOffset.UTC);
        return dateTime.atZone(ZoneOffset.UTC).format(sdf);
    }

    public static String getStringDateTime(Long timeStamp) {
        DateTimeFormatter sdf = DateTimeFormatter.ofPattern(STANDARD_DATE_TIME);
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(timeStamp, 0, ZoneOffset.UTC);
        return dateTime.atZone(ZoneOffset.UTC).format(sdf);
    }

    /**
     * 将某个格式的日期转化为Date类型
     *
     * @return
     */
    public static Date convertStringToDate(String strDate, String format) throws ParseException {
        if (StringUtils.isEmpty(format)) {
            format = STANDARD_DATE;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.parse(strDate);
    }

    public static Date convertStringToDate(String strDate) throws ParseException {
        return convertStringToDate(strDate, STANDARD_DATE);
    }

    /**
     * 获取当前date年月日下一天
     *
     * @return
     */
    public static Date getNextDate() {
        LocalDate localDate = LocalDate.now();
        localDate = localDate.plusDays(1L);
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        Date date = Date.from(instant);
        return date;
    }

    /**
     * 获取当前date年月日几天
     *
     * @return
     */
    public static Date getNextDateFew(Integer days) {
        LocalDate localDate = LocalDate.now();
        localDate = localDate.plusDays(days);
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        Date date = Date.from(instant);
        return date;
    }

    /**
     * 获取未来或者过去时间（字符串格式）
     *
     * @param days   大于0表示未来时间，小于0表示历史时间
     * @param format
     * @return
     */
    public static String getNextDateStr(Integer days, String format) {
        if (days == null) {
            days = 0;
        }
        if (StringUtil.isEmpty(format)) {
            format = STANDARD_DATE;
        }

        return getFormatStrForDate(getNextDateFew(days), format);
    }

    /**
     * 指定local date 获取date
     *
     * @param localDate
     * @return
     */
    public static Date convertLocalDateToDate(LocalDate localDate) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        Date date = Date.from(instant);
        return date;
    }

    public static String convertLocalDateToDateString(LocalDateTime localDate, String zoneName, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atZone(zone).toInstant();
        Date date = Date.from(instant);

        sdf.setTimeZone(TimeZone.getTimeZone(zoneName));
        String format1 = sdf.format(date);
        return format1;
    }

    /**
     * 计算第一个日期相比第二个日期的day差值，
     *
     * @param date1 第一个日期
     * @param date2 第二个日期
     * @return
     */
    public static Long getDaysDiffByTwoDate(String date1, String date2) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(STANDARD_DATE);
        LocalDate d1 = LocalDate.parse(date1, fmt);
        LocalDate d2 = LocalDate.parse(date2, fmt);
        return ChronoUnit.DAYS.between(d2, d1);
    }

    /**
     * 计算 date 减 daysDiff 后的结果
     *
     * @param date
     * @param daysDiff
     * @return
     */
    public static String getStrDateByDaysDiff(String date, Long daysDiff) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(STANDARD_DATE);
        LocalDate d1 = LocalDate.parse(date, fmt);
        LocalDate plusResult = d1.plusDays(-daysDiff);
        return plusResult.toString();
    }

    public static String convertLocalDateToDateString(LocalDate localDate) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(STANDARD_DATE);
        String dateStr = localDate.format(fmt);
        return dateStr;
    }

    public static String dateToStr(Date date) {
        return dateToStr(date, STANDARD_DATE);
    }

    public static String dateToStr(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        String format1 = sdf.format(date);
        return format1;
    }

    public static String dateToStrWithLocale(Date date, String format, Locale locale) {
        SimpleDateFormat sdf = new SimpleDateFormat(format, locale);
        String format1 = sdf.format(date);
        return format1;
    }

    /**
     * 转换时区
     *
     * @param format
     * @param old
     * @param zoneName
     * @return
     */
    public static String convertTimeZone(String format, Date old, String zoneName) {
        if (StringUtils.isEmpty(format)) {
            format = STANDARD_DATE;
        }
        if (StringUtils.isEmpty(zoneName)) {
            zoneName = "America/Los_Angeles";
        }
        if (old == null) {
            return null;
        }

        SimpleDateFormat bjSdf = new SimpleDateFormat(format, Locale.US);
        bjSdf.setTimeZone(TimeZone.getTimeZone(zoneName));
        String StringDate = bjSdf.format(old);
        return StringDate;
    }

    public static boolean testDateIsValid(String appointmentDate) {
        SimpleDateFormat bjSdf = new SimpleDateFormat(STANDARD_DATE, Locale.US);
        if (StringUtils.isEmpty(appointmentDate) || "0000-00-00".equals(appointmentDate)) {
            return false;
        }
        try {
            bjSdf.parse(appointmentDate);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    public static Long getLongTimeByAppointmentDate(String appointmentDate, String zoneName) {
        SimpleDateFormat bjSdf = new SimpleDateFormat(STANDARD_DATE, Locale.US);
        bjSdf.setTimeZone(TimeZone.getTimeZone(zoneName));
        try {
            return bjSdf.parse(appointmentDate).getTime();
        } catch (ParseException e) {
            throw new CommonException(ResponseCodeEnum.SERVER_ERROR, "getTimeByAppointmentDate ParseException");
        }
    }

    /**
     * 时间戳 转化为 标准时间格式
     *
     * @param seconds
     * @param zoneName
     * @return
     */
    public static String convertTimeZoneDateTimeBySeconds(Long seconds, String zoneName) {
        if (StringUtils.isEmpty(zoneName)) {
            zoneName = "America/Los_Angeles";
        }
        Date date = new Date();
        date.setTime(seconds * 1000);

        SimpleDateFormat bjSdf = new SimpleDateFormat(STANDARD_DATE_TIME, Locale.US);
        bjSdf.setTimeZone(TimeZone.getTimeZone(zoneName));

        return bjSdf.format(date);
    }

    public static String convertTimeZoneBySeconds(Long seconds, String zoneName) {
        if (StringUtils.isEmpty(zoneName)) {
            zoneName = "America/Los_Angeles";
        }
        Date date = new Date();
        date.setTime(seconds * 1000);

        SimpleDateFormat bjSdf = new SimpleDateFormat(STANDARD_DATE, Locale.US);
        bjSdf.setTimeZone(TimeZone.getTimeZone(zoneName));

        return bjSdf.format(date);
    }

    /**
     * 把时间戳转换为时间，不包含日期
     *
     * @param seconds
     * @param zoneName
     * @return
     */
    public static String convertTimeBySeconds(Long seconds, String zoneName, Byte formatType) {
        if (StringUtils.isEmpty(zoneName)) {
            zoneName = "America/Los_Angeles";
        }
        Date date = new Date();
        date.setTime(seconds * 1000);

        SimpleDateFormat bjSdf = new SimpleDateFormat(
                BusinessConst.TIME_12_HOURS_TYPE.equals(formatType) ? "hh:mm a" : "HH:mm", Locale.US);
        bjSdf.setTimeZone(TimeZone.getTimeZone(zoneName));

        String StringDate = bjSdf.format(date);
        return StringDate;
    }

    /**
     * 把时间戳转换为日期，不包含时间
     *
     * @param seconds
     * @param zoneName
     * @return
     */
    public static String convertDateBySeconds(Long seconds, String zoneName, String dateFormat) {
        if (StringUtils.isEmpty(zoneName)) {
            zoneName = "America/Los_Angeles";
        }
        Date date = new Date();
        date.setTime(seconds * 1000);
        SimpleDateFormat outPutFormat = new SimpleDateFormat(
                getDateFormatForBusiness(StringUtils.hasText(dateFormat) ? dateFormat : STANDARD_DATE));
        outPutFormat.setTimeZone(TimeZone.getTimeZone(zoneName));
        return outPutFormat.format(date);
    }

    /**
     * 把秒数转换为日期，使用java 8的日期函数加上一个月，再转换为秒数
     *
     * @return
     */
    public static Long getNextMonthTimeStamp() {
        Long timeStamp = get10Timestamp();
        return getNextMonthTimeStamp(timeStamp);
    }

    /**
     * 把秒数转换为日期，使用java 8的日期函数加上一个月，再转换为秒数
     *
     * @param timeStamp
     * @return
     */
    public static Long getNextMonthTimeStamp(Long timeStamp) {
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(timeStamp, 0, ZoneOffset.UTC);
        return dateTime.plusMonths(1).atZone(ZoneOffset.UTC).toInstant().toEpochMilli() / 1000;
    }

    /**
     * include start, not end.
     * e.g. start 2020-11-10, end 2020-11-20 => get 10
     */
    public static int countDaysBetween(String startDate, String endDate) {
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);

        return (int) ChronoUnit.DAYS.between(start, end);
    }

    /**
     * 获取传入时间的当天结束时间
     *
     * @param timestamp 传空获取当天的结束时间
     * @param zoneName  时区
     * @return
     */
    public static Long getDateEndTimestamp(Long timestamp, String zoneName) {
        Calendar calendar = Calendar.getInstance();
        if (timestamp != null) {
            calendar.setTimeInMillis(timestamp * 1000);
        }
        if (StringUtils.isEmpty(zoneName)) {
            zoneName = "America/Los_Angeles";
        }
        calendar.setTimeZone(TimeZone.getTimeZone(zoneName));
        calendar.set(
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH),
                23,
                59,
                59);
        Date endOfDate = calendar.getTime();
        return endOfDate.getTime() / 1000;
    }

    public static Integer getMinsByHourMins(Integer hour, Integer mins) {
        return hour * 60 + mins;
    }

    public static String getStringDateByDateMins(String appointmentDate, Integer mins) {
        String hourStr = String.valueOf(mins / 60);
        if (hourStr.length() == 1) {
            hourStr = "0" + hourStr;
        }
        String minusStr = String.valueOf(mins % 60);
        if (minusStr.length() == 1) {
            minusStr = "0" + minusStr;
        }
        return String.format("%s %s:%s", appointmentDate, hourStr, minusStr);
    }

    public static Integer minute2UpperDay(Integer minutes) {
        if (Objects.isNull(minutes) || minutes <= 0) {
            return 0;
        }
        return Double.valueOf(Math.ceil(Double.valueOf(minutes) / (24 * 60))).intValue();
    }

    /**
     * Get specific zone date timestamp.
     * <p>
     * {@code timestamp("2022-01-01", "Asia/Shanghai")} is equal to {@code timestamp("2022-01-01 00:00:00", "Asia/Shanghai")}
     *
     * @param dateOrDateTime date, format: yyyy-MM-dd or yyyy-MM-dd HH:mm:ss
     * @param timezone       timezone, e.g. Asia/Shanghai
     * @return timestamp
     */
    public static long timestamp(String dateOrDateTime, String timezone) {
        LocalDateTime localDateTime = convertToLocalDateTime(dateOrDateTime);
        return localDateTime.atZone(ZoneId.of(timezone)).toInstant().toEpochMilli();
    }

    public static Integer getMinutesOfDay(LocalDateTime dateTime) {
        return dateTime.getHour() * 60 + dateTime.getMinute();
    }

    private static LocalDateTime convertToLocalDateTime(String dateOrDateTime) {
        String date = convertToDateTime(dateOrDateTime);
        return LocalDateTime.parse(date, formatter);
    }

    private static String convertToDateTime(String dateOrDateTime) {
        String date = dateOrDateTime.trim();
        if (!date.contains(" ")) {
            date += " 00:00:00";
        }
        return date;
    }

    /**
     * yyyy-MM-dd HH:mm:ss -> yyyy-MM-dd
     *
     * @param dateOrDateTimeString
     * @return dateString
     */
    @SuppressFBWarnings("DCN_NULLPOINTER_EXCEPTION")
    public static String convertToDateString(String dateOrDateTimeString) {
        try {
            LocalDateTime localDateTime =
                    LocalDateTime.parse(dateOrDateTimeString.trim(), DateTimeFormatter.ofPattern(STANDARD_DATE_TIME));
            return localDateTime.toLocalDate().toString();
        } catch (NullPointerException e) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "empty date");
        } catch (Exception e) {
            try {
                LocalDate localDate = LocalDate.parse(dateOrDateTimeString.trim());
                return localDate.toString();
            } catch (Exception ex) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "invalid date");
            }
        }
    }

    /**
     * 将 Long 类型的秒级时间戳计算出当天的分钟数
     * input -> 1621846789
     * output -> 539
     *
     * @param timestamp
     * @return
     */
    public static Integer convertTimeStampToMinute(Long timestamp, String timezoneName) {
        // 创建一个 Instant 对象
        Instant instant = Instant.ofEpochSecond(timestamp);
        // 将 Instant 转换为 LocalDateTime
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.of(timezoneName));
        // 获取当天的分钟数
        return dateTime.getHour() * 60 + dateTime.getMinute();
    }

    /**
     *
     * hubspot time convert
     * @param name time zone name
     * @return
     */
    public static String hubspotConvertTimeZone(String name) {
        // 将/ 替换为 "_ slash"
        String convertedName = name.replace("/", "_slash_");
        // 将所有字母转化为小写字母
        convertedName = convertedName.toLowerCase();
        return convertedName;
    }

    /**
     *
     * @param timeZoneName
     * @return
     */
    public static String getReadableTimeZoneName(String timeZoneName) {
        // 获取指定时区的 TimeZone 对象
        TimeZone timeZone = TimeZone.getTimeZone(timeZoneName);
        // 获取指定时区的偏移量（毫秒）
        int offsetInMillis = timeZone.getRawOffset();
        // 将偏移量转换为小时，并向下取整
        int offsetInHours = (int) Math.floor(offsetInMillis / (1000.0 * 60 * 60));
        // Asia/Shanghai(UTC +8)
        return String.format("%s(UTC %s%s)", timeZoneName, (offsetInHours >= 0 ? "+" : "-"), offsetInHours);
    }
}
