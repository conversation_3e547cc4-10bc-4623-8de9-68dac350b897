package com.moego.common;

import lombok.extern.slf4j.Slf4j;

/**
 * @create: 2023/3/10 10:37
 * @author: channy.shu
 **/
@Slf4j
public enum SquarePaymentMethodEnum {
    CARD(1, "Square card"),
    COF(2, "Square cof"),
    TERMINAL(3, "Square terminal"),
    READER(4, "Square reader"),
    POS(5, "Square POS");

    private final Integer code;
    private final String desc;

    SquarePaymentMethodEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String valueOf(Byte code) {
        if (code == null) {
            return "";
        }
        Integer c = Integer.valueOf(code);
        for (SquarePaymentMethodEnum value : SquarePaymentMethodEnum.values()) {
            if (value.getCode().equals(c)) {
                return value.getDesc();
            }
        }
        return "";
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
