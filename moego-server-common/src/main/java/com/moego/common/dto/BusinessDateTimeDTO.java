package com.moego.common.dto;

import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2022/12/6
 */
@Data
@Accessors(chain = true)
public class BusinessDateTimeDTO {
    private Long companyId;

    @Deprecated
    private Integer businessId;

    private LocalDateTime localDateTime;
    private String currentDate;
    private Integer currentMinutes;
    private String timezoneName;
}
