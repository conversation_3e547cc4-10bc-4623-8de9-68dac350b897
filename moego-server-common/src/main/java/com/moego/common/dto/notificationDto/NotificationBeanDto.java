package com.moego.common.dto.notificationDto;

import com.moego.common.utils.GsonUtil;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class NotificationBeanDto {

    private Integer notificationId;
    private String title;
    private String body;
    private String type;
    private Object extra;
    private Long sendTime;
    private Long readTime;
    private Long createTime;
    private Integer businessId;
    private Long companyId;

    public void convertExtraStringToObj() {
        if (extra instanceof String) {
            this.extra = GsonUtil.fromJson((String) extra, getExtraObjByType());
        }
    }

    private Class<?> getExtraObjByType() {
        switch (this.type) {
            case NotificationEnum.TYPE_SYSTEM:
                return String.class;
            default:
                return Object.class;
        }
    }
}
