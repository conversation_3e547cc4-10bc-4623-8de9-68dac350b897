package com.moego.common.dto;

import com.moego.common.enums.CompanyFunctionControlConst;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class NewPricingLevelDto {

    @Schema(description = "付费级别 0 1 2 3")
    private Integer premiumType;

    /**
     * 是否允许发送 two way message
     */
    @Schema(description = "plan version")
    private Integer planVersion;

    public NewPricingLevelDto() {
        /**
         * 免费company 返回的默认值
         */
        this.premiumType = CompanyFunctionControlConst.PREMIUM_TYPE_FREE;
        this.planVersion = CompanyFunctionControlConst.PLAN_VERSION_3;
    }

    public NewPricingLevelDto(Integer premiumType, Integer planVersion) {
        this.premiumType = premiumType;
        this.planVersion = planVersion;
    }
}
