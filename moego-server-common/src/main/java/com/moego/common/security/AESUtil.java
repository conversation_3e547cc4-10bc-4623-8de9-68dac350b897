package com.moego.common.security;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class AESUtil {

    private static final String AES_IV = "E1D76150EA84XEN1";
    private static final String AES_ALGORITHM = "AES/CBC/PKCS5Padding";

    public static String encode(String key, String src) {
        try {
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
            cipher.init(
                    Cipher.ENCRYPT_MODE, secretKeySpec, new IvParameterSpec(AES_IV.getBytes(StandardCharsets.UTF_8)));
            byte[] cipherText = cipher.doFinal(src.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(cipherText);
        } catch (Exception e) {
            throw new RuntimeException("Failed encrypting: " + src, e);
        }
    }

    public static String decode(String key, String src) {
        try {
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
            cipher.init(
                    Cipher.DECRYPT_MODE, secretKeySpec, new IvParameterSpec(AES_IV.getBytes(StandardCharsets.UTF_8)));
            byte[] plainText = cipher.doFinal(Base64.getDecoder().decode(src));
            return new String(plainText, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("Failed decrypting: " + src, e);
        }
    }
}
