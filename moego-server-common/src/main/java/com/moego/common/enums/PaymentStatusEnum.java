package com.moego.common.enums;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

/**
 * <AUTHOR>
 */
public interface PaymentStatusEnum {
    Byte CREATED = 0;
    Byte PROCESSING = 1;
    Byte PAID = 2;
    Byte COMPLETED = 3;
    Byte FAILED = -1;
    // 只在refund使用
    Byte INIT = -2;

    String CREATED_STR = "created";
    String PROCESSING_STR = "processing";
    String PAID_STR = "paid";
    String COMPLETED_STR = "completed";
    String FAILED_STR = "failed";

    @SuppressFBWarnings("MS_MUTABLE_ARRAY")
    String[] STRIPE_ALLOW_CURRENCY = {"USD", "CAD", "AUD", "GBP"};
}
