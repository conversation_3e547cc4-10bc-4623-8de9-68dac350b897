package com.moego.common.enums;

/**
 * <AUTHOR>
 * @since 2021/4/16 2:48 PM
 * please use moego/models/payment/v1/payment_method_enums.proto:10  instead
 */
@Deprecated
public interface PaymentStripeStatus {
    // "1 - card, 2 - card on file, 3- bluetooth reader, 4 - smart reader, 5 - apple pay, 6 - google pay"
    Byte CARD_PAY = 1;
    Byte COF_PAY = 2;
    Byte BLUETOOTH_PAY = 3;
    Byte SMART_READER_PAY = 4;
    Byte APPLE_PAY = 5;
    Byte GOOGLE_PAY = 6;
    Byte TAP_TO_PAY_ON_IPHONE = 7;

    static boolean isStripeTerminal(Byte paymentMethodType) {
        return (BLUETOOTH_PAY.equals(paymentMethodType)
                || SMART_READER_PAY.equals(paymentMethodType)
                || TAP_TO_PAY_ON_IPHONE.equals(paymentMethodType));
    }

    /**
     * 自定义来源字段，用于区分不同支付渠道做特殊处理
     */
    String SOURCE_FROM_BUSINESS = "Business";

    String SOURCE_FROM_PAY_ONLINE_LINK = "PayOnlineLink";
    String SOURCE_FROM_OB = "OnlineBooking";
    String SOURCE_FROM_PREAUTH = "PREAUTH";

    /**
     * request card link key
     */
    String FIRST_NAME = "firstName";

    String LAST_NAME = "lastName";
    String EMAIL = "email";
    String PHONE = "phone";
    String SUBMITTED_TIME = "submittedTime";
    /**
     * CARD STATUS
     * @see <a href="https://stripe.com/docs/api/cards/object#card_object-cvc_check"> cvc check of Card Object</a>
     */
    String PASS = "pass";

    String FAIL = "fail";
    String UNAVAILABLE = "unavailable";
    String UNCHECKED = "unchecked";

    /**
     * SUBSCRIPTION STATUS
     * @see <a href="https://stripe.com/docs/api/subscriptions/object#subscription_object-status">subscription status</a>
     */
    String ACTIVE = "active";

    String INCOMPLETE = "incomplete";

    /**
     * stripe signature header
     */
    String SIG_HEADER = "Stripe-Signature";

    /**
     * square header
     * @see <a href="https://developer.squareup.com/docs/webhooks-api/validate-notifications"> validate square webhook</a>
     */
    String SQUARE_SIG_HEADER = "X-Square-Signature";

    String SQUARE_ENV = "Square-Environment";
    String SQUARE_NOTIFY_INIT_TIME = "Square-Initial-Delivery-Timestamp";

    /**
     * Payment method prefix pm_1IzeDBIZwcIFVLGrbLfyFPcE
     */
    String PM_PREFIX = "pm_";

    /**
     * stripe card token prefix  tok_1IzbseIZwcIFVLGrSlgETCY0
     */
    String TOKEN_PREFIX = "tok_";

    String CUSTOMER_PREFIX = "cus_";
    String SQUARE_COF_PREFIX = "ccof:";
    String CARD_PREFIX = "card_";
    String PAYMENT_METHOD_PREFIX = "pm_";

    /**
     * payment_intent meta data key
     */
    String CUSTOMER_KEY = "customer_id";

    String PRIMARY_ID_KEY = "primary_id";
    String BUSINESS_KEY = "business_id";
    String INVOICE_KEY = "invoice_id";
    String TIPS_KEY = "TIPS_AMOUNT";
    String SAVE_CARD = "save_card";
    String SOURCE_KEY = "source";
    String BOOKING_FEE_KEY = "booking_fee";
    String SEPARATE_CHARGE_APPLICATION_FEE = "separate_charge_application_fee";
    String SEPARATE_CHARGE_FLAG = "separate_charge";

    String DEBIT_CARD_AUTO_CANCEL_KEY = "auto_cancel_for_debit";
    String PARTIAL_CAPTURE_AMOUNT_KEY = "partial_cap_amount";
    String CONVENIENCE_FEE_FLAG_KEY = "convenience_fee_flag";
    String CONVENIENCE_FEE_KEY = "convenience_fee_amount";
    /**
     * stripe customer meta data key
     */
    Long DEFAULT_EXPIRE_SECONDS = 25 * 3600L;

    String COF_TIME_STAMP_KEY = "cofTimeStamp";
    String COF_EXPIRE_TIME_STAMP_KEY = "cofTimeStampExpireAt";
    String COF_SEND_TIME_STAMP_KEY = "cofTimeStampSendAt";
    String COF_SUBMITTED_KEY = "submitted";
    String META_ACCOUNT_ID_KEY = "stripeAccountId";
    String STRIPE_CUSTOMER_TYPE_PLATFORM = "platform";
    String STRIPE_CUSTOMER_TYPE_CONNECT = "customer";
    Integer SUBMIT_VALID = 1;
    Integer SUBMIT_SUBMITTED = 2;
    Integer SUBMIT_EXPIRED = 3;
    /**
     * stripe card authentication by pre auth
     */
    String AUTH_STATUS_KEY = "authStatus";

    Byte AUTH_NOT_YET = 0;
    Byte AUTH_SUCCEED = 1;
    Byte AUTH_FAILED = -1;

    /**
     * stripe event name
     */
    String PAYMENT_CANCEL = "payment_intent.canceled";

    String PAYMENT_FAILED = "payment_intent.failed";
    String DISPUTE_CREATED = "charge.dispute.created";
    /**
     * processing fee in event
     */
    String SQUARE_FEE_KEY = "processing_fee";

    String SQUARE_TIPS_KEY = "tip_money";
    /**
     * payment_intent:
     * {
     *     "id": "evt_3JWzGERP5vFOYSS70sZU5jsQ",
     *     "object": "event",
     *     "account": "acct_1HrfjmRP5vFOYSS7",
     *     "api_version": "2020-08-27",
     *     "created": **********,
     *     "data": {
     *         "object": {
     *             "id": "pi_3JWzGERP5vFOYSS704Ij57cu",
     *             "object": "payment_intent",
     *             "amount": 10250,
     *             "amount_capturable": 0,
     *             "amount_received": 10250,
     *             "application": "ca_AfNDxml35CwuUaoTb99nGTJIzg8yFNF9",
     *             "application_fee_amount": 52,
     *             "canceled_at": null,
     *             "cancellation_reason": null,
     *             "capture_method": "automatic",
     *             "charges": {
     *                 "object": "list",
     *                 "data": [
     *                     {
     *                         "id": "ch_3JWzGERP5vFOYSS70LwcupoS",
     *                         "object": "charge",
     *                         "amount": 10250,
     *                         "amount_captured": 10250,
     *                         "amount_refunded": 0,
     *                         "application": "ca_AfNDxml35CwuUaoTb99nGTJIzg8yFNF9",
     *                         "application_fee": "fee_1JWzGGRP5vFOYSS7zKhNN5g0",
     *                         "application_fee_amount": 52,
     *                         "balance_transaction": "txn_3JWzGERP5vFOYSS703nnbWxm",
     *                         "billing_details": {
     *                             "address": {
     *                                 "city": null,
     *                                 "country": null,
     *                                 "line1": null,
     *                                 "line2": null,
     *                                 "postal_code": "11111",
     *                                 "state": null
     *                             },
     *                             "email": null,
     *                             "name": null,
     *                             "phone": null
     *                         },
     *                         "calculated_statement_descriptor": "TEST STRIPE",
     *                         "captured": true,
     *                         "created": 1631001751,
     *                         "currency": "usd",
     *                         "customer": "cus_KB1BfvO24S8Ofs",
     *                         "description": "TEST STRIPE",
     *                         "destination": null,
     *                         "dispute": null,
     *                         "disputed": false,
     *                         "failure_code": null,
     *                         "failure_message": null,
     *                         "fraud_details": {},
     *                         "invoice": null,
     *                         "livemode": false,
     *                         "metadata": {},
     *                         "on_behalf_of": null,
     *                         "order": null,
     *                         "outcome": {
     *                             "network_status": "approved_by_network",
     *                             "reason": null,
     *                             "risk_level": "normal",
     *                             "risk_score": 14,
     *                             "seller_message": "Payment complete.",
     *                             "type": "authorized"
     *                         },
     *                         "paid": true,
     *                         "payment_intent": "pi_3JWzGERP5vFOYSS704Ij57cu",
     *                         "payment_method": "pm_1JWzGARP5vFOYSS7tOOxDKU1",
     *                         "payment_method_details": {
     *                             "card": {
     *                                 "brand": "visa",
     *                                 "checks": {
     *                                     "address_line1_check": null,
     *                                     "address_postal_code_check": "pass",
     *                                     "cvc_check": "pass"
     *                                 },
     *                                 "country": "US",
     *                                 "exp_month": 11,
     *                                 "exp_year": 2022,
     *                                 "fingerprint": "QQHAA225cOKKC7yE",
     *                                 "funding": "credit",
     *                                 "installments": null,
     *                                 "last4": "4242",
     *                                 "network": "visa",
     *                                 "three_d_secure": null,
     *                                 "wallet": null
     *                             },
     *                             "type": "card"
     *                         },
     *                         "receipt_email": null,
     *                         "receipt_number": null,
     *                         "receipt_url": "https://pay.stripe.com/receipts/acct_1HrfjmRP5vFOYSS7/ch_3JWzGERP5vFOYSS70LwcupoS/rcpt_KBLyrBpj3mdGOmy5RuH7gmyklSRxABu",
     *                         "refunded": false,
     *                         "refunds": {
     *                             "object": "list",
     *                             "data": [],
     *                             "has_more": false,
     *                             "total_count": 0,
     *                             "url": "/v1/charges/ch_3JWzGERP5vFOYSS70LwcupoS/refunds"
     *                         },
     *                         "review": null,
     *                         "shipping": null,
     *                         "source": null,
     *                         "source_transfer": null,
     *                         "statement_descriptor": "TEST STRIPE",
     *                         "statement_descriptor_suffix": null,
     *                         "status": "succeeded",
     *                         "transfer_data": null,
     *                         "transfer_group": null
     *                     }
     *                 ],
     *                 "has_more": false,
     *                 "total_count": 1,
     *                 "url": "/v1/charges?payment_intent=pi_3JWzGERP5vFOYSS704Ij57cu"
     *             },
     *             "client_secret": "pi_3JWzGERP5vFOYSS704Ij57cu_secret_IkwegmkNG1fYpXSzpteKBSyrf",
     *             "confirmation_method": "automatic",
     *             "created": 1631001750,
     *             "currency": "usd",
     *             "customer": "cus_KB1BfvO24S8Ofs",
     *             "description": "TEST STRIPE",
     *             "invoice": null,
     *             "last_payment_error": null,
     *             "livemode": false,
     *             "metadata": {},
     *             "next_action": null,
     *             "on_behalf_of": null,
     *             "payment_method": "pm_1JWzGARP5vFOYSS7tOOxDKU1",
     *             "payment_method_options": {
     *                 "card": {
     *                     "installments": null,
     *                     "network": null,
     *                     "request_three_d_secure": "automatic"
     *                 }
     *             },
     *             "payment_method_types": [
     *                 "card"
     *             ],
     *             "receipt_email": null,
     *             "review": null,
     *             "setup_future_usage": null,
     *             "shipping": null,
     *             "source": null,
     *             "statement_descriptor": "TEST STRIPE",
     *             "statement_descriptor_suffix": null,
     *             "status": "succeeded",
     *             "transfer_data": null,
     *             "transfer_group": null
     *         }
     *     },
     *     "livemode": false,
     *     "pending_webhooks": 2,
     *     "request": {
     *         "id": "req_d1I1chgqbYAi0t",
     *         "idempotency_key": null
     *     },
     *     "type": "payment_intent.succeeded"
     * }
     */

    /**  charge.succeed:
     *
     * {
     *     "id": "evt_3JWzGERP5vFOYSS70Z9EjGG8",
     *     "object": "event",
     *     "account": "acct_1HrfjmRP5vFOYSS7",
     *     "api_version": "2020-08-27",
     *     "created": **********,
     *     "data": {
     *         "object": {
     *             "id": "ch_3JWzGERP5vFOYSS70LwcupoS",
     *             "object": "charge",
     *             "amount": 10250,
     *             "amount_captured": 10250,
     *             "amount_refunded": 0,
     *             "application": "ca_AfNDxml35CwuUaoTb99nGTJIzg8yFNF9",
     *             "application_fee": "fee_1JWzGGRP5vFOYSS7zKhNN5g0",
     *             "application_fee_amount": 52,
     *             "balance_transaction": "txn_3JWzGERP5vFOYSS703nnbWxm",
     *             "billing_details": {
     *                 "address": {
     *                     "city": null,
     *                     "country": null,
     *                     "line1": null,
     *                     "line2": null,
     *                     "postal_code": "11111",
     *                     "state": null
     *                 },
     *                 "email": null,
     *                 "name": null,
     *                 "phone": null
     *             },
     *             "calculated_statement_descriptor": "TEST STRIPE",
     *             "captured": true,
     *             "created": 1631001751,
     *             "currency": "usd",
     *             "customer": "cus_KB1BfvO24S8Ofs",
     *             "description": "TEST STRIPE",
     *             "destination": null,
     *             "dispute": null,
     *             "disputed": false,
     *             "failure_code": null,
     *             "failure_message": null,
     *             "fraud_details": {},
     *             "invoice": null,
     *             "livemode": false,
     *             "metadata": {},
     *             "on_behalf_of": null,
     *             "order": null,
     *             "outcome": {
     *                 "network_status": "approved_by_network",
     *                 "reason": null,
     *                 "risk_level": "normal",
     *                 "risk_score": 14,
     *                 "seller_message": "Payment complete.",
     *                 "type": "authorized"
     *             },
     *             "paid": true,
     *             "payment_intent": "pi_3JWzGERP5vFOYSS704Ij57cu",
     *             "payment_method": "pm_1JWzGARP5vFOYSS7tOOxDKU1",
     *             "payment_method_details": {
     *                 "card": {
     *                     "brand": "visa",
     *                     "checks": {
     *                         "address_line1_check": null,
     *                         "address_postal_code_check": "pass",
     *                         "cvc_check": "pass"
     *                     },
     *                     "country": "US",
     *                     "exp_month": 11,
     *                     "exp_year": 2022,
     *                     "fingerprint": "QQHAA225cOKKC7yE",
     *                     "funding": "credit",
     *                     "installments": null,
     *                     "last4": "4242",
     *                     "network": "visa",
     *                     "three_d_secure": null,
     *                     "wallet": null
     *                 },
     *                 "type": "card"
     *             },
     *             "receipt_email": null,
     *             "receipt_number": null,
     *             "receipt_url": "https://pay.stripe.com/receipts/acct_1HrfjmRP5vFOYSS7/ch_3JWzGERP5vFOYSS70LwcupoS/rcpt_KBLyrBpj3mdGOmy5RuH7gmyklSRxABu",
     *             "refunded": false,
     *             "refunds": {
     *                 "object": "list",
     *                 "data": [],
     *                 "has_more": false,
     *                 "total_count": 0,
     *                 "url": "/v1/charges/ch_3JWzGERP5vFOYSS70LwcupoS/refunds"
     *             },
     *             "review": null,
     *             "shipping": null,
     *             "source": null,
     *             "source_transfer": null,
     *             "statement_descriptor": "TEST STRIPE",
     *             "statement_descriptor_suffix": null,
     *             "status": "succeeded",
     *             "transfer_data": null,
     *             "transfer_group": null
     *         }
     *     },
     *     "livemode": false,
     *     "pending_webhooks": 2,
     *     "request": {
     *         "id": "req_d1I1chgqbYAi0t",
     *         "idempotency_key": null
     *     },
     *     "type": "charge.succeeded"
     * }
     *
     * balance tx:
     *
     * {
     *     "id": "txn_3JWzGERP5vFOYSS703nnbWxm",
     *     "object": "balance_transaction",
     *     "amount": 10250,
     *     "available_on": 1631145600,
     *     "created": 1631001751,
     *     "currency": "usd",
     *     "description": "TEST STRIPE",
     *     "exchange_rate": null,
     *     "fee": 379,
     *     "fee_details": [
     *         {
     *             "amount": 327,
     *             "application": null,
     *             "currency": "usd",
     *             "description": "Stripe processing fees",
     *             "type": "stripe_fee"
     *         },
     *         {
     *             "amount": 52,
     *             "application": "ca_AfNDxml35CwuUaoTb99nGTJIzg8yFNF9",
     *             "currency": "usd",
     *             "description": "MoeGo for Pet Business application fee",
     *             "type": "application_fee"
     *         }
     *     ],
     *     "net": 9871,
     *     "reporting_category": "charge",
     *     "source": "ch_3JWzGERP5vFOYSS70LwcupoS",
     *     "status": "available",
     *     "type": "charge"
     * }
     */
}
