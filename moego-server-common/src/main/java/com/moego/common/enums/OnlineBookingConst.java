package com.moego.common.enums;

public interface OnlineBookingConst {
    byte AVAILABLE_TIME_TYPE_WORKING_HOUR = 0;
    byte AVAILABLE_TIME_TYPE_BY_SLOT = 1;
    byte AVAILABLE_TIME_TYPE_DISABLE = 2;
    byte SYNC_WITH_WORKING_HOUR_DISABLE = 0;
    byte SYNC_WITH_WORKING_HOUR_ENABLE = 1;

    byte STAFF_AVAILABLE_DISABLE = 0;
    byte STAFF_AVAILABLE_ENABLE = 1;

    // only show applicable service开关
    byte SERVICE_FILTER_DISABLE = 0;
    byte SERVICE_FILTER_ENABLE = 1;

    byte VERSION_TWO = 2;

    // no show protection类型
    Byte NO_SHOW_PROTECTION_DISABLE = 0;
    Byte NO_SHOW_PROTECTION_CARD_ON_FILE = 1;
    Byte NO_SHOW_PROTECTION_PREPAY = 2;
    Byte NO_SHOW_PROTECTION_PREAUTH = 3;

    // prepay类型
    Byte PREPAY_TYPE_FULL_PAY = 0;
    Byte PREPAY_TYPE_DEPOSIT = 1;

    String PREPAY_GUID_PREFIX = "ob:prepay:";
    String ONLINE_BOOKING_PAYMENT_SETTING = "ob:payment:client:setting:%s:%s";

    // deposit类型
    Byte DEPOSIT_TYPE_BY_FIXED_AMOUNT = 0;
    Byte DEPOSIT_TYPE_BY_PERCENTAGE = 1;

    // prepay tip开关
    Byte PREPAY_TIP_DISABLE = 0;
    Byte PREPAY_TIP_ENABLE = 1;

    /**
     * 时间间隔类型 Exact times
     */
    Byte TIME_SLOT_FORMAT_EXACT_TIMES = 1;

    /**
     * 时间间隔类型 Arrival window
     */
    Byte TIME_SLOT_FORMAT_ARRIVAL_WINDOW = 2;

    // default icon flag for online booking care type
    String DEFAULT_BOARDING_ICON = "boarding";
    String DEFAULT_DAYCARE_ICON = "daycare";
    String DEFAULT_GROOMING_ICON = "grooming";
    String DEFAULT_DOG_WALKING_ICON = "dogWalking";
    String DEFAULT_GROUP_CLASS_ICON = "training";

    // default image url for online booking care type
    String DEFAULT_BOARDING_IMAGE =
            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1751539652af046a13ca434796995dffa243b188f7.png?name=boarding-illustration.png";
    String DEFAULT_DAYCARE_IMAGE =
            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1751254272bdd6529c11ea4c5d9cb7e49daba57c8e.png?name=daycare-illustration.png";
    String DEFAULT_GROOMING_IMAGE =
            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/17512542846fa7d486966f446dbc898b53c1bb6cc9.png?name=grooming-illustration.png";
    String DEFAULT_DOG_WALKING_IMAGE =
            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1751254534c398cc85685f4e42aa23e3f013067c11.png?name=walking-illustration.png";
    String DEFAULT_GROUP_CLASS_IMAGE =
            "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1751254345bee561e2d37642ec8434c3ee8d526a58.png?name=training-illustration.png";

    // default description for online booking care type
    String DEFAULT_BOARDING_DESCRIPTION = "Offers temporary accommodation";
    String DEFAULT_DAYCARE_DESCRIPTION = "Provides daytime care and play";
    String DEFAULT_GROOMING_DESCRIPTION = "Enhancing hygiene and appearance";
    String DEFAULT_GROUP_CLASS_DESCRIPTION = "Learn alongside other dogs and pet parents in your community";
}
