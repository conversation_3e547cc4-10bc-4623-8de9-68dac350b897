package com.moego.common.enums;

import lombok.Getter;

/**
 * business sourceFrom Enum
 */
@Getter
public enum BusinessSourceFromEnum {
    APP_ANDROID(1, "android app"),
    APP_IOS(2, "ios app"),
    WEB_ANDROID(3, "android web"),
    WEB_IOS(4, "ios web"),
    WEB_DESKTOP(5, "desktop web");

    private final int type;
    private final String desc;

    BusinessSourceFromEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 通过type获取desc
     * @param type
     * @return
     */
    public static String getDescByType(int type) {
        for (BusinessSourceFromEnum be : BusinessSourceFromEnum.values()) {
            if (be.getType() == type) {
                return be.getDesc();
            }
        }
        return "unknown";
    }
}
