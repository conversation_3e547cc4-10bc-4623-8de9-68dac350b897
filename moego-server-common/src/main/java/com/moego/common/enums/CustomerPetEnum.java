package com.moego.common.enums;

public interface CustomerPetEnum {
    // life status
    Byte LIFE_STATUS_ALIVE = 1;
    Byte LIFE_STATUS_DIE = 2;
    // gender
    Byte GENDER_MALE = 1;
    Byte GENDER_FEMALE = 2;

    // status
    Byte STATUS_NORMAL = 1;
    Byte STATUS_DELETE = 2;

    // moe_grooming_customer_service save type
    Byte SERVICE_SAVE_TYPE_TIME = 2;
    Byte SERVICE_SAVE_TYPE_PRICE = 1;

    // vaccine source type
    Byte VACCINE_SOURCE_TYPE_BUSINESS = 1;
    Byte VACCINE_SOURCE_TYPE_BOOK_ONLINE = 2;

    Byte EVALUATION_STATUS_PASS = 1;
    Byte EVALUATION_STATUS_FAIL = 2;
}
