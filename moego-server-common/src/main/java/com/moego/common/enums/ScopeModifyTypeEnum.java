package com.moego.common.enums;

import lombok.Getter;

public enum ScopeModifyTypeEnum {
    UNKNOWN(0),
    THIS_FUTURE(1),
    DO_NOT_SAVE(2),
    THIS_FOLLOWING(3),
    ALL_UPCOMING(4);

    @Getter
    private final Integer scopeType;

    ScopeModifyTypeEnum(Integer scopeType) {
        this.scopeType = scopeType;
    }

    public static ScopeModifyTypeEnum fromScopeType(Integer scopeType) {
        for (ScopeModifyTypeEnum scopeModifyTypeEnum : ScopeModifyTypeEnum.values()) {
            if (scopeModifyTypeEnum.getScopeType().equals(scopeType)) {
                return scopeModifyTypeEnum;
            }
        }
        return null;
    }
}
