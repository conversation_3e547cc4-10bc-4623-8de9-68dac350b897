package com.moego.common.enums.order;

public enum LineApplyType {
    TYPE_ALL("all"),
    TYPE_ALL_NO_TIP("all_no_tip"),
    TYPE_SERVICE("service"),
    TYPE_PRODUCT("product"),
    TYPE_PACKAGE("package"),
    TYPE_SERVICE_CHARGE("service_charge"),
    TYPE_ITEM("item"),
    TYPE_NONE("none");

    private final String type;

    LineApplyType(String type) {
        this.type = type;
    }

    public String getType() {
        return this.type;
    }
}
