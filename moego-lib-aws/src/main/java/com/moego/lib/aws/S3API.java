package com.moego.lib.aws;

import com.moego.lib.aws.model.S3Context;
import com.moego.lib.utils.PathUtils;
import com.moego.lib.utils.StringUtils;
import com.moego.lib.utils.model.Pair;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Path;
import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import lombok.SneakyThrows;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.http.ContentStreamProvider;
import software.amazon.awssdk.http.HttpExecuteRequest;
import software.amazon.awssdk.http.SdkHttpClient;
import software.amazon.awssdk.http.SdkHttpMethod;
import software.amazon.awssdk.http.SdkHttpRequest;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.Bucket;
import software.amazon.awssdk.services.s3.model.Delete;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.DeleteObjectsRequest;
import software.amazon.awssdk.services.s3.model.DeletedObject;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectRetentionRequest;
import software.amazon.awssdk.services.s3.model.GetObjectTaggingRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectResponse;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request;
import software.amazon.awssdk.services.s3.model.ObjectIdentifier;
import software.amazon.awssdk.services.s3.model.ObjectLockRetention;
import software.amazon.awssdk.services.s3.model.S3Exception;
import software.amazon.awssdk.services.s3.model.S3Object;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;
import software.amazon.awssdk.utils.IoUtils;

public class S3API implements AutoCloseable {

    private final S3Client client;
    private final S3Presigner presigner;
    private static final SdkHttpClient sdkHttpClient = ApacheHttpClient.create();

    public S3API(Region region) {
        this.client = S3Client.builder().region(region).build();
        this.presigner =
                S3Presigner.builder().region(region).s3Client(this.client).build();
    }

    public S3API(Region region, String accessKey, String accessSecret) {
        var credential = AwsBasicCredentials.create(accessKey, accessSecret);
        var provider = StaticCredentialsProvider.create(credential);

        this.client =
                S3Client.builder().region(region).credentialsProvider(provider).build();
        this.presigner = S3Presigner.builder()
                .region(region)
                .credentialsProvider(provider)
                .s3Client(this.client)
                .build();
    }

    // list buckets
    public List<String> listBuckets() {
        var response = this.client.listBuckets();
        return response.buckets().stream().map(Bucket::name).toList();
    }

    // find bucket by name
    public Bucket findBucket(String name) {
        var response = this.client.listBuckets();
        return response.buckets().stream()
                .filter(bucket -> bucket.name().equals(name))
                .findFirst()
                .orElse(null);
    }

    // list objects
    public Pair<String, List<S3Object>> listObjects(String bucketName, String prefix, String next, Integer limit) {
        var builder = ListObjectsV2Request.builder();
        builder.bucket(bucketName);
        if (!StringUtils.isBlank(prefix)) {
            builder.prefix(prefix);
        }
        if (!StringUtils.isBlank(next)) {
            builder.continuationToken(next);
        }
        if (limit != null && 0 < limit) {
            builder.maxKeys(limit);
        }
        var response = this.client.listObjectsV2(builder.build());
        return Pair.of(response.nextContinuationToken(), response.contents());
    }

    // foreach objects
    public int foreachObjects(String bucketName, String prefix, Consumer<S3Object> consumer) {
        int count = 0;
        final int pageSize = 100;
        for (String next = null; ; ) {
            var builder = ListObjectsV2Request.builder();
            builder.bucket(bucketName);
            if (!StringUtils.isBlank(prefix)) {
                builder.prefix(prefix);
            }
            if (!StringUtils.isBlank(next)) {
                builder.continuationToken(next);
            }
            builder.maxKeys(pageSize);
            var response = this.client.listObjectsV2(builder.build());
            int n = response.contents().size();
            if (0 < n) {
                response.contents().forEach(consumer);
                count += n;
            }

            if (StringUtils.isBlank(response.nextContinuationToken()) || n < pageSize) {
                break;
            }

            next = response.nextContinuationToken();
        }

        return count;
    }

    // count objects
    public int countObject(String bucketName, String prefix) {
        int count = 0;
        final int pageSize = 100;
        for (String next = null; ; ) {
            var builder = ListObjectsV2Request.builder();
            builder.bucket(bucketName);
            builder.maxKeys(pageSize);
            if (!StringUtils.isBlank(prefix)) {
                builder.prefix(prefix);
            }
            if (!StringUtils.isBlank(next)) {
                builder.continuationToken(next);
            }
            var response = this.client.listObjectsV2(builder.build());
            int n = response.contents().size();
            count += n;
            if (!response.hasContents() || n < pageSize) {
                break;
            }

            next = response.nextContinuationToken();
        }

        return count;
    }

    // is existing object?
    public boolean isExistingObject(String bucketName, String key) {
        try {
            var response = headObject(bucketName, key);
            return response != null && !StringUtils.isBlank(response.eTag());
        } catch (S3Exception ignored) {
            return false;
        }
    }

    // get object metadata
    public HeadObjectResponse headObject(String bucketName, String key) {
        var request = HeadObjectRequest.builder().bucket(bucketName).key(key).build();
        return this.client.headObject(request);
    }

    // download object
    public S3Context download(String bucketName, String key) {
        var request = GetObjectRequest.builder().bucket(bucketName).key(key).build();
        var response = this.client.getObjectAsBytes(request);
        return S3Context.from(response.asByteArray(), response.response());
    }

    // download object to file
    public S3Context download(String bucketName, String key, Path path) {
        var request = GetObjectRequest.builder().bucket(bucketName).key(key).build();
        var response = this.client.getObject(request, path);
        return S3Context.from(null, response);
    }

    // upload object
    public String upload(String bucketName, String key, S3Context ctx) {
        var request = S3Context.toPutObjectRequest(bucketName, key, ctx);
        var body = S3Context.toRequestBody(ctx);
        var response = this.client.putObject(request, body);
        return S3Context.processETag(response.eTag());
    }

    // delete object
    public boolean deleteObject(String bucketName, String key) {
        try {
            var request =
                    DeleteObjectRequest.builder().bucket(bucketName).key(key).build();
            var response = this.client.deleteObject(request);
            return response != null;
        } catch (Exception ignored) {
            return false;
        }
    }

    // delete objects
    public List<String> deleteObjects(String bucketName, String... keys) {
        var objIds = Arrays.stream(keys)
                .map(key -> ObjectIdentifier.builder().key(key).build())
                .toList();
        var delete = Delete.builder().objects(objIds).quiet(false).build();
        var request =
                DeleteObjectsRequest.builder().bucket(bucketName).delete(delete).build();
        var response = this.client.deleteObjects(request);
        return response.deleted().stream().map(DeletedObject::key).toList();
    }

    // get presigned url for upload
    public String urlForUpload(String bucketName, String key, Duration duration, S3Context ctx) {
        var objectRequest = S3Context.toPutObjectRequest(bucketName, key, ctx);
        var presignRequest = PutObjectPresignRequest.builder()
                .signatureDuration(duration)
                .putObjectRequest(objectRequest)
                .build();

        var request = this.presigner.presignPutObject(presignRequest);
        return request.url().toExternalForm();
    }

    // get presigned url for download
    public String urlForDownload(String bucketName, String key, Duration duration) {
        var objectRequest =
                GetObjectRequest.builder().bucket(bucketName).key(key).build();
        var presignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(duration)
                .getObjectRequest(objectRequest)
                .build();

        var request = this.presigner.presignGetObject(presignRequest);
        return request.url().toExternalForm();
    }

    // get object tagging
    public Map<String, String> getObjectTagging(String bucketName, String key, String versionId) {
        Map<String, String> tags = new HashMap<>();
        var builder = GetObjectTaggingRequest.builder();
        builder.bucket(bucketName);
        builder.key(key);
        if (!StringUtils.isBlank(versionId)) {
            builder.versionId(versionId);
        }
        var response = this.client.getObjectTagging(builder.build());
        if (response.hasTagSet()) {
            response.tagSet().forEach(tag -> tags.put(tag.key(), tag.value()));
        }
        return tags;
    }

    // get object retention
    public ObjectLockRetention getObjectRetention(String bucketName, String key, String versionId) {
        var builder = GetObjectRetentionRequest.builder();
        builder.bucket(bucketName);
        builder.key(key);
        if (!StringUtils.isBlank(versionId)) {
            builder.versionId(versionId);
        }
        var response = this.client.getObjectRetention(builder.build());
        return response.retention();
    }

    @Override
    public void close() throws Exception {
        this.client.close();
    }

    @SneakyThrows
    public static boolean uploadByURL(String url, byte[] data, Map<String, String> headers) {
        var builder = SdkHttpRequest.builder();
        builder.method(SdkHttpMethod.PUT);
        builder.uri(new URL(url).toURI());
        headers.forEach(builder::putHeader);
        HttpExecuteRequest request = HttpExecuteRequest.builder()
                .request(builder.build())
                .contentStreamProvider(ContentStreamProvider.fromByteArray(data))
                .build();

        var response = sdkHttpClient.prepareRequest(request).call();
        return (response != null && response.httpResponse().isSuccessful());
    }

    @SneakyThrows
    public static boolean downloadByURL(String url, Path path) {
        var outputStream = new ByteArrayOutputStream();
        SdkHttpRequest params = SdkHttpRequest.builder()
                .method(SdkHttpMethod.GET)
                .uri(new URL(url).toURI())
                .build();
        var request = HttpExecuteRequest.builder().request(params).build();
        var response = sdkHttpClient.prepareRequest(request).call();
        if (response != null && response.httpResponse().isSuccessful()) {
            response.responseBody()
                    .ifPresentOrElse(
                            inputStream -> {
                                try {
                                    IoUtils.copy(inputStream, outputStream);
                                } catch (IOException e) {
                                    throw new RuntimeException(e);
                                }
                            },
                            () -> {
                                throw new RuntimeException("download failed");
                            });
            PathUtils.writeFile(path, outputStream.toByteArray());
            return true;
        }

        return false;
    }
}
