{"arn": "arn:aws:mediaconvert:us-west-2:693727988157:presets/System-Imx50_512i_29_97fps", "category": "MXF", "createdAt": 1720828096.0, "description": "MPEG IMX, 720x512, 29.97fps, 50mbps", "lastUpdated": 1720828096.0, "name": "System-Imx50_512i_29_97fps", "settings": {"audioDescriptions": [{"audioType": 0, "audioTypeControl": "FOLLOW_INPUT", "codecSettings": {"codec": "WAV", "wavSettings": {"bitDepth": 24, "channels": 4, "sampleRate": 48000}}, "languageCodeControl": "FOLLOW_INPUT"}], "containerSettings": {"container": "MXF"}, "videoDescription": {"afdSignaling": "NONE", "antiAlias": "ENABLED", "codecSettings": {"codec": "MPEG2", "mpeg2Settings": {"adaptiveQuantization": "LOW", "bitrate": 50000000, "codecLevel": "MAIN", "codecProfile": "PROFILE_422", "framerateControl": "SPECIFIED", "framerateConversionAlgorithm": "DUPLICATE_DROP", "framerateDenominator": 1001, "framerateNumerator": 30000, "gopClosedCadence": 1, "gopSize": 1.0, "gopSizeUnits": "FRAMES", "hrdBufferSize": 0, "interlaceMode": "TOP_FIELD", "intraDcPrecision": "AUTO", "minIInterval": 0, "numberBFramesBetweenReferenceFrames": 0, "parControl": "SPECIFIED", "parDenominator": 11, "parNumerator": 10, "qualityTuningLevel": "SINGLE_PASS", "rateControlMode": "CBR", "sceneChangeDetect": "ENABLED", "slowPal": "DISABLED", "spatialAdaptiveQuantization": "ENABLED", "syntax": "D_10", "telecine": "NONE", "temporalAdaptiveQuantization": "ENABLED"}}, "colorMetadata": "INSERT", "dropFrameTimecode": "ENABLED", "height": 512, "respondToAfd": "NONE", "scalingBehavior": "DEFAULT", "sharpness": 50, "timecodeInsertion": "DISABLED", "width": 720}}, "type": "SYSTEM"}