package com.moego.server.payment.dto;

import com.moego.server.payment.enums.CouponBusinessCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/5/10 7:14 PM
 */
@Data
public class CouponDTO {

    @Schema(description = "coupon code 主键")
    private Integer id;

    @Schema(description = "前端输入的code  重新返回")
    private String code;

    @Schema(description = "对应的stripe coupon id")
    private String stripeCouponId;

    @Schema(description = "打折减掉的比例。 例如：20  减去20%")
    private BigDecimal percentOff;

    @Schema(description = "减掉的固定金额。 例如：5   减去5美元")
    private BigDecimal amountOff;

    @Schema(description = "coupon类型")
    private CouponBusinessCategoryEnum businessCategory;
}
