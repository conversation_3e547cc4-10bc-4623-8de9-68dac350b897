package com.moego.server.payment.dto.refund;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/1/2
 */
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class TransactionHistoryListDTO {
    // views
    private List<TransactionHistoryView> views;

    // total
    private int total;
}
