package com.moego.server.payment.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/2/23 3:23 PM
 */
@Data
@Schema(description = "订阅参数")
public class SubscriptionParams {

    @NotNull
    Integer companyId;

    @Min(1)
    @Schema(description = "admin 使用，期望根据此参数获得的短信条数")
    Integer newBusinessNums;

    @NotNull
    @Schema(description = "locations number")
    Integer locationsNum;

    @NotNull
    @Schema(description = "mobile vans number")
    Integer vansNum;

    @NotNull
    @Schema(description = "stripe plan id, 从/payment/subscription/plans接口下发")
    String stripePlanId;

    @Schema(description = "assigned from backendServer")
    String vanStripePlanId;

    @Schema(description = "moe_price_plan_conf table的主键ID")
    Integer planId;

    Integer accountId;
    String chargeToken;

    @Schema(
            description = "根据客服提供给用户的优惠码， 从/payment/subscription/coupon接口查询stripe coupon code id；"
                    + "根据prefix判断兼容referral功能，referralCode复用该字段")
    String stripeCouponId;

    @Schema(
            description = "Test Clock 是Stripe 提供的用于测试Subscription的时钟，" + "可以修改时间触发billing cycle 更新。"
                    + "详情参考：https://moego.atlassian.net/l/c/E8TPSm11 测试章节")
    Boolean createTestClock;
}
