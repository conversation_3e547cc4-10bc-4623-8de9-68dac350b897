package com.moego.server.payment.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class CompanyPermissionStateDto {

    private Integer id;
    private Integer companyId;
    private String stripeSubscriptionsId;
    private Integer level;
    private Long beginDate;
    private Long expireDate;
    private Integer currentPlanId;
    private Byte autoRenew;
    private Integer nextPlanId;
    private Byte chargeStatus;
    private String chargeMsg;
    private Integer packageMsgNum;
    private Integer packageMsgUsedNum;
    private Integer buyMsgRemainNum;
    private Integer businessNum;
    private Long updateTime;
    private Long createTime;
    private Long chargeFailedTime;
    private Byte additionalStaffNum;
}
