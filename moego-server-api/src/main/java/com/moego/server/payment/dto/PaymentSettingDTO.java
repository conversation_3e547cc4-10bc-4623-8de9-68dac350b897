package com.moego.server.payment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
public class PaymentSettingDTO {

    private Integer businessId;
    private Byte processingFeePayBy;

    @Schema(description = "0: full amount, 1: wave")
    private Byte processingFeeCalMethod;

    private String processingFeeSignature;
    private Long signatureTime;

    @Max(4)
    @Min(0)
    private BigDecimal onlineFeeRate;

    @Max(100)
    @Min(0)
    private Integer onlineFeeCents;

    @Max(4)
    @Min(0)
    private BigDecimal readerFeeRate;

    @Max(100)
    @Min(0)
    private Integer readerFeeCents;

    @Size(max = 100)
    private String customizedFeeName;

    @Schema(description = "1: 跳过tips, 0/null： 保持tips")
    Byte skipTipping;

    @Schema(description = "0: disable, 1: enable")
    Byte cardAuthEnable;

    @Schema(description = "0: disable, 针对debit card仍然扣除， 1: enable,即自动去掉convenience fee")
    Byte autoCancelFeeByClient;

    @Schema(description = "0: no, 1: yes")
    Byte closeCustomRate;

    @Schema(description = "0: not allowed, 1: allowed")
    Byte allowCustomRate;

    @Schema(description = "定制json报文")
    @Size(max = 512, min = 2)
    private String customTipping;

    private Date createTime;
    private Date updateTime;

    @Schema(description = "payment setting primary id")
    private Integer id;

    @Schema(description = "提前多久进行扣款,单位：hour")
    @Min(6)
    @Max(48)
    private Integer preAuthBspd;

    @Schema(description = "首选信用卡支付方式 0 默认; 1 stripe; 2 square")
    @Min(0)
    @Max(2)
    private Byte primaryPayType;

    @Schema(description = "update time of the primaryPayType field")
    private Date primaryPayTypeUpdateTime;
}
