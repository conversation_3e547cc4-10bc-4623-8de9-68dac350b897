package com.moego.server.payment.params;

import com.moego.common.utils.Pagination;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/10/8
 */
@Data
@Builder
public class PlatformSalesQueryParams {
    String code;

    String email;

    String creator;

    Long agreementId;

    String agreementRecordUuid;

    Long accountId;

    Integer status;

    @Builder.Default
    Byte ordered = 0;

    Pagination pagination;

    List<Integer> customRateApprovalStatuses;
}
