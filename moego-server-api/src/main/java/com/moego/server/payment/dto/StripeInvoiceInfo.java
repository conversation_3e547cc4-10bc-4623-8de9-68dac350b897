package com.moego.server.payment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/12/12
 */
@Data
@Builder
public class StripeInvoiceInfo {
    Long amountDue;
    Long created;
    Long periodStart;
    Long periodEnd;
    String accountCountry;

    @Schema(description = "https://stripe.com/docs/api/invoices/object#invoice_object-amount_due")
    String billingReason;

    @Schema(description = "Total after discounts and taxes.")
    Long total;

    String description;
    String hostedInvoiceUrl;
    String paymentIntent;
}
