package com.moego.server.payment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/12/1
 */
@Data
@Builder
public class StripeCouponInfo {
    String id;
    String name;
    Long created;
    String duration;
    Long durationInMonths;
    Boolean livemode;
    Boolean deleted;
    Long maxRedemptions;
    String metadata;
    BigDecimal percentOff;
    Long redeemBy;
    Long timesRedeemed;
    Boolean valid;

    @Schema(description = "coupon 针对subscription 生效时间")
    Long start;

    @Schema(description = "coupon 针对subscription 失效效时间")
    Long end;
}
