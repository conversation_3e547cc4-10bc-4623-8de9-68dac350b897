package com.moego.server.payment.client;

import com.moego.server.payment.api.IPaymentBusinessDisputeService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @since 2023/12/14
 */
@FeignClient(
        value = "moego-payment-server",
        url = "${moego.server.url.payment}",
        contextId = "IPaymentBusinessDisputeService")
public interface IPaymentBusinessDisputeClient extends IPaymentBusinessDisputeService {}
