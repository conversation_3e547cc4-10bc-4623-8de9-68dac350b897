package com.moego.server.payment.dto;

import java.math.BigDecimal;
import java.util.Objects;
import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2022/7/15
 */
@Data
public class StripeCustomerDTO {

    private Integer companyId;

    private String stripeCustomerId;

    private BigDecimal balance;

    private String livemode;

    public static final StripeCustomerDTO NULL_OBJECT = new StripeCustomerDTO();

    public boolean isNull() {
        return (Objects.isNull(companyId)
                && StringUtils.isEmpty(stripeCustomerId)
                && Objects.isNull(balance)
                && StringUtils.isEmpty(livemode));
    }
}
