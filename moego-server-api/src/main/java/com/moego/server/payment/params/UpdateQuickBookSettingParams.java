package com.moego.server.payment.params;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/8/19
 */
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class UpdateQuickBookSettingParams {

    @NotNull
    Integer businessId;

    Byte receiptStatus;

    Integer userVersion;

    Byte taxSyncType;
}
