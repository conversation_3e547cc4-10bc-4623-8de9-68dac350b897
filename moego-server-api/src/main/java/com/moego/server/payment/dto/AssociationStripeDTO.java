package com.moego.server.payment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/6/26
 */
@Data
@Builder(toBuilder = true)
public class AssociationStripeDTO {
    @Schema(description = "stripe account id")
    private String stripeAccountId;

    @Schema(description = "stripe account metadata")
    private Map<String, String> metadata;
}
