package com.moego.server.payment.params.refund;

import com.moego.common.enums.PaymentMethodEnum;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class RefundHistoryListParams {
    private Long businessId;
    private Long refundCreateTimeStart;
    private Long refundCreateTimeEnd;
    /**
     * Use METHOD_* constants defined in PaymentMethodEnum, e.g. METHOD_CREDIT_CARD.
     * @see PaymentMethodEnum
     */
    @Min(1)
    @Max(8)
    private Byte methodId;
}
