package com.moego.server.payment.params;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IntakeFormCustomerStripRequest {

    @NotNull
    Integer businessId;

    @NotNull
    String desc;

    String firstName;
    String lastName;

    String email;

    @NotNull
    String phone;

    @NotNull
    private String chargeToken;
}
