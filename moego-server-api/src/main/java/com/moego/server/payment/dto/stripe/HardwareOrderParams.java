package com.moego.server.payment.dto.stripe;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.moego.common.enums.hardware.BundleSaleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/10/23
 */
@Data
public class HardwareOrderParams {
    @NotNull
    @Min(1)
    Integer companyId;

    @NotEmpty
    @Schema(description = "get all available shipping methods from stripe, ref api: /payment/hardware/shippingMethods")
    String shippingMethod;

    @Schema(
            description = "The purchase order number will appear on the packing slip, shipping label,"
                    + " and monthly billing invoice. No more than 15 characters.")
    @Size(max = 15)
    @JsonIgnoreProperties
    String poNumber;

    @JsonIgnoreProperties
    Long accountId;

    @NotEmpty
    @Size(min = 3, max = 40)
    String companyName;

    @NotEmpty
    @Schema(description = "receiver name")
    @Size(min = 3, max = 15)
    String recipientName;

    @NotEmpty
    @Email
    String email;

    @NotEmpty
    @Size(min = 10, max = 14)
    String phoneNumber;

    @Schema(
            description =
                    "The shipping address for the order. Required if any of the SKUs are physical goods. "
                            + "ref: https://stripe.com/docs/api/terminal/hardware_orders/preview#preview_terminal_hardware_order-shipping-address")
    @NotNull
    @Valid
    Address address;

    @NotNull
    @Min(0)
    @Schema(description = "stripe_m2/bluetooth reader 购买个数")
    Integer m2Quantity;

    @NotNull
    @Min(0)
    @Schema(description = "bbpos_wisepos_e/smart reader 购买个数")
    Integer smartReaderQuantity;

    @Schema(description = "如果为null, 不做校验; 非空字符串校验合法性, 目前固定值")
    String hardwareCouponCode;

    @Schema(description = "bundle sale type: 目前有UNIQUE_LINK， SALE")
    BundleSaleEnum bundleSaleType;

    @Schema(description = "bundle sale type 非 SALE 时需要填写")
    String bundleSaleUuid;

    @Schema(description = "Stripe token，未加卡商家仅购买硬件时使用")
    String chargeToken;

    @Data
    public static class Address {
        @NotEmpty
        @Size(max = 40)
        String line1;

        @NotEmpty
        @Size(max = 10)
        String postalCode;

        @Schema(description = "目前固定为US, ref https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2")
        @NotEmpty
        @Size(max = 2)
        String country;

        @Size(max = 35)
        String city;

        @Size(max = 29)
        String state;

        @Schema(description = "optional property")
        @Size(max = 40)
        String line2;
    }
}
