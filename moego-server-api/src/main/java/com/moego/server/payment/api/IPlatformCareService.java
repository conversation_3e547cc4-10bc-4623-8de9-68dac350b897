package com.moego.server.payment.api;

import com.moego.server.payment.dto.PlatformCareRecordDTO;
import com.moego.server.payment.params.CreatePlatformCareMailLinkParams;
import com.moego.server.payment.params.PlatformCareQueryParams;
import java.util.Map;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2023/10/23
 */
public interface IPlatformCareService {
    @PostMapping("/service/payment/platform/care/link")
    String createPlatformCareMailLink(@RequestBody @Validated CreatePlatformCareMailLinkParams params);

    @PostMapping("/service/payment/platform/care/list")
    PlatformCareRecordDTO getPlatformCareRecordList(@RequestBody @Validated PlatformCareQueryParams params);

    @PostMapping("/service/payment/platform/care/delete")
    int deletePlatformCare(@RequestParam("id") Long id);

    @PostMapping("/service/payment/platform/care/get/status")
    Map<Integer, String> getPlatformCareRecordStatusMap();

    @PostMapping("/service/payment/platform/care/monitor/link")
    void monitorLinkStatus(@RequestParam("type") String type);
}
