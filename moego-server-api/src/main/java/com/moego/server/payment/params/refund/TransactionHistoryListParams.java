package com.moego.server.payment.params.refund;

import com.moego.common.enums.PaymentMethodEnum;
import com.moego.common.enums.PaymentStatusEnum;
import com.moego.common.utils.Pagination;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/1/2
 */
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class TransactionHistoryListParams {
    private String paymentIntentId;
    private String invoiceId;
    private String clientName;
    private Long businessId;
    private List<Integer> customerIds;

    @Min(0)
    private BigDecimal amountMin;

    @Min(0)
    private BigDecimal amountMax;
    /**
     * Use METHOD_* constants defined in PaymentMethodEnum, e.g. METHOD_CREDIT_CARD.
     * @see PaymentMethodEnum
     */
    @Min(1)
    @Max(8)
    private Byte methodId;
    /**
     * @see com.moego.common.SquarePaymentMethodEnum
     */
    private List<@Min(1) @Max(5) Byte> squarePaymentMethod;
    /**
     * @see com.moego.common.StripePaymentMethodEnum
     */
    private List<@Min(1) @Max(8) Byte> stripePaymentMethod;

    private Long paymentCreateTimeStart;
    private Long paymentCreateTimeEnd;
    private String vendor;
    /**
     * @see PaymentStatusEnum
     */
    private List<@Min(-1) @Max(4) Byte> status;

    private Pagination pagination;
}
