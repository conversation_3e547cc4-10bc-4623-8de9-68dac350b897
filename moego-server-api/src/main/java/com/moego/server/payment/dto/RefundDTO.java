package com.moego.server.payment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Schema(description = "商家扣费记录")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundDTO {

    private Integer id;
    private Integer refundId;
    private String method;
    private String module;
    private Byte status;
    private BigDecimal amount;
    private Integer originPaymentId;
    private String stripeRefundId;
    private String reason;
    private String error;
    private Long createTime;
    private Long updateTime;

    private Integer businessId;
    private Integer invoiceId;
    private Integer customerId;
    private Integer staffId;
    private Integer methodId;
    private Long refundOrderPaymentId;
}
