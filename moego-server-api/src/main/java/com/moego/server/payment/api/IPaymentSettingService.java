package com.moego.server.payment.api;

import com.moego.server.payment.dto.CompanyProcessingFeeDTO;
import com.moego.server.payment.dto.PaymentSettingDTO;
import com.moego.server.payment.dto.SmartTipConfigDTO;
import com.moego.server.payment.params.PaymentSettingParams;
import com.moego.server.payment.params.PrimaryPayTypeParams;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IPaymentSettingService {
    @GetMapping("/service/payment/setting/info")
    PaymentSettingDTO getPaymentSetting(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/payment/company/setting")
    CompanyProcessingFeeDTO getCompanySetting(@RequestParam("companyId") Integer companyId);

    @GetMapping("/service/payment/membership/applicationFeePercent")
    BigDecimal getMembershipApplicationFeePercent(
            @RequestParam("companyId") Integer companyId,
            @RequestParam("stripePaymentMethodId") String stripePaymentMethodId);

    @GetMapping("/service/payment/setting/infoByCompanyId")
    List<PaymentSettingDTO> getPaymentSettingByCompanyId(@RequestParam("companyId") Long companyId);

    @PostMapping("/service/payment/setting/multiUpdate")
    Integer updatePaymentSettingByBusinessIds(@RequestBody @Valid PaymentSettingParams params);

    @PutMapping("/service/payment/setting/primaryPayType")
    Integer updatePrimaryPayTypeByBusinessId(@RequestBody PrimaryPayTypeParams params);

    @GetMapping("/service/payment/smartTip/config")
    SmartTipConfigDTO getSmartTipConfig(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/payment/needCloseProcessingFeeByClient")
    Boolean isNeedCloseProcessingFeeByClient(
            @RequestParam("businessId") Integer businessId, @RequestParam("addressState") String addressState);

    @GetMapping("/service/payment/refreshTTP/discount")
    void refreshTTPDiscount();
}
