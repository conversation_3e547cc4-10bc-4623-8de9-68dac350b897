package com.moego.server.grooming.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Map;
import lombok.Data;

@Data
public class PetScheduleSettingDTO {
    @Schema(description = "Schedule time in minutes, e.g., 09:00 AM = 540, 09:30 AM = 570 etc.")
    private Integer scheduleTime; // in minutes, 09:00 AM = 540, 09:30 AM = 570 etc.

    @Schema(description = "JSON string for additional information like schedule time label etc.")
    private Map<String, String> extraJson;
}
