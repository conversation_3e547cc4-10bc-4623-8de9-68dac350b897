package com.moego.server.grooming.dto.ob;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/4/8
 */
@Data
@Accessors(chain = true)
public class BookOnlineServiceDTO {

    private Long companyId;

    private Integer businessId;

    private Integer serviceId;

    /**
     * booking online 是否显示价格
     * 0 do not show price
     * 1 show fixed service price
     * 2 show price with "starting at"
     * 3 show price sa "Varies"
     */
    private Byte showBasePrice;

    /**
     * 0-not  1-yes
     */
    private Byte bookOnlineAvailable;

    /**
     * 0-not  1-yes
     */
    private Byte isAllStaff;
}
