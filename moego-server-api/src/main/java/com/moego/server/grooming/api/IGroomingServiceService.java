package com.moego.server.grooming.api;

import com.moego.common.response.ResponseResult;
import com.moego.server.grooming.dto.GroomingServiceBreedBindingDTO;
import com.moego.server.grooming.dto.GroomingServiceDTO;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.params.CommonIdsParams;
import com.moego.server.grooming.params.CreateCustomerPackageResult;
import com.moego.server.grooming.params.PurchasedPackage;
import com.moego.server.grooming.params.QueryServiceByTagIdParams;
import com.moego.server.grooming.params.QueryServiceByTypeParams;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IGroomingServiceService {

    /**
     * List grooming services by service ids, not include deleted records.
     *
     * @param serviceIds service ids
     * @return grooming services
     */
    @PostMapping("/service/grooming/service/list")
    List<GroomingServiceDTO> list(@RequestBody Collection<Integer> serviceIds);

    @PostMapping("/service/grooming/service/query")
    ResponseResult<List<MoeGroomingServiceDTO>> getServicesByServiceIds(@RequestBody CommonIdsParams commonIdsParams);

    @PostMapping("/service/grooming/service/filter/location")
    List<Integer> getServiceIdByServiceIdsLocationIdFilter(@RequestBody CommonIdsParams commonIdsParams);

    @PostMapping("/service/grooming/service/tagId")
    Boolean selectServiceByTagId(@RequestBody QueryServiceByTagIdParams params);

    /**
     *  Create purchased package.All package must be in the same business.
     *  请使用 createPurchasedPackageAndReturnResult
     */
    @Deprecated(since = "2025/01/01", forRemoval = true)
    @PostMapping("/service/grooming/service/purchased")
    ResponseResult<Integer> createPurchasedPackage(@RequestBody List<PurchasedPackage> purchasedPackages);

    /**
     *  createPurchasedPackage and return customer package id list.
     */
    @PostMapping("/service/grooming/createPurchasedPackageAndReturnResult")
    CreateCustomerPackageResult createPurchasedPackageAndReturnResult(
            @RequestBody List<PurchasedPackage> purchasedPackages);

    @GetMapping("/service/grooming/service/getGroomingServiceById")
    GroomingServiceDTO getGroomingServiceById(@RequestParam("id") int id);

    /**
     * TODO(account structure): 迁移后用户需要在 company 维度修改
     */
    @PostMapping("/service/grooming/service/updateCoatBinding")
    Boolean updateCoatBinding(@RequestParam("businessId") int businessId, @RequestParam("coatId") int coatId);

    /**
     * TODO(account structure): 迁移后用户需要在 company 维度修改
     */
    @PostMapping("/service/grooming/service/updateBreedBinding")
    Boolean updateBreedBinding(@RequestParam("businessId") int businessId, @RequestParam("breedName") String breedName);

    /**
     * Get grooming services by business ids.
     *
     * @param businessIds business ids
     * @return key: business id, value: services
     */
    @PostMapping("/service/grooming/services/byBusinessIds")
    Map<Integer, List<GroomingServiceDTO>> getServices(@RequestBody Collection<Integer> businessIds);

    @PostMapping("/service/grooming/services/byCompanyIds")
    Map<Long, List<GroomingServiceDTO>> getServicesByCompanyIds(@RequestBody Collection<Long> companyIds);

    @PostMapping("/service/grooming/services/getBreedBindings")
    List<GroomingServiceBreedBindingDTO> getBreedBindings(@RequestBody CommonIdsParams commonIdsParams);

    @PostMapping("/service/grooming/services/updateBreedBindings")
    Integer updateBreedBindings(@RequestBody List<GroomingServiceBreedBindingDTO> dtos);

    @PostMapping("/service/grooming/services/getObAvailableServiceIds")
    List<Long> getObAvailableServiceIds(@RequestBody QueryServiceByTypeParams queryServiceByTypeParams);
}
