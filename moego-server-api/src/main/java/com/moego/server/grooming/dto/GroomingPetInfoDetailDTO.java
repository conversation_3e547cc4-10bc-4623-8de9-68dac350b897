package com.moego.server.grooming.dto;

import java.util.List;
import lombok.Data;

@Data
public class GroomingPetInfoDetailDTO {

    private Integer petId;
    private String petName;
    private String breed;
    private Integer petTypeId;
    private String avatarPath;
    private Integer lifeStatus;

    private Integer customerId;
    private String typeName;
    private Integer breedMix;
    private String birthday;
    private Integer gender;
    private String hairLength;
    private String weight;
    private String fixed;
    private String behavior;
    private Byte expiryNotification;
    private Byte status;
    private String vetName;
    private String vetPhone;
    private String vetAddress;
    private String emergencyContactName;
    private String emergencyContactPhone;
    private String healthIssues;

    // 宠物上传的image
    private String petPhotoImage;

    private List<VaccineBindingRecordDto> vaccineBindings;
    private Boolean vaccineStatus; // true 过期false没过期

    private String petAnswerJson;
    private String petQuestionJson;

    private List<GroomingPetCodeDTO> petCodes;
    private List<GroomingPetServiceDTO> groomingPetServiceDTOS;
}
