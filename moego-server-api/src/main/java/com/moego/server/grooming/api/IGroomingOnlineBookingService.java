package com.moego.server.grooming.api;

import com.moego.common.response.ResponseResult;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.grooming.dto.BookOnlineAutoMoveAppointmentDTO;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.BookOnlineQuestionSaveDto;
import com.moego.server.grooming.dto.BookOnlineRequestCountDto;
import com.moego.server.grooming.dto.BookOnlineStaffAvailabilityDTO;
import com.moego.server.grooming.dto.CompanyBusinessIdDTO;
import com.moego.server.grooming.dto.MoeBookOnlineNotificationDto;
import com.moego.server.grooming.dto.ob.BookOnlineConfigDTO;
import com.moego.server.grooming.dto.ob.BookOnlineGalleryDTO;
import com.moego.server.grooming.dto.ob.BookOnlinePaymentGroupSettingDTO;
import com.moego.server.grooming.dto.ob.BookOnlineProfileDTO;
import com.moego.server.grooming.dto.ob.GroomingOnlyBookingRequestDTO;
import com.moego.server.grooming.dto.ob.MoeZipCodeDTO;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.dto.ob.ServiceAreaResultDTO;
import com.moego.server.grooming.params.BatchUpdateOBSettingParam;
import com.moego.server.grooming.params.CustomerIdWithPetIdsParams;
import com.moego.server.grooming.params.InitOBServiceParams;
import com.moego.server.grooming.params.MoeBookOnlineStaffTimeParams;
import com.moego.server.grooming.params.MoeBusinessBookOnlineDto;
import com.moego.server.grooming.params.UpdateStaffTimeParam;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.grooming.params.ob.ServiceAreaParams;
import java.util.List;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IGroomingOnlineBookingService {

    /**
     * use {@link #getCompanyBusinessId(String)} instead.
     */
    @Deprecated
    @GetMapping("/service/grooming/bookOnline/client/businessId")
    Integer getBusinessId(@RequestParam("obName") String obName);

    @GetMapping("/service/grooming/bookOnline/client/companyBusinessId")
    CompanyBusinessIdDTO getCompanyBusinessId(@RequestParam("obName") String obName);

    @GetMapping("/service/grooming/bookOnline/client/obName")
    String getObNameByBusinessId(@RequestParam("tokenBusinessId") Integer tokenBusinessId);

    @PostMapping("/service/grooming/bookOnline/question/save")
    List<BookOnlineQuestionSaveDto> getQuestionSaveByCustomerInfo(@RequestBody CustomerIdWithPetIdsParams petIdsParams);

    @GetMapping("/service/grooming/bookOnline/client/template")
    // 返回的 client notification 不准确，不能使用
    MoeBookOnlineNotificationDto getOnlineBookingSendTemplate(@RequestParam("businessId") Integer businessId);

    @PutMapping("/service/grooming/bookOnline/close/noShowFee")
    void updateBusinessBookOnlineSetting(@RequestParam("businessId") Integer businessId);

    @PostMapping("/service/grooming/bookOnline/checkAvailableDist/forCustomerLogin")
    Boolean checkAvailableDistForCustomerLogin(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("lat") String lat,
            @RequestParam("lng") String lng,
            @RequestParam("zipcode") String zipcode);

    @PostMapping("/service/grooming/bookOnline/staff/time")
    ResponseResult saveAvailableStaffTime(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam(value = "tokenCompanyId", required = false) Long companyId,
            @RequestBody MoeBookOnlineStaffTimeParams staffTimeParams);

    @PutMapping("/service/grooming/bookOnline/staff/time")
    Boolean updateStaffAvailableTimeByWorkingTime(@RequestBody UpdateStaffTimeParam updateStaffTimeParam);

    /**
     * @deprecated by Freeman, use {@link #getOBSetting(Integer)} instead.
     */
    @Deprecated(forRemoval = true, since = "2023/8/31")
    @GetMapping("/service/grooming/bookOnline/setting")
    MoeBusinessBookOnlineDto getBusinessBookOnlineSetting(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/grooming/bookOnline/setting/info")
    // 返回的 client notification 不准确，不能使用
    ResponseResult getSettingInfo(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam(value = "tokenCompanyId", required = false) Long companyId);

    @GetMapping("/service/grooming/bookOnline/autoMoveAppointment")
    List<BookOnlineAutoMoveAppointmentDTO> queryGroomingAllOBAppointment();

    @PostMapping("/service/grooming/bookOnline/autoMoveWaitlist")
    Boolean autoMoveWaitlist();

    @GetMapping("/service/grooming/bookOnline/appointment/count/forNotification")
    BookOnlineRequestCountDto getBookOnlineRequestCountForNotification(@RequestParam("businessId") Integer businessId);

    @PutMapping("/service/grooming/bookOnline/staff/availability")
    void updateStaffOBAvailability(
            @RequestParam("businessId") Integer businessId, @RequestBody BookOnlineStaffAvailabilityDTO params);

    /**
     * DONE(account structure): 保留 business 维度
     */
    @PostMapping("/service/grooming/bookOnline/client/address/area")
    List<ServiceAreaResultDTO> getServiceAreaResultList(@RequestBody ServiceAreaParams serviceAreaParams);

    @GetMapping("/service/grooming/bookOnline/profiles")
    List<BookOnlineProfileDTO> listBookOnlineProfile(@RequestParam List<Integer> businessIdList);

    @GetMapping("/service/grooming/bookOnline/gallery/max")
    List<BookOnlineGalleryDTO> listGalleryMaxSortImageByBusinessId(@RequestParam List<Integer> businessIdList);

    @GetMapping("/service/grooming/bookOnline/configs")
    List<BookOnlineConfigDTO> listBookOnlineConfig(@RequestParam List<Integer> businessIdList);

    /**
     * initialize moe_book_online_available_staff table data
     *
     * @param staffDtoList staff id and business id
     */
    @PostMapping("/service/grooming/bookOnline/staff/availability")
    boolean initializeAvailableStaff(@RequestBody List<MoeStaffDto> staffDtoList);

    @PostMapping("/service/grooming/bookOnline/staff/availability/v2")
    boolean initializeAvailableStaffV2(
            @RequestParam("businessId") Integer businessId, @RequestBody List<MoeStaffDto> staffDtoList);

    /**
     * Get business id by book online name or domain name for anonymous API
     *
     * @param anonymousParams OB book online name (2.0) Domain name (3.0)
     * @return business id
     */
    @GetMapping("/service/grooming/bookOnline/client/anonymous")
    Integer getBusinessIdByOBNameOrDomain(@SpringQueryMap OBAnonymousParams anonymousParams);

    @GetMapping("/service/grooming/bookOnline/client/anonymousV2")
    OBBusinessDTO getBusinessDTOByOBNameOrDomain(@SpringQueryMap OBAnonymousParams anonymousParams);

    @GetMapping("/service/grooming/bookOnline/mustGetBusinessDTOByOBNameOrDomain")
    OBBusinessDTO mustGetBusinessDTOByOBNameOrDomain(@SpringQueryMap OBAnonymousParams anonymousParams);

    /**
     * List book online setting for specific business ids.
     *
     * @param businessIds business id list
     * @return {@link BookOnlineDTO} list
     */
    @PostMapping("/service/grooming/bookOnline/listOBSetting")
    List<BookOnlineDTO> listOBSetting(@RequestBody List<Integer> businessIds);

    /**
     * Get {@link BookOnlineDTO} for specific business id.
     *
     * @param businessId business id
     * @return {@link BookOnlineDTO}
     */
    @GetMapping("/service/grooming/bookOnline/getOBSetting")
    BookOnlineDTO getOBSetting(@RequestParam Integer businessId);

    /**
     * Batch update OB setting.
     *
     * @param param {@link BatchUpdateOBSettingParam}
     * @return affected rows
     */
    @PostMapping("/service/grooming/bookOnline/batchUpdateOBSetting")
    int batchUpdateOBSetting(@RequestBody BatchUpdateOBSettingParam param);

    /**
     * init moe_book_online_service table data
     *
     * @param param companyId,businessId,serviceId
     */
    @PostMapping("/service/grooming/bookOnline/initOBService")
    boolean initOBService(@RequestBody InitOBServiceParams param);

    /**
     * 删除 group payment setting，这是因为上面这个接口无法把某些字段置为 null。
     */
    @DeleteMapping("/service/grooming/bookOnline/deleteOBGroupPaymentSetting")
    void deleteOBGroupPaymentSetting(@RequestParam("businessId") Integer businessId);

    @DeleteMapping("/service/grooming/bookOnline/staff/serviceArea")
    void deleteServiceArea(
            @RequestParam("businessId") Integer businessId, @RequestParam("serviceAreaId") Integer serviceAreaId);

    @GetMapping("/service/grooming/bookOnline/client/payment/setting")
    BookOnlinePaymentGroupSettingDTO getOBClientPaymentSetting(
            @RequestParam Integer businessId, @RequestParam(required = false) Integer customerId);

    /**
     * Get booking request by appointment id
     *
     * @param appointmentId appointment id
     * @return booking request details
     */
    @GetMapping("/service/grooming/bookOnline/getBookingRequest")
    GroomingOnlyBookingRequestDTO getBookingRequest(@RequestParam Integer appointmentId);

    /**
     * 此接口用于 booking request 双写期间的数据一致性校验
     * 每天执行一次，按照 business 纬度，扫取 source = OB 的预约请求发送 VALIDATE event
     *
     * @param businessId specific business id
     * @param startDate filter start date，不指定扫取前7天开始的数据（可能会遗漏操作历史数据的情况）
     */
    @GetMapping("/service/grooming/bookOnline/sendBookingRequestValidateEvent")
    Boolean sendBookingRequestValidateEvent(
            @RequestParam(required = false, name = "businessId") Integer businessId,
            @RequestParam(required = false, name = "startDate") String startDate);

    // used by enterprise-api-v1,do not make any breaking changes
    @GetMapping("/service/grooming/bookOnline/zipcode/search")
    List<MoeZipCodeDTO> searchByPrefix(@RequestParam String prefix);

    // used by enterprise-api-v1,do not make any breaking changes
    @GetMapping("/service/grooming/bookOnline/zipcode/details")
    List<MoeZipCodeDTO> selectByZipCodes(@RequestParam List<String> zipCodes);

    @PostMapping("/service/grooming/bookOnline/zipcode/details")
    List<MoeZipCodeDTO> selectByZipCodesPost(@RequestBody List<String> zipCodes);
}
