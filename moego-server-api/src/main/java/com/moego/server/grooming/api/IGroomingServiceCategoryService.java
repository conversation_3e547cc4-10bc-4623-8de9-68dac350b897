package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.ServiceCategoryDTO;
import com.moego.server.grooming.dto.ServiceCategoryListDto;
import com.moego.server.grooming.params.ServiceCategoryBatchUpdateParams;
import java.util.Collection;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
public interface IGroomingServiceCategoryService {
    /**
     * Get service categories by ids.
     *
     * <p><span color="orange">NOTE: {@link ServiceCategoryDTO#petServices} in response is always null</span></p>
     *
     * @param ids service category ids
     * @return service categories
     */
    @PostMapping("/service/grooming/service-categories/byIds")
    List<ServiceCategoryDTO> getServiceCategories(@RequestBody Collection<Integer> ids);

    @PostMapping("/service/grooming/company/batch/serviceCategory")
    List<ServiceCategoryListDto> getEditServiceCategory(
            @RequestBody ServiceCategoryBatchUpdateParams batchUpdateParams);

    @GetMapping("/service/grooming/company/serviceCategories")
    List<com.moego.server.grooming.dto.ServiceCategoryListDto> listServiceCategories(
            @RequestParam("companyId") Long companyId,
            @RequestParam("type") Byte type,
            @RequestParam("serviceItemType") Byte serviceItemType);
}
