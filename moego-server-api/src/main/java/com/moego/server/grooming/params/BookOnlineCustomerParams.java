package com.moego.server.grooming.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BookOnlineCustomerParams {

    @Size(max = 50)
    private String firstName;

    @Size(max = 50)
    private String lastName;

    @Size(max = 30)
    private String phoneNumber;

    @Size(max = 50)
    private String email;

    /**
     *  main address
     *  */
    @Size(max = 255)
    private String address1;

    @Size(max = 255)
    private String address2;

    @Size(max = 255)
    private String city;

    @Size(max = 255)
    private String state;

    @Size(max = 10)
    private String zipcode;

    @Size(max = 255)
    private String country;

    @Size(max = 50)
    private String lat;

    @Size(max = 50)
    private String lng;

    private Map<String, Object> answersMap;
    /**
     * used to save address which is combined by fileds above
     */
    private String address;
    /**
     * new or old customer
     */
    private Integer customerId;

    private Integer addressId;

    @Schema(description = "credit card token for stripe or square")
    @Size(max = 128)
    private String chargeToken;

    @Schema(description = "true if client supplied")
    private Boolean hasStripeCard;

    private String stripeCustomerId;

    private Boolean isProfileRequestAddress;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime birthday;

    // CRM-3555 emergency contact
    private String emergencyContactFirstName;
    private String emergencyContactLastName;
    private String emergencyContactPhone;

    // CRM-3674 pickup contact
    private String pickupContactFirstName;
    private String pickupContactLastName;
    private String pickupContactPhone;

    // ENT-248 K9 intake form add marketing policy
    private Boolean ackMarketingPolicy;
}
