package com.moego.server.grooming.params;

import com.moego.common.utils.Pagination;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class PetServicePageParams {

    /**
     * 1-主服务(service)；2-额外服务(addons)
     */
    @NotNull
    Byte type;

    /**
     * 0-正常；1-删除
     */
    @NotNull
    Byte inactive;

    /**
     * 模糊搜索关键字
     */
    @Size(max = 50)
    String keyword;

    @NotNull
    Pagination pagination;
}
