package com.moego.server.grooming.dto.groomingreport;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Grooming report 表原数据，不包含 template, content 的格式化
 */
@Data
public class GroomingReportDTO {

    @Schema(description = "grooming report id")
    private Integer id;

    private Integer businessId;
    private Integer customerId;
    private Integer groomingId;
    private Integer petId;

    @Schema(description = "pet type id: 1-dog,2-cat")
    private Integer petTypeId;

    @Schema(description = "unique id for grooming report")
    private String uuid;

    @Schema(description = "unique id for share")
    private String uuidForShare;

    @Schema(description = "grooming report used template published time")
    private Long templatePublishTime;

    @Schema(description = "grooming report status")
    private String status;

    @Schema(description = "last update staff id")
    private Integer updateBy;

    @Schema(description = "last submitted time")
    private Long submittedTime;

    @Schema(description = "link opened count")
    private Integer linkOpenedCount;

    private Long createTime;
    private Long updateTime;

    private String templateJson;
    private String contentJson;
}
