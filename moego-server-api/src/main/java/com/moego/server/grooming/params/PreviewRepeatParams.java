package com.moego.server.grooming.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class PreviewRepeatParams extends RepeatParams {

    // token businessId, staffId, 前端可忽略
    @JsonIgnore
    private Integer companyId;

    @JsonIgnore
    private Integer businessId;

    @JsonIgnore
    private Integer staffId;

    @Deprecated
    @Schema(description = "是否基于已存在的预约，废弃字段，老版客户端使用")
    private Boolean isExistAppt;

    @Schema(description = "已存在预约的 id，检查冲突时需要排除这个预约")
    private Integer existAppointmentId;

    @Schema(
            description =
                    "当前已存在预约日期修改，同时更新 appointment date/startTime 和 repeat rules 时需要传，会对 repeat appointment date 整体偏移")
    private String updateAppointmentDate;

    @Schema(
            description =
                    "当前已存在预约的 startTime 修改，同时更新 appointment date/startTime 和 repeat rules 时需要传，会覆盖 upcoming 的 startTime")
    private Integer updateAppointmentStartTime;

    @NotEmpty
    private List<@Valid RepeatStaffInfoParams> repeatStaffInfoParams;

    // ss repeat 相关参数
    @Schema(description = "smart schedule 开关: true/false")
    private Boolean smartSchedule;

    @Schema(description = "是否应用client preference")
    private Boolean applyClientPreference;

    @Schema(description = "buffer time in minutes, 与其他Appointment需要间隔的时间")
    private Integer bufferTime;
}
