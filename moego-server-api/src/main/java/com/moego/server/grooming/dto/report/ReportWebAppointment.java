package com.moego.server.grooming.dto.report;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * BeanUtils copy with AppointmentReportRecord
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReportWebAppointment {

    private Integer bookingId;
    private String apptDateTime;
    private String apptDate;
    private String apptTime;
    private Integer clientId;
    private List<String> petNames;
    private List<Integer> petIds;
    private List<String> serviceAndAddOns;
    private List<String> service;
    private List<String> addOns;
    private List<String> serviceCharges;
    private BigDecimal totalPrice;
    private List<String> staffs;
    private List<Integer> staffIds;
    private String ticketComment;
    private String alertNote;
    private String status;
    private String paymentStatus;
    private String createDate;
    private Integer createById;

    // unpaid appt report
    private BigDecimal paidPrice;
    private BigDecimal unpaidPrice;

    // cancel appt report
    private String cancelDate;
    private String cancelByType;
    private Integer cancelById;
    private String cancelReason;

    // noshow appt report
    private String noShowMarkDate;
    private Integer noShowMarkById;
    private String noShowReason;
    private BigDecimal noShowFee;

    // appt by staff report
    private String staffName;
    private Integer staffId;

    // inter-media variables 中间逻辑处理使用
    private String createBy;
    private String cancelledBy;
    private String noShowMarkBy;
    private String firstName;
    private String lastName;
    private String primaryNumber;
    private String email;
    private String fullAddress;
    private Integer invoiceId;

    // sales and appointments report
    private BigDecimal revenue; // tips and taxes
    private BigDecimal tips;
    private BigDecimal tax;
    private BigDecimal discount;
    private BigDecimal collectedRevenue; // 实收金额
    private BigDecimal totalPayment; // 总付款金额
    private BigDecimal totalRefund; // 总退款金额
    private BigDecimal conFeeWithhold; // 退款时扣留的Convenience fee，商家损失的钱
    private BigDecimal totalUnpaid;
    private String petAndServices;
    private BigDecimal totalServicePrice;
    private BigDecimal totalAddOnsPrice;
    private BigDecimal totalProductPrice; // product总应收金额
    private BigDecimal totalServiceChargePrice; // service charge 总应收金额
    // 净实收收入 = payment - convenienceFee - tax - tips - refund
    private BigDecimal netSaleRevenue;

    // net income report info: reportID = 5012
    private BigDecimal convenienceFee;
    private BigDecimal processingFee;
    private BigDecimal processingFeeBalance; // Convenience fee-processing fee
    /**
     * expectedNetIncome = netSaleRevenue + processingFeeBalance
     * = paid amount-Convenience fee+(processing fee balance)-refund-tax-tip
     */
    private BigDecimal expectedNetIncome;

    @Schema(description = "key 为payment method名称，value 为对应的支付金额")
    private Map<String, BigDecimal> paymentMap;

    // 多个invoice
    private List<Integer> invoiceIds;

    private BigDecimal collectedServicePrice;
    private BigDecimal collectedProductPrice; // product总实收金额
    private BigDecimal collectedServiceChargePrice; // service charge 总实收金额
    private BigDecimal collectedTips;
    private BigDecimal collectedTax;

    // grooming report info
    private GroomingReportInfo groomingReportInfo;

    // waitList 专属
    private Long relateAppointmentId;
    private String validTill;
    private String datePreference;
    private String timePreference;
    private String staffPreference;
}
