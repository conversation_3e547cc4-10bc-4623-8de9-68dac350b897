package com.moego.server.grooming.dto;

import java.util.List;
import lombok.Data;

@Data
public class LodgingTypeDTO {
    private Long id;

    private String name;

    private String description;

    private List<String> photoList;

    private Integer maxPetNum;

    private Boolean allPetSizes;

    private List<Long> petSizeIds;

    private String lodgingUnitType;

    private Boolean petSizeFilter;

    private Integer sort;
}
