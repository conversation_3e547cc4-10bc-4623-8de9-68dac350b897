package com.moego.server.grooming.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import org.springframework.util.CollectionUtils;

@Data
public class LimitDto {
    private List<ServiceLimitDto> serviceLimitList;
    private List<PetSizeLimitDto> petSizeLimitList;
    private List<PetBreedLimitDto> petBreedLimitList;

    public LimitDto() {
        this.serviceLimitList = new ArrayList<>();
        this.petSizeLimitList = new ArrayList<>();
        this.petBreedLimitList = new ArrayList<>();
    }

    public boolean isEmpty() {
        return CollectionUtils.isEmpty(serviceLimitList)
                && CollectionUtils.isEmpty(petBreedLimitList)
                && CollectionUtils.isEmpty(petSizeLimitList);
    }
}
