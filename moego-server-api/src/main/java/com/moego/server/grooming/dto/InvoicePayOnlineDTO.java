package com.moego.server.grooming.dto;

import com.moego.common.dto.BusinessPreferenceDto;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.customer.dto.PreferredTipDto;
import com.moego.server.payment.dto.GetSquareTokenResponse;
import com.moego.server.payment.dto.PaymentSettingForClientDTO;
import com.moego.server.payment.dto.SmartTipConfigForClientDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/6/23 10:25 AM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoicePayOnlineDTO {

    Integer businessId;
    String avatarPath;
    String businessName;
    MoeBusinessDto businessInfo;
    InvoiceSummaryDTO invoiceInfo;
    BusinessPreferenceDto businessPreference;
    GetSquareTokenResponse squareInfo;

    @Schema(description = "for stripe")
    String stripeAccountId;

    @Schema(description = "for partial pay/deposit")
    DepositDto depositInfo;

    @Schema(description = "payment setting")
    PaymentSettingForClientDTO paymentSetting;

    @Schema(description = "business tip config")
    SmartTipConfigForClientDTO tipConfig;

    @Schema(description = "preferred tips setting ")
    PreferredTipDto preferredTip;

    @Schema(description = "business 是否在白名单中")
    Boolean isInInvoiceWhiteList;
}
