package com.moego.server.grooming.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
@Schema(description = "pickup notification 发送状态", type = "integer")
public enum PickupNotificationStatusEnum {
    NOT_SENT(0),
    SUCCESS(1),
    FAILED(2);
    private final Integer value;

    @JsonValue
    public Integer getValue() {
        return value;
    }
}
