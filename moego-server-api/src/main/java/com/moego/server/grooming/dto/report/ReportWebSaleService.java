package com.moego.server.grooming.dto.report;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 5/8/21 3:38 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportWebSaleService {

    private Integer serviceId;
    private String serviceName;
    private String categoryName;
    private BigDecimal totalSale;
    private Integer ticketNum;
    private Integer clientNum;
    private Integer petNum;
    private BigDecimal collectedServicePrice;
    // 净实收收入 = payment - convenienceFee - tax - tips - refund
    private BigDecimal netSaleRevenue;
}
