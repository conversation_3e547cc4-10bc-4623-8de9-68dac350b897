package com.moego.server.grooming.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
@Schema(description = "package activity", type = "integer")
public enum PackageActivityEnum {
    UNKNOWN((byte) 0),
    REDEEM_PACKAGE((byte) 1),
    EXTEND_PACKAGE_VALIDITY((byte) 2),
    MANUAL_CHANGE_REMAINING((byte) 3);

    private final Byte value;

    @JsonValue
    public Byte getValue() {
        return value;
    }

    public static PackageActivityEnum fromValue(Byte value) {
        for (PackageActivityEnum status : PackageActivityEnum.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return UNKNOWN;
    }
}
