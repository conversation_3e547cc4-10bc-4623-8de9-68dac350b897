package com.moego.server.grooming.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class GroomingPetServiceDTO {

    private Integer petDetailId;
    private Byte serviceStatus;
    private Integer staffId;
    private Integer serviceId;
    private Integer serviceType;
    private Integer serviceTime;
    private BigDecimal servicePrice;
    private Long startTime;
    private Long endTime;
    private Integer scopeTypePrice;
    private Integer scopeTypeTime;

    private String staffLastName;
    private String staffFirstName;
    private String serviceName;

    /**
     * work mode
     * 0 - parallel
     * 1 - sequence
     */
    private Integer workMode;

    /**
     * operation record list
     */
    private List<GroomingServiceOperationDTO> operationList;

    /**
     * @see com.moego.common.enums.ServiceItemEnum
     */
    private Integer serviceItemType;

    private String lodgingUnitName;
    private List<String> specificDates;
    private String startDate;
    private String endDate;

    /**
     * ServiceOverrideType 用的是 protobuf enum，会导致前端生成两份 enum，这里返回 int
     */
    @Schema(type = "integer", format = "int32")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private ServiceOverrideType priceOverrideType;

    @Schema(type = "integer", format = "int32")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private ServiceOverrideType durationOverrideType;

    private Integer quantityPerDay;

    /**
     * date type
     * 1-every day except checkout day, 2-specific date, 3-date point, 4-everyday
     */
    private Integer dateType;

    /**
     * split lodging
     */
    private List<LodgingInfo> lodgingInfos;
}
