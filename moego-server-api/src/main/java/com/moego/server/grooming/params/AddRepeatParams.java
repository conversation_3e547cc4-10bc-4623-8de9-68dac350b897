package com.moego.server.grooming.params;

import jakarta.validation.constraints.Min;
import lombok.Data;

@Data
public class AddRepeatParams {

    private Integer customerId;

    private Integer businessId;
    private Long companyId;
    private Integer staffId;
    private Byte repeatType;

    @Min(1)
    private Integer repeatEvery;

    private String repeatBy;
    private String startsOn;
    private Integer times;
    private Byte isNotice;
    private String setEndOn;
    private Byte repeatEveryType;
    private Byte monthDay;
    private Byte monthWeekTimes;
    private Byte monthWeekDay;

    private Boolean isExistAppt;
    private String type; // 1次数2设置结束时间

    private Boolean smartSchedule; // 是否是smart schedule
    private Integer ssBeforeDays; // smart schedule设置，最多往前查询多少天
    private Integer ssAfterDays; // smart schedule设置，最多往后查询多少天
}
