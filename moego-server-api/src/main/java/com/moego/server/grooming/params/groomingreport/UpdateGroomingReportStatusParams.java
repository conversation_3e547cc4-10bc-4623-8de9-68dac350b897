package com.moego.server.grooming.params.groomingreport;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
public record UpdateGroomingReportStatusParams(
        @NotNull Integer businessId, Integer updateBy, @NotEmpty List<Integer> groomingReportIdList) {}
