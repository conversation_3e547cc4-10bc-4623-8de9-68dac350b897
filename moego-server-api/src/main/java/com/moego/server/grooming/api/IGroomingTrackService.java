package com.moego.server.grooming.api;

import com.moego.server.grooming.dto.track.GroomingApptDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface IGroomingTrackService {
    @GetMapping("/service/grooming/track/firstAppt")
    GroomingApptDTO getCustomerFirstAppt(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);
}
