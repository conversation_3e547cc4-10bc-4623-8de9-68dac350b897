package com.moego.server.grooming.params.appointment;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

@Builder(toBuilder = true)
public record DeletePetParams(
        @Schema(description = "business id", hidden = true) Integer businessId,
        @Schema(description = "token staff id", hidden = true) Integer tokenStaffId,
        @NotNull @Min(1) Long appointmentId,
        @NotNull @Min(1) Integer petId,
        Boolean allPetsStartAtSameTime,
        @Schema(description = "repeat type: 1-only this, 2-apply to upcoming, 3-apply to all") @Max(3) @Min(1)
                Integer repeatType) {}
