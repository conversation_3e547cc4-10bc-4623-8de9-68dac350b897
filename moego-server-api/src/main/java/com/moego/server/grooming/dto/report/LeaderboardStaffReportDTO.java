package com.moego.server.grooming.dto.report;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LeaderboardStaffReportDTO {

    private Integer staffId;

    private BigDecimal collectedRev;

    private BigDecimal collectedTips;

    /**
     * 每个员工的总预约数
     */
    private Integer totalAppts;

    /**
     * 总预约数
     */
    private Integer totalApptsAll;

    /**
     * 已完成的预约数
     */
    private Integer finishedApptNum;

    /**
     * partial/fully paid的预约数
     */
    private Integer paidApptNum;

    /**
     * 服务过的宠物数，已去重
     */
    private Integer servicedPetNum;

    /**
     * 服务时间（分钟）
     */
    private Long totalServiceMinute;

    /**
     * 有效工作时间（分钟）
     */
    private Integer totalWorkingMinute;

    /**
     * 无效工作时间（分钟）
     */
    private Integer totalBlockMinute;
}
