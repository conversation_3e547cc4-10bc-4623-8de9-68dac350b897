package com.moego.server.grooming.params;

import com.moego.common.params.PageParams;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2020-09-22 01:29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RepeatReminderParams extends PageParams {

    private Integer businessId;
    List<Integer> dismissIds;
    private String startDate;
    private String endDate;
}
