package com.moego.server.grooming.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class AppointmentParams {

    private Integer id;

    private Integer businessId;
    private Long companyId;

    private Integer customerId;

    @JsonIgnore
    private Integer operatorId; // 用于传递updateStaffId

    @NotNull
    private String appointmentDateString;

    private Integer appointmentStartTime;

    private Boolean noStartTime;

    private Byte isWaitingList;

    private Integer source;

    private Integer repeatId;

    private Integer customerAddressId;

    private String alertNotes;

    private Boolean isNeedUpdateAlertNotes;

    private String ticketComments;

    private String additionalNote;

    private Byte isPaid;

    @NotNull
    private String colorCode;

    private Integer createdById;
    /**
     * 是否是repeat的第一个预约，创建N个repeat预约，只有第一个需要发送repeat，后面都忽略
     */
    private Boolean isRepeatFirstAppt;

    private List<@Valid PetDetailParams> petServices;

    private Byte status;

    private Byte bookOnlineStatus;

    /**
     * for mobile grooming
     */
    private Byte outOfArea;

    /**
     * repeat修改时，修改日期后，基于这个变量调整其余repeat内的预约
     */
    private Long repeatUpdateDaysDiff;

    // 预约类型
    private Byte scheduleType;

    private Boolean preAuthEnable;

    private String preAuthPaymentMethod;

    private String preAuthCardNumber;
    /**
     * 标记来自 abandoned record booking flow
     */
    private String bookingFlowId;

    private Boolean isAutoAccept;

    // 所有 pet 是否同时开始
    private Boolean allPetsStartAtSameTime;

    /**
     * gc sync 是否要延时
     */
    @JsonIgnore
    private Boolean isGcSyncDelay;

    private String endDate;

    private String sourcePlatform;
}
