package com.moego.server.grooming.client;

import com.moego.server.grooming.api.IOrderDecouplingFlowMarkerService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-grooming-server",
        url = "${moego.server.url.grooming}",
        contextId = "IOrderDecouplingFlowMarkerClient")
public interface IOrderDecouplingFlowMarkerClient extends IOrderDecouplingFlowMarkerService {}
