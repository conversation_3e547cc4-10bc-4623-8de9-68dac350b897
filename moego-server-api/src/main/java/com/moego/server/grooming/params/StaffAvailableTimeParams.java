package com.moego.server.grooming.params;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class StaffAvailableTimeParams {

    @NotNull
    private List<String> dayI;

    @NotNull
    private List<Integer> serviceIds;

    private boolean querySmartScheduling;
    private Integer customerId;
    private Integer businessId;
    private String lat;
    private String lng;
}
