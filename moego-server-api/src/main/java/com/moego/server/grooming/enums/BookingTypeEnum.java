package com.moego.server.grooming.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Schema(description = "booking type", type = "integer")
public enum BookingTypeEnum {
    NORMAL((byte) 0),
    ONLINE_BOOKING((byte) 1);

    private final Byte value;

    @JsonValue
    public Byte getValue() {
        return value;
    }

    public static BookingTypeEnum fromValue(Byte value) {
        for (BookingTypeEnum status : BookingTypeEnum.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return NORMAL;
    }
}
