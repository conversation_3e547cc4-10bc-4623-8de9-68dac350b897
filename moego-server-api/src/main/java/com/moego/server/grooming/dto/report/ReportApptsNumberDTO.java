package com.moego.server.grooming.dto.report;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportApptsNumberDTO {

    private Integer unpaidAppts;
    private Integer unclosedAppts;
    private Integer cancelledAppts;
    private Integer noShowAppts;
    private Integer upcomingAppts;
    private Integer waitingListAppts;
    private Integer onlineBookingAppts;
}
