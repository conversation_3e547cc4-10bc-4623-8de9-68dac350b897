package com.moego.server.grooming.params.ob;

import com.moego.server.grooming.params.BookOnlinePetParams;
import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/6/29
 */
@Data
public class CalculatePaymentParams {
    @NotNull
    private Long businessId;

    @NotNull
    private Long customerId;

    @NotNull
    @Valid
    private List<@NotNull @Valid BookOnlinePetParams> petData;

    @Size(min = 4, max = 20)
    private String discountCode;

    @Nullable
    private Integer staffId;
}
