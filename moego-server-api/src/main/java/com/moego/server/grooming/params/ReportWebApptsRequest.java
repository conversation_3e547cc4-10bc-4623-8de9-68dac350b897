package com.moego.server.grooming.params;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ReportWebApptsRequest {

    private Integer businessId;
    private Long companyId;
    private Integer reportId;
    private String dateFormat;
    private Integer timeFormatType;
    private String startDate;
    private String endDate;

    private String timezoneName;
}
