package com.moego.server.grooming.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
@Schema(description = "预约状态", type = "integer")
public enum AppointmentStatusEnum {
    UNKNOWN((byte) 0),
    UNCONFIRMED((byte) 1),
    CONFIRMED((byte) 2),
    FINISHED((byte) 3),
    CANCELED((byte) 4),
    READY((byte) 5),
    CHECK_IN((byte) 6);

    private final Byte value;

    @JsonValue
    public Byte getValue() {
        return value;
    }

    public static AppointmentStatusEnum fromValue(Byte value) {
        for (AppointmentStatusEnum status : AppointmentStatusEnum.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return UNKNOWN;
    }
}
