package com.moego.server.grooming.dto;

import com.moego.common.utils.Pagination;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/9/25
 */
@Data
@Accessors(chain = true)
public class AppointmentListClientPortalDTO {

    private List<AppointmentList> appointmentList;
    private Pagination pagination;

    @Data
    @Accessors(chain = true)
    public static class AppointmentList {
        /**
         * Appointment id
         */
        private Integer id;

        private Integer businessId;
        private Integer customerId;
        private String appointmentDate;
        private Integer appointmentStartTime;
        private Integer appointmentEndTime;
        private boolean noStartTime;
    }
}
