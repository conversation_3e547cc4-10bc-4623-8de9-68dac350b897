package com.moego.server.message.api;

import com.moego.api.common.Range;
import com.moego.server.message.dto.AbandonedScheduleMessageDTO;
import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Set;
import lombok.Builder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@Validated
public interface IAbandonedScheduleMessageService {

    /**
     * Insert an AbandonedScheduleMessage.
     *
     * @param dto {@link AbandonedScheduleMessageDTO}
     * @return id
     */
    @PostMapping("/service/message/abandoned-schedule-message/insert")
    int insert(@RequestBody AbandonedScheduleMessageDTO dto);

    /**
     * List {@link AbandonedScheduleMessageDTO} by condition.
     *
     * <p> {@link ListByConditionParam#customerPhoneIn} is a set of customer phones with or without country code.
     *
     * @param param {@link ListByConditionParam}
     * @return {@link AbandonedScheduleMessageDTO} list
     */
    @PostMapping("/service/message/abandoned-schedule-message/listByCondition")
    List<AbandonedScheduleMessageDTO> listByCondition(@RequestBody @Valid ListByConditionParam param);

    /**
     * @param customerPhoneIn customer phones with or without country code
     */
    @Builder(toBuilder = true)
    record ListByConditionParam(
            @Nullable Set<Integer> businessIdIn,
            @Nullable Set<String> customerPhoneIn,
            @Nullable Range<Date> customerReplyTimeRange,
            @Nullable Range<Date> createdAtRange) {}
}
