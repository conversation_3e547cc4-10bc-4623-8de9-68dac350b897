package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraClockDto;
import com.moego.common.enums.ClockInOutEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021/10/25 6:42 PM
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationClockInParams extends NotificationParams {

    private String title = "Staff Clock In/Out";
    private String type = NotificationEnum.TYPE_CLOCK_IN_OUT;
    private Boolean isNotifyBusinessOwner = true;
    private NotificationExtraClockDto webPushDto;
    private String mobilePushTitle = "Clock In";
    private String mobilePushBody = "{staffFirstName} Clock in on {Time}.";

    public void formatPushBody(String clockTime, Double hours) {
        if (ClockInOutEnum.OPERATE_TYPE_CLOCK_OUT.equals(webPushDto.getClockType())) {
            mobilePushTitle = "Clock Out";
            int hour = hours.intValue();
            double minutes = (hours - hour) * 60;
            mobilePushBody = String.format(
                    "%s clocked out on %s, total hour: %dh %d mins",
                    webPushDto.getStaffName(), clockTime, hour, (int) minutes);
        } else {
            mobilePushBody = String.format("%s clocked in on %s", webPushDto.getStaffName(), clockTime);
        }
    }
}
