package com.moego.server.message.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.Data;

@Data
public class GroomingReportSendParams {

    @Schema(description = "需要发送的 reportId 列表")
    @NotEmpty
    private List<Integer> reportIdList;

    @Schema(description = "sending method to send，如不传，则根据设置项选择的方式来发送")
    private List<Byte> sendingMethodList;
}
