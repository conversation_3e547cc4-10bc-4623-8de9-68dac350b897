package com.moego.server.message.dto;

import com.moego.server.message.enums.VerificationCodeScenarioEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/2/10
 */
@Data
@Accessors(chain = true)
public class SendPhoneDTO {

    /**
     * Send scenario
     */
    private VerificationCodeScenarioEnum scenario;

    /**
     * Business id
     */
    private Integer businessId;

    /**
     * Business name
     */
    private String businessName;

    /**
     * Existing client id
     */
    private Integer customerId;

    /**
     * Existing client first name
     */
    private String firstName;

    /**
     * Existing client last name
     */
    private String lastName;

    /**
     * Receive phone number
     */
    private String phoneNumber;
}
