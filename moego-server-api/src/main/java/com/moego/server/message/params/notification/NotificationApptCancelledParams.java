package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraApptCommonDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationApptCancelledParams extends NotificationParams {

    private String title = "Appointment cancelled";
    private String type = NotificationEnum.TYPE_ACTIVITY_APPT_CANCELLED;
    private NotificationExtraApptCommonDto webPushDto;
    private Boolean isNotifyBusinessOwner = true;
    private String mobilePushTitle = "Appointment cancelled";
    private String mobilePushBody = "{customerFullName} {Date}{Time} with {staffFirstName}";
    private Boolean isAppointmentRelated = true;
}
