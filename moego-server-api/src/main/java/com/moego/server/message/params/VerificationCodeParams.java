package com.moego.server.message.params;

import com.moego.server.message.dto.MoeTwilioNumberDTO;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/10/22
 */
@Data
@Accessors(chain = true)
public class VerificationCodeParams {

    /**
     * 接收验证码的手机号
     */
    private String phoneNumber;

    /**
     * 接收验证码的邮箱
     */
    private String email;

    /**
     * 验证码
     */
    private String verificationCode;

    /**
     * 有效时间，单位秒
     */
    private Integer expirationSeconds;

    /**
     * 发送验证码的手机号信息
     */
    private MoeTwilioNumberDTO fromTwilioNumber;

    /**
     * 指定发送的消息体
     */
    private String messageBody;
}
