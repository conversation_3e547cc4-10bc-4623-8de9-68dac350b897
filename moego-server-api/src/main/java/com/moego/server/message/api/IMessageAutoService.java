package com.moego.server.message.api;

import com.moego.server.message.dto.AutoMessageTemplateDTO;
import com.moego.server.message.dto.AutoMessageTemplatePreviewDTO;
import com.moego.server.message.dto.MoeBusinessAutoReplyDto;
import com.moego.server.message.params.AutoMessageTemplatePreviewParams;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2020-08-15 22:53
 */
public interface IMessageAutoService {
    @GetMapping("/service/message/autoReply/getBusinessAutoReplyForBusiness")
    MoeBusinessAutoReplyDto getBusinessAutoReplyForBusiness(@RequestParam("tokenBusinessId") Integer tokenBusinessId);

    /**
     * List auto message templates for business
     *
     * @param businessId business id
     * @return auto message templates
     */
    @Deprecated
    @GetMapping("/service/message/autoMessage/getAutoMessageTemplate")
    List<AutoMessageTemplateDTO> getAutoMessageTemplate(@RequestParam("businessId") Integer businessId);

    /**
     * Get auto message template with preview message
     *
     * @param params business id and appointment id
     * @return auto message template with preview message
     */
    @PostMapping("/service/message/autoMessage/getPreviewAutoMessageTemplate")
    AutoMessageTemplatePreviewDTO getPreviewAutoMessageTemplate(@RequestBody AutoMessageTemplatePreviewParams params);
}
