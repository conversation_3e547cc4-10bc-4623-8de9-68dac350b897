package com.moego.server.message.params;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-07-19 09:58
 */
@Data
public class TwilioReceiveMessageCallbackParams {

    private Long companyId;
    private Integer businessId;
    private String smsMessageSid;
    private String to;
    private String from;
    private String body;

    @Deprecated
    private String mediaUrl0;

    @Deprecated
    private String mediaUrl1;

    private Integer customerId;
    private String customerName;

    // mms media
    private List<TwilioReceiveMessageMedia> mediaList;

    @Override
    public String toString() {
        return ("{" + "\"businessId\":"
                + businessId
                + ",\"smsMessageSid\":\""
                + smsMessageSid
                + '\"'
                + ",\"to\":\""
                + to
                + '\"'
                + ",\"from\":\""
                + from
                + '\"'
                + ",\"body\":\""
                + body
                + '\"'
                + ",\"mediaUrl0\":\""
                + mediaUrl0
                + '\"'
                + ",\"mediaUrl1\":\""
                + mediaUrl1
                + '\"'
                + ",\"customerId\":"
                + customerId
                + "}");
    }
}
