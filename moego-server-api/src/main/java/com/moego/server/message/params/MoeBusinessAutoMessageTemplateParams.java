package com.moego.server.message.params;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-07-26 23:37
 */
@Data
public class MoeBusinessAutoMessageTemplateParams {

    private Integer id;
    private Integer businessId;
    private Integer type;
    private String body;

    @Min(1)
    @Max(2)
    private Integer status;
}
