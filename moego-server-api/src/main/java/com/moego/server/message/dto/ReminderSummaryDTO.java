package com.moego.server.message.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-09-29 02:17
 */
@Data
public class ReminderSummaryDTO {

    /**
     * appointmentReminderType
     * 1 查询7天内将要发送的预约   默认
     * 2 查询最近7天内需要发送的预约
     */
    @JsonIgnore
    public static Integer APPOINTMENT_REMINDER_TYPE_DEFAULT = 1;

    @JsonIgnore
    public static Integer APPOINTMENT_REMINDER_TYPE_REAL = 2;

    // appointmentReminderType

    private Long unconfirmedAppointment = 0L;
    private Long rebook = 0L;
    private Long repeat = 0L;
    private Long birthday = 0L;
}
