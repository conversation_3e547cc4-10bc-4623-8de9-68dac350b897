package com.moego.server.message.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.moego.common.params.FilterParams;
import com.moego.common.params.PageParams;
import com.moego.common.params.QueryParams;
import com.moego.idl.models.message.v2.MessageModel;
import com.moego.server.customer.params.CustomerSearchStatusVo;
import jakarta.validation.constraints.Size;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2020-07-05 15:46
 * FIXME(JX, P2): 这个类太多字段, 入参不应该这样, 考虑给用到这个类的各接口单独制定入参, 限定入参结构
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Deprecated
public class SendMessagesParams extends PageParams {

    public static final String EXT_PARAM_NAME_BUSINESS = "business";
    public static final String EXT_PARAM_NAME_CUSTOMER = "customer";
    public static final String EXT_PARAM_NAME_GROOMINGID = "groomingId";
    public static final String EXT_PARAM_NAME_UNSIGNID = "unsignId";
    public static final String EXT_PARAM_NAME_APPOINTMENT = "appointment";
    public static final String EXT_PARAM_NAME_CUSTOMER_PETS = "customerPets";
    public static final String EXT_PARAM_NAME_AGREEMENT_ID = "agreementId";
    public static final String EXT_PARAM_NAME_AGREEMENT = "agreement";
    public static final String EXT_PARAM_NAME_AGREEMENT_SING = "agreementSign";
    public static final String EXT_PARAM_NAME_APPOINTMENT_REMINDER = "appointmentReminder";
    public static final String EXT_PARAM_NAME_REBOOK_REMINDER = "rebookReminder";
    public static final String EXT_PARAM_NAME_ORDER_UUID = "orderUuid";
    private Integer messageDetailId;
    private Integer targetType;

    private Integer targetId;

    /**
     * 给business发消email时，传递ownerEmail
     */
    private String targetEmail;

    private List<String> notificationEmailList;

    private Integer type;
    private Integer method;
    private Integer source;
    private Integer messageType;
    private Integer businessId;
    private Long companyId;
    private String dateFormat;
    private Integer timeFormatType;
    private Integer staffId;
    private Integer accountId;
    SendMessageCustomerParams customer = new SendMessageCustomerParams();
    private List<SendMessageCustomerParams> customerList;
    /**
     * 用户mass text发送，true时给所有符合筛选条件client发送
     */
    private Boolean isAllCustomer;
    /**
     * isAllCustomer为true时 client筛选条件 keyword
     */
    private String keyword;
    /**
     * isAllCustomer为true时 client筛选条件 tagIds
     */
    private List<Integer> tagIds;
    /**
     * isAllCustomer为true时 client筛选项目 client status
     */
    private CustomerSearchStatusVo statusSetting;

    /**
     * Is smart client list filter
     */
    private boolean isSmartFilter;
    /**
     * Smart client list filters
     */
    private FilterParams filters;
    /**
     * Smart client list queries
     */
    private QueryParams queries;

    @Size(max = 2048)
    private String messageBody;

    private Integer serviceId;
    private String businessOwnerEmail;
    private String businessName;
    /**
     * email用的subject
     */
    private String subject;

    private String emailBody; // email body 与 sms body 区分

    private Map<String, Object> extParams = new HashMap<>();

    private Boolean needRefreshCOF;

    // 只在服务内部透传
    @JsonIgnore
    MessageModel.DisplayExtraInfo displayExtraInfo;
    // 只在服务内部透传
    // 是重发的消息需要更新 detail extension 表
    // send 方法都不返回messageDetailId，只能在 send 方法里更新了
    @JsonIgnore
    Integer resendSourceMessageId;

    @Override
    public String toString() {
        return ("{" + "\"messageDetailId\":"
                + messageDetailId
                + ",\"targetType\":"
                + targetType
                + ",\"targetId\":"
                + targetId
                + ",\"type\":"
                + type
                + ",\"method\":"
                + method
                + ",\"source\":"
                + source
                + ",\"messageType\":"
                + messageType
                + ",\"businessId\":"
                + businessId
                + ",\"staffId\":"
                + staffId
                + ",\"customer\":"
                + customer
                + ",\"customerList\":"
                + customerList
                + ",\"messageBody\":\""
                + messageBody
                + '\"'
                + ",\"serviceId\":"
                + serviceId
                + ",\"extParams\":"
                + extParams
                + "},\"SendMessagesParams\":"
                + super.toString()
                + "}");
    }
}
