package com.moego.server.business.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MoeWorkingDailyDTO {

    private Integer id;

    private Integer businessId;

    // FIXME: should be @NotNull
    private Integer staffId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    private Long createTime;

    private Long updateTime;

    private Byte dayOfWeek;

    private String timeRange;

    /**
     * (0:重复，1：不重复）
     */
    private Byte isRepeat;

    private Date untilDate;
}
