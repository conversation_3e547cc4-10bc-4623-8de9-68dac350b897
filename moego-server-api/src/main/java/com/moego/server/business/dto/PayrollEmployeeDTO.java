package com.moego.server.business.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class PayrollEmployeeDTO {

    private Integer staffId;
    private String staffName;
    private String avatarPath; // 增加头像字段，失效staff前端获取不到信息
    private BigDecimal serviceCommission;
    private BigDecimal tipsCommission;

    @Schema(description = "Staff 打卡总时长")
    private Double totalWorkingHour;

    private BigDecimal hourlyPay;
    private BigDecimal total;
}
