package com.moego.server.business.api;

import com.moego.server.business.dto.BusinessClosedDateDTO;
import com.moego.server.business.dto.ParsedCloseDate;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface IBusinessClosedDateService {
    @GetMapping("/service/business/closedDate/getAllCloseDate")
    List<ParsedCloseDate> getAllCloseDate(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/business/closedDate/getThisWeekClosedDate")
    List<BusinessClosedDateDTO> getThisWeekClosedDate(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/business/closedDate/getParsedCloseDateByStartDateEndDate")
    List<ParsedCloseDate> getParsedCloseDateByStartDateEndDate(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate);
}
