package com.moego.server.business.dto;

public class MoeBusinessPaymentMethodDto {

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.method
     *
     * @mbg.generated
     */
    private String method;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.sort
     *
     * @mbg.generated
     */
    private Integer sort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.inactive
     *
     * @mbg.generated
     */
    private Byte inactive;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.create_time
     *
     * @mbg.generated
     */
    private Integer createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.update_time
     *
     * @mbg.generated
     */
    private Integer updateTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payment_method.id
     *
     * @return the value of moe_business_payment_method.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payment_method.id
     *
     * @param id the value for moe_business_payment_method.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payment_method.business_id
     *
     * @return the value of moe_business_payment_method.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payment_method.business_id
     *
     * @param businessId the value for moe_business_payment_method.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payment_method.method
     *
     * @return the value of moe_business_payment_method.method
     *
     * @mbg.generated
     */
    public String getMethod() {
        return method;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payment_method.method
     *
     * @param method the value for moe_business_payment_method.method
     *
     * @mbg.generated
     */
    public void setMethod(String method) {
        this.method = method == null ? null : method.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payment_method.sort
     *
     * @return the value of moe_business_payment_method.sort
     *
     * @mbg.generated
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payment_method.sort
     *
     * @param sort the value for moe_business_payment_method.sort
     *
     * @mbg.generated
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payment_method.inactive
     *
     * @return the value of moe_business_payment_method.inactive
     *
     * @mbg.generated
     */
    public Byte getInactive() {
        return inactive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payment_method.inactive
     *
     * @param inactive the value for moe_business_payment_method.inactive
     *
     * @mbg.generated
     */
    public void setInactive(Byte inactive) {
        this.inactive = inactive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payment_method.status
     *
     * @return the value of moe_business_payment_method.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payment_method.status
     *
     * @param status the value for moe_business_payment_method.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payment_method.create_time
     *
     * @return the value of moe_business_payment_method.create_time
     *
     * @mbg.generated
     */
    public Integer getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payment_method.create_time
     *
     * @param createTime the value for moe_business_payment_method.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Integer createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payment_method.update_time
     *
     * @return the value of moe_business_payment_method.update_time
     *
     * @mbg.generated
     */
    public Integer getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payment_method.update_time
     *
     * @param updateTime the value for moe_business_payment_method.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Integer updateTime) {
        this.updateTime = updateTime;
    }
}
