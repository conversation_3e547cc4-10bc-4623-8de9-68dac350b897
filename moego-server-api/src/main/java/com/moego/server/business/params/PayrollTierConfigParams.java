package com.moego.server.business.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class PayrollTierConfigParams {

    @Schema(description = "tier start 金额，2位小数，第一个必须为0")
    @DecimalMin(value = "0")
    private BigDecimal start;

    @Schema(description = "tier end 金额，2位小数")
    @DecimalMin(value = "0")
    private BigDecimal end;

    @Schema(description = "tier rate，整数")
    @DecimalMax(value = "100")
    @DecimalMin(value = "0")
    private BigDecimal rate;
}
