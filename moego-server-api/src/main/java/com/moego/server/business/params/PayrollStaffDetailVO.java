package com.moego.server.business.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class PayrollStaffDetailVO {

    @JsonIgnore
    private Integer businessId;

    @JsonIgnore
    private Long tokenStaffId;

    @JsonIgnore
    private Long tokenCompanyId;

    @Schema(description = "查询Commission类型，1-service commission,2-hourly commission,3-tip commission")
    @NotNull
    private Byte type;

    @Schema(description = "staffId, 非必传参数，不传时，返回表头结构")
    private Integer staffId;

    @Schema(description = "查询开始日期")
    private String startDate;

    @Schema(description = "查询结束日期")
    private String endDate;

    @Schema(description = "分页参数 页数，非必传，默认1")
    @Min(1)
    private Integer pageNum;

    @Schema(description = "分页参数 分页大小，非必传，默认10")
    @Min(1)
    private Integer pageSize;

    @Schema(description = "是否查询全部 location")
    private Boolean isAllLocation;

    @Schema(description = "需要导出的 businessIds，传参数默认查询 token business 的数据")
    private List<Long> businessIds;
}
