package com.moego.server.business.params;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class ServiceAreaInsideBatchRequestV2 {

    @NotNull
    private List<Integer> staffIds;

    @NotNull
    private Double lat;

    @NotNull
    private Double lng;

    private String zipcode;

    // include
    @NotNull
    private String startDate;

    // include
    @NotNull
    private String endDate;
}
