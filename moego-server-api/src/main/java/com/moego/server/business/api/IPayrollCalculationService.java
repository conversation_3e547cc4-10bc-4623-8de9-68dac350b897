package com.moego.server.business.api;

import com.moego.server.business.dto.StaffPayrollCalculationDTO;
import java.math.BigDecimal;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface IPayrollCalculationService {
    /**
     * 根据 实收金额 和 tierRate 计算 commission
     *
     * @param staffPayrollCalculationDTO
     * @return
     */
    @PostMapping("/service/business/payroll/calculation")
    BigDecimal calculateCommissionByTierRate(@RequestBody StaffPayrollCalculationDTO staffPayrollCalculationDTO);
}
