package com.moego.server.retail.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class CreatePackageParams {
    @NotNull
    private Long companyId;

    @NotNull
    private Integer businessId;

    @Size(max = 255)
    private String name;

    @Size(max = 1023)
    private String description;

    private BigDecimal price;
    private Integer taxId;

    /**
     * 为了保证接口向后兼容，如果没传 isActive，则判断当前日期是否在 startDate 和 endDate 之间
     */
    private Boolean isActive;

    /**
     * 为了保证接口向后兼容，如果没传 expirationDays，则默认使用 endDate - startDate
     */
    @Min(1)
    @Max(99999999)
    @Schema(description = "过期天数，用一个魔法值 99999999 代表永不过期")
    private Integer expirationDays;

    /**
     * package 包含的 services，多个 service 共享 quantity
     *
     * <p> nullable 是为了接口向后兼容
     */
    @Nullable
    private List<@Valid Item> items;

    @Data
    public static class Item {
        @Positive
        private Integer quantity;

        @NotEmpty
        private List<@Valid Service> services;
    }

    @Data
    public static class Service {
        @Positive
        private Integer serviceId;

        @PositiveOrZero
        private BigDecimal unitPrice;

        /**
         * 冗余字段
         */
        @Nullable
        private String name;
    }
}
