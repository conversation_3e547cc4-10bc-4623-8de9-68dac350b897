package com.moego.server.customer.dto;

import com.moego.server.customer.params.CustomerSearchStatusVo;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CustomerSearchVo {

    private Integer pageNum;
    private Integer pageSize;
    private String keyword;
    private String findNameKeyword;
    private List<Integer> tagIds;
    /**
     * 1customer firstName 2 customer lastName 3 pet name
     */
    private Byte sortType;

    private CustomerSearchStatusVo statusSetting;

    private Integer searchBusinessId;
}
