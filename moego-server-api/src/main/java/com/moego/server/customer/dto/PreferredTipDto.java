package com.moego.server.customer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class PreferredTipDto {

    @Schema(description = "preferred tip开关")
    private Byte enable;

    @Schema(description = "0-by amount, 1-by percentage")
    private Byte tipType;

    @Schema(description = "by amount时，设置tips金额")
    private BigDecimal amount;

    @Schema(description = "by percentage时，设置百分比取值[1，100]")
    private Integer percentage;
}
