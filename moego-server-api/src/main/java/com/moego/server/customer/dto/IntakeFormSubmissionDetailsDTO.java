package com.moego.server.customer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/1/14 11:18 AM
 */
@Data
public class IntakeFormSubmissionDetailsDTO {

    private String title;
    private Integer id;
    private Integer formId;
    private Integer businessId;
    private Byte isRead;
    private Byte isCreate;

    @Schema(description = "是否card选项 1：显示 0：不显示")
    private Byte isCardShow;

    @Schema(description = "是否card选项 1： 必填 0：不必填")
    private Byte isCardRequired;

    private Long createTime;
    private Long updateTime;
    private String customerData;
    private String stripeCustomerId;
}
