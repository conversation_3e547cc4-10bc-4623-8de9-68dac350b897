package com.moego.server.customer.params;

import jakarta.annotation.Nullable;
import java.util.Set;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class FindCustomerIdsByKeywordParam {

    @Nullable
    private Long companyId;

    @Nullable
    private Integer businessId;

    @Nullable
    private Set<Integer> customerIds;

    @Nullable
    private String keyword;
}
