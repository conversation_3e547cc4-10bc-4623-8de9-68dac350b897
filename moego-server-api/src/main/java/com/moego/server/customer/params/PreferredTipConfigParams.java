package com.moego.server.customer.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(description = "设置要修改的字段， 不修改或者不设置的字段设置为null即可")
public class PreferredTipConfigParams {

    @JsonIgnore
    private Long companyId;

    @JsonIgnore
    private Integer businessId;

    @NotNull
    @Min(1)
    private Integer customerId;

    @Schema(description = "preferred tip开关: 0 close, 1 open")
    private Byte enable;

    @Schema(description = "0-by amount, 1-by percentage")
    @Max(1)
    @Min(0)
    private Byte tipType;

    @Schema(description = "by amount时，设置tips金额")
    @Min(0)
    private BigDecimal amount;

    @Schema(description = "by percentage时，设置百分比取值[1，100]")
    @Max(100)
    @Min(1)
    private Integer percentage;
}
