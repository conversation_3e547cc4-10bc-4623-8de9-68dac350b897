package com.moego.server.customer.api;

import com.moego.server.customer.dto.CustomerWithAddress;
import com.moego.server.customer.dto.CustomerWithAddressAndContact;
import com.moego.server.customer.dto.DashboardClientSummaryDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.dto.ReportWebApptCustomerRequest;
import com.moego.server.customer.dto.ReportWebApptCustomerResponse;
import com.moego.server.customer.dto.ReportWebPet;
import com.moego.server.customer.params.DashboardClientSummaryRequest;
import com.moego.server.customer.params.GetReportWebPetParam;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2021/11/16 5:19 PM
 */
public interface ICustomerReportService {
    /**
     * DONE(account structure): 入参的 business id 没有用，直接用的 petIds 和 customerIds
     */
    @PostMapping("/service/customer/report/getDashboardClientSummary")
    DashboardClientSummaryDTO getDashboardClientSummary(@RequestBody DashboardClientSummaryRequest request);

    /**
     * DONE(account structure): 无需处理
     */
    @PostMapping("/service/customer/report/queryCustomerBasicInfo")
    List<MoeBusinessCustomerDTO> queryCustomerBasicInfo(@RequestBody List<Integer> ids);

    /**
     * DONE(account structure): 无需处理
     */
    @PostMapping("/service/customer/report/getRecurringCustomerRateByPost")
    Integer getRecurringCustomerRateByPost(@RequestBody List<Integer> customerIds);

    /**
     * DONE(account structure): 保留 business 维度
     *
     * Get {@link ReportWebPet}s by {@link GetReportWebPetParam}.
     *
     * @param param {@link GetReportWebPetParam}
     * @return {@link ReportWebPet}s
     */
    @PostMapping("/service/customer/report/getReportWebPets")
    List<ReportWebPet> getReportWebPets(@RequestBody GetReportWebPetParam param);

    /**
     * DONE(account structure): 无需处理
     */
    @PostMapping("/service/customer/report/queryCustomerWithAddress")
    List<CustomerWithAddress> queryCustomerWithAddress(@RequestBody List<Integer> customerIds);

    /**
     * DONE(account structure): 无需处理
     */
    @PostMapping("/service/customer/report/queryCustomerWithAddressAndContact")
    List<CustomerWithAddressAndContact> queryCustomerWithAddressAndContact(@RequestBody List<Integer> customerIds);

    /**
     * DONE(account structure): 无需处理
     *
     * type: 0 return no address, 1 will contain address.
     */
    @PostMapping("/service/customer/report/queryReportWebApptCustomerInfo")
    ReportWebApptCustomerResponse queryReportWebApptCustomerInfo(@RequestBody ReportWebApptCustomerRequest request);
}
