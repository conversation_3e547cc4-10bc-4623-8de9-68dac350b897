package com.moego.server.customer.api;

import com.moego.common.dto.PageDTO;
import com.moego.common.response.ResponseResult;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerPetPetCodeDTO;
import com.moego.server.customer.dto.CustomerPetReminderDTO;
import com.moego.server.customer.dto.GroomingCalenderPetInfo;
import com.moego.server.customer.dto.WebsitePetSummaryDTO;
import com.moego.server.customer.params.CustomerPetAddParams;
import com.moego.server.customer.params.CustomerPetBirthdayReminderParams;
import com.moego.server.customer.params.CustomerPetUpdateParams;
import com.moego.server.grooming.params.CustomerIdWithPetIdsParams;
import jakarta.annotation.Nullable;
import java.util.List;
import java.util.Map;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2021/11/15 4:39 PM
 */
public interface IPetService {

    /**
     * DONE(account structure): 不需要兼容;
     * 返回所有pet，无论是否删除
     */
    @PostMapping("/service/customer/pet/getCustomerPetListByIdList")
    List<CustomerPetDetailDTO> getCustomerPetListByIdList(@RequestBody List<Integer> petIdList);

    /**
     * DONE(account structure): 不需要兼容;
     * 查询 pet 的信息
     * 包括 code、vaccine
     *
     * @param petIdList
     * @return
     */
    @PostMapping("/service/customer/pet/getCustomerPetInfoListByIdList")
    List<CustomerPetPetCodeDTO> getCustomerPetInfoListByIdList(@RequestBody List<Integer> petIdList);

    /**
     * DONE(account structure): 不需要兼容;
     * 返回未去世&未删除的pet信息，根据customerId查询
     *
     * @param customerId
     * @param businessId
     * @return
     */
    @GetMapping("/service/customer/pet/getPetNameBreedByCustomerId")
    List<GroomingCalenderPetInfo> getPetNameBreedByCustomerId(
            @RequestParam("customerId") Integer customerId, @RequestParam("businessId") @Deprecated Integer businessId);

    /**
     * DONE(account structure): 不需要兼容;
     * @param customerIdList
     * @return
     */
    @GetMapping("/service/customer/pet/getCustomerPetListByCustomerId")
    List<CustomerPetDetailDTO> getCustomerPetListByCustomerId(
            @RequestParam("customerIdList") List<Integer> customerIdList);

    /**
     * DONE(account structure): 不需要兼容;
     * @param petIdMap
     * @return
     */
    @PostMapping("/service/customer/pet/linkPet")
    boolean linkPet(@RequestBody Map<Integer, Long> petIdMap);

    /**
     * DONE(account structure): 不需要兼容;
     * 给grooming模块获取预约列表信息调用 获取宠物的name breed
     *
     * @param petIds petIds
     * @return list
     */
    @PostMapping("/service/customer/pet/getGroomingCalenderPetInfo")
    List<GroomingCalenderPetInfo> getGroomingCalenderPetInfo(@RequestBody List<Integer> petIds);

    /**
     * DONE(account structure): pet.business_id 含义已更改为 prefer location
     * birthday reminder 由 prefer location 发送
     * 接口不做改动, 但需注意该接口含义已改为查询 prefer location 为 business_id 对应的 location 的符合条件的 pet
     * 获取business customer pet reminder birthday
     *
     * @param customerPetBirthdayReminderParams
     * @return
     */
    @PostMapping("/service/customer/pet/getReminderBirthdayCustomerPet")
    PageDTO<CustomerPetReminderDTO> getReminderBirthdayCustomerPet(
            @RequestBody CustomerPetBirthdayReminderParams customerPetBirthdayReminderParams);

    /**
     * DONE(account structure): 保留 business （preferred location）维度发送 birthday reminder
     *
     * 获取business customer pet notification birthday
     *
     * @param customerPetBirthdayReminderParams
     * @return
     */
    @PostMapping("/service/customer/pet/getNotificationBirthdayCustomerPet")
    List<CustomerPetReminderDTO> getNotificationBirthdayCustomerPet(
            @RequestBody CustomerPetBirthdayReminderParams customerPetBirthdayReminderParams);

    /**
     * DONE(account structure): 已支持 company / business 鉴权
     *
     * 创建宠物，并记录与pet code的绑定关系
     *
     * @param tokenBusinessId
     * @param customerPetParams
     * @return
     */
    @PostMapping("/service/customer/pet/insertCustomerPet")
    ResponseResult<Integer> insertCustomerPet(
            @RequestParam(value = "tokenCompanyId", required = false) Long tokenCompanyId,
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestBody CustomerPetAddParams customerPetParams);

    /**
     * DONE: 已支持 company / business 鉴权
     */
    @GetMapping("/service/customer/pet/checkBusinessCustomerPet")
    Boolean checkBusinessCustomerPet(
            @RequestParam("petId") Integer petId,
            @RequestParam("businessId") Integer businessId,
            @RequestParam("customerId") Integer customerId);

    /**
     * DONE: 已支持 company / business 鉴权
     */
    @GetMapping("/service/customer/pet/checkBusinessPet")
    Boolean checkBusinessPet(@RequestParam("petId") Integer petId, @RequestParam("businessId") Integer businessId);

    /**
     * DONE(account structure): 无需改动
     *
     * Count pet between startId (inclusive) and endId (inclusive).
     *
     * @param startId startId, null means start at -infinity
     * @param endId   endId, null means end at +infinity
     * @return {@link WebsitePetSummaryDTO}
     */
    @GetMapping("/service/customer/pet/countBetween")
    WebsitePetSummaryDTO countBetween(
            @RequestParam(required = false) @Nullable Integer startId,
            @RequestParam(required = false) @Nullable Integer endId);

    /**
     * DONE(account structure): 已支持 company 鉴权
     *
     * Get pet with vaccine by pet id
     *
     * @param businessId businessId
     * @param customerId customerId
     * @param petId      petId
     * @return pet with vaccine
     */
    @PostMapping("/service/customer/pet/getPetWithVaccine")
    CustomerPetDetailDTO getPetWithVaccine(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("customerId") Integer customerId,
            @RequestParam("petId") Integer petId);

    /**
     * DONE(account structure): 已支持 company 鉴权
     *
     * Get pet with vaccine by customerId or petIds
     *
     * @param params businessId, customerId, petIds
     * @return pet with vaccine
     */
    @PostMapping("/service/customer/pet/getPetListWithVaccine")
    List<CustomerPetDetailDTO> getPetListWithVaccine(@RequestBody CustomerIdWithPetIdsParams params);

    /**
     * DONE(account structure): 已支持 company 鉴权
     *
     * Get all pet with vaccine by customerIds
     *
     * @param businessId businessId
     * @param customerIds customerIds
     * @return pet with vaccine
     */
    @GetMapping("/service/customer/pet/getAllPetListWithVaccine")
    List<CustomerPetDetailDTO> getAllPetListWithVaccine(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerIds") List<Integer> customerIds);

    /**
     * DONE(account structure): 已支持 company / business 鉴权
     *
     * Update pet profile and vaccine.
     *
     * @param updateParams update params
     * @return update result
     */
    @PostMapping("/service/customer/pet/updatePetWithVaccine")
    Boolean updatePetWithVaccine(
            @RequestParam("businessId") Integer businessId, @RequestBody CustomerPetUpdateParams updateParams);

    /**
     * DONE(account structure): 已支持 company / business 鉴权
     *
     * Batch update pet profile and vaccine.
     *
     * @param updateParamsList update params list
     * @return update result
     */
    @PostMapping("/service/customer/pet/batchUpdatePetWithVaccine")
    Boolean batchUpdatePetWithVaccine(
            @RequestParam("businessId") Integer businessId,
            @RequestBody List<CustomerPetUpdateParams> updateParamsList);
}
