package com.moego.server.customer.params;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class SearchPetParams {
    // company id, should not be null or 0
    @NotNull
    @Min(1)
    private Long companyId;

    // staff id, optional.
    // if not null, only return the pets that the staff can see (permission check)
    @Min(1)
    private Long staffId;

    // customer name or pet name, optional
    private String term;

    // pet type and breed filters, optional
    // each filter will be connected by OR
    private List<@NotNull @Valid PetTypeAndBreedFilter> petTypeAndBreedFilters;

    // pet weight filters, optional
    // each filter will be connected by OR
    private List<@NotNull @Valid PetWeightFilter> petWeightFilters;

    // pet coat types, optional
    // each filter will be connected by OR
    private List<@NotNull @Valid PetCoatTypeFilter> coatTypeFilters;

    // page num, default 1
    @Min(1)
    private Integer pageNum;
    // page size, default 10
    @Min(1)
    @Max(100)
    private Integer pageSize;

    @Data
    public static class PetTypeAndBreedFilter {
        @NotNull
        private Integer petTypeId;
        // pet breeds, empty means all breeds
        private List<@NotEmpty String> breedNames;
        // private List<Long> breedIds;
    }

    /**
     * 支持 petSizeId 和 weight 区间两种模式，两者只能选其一
     * 如果都传了，优先使用 petSizeId，忽略 weight 区间
     */
    @Data
    public static class PetWeightFilter {
        private Long petSizeId;

        // pet weight range (weightLow <= weight <= weightHigh)
        @Min(0)
        private Double weightLow;

        @Min(0)
        private Double weightHigh;
    }

    @Data
    public static class PetCoatTypeFilter {
        //        @NotEmpty
        //        private String coatTypeName;
        @NotNull
        private Long coatTypeId;
    }
}
