package com.moego.server.customer.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/10/24
 */
@Data
@Accessors(chain = true)
public class LinkCustomerDTO {

    private Integer customerId;
    private Integer businessId;
    private String firstName;
    private String lastName;
    private String customerCode;
    /**
     * link MoeGo account id
     */
    private Long accountId;
}
