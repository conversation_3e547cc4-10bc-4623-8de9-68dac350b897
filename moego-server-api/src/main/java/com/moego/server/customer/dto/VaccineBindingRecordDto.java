package com.moego.server.customer.dto;

import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class VaccineBindingRecordDto {

    private Integer vaccineBindingId;
    private Integer vaccineId;
    private String vaccineName;
    private String expirationDate;
    private List<String> documentUrls;
    private Byte source;
    private Byte verifyStatus;
}
