package com.moego.server.customer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@Accessors(chain = true)
public class PlaygroupDto {

    @Schema(description = "playgroup id")
    private Long playgroupId;

    @Schema(description = "playgroup name")
    private String name;

    @Schema(description = "playgroup color code")
    private String colorCode;
}
