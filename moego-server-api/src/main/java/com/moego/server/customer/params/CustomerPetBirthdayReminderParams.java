package com.moego.server.customer.params;

import com.moego.common.params.PageParams;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2020-08-16 16:40
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerPetBirthdayReminderParams extends PageParams {

    private Integer businessId;
    private String startDate;
    private String endDate;
    private String birthDay;
    private List<Integer> dismissIds;
}
