syntax = "proto3";

package exception;

option java_multiple_files = true;
option java_package = "com.moego.lib.common.exception";
option java_outer_classname = "ExceptionProto";

import "google/protobuf/empty.proto";

// The greeting service definition.
service Greeter {
  // Sends a greeting
  rpc Say<PERSON>ello (google.protobuf.Empty) returns (google.protobuf.Empty) {}
  rpc SayHello2 (google.protobuf.Empty) returns (google.protobuf.Empty) {}
}

service Helper {
  rpc Help (HelpRequest) returns (HelpResponse) {}
}

message HelpRequest {
  string name = 1;
}

message HelpResponse {
  string content = 1;
}