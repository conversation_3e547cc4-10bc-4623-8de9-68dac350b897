package com.moego.lib.common.exception.grpc;

import static org.assertj.core.api.Assertions.assertThatExceptionOfType;

import com.google.protobuf.Empty;
import com.moego.lib.common.exception.GreeterGrpc;
import com.moego.lib.common.exception.grpc.advice.GrpcAdvice;
import com.moego.lib.common.exception.grpc.advice.GrpcExceptionHandler;
import com.moego.lib.common.grpc.server.GrpcServerStartedEvent;
import io.grpc.ManagedChannelBuilder;
import io.grpc.Status;
import io.grpc.StatusException;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Controller;

@SpringBootTest(
        classes = ExceptionIntegrationTests.ExceptionConfig.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ExceptionIntegrationTests {

    static int port;

    @Test
    public void testExceptionHandlerWorks() {
        assertThatExceptionOfType(StatusRuntimeException.class)
                .isThrownBy(() -> blockingStub().sayHello(Empty.newBuilder().build()))
                .withMessageContaining("RuntimeException");
        assertThatExceptionOfType(
                        StatusRuntimeException.class) // throws StatusException, but receives StatusRuntimeException
                .isThrownBy(() -> blockingStub().sayHello2(Empty.newBuilder().build()))
                .withMessageContaining("IllegalStateException");
    }

    private static GreeterGrpc.GreeterBlockingStub blockingStub() {
        return com.moego.lib.common.exception.GreeterGrpc.newBlockingStub(
                ManagedChannelBuilder.forTarget("localhost:" + port)
                        .usePlaintext()
                        .build());
    }

    @EnableAutoConfiguration
    @Import(ExceptionService.class)
    @Configuration(proxyBeanMethods = false)
    public static class ExceptionConfig {

        @GrpcAdvice
        public static class CustomExceptionHandler {

            @GrpcExceptionHandler
            public StatusException illegalStateException(IllegalStateException e) {
                return Status.INTERNAL
                        .withDescription(e.getMessage())
                        .withCause(e)
                        .asException();
            }
        }

        @EventListener
        public void onApplicationEvent(GrpcServerStartedEvent event) {
            port = event.getSource().getPort();
        }
    }
}

@Controller
class ExceptionService extends GreeterGrpc.GreeterImplBase {

    @Override
    public void sayHello(Empty request, StreamObserver<Empty> responseObserver) {
        throw new RuntimeException("sayHello RuntimeException");
    }

    @Override
    public void sayHello2(Empty request, StreamObserver<Empty> responseObserver) {
        throw new IllegalStateException("sayHello2 IllegalStateException");
    }
}
