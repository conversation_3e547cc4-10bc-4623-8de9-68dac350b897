package com.moego.lib.common.autoconfigure.config;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.builder.SpringApplicationBuilder;

/**
 * {@link DefaultConfigEnvironmentPostProcessor} tester.
 *
 * <AUTHOR>
 */
class DefaultConfigEnvironmentPostProcessorIntegrationTests {

    @Test
    void testDefaultConfigurationSetAllowCircularReferences2True() {
        try (var ctx = new SpringApplicationBuilder(Empty.class)
                .web(WebApplicationType.NONE)
                .run()) {
            var env = ctx.getEnvironment();

            boolean allowCircular = env.getProperty("spring.main.allow-circular-references", Boolean.class, false);
            assertThat(allowCircular).isTrue();

            assertThat(env.getProperty("moego.http.server.observability.logging.enabled", Boolean.class))
                    .isFalse();
        }
    }

    @Test
    void testDefaultProfileSentryIsDisabled() {
        try (var ctx = new SpringApplicationBuilder(Empty.class)
                .web(WebApplicationType.NONE)
                .run()) {
            var env = ctx.getEnvironment();
            assertThat(env.getProperty("sentry.dsn", String.class)).isNull();
        }
    }

    @Test
    void testProdProfileSentryIsEnabled() {
        try (var ctx = new SpringApplicationBuilder(Empty.class)
                .web(WebApplicationType.NONE)
                .profiles("prod")
                .run()) {
            var env = ctx.getEnvironment();
            assertThat(env.getProperty("sentry.dsn", String.class)).isNotNull();
            assertThat(env.getProperty("sentry.environment", String.class)).isEqualTo("prod");
        }
    }

    @ParameterizedTest
    @ValueSource(strings = {"prod", "staging"})
    void
            testProfileBasedDefaultConfiguration_whenConfigurationNotSetByApp_thenShouldUseProfileBasedDefaultConfiguration(
                    String profile) {
        try (var ctx = new SpringApplicationBuilder(Empty.class)
                .web(WebApplicationType.NONE)
                .profiles(profile)
                .run()) {
            var env = ctx.getEnvironment();

            assertThat(env.getProperty("spring.datasource.hikari.maximum-pool-size", Integer.class))
                    .isEqualTo(100);
        }
    }

    @ParameterizedTest
    @ValueSource(strings = {"prod", "staging"})
    void testProfileBasedDefaultConfiguration_whenConfigurationSetByApp_thenShouldUseConfigurationSetByApp(
            String profile) {
        try (var ctx = new SpringApplicationBuilder(Empty.class)
                .web(WebApplicationType.NONE)
                .profiles(profile)
                .run("--spring.datasource.hikari.maximum-pool-size=20")) {
            var env = ctx.getEnvironment();

            assertThat(env.getProperty("spring.datasource.hikari.maximum-pool-size", Integer.class))
                    .isEqualTo(20);
        }
    }

    static class Empty {}
}
