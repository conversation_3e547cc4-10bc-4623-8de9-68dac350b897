package com.moego.lib.common.autoconfigure.config;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import java.util.Map;
import org.assertj.core.api.Condition;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.ConfigurableEnvironment;

/**
 * {@link RemoveProxyPropertiesEnvironmentPostProcessor} tester.
 */
@ExtendWith(OutputCaptureExtension.class)
class RemoveProxyPropertiesEnvironmentPostProcessorTest {

    /**
     * {@link RemoveProxyPropertiesEnvironmentPostProcessor#removeProxyProperties()}
     */
    @Test
    void removeProxyProperties_whenNotRemoveProxyProperties_thenRemoveProxyPropertiesShouldNotBeCalled() {
        RemoveProxyPropertiesEnvironmentPostProcessor processor =
                mock(RemoveProxyPropertiesEnvironmentPostProcessor.class);

        ConfigurableEnvironment env = mock(ConfigurableEnvironment.class);
        when(env.getProperty(
                        RemoveProxyPropertiesEnvironmentPostProcessor.REMOVE_PROXY_PROPERTIES, String.class, "false"))
                .thenReturn("false");

        processor.postProcessEnvironment(env, mock(SpringApplication.class));

        verify(processor, never()).removeProxyProperties();
    }

    /**
     * {@link RemoveProxyPropertiesEnvironmentPostProcessor#removeProxyProperties()}
     */
    @Test
    void removeProxyProperties_whenRemoveProxyProperties_thenNoProxyPropertiesShouldBePresent(CapturedOutput output) {
        ConfigurableApplicationContext ctx = new SpringApplicationBuilder(Cfg.class)
                .properties(GrpcProperties.Server.PREFIX + ".enabled=false")
                .run("--REMOVE_PROXY_PROPERTIES=true");

        assertThat(System.getProperties()).hasEntrySatisfying(new Condition<>() {
            @Override
            public boolean matches(Map.Entry<Object, Object> value) {
                return !RemoveProxyPropertiesEnvironmentPostProcessor.PROXY_PROPERTIES.contains(
                        String.valueOf(value.getKey()));
            }
        });

        assertThat(output).contains("Removed proxy system properties");

        ctx.close();
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    static class Cfg {}
}
