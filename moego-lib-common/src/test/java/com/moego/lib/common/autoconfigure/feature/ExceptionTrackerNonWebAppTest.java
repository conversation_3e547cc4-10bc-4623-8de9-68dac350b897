package com.moego.lib.common.autoconfigure.feature;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

import com.moego.lib.common.exception.grpc.advice.UncaughtExceptionHandler;
import io.sentry.IHub;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;

/**
 * {@link ExceptionTracker} tester.
 */
class ExceptionTrackerNonWebAppTest {

    private final ApplicationContextRunner runner = new ApplicationContextRunner()
            .withUserConfiguration(ExceptionTracker.class)
            .withBean(IHub.class, () -> mock(IHub.class));

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).hasSingleBean(ExceptionTracker.class);
            assertThat(context).doesNotHaveBean(ExceptionTracker.Grpc.Sentry.class);
            assertThat(context).doesNotHaveBean(UncaughtExceptionHandler.class);
        });
    }

    @Test
    public void testSentryEnabled() {
        runner.withPropertyValues("sentry.dsn=xx").run(context -> {
            assertThat(context).hasSingleBean(ExceptionTracker.class);
            assertThat(context).hasSingleBean(ExceptionTracker.Grpc.Sentry.class);
            assertThat(context).hasSingleBean(UncaughtExceptionHandler.class);
        });
    }
}
