package com.moego.lib.common;

import com.moego.lib.common.grpc.server.GrpcServerStartedEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

@Configuration(proxyBeanMethods = false)
public class GrpcServerPortSupplier {

    static int port;

    @EventListener
    public void onGrpcServerStarted(GrpcServerStartedEvent event) {
        port = event.getSource().getPort();
    }

    public static int getPort() {
        return port;
    }
}
