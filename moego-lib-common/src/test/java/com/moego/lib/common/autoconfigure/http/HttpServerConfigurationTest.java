package com.moego.lib.common.autoconfigure.http;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.WebApplicationContextRunner;

/**
 * {@link HttpServerConfiguration} tester.
 */
class HttpServerConfigurationTest {

    private final WebApplicationContextRunner runner =
            new WebApplicationContextRunner().withUserConfiguration(HttpServerConfiguration.class);

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).hasSingleBean(HttpServerConfiguration.class);
        });
    }

    @Test
    void testDisableHttp() {
        runner.withPropertyValues(HttpProperties.PREFIX + ".enabled=false").run(context -> {
            assertThat(context).doesNotHaveBean(HttpServerConfiguration.class);
        });
    }

    @Test
    void testDisableHttpServer() {
        runner.withPropertyValues(HttpProperties.PREFIX + ".server.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(HttpServerConfiguration.class);
                });
    }
}
