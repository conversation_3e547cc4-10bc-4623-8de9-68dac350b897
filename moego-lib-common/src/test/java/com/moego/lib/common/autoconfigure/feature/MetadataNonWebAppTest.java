package com.moego.lib.common.autoconfigure.feature;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.autoconfigure.http.HttpProperties;
import com.moego.lib.common.observability.tracing.grpc.GrpcMetadataClientInterceptor;
import com.moego.lib.common.observability.tracing.grpc.GrpcMetadataServerInterceptor;
import com.moego.lib.common.observability.tracing.http.feign.MetadataRequestInterceptor;
import com.moego.lib.common.observability.tracing.http.mvc.MetadataFilter;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * {@link Metadata} tester.
 */
public class MetadataNonWebAppTest {

    private final ApplicationContextRunner runner =
            new ApplicationContextRunner().withUserConfiguration(Metadata.class);

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).doesNotHaveBean(MetadataFilter.class);
            assertThat(context).hasSingleBean(MetadataRequestInterceptor.class);
            assertThat(context).hasSingleBean(GrpcMetadataServerInterceptor.class);
            assertThat(context).hasSingleBean(GrpcMetadataClientInterceptor.class);
        });
    }

    @Test
    public void testHttpDisabled() {
        runner.withPropertyValues(HttpProperties.PREFIX + ".enabled=false").run(context -> {
            assertThat(context).doesNotHaveBean(WebMvcConfigurer.class);
        });
    }
}
