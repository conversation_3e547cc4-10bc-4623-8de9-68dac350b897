package com.moego.lib.common.thread;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.util.ThreadPoolUtil;
import java.time.Duration;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicBoolean;
import org.junit.jupiter.api.Test;

/**
 * {@link ExecutorServiceWrapper} tester.
 */
class ExecutorServiceWrapperTest {

    @Test
    void testTransmissibleThreadPool() throws InterruptedException {
        MetadataContext mc = new MetadataContext();
        mc.put(String.class, "test");
        ThreadContextHolder.set(new ThreadContext(mc));

        ExecutorService pool = ThreadPoolUtil.newExecutorService(
                1, 1, Duration.ofSeconds(0), 1, "test-", new ThreadPoolExecutor.CallerRunsPolicy());

        final int threadCount = 100;

        AtomicBoolean hasError = new AtomicBoolean(false);

        CountDownLatch latch = new CountDownLatch(threadCount);
        new Thread(() -> {
                    if (ThreadContextHolder.get() == null) hasError.set(true);
                    for (int i = 0; i < threadCount; i++) {
                        pool.execute(() -> {
                            if (ThreadContextHolder.get() == null) hasError.set(true);
                            pool.submit(() -> {
                                if (ThreadContextHolder.get() == null) hasError.set(true);
                                new Thread(() -> {
                                            pool.submit(() -> {
                                                if (ThreadContextHolder.get() == null) hasError.set(true);
                                            });
                                            if (ThreadContextHolder.get() == null) hasError.set(true);
                                            latch.countDown();
                                        })
                                        .start();
                            });
                        });
                    }
                })
                .start();

        latch.await();

        assertThat(hasError.get()).isFalse();

        pool.shutdown();
    }
}
