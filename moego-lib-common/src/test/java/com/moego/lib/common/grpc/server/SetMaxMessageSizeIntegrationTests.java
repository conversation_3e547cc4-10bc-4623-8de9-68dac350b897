package com.moego.lib.common.grpc.server;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;

import com.moego.api.echo.v1.EchoUiGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.client.GrpcClient;
import com.moego.model.echo.v1.EchoRequest;
import com.moego.model.echo.v1.EchoResponse;
import com.moego.svc.echo.v1.EchoGrpc;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import java.util.concurrent.atomic.AtomicInteger;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

@ExtendWith(OutputCaptureExtension.class)
class SetMaxMessageSizeIntegrationTests {

    static AtomicInteger uiPort = new AtomicInteger();
    static AtomicInteger svcPort = new AtomicInteger();

    @Test
    void testClientSideMaxMessageSize() {
        String messageLessThan10Bytes = "12";
        String messageGreaterThan10Bytes = "12345678901234567890";

        ConfigurableApplicationContext svcApp = new SpringApplicationBuilder(SvcConfig.class)
                .web(WebApplicationType.NONE)
                .properties("moego.grpc.server.port=0")
                .properties("moego.grpc.server.observability.metrics.enabled=false")
                .run();

        ConfigurableApplicationContext uiApp = new SpringApplicationBuilder(UiConfig.class)
                .web(WebApplicationType.NONE)
                .properties("moego.grpc.server.port=0")
                .properties("moego.grpc.server.observability.metrics.enabled=false")
                // FIXME(Freeman): why this property doesn't work?
                //            .properties("moego.grpc.client.max-inbound-message-size=10B")
                .properties("moego.grpc.client.channels[echo]=localhost:" + svcPort.get())
                .run("--moego.grpc.client.max-inbound-message-size=10B");

        ManagedChannel channel = ManagedChannelBuilder.forAddress("localhost", uiPort.get())
                .usePlaintext()
                .build();
        EchoUiGrpc.EchoUiBlockingStub client = EchoUiGrpc.newBlockingStub(channel);

        EchoResponse resp = client.echo(
                EchoRequest.newBuilder().setMessage(messageLessThan10Bytes).build());
        assertThat(resp.getMessage()).isEqualTo(messageLessThan10Bytes);

        assertThatExceptionOfType(StatusRuntimeException.class)
                .isThrownBy(() -> client.echo(EchoRequest.newBuilder()
                        .setMessage(messageGreaterThan10Bytes)
                        .build()))
                .withMessageContaining("RESOURCE_EXHAUSTED: gRPC message exceeds maximum size 10");

        channel.shutdown();
        uiApp.close();
        svcApp.close();
    }

    @Test
    void testServerSideMaxMessageSize(CapturedOutput output) {
        String messageLessThan10Bytes = "12";
        String messageGreaterThan10Bytes = "12345678901234567890";

        ConfigurableApplicationContext svcApp = new SpringApplicationBuilder(SvcConfig.class)
                .web(WebApplicationType.NONE)
                .properties("moego.grpc.server.port=0")
                .properties("moego.grpc.server.observability.metrics.enabled=false")
                .run("--moego.grpc.server.max-inbound-message-size=10B");

        ManagedChannel channel = ManagedChannelBuilder.forAddress("localhost", svcPort.get())
                .usePlaintext()
                .build();
        EchoGrpc.EchoBlockingStub client = EchoGrpc.newBlockingStub(channel);

        EchoResponse resp = client.echo(
                EchoRequest.newBuilder().setMessage(messageLessThan10Bytes).build());
        assertThat(resp.getMessage()).isEqualTo(messageLessThan10Bytes);

        assertThatExceptionOfType(StatusRuntimeException.class)
                .isThrownBy(() -> client.echo(EchoRequest.newBuilder()
                        .setMessage(messageGreaterThan10Bytes)
                        .build()));
        assertThat(output).contains("RESOURCE_EXHAUSTED: gRPC message exceeds maximum size 10");

        channel.shutdown();
        svcApp.close();
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    static class UiConfig {

        @GrpcService
        static class EchoUiController extends EchoUiGrpc.EchoUiImplBase {

            @GrpcClient("echo")
            EchoGrpc.EchoBlockingStub echoStub;

            @Override
            @Auth(AuthType.ANONYMOUS)
            public void echo(EchoRequest request, StreamObserver<EchoResponse> responseObserver) {
                EchoResponse resp = echoStub.echo(request);

                responseObserver.onNext(resp);
                responseObserver.onCompleted();
            }
        }

        @EventListener
        public void onServerStarted(GrpcServerStartedEvent event) {
            uiPort.set(event.getSource().getPort());
        }
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    static class SvcConfig {

        @GrpcService
        static class EchoSvcController extends EchoGrpc.EchoImplBase {

            @Override
            public void echo(EchoRequest request, StreamObserver<EchoResponse> responseObserver) {
                String msg = request.getMessage();
                EchoResponse response =
                        EchoResponse.newBuilder().setMessage(msg).build();

                responseObserver.onNext(response);
                responseObserver.onCompleted();
            }
        }

        @EventListener
        public void onServerStarted(GrpcServerStartedEvent event) {
            svcPort.set(event.getSource().getPort());
        }
    }
}
