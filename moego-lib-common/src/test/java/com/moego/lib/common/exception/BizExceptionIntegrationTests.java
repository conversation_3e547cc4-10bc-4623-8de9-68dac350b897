package com.moego.lib.common.exception;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.lib.common.exception.ExceptionUtil.extractCode;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;

@SpringBootTest(
        classes = BizExceptionIntegrationTests.Config.class,
        webEnvironment = SpringBootTest.WebEnvironment.MOCK)
public class BizExceptionIntegrationTests {

    @Autowired
    private HelperGrpc.HelperBlockingStub helperBlockingStub;

    @Test
    void testException_whenExpectedException() {
        HelpResponse response;
        try {
            response = helperBlockingStub.help(
                    HelpRequest.newBuilder().setName("freeman").build());
        } catch (StatusRuntimeException e) {
            if (Code.CODE_SERVER_ERROR != extractCode(e)) {
                throw e;
            }
            response = HelpResponse.newBuilder().setContent("I can help you").build();
        }
        assertThat(response.getContent()).isEqualTo("I can help you");
    }

    @Test
    void testException_whenNotExpectedException() {
        assertThatExceptionOfType(StatusRuntimeException.class)
                .isThrownBy(() -> helperBlockingStub.help(
                        HelpRequest.newBuilder().setName("freeman").build()))
                .withMessageContaining("I can't help you");
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    @GrpcService
    static class Config extends HelperGrpc.HelperImplBase {

        @Override
        public void help(HelpRequest request, StreamObserver<HelpResponse> responseObserver) {
            throw bizException(Code.CODE_SERVER_ERROR, "I can't help you");
        }
    }
}
