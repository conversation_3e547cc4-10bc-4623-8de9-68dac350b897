package com.moego.lib.common.autoconfigure.feature;

import static org.assertj.core.api.Assertions.assertThat;

import com.freemanan.cr.core.anno.Action;
import com.freemanan.cr.core.anno.ClasspathReplacer;
import com.freemanan.cr.core.anno.Verb;
import com.moego.lib.common.observability.metrics.prometheus.timer.TimerMetricsAspect;
import com.moego.lib.common.thread.ThreadPoolMetricExporter;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.WebApplicationContextRunner;

/**
 * {@link Metrics} tester.
 */
@ClasspathReplacer({@Action(verb = Verb.EXCLUDE, value = "simpleclient*.jar")})
class MetricsWebAppNoPrometheusTest {

    private final WebApplicationContextRunner runner =
            new WebApplicationContextRunner().withUserConfiguration(Metrics.class);

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).doesNotHaveBean(ThreadPoolMetricExporter.class);
            assertThat(context).doesNotHaveBean(TimerMetricsAspect.class);
            assertThat(context).doesNotHaveBean("com.moego.lib.common.autoconfigure.feature.Metrics$Http$Prometheus");
            assertThat(context).doesNotHaveBean("com.moego.lib.common.autoconfigure.feature.Metrics$Grpc$Prometheus");
        });
    }
}
