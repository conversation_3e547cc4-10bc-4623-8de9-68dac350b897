package com.moego.lib.common.util;

import static com.freemanan.cr.core.anno.Verb.EXCLUDE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;

import com.freemanan.cr.core.anno.Action;
import com.freemanan.cr.core.anno.ClasspathReplacer;
import com.google.protobuf.Int32Value;
import com.google.protobuf.StringValue;
import com.google.protobuf.Struct;
import com.google.protobuf.Value;
import com.google.protobuf.util.Structs;
import com.google.protobuf.util.Values;
import com.google.rpc.Code;
import com.moego.idl.models.grey_gateway.v1.GreyItemModel;
import com.moego.lib.common.core.TypeRef;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

/**
 * {@link JsonUtil}.
 */
class JsonUtilTest {

    @Nested
    @DisplayName("Basic Java Object Serialization Tests")
    class BasicSerializationTests {

        @Test
        @DisplayName("Should serialize Java object to JSON")
        void shouldSerializeObjectToJson() {
            // Given
            User user = new User().setName("Freeman").setAge(18);

            // When
            String json = JsonUtil.toJson(user);

            // Then
            assertThat(json).isEqualTo("{\"name\":\"Freeman\",\"age\":18}");
        }

        @Test
        @DisplayName("Should handle null properties when serializing")
        void shouldHandleNullPropertiesWhenSerializing() {
            // Given
            User userWithNullProperty = new User().setName(null).setAge(18);

            // When
            String json = JsonUtil.toJson(userWithNullProperty);

            // Then
            assertThat(json).isEqualTo("{\"name\":null,\"age\":18}");
        }

        @Test
        @DisplayName("Should handle null and empty objects when serializing")
        void shouldHandleNullAndEmptyObjectsWhenSerializing() {
            assertThat(JsonUtil.toJson(null)).isEqualTo("null");
            assertThat(JsonUtil.toJson(new Object())).isEqualTo("{}");
        }
    }

    @Nested
    @DisplayName("Basic Java Object Deserialization Tests")
    class BasicDeserializationTests {

        @Test
        @DisplayName("Should deserialize JSON to Java object")
        void shouldDeserializeJsonToObject() {
            // Given
            String json = "{\"name\":\"Freeman\",\"age\":18}";

            // When
            User user = JsonUtil.toBean(json, User.class);

            // Then
            assertThat(user).isNotNull();
            assertThat(user.getName()).isEqualTo("Freeman");
            assertThat(user.getAge()).isEqualTo(18);
        }

        @Test
        @DisplayName("Should handle partial JSON when deserializing")
        void shouldHandlePartialJsonWhenDeserializing() {
            // Given
            String json = "{\"age\":18}";

            // When
            User user = JsonUtil.toBean(json, User.class);

            // Then
            assertThat(user.getAge()).isEqualTo(18);
            assertThat(user.getName()).isNull();
        }

        @Test
        @DisplayName("Should ignore unrecognized properties when deserializing")
        void shouldIgnoreUnrecognizedPropertiesWhenDeserializing() {
            // Given
            String jsonWithUnrecognizedProperty = "{\"name\":\"Freeman\",\"ages\":18}";

            // When
            User user = JsonUtil.toBean(jsonWithUnrecognizedProperty, User.class);

            // Then
            assertThat(user.getName()).isEqualTo("Freeman");
            assertThat(user.getAge()).isNull();
        }
    }

    @Nested
    @DisplayName("Protobuf Deserialization Tests")
    class ProtobufDeserializationTests {

        @Test
        @DisplayName("Should deserialize JSON to Protobuf message")
        void shouldDeserializeJsonToProtobufMessage() {
            // Given
            String json = "{\"name\":\"Freeman\",\"age\":18}";

            // When
            Struct struct = JsonUtil.toBean(json, Struct.class);

            // Then
            assertThat(struct.getFieldsCount()).isEqualTo(2);
            assertThat(struct.getFieldsOrThrow("name").getStringValue()).isEqualTo("Freeman");
            assertThat(struct.getFieldsOrThrow("age").getNumberValue()).isEqualTo(18.0);

            // When
            Value value = JsonUtil.toBean(json, Value.class);

            // Then
            assertThat(value.hasStructValue()).isTrue();
        }

        @Test
        @DisplayName("Should deserialize string or number to Protobuf enum")
        void shouldDeserializeStringOrNumberToProtobufEnum() {
            // Given
            var json = "[\"OK\", 1, \"UNRECOGNIZED_ENUM\", 100]";

            // When
            var actual = JsonUtil.toList(json, Code.class);

            // Then
            var expected = List.of(Code.OK, Code.CANCELLED, Code.UNRECOGNIZED, Code.UNRECOGNIZED);
            assertThat(actual).isEqualTo(expected);

            // Given illegal enum format
            var json1 = "[{}]";

            // Then
            assertThatCode(() -> JsonUtil.toList(json1, Code.class))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessageContaining("Can't deserialize protobuf enum");
        }
    }

    @Nested
    @DisplayName("Protobuf Serialization Tests")
    class ProtobufSerializationTests {

        @Test
        @DisplayName("Should serialize Protobuf message to JSON")
        void shouldSerializeProtobufMessageToJson() {
            // Given
            GreyItemModel defaultModel = GreyItemModel.newBuilder().build();

            // When
            String json = JsonUtil.toJson(defaultModel);

            // Then
            assertThat(json)
                    .isEqualTo(
                            "{\"id\":\"0\",\"name\":\"\",\"namespace\":\"\",\"description\":\"\",\"jiraTickets\":[],\"svcBranchMap\":{}}");
        }

        @Test
        @DisplayName("Should serialize Protobuf message with non-empty fields")
        void shouldSerializeProtobufMessageWithNonEmptyFields() {
            // Given
            GreyItemModel model =
                    GreyItemModel.newBuilder().addJiraTickets("TECH-1").build();

            // When
            String json = JsonUtil.toJson(model);

            // Then
            assertThat(json)
                    .isEqualTo(
                            "{\"id\":\"0\",\"name\":\"\",\"namespace\":\"\",\"description\":\"\",\"jiraTickets\":[\"TECH-1\"],\"svcBranchMap\":{}}");
        }

        @Test
        @DisplayName("Should serialize Protobuf enum as number")
        void shouldSerializeProtobufEnumAsNumber() {
            // Given
            Code code = Code.OK;

            // When
            String json = JsonUtil.toJson(code);

            // Then
            assertThat(json).isEqualTo("0");

            // Given
            var codes = List.of(Code.OK, Code.CANCELLED);

            // When
            String json1 = JsonUtil.toJson(codes);

            // Then
            assertThat(json1).isEqualTo("[0,1]");

            // Given UNRECOGNIZED, should throw exception
            var codes2 = List.of(Code.OK, Code.UNRECOGNIZED);

            assertThatCode(() -> JsonUtil.toJson(codes2))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessageContaining("Can't get the number of an unknown enum value");
        }
    }

    @Nested
    @DisplayName("Field Name Handling Tests")
    class FieldNameHandlingTests {

        @Test
        @DisplayName("Should handle different field name formats in Protobuf")
        void shouldHandleDifferentFieldNameFormatsInProtobuf() {
            // Given
            GreyItemModel model =
                    GreyItemModel.newBuilder().addJiraTickets("TECH-1").build();

            // When/Then - Standard field name
            assertThat(JsonUtil.toBean("{\"jiraTickets\":[\"TECH-1\"]}", GreyItemModel.class))
                    .isEqualTo(model);

            // When/Then - Snake case field name
            assertThat(JsonUtil.toBean("{\"jira_tickets\":[\"TECH-1\"]}", GreyItemModel.class))
                    .isEqualTo(model);

            // When/Then - Incorrect field name
            assertThat(JsonUtil.toBean("{\"jiratickets\":[\"TECH-1\"]}", GreyItemModel.class))
                    .isNotEqualTo(model);
        }
    }

    @Test
    @ClasspathReplacer({@Action(verb = EXCLUDE, value = "com.google.protobuf:protobuf-java")})
    void toBean_whenNoProtobuf_thenWorksFine() {
        assertThatCode(() -> Class.forName("com.google.protobuf.Message")).isInstanceOf(ClassNotFoundException.class);

        assertThat(JsonUtil.toJson(new Object())).isEqualTo("{}");
    }

    @Nested
    @DisplayName("TypeRef Deserialization Tests")
    class TypeRefDeserializationTests {

        @Test
        @DisplayName("Should deserialize JSON to List of objects using TypeRef")
        void shouldDeserializeJsonToListOfObjectsUsingTypeRef() {
            // Given
            String json = "[{\"name\":\"Bob\",\"age\":18},{\"name\":\"Jason\",\"age\":18}]";

            // When
            List<User> users = JsonUtil.toBean(json, new TypeRef<>() {});

            // Then
            assertThat(users).hasSize(2);

            // Given
            String jsonWithEmptyObject = "[{\"name\":\"Bob\",\"age\":18},{\"name\":\"Jason\",\"age\":18},{}]";

            // When
            users = JsonUtil.toBean(jsonWithEmptyObject, new TypeRef<>() {});

            // Then
            assertThat(users).hasSize(3);
        }

        @Test
        @DisplayName("Should deserialize JSON to List of Maps using TypeRef")
        void shouldDeserializeJsonToListOfMapsUsingTypeRef() {
            // Given
            String json = "[{\"name\":\"Bob\",\"age\":18},{\"name\":\"Jason\",\"age\":18}]";

            // When
            List<Map<String, Object>> list = JsonUtil.toBean(json, new TypeRef<>() {});

            // Then
            assertThat(list).hasSize(2);
            assertThat(list.get(0).get("name")).isEqualTo("Bob");
            assertThat(list.get(0).get("age")).isEqualTo(18);
            assertThat(list.get(1).get("name")).isEqualTo("Jason");
            assertThat(list.get(1).get("age")).isEqualTo(18);
        }

        @Test
        @DisplayName("Should deserialize JSON to Protobuf Value using TypeRef")
        void shouldDeserializeJsonToProtobufValueUsingTypeRef() {
            // Given
            String json = "[{\"name\":\"Bob\",\"age\":18},{\"name\":\"Jason\",\"age\":18}]";

            // When
            Value value = JsonUtil.toBean(json, new TypeRef<>() {});

            // Then
            assertThat(value.hasListValue()).isTrue();
        }

        @Test
        @DisplayName("Should deserialize JSON to object using TypeRef")
        void shouldDeserializeJsonToProtobufContainerUsingTypeRef() {
            // Given
            String json = "{\"name\":\"Freeman\",\"age\":18}";

            // When
            Struct struct = JsonUtil.toBean(json, new TypeRef<>() {});

            // Then
            assertThat(struct.getFieldsOrThrow("name").getStringValue()).isEqualTo("Freeman");
            assertThat(struct.getFieldsOrThrow("age").getNumberValue()).isEqualTo(18.0);

            // When
            Map<String, Value> map = JsonUtil.toBean(json, new TypeRef<>() {});

            // Then
            assertThat(map).hasSize(2);
            assertThat(map.get("name").getStringValue()).isEqualTo("Freeman");
            assertThat(map.get("age").getNumberValue()).isEqualTo(18.0);

            // Given
            String json1 = "[{\"name\":\"Freeman\",\"age\":18}, {\"name\":\"Freeman\",\"age\":20}]";

            // When
            List<Struct> actualList1 = JsonUtil.toBean(json1, new TypeRef<>() {});
            List<Struct> actualList2 = JsonUtil.toList(json1, Struct.class);

            // Then
            var expectedList = List.of(
                    Structs.of("name", Values.of("Freeman"), "age", Values.of(18.0)),
                    Structs.of("name", Values.of("Freeman"), "age", Values.of(20.0)));
            assertThat(actualList1).isEqualTo(expectedList);
            assertThat(actualList2).isEqualTo(expectedList);
        }
    }

    @Nested
    @DisplayName("toList Deserialization Tests")
    class ToListDeserializationTests {

        @Test
        @DisplayName("Should deserialize JSON to List of objects")
        void shouldDeserializeJsonToListOfObjects() {
            // Given
            String json = "[{\"name\":\"Bob\",\"age\":18},{\"name\":\"Jason\",\"age\":18}]";

            // When
            List<User> users = JsonUtil.toList(json, User.class);

            // Then
            assertThat(users.get(0)).isInstanceOf(User.class);
            assertThat(users).hasSize(2);

            // Given
            String jsonWithEmptyObject = "[{\"name\":\"Bob\",\"age\":18},{\"name\":\"Jason\",\"age\":18},{}]";

            // When
            users = JsonUtil.toList(jsonWithEmptyObject, User.class);

            // Then
            assertThat(users).hasSize(3);
        }

        @Test
        @DisplayName("Should deserialize JSON to List of Protobuf messages")
        void shouldDeserializeJsonToListOfProtobufMessages() {
            // Given
            String json = "[{\"name\":\"Bob\",\"age\":18},{\"name\":\"Jason\",\"age\":18}]";

            // When
            List<Struct> actual = JsonUtil.toList(json, Struct.class);

            // Then
            var expected = List.of(
                    Structs.of("name", Values.of("Bob"), "age", Values.of(18.0)),
                    Structs.of("name", Values.of("Jason"), "age", Values.of(18.0)));
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        @DisplayName("Should deserialize JSON to List of Protobuf enums")
        void shouldDeserializeJsonToListOfProtobufEnums() {
            // Given
            String json = "[0, 1, -1]";

            // When
            List<Code> actual = JsonUtil.toList(json, Code.class);

            // Then
            var expected = List.of(Code.OK, Code.CANCELLED, Code.UNRECOGNIZED);
            assertThat(actual).isEqualTo(expected);
        }
    }

    @Nested
    @DisplayName("ProtobufContainerBean Tests")
    class ProtobufContainerBeanTests {

        @Test
        @DisplayName("Should serialize and deserialize ProtobufContainerBean")
        void shouldSerializeAndDeserializeProtobufContainerBean() {
            // Given
            var struct1 = Structs.of("hello", Values.of("world"));
            var struct2 = Structs.of("foo", Values.of("bar"));
            var protobufContainerBean = new ProtobufContainerBean()
                    .setStructs(List.of(struct1, struct2))
                    .setEnumField(Code.OK)
                    .setStringValueField(StringValue.of("test string"))
                    .setInt32ValueField(Int32Value.of(42));

            // When
            String json = JsonUtil.toJson(protobufContainerBean);

            // Then
            String expectedJson =
                    """
                    {"structs":[{"hello":"world"},{"foo":"bar"}],"enumField":0,"stringValueField":"test string","int32ValueField":42}""";
            assertThat(json).isEqualTo(expectedJson);

            // When
            var deserializedProtobufContainerBean = JsonUtil.toBean(json, ProtobufContainerBean.class);

            // Then
            assertThat(deserializedProtobufContainerBean).isEqualTo(protobufContainerBean);

            // Given
            var jsonArray =
                    """
                    [{"structs":[{"hello":"world"},{"foo":"bar"}],"enumField":0,"stringValueField":"test string","int32ValueField":42}]""";

            // When
            var protobufContainerBeanList = JsonUtil.toList(jsonArray, ProtobufContainerBean.class);

            // Then
            var expectedProtobufContainerBeanList = List.of(new ProtobufContainerBean()
                    .setStructs(List.of(
                            Struct.newBuilder()
                                    .putFields("hello", Values.of("world"))
                                    .build(),
                            Struct.newBuilder()
                                    .putFields("foo", Values.of("bar"))
                                    .build()))
                    .setEnumField(Code.OK)
                    .setStringValueField(StringValue.of("test string"))
                    .setInt32ValueField(Int32Value.of(42)));
            assertThat(protobufContainerBeanList).isEqualTo(expectedProtobufContainerBeanList);
        }
    }

    @Nested
    @DisplayName("Long Value Handling Tests")
    class LongValueHandlingTests {

        @Test
        @DisplayName("Should handle large Long values correctly")
        void shouldHandleLargeLongValuesCorrectly() {
            // Given
            Map<String, Long> map = Map.of(
                    "pivot",
                    (1L << 53) - 1, // 9007199254740991
                    "bigLong",
                    1L << 53); // 9007199254740992

            // When
            String json = JsonUtil.toJson(map);

            // Then
            assertThat(json).contains("9007199254740991").doesNotContain("\"9007199254740991\"");
            assertThat(json).contains("\"9007199254740992\"");

            // When
            Map<String, Long> deserializedMap = JsonUtil.toBean(json, new TypeRef<>() {});

            // Then
            assertThat(deserializedMap).isEqualTo(map);
        }
    }

    @Nested
    @DisplayName("Date and Time Handling Tests")
    class DateAndTimeHandlingTests {

        @Test
        @DisplayName("Should handle date and time formats correctly")
        void shouldHandleDateAndTimeFormatsCorrectly() {
            // Given - JSON with timestamp format
            var jsonStr =
                    """
                    {"date":1740375194888,"localDate":[2025,2,24],"localDateTime":[2025,2,24,13,33,14,891685000],"zonedDateTime":1740375194.892039000}
                    """;

            // When/Then - Should parse without exception
            assertThatCode(() -> JsonUtil.toBean(jsonStr, DateBean.class)).doesNotThrowAnyException();

            // Given - Date objects
            var fixedDate = Date.from(Instant.parse("2025-02-24T05:39:56.537Z"));
            var fixedLocalDate = LocalDate.of(2025, 2, 24);
            var fixedLocalDateTime = LocalDateTime.of(2025, 2, 24, 13, 39, 56, 538048000);
            var fixedZonedDateTime = ZonedDateTime.of(2025, 2, 24, 13, 39, 56, 538058000, ZoneId.of("+08:00"));

            var bean = new DateBean();
            bean.setDate(fixedDate);
            bean.setLocalDate(fixedLocalDate);
            bean.setLocalDateTime(fixedLocalDateTime);
            bean.setZonedDateTime(fixedZonedDateTime);

            // When
            var actual = JsonUtil.toJson(bean);

            // Then - Should use ISO format instead of timestamps
            var expected =
                    """
                {"date":"2025-02-24T05:39:56.537+00:00","localDate":"2025-02-24","localDateTime":"2025-02-24T13:39:56.538048","zonedDateTime":"2025-02-24T13:39:56.538058+08:00"}""";

            assertThat(actual).isEqualTo(expected);
        }
    }

    @Data
    @Accessors(chain = true)
    static class User {
        private String name;
        private Integer age;
    }

    @Data
    @Accessors(chain = true)
    static class ProtobufContainerBean {
        private List<Struct> structs;
        private Code enumField;
        private StringValue stringValueField;
        private Int32Value int32ValueField;
    }

    @Data
    static class DateBean {
        private Date date;
        private LocalDate localDate;
        private LocalDateTime localDateTime;
        private ZonedDateTime zonedDateTime;
    }
}
