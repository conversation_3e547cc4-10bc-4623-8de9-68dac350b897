package com.moego.lib.common.autoconfigure.http;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.http.feign.MoeDecoder;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.WebApplicationContextRunner;

/**
 * {@link HttpClientConfiguration} tester.
 */
class HttpClientConfigurationTest {

    private final WebApplicationContextRunner runner =
            new WebApplicationContextRunner().withUserConfiguration(HttpClientConfiguration.class);

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).hasSingleBean(HttpClientConfiguration.class);
        });
    }

    @Test
    void testDisableHttpClient() {
        runner.withPropertyValues(HttpProperties.PREFIX + ".client.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(MoeDecoder.class);
                });
    }
}
