package com.moego.lib.common;

import com.google.protobuf.Empty;
import com.moego.lib.common.grpc.server.GrpcResponseUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.model.echo.v1.EchoRequest;
import com.moego.model.echo.v1.EchoResponse;
import com.moego.svc.echo.v1.EchoGrpc;
import io.grpc.stub.StreamObserver;
import lombok.SneakyThrows;

@GrpcService
public class EchoSvcController extends EchoGrpc.EchoImplBase {

    @Override
    @SneakyThrows
    public void echo(EchoRequest request, StreamObserver<EchoResponse> responseObserver) {
        GrpcResponseUtil.putMetadata("response", request.getMessage());

        new Thread(() -> {
                    GrpcResponseUtil.putMetadata("response2", request.getMessage());
                })
                .start();

        Thread.sleep(50);

        responseObserver.onNext(
                EchoResponse.newBuilder().setMessage(request.getMessage()).build());
        responseObserver.onCompleted();
    }

    @Override
    public void uncaughtException(Empty request, StreamObserver<Empty> responseObserver) {
        throw new RuntimeException("uncaught Exception");
    }
}
