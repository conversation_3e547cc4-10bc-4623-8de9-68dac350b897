package example.entity.mysql;

import example.models.AppointmentModel.Status;
import jakarta.annotation.Generated;
import java.math.BigDecimal;

/**
 * Database Table Remarks:
 *   预约订单表(新)
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_appointment
 */
public class MoeGroomingAppointment {
    /**
     * Database Column Remarks:
     *   预约订单id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.id")
    private Integer id;

    /**
     * Database Column Remarks:
     *   订单流水号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.order_id")
    private String orderId;

    /**
     * Database Column Remarks:
     *   店铺id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.business_id")
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   客户id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.customer_id")
    private Integer customerId;

    /**
     * Database Column Remarks:
     *   预约日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.appointment_date")
    private String appointmentDate;

    /**
     * Database Column Remarks:
     *   预约服务开始时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.appointment_start_time")
    private Integer appointmentStartTime;

    /**
     * Database Column Remarks:
     *   预约服务结束时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.appointment_end_time")
    private Integer appointmentEndTime;

    /**
     * Database Column Remarks:
     *   是否在waiting list内  0否  1是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_waiting_list")
    private Byte isWaitingList;

    /**
     * Database Column Remarks:
     *   把订单移动到waiting list的员工
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.move_waiting_by")
    private Integer moveWaitingBy;

    /**
     * Database Column Remarks:
     *   订单确认时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.confirmed_time")
    private Long confirmedTime;

    /**
     * Database Column Remarks:
     *   真实服务开始时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.check_in_time")
    private Long checkInTime;

    /**
     * Database Column Remarks:
     *   真实服务结束时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.check_out_time")
    private Long checkOutTime;

    /**
     * Database Column Remarks:
     *   取消时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.canceled_time")
    private Long canceledTime;

    /**
     * Database Column Remarks:
     *   1未确认 2确认 3已完成 4已取消
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.status")
    private Status status;

    /**
     * Database Column Remarks:
     *   是否为block 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_block")
    private Boolean isBlock;

    /**
     * Database Column Remarks:
     *   1 未确认 2 confirm 3 waitinglist 4 cancle
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.book_online_status")
    private Byte bookOnlineStatus;

    /**
     * Database Column Remarks:
     *   客户地址id  （mm_customer_address   id）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.customer_address_id")
    private Integer customerAddressId;

    /**
     * Database Column Remarks:
     *   重复预约id（mm_repeat_appointment）为0则表示普通订单
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.repeat_id")
    private Integer repeatId;

    /**
     * Database Column Remarks:
     *   是否已完全支付 1是 2否( 部分支付为2)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_paid")
    private Byte isPaid;

    /**
     * Database Column Remarks:
     *   订单颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.color_code")
    private String colorCode;

    /**
     * Database Column Remarks:
     *   用户爽约 1-爽约 2-没有爽约
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.no_show")
    private Byte noShow;

    /**
     * Database Column Remarks:
     *   noShow费用，扣费后同步
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.no_show_fee")
    private BigDecimal noShowFee;

    /**
     * Database Column Remarks:
     *   是否推送过通知 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_pust_notification")
    private Byte isPustNotification;

    /**
     * Database Column Remarks:
     *   0-by business, 1-by customer reply msg, 2-by delete pet
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.cancel_by_type")
    private Byte cancelByType;

    /**
     * Database Column Remarks:
     *   预约取消员工 business_account_id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.cancel_by")
    private Integer cancelBy;

    /**
     * Database Column Remarks:
     *   0-by business, 1-by customer reply msg
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.confirm_by_type")
    private Byte confirmByType;

    /**
     * Database Column Remarks:
     *   预约确定员工 business_account_id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.confirm_by")
    private Integer confirmBy;

    /**
     * Database Column Remarks:
     *   预约创建员工
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.created_by_id")
    private Integer createdById;

    /**
     * Database Column Remarks:
     *   提交数据时，是否超出服务区域
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.out_of_area")
    private Byte outOfArea;

    /**
     * Database Column Remarks:
     *   是否弃用 默认0 未弃用 1弃用  修改repeat规则后弃用的旧数据
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_deprecate")
    private Byte isDeprecate;

    /**
     * Database Column Remarks:
     *   订单创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.create_time")
    private Long createTime;

    /**
     * Database Column Remarks:
     *   最后修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.update_time")
    private Long updateTime;

    /**
     * Database Column Remarks:
     *   22168-book-online 22018-web,17216-android,17802-ios
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.source")
    private Integer source;

    /**
     * Database Column Remarks:
     *   reschedule前的预约日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.old_appointment_date")
    private String oldAppointmentDate;

    /**
     * Database Column Remarks:
     *   reschedule前的预约服务开始时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.old_appointment_start_time")
    private Integer oldAppointmentStartTime;

    /**
     * Database Column Remarks:
     *   reschedule前的预约服务结束时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.old_appointment_end_time")
    private Integer oldAppointmentEndTime;

    /**
     * Database Column Remarks:
     *   旧版本的apptId
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.old_appt_id")
    private Integer oldApptId;

    /**
     * Database Column Remarks:
     *   repeat 类型，1-普通 repeat，2-Smart schedule repeat
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.schedule_type")
    private Byte scheduleType;

    /**
     * Database Column Remarks:
     *   This field indicates the third-party platform through which the appointment was initiated and entered into our system. Possible values: GOOGLE, INSTAGRAM, FACEBOOK, and other relevant platforms.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.source_platform")
    private String sourcePlatform;

    /**
     * Database Column Remarks:
     *   设置为 ready for pickup 的时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.ready_time")
    private Long readyTime;

    /**
     * Database Column Remarks:
     *   发送 ready for pickup 通知的状态, 0 - 未发送, 1 - 已发送, 2 - 发送失败
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.pickup_notification_send_status")
    private Integer pickupNotificationSendStatus;

    /**
     * Database Column Remarks:
     *   发送 ready for pickup 通知失败的原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.pickup_notification_failed_reason")
    private String pickupNotificationFailedReason;

    /**
     * Database Column Remarks:
     *   checkin 之前的状态, 0 - unknown, 1 - unconfirmed, 2 - confirmed, 3 - finished, 4 - cancelled, 5 - ready, 6 - checkin
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.status_before_checkin")
    private Byte statusBeforeCheckin;

    /**
     * Database Column Remarks:
     *   ready for pickup 之前的状态, 0 - unknown, 1 - unconfirmed, 2 - confirmed, 3 - finished, 4 - cancelled, 5 - ready, 6 - checkin
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.status_before_ready")
    private Byte statusBeforeReady;

    /**
     * Database Column Remarks:
     *   finish 之前的状态, 0 - unknown, 1 - unconfirmed, 2 - confirmed, 3 - finished, 4 - cancelled, 5 - ready, 6 - checkin
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.status_before_finish")
    private Byte statusBeforeFinish;

    /**
     * Database Column Remarks:
     *   当前 appointment 是否包含时间，新增这个字段的原因是因为 start_time 字段默认值 0 不能区分是 0 时还是没有 time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.no_start_time")
    private Boolean noStartTime;

    /**
     * Database Column Remarks:
     *   更新人 id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.updated_by_id")
    private Long updatedById;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.company_id")
    private Long companyId;

    /**
     * Database Column Remarks:
     *   Is auto accept OB appointment? Default value is false
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_auto_accept")
    private Boolean isAutoAccept;

    /**
     * Database Column Remarks:
     *   0有 appt 无 waitlist 1无 appt 有 waitlist 2有 appt 有 waitlist
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.wait_list_status")
    private Byte waitListStatus;

    /**
     * Database Column Remarks:
     *   结束日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.appointment_end_date")
    private String appointmentEndDate;

    /**
     * Database Column Remarks:
     *   包含的服务类型，用位图表示，二进制从低到高依次为 grooming, boarding, daycare, evaluation
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.service_type_include")
    private Integer serviceTypeInclude;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.id")
    public Integer getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.id")
    public void setId(Integer id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.order_id")
    public String getOrderId() {
        return orderId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.order_id")
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.business_id")
    public Integer getBusinessId() {
        return businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.business_id")
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.customer_id")
    public Integer getCustomerId() {
        return customerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.customer_id")
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.appointment_date")
    public String getAppointmentDate() {
        return appointmentDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.appointment_date")
    public void setAppointmentDate(String appointmentDate) {
        this.appointmentDate = appointmentDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.appointment_start_time")
    public Integer getAppointmentStartTime() {
        return appointmentStartTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.appointment_start_time")
    public void setAppointmentStartTime(Integer appointmentStartTime) {
        this.appointmentStartTime = appointmentStartTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.appointment_end_time")
    public Integer getAppointmentEndTime() {
        return appointmentEndTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.appointment_end_time")
    public void setAppointmentEndTime(Integer appointmentEndTime) {
        this.appointmentEndTime = appointmentEndTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_waiting_list")
    public Byte getIsWaitingList() {
        return isWaitingList;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_waiting_list")
    public void setIsWaitingList(Byte isWaitingList) {
        this.isWaitingList = isWaitingList;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.move_waiting_by")
    public Integer getMoveWaitingBy() {
        return moveWaitingBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.move_waiting_by")
    public void setMoveWaitingBy(Integer moveWaitingBy) {
        this.moveWaitingBy = moveWaitingBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.confirmed_time")
    public Long getConfirmedTime() {
        return confirmedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.confirmed_time")
    public void setConfirmedTime(Long confirmedTime) {
        this.confirmedTime = confirmedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.check_in_time")
    public Long getCheckInTime() {
        return checkInTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.check_in_time")
    public void setCheckInTime(Long checkInTime) {
        this.checkInTime = checkInTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.check_out_time")
    public Long getCheckOutTime() {
        return checkOutTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.check_out_time")
    public void setCheckOutTime(Long checkOutTime) {
        this.checkOutTime = checkOutTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.canceled_time")
    public Long getCanceledTime() {
        return canceledTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.canceled_time")
    public void setCanceledTime(Long canceledTime) {
        this.canceledTime = canceledTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.status")
    public Status getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.status")
    public void setStatus(Status status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_block")
    public Boolean getIsBlock() {
        return isBlock;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_block")
    public void setIsBlock(Boolean isBlock) {
        this.isBlock = isBlock;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.book_online_status")
    public Byte getBookOnlineStatus() {
        return bookOnlineStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.book_online_status")
    public void setBookOnlineStatus(Byte bookOnlineStatus) {
        this.bookOnlineStatus = bookOnlineStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.customer_address_id")
    public Integer getCustomerAddressId() {
        return customerAddressId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.customer_address_id")
    public void setCustomerAddressId(Integer customerAddressId) {
        this.customerAddressId = customerAddressId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.repeat_id")
    public Integer getRepeatId() {
        return repeatId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.repeat_id")
    public void setRepeatId(Integer repeatId) {
        this.repeatId = repeatId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_paid")
    public Byte getIsPaid() {
        return isPaid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_paid")
    public void setIsPaid(Byte isPaid) {
        this.isPaid = isPaid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.color_code")
    public String getColorCode() {
        return colorCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.color_code")
    public void setColorCode(String colorCode) {
        this.colorCode = colorCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.no_show")
    public Byte getNoShow() {
        return noShow;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.no_show")
    public void setNoShow(Byte noShow) {
        this.noShow = noShow;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.no_show_fee")
    public BigDecimal getNoShowFee() {
        return noShowFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.no_show_fee")
    public void setNoShowFee(BigDecimal noShowFee) {
        this.noShowFee = noShowFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_pust_notification")
    public Byte getIsPustNotification() {
        return isPustNotification;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_pust_notification")
    public void setIsPustNotification(Byte isPustNotification) {
        this.isPustNotification = isPustNotification;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.cancel_by_type")
    public Byte getCancelByType() {
        return cancelByType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.cancel_by_type")
    public void setCancelByType(Byte cancelByType) {
        this.cancelByType = cancelByType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.cancel_by")
    public Integer getCancelBy() {
        return cancelBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.cancel_by")
    public void setCancelBy(Integer cancelBy) {
        this.cancelBy = cancelBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.confirm_by_type")
    public Byte getConfirmByType() {
        return confirmByType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.confirm_by_type")
    public void setConfirmByType(Byte confirmByType) {
        this.confirmByType = confirmByType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.confirm_by")
    public Integer getConfirmBy() {
        return confirmBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.confirm_by")
    public void setConfirmBy(Integer confirmBy) {
        this.confirmBy = confirmBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.created_by_id")
    public Integer getCreatedById() {
        return createdById;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.created_by_id")
    public void setCreatedById(Integer createdById) {
        this.createdById = createdById;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.out_of_area")
    public Byte getOutOfArea() {
        return outOfArea;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.out_of_area")
    public void setOutOfArea(Byte outOfArea) {
        this.outOfArea = outOfArea;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_deprecate")
    public Byte getIsDeprecate() {
        return isDeprecate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_deprecate")
    public void setIsDeprecate(Byte isDeprecate) {
        this.isDeprecate = isDeprecate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.create_time")
    public Long getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.create_time")
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.update_time")
    public Long getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.update_time")
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.source")
    public Integer getSource() {
        return source;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.source")
    public void setSource(Integer source) {
        this.source = source;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.old_appointment_date")
    public String getOldAppointmentDate() {
        return oldAppointmentDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.old_appointment_date")
    public void setOldAppointmentDate(String oldAppointmentDate) {
        this.oldAppointmentDate = oldAppointmentDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.old_appointment_start_time")
    public Integer getOldAppointmentStartTime() {
        return oldAppointmentStartTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.old_appointment_start_time")
    public void setOldAppointmentStartTime(Integer oldAppointmentStartTime) {
        this.oldAppointmentStartTime = oldAppointmentStartTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.old_appointment_end_time")
    public Integer getOldAppointmentEndTime() {
        return oldAppointmentEndTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.old_appointment_end_time")
    public void setOldAppointmentEndTime(Integer oldAppointmentEndTime) {
        this.oldAppointmentEndTime = oldAppointmentEndTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.old_appt_id")
    public Integer getOldApptId() {
        return oldApptId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.old_appt_id")
    public void setOldApptId(Integer oldApptId) {
        this.oldApptId = oldApptId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.schedule_type")
    public Byte getScheduleType() {
        return scheduleType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.schedule_type")
    public void setScheduleType(Byte scheduleType) {
        this.scheduleType = scheduleType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.source_platform")
    public String getSourcePlatform() {
        return sourcePlatform;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.source_platform")
    public void setSourcePlatform(String sourcePlatform) {
        this.sourcePlatform = sourcePlatform;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.ready_time")
    public Long getReadyTime() {
        return readyTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.ready_time")
    public void setReadyTime(Long readyTime) {
        this.readyTime = readyTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.pickup_notification_send_status")
    public Integer getPickupNotificationSendStatus() {
        return pickupNotificationSendStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.pickup_notification_send_status")
    public void setPickupNotificationSendStatus(Integer pickupNotificationSendStatus) {
        this.pickupNotificationSendStatus = pickupNotificationSendStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.pickup_notification_failed_reason")
    public String getPickupNotificationFailedReason() {
        return pickupNotificationFailedReason;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.pickup_notification_failed_reason")
    public void setPickupNotificationFailedReason(String pickupNotificationFailedReason) {
        this.pickupNotificationFailedReason = pickupNotificationFailedReason;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.status_before_checkin")
    public Byte getStatusBeforeCheckin() {
        return statusBeforeCheckin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.status_before_checkin")
    public void setStatusBeforeCheckin(Byte statusBeforeCheckin) {
        this.statusBeforeCheckin = statusBeforeCheckin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.status_before_ready")
    public Byte getStatusBeforeReady() {
        return statusBeforeReady;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.status_before_ready")
    public void setStatusBeforeReady(Byte statusBeforeReady) {
        this.statusBeforeReady = statusBeforeReady;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.status_before_finish")
    public Byte getStatusBeforeFinish() {
        return statusBeforeFinish;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.status_before_finish")
    public void setStatusBeforeFinish(Byte statusBeforeFinish) {
        this.statusBeforeFinish = statusBeforeFinish;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.no_start_time")
    public Boolean getNoStartTime() {
        return noStartTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.no_start_time")
    public void setNoStartTime(Boolean noStartTime) {
        this.noStartTime = noStartTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.updated_by_id")
    public Long getUpdatedById() {
        return updatedById;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.updated_by_id")
    public void setUpdatedById(Long updatedById) {
        this.updatedById = updatedById;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.company_id")
    public Long getCompanyId() {
        return companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.company_id")
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_auto_accept")
    public Boolean getIsAutoAccept() {
        return isAutoAccept;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_auto_accept")
    public void setIsAutoAccept(Boolean isAutoAccept) {
        this.isAutoAccept = isAutoAccept;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.wait_list_status")
    public Byte getWaitListStatus() {
        return waitListStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.wait_list_status")
    public void setWaitListStatus(Byte waitListStatus) {
        this.waitListStatus = waitListStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.appointment_end_date")
    public String getAppointmentEndDate() {
        return appointmentEndDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.appointment_end_date")
    public void setAppointmentEndDate(String appointmentEndDate) {
        this.appointmentEndDate = appointmentEndDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.service_type_include")
    public Integer getServiceTypeInclude() {
        return serviceTypeInclude;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.service_type_include")
    public void setServiceTypeInclude(Integer serviceTypeInclude) {
        this.serviceTypeInclude = serviceTypeInclude;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderId=").append(orderId);
        sb.append(", businessId=").append(businessId);
        sb.append(", customerId=").append(customerId);
        sb.append(", appointmentDate=").append(appointmentDate);
        sb.append(", appointmentStartTime=").append(appointmentStartTime);
        sb.append(", appointmentEndTime=").append(appointmentEndTime);
        sb.append(", isWaitingList=").append(isWaitingList);
        sb.append(", moveWaitingBy=").append(moveWaitingBy);
        sb.append(", confirmedTime=").append(confirmedTime);
        sb.append(", checkInTime=").append(checkInTime);
        sb.append(", checkOutTime=").append(checkOutTime);
        sb.append(", canceledTime=").append(canceledTime);
        sb.append(", status=").append(status);
        sb.append(", isBlock=").append(isBlock);
        sb.append(", bookOnlineStatus=").append(bookOnlineStatus);
        sb.append(", customerAddressId=").append(customerAddressId);
        sb.append(", repeatId=").append(repeatId);
        sb.append(", isPaid=").append(isPaid);
        sb.append(", colorCode=").append(colorCode);
        sb.append(", noShow=").append(noShow);
        sb.append(", noShowFee=").append(noShowFee);
        sb.append(", isPustNotification=").append(isPustNotification);
        sb.append(", cancelByType=").append(cancelByType);
        sb.append(", cancelBy=").append(cancelBy);
        sb.append(", confirmByType=").append(confirmByType);
        sb.append(", confirmBy=").append(confirmBy);
        sb.append(", createdById=").append(createdById);
        sb.append(", outOfArea=").append(outOfArea);
        sb.append(", isDeprecate=").append(isDeprecate);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", source=").append(source);
        sb.append(", oldAppointmentDate=").append(oldAppointmentDate);
        sb.append(", oldAppointmentStartTime=").append(oldAppointmentStartTime);
        sb.append(", oldAppointmentEndTime=").append(oldAppointmentEndTime);
        sb.append(", oldApptId=").append(oldApptId);
        sb.append(", scheduleType=").append(scheduleType);
        sb.append(", sourcePlatform=").append(sourcePlatform);
        sb.append(", readyTime=").append(readyTime);
        sb.append(", pickupNotificationSendStatus=").append(pickupNotificationSendStatus);
        sb.append(", pickupNotificationFailedReason=").append(pickupNotificationFailedReason);
        sb.append(", statusBeforeCheckin=").append(statusBeforeCheckin);
        sb.append(", statusBeforeReady=").append(statusBeforeReady);
        sb.append(", statusBeforeFinish=").append(statusBeforeFinish);
        sb.append(", noStartTime=").append(noStartTime);
        sb.append(", updatedById=").append(updatedById);
        sb.append(", companyId=").append(companyId);
        sb.append(", isAutoAccept=").append(isAutoAccept);
        sb.append(", waitListStatus=").append(waitListStatus);
        sb.append(", appointmentEndDate=").append(appointmentEndDate);
        sb.append(", serviceTypeInclude=").append(serviceTypeInclude);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MoeGroomingAppointment other = (MoeGroomingAppointment) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
            && (this.getCustomerId() == null ? other.getCustomerId() == null : this.getCustomerId().equals(other.getCustomerId()))
            && (this.getAppointmentDate() == null ? other.getAppointmentDate() == null : this.getAppointmentDate().equals(other.getAppointmentDate()))
            && (this.getAppointmentStartTime() == null ? other.getAppointmentStartTime() == null : this.getAppointmentStartTime().equals(other.getAppointmentStartTime()))
            && (this.getAppointmentEndTime() == null ? other.getAppointmentEndTime() == null : this.getAppointmentEndTime().equals(other.getAppointmentEndTime()))
            && (this.getIsWaitingList() == null ? other.getIsWaitingList() == null : this.getIsWaitingList().equals(other.getIsWaitingList()))
            && (this.getMoveWaitingBy() == null ? other.getMoveWaitingBy() == null : this.getMoveWaitingBy().equals(other.getMoveWaitingBy()))
            && (this.getConfirmedTime() == null ? other.getConfirmedTime() == null : this.getConfirmedTime().equals(other.getConfirmedTime()))
            && (this.getCheckInTime() == null ? other.getCheckInTime() == null : this.getCheckInTime().equals(other.getCheckInTime()))
            && (this.getCheckOutTime() == null ? other.getCheckOutTime() == null : this.getCheckOutTime().equals(other.getCheckOutTime()))
            && (this.getCanceledTime() == null ? other.getCanceledTime() == null : this.getCanceledTime().equals(other.getCanceledTime()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getIsBlock() == null ? other.getIsBlock() == null : this.getIsBlock().equals(other.getIsBlock()))
            && (this.getBookOnlineStatus() == null ? other.getBookOnlineStatus() == null : this.getBookOnlineStatus().equals(other.getBookOnlineStatus()))
            && (this.getCustomerAddressId() == null ? other.getCustomerAddressId() == null : this.getCustomerAddressId().equals(other.getCustomerAddressId()))
            && (this.getRepeatId() == null ? other.getRepeatId() == null : this.getRepeatId().equals(other.getRepeatId()))
            && (this.getIsPaid() == null ? other.getIsPaid() == null : this.getIsPaid().equals(other.getIsPaid()))
            && (this.getColorCode() == null ? other.getColorCode() == null : this.getColorCode().equals(other.getColorCode()))
            && (this.getNoShow() == null ? other.getNoShow() == null : this.getNoShow().equals(other.getNoShow()))
            && (this.getNoShowFee() == null ? other.getNoShowFee() == null : this.getNoShowFee().equals(other.getNoShowFee()))
            && (this.getIsPustNotification() == null ? other.getIsPustNotification() == null : this.getIsPustNotification().equals(other.getIsPustNotification()))
            && (this.getCancelByType() == null ? other.getCancelByType() == null : this.getCancelByType().equals(other.getCancelByType()))
            && (this.getCancelBy() == null ? other.getCancelBy() == null : this.getCancelBy().equals(other.getCancelBy()))
            && (this.getConfirmByType() == null ? other.getConfirmByType() == null : this.getConfirmByType().equals(other.getConfirmByType()))
            && (this.getConfirmBy() == null ? other.getConfirmBy() == null : this.getConfirmBy().equals(other.getConfirmBy()))
            && (this.getCreatedById() == null ? other.getCreatedById() == null : this.getCreatedById().equals(other.getCreatedById()))
            && (this.getOutOfArea() == null ? other.getOutOfArea() == null : this.getOutOfArea().equals(other.getOutOfArea()))
            && (this.getIsDeprecate() == null ? other.getIsDeprecate() == null : this.getIsDeprecate().equals(other.getIsDeprecate()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getSource() == null ? other.getSource() == null : this.getSource().equals(other.getSource()))
            && (this.getOldAppointmentDate() == null ? other.getOldAppointmentDate() == null : this.getOldAppointmentDate().equals(other.getOldAppointmentDate()))
            && (this.getOldAppointmentStartTime() == null ? other.getOldAppointmentStartTime() == null : this.getOldAppointmentStartTime().equals(other.getOldAppointmentStartTime()))
            && (this.getOldAppointmentEndTime() == null ? other.getOldAppointmentEndTime() == null : this.getOldAppointmentEndTime().equals(other.getOldAppointmentEndTime()))
            && (this.getOldApptId() == null ? other.getOldApptId() == null : this.getOldApptId().equals(other.getOldApptId()))
            && (this.getScheduleType() == null ? other.getScheduleType() == null : this.getScheduleType().equals(other.getScheduleType()))
            && (this.getSourcePlatform() == null ? other.getSourcePlatform() == null : this.getSourcePlatform().equals(other.getSourcePlatform()))
            && (this.getReadyTime() == null ? other.getReadyTime() == null : this.getReadyTime().equals(other.getReadyTime()))
            && (this.getPickupNotificationSendStatus() == null ? other.getPickupNotificationSendStatus() == null : this.getPickupNotificationSendStatus().equals(other.getPickupNotificationSendStatus()))
            && (this.getPickupNotificationFailedReason() == null ? other.getPickupNotificationFailedReason() == null : this.getPickupNotificationFailedReason().equals(other.getPickupNotificationFailedReason()))
            && (this.getStatusBeforeCheckin() == null ? other.getStatusBeforeCheckin() == null : this.getStatusBeforeCheckin().equals(other.getStatusBeforeCheckin()))
            && (this.getStatusBeforeReady() == null ? other.getStatusBeforeReady() == null : this.getStatusBeforeReady().equals(other.getStatusBeforeReady()))
            && (this.getStatusBeforeFinish() == null ? other.getStatusBeforeFinish() == null : this.getStatusBeforeFinish().equals(other.getStatusBeforeFinish()))
            && (this.getNoStartTime() == null ? other.getNoStartTime() == null : this.getNoStartTime().equals(other.getNoStartTime()))
            && (this.getUpdatedById() == null ? other.getUpdatedById() == null : this.getUpdatedById().equals(other.getUpdatedById()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getIsAutoAccept() == null ? other.getIsAutoAccept() == null : this.getIsAutoAccept().equals(other.getIsAutoAccept()))
            && (this.getWaitListStatus() == null ? other.getWaitListStatus() == null : this.getWaitListStatus().equals(other.getWaitListStatus()))
            && (this.getAppointmentEndDate() == null ? other.getAppointmentEndDate() == null : this.getAppointmentEndDate().equals(other.getAppointmentEndDate()))
            && (this.getServiceTypeInclude() == null ? other.getServiceTypeInclude() == null : this.getServiceTypeInclude().equals(other.getServiceTypeInclude()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getCustomerId() == null) ? 0 : getCustomerId().hashCode());
        result = prime * result + ((getAppointmentDate() == null) ? 0 : getAppointmentDate().hashCode());
        result = prime * result + ((getAppointmentStartTime() == null) ? 0 : getAppointmentStartTime().hashCode());
        result = prime * result + ((getAppointmentEndTime() == null) ? 0 : getAppointmentEndTime().hashCode());
        result = prime * result + ((getIsWaitingList() == null) ? 0 : getIsWaitingList().hashCode());
        result = prime * result + ((getMoveWaitingBy() == null) ? 0 : getMoveWaitingBy().hashCode());
        result = prime * result + ((getConfirmedTime() == null) ? 0 : getConfirmedTime().hashCode());
        result = prime * result + ((getCheckInTime() == null) ? 0 : getCheckInTime().hashCode());
        result = prime * result + ((getCheckOutTime() == null) ? 0 : getCheckOutTime().hashCode());
        result = prime * result + ((getCanceledTime() == null) ? 0 : getCanceledTime().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getIsBlock() == null) ? 0 : getIsBlock().hashCode());
        result = prime * result + ((getBookOnlineStatus() == null) ? 0 : getBookOnlineStatus().hashCode());
        result = prime * result + ((getCustomerAddressId() == null) ? 0 : getCustomerAddressId().hashCode());
        result = prime * result + ((getRepeatId() == null) ? 0 : getRepeatId().hashCode());
        result = prime * result + ((getIsPaid() == null) ? 0 : getIsPaid().hashCode());
        result = prime * result + ((getColorCode() == null) ? 0 : getColorCode().hashCode());
        result = prime * result + ((getNoShow() == null) ? 0 : getNoShow().hashCode());
        result = prime * result + ((getNoShowFee() == null) ? 0 : getNoShowFee().hashCode());
        result = prime * result + ((getIsPustNotification() == null) ? 0 : getIsPustNotification().hashCode());
        result = prime * result + ((getCancelByType() == null) ? 0 : getCancelByType().hashCode());
        result = prime * result + ((getCancelBy() == null) ? 0 : getCancelBy().hashCode());
        result = prime * result + ((getConfirmByType() == null) ? 0 : getConfirmByType().hashCode());
        result = prime * result + ((getConfirmBy() == null) ? 0 : getConfirmBy().hashCode());
        result = prime * result + ((getCreatedById() == null) ? 0 : getCreatedById().hashCode());
        result = prime * result + ((getOutOfArea() == null) ? 0 : getOutOfArea().hashCode());
        result = prime * result + ((getIsDeprecate() == null) ? 0 : getIsDeprecate().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getSource() == null) ? 0 : getSource().hashCode());
        result = prime * result + ((getOldAppointmentDate() == null) ? 0 : getOldAppointmentDate().hashCode());
        result = prime * result + ((getOldAppointmentStartTime() == null) ? 0 : getOldAppointmentStartTime().hashCode());
        result = prime * result + ((getOldAppointmentEndTime() == null) ? 0 : getOldAppointmentEndTime().hashCode());
        result = prime * result + ((getOldApptId() == null) ? 0 : getOldApptId().hashCode());
        result = prime * result + ((getScheduleType() == null) ? 0 : getScheduleType().hashCode());
        result = prime * result + ((getSourcePlatform() == null) ? 0 : getSourcePlatform().hashCode());
        result = prime * result + ((getReadyTime() == null) ? 0 : getReadyTime().hashCode());
        result = prime * result + ((getPickupNotificationSendStatus() == null) ? 0 : getPickupNotificationSendStatus().hashCode());
        result = prime * result + ((getPickupNotificationFailedReason() == null) ? 0 : getPickupNotificationFailedReason().hashCode());
        result = prime * result + ((getStatusBeforeCheckin() == null) ? 0 : getStatusBeforeCheckin().hashCode());
        result = prime * result + ((getStatusBeforeReady() == null) ? 0 : getStatusBeforeReady().hashCode());
        result = prime * result + ((getStatusBeforeFinish() == null) ? 0 : getStatusBeforeFinish().hashCode());
        result = prime * result + ((getNoStartTime() == null) ? 0 : getNoStartTime().hashCode());
        result = prime * result + ((getUpdatedById() == null) ? 0 : getUpdatedById().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getIsAutoAccept() == null) ? 0 : getIsAutoAccept().hashCode());
        result = prime * result + ((getWaitListStatus() == null) ? 0 : getWaitListStatus().hashCode());
        result = prime * result + ((getAppointmentEndDate() == null) ? 0 : getAppointmentEndDate().hashCode());
        result = prime * result + ((getServiceTypeInclude() == null) ? 0 : getServiceTypeInclude().hashCode());
        return result;
    }
}