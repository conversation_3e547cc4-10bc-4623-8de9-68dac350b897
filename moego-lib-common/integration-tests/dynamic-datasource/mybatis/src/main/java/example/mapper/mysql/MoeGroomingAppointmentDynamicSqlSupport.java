package example.mapper.mysql;

import example.models.AppointmentModel.Status;
import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.sql.JDBCType;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MoeGroomingAppointmentDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    public static final MoeGroomingAppointment moeGroomingAppointment = new MoeGroomingAppointment();

    /**
     * Database Column Remarks:
     *   预约订单id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.id")
    public static final SqlColumn<Integer> id = moeGroomingAppointment.id;

    /**
     * Database Column Remarks:
     *   订单流水号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.order_id")
    public static final SqlColumn<String> orderId = moeGroomingAppointment.orderId;

    /**
     * Database Column Remarks:
     *   店铺id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.business_id")
    public static final SqlColumn<Integer> businessId = moeGroomingAppointment.businessId;

    /**
     * Database Column Remarks:
     *   客户id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.customer_id")
    public static final SqlColumn<Integer> customerId = moeGroomingAppointment.customerId;

    /**
     * Database Column Remarks:
     *   预约日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.appointment_date")
    public static final SqlColumn<String> appointmentDate = moeGroomingAppointment.appointmentDate;

    /**
     * Database Column Remarks:
     *   预约服务开始时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.appointment_start_time")
    public static final SqlColumn<Integer> appointmentStartTime = moeGroomingAppointment.appointmentStartTime;

    /**
     * Database Column Remarks:
     *   预约服务结束时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.appointment_end_time")
    public static final SqlColumn<Integer> appointmentEndTime = moeGroomingAppointment.appointmentEndTime;

    /**
     * Database Column Remarks:
     *   是否在waiting list内  0否  1是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_waiting_list")
    public static final SqlColumn<Byte> isWaitingList = moeGroomingAppointment.isWaitingList;

    /**
     * Database Column Remarks:
     *   把订单移动到waiting list的员工
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.move_waiting_by")
    public static final SqlColumn<Integer> moveWaitingBy = moeGroomingAppointment.moveWaitingBy;

    /**
     * Database Column Remarks:
     *   订单确认时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.confirmed_time")
    public static final SqlColumn<Long> confirmedTime = moeGroomingAppointment.confirmedTime;

    /**
     * Database Column Remarks:
     *   真实服务开始时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.check_in_time")
    public static final SqlColumn<Long> checkInTime = moeGroomingAppointment.checkInTime;

    /**
     * Database Column Remarks:
     *   真实服务结束时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.check_out_time")
    public static final SqlColumn<Long> checkOutTime = moeGroomingAppointment.checkOutTime;

    /**
     * Database Column Remarks:
     *   取消时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.canceled_time")
    public static final SqlColumn<Long> canceledTime = moeGroomingAppointment.canceledTime;

    /**
     * Database Column Remarks:
     *   1未确认 2确认 3已完成 4已取消
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.status")
    public static final SqlColumn<Status> status = moeGroomingAppointment.status;

    /**
     * Database Column Remarks:
     *   是否为block 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_block")
    public static final SqlColumn<Boolean> isBlock = moeGroomingAppointment.isBlock;

    /**
     * Database Column Remarks:
     *   1 未确认 2 confirm 3 waitinglist 4 cancle
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.book_online_status")
    public static final SqlColumn<Byte> bookOnlineStatus = moeGroomingAppointment.bookOnlineStatus;

    /**
     * Database Column Remarks:
     *   客户地址id  （mm_customer_address   id）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.customer_address_id")
    public static final SqlColumn<Integer> customerAddressId = moeGroomingAppointment.customerAddressId;

    /**
     * Database Column Remarks:
     *   重复预约id（mm_repeat_appointment）为0则表示普通订单
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.repeat_id")
    public static final SqlColumn<Integer> repeatId = moeGroomingAppointment.repeatId;

    /**
     * Database Column Remarks:
     *   是否已完全支付 1是 2否( 部分支付为2)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_paid")
    public static final SqlColumn<Byte> isPaid = moeGroomingAppointment.isPaid;

    /**
     * Database Column Remarks:
     *   订单颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.color_code")
    public static final SqlColumn<String> colorCode = moeGroomingAppointment.colorCode;

    /**
     * Database Column Remarks:
     *   用户爽约 1-爽约 2-没有爽约
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.no_show")
    public static final SqlColumn<Byte> noShow = moeGroomingAppointment.noShow;

    /**
     * Database Column Remarks:
     *   noShow费用，扣费后同步
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.no_show_fee")
    public static final SqlColumn<BigDecimal> noShowFee = moeGroomingAppointment.noShowFee;

    /**
     * Database Column Remarks:
     *   是否推送过通知 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_pust_notification")
    public static final SqlColumn<Byte> isPustNotification = moeGroomingAppointment.isPustNotification;

    /**
     * Database Column Remarks:
     *   0-by business, 1-by customer reply msg, 2-by delete pet
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.cancel_by_type")
    public static final SqlColumn<Byte> cancelByType = moeGroomingAppointment.cancelByType;

    /**
     * Database Column Remarks:
     *   预约取消员工 business_account_id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.cancel_by")
    public static final SqlColumn<Integer> cancelBy = moeGroomingAppointment.cancelBy;

    /**
     * Database Column Remarks:
     *   0-by business, 1-by customer reply msg
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.confirm_by_type")
    public static final SqlColumn<Byte> confirmByType = moeGroomingAppointment.confirmByType;

    /**
     * Database Column Remarks:
     *   预约确定员工 business_account_id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.confirm_by")
    public static final SqlColumn<Integer> confirmBy = moeGroomingAppointment.confirmBy;

    /**
     * Database Column Remarks:
     *   预约创建员工
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.created_by_id")
    public static final SqlColumn<Integer> createdById = moeGroomingAppointment.createdById;

    /**
     * Database Column Remarks:
     *   提交数据时，是否超出服务区域
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.out_of_area")
    public static final SqlColumn<Byte> outOfArea = moeGroomingAppointment.outOfArea;

    /**
     * Database Column Remarks:
     *   是否弃用 默认0 未弃用 1弃用  修改repeat规则后弃用的旧数据
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_deprecate")
    public static final SqlColumn<Byte> isDeprecate = moeGroomingAppointment.isDeprecate;

    /**
     * Database Column Remarks:
     *   订单创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.create_time")
    public static final SqlColumn<Long> createTime = moeGroomingAppointment.createTime;

    /**
     * Database Column Remarks:
     *   最后修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.update_time")
    public static final SqlColumn<Long> updateTime = moeGroomingAppointment.updateTime;

    /**
     * Database Column Remarks:
     *   22168-book-online 22018-web,17216-android,17802-ios
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.source")
    public static final SqlColumn<Integer> source = moeGroomingAppointment.source;

    /**
     * Database Column Remarks:
     *   reschedule前的预约日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.old_appointment_date")
    public static final SqlColumn<String> oldAppointmentDate = moeGroomingAppointment.oldAppointmentDate;

    /**
     * Database Column Remarks:
     *   reschedule前的预约服务开始时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.old_appointment_start_time")
    public static final SqlColumn<Integer> oldAppointmentStartTime = moeGroomingAppointment.oldAppointmentStartTime;

    /**
     * Database Column Remarks:
     *   reschedule前的预约服务结束时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.old_appointment_end_time")
    public static final SqlColumn<Integer> oldAppointmentEndTime = moeGroomingAppointment.oldAppointmentEndTime;

    /**
     * Database Column Remarks:
     *   旧版本的apptId
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.old_appt_id")
    public static final SqlColumn<Integer> oldApptId = moeGroomingAppointment.oldApptId;

    /**
     * Database Column Remarks:
     *   repeat 类型，1-普通 repeat，2-Smart schedule repeat
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.schedule_type")
    public static final SqlColumn<Byte> scheduleType = moeGroomingAppointment.scheduleType;

    /**
     * Database Column Remarks:
     *   This field indicates the third-party platform through which the appointment was initiated and entered into our system. Possible values: GOOGLE, INSTAGRAM, FACEBOOK, and other relevant platforms.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.source_platform")
    public static final SqlColumn<String> sourcePlatform = moeGroomingAppointment.sourcePlatform;

    /**
     * Database Column Remarks:
     *   设置为 ready for pickup 的时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.ready_time")
    public static final SqlColumn<Long> readyTime = moeGroomingAppointment.readyTime;

    /**
     * Database Column Remarks:
     *   发送 ready for pickup 通知的状态, 0 - 未发送, 1 - 已发送, 2 - 发送失败
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.pickup_notification_send_status")
    public static final SqlColumn<Integer> pickupNotificationSendStatus = moeGroomingAppointment.pickupNotificationSendStatus;

    /**
     * Database Column Remarks:
     *   发送 ready for pickup 通知失败的原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.pickup_notification_failed_reason")
    public static final SqlColumn<String> pickupNotificationFailedReason = moeGroomingAppointment.pickupNotificationFailedReason;

    /**
     * Database Column Remarks:
     *   checkin 之前的状态, 0 - unknown, 1 - unconfirmed, 2 - confirmed, 3 - finished, 4 - cancelled, 5 - ready, 6 - checkin
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.status_before_checkin")
    public static final SqlColumn<Byte> statusBeforeCheckin = moeGroomingAppointment.statusBeforeCheckin;

    /**
     * Database Column Remarks:
     *   ready for pickup 之前的状态, 0 - unknown, 1 - unconfirmed, 2 - confirmed, 3 - finished, 4 - cancelled, 5 - ready, 6 - checkin
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.status_before_ready")
    public static final SqlColumn<Byte> statusBeforeReady = moeGroomingAppointment.statusBeforeReady;

    /**
     * Database Column Remarks:
     *   finish 之前的状态, 0 - unknown, 1 - unconfirmed, 2 - confirmed, 3 - finished, 4 - cancelled, 5 - ready, 6 - checkin
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.status_before_finish")
    public static final SqlColumn<Byte> statusBeforeFinish = moeGroomingAppointment.statusBeforeFinish;

    /**
     * Database Column Remarks:
     *   当前 appointment 是否包含时间，新增这个字段的原因是因为 start_time 字段默认值 0 不能区分是 0 时还是没有 time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.no_start_time")
    public static final SqlColumn<Boolean> noStartTime = moeGroomingAppointment.noStartTime;

    /**
     * Database Column Remarks:
     *   更新人 id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.updated_by_id")
    public static final SqlColumn<Long> updatedById = moeGroomingAppointment.updatedById;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.company_id")
    public static final SqlColumn<Long> companyId = moeGroomingAppointment.companyId;

    /**
     * Database Column Remarks:
     *   Is auto accept OB appointment? Default value is false
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.is_auto_accept")
    public static final SqlColumn<Boolean> isAutoAccept = moeGroomingAppointment.isAutoAccept;

    /**
     * Database Column Remarks:
     *   0有 appt 无 waitlist 1无 appt 有 waitlist 2有 appt 有 waitlist
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.wait_list_status")
    public static final SqlColumn<Byte> waitListStatus = moeGroomingAppointment.waitListStatus;

    /**
     * Database Column Remarks:
     *   结束日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.appointment_end_date")
    public static final SqlColumn<String> appointmentEndDate = moeGroomingAppointment.appointmentEndDate;

    /**
     * Database Column Remarks:
     *   包含的服务类型，用位图表示，二进制从低到高依次为 grooming, boarding, daycare, evaluation
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: moe_grooming_appointment.service_type_include")
    public static final SqlColumn<Integer> serviceTypeInclude = moeGroomingAppointment.serviceTypeInclude;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_appointment")
    public static final class MoeGroomingAppointment extends AliasableSqlTable<MoeGroomingAppointment> {
        public final SqlColumn<Integer> id = column("id", JDBCType.INTEGER);

        public final SqlColumn<String> orderId = column("order_id", JDBCType.CHAR);

        public final SqlColumn<Integer> businessId = column("business_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> customerId = column("customer_id", JDBCType.INTEGER);

        public final SqlColumn<String> appointmentDate = column("appointment_date", JDBCType.VARCHAR);

        public final SqlColumn<Integer> appointmentStartTime = column("appointment_start_time", JDBCType.INTEGER);

        public final SqlColumn<Integer> appointmentEndTime = column("appointment_end_time", JDBCType.INTEGER);

        public final SqlColumn<Byte> isWaitingList = column("is_waiting_list", JDBCType.TINYINT);

        public final SqlColumn<Integer> moveWaitingBy = column("move_waiting_by", JDBCType.INTEGER);

        public final SqlColumn<Long> confirmedTime = column("confirmed_time", JDBCType.BIGINT);

        public final SqlColumn<Long> checkInTime = column("check_in_time", JDBCType.BIGINT);

        public final SqlColumn<Long> checkOutTime = column("check_out_time", JDBCType.BIGINT);

        public final SqlColumn<Long> canceledTime = column("canceled_time", JDBCType.BIGINT);

        public final SqlColumn<Status> status = column("`status`", JDBCType.TINYINT, "example.mapper.typehandler.AppointmentStatusTypeHandler");

        public final SqlColumn<Boolean> isBlock = column("is_block", JDBCType.BIT);

        public final SqlColumn<Byte> bookOnlineStatus = column("book_online_status", JDBCType.TINYINT);

        public final SqlColumn<Integer> customerAddressId = column("customer_address_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> repeatId = column("repeat_id", JDBCType.INTEGER);

        public final SqlColumn<Byte> isPaid = column("is_paid", JDBCType.TINYINT);

        public final SqlColumn<String> colorCode = column("color_code", JDBCType.VARCHAR);

        public final SqlColumn<Byte> noShow = column("no_show", JDBCType.TINYINT);

        public final SqlColumn<BigDecimal> noShowFee = column("no_show_fee", JDBCType.DECIMAL);

        public final SqlColumn<Byte> isPustNotification = column("is_pust_notification", JDBCType.TINYINT);

        public final SqlColumn<Byte> cancelByType = column("cancel_by_type", JDBCType.TINYINT);

        public final SqlColumn<Integer> cancelBy = column("cancel_by", JDBCType.INTEGER);

        public final SqlColumn<Byte> confirmByType = column("confirm_by_type", JDBCType.TINYINT);

        public final SqlColumn<Integer> confirmBy = column("confirm_by", JDBCType.INTEGER);

        public final SqlColumn<Integer> createdById = column("created_by_id", JDBCType.INTEGER);

        public final SqlColumn<Byte> outOfArea = column("out_of_area", JDBCType.TINYINT);

        public final SqlColumn<Byte> isDeprecate = column("is_deprecate", JDBCType.TINYINT);

        public final SqlColumn<Long> createTime = column("create_time", JDBCType.BIGINT);

        public final SqlColumn<Long> updateTime = column("update_time", JDBCType.BIGINT);

        public final SqlColumn<Integer> source = column("`source`", JDBCType.INTEGER);

        public final SqlColumn<String> oldAppointmentDate = column("old_appointment_date", JDBCType.VARCHAR);

        public final SqlColumn<Integer> oldAppointmentStartTime = column("old_appointment_start_time", JDBCType.INTEGER);

        public final SqlColumn<Integer> oldAppointmentEndTime = column("old_appointment_end_time", JDBCType.INTEGER);

        public final SqlColumn<Integer> oldApptId = column("old_appt_id", JDBCType.INTEGER);

        public final SqlColumn<Byte> scheduleType = column("schedule_type", JDBCType.TINYINT);

        public final SqlColumn<String> sourcePlatform = column("source_platform", JDBCType.VARCHAR);

        public final SqlColumn<Long> readyTime = column("ready_time", JDBCType.BIGINT);

        public final SqlColumn<Integer> pickupNotificationSendStatus = column("pickup_notification_send_status", JDBCType.INTEGER);

        public final SqlColumn<String> pickupNotificationFailedReason = column("pickup_notification_failed_reason", JDBCType.VARCHAR);

        public final SqlColumn<Byte> statusBeforeCheckin = column("status_before_checkin", JDBCType.TINYINT);

        public final SqlColumn<Byte> statusBeforeReady = column("status_before_ready", JDBCType.TINYINT);

        public final SqlColumn<Byte> statusBeforeFinish = column("status_before_finish", JDBCType.TINYINT);

        public final SqlColumn<Boolean> noStartTime = column("no_start_time", JDBCType.BIT);

        public final SqlColumn<Long> updatedById = column("updated_by_id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Boolean> isAutoAccept = column("is_auto_accept", JDBCType.BIT);

        public final SqlColumn<Byte> waitListStatus = column("wait_list_status", JDBCType.TINYINT);

        public final SqlColumn<String> appointmentEndDate = column("appointment_end_date", JDBCType.VARCHAR);

        public final SqlColumn<Integer> serviceTypeInclude = column("service_type_include", JDBCType.INTEGER);

        public MoeGroomingAppointment() {
            super("moe_grooming_appointment", MoeGroomingAppointment::new);
        }
    }
}