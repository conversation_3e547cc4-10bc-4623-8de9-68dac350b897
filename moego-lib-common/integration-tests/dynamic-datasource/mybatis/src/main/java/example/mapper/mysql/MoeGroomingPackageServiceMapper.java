package example.mapper.mysql;

import static example.mapper.mysql.MoeGroomingPackageServiceDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import example.entity.mysql.MoeGroomingPackageService;
import example.mapper.typehandler.PackageServiceTypeHandler;
import jakarta.annotation.Generated;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MoeGroomingPackageServiceMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<MoeGroomingPackageServiceMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    BasicColumn[] selectList = BasicColumn.columnList(id, packageId, totalQuantity, remainingQuantity, services);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Integer.class)
    int insert(InsertStatementProvider<MoeGroomingPackageService> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MoeGroomingPackageServiceResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="package_id", property="packageId", jdbcType=JdbcType.INTEGER),
        @Result(column="total_quantity", property="totalQuantity", jdbcType=JdbcType.INTEGER),
        @Result(column="remaining_quantity", property="remainingQuantity", jdbcType=JdbcType.INTEGER),
        @Result(column="services", property="services", typeHandler=PackageServiceTypeHandler.class, jdbcType=JdbcType.LONGVARCHAR)
    })
    List<MoeGroomingPackageService> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MoeGroomingPackageServiceResult")
    Optional<MoeGroomingPackageService> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, moeGroomingPackageService, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, moeGroomingPackageService, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    default int deleteByPrimaryKey(Integer id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    default int insertSelective(MoeGroomingPackageService row) {
        return MyBatis3Utils.insert(this::insert, row, moeGroomingPackageService, c ->
            c.map(packageId).toPropertyWhenPresent("packageId", row::getPackageId)
            .map(totalQuantity).toPropertyWhenPresent("totalQuantity", row::getTotalQuantity)
            .map(remainingQuantity).toPropertyWhenPresent("remainingQuantity", row::getRemainingQuantity)
            .map(services).toPropertyWhenPresent("services", row::getServices)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    default Optional<MoeGroomingPackageService> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, moeGroomingPackageService, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    default List<MoeGroomingPackageService> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, moeGroomingPackageService, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    default List<MoeGroomingPackageService> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, moeGroomingPackageService, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    default Optional<MoeGroomingPackageService> selectByPrimaryKey(Integer id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, moeGroomingPackageService, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    static UpdateDSL<UpdateModel> updateAllColumns(MoeGroomingPackageService row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(packageId).equalTo(row::getPackageId)
                .set(totalQuantity).equalTo(row::getTotalQuantity)
                .set(remainingQuantity).equalTo(row::getRemainingQuantity)
                .set(services).equalTo(row::getServices);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MoeGroomingPackageService row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(packageId).equalToWhenPresent(row::getPackageId)
                .set(totalQuantity).equalToWhenPresent(row::getTotalQuantity)
                .set(remainingQuantity).equalToWhenPresent(row::getRemainingQuantity)
                .set(services).equalToWhenPresent(row::getServices);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: moe_grooming_package_service")
    default int updateByPrimaryKeySelective(MoeGroomingPackageService row) {
        return update(c ->
            c.set(packageId).equalToWhenPresent(row::getPackageId)
            .set(totalQuantity).equalToWhenPresent(row::getTotalQuantity)
            .set(remainingQuantity).equalToWhenPresent(row::getRemainingQuantity)
            .set(services).equalToWhenPresent(row::getServices)
            .where(id, isEqualTo(row::getId))
        );
    }
}