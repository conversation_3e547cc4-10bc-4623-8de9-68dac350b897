package com.moego.svc.online.booking.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.EvaluationTestDetailModel;
import com.moego.idl.models.online_booking.v1.PetToLodgingDef;
import com.moego.idl.models.online_booking.v1.PetToStaffDef;
import com.moego.idl.models.organization.v1.StaffBasicView;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoResponse;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetSizeServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListPetSizeRequest;
import com.moego.idl.service.business_customer.v1.ListPetSizeResponse;
import com.moego.idl.service.online_booking.v1.GetAutoAssignResponse;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AutoAssignServiceTest {

    @Mock
    private BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub businessCustomerPetService;

    @Mock
    private BusinessPetSizeServiceGrpc.BusinessPetSizeServiceBlockingStub businessPetSizeServiceClient;

    @InjectMocks
    private AutoAssignService autoAssignService;

    @Test
    void testGetPetMap() {
        // Arrange
        long companyId = 1L;
        List<Long> petIdList = Arrays.asList(1L, 2L, 3L);
        BatchGetPetInfoResponse mockResponse = BatchGetPetInfoResponse.newBuilder()
                .addPets(BusinessCustomerPetInfoModel.newBuilder().setId(1L).build())
                .addPets(BusinessCustomerPetInfoModel.newBuilder().setId(2L).build())
                .addPets(BusinessCustomerPetInfoModel.newBuilder().setId(3L).build())
                .build();
        when(businessCustomerPetService.batchGetPetInfo(any(BatchGetPetInfoRequest.class)))
                .thenReturn(mockResponse);

        // Act
        Map<Long, BusinessCustomerPetInfoModel> result = autoAssignService.getPetMap(companyId, petIdList);

        // Assert
        assertEquals(3, result.size());
        assertTrue(result.containsKey(1L));
        assertTrue(result.containsKey(2L));
        assertTrue(result.containsKey(3L));
        verify(businessCustomerPetService).batchGetPetInfo(any(BatchGetPetInfoRequest.class));
    }

    @Test
    void testGetPetMapWithEmptyList() {
        // Arrange
        long companyId = 1L;
        List<Long> petIdList = Collections.emptyList();

        // Act
        Map<Long, BusinessCustomerPetInfoModel> result = autoAssignService.getPetMap(companyId, petIdList);

        // Assert
        assertTrue(result.isEmpty());
        verify(businessCustomerPetService, never()).batchGetPetInfo(any(BatchGetPetInfoRequest.class));
    }

    @Test
    void testGetPetSizeList() {
        // Arrange
        long companyId = 1L;
        ListPetSizeResponse mockResponse = ListPetSizeResponse.newBuilder()
                .addSizes(BusinessPetSizeModel.newBuilder().setId(1L).build())
                .addSizes(BusinessPetSizeModel.newBuilder().setId(2L).build())
                .build();
        when(businessPetSizeServiceClient.listPetSize(any(ListPetSizeRequest.class)))
                .thenReturn(mockResponse);

        // Act
        List<BusinessPetSizeModel> result = autoAssignService.getPetSizeList(companyId);

        // Assert
        assertEquals(2, result.size());
        verify(businessPetSizeServiceClient).listPetSize(any(ListPetSizeRequest.class));
    }

    @Test
    void testAutoAssignAllPetsToSameLodging() {
        // Arrange
        BookingRequestModel bookingRequest = createMockBookingRequest();
        List<LodgingTypeModel> lodgingTypeList = createMockLodgingTypeList();
        List<LodgingUnitModel> lodgingUnitList = createMockLodgingUnitList();
        Map<Long, ServiceBriefView> serviceMap = createMockServiceMap();
        List<BusinessPetSizeModel> petSizeList = createMockPetSizeList();
        Map<Long, BusinessCustomerPetInfoModel> petMap = createMockPetMap();
        List<LodgingAssignInfo> assignInfoList = Collections.emptyList();

        // Act
        List<PetToLodgingDef> result = AutoAssignService.autoAssign(
                bookingRequest, lodgingTypeList, lodgingUnitList, serviceMap, petSizeList, petMap, assignInfoList);

        // Assert
        assertFalse(result.isEmpty());
        long assignedLodgingId = result.get(0).getLodgingUnitId();
        for (PetToLodgingDef petToLodging : result) {
            assertEquals(assignedLodgingId, petToLodging.getLodgingUnitId());
        }
    }

    @Test
    void testAutoAssignPetsToDifferentLodgings() {
        // Arrange
        BookingRequestModel bookingRequest = createMockBookingRequest();
        List<LodgingTypeModel> lodgingTypeList = createMockLodgingTypeList();
        List<LodgingUnitModel> lodgingUnitList = createMockLodgingUnitListWithLimitedCapacity();
        Map<Long, ServiceBriefView> serviceMap = createMockServiceMap();
        List<BusinessPetSizeModel> petSizeList = createMockPetSizeList();
        Map<Long, BusinessCustomerPetInfoModel> petMap = createMockPetMap();
        List<LodgingAssignInfo> assignInfoList = Collections.emptyList();

        // Act
        List<PetToLodgingDef> result = AutoAssignService.autoAssign(
                bookingRequest, lodgingTypeList, lodgingUnitList, serviceMap, petSizeList, petMap, assignInfoList);

        // Assert
        assertFalse(result.isEmpty());
        Set<Long> assignedLodgingIds = new HashSet<>();
        for (PetToLodgingDef petToLodging : result) {
            assignedLodgingIds.add(petToLodging.getLodgingUnitId());
        }
        assertTrue(assignedLodgingIds.size() > 1);
    }

    @Test
    void testFilterLodgingType() {
        // Arrange
        List<LodgingTypeModel> lodgingTypeList = createMockLodgingTypeList();
        List<GetAutoAssignResponse.AssignRequire> roomAssignRequires = createMockAssignRequires();
        Map<Long, BusinessCustomerPetInfoModel> petMap = createMockPetMap();
        List<BusinessPetSizeModel> petSizeList = createMockPetSizeList();
        Map<Long, ServiceBriefView> serviceMap = createMockServiceMap();

        // Act
        List<LodgingTypeModel> result = AutoAssignService.filterLodgingType(
                lodgingTypeList, roomAssignRequires, petMap, petSizeList, serviceMap);

        // Assert
        assertFalse(result.isEmpty());
        assertFalse(result.size() < lodgingTypeList.size());
    }

    // Helper methods to create mock data

    private BookingRequestModel createMockBookingRequest() {
        return BookingRequestModel.newBuilder()
                .setStartDate("2024-10-01")
                .setEndDate("2024-10-05")
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setBoarding(BookingRequestModel.BoardingService.newBuilder()
                                .setService(BoardingServiceDetailModel.newBuilder()
                                        .setPetId(1L)
                                        .setServiceId(1L)
                                        .setStartDate("2024-10-01")
                                        .setEndDate("2024-10-05")
                                        .build())
                                .build())
                        .build())
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setBoarding(BookingRequestModel.BoardingService.newBuilder()
                                .setService(BoardingServiceDetailModel.newBuilder()
                                        .setPetId(2L)
                                        .setServiceId(1L)
                                        .setStartDate("2024-10-01")
                                        .setEndDate("2024-10-05")
                                        .build())
                                .build())
                        .build())
                .build();
    }

    private List<LodgingTypeModel> createMockLodgingTypeList() {
        return Arrays.asList(
                LodgingTypeModel.newBuilder().setId(1L).setMaxPetNum(2).build(),
                LodgingTypeModel.newBuilder().setId(2L).setMaxPetNum(1).build());
    }

    private List<LodgingUnitModel> createMockLodgingUnitList() {
        return Arrays.asList(
                LodgingUnitModel.newBuilder().setId(1L).setLodgingTypeId(1L).build(),
                LodgingUnitModel.newBuilder().setId(2L).setLodgingTypeId(1L).build(),
                LodgingUnitModel.newBuilder().setId(3L).setLodgingTypeId(2L).build());
    }

    private List<LodgingUnitModel> createMockLodgingUnitListWithLimitedCapacity() {
        return Arrays.asList(
                LodgingUnitModel.newBuilder().setId(1L).setLodgingTypeId(2L).build(),
                LodgingUnitModel.newBuilder().setId(2L).setLodgingTypeId(2L).build());
    }

    private Map<Long, ServiceBriefView> createMockServiceMap() {
        Map<Long, ServiceBriefView> serviceMap = new HashMap<>();
        serviceMap.put(1L, ServiceBriefView.newBuilder().setId(1L).build());
        return serviceMap;
    }

    private List<BusinessPetSizeModel> createMockPetSizeList() {
        return Arrays.asList(
                BusinessPetSizeModel.newBuilder()
                        .setId(1L)
                        .setWeightLow(0)
                        .setWeightHigh(10)
                        .build(),
                BusinessPetSizeModel.newBuilder()
                        .setId(2L)
                        .setWeightLow(11)
                        .setWeightHigh(20)
                        .build());
    }

    private Map<Long, BusinessCustomerPetInfoModel> createMockPetMap() {
        Map<Long, BusinessCustomerPetInfoModel> petMap = new HashMap<>();
        petMap.put(
                1L,
                BusinessCustomerPetInfoModel.newBuilder()
                        .setId(1L)
                        .setWeight("5")
                        .build());
        petMap.put(
                2L,
                BusinessCustomerPetInfoModel.newBuilder()
                        .setId(2L)
                        .setWeight("15")
                        .build());
        return petMap;
    }

    private List<GetAutoAssignResponse.AssignRequire> createMockAssignRequires() {
        return Arrays.asList(
                GetAutoAssignResponse.AssignRequire.newBuilder()
                        .setPetId(1L)
                        .setServiceId(1L)
                        .build(),
                GetAutoAssignResponse.AssignRequire.newBuilder()
                        .setPetId(2L)
                        .setServiceId(1L)
                        .build());
    }

    @Test
    void autoAssignToStaff_SpecificStaffAllowed() {
        var evaluations = Map.of(
                2L,
                EvaluationBriefView.newBuilder()
                        .setId(2L)
                        .setIsAllStaff(false)
                        .setAllowStaffAutoAssign(true)
                        .addAllowedStaffList(1L)
                        .build());
        var staff1 = StaffBasicView.newBuilder().setId(1L).setSort(1).build();
        var staff2 = StaffBasicView.newBuilder().setId(2L).setSort(2).build();
        var staffs = List.of(staff1, staff2);

        var petEvaluation = EvaluationTestDetailModel.newBuilder()
                .setPetId(1L)
                .setEvaluationId(2L)
                .build();
        var result = AutoAssignService.autoAssignToStaff(List.of(petEvaluation), evaluations, staffs);
        var expect = PetToStaffDef.newBuilder()
                .setPetId(1L)
                .setServiceId(2L)
                .setStaffId(1L)
                .build();

        assertThat(result).isEqualTo(List.of(expect));
    }

    @Test
    void autoAssignToStaff_NoStaffAllowed() {
        var evaluations = Map.of(
                2L,
                EvaluationBriefView.newBuilder().setId(2L).setIsAllStaff(false).build());
        var staff1 = StaffBasicView.newBuilder().setId(1L).setSort(1).build();
        var staff2 = StaffBasicView.newBuilder().setId(2L).setSort(2).build();
        var staffs = Arrays.asList(staff1, staff2);

        EvaluationTestDetailModel petEvaluation = EvaluationTestDetailModel.newBuilder()
                .setPetId(1L)
                .setEvaluationId(2L)
                .build();
        var result = AutoAssignService.autoAssignToStaff(List.of(petEvaluation), evaluations, staffs);

        assertThat(result).isEmpty();
    }

    @Test
    void autoAssignToStaff_MultiplePetEvaluations() {
        var evaluation1 = EvaluationBriefView.newBuilder()
                .setId(2L)
                .setIsAllStaff(true)
                .setAllowStaffAutoAssign(true)
                .build();
        var evaluation2 = EvaluationBriefView.newBuilder()
                .setId(4L)
                .setIsAllStaff(true)
                .setAllowStaffAutoAssign(true)
                .build();
        var evaluations = Map.of(2L, evaluation1, 4L, evaluation2);
        var staff1 = StaffBasicView.newBuilder().setId(1L).setSort(1).build();
        var staff2 = StaffBasicView.newBuilder().setId(2L).setSort(2).build();
        List<StaffBasicView> staffs = List.of(staff1, staff2);

        var petEvaluation1 = EvaluationTestDetailModel.newBuilder()
                .setPetId(1L)
                .setEvaluationId(2L)
                .build();
        var petEvaluation2 = EvaluationTestDetailModel.newBuilder()
                .setPetId(3L)
                .setEvaluationId(4L)
                .build();
        var result = AutoAssignService.autoAssignToStaff(List.of(petEvaluation1, petEvaluation2), evaluations, staffs);

        var expect1 = PetToStaffDef.newBuilder()
                .setPetId(1L)
                .setServiceId(2L)
                .setStaffId(2L)
                .build();
        var expect2 = PetToStaffDef.newBuilder()
                .setPetId(3L)
                .setServiceId(4L)
                .setStaffId(2L)
                .build();
        assertThat(result).isEqualTo(List.of(expect1, expect2));
    }
}
