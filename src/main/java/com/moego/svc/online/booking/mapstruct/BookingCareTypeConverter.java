package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.online_booking.v1.ApplicableServices;
import com.moego.idl.models.online_booking.v1.BookingCareType;
import com.moego.idl.models.online_booking.v1.BookingCareTypeView;
import com.moego.svc.online.booking.entity.ObCustomizeCareType;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

/**
 * BookingCareType转换器
 * 用于在protobuf对象和实体对象之间进行转换
 */
@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {TimestampConverter.class})
public interface BookingCareTypeConverter {
    
    BookingCareTypeConverter INSTANCE = Mappers.getMapper(BookingCareTypeConverter.class);

    /**
     * 实体转换为protobuf模型
     */
    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "localDateTimeToTimestamp")
    @Mapping(target = "updatedAt", source = "updatedAt", qualifiedByName = "localDateTimeToTimestamp")
    @Mapping(target = "deletedAt", source = "deletedAt", qualifiedByName = "localDateTimeToTimestamp")
    @Mapping(target = "isAllServiceApplicable", ignore = true)
    @Mapping(target = "selectedServices", ignore = true)
    BookingCareType entityToModel(ObCustomizeCareType entity);

    /**
     * 实体转换为视图模型
     */
    BookingCareTypeView entityToView(ObCustomizeCareType entity);

    /**
     * protobuf模型转换为实体
     */
    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "timestampToLocalDateTime")
    @Mapping(target = "updatedAt", source = "updatedAt", qualifiedByName = "timestampToLocalDateTime")
    @Mapping(target = "deletedAt", source = "deletedAt", qualifiedByName = "timestampToLocalDateTime")
    @Mapping(target = "isAllServiceApplicable", ignore = true)
    @Mapping(target = "selectedServices", ignore = true)
    ObCustomizeCareType modelToEntity(BookingCareType model);

    /**
     * 设置ApplicableServices
     */
    @AfterMapping
    default void setApplicableServices(ObCustomizeCareType source, @MappingTarget BookingCareType.Builder target) {
        if (source.getIsAllServiceApplicable() != null) {
            target.setIsAllServiceApplicable(source.getIsAllServiceApplicable());
        }

        if (!CollectionUtils.isEmpty(source.getSelectedServices())) {
            target.addAllSelectedServices(source.getSelectedServices());
        }
    }

    /**
     * 设置ApplicableServices for View
     */
    @AfterMapping
    default void setApplicableServicesForView(ObCustomizeCareType source, @MappingTarget BookingCareTypeView.Builder target) {
        ApplicableServices.Builder applicableServicesBuilder = ApplicableServices.newBuilder();
        
        if (source.getIsAllServiceApplicable() != null) {
            applicableServicesBuilder.setIsAllServiceApplicable(source.getIsAllServiceApplicable());
        }
        
        if (!CollectionUtils.isEmpty(source.getSelectedServices())) {
            applicableServicesBuilder.addAllSelectedServices(source.getSelectedServices());
        }
        
        target.setApplicableServices(applicableServicesBuilder.build());
    }

    /**
     * 从protobuf模型设置ApplicableServices到实体
     */
    @AfterMapping
    default void setApplicableServicesFromModel(BookingCareType source, @MappingTarget ObCustomizeCareType target) {
        target.setIsAllServiceApplicable(source.getIsAllServiceApplicable());
        target.setSelectedServices(source.getSelectedServicesList());
    }

    /**
     * LocalDateTime转换为protobuf Timestamp
     */
    @Named("localDateTimeToTimestamp")
    default com.google.protobuf.Timestamp localDateTimeToTimestamp(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return com.google.protobuf.Timestamp.getDefaultInstance();
        }
        return com.google.protobuf.Timestamp.newBuilder()
                .setSeconds(localDateTime.toEpochSecond(ZoneOffset.UTC))
                .setNanos(localDateTime.getNano())
                .build();
    }

    /**
     * protobuf Timestamp转换为LocalDateTime
     */
    @Named("timestampToLocalDateTime")
    default LocalDateTime timestampToLocalDateTime(com.google.protobuf.Timestamp timestamp) {
        if (timestamp == null || timestamp.equals(com.google.protobuf.Timestamp.getDefaultInstance())) {
            return null;
        }
        return LocalDateTime.ofEpochSecond(timestamp.getSeconds(), timestamp.getNanos(), ZoneOffset.UTC);
    }

    /**
     * 批量转换实体列表为视图列表
     */
    default List<BookingCareTypeView> entityListToViewList(List<ObCustomizeCareType> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return List.of();
        }
        return entities.stream()
                .map(this::entityToView)
                .toList();
    }
}
