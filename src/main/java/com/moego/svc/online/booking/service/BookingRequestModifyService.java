package com.moego.svc.online.booking.service;

import static com.moego.common.utils.CommonUtil.isNormal;

import com.moego.common.enums.ServiceEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.api.online_booking.v1.ServiceDetail;
import com.moego.idl.models.appointment.v1.AppointmentCreateForOnlineBookingDef;
import com.moego.idl.models.appointment.v1.AppointmentNoteCreateDef;
import com.moego.idl.models.appointment.v1.AppointmentNoteType;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.SelectedAddOnDef;
import com.moego.idl.models.appointment.v1.SelectedEvaluationDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.EvaluationTestDetailModel;
import com.moego.idl.models.online_booking.v1.PetToLodgingDef;
import com.moego.idl.models.online_booking.v1.PetToServiceDef;
import com.moego.idl.models.online_booking.v1.PetToStaffDef;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.CreateAppointmentForOnlineBookingRequest;
import com.moego.idl.service.appointment.v1.CreateAppointmentForOnlineBookingResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.online.booking.mapstruct.BookingRequestConverter;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class BookingRequestModifyService {

    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentService;

    public List<Long> createAppointment(
            BookingRequestModel bookingRequest,
            Long companyId,
            Long businessId,
            Long staffId,
            AppointmentStatus appointmentStatus,
            List<PetToLodgingDef> petToLodgings,
            List<PetToStaffDef> petToStaffs,
            List<PetToStaffDef> evaluationPetToStaffs,
            List<PetToServiceDef> petToServices) {
        Map<Long, Long> petToLodgingMap = petToLodgings.stream()
                .collect(Collectors.toMap(PetToLodgingDef::getPetId, PetToLodgingDef::getLodgingUnitId));

        Map<AbstractMap.SimpleEntry<Long, Long>, PetToStaffDef> petToStaffMap = petToStaffs.stream()
                .collect(Collectors.toMap(
                        obj -> new AbstractMap.SimpleEntry<>(obj.getPetId(), obj.getServiceId()), Function.identity()));
        Map<AbstractMap.SimpleEntry<Long, Long>, PetToStaffDef> evaluationPetToStaffMap = evaluationPetToStaffs.stream()
                .collect(Collectors.toMap(
                        obj -> new AbstractMap.SimpleEntry<>(obj.getPetId(), obj.getServiceId()), Function.identity()));
        var petToEvaluationMap = petToServices.stream()
                .filter(k -> k.hasFromEvaluationServiceId() && k.hasToEvaluationServiceId())
                .collect(Collectors.toMap(
                        obj -> new AbstractMap.SimpleEntry<>(obj.getPetId(), obj.getFromEvaluationServiceId()),
                        PetToServiceDef::getToEvaluationServiceId));

        List<Long> appointmentIds = new ArrayList<>();
        // If the booking request is for daycare, create an appointment for each date
        if (Objects.equals(ServiceItemEnum.DAYCARE.getBitValue(), bookingRequest.getServiceTypeInclude())) {
            bookingRequest.getServicesList().stream()
                    .filter(BookingRequestModel.Service::hasDaycare)
                    .flatMap(service -> service.getDaycare().getService().getSpecificDatesList().stream())
                    .distinct()
                    .sorted()
                    .forEach(date -> {
                        BookingRequestModel build = bookingRequest.toBuilder()
                                .setStartDate(date)
                                .setEndDate(date)
                                .build();
                        Long appointmentId = createOneAppointment(
                                companyId,
                                businessId,
                                staffId,
                                build,
                                appointmentStatus,
                                petToLodgingMap,
                                petToStaffMap,
                                evaluationPetToStaffMap,
                                petToEvaluationMap);
                        appointmentIds.add(appointmentId);
                    });
            return appointmentIds;
        }

        Long appointmentId = createOneAppointment(
                companyId,
                businessId,
                staffId,
                bookingRequest,
                appointmentStatus,
                petToLodgingMap,
                petToStaffMap,
                evaluationPetToStaffMap,
                petToEvaluationMap);
        return List.of(appointmentId);
    }

    public Long createOneAppointment(
            Long companyId,
            Long businessId,
            Long staffId,
            BookingRequestModel bookingRequest,
            AppointmentStatus appointmentStatus,
            Map<Long, Long> petToLodgingUnitMap,
            Map<AbstractMap.SimpleEntry<Long, Long>, PetToStaffDef> petToStaffMap,
            Map<AbstractMap.SimpleEntry<Long, Long>, PetToStaffDef> evaluationPetToStaffMap,
            Map<AbstractMap.SimpleEntry<Long, Long>, Long> petToEvaluationMap) {

        Map<Long, List<BookingRequestModel.Service>> petServicesMap = bookingRequest.getServicesList().stream()
                .collect(Collectors.groupingBy(service -> switch (service.getServiceCase()) {
                    case BOARDING -> service.getBoarding().getService().getPetId();
                    case DAYCARE -> service.getDaycare().getService().getPetId();
                    case GROOMING -> service.getGrooming().getService().getPetId();
                    case EVALUATION -> service.getEvaluation().getService().getPetId();
                    case DOG_WALKING -> service.getDogWalking().getService().getPetId();
                    default -> 0L;
                }));

        List<PetDetailDef> serviceDetails = petServicesMap.entrySet().stream()
                .map(entry -> {
                    Long petId = entry.getKey();
                    List<BookingRequestModel.Service> services = entry.getValue();

                    List<SelectedServiceDef> serviceDefs = new ArrayList<>();
                    List<SelectedAddOnDef> addOnDefs = new ArrayList<>();
                    List<SelectedEvaluationDef> evaluationDefs = new ArrayList<>();

                    services.forEach(service -> {
                        ServiceDetail.Service.Builder serviceBuilder = ServiceDetail.Service.newBuilder();
                        switch (service.getServiceCase()) {
                            case BOARDING -> {
                                BookingRequestModel.BoardingService boarding = service.getBoarding();
                                BoardingServiceDetailModel boardingService = boarding.getService();

                                long lodgingId = boardingService.getLodgingId();
                                if (!CollectionUtils.isEmpty(petToLodgingUnitMap)) {
                                    lodgingId = petToLodgingUnitMap.getOrDefault(petId, lodgingId);
                                }
                                serviceDefs.add(BookingRequestConverter.INSTANCE.boardingToSelectedServiceDef(
                                        boardingService,
                                        lodgingId,
                                        boarding.getFeedingsList(),
                                        boarding.getMedicationsList()));

                                addOnDefs.addAll(boarding.getAddonsList().stream()
                                        .map(addOn -> {
                                            SelectedAddOnDef.Builder builder = BookingRequestConverter.INSTANCE
                                                    .boardingAddOnToSelectedServiceAddOnDef(
                                                            addOn,
                                                            bookingRequest.getStartDate(),
                                                            bookingRequest.getStartTime(),
                                                            boardingService.getServiceId())
                                                    .toBuilder();
                                            if (!CollectionUtils.isEmpty(petToStaffMap)) {
                                                PetToStaffDef petToStaff = petToStaffMap.get(
                                                        new AbstractMap.SimpleEntry<>(petId, addOn.getAddOnId()));
                                                if (Objects.nonNull(petToStaff)) {
                                                    builder.setStaffId(petToStaff.getStaffId())
                                                            .setStartTime(petToStaff.getStartTime())
                                                            .setStartDate(
                                                                    StringUtils.hasText(addOn.getSpecificDates(0))
                                                                            ? addOn.getSpecificDates(0)
                                                                            : bookingRequest.getStartDate())
                                                            .setAddonDateType(
                                                                    PetDetailDateType.PET_DETAIL_DATE_DATE_POINT);
                                                }
                                            }
                                            return builder.build();
                                        })
                                        .toList());
                            }
                            case DAYCARE -> {
                                BookingRequestModel.DaycareService daycare = service.getDaycare();
                                Long lodgingId = null;
                                if (!CollectionUtils.isEmpty(petToLodgingUnitMap)
                                        && petToLodgingUnitMap.containsKey(petId)) {
                                    lodgingId = petToLodgingUnitMap.get(petId);
                                }

                                serviceDefs.add(BookingRequestConverter.INSTANCE.daycareToSelectedServiceDef(
                                        daycare.getService(),
                                        bookingRequest.getStartDate(),
                                        bookingRequest.getEndDate(),
                                        lodgingId,
                                        daycare.getFeedingsList(),
                                        daycare.getMedicationsList()));

                                addOnDefs.addAll(daycare.getAddonsList().stream()
                                        .map(addOn -> {
                                            List<String> specificDates = addOn.getSpecificDatesList();
                                            if (!addOn.getIsEveryday()) {
                                                boolean matchDate = addOn.getSpecificDatesList().stream()
                                                        .anyMatch(date ->
                                                                Objects.equals(date, bookingRequest.getStartDate()));
                                                if (!matchDate) {
                                                    return null;
                                                }
                                                // if the add-on is not everyday, only add the specific date
                                                specificDates = List.of(bookingRequest.getStartDate());
                                            }
                                            SelectedAddOnDef.Builder builder = BookingRequestConverter.INSTANCE
                                                    .daycareAddOnToSelectedServiceAddOnDef(
                                                            addOn,
                                                            bookingRequest.getStartDate(),
                                                            bookingRequest.getStartTime(),
                                                            daycare.getService().getServiceId(),
                                                            specificDates)
                                                    .toBuilder();
                                            if (!CollectionUtils.isEmpty(petToStaffMap)) {
                                                PetToStaffDef petToStaff = petToStaffMap.get(
                                                        new AbstractMap.SimpleEntry<>(petId, addOn.getAddOnId()));
                                                if (Objects.nonNull(petToStaff)) {
                                                    builder.setStaffId(petToStaff.getStaffId())
                                                            .setStartTime(petToStaff.getStartTime())
                                                            .setAddonDateType(
                                                                    PetDetailDateType.PET_DETAIL_DATE_DATE_POINT);
                                                }
                                            }
                                            return builder.build();
                                        })
                                        .filter(Objects::nonNull)
                                        .toList());
                            }
                            case GROOMING -> {
                                BookingRequestModel.GroomingService grooming = service.getGrooming();
                                SelectedServiceDef.Builder serviceDefBuilder =
                                        BookingRequestConverter.INSTANCE
                                                .groomingToSelectedServiceDef(grooming.getService())
                                                .toBuilder();
                                // TODO: need adding auto assign staff

                                // createAppointmentForOnlineBooking 接口 grooming 必须传 staffId 和 startTime
                                var s = serviceDefBuilder.build();
                                if (isNormal(s.getStaffId()) && s.hasStartTime()) {
                                    serviceDefs.add(s);
                                }

                                addOnDefs.addAll(grooming.getAddonsList().stream()
                                        .map(addOn ->
                                                BookingRequestConverter.INSTANCE.groomingAddOnToSelectedServiceAddOnDef(
                                                        addOn, bookingRequest.getStartDate()))
                                        .filter(e -> isNormal(e.getStaffId()) && e.hasStartTime())
                                        .toList());
                            }
                            case EVALUATION -> evaluationDefs.add(buildSelectedEvaluationDef(
                                    service.getEvaluation().getService(), evaluationPetToStaffMap, petToEvaluationMap));
                            case DOG_WALKING -> {
                                var dogWalking = service.getDogWalking();
                                var serviceDef = BookingRequestConverter.INSTANCE.dogWalkingToSelectedServiceDef(
                                        dogWalking.getService());
                                serviceDefs.add(serviceDef);
                                // TODO dog walking add-ons
                            }
                            default -> serviceBuilder.build();
                        }
                    });

                    return PetDetailDef.newBuilder()
                            .setPetId(petId)
                            .addAllServices(serviceDefs)
                            .addAllAddOns(addOnDefs)
                            .addAllEvaluations(evaluationDefs)
                            .build();
                })
                .sorted(Comparator.comparing(PetDetailDef::getPetId))
                .toList();

        CreateAppointmentForOnlineBookingRequest.Builder builder =
                CreateAppointmentForOnlineBookingRequest.newBuilder();
        AppointmentCreateForOnlineBookingDef.Builder appointmentBuilder =
                AppointmentCreateForOnlineBookingDef.newBuilder()
                        .setCustomerId(bookingRequest.getCustomerId())
                        .setStatus(appointmentStatus)
                        .setCreatedAt(bookingRequest.getCreatedAt());
        if (Objects.equals(appointmentStatus, AppointmentStatus.CANCELED)) {
            appointmentBuilder.setCancelBy(staffId);
        }
        builder.setAppointment(appointmentBuilder.build());

        if (Objects.equals(appointmentStatus, AppointmentStatus.CANCELED)) {
            AppointmentNoteCreateDef cancelReasonNote = AppointmentNoteCreateDef.newBuilder()
                    .setNote(ServiceEnum.OB_CANCEL_REASON)
                    .setType(AppointmentNoteType.CANCEL)
                    .build();
            builder.addNotes(cancelReasonNote);
        }

        if (StringUtils.hasText(bookingRequest.getAdditionalNote())) {
            AppointmentNoteCreateDef additionalNote = AppointmentNoteCreateDef.newBuilder()
                    .setNote(bookingRequest.getAdditionalNote())
                    .setType(AppointmentNoteType.ADDITIONAL)
                    .build();
            builder.addNotes(additionalNote);
        }

        builder.setCompanyId(companyId)
                .setBusinessId(businessId)
                .setStaffId(staffId)
                .addAllPetDetails(serviceDetails)
                .setBookingRequestId(bookingRequest.getId())
                .setBookingRequestIdentifier(bookingRequest.getId() + "." + bookingRequest.getStartDate());
        CreateAppointmentForOnlineBookingResponse response =
                appointmentService.createAppointmentForOnlineBooking(builder.build());
        return response.getAppointmentId();
    }

    SelectedEvaluationDef buildSelectedEvaluationDef(
            EvaluationTestDetailModel evaluationService,
            Map<AbstractMap.SimpleEntry<Long, Long>, PetToStaffDef> evaluationPetToStaffMap,
            Map<AbstractMap.SimpleEntry<Long, Long>, Long> petToEvaluationMap) {
        Long petId = evaluationService.getPetId();

        var builder = BookingRequestConverter.INSTANCE.evaluationToSelectedServiceDef(evaluationService).toBuilder();

        if (!CollectionUtils.isEmpty(evaluationPetToStaffMap)) {
            var petToStaff = evaluationPetToStaffMap.get(
                    new AbstractMap.SimpleEntry<>(petId, evaluationService.getEvaluationId()));
            if (Objects.nonNull(petToStaff)) {
                builder.setStaffId(petToStaff.getStaffId()).setStartTime(petToStaff.getStartTime());
            }
        }

        if (!CollectionUtils.isEmpty(petToEvaluationMap)) {
            Long toEvaluationServiceId =
                    petToEvaluationMap.get(new AbstractMap.SimpleEntry<>(petId, evaluationService.getEvaluationId()));
            if (Objects.nonNull(toEvaluationServiceId)) {
                builder.setServiceId(toEvaluationServiceId);
            }
        }

        return builder.build();
    }
}
