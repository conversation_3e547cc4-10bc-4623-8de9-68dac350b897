package com.moego.svc.online.booking.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.svc.online.booking.mapper.ObCustomizeCareTypeDynamicSqlSupport.obCustomizeCareType;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.common.constant.Dictionary;
import com.moego.common.enums.OnlineBookingConst;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CustomizeCareTypeView;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceCategoryModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.online_booking.v1.BookingCareTypeView;
import com.moego.idl.service.metadata.v1.GetKeyRequest;
import com.moego.idl.service.metadata.v1.GetValueRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.idl.service.offering.v1.CustomizeCareTypeServiceGrpc;
import com.moego.idl.service.offering.v1.GetApplicableServiceListRequest;
import com.moego.idl.service.offering.v1.GetServiceListRequest;
import com.moego.idl.service.offering.v1.ListCareTypesRequest;
import com.moego.idl.service.offering.v1.ListCareTypesResponse;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.offering.v1.WhiteListFilter;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.common.util.Tx;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.server.grooming.api.IGroomingServiceService;
import com.moego.server.grooming.params.QueryServiceByTypeParams;
import com.moego.server.grooming.params.UpdateServiceByCareTypeParams;
import com.moego.svc.online.booking.entity.ObCustomizeCareType;
import com.moego.svc.online.booking.mapper.ObCustomizeCareTypeMapper;
import jakarta.annotation.Nullable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class BookingCareTypeService {

    private final ObCustomizeCareTypeMapper obCustomizeCareTypeMapper;

    private final IGroomingServiceService groomingServiceService;

    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementService;

    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceBlockingStub;

    private final CustomizeCareTypeServiceGrpc.CustomizeCareTypeServiceBlockingStub customizeCareTypeService;

    private final MetadataServiceGrpc.MetadataServiceBlockingStub metadataServiceBlockingStub;

    private final FeatureFlagApi featureFlagApi;

    /**
     * get booking care type
     *
     * @param id ID
     * @return 存在的记录或null
     */
    @Nullable
    public ObCustomizeCareType get(long id) {
        return obCustomizeCareTypeMapper
                .selectByPrimaryKey(id)
                .filter(e -> e.getDeletedAt() == null)
                .orElse(null);
    }

    public ObCustomizeCareType mustGet(long id) {
        return Optional.ofNullable(get(id))
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Booking care type not found: " + id));
    }

    /**
     * insert booking care type
     *
     * @param entity 实体
     * @return created ID
     */
    public long insert(ObCustomizeCareType entity) {
        check(entity);

        LocalDateTime now = LocalDateTime.now();
        entity.setCreatedAt(now);
        entity.setUpdatedAt(now);

        if (entity.getSort() == null) {
            entity.setSort(getNextSortValue(entity.getCompanyId(), entity.getBusinessId(), entity.getServiceType()));
        }

        obCustomizeCareTypeMapper.insertSelective(entity);
        return entity.getId();
    }

    /**
     * batch insert booking care types
     *
     * @param entities booking care types
     * @param staffId staff ID
     */
    private void batchInsert(List<ObCustomizeCareType> entities, Long staffId) {
        entities.forEach(entity -> {
            entity.setUpdatedBy(staffId);
            insert(entity);
        });
    }

    /**
     * update booking care type
     *
     * @param entity 实体
     * @return 更新的行数
     */
    public int update(ObCustomizeCareType entity) {
        if (entity.getId() == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "ID is required for update");
        }
        entity.setUpdatedAt(LocalDateTime.now());

        ObCustomizeCareType originalEntity = get(entity.getId());
        if (originalEntity == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Booking care type not found: " + entity.getId());
        }

        return Tx.doInTransaction(() -> {
            // 校验是否修改 selected_services
            Set<Long> updateIds = new HashSet<>(entity.getSelectedServices());
            Set<Long> originalIds = new HashSet<>(originalEntity.getSelectedServices());
            if (!originalEntity.getIsAllServiceApplicable().equals(entity.getIsAllServiceApplicable())
                    || !originalIds.equals(updateIds)) {
                if (!entity.getIsAllServiceApplicable()
                        && updateIds.isEmpty()
                        && entity.getServiceType() != ServiceType.ADDON) {
                    throw bizException(Code.CODE_PARAMS_ERROR, "No service selected");
                }

                // 检查是否有重复service
                if (entity.getServiceType().equals(ServiceType.SERVICE)) {
                    checkServiceDuplicate(originalEntity, entity, originalIds, updateIds);
                }
                originalIds.removeAll(updateIds);
                // 更新 BookOnlineAvailable
                updateServiceBookOnlineAvailable(entity, originalIds);
            }

            // update
            return obCustomizeCareTypeMapper.update(c -> ObCustomizeCareTypeMapper.updateSelectiveColumns(entity, c)
                    .where(obCustomizeCareType.id, isEqualTo(entity.getId()))
                    .and(obCustomizeCareType.deletedAt, isNull()));
        });
    }

    /**
     * list booking care types
     *
     * @param companyId company ID
     * @param businessId business ID
     * @return booking care types列表
     */
    public List<ObCustomizeCareType> listByCompanyAndBusiness(long companyId, long businessId) {
        return obCustomizeCareTypeMapper.select(c -> c.where(obCustomizeCareType.companyId, isEqualTo(companyId))
                .and(obCustomizeCareType.businessId, isEqualTo(businessId))
                .and(obCustomizeCareType.deletedAt, isNull())
                .orderBy(obCustomizeCareType.sort, obCustomizeCareType.id));
    }

    /**
     * delete booking care type
     *
     * @param id ID
     * @return 删除的行数
     */
    @Transactional
    public int delete(long id) {
        LocalDateTime now = LocalDateTime.now();
        ObCustomizeCareType updateEntity = new ObCustomizeCareType();
        updateEntity.setDeletedAt(now);
        updateEntity.setUpdatedAt(now);

        ObCustomizeCareType entity = mustGet(id);
        // 更新 BookOnlineAvailable
        updateServiceBookOnlineAvailable(entity, new HashSet<>(entity.getSelectedServices()));

        return obCustomizeCareTypeMapper.update(c -> ObCustomizeCareTypeMapper.updateSelectiveColumns(updateEntity, c)
                .where(obCustomizeCareType.id, isEqualTo(id))
                .and(obCustomizeCareType.deletedAt, isNull()));
    }

    /**
     * update service book online available
     *
     * @param careType booking care type
     * @return
     */
    public Boolean updateServiceBookOnlineAvailable(ObCustomizeCareType careType, Set<Long> deselectIds) {
        UpdateServiceByCareTypeParams params = new UpdateServiceByCareTypeParams(
                careType.getBusinessId(),
                careType.getCompanyId(),
                careType.getSelectedServices(),
                deselectIds.stream().toList(),
                careType.getIsAllServiceApplicable(),
                careType.getServiceType(),
                careType.getServiceItemType());
        return groomingServiceService.updateBookOnlineAvailable(params);
    }

    /**
     * get next sort value
     */
    private int getNextSortValue(long companyId, long businessId, ServiceType serviceType) {
        if (serviceType == ServiceType.ADDON) {
            // ADDON类型排在最后
            List<ObCustomizeCareType> existing =
                    obCustomizeCareTypeMapper.select(c -> c.where(obCustomizeCareType.companyId, isEqualTo(companyId))
                            .and(obCustomizeCareType.businessId, isEqualTo(businessId))
                            .and(obCustomizeCareType.deletedAt, isNull())
                            .orderBy(obCustomizeCareType.sort.descending())
                            .limit(1));

            if (existing.isEmpty()) {
                return 1;
            }

            Integer maxSort = existing.get(0).getSort();
            return (maxSort != null ? maxSort : 0) + 1;
        } else {
            // SERVICE类型插入到ADDON类型之前
            return getNextServiceSortValue(companyId, businessId);
        }
    }

    /**
     * 获取SERVICE类型的下一个排序值
     */
    private int getNextServiceSortValue(long companyId, long businessId) {
        // 查找ADDON类型的最小排序值
        List<ObCustomizeCareType> addonTypes =
                obCustomizeCareTypeMapper.select(c -> c.where(obCustomizeCareType.companyId, isEqualTo(companyId))
                        .and(obCustomizeCareType.businessId, isEqualTo(businessId))
                        .and(obCustomizeCareType.serviceType, isEqualTo(ServiceType.ADDON))
                        .and(obCustomizeCareType.deletedAt, isNull())
                        .orderBy(obCustomizeCareType.sort)
                        .limit(1));

        if (addonTypes.isEmpty()) {
            // 没有ADDON类型，查找最大排序值
            List<ObCustomizeCareType> existing =
                    obCustomizeCareTypeMapper.select(c -> c.where(obCustomizeCareType.companyId, isEqualTo(companyId))
                            .and(obCustomizeCareType.businessId, isEqualTo(businessId))
                            .and(obCustomizeCareType.deletedAt, isNull())
                            .orderBy(obCustomizeCareType.sort.descending())
                            .limit(1));

            if (existing.isEmpty()) {
                return 1;
            }

            Integer maxSort = existing.get(0).getSort();
            return (maxSort != null ? maxSort : 0) + 1;
        } else {
            // 有ADDON类型，插入到ADDON之前
            Integer addonSort = addonTypes.get(0).getSort();
            if (addonSort == null || addonSort <= 1) {
                // 需要重新调整ADDON的排序值
                adjustAddonSortValues(companyId, businessId);
                return getNextServiceSortValue(companyId, businessId);
            }
            return addonSort - 1;
        }
    }

    /**
     * 调整ADDON类型的排序值
     */
    private void adjustAddonSortValues(long companyId, long businessId) {
        List<ObCustomizeCareType> addonTypes =
                obCustomizeCareTypeMapper.select(c -> c.where(obCustomizeCareType.companyId, isEqualTo(companyId))
                        .and(obCustomizeCareType.businessId, isEqualTo(businessId))
                        .and(obCustomizeCareType.serviceType, isEqualTo(ServiceType.ADDON))
                        .and(obCustomizeCareType.deletedAt, isNull())
                        .orderBy(obCustomizeCareType.sort));

        if (!addonTypes.isEmpty()) {
            // 获取当前最大的SERVICE类型排序值
            List<ObCustomizeCareType> serviceTypes =
                    obCustomizeCareTypeMapper.select(c -> c.where(obCustomizeCareType.companyId, isEqualTo(companyId))
                            .and(obCustomizeCareType.businessId, isEqualTo(businessId))
                            .and(obCustomizeCareType.serviceType, isEqualTo(ServiceType.SERVICE))
                            .and(obCustomizeCareType.deletedAt, isNull())
                            .orderBy(obCustomizeCareType.sort.descending())
                            .limit(1));

            int startSort = serviceTypes.isEmpty()
                    ? 1
                    : (serviceTypes.get(0).getSort() != null
                                    ? serviceTypes.get(0).getSort()
                                    : 0)
                            + 2;

            // 重新设置ADDON类型的排序值
            LocalDateTime now = LocalDateTime.now();
            for (int i = 0; i < addonTypes.size(); i++) {
                ObCustomizeCareType addonType = addonTypes.get(i);
                ObCustomizeCareType updateEntity = new ObCustomizeCareType();
                updateEntity.setSort(startSort + i);
                updateEntity.setUpdatedAt(now);

                obCustomizeCareTypeMapper.update(c -> ObCustomizeCareTypeMapper.updateSelectiveColumns(updateEntity, c)
                        .where(obCustomizeCareType.id, isEqualTo(addonType.getId())));
            }
        }
    }

    /**
     * 检查name是否重复
     *
     * @param companyId companyId
     * @param businessId businessId
     * @param name care type name
     * @param excludeId excludeId
     * @return true if name is duplicate, false otherwise
     */
    public boolean isNameDuplicate(long companyId, long businessId, String name, Long excludeId) {
        if (name == null) {
            return false;
        }

        String trimmedName = name.trim();
        if (trimmedName.isEmpty()) {
            return false;
        }

        List<ObCustomizeCareType> existing = obCustomizeCareTypeMapper.select(c -> {
            var condition = c.where(obCustomizeCareType.companyId, isEqualTo(companyId))
                    .and(obCustomizeCareType.businessId, isEqualTo(businessId))
                    .and(obCustomizeCareType.deletedAt, isNull());

            if (excludeId != null) {
                condition = condition.and(obCustomizeCareType.id, isNotEqualTo(excludeId));
            }

            return condition;
        });

        return existing.stream()
                .anyMatch(entity -> trimmedName.equalsIgnoreCase(
                        entity.getName() != null ? entity.getName().trim() : ""));
    }

    /**
     * 检查 service 是否在其他 booking care type 中重复配置
     *
     * @param originalEntity original entity
     * @param updateEntity update entity
     * @param originalIds original service ids
     * @param updateIds update service ids
     */
    public void checkServiceDuplicate(
            ObCustomizeCareType originalEntity,
            ObCustomizeCareType updateEntity,
            Set<Long> originalIds,
            Set<Long> updateIds) {

        Long excludeId = updateEntity.getId();
        boolean isOriginalAll =
                originalEntity != null && Boolean.TRUE.equals(originalEntity.getIsAllServiceApplicable());
        boolean isUpdateAll = updateEntity.getIsAllServiceApplicable();

        // 1. 查询已存在同类型 care type
        List<ObCustomizeCareType> existingCareTypes = obCustomizeCareTypeMapper.select(
                c -> c.where(obCustomizeCareType.companyId, isEqualTo(updateEntity.getCompanyId()))
                        .and(obCustomizeCareType.businessId, isEqualTo(updateEntity.getBusinessId()))
                        .and(obCustomizeCareType.serviceItemType, isEqualTo(updateEntity.getServiceItemType()))
                        .and(obCustomizeCareType.serviceType, isEqualTo(updateEntity.getServiceType()))
                        .and(obCustomizeCareType.id, isNotEqualTo(excludeId))
                        .and(obCustomizeCareType.deletedAt, isNull()));

        // 2. 检查同类型 care type 中是否为"全部服务"
        boolean hasAllServiceCareType =
                existingCareTypes.stream().anyMatch(ObCustomizeCareType::getIsAllServiceApplicable);

        // 3. 收集其他 care type 的服务ID（排除全部服务的类型）
        Set<Long> existingServiceIds = existingCareTypes.stream()
                .filter(careType -> !careType.getIsAllServiceApplicable())
                .flatMap(careType -> careType.getSelectedServices().stream())
                .collect(Collectors.toSet());

        // 4. 场景处理
        if (!isOriginalAll && !isUpdateAll) {
            // 场景1: 部分服务 → 部分服务
            handlePartialToPartial(
                    updateIds, originalIds, hasAllServiceCareType, existingServiceIds, updateEntity.getCompanyId());
        } else if (!isOriginalAll) {
            // 场景2: 部分服务 → 全部服务
            handlePartialToAll(updateEntity, originalIds, hasAllServiceCareType, existingServiceIds);
        } else {
            // 场景3&4: 全部服务 → 部分/全部服务
            handleAllToAny(isUpdateAll, hasAllServiceCareType, existingServiceIds);
        }
    }

    // 分支处理方法
    private void handlePartialToPartial(
            Set<Long> updateIds,
            Set<Long> originalIds,
            boolean hasAllServiceCareType,
            Set<Long> existingServiceIds,
            Long companyId) {
        // 新增服务ID = 更新ID - 原始ID
        Set<Long> newServiceIds = new HashSet<>(updateIds);
        newServiceIds.removeAll(originalIds);

        if (!newServiceIds.isEmpty()) {
            checkForDuplicates(newServiceIds, hasAllServiceCareType, existingServiceIds, companyId);
        }
    }

    private void handlePartialToAll(
            ObCustomizeCareType updateEntity,
            Set<Long> originalIds,
            boolean hasAllServiceCareType,
            Set<Long> existingServiceIds) {
        // 获取所有可用serviceID
        Set<Long> allServiceIds = serviceManagementService
                .getApplicableServiceList(GetApplicableServiceListRequest.newBuilder()
                        .setCompanyId(updateEntity.getCompanyId())
                        .setBusinessId(updateEntity.getBusinessId())
                        .setServiceItemType(updateEntity.getServiceItemType())
                        .setServiceType(updateEntity.getServiceType())
                        .setInactive(false)
                        .setOnlyAvailable(false)
                        .build())
                .getCategoryListList()
                .stream()
                .flatMap(category -> category.getServicesList().stream())
                .map(CustomizedServiceView::getId)
                .collect(Collectors.toSet());

        // 新增服务ID = 所有服务ID - 原始ID
        Set<Long> newServiceIds = new HashSet<>(allServiceIds);
        newServiceIds.removeAll(originalIds);

        if (!newServiceIds.isEmpty()) {
            checkForDuplicates(newServiceIds, hasAllServiceCareType, existingServiceIds, updateEntity.getCompanyId());
        }
    }

    private void handleAllToAny(boolean isUpdateAll, boolean hasAllServiceCareType, Set<Long> existingServiceIds) {
        // 规则：只要原care type是全部服务，任何更新需严格检查
        if (hasAllServiceCareType || !existingServiceIds.isEmpty()) {
            throw bizException(
                    Code.CODE_PARAMS_ERROR, "Cannot edit care type when other 'all service' care types exist");
        }
    }

    // 公共校验方法
    private void checkForDuplicates(
            Set<Long> targetIds, boolean hasAllServiceCareType, Set<Long> existingServiceIds, Long companyId) {
        // 规则1: 存在其他"全部服务"的 care type
        if (hasAllServiceCareType) {
            throw bizException(
                    Code.CODE_PARAMS_ERROR, "Service conflict: Other 'all service' care types already exist");
        }

        // 规则2: 检查与现有服务ID的交集
        Set<Long> duplicateIds = new HashSet<>(targetIds);
        duplicateIds.retainAll(existingServiceIds);

        if (!duplicateIds.isEmpty()) {
            Set<String> duplicateNames = serviceManagementService
                    .getServiceList(GetServiceListRequest.newBuilder()
                            .setTokenCompanyId(companyId)
                            .addAllServiceIds(duplicateIds)
                            .build())
                    .getCategoryListList()
                    .stream()
                    .flatMap(category -> category.getServicesList().stream())
                    .map(ServiceModel::getName)
                    .collect(Collectors.toSet());

            throw bizException(Code.CODE_PARAMS_ERROR, "Service already exists in other care types: " + duplicateNames);
        }
    }

    private static void check(ObCustomizeCareType entity) {
        if (!isNormal(entity.getCompanyId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "companyId is required");
        }
        if (!isNormal(entity.getBusinessId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "businessId is required");
        }
        if (entity.getName() == null || entity.getName().trim().isEmpty()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "name is required");
        }
        if (entity.getServiceType() == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "serviceType is required");
        }
        if (entity.getServiceItemType() == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "serviceItemType is required");
        }
    }

    /**
     * 批量初始化booking care types - 生产环境优化版本
     *
     * @param companyIdBusinessIds 公司ID和业务ID的映射
     */
    public void initBookingCareTypesBatch(Map<Long, List<Long>> companyIdBusinessIds) {
        Long staffId = 0L;
        int batchSize = 50; // 每批处理50个business

        log.info("开始批量初始化booking care types，总计公司数: {}, 总计business数: {}",
                companyIdBusinessIds.size(),
                companyIdBusinessIds.values().stream().mapToInt(List::size).sum());

        companyIdBusinessIds.forEach((companyId, businessIds) -> {
            log.info("开始处理公司: {}, business数量: {}", companyId, businessIds.size());

            try {
                // 预先获取公司级别的care types，避免重复调用
                ListCareTypesResponse response = customizeCareTypeService.listCareTypes(
                    ListCareTypesRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setWhiteListFilter(buildWhiteListFilter(companyId))
                        .build());

                List<CustomizeCareTypeView> careTypesList = response.getCareTypesList()
                    .stream()
                    .filter(careType -> careType.getServiceItemType() != ServiceItemType.EVALUATION)
                    .toList();

                if (careTypesList.isEmpty()) {
                    log.warn("公司 {} 没有可用的care types，跳过初始化", companyId);
                    return;
                }

                // 分批处理business
                List<List<Long>> batches = partitionList(businessIds, batchSize);
                for (int i = 0; i < batches.size(); i++) {
                    List<Long> batch = batches.get(i);
                    log.info("处理公司 {} 的第 {}/{} 批，包含 {} 个business",
                            companyId, i + 1, batches.size(), batch.size());

                    processBatchWithRetry(companyId, batch, careTypesList, staffId, 3);

                    // 批次间暂停，避免对外部服务造成压力
                    if (i < batches.size() - 1) {
                        try {
                            Thread.sleep(1000); // 暂停1秒
                        } catch (Exception e) {
                            Thread.currentThread().interrupt();
                            throw bizException(Code.CODE_PARAMS_ERROR, "care type 初始化过程被中断", e);
                        }
                    }
                }

                log.info("公司 {} 初始化完成", companyId);

            } catch (Exception e) {
                log.error("公司 {} 初始化失败", companyId, e);
                throw bizException(Code.CODE_PARAMS_ERROR,
                    String.format("公司 %d 初始化失败: %s", companyId, e.getMessage()));
            }
        });

        log.info("所有公司booking care types初始化完成");
    }

    public void initBookingCareTypes(Map<Long, List<Long>> companyIdBusinessIds) {
        Long staffId = 0L;

        companyIdBusinessIds.forEach((companyId, businessIds) -> {
            // 调用 offering service 接口
            ListCareTypesResponse response = customizeCareTypeService.listCareTypes(ListCareTypesRequest.newBuilder()
                .setCompanyId(companyId)
                .setWhiteListFilter(buildWhiteListFilter(companyId))
                .build());
            List<CustomizeCareTypeView> careTypesList = response.getCareTypesList();

            if (careTypesList.isEmpty()) {
                throw bizException(Code.CODE_PARAMS_ERROR, "careTypesList cannot be null");
            }

            careTypesList = careTypesList.stream()
                .filter(careType -> careType.getServiceItemType() != ServiceItemType.EVALUATION)
                .toList();

            List<CustomizeCareTypeView> finalCareTypesList = careTypesList;
            businessIds.forEach(businessId -> {
                List<ObCustomizeCareType> careTypes = Tx.doInTransaction(() -> {
                    // 初始化 service
                    List<ObCustomizeCareType> entities = finalCareTypesList.stream()
                        .map(careType -> {
                            List<Long> serviceIds = Optional.ofNullable(
                                    // 调用 grooming service 接口
                                    groomingServiceService.getObAvailableServiceIds(toQueryServiceByTypeParams(
                                        companyId,
                                        businessId,
                                        careType.getServiceItemType(),
                                        ServiceType.SERVICE)))
                                .orElse(Collections.emptyList());
                            boolean isAllServiceApplicable = false;
                            return buildCareTypeEntity(
                                companyId,
                                businessId,
                                staffId,
                                careType,
                                serviceIds,
                                ServiceType.SERVICE,
                                isAllServiceApplicable);
                        })
                        .filter(entity -> entity.getServiceItemType() != ServiceItemType.DOG_WALKING)
                        .collect(Collectors.toList());
                    batchInsert(entities, staffId);

                    // 初始化 add-ons
                    ServiceItemType addonType = ServiceItemType.GROOMING;
                    List<Long> addonServiceIds = Optional.ofNullable(groomingServiceService.getObAvailableServiceIds(
                            toQueryServiceByTypeParams(companyId, businessId, addonType, ServiceType.ADDON)))
                        .orElse(Collections.emptyList());
                    boolean isAllServiceApplicable = false;

                    int maxSort = entities.stream()
                        .mapToInt(ObCustomizeCareType::getSort)
                        .max()
                        .orElse(0);

                    ObCustomizeCareType addOnsEntity = buildCareTypeEntity(
                        companyId,
                        businessId,
                        staffId,
                        CustomizeCareTypeView.newBuilder()
                            .setName(Dictionary.ADD_ONS)
                            .setServiceItemType(addonType)
                            .setSort(maxSort + 1)
                            .build(),
                        addonServiceIds,
                        ServiceType.ADDON,
                        isAllServiceApplicable);
                    addOnsEntity.setServiceType(ServiceType.ADDON);
                    insert(addOnsEntity);
                    entities.add(addOnsEntity);

                    return entities;
                });

                if (careTypes.isEmpty()) {
                    throw bizException(Code.CODE_PARAMS_ERROR, "Init booking care types failed, companyId: " + companyId);
                }
            });
        });
    }

    private String getDefaultIcon(ServiceItemType serviceItemType) {
        return switch (serviceItemType) {
            case GROOMING -> OnlineBookingConst.DEFAULT_GROOMING_ICON;
            case BOARDING -> OnlineBookingConst.DEFAULT_BOARDING_ICON;
            case DAYCARE -> OnlineBookingConst.DEFAULT_DAYCARE_ICON;
            case GROUP_CLASS -> OnlineBookingConst.DEFAULT_GROUP_CLASS_ICON;
            case DOG_WALKING -> OnlineBookingConst.DEFAULT_DOG_WALKING_ICON;
            default -> "";
        };
    }

    private String getDefaultImage(ServiceItemType serviceItemType) {
        return switch (serviceItemType) {
            case GROOMING -> OnlineBookingConst.DEFAULT_GROOMING_IMAGE;
            case BOARDING -> OnlineBookingConst.DEFAULT_BOARDING_IMAGE;
            case DAYCARE -> OnlineBookingConst.DEFAULT_DAYCARE_IMAGE;
            case GROUP_CLASS -> OnlineBookingConst.DEFAULT_GROUP_CLASS_IMAGE;
            case DOG_WALKING -> OnlineBookingConst.DEFAULT_DOG_WALKING_IMAGE;
            default -> "";
        };
    }

    private String getDefaultDescription(ServiceItemType serviceItemType) {
        return switch (serviceItemType) {
            case GROOMING -> OnlineBookingConst.DEFAULT_GROOMING_DESCRIPTION;
            case BOARDING -> OnlineBookingConst.DEFAULT_BOARDING_DESCRIPTION;
            case DAYCARE -> OnlineBookingConst.DEFAULT_DAYCARE_DESCRIPTION;
            case GROUP_CLASS -> OnlineBookingConst.DEFAULT_GROUP_CLASS_DESCRIPTION;
            default -> "";
        };
    }

    /**
     * 批量更新排序
     *
     * @param sortItems 排序项列表，包含ID和新的排序值
     * @return 更新的行数
     */
    public int batchUpdateSort(List<SortItem> sortItems, Long staffId) {
        if (sortItems == null || sortItems.isEmpty()) {
            return 0;
        }

        return Tx.doInTransaction(() -> {
            int totalUpdated = 0;
            LocalDateTime now = LocalDateTime.now();
            for (SortItem item : sortItems) {
                ObCustomizeCareType updateEntity = new ObCustomizeCareType();
                updateEntity.setSort(item.getSort());
                updateEntity.setUpdatedAt(now);
                updateEntity.setUpdatedBy(staffId);

                int updated = obCustomizeCareTypeMapper.update(
                        c -> ObCustomizeCareTypeMapper.updateSelectiveColumns(updateEntity, c)
                                .where(obCustomizeCareType.id, isEqualTo(item.getId()))
                                .and(obCustomizeCareType.deletedAt, isNull()));
                totalUpdated += updated;
            }
            return totalUpdated;
        });
    }

    @Data
    public static class SortItem {
        private Long id;
        private Integer sort;

        public SortItem() {}

        public SortItem(Long id, Integer sort) {
            this.id = id;
            this.sort = sort;
        }
    }

    private QueryServiceByTypeParams toQueryServiceByTypeParams(
            Long companyId, Long businessId, ServiceItemType serviceItemType, ServiceType serviceType) {
        QueryServiceByTypeParams params = new QueryServiceByTypeParams();
        params.setCompanyId(companyId);
        params.setBusinessId(businessId.intValue());
        params.setType(serviceType);
        params.setServiceItemType(serviceItemType);
        return params;
    }

    private ObCustomizeCareType buildCareTypeEntity(
            Long companyId,
            Long businessId,
            Long staffId,
            CustomizeCareTypeView view,
            List<Long> serviceIds,
            ServiceType serviceType,
            Boolean isAllServiceApplicable) {

        ObCustomizeCareType entity = new ObCustomizeCareType();
        entity.setName(view.getName());
        entity.setCompanyId(companyId);
        entity.setBusinessId(businessId);
        entity.setServiceItemType(view.getServiceItemType());
        entity.setServiceType(serviceType);
        entity.setIsAllServiceApplicable(isAllServiceApplicable);
        entity.setSelectedServices(serviceIds);
        entity.setDescription(
                serviceType == ServiceType.SERVICE ? getDefaultDescription(view.getServiceItemType()) : "");
        if (ServiceType.SERVICE.equals(serviceType)) {
            entity.setIcon(getDefaultIcon(view.getServiceItemType()));
            entity.setImage(JsonUtil.toJson(getDefaultImage(view.getServiceItemType())));
        }
        entity.setSort(view.getSort());
        entity.setUpdatedBy(staffId);
        return entity;
    }

    /**
     * 更新 selected services，移除指定的 service
     *
     * @param companyId 公司ID
     * @param serviceId 要移除的serviceID
     * @param serviceItemType 服务项目类型
     * @param removedLocationIds 要处理的businessID列表
     * @return 更新的记录数
     */
    public int removeServiceFromSelectedServices(
            long companyId, long serviceId, ServiceItemType serviceItemType, List<Long> removedLocationIds) {
        List<ObCustomizeCareType> careTypesToUpdate = obCustomizeCareTypeMapper.select(c -> {
            var condition = c.where(obCustomizeCareType.companyId, isEqualTo(companyId))
                    .and(obCustomizeCareType.serviceItemType, isEqualTo(serviceItemType))
                    .and(obCustomizeCareType.deletedAt, isNull());

            if (removedLocationIds != null && !removedLocationIds.isEmpty()) {
                condition = condition.and(obCustomizeCareType.businessId, isIn(removedLocationIds));
            }

            return condition;
        });

        LocalDateTime now = LocalDateTime.now();

        return Tx.doInTransaction(() -> {
            int updatedCount = 0;
            for (ObCustomizeCareType careType : careTypesToUpdate) {
                List<Long> selectedServices = careType.getSelectedServices();

                if (selectedServices == null || !selectedServices.contains(serviceId)) {
                    continue;
                }

                List<Long> updatedServices = selectedServices.stream()
                        .filter(id -> !id.equals(serviceId))
                        .collect(Collectors.toList());

                // 更新
                ObCustomizeCareType updateEntity = new ObCustomizeCareType();
                updateEntity.setSelectedServices(updatedServices);
                updateEntity.setUpdatedAt(now);

                int updated = obCustomizeCareTypeMapper.update(
                        c -> ObCustomizeCareTypeMapper.updateSelectiveColumns(updateEntity, c)
                                .where(obCustomizeCareType.id, isEqualTo(careType.getId()))
                                .and(obCustomizeCareType.deletedAt, isNull()));

                updatedCount += updated;
            }
            return updatedCount;
        });
    }

    /**
     * 对 care type 过滤 active services
     */
    public List<BookingCareTypeView> getActiveServiceOBCareTypeViews(
            List<BookingCareTypeView> views, Long companyId, Long businessId) {
        return views.stream()
                .map(bookingCareType -> {
                    if (bookingCareType
                            .getApplicableServices()
                            .getSelectedServicesList()
                            .isEmpty()) {
                        return bookingCareType;
                    }
                    List<Long> services = serviceBlockingStub
                            .getServiceList(GetServiceListRequest.newBuilder()
                                    .setTokenCompanyId(companyId)
                                    .addBusinessIds(businessId)
                                    .addAllServiceIds(bookingCareType
                                            .getApplicableServices()
                                            .getSelectedServicesList())
                                    .setInactive(false)
                                    .build())
                            .getCategoryListList()
                            .stream()
                            .map(ServiceCategoryModel::getServicesList)
                            .flatMap(Collection::stream)
                            .map(ServiceModel::getServiceId)
                            .toList();

                    return BookingCareTypeView.newBuilder(bookingCareType)
                            .setApplicableServices(bookingCareType.getApplicableServices().toBuilder()
                                    .clearSelectedServices()
                                    .addAllSelectedServices(services)
                                    .build())
                            .build();
                })
                .toList();
    }

    private WhiteListFilter buildWhiteListFilter(Long companyId) {
        boolean isAllowBoardingAndDaycare = isInBoardingWhiteList(companyId);
        boolean isAllowGroupClass = featureFlagApi.isOn(
            FeatureFlags.ALLOW_GROUP_CLASS,
            FeatureFlagContext.builder().company(companyId).build());
        boolean isAllowDogWalking = featureFlagApi.isOn(
            FeatureFlags.ALLOW_DOG_WALKING,
            FeatureFlagContext.builder().company(companyId).build());
        return WhiteListFilter.newBuilder()
            .setIsAllowBoardingAndDaycare(isAllowBoardingAndDaycare)
            .setIsAllowDogWalking(isAllowDogWalking)
            .setIsAllowGroupClass(isAllowGroupClass)
            .build();
    }

    private boolean isInBoardingWhiteList(Long companyId) {
        try {
            String boardingWhiteListKey = "allow_boarding_and_daycare";
            var key = metadataServiceBlockingStub
                .getKey(GetKeyRequest.newBuilder()
                    .setName(boardingWhiteListKey)
                    .build())
                .getKey();
            return metadataServiceBlockingStub
                .getValue(GetValueRequest.newBuilder()
                    .setKeyId(key.getId())
                    .setOwnerId(companyId)
                    .build())
                .getValue()
                .getValue()
                .equals("true");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 将列表分割成指定大小的批次
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            batches.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return batches;
    }

    /**
     * 带重试机制的批次处理
     */
    private void processBatchWithRetry(Long companyId, List<Long> businessIds,
                                     List<CustomizeCareTypeView> careTypesList,
                                     Long staffId, int maxRetries) {
        int attempt = 0;
        while (attempt < maxRetries) {
            try {
                processBatch(companyId, businessIds, careTypesList, staffId);
                return; // 成功则退出
            } catch (Exception e) {
                attempt++;
                log.warn("批次处理失败，第 {}/{} 次重试，公司: {}, business数量: {}",
                        attempt, maxRetries, companyId, businessIds.size(), e);

                if (attempt >= maxRetries) {
                    throw new RuntimeException(
                        String.format("批次处理失败，已重试 %d 次，公司: %d", maxRetries, companyId), e);
                }

                // 重试前等待
                try {
                    Thread.sleep(2000 * attempt); // 递增等待时间
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试等待被中断", ie);
                }
            }
        }
    }

    /**
     * 处理单个批次
     */
    @Transactional
    public void processBatch(Long companyId, List<Long> businessIds,
                            List<CustomizeCareTypeView> careTypesList, Long staffId) {
        List<ObCustomizeCareType> allEntities = new ArrayList<>();

        for (Long businessId : businessIds) {
            try {
                // 为每个business创建care types
                List<ObCustomizeCareType> businessEntities = createCareTypesForBusiness(
                    companyId, businessId, careTypesList, staffId);
                allEntities.addAll(businessEntities);

            } catch (Exception e) {
                log.error("为business {} 创建care types失败", businessId, e);
                throw e;
            }
        }

        // 批量插入所有实体
        if (!allEntities.isEmpty()) {
            batchInsert(allEntities, staffId);
            log.info("批量插入完成，公司: {}, 插入记录数: {}", companyId, allEntities.size());
        }
    }

    /**
     * 为单个business创建care types
     */
    private List<ObCustomizeCareType> createCareTypesForBusiness(Long companyId, Long businessId,
                                                               List<CustomizeCareTypeView> careTypesList,
                                                               Long staffId) {
        List<ObCustomizeCareType> entities = new ArrayList<>();

        // 创建SERVICE类型的care types
        for (CustomizeCareTypeView careType : careTypesList) {
            try {
                List<Long> serviceIds = getServiceIdsWithFallback(companyId, businessId,
                    careType.getServiceItemType(), ServiceType.SERVICE);

                ObCustomizeCareType entity = buildCareTypeEntity(
                    companyId, businessId, staffId, careType, serviceIds,
                    ServiceType.SERVICE, false);

                if (entity.getServiceItemType() != ServiceItemType.DOG_WALKING) {
                    entities.add(entity);
                }
            } catch (Exception e) {
                log.warn("为business {} 创建care type {} 失败，跳过", businessId, careType.getName(), e);
            }
        }

        // 创建ADDON类型的care type
        try {
            List<Long> addonServiceIds = getServiceIdsWithFallback(companyId, businessId,
                ServiceItemType.GROOMING, ServiceType.ADDON);

            int maxSort = entities.stream()
                .mapToInt(ObCustomizeCareType::getSort)
                .max()
                .orElse(0);

            ObCustomizeCareType addOnsEntity = buildCareTypeEntity(
                companyId, businessId, staffId,
                CustomizeCareTypeView.newBuilder()
                    .setName(Dictionary.ADD_ONS)
                    .setServiceItemType(ServiceItemType.GROOMING)
                    .setSort(maxSort + 1)
                    .build(),
                addonServiceIds, ServiceType.ADDON, false);

            entities.add(addOnsEntity);

        } catch (Exception e) {
            log.warn("为business {} 创建ADDON care type失败", businessId, e);
        }

        return entities;
    }

    /**
     * 获取服务ID列表，带降级处理
     */
    private List<Long> getServiceIdsWithFallback(Long companyId, Long businessId,
                                                ServiceItemType serviceItemType, ServiceType serviceType) {
        try {
            return Optional.ofNullable(
                groomingServiceService.getObAvailableServiceIds(
                    toQueryServiceByTypeParams(companyId, businessId, serviceItemType, serviceType)))
                .orElse(Collections.emptyList());
        } catch (Exception e) {
            log.warn("获取服务ID失败，使用空列表，companyId: {}, businessId: {}, serviceItemType: {}, serviceType: {}",
                    companyId, businessId, serviceItemType, serviceType, e);
            return Collections.emptyList();
        }
    }
}
