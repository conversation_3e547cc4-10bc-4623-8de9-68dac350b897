package com.moego.svc.online.booking.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.svc.online.booking.mapper.ObCustomizeCareTypeDynamicSqlSupport.obCustomizeCareType;
import static org.mybatis.dynamic.sql.SqlBuilder.and;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.common.enums.OnlineBookingConst;
import com.moego.common.constant.Dictionary;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CustomizeCareTypeView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.service.offering.v1.ListCareTypesRequest;
import com.moego.idl.service.offering.v1.ListCareTypesResponse;
import com.moego.svc.online.booking.entity.ObCustomizeCareType;
import com.moego.svc.online.booking.mapper.ObCustomizeCareTypeMapper;
import jakarta.annotation.Nullable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * BookingCareType业务服务类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BookingCareTypeService {

    private final ObCustomizeCareTypeMapper obCustomizeCareTypeMapper;


    /**
     * 根据ID获取存在的记录，不包括已删除的记录
     *
     * @param id ID
     * @return 存在的记录或null
     */
    @Nullable
    public ObCustomizeCareType get(long id) {
        return obCustomizeCareTypeMapper
                .selectByPrimaryKey(id)
                .filter(e -> e.getDeletedAt() == null)
                .orElse(null);
    }

    /**
     * 根据ID获取存在的记录，不包括已删除的记录，如果未找到则抛出异常
     *
     * @param id booking care type id
     * @return 存在的记录
     */
    public ObCustomizeCareType mustGet(long id) {
        return Optional.ofNullable(get(id))
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Booking care type not found: " + id));
    }

    /**
     * 插入记录
     *
     * @param entity 实体
     * @return 插入的ID
     */
    public long insert(ObCustomizeCareType entity) {
        check(entity);

        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        entity.setCreatedAt(now);
        entity.setUpdatedAt(now);

        // 设置排序值，如果没有提供的话
        if (entity.getSort() == null) {
            entity.setSort(getNextSortValue(entity.getCompanyId(), entity.getBusinessId()));
        }

        obCustomizeCareTypeMapper.insertSelective(entity);
        return entity.getId();
    }

    /**
     * 更新记录
     *
     * @param entity 实体
     * @return 更新的行数
     */
    @Transactional
    public int update(ObCustomizeCareType entity) {
        if (entity.getId() == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "ID is required for update");
        }

        // 设置更新时间
        entity.setUpdatedAt(LocalDateTime.now());

        return obCustomizeCareTypeMapper.update(c ->
            ObCustomizeCareTypeMapper.updateSelectiveColumns(entity, c)
                .where(obCustomizeCareType.id, isEqualTo(entity.getId()))
                .and(obCustomizeCareType.deletedAt, isNull())
        );
    }

    /**
     * 根据公司ID和业务ID列出所有booking care types
     *
     * @param companyId 公司ID
     * @param businessId 业务ID
     * @return booking care types列表
     */
    public List<ObCustomizeCareType> listByCompanyAndBusiness(long companyId, long businessId) {
        return obCustomizeCareTypeMapper.select(c ->
            c.where(obCustomizeCareType.companyId, isEqualTo(companyId))
                .and(obCustomizeCareType.businessId, isEqualTo(businessId))
                .and(obCustomizeCareType.deletedAt, isNull())
                .orderBy(obCustomizeCareType.sort, obCustomizeCareType.id)
        );
    }

    /**
     * 根据ID删除记录（软删除）
     *
     * @param id ID
     * @return 删除的行数
     */
    @Transactional
    public int delete(long id) {
        LocalDateTime now = LocalDateTime.now();
        ObCustomizeCareType updateEntity = new ObCustomizeCareType();
        updateEntity.setDeletedAt(now);
        updateEntity.setUpdatedAt(now);

        return obCustomizeCareTypeMapper.update(c ->
            ObCustomizeCareTypeMapper.updateSelectiveColumns(updateEntity, c)
                .where(obCustomizeCareType.id, isEqualTo(id))
                .and(obCustomizeCareType.deletedAt, isNull())
        );
    }

    /**
     * 获取下一个排序值
     */
    private int getNextSortValue(long companyId, long businessId) {
        List<ObCustomizeCareType> existing = obCustomizeCareTypeMapper.select(c ->
            c.where(obCustomizeCareType.companyId, isEqualTo(companyId))
                .and(obCustomizeCareType.businessId, isEqualTo(businessId))
                .and(obCustomizeCareType.deletedAt, isNull())
                .orderBy(obCustomizeCareType.sort.descending())
                .limit(1)
        );

        if (existing.isEmpty()) {
            return 1;
        }

        Integer maxSort = existing.get(0).getSort();
        return (maxSort != null ? maxSort : 0) + 1;
    }

    /**
     * 验证实体数据
     */
    private static void check(ObCustomizeCareType entity) {
        if (!isNormal(entity.getCompanyId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "companyId is required");
        }
        if (!isNormal(entity.getBusinessId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "businessId is required");
        }
        if (entity.getName() == null || entity.getName().trim().isEmpty()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "name is required");
        }
        if (entity.getServiceType() == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "serviceType is required");
        }
        if (entity.getServiceItemType() == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "serviceItemType is required");
        }
    }

    public List<ObCustomizeCareType> initBookingCareTypes(
        Long companyId, Long businessId, Long staffId, List<CustomizeCareTypeView> careTypesList) {
        List<ObCustomizeCareType> entities = new ArrayList<>();
        careTypesList.stream().map(careType -> {
            ObCustomizeCareType entity = new ObCustomizeCareType();
            entity.setName(careType.getName());
            entity.setCompanyId(companyId);
            entity.setBusinessId(businessId);
            entity.setServiceItemType(careType.getServiceItemType());
            entity.setServiceType(ServiceType.SERVICE);
            entity.setIsAllServiceApplicable(true);
            entity.setIcon(getDefaultIcon(careType.getServiceItemType()));
            entity.setImage(getDefaultImage(careType.getServiceItemType()));
            entity.setSort(careType.getSort());
            entity.setUpdatedBy(staffId);
            insert(entity);
            return entity;
        }).forEach(entities::add);
        // 初始化 add-ons
        ObCustomizeCareType addOnsEntity = new ObCustomizeCareType();
        addOnsEntity.setName(Dictionary.ADD_ONS);
        addOnsEntity.setCompanyId(companyId);
        addOnsEntity.setBusinessId(businessId);
        addOnsEntity.setServiceType(ServiceType.ADDON);
        addOnsEntity.setIsAllServiceApplicable(true);
        addOnsEntity.setSort();
        insert(addOnsEntity);
        entities.add(addOnsEntity);

        return entities;
    }

    private String getDefaultIcon(ServiceItemType serviceItemType) {
        return switch (serviceItemType) {
            case GROOMING -> OnlineBookingConst.DEFAULT_GROOMING_ICON;
            case BOARDING -> OnlineBookingConst.DEFAULT_BOARDING_ICON;
            case DAYCARE -> OnlineBookingConst.DEFAULT_DAYCARE_ICON;
            case GROUP_CLASS -> OnlineBookingConst.DEFAULT_GROUP_CLASS_ICON;
            case DOG_WALKING -> OnlineBookingConst.DEFAULT_DOG_WALKING_ICON;
            default -> throw bizException(Code.CODE_PARAMS_ERROR, "Unsupported service type: " + serviceItemType);
        };
    }

    private String getDefaultImage(ServiceItemType serviceItemType) {
        return switch (serviceItemType) {
            case GROOMING -> OnlineBookingConst.DEFAULT_GROOMING_IMAGE;
            case BOARDING -> OnlineBookingConst.DEFAULT_BOARDING_IMAGE;
            case DAYCARE -> OnlineBookingConst.DEFAULT_DAYCARE_IMAGE;
            case GROUP_CLASS -> OnlineBookingConst.DEFAULT_GROUP_CLASS_IMAGE;
            case DOG_WALKING -> OnlineBookingConst.DEFAULT_DOG_WALKING_IMAGE;
            default -> throw bizException(Code.CODE_PARAMS_ERROR, "Unsupported service type: " + serviceItemType);
        };
    }

}
