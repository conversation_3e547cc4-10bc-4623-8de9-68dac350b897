package com.moego.svc.online.booking.helper;

import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetPetInfoRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/3/10
 */
@Component
@RequiredArgsConstructor
public class PetHelper {

    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub petStub;

    /**
     * Must get pet info by id, throw exception if not found.
     *
     * @param petId pet id
     * @return pet info
     */
    public BusinessCustomerPetInfoModel mustGetPet(long petId) {

        var resp =
                petStub.getPetInfo(GetPetInfoRequest.newBuilder().setId(petId).build());

        if (!resp.hasPet()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Pet not found: " + petId);
        }

        return resp.getPet();
    }
}
