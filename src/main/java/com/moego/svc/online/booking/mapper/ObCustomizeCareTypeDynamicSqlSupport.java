package com.moego.svc.online.booking.mapper;

import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;
import java.util.List;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class ObCustomizeCareTypeDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    public static final ObCustomizeCareType obCustomizeCareType = new ObCustomizeCareType();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.id")
    public static final SqlColumn<Long> id = obCustomizeCareType.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.name")
    public static final SqlColumn<String> name = obCustomizeCareType.name;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.company_id")
    public static final SqlColumn<Long> companyId = obCustomizeCareType.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.business_id")
    public static final SqlColumn<Long> businessId = obCustomizeCareType.businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.description")
    public static final SqlColumn<String> description = obCustomizeCareType.description;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.icon")
    public static final SqlColumn<String> icon = obCustomizeCareType.icon;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.image")
    public static final SqlColumn<String> image = obCustomizeCareType.image;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.sort")
    public static final SqlColumn<Integer> sort = obCustomizeCareType.sort;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.service_type")
    public static final SqlColumn<ServiceType> serviceType = obCustomizeCareType.serviceType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.is_all_service_applicable")
    public static final SqlColumn<Boolean> isAllServiceApplicable = obCustomizeCareType.isAllServiceApplicable;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.selected_services")
    public static final SqlColumn<List<Long>> selectedServices = obCustomizeCareType.selectedServices;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.service_item_type")
    public static final SqlColumn<ServiceItemType> serviceItemType = obCustomizeCareType.serviceItemType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.updated_by")
    public static final SqlColumn<Long> updatedBy = obCustomizeCareType.updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = obCustomizeCareType.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = obCustomizeCareType.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.deleted_at")
    public static final SqlColumn<LocalDateTime> deletedAt = obCustomizeCareType.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    public static final class ObCustomizeCareType extends AliasableSqlTable<ObCustomizeCareType> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> name = column("\"name\"", JDBCType.VARCHAR);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<String> description = column("description", JDBCType.VARCHAR);

        public final SqlColumn<String> icon = column("icon", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler");

        public final SqlColumn<String> image = column("image", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler");

        public final SqlColumn<Integer> sort = column("sort", JDBCType.INTEGER);

        public final SqlColumn<ServiceType> serviceType = column("service_type", JDBCType.SMALLINT, "com.moego.svc.online.booking.typehandler.ServiceTypeHandler");

        public final SqlColumn<Boolean> isAllServiceApplicable = column("is_all_service_applicable", JDBCType.BIT);

        public final SqlColumn<List<Long>> selectedServices = column("selected_services", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler");

        public final SqlColumn<ServiceItemType> serviceItemType = column("service_item_type", JDBCType.SMALLINT, "com.moego.svc.online.booking.typehandler.ServiceItemTypeHandler");

        public final SqlColumn<Long> updatedBy = column("updated_by", JDBCType.BIGINT);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public ObCustomizeCareType() {
            super("ob_customize_care_type", ObCustomizeCareType::new);
        }
    }
}