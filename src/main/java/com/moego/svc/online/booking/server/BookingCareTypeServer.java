package com.moego.svc.online.booking.server;

import com.moego.idl.models.offering.v1.CustomizeCareTypeView;
import com.moego.idl.models.online_booking.v1.BookingCareType;
import com.moego.idl.models.online_booking.v1.BookingCareTypeView;
import com.moego.idl.service.offering.v1.CustomizeCareTypeServiceGrpc;
import com.moego.idl.service.offering.v1.ListCareTypesRequest;
import com.moego.idl.service.offering.v1.ListCareTypesResponse;
import com.moego.idl.service.online_booking.v1.BookingCareTypeServiceGrpc;
import com.moego.idl.service.online_booking.v1.CreateBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.CreateBookingCareTypeResponse;
import com.moego.idl.service.online_booking.v1.DeleteBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.DeleteBookingCareTypeResponse;
import com.moego.idl.service.online_booking.v1.GetBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.GetBookingCareTypeResponse;
import com.moego.idl.service.online_booking.v1.ListBookingCareTypesRequest;
import com.moego.idl.service.online_booking.v1.ListBookingCareTypesResponse;
import com.moego.idl.service.online_booking.v1.SortBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.SortBookingCareTypeResponse;
import com.moego.idl.service.online_booking.v1.UpdateBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingCareTypeResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.online.booking.entity.ObCustomizeCareType;
import com.moego.svc.online.booking.mapstruct.BookingCareTypeConverter;
import com.moego.svc.online.booking.service.BookingCareTypeService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class BookingCareTypeServer extends BookingCareTypeServiceGrpc.BookingCareTypeServiceImplBase {

    private final BookingCareTypeService bookingCareTypeService;

    private final CustomizeCareTypeServiceGrpc.CustomizeCareTypeServiceBlockingStub customizeCareTypeService;

    @Override
    public void createBookingCareType(CreateBookingCareTypeRequest request, StreamObserver<CreateBookingCareTypeResponse> responseObserver) {
            // 从请求中获取BookingCareType
            BookingCareType bookingCareType = request.getBookingCareType();

            // 转换为实体对象
            ObCustomizeCareType entity = BookingCareTypeConverter.INSTANCE.modelToEntity(bookingCareType);

            // 设置ApplicableServices相关字段
            entity.setIsAllServiceApplicable(bookingCareType.getIsAllServiceApplicable());
            entity.setSelectedServices(bookingCareType.getSelectedServicesList());

            // 插入数据库
            long id = bookingCareTypeService.insert(entity);

            // 查询插入后的完整数据
            ObCustomizeCareType createdEntity = bookingCareTypeService.mustGet(id);

            // 转换为响应对象
            BookingCareType createdModel = BookingCareTypeConverter.INSTANCE.entityToModel(createdEntity);

            CreateBookingCareTypeResponse response = CreateBookingCareTypeResponse.newBuilder()
                    .setBookingCareType(createdModel)
                    .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();
    }

    @Override
    public void updateBookingCareType(UpdateBookingCareTypeRequest request, StreamObserver<UpdateBookingCareTypeResponse> responseObserver) {
        try {
            log.info("Updating booking care type: {}", request);

            // 从请求中获取BookingCareType
            BookingCareType bookingCareType = request.getBookingCareType();

            // 转换为实体对象
            ObCustomizeCareType entity = BookingCareTypeConverter.INSTANCE.modelToEntity(bookingCareType);

            // 设置ApplicableServices相关字段
            entity.setIsAllServiceApplicable(bookingCareType.getIsAllServiceApplicable());
            entity.setSelectedServices(bookingCareType.getSelectedServicesList());

            // 更新数据库
            bookingCareTypeService.update(entity);

            // 查询更新后的完整数据
            ObCustomizeCareType updatedEntity = bookingCareTypeService.mustGet(entity.getId());

            // 转换为响应对象
            BookingCareType updatedModel = BookingCareTypeConverter.INSTANCE.entityToModel(updatedEntity);

            UpdateBookingCareTypeResponse response = UpdateBookingCareTypeResponse.newBuilder()
                    .setBookingCareType(updatedModel)
                    .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("Error updating booking care type", e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void listBookingCareTypes(ListBookingCareTypesRequest request, StreamObserver<ListBookingCareTypesResponse> responseObserver) {
        try {
            log.info("Listing booking care types: {}", request);

            // 从数据库查询数据
            List<ObCustomizeCareType> entities = bookingCareTypeService.listByCompanyAndBusiness(
                    request.getCompanyId(),
                    request.getBusinessId()
            );
            if (entities.isEmpty()) {
                // 初始化
                ListCareTypesResponse careTypesResponse = customizeCareTypeService.listCareTypes(ListCareTypesRequest.newBuilder()
                    .setCompanyId(request.getCompanyId())
                    .setStaffId(request.getStaffId())
                    .build());
                List<CustomizeCareTypeView> careTypesList = careTypesResponse.getCareTypesList();
                entities = bookingCareTypeService.initBookingCareTypes(
                    request.getCompanyId(), request.getBusinessId(), request.getStaffId(), careTypesList);
            }

            // 转换为视图对象列表
            List<BookingCareTypeView> views = BookingCareTypeConverter.INSTANCE.entityListToViewList(entities);

            ListBookingCareTypesResponse response = ListBookingCareTypesResponse.newBuilder()
                    .addAllBookingCareTypes(views)
                    .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("Error listing booking care types", e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void getBookingCareType(GetBookingCareTypeRequest request, StreamObserver<GetBookingCareTypeResponse> responseObserver) {
        super.getBookingCareType(request, responseObserver);
    }

    @Override
    public void deleteBookingCareType(DeleteBookingCareTypeRequest request, StreamObserver<DeleteBookingCareTypeResponse> responseObserver) {
        super.deleteBookingCareType(request, responseObserver);
    }

    @Override
    public void sortBookingCareType(SortBookingCareTypeRequest request, StreamObserver<SortBookingCareTypeResponse> responseObserver) {
        super.sortBookingCareType(request, responseObserver);
    }
}
