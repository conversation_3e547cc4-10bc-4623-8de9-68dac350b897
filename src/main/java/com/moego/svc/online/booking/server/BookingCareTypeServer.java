package com.moego.svc.online.booking.server;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceCategoryModel;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.online_booking.v1.BookingCareType;
import com.moego.idl.models.online_booking.v1.BookingCareTypeView;
import com.moego.idl.service.metadata.v1.GetKeyRequest;
import com.moego.idl.service.metadata.v1.GetValueRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.idl.service.offering.v1.CustomizeCareTypeServiceGrpc;
import com.moego.idl.service.offering.v1.GetServiceListRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.offering.v1.WhiteListFilter;
import com.moego.idl.service.online_booking.v1.BookingCareTypeServiceGrpc;
import com.moego.idl.service.online_booking.v1.CreateBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.CreateBookingCareTypeResponse;
import com.moego.idl.service.online_booking.v1.DeleteBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.DeleteBookingCareTypeResponse;
import com.moego.idl.service.online_booking.v1.GetBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.GetBookingCareTypeResponse;
import com.moego.idl.service.online_booking.v1.ListBookingCareTypesRequest;
import com.moego.idl.service.online_booking.v1.ListBookingCareTypesResponse;
import com.moego.idl.service.online_booking.v1.SortBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.SortBookingCareTypeResponse;
import com.moego.idl.service.online_booking.v1.UpdateBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingCareTypeResponse;
import com.moego.idl.service.online_booking.v1.UpdateSelectedServicesRequest;
import com.moego.idl.service.online_booking.v1.UpdateSelectedServicesResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.svc.online.booking.entity.ObCustomizeCareType;
import com.moego.svc.online.booking.mapstruct.BookingCareTypeConverter;
import com.moego.svc.online.booking.service.BookingCareTypeService;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class BookingCareTypeServer extends BookingCareTypeServiceGrpc.BookingCareTypeServiceImplBase {

    private final BookingCareTypeService bookingCareTypeService;

    private final CustomizeCareTypeServiceGrpc.CustomizeCareTypeServiceBlockingStub customizeCareTypeService;

    private final MetadataServiceGrpc.MetadataServiceBlockingStub metadataServiceBlockingStub;

    private final FeatureFlagApi featureFlagApi;

    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceBlockingStub;

    @Override
    public void createBookingCareType(
            CreateBookingCareTypeRequest request, StreamObserver<CreateBookingCareTypeResponse> responseObserver) {
        BookingCareType bookingCareType = request.getBookingCareType();

        validateCreateBookingCareType(bookingCareType);

        ObCustomizeCareType entity = BookingCareTypeConverter.INSTANCE.modelToEntity(bookingCareType);
        entity.setIsAllServiceApplicable(bookingCareType.getIsAllServiceApplicable());
        entity.setSelectedServices(bookingCareType.getSelectedServicesList());

        long id = bookingCareTypeService.insert(entity);
        ObCustomizeCareType createdEntity = bookingCareTypeService.mustGet(id);

        CreateBookingCareTypeResponse response = CreateBookingCareTypeResponse.newBuilder()
                .setBookingCareType(BookingCareTypeConverter.INSTANCE.entityToModel(createdEntity))
                .build();

        // 更新 BookOnlineAvailable
        bookingCareTypeService.updateServiceBookOnlineAvailable(createdEntity, Set.of());
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void updateBookingCareType(
            UpdateBookingCareTypeRequest request, StreamObserver<UpdateBookingCareTypeResponse> responseObserver) {
        BookingCareType bookingCareType = request.getBookingCareType();

        validateUpdateBookingCareType(bookingCareType);

        ObCustomizeCareType entity = BookingCareTypeConverter.INSTANCE.modelToEntity(bookingCareType);
        entity.setIsAllServiceApplicable(bookingCareType.getIsAllServiceApplicable());
        entity.setSelectedServices(bookingCareType.getSelectedServicesList());

        bookingCareTypeService.update(entity);
        ObCustomizeCareType updatedEntity = bookingCareTypeService.mustGet(entity.getId());

        BookingCareType updatedModel = BookingCareTypeConverter.INSTANCE.entityToModel(updatedEntity);
        UpdateBookingCareTypeResponse response = UpdateBookingCareTypeResponse.newBuilder()
                .setBookingCareType(updatedModel)
                .build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void listBookingCareTypes(
            ListBookingCareTypesRequest request, StreamObserver<ListBookingCareTypesResponse> responseObserver) {
        List<ObCustomizeCareType> entities =
                bookingCareTypeService.listByCompanyAndBusiness(request.getCompanyId(), request.getBusinessId());

        List<BookingCareTypeView> views = BookingCareTypeConverter.INSTANCE.entityListToViewList(entities);

        // 过滤 inactive services
        List<BookingCareTypeView> updatedViews = bookingCareTypeService.getActiveServiceOBCareTypeViews(
            views,
            request.getCompanyId(),
            request.getBusinessId());

        ListBookingCareTypesResponse response = ListBookingCareTypesResponse.newBuilder()
                .addAllBookingCareTypes(updatedViews)
                .build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void getBookingCareType(
            GetBookingCareTypeRequest request, StreamObserver<GetBookingCareTypeResponse> responseObserver) {
        ObCustomizeCareType entity = bookingCareTypeService.mustGet(request.getId());

        BookingCareType model = BookingCareTypeConverter.INSTANCE.entityToModel(entity);

        GetBookingCareTypeResponse response = GetBookingCareTypeResponse.newBuilder()
                .setBookingCareType(model)
                .build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void deleteBookingCareType(
            DeleteBookingCareTypeRequest request, StreamObserver<DeleteBookingCareTypeResponse> responseObserver) {
        bookingCareTypeService.delete(request.getId());

        DeleteBookingCareTypeResponse response =
                DeleteBookingCareTypeResponse.newBuilder().build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void sortBookingCareType(
            SortBookingCareTypeRequest request, StreamObserver<SortBookingCareTypeResponse> responseObserver) {
        List<BookingCareTypeService.SortItem> sortItems = new ArrayList<>();
        List<Long> bookingCareTypeIds = request.getBookingCareTypeIdsList();

        for (int i = 0; i < bookingCareTypeIds.size(); i++) {
            Long id = bookingCareTypeIds.get(i);
            Integer sort = i + 1;
            sortItems.add(new BookingCareTypeService.SortItem(id, sort));
        }

        // 批量更新排序
        bookingCareTypeService.batchUpdateSort(sortItems, request.getStaffId());

        List<ObCustomizeCareType> entities =
                bookingCareTypeService.listByCompanyAndBusiness(request.getCompanyId(), request.getBusinessId());

        List<BookingCareTypeView> views = BookingCareTypeConverter.INSTANCE.entityListToViewList(entities);

        SortBookingCareTypeResponse response = SortBookingCareTypeResponse.newBuilder()
                .addAllBookingCareTypes(views)
                .build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void updateSelectedServices(
            UpdateSelectedServicesRequest request, StreamObserver<UpdateSelectedServicesResponse> responseObserver) {
        log.info("Updating selected services: {}", request);

        // 参数校验
        if (request.getCompanyId() <= 0) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Valid companyId is required");
        }
        if (request.getServiceId() <= 0) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Valid serviceId is required");
        }

        // 调用服务层方法删除指定的service_id
        int updatedCount = bookingCareTypeService.removeServiceFromSelectedServices(
                request.getCompanyId(),
                request.getServiceId(),
                request.getServiceItemType(),
                request.getRemovedLocationIdsList());

        log.info("Updated {} care types by removing service_id: {}", updatedCount, request.getServiceId());

        responseObserver.onNext(UpdateSelectedServicesResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private void validateCreateBookingCareType(BookingCareType bookingCareType) {
        if (bookingCareType == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "BookingCareType is required");
        }

        // check care type
        if (bookingCareType.getServiceType() != ServiceType.SERVICE) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Only SERVICE type booking care types can be created");
        }

        // check name
        if (bookingCareType.getName().trim().isEmpty()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Name is required");
        }

        // name重复校验：大小写敏感、去除前后空格进行比较
        boolean isDuplicate = bookingCareTypeService.isNameDuplicate(
                bookingCareType.getCompanyId(), bookingCareType.getBusinessId(), bookingCareType.getName(), null);
        if (isDuplicate) {
            throw bizException(
                    Code.CODE_PARAMS_ERROR,
                    "Name already exists: " + bookingCareType.getName().trim());
        }

        // check selected services
        if (bookingCareType.getSelectedServicesList().isEmpty() && !bookingCareType.getIsAllServiceApplicable()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Selected services cannot be empty");
        }

        // 检查是否有重复service
        if (bookingCareType.getServiceType().equals(ServiceType.SERVICE)) {
            ObCustomizeCareType obCustomizeCareType = BookingCareTypeConverter.INSTANCE.modelToEntity(bookingCareType);
            Set<Long> selectedServices = new HashSet<>(bookingCareType.getSelectedServicesList());
            bookingCareTypeService.checkServiceDuplicate(null, obCustomizeCareType, Set.of(), selectedServices);
        }
    }

    private void validateUpdateBookingCareType(BookingCareType bookingCareType) {
        if (bookingCareType.getSelectedServicesList().isEmpty()
                && !bookingCareType.getIsAllServiceApplicable()
                && bookingCareType.getServiceType() != ServiceType.ADDON) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Selected services cannot be empty");
        }

        boolean isDuplicate = bookingCareTypeService.isNameDuplicate(
                bookingCareType.getCompanyId(),
                bookingCareType.getBusinessId(),
                bookingCareType.getName(),
                bookingCareType.getId());
        if (isDuplicate) {
            throw bizException(
                    Code.CODE_PARAMS_ERROR,
                    "Name already exists: " + bookingCareType.getName().trim());
        }
    }

    private WhiteListFilter buildWhiteListFilter(Long companyId) {
        boolean isAllowBoardingAndDaycare = isInBoardingWhiteList(companyId);
        boolean isAllowGroupClass = featureFlagApi.isOn(
                FeatureFlags.ALLOW_GROUP_CLASS,
                FeatureFlagContext.builder().company(companyId).build());
        boolean isAllowDogWalking = featureFlagApi.isOn(
                FeatureFlags.ALLOW_DOG_WALKING,
                FeatureFlagContext.builder().company(companyId).build());
        return WhiteListFilter.newBuilder()
                .setIsAllowBoardingAndDaycare(isAllowBoardingAndDaycare)
                .setIsAllowDogWalking(isAllowDogWalking)
                .setIsAllowGroupClass(isAllowGroupClass)
                .build();
    }

    private boolean isInBoardingWhiteList(Long companyId) {
        try {
            String boardingWhiteListKey = "allow_boarding_and_daycare";
            var key = metadataServiceBlockingStub
                    .getKey(GetKeyRequest.newBuilder()
                            .setName(boardingWhiteListKey)
                            .build())
                    .getKey();
            return metadataServiceBlockingStub
                    .getValue(GetValueRequest.newBuilder()
                            .setKeyId(key.getId())
                            .setOwnerId(companyId)
                            .build())
                    .getValue()
                    .getValue()
                    .equals("true");
        } catch (Exception e) {
            return false;
        }
    }
}
