package com.moego.svc.online.booking.server;

import com.moego.idl.service.online_booking.v1.BookingCareTypeServiceGrpc;
import com.moego.idl.service.online_booking.v1.CreateBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.CreateBookingCareTypeResponse;
import com.moego.idl.service.online_booking.v1.DeleteBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.DeleteBookingCareTypeResponse;
import com.moego.idl.service.online_booking.v1.GetBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.GetBookingCareTypeResponse;
import com.moego.idl.service.online_booking.v1.ListBookingCareTypesRequest;
import com.moego.idl.service.online_booking.v1.ListBookingCareTypesResponse;
import com.moego.idl.service.online_booking.v1.SortBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.SortBookingCareTypeResponse;
import com.moego.idl.service.online_booking.v1.UpdateBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingCareTypeResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class BookingCareTypeServer extends BookingCareTypeServiceGrpc.BookingCareTypeServiceImplBase {

    @Override
    public void createBookingCareType(CreateBookingCareTypeRequest request, StreamObserver<CreateBookingCareTypeResponse> responseObserver) {


    }

    @Override
    public void updateBookingCareType(UpdateBookingCareTypeRequest request, StreamObserver<UpdateBookingCareTypeResponse> responseObserver) {
        super.updateBookingCareType(request, responseObserver);
    }

    @Override
    public void listBookingCareTypes(ListBookingCareTypesRequest request, StreamObserver<ListBookingCareTypesResponse> responseObserver) {
        super.listBookingCareTypes(request, responseObserver);
    }

    @Override
    public void getBookingCareType(GetBookingCareTypeRequest request, StreamObserver<GetBookingCareTypeResponse> responseObserver) {
        super.getBookingCareType(request, responseObserver);
    }

    @Override
    public void deleteBookingCareType(DeleteBookingCareTypeRequest request, StreamObserver<DeleteBookingCareTypeResponse> responseObserver) {
        super.deleteBookingCareType(request, responseObserver);
    }

    @Override
    public void sortBookingCareType(SortBookingCareTypeRequest request, StreamObserver<SortBookingCareTypeResponse> responseObserver) {
        super.sortBookingCareType(request, responseObserver);
    }
}
