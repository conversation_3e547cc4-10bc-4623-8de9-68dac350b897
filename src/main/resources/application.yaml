spring:
  profiles:
    active: local
  application:
    name: moego-svc-online-booking
  cloud:
    aws:
      region:
        static: ${AWS_REGION:us-west-2}
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${secret.datasource.postgres.url}:${secret.datasource.postgres.port}/moego_online_booking
    username: ${secret.datasource.postgres.moego_online_booking.username}
    password: ${secret.datasource.postgres.moego_online_booking.password}
  activemq:
    broker-url: ${secret.mq.activemq.url}
    password: ${secret.mq.activemq.password}
    user: ${secret.mq.activemq.user}
    enabled: true
    destinationPrefix: refund
  data:
    redis:
      host: ${secret.redis.host}
      port: ${secret.redis.port}
      ssl:
        enabled: ${secret.redis.tls}
      timeout: 60000
      password: ${secret.redis.password}
      key:
        delimiter: ':'
        prefix: apiv2

mybatis:
  configuration:
    cache-enabled: false

moego:
  feature-flag:
    growth-book:
      api-host: ${secret.growthbook.host}
      client-key: ${secret.growthbook.client_key}
  event-bus:
    brokers:
      - name: default
        addresses:
          - ${secret.mq.kafka.broker_url_0}
          - ${secret.mq.kafka.broker_url_1}
          - ${secret.mq.kafka.broker_url_2}
        security:
          enabled: true
          properties:
            security.protocol: SASL_SSL
            sasl.mechanism: AWS_MSK_IAM
            sasl.jaas.config: software.amazon.msk.auth.iam.IAMLoginModule required;
            sasl.client.callback.handler.class: software.amazon.msk.auth.iam.IAMClientCallbackHandler
    producer:
      enabled: true
      log-success: false
      log-failure: true
    consumer:
      enabled: true
  server:
    url:
      grooming: moego-service-grooming:9206
      customer: moego-service-customer:9201
      business: moego-service-business:9203
      payment: moego-service-payment:9204
      message: moego-service-message:9205
  grpc:
    server:
      empty-server-enabled: true
      debug-enabled: true
      port: 9090
    client:
      stubs:
        - service: moego.service.order.**
          authority: moego-svc-order:9090
        - service: moego.service.offering.**
          authority: moego-svc-offering:9090
        - service: moego.service.appointment.**
          authority: moego-svc-appointment:9090
        - service: moego.service.fulfillment.**
          authority: moego-svc-fulfillment:9090
        - service: moego.service.business_customer.v1.**
          authority: moego-svc-business-customer:9090
        - service: moego.service.organization.v1.**
          authority: moego-svc-organization:9090
        - service: moego.service.metadata.**
          authority: moego-svc-metadata:9090
        - service: moego.service.activity_log.**
          authority: moego-svc-activity-log:9090
        - service: moego.service.permission.**
          authority: moego-svc-permission:9090
        - service: moego.service.membership.**
          authority: moego-svc-membership:9090
pagehelper:
  reasonable: false
  defaultCount: true
