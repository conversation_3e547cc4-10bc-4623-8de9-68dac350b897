package com.moego.lib.utils;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.Test;

public class PathUtilsTest {

    @Test
    public void testFileExtensions() {
        var dir = "src/test/resources/extensions";
        assertEquals("", PathUtils.extensions((String) null));
        assertEquals("", PathUtils.extensions(""));
        assertEquals("", PathUtils.extensions(dir));
        assertEquals("txt", PathUtils.extensions(dir + "notExisting.txt"));
        assertEquals("", PathUtils.extensions(dir + "extension"));
        assertEquals("c", PathUtils.extensions(dir + "extension.c"));
        assertEquals("cc", PathUtils.extensions(dir + "extension.cc"));
        assertEquals("cpp", PathUtils.extensions(dir + "extension.cpp"));
        assertEquals("java", PathUtils.extensions(dir + "extension.java"));
        assertEquals("java", PathUtils.extensions("extension.java"));
        assertEquals("cpp", PathUtils.extensions("a/b/c.cpp"));
        assertEquals("", PathUtils.extensions("a/b/c.cpp/"));
        assertEquals("", PathUtils.extensions("a/b/c.cpp/extension"));
        assertEquals("", PathUtils.extensions("a/b/c.cpp/extension."));
        assertEquals("java", PathUtils.extensions("a/b/c.cpp/extension.java"));

        assertEquals("", PathUtils.extensions(Path.of(dir)));
        assertEquals("", PathUtils.extensions(Path.of(dir, "notExisting.txt")));
        assertEquals("", PathUtils.extensions(Path.of(dir, "extension")));
        assertEquals("c", PathUtils.extensions(Path.of(dir, "extension.c")));
        assertEquals("cc", PathUtils.extensions(Path.of(dir, "extension.cc")));
        assertEquals("cpp", PathUtils.extensions(Path.of(dir, "extension.cpp")));

        System.out.println("--> test extract file extensions OK !");
    }

    @Test
    public void testReadWriteDelete() throws IOException {
        File dir = new File("./src/test/resources/tmp/a/b/c");
        assertTrue(dir.mkdirs());
        assertTrue(dir.exists() || dir.mkdirs());

        PathUtils.writeFile(Path.of("./src/test/resources/tmp/t.txt"), "abc".getBytes());
        PathUtils.writeFile("./src/test/resources/tmp/a/x.txt", "efg".getBytes());
        PathUtils.writeFile(Path.of("./src/test/resources/tmp/a/b/y.txt"), "opq");
        PathUtils.writeFile("./src/test/resources/tmp/a/b/c/z.txt", "xyz");

        assertEquals("abc", PathUtils.readText("./src/test/resources/tmp/t.txt"));
        assertEquals("efg", PathUtils.readText("./src/test/resources/tmp/a/x.txt"));
        assertArrayEquals("opq".getBytes(), PathUtils.readBytes("./src/test/resources/tmp/a/b/y.txt"));
        assertArrayEquals("xyz".getBytes(), PathUtils.readBytes("./src/test/resources/tmp/a/b/c/z.txt"));
        assertEquals(1, PathUtils.deletePath("./src/test/resources/tmp/t.txt"));
        assertEquals(7, PathUtils.deletePath("./src/test/resources/tmp"));
        assertEquals(0, PathUtils.deletePath("./src/test/resources/tmp"));
        assertEquals(0, PathUtils.deletePath((String) null));

        System.out.println("--> test delete path OK !");
    }

    @Test
    public void testWalkFiles() throws IOException {
        String dir = "src/test/resources/tmp";
        PathUtils.writeFile("src/test/resources/tmp/a/b/06.log", "this is 04");
        PathUtils.writeFile("src/test/resources/tmp/a/b/05.txt", "this is 03");
        PathUtils.writeFile("src/test/resources/tmp/a/04.log", "this is 04");
        PathUtils.writeFile("src/test/resources/tmp/a/03.txt", "this is 03");
        PathUtils.writeFile("src/test/resources/tmp/02.log", "this is 02");
        PathUtils.writeFile("src/test/resources/tmp/01.txt", "this is 01");

        Set<String> paths = new HashSet<>();
        PathUtils.walkFiles(dir, p -> paths.add(p.getFileName().toString()));
        assertEquals(Set.of("01.txt", "02.log", "03.txt", "04.log", "05.txt", "06.log"), paths);

        paths.clear();
        PathUtils.walkFiles(dir, 3, p -> paths.add(p.getFileName().toString()));
        assertEquals(Set.of("01.txt", "02.log", "03.txt", "04.log", "05.txt", "06.log"), paths);

        paths.clear();
        PathUtils.walkFiles(dir, 2, p -> paths.add(p.getFileName().toString()));
        assertEquals(Set.of("01.txt", "02.log", "03.txt", "04.log"), paths);

        paths.clear();
        PathUtils.walkFiles(dir, 1, p -> paths.add(p.getFileName().toString()));
        assertEquals(Set.of("01.txt", "02.log"), paths);

        paths.clear();
        PathUtils.walkFiles(dir, "a/*", true, p -> paths.add(p.getFileName().toString()));
        assertEquals(Set.of("03.txt", "04.log"), paths);

        paths.clear();
        PathUtils.walkFiles(dir, "b/*", true, p -> paths.add(p.getFileName().toString()));
        assertTrue(paths.isEmpty());

        paths.clear();
        PathUtils.walkFiles(dir, "a/b/*", true, p -> paths.add(p.getFileName().toString()));
        assertEquals(Set.of("05.txt", "06.log"), paths);

        paths.clear();
        PathUtils.walkFiles(dir, "*/b/*", true, p -> paths.add(p.getFileName().toString()));
        assertEquals(Set.of("05.txt", "06.log"), paths);

        paths.clear();
        PathUtils.walkFiles(dir, "**.txt", true, p -> paths.add(p.getFileName().toString()));
        assertEquals(Set.of("01.txt", "03.txt", "05.txt"), paths);

        paths.clear();
        PathUtils.walkFiles(dir, "**.log", true, p -> paths.add(p.getFileName().toString()));
        assertEquals(Set.of("02.log", "04.log", "06.log"), paths);

        paths.clear();
        PathUtils.walkFiles(dir, "**.xml", true, p -> paths.add(p.getFileName().toString()));
        assertTrue(paths.isEmpty());

        assertEquals(9, PathUtils.deletePath("src/test/resources/tmp"));

        System.out.println("--> test walk files OK !");
    }

    @Test
    public void testWalkLines() throws IOException {
        String dir = "src/test/resources/tmp";
        String path = dir + "/lines.txt";
        PathUtils.writeFile(path, "100,200\n300,400\n333,444\n");

        List<Integer> result = new ArrayList<>();
        PathUtils.walkLines(path, (i, line) -> {
            if (!StringUtils.isBlank(line)) {
                var ss = line.split(",");
                result.add(Integer.parseInt(ss[0]) + Integer.parseInt(ss[1]));
            }
        });
        assertEquals(List.of(300, 700, 777), result);

        assertEquals(2, PathUtils.deletePath(dir));

        System.out.println("--> test walk lines OK !");
    }
}
