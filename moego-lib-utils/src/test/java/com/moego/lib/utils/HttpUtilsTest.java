package com.moego.lib.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;

public class HttpUtilsTest {

    @Test
    public void testMakeURL() {
        String url = "http://moego.pet";
        assertEquals("http://moego.pet", HttpUtils.makeURL(url, null));

        Map<String, Object> params = new HashMap<>();
        assertEquals("http://moego.pet", HttpUtils.makeURL(url, params));

        params.put("k", "v");
        assertEquals("http://moego.pet?k=v", HttpUtils.makeURL(url, params));

        params.put("id", 100);
        params.put("text", "中文");
        params.put("enabled", true);
        assertEquals(
                "http://moego.pet?k=v&id=100&text=%E4%B8%AD%E6%96%87&enabled=true", HttpUtils.makeURL(url, params));

        System.out.println("--> test http utils: make url ok !");
    }

    @Test
    public void testTakeParamsFromURL() {
        var params1 = HttpUtils.takeParamsFromURL("http://moego.pet");
        assertTrue(params1.isEmpty());

        var params2 = HttpUtils.takeParamsFromURL("http://moego.pet?k=v");
        assertEquals(1, params2.size());
        assertEquals("v", params2.get("k"));

        var params3 = HttpUtils.takeParamsFromURL("http://moego.pet?k=v&id=100&text=%E4%B8%AD%E6%96%87&enabled=true");
        assertEquals(4, params3.size());
        assertEquals("v", params3.get("k"));
        assertEquals("100", params3.get("id"));
        assertEquals("中文", params3.get("text"));
        assertEquals("true", params3.get("enabled"));

        var params4 = HttpUtils.takeParamsFromURL("http://moego.pet/k=v&id=100&text=%E4%B8%AD%E6%96%87&enabled=true");
        assertTrue(params4.isEmpty());

        System.out.println("--> test http utils: take params from url ok !");
    }
}
