package com.moego.lib.utils;

import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.utils.model.Pair;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiFunction;

public class PageUtil {
    public static final int DEFAULT_PAGE_SIZE = 500;

    /**
     * Fetch all paginated data
     * @param pageSize size of each page
     * @param loader loading function that takes page number and page size, returns data list and total count
     * @return list of all data
     */
    public static <T> List<T> fetchAll(
            final int pageSize,
            BiFunction<Integer /* page */, Integer /* size */, Pair<List<T>, Integer /* total */>> loader) {
        var res = loader.apply(1, pageSize);
        final var out = new ArrayList<T>(res.value());
        out.addAll(res.key());
        final var end = (res.value() / pageSize) + (res.value() % pageSize == 0 ? 0 : 1);
        for (int i = 2; i <= end; i++) {
            res = loader.apply(i, pageSize);
            out.addAll(res.key());
        }
        return out;
    }

    /**
     * Fetch all paginated data using default page size
     * @param loader loading function that takes page number and page size, returns data list and total count
     * @return list of all data
     */
    public static <T> List<T> fetchAll(
            BiFunction<Integer /* page */, Integer /* size */, Pair<List<T>, Integer /* total */>> loader) {
        return fetchAll(DEFAULT_PAGE_SIZE, loader);
    }

    /**
     * Fetch all GRPC paginated data
     * @param loader loading function that takes page number and page size, returns data list and pagination response
     * @return list of all data
     */
    public static <T> List<T> fetchAllGrpc(
            BiFunction<Integer /* page */, Integer /* size */, Pair<List<T>, PaginationResponse>> loader) {
        return fetchAllGrpc(DEFAULT_PAGE_SIZE, loader);
    }

    /**
     * Fetch all GRPC paginated data
     * @param pageSize size of each page
     * @param loader loading function that takes page number and page size, returns data list and pagination response
     * @return list of all data
     */
    public static <T> List<T> fetchAllGrpc(
            final int pageSize,
            BiFunction<Integer /* page */, Integer /* size */, Pair<List<T>, PaginationResponse>> loader) {
        return fetchAll(pageSize, (page, size) -> {
            var response = loader.apply(page, size);
            return Pair.of(response.key(), response.value().getTotal());
        });
    }
}
