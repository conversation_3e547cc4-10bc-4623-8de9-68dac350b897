package com.moego.lib.utils;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.lang.reflect.Array;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HexFormat;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.StringJoiner;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public final class CoreUtils {

    // a random object
    private static final Random globalRandom = new Random();

    // a global id
    private static final AtomicLong globalID = new AtomicLong(0);

    // get a global id, range: [0, 2^64)
    public static long getId() {
        return globalID.getAndIncrement();
    }

    // get a global id, range: [0, bound)
    public static long getId(long bound) {
        if (0 < bound) {
            return getId() % bound;
        }

        throw new IllegalArgumentException("'bound' must be greater than 0");
    }

    // get a global id, range: [origin, bound)
    public static long getId(long origin, long bound) {
        if (origin < bound) {
            var v = getId();
            if (origin <= v && v < bound) {
                return v;
            }

            return (v % (bound - origin)) + origin;
        }

        throw new IllegalArgumentException(
                "'origin' must be less than 'bound', origin: " + origin + ", bound: " + bound);
    }

    // get environment variable
    public static String getenv(String key) {
        return getenv(key, null);
    }

    // get environment variable, if key does not exist, return default value.
    public static String getenv(String key, String defaultValue) {
        String value = System.getenv(key);
        return value == null ? defaultValue : value;
    }

    // get a uuid
    public static String uuid() {
        return UUID.randomUUID().toString();
    }

    // get a uuid but no dash: '-'
    public static String uuidShort() {
        String id = uuid();
        return (id.substring(0, 8)
                + id.substring(9, 13)
                + id.substring(14, 18)
                + id.substring(19, 23)
                + id.substring(24));
    }

    // get a random number, range: [0, 1)
    public static double randomDouble() {
        return globalRandom.nextDouble();
    }

    // get a random number, range: [0, 2^32)
    public static int randomInt() {
        return globalRandom.nextInt();
    }

    // get a random number, range: [0, 2^64)
    public static long randomLong() {
        return globalRandom.nextLong();
    }

    // get a random number, range: [0, bound)
    public static int random(int bound) {
        return globalRandom.nextInt(bound);
    }

    // get a random number, range: [origin, bound)
    public static int random(int origin, int bound) {
        return globalRandom.nextInt(origin, bound);
    }

    // get a random number, range: [0, bound)
    public static long random(long bound) {
        return globalRandom.nextLong(bound);
    }

    // get a random number, range: [origin, bound)
    public static long random(long origin, long bound) {
        return globalRandom.nextLong(origin, bound);
    }

    // start a new thread to run function f
    public static void startThread(Runnable f) {
        new Thread(f).start();
    }

    // start n threads to run function f
    public static void startThread(Runnable f, int n) {
        for (int i = 0; i < n; ++i) {
            startThread(f);
        }
    }

    // sleep
    public static void sleep(long milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException ignored) {
        }
    }

    // concat two byte array
    public static byte[] concatBytes(byte[] a, int aStart, int aSize, byte[] b, int bStart, int bSize) {
        if (a == null || b == null) {
            throw new NullPointerException("The array parameter is null");
        }
        if (aStart < 0 || aSize < 0 || bStart < 0 || bSize < 0) {
            throw new IllegalArgumentException("The starting position or length of the array is invalid");
        }

        byte[] result = new byte[aSize + bSize];
        System.arraycopy(a, aStart, result, 0, aSize);
        System.arraycopy(b, bStart, result, aSize, bSize);
        return result;
    }

    // concat two byte array
    public static byte[] concatBytes(byte[] a, byte[] b) {
        if (a == null || b == null) {
            throw new NullPointerException("The array parameter is null");
        }
        var result = Arrays.copyOf(a, a.length + b.length);
        System.arraycopy(b, 0, result, a.length, b.length);
        return result;
    }

    private static final byte[] globalCharDigit =
            new byte[] {0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39};
    private static final byte[] globalLowerAlpha = new byte[] {
        0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d,
        0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7a
    };
    private static final byte[] globalUpperAlpha = new byte[] {
        0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d,
        0x4e, 0x4f, 0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a
    };

    // all alpha
    private static final byte[] globalAlpha = concatBytes(globalLowerAlpha, globalUpperAlpha);
    // alpha and digit
    private static final byte[] globalAlphaDigit = concatBytes(globalCharDigit, globalAlpha);
    // hex char
    private static final byte[] globalHexChar =
            concatBytes(globalCharDigit, 0, globalCharDigit.length, globalLowerAlpha, 0, 6);

    public static String byteToHexString(byte v) {
        return digitToHexString(v, Byte.SIZE);
    }

    public static String shortToHexString(short v) {
        return digitToHexString(v, Short.SIZE);
    }

    public static String intToHexString(int v) {
        return digitToHexString(v, Integer.SIZE);
    }

    public static String longToHexString(long v) {
        return digitToHexString(v, Long.SIZE);
    }

    private static String digitToHexString(long v, int n) {
        n >>= 2;
        byte[] s = new byte[n];
        while (0 < n) {
            s[--n] = globalHexChar[(int) (v & 0x0f)];
            v >>>= 4;
        }

        return new String(s, StandardCharsets.UTF_8);
    }

    // byte sequence to hexadecimal text
    public static String bytesToHexString(byte[] bytes) {
        return HexFormat.of().formatHex(bytes);
    }

    public static byte[] hexStringToBytes(String text) {
        return HexFormat.of().parseHex(text);
    }

    // check whether a string is an digest string
    public static boolean isDigestString(String str) {
        if (str == null || str.isBlank()) {
            return false;
        }

        return str.chars().allMatch(HexFormat::isHexDigit);
    }

    public static byte[] randomBytes(byte[] s, int from, int to, int n) {
        byte[] result = new byte[n];
        for (int i = 0; i < n; ++i) {
            result[i] = s[random(from, to)];
        }

        return result;
    }

    public static byte[] randomBytes(byte[] s, int n) {
        return randomBytes(s, 0, s.length, n);
    }

    // randomly generate a sequence of n bytes
    public static byte[] randomBytes(int n) {
        return randomBytes(globalAlphaDigit, n);
    }

    // randomly generate a string of length n
    public static String randomString(int n) {
        if (n < 1) {
            return "";
        }
        return new String(randomBytes(n), StandardCharsets.UTF_8);
    }

    // randomly generate a digest string of length n
    public static String randomDigest(int n) {
        if (n < 1) {
            return "";
        }
        return new String(randomBytes(globalHexChar, n), StandardCharsets.UTF_8);
    }

    // earth radius, in meters
    public static final long EARTH_RADIUS = 6371004L;

    /**
     * calculate the distance between two points based on latitude and longitude
     * params unit: angle
     * return unit: meters
     */
    public static double straightDistanceByAngle(double lat1, double lng1, double lat2, double lng2) {
        return straightDistanceByRadians(
                Math.toRadians(lat1), Math.toRadians(lng1), Math.toRadians(lat2), Math.toRadians(lng2));
    }

    /**
     * calculate the distance between two points based on latitude and longitude
     * params unit: radians
     * return unit: meters
     */
    public static double straightDistanceByRadians(double lat1, double lng1, double lat2, double lng2) {
        double dLat = lat2 - lat1;
        double dLng = lng2 - lng1;
        double a = Math.pow(Math.sin(dLat / 2), 2)
                + Math.cos(lat1) * Math.cos(lat2) * Math.sin(dLng / 2) * Math.sin(dLng / 2);
        return 2 * EARTH_RADIUS * Math.asin(Math.sqrt(a));
    }

    // compares two 'Double' numbers with the specified precision
    // doubleEquals(1.2345, 1.2355, 0.01)  => true
    // doubleEquals(1.2345, 1.2355, 0.001) => false
    public static boolean doubleEquals(double a, double b, double precision) {
        return Double.doubleToLongBits(a) == Double.doubleToLongBits(b) || Math.abs(a - b) < precision;
    }

    // try to get a non-null object
    public static <T> T requiredNonNull(T a, T b) {
        return a == null ? b : a;
    }

    // Object to byte
    public static Byte objToByte(Object v, Byte defaultValue) {
        try {
            var value = objToByte(v);
            return value == null ? defaultValue : value;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static Byte objToByte(Object v) {
        if (v == null) {
            return null;
        }
        if (v instanceof Number n) {
            return n.byteValue();
        }
        if (v instanceof String s) {
            return Byte.parseByte(s);
        }

        throw new IllegalArgumentException("Object can't be cast to Byte: " + v);
    }

    // Object to Short
    public static Short objToShort(Object v, Short defaultValue) {
        try {
            var value = objToShort(v);
            return value == null ? defaultValue : value;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static Short objToShort(Object v) {
        if (v == null) {
            return null;
        }
        if (v instanceof Number n) {
            return n.shortValue();
        }
        if (v instanceof String s) {
            return Short.parseShort(s);
        }

        throw new IllegalArgumentException("Object can't be cast to Short: " + v);
    }

    // Object to Integer
    public static Integer objToInteger(Object v, Integer defaultValue) {
        try {
            var value = objToInteger(v);
            return value == null ? defaultValue : value;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static Integer objToInteger(Object v) {
        if (v == null) {
            return null;
        }
        if (v instanceof Number n) {
            return n.intValue();
        }
        if (v instanceof String s) {
            return Integer.parseInt(s);
        }

        throw new IllegalArgumentException("Object can't be cast to Integer: " + v);
    }

    // Object to Long
    public static Long objToLong(Object v, Long defaultValue) {
        try {
            var value = objToLong(v);
            return value == null ? defaultValue : value;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static Long objToLong(Object v) {
        if (v == null) {
            return null;
        }
        if (v instanceof Number n) {
            return n.longValue();
        }
        if (v instanceof String s) {
            return Long.parseLong(s);
        }

        throw new IllegalArgumentException("Object can't be cast to Long: " + v);
    }

    // Object to Float
    public static Float objToFloat(Object v, Float defaultValue) {
        try {
            var value = objToFloat(v);
            return value == null ? defaultValue : value;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static Float objToFloat(Object v) {
        if (v == null) {
            return null;
        }
        if (v instanceof Number n) {
            return n.floatValue();
        }
        if (v instanceof String s) {
            return Float.parseFloat(s);
        }

        throw new IllegalArgumentException("Object can't be cast to Float: " + v);
    }

    // Object to Double
    public static Double objToDouble(Object v, Double defaultValue) {
        try {
            var value = objToDouble(v);
            return value == null ? defaultValue : value;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static Double objToDouble(Object v) {
        if (v == null) {
            return null;
        }
        if (v instanceof Number n) {
            return n.doubleValue();
        }
        if (v instanceof String s) {
            return Double.parseDouble(s);
        }

        throw new IllegalArgumentException("Object can't be cast to Double: " + v);
    }

    // Object to Boolean
    public static Boolean objToBoolean(Object v, Boolean defaultValue) {
        try {
            var value = objToBoolean(v);
            return value == null ? defaultValue : value;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    @SuppressFBWarnings("NP_BOOLEAN_RETURN_NULL")
    public static Boolean objToBoolean(Object v) {
        if (v == null) {
            return null;
        }
        if (v instanceof Boolean b) {
            return b;
        }
        if (v instanceof String s) {
            if ("true".equalsIgnoreCase(s)) {
                return true;
            }
            if ("false".equalsIgnoreCase(s)) {
                return false;
            }
        }

        throw new IllegalArgumentException("Object can't be cast to Boolean: " + v);
    }

    // Object to String
    public static String objToString(Object v, String defaultValue) {
        try {
            var value = objToString(v);
            return value == null ? defaultValue : value;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static String objToString(Object v) {
        if (v == null) {
            return null;
        }
        if (v.getClass().isArray()) {
            return arrayToString(v);
        }
        if (v instanceof Collection c) {
            return collToString(c);
        }
        return v.toString();
    }

    // Object to specified type
    public static <T> T objToType(Class<T> cls, Object v, T defaultValue) {
        try {
            var value = objToType(cls, v);
            return value == null ? defaultValue : value;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static <T> T objToType(Class<T> cls, Object v) {
        if (v == null) {
            return null;
        }
        if (cls == Byte.class) {
            return cls.cast(objToByte(v));
        }
        if (cls == Short.class) {
            return cls.cast(objToShort(v));
        }
        if (cls == Integer.class) {
            return cls.cast(objToInteger(v));
        }
        if (cls == Long.class) {
            return cls.cast(objToLong(v));
        }
        if (cls == Float.class) {
            return cls.cast(objToFloat(v));
        }
        if (cls == Double.class) {
            return cls.cast(objToDouble(v));
        }
        if (cls == Boolean.class) {
            return cls.cast(objToBoolean(v));
        }
        if (cls == String.class) {
            return cls.cast(objToString(v));
        }

        return cls.cast(v);
    }

    // converts a sequence to a list with the specified element type
    public static <T> List<T> objToList(Object obj, Class<T> cls) {
        if (obj == null) {
            return null;
        }
        if (obj.getClass().isArray()) {
            List<T> result = new ArrayList<>();
            for (int i = 0; i < Array.getLength(obj); ++i) {
                result.add(CoreUtils.objToType(cls, Array.get(obj, i)));
            }
            return result;
        } else if (obj instanceof Collection c) {
            List<T> result = new ArrayList<>();
            for (var item : c) {
                result.add(CoreUtils.objToType(cls, item));
            }
            return result;
        }

        throw new ClassCastException("Object can't be cast to List: " + obj);
    }

    // converts object to map with the specified key type and value type
    public static <K, V> Map<K, V> objToMap(Object obj, Class<K> clsKey, Class<V> clsValue) {
        if (obj == null) {
            return null;
        }
        if (obj instanceof Map<?, ?> map) {
            Map<K, V> result = new HashMap<>();
            for (var entry : map.entrySet()) {
                result.put(
                        CoreUtils.objToType(clsKey, entry.getKey()), CoreUtils.objToType(clsValue, entry.getValue()));
            }
            return result;
        }

        throw new ClassCastException("Object can't be cast to Map: " + obj);
    }

    @SuppressWarnings("unchecked")
    public static <K, V> Map<K, V> castMap(Object src) {
        if (src == null) {
            return null;
        }
        return (Map<K, V>) src;
    }

    @SuppressWarnings("unchecked")
    public static <T> List<T> castList(Object src) {
        if (src == null) {
            return null;
        }
        return (List<T>) src;
    }

    private static String arrayToString(Object v) {
        var len = Array.getLength(v);
        if (len == 0) {
            return "[]";
        }

        StringJoiner joiner = new StringJoiner(", ");
        for (int i = 0; i < len; ++i) {
            joiner.add(objToString(Array.get(v, i)));
        }
        return "[" + joiner + "]";
    }

    private static String collToString(Collection<?> c) {
        if (c.isEmpty()) {
            return "[]";
        }

        StringJoiner joiner = new StringJoiner(", ");
        for (var e : c) {
            joiner.add(objToString(e));
        }
        return "[" + joiner + "]";
    }
}
