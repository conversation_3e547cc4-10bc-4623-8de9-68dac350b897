package com.moego.lib.utils;

import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpRequest.BodyPublishers;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodyHandlers;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.StringJoiner;

public final class HttpUtils {

    private static final HttpClient httpClient = HttpClient.newBuilder().build();

    public static HttpResponse<String> get(String url, String... headers) throws IOException, InterruptedException {
        return get(url, null, headers);
    }

    public static HttpResponse<String> get(String url, Long timeout, String... headers)
            throws IOException, InterruptedException {
        return request("GET", url, (String) null, timeout, headers);
    }

    public static HttpResponse<String> delete(String url, String... headers) throws IOException, InterruptedException {
        return delete(url, null, headers);
    }

    public static HttpResponse<String> delete(String url, Long timeout, String... headers)
            throws IOException, InterruptedException {
        return request("DELETE", url, (String) null, timeout, headers);
    }

    public static HttpResponse<String> post(String url, String body, String... headers)
            throws IOException, InterruptedException {
        return post(url, body, null, headers);
    }

    public static HttpResponse<String> post(String url, String body, Long timeout, String... headers)
            throws IOException, InterruptedException {
        return request("POST", url, body, timeout, headers);
    }

    public static HttpResponse<String> put(String url, String body, String... headers)
            throws IOException, InterruptedException {
        return put(url, body, null, headers);
    }

    public static HttpResponse<String> put(String url, String body, Long timeout, String... headers)
            throws IOException, InterruptedException {
        return request("PUT", url, body, timeout, headers);
    }

    public static HttpResponse<byte[]> request(
            HttpClient client, String method, String url, InputStream params, Long timeout, String... headers)
            throws IOException, InterruptedException {
        return client.send(makeRequest(url, method, params, timeout, headers), BodyHandlers.ofByteArray());
    }

    public static HttpResponse<byte[]> request(
            HttpClient client, String method, String url, byte[] params, Long timeout, String... headers)
            throws IOException, InterruptedException {
        return client.send(makeRequest(url, method, params, timeout, headers), BodyHandlers.ofByteArray());
    }

    public static HttpResponse<String> request(
            HttpClient client, String method, String url, String params, Long timeout, String... headers)
            throws IOException, InterruptedException {
        return client.send(makeRequest(url, method, params, timeout, headers), BodyHandlers.ofString());
    }

    public static HttpResponse<byte[]> request(
            String method, String url, InputStream params, Long timeout, String... headers)
            throws IOException, InterruptedException {
        return request(httpClient, method, url, params, timeout, headers);
    }

    public static HttpResponse<byte[]> request(
            String method, String url, byte[] params, Long timeout, String... headers)
            throws IOException, InterruptedException {
        return request(httpClient, method, url, params, timeout, headers);
    }

    public static HttpResponse<String> request(
            String method, String url, String params, Long timeout, String... headers)
            throws IOException, InterruptedException {
        return request(httpClient, method, url, params, timeout, headers);
    }

    public static String concatParams(Map<String, Object> params) {
        StringJoiner joiner = new StringJoiner("&");
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            var key = URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8);
            var value = URLEncoder.encode(entry.getValue().toString(), StandardCharsets.UTF_8);
            joiner.add(key + "=" + value);
        }

        return joiner.toString();
    }

    // concatenate url with params
    public static String makeURL(String url, Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return url;
        }

        return url + "?" + concatParams(params);
    }

    // take params from url
    public static Map<String, String> takeParamsFromURL(String url) {
        Map<String, String> params = new HashMap<>();
        int index = url.indexOf("?");
        if (0 < index) {
            String[] pairs = url.substring(index + 1).split("&");
            for (String pair : pairs) {
                int i = pair.indexOf("=");
                if (0 < i) {
                    var key = URLDecoder.decode(pair.substring(0, i), StandardCharsets.UTF_8);
                    var value = URLDecoder.decode(pair.substring(i + 1), StandardCharsets.UTF_8);
                    params.put(key, value);
                }
            }
        }

        return params;
    }

    private static HttpRequest.Builder makeBuilder(String url, Long timeout, String... headers) {
        HttpRequest.Builder builder = HttpRequest.newBuilder();
        builder.uri(URI.create(url));
        if (timeout != null && 0 < timeout) {
            builder.timeout(Duration.ofMillis(timeout));
        }
        if (headers != null && 0 < headers.length) {
            builder.headers(headers);
        }

        return builder;
    }

    private static HttpRequest makeRequest(String url, String method, byte[] params, Long timeout, String... headers) {
        var publisher = params == null ? BodyPublishers.noBody() : BodyPublishers.ofByteArray(params);
        return makeBuilder(url, timeout, headers).method(method, publisher).build();
    }

    private static HttpRequest makeRequest(String url, String method, String params, Long timeout, String... headers) {
        var publisher = params == null ? BodyPublishers.noBody() : BodyPublishers.ofString(params);
        return makeBuilder(url, timeout, headers).method(method, publisher).build();
    }

    private static HttpRequest makeRequest(
            String url, String method, InputStream params, Long timeout, String... headers) {
        var publisher = params == null ? BodyPublishers.noBody() : BodyPublishers.ofInputStream(() -> params);
        return makeBuilder(url, timeout, headers).method(method, publisher).build();
    }
}
