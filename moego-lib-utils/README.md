# moego-lib-utils

通用功能库

注意：本项目不同于 moego-lib-common，本项目表达的是“通用”语义，而非“公共”的，这也意味着对本项目的依赖并非必须的，而是可选的。

1. 尽量以纯函数的形式提供各种功能，因此对外暴露的尽量应该是 static function。
2. 所有的接口都必须提供测试用例，尽量将测试覆盖率维持在 95% 以上。如果第一点做的足够好，这一点也将更容易达到。
3. 测试用例尽量不要使用 mock 方式，尽量测试函数真实的执行过程。除非测试中需要调用第三方 API 则可以对第三方 API 进行 mock。
4. 尽量不要依赖其他库，所有功能尽量使用 jdk 提供的能力去实现，除非 jdk 没有提供，并且该功能又比较复杂则可以引入其他库(例如 json 序列化)。
5. 函数的参数和返回值尽量使用内置类型(例如：int、boolean)，而非包装类型(除非需要表达 `null` 语义)。我们提供的功能库用于本地调用，因此可以尽量减少不必要的判空操作。
